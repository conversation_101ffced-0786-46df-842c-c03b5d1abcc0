; Trace32 Configuration file for AMP multicore debugging
;
; Syntax:
;  t32m*.exe [<-option> <filename> <arguments>] [<-option> <filename> <arguments>]
;    Available options:
;    -c Define T32 configuration file. Default : config.t32
;    -s Define T32 startup script. Default (if available): t32.cmm
; Parameters:
;   t32mppc -c config_multicore.t32 <intercom_port> <title> <tmp-dir> <sys-dir> <help-dir> <interface-to-debug-module> [interface options]
; Examples:
;   t32mppc -c config_multicore.t32 10000 MPC_CORE0 C:\temp C:\t32 C:\t32\pdf USB CORE=1 -s mpc5746m_demo_led.cmm
;   t32mppc -c config_multicore.t32 10000 MPC_CORE0 C:\temp C:\t32 C:\t32\pdf NET NODE=pod-mob2 PACKLEN=1024 CORE=1 -s mpc5746m_demo_led.cmm
;
; $Id: config_multicore.t32 7384 2014-07-09 07:44:45Z mobermeir $

IC=NETASSIST
PORT=${1}

; Environment variables
OS=
ID=T32${1}
TMP=${3}
SYS=${4}
HELP=${5}

PBI=
${6}
${7}
${8}
${9}

; Printer settings
PRINTER=WINDOWS

; Screen fonts
SCREEN=
FONT=SMALL
HEADER=Trace32 ${2}
