/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmIn
**  Filename        :  CanMgmIn_out.h
**  Created on      :  30-nov-2020 09:37:00
**  Original author :  SantoroR
******************************************************************************/
/*****************************************************************************
**
**                        CanMgmIn Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef CANMGMIN_OUT_H
#define CANMGMIN_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
typedef enum{
    ST_ERASE_WAIT,
    ST_ERASE_COMPLETE,
    ST_ERASE_RETRY
}StEraseFaults_T;

typedef enum{
    OFF,
    PREPARE_E_DRIVE,
    E_DRIVE,
    STOP_E_DRIVE
}EDriveSts_T;

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint16_T RpmCAN;
extern uint8_T  FlgDiagRpmDis;
extern int16_T  DSAEcu;
extern uint8_T  VDGasPosCAN;
extern uint32_T TotOdometerCAN;
extern uint8_T  AckReqResetCylCorrAd;
extern uint8_T  FlgNoTrqCtrSA;
extern uint8_T  FlgDCTShifting;
extern uint16_T AngThrottle;
extern uint8_T  NmaxControllerActive;
extern uint16_T VehSpeed;
extern uint8_T  ASRActive;
extern uint8_T  RefuelDetected;
extern uint8_T  VDTWaterCAN;
extern uint8_T  VDTAirCAN;
extern int16_T  TWaterCAN;
extern int16_T  TAirCAN;
extern uint16_T LoadCAN;
extern uint8_T  VDRpmCAN;
extern uint8_T  BEngineFuelCutoffStatusCAN;
extern uint8_T  BEngStSCAN;
extern uint8_T  EngstsCAN;
extern uint8_T  FlgReqCALID;
extern uint8_T  EngineAtLimiter;
extern uint8_T  FlgRonDetectDis;
extern uint8_T  EngineShutDownCAN;
extern uint8_T  FlgMKnockDis;
extern uint16_T LamObjRichX1CAN;
extern uint16_T LamObjLeanX1CAN;
extern uint16_T LamObjRichX2CAN;
extern uint16_T LamObjLeanX2CAN;
extern uint16_T LamObjCAN;
extern uint16_T LamSensX1CAN;
extern uint8_T  ReqResetCylCorrAdCAN;
extern uint8_T  OLLamX1CAN;
extern uint8_T  OLLamX2CAN;
extern uint8_T  AFRCLActive;
extern uint8_T  CylBalCLReq;
extern uint8_T  EngineTypeCAN;
extern uint8_T  DesCutOffCAN;
extern uint8_T  IgnitionCutOffDxCAN;
extern uint8_T  IgnitionCutOffSxCAN;
extern uint8_T  CntRpmCAN;
extern uint8_T  VDLoadSxCAN;
extern uint8_T  VDLoadDxCAN;
extern uint16_T LoadSxCAN;
extern uint16_T LoadDxCAN;
extern uint8_T  StEraseFaults;
extern uint8_T  DtcErasedST;
extern uint8_T  EDriveSts;
extern uint16_T GasPos;
extern uint8_T  FuelLevelRawValueCAN;
extern uint8_T  FuelLevelFailStsCAN;
extern uint8_T  KeyStatusCAN;
extern uint8_T  WarmingUpCycleCAN;
extern uint8_T  ActivePhaseSearchCAN;
extern uint8_T  NcmLivenessFaultCAN;
extern uint32_T FlgAnyAbsentVeh;
extern uint8_T  FlgAnyAbsentPri;

extern uint8_T  FlgRonStoredIn;
extern uint8_T  RonLevelIn;

/// TIME_E_DATE
extern uint8_T  Hour1;
extern uint8_T  Hour2;
extern uint8_T  Minute1;
extern uint8_T  Minute2;
extern uint8_T  Day1;
extern uint8_T  Day2;
extern uint8_T  Month1;
extern uint8_T  Month2;
extern uint8_T  Year1;
extern uint8_T  Year2;
extern uint8_T  Year3;
extern uint8_T  Year4;

/// Stub
extern uint8_T  CutoffFlg;
extern uint8_T  ResetTSparkAdat;
extern uint8_T  ISCMKnkPIUsedByECMCAN;
extern uint16_T LamObj1CAN;
extern uint16_T LamObj2CAN;
extern uint8_T CylBalCLReqCAN;
extern uint8_T AfrClActiveCAN;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : CanMgm_CanRecv5ms
**
**   Description:
**    CAN receive operations at 5ms.
**    This functions performs:
**     - Decide whether to receive or to initialize depending on the CAN status
**     - Diagnose the CAN Busoff.
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CanRecv5ms(void);

/******************************************************************************
**   Function    : CanMgm_CanRecv10ms
**
**   Description:
**    CAN receive operations at 10ms.
**    This functions performs:
**     - Decide whether to receive or to initialize depending on the CAN status
**     - Diagnose the CAN Busoff.
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CanRecv10ms(void);

/******************************************************************************
**   Function    : CanMgm_CanRecv20ms
**
**   Description:
**    CAN receive operations at 20ms.
**    This functions performs:
**     - Decide whether to receive or to initialize depending on the CAN status
**     - Diagnose the CAN Busoff.
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CanRecv20ms(void);

/******************************************************************************
**   Function    : CanMgm_CanRecv50ms
**
**   Description:
**    CAN receive operations at 50ms.
**    This functions performs:
**     - Decide whether to receive or to initialize depending on the CAN status
**     - Diagnose the CAN Busoff.
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CanRecv50ms(void);

/******************************************************************************
**   Function    : CanMgm_CanRecv100ms
**
**   Description:
**    CAN receive operations at 100ms.
**    This functions performs:
**     - Decide whether to receive or to initialize depending on the CAN status
**     - Diagnose the CAN Busoff.
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CanRecv100ms(void);

/******************************************************************************
**   Function    : CanMgm_Initialize
**
**   Description:
**    Reset the values of the module variables. The global variables (outputs) 
**    and the static variables (states) are reset to the default values by this function.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_Initialize(void);

/******************************************************************************
**   Function    : CanMgm_CheckConditions
**
**   Description:
**    This function checks parameter consistency for the activation of diagnostic 
**    jobs (download enabling, DTCs erasing and IOLIs activation
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
uint8_T CanMgm_CheckConditions(void);

/******************************************************************************
**   Function    : CanMgm_SetClearDtcErasedST
**
**   Description:
**    Set DtcErasedST to its input value
**
**   Parameters :
**    [IN]value: TRUE or FALSE
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_SetClearDtcErasedST(uint8_T value);

#endif  /* CANMGMIN_OUT_H */

/****************************************************************************
 ****************************************************************************/

