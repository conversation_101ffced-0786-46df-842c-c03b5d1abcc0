/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  STM
**  Filename        :  STM.h
**  Created on      :  09-Feb-2022 11:00:00
**  Original author :  MocciA
******************************************************************************/
#ifndef _STM_H_
#define _STM_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "stm_out.h"
#include "pit_out.h"
#include "SafetyMngr_PIT_out.h"
#include "sys.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static int16_T STM_ChannelConfig(uint8_T Ch, uint32_T compareValue);

#endif // PIT_H

/****************************************************************************
 ****************************************************************************/

