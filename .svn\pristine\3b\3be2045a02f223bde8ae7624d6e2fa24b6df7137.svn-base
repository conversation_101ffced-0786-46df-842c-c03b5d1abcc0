/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  Flasherase.c
**  Created on      :  22-giu-2020 10:00:00
**  Original author :  CarboniM
**  Integration history: 
**  -  Standard software driver for C55 Flash from "STM - SPC57xKLS-FLASH Driver for C55: Package version 0.2.0"
**          
*******************************************************************************************************************/

#ifdef  _BUILD_FLASH_

#pragma ghs startnomisra

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "ssd_c55.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1u)
extern unsigned short FlashErase_C[] = 
#else
extern const unsigned short FlashErase_C[] = 
#endif
{
#if (TARGET_TYPE == SPC574K2_CUT24) || (TARGET_TYPE == SPC574K2)     /* Total Size = 229 half words */

    0x0080, 0x1821, 0x06F0, 0xD2E1, 0xD3F1, 0xD501, 0x0244, 
    0x015E, 0x0261, 0x0272, 0x7D0B, 0x4378, 0xC053, 0xC065,
    0x480F, 0x0347, 0x2207, 0xE104, 0x2227, 0xE104, 0xE810, 
    0xC143, 0xE811, 0xCF43, 0x480E, 0x7120, 0x0000, 0x7140,
    0x0000, 0x4807, 0x54EB, 0x0000, 0x4807, 0x54EB, 0x0004, 
    0xE804, 0x631F, 0x7800, 0x0152, 0x18C7, 0xC810, 0xE207,
    0x18C7, 0xC804, 0xE607, 0x18C7, 0xC803, 0xE604, 0x484F, 
    0x7800, 0x013A, 0x180C, 0xA800, 0xE604, 0x180C, 0xA801,
    0xE26B, 0xC363, 0xC473, 0x0476, 0xC573, 0x0476, 0xC273, 
    0x0467, 0x18E7, 0xB020, 0x18C7, 0x84FF, 0x2C07, 0x4067,
    0x467E, 0xC763, 0xC873, 0x0476, 0xC973, 0x0476, 0xC673, 
    0x0467, 0x18C7, 0xB020, 0x2C07, 0x4067, 0x7D29, 0x3838,
    0xCB63, 0xCC73, 0x0476, 0xCD73, 0x0476, 0xCA73, 0x0467, 
    0x18C7, 0xB020, 0x2C07, 0x4067, 0x7D4A, 0x3838, 0xCE73,
    0x23F7, 0xE510, 0xCE73, 0x18C7, 0xB020, 0x2C07, 0x7CE6, 
    0x3430, 0x50EB, 0x0000, 0x4667, 0x54EB, 0x0000, 0x4807,
    0x54EB, 0x0004, 0xE810, 0x2C07, 0x54EB, 0x0000, 0xCE73, 
    0x25F7, 0x18C7, 0xB020, 0x2C07, 0x7CE6, 0x3430, 0x50EB,
    0x0004, 0x4667, 0x54EB, 0x0004, 0x7FC7, 0x4B78, 0x7CE6, 
    0x5378, 0x50EB, 0x0000, 0x4476, 0x50EB, 0x0004, 0x7CC7,
    0x3B79, 0xE641, 0x18C5, 0x8038, 0x7FC7, 0x8070, 0x7CE7, 
    0x4B78, 0xD076, 0x18E5, 0x803C, 0x5547, 0x0000, 0x18C5,
    0x8040, 0x50EB, 0x0000, 0xD076, 0x18C5, 0x8044, 0x50EB, 
    0x0004, 0xD076, 0x18C5, 0x8101, 0xC076, 0x6407, 0xD076,
    0x1CC5, 0x01B0, 0x6387, 0xD076, 0xC075, 0x65D7, 0x18E7, 
    0xC51F, 0xD075, 0x180C, 0xA801, 0xE604, 0x180C, 0xA803,
    0xE211, 0xC075, 0x6587, 0x18E7, 0xC51F, 0xD075, 0xC075,
    0x70E0, 0xC880, 0xE208, 0xC075, 0x61D7, 0x18E7, 0xC51F,
    0xD075, 0x636F, 0xE808, 0x2C07, 0xD074, 0xC075, 0x65F7,
    0x18E7, 0xC51F, 0xD075, 0x30E3, 0x0048, 0x2A07, 0xE608,
    0x7FE3, 0xFB78, 0x1800, 0xD000, 0x0002, 0x1800, 0xD000, 
    0x01F7, 0x0173, 0xC2E1, 0xC3F1, 0xC501, 0x20F1, 0x0090,
    0x0004, 0x3038, 0x3030, 0x3246, 0x4646

#endif
};

extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned short *functionBuffer, uint32_T functionSize);

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FlashErase - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
UINT32 FlashErase ( PSSD_CONFIG pSSDConfig,
                    uint8_T eraseOption,
                    uint32_T lowBlockSelect,
                    uint32_T midBlockSelect,
                    uint32_T highBlockSelect,
                    NLARGE_BLOCK_SEL nLargeBlockSelect
                    )
{

}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */


#pragma ghs endnomisra


#endif /*  _BUILD_FLASH_ */
