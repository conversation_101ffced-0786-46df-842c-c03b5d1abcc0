/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DiagCanMgm
**  Filename        :  DiagCanMgm.c
**  Created on      :  26-Apr-2021 12:01:00
**  Original author :  MocciA
******************************************************************************/
#ifdef _BUILD_DIAGCANMGM_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "diagcanmgm.h"
#include "tpe_out.h"
#include <string.h> //for memset, needed if dialect is C99
#ifdef _BUILD_ACTIVE_DIAG_
#include "active_diag_out.h"
#endif

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/// Security Timeout after ECU startup
uint16_T SecurityTimeout = SEC_TIMEOUT_INIT;
/// Flag to invoke a reset at next the Background Task
uint8_T System_Reset = 0u;
/// Flag to activate or not Server response to a diagnostic request
uint8_T response = NO_RESPONSE;
/// Active Diagnostic Session
uint8_T DIAGsession = DIAG_DEFAULT_SESSION;
/// struct for information exchange between Bootloader and Main Application program
union DownloadStruct_tag DownloadStruct = {0u};
/// Diagnostic Session Enabled Services BitMask
uint32_T SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
/// Diagnostic Service Identifier
uint8_T ServiceID;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/// Container for message type and relative diagnostic session
static DecAnswer_T Decode = {0u, 0u};

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : DIAGCANMGM_Init
**
**   Description:
**    This function sets Decoded Message Request and Server Response flag on ECU start-up
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/

void DIAGCANMGM_Init(void)
{
    Decode.message = 0;
    Decode.message_session = 0;
    response = NO_RESPONSE;
}

/******************************************************************************
**   Function    : DIAGCANMGM_APP_Init
**
**   Description:
**    This function starts Diagnostic+Application Layers in terms of diagnostic session and 
**    active diagnostic services; these values could be loaded from NVM in case of normal running 
**    or from VSRAM in case of download procedures incoming from the main Application Level (usually 
**    a $10 $02 causes a jump to Bootloader)
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DIAGCANMGM_APP_Init(void)
{
    {
        DIAGsession = DIAG_DEFAULT_SESSION;
        DownloadStruct.CF = 0;
    }
}

/******************************************************************************
**   Function    : DIAGCANMGM_PowerOn
**
**   Description:
**    This method initializes dignostic layer at powerOn
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DIAGCANMGM_PowerOn(void)
{
    DIAGCANMGM_Init();
    DIAGCANMGM_APP_Init();
}

/******************************************************************************
**   Function    : DIAGCANMGM_DiagApl
**
**   Description:
**    This function handles communication between Session Layer and lower (Transport)/upper (Application)
**    diagnostic layers.
**    Decoding(..) API for communication between Application and Session Layer, 
**    AnswerFunction(..) API for communication between Session and Transport Layers.
**    DST(..) API is the Diagnostic Session Transtion Manager (start and keep alive the diagnostic Session based on message received)
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DIAGCANMGM_DiagApl(void)
{
uint8_T status;

    /* Checking pending transmission */
    status = DIAGCANMGM_SendStatus();

    /* Security protection Timer management */
    if(SecurityTimeout > 0U)
    {
        SecurityTimeout--;
    }
    else
    {
        SecurityTimeout = 0U;
    }

    if(status != DIAGCANMGM_PENDING)
    {
        Decode.message_session = 0;
        Decode.message = 0;

        if(status == DIAGCANMGM_ERROR_TIMEOUT)
        {
            DIAGCANMGM_ResetTPE();
        }

        if((status == DIAGCANMGM_SUCCESS)||(status == DIAGCANMGM_ERROR_TIMEOUT))
        {
            response = NO_RESPONSE;
        }

        if ((!(DIAGCANMGM_Receive (T_DataIND_ptr->tpdu_data,&(T_DataIND_ptr->tpdu_len)))) && (response == NO_RESPONSE))
        {
            Decoding();
            ResponseEvaluation();
        }
        AnswerFunction();

    }

    DST(Decode.message_session);
    /*Buffer for incoming messages shall be reset*/
//    memset(&T_DataIND,0,sizeof(T_DataIND));

}

/******************************************************************************
**   Function    : DIAGCANMGM_Receive
**
**   Description:
**    Function called for data reception from the physical channel using transport protocol interfaces
**
**   Parameters :
**    [out] uint8_T * data, pointer to the received data container
**    [out] uint16_T * data_len, number of bytes of the diagnostic request
**
**   Returns:
**    DIAGCANMGM_NOTSUCCESS, error occurred while receiving data from lower layers
**    DIAGCANMGM_SUCCESS, no error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint8_T DIAGCANMGM_Receive(uint8_T *data, uint16_T *data_len)
{
uint8_T returnCode = DIAGCANMGM_NOTSUCCESS;

    if(tpeGetCommStatus() == DIAG_ON)
    {
        returnCode = tpeReceive(data, data_len);
    }

    return returnCode;
}

/******************************************************************************
**   Function    : DIAGCANMGM_Send
**
**   Description:
**    Function called for data transmission over the physical channel using transport protocol interfaces
**
**   Parameters :
**    [in] uint8_T * data, pointer to the received data container
**    [in] uint8_T * data_len, number of bytes of the diagnostic request
**
**   Returns:
**    DIAGCANMGM_NOTSUCCESS, error occurred while transmitting data to lower layers
**    DIAGCANMGM_SUCCESS, no error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint8_T DIAGCANMGM_Send (uint8_T *data, uint8_T data_len)
{
uint8_T returnCode = DIAGCANMGM_NOTSUCCESS;

    if(tpeGetCommStatus() == DIAG_ON)
    {
        returnCode = tpeSend(data, data_len);
    }

    return returnCode;
}

/******************************************************************************
**   Function    : DIAGCANMGM_SendStatus
**
**   Description:
**    This function returns TP state machine status for what concern the diagnostic response message transmission
**
**   Parameters :
**
**   Returns:
**    DIAGCANMGM_NOTSUCCESS, error occurred while transmitting data to lower layers
**    DIAGCANMGM_SUCCESS, no error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint8_T DIAGCANMGM_SendStatus (void)
{
uint8_T txStatus = DIAGCANMGM_NOTSUCCESS;

    if(tpeGetCommStatus() == DIAG_ON)
    {
        txStatus = tpeSendStatus();
    }

    return txStatus;
}

/******************************************************************************
**   Function    : DIAGCANMGM_ResetTPE
**
**   Description:
**    Function called for TP state machine reset
**
**   Parameters :
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DIAGCANMGM_ResetTPE(void)
{
    tpeSetCommStatus(DIAG_OFF);
}

/******************************************************************************
**   Function    : AnswerFunction
**
**   Description:
**    This function forwards the diagnostic message from Session to Transport Layer
**
**   Parameters :
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnswerFunction(void)
{
    if (response != NO_RESPONSE)
    {
        DIAGCANMGM_Send(T_DataREQ_ptr->tpdu_data,T_DataREQ_ptr->tpdu_len);
    }
}

/******************************************************************************
**   Function    : UpdateBootSession
**
**   Description:
**    This function initializes diagnotic session stack when communication protocol 
**    parameters are incoming from Application Level (diagnostic information stored 
**    in VSRAM)
**
**   Parameters :
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void UpdateBootSession(void)
{
  switch(DIAGsession)
  {
    case DIAG_ECUPROGR_SESSION:
        SessionEnabledServices = ECUPROGR_ENABLED_SERVICES;
        break;
    case DIAG_EXTENDED_SESSION:
        SessionEnabledServices = EXTENDED_ENABLED_SERVICES;
        break;
    case DIAG_SUPPLIER_SESSION:
        SessionEnabledServices = SUPPLIER_ENABLED_SERVICES;
        break;
    default:
        SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
    break;
  }
}

/******************************************************************************
**   Function    : DownloadInProgress
**
**   Description:
**    This cunction cheks if a SW update is in progress or not
**
**   Parameters :
**
**   Returns:
**    0 -> no active download at time request, 
**    1 -> at least active download at time request
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint8_T DownloadInProgress(void)
{
    return ((DownloadStruct.BF.downloadInProgress == DIAG_ON) ? TRUE : FALSE);
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : Decoding
**
**   Description:
**    This method decodes calls layer 7 Application/Diagnostic interfaces 
**
**   Parameters :
**    void
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void Decoding (void)
{

    ServiceID = T_DataIND_ptr->tpdu_data[0];
    T_DataREQ_ptr->tpdu_data[0] = ServiceID + 0x40;  // Positive Response Service Id  0x40

    /* Generic diagnostic message reception signaling */
    Decode.message_session = DIAG_MESSAGE_RECEIVED;

#ifdef _BUILD_UDS_
    CallApplicationService(ServiceID);
#endif
}  // End function Decoding

/******************************************************************************
**   Function    : ResponseEvaluation
**
**   Description:
**    This method evaluates server behaviour in Tx phase, according to SPRMIB ~ stopAnswering
**
**   Parameters :
**    void
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void ResponseEvaluation (void)
{
    response = NO_RESPONSE;
    
    if(DownloadStruct.BF.stopAnswering == DIAG_ON) // if 1, server does not send response to the client
    {
        DownloadStruct.BF.stopAnswering = DIAG_OFF;
    }
    else
    {
        response = SEND_RESPONSE;
    }
}

/******************************************************************************
**   Function    : DST
**
**   Description:
**    This method handles diagnostic state transition according to processed message
**
**   Parameters :
**   [in] uint8_T MessageSession, Decoded Message Request
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void DST (uint8_T MessageSession)
{
/* Internal timers */
static uint16_T    S3Server_timer = S3SERVER_TIMEOUT;

    if (S3Server_timer)
    {
        S3Server_timer--;
    }

    if (MessageSession & DIAG_MESSAGE_RECEIVED)
    {
        /* Timers are resetted each time that a message is received */
        S3Server_timer = S3SERVER_TIMEOUT;
    }

    /* Diagnostic Session and Services Refreshing */
    if (DIAGsession == DIAG_DEFAULT_SESSION) // always on startup
    {
        /* Nothing to do, stay on Default Session */
    }
    else // a $10 $XX has been received
    {
        if (S3Server_timer == 0u) //keep-alive TP not received in time -> return to Default
        {
            DIAGsession = DIAG_DEFAULT_SESSION;
            SessionEnabledServices = DEFAULT_ENABLED_SERVICES;
            /* Reinit all enabled download flags */
            DownloadStruct.CF = (uint32_T)0u;
            resetDefaultValues();
        }
        else
        {
            /* Nothing to do, stay on Non-Default Session */
        }
    }
}


/******************************************************************************
**   Function    : resetDefaultValues
**
**   Description:
**    This method If the ECU resets all activated/initiated/changed settings/controls during 
**    the activated session in the following cases:
**    - $10 $01/81 Jump to default session,
**    - $11 $01 ECU Reset
**    - P3_server timeout
**   Parameters :
**    void
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void resetDefaultValues(void)
{
    /* Service $28 flags reset */
    Flg_CANTX_en = TRUE;
    Flg_CANRX_en = TRUE;
    Flg_NMTX_en = TRUE;
    Flg_NMRX_en = TRUE;

    /* Service $85 flags reset */
    //setEnDTCSetting(DIAG_ON);

    /* Service $2F flags reset */
#ifdef _BUILD_ACTIVE_DIAG_    
    Init_ActiveDiag();
#endif
}


#endif // _BUILD_DIAGCANMGM_
