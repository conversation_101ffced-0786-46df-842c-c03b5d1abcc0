/******************************************************************************************************************************/
/* $HeadURL::                                                                                                             $   */
/* $ Description:                                                                                                             */
/* $Revision::        $                                                                                                       */
/* $Date::                                                $                                                                   */
/* $Author::                         $                                                                                        */
/******************************************************************************************************************************/



#include "sys.h"
#include "ccp.h"
#include "ccp_can_interface.h"
#include "Mcan_out.h"
#include "TTcan_out.h"
//#include "flash_api.h" //for NULL_CALLBACK_AKH*/

#include "Utils_out.h"
#include "OS_api.h"
#include "OS_resources.h"
#include "timing_out.h"

#include "vsrammgm.h"
#include <string.h> // aggiunto per memcpy, serve se compilato C99n

#ifdef _BUILD_SWT_
#include "swt.h"
#else // akhela possible external WD implementations
#endif

#include "WDT_out.h"
#include "WDT_wrapper_out.h"
#include "mpc5500_spr_macros.h"

#ifdef _BUILD_CCP_


#pragma ghs startnomisra

#ifdef CCP_PROGRAM
#include "Flash_out.h"

/* Project dependent           */
#include "AnalogIn_out.h"
#include "pwrmgm_out.h"
/* end of Project dependent section  */
#endif

#define RAM_START    0x40000000
#define RAM_SIZE     0x0000C000
#define RAM_END      (RAM_START+RAM_SIZE)


//#define CCP_CAN_BUFF_NUMBER 4

/* CCP internal programming status defs*/
#define CCP_IDLE     0
#define CCP_APPL     1
#define CCP_BOOT     2
#define CCP_CALIB    3

uint32_T blkLockState;       /* block lock status to be retrieved */

uint8_T ccp_init_done = 0u;


extern uint8_T  buffsel;

extern uint32_T comm_protocol;
extern uint8_T ccpCrmTmp[];
extern uint8_T ccpSessionStatusTmp;
extern boolean_T boot_flashing_rqst;
extern int16_T ret_erase_backup_boot;

static uint32_T ccp_LastMTA;

static CANMsg_T CCP_MSG_Rx ;

static CANMsg_T* iptr = &(CCP_MSG_Rx);

static vuint8_T ccpSendAtOnce = CCP_SEND_AT_ONCE;
/******************************************************************************/
/* LOCAL FUNCTIONS                                                            */
/******************************************************************************/
/* Add Protypes to respect Misra Rules   */
static uint8_T ccpClearDaqList( uint8_T daq );
static uint8_T ccpPrepareDaq(  uint8_T daq_num, uint8_T last_num, uint8_T eventChannel_num, uint16_T prescaler_num );
static uint8_T ccpStartDaq( uint8_T daq_nmb);
static void ccpStartAllPreparedDaq(void);
static void ccpStopDaq ( uint8_T daq_numb );
static uint8_T ccpSampleAndTransmitDtm( uint8_T pid, uint8_T daq_n, uint8_T odt_num );
static uint8_T ccpWriteMTA( uint8_T n, uint8_T size_p, uint8_T* d );
static void ccpReadMTA( uint8_T n, uint8_T size_p, uint8_T* d ) ;
static inline void ccpSetMTA(uint8_T n, uint8_T* ptr_p) ;


/******************************************************************************/
/* Version check                                                              */
/******************************************************************************/
#if( CCP_DRIVER_VERSION != 142)
#error "Source and Header file of CCP-Module inconsistent!"
#endif
#if( CCP_DRIVER_BUGFIX_VERSION != 0)
#error "Source and Header file of CCP-Module inconsistent!"
#endif

#if( CCP_DRIVER_VERSION > 255)
#error "Version decreased in CCP-Module"
#endif

/* GK: moved here */
static inline void ccpSetMTA(uint8_T n, uint8_T* ptr_p) 
{   
    ccp.MTA[n] = ptr_p;
}

/*--------------------------------------------------------------------------*/
void CCP_ActivityMonitoring(void)
{
    int16_T res=CAN_RX_BUFFER_EMPTY;
    //struct CAN_buff * iptr;

    uint8_T  Data[8];
    int8_T   i;

    /*  Used in MV Project 
    res = CAN_RxData(CCP_CAN,CCP_RXCH,&iptr);  */

    /*  Used in HPU Autosar Project
    res = ReceiveMsg (&CCP_MSG_Rx) ; */

#if (CCP_USE_ENGCAN == CCP_USE_MCAN)
    res = CAN_ReceiveMsg (CCP_CAN, 0, &CCP_MSG_Rx );
#elif (CCP_USE_ENGCAN == CCP_USE_TTCAN)
    res = TTCAN_ReceiveMsg (CCP_CAN, 0, &CCP_MSG_Rx );
#else
#error
#endif

    if((res != CAN_RX_BUFFER_EMPTY) && (res != CAN_BUSOFF))
    {
        for(i=0;i<8;i++)
        {
            Data[i] = (iptr->DATA)[i];
        }
        ccpCommand(Data);
        if (ccpTxCrmPossible()==0)
        {
            ccpSendCallBack();
        }
    }

    if (res == CAN_BUSOFF)
    {
        CAN_BusOffRecovery((uint8_T)CCP_CAN);    /* Explicit cast to respect Misra Rules   */
    }
}



/*--------------------------------------------------------------------------*/
/* ROM */
/*--------------------------------------------------------------------------*/

/*
   Identification
   Must be 0 terminated !!

   This string is used by CANape as the ASAP2 database filename
   The extension .A2L or .DB is added by CANape
 */

static uint8_T ccpStationId[] = CCP_STATION_ID;

/*--------------------------------------------------------------------------*/
/* RAM */
/*--------------------------------------------------------------------------*/

/*
   The following structure containes all RAM locations needed by the CCP drive
 */

/* ##Hp - rename struct ccp */
struct stCCP ccp;


/*--------------------------------------------------------------------------*/
/* CODE */
/*--------------------------------------------------------------------------*/

/*--------------------------------------------------------------------------*/
/* Transmit Queue */
/*--------------------------------------------------------------------------*/
#ifdef CCP_SEND_QUEUE

static uint32_T My_ccpSendError = 0U;
static uint32_T My_ccpQueueIsFull = 0U;


static void ccpQueueInit(void)
{
    ccp.Queue.size          = (uint8_T)CCP_SEND_QUEUE_SIZE;
    ccp.Queue.writePointer  = (uint8_T)0;
    ccp.Queue.readPointer   = (uint8_T)0;
}

inline uint8_T ccpQueueIsFull(void)
{
    return (((ccp.Queue.writePointer + 1) % ccp.Queue.size) == ccp.Queue.readPointer);
}

inline uint8_T ccpQueueIsEmpty(void)
{
    return (ccp.Queue.readPointer == ccp.Queue.writePointer);
}

static inline uint8_T ccpQueueWrite(ccpMsg_t *msg_p)
{
    uint8_T isFull = ccpQueueIsFull();
    if (isFull == 0)
    {
        ccp.Queue.msg[ccp.Queue.writePointer] = *msg_p;
        ccp.Queue.writePointer++;
        ccp.Queue.writePointer %= ccp.Queue.size;
    }
    else
    {
        My_ccpQueueIsFull++;
    }

    return isFull;
}

static inline uint8_T ccpQueueSend(void)
{
    ccpMsg_t *p_currMsg = (ccpMsg_t *)0;
    uint8_T isEmpty = ccpQueueIsEmpty();
    uint8_T result = 0;

    if (isEmpty == 0)
    {
        ccp.SendStatus |= CCP_DTM_PENDING;

        p_currMsg = &(ccp.Queue.msg[ccp.Queue.readPointer]);

        if(ccpSend((uint8_T *)p_currMsg)==(uint8_T)CCP_NO_ERROR)
        {
            ccp.Queue.readPointer++;
            ccp.Queue.readPointer %= ccp.Queue.size;

            result = 1U;
        }
        else
        {
            My_ccpSendError++;

            result = 1U;
        }
    }

    return result;
}


#endif


/* Clear DAQ list */
static uint8_T ccpClearDaqList( uint8_T daq )
{
    uint8_T * p;
    uint8_T * pl;
    uint8_T returncode= (uint8_T)CCP_MAX_ODT; // MC misra 14.9 

    if (daq>=(uint8_T)CCP_MAX_DAQ)
    {
        returncode=(uint8_T) 0;
    }
    if(returncode==(uint8_T)CCP_MAX_ODT)
    {
        /* Clear this daq list to zero */
        p = (uint8_T *)&ccp.DaqList[daq];
        pl = p+sizeof(ccpDaqList_t);
        while (p<pl) 
        {
            *p = (uint8_T)0;
            p++;
        }
        /* Not DAQ list specific */
        ccp.SessionStatus |= (uint8_T)SS_DAQ;   /* MC Misra */
#ifdef CCP_SEND_SINGLE
        ccp.CurrentDaq = 0;
        ccp.CurrentOdt = 0;
#endif
#ifdef CCP_SEND_QUEUE
        ccpQueueInit();
#endif
    }
    return returncode;
}





/* Prepare DAQ */
static uint8_T ccpPrepareDaq(  uint8_T daq_num, uint8_T last_num, uint8_T eventChannel_num, uint16_T prescaler_num )
{
    uint8_T returncode=(uint8_T)1;  //misra 14.9 
    if (daq_num>=(uint8_T)CCP_MAX_DAQ)
    {
        returncode=(uint8_T) 0;
    } 
    if(returncode!=(uint8_T)0)
    {
        ccp.DaqList[daq_num].eventChannel = eventChannel_num;
        if (prescaler_num==(uint16_T)0)
        {
            prescaler_num =(uint16_T) 1;
        }
        ccp.DaqList[daq_num].prescaler = (uint16_T) prescaler_num;
        ccp.DaqList[daq_num].cycle = (uint16_T)1;
        ccp.DaqList[daq_num].last = last_num;
        ccp.DaqList[daq_num].flags = (uint8_T)DAQ_FLAG_PREPARED;
    }
    return returncode;
}

/* Start DAQ */
static uint8_T ccpStartDaq( uint8_T daq_nmb ) {
    uint8_T returncode=(uint8_T)1; //misra 14.9
    if (daq_nmb>=(uint8_T)CCP_MAX_DAQ)
    {
        returncode= (uint8_T)0;
    }   
    if(returncode!=(uint8_T)0)
    {
        ccp.DaqList[daq_nmb].flags = (uint8_T)DAQ_FLAG_START;
        ccp.SessionStatus |= (uint8_T)SS_RUN;

#ifdef CCP_TIMESTAMPING
        ccpClearTimestamp();
#endif
    }
    return returncode;
}

/* Start all prepared DAQs */
static void ccpStartAllPreparedDaq(void) {

    uint8_T q;

    for (q=(uint8_T)0;q<(uint8_T)CCP_MAX_DAQ;q++)
    {
        if (ccp.DaqList[q].flags==(uint8_T)DAQ_FLAG_PREPARED)
        {
            ccp.DaqList[q].flags =(uint8_T) DAQ_FLAG_START;
        }    
    }
    ccp.SessionStatus |= (uint8_T)SS_RUN;

#ifdef CCP_TIMESTAMPING
    ccpClearTimestamp();
#endif
}

/* Stop DAQ */
static void ccpStopDaq ( uint8_T daq_numb )
{
    uint8_T k;
    uint8_T control_flow=(uint8_T)1;

    if (daq_numb>=(uint8_T)CCP_MAX_DAQ)
    {
        control_flow=(uint8_T)0;
    }
    if(control_flow!=(uint8_T)0)
    {
        ccp.DaqList[daq_numb].flags =(uint8_T) 0;

        /* check if all DAQ lists are stopped */
        for (k=(uint8_T)0;(k<(uint8_T)CCP_MAX_DAQ) && (control_flow==(uint8_T)1);k++)
        {
            if ((ccp.DaqList[k].flags&(uint8_T)DAQ_FLAG_START) != 0)
            {
                control_flow=(uint8_T)0;
            }
        }
    }   
    if(control_flow!=(uint8_T)0)
    {
        ccp.SessionStatus &=(uint8_T)( ~SS_RUN);
    }

    return;
}

/* Stop all DAQs */
void ccpStopAllDaq( void )
{
    uint8_T l;

    for (l=(uint8_T)0;l<(uint8_T)CCP_MAX_DAQ;l++)
    {
        ccp.DaqList[l].flags = (uint8_T)0;
    }
    ccp.SessionStatus &= (uint8_T)(~SS_RUN);
}




#ifndef CCP_SEND_SINGLE

/* Sample and transmit a DTM */
static uint8_T ccpSampleAndTransmitDtm( uint8_T pid, uint8_T daq_n, uint8_T odt_num )
{
    uint8_T returncode=1;

#ifdef CCP_SEND_QUEUE
    uint8_T dtm[8];
#else
#define dtm ccp.Dtm
#endif
#ifdef CCP_DAQ_BASE_ADDR
    uint8_T * p;
#else
    CCP_DAQBYTEPTR p;
#endif
#ifdef CCP_ODT_ENTRY_SIZE
    uint8_T s;
    uint8_T *d,*dl;
    ccpOdtEntry_t *e,*el;
#else
    uint8_T i;
    ccpOdtEntry_t *e;
#endif

    //ec akhela
    // memset(dtm, 0, 8*sizeof(uint8_T));

    /* PID */
    dtm[0] = pid;

    /* Assure data consistency */
    //DISABLE_INTERRUPT;

    /* Sample */
#ifdef CCP_ODT_ENTRY_SIZE

    e = &ccp.DaqList[daq_n].odt[odt_num][0];
    el = e+8;
    d = &dtm[1];
    dl = d+7;
    while ( (d<dl) && (e<el) && (e->ptr != 0) ) 
    {
#ifdef CCP_DAQ_BASE_ADDR
        p = (uint8_T *)( e->ptr ) + CCP_DAQ_BASE_ADDR;
#else
        //!!        p = e->ptr
        {
            uint32_T tmp;
            tmp = (uint32_T)e->ptr;
            p = (uint8_T *)tmp;
        }
        //////////////////////

#endif
        s = e->siz;
#ifdef CCP_DOUBLE_FLOAT
        if (s==8)
        {
            *(float*)d = (float)(*(double*)p);
            s = 4;
        }
        else
#endif
            if (s==4)
            {
                *(uint32_T *)d = *(uint32_T *)p;
            }
            else if (s==2)
            {
                *(uint16_T *)d = *(uint16_T *)p;
            }
            else
            {
                *d = *p;
            }
        d += s;
        e++;
    }

#else

    e =  &ccp.DaqList[daq_n].odt[odt][0];
    for (i=1;i<8;i++) 
    {
#ifdef CCP_DAQ_BASE_ADDR
        p = (uint8_T +)( (e++)->ptr ) + CCP_DAQ_BASE_ADDR;
#else
        p = (e++)->ptr;
#endif
        if (p)
        {
            dtm[i] = *p;
        }
    }

#endif

    /* Optional for CANape: Put a timestamp in the first ODT (Byte6+7) of each DAQ */
#ifdef CCP_TIMESTAMPING
    if (odt==0) 
    {
        *(uint16_T *)&dtm[6] = ccpGetTimestamp();
    }
#endif

    /* Queue or transmit the DTM */
#ifdef CCP_SEND_QUEUE

    if ((ccp.SendStatus&CCP_SEND_PENDING) != 0) 
    {
        if (ccpQueueWrite((ccpMsg_t*)dtm) == 0)
        {
            /* Overun */
            // ENABLE_INTERRUPT
            returncode = 0;
        }
    } 
    else 
    {
        // ccp.SendStatus |= CCP_DTM_PENDING
        // if(ccpSend(dtm))
        if (ccpQueueWrite((ccpMsg_t*)dtm) == 0) 
        {
            /* Overun */
            // ENABLE_INTERRUPT
            returncode = 0;
        }
    }

#else   /* CCP_SEND_QUEUE*/

    if (ccp.SendStatus&CCP_DTM_REQUEST)
    {
        /* Overun */
        //  ENABLE_INTERRUPT
        returncode = 0;
    }
    if(returncode!=0)
    {

        if (ccp.SendStatus&CCP_SEND_PENDING) 
        {
            ccp.SendStatus |= CCP_DTM_REQUEST;
        } 
        else 
        {
            ccp.SendStatus |= CCP_DTM_PENDING;
            ccpSend(dtm);
        }
    }
#endif

    //ENABLE_INTERRUPT;
    return returncode;
}


#else


/* Sample and transmit the next DTM in SEND_SINGLE mode */
static void ccpSampleAndSendNextDtm( void )
{

    uint8_T i,j;
    uint8_T * p;
    ccpOdtEntry_t *e;

    /* Request for DTM transmission pending */
    if (ccp.SendStatus&CCP_DTM_REQUEST) return;

    /* Find a DAQ list marked for transmission */
    for (i=0;i<CCP_MAX_DAQ;i++) {

        if (ccp.DaqList[ccp.CurrentDaq].flags&DAQ_FLAG_SEND)
        {

            /* PID */
            ccp.Dtm[0] = ccp.CurrentDaq*CCP_MAX_ODT+ccp.CurrentOdt;

            /* Sample */
            e =  &ccp.DaqList[ccp.CurrentDaq].odt[ccp.CurrentOdt][0];
            for (j=1;j<8;j++) {
                p = (e++)->ptr;
                if (p) ccp.Dtm[j] = *p;
            }

            /* Send */
            ccpSendDtm();

            /* Increment ODT */
            if (++ccp.CurrentOdt>ccp.DaqList[ccp.CurrentDaq].last) {

                /* DAQ list done */
                ccp.CurrentOdt = 0;
                ccp.DaqList[ccp.CurrentDaq].flags &= ~DAQ_FLAG_SEND;

                /* Increment DAQ */
                if (++ccp.CurrentDaq>=CCP_MAX_DAQ)
                {
                    ccp.CurrentDaq = 0;
                }
            }

            break;

        }
        else
        {

            /* Increment DAQ */
            if (++ccp.CurrentDaq>=CCP_MAX_DAQ)
            {
                ccp.CurrentDaq = 0;
            }

        }
    }
}

#endif


/* Data aquisition */
void ccpDaq( uint8_T evChannel )
{
    uint8_T q,o;
    uint8_T dummy = (uint8_T)0;
    uint8_T control_flow = (uint8_T)1;
#ifndef CCP_SEND_SINGLE
    uint8_T j;
#endif
    static uint32_T My_QueueError = 0U;
    //  DISABLE_INTERRUPT;

    if ((ccp.SessionStatus&(uint8_T)SS_RUN) == 0)
    {
        control_flow=(uint8_T)0;
    }
    if(control_flow != (uint8_T)0)
    {
        o=(uint8_T)0;
        for (q=(uint8_T)0; q<(uint8_T)CCP_MAX_DAQ;q++)
        {
            if ((ccp.DaqList[q].flags&(uint8_T)DAQ_FLAG_START) == 0)
            {
                /* continue; */
                dummy++;
            }
            else if (ccp.DaqList[q].eventChannel!=evChannel)
            {
                /* continue; */
                dummy++;
            }
            else
            {
                ccp.DaqList[q].cycle--;
                if (/*--*/ccp.DaqList[q].cycle!=(uint16_T)0)
                {
                    /* continue;  */
                    dummy++;
                }
                else
                {
                    ccp.DaqList[q].cycle = ccp.DaqList[q].prescaler;

#ifdef CCP_SEND_SINGLE

                    /* Just mark DAQ for transmission */
                    ccp.DaqList[q].flags |= DAQ_FLAG_SEND;

#else

                    /* Check that the current queue space fits a complete cycle */
#if defined(CCP_SEND_QUEUE) && defined(CCP_SEND_QUEUE_OVERRUN_INDICATION)
                    if (CCP_SEND_QUEUE_SIZE-ccp.Queue.len<=ccp.DaqList[q].last)
                    {
                        ccp.DaqList[q].flags |= (uint16_T)DAQ_FLAG_OVERRUN;
                        /*continue;*/ /* Skip this DAQ list on overrun */
                    }
                    else
                    {
#endif

                        /* Use BIT7 of PID to indicate overruns (CANape special feature) */
#ifdef CCP_SEND_QUEUE_OVERRUN_INDICATION

                        for (j=0;j<=ccp.DaqList[q].last;j++)
                        {
                            if (!ccpSampleAndTransmitDtm((o+j)|(ccp.DaqList[q].flags&DAQ_FLAG_OVERRUN),q,j))
                            {
                                ccp.DaqList[q].flags |= DAQ_FLAG_OVERRUN;
                            }
                            else
                            {
                                ccp.DaqList[q].flags &= ~DAQ_FLAG_OVERRUN;
                            }
                        } /* j */

#else    /*CCP_SEND_QUEUE_OVERRUN_INDICATION*/

                        for (j=(uint8_T)0;j<=ccp.DaqList[q].last;j++)
                        {
                            if (ccpSampleAndTransmitDtm(o+j,q,j) == 0)
                            {
                                My_QueueError++;
                            }
                        } /* j */

#endif   /*CCP_SEND_QUEUE_OVERRUN_INDICATION*/
#if defined(CCP_SEND_QUEUE) && defined(CCP_SEND_QUEUE_OVERRUN_INDICATION)
                    }
#endif
#endif   /* CCP_SEND_SINGLE*/

                } /* q */
                /* Check for the next ODT to send */
            }

            o+=(uint8_T)CCP_MAX_ODT;
        }
#ifdef CCP_SEND_SINGLE
        ccpSampleAndSendNextDtm();
#endif
        //  ENABLE_INTERRUPT;
    }
    return;
}





/*--------------------------------------------------------------------------*/
/* Transmit */
/*--------------------------------------------------------------------------*/

/* Send a CRM, if no other message is pending */
static void ccpSendCrm( void )
{
    uint8_T res = CCP_NO_ERROR;

    if ((ccp.SendStatus&((uint8_T)CCP_SEND_PENDING)) != 0)
    {
        ccp.SendStatus |= CCP_CRM_REQUEST;
        /* ccpSendCallBack();   MC Used in HPU Autosar Project*/
    }
    else
    {
        //  GetResource(RES_CCP);
        res = ccpSend(ccp.Crm);
        //  ReleaseResource(RES_CCP);

        if(res == (uint8_T)CCP_NO_ERROR)
        {
            ccp.SendStatus |= (uint8_T)CCP_CRM_PENDING;
        }
        else
        {
            ccp.SendStatus |= (uint8_T)CCP_CRM_REQUEST;
            /* ccpSendCallBack(); MC Used in HPU Autosar Project */
        }
    }
    //ENABLE_INTERRUPT;
}

/* Send a DTM, if no other message is pending */
#ifdef CCP_DAQ
#ifndef CCP_SEND_QUEUE
static void ccpSendDtm(void) {

    //DISABLE_INTERRUPT;

    if (ccp.SendStatus&CCP_SEND_PENDING) {

        ccp.SendStatus |= CCP_DTM_REQUEST;
    }
    else
    {
        if(ccpSend(ccp.Dtm) == CCP_NO_ERROR)
        {
            ccp.SendStatus |= CCP_DTM_PENDING;
        }
        else
        {
            ccp.SendStatus |= CCP_DTM_REQUEST;
        }
    }

    //ENABLE_INTERRUPT;
}
#endif /* CCP_SEND_QUEUE*/
#endif   /*CCP_DAQ*/

#ifndef _BUILD_CCP_PROD_
/*--------------------------------------------------------------------------*/
/* Handle MTAs (Memory-Transfer-Address) */
/*--------------------------------------------------------------------------*/
/* Write n bytes */
static uint8_T ccpWriteMTA( uint8_T n, uint8_T size_p, uint8_T* d ) {

    uint8_T temp;
    /* EEPROM write access */
#ifdef CCP_WRITE_EEPROM
    CCP_BYTE r = ccpCheckWriteEEPROM(ccp.MTA[n],size_p,d);
    if (r) { /* EEPROM write access */
        ccp.MTA[n] += size_p;
        return r;
    }
#endif

    /* Checked ram memory write access */
#ifdef CCP_WRITE_PROTECTION
    if (!ccpCheckWriteAccess(ccp.MTA[n],size_p)) {
        ccp.MTA[n] += size_p;
        return CCP_WRITE_DENIED;
    }
#endif

    while (size_p>(uint8_T)0)
    {
        temp = *d;
        size_p--;
        *ccp.MTA[n] = temp;
        ccp.MTA[n]++;
        d++;
    }
    return (uint8_T)CCP_WRITE_OK;
}
#endif /* _BUILD_CCP_PROD_ */
/* Read n bytes */
static void ccpReadMTA( uint8_T n, uint8_T size_p, uint8_T* d ) {

    /* EEPROM read access */
    uint8_T *p;
    uint8_T i;

    p = ccp.MTA[n];



    for(i=(uint8_T)0; i< size_p; i++)
    {
        ccp.Crm[i+(uint8_T)3]= *p;
        p++;
    }
    //  #ifdef CCP_READ_EEPROM
    //    if (ccpCheckReadEEPROM(ccp.MTA[n],size,d)) {
    ccp.MTA[n] += size_p;
#ifdef __MWERKS__
    d;
#endif /* __MWERKS__ */
    return;


}

/*--------------------------------------------------------------------------*/
/* Command Processor */
/*--------------------------------------------------------------------------*/


static BlockDescription ccpInvalidateRegTag;

extern uint32_T __BOOT_START;
extern uint32_T __BOOT_SIZE;
extern uint32_T __CALIB_ROM_START;
extern uint32_T __CALIB_ROM_SIZE;
extern uint32_T __APP_START;
extern uint32_T __APP_SIZE;
extern uint32_T __BACKUP_START;
extern uint32_T __BACKUP_SIZE;

/*MI: flag for CCP connection status */
uint8_T CCPStatusFLG = 0;

static uint32_T dest;
static uint32_T gSize; /* oldName size -> MISRA 5.5 is renamed gSize */
static uint32_T source __attribute__ ((aligned(32)));

#ifdef CCP_SEED_KEY
static CCP_DWORD ccp_seed;
static CCP_BYTE  ccp_protStsToUnlock;
#endif

static void ccpCommand( CCP_BYTE com[] ) 
{
    uint8_T i;
    uint8_T cmd = com[0];
    uint8_T ctr = com[1];
    //uint16_T * stationAddr = (*(uint16_T*)&com[2]); /* Has to be Intel-Format ! */
    uint8_T disconnectCmd = com[2];
    uint8_T control_flow = (uint8_T)1;
    //uint16_T * disconnectStationAddr = (*(uint16_T*)&com[4]);
    //uint8_T mta;
    CCP_BYTE r;
    CCP_DWORD s;

    //int16_T retErase, retProg1, retProg2  = NO_ERROR;

    /* Handle CONNECT or TEST command */
    if ((cmd==(uint8_T)CC_CONNECT)||(cmd==(uint8_T)CC_TEST)) {

        /* This station */

        if (((*(uint16_T*)&com[2])== CCP_STATION_ADDR)|| ((*(uint16_T*)&com[2]) == CCP_BROADCAST_STATION_ADDR)) { /* This station */
            if (cmd==(uint8_T)CC_CONNECT) {
#ifdef CCP_DAQ
                if ((ccp.SessionStatus&SS_TMP_DISCONNECTED) == 0)
                {
                    ccpStopAllDaq();
                    ccp.SendStatus = (uint8_T) 0; /* Clear all transmission flags */
                }
#endif
                ccp.SessionStatus |= (uint8_T)SS_CONNECTED;
                ccp.SessionStatus &= (uint8_T)(~SS_TMP_DISCONNECTED);
                /* CCP connected */
                CCPStatusFLG = 1;
            }


            /* Responce */
            /* Station addresses in Intel Format */
            ccp.Crm[0] = (uint8_T)0xFF;
            ccp.Crm[1] = (uint8_T)CRC_OK;
            ccp.Crm[2] = (uint8_T)ctr;
            ccp.Crm[3] = (uint8_T)0x00;
            *(uint16_T *)&ccp.Crm[4] = (uint16_T)CCP_STATION_ADDR;
            *(uint16_T*)&ccp.Crm[6] = (uint16_T)CCP_BROADCAST_STATION_ADDR;

            /* responce */
            /* |||| */
        }

        /* Another station */
        else
        {

            /* If connected, temporary disconnect */
            if ((ccp.SessionStatus&(uint8_T)SS_CONNECTED) != 0)
            {
                ccp.SessionStatus &=(uint8_T)( ~SS_CONNECTED);
                ccp.SessionStatus |=(uint8_T) SS_TMP_DISCONNECTED;
            }
            /* no responce */
            control_flow=(uint8_T)0;//return; mod to 14.7 Rules Misra 2004
        }
    }


    /* Handle other commands only if connected */
    else if ((ccp.SessionStatus&(uint8_T)SS_CONNECTED) != 0)
    {
        /* prepare the responce */
        ccp.Crm[0] = (uint8_T)0xFF;
        ccp.Crm[1] = (uint8_T)CRC_OK;
        ccp.Crm[2] = (uint8_T)ctr;
        for (i=(uint8_T)3;i<(uint8_T)8;i++)
        {
            ccp.Crm[i] = (uint8_T)0x00;
        }
        switch (cmd)
        {

        case CC_DISCONNECT:
            ccp.SessionStatus &=(uint8_T)(~SS_CONNECTED);
            if (disconnectCmd==(uint8_T)0x00)
            { /* Temporary */
                ccp.SessionStatus |= (uint8_T)SS_TMP_DISCONNECTED;
            }
            else
            {           /* End of session */

#ifdef CCP_DAQ
                ccpStopAllDaq();
#endif
#ifdef CCP_SEED_KEY
                ccp.ProtectionStatus = 0; /* Clear Protection Status */
#endif
            }
            /* CCP disconnected */
            CCPStatusFLG = 0;
#ifdef _BUILD_VSRAMMGM_
            ccpSessionStatusTmp = ccp.SessionStatus;
            VSRAMMGM_Update();
#endif
            break;
#ifndef _BUILD_CCP_PROD_
        case CC_EXCHANGE_ID: /* Exchange Station Identifications */
            /*uint8_T ccpStationId[8];*/
            //uint8_T masterId = com[2];
            for(i=(uint8_T)0; ccpStationId[i]!=(uint8_T)0;i++)
            {
            }
            ccp.Crm[3] = i; /* Lenght of slave device identifier */
            ccp.Crm[4] =(uint8_T) 0;
            /* Build the Resource Availability and Protection Mask */
            ccp.Crm[5] = (uint8_T)PL_CAL; /* Default: Calibration available */
            ccp.Crm[6] = (uint8_T)0;      /* Default: No Protection */
#ifdef CCP_SEED_KEY
            ccp.Crm[6] |= (uint8_T)PL_CAL;   /* Protected Calibration */
#endif
#ifdef CCP_DAQ
            ccp.Crm[5] |= (uint8_T)PL_DAQ;     /* Data Acquisition */
#ifdef CCP_SEED_KEY
            ccp.Crm[6] |= (uint8_T)PL_DAQ;   /* Protected Data Acquisition */
#endif
#endif
#if defined(CCP_PROGRAM)
            ccp.Crm[5] |= (uint8_T)PL_PGM;     /* Flash Programming */
#ifdef CCP_SEED_KEY
            ccp.Crm[6] |= (uint8_T)PL_PGM;   /* Protected Flash Programming */
#endif
#endif
            ccp.Crm[7] = (uint8_T)CCP_DRIVER_VERSION; /* Driver version number */
            ccpSetMTA((uint8_T)0, (uint8_T *)ccpStationId);

            break;

#ifdef CCP_SEED_KEY

        case CC_GET_SEED: /* Get Seed for Key */
            //uint8_T privilegeLevel = com[2];
            ccp.Crm[3] = 0; /* Protection Status: No key required */
            *(CCP_DWORD*)&ccp.Crm[4] = 0;
#ifdef CCP_SEED_KEY
            /* Keys required for CAL or PGM */
            switch (privilegeLevel) {
            case PL_CAL:
                ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_CAL)); /* Protection Status */
                *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_CAL);
                break;
            case PL_PGM:
                ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_PGM)); /* Protection Status */
                *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_PGM);
                break;
            case PL_DAQ:
                ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_DAQ)); /* Protection Status */
                *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_DAQ);
                break;
            default:
                ccp.Crm[1] = CRC_CMD_SYNTAX;
                /* Error */
            }
#endif /* CCP_SEED_KEY riga 623*/

            break;

            case CC_UNLOCK: /* Unlock Protection */

                //uint8_T key = com[2]; /* Key may be up to 6 Bytes */
                /* Check key */
                ccp.ProtectionStatus |= ccpUnlock(&com[2]); /* Reset the appropriate resource protection mask bit */
                ccp.Crm[3] = ccp.ProtectionStatus; /* Current Protection Status */

                break;

#endif /* CCP_SEED_KEY riga 616*/
#else
            /* CCP PRODUCTION SETUP */
            case CC_EXCHANGE_ID: /* Exchange Station Identifications */
            {
                /*uint8_t ccpStationId[8];*/
                //uint8_t masterId = com[2];
                for(i=0; ccpStationId[i]!=0;i++) ;
                ccp.Crm[3] = 0; /* Lenght of slave device identifier */
                ccp.Crm[4] = 0;

                /* Build the Resource Availability and Protection Mask */

                ccp.Crm[5] = 0;      /* Default: Calibration NOT available */
                ccp.Crm[6] = 0;      /* Default: No Protection */

#ifdef CCP_CALPAGE
                ccp.Crm[5] = PL_CAL;      /* Calibration Available */
#ifdef CCP_SEED_KEY
                    ccp.Crm[6] |= PL_CAL; /* Protected Calibration */
#endif
#endif
#ifdef CCP_DAQ
                ccp.Crm[5] |= PL_DAQ;     /* Data Acquisition Available */
#ifdef CCP_SEED_KEY
                  ccp.Crm[6] |= PL_DAQ;   /* Protected Data Acquisition */
#endif
#endif
#ifdef CCP_PROGRAM
                ccp.Crm[5] |= PL_PGM;     /* Flash Programming Available */
#ifdef CCP_SEED_KEY
                  ccp.Crm[6] |= PL_PGM;   /* Protected Flash Programming */
#endif
#endif
                ccp.Crm[7] = CCP_DRIVER_VERSION; /* Driver version number */
                ccpSetMTA(0,(uint8_t *)ccpStationId);
            }
            break;

#ifdef CCP_SEED_KEY

            case CC_GET_SEED: /* Get Seed for Key */
            {
              uint8_t privilegeLevel = com[2];
              ccp.Crm[3] = 0; /* Protection Status: No key required */
              *(CCP_DWORD*)&ccp.Crm[4] = 0;

#ifdef CCP_SEED_KEY
                /* Keys required for CAL or PGM */
                switch (privilegeLevel) {
                  case PL_CAL:
                  {
                    ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_CAL)); /* Protection Status */
                    *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_CAL);
                  }
                  break;
                  case PL_PGM:
                  {
                    ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_PGM)); /* Protection Status */
                    *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_PGM);
                  }
                  break;
                  case PL_DAQ:
                  {
                    ccp.Crm[3] = (0==(ccp.ProtectionStatus&PL_DAQ)); /* Protection Status */
                    *(CCP_DWORD*)&ccp.Crm[4] = ccpGetSeed(PL_DAQ);
                  }
                  break;
                  default:
                    ccp.Crm[1] = CRC_CMD_SYNTAX;
                    /* Error */
                }
#endif /* CCP_SEED_KEY riga 623*/
            }
              break;
        
              case CC_UNLOCK: /* Unlock Protection */
              {
                  //uint8_t key = com[2]; /* Key may be up to 6 Bytes */
                  /* Check key */
                  CCP_BYTE unlocked =0;
                  
                  unlocked = ccpUnlock(&com[2]);
                  ccp.ProtectionStatus |= unlocked; /* Reset the appropriate resource protection mask bit */
        
                  if(unlocked == 0)
                  {
                    ccp.Crm[1] = CRC_ACCESS_LOCKED; /*Return ERROR code UNLOCKED*/
                  }
                  
                  ccp.Crm[3] = ccp.ProtectionStatus; /* Current Protection Status */
        
              }
              break;
        
#endif /* CCP_SEED_KEY riga 616*/
        
            
#endif/*_BUILD_CCP_PROD_*/


            case CC_SET_MTA: /* Set transfer address */
                //mta = com[2];
                //addrExt = com[3];
                //addr = (*(CCP_DWORD*)&com[4]);
                if (com[2] < (CCP_MAX_MTA-(uint8_T)1))
                {
                    ccpSetMTA(com[2],(uint8_T *)ccpGetPointer(com[3],(*(CCP_DWORD*)&com[4])));
                }
                else
                {
                    ccp.Crm[1] = (uint8_T)CRC_OUT_OF_RANGE;
                }
                break;

#ifndef _BUILD_CCP_PROD_
            case CC_DNLOAD: /* Download */

                //size = com[2];
#ifdef CCP_SEED_KEY
                if (!(ccp.ProtectionStatus&PL_CAL))
                {
                    ccp.Crm[1] = CRC_ACCESS_DENIED;
                    r = 0;
                }
                else  /*CCP_SEED_KEY*/
#endif
                    r = ccpWriteMTA((uint8_T)0,com[2],&com[3]);
                // tempAddr= ccp.MTA;
                *(uint32_T *)&ccp.Crm[4] = (uint32_T )ccp.MTA[0];
                if (r==(uint8_T)CCP_WRITE_PENDING)
                {
                    control_flow=(uint8_T)0;
                }//  return; /* EEPROM write pending */mod to misra 14.7
                else
                {
                    if ((r==(uint8_T)CCP_WRITE_DENIED)||(r==(uint8_T)CCP_WRITE_ERROR))
                    {
                        ccp.Crm[1] = CRC_ACCESS_DENIED; /* No write access */
                    }

                }
                break;
            case CC_DNLOAD6: /* Download */
#ifdef CCP_SEED_KEY
                if (!(ccp.ProtectionStatus&PL_CAL))
                {
                    ccp.Crm[1] = (uint8_T)CRC_ACCESS_DENIED;
                    r = 0;
                }
                else
#endif
                    r = ccpWriteMTA((uint8_T)0,(uint8_T)6,&com[2]);
                *(uint32_T *)&ccp.Crm[4] = (uint32_T )ccp.MTA[0];         /* added by Soro & Ibba : TBC*/
#ifdef CCP_STANDARD
                ccpGetMTA0((CCP_BYTE*)&ccp.Crm[3],(CCP_DWORD*)&ccp.Crm[4]);
#endif
                if (r==(uint8_T)CCP_WRITE_PENDING)
                {
                    control_flow=(uint8_T)0;
                }//  return; /* EEPROM write pending */mod to misra 14.7
                else
                {
                    if ((r==CCP_WRITE_DENIED)||(r==CCP_WRITE_ERROR))
                    {
                        ccp.Crm[1] = (uint8_T)CRC_ACCESS_DENIED;/* No write access */
                    }
                }
                break;
#endif/*_BUILD_CCP_PROD_*/

            case CC_UPLOAD: /* Upload */
            {
                uint8_T size2= com[2];

                ccpReadMTA((uint8_T)0,size2,&ccp.Crm[3]);
            }
            break;

            case CC_SHORT_UPLOAD: /* Upload with Address */

                //size = com[2];
                //addrExt = com[3];
                //addr = (*(CCP_DWORD*)&com[4]);
                ccpSetMTA(CCP_INTERNAL_MTA,(uint8_T *) ccpGetPointer(com[3],(*(CCP_DWORD*)&com[4])));
                ccpReadMTA((uint8_T)CCP_INTERNAL_MTA,com[2],&ccp.Crm[3]);
                break;

            case CC_GET_DAQ_SIZE: /* Return the size of a DAQ list and clear */

                //uint8_T daqList = com[2];
                //uint32_T * daqId = (*(CCP_DWORD*)&com[4]);
#ifdef CCP_DAQ
                ccpStopDaq(com[4]); /* Stop this daq list */
                ccp.Crm[3] = ccpClearDaqList(com[2]); /* Number of  ODTs */
                ccp.Crm[4] = com[2]*CCP_MAX_ODT; /* PID of the first ODT */
#else
                ccp.Crm[3] = 0;
                ccp.Crm[4] = 0;
#endif

                break;

#ifdef CCP_DAQ

            case CC_SET_DAQ_PTR: /* Set DAQ pointer */

                //uint8_T comDaq = com[2];
                //uint8_T comOdt = com[3];
                //uint8_T comIdx = com[4];

                if ((com[2]>=CCP_MAX_DAQ)||(com[3]>=CCP_MAX_ODT)||(com[4]>(uint8_T)7))
                {
                    ccp.Crm[1] = (uint8_T)CRC_CMD_SYNTAX;
                    ccp.DaqListPtr = 0;
                }
                else
                {
                    ccp.DaqListPtr = &ccp.DaqList[com[2]].odt[com[3]][com[4]];
                }

                break;

            case CC_WRITE_DAQ: /* Write DAQ entry */

                //uint8_T writeDaqSize = com[2];
                //uint8_T writeDaqAddrExt = com[3];
                //uint32_T * writeDaqAddr = (*(CCP_DWORD*)&com[4]);
                if (
#ifdef CCP_ODT_ENTRY_SIZE
#ifdef CCP_DOUBLE_FLOAT
                        (com[2]!=8) &&
#endif
                        ( (com[2]!=1U) && (com[2]!=2U) && (com[2]!=(uint8_T)4U))
#else
                        com[2]!=1
#endif
                        || (ccp.DaqListPtr==0U))
                {
                    ccp.Crm[1] = (uint8_T) CRC_CMD_SYNTAX;
                }
                else
                {
#ifdef CCP_DAQ_BASE_ADDR
                    ccp.DaqListPtr->ptr = ccpGetDaqPointer(com[3],(*(CCP_DWORD*)&com[4]));
#else
                    ccp.DaqListPtr->ptr = (CCP_DAQBYTEPTR)ccpGetPointer(com[3],(*(CCP_DWORD*)&com[4]));
#endif
#ifdef CCP_ODT_ENTRY_SIZE
                    ccp.DaqListPtr->siz = com[2];
#endif
                }

                break;

            case CC_START_STOP: /* Cyclic aquisition start/stop */

                /*        uint8_T ssCmd = com[2];  @/@*$ Start or Stop $*@/@
 -         uint8_T ssDaq = com[3];  @/@*$ DAQ list $*@/@
 -         uint8_T ssLast = com[4];  @/@*$ Last ODT to send $*@/@
 -         uint8_T ssEventChannel = com[5];  @/@*$ Event Channel Number $*@/@
 -         uint16_T ssPrescaler = (*(CCP_WORD*)&com[6]); @/@*$ Prescaler $*@/@*/

#ifdef CCP_SEED_KEY
                if (!(ccp.ProtectionStatus&PL_DAQ))
                {
                    ccp.Crm[1] = CRC_ACCESS_DENIED;                /****?????????****/
                }
                else
#endif
                    if ((ccp.SessionStatus&(uint8_T)SS_DAQ) == 0)
                    { /* Not initialized */
                        ccp.Crm[1] = CRC_DAQ_INIT_REQUEST;
                    }
                    else
                    {
                        switch (com[2])
                        {
                        case 0: /* stop */
                            ccpStopDaq(com[3]);
                            break;
                        case 1: /* start */
                            ccpPrepareDaq(com[3],com[4],com[5],(*(CCP_WORD*)&com[6]));
                            ccpStartDaq(com[3]);
                            break;
                        case 2: /* prepare */
                            ccpPrepareDaq(com[3],com[4],com[5],(*(CCP_WORD*)&com[6]));
                            break;
                        default:
                            ccp.Crm[1] = CRC_CMD_SYNTAX;
                            break;
                        }
                    }
                break;
            case CC_START_STOP_ALL: /* Cyclic aquisition start/stop */
                //uint8_T ssCmd = com[2];  /* Start or Stop */

#ifdef CCP_SEED_KEY
                if ((ccp.ProtectionStatus&PL_DAQ) == 0)
                    ccp.Crm[1] = CRC_ACCESS_DENIED;
                else
#endif
                    if ((ccp.SessionStatus&SS_DAQ) == 0)
                    { /* Not initialized */
                        ccp.Crm[1] = CRC_DAQ_INIT_REQUEST;
                    }
                    else
                    {
                        switch (com[2])
                        {
                        case 0: /* Stop */
                            ccpStopAllDaq();
                            break;
                        case 1: /* Start */
                            ccpStartAllPreparedDaq();
                            break;
                        default:
                            ccp.Crm[1] = CRC_CMD_SYNTAX;
                            break;
                        }
                    }
                break;


#endif /*CCP_DAQ*/


#ifdef CCP_CHECKSUM

            case CC_BUILD_CHKSUM: /* Build Checksum */
            {
                uint32_T sum;
                uint32_T destIndex;             /* destination address index */

                /* Initialize Responce */
                ccp.Crm[3] = sizeof(CCP_CHECKSUM_TYPE); /* Checksum Size */
#ifdef CCP_CHECKSUM_CCITT               /* Checksum */
                *(CCP_DWORD*)&ccp.Crm[4] = 0xFFFFFFFF;
#else
                *(CCP_DWORD*)&ccp.Crm[4] = 0U;
#endif
                ccp.MTA[CCP_INTERNAL_MTA] = ccp.MTA[0];        /* MTA[0] is not affected */
#ifdef CCP_MOTOROLA
                s = (*(CCP_WORD*)&com[4]) | ((*(CCP_WORD*)&com[2])<<16);
#else
                s = (*(CCP_WORD*)&com[2]) | ((*(CCP_WORD*)&com[4])<<16);
#endif
#ifndef CCP_CPUTYPE_32BIT
                if ((s&0xffff0000u) != 0) { ccp.Crm[1] = CRC_OUT_OF_RANGE;} /* Range, max. 64K-1 on <32Bit CPUs */
                ccp.CheckSumSize = (CCP_WORD)s;
#else
                ccp.CheckSumSize = s;
#endif

                sum = 0U;

                /* word by word checksum */
                for (destIndex = 0U; destIndex < (s / (sizeof(uint32_T))); destIndex++)
                {
                    sum +=*((uint32_T *)ccp.MTA[0]);
                    ccp.MTA[0] += sizeof(uint32_T);
                }
                ccp.Crm[4]= (sum >> 24) & 0x000000ff;
                ccp.Crm[5]= (sum >> 16) & 0x000000ff;
                ccp.Crm[6]= (sum >> 8 ) & 0x000000ff;
                ccp.Crm[7]= (sum      ) & 0x000000ff;
            }
            break;

#endif /* CCP_CHECKSUM */
            /* Flash Programming */
#ifdef CCP_PROGRAM
#ifndef _BUILD_CCP_PROD_
            case CC_CLEAR_MEMORY: /* Clear Memory */
            {

#ifdef CCP_SEED_KEY
                if ((ccp.ProtectionStatus&PL_PGM) == 0)
                    ccp.Crm[1] = CRC_ACCESS_DENIED;
                else
#endif
                    /* Clear flash sector */
#ifdef CCP_MOTOROLA
                    s = (*(uint16_T*)&com[4]) | ((*(CCP_WORD*)&com[2])<<16);
#else
                s = (*(uint16_T*)&com[2]) | ((*(CCP_WORD*)&com[4])<<16);
#endif /* CCP_MOTOROLA */
                ccp_LastMTA=(uint32_T )ccp.MTA[0];

                /* CCP region Programming state machine */
                {
                    if(ccp_LastMTA == (uint32_T)(&__BOOT_START))
                    {
#ifdef _BUILD_WDT_SBC_
                        WDT_SetPendingOperation(WDT_PENDING_CCP_CLEAR_MEMORY_BOOT);
#else
                        ccpClearMemoryBoot();
#endif
                        /* set flag to not send answer now (exit from function) */
                        control_flow = (uint8_T)0;
#if 0
                        boot_flashing_rqst = TRUE;
                        DisableAllInterrupts();
                        /* GK! - change WDT management to PWM mode? */

                        ccp_LastMTA = (uint32_T)(&__BACKUP_START);

                        retErase = FLASH_Erase(ccp_LastMTA, s,(void(*)(void))NULL_CALLBACK_AKH);
                        if (retErase!=NO_ERROR)  /*((void *)0xFFFFFFFF) */
                        {
                            //ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
                            ret_erase_backup_boot = C90FL_ERROR_EGOOD;
                        }
                        else
                        {
                            ret_erase_backup_boot = C90FL_OK;
                        }

                        dest = ((uint32_T)(&__CALIB_ROM_START)+(uint32_T)(&__CALIB_ROM_SIZE))- sizeof(BlockDescription);
                        ccpInvalidateRegTag = *((BlockDescription*) dest);
                        ccpInvalidateRegTag.validMemoryRegion = (uint32_T)0;
                        source = (uint32_T)&ccpInvalidateRegTag;
                        gSize = sizeof(BlockDescription);

                        retProg1 = FLASH_Program(dest, gSize, source);
                        if (retProg1==C90FL_OK)
                        {
                            /* verifying previous program... */
                            retProg1 = FLASH_ProgramVerify(dest, gSize, source);
                        }

                        dest = ((uint32_T)(&__APP_START)+(uint32_T)(&__APP_SIZE))-sizeof(BlockDescription);
                        ccpInvalidateRegTag = *((BlockDescription*) dest);
                        ccpInvalidateRegTag.validMemoryRegion = (uint32_T)0;
                        source = (uint32_T)&ccpInvalidateRegTag;
                        gSize = sizeof(BlockDescription);

                        retProg2 = FLASH_Program(dest, gSize, source);
                        if (retProg2==C90FL_OK)
                        {
                            /* verifying previous program... */
                            retProg2 = FLASH_ProgramVerify(dest, gSize, source);
                        }

                        /*  ccpSendCrm(); */
                        /*  while (Can_GetTxStatus(CCP_CAN)==(int16_T)CAN_TX_BUSY) {} */

                        /* backup erasing and calib+appl invalidation shall be exited successful to go on...*/
                        if (retErase || retProg1 || retProg2)
                        {
                            ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
                            /* In case of error enable ISR previusly disable */
                            EnableAllInterrupts();
                        }
                        else
                        {
                            comm_protocol = (uint32_T)3;
                            /* store also SessionStatus and Crm in VSRAM for delayed response after erase */
                            ccpSessionStatusTmp = ccp.SessionStatus;
                            memcpy(ccpCrmTmp, ccp.Crm, 8);
#ifdef _BUILD_VSRAMMGM_
                            VSRAMMGM_Update();
#endif

                            Delay_ms((uint32_T)2);

                            SYS_SwRST();
                        }
#endif
                    }
                    //!! ap: to be addd a check of the size
                    else if(ccp_LastMTA == (uint32_T)(&__CALIB_ROM_START))
                    {
#ifdef _BUILD_WDT_SBC_
                        WDT_SetPendingOperation(WDT_PENDING_CCP_CLEAR_MEMORY_CALIB);
#else
                        ccpClearMemoryCalib();
#endif
                        /* set flag to not send answer now (exit from function) */
                        control_flow = (uint8_T)0;
                    }
                    else if(ccp_LastMTA == (uint32_T)(&__APP_START))
                    {
#ifdef _BUILD_WDT_SBC_
                        WDT_SetPendingOperation(WDT_PENDING_CCP_CLEAR_MEMORY_APPL);
#else
                        ccpClearMemoryAppl();
#endif
                        /* set flag to not send answer now (exit from function) */
                        control_flow = (uint8_T)0;
                    }
                    else
                    {
                        ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
                        EnableAllInterrupts();
                    }
                }
            }
            break;
#endif
#endif /* CCP_PROGRAM */

#ifdef CCP_CALPAGE

            case CC_SET_CAL_PAGE: /* Select Calibration Page */

                ccpSetCalPage((CCP_DWORD)ccp.MTA[0]);

                break;

            case CC_GET_CAL_PAGE: /* Get Active Calibration Page */

                ccp.Crm[3] = 0U; /* Address Extension */
                *(CCP_DWORD*)&ccp.Crm[4] = ccpGetCalPage(); /* Address */

                break;

#endif /* CCP_CALPAGE */

#ifdef CCP_SET_SESSION_STATUS

            case CC_SET_S_STATUS: /* Set Session Status */
                /* Set Resume and Store mode in SessionStatus */
                ccp.SessionStatus &=(uint8_T) ~(SS_STORE|SS_RESUME);
                ccp.SessionStatus |= (com[2]&(SS_STORE|SS_RESUME));

                /* Save as UserSessionStatus */
                ccp.UserSessionStatus = com[2];
                break;

            case CC_GET_S_STATUS: /* Get Session Status */

                ccp.Crm[3] = (uint8_T)ccp.UserSessionStatus;
                ccp.Crm[4] = (uint8_T)0; /* No additional status */

                break;

#endif /* CCP_SET_SESSION_STATUS */

            case CC_GET_CCP_VERSION: /* Get Version */

                ccp.Crm[3] = (uint8_T)CCP_VERSION_MAJOR;
                ccp.Crm[4] = (uint8_T)CCP_VERSION_MINOR;

                break;

            default: /* unknown */

                ccp.Crm[1] = (uint8_T)CRC_CMD_UNKNOWN;

                break;

        } /* switch */
        /* Responce */
        /* |||| */
    }

    /* Not connected */
    else
    {
        /* No responce */
        control_flow=(uint8_T)0;//return;mod to misra14.7
    }
    if (control_flow!=(uint8_T)0)
    {
        ccpSendCrm();
    }
}

#ifdef CCP_PROGRAM
/*--------------------------------------------------------------------------*/
/* ccpClearMemoryBoot */
/*--------------------------------------------------------------------------*/
void ccpClearMemoryBoot(void)
{
    int16_T retErase, retProg  = NO_ERROR;
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFF;
    uint8_T Debug_ReadGpio85_Callback;


    if (checkEcuState()==NO_ERROR)
    {
        DisableAllInterrupts();

        boot_flashing_rqst = TRUE;

        ccp_LastMTA = (uint32_T)(&__BACKUP_START);

        retErase = FLASH_Erase(ccp_LastMTA, (uint32_T)(&__BACKUP_SIZE), FLASH_ERASE_NO_CBK);
        if (retErase!=NO_ERROR)  /*((void *)0xFFFFFFFF) */
        {
            //ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
            ret_erase_backup_boot = C55_ERROR_EGOOD;
        }
        else
        {
            ret_erase_backup_boot = C55_OK;
        }   

        dest = ((uint32_T)(&__APP_START)+(uint32_T)(&__APP_SIZE))-sizeof(BlockDescription);
        ccpInvalidateRegTag = *((BlockDescription*) dest);
        ccpInvalidateRegTag.validMemoryRegion = (uint32_T)0;
        source = (uint32_T)&ccpInvalidateRegTag;
        gSize = sizeof(BlockDescription);

        retProg = FLASH_Program(dest, gSize, source);
        if (retProg==C55_OK)  
        {
            /* verifying previous program... */
            retProg = FLASH_ProgramVerify(dest, gSize, source);                    
        }

        /* backup erasing and appl invalidation shall be exited successful to go on...*/
        if (retErase || retProg) 
        {                    
            ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
            ccpSendCrm();
#ifdef _BUILD_WDT_SBC_
            SYS_SwRST();
#else
            SYS_SwRST();
#endif
        }
        else
        {
            comm_protocol = (uint32_T)3;
            memcpy(ccpCrmTmp, ccp.Crm, 8);
#ifdef _BUILD_VSRAMMGM_
            /* store also SessionStatus and Crm in VSRAM for delayed response after erase */
            ccpSessionStatusTmp = ccp.SessionStatus;
            VSRAMMGM_Update();
#endif


#ifdef _BUILD_WDT_SBC_
            SYS_SwRST();
#else
            SYS_SwRST();
#endif
        }
    }
}

/*--------------------------------------------------------------------------*/
/* ccpClearMemoryCalib */
/*--------------------------------------------------------------------------*/
void ccpClearMemoryCalib(void)
{
    int16_T retProg = NO_ERROR;

    dest = (uint32_T)(&__CALIB_ROM_START)+(uint32_T)(&__CALIB_ROM_SIZE)-sizeof(BlockDescription);
    ccpInvalidateRegTag = *((BlockDescription*) dest);
    ccpInvalidateRegTag.validMemoryRegion = (uint32_T)0;
    source = (uint32_T)&ccpInvalidateRegTag;
    gSize = sizeof(BlockDescription);

    if (checkEcuState()==NO_ERROR)
    {
        DisableAllInterrupts();

        retProg = FLASH_Program(dest, gSize, source);
        if (retProg == C55_OK)  
        {
            /* verifying previous program... */
            retProg = FLASH_ProgramVerify(dest, gSize, source);
        }

        if (retProg==C55_OK) /* Calib  invalidation shall be exited successful to go on...*/
        {
            comm_protocol = (uint32_T)3;
            memcpy(ccpCrmTmp, ccp.Crm, 8);
#ifdef _BUILD_VSRAMMGM_
            /* store also SessionStatus and Crm in VSRAM for delayed response after erase */
            ccpSessionStatusTmp = ccp.SessionStatus;
            VSRAMMGM_Update();
#endif

#ifdef _BUILD_WDT_SBC_
            SYS_SwRST();
#else
            SYS_SwRST();
#endif
        }
        else
        {
            ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
            ccpSendCrm();
#ifdef _BUILD_WDT_SBC_
            SYS_SwRST();
#else
            SYS_SwRST();
#endif

        }
    }
}

/*--------------------------------------------------------------------------*/
/* ccpClearMemoryBoot */
/*--------------------------------------------------------------------------*/
void ccpClearMemoryAppl(void)
{
    int16_T retProg = NO_ERROR;

    dest = (uint32_T)(&__APP_START)+(uint32_T)(&__APP_SIZE)-sizeof(BlockDescription);
    ccpInvalidateRegTag = *((BlockDescription*) dest);
    ccpInvalidateRegTag.validMemoryRegion = (uint32_T)0;
    source = (uint32_T)&ccpInvalidateRegTag;
    gSize = sizeof(BlockDescription);

    if (checkEcuState()==NO_ERROR)
    {
        DisableAllInterrupts();

        //!! ap: just invalidate application region. erase and program performed in boot mode
        retProg = FLASH_Program(dest, gSize, source);
        if (retProg == C55_OK)  
        {
            /* verifying previous program... */
            retProg = FLASH_ProgramVerify(dest, gSize, source);
        }

        if (retProg == C55_OK) /* Appl  invalidation shall be exited successful to go on...*/
        {
            comm_protocol = (uint32_T)3;
            /* store also SessionStatus and Crm in VSRAM for delayed response after erase */
            memcpy(ccpCrmTmp, ccp.Crm, 8);
#ifdef _BUILD_VSRAMMGM_
            ccpSessionStatusTmp = ccp.SessionStatus;
            VSRAMMGM_Update();
#endif

#ifdef _BUILD_WDT_SBC_
            SYS_SwRST();
#else
            SYS_SwRST();
#endif
        }
        else
        {
            ccp.Crm[1] = CRC_OUT_OF_RANGE; /* to be controlled */
            ccpSendCrm();

#ifdef _BUILD_WDT_SBC_
            SYS_SwRST();
#else
            SYS_SwRST();
#endif
        }
    }
}
#endif //CCP_PROGRAM

/*--------------------------------------------------------------------------*/
/* Send notification callback */
/* ccpSend must not fail, when called from this function */
/*--------------------------------------------------------------------------*/
uint8_T  ccpSendCallBack( void )
{
    uint32_T i = 0;
    uint8_T returncode=0U;

    /* Clear all pending flags, except for CCP_CMD_PENDING */
    ccp.SendStatus &=(uint8_T)( ~CCP_SEND_PENDING);

    /* Send a CRM message */
    if ((ccp.SendStatus&CCP_CRM_REQUEST) != 0)
    {
        ccp.SendStatus &=(uint8_T)( ~CCP_CRM_REQUEST);
        ccpSendCrm();

        //ec akhela     ActivateTask(CCPBackgroundTaskID);

        //RST_PORT_BIT(2); /* Timingtest */
        returncode=1U;//return 1; mod to misra 14.9
    }
    if (returncode == 0U)
    {
        /* Send a DAQ message */
#ifdef CCP_DAQ
        if ((ccp.SessionStatus&SS_RUN) != 0)
        {

            /* Send a  DAQ message (DTM) from the queue */
#ifdef CCP_SEND_QUEUE
            {

                //   GetResource(RES_CCP)

                for (i = 0; i < ccpSendAtOnce; i++)
                {
                    if (ccpQueueSend() == 0)
                    {
                        break;
                    }
                }

                //    ReleaseResource(RES_CCP)


            }
            /* Send a pending DAQ message (DTM) */
#else
            if(returncode==0)
            {
                if (ccp.SendStatus&CCP_DTM_REQUEST)
                {
                    ccp.SendStatus &= ~CCP_DTM_REQUEST;
                    ccpSendDtm();
                    returncode=1;//return 1;misra 14.9
                }
            }

#endif /*CCP_SEND_QUEUE*/

        }

#endif  /*CCP_DAQ*/
    }


    return returncode;
}

/*--------------------------------------------------------------------------*/
/* Initialization */
/*--------------------------------------------------------------------------*/

void ccp_init( void ) {
    /* Initialize all CCP variables to zero */
    uint8_T * p;
    uint8_T * pl;
    p = (uint8_T *)&ccp;
    pl = p+sizeof(ccp);
    while (p<pl)
    {
        *p = 0;
        p++;
    }

    /* initialize ccpInvalidateRegTag */
    p = (uint8_T *)&ccpInvalidateRegTag;
    pl = p+sizeof(BlockDescription);
    while (p<pl)
    {
        *p = 0;
        p++;
    }
    ccp_LastMTA = (uint32_T)0;
    buffsel=CCP_START_OF_RANGE;
    ccp_init_done = 1u;
}


#ifdef CCP_SEED_KEY

CCP_DWORD ccpGetSeed( CCP_BYTE resourceMask )
{
uint64_T abstime = 0u;
#ifdef _BUILD_TIMING_
    TIMING_GetAbsTimer(&abstime);
    ccp_seed = (uint32_T)(abstime & 0xFFFFFFFFu);
#else
    ccp_seed = getSpecReg32SPR_TBL();
#endif

    ccp_protStsToUnlock = resourceMask;

    return (CCP_DWORD) ccp_seed;
}

CCP_BYTE ccpUnlock( CCP_BYTE *key )
{
    static volatile CCP_DWORD keyVal = 0;
    
    uint8_t i;
    CCP_BYTE resUnlocking = TRUE;
    uint8_t * ccp_SeedPtr = (uint8_t*)&ccp_seed;
    
//    keyVal = * ((CCP_DWORD*) key);
    keyVal = 0;
    keyVal |= (key[0]<<24) & 0xFF000000;
    keyVal |= (key[1]<<16) & 0x00FF0000;
    keyVal |= (key[2]<<8)  & 0x0000FF00;
    keyVal |= (key[3])     & 0x000000FF;

    /* Key Calculation */
    for(i=0; i < 4; i++)
    {
        if(key[i] != (0xCC ^ (ccp_SeedPtr[i]))) /*KEY Verification!*/
        {
            resUnlocking = FALSE;
        }
    }
    
    if(resUnlocking == TRUE)
    {
        /*decide which unlock*/
        resUnlocking = 0;
        if(0 != (ccp_protStsToUnlock & PL_CAL))
        {
          #ifdef CCP_SEED_KEY_CAL
            resUnlocking |= PL_CAL;
          #else
            resUnlocking |= 0;
          #endif
        }

        if(0 != (ccp_protStsToUnlock & PL_DAQ))
        {
          #ifdef CCP_SEED_KEY_DAQ
            resUnlocking |= PL_DAQ;
          #else
            resUnlocking |= 0;
          #endif
        }

        if(0 != (ccp_protStsToUnlock & PL_PGM))
        {
          #ifdef CCP_SEED_KEY_PGM
            resUnlocking |= PL_PGM;
          #else
            resUnlocking |= 0;
          #endif
        }

        ccp_seed = 0;            /*seed have to change every new request*/
        ccp_protStsToUnlock = 0; /* the seed must be recalculated for a new unlock request 
                                    and a new sequence SEED/KEY must be done*/
    }
    else
    {
        /*Do NOT unlock*/
        resUnlocking = 0;
        ccp_seed = 0;            /*seed have to change every new request*/
        ccp_protStsToUnlock = 0; /* the seed must be recalculated for a new unlock request 
                                    and a new sequence SEED/KEY must be done*/
    }
     
        
    return (CCP_BYTE) resUnlocking;
}

#endif


#pragma ghs endnomisra

#endif /* BUILD_CCP_ */


