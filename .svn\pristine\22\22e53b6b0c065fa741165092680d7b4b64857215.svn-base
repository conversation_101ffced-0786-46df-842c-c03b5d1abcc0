/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_atom.h
 * @brief   SPC5xx GTM ATOM header file.
 *
 * @addtogroup ATOM
 * @{
 */

#ifndef _GTM_ATOM_H_
#define _GTM_ATOM_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"

/*lint -e621*/

/**
 * @name    ATOM definitions
 * @{
 */

/** Number of channels */
#define SPC5_GTM_ATOM_CHANNELS                                8U

/** ATOM channel 0 identifier */
#define ATOM_CHANNEL0                                         0U
/** ATOM channel 1 identifier */
#define ATOM_CHANNEL1                                         1U
/** ATOM channel 2 identifier */
#define ATOM_CHANNEL2                                         2U
/** ATOM channel 3 identifier */
#define ATOM_CHANNEL3                                         3U
/** ATOM channel 4 identifier */
#define ATOM_CHANNEL4                                         4U
/** ATOM channel 5 identifier */
#define ATOM_CHANNEL5                                         5U
/** ATOM channel 6 identifier */
#define ATOM_CHANNEL6                                         6U
/** ATOM channel 7 identifier */
#define ATOM_CHANNEL7                                         7U

/** ATOM channel operating mode immediate */
#define SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE                   0U
/** ATOM channel operating mode compare */
#define SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE                     1U
/** ATOM channel operating mode PWM */
#define SPC5_GTM_ATOM_OUTPUT_MODE_PWM                         2U
/** ATOM channel operating mode serial */
#define SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL                      3U

/** Do not get data from ARU */
#define SPC5_GTM_ATOM_ARU_MODE_NONE                           0U

/** Get both word from ARU */
#define SPC5_GTM_ATOM_ARU_MODE_BOTH                           0U
/** Get low word from ARU */
#define SPC5_GTM_ATOM_ARU_MODE_LOW                            1U
/** Get high word from ARU */
#define SPC5_GTM_ATOM_ARU_MODE_HIGH                           2U

/** ACB ARU bit */
#define SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED              0U
#define SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL                    1U

/** Default signal level high */
#define SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH                       1U
/** Default signal level low */
#define SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW                        0U

/** TBU_TS1 selected for comparison */
#define SPC5_GTM_ATOM_COMPARE_USE_TBU_TS1                     0U
/** TBU_TS2 selected for comparison */
#define SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2                     1U

/** Greater/equal compare against TBU time base values (TBU_TS1/2 >= CM0/1) */
#define SPC5_GTM_ATOM_COMPARE_STRATEGY_GREATER_EQUAL          0U
/** Less/equal compare against TBU time base values (TBU_TS1/2 <= CM0/1) */
#define SPC5_GTM_ATOM_COMPARE_STRATEGY_LESS_EQUAL             1U

/** No signal level change at output. */
#define SPC5_GTM_ATOM_SIGNAL_LEVEL_CRTL_NO_CHANGE             0U
/* Set output signal level to 1 when SL bit = 0 else output signal level to 0. */
#define SPC5_GTM_ATOM_SIGNAL_LEVEL_CRTL_HIGH                  1U
/** Set output signal level to 0 when SL bit = 0 else output signal level to 1. */
#define SPC5_GTM_ATOM_SIGNAL_LEVEL_CRTL_LOW                   2U
/** Toggle output signal level. */
#define SPC5_GTM_ATOM_SIGNAL_LEVEL_CRTL_TOGGLE                3U



/**
 * 000: Compare in CCU0 and CCU1 in parallel, disable the CCUx on a compare match on either of compare units.
 * Use TBU_TS0 in CCU0 and TBU_TS1 or TBU_TS2 in CCU1.
 */
#define SPC5_GTM_ATOM_COMPARE_CCU0_TS0_CCU1_TS12_PARALLEL_0   0U
/**
 * 001: Compare in CCU0 and CCU1 in parallel, disable the CCUx on a compare match on either compare units.
 * Use TBU_TS0 in CCU0 and TBU_TS1 or TBU_TS2 in CCU1.
 */
#define SPC5_GTM_ATOM_COMPARE_CCU0_TS0_CCU1_TS12_PARALLEL_1   1U
/**
 * 010: Compare in CCU0 only against TBU_TS0.
 */
#define SPC5_GTM_ATOM_COMPARE_ONLY_CCU0_TS0                   2U
/**
 * 011: Compare in CCU1 only against TBU_TS1 or TBU_TS2.
 */
#define SPC5_GTM_ATOM_COMPARE_ONLY_CCU1_TS12                  3U
/**
 * 100: Compare first in CCU0 and then in CCU1. Use TBU_TS0.
 */
#define SPC5_GTM_ATOM_COMPARE_CCU0_THEN_CCU1_TS0              4U
/**
 * 101: Compare first in CCU0 and then in CCU1. Use TBU_TS1 or TBU_TS2.
 */
#define SPC5_GTM_ATOM_COMPARE_CCU0_THEN_CCU1_TS12             5U
/**
 * 110: Compare first in CCU0 and then in CCU1. Use TBU_TS0 in CCU0 and TBU_TS1 or TBU_TS2 in CCU1.
 */
#define SPC5_GTM_ATOM_COMPARE_CCU0_TS0_THEN_CCU1_TS12         6U


/** ATOM (SOMC) IRQ CCU0 Interrupt Notified */
#define SPC5_GTM_ATOM_IRQ_STATUS_CCU0                         1UL
/** ATOM (SOMC) IRQ CCU1 Interrupt Notified */
#define SPC5_GTM_ATOM_IRQ_STATUS_CCU1                         2UL

/** ATOM (SOMC) IRQ Enable CCU0 interrupt */
#define SPC5_GTM_ATOM_IRQ_ENABLE_CCU0                         1UL
/** ATOM (SOMC) IRQ Enable CCU1 interrupt */
#define SPC5_GTM_ATOM_IRQ_ENABLE_CCU1                         2UL

/** ATOM (SOMC) IRQ Force CCU0 interrupt */
#define SPC5_GTM_ATOM_IRQ_FORCE_INT_CCU0                      1UL
/** ATOM (SOMC) IRQ Force CCU1 interrupt */
#define SPC5_GTM_ATOM_IRQ_FORCE_INT_CCU1                      2UL

/** ATOM (SOMC) IRQ Mode Level */
#define SPC5_GTM_ATOM_IRQ_MODE_LEVEL                          0U
/** ATOM (SOMC) IRQ Mode Pulse */
#define SPC5_GTM_ATOM_IRQ_MODE_PULSE                          1U
/** ATOM (SOMC) IRQ Mode Pulse-Notify */
#define SPC5_GTM_ATOM_IRQ_MODE_PULSE_NOTIFY                   2U
/** ATOM (SOMC) IRQ Mode Single-Pulse Mode */
#define SPC5_GTM_ATOM_IRQ_MODE_SINGLE_PULSE                   3U

/** ATOM IRQ notify interrupt to ICM as Normal */
#define SPC5_GTM_ATOM_INT_MODE_NORMAL                         1U
/** ATOM IRQ notify interrupt to ICM as Error */
#define SPC5_GTM_ATOM_INT_MODE_ERROR                          2U
/** ATOM IRQ notify interrupt to ICM as Normal and Error */
#define SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR               3U

/*
 * TODO:
 *  remove following defines by reviewing gtm_atomStart()
 */
#define ATOM_CH_ENABLE                                        0x2UL
#define ATOM_CH_DISABLE                                       0x1UL
#define ATOM_BIT_SHIFT                                        0x2UL

/** @} */

/**
 * @brief Type of a structure representing a (GTM-IP) ATOM driver.
 */
typedef struct GTM_ATOMDriver GTM_ATOMDriver;

/**
 * @brief   (GTM-IP) ATOM notification callback type.
 *
 * @param[in] atomd     pointer to the @p ATOMDriver object triggering the callback
 * @param[in] channel   channel triggering the callback
 */
typedef void (*gtm_atom_callback_t)(GTM_ATOMDriver *atomd, uint8_t channel);

/**
 * @brief Type of a structure representing a (GTM-IP) ATOM channel callbacks.
 */
typedef struct GTM_ATOM_Channel_Callbacks GTM_ATOM_Channel_Callbacks;

/**
 * @brief   Structure representing an ATOM Channel callbacks
 */
struct GTM_ATOM_Channel_Callbacks {
  /**
   * @brief CCU0 channel callback function.
   */
	gtm_atom_callback_t ccu0;

  /**
   * @brief CCU1 channel callback function.
   */
	gtm_atom_callback_t ccu1;
};

/**
 * @brief   Structure representing a (GTM) ATOM driver.
 */
struct GTM_ATOMDriver {

  /**
   * @brief Pointer to the (GTM) ATOM registers block.
   */
	volatile GTM_ATOM_TAG *atom;

  /**
   * @brief Pointer to the (GTM) ATOM registers block.
   */
	struct GTM_ICMDriver *icmd;

  /**
   * @brief Interrupts callbacks.
   */
	GTM_ATOM_Channel_Callbacks **callbacks;

  /**
   * @brief Pointer for application private data.
   */
  void *priv;
};

/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_ATOM0 == TRUE) && !defined(__DOXYGEN__)
extern GTM_ATOMDriver ATOMD1;
#endif

#if (SPC5_GTM_USE_ATOM1 == TRUE) && !defined(__DOXYGEN__)
extern GTM_ATOMDriver ATOMD2;
#endif

#if (SPC5_GTM_USE_ATOM2 == TRUE) && !defined(__DOXYGEN__)
extern GTM_ATOMDriver ATOMD3;
#endif

#if (SPC5_GTM_USE_ATOM3 == TRUE) && !defined(__DOXYGEN__)
extern GTM_ATOMDriver ATOMD4;
#endif

#if (SPC5_GTM_USE_ATOM4 == TRUE) && !defined(__DOXYGEN__)
extern GTM_ATOMDriver ATOMD5;
#endif

#if (SPC5_GTM_USE_ATOM5 == TRUE) && !defined(__DOXYGEN__)
extern GTM_ATOMDriver ATOMD6;
#endif

#ifdef __cplusplus
extern "C" {
#endif

void gtm_atomInit(void);
extern void gtm_atomStart(GTM_ATOMDriver *atomd, uint8_t channel);
extern void gtm_atomStop(GTM_ATOMDriver *atomd, uint8_t channel);

extern void gtm_atomSetMode(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t out_mode);

extern void gtm_atomSetCM1TimeBaseSource(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t tbase);

extern void gtm_atomSetCompareStrategy(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t strategy);
extern void gtm_atomCompareControl(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t cmp_strategy);

extern void gtm_atomSetSignalLevel(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t sig_level);
extern void gtm_atomSignalLevelControl(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t sl_ctrl);

extern void gtm_atomARUEnable(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t enable);
extern void gtm_atomARUMode(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t mode);

extern void gtm_atomClkSrc(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t mode, uint8_t clk_src);

extern void gtm_atomSetCompare0(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t value);
extern void gtm_atomSetCompare1(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t value);

extern uint32_t gtm_atomGetCompare0(GTM_ATOMDriver *atomd, uint8_t channel);
extern uint32_t gtm_atomGetCompare1(GTM_ATOMDriver *atomd, uint8_t channel);

extern void gtm_atomSetShadowReg0(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t value);
extern void gtm_atomSetShadowReg1(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t value);

extern uint32_t gtm_atomGetShadowReg0(GTM_ATOMDriver *atomd, uint8_t channel);
extern uint32_t gtm_atomGetShadowReg1(GTM_ATOMDriver *atomd, uint8_t channel);

extern void gtm_atomSetDataSource(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t source);
extern void gtm_atomSetDataSource1(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t source);

extern void gtm_atomSetIRQMode(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t mode);

extern void gtm_atomEnableInt(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t int_num);
extern void gtm_atomNotifyInt(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t int_num);
extern void gtm_atomAckInt(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t int_num);
extern void gtm_atomDisableInt(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t int_num);

extern uint32_t gtm_atomGetIntStatus(GTM_ATOMDriver *atomd, uint8_t channel);
extern uint32_t gtm_atomGetIntEnabled(GTM_ATOMDriver *atomd, uint8_t channel);

extern uint32_t gtm_atomGetDuty(GTM_ATOMDriver *atomd, uint8_t channel);
extern uint32_t gtm_atomGetPeriod(GTM_ATOMDriver *atomd, uint8_t channel);
extern void gtm_atomSetDuty(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t duty);
extern void gtm_atomSetPeriod(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t period);

extern void gtm_atomOutputDisable(GTM_ATOMDriver *atomd, uint8_t channel);
extern void gtm_atomOutputEnable(GTM_ATOMDriver *atomd, uint8_t channel);
extern void gtm_atomOutputDisableExt(GTM_ATOMDriver *atomd, uint32_t mask);
extern void gtm_atomOutputEnableExt(GTM_ATOMDriver *atomd, uint32_t mask);

/* Inline function for Ram execution in Isec isr */
inline void gtm_atomStart_inl(GTM_ATOMDriver *atomd, uint8_t channel) {
    atomd->atom->GTM_ATOM_AGC_REG(GLB_CTRL).R = (ATOM_CH_ENABLE << (16UL + (ATOM_BIT_SHIFT * channel)));
    atomd->atom->GTM_ATOM_AGC_REG(OUTEN_STAT).R = (ATOM_CH_ENABLE << (ATOM_BIT_SHIFT * channel));
    atomd->atom->GTM_ATOM_AGC_REG(ENDIS_STAT).R = (ATOM_CH_ENABLE << (ATOM_BIT_SHIFT * channel));
}
#ifdef __cplusplus
}
#endif

/*lint +e621*/
#endif /* _GTM_ATOM_H_ */
/** @} */
