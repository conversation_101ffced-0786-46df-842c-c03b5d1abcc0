/*****************************************************************************************************************/
/* $HeadURL:: https://172.26.1.29/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_RS_35_PI_0204/tree/COMMON/CON#$  */
/* $Revision:: 193649                                                                                         $  */
/* $Date:: 2021-11-16 17:15:04 +0100 (mar, 16 nov 2021)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MCAN
**  Filename        :  CAN_BR.cfg
**  Created on      :  07-jul-2023 10:00:00
**  Original author :  SantoroR
******************************************************************************/
#ifndef _CAN_BR_H
#define _CAN_BR_H

#pragma ghs startnomisra    // 19.4, 19.6, 19.11 - Proven in use

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* CAN ENGINES */
#define NUM_MCAN_ENGINES  2u
#define NUM_TTCAN_ENGINES 1u

/* MCAN CONFIGURATION */

#define MCAN_1_RAM_START             0U
#define MCAN_1_RAM_SIZE           4352U
#define MCAN_1_RXBUFF_NUM           64U
#define MCAN_1_STD_FILTER            2U //Standard filters actually used
#define MCAN_1_XTD_FILTER            0U //Extended filters actually used
#define MCAN_1_MAX_DATA_SIZE         8U
#define MCAN_1_TXBUFF_NUM           32U
#define MCAN_1_RX_INT_LINE      CAN_LINE0_INT
#define MCAN_1_CB_EN                 0u
#define MCAN_1_TIMEOUT_INIT        500u // [us] 

#define MCAN_2_RAM_START    MCAN_1_RAM_START + MCAN_1_RAM_SIZE
#define MCAN_2_RAM_SIZE           4352U
#define MCAN_2_RXBUFF_NUM           64U
#define MCAN_2_STD_FILTER            0U //Standard filters actually used
#define MCAN_2_XTD_FILTER            2U //Extended filters actually used
#define MCAN_2_MAX_DATA_SIZE         8U
#define MCAN_2_TXBUFF_NUM           32U
#define MCAN_2_RX_INT_LINE    CAN_LINE0_INT// CAN_RX_INT_DISABLE
#define MCAN_2_CB_EN                 0u
#define MCAN_2_TIMEOUT_INIT        500u // [us] 

//MCAN 3 ==> TTCAN
#define MCAN_3_RAM_START    MCAN_2_RAM_START + MCAN_2_RAM_SIZE
#define MCAN_3_RAM_SIZE           1344U
#define MCAN_3_RXBUFF_NUM           64U
#define MCAN_3_STD_FILTER            1U //Standard filters actually used
#define MCAN_3_XTD_FILTER            0U //Extended filters actually used
#define MCAN_3_MAX_DATA_SIZE         8U //FIXED
#define MCAN_3_TXBUFF_NUM           32U
#define MCAN_3_RX_INT_LINE     CAN_RX_INT_DISABLE //CAN_LINE1_INT
#define MCAN_3_CB_EN                 0u
#define MCAN_3_TIMEOUT_INIT        500u // [us] 

//MB Queue length in the virtual structures */
#define MCAN_QUELEN             2u

/* MCAN */
// Enable error ISR
#if (TARGET_TYPE == SPC574K2_CUT24) // Default value: 0x3FF60000UL
#define MCAN_ERROR_MASK  0U // ( MCAN_ERROR_ARAE | MCAN_ERROR_PEDE | MCAN_ERROR_PEAE | MCAN_ERROR_WDIE | MCAN_ERROR_EWE | MCAN_ERROR_EPE | MCAN_ERROR_ELOE | MCAN_ERROR_BEUE | MCAN_ERROR_BECE | MCAN_ERROR_TOOE | MCAN_ERROR_MRAFE | MCAN_ERROR_TSWE)
#elif (TARGET_TYPE == SPC574K2) // Default value: 0xFFF60000UL
#define MCAN_ERROR_MASK   ( MCAN_ERROR_ARAE | MCAN_ERROR_PEDE | MCAN_ERROR_PEAE | MCAN_ERROR_WDIE | MCAN_ERROR_EWE | MCAN_ERROR_EPE | MCAN_ERROR_ELOE | MCAN_ERROR_BEUE | MCAN_ERROR_BECE | MCAN_ERROR_TOOE | MCAN_ERROR_MRAFE | MCAN_ERROR_TSWE)
#endif
// Enable error ISR Line
#define MCAN_ERROR_LINE  0U // ( MCAN_ERROR_ARAE_ISR | MCAN_ERROR_PEDE_ISR | MCAN_ERROR_PEAE_ISR | MCAN_ERROR_WDIE_ISR | MCAN_ERROR_EWE_ISR | MCAN_ERROR_EPE_ISR | MCAN_ERROR_ELOE_ISR | MCAN_ERROR_BEUE_ISR | MCAN_ERROR_BECE_ISR | MCAN_ERROR_TOOE_ISR | MCAN_ERROR_MRAFE_ISR | MCAN_ERROR_TSWE_ISR)  //0xFCF20000UL
//Enable BusOFF ISR
#define MCAN_BOFF_ISR_ENABLE  0x0u
//Select BusOFF ISR Line
#define MCAN_BOFF_INT_LINE    CAN_LINE1_INT

/* TTCAN */
// Enable error ISR
#define TTCAN_ERROR_MASK  0u //( TTCAN_ERROR_STE | TTCAN_ERROR_FOE | TTCAN_ERROR_ACKE | TTCAN_ERROR_BE | TTCAN_ERROR_CRCE | TTCAN_ERROR_WDIE | TTCAN_ERROR_EWE | TTCAN_ERROR_EPE | TTCAN_ERROR_ELOE | TTCAN_ERROR_BEUE | TTCAN_ERROR_BECE | TTCAN_ERROR_TOOE | TTCAN_ERROR_UMDE | TTCAN_ERROR_TSWE)
// Enable error ISR Line
#define TTCAN_ERROR_LINE  0u //( TTCAN_ERROR_STE_ISR | TTCAN_ERROR_FOE_ISR | TTCAN_ERROR_ACKE_ISR | TTCAN_ERROR_BE_ISR | TTCAN_ERROR_CRCE_ISR | TTCAN_ERROR_WDIE_ISR | TTCAN_ERROR_EWE_ISR | TTCAN_ERROR_EPE_ISR | TTCAN_ERROR_ELOE_ISR | TTCAN_ERROR_BEUE_ISR | TTCAN_ERROR_BECE_ISR | TTCAN_ERROR_TOOE_ISR | TTCAN_ERROR_UMDE_ISR | TTCAN_ERROR_TSWE_ISR )
//Enable BusOFF ISR
#define TTCAN_BOFF_ISR_ENABLE  0x1u
//Select BusOFF ISR Line
#define TTCAN_BOFF_INT_LINE    CAN_LINE1_INT

///Disable the Message Buffer
#define CAN_BUFF_DISABLE 0U
///Enable the Message Buffer
#define CAN_BUFF_ENABLE 1U

/****************************************************************************
     CAN EISB6C specific configuration
 ****************************************************************************/

/****************************************************************************
     CAN SCU specific configuration
 ****************************************************************************/
/* CAN Engine chosen CLK source */
#define CAN_CLK_SRC CAN_CLK_SRC_BUSCLK

/****************************************************************************
     CAN Engine for CCP
 ****************************************************************************/
#ifdef _BUILD_CCP_
/* CAN Engine chosen for CCP */
#define     CCP_CAN         MCAN_ENG_C

/*receiving MB for CCP_CAN*/
#define     CCP_CAN_RXMB          0u         /* MB for CCP rx (only one)*/

/*CAN MBs RANGE used for CCP tx: use contiguous MBs*/
#define     CCP_CAN_TXMB_FIRST    0u         /* first MB for CCP tx*/
#define     CCP_CAN_TXMB_LAST    31u        /* last  MB for CCP tx */

/* sending message on CCP */
#define ID_CCP_DTO      0x200u
/* receiving message on CCP */
#define ID_CCP_CRO      0x201u 

/* CCP MBs RECEPTION MASK: used to enable this MBs  */
#define CAN_CCP_MB_RX_MASK_LOW  ((uint32_T) ( (1u << CCP_CAN_RXMB)))
#define CAN_CCP_MB_RX_MASK_HIGH ((uint32_T) (0x0))
#else
#undef CCP_CAN
#endif  //_BUILD_CCP_

/****************************************************************************
     CAN Engine for Diagnostic CAN (UDS)
****************************************************************************/
#define     USE_FUNCTIONAL_ADDRESS

#ifdef  _BUILD_TPE_
#define     TPE_CAN        MCAN_ENG_B
#define     TPE_CAN_TXID        (0x18DA0000u | (TPE_TPDU_SA << 8) | TPE_TPDU_TA ) // Physical response CAN identifier from ECU to the external test equipment
#define     TPE_CAN_RXID        (0x18DA0000u | (TPE_TPDU_TA << 8) | TPE_TPDU_SA ) // Physical request CAN identifier from the external test equipment to ECU
#define     TPE_CAN_TXBUF           0u
#define     TPE_CAN_RXBUF           CAN_CHB_BUF1_RX_IDE_NUM_TYPE
#ifdef USE_FUNCTIONAL_ADDRESS
#define     TPE_CAN_TXBROADCASTID         (0x18DB0000u | (TPE_TPDU_SA << 8) | TPE_TPDU_TA_FUNC) // CAN identifier for functionally addressed request messages sent by the external test equipment
#define     TPE_CAN_RXBROADCASTID         (0x18DB0000u | (TPE_TPDU_TA_FUNC << 8) | TPE_TPDU_SA ) // CAN identifier for functionally addressed request messages sent by the external test equipment
#define     TPE_CAN_RXBROADCASTBUF  CAN_CHB_BUF0_RX_IDE_NUM_TYPE
#endif
#else
#undef      TPE_CAN
#endif /* _BUILD_TPE_ */

#define USE_29BIT_IDS        // if defined, it allows 29-bits CAN identifiers usage

/* sending messages on TPE/UDS from DBC */
#define DIAGNOSTIC_RESPONSE_ISCM_ID              TPE_CAN_TXID
#define DIAGNOSTIC_RESPONSE_ISCM_DLC             0x8u

/* receiving messages on TPE/UDS from DBC */
#define DIAGNOSTIC_REQUEST_FUNC_ID               TPE_CAN_RXBROADCASTID
#define DIAGNOSTIC_REQUEST_FUNC_DLC              0x8u

#define DIAGNOSTIC_REQUEST_ISCM_ID               TPE_CAN_RXID
#define DIAGNOSTIC_REQUEST_ISCM_DLC              0x8u


/****************************************************************************
     CAN Engine for POWER TRAIN / VEHICLE  
 ****************************************************************************/

#define POWER_TRAIN_CAN MCAN_ENG_A

 
#ifdef POWER_TRAIN_CAN

/* sending messages from DBC */
#define EISB_ECM_1_M_ID                         0x50u
#define EISB_ECM_1_M_DLC                         0x8u
#define EISB_ECM_1_M_BUF                           0u

#define EISB_ECM_1_S_ID                         0x51u
#define EISB_ECM_1_S_DLC                         0x8u
#define EISB_ECM_1_S_BUF                           1u

#define EISB_ECM_2_M_ID                         0x52u
#define EISB_ECM_2_M_DLC                         0x8u
#define EISB_ECM_2_M_BUF                           2u

#define EISB_ECM_2_S_ID                         0x53u
#define EISB_ECM_2_S_DLC                         0x8u
#define EISB_ECM_2_S_BUF                           3u

#define EISB_ECM_3_M_ID                         0x84u
#define EISB_ECM_3_M_DLC                         0x8u
#define EISB_ECM_3_M_BUF                           4u

#define EISB_ECM_3_S_ID                         0x85u
#define EISB_ECM_3_S_DLC                         0x8u
#define EISB_ECM_3_S_BUF                           5u

#define EISB_ECM_4_M_ID                        0x451u
#define EISB_ECM_4_M_DLC                         0x8u
#define EISB_ECM_4_M_BUF                           6u

#define EISB_ECM_4_S_ID                        0x452u
#define EISB_ECM_4_S_DLC                         0x8u
#define EISB_ECM_4_S_BUF                           7u

/* receiving messages from DBC */
#define ECM_EISB_1_ID                           0x60u
#define ECM_EISB_1_DLC                           0x8u
#define ECM_EISB_1_BUF_QUELEN             MCAN_QUELEN
#define ECM_EISB_1_BUF                             0u

#define ECM_EISB_2_ID                           0x62u
#define ECM_EISB_2_DLC                           0x8u
#define ECM_EISB_2_BUF_QUELEN             MCAN_QUELEN
#define ECM_EISB_2_BUF                             1u

/* POWER TRAIN MBs RECEPTION MASK: used to enable this MBs  */
#define CAN_POWERTRAIN_MB_RX_MASK_LOW  ((uint32_T) ((1u << ECM_EISB_1_BUF) | (1u << ECM_EISB_2_BUF)))
#define CAN_POWERTRAIN_MB_RX_MASK_HIGH ((uint32_T) (0x0))

#endif

/****************************************************************************
     CAN Engine for VEHICLE  
 ****************************************************************************/
#define VEHICLE_CAN MCAN_ENG_B

#ifdef VEHICLE_CAN

/* sending messages on VEHICLE_CAN CAN from DBC  */


/* receiving messages on VEHICLE_CAN  from DBC */


/* POWER VEHICLE MBs RECEPTION MASK: used to enable this MBs  */
#define CAN_VEHICLE_MB_RX_MASK_LOW ((uint32_T) (0X0) >> MCAN_1_XTD_FILTER)
#define CAN_VEHICLE_MB_RX_MASK_HIGH ((uint32_T)(0x0))

#endif

/****************************************************************************
     MCAN Engine A (MCAN_1): specific configuration
 ****************************************************************************/
#define CAN_CHA_EN        1u              /* Enable the MCAN Channel A */
#define CAN_CHA_BR        CAN_BR_1000k
#define CAN_CHA_LOOPBACK  CAN_NO_LOOPBACK
//Define here specific configuration compliant with customer request //todo da sistemare
#if (CAN_CHA_BR == CAN_A_BR_CUSTOMER)
//#undef CAN_A_CR_CUSTOM_CFG  0x01AC0017u  MC, tba
#endif

//#undef CAN_CHA_LASTUSED_MB_POLL    MC, tba         /* 4 */      /* Necessary for Polling Mode, defines the last used MB to poll */
                                        /* Reducing polling overhead :*/
                                        /* - Use contiguous MBs */
                                        /* - if not need tx callback, */
                                        /*   use the first MBs for rx and set CAN_CHA_LASTUSED_MB_POLL at the last used */
 #define CAN_CHA_TXRX_EXC 0u     /* CAN A Tx/Rx Exception enable - not used */                                  


/* MCAN Engine A - MBs Configuration  */

#if CAN_CHA_EN 

#define MCAN_1_DEDICATED_RXMB_USED  (MCAN_1_STD_FILTER + MCAN_1_XTD_FILTER) //Number of used contiguous  dedicated rx message buffers

/* Rx Buffers */
#define CAN_CHA_BUF0_RX              CAN_BUFF_ENABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF0_RX_IDE          0x0u                        /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF0_RX_IDE_NUM_TYPE 0u                          /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF0_RX_LID          ECM_EISB_1_ID               /* Frame Identifier */
#define CAN_CHA_BUF0_RX_DLC          ECM_EISB_1_DLC              /* Data lenght code */
#define CAN_CHA_BUF0_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF1_RX              CAN_BUFF_ENABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF1_RX_IDE          0x0u                        /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF1_RX_IDE_NUM_TYPE 1u                          /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF1_RX_LID          ECM_EISB_2_ID               /* Frame Identifier */
#define CAN_CHA_BUF1_RX_DLC          ECM_EISB_2_DLC              /* Data lenght code */
#define CAN_CHA_BUF1_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF2_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF2_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF2_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF2_RX_LID                                      /* Frame Identifier */
#define CAN_CHA_BUF2_RX_DLC                                      /* Data lenght code */
#define CAN_CHA_BUF2_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF3_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF3_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF3_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF3_RX_LID                                      /* Frame Identifier */
#define CAN_CHA_BUF3_RX_DLC                                      /* Data lenght code */
#define CAN_CHA_BUF3_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF4_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF4_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF4_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF4_RX_LID                                      /* Frame Identifier */
#define CAN_CHA_BUF4_RX_DLC                                      /* Data lenght code */
#define CAN_CHA_BUF4_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF5_RX             CAN_BUFF_DISABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF5_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF5_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF5_RX_LID                                      /* Frame Identifier */
#define CAN_CHA_BUF5_RX_DLC                                      /* Data lenght code */
#define CAN_CHA_BUF5_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF6_RX             CAN_BUFF_DISABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF6_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF6_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF6_RX_LID                                      /* Frame Identifier */
#define CAN_CHA_BUF6_RX_DLC                                      /* Data lenght code */
#define CAN_CHA_BUF6_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF7_RX             CAN_BUFF_DISABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF7_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF7_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF7_RX_LID                                      /* Frame Identifier */
#define CAN_CHA_BUF7_RX_DLC                                      /* Data lenght code */
#define CAN_CHA_BUF7_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF8_RX             CAN_BUFF_DISABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF8_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF8_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF8_RX_LID                                      /* Frame Identifier */
#define CAN_CHA_BUF8_RX_DLC                                      /* Data lenght code */
#define CAN_CHA_BUF8_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF9_RX             CAN_BUFF_DISABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF9_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF9_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF9_RX_LID                                      /* Frame Identifier */
#define CAN_CHA_BUF9_RX_DLC                                      /* Data lenght code */
#define CAN_CHA_BUF9_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHA_BUF10_RX             CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF10_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF10_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF10_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF10_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF10_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHA_BUF11_RX             CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF11_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF11_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF11_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF11_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF11_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHA_BUF12_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF12_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF12_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF12_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF12_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF12_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF13_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF13_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF13_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF13_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF13_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF13_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF14_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF14_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF14_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF14_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF14_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF14_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF15_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF15_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF15_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF15_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF15_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF15_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF16_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF16_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF16_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF16_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF16_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF16_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF17_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF17_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF17_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF17_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF17_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF17_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF18_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF18_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF18_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF18_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF18_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF18_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF19_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF19_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF19_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF19_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF19_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF19_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF20_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF20_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF20_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF20_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF20_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF20_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF21_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF21_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF21_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF21_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF21_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF21_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF22_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF22_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF22_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF22_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF22_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF22_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF23_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF23_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF23_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF23_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF23_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF23_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF24_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF24_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF24_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF24_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF24_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF24_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF25_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF25_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF25_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF25_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF25_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF25_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF26_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF26_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF26_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF26_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF26_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF26_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF27_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF27_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF27_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF27_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF27_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF27_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF28_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF28_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF28_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF28_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF28_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF28_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF29_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF29_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF29_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF29_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF29_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF29_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF30_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF30_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF30_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF30_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF30_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF30_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF31_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF31_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF31_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF31_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF31_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF31_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF32_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF32_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF32_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF32_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF32_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF32_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF33_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF33_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF33_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF33_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF33_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF33_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF34_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF34_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF34_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF34_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF34_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF34_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF35_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF35_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF35_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF35_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF35_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF35_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF36_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF36_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF36_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF36_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF36_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF36_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF37_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF37_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF37_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF37_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF37_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF37_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF38_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF38_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF38_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF38_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF38_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF38_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF39_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF39_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF39_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF39_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF39_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF39_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF40_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF40_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF40_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF40_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF40_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF40_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF41_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF41_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF41_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF41_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF41_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF41_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF42_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF42_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF42_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF42_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF42_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF42_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF43_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF43_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF43_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF43_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF43_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF43_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF44_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF44_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF44_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF44_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF44_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF44_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF45_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF45_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF45_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF45_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF45_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF45_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF46_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF46_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF46_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF46_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF46_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF46_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF47_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF47_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF47_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF47_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF47_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF47_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF48_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF48_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF48_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF48_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF48_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF48_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF49_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF49_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF49_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF49_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF49_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF49_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF50_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF50_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF50_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF50_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF50_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF50_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF51_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF51_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF51_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF51_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF51_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF51_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF52_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF52_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF52_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF52_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF52_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF52_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF53_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF53_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF53_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF53_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF53_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF53_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF54_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF54_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF54_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF54_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF54_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF54_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF55_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF55_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF55_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF55_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF55_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF55_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF56_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF56_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF56_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF56_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF56_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF56_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF57_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF57_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF57_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF57_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF57_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF57_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF58_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF58_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF58_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF58_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF58_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF58_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF59_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF59_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF59_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF59_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF59_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF59_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF60_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF60_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF60_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF60_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF60_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF60_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF61_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF61_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF61_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF61_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF61_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF61_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF62_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF62_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF62_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF62_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF62_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF62_RX_EXC 0u                                  /* Enabling MB exception task */

#define CAN_CHA_BUF63_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHA_BUF63_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHA_BUF63_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHA_BUF63_RX_LID                                     /* Frame Identifier */
#define CAN_CHA_BUF63_RX_DLC                                     /* Data lenght code */
#define CAN_CHA_BUF63_RX_EXC 0u                                  /* Enabling MB exception task */

/* Tx Buffers */
#define CAN_CHA_BUF0_TX     CAN_BUFF_ENABLE
#define CAN_CHA_BUF0_TX_IDE 0x0u
#define CAN_CHA_BUF0_TX_LID EISB_ECM_1_M_ID
#define CAN_CHA_BUF0_TX_DLC EISB_ECM_1_M_DLC
#define CAN_CHA_BUF0_TX_EXC 0u
#define CAN_CHA_BUF0_TX_INT 1u

#define CAN_CHA_BUF1_TX     CAN_BUFF_ENABLE
#define CAN_CHA_BUF1_TX_IDE 0x0u
#define CAN_CHA_BUF1_TX_LID EISB_ECM_1_S_ID
#define CAN_CHA_BUF1_TX_DLC EISB_ECM_1_S_DLC
#define CAN_CHA_BUF1_TX_EXC 0u
#define CAN_CHA_BUF1_TX_INT 1u

#define CAN_CHA_BUF2_TX     CAN_BUFF_ENABLE
#define CAN_CHA_BUF2_TX_IDE 0x0u
#define CAN_CHA_BUF2_TX_LID EISB_ECM_2_M_ID
#define CAN_CHA_BUF2_TX_DLC EISB_ECM_2_M_DLC
#define CAN_CHA_BUF2_TX_EXC 0u
#define CAN_CHA_BUF2_TX_INT 1u

#define CAN_CHA_BUF3_TX     CAN_BUFF_ENABLE
#define CAN_CHA_BUF3_TX_IDE 0x0u
#define CAN_CHA_BUF3_TX_LID EISB_ECM_2_S_ID
#define CAN_CHA_BUF3_TX_DLC EISB_ECM_2_S_DLC
#define CAN_CHA_BUF3_TX_EXC 0u
#define CAN_CHA_BUF3_TX_INT 1u

#define CAN_CHA_BUF4_TX     CAN_BUFF_ENABLE
#define CAN_CHA_BUF4_TX_IDE 0x0u
#define CAN_CHA_BUF4_TX_LID EISB_ECM_3_M_ID
#define CAN_CHA_BUF4_TX_DLC EISB_ECM_3_M_DLC
#define CAN_CHA_BUF4_TX_EXC 0u
#define CAN_CHA_BUF4_TX_INT 1u

#define CAN_CHA_BUF5_TX     CAN_BUFF_ENABLE
#define CAN_CHA_BUF5_TX_IDE 0x0u
#define CAN_CHA_BUF5_TX_LID EISB_ECM_3_S_ID
#define CAN_CHA_BUF5_TX_DLC EISB_ECM_3_S_DLC
#define CAN_CHA_BUF5_TX_EXC 0u
#define CAN_CHA_BUF5_TX_INT 1u

#define CAN_CHA_BUF6_TX     CAN_BUFF_ENABLE
#define CAN_CHA_BUF6_TX_IDE 0u
#define CAN_CHA_BUF6_TX_LID EISB_ECM_4_M_ID
#define CAN_CHA_BUF6_TX_DLC EISB_ECM_4_M_DLC
#define CAN_CHA_BUF6_TX_EXC 0u
#define CAN_CHA_BUF6_TX_INT 1u

#define CAN_CHA_BUF7_TX     CAN_BUFF_ENABLE
#define CAN_CHA_BUF7_TX_IDE 0u
#define CAN_CHA_BUF7_TX_LID EISB_ECM_4_S_ID
#define CAN_CHA_BUF7_TX_DLC EISB_ECM_4_S_DLC
#define CAN_CHA_BUF7_TX_EXC 0u
#define CAN_CHA_BUF7_TX_INT 1u

#define CAN_CHA_BUF8_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF8_TX_IDE
#define CAN_CHA_BUF8_TX_LID
#define CAN_CHA_BUF8_TX_DLC
#define CAN_CHA_BUF8_TX_EXC
#define CAN_CHA_BUF8_TX_INT

#define CAN_CHA_BUF9_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF9_TX_IDE
#define CAN_CHA_BUF9_TX_LID
#define CAN_CHA_BUF9_TX_DLC
#define CAN_CHA_BUF9_TX_EXC
#define CAN_CHA_BUF9_TX_INT

#define CAN_CHA_BUF10_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF10_TX_IDE
#define CAN_CHA_BUF10_TX_LID
#define CAN_CHA_BUF10_TX_DLC
#define CAN_CHA_BUF10_TX_EXC
#define CAN_CHA_BUF10_TX_INT

#define CAN_CHA_BUF11_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF11_TX_IDE
#define CAN_CHA_BUF11_TX_LID
#define CAN_CHA_BUF11_TX_DLC
#define CAN_CHA_BUF11_TX_EXC
#define CAN_CHA_BUF11_TX_INT

#define CAN_CHA_BUF12_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF12_TX_IDE
#define CAN_CHA_BUF12_TX_LID
#define CAN_CHA_BUF12_TX_DLC
#define CAN_CHA_BUF12_TX_EXC
#define CAN_CHA_BUF12_TX_INT

#define CAN_CHA_BUF13_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF13_TX_IDE
#define CAN_CHA_BUF13_TX_LID
#define CAN_CHA_BUF13_TX_DLC
#define CAN_CHA_BUF13_TX_EXC
#define CAN_CHA_BUF13_TX_INT

#define CAN_CHA_BUF14_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF14_TX_IDE
#define CAN_CHA_BUF14_TX_LID
#define CAN_CHA_BUF14_TX_DLC
#define CAN_CHA_BUF14_TX_EXC
#define CAN_CHA_BUF14_TX_INT

#define CAN_CHA_BUF15_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF15_TX_IDE
#define CAN_CHA_BUF15_TX_LID
#define CAN_CHA_BUF15_TX_DLC
#define CAN_CHA_BUF15_TX_EXC
#define CAN_CHA_BUF15_TX_INT

#define CAN_CHA_BUF16_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF16_TX_IDE
#define CAN_CHA_BUF16_TX_LID
#define CAN_CHA_BUF16_TX_DLC
#define CAN_CHA_BUF16_TX_EXC
#define CAN_CHA_BUF16_TX_INT

#define CAN_CHA_BUF17_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF17_TX_IDE
#define CAN_CHA_BUF17_TX_LID
#define CAN_CHA_BUF17_TX_DLC
#define CAN_CHA_BUF17_TX_EXC
#define CAN_CHA_BUF17_TX_INT

#define CAN_CHA_BUF18_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF18_TX_IDE
#define CAN_CHA_BUF18_TX_LID
#define CAN_CHA_BUF18_TX_DLC
#define CAN_CHA_BUF18_TX_EXC
#define CAN_CHA_BUF18_TX_INT

#define CAN_CHA_BUF19_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF19_TX_IDE
#define CAN_CHA_BUF19_TX_LID
#define CAN_CHA_BUF19_TX_DLC
#define CAN_CHA_BUF19_TX_EXC
#define CAN_CHA_BUF19_TX_INT

#define CAN_CHA_BUF20_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF20_TX_IDE
#define CAN_CHA_BUF20_TX_LID
#define CAN_CHA_BUF20_TX_DLC
#define CAN_CHA_BUF20_TX_EXC
#define CAN_CHA_BUF20_TX_INT

#define CAN_CHA_BUF21_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF21_TX_IDE
#define CAN_CHA_BUF21_TX_LID
#define CAN_CHA_BUF21_TX_DLC
#define CAN_CHA_BUF21_TX_EXC
#define CAN_CHA_BUF21_TX_INT

#define CAN_CHA_BUF22_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF22_TX_IDE
#define CAN_CHA_BUF22_TX_LID
#define CAN_CHA_BUF22_TX_DLC
#define CAN_CHA_BUF22_TX_EXC
#define CAN_CHA_BUF22_TX_INT

#define CAN_CHA_BUF23_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF23_TX_IDE
#define CAN_CHA_BUF23_TX_LID
#define CAN_CHA_BUF23_TX_DLC
#define CAN_CHA_BUF23_TX_EXC
#define CAN_CHA_BUF23_TX_INT

#define CAN_CHA_BUF24_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF24_TX_IDE
#define CAN_CHA_BUF24_TX_LID
#define CAN_CHA_BUF24_TX_DLC
#define CAN_CHA_BUF24_TX_EXC
#define CAN_CHA_BUF24_TX_INT

#define CAN_CHA_BUF25_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF25_TX_IDE
#define CAN_CHA_BUF25_TX_LID
#define CAN_CHA_BUF25_TX_DLC
#define CAN_CHA_BUF25_TX_EXC
#define CAN_CHA_BUF25_TX_INT

#define CAN_CHA_BUF26_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF26_TX_IDE
#define CAN_CHA_BUF26_TX_LID
#define CAN_CHA_BUF26_TX_DLC
#define CAN_CHA_BUF26_TX_EXC
#define CAN_CHA_BUF26_TX_INT

#define CAN_CHA_BUF27_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF27_TX_IDE 
#define CAN_CHA_BUF27_TX_LID
#define CAN_CHA_BUF27_TX_DLC
#define CAN_CHA_BUF27_TX_EXC
#define CAN_CHA_BUF27_TX_INT

#define CAN_CHA_BUF28_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF28_TX_IDE
#define CAN_CHA_BUF28_TX_LID
#define CAN_CHA_BUF28_TX_DLC
#define CAN_CHA_BUF28_TX_EXC
#define CAN_CHA_BUF28_TX_INT

#define CAN_CHA_BUF29_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF29_TX_IDE
#define CAN_CHA_BUF29_TX_LID
#define CAN_CHA_BUF29_TX_DLC
#define CAN_CHA_BUF29_TX_EXC
#define CAN_CHA_BUF29_TX_INT

#define CAN_CHA_BUF30_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF30_TX_IDE
#define CAN_CHA_BUF30_TX_LID
#define CAN_CHA_BUF30_TX_DLC
#define CAN_CHA_BUF30_TX_EXC
#define CAN_CHA_BUF30_TX_INT

#define CAN_CHA_BUF31_TX     CAN_BUFF_DISABLE
#define CAN_CHA_BUF31_TX_IDE
#define CAN_CHA_BUF31_TX_LID
#define CAN_CHA_BUF31_TX_DLC
#define CAN_CHA_BUF31_TX_EXC
#define CAN_CHA_BUF31_TX_INT

#endif

/****************************************************************************
     MCAN Engine B (MCAN_2) : specific configuration
 ****************************************************************************/
#define CAN_CHB_EN          1u   
#define CAN_B_BR_CUSTOMER   1u     // 20Tq PRESDIV=1, RJW=1, PSEG1=7, PSEG2=2, PROPSEG=7
#define CAN_CHB_BR        CAN_BR_500k
#define CAN_CHB_LOOPBACK  CAN_NO_LOOPBACK

//Define here specific configuration compliant with customer request
#if (CAN_CHB_BR == CAN_B_BR_CUSTOMER) //todo da sistemare
//#undef CAN_B_CR_CUSTOM_CFG  0x017A0017u  MC, TBA       // F173 cfg 20Tq PRESDIV=1, RJW=1, PSEG1=7, PSEG2=2, BOFFMSK=0, ERRMSK=0, CLK_SRC=0, LPB=0, TWRNMSK=0, RWRNMSK=0, SMP=0, BOFFREC=0, TSYN=0, LBUF=1, LOM=0, PROPSEG=7
#endif

//#undef CAN_CHB_LASTUSED_MB_POLL       /*MC, TBA 0 */
#define CAN_CHB_TXRX_EXC 1u     /* CAN B Tx/Rx Exception enable*/

#if CAN_CHB_EN 

#define  MCAN_2_DEDICATED_RXMB_USED (MCAN_2_STD_FILTER + MCAN_2_XTD_FILTER)  //Number of used contiguous  dedicated rx message buffers 3u

#ifdef VEHICLE_CAN
#if (VEHICLE_CAN==MCAN_ENG_B)

/* Rx Buffers */
#define CAN_CHB_BUF0_RX              CAN_BUFF_ENABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF0_RX_IDE          0x1u                        /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF0_RX_IDE_NUM_TYPE 0u                          /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF0_RX_LID          DIAGNOSTIC_REQUEST_FUNC_ID  /* Frame Identifier */
#define CAN_CHB_BUF0_RX_DLC          DIAGNOSTIC_REQUEST_FUNC_DLC /* Data lenght code */
#define CAN_CHB_BUF0_RX_EXC          1u                          /* Enabling MB exception task */

#define CAN_CHB_BUF1_RX              CAN_BUFF_ENABLE             /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF1_RX_IDE          0x1u                        /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF1_RX_IDE_NUM_TYPE 1u                          /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF1_RX_LID          DIAGNOSTIC_REQUEST_ISCM_ID  /* Frame Identifier */
#define CAN_CHB_BUF1_RX_DLC          DIAGNOSTIC_REQUEST_ISCM_DLC /* Data lenght code */
#define CAN_CHB_BUF1_RX_EXC          1u                          /* Enabling MB exception task */

#define CAN_CHB_BUF2_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF2_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF2_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF2_RX_LID                                      /* Frame Identifier */
#define CAN_CHB_BUF2_RX_DLC                                      /* Data lenght code */
#define CAN_CHB_BUF2_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHB_BUF3_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF3_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF3_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF3_RX_LID                                      /* Frame Identifier */
#define CAN_CHB_BUF3_RX_DLC                                      /* Data lenght code */
#define CAN_CHB_BUF3_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHB_BUF4_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF4_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF4_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF4_RX_LID                                      /* Frame Identifier */
#define CAN_CHB_BUF4_RX_DLC                                      /* Data lenght code */
#define CAN_CHB_BUF4_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHB_BUF5_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF5_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF5_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF5_RX_LID                                      /* Frame Identifier */
#define CAN_CHB_BUF5_RX_DLC                                      /* Data lenght code */
#define CAN_CHB_BUF5_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHB_BUF6_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF6_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF6_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF6_RX_LID                                      /* Frame Identifier */
#define CAN_CHB_BUF6_RX_DLC                                      /* Data lenght code */
#define CAN_CHB_BUF6_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHB_BUF7_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF7_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF7_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF7_RX_LID                                      /* Frame Identifier */
#define CAN_CHB_BUF7_RX_DLC                                      /* Data lenght code */
#define CAN_CHB_BUF7_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHB_BUF8_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF8_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF8_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF8_RX_LID                                      /* Frame Identifier */
#define CAN_CHB_BUF8_RX_DLC                                      /* Data lenght code */
#define CAN_CHB_BUF8_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHB_BUF9_RX              CAN_BUFF_DISABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF9_RX_IDE                                      /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF9_RX_IDE_NUM_TYPE                             /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF9_RX_LID                                      /* Frame Identifier */
#define CAN_CHB_BUF9_RX_DLC                                      /* Data lenght code */
#define CAN_CHB_BUF9_RX_EXC          0u                          /* Enabling MB exception task */

#define CAN_CHB_BUF10_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF10_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF10_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF10_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF10_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF10_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF11_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF11_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF11_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF11_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF11_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF11_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF12_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF12_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF12_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF12_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF12_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF12_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF13_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF13_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF13_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF13_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF13_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF13_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF14_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF14_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF14_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF14_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF14_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF14_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF15_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF15_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF15_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF15_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF15_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF15_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF16_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF16_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF16_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF16_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF16_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF16_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF17_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF17_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF17_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF17_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF17_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF17_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF18_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF18_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF18_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF18_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF18_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF18_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF19_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF19_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF19_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF19_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF19_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF19_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF20_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF20_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF20_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF20_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF20_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF20_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF21_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF21_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF21_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF21_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF21_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF21_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF22_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF22_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF22_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF22_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF22_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF22_RX_EXC          0u                         /* Enabling MB exception task */
#define CAN_CHB_BUF22_RX_INT                                     /* Enabling MB interrupt */

#define CAN_CHB_BUF23_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF23_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF23_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF23_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF23_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF23_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF24_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF24_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF24_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF24_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF24_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF24_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF25_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF25_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF25_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF25_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF25_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF25_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF26_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF26_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF26_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF26_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF26_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF26_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF27_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF27_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF27_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF27_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF27_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF27_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF28_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF28_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF28_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF28_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF28_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF28_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF29_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF29_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF29_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF29_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF29_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF29_RX_EXC          0u                         /* Enabling MB exception task */
#define CAN_CHB_BUF30_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF30_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF30_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF30_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF30_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF30_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF31_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF31_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF31_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF31_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF31_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF31_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF32_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF32_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF32_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF32_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF32_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF32_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF33_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF33_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF33_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF33_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF33_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF33_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF34_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF34_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF34_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF34_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF34_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF34_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF35_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF35_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF35_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF35_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF35_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF35_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF36_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF36_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF36_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF36_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF36_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF36_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF37_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF37_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF37_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF37_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF37_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF37_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF38_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF38_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF38_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF38_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF38_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF38_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF39_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF39_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF39_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF39_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF39_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF39_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF40_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF40_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF40_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF40_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF40_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF40_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF41_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF41_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF41_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF41_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF41_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF41_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF42_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF42_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF42_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF42_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF42_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF42_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF43_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF43_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF43_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF43_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF43_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF43_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF44_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF44_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF44_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF44_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF44_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF44_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF45_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF45_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF45_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF45_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF45_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF45_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF46_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF46_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF46_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF46_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF46_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF46_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF47_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF47_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF47_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF47_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF47_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF47_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF48_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF48_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF48_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF48_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF48_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF48_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF49_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF49_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF49_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF49_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF49_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF49_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF50_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF50_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF50_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF50_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF50_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF50_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF51_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF51_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF51_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF51_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF51_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF51_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF52_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF52_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF52_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF52_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF52_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF52_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF53_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF53_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF53_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF53_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF53_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF53_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF54_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF54_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF54_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF54_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF54_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF54_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF55_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF55_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF55_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF55_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF55_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF55_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF56_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF56_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF56_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF56_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF56_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF56_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF57_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF57_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF57_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF57_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF57_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF57_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF58_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF58_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF58_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF58_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF58_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF58_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF59_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF59_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF59_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF59_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF59_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF59_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF60_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF60_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF60_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF60_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF60_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF60_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF61_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF61_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF61_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF61_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF61_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF61_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF62_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF62_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF62_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF62_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF62_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF62_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHB_BUF63_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHB_BUF63_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHB_BUF63_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHB_BUF63_RX_LID                                     /* Frame Identifier */
#define CAN_CHB_BUF63_RX_DLC                                     /* Data lenght code */
#define CAN_CHB_BUF63_RX_EXC          0u                         /* Enabling MB exception task */

/* Tx Buffers */
#define CAN_CHB_BUF0_TX     CAN_BUFF_ENABLE
#define CAN_CHB_BUF0_TX_IDE 0x1u
#define CAN_CHB_BUF0_TX_LID DIAGNOSTIC_RESPONSE_ISCM_ID
#define CAN_CHB_BUF0_TX_DLC DIAGNOSTIC_RESPONSE_ISCM_DLC
#define CAN_CHB_BUF0_TX_EXC 0u
#define CAN_CHB_BUF0_TX_INT 1u

#define CAN_CHB_BUF1_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF1_TX_IDE
#define CAN_CHB_BUF1_TX_LID
#define CAN_CHB_BUF1_TX_DLC
#define CAN_CHB_BUF1_TX_EXC
#define CAN_CHB_BUF1_TX_INT

#define CAN_CHB_BUF2_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF2_TX_IDE
#define CAN_CHB_BUF2_TX_LID
#define CAN_CHB_BUF2_TX_DLC
#define CAN_CHB_BUF2_TX_EXC
#define CAN_CHB_BUF2_TX_INT

#define CAN_CHB_BUF3_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF3_TX_IDE
#define CAN_CHB_BUF3_TX_LID
#define CAN_CHB_BUF3_TX_DLC
#define CAN_CHB_BUF3_TX_EXC
#define CAN_CHB_BUF3_TX_INT

#define CAN_CHB_BUF4_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF4_TX_IDE
#define CAN_CHB_BUF4_TX_LID
#define CAN_CHB_BUF4_TX_DLC
#define CAN_CHB_BUF4_TX_EXC
#define CAN_CHB_BUF4_TX_INT

#define CAN_CHB_BUF5_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF5_TX_IDE
#define CAN_CHB_BUF5_TX_LID
#define CAN_CHB_BUF5_TX_DLC
#define CAN_CHB_BUF5_TX_EXC
#define CAN_CHB_BUF5_TX_INT

#define CAN_CHB_BUF6_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF6_TX_IDE
#define CAN_CHB_BUF6_TX_LID
#define CAN_CHB_BUF6_TX_DLC
#define CAN_CHB_BUF6_TX_EXC
#define CAN_CHB_BUF6_TX_INT

#define CAN_CHB_BUF7_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF7_TX_IDE
#define CAN_CHB_BUF7_TX_LID
#define CAN_CHB_BUF7_TX_DLC
#define CAN_CHB_BUF7_TX_EXC
#define CAN_CHB_BUF7_TX_INT

#define CAN_CHB_BUF8_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF8_TX_IDE
#define CAN_CHB_BUF8_TX_LID
#define CAN_CHB_BUF8_TX_DLC
#define CAN_CHB_BUF8_TX_EXC
#define CAN_CHB_BUF8_TX_INT

#define CAN_CHB_BUF9_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF9_TX_IDE
#define CAN_CHB_BUF9_TX_LID
#define CAN_CHB_BUF9_TX_DLC
#define CAN_CHB_BUF9_TX_EXC
#define CAN_CHB_BUF9_TX_INT

#define CAN_CHB_BUF10_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF10_TX_IDE 
#define CAN_CHB_BUF10_TX_LID 
#define CAN_CHB_BUF10_TX_DLC 
#define CAN_CHB_BUF10_TX_EXC 
#define CAN_CHB_BUF10_TX_INT 

#define CAN_CHB_BUF11_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF11_TX_IDE
#define CAN_CHB_BUF11_TX_LID
#define CAN_CHB_BUF11_TX_DLC
#define CAN_CHB_BUF11_TX_EXC
#define CAN_CHB_BUF11_TX_INT

#define CAN_CHB_BUF12_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF12_TX_IDE
#define CAN_CHB_BUF12_TX_LID
#define CAN_CHB_BUF12_TX_DLC
#define CAN_CHB_BUF12_TX_EXC
#define CAN_CHB_BUF12_TX_INT

#define CAN_CHB_BUF13_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF13_TX_IDE
#define CAN_CHB_BUF13_TX_LID
#define CAN_CHB_BUF13_TX_DLC
#define CAN_CHB_BUF13_TX_EXC
#define CAN_CHB_BUF13_TX_INT

#define CAN_CHB_BUF14_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF14_TX_IDE
#define CAN_CHB_BUF14_TX_LID
#define CAN_CHB_BUF14_TX_DLC
#define CAN_CHB_BUF14_TX_EXC
#define CAN_CHB_BUF14_TX_INT

#define CAN_CHB_BUF15_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF15_TX_IDE
#define CAN_CHB_BUF15_TX_LID
#define CAN_CHB_BUF15_TX_DLC
#define CAN_CHB_BUF15_TX_EXC
#define CAN_CHB_BUF15_TX_INT

#define CAN_CHB_BUF16_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF16_TX_IDE
#define CAN_CHB_BUF16_TX_LID
#define CAN_CHB_BUF16_TX_DLC
#define CAN_CHB_BUF16_TX_EXC
#define CAN_CHB_BUF16_TX_INT

#define CAN_CHB_BUF17_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF17_TX_IDE
#define CAN_CHB_BUF17_TX_LID
#define CAN_CHB_BUF17_TX_DLC
#define CAN_CHB_BUF17_TX_EXC
#define CAN_CHB_BUF17_TX_INT

#define CAN_CHB_BUF18_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF18_TX_IDE
#define CAN_CHB_BUF18_TX_LID
#define CAN_CHB_BUF18_TX_DLC
#define CAN_CHB_BUF18_TX_EXC
#define CAN_CHB_BUF18_TX_INT

#define CAN_CHB_BUF19_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF19_TX_IDE
#define CAN_CHB_BUF19_TX_LID
#define CAN_CHB_BUF19_TX_DLC
#define CAN_CHB_BUF19_TX_EXC
#define CAN_CHB_BUF19_TX_INT

#define CAN_CHB_BUF20_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF20_TX_IDE
#define CAN_CHB_BUF20_TX_LID
#define CAN_CHB_BUF20_TX_DLC
#define CAN_CHB_BUF20_TX_EXC
#define CAN_CHB_BUF20_TX_INT

#define CAN_CHB_BUF21_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF21_TX_IDE
#define CAN_CHB_BUF21_TX_LID
#define CAN_CHB_BUF21_TX_DLC
#define CAN_CHB_BUF21_TX_EXC
#define CAN_CHB_BUF21_TX_INT

#define CAN_CHB_BUF22_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF22_TX_IDE
#define CAN_CHB_BUF22_TX_LID
#define CAN_CHB_BUF22_TX_DLC
#define CAN_CHB_BUF22_TX_EXC
#define CAN_CHB_BUF22_TX_INT

#define CAN_CHB_BUF23_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF23_TX_IDE
#define CAN_CHB_BUF23_TX_LID
#define CAN_CHB_BUF23_TX_DLC
#define CAN_CHB_BUF23_TX_EXC
#define CAN_CHB_BUF23_TX_INT

#define CAN_CHB_BUF24_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF24_TX_IDE
#define CAN_CHB_BUF24_TX_LID
#define CAN_CHB_BUF24_TX_DLC
#define CAN_CHB_BUF24_TX_EXC
#define CAN_CHB_BUF24_TX_INT

#define CAN_CHB_BUF25_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF25_TX_IDE
#define CAN_CHB_BUF25_TX_LID
#define CAN_CHB_BUF25_TX_DLC
#define CAN_CHB_BUF25_TX_EXC
#define CAN_CHB_BUF25_TX_INT

#define CAN_CHB_BUF26_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF26_TX_IDE
#define CAN_CHB_BUF26_TX_LID
#define CAN_CHB_BUF26_TX_DLC
#define CAN_CHB_BUF26_TX_EXC
#define CAN_CHB_BUF26_TX_INT

#define CAN_CHB_BUF27_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF27_TX_IDE
#define CAN_CHB_BUF27_TX_LID
#define CAN_CHB_BUF27_TX_DLC
#define CAN_CHB_BUF27_TX_EXC
#define CAN_CHB_BUF27_TX_INT

#define CAN_CHB_BUF28_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF28_TX_IDE
#define CAN_CHB_BUF28_TX_LID
#define CAN_CHB_BUF28_TX_DLC
#define CAN_CHB_BUF28_TX_EXC
#define CAN_CHB_BUF28_TX_INT

#define CAN_CHB_BUF29_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF29_TX_IDE
#define CAN_CHB_BUF29_TX_LID
#define CAN_CHB_BUF29_TX_DLC
#define CAN_CHB_BUF29_TX_EXC
#define CAN_CHB_BUF29_TX_INT

#define CAN_CHB_BUF30_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF30_TX_IDE
#define CAN_CHB_BUF30_TX_LID
#define CAN_CHB_BUF30_TX_DLC
#define CAN_CHB_BUF30_TX_EXC
#define CAN_CHB_BUF30_TX_INT

#define CAN_CHB_BUF31_TX     CAN_BUFF_DISABLE
#define CAN_CHB_BUF31_TX_IDE
#define CAN_CHB_BUF31_TX_LID
#define CAN_CHB_BUF31_TX_DLC
#define CAN_CHB_BUF31_TX_EXC
#define CAN_CHB_BUF31_TX_INT

#endif
#endif


#endif  //CAN_CHB_EN

/****************************************************************************
     TTCAN Engine C : specific configuration  
 ****************************************************************************/
#define CAN_CHC_EN        1u
#define CAN_CHC_BR        CAN_BR_1000k
#define CAN_CHC_LOOPBACK  CAN_NO_LOOPBACK

//Define here specific configuration compliant with customer request
#if (CAN_CHC_BR == CAN_C_BR_CUSTOMER) //todo da sistemare
//#undef CAN_C_CR_CUSTOM_CFG  0x01AC0017u  MC, TBA
#endif
//#undef CAN_CHC_LASTUSED_MB_POLL       /*  MC, TBA 0 */
#define CAN_CHC_TXRX_EXC 1u     /* CAN C Tx/Rx Exception enable - used with KWP */

/* sending message on CCP */
#define ID_CCP_DTO 0x200u
/* receiving message on CCP */
#define ID_CCP_CRO 0x201u

#if CAN_CHC_EN 

#define MCAN_3_DEDICATED_RXMB_USED  (MCAN_3_STD_FILTER + MCAN_3_XTD_FILTER)  //Number of used contiguous  dedicated rx message buffers

/* Rx Buffers */
#define CAN_CHC_BUF0_RX              CAN_BUFF_ENABLE            /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF0_RX_IDE          0x0u                       /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF0_RX_IDE_NUM_TYPE 0u                         /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF0_RX_LID          ID_CCP_CRO                 /* Frame Identifier */
#define CAN_CHC_BUF0_RX_DLC          8u                         /* Data lenght code */
#define CAN_CHC_BUF0_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF1_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF1_RX_IDE          0x1u                       /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF1_RX_IDE_NUM_TYPE 0u                         /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF1_RX_LID          0x45u                      /* Frame Identifier */
#define CAN_CHC_BUF1_RX_DLC          8u                         /* Data lenght code */
#define CAN_CHC_BUF1_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF2_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF2_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF2_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF2_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF2_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF2_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF3_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF3_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF3_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF3_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF3_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF3_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF4_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF4_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF4_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF4_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF4_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF4_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF5_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF5_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF5_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF5_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF5_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF5_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF6_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF6_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF6_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF6_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF6_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF6_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF7_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF7_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF7_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF7_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF7_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF7_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF8_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF8_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF8_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF8_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF8_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF8_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF9_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF9_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF9_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF9_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF9_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF9_RX_EXC          0u                         /* Enabling MB exception task */
#define CAN_CHC_BUF10_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF10_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF10_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF10_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF10_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF10_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF11_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF11_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF11_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF11_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF11_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF11_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF12_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF12_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF12_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF12_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF12_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF12_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF13_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF13_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF13_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF13_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF13_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF13_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF14_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF14_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF14_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF14_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF14_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF14_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF15_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF15_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF15_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF15_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF15_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF15_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF16_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF16_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF16_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF16_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF16_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF16_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF17_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF17_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF17_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF17_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF17_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF17_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF18_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF18_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF18_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF18_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF18_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF18_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF19_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF19_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF19_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF19_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF19_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF19_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF20_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF20_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF20_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF20_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF20_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF20_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF21_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF21_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF21_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF21_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF21_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF21_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF22_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF22_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF22_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF22_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF22_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF22_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF23_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF23_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF23_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF23_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF23_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF23_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF24_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF24_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF24_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF24_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF24_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF24_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF25_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF25_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF25_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF25_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF25_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF25_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF26_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF26_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF26_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF26_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF26_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF26_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF27_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF27_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF27_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF27_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF27_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF27_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF28_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF28_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF28_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF28_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF28_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF28_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF29_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF29_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF29_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF29_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF29_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF29_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF30_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF30_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF30_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF30_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF30_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF30_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF31_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF31_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF31_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF31_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF31_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF31_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF32_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF32_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF32_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF32_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF32_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF32_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF33_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF33_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF33_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF33_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF33_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF33_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF34_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF34_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF34_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF34_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF34_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF34_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF35_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF35_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF35_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF35_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF35_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF35_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF36_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF36_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF36_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF36_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF36_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF36_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF37_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF37_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF37_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF37_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF37_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF37_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF38_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF38_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF38_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF38_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF38_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF38_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF39_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF39_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF39_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF39_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF39_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF39_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF40_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF40_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF40_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF40_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF40_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF40_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF41_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF41_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF41_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF41_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF41_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF41_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF42_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF42_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF42_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF42_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF42_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF42_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF43_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF43_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF43_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF43_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF43_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF43_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF44_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF44_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF44_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF44_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF44_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF44_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF45_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF45_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF45_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF45_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF45_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF45_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF46_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF46_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF46_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF46_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF46_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF46_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF47_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF47_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF47_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF47_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF47_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF47_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF48_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF48_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF48_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF48_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF48_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF48_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF49_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF49_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF49_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF49_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF49_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF49_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF50_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF50_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF50_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF50_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF50_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF50_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF51_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF51_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF51_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF51_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF51_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF51_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF52_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF52_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF52_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF52_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF52_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF52_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF53_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF53_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF53_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF53_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF53_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF53_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF54_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF54_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF54_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF54_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF54_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF54_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF55_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF55_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF55_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF55_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF55_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF55_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF56_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF56_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF56_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF56_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF56_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF56_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF57_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF57_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF57_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF57_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF57_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF57_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF58_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF58_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF58_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF58_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF58_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF58_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF59_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF59_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF59_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF59_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF59_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF59_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF60_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF60_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF60_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF60_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF60_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF60_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF61_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF61_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF61_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF61_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF61_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF61_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF62_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF62_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF62_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF62_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF62_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF62_RX_EXC          0u                         /* Enabling MB exception task */

#define CAN_CHC_BUF63_RX              CAN_BUFF_DISABLE           /* Activates MB config with chosen Frame direction */
#define CAN_CHC_BUF63_RX_IDE                                     /* CAN Frame Format: 0->standard; 1->extended */
#define CAN_CHC_BUF63_RX_IDE_NUM_TYPE                            /* Relative Number of Rx Buffer according to its frame format */
#define CAN_CHC_BUF63_RX_LID                                     /* Frame Identifier */
#define CAN_CHC_BUF63_RX_DLC                                     /* Data lenght code */
#define CAN_CHC_BUF63_RX_EXC          0u                         /* Enabling MB exception task */

/* Tx Buffers */
#define CAN_CHC_BUF0_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF0_TX_IDE 0x0u
#define CAN_CHC_BUF0_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF0_TX_DLC 8u
#define CAN_CHC_BUF0_TX_EXC 0u
#define CAN_CHC_BUF0_TX_INT 0u

#define CAN_CHC_BUF1_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF1_TX_IDE 0x0u
#define CAN_CHC_BUF1_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF1_TX_DLC 8u
#define CAN_CHC_BUF1_TX_EXC 0u
#define CAN_CHC_BUF1_TX_INT 0u

#define CAN_CHC_BUF2_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF2_TX_IDE 0x0u
#define CAN_CHC_BUF2_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF2_TX_DLC 8u
#define CAN_CHC_BUF2_TX_EXC 0u
#define CAN_CHC_BUF2_TX_INT 0u

#define CAN_CHC_BUF3_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF3_TX_IDE 0x0u
#define CAN_CHC_BUF3_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF3_TX_DLC 8u
#define CAN_CHC_BUF3_TX_EXC 0u
#define CAN_CHC_BUF3_TX_INT 0u

#define CAN_CHC_BUF4_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF4_TX_IDE 0x0u
#define CAN_CHC_BUF4_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF4_TX_DLC 8u
#define CAN_CHC_BUF4_TX_EXC 0u
#define CAN_CHC_BUF4_TX_INT 0u

#define CAN_CHC_BUF5_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF5_TX_IDE 0x0u
#define CAN_CHC_BUF5_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF5_TX_DLC 8u
#define CAN_CHC_BUF5_TX_EXC 0u
#define CAN_CHC_BUF5_TX_INT 0u

#define CAN_CHC_BUF6_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF6_TX_IDE 0x0u
#define CAN_CHC_BUF6_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF6_TX_DLC 8u
#define CAN_CHC_BUF6_TX_EXC 0u
#define CAN_CHC_BUF6_TX_INT 0u

#define CAN_CHC_BUF7_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF7_TX_IDE 0x0u
#define CAN_CHC_BUF7_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF7_TX_DLC 8u
#define CAN_CHC_BUF7_TX_EXC 0u
#define CAN_CHC_BUF7_TX_INT 0u

#define CAN_CHC_BUF8_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF8_TX_IDE 0x0u
#define CAN_CHC_BUF8_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF8_TX_DLC 8u
#define CAN_CHC_BUF8_TX_EXC 0u
#define CAN_CHC_BUF8_TX_INT 0u

#define CAN_CHC_BUF9_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF9_TX_IDE 0x0u
#define CAN_CHC_BUF9_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF9_TX_DLC 8u
#define CAN_CHC_BUF9_TX_EXC 0u
#define CAN_CHC_BUF9_TX_INT 0u

#define CAN_CHC_BUF10_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF10_TX_IDE 0x0u
#define CAN_CHC_BUF10_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF10_TX_DLC 8u
#define CAN_CHC_BUF10_TX_EXC 0u
#define CAN_CHC_BUF10_TX_INT 0u

#define CAN_CHC_BUF11_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF11_TX_IDE 0x0u
#define CAN_CHC_BUF11_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF11_TX_DLC 8u
#define CAN_CHC_BUF11_TX_EXC 0u
#define CAN_CHC_BUF11_TX_INT 0u

#define CAN_CHC_BUF12_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF12_TX_IDE 0x0u
#define CAN_CHC_BUF12_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF12_TX_DLC 8u
#define CAN_CHC_BUF12_TX_EXC 0u
#define CAN_CHC_BUF12_TX_INT 0u

#define CAN_CHC_BUF13_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF13_TX_IDE 0x0u
#define CAN_CHC_BUF13_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF13_TX_DLC 8u
#define CAN_CHC_BUF13_TX_EXC 0u
#define CAN_CHC_BUF13_TX_INT 0u

#define CAN_CHC_BUF14_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF14_TX_IDE 0x0u
#define CAN_CHC_BUF14_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF14_TX_DLC 8u
#define CAN_CHC_BUF14_TX_EXC 0u
#define CAN_CHC_BUF14_TX_INT 0u

#define CAN_CHC_BUF15_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF15_TX_IDE 0x0u
#define CAN_CHC_BUF15_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF15_TX_DLC 8u
#define CAN_CHC_BUF15_TX_EXC 0u
#define CAN_CHC_BUF15_TX_INT 0u

#define CAN_CHC_BUF16_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF16_TX_IDE 0x0u
#define CAN_CHC_BUF16_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF16_TX_DLC 8u
#define CAN_CHC_BUF16_TX_EXC 0u
#define CAN_CHC_BUF16_TX_INT 0u

#define CAN_CHC_BUF17_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF17_TX_IDE 0x0u
#define CAN_CHC_BUF17_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF17_TX_DLC 8u
#define CAN_CHC_BUF17_TX_EXC 0u
#define CAN_CHC_BUF17_TX_INT 0u

#define CAN_CHC_BUF18_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF18_TX_IDE 0x0u
#define CAN_CHC_BUF18_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF18_TX_DLC 8u
#define CAN_CHC_BUF18_TX_EXC 0u
#define CAN_CHC_BUF18_TX_INT 0u

#define CAN_CHC_BUF19_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF19_TX_IDE 0x0u
#define CAN_CHC_BUF19_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF19_TX_DLC 8u
#define CAN_CHC_BUF19_TX_EXC 0u
#define CAN_CHC_BUF19_TX_INT 0u

#define CAN_CHC_BUF20_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF20_TX_IDE 0x0u
#define CAN_CHC_BUF20_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF20_TX_DLC 8u
#define CAN_CHC_BUF20_TX_EXC 0u
#define CAN_CHC_BUF20_TX_INT 0u

#define CAN_CHC_BUF21_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF21_TX_IDE 0x0u
#define CAN_CHC_BUF21_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF21_TX_DLC 8u
#define CAN_CHC_BUF21_TX_EXC 0u
#define CAN_CHC_BUF21_TX_INT 0u

#define CAN_CHC_BUF22_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF22_TX_IDE 0x0u
#define CAN_CHC_BUF22_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF22_TX_DLC 8u
#define CAN_CHC_BUF22_TX_EXC 0u
#define CAN_CHC_BUF22_TX_INT 0u

#define CAN_CHC_BUF23_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF23_TX_IDE 0x0u
#define CAN_CHC_BUF23_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF23_TX_DLC 8u
#define CAN_CHC_BUF23_TX_EXC 0u
#define CAN_CHC_BUF23_TX_INT 0u

#define CAN_CHC_BUF24_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF24_TX_IDE 0x0u
#define CAN_CHC_BUF24_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF24_TX_DLC 8u
#define CAN_CHC_BUF24_TX_EXC 0u
#define CAN_CHC_BUF24_TX_INT 0u

#define CAN_CHC_BUF25_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF25_TX_IDE 0x0u
#define CAN_CHC_BUF25_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF25_TX_DLC 8u
#define CAN_CHC_BUF25_TX_EXC 0u
#define CAN_CHC_BUF25_TX_INT 0u

#define CAN_CHC_BUF26_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF26_TX_IDE 0x0u
#define CAN_CHC_BUF26_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF26_TX_DLC 8u
#define CAN_CHC_BUF26_TX_EXC 0u
#define CAN_CHC_BUF26_TX_INT 0u

#define CAN_CHC_BUF27_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF27_TX_IDE 0x0u
#define CAN_CHC_BUF27_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF27_TX_DLC 8u
#define CAN_CHC_BUF27_TX_EXC 0u
#define CAN_CHC_BUF27_TX_INT 0u

#define CAN_CHC_BUF28_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF28_TX_IDE 0x0u
#define CAN_CHC_BUF28_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF28_TX_DLC 8u
#define CAN_CHC_BUF28_TX_EXC 0u
#define CAN_CHC_BUF28_TX_INT 0u

#define CAN_CHC_BUF29_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF29_TX_IDE 0x0u
#define CAN_CHC_BUF29_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF29_TX_DLC 8u
#define CAN_CHC_BUF29_TX_EXC 0u
#define CAN_CHC_BUF29_TX_INT 0u

#define CAN_CHC_BUF30_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF30_TX_IDE 0x0u
#define CAN_CHC_BUF30_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF30_TX_DLC 8u
#define CAN_CHC_BUF30_TX_EXC 0u
#define CAN_CHC_BUF30_TX_INT 0u

#define CAN_CHC_BUF31_TX     CAN_BUFF_ENABLE
#define CAN_CHC_BUF31_TX_IDE 0x0u
#define CAN_CHC_BUF31_TX_LID ID_CCP_DTO
#define CAN_CHC_BUF31_TX_DLC 8u
#define CAN_CHC_BUF31_TX_EXC 0u
#define CAN_CHC_BUF31_TX_INT 0u

#endif  //CAN_CHC_EN

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

#pragma ghs endnomisra

#endif  /* _CAN_BR_H */

/****************************************************************************
 ****************************************************************************/
