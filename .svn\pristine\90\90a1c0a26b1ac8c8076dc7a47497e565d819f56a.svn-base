
    print "Script to merge two binary files\n";

    # check parameters or die
    die "Usage: bin_append.pl -infiles [filename_1] [filename_2] -offset [offset_infile2] -target [filename_output]\n" if $#ARGV !=6;

    use Cwd;

    my $offsetF0 = $ARGV[4];
    my $hex_offsetF0 = hex $offsetF0;

    $dir = getcwd();
    opendir (DIR, $dir) or die "Can not open directory $dir";
    #print "Working directory: $dir \n"   ;


    $calib = $ARGV[1];
    open (FILE,  $calib) or die "Can't open binary file: $calib!";  


    $appl = $ARGV[2];;
    open (FILE_F0,  $appl) or die "Can't open binary file: $appl!"; 

    open (OFILE, ">$ARGV[6]") or die "Can't open binary file: $! ";
    
    binmode  (FILE);
    binmode  (FILE_F0);
    binmode  (OFILE);
    
    # ----------------------> Parsing file <----------------------
    # copying general cal bin file until reached the offset value
    while (read (FILE, $buffer, 1) ) 
    {
        my $currentPosition = tell(FILE);
        if ($currentPosition != $hex_offsetF0)
        {
            print OFILE $buffer ;
            #print "currentPosition: $currentPosition ; hex_offsetF0 $hex_offsetF0\n";
        }
        else
        {
            print OFILE $buffer ;
            last;
        }
        $cnt = $cnt + 1 ;
    }
    
    #copy FILE_F0 calibrations at the end of file
    while (read (FILE_F0, $buffer0, 1) ) 
    { 
        print OFILE $buffer0 ;
        $cnt0 = $cnt0 + 1 ;
        $currentPosition2 = tell(FILE_F0);
    }
#print "currentPosition: $currentPosition2\n";
#At this point the BINARY ends with no worries about linker file section alignment
#To fill the gap to 16 bytes alignment with "0x00" as gmem utility would make, current operations are provided
    my $resto = $currentPosition2 % 16;
#print "resto=$resto\n";
    $fillCounter = 16- $resto;
#print "fillCounter=$fillCounter\n";
    $fillString = "0";
    
    while ($fillCounter > 0 && $resto != 0)
    {
        print OFILE pack "H*" => $fillString;
        $fillCounter--;
#print "fillCounter=$fillCounter\n";
    }

    # close files 
    close (FILE);
    close (FILE_F0);
    close (OFILE);        

    print "Binary files merged \n";