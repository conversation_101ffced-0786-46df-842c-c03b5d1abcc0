#**************************************************************************/
#* FILE NAME: intc_sw_handlers.s            COPYRIGHT (c) Freescale 2004  */
#*                                                All Rights Reserved     */
#* DESCRIPTION:                                                           */
#*        This file creates prolog, epilog for C ISR and enables nested   */
#*        interrupts. This file starts in memory at the beginning of the  */
#*        ".xcptn" section designated by the label "IVOR4Handler".        */
#* WARNING:  This stack frame does not save the SPE s Accumulator, which  */
#*           is required if SPE instructions are used in ISRs.   If SPE   */
#*           instructions are used, the stack frame must include the      */
#*           accumulator, and prologue and epilogue must be modified.     */
#=========================================================================*/
#*                                                                        */
#* REV      AUTHOR       DATE       DESCRIPTION OF CHANGE                 */
#* ---   -----------   ----------   ---------------------                 */
#* 1.0:  S. Mihalik    23/Apr/04     Initial version                      */
#* 1.1:  B. Terry      29/Jul/04    Modified read of IACKR using          */
#*                                  pointer to determine vector  address. */
#* 1.2   G. Jackson    30/Jul/04    Added ".xcptn" section designation    */
#*                                   for placement into mpc5500cfg.       */
#* 1.3   G. Jackson    12/Oct/04    Green Hills now does not require      */
#*                                    quotation marks around the section  */
#*                                  Added syntax to generate symbols for  */
#*                                    debug.                              */
#**************************************************************************/

// Designation for the Green Hills compiler

    .include "../tree/COMMON/CONFIG/ASM/mpc5500_usrdefs.inc"

    .globl   IVOR0_c2
    .globl   IVOR1_c2
    .globl   IVOR2_c2
    .globl   IVOR3_c2
    .globl   IVOR4_c2
    .globl   IVOR5_c2
    .globl   IVOR6_c2
    .globl   IVOR7_c2
    .globl   IVOR8_c2
    .globl   IVOR9_c2
    .globl   IVOR10_c2
    .globl   IVOR11_c2
    .globl   IVOR12_c2
    .globl   IVOR13_c2
    .globl   IVOR14_c2
    .globl   IVOR15_c2
    .globl   IVOR0Handler_c2
    .globl   IVOR1Handler_c2
    .globl   IVOR2Handler_c2
    .globl   IVOR3Handler_c2
    .globl   IVOR4Handler_c2
    .globl   IVOR5Handler_c2
    .globl   IVOR6Handler_c2
    .globl   IVOR7Handler_c2
    .globl   IVOR8Handler_c2
    .globl   IVOR9Handler_c2
    .globl   IVOR10Handler_c2
    .globl   IVOR11Handler_c2
    .globl   IVOR12Handler_c2
    .globl   IVOR13Handler_c2
    .globl   IVOR14Handler_c2
    .globl   IVOR15Handler_c2

    .extern  IVOR1_Manager_c2              #  defined in Ivor.c file
    .extern  IVOR_Common_Manager_c2        #  defined in Ivor.c file
    
######################################################################################################
# The following variables are user facilities to detect the type of Ivor # occurred and its own info #
######################################################################################################

    .extern  IvorIndex_c2                  #  defined in Ivor.c file
    .extern  CSRR0_Value_c2                #  defined in Ivor.c file
    .extern  CSRR1_Value_c2                #  defined in Ivor.c file
    .extern  SRR0_Value_c2                 #  defined in Ivor.c file
    .extern  SRR1_Value_c2                 #  defined in Ivor.c file
    .extern  SPR_ESRValue_c2               #  defined in Ivor.c file
    .extern  SPR_DEARValue_c2              #  defined in Ivor.c file
    .extern  SPR_MCSRValue_c2              #  defined in Ivor.c file
    .extern  MCSRR0_Value_c2               #  defined in Ivor.c file
    .extern  MCSRR1_Value_c2               #  defined in Ivor.c file   
    
####################################################
#   This is the start of the .xcptn_c2 section.    #
####################################################
    .section .xcptn_c2,"axv"            // The "ax" generates symbols for debug
    .vle
    .align 4                      # Align IVOR handlers on a 16 byte boundary 

    .equ  INTC_CPR_C2,   0xFC040018  # C2 - Current priority register address
    .equ  INTC_IACKR_C2, 0xFC040028  # C2 - Interrupt Acknowledge Register address
    .equ  INTC_EOIR_C2,  0xFC040038  # C2 - End Of Interrupt Register address

####################################################
#                  IVOR Vector                     #
####################################################

IVOR0_c2:  e_b           IVOR0Handler_c2
        .align      4
IVOR1_c2:  e_b           IVOR1Handler_c2
        .align      4
IVOR2_c2:  e_b           IVOR2Handler_c2
        .align      4
IVOR3_c2:  e_b           IVOR3Handler_c2
        .align      4
IVOR4_c2:  e_b           IVOR4Handler_c2
        .align      4
IVOR5_c2:  e_b           IVOR5Handler_c2
        .align      4
IVOR6_c2:  e_b           IVOR6Handler_c2
        .align      4
IVOR7_c2:  e_b           IVOR7Handler_c2
        .align      4
IVOR8_c2:  e_b           IVOR8Handler_c2
        .align      4
IVOR9_c2:  e_b           IVOR9Handler_c2
        .align      4
IVOR10_c2: e_b           IVOR10Handler_c2
        .align      4
IVOR11_c2: e_b           IVOR11Handler_c2
        .align      4
IVOR12_c2: e_b           IVOR12Handler_c2
        .align      4
IVOR13_c2: e_b           IVOR13Handler_c2
        .align      4
IVOR14_c2: e_b           IVOR14Handler_c2
        .align      4
IVOR15_c2: e_b           IVOR15Handler_c2


######################################
# IVOR 0 - Critical Input Interrupt  #
######################################
IVOR0Handler_c2:
    e_b  IVOR0Handler_c2
##################################

    .align 4

####################################
# IVOR 1 - Machine Check Interrupt #
####################################
IVOR1Handler_c2:
#ECC flash management
    .extern ECCflash_ReturnJumpingFaultInstr # Shared between Ivor2 handler and 
                                          # checkFaultyAddrFromLastIvor2 function
                                          # to make the Ivor2 return to the address
                                          # of the faulty instruction + 4,
                                          # to not execute it again.

    e_stwu r1, -0x54 (r1)         # Create stack frame and store back chain
    e_stw  r0, 0x24 (r1)          # Save working registers R0
    mfLR   r0                     # Store LR (Store now since LR will be used for ISR Vector)
    e_stw  r0, 0x14 (r1)
    e_stw  r3, 0x28 (r1)          # Store a working register R3
  
### HandlerSaveContext_ph1 CUSTOMIZATION to change SRR0 to the next address ###
  
    mfSRR1 r0                     # get SRR1 
    e_stw  r0, 0x10 (r1)          # save it on the stack

    mfSRR0 r0                     # get SRR0
    e_stw  r0, 0x0C (r1)          # save it on the stack


    e_lis  r3,    ECCflash_ReturnJumpingFaultInstr@h
    ori    r3,r3, ECCflash_ReturnJumpingFaultInstr@l
    e_lbz  r0, 0x0(r3)            # Get an adder for the SRRO address to accomplish
    mr     r3, r0                 # ivor2 behaviour explained in the following comment
    mfMCSRR0  r0                  # get MCSRR0 (storeaddress of some instruction that was executing)
    add    r3, r0, r3     #MC,TbV     # adding 4 in case of EEPROM probably corrupted,
                                  # because we have to continue after interrupt WITHOUT
                                  # repeat the faulty instruction. The saved MCSRR0
                                  # will be the value of the PC when IVOR2 returns.
                                  # It is incremented here by 4(an instruction)
                                  # and restored when IVOR2_Manager ends
    e_stw  r3, 0x74 (r1) # save it on the stack

    mfCR   r0                     # get CR
    e_stw  r0, 0x20 (r1)          # save it on the stack
  
######### end of HandlerSaveContext_ph1 CUSTOMIZATION
  
    e_bl   HandlerSaveContext_ph2    
    e_li   r4,1  
    e_lis  r3,IvorIndex_c2@ha
    e_add16i r3,r3,IvorIndex_c2@l
    e_stb  r4,0(r3)
 
    mfMCSRR1 r4                     # get SRR1 
    e_lis  r3,MCSRR1_Value_c2@h 
    ori    r3,r3,MCSRR1_Value_c2@l
    e_stw  r4,0(r3)    

    mfMCSRR0 r4                     # get SRR0 
    e_lis  r3,MCSRR0_Value_c2@h
    ori    r3,r3,MCSRR0_Value_c2@l
    e_stw  r4,0(r3)

    mfspr  r4,573                    # get MCAR; SPR_MCAR=573
    e_lis  r3,SPR_MCARValue_c2@h
    ori    r3,r3,SPR_MCARValue_c2@l
    e_stw  r4,0(r3)

    mfspr r4,572                    # get MCSR; SPR_MCSR=572 
    e_lis r3,SPR_MCSRValue_c2@h
    ori   r3,r3,SPR_MCSRValue_c2@l
    e_stw r4,0(r3)

    mfspr r4,62                     # get ESR; SPR_ESR=62; Unchanged
    e_lis r3,SPR_ESRValue_c2@h
    ori   r3,r3,SPR_ESRValue_c2@l
    e_stw r4,0(r3)

    #mfspr r4,61                    # get ESR; SPR_DEAR=61;Unchanged
    #e_lis r3,SPR_DEARValue_c2@h
    #ori   r3,r3,SPR_DEARValue_c2@l
    #e_stw r4,0(r3)

    e_lis  r3,IVOR1_Manager_c2@ha
    e_add16i r3,r3,IVOR1_Manager_c2@l
    mtlr r3
    se_blrl

# MC, This section is executed only when Ivor is triggered by check of memory at the startup;
# in all the other cases IVOR1_Manager trigger a shutdown

    mbar 0                        #sinchronization
                           
    e_bl  HandlerRestoreContext_ph1
    e_bl  HandlerRestoreContext_ph2

    e_lwz  r0,  0x74 (r1)        
    mtSRR0 r0                     # MC - Restore SRR0 with MCSRR0 + ECCflash_ReturnJumpingFaultInstr 
                                  # to continue after interrupt WITHOUT repeat the faulty instruction 
                                  # of check of memory at the startup
    
    e_lis  r3,0xFFFFFFFF@h
    ori    r3,r3,0xFFFFFFFF@l
    mtmcsr r3                     # MC - Clean of MCSR;  
                                  # necessary when Ivor is triggered during check of memory at the startup,
                                  # that not execute a reset leaving the register written
                                  # and the core not able to store the address of the istruction 
                                  # that will trigger the next Ivor
    
    e_lwz  r0,  0x14 (r1)       # Restore LR
    mtLR   r0
    e_lwz  r0,  0x24 (r1)       # Restore working register
    e_add16i r1,  r1, 0x54      # Restore space on stack

    se_rfi

##################################    

    .align 4

####################################
# IVOR 2 - Data Storage Interrupt  #
####################################
IVOR2Handler_c2:
prolog1:                          # PROLOGUE 
    e_stwu  r1, -0x50 (r1)        # create stack frame and store back chain
    e_stw   r0, 0x24 (r1)         # save working registers R0
    e_stw   r3, 0x28 (r1)         # store a working register
    mfLR    r0                    # store LR (Store now since LR will be used)
    e_stw   r0, 0x14 (r1)        

    e_li    r0, 2                 # load in R0 IVOR index
    e_lis   r3,IvorIndex_c2@ha
    e_add16i  r3,r3,IvorIndex_c2@l
    e_stb   r0,0(r3)

    mfSRR1  r0                     # get CSRR1 
    e_stw   r0, 0x10 (r1)          # save it on the stack
    e_lis   r3, SRR1_Value_c2@h
    ori     r3, r3, SRR1_Value_c2@l
    e_stw   r0, 0(r3)

    mfSRR0  r0                    # get CSRR0 
    e_stw   r0, 0x0C (r1)         # save it on the stack
    e_lis   r3, SRR0_Value_c2@h
    ori     r3, r3, SRR0_Value_c2@l
    e_stw   r0, 0(r3) 
    mfCR    r0                    # get CR
    e_stw   r0,  0x20 (r1)        # save it on the stack
  
    mfspr   r0, 61                # get DEAR; SPR_DEAR=61
    e_lis   r3, SPR_DEARValue_c2@h
    ori     r3, r3, SPR_DEARValue_c2@l
    e_stw   r0, 0(r3)

    mfspr   r0, 572	              # get MCSR; SPR_MCSR=572; unchanged
    e_lis   r3, SPR_MCSRValue_c2@h
    ori     r3, r3, SPR_MCSRValue_c2@l
    e_stw   r0, 0(r3)

    mfspr   r4,62                 # get ESR; SPR_ESR=62
    e_lis   r3,SPR_ESRValue_c2@h
    ori     r3,r3,SPR_ESRValue_c2@l
    e_stw   r4,0(r3)

    e_bl    HandlerSaveContext_ph2 # branch to routine that save gprs contents

    e_lis   r3, IVOR_Common_Manager_c2@ha
    e_add16i  r3, r3, IVOR_Common_Manager_c2@l
    mtLR    r3                    # store ISR address to LR to use for branching later
    se_blrl                       # branch to ISR, but return here
epilog1:                          # EPILOGUE
#                                 # STEP 6 :  RESTORE CONTEXT
    mbar 0                        # ensure interrupt flag has finished clearing

    e_bl    HandlerRestoreContext_ph1

    e_lwz   r0, 0x20 (r1)         # Restore CR
    mtcrf   0xff, r0
    e_lwz   r3,  0x28 (r1)        # restore R3 gprs register
    e_lwz   r4,  0x2C (r1)        # restore R4 gprs register
    e_lwz   r0,  0x0C (r1)        # Restore CSRR0
    mtCSRR0 r0
    e_lwz   r0,  0x10 (r1)        # Restore CSRR1
    mtCSRR1 r0
    e_lwz   r0,  0x14 (r1)        # restore LR
    mtLR    r0 
    e_lwz   r0,  0x24 (r1)        # restore working register
    e_add16i  r1,  r1, 0x50         # restore space on stack
    rfci                          # restores machine state, including reenabling
                                  # critical interrupts MSR[CE].

################################################################################    

    .align 4

###########################################
# IVOR 3 - Instruction Storage Interrupt  #
###########################################
IVOR3Handler_c2:
    e_li   r4,3  
    e_b    NCI_Handler

##################################    

    .align 4

###########################################
# IVOR 4 - External Input Interrupt       #
###########################################
IVOR4Handler_c2:
prolog4:                          # PROLOGUE 
    e_stwu r1, -0x50 (r1)         # Create stack frame and store back chain
    e_stw  r0, 0x24 (r1)          # Save working registers R0
    mfLR   r0                     # Store LR (Store now since LR will be used for ISR Vector)
    e_stw  r0, 0x14 (r1)
    e_stw  r3, 0x28 (r1)          # Store a working register R3

    e_bl   HandlerSaveContext_ph1

    e_lis  r3, INTC_IACKR_C2@h     # Store address of IACKR in r3
    ori    r3, r3, INTC_IACKR_C2@l
    e_lwz  r3, 0(r3)               # Store contents of IACKR in r3 (this is vector table
                                   # address)
    e_lwz  r0, 0(r3)               # Read ISR address from ISR Vector Table address
    e_stw  r0, 0x08(r1)

    #lis    r0, 0x02008200@h       # patch for SPE functionalities
    #ori   r0,r0,0x02008200@l
    #mtmsr  r0

    wrteei 1                       # Enable interrupts; Set MSR[EE]=1 (must wait a couple clocks after reading IACKR)

    e_bl HandlerSaveContext_ph2     

    e_lwz r0,  0x8(r1)
    mtLR  r0                       # Store ISR address to LR to use for branching later
    se_blrl                        # Branch to ISR, but return here

epilog4:                           # EPILOGUE
                                   # STEP 6 :  RESTORE CONTEXT
    mbar 0                         # Ensure interrupt flag has finished clearing
                                   # before writing to INTC_EIOR
    e_bl    HandlerRestoreContext_ph1

    wrteei 0                       # Disable interrupts

    e_li     r3,0
    e_lis    r4, INTC_EOIR_C2@ha         # Load upper half of EIOR address to r4
    e_add16i r4, r4, INTC_EOIR_C2@l    # Load lower half of EIOR address to R4
    e_stw    r3, 0(r4)                   # Write 0 to INTC_EOIR_C2, address 0xFFF4 8018

    e_bl    HandlerRestoreContext_ph2

    e_lwz r0, 0x14 (r1)         # Restore LR
    mtLR  r0
    e_lwz r0, 0x24 (r1)         # Restore working register
    e_add16i r1, r1, 0x50       # Restore space on stack
    se_rfi                      # End of Interrupt - re-enables interrupts.

##################################    

    .align 4

###########################################
# IVOR 5 - Alignment Interrupt            #
###########################################
IVOR5Handler_c2:
    e_li   r4,5  
    e_b    NCI_Handler
    
###########################################    

    .align 4

###########################################
# IVOR 6 - Program Interrupt              #
###########################################
IVOR6Handler_c2:
    e_li   r4,6  
    e_b    NCI_Handler

###########################################    

    .align 4

###########################################
# IVOR 7 - Performance Monitor Interrupt  #
###########################################
IVOR7Handler_c2:
    e_li   r4,7  
    e_b    NCI_Handler

##########################################    

    .align 4

###########################################
# IVOR 8 - System Call Interrupt          #
###########################################
IVOR8Handler_c2:
    e_li   r4,8  
    e_b    NCI_Handler
 
###########################################    

    .align 4

###########################################
# IVOR 9 - Debug Interrupt                #
###########################################
IVOR9Handler_c2:
    e_li   r4,9  
    e_b    NCI_Handler

###########################################    

    .align 4

####################################################
# IVOR 10 - Embedded Floating-point Data Interrupt #
#################################################### 
IVOR10Handler_c2:
    e_li   r4,10  
    e_b    NCI_Handler

####################################################    

    .align 4

#####################################################
# IVOR 11 - Embedded Floating-point Round Interrupt #
#####################################################
IVOR11Handler_c2:
    e_li   r4,11  
    e_b    NCI_Handler

##################################    

    .align 4

#####################################################################################
# IVOR 12 - Embedded Floating-point Unavailable - reserved for future processor use #
##################################################################################### 
IVOR12Handler_c2:
    e_b IVOR12Handler_c2
##############################################

    .align 4

###############################################
# IVOR 13 - reserved for future processor use #
###############################################
IVOR13Handler_c2:
    e_b IVOR13Handler_c2
##############################################

    .align 4

###############################################
# IVOR 14 - reserved for future processor use #
###############################################    
IVOR14Handler_c2:
    e_b IVOR14Handler_c2 
###############################################    

    .align 4

###############################################
# IVOR 15 - reserved for future processor use #
###############################################
IVOR15Handler_c2:
    e_b  IVOR15Handler_c2 

    

################################## 
# HandlerSaveContext - phase 1   #
##################################    
HandlerSaveContext_ph1:
    mfSRR1 r0                   # get SRR1 
    e_stw  r0, 0x10 (r1)        # save it on the stack
    mfSRR0 r0                   # get SRR0
    e_stw  r0, 0x0C (r1)        # save it on the stack
    mfCR   r0                   # get CR
    e_stw  r0, 0x20 (r1)        # save it on the stack
    se_blr

################################## 
# HandlerSaveContext - phase 2   #
##################################   
HandlerSaveContext_ph2:
    e_stw    r12, 0x4C (r1)           # store rest of gprs
    e_stw    r11, 0x48 (r1)           #    |
    e_stw    r10, 0x44 (r1)           #    |
    e_stw    r9,  0x40 (r1)           #    |
    e_stw    r8,  0x3C (r1)           #    |
    e_stw    r7,  0x38 (r1)           #    |
    e_stw    r6,  0x34 (r1)           #    |
    e_stw    r5,  0x30 (r1)           #    |
    e_stw    r4,  0x2c (r1)           #    |
    #evstwwe r12, 0x50 (r1)           #    |
    #evstwwe r11, 0x54 (r1)           #    |
    #evstwwe r10, 0x58 (r1)           #    |
    #evstwwe r9, 0x5C (r1)            #    |
    #evstwwe r8, 0x60 (r1)            #    |
    #evstwwe r7, 0x64 (r1)            #    |
    #evstwwe r6, 0x68 (r1)            #    |
    #evstwwe r5, 0x6C (r1)            #    |
    #evstwwe r4, 0x70 (r1)            #    |

    mfXER r0                         # get XER
    e_stw r0,  0x1C (r1)             # save it on the stack
    mfCTR r0                         # get CTR
    e_stw r0,  0x18 (r1)             # save it on the stack
    se_blr

################################### 
# HandlerRestoreContext - phase 1 #
###################################   
HandlerRestoreContext_ph1:
    e_lwz r0, 0x18 (r1)            # get CTR off stack
    mtCTR r0                       # restore it
    e_lwz r0, 0x1C (r1)            # get XER off stack
    mtXER r0                       # restore it
    #evlwwsplat r5, 0x6C (r1)
    #evlwwsplat r6, 0x68 (r1)
    #evlwwsplat r7, 0x64 (r1)
    #evlwwsplat r8, 0x60 (r1)
    #evlwwsplat r9, 0x5C (r1)
    #evlwwsplat r10, 0x58 (r1)
    #evlwwsplat r11, 0x54 (r1)
    #evlwwsplat r12, 0x50 (r1)
    e_lwz r5,  0x30 (r1)	        # restore rest of gprs
    e_lwz r6,  0x34 (r1)	        #    |
    e_lwz r7,  0x38 (r1)	        #    |
    e_lwz r8,  0x3C (r1)	        #    |
    e_lwz r9,  0x40 (r1)	        #    |
    e_lwz r10, 0x44 (r1)	        #    |
    e_lwz r11, 0x48 (r1)	        #    |
    e_lwz r12, 0x4C (r1)	        #____v____

    se_blr

################################### 
# HandlerRestoreContext - phase 2 #
###################################   
HandlerRestoreContext_ph2:
    e_lwz  r0, 0x20 (r1)          # Restore CR
    mtcrf  0xff, r0
    e_lwz  r3,  0x28 (r1)         # restore R3 gprs register
    #evlwwsplat r4, 0x70 (r1)
    e_lwz  r4,  0x2C (r1)         # restore R4 gprs register
    e_lwz  r0,  0x0C (r1)         # Restore SRR0
    mtSRR0 r0
    e_lwz  r0,  0x10 (r1)         # Restore SRR1
    mtSRR1 r0
    se_blr

##################################    

########################################## 
# Not Recoverable Interrupt handler body # 
##########################################    
NCI_Handler:                      # Non Recoverable Interrupt handler body 
    e_lis  r3,IvorIndex_c2@ha
    e_add16i r3,r3,IvorIndex_c2@l
    e_stb  r4,0(r3)

    mfSRR1 r4                     # get SRR1 
    e_lis  r3,SRR1_Value_c2@h 
    ori  r3,r3,SRR1_Value_c2@l
    e_stw  r4,0(r3)    

    mfSRR0 r4                     # get SRR0 
    e_lis  r3,SRR0_Value_c2@h
    ori  r3,r3,SRR0_Value_c2@l
    e_stw  r4,0(r3)

    mfspr r4,62	                  # get ESR; SPR_ESR=62
    e_lis   r3,SPR_ESRValue_c2@h
    ori   r3,r3,SPR_ESRValue_c2@l
    e_stw   r4,0(r3)

    mfspr r4,61	                  # get ESR; SPR_DEAR=61
    e_lis   r3,SPR_DEARValue_c2@h
    ori   r3,r3,SPR_DEARValue_c2@l
    e_stw   r4,0(r3)
    
    e_lis  r3,IVOR_Common_Manager_c2@ha
    e_add16i r3,r3,IVOR_Common_Manager_c2@l
    mtlr r3
    se_blrl
#######################################################

