/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  BuckDiagMgm
**  Filename        :  BuckDiagMgm_out.h
**  Created on      :  31-mar-2021 12:01:00
**  Original author :  SantoroR
******************************************************************************/
/*****************************************************************************
**
**                        BuckDiagMgm Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef BUCKDIAGMGM_OUT_H
#define BUCKDIAGMGM_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
#define BKTHRIBATTDIAG_dim          (6u)
#define BKTHRISUPPLYCOILDIAG_dim    (6u)

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : BuckDiagMgm_Init
**
**   Description:
**    Buck Diag management init
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void BuckDiagMgm_Init(void);

/******************************************************************************
**   Function    : BuckDiagMm_Batt
**
**   Description:
**    Falling edge Buck diag check
**
**   Parameters :
**    cyl:      cylinder index
**    riseEdgeTime: absolute rise time
**
**   Returns:
**    void
**
******************************************************************************/
extern void BuckDiagMgm_RunCheck(uint8_T buckIdx);

/******************************************************************************
**   Function    : BuckDiagMm_Batt
**
**   Description:
**    Rising edge Trigger In event
**
**   Parameters :
**    cyl:      cylinder index
**    riseEdgeTime: absolute rise time
**
**   Returns:
**    void
**
******************************************************************************/
extern void BuckDiagMgm_Batt(void);

/******************************************************************************
**   Function    : BuckDiagMgm_PowerOn
**
**   Description:
**    Buck diagnosis at power on
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void BuckDiagMgm_5ms(void);

/******************************************************************************
**   Function    : BuckDiagMgm_IgnBuckTest
**
**   Description:
**    Diagnosis at power on
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
uint8_T BuckDiagMgm_IgnBuckTest(void);

#endif // BUCKDIAGMGM_OUT_H

/****************************************************************************
 ****************************************************************************/


