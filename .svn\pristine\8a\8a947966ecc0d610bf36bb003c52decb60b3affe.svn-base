/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef _UDS_SERVICES_H_
#define _UDS_SERVICES_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
 
#include "../DIAGCANMGM/include/Diagcanmgm_Ferrari.h"
#if 0
/*!
\defgroup PublicDefines Public Defines
\brief Defines with global scope

\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* UDS Diagnostic Sessions: */
/// Default Session ID - Mandatory
#define UDS_DEFAULT_SESSION                     0x01u
/// Programming Session ID - Mandatory
#define UDS_PROGRAMMING_SESSION                 0x02u
/// Extended Session ID - Mandatory
#define UDS_EXTENDED_SESSION                    0x03u
#if ((ENGINE_TYPE != ML_P15_8C) && (ENGINE_TYPE != AM_AMG_6C)) // only FCA-FE based targets
/// Vehicle Manufacture - EOL Session ID
#define UDS_MANUFACTURE_EOL_SESSION             0x40u
#endif
/// Supplier Session ID - User Optional
#define UDS_SUPPLIER_SESSION                    0x60u

/* UDS Message List */
/// Start Default Session message received
#define UDS_START_DEFAULT_SESSION               1u
/// Start Programming Session message received
#define UDS_START_PROGRAMMING_SESSION           2u
/// Start Extended Session message received
#define UDS_START_EXTENDED_SESSION              3u
/// Start Manufacture and EOL Session message received
#define UDS_START_MANUFACTURE_EOL_SESSION       4u
/// Start Supplier Session message received
#define UDS_START_SUPPLIER_SESSION              5u
/// ECU reset message received
#define UDS_ECU_RESET                           6u
/// Clear Diagnostic Information message received
#define UDS_CLEAR_DIAGNOSTIC_INFO               7u
/// Read Diagnostic Trouble Codes message received
#define UDS_READ_DTC_INFO                       8u
/// Read Data By Identifier message received
#define UDS_READ_DATA_BY_ID                     9u
/// Read Memory By Address message received
#define UDS_READ_MEM_BY_ADDR                   10u
/// Security Access message received
#define UDS_SEC_ACCESS                          11u
/// Communication Control message received
#define UDS_COMM_CONTROL                        12u
/// Write Data By Identifier message received
#define UDS_WRITE_DATA_BY_ID                    13u
/// Input/Output control message received
#define UDS_IOCONTROL                           14u
/// Routine Control message received
#define UDS_ROUTINE_CONTROL                     15u
/// Request Download message received
#define UDS_REQUEST_DWNL                        16u
/// Transfer Data message received
#define UDS_TRANSFER_DATA                       17u
/// Transfer Data Exit message received
#define UDS_REQUEST_TRANSFER_EXIT               18u
/// Write memory By Address received
#define UDS_WRITE_MEM_BY_ADDR                   19u
/// Tester Present message received
#define UDS_TESTER_PRESENT                      20u
/// Control DTCs Settings message received
#define UDS_CTRL_DTC_SETTING                    21u

/* UDS Diagnostic Service Identifiers (SIDs) : */
/// DiagnosticSessionControl SID
#define UDS_SERVICE_START_SESSION                  0x10u
/// ECU Reset SID
#define UDS_SERVICE_ECU_RESET                      0x11u
/// ClearDiagnosticInformation SID
#define UDS_SERVICE_CLEAR_DIAGNOSTIC_INFORMATION   0x14u
/// ReadDTCInformation SID
#define UDS_SERVICE_READ_DTC_INFO                  0x19u
/// ReadDataByIdentifier SID
#define UDS_SERVICE_READ_DATA_BY_ID                0x22u
/// ReadMmoeryByAddress SID
#define UDS_SERVICE_READ_MEM_BY_ADDR               0x23u
/// SecurityAccess SID
#define UDS_SERVICE_SECURITY_ACCESS                0x27u
/// CommunicationControl SID
#define UDS_SERVICE_COMM_CONTROL                   0x28u
/// WriteDatByIdentifier SID
#define UDS_SERVICE_WRITE_DATA_BY_ID               0x2Eu
/// InputOutputControl SID
#define UDS_SERVICE_IOCONTROL                      0x2Fu
/// RoutineControl SID
#define UDS_SERVICE_ROUTINE_CONTROL                0x31u
/// RequestDownload SID
#define UDS_SERVICE_REQUEST_DOWNLOAD               0x34u
/// TransferData SID
#define UDS_SERVICE_TRANSFER_DATA                  0x36u
/// TransferDataExit SID
#define UDS_SERVICE_REQUEST_TRANSFER_EXIT          0x37u
/// WriteMemoryByAddress SID
#define UDS_SERVICE_WRITE_MEM_BY_ADDR              0x3Du
/// TesterPresent SID
#define UDS_SERVICE_TESTER_PRESENT                 0x3Eu
/// ControlDTCSetting SID
#define UDS_SERVICE_CTRL_DTC_SETTING               0x85u

/// Suspend Positive Response Message Identification Bit Mask
#define SPRMIB_MASK 0x80u

#if (ENGINE_TYPE == ML_P15_8C)
#pragma ghs startnomisra
/*  Constant defines only for test purposes */

#define SHR(x,n) ((x & 0xFFFFFFFF) >> n)
#define ROTR(x,n) (SHR(x,n) | (x << (32 - n)))

#if defined _DEV_CONST_
/*  Constant defines only for test purposes */
#define CONST1_L1 0x4A71D8DFu
#define CONST1_L2 0x439958F3u
#define CONST2_L1 0x7896DD92u
#define CONST2_L2 0x4E670FB2u
#elif defined _PROD_CONST_
/*  Constant defines only for production targets */
#define CONST1_L1 0x3457A96Cu
#define CONST1_L2 0x715962FBu
#define CONST2_L1 0x6C476BE4u
#define CONST2_L2 0x1A6A3879u
#else
#error Constants not defined!!!
#endif


#define C2_1 0x9E66037Au
#define C2_2 0xAADD38F4u
#define C3_1 0x354213D1u
#define C3_2 0xA4B23FDAu

/* static method */
static unsigned long ENCRYPT(unsigned long seed, unsigned long c1, unsigned long c2, unsigned char s1, unsigned char s2);
#pragma ghs endnomisra
#endif

#if (ENGINE_TYPE == FE_173_8C)
static uint32_T calculateIontelActKey(uint32_T odometerValue);
#endif



/*!\egroup*/

/*!
\defgroup PublicTypedef Public Typedefs
\brief Types definitions with global scopes
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/

/*!\egroup*/

/*!
\defgroup PublicInline Public Inline Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC INLINE FUNCTIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   Inline function name
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this inline function 
*/
/**************************************************************************/


/*!\egroup*/


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern volatile uint8_T System_Reset;
extern uint8_T PendingEventID;
extern uint16_T PendingRLI;
extern boolean_T PendingOperationOngoing;
extern uint8_T checkEnvCondOnRunning;
extern uint8_T checkKillSigOnRunning;
extern uint8_T FlgDiagReset;
extern uint8_T FlgEOL;


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
extern void UDS_DiagnosticSessionControl(void);
extern void UDS_EcuReset(void);
extern void UDS_ClearDiagnosticInformation(void);
extern void UDS_ReadDataByIdentifier(void);
extern void UDS_ReadDtcInformation(void);
extern void UDS_SecurityAccess(void);
extern void UDS_WriteDataByIdentifier(void);
extern void UDS_InputOutputControlByIdentifier(void);
extern void UDS_RoutineControl(void);
extern void UDS_RequestDownload(void);
extern void UDS_TransferData(void);
extern void UDS_RequestTransferExit(void);
extern void UDS_TesterPresent(void);
extern void UDS_ReadMemByAddress(void);
extern void UDS_CommunicationControl(void);
extern void UDS_ControlDTCSetting(void);
extern void UDS_100ms(void);
extern void UDS_PendingClearDiagInfo(void);
extern void UDS_PendingWriteData(void);
extern void UDS_PendingWriteData_Bkgd(void);
extern void UDS_PendingResetFactoryCalib(void);
extern void UDS_PendingStartRoutine(void);
extern void UDS_ClearDtcErasedFlg(void);
#endif

#endif // _UDS_SERVICES_H_
