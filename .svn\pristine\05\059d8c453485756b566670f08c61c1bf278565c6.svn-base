/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#include "rtwtypes.h"
 
#pragma ghs section data=".ee_id3_data"

#ifdef _TEST_EEPROM_
/*  MC Test Struct  */
 uint32_T Prova_ID3[8] = {  
                                 0x33333333u, 0x33333333u, 0x33333333u, 0x33333333u,
                                 0x33333333u, 0x33333333u, 0x33333333u, 0x33333333u 
                         };
#endif

// Declare here all the variables to be stored in EEPROM with ID3
///Dummy_EE ID3
uint32_T EEDummyID3_00 = 0u;
uint32_T EEDummyID3_01 = 0u;

/* Counter of physical errors in EEPROM detected during power on */
uint32_T EEPhysErrCnt = 0u;
/* Counter of logic errors in EEPROM detected during power on */
uint32_T EELogicErrCnt = 0u;     

#ifdef _BUILD_DIAGMGM_
#pragma ghs startnomisra
#include "diagmgm_eep.c"
#pragma ghs endnomisra
#endif

#pragma ghs startnomisra
#include "Bios_eep.c"
#pragma ghs endnomisra

#ifdef _BUILD_MSPARKCMD_
#pragma ghs startnomisra
#include "msparkcmd_eep.c"
#pragma ghs endnomisra
#endif

/* EOF EEPROM */

