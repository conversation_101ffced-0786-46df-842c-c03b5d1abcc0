/******************************************************************************
** COPYRIGHT
** Copyright (c) 2019 by Eldor Corporation S.P.A. , All rights reserved.
**
******************************************************************************/
/******************************************************************************
** SWC : SafetyMngr
** Filename : FlashCheckSM_MCU_test.c
** Created on : 02-feb-2022 12:00:00
** Original author : ZandaE
******************************************************************************/
#ifdef _BUILD_FLASHCHECK_SM_TEST_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "FlashCheckSM_MCU_test.h"

/* No other .h files shall be added here */
/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* None */
/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

static uint8_T   Debug_RecoverySM_source[16]    __attribute__ ((aligned(32)));
static uint8_T   Debug_RecoverySM_dest[16]      __attribute__ ((aligned(32)));
static uint8_T   Debug_RecoverySM_dest_pre[16]  __attribute__ ((aligned(32)));
static uint8_T Flag_SetSMsource = 0U;

static uint8_T DEBUG_RecoverySM_FlashSBE = 0U;


/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/

/******************************************************************************
** Function:   SafetyMngr_Test_FlashSBE
**
** Description:
** generates a single bit ECC error in the chosen memory region
** that will be flagged in the ECC module.
** The target address should not be already written, otherwise a MBE will be
** generated, instead of SBE.
**
** Parameters :
** [in] uint32_T targetAddr: target address to unwritten memory (0xFFFFFFFFU)
**
** Returns:
** void
**
** SW Requirements:
** NA
**
** Implementation Notes:
**
** EA GUID:
******************************************************************************/
void SafetyMngr_Test_FlashSBE(uint32_T targetAddr)
{

int16_T debug_resProgram = NO_ERROR;
/* Set, at the first time, the values used to corrupt the memory */
/* Execute at runtime in order to avoid problem of explicit linking for this test file */
    if (Flag_SetSMsource == 0U)
    {
        /* Set first two bytes to write the memory region */
        Debug_RecoverySM_source[0] = (uint8_T)0xFF;
        Debug_RecoverySM_source[1] = (uint8_T)0xFF;
        Debug_RecoverySM_source[2] = (uint8_T)0xFF;
        Debug_RecoverySM_source[3] = (uint8_T)0xFF;
        Debug_RecoverySM_source[4] = (uint8_T)0x00;
        Debug_RecoverySM_source[5] = (uint8_T)0x00;
        Debug_RecoverySM_source[6] = (uint8_T)0x00;
        Debug_RecoverySM_source[7] = (uint8_T)0x00;

        Flag_SetSMsource = 1U ;
    }

    if (DEBUG_RecoverySM_FlashSBE != 0U)
    {
        DisableAllInterrupts();
        /* Write Boot Blk0 */
        memcpy((void*)Debug_RecoverySM_dest_pre,(const void *)(targetAddr), (uint8_T)16U);
        debug_resProgram = FLASH_Program ( (uint32_T)(targetAddr) , (uint32_T)8U, (uint32_T)&Debug_RecoverySM_source);
        /* Overwrite Boot Blk0 with a single bit changed*/
        Debug_RecoverySM_source[7] = (uint8_T)0x01;
        debug_resProgram = FLASH_Program ( (uint32_T)(targetAddr) , (uint32_T)8U, (uint32_T)&Debug_RecoverySM_source);
        EnableAllInterrupts();
        /* reads the overwritten region to trigger SBE */
        memcpy((void*)Debug_RecoverySM_dest,(const void *)(targetAddr), (uint8_T)16U);
        Debug_RecoverySM_source[7] = (uint8_T)0x00;
        DEBUG_RecoverySM_FlashSBE = 0U ;
    }
}


/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/* None */


#endif /*_BUILD_FLASHCHECK_SM_TEST_*/
/****************************************************************************
****************************************************************************/

