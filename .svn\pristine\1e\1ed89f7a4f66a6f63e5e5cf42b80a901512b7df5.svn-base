/** ###################################################################
**     Filename  : INTC.cfg
**     Project   :
**     Processor : MPC5554
**     Version   : 
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 28/02/2005
**     Abstract  :
**
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright
** ###################################################################*/
#ifndef _TASK_CFG_H_
#define _TASK_CFG_H_

#pragma ghs startnomisra


#define TASK_SETIRQ_TEST
#define TASK_SWSETCLRINT_TEST

/* ------------------------------------------------------------------ */
/*     Common errors defines                                            */
/* ------------------------------------------------------------------ */

#define TASK_NOT_ENABLED_DUE_FILLED_LIST  -2
#define SOFTWARE_INTERRUPT_REQUEST_ERROR -3

/* ------------------------------------------------------------------ */
/* Core selection macros for PSR register.                   */
/* ------------------------------------------------------------------ */
#define INTC_PSR_NOSENT  0x0u //No interrupt request sent
#define INTC_PSR_CORE0   0x8u //Interrupt request sent to processors 0
#define INTC_PSR_CORE2   0x2u //Interrupt request sent to processors 2
#define INTC_PSR_CORE02  0xAu //Interrupt request sent to processors 0, 2

/* ------------------------------------------------------------------ */
/* Interrupts Priority symbols                                        */
/* ------------------------------------------------------------------ */
#define PRI_0    0x0u /* --> lower priority */
#define PRI_1    0x1u
#define PRI_2    0x2u
#define PRI_3    0x3u
#define PRI_4    0x4u
#define PRI_5    0x5u
#define PRI_6    0x6u
#define PRI_7    0x7u
#define PRI_8    0x8u
#define PRI_9    0x9u
#define PRI_10   0xAu
#define PRI_11   0xBu
#define PRI_12   0xCu
#define PRI_13   0xDu
#define PRI_14   0xEu
#define PRI_15   0xFu
#define PRI_16   0x10u
#define PRI_17   0x11u
#define PRI_18   0x12u
#define PRI_19   0x13u
#define PRI_20   0x14u
#define PRI_21   0x15u
#define PRI_22   0x16u
#define PRI_23   0x17u
#define PRI_24   0x18u 
#define PRI_25   0x19u
#define PRI_26   0x1Au
#define PRI_27   0x1Bu
#define PRI_28   0x1Cu
#define PRI_29   0x1Du
#define PRI_30   0x1Eu
#define PRI_31   0x1Fu  /* --> medium priority */
#define PRI_32   0x20u
#define PRI_33   0x21u
#define PRI_34   0x22u
#define PRI_35   0x23u
#define PRI_36   0x24u
#define PRI_37   0x25u
#define PRI_38   0x26u
#define PRI_39   0x27u
#define PRI_40   0x28u
#define PRI_41   0x29u
#define PRI_42   0x2Au
#define PRI_43   0x2Bu
#define PRI_44   0x2Cu
#define PRI_45   0x2Du
#define PRI_46   0x2Eu
#define PRI_47   0x2Fu
#define PRI_48   0x30u
#define PRI_49   0x31u
#define PRI_50   0x32u
#define PRI_51   0x33u
#define PRI_52   0x34u
#define PRI_53   0x35u
#define PRI_54   0x36u
#define PRI_55   0x37u
#define PRI_56   0x38u
#define PRI_57   0x39u
#define PRI_58   0x3Au
#define PRI_59   0x3Bu
#define PRI_60   0x3Cu
#define PRI_61   0x3Du
#define PRI_62   0x3Eu
#define PRI_63   0x3Fu /* --> high priority       */

/* ------------------------------------------------------------------ */
/* Interrupts request symbols - c0 (z4)                               */
/* ------------------------------------------------------------------ */
/************************************************************************************************/
/*************************************** OSEK ISR ***********************************************/
/************************************************************************************************/
#ifndef _OSEK_

#ifdef _BUILD_TASK_
#define INTC_SSCIR0_ISR_C0                RoutineISR0          /* Interrupt no. 0     - ivINT_SSCIR0_CLR0    */
#define INTC_SSCIR1_ISR_C0                DUMMY_FUNC           /* Interrupt no. 1     - ivINT_SSCIR1_CLR1    */
#define INTC_SSCIR2_ISR_C0                DUMMY_FUNC           /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_ISR_C0                DUMMY_FUNC           /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_ISR_C0                DUMMY_FUNC           /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_ISR_C0                DUMMY_FUNC           /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_ISR_C0                DUMMY_FUNC           /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_ISR_C0                DUMMY_FUNC           /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_ISR_C0                DUMMY_FUNC           /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_ISR_C0                DUMMY_FUNC           /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_ISR_C0               DUMMY_FUNC           /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_ISR_C0               DUMMY_FUNC           /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_ISR_C0               DUMMY_FUNC           /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_ISR_C0               DUMMY_FUNC           /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_ISR_C0               DUMMY_FUNC           /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_ISR_C0               DUMMY_FUNC           /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_ISR_C0               DUMMY_FUNC           /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_ISR_C0               DUMMY_FUNC           /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_ISR_C0               DUMMY_FUNC           /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_ISR_C0               DUMMY_FUNC           /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_ISR_C0               DUMMY_FUNC           /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_ISR_C0               DUMMY_FUNC           /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_ISR_C0               DUMMY_FUNC           /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_ISR_C0               DUMMY_FUNC           /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_ISR_C0               DUMMY_FUNC           /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_ISR_C0               DUMMY_FUNC           /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_ISR_C0               DUMMY_FUNC           /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_ISR_C0               DUMMY_FUNC           /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_ISR_C0               DUMMY_FUNC           /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_ISR_C0               DUMMY_FUNC           /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_ISR_C0               RoutineISR30         /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_ISR_C0               DUMMY_FUNC           /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#else
#define INTC_SSCIR0_ISR_C0                DUMMY_FUNC           /* Interrupt no. 0     - ivINT_SSCIR0_CLR0    */
#define INTC_SSCIR1_ISR_C0                DUMMY_FUNC           /* Interrupt no. 1     - ivINT_SSCIR1_CLR1    */
#define INTC_SSCIR2_ISR_C0                DUMMY_FUNC           /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_ISR_C0                DUMMY_FUNC           /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_ISR_C0                DUMMY_FUNC           /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_ISR_C0                DUMMY_FUNC           /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_ISR_C0                DUMMY_FUNC           /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_ISR_C0                DUMMY_FUNC           /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_ISR_C0                DUMMY_FUNC           /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_ISR_C0                DUMMY_FUNC           /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_ISR_C0               DUMMY_FUNC           /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_ISR_C0               DUMMY_FUNC           /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_ISR_C0               DUMMY_FUNC           /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_ISR_C0               DUMMY_FUNC           /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_ISR_C0               DUMMY_FUNC           /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_ISR_C0               DUMMY_FUNC           /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_ISR_C0               DUMMY_FUNC           /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_ISR_C0               DUMMY_FUNC           /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_ISR_C0               DUMMY_FUNC           /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_ISR_C0               DUMMY_FUNC           /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_ISR_C0               DUMMY_FUNC           /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_ISR_C0               DUMMY_FUNC           /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_ISR_C0               DUMMY_FUNC           /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_ISR_C0               DUMMY_FUNC           /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_ISR_C0               DUMMY_FUNC           /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_ISR_C0               DUMMY_FUNC           /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_ISR_C0               DUMMY_FUNC           /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_ISR_C0               DUMMY_FUNC           /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_ISR_C0               DUMMY_FUNC           /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_ISR_C0               DUMMY_FUNC           /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_ISR_C0               DUMMY_FUNC           /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_ISR_C0               DUMMY_FUNC           /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#endif /* _BUILD_TASK_ */

#else /* _OSEK_ */
#define INTC_SSCIR0_ISR_C0                Schedule             /* Interrupt no. 0     - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_ISR_C0                Schedule             /* Interrupt no. 1     - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_ISR_C0                Schedule             /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_ISR_C0                Schedule             /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_ISR_C0                Schedule             /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_ISR_C0                Schedule             /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_ISR_C0                Schedule             /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_ISR_C0                Schedule             /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_ISR_C0                DUMMY_FUNC             /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_ISR_C0                DUMMY_FUNC             /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_ISR_C0               DUMMY_FUNC             /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_ISR_C0               DUMMY_FUNC             /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_ISR_C0               DUMMY_FUNC             /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_ISR_C0               DUMMY_FUNC             /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_ISR_C0               DUMMY_FUNC             /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_ISR_C0               DUMMY_FUNC             /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_ISR_C0               DUMMY_FUNC             /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_ISR_C0               DUMMY_FUNC             /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_ISR_C0               DUMMY_FUNC             /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_ISR_C0               DUMMY_FUNC             /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_ISR_C0               DUMMY_FUNC             /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_ISR_C0               DUMMY_FUNC             /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_ISR_C0               DUMMY_FUNC             /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_ISR_C0               DUMMY_FUNC             /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_ISR_C0               DUMMY_FUNC             /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_ISR_C0               DUMMY_FUNC             /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_ISR_C0               DUMMY_FUNC             /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_ISR_C0               DUMMY_FUNC             /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_ISR_C0               DUMMY_FUNC             /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_ISR_C0               DUMMY_FUNC             /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_ISR_C0               DUMMY_FUNC             /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_ISR_C0               DUMMY_FUNC             /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#endif /* _OSEK_ */
/************************************************************************************************/
/*************************************** SWT ISR ************************************************/
/************************************************************************************************/
#define SWT0_ISR_C0                       DUMMY_FUNC           /* Interrupt no. 32    -    */
#define SWT2_ISR_C0                       DUMMY_FUNC           /* Interrupt no. 34    -   */
#define SWT3_ISR_C0                       DUMMY_FUNC           /* Interrupt no. 35    -   */
/************************************************************************************************/
/*************************************** STM ISR ************************************************/
/************************************************************************************************/
#ifdef _BUILD_STM_
#define STM_INT_0_CIR0_ISR_C0             STM_CH0_Isr36           /* Interrupt no. 36    -   */
#define STM_INT_0_CIR1_ISR_C0             STM_CH1_Isr37           /* Interrupt no. 37    -   */
#define STM_INT_0_CIR2_ISR_C0             STM_CH2_Isr38           /* Interrupt no. 38    -   */
#define STM_INT_0_CIR3_ISR_C0             STM_CH3_Isr39           /* Interrupt no. 39    -   */
#else
#define STM_INT_0_CIR0_ISR_C0             DUMMY_FUNC           /* Interrupt no. 36    -   */
#define STM_INT_0_CIR1_ISR_C0             DUMMY_FUNC           /* Interrupt no. 37    -   */
#define STM_INT_0_CIR2_ISR_C0             DUMMY_FUNC           /* Interrupt no. 38    -   */
#define STM_INT_0_CIR3_ISR_C0             DUMMY_FUNC           /* Interrupt no. 39    -   */
#endif
#define STM_INT_2_CIR0_ISR_C0             DUMMY_FUNC           /* Interrupt no. 44    -   */
#define STM_INT_2_CIR1_ISR_C0             DUMMY_FUNC           /* Interrupt no. 45    -   */
#define STM_INT_2_CIR2_ISR_C0             DUMMY_FUNC           /* Interrupt no. 46    -   */
#define STM_INT_2_CIR3_ISR_C0             DUMMY_FUNC           /* Interrupt no. 47    -   */
/************************************************************************************************/
/*************************************** EDMA ISR ***********************************************/
/************************************************************************************************/
#define EDMA_ERL_ERR31_ERR0_ISR_C0        DUMMY_FUNC           /* Interrupt no. 52   - ivINT_EDMA_ERRL_ERR31_0  */
#define EDMA_IRQRL_INT00_ISR_C0           DUMMY_FUNC           /* Interrupt no. 53   - ivINT_EDMA_IRQRL_INT0  */
#define EDMA_IRQRL_INT01_ISR_C0           DUMMY_FUNC           /* Interrupt no. 54   - ivINT_EDMA_IRQRL_INT1  */
#define EDMA_IRQRL_INT02_ISR_C0           DMA_CH2_ISR           /* Interrupt no. 55   - ivINT_EDMA_IRQRL_INT2  */
#define EDMA_IRQRL_INT03_ISR_C0           DMA_CH3_ISR           /* Interrupt no. 56   - ivINT_EDMA_IRQRL_INT3  */
#define EDMA_IRQRL_INT04_ISR_C0           DUMMY_FUNC           /* Interrupt no. 57   - ivINT_EDMA_IRQRL_INT4  */
#define EDMA_IRQRL_INT05_ISR_C0           DUMMY_FUNC           /* Interrupt no. 58   - ivINT_EDMA_IRQRL_INT5  */
#define EDMA_IRQRL_INT06_ISR_C0           DUMMY_FUNC           /* Interrupt no. 59   - ivINT_EDMA_IRQRL_INT6  */
#define EDMA_IRQRL_INT07_ISR_C0           DUMMY_FUNC           /* Interrupt no. 60   - ivINT_EDMA_IRQRL_INT7  */
#define EDMA_IRQRL_INT08_ISR_C0           DUMMY_FUNC           /* Interrupt no. 61   - ivINT_EDMA_IRQRL_INT8  */
#define EDMA_IRQRL_INT09_ISR_C0           DUMMY_FUNC           /* Interrupt no. 62   - ivINT_EDMA_IRQRL_INT9  */
#define EDMA_IRQRL_INT10_ISR_C0           DUMMY_FUNC           /* Interrupt no. 63   - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_ISR_C0           DUMMY_FUNC           /* Interrupt no. 64   - ivINT_EDMA_IRQRL_INT11  */
#define EDMA_IRQRL_INT12_ISR_C0           DUMMY_FUNC           /* Interrupt no. 65   - ivINT_EDMA_IRQRL_INT12  */
#define EDMA_IRQRL_INT13_ISR_C0           DUMMY_FUNC           /* Interrupt no. 66   - ivINT_EDMA_IRQRL_INT13  */
#define EDMA_IRQRL_INT14_ISR_C0           DUMMY_FUNC           /* Interrupt no. 67   - ivINT_EDMA_IRQRL_INT14  */
#define EDMA_IRQRL_INT15_ISR_C0           DUMMY_FUNC           /* Interrupt no. 68   - ivINT_EDMA_IRQRL_INT15  */
#define EDMA_IRQRL_INT16_ISR_C0           DUMMY_FUNC           /* Interrupt no. 69   - ivINT_EDMA_IRQRL_INT16  */
#define EDMA_IRQRL_INT17_ISR_C0           DUMMY_FUNC           /* Interrupt no. 70   - ivINT_EDMA_IRQRL_INT17  */
#define EDMA_IRQRL_INT18_ISR_C0           DUMMY_FUNC           /* Interrupt no. 71   - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_ISR_C0           DUMMY_FUNC           /* Interrupt no. 72   - ivINT_EDMA_IRQRL_INT19  */
#define EDMA_IRQRL_INT20_ISR_C0           DUMMY_FUNC           /* Interrupt no. 73   - ivINT_EDMA_IRQRL_INT20  */
#define EDMA_IRQRL_INT21_ISR_C0           DUMMY_FUNC           /* Interrupt no. 74   - ivINT_EDMA_IRQRL_INT21  */
#define EDMA_IRQRL_INT22_ISR_C0           DUMMY_FUNC           /* Interrupt no. 75   - ivINT_EDMA_IRQRL_INT22  */
#define EDMA_IRQRL_INT23_ISR_C0           DUMMY_FUNC           /* Interrupt no. 76   - ivINT_EDMA_IRQRL_INT23  */
#define EDMA_IRQRL_INT24_ISR_C0           DMA_CH24_ISR           /* Interrupt no. 77   - ivINT_EDMA_IRQRL_INT24  */
#define EDMA_IRQRL_INT25_ISR_C0           DMA_CH25_ISR           /* Interrupt no. 78   - ivINT_EDMA_IRQRL_INT25  */
#define EDMA_IRQRL_INT26_ISR_C0           DUMMY_FUNC           /* Interrupt no. 79   - ivINT_EDMA_IRQRL_INT26  */
#define EDMA_IRQRL_INT27_ISR_C0           DUMMY_FUNC           /* Interrupt no. 80   - ivINT_EDMA_IRQRL_INT27  */
#define EDMA_IRQRL_INT28_ISR_C0           DUMMY_FUNC           /* Interrupt no. 81   - ivINT_EDMA_IRQRL_INT28  */
#define EDMA_IRQRL_INT29_ISR_C0           DUMMY_FUNC           /* Interrupt no. 82   - ivINT_EDMA_IRQRL_INT29  */
#define EDMA_IRQRL_INT30_ISR_C0           DUMMY_FUNC           /* Interrupt no. 83   - ivINT_EDMA_IRQRL_INT30  */
#define EDMA_IRQRL_INT31_ISR_C0           DUMMY_FUNC           /* Interrupt no. 84   - ivINT_EDMA_IRQRL_INT31  */
/************************************************************************************************/
/*************************************** FLASH ISR **********************************************/
/************************************************************************************************/
#define FLASH_SRAM_ECC_INT_ISR_C0         DUMMY_FUNC           /* Interrupt no. 185  - ivINT_FLASH_ECC        */
/************************************************************************************************/
/*************************************** ETHERNET ISR *******************************************/
/************************************************************************************************/
#define ETH_EIR_TX_ISR_C0                 DUMMY_FUNC           /* Interrupt no. 218   -   */
#define ETH_EIR_RX_ISR_C0                 DUMMY_FUNC           /* Interrupt no. 219   -   */
#define ETH_EIR_COMB_ISR_C0               DUMMY_FUNC           /* Interrupt no. 220   -   */
/************************************************************************************************/
/*************************************** PIT-RTI ISR ********************************************/
/************************************************************************************************/
#ifdef _BUILD_PIT_
#define PIT_INT0_PIT0_ISR_C0              PIT_1msTimebase_ISR  /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_ISR_C0              PIT0_CH1_ISR         /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 231  -        */
#define PIT_RTI_PIT0_ISR_C0               DUMMY_FUNC           /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_ISR_C0              PIT1_CH0_ISR         /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_ISR_C0              DUMMY_FUNC           /* Interrupt no. 241  -        */
#else
#define PIT_INT0_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 231  -        */
#define PIT_RTI_PIT0_ISR_C0               DUMMY_FUNC           /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_ISR_C0              DUMMY_FUNC           /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_ISR_C0              DUMMY_FUNC           /* Interrupt no. 241  -        */
#endif
/************************************************************************************************/
/*************************************** XOSC ISR ***********************************************/
/************************************************************************************************/
#define XOSC_CTL_ISR_C0                   DUMMY_FUNC          /* Interrupt no. 242   - ivFMPLL_SYNSR_LOCF  */
/************************************************************************************************/
/*************************************** SIUL ISR ***********************************************/
/************************************************************************************************/
#define SIUL2_COMB_EXT0_ISR_C0            DUMMY_FUNC          /* Interrupt no. 243   - ivINT_SIU_OSR_OVF15_0  */
#define SIUL2_COMB_EXT1_ISR_C0            DUMMY_FUNC          /* Interrupt no. 244   - ivINT_SIU_EISR_EIF0  */
/************************************************************************************************/
/*************************************** MC ISR *************************************************/
/************************************************************************************************/
#define MC_ME_SAFE_ISR_C0                 DUMMY_FUNC          /* Interrupt no. 251   -   */
#define MC_ME_MTC_ISR_C0                  DUMMY_FUNC          /* Interrupt no. 252   -   */
#define MC_ME_IMODE_ISR_C0                DUMMY_FUNC          /* Interrupt no. 253   -   */
#define MC_ME_ICONF_ISR_C0                DUMMY_FUNC          /* Interrupt no. 254   -   */
#define MC_RGM_ISR_C0                     DUMMY_FUNC          /* Interrupt no. 255   -   */
/************************************************************************************************/
/*************************************** DSPI ISR ***********************************************/
/************************************************************************************************/
#define DSPI_0_ISR_TFUF_RFOF_ISR_C0       DUMMY_FUNC          /* Interrupt no. 259  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_0_ISR_EOQF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 260  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_0_ISR_TFFF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 261  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_0_ISR_TCF_ISR_C0             DUMMY_FUNC          /* Interrupt no. 262  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_0_ISR_RFDF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 263  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDTCF_SPITCF_ISR_C0   DUMMY_FUNC          /* Interrupt no. 264  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDFFF_ISR_C0          DUMMY_FUNC          /* Interrupt no. 265  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_SPEF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 266  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_TFUF_RFOF_ISR_C0       DUMMY_FUNC          /* Interrupt no. 268  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_1_ISR_EOQF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 269  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_1_ISR_TFFF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 270  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_1_ISR_TCF_ISR_C0             DUMMY_FUNC          /* Interrupt no. 271  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_1_ISR_RFDF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 272  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDTCF_SPITCF_ISR_C0   DUMMY_FUNC          /* Interrupt no. 273  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDFFF_ISR_C0          DUMMY_FUNC          /* Interrupt no. 274  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_SPEF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 275  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_TFUF_RFOF_ISR_C0       DUMMY_FUNC          /* Interrupt no. 277  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_2_ISR_EOQF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 278  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_2_ISR_TFFF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 279  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_2_ISR_TCF_ISR_C0             DUMMY_FUNC          /* Interrupt no. 280  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_2_ISR_RFDF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 281  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDTCF_SPITCF_ISR_C0   DUMMY_FUNC          /* Interrupt no. 282  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDFFF_ISR_C0          DUMMY_FUNC          /* Interrupt no. 283  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_SPEF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 284  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_TFUF_RFOF_ISR_C0       DUMMY_FUNC          /* Interrupt no. 295  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_4_ISR_EOQF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 296  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_4_ISR_TFFF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 297  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_4_ISR_TCF_ISR_C0             DUMMY_FUNC          /* Interrupt no. 298  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_4_ISR_RFDF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 299  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDTCF_SPITCF_ISR_C0   DUMMY_FUNC          /* Interrupt no. 300  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDFFF_DSITCF_ISR_C0   DUMMY_FUNC          /* Interrupt no. 301  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_SPEF_DPEF_ISR_C0       DUMMY_FUNC          /* Interrupt no. 302  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_DDIF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 303  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_TFUF_RFOF_ISR_C0       DUMMY_FUNC          /* Interrupt no. 304  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_5_ISR_EOQF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 305  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_5_ISR_TFFF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 306  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_5_ISR_TCF_ISR_C0             DUMMY_FUNC          /* Interrupt no. 307  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_5_ISR_RFDF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 308  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDTCF_SPITCF_ISR_C0   DUMMY_FUNC          /* Interrupt no. 309  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDFFF_DSITCF_ISR_C0   DUMMY_FUNC          /* Interrupt no. 310  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_SPEF_DPEF_ISR_C0       DUMMY_FUNC          /* Interrupt no. 311  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_DDIF_ISR_C0            DUMMY_FUNC          /* Interrupt no. 312  - ivINT_DSPI_B_ISR_RFDF  */
/************************************************************************************************/
/*************************************** LINFLEX ISR ********************************************/
/************************************************************************************************/
#define LINFLEX_0_RX_COMB_ISR_C0          DUMMY_FUNC          /* Interrupt no. 376    -   */
#define LINFLEX_0_TX_COMB_ISR_C0          DUMMY_FUNC          /* Interrupt no. 377    -   */
#define LINFLEX_0_ERROR_COMB_ISR_C0       DUMMY_FUNC          /* Interrupt no. 378    -   */
#define LINFLEX_1_RX_COMB_ISR_C0          DUMMY_FUNC          /* Interrupt no. 380    -   */
#define LINFLEX_1_TX_COMB_ISR_C0          DUMMY_FUNC          /* Interrupt no. 381    -   */
#define LINFLEX_1_ERROR_COMB_ISR_C0       DUMMY_FUNC          /* Interrupt no. 382    -   */
#define LINFLEX_2_RX_COMB_ISR_C0          DUMMY_FUNC          /* Interrupt no. 384    -   */
#define LINFLEX_2_TX_COMB_ISR_C0          DUMMY_FUNC          /* Interrupt no. 385    -   */
#define LINFLEX_2_ERROR_COMB_ISR_C0       DUMMY_FUNC          /* Interrupt no. 386    -   */
#define LINFLEX_14_RX_COMB_ISR_C0         DUMMY_FUNC          /* Interrupt no. 432    -   */
#define LINFLEX_14_TX_COMB_ISR_C0         DUMMY_FUNC          /* Interrupt no. 433    -   */
#define LINFLEX_14_ERROR_COMB_ISR_C0      DUMMY_FUNC          /* Interrupt no. 434    -   */
#define LINFLEX_15_RX_COMB_ISR_C0         DUMMY_FUNC          /* Interrupt no. 436    -   */
#define LINFLEX_15_TX_COMB_ISR_C0         DUMMY_FUNC          /* Interrupt no. 437    -   */
#define LINFLEX_15_ERROR_COMB_ISR_C0      DUMMY_FUNC          /* Interrupt no. 438    -   */
/************************************************************************************************/
/**************************************** I2C ISR ***********************************************/
/************************************************************************************************/
#define I2C_IBIF_IAAS_IBAL_ISR_C0         DUMMY_FUNC          /* Interrupt no. 440    -   */
/************************************************************************************************/
/*************************************** FLEXRAY ISR ********************************************/
/************************************************************************************************/
#define FLEXRAY_LRNE_ISR_C0               DUMMY_FUNC          /* Interrupt no. 453 - ivINT_FlexRAY LRNE   */
#define FLEXRAY_LRCE_ISR_C0               DUMMY_FUNC          /* Interrupt no. 454 - ivINT_FlexRAY LRCE   */
#define FLEXRAY_FAFAIF_ISR_C0             DUMMY_FUNC          /* Interrupt no. 455 - ivINT_FlexRAY FAFAIF */
#define FLEXRAY_FAFBIF_ISR_C0             DUMMY_FUNC          /* Interrupt no. 456 - ivINT_FlexRAY FAFBIF */
#define FLEXRAY_WUPIF_ISR_C0              DUMMY_FUNC          /* Interrupt no. 457 - ivINT_FlexRAY WUPIF  */
#define FLEXRAY_PRIF_ISR_C0               DUMMY_FUNC          /* Interrupt no. 458 - ivINT_FlexRAY PRIF   */
#define FLEXRAY_CHIF_ISR_C0               DUMMY_FUNC          /* Interrupt no. 459 - ivINT_FlexRAY CHIF   */
#define FLEXRAY_TBIF_ISR_C0               DUMMY_FUNC          /* Interrupt no. 460 - ivINT_FlexRAY TBIF   */
#define FLEXRAY_RBIF_ISR_C0               DUMMY_FUNC          /* Interrupt no. 461 - ivINT_FlexRAY RBIF   */
#define FLEXRAY_MIF_ISR_C0                DUMMY_FUNC          /* Interrupt no. 462 - ivINT_FlexRAY MIF    */
/************************************************************************************************/
/*************************************** GR ISR ** **********************************************/
/************************************************************************************************/
#define GR_VD_ISR_C0                      DUMMY_FUNC          /* Interrupt no. 477   - ivGR_VD  */
/************************************************************************************************/
/*************************************** EPR ISR ************************************************/
/************************************************************************************************/
#define EPR_TEMP_ISR_C0                   DUMMY_FUNC           /* Interrupt no. 478   - ivEPR_TEMP  */
/************************************************************************************************/
/*************************************** FCCU ISR ***********************************************/
/************************************************************************************************/
#define FCCU_ALRM_STAT_ISR_C0             DUMMY_FUNC           /* Interrupt no. 488   - ivFMPLL_SYNSR_LOCF  */
#define FCCU_CFG_TO_STAT_ISR_C0           DUMMY_FUNC           /* Interrupt no. 489   - ivFMPLL_SYNSR_LOLF  */
/************************************************************************************************/
/*************************************** STCU ISR ***********************************************/
/************************************************************************************************/
#define STCU_LBIE_ISR_C0                  DUMMY_FUNC           /* Interrupt no. 494   - ivSTCU_LBIE  */
#define STCU_MBIE_ISR_C0                  DUMMY_FUNC           /* Interrupt no. 495   - ivSTCU_MBIE  */
/************************************************************************************************/
/*************************************** SAR ISR ************************************************/
/************************************************************************************************/
#define SAR_0_INT_ISR_C0                  DUMMY_FUNC           /* Interrupt no. 528    -   */
#define SAR_2_INT_ISR_C0                  DUMMY_FUNC           /* Interrupt no. 530    -   */
#define SAR_4_INT_ISR_C0                  DUMMY_FUNC           /* Interrupt no. 532    -   */
#define SAR_6_INT_ISR_C0                  DUMMY_FUNC           /* Interrupt no. 534    -   */
#define SAR_B_INT_ISR_C0                  DUMMY_FUNC           /* Interrupt no. 543    -   */
/************************************************************************************************/
/*************************************** SD ISR *************************************************/
/************************************************************************************************/
#define SD_0_INT_ISR_C0                   DUMMY_FUNC           /* Interrupt no. 544  - ivSD_0_INT  */
#define SD_3_INT_ISR_C0                   DUMMY_FUNC           /* Interrupt no. 547  - ivSD_3_INT  */
/************************************************************************************************/
/**************************************** SENT ISR **********************************************/
/************************************************************************************************/
#define SENT_0_FAST_COMB_ISR_C0           DUMMY_FUNC           /* Interrupt no. 558    - ivSENT_0_FAST_COMB  */
#define SENT_0_SLOW_COMB_ISR_C0           DUMMY_FUNC           /* Interrupt no. 559    -   */
#define SENT_0_GBL_ERROR_ISR_C0           DUMMY_FUNC           /* Interrupt no. 560    -   */
#define SENT_1_SLOW_RDY_ISR_C0            DUMMY_FUNC           /* Interrupt no. 562    -   */
#define SENT_1_FAST_RDY_ISR_C0            DUMMY_FUNC           /* Interrupt no. 561    -   */
#define SENT_1_GBL_ERROR_ISR_C0           DUMMY_FUNC           /* Interrupt no. 563    -   */
#define SENT_0_FMSG_0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 564    -   */
#define SENT_0_SMSG_0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 565    -   */
#define SENT_0_ERROR_0_ISR_C0             DUMMY_FUNC           /* Interrupt no. 566    -   */
#define SENT_0_FMSG_1_ISR_C0              DUMMY_FUNC           /* Interrupt no. 567    -   */
#define SENT_0_SMSG_1_ISR_C0              DUMMY_FUNC           /* Interrupt no. 568    -   */
#define SENT_0_ERROR_1_ISR_C0             DUMMY_FUNC           /* Interrupt no. 569    -   */
#define SENT_0_FMSG_2_ISR_C0              DUMMY_FUNC           /* Interrupt no. 570    -   */
#define SENT_0_SMSG_2_ISR_C0              DUMMY_FUNC           /* Interrupt no. 571    -   */
#define SENT_0_ERROR_2_ISR_C0             DUMMY_FUNC           /* Interrupt no. 572    -   */
#define SENT_0_FMSG_3_ISR_C0              DUMMY_FUNC           /* Interrupt no. 573    -   */
#define SENT_0_SMSG_3_ISR_C0              DUMMY_FUNC           /* Interrupt no. 574    -   */
#define SENT_0_ERROR_3_ISR_C0             DUMMY_FUNC           /* Interrupt no. 575    -   */
#define SENT_1_FMSG_0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 579    -   */
#define SENT_1_SMSG_0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 580    -   */
#define SENT_1_ERROR_0_ISR_C0             DUMMY_FUNC           /* Interrupt no. 581    -   */
#define SENT_1_FMSG_1_ISR_C0              DUMMY_FUNC           /* Interrupt no. 582    -   */
#define SENT_1_SMSG_1_ISR_C0              DUMMY_FUNC           /* Interrupt no. 583    -   */
#define SENT_1_ERROR_1_ISR_C0             DUMMY_FUNC           /* Interrupt no. 584    -   */
/************************************************************************************************/
/*************************************** PSI ISR ************************************************/
/************************************************************************************************/
#define PSI5_0_DMA_0_ISR_C0               DUMMY_FUNC           /* Interrupt no. 612    -   */
#define PSI5_0_GEN_0_ISR_C0               DUMMY_FUNC           /* Interrupt no. 613    -   */
#define PSI5_0_NEW_MSG_0_ISR_C0           DUMMY_FUNC           /* Interrupt no. 614    -   */
#define PSI5_0_MSG_OW_0_ISR_C0            DUMMY_FUNC           /* Interrupt no. 615    -   */
#define PSI5_0_ERROR_COMB_0_ISR_C0        DUMMY_FUNC           /* Interrupt no. 616    -   */
#define PSI5_0_GLOBAL_0_ISR_C0            DUMMY_FUNC           /* Interrupt no. 617    -   */
#define PSI5_1_DMA_0_ISR_C0               DUMMY_FUNC           /* Interrupt no. 624    -   */
#define PSI5_1_GEN_0_ISR_C0               DUMMY_FUNC           /* Interrupt no. 625    -   */
#define PSI5_1_NEW_MSG_0_ISR_C0           DUMMY_FUNC           /* Interrupt no. 626    -   */
#define PSI5_1_MSG_OW_0_ISR_C0            DUMMY_FUNC           /* Interrupt no. 627    -   */
#define PSI5_1_ERROR_COMB_0_ISR_C0        DUMMY_FUNC           /* Interrupt no. 628    -   */
#define PSI5_1_GLOBAL_0_ISR_C0            DUMMY_FUNC           /* Interrupt no. 629    -   */
/************************************************************************************************/
/*************************************** SIPI ISR ***********************************************/
/************************************************************************************************/
#define SIPI_ERROR_COMB_ISR_C0            DUMMY_FUNC           /* Interrupt no. 654    -   */
#define SIPI_CRC_ERROR_ISR_C0             DUMMY_FUNC           /* Interrupt no. 655    -   */
#define SIPI_CH0_RX_ISR_C0                DUMMY_FUNC           /* Interrupt no. 656    -   */
#define SIPI_CH1_RX_ISR_C0                DUMMY_FUNC           /* Interrupt no. 657    -   */
#define SIPI_CH2_RX_ISR_C0                DUMMY_FUNC           /* Interrupt no. 658    -   */
#define SIPI_CH3_RX_ISR_C0                DUMMY_FUNC           /* Interrupt no. 659    -   */
#define SIPI_EVENT_COMB_ISR_C0            DUMMY_FUNC           /* Interrupt no. 660    -   */
/************************************************************************************************/
/*************************************** LFAST ISR **********************************************/
/************************************************************************************************/
#define LFAST_0_TX_ISR_C0                 DUMMY_FUNC           /* Interrupt no. 661    -   */
#define LFAST_0_TX_ERROR_ISR_C0           DUMMY_FUNC           /* Interrupt no. 662    -   */
#define LFAST_0_RX_ISR_C0                 DUMMY_FUNC           /* Interrupt no. 663    -   */
#define LFAST_0_RX_ERROR_ISR_C0           DUMMY_FUNC           /* Interrupt no. 664    -   */
#define LFAST_0_ICLC_RX_ISR_C0            DUMMY_FUNC           /* Interrupt no. 665    -   */
/************************************************************************************************/
/*************************************** JTAG ISR ***********************************************/
/************************************************************************************************/
#define JTAG_GM_ISR_C0                    DUMMY_FUNC           /* Interrupt no. 674    -   */
#define JTAG_DC_ISR_C0                    DUMMY_FUNC           /* Interrupt no. 675    -   */
/************************************************************************************************/
/*************************************** M_TTCAN ISR ********************************************/
/************************************************************************************************/
#ifdef _BUILD_CAN_
#define M_TTCAN_LINE0_ISR_C0              CAN_CHC_L0           /* Interrupt no. 677    -   */
#define M_TTCAN_LINE1_ISR_C0              CAN_CHC_L1           /* Interrupt no. 678    -   */
#define M_TTCAN_RTMI_ISR_C0               DUMMY_FUNC           /* Interrupt no. 679    -   */
#else
#define M_TTCAN_LINE0_ISR_C0              DUMMY_FUNC           /* Interrupt no. 677    -   */
#define M_TTCAN_LINE1_ISR_C0              DUMMY_FUNC           /* Interrupt no. 678    -   */
#define M_TTCAN_RTMI_ISR_C0               DUMMY_FUNC           /* Interrupt no. 679    -   */
#endif
/************************************************************************************************/
/**************************************** M_CAN ISR *********************************************/
/************************************************************************************************/
#ifdef _BUILD_CAN_
#define MCAN1_LINE0_ISR_C0                CAN_CHA_L0           /* Interrupt no. 688    -   */
#define MCAN1_LINE1_ISR_C0                CAN_CHA_L1           /* Interrupt no. 689    -   */
#define MCAN2_LINE0_ISR_C0                CAN_CHB_L0           /* Interrupt no. 690    -   */
#define MCAN2_LINE1_ISR_C0                CAN_CHB_L1           /* Interrupt no. 691    -   */
#else
#define MCAN1_LINE0_ISR_C0                DUMMY_FUNC           /* Interrupt no. 688    -   */
#define MCAN1_LINE1_ISR_C0                DUMMY_FUNC           /* Interrupt no. 689    -   */
#define MCAN2_LINE0_ISR_C0                DUMMY_FUNC           /* Interrupt no. 690    -   */
#define MCAN2_LINE1_ISR_C0                DUMMY_FUNC           /* Interrupt no. 691    -   */
#endif
/************************************************************************************************/
/*************************************** GTM ISR ************************************************/
/************************************************************************************************/
#define GTM_AEI_ISR_C0                    DUMMY_FUNC           /* Interrupt no. 706    -   */
#define GTM_ARU_NEW_DATA0_ISR_C0          DUMMY_FUNC           /* Interrupt no. 707    -   */
#define GTM_ARU_NEW_DATA1_ISR_C0          DUMMY_FUNC           /* Interrupt no. 708    -   */
#define GTM_ARU_ACC_ACK_ISR_C0            DUMMY_FUNC           /* Interrupt no. 709    -   */
#define GTM_BRC_ISR_C0                    DUMMY_FUNC           /* Interrupt no. 710    -   */
#define GTM_CMP_ISR_C0                    DUMMY_FUNC           /* Interrupt no. 711    -   */
#define GTM_SPE0_ISR_C0                   DUMMY_FUNC           /* Interrupt no. 712    -   */
#define GTM_SPE1_ISR_C0                   DUMMY_FUNC           /* Interrupt no. 713    -   */
#define GTM_PSM0_CH0_ISR_C0               DUMMY_FUNC           /* Interrupt no. 714    -   */
#define GTM_PSM0_CH1_ISR_C0               DUMMY_FUNC           /* Interrupt no. 715    -   */
#define GTM_PSM0_CH2_ISR_C0               DUMMY_FUNC           /* Interrupt no. 716    -   */
#define GTM_PSM0_CH3_ISR_C0               DUMMY_FUNC           /* Interrupt no. 717    -   */
#define GTM_PSM0_CH4_ISR_C0               DUMMY_FUNC           /* Interrupt no. 718    -   */
#define GTM_PSM0_CH5_ISR_C0               DUMMY_FUNC           /* Interrupt no. 719    -   */
#define GTM_PSM0_CH6_ISR_C0               DUMMY_FUNC           /* Interrupt no. 720    -   */
#define GTM_PSM0_CH7_ISR_C0               DUMMY_FUNC           /* Interrupt no. 721    -   */
#define GTM_DPLL_DCGI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 722    -   */
#define GTM_DPLL_EDI_ISR_C0               DUMMY_FUNC           /* Interrupt no. 723    -   */
#define GTM_DPLL_TINI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 724    -   */
#define GTM_DPLL_TAXI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 725    -   */
#define GTM_DPLL_SISI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 726    -   */
#define GTM_DPLL_TISI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 727    -   */
#define GTM_DPLL_MSI_ISR_C0               DUMMY_FUNC           /* Interrupt no. 728    -   */
#define GTM_DPLL_MTI_ISR_C0               DUMMY_FUNC           /* Interrupt no. 729    -   */
#define GTM_DPLL_SASI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 730    -   */
#define GTM_DPLL_TASI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 731    -   */
#define GTM_DPLL_PWI_ISR_C0               DUMMY_FUNC           /* Interrupt no. 732    -   */
#define GTM_DPLL_W2I_ISR_C0               DUMMY_FUNC           /* Interrupt no. 733    -   */
#define GTM_DPLL_W1I_ISR_C0               DUMMY_FUNC           /* Interrupt no. 734    -   */
#define GTM_DPLL_GL1I_ISR_C0              GTM_DPLL_GL1I_IntHandler           /* Interrupt no. 735    -   */
#define GTM_DPLL_LL1I_ISR_C0              GTM_DPLL_LL1I_IntHandler           /* Interrupt no. 736    -   */
#define GTM_DPLL_EI_ISR_C0                DUMMY_FUNC           /* Interrupt no. 737    -   */
#define GTM_DPLL_GL2I_ISR_C0              DUMMY_FUNC           /* Interrupt no. 738    -   */
#define GTM_DPLL_LL2I_ISR_C0              DUMMY_FUNC           /* Interrupt no. 739    -   */
#define GTM_DPLL_TE0I_ISR_C0              DUMMY_FUNC           /* Interrupt no. 740    -   */
#define GTM_DPLL_TE1I_ISR_C0              DUMMY_FUNC           /* Interrupt no. 741    -   */
#define GTM_DPLL_TE2I_ISR_C0              DUMMY_FUNC           /* Interrupt no. 742    -   */
#define GTM_DPLL_TE3I_ISR_C0              DUMMY_FUNC           /* Interrupt no. 743    -   */
#define GTM_DPLL_TE4I_ISR_C0              DUMMY_FUNC           /* Interrupt no. 744    -   */
#define GTM_DPLL_CDTI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 745    -   */
#define GTM_DPLL_CDSI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 746    -   */
#define GTM_DPLL_TORI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 747    -   */
#define GTM_DPLL_SORI_ISR_C0              DUMMY_FUNC           /* Interrupt no. 748    -   */
#define GTM_TIM0_CH0_ISR_C0               GTM_Tim0_Channel0_IntHandler  /* Interrupt no. 749  - */
#define GTM_TIM0_CH1_ISR_C0               GTM_Tim0_Channel1_IntHandler  /* Interrupt no. 750    -   */
#define GTM_TIM0_CH2_ISR_C0               GTM_Tim0_Channel2_IntHandler  /* Interrupt no. 751    -   */
#define GTM_TIM0_CH3_ISR_C0               DUMMY_FUNC           /* Interrupt no. 752    -   */
#define GTM_TIM0_CH4_ISR_C0               DUMMY_FUNC           /* Interrupt no. 753    -   */
#define GTM_TIM0_CH5_ISR_C0               DUMMY_FUNC           /* Interrupt no. 754    -   */
#define GTM_TIM0_CH6_ISR_C0               DUMMY_FUNC           /* Interrupt no. 755    -   */
#define GTM_TIM0_CH7_ISR_C0               DUMMY_FUNC           /* Interrupt no. 756    -   */
#define GTM_TIM1_CH0_ISR_C0               GTM_Tim1_Channel0_IntHandler  /* Interrupt no. 757    -   */
#define GTM_TIM1_CH1_ISR_C0               GTM_Tim1_Channel1_IntHandler  /* Interrupt no. 758    -   */
#define GTM_TIM1_CH2_ISR_C0               GTM_Tim1_Channel2_IntHandler  /* Interrupt no. 759    -   */
#define GTM_TIM1_CH3_ISR_C0               GTM_Tim1_Channel3_IntHandler  /* Interrupt no. 760    -   */
#define GTM_TIM1_CH4_ISR_C0               GTM_Tim1_Channel4_IntHandler  /* Interrupt no. 761    -   */
#define GTM_TIM1_CH5_ISR_C0               GTM_Tim1_Channel5_IntHandler  /* Interrupt no. 762    -   */
#define GTM_TIM1_CH6_ISR_C0               GTM_Tim1_Channel6_IntHandler  /* Interrupt no. 763    -   */
#define GTM_TIM1_CH7_ISR_C0               GTM_Tim1_Channel7_IntHandler  /* Interrupt no. 764    -   */
#define GTM_TIM2_CH0_ISR_C0               GTM_Tim2_Channel0_IntHandler  /* Interrupt no. 765    -   */
#define GTM_TIM2_CH1_ISR_C0               GTM_Tim2_Channel1_IntHandler  /* Interrupt no. 766    -   */
#define GTM_TIM2_CH2_ISR_C0               GTM_Tim2_Channel2_IntHandler  /* Interrupt no. 767    -   */
#define GTM_TIM2_CH3_ISR_C0               GTM_Tim2_Channel3_IntHandler  /* Interrupt no. 768    -   */
#define GTM_TIM2_CH4_ISR_C0               GTM_Tim2_Channel4_IntHandler  /* Interrupt no. 769    -   */
#define GTM_TIM2_CH5_ISR_C0               GTM_Tim2_Channel5_IntHandler  /* Interrupt no. 770    -   */
#define GTM_TIM2_CH6_ISR_C0               GTM_Tim2_Channel6_IntHandler  /* Interrupt no. 771    -   */
#define GTM_TIM2_CH7_ISR_C0               GTM_Tim2_Channel7_IntHandler  /* Interrupt no. 772    -   */
#define GTM_MCS0_CH0_ISR_C0               GTM_Mcs0_Channel0_IntHandler  /* Interrupt no. 781    -   */
#define GTM_MCS0_CH1_ISR_C0               GTM_Mcs0_Channel1_IntHandler  /* Interrupt no. 782    -   */
#define GTM_MCS0_CH2_ISR_C0               GTM_Mcs0_Channel2_IntHandler  /* Interrupt no. 783    -   */
#define GTM_MCS0_CH3_ISR_C0               GTM_Mcs0_Channel3_IntHandler  /* Interrupt no. 784    -   */
#define GTM_MCS0_CH4_ISR_C0               DUMMY_FUNC           /* Interrupt no. 785    -   */
#define GTM_MCS0_CH5_ISR_C0               GTM_Mcs0_Channel5_IntHandler  /* Interrupt no. 786    -   */
#define GTM_MCS0_CH6_ISR_C0               GTM_Mcs0_Channel6_IntHandler  /* Interrupt no. 787    -   */
#define GTM_MCS0_CH7_ISR_C0               GTM_Mcs0_Channel7_IntHandler  /* Interrupt no. 788    -   */
#define GTM_MCS1_CH0_ISR_C0               GTM_Mcs1_Channel0_IntHandler  /* Interrupt no. 789    -   */
#define GTM_MCS1_CH1_ISR_C0               GTM_Mcs1_Channel1_IntHandler  /* Interrupt no. 790    -   */
#define GTM_MCS1_CH2_ISR_C0               GTM_Mcs1_Channel2_IntHandler  /* Interrupt no. 791    -   */
#define GTM_MCS1_CH3_ISR_C0               GTM_Mcs1_Channel3_IntHandler  /* Interrupt no. 792    -   */
#define GTM_MCS1_CH4_ISR_C0               DUMMY_FUNC           /* Interrupt no. 793    -   */
#define GTM_MCS1_CH5_ISR_C0               GTM_Mcs1_Channel5_IntHandler  /* Interrupt no. 794    -   */
#define GTM_MCS1_CH6_ISR_C0               GTM_Mcs1_Channel6_IntHandler  /* Interrupt no. 795    -   */
#define GTM_MCS1_CH7_ISR_C0               GTM_Mcs1_Channel7_IntHandler  /* Interrupt no. 796    -   */
#define GTM_MCS2_CH0_ISR_C0               GTM_Mcs2_Channel0_IntHandler  /* Interrupt no. 797    -   */
#define GTM_MCS2_CH1_ISR_C0               GTM_Mcs2_Channel1_IntHandler  /* Interrupt no. 798    -   */
#define GTM_MCS2_CH2_ISR_C0               GTM_Mcs2_Channel2_IntHandler  /* Interrupt no. 799    -   */
#define GTM_MCS2_CH3_ISR_C0               GTM_Mcs2_Channel3_IntHandler  /* Interrupt no. 800    -   */
#define GTM_MCS2_CH4_ISR_C0               GTM_Mcs2_Channel4_IntHandler  /* Interrupt no. 801    -   */
#define GTM_MCS2_CH5_ISR_C0               GTM_Mcs2_Channel5_IntHandler  /* Interrupt no. 802    -   */
#define GTM_MCS2_CH6_ISR_C0               DUMMY_FUNC           /* Interrupt no. 803    -   */
#define GTM_MCS2_CH7_ISR_C0               DUMMY_FUNC           /* Interrupt no. 804    -   */
#define GTM_TOM0_CH0_1_ISR_C0             DUMMY_FUNC           /* Interrupt no. 813    -   */
#define GTM_TOM0_CH2_3_ISR_C0             DUMMY_FUNC           /* Interrupt no. 814    -   */
#define GTM_TOM0_CH4_5_ISR_C0             DUMMY_FUNC           /* Interrupt no. 815    -   */
#define GTM_TOM0_CH6_7_ISR_C0             DUMMY_FUNC           /* Interrupt no. 816    -   */
#define GTM_TOM0_CH8_9_ISR_C0             DUMMY_FUNC           /* Interrupt no. 817    -   */
#define GTM_TOM0_CH10_11_ISR_C0           DUMMY_FUNC           /* Interrupt no. 818    -   */
#define GTM_TOM0_CH12_13_ISR_C0           DUMMY_FUNC           /* Interrupt no. 819    -   */
#define GTM_TOM0_CH14_15_ISR_C0           DUMMY_FUNC           /* Interrupt no. 820    -   */
#define GTM_TOM1_CH0_1_ISR_C0             DUMMY_FUNC           /* Interrupt no. 821    -   */
#define GTM_TOM1_CH2_3_ISR_C0             DUMMY_FUNC           /* Interrupt no. 822    -   */
#define GTM_TOM1_CH4_5_ISR_C0             DUMMY_FUNC           /* Interrupt no. 823    -   */
#define GTM_TOM1_CH6_7_ISR_C0             DUMMY_FUNC           /* Interrupt no. 824    -   */
#define GTM_TOM1_CH8_9_ISR_C0             DUMMY_FUNC           /* Interrupt no. 825    -   */
#define GTM_TOM1_CH10_11_ISR_C0           DUMMY_FUNC           /* Interrupt no. 826    -   */
#define GTM_TOM1_CH12_13_ISR_C0           DUMMY_FUNC           /* Interrupt no. 827    -   */
#define GTM_TOM1_CH14_15_ISR_C0           DUMMY_FUNC           /* Interrupt no. 828    -   */
#define GTM_ATOM0_CH0_1_ISR_C0            GTM_Atom0_Ch0_Ch1_IntHandler           /* Interrupt no. 837    -   */
#define GTM_ATOM0_CH2_3_ISR_C0            DUMMY_FUNC           /* Interrupt no. 838    -   */
#define GTM_ATOM0_CH4_5_ISR_C0            DUMMY_FUNC           /* Interrupt no. 839    -   */
#define GTM_ATOM0_CH6_7_ISR_C0            DUMMY_FUNC           /* Interrupt no. 840    -   */
#define GTM_ATOM1_CH0_1_ISR_C0            DUMMY_FUNC           /* Interrupt no. 841    -   */
#define GTM_ATOM1_CH2_3_ISR_C0            DUMMY_FUNC           /* Interrupt no. 842    -   */
#define GTM_ATOM1_CH4_5_ISR_C0            DUMMY_FUNC           /* Interrupt no. 843    -   */
#define GTM_ATOM1_CH6_7_ISR_C0            DUMMY_FUNC           /* Interrupt no. 844    -   */
#define GTM_ATOM2_CH0_1_ISR_C0            DUMMY_FUNC           /* Interrupt no. 845    -   */
#define GTM_ATOM2_CH2_3_ISR_C0            DUMMY_FUNC           /* Interrupt no. 846    -   */
#define GTM_ATOM2_CH4_5_ISR_C0            DUMMY_FUNC           /* Interrupt no. 847    -   */
#define GTM_ATOM2_CH6_7_ISR_C0            DUMMY_FUNC           /* Interrupt no. 848    -   */
#define GTM_ATOM3_CH0_1_ISR_C0            DUMMY_FUNC           /* Interrupt no. 849    -   */
#define GTM_ATOM3_CH2_3_ISR_C0            DUMMY_FUNC           /* Interrupt no. 850    -   */
#define GTM_ATOM3_CH4_5_ISR_C0            DUMMY_FUNC           /* Interrupt no. 851    -   */
#define GTM_ATOM3_CH6_7_ISR_C0            DUMMY_FUNC           /* Interrupt no. 852    -   */
#define GTM_ERR_ISR_C0                    DUMMY_FUNC           /* Interrupt no. 931    -   */
/************************************************************************************************/
/**************************************** CCCU ISR **********************************************/
/************************************************************************************************/
#define CCCU_CSCE_CWEE_ISR_C0             DUMMY_FUNC           /* Interrupt no. 935    -   */  
/************************************************************************************************/

/* ------------------------------------------------------------------ */
/* Interrupts request symbols - c2 (z2)                               */
/* ------------------------------------------------------------------ */
/************************************************************************************************/
/*************************************** OSEK ISR ***********************************************/
/************************************************************************************************/
#ifdef _BUILD_TASK_
#define INTC_SSCIR0_ISR_C2                DUMMY_FUNC           /* Interrupt no. 0     - ivINT_SSCIR0_CLR0    */
#define INTC_SSCIR1_ISR_C2                DUMMY_FUNC           /* Interrupt no. 1     - ivINT_SSCIR1_CLR1    */
#define INTC_SSCIR2_ISR_C2                DUMMY_FUNC           /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_ISR_C2                DUMMY_FUNC           /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_ISR_C2                DUMMY_FUNC           /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_ISR_C2                DUMMY_FUNC           /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_ISR_C2                DUMMY_FUNC           /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_ISR_C2                DUMMY_FUNC           /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_ISR_C2                RoutineISR8          /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_ISR_C2                DUMMY_FUNC           /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_ISR_C2               DUMMY_FUNC           /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_ISR_C2               DUMMY_FUNC           /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_ISR_C2               DUMMY_FUNC           /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_ISR_C2               DUMMY_FUNC           /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_ISR_C2               DUMMY_FUNC           /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_ISR_C2               DUMMY_FUNC           /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_ISR_C2               DUMMY_FUNC           /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_ISR_C2               DUMMY_FUNC           /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_ISR_C2               DUMMY_FUNC           /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_ISR_C2               DUMMY_FUNC           /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_ISR_C2               DUMMY_FUNC           /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_ISR_C2               DUMMY_FUNC           /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_ISR_C2               DUMMY_FUNC           /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_ISR_C2               DUMMY_FUNC           /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_ISR_C2               DUMMY_FUNC           /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_ISR_C2               DUMMY_FUNC           /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_ISR_C2               DUMMY_FUNC           /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_ISR_C2               DUMMY_FUNC           /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_ISR_C2               DUMMY_FUNC           /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_ISR_C2               DUMMY_FUNC           /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_ISR_C2               DUMMY_FUNC           /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_ISR_C2               RoutineISR31         /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#else
#define INTC_SSCIR0_ISR_C2                DUMMY_FUNC           /* Interrupt no. 0     - ivINT_SSCIR0_CLR0    */
#define INTC_SSCIR1_ISR_C2                DUMMY_FUNC           /* Interrupt no. 1     - ivINT_SSCIR1_CLR1    */
#define INTC_SSCIR2_ISR_C2                DUMMY_FUNC           /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_ISR_C2                DUMMY_FUNC           /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_ISR_C2                DUMMY_FUNC           /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_ISR_C2                DUMMY_FUNC           /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_ISR_C2                DUMMY_FUNC           /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_ISR_C2                DUMMY_FUNC           /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_ISR_C2                DUMMY_FUNC           /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_ISR_C2                DUMMY_FUNC           /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_ISR_C2               DUMMY_FUNC           /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_ISR_C2               DUMMY_FUNC           /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_ISR_C2               DUMMY_FUNC           /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_ISR_C2               DUMMY_FUNC           /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_ISR_C2               DUMMY_FUNC           /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_ISR_C2               DUMMY_FUNC           /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_ISR_C2               DUMMY_FUNC           /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_ISR_C2               DUMMY_FUNC           /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_ISR_C2               DUMMY_FUNC           /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_ISR_C2               DUMMY_FUNC           /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_ISR_C2               DUMMY_FUNC           /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_ISR_C2               DUMMY_FUNC           /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_ISR_C2               DUMMY_FUNC           /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_ISR_C2               DUMMY_FUNC           /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_ISR_C2               DUMMY_FUNC           /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_ISR_C2               DUMMY_FUNC           /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_ISR_C2               DUMMY_FUNC           /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_ISR_C2               DUMMY_FUNC           /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_ISR_C2               DUMMY_FUNC           /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_ISR_C2               DUMMY_FUNC           /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_ISR_C2               DUMMY_FUNC           /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_ISR_C2               DUMMY_FUNC           /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#endif /* _BUILD_TASK_ */
/************************************************************************************************/
/*************************************** SWT ISR ************************************************/
/************************************************************************************************/
#define SWT0_ISR_C2                       DUMMY_FUNC           /* Interrupt no. 32    -    */
#define SWT2_ISR_C2                       DUMMY_FUNC           /* Interrupt no. 34    -   */
#define SWT3_ISR_C2                       DUMMY_FUNC           /* Interrupt no. 35    -   */
/************************************************************************************************/
/*************************************** STM ISR ************************************************/
/************************************************************************************************/
#define STM_INT_0_CIR0_ISR_C2             DUMMY_FUNC           /* Interrupt no. 36    -   */
#define STM_INT_0_CIR1_ISR_C2             DUMMY_FUNC           /* Interrupt no. 37    -   */
#define STM_INT_0_CIR2_ISR_C2             DUMMY_FUNC           /* Interrupt no. 38    -   */
#define STM_INT_0_CIR3_ISR_C2             DUMMY_FUNC           /* Interrupt no. 39    -   */
#define STM_INT_2_CIR0_ISR_C2             DUMMY_FUNC           /* Interrupt no. 44    -   */
#define STM_INT_2_CIR1_ISR_C2             DUMMY_FUNC           /* Interrupt no. 45    -   */
#define STM_INT_2_CIR2_ISR_C2             DUMMY_FUNC           /* Interrupt no. 46    -   */
#define STM_INT_2_CIR3_ISR_C2             DUMMY_FUNC           /* Interrupt no. 47    -   */
/************************************************************************************************/
/*************************************** EDMA ISR ***********************************************/
/************************************************************************************************/
#define EDMA_ERL_ERR31_ERR0_ISR_C2        DUMMY_FUNC           /* Interrupt no. 52   - ivINT_EDMA_ERRL_ERR31_0  */
#define EDMA_IRQRL_INT00_ISR_C2           DMA_CH0_ISR          /* Interrupt no. 53   - ivINT_EDMA_IRQRL_INT0  */
#define EDMA_IRQRL_INT01_ISR_C2           DMA_CH1_ISR          /* Interrupt no. 54   - ivINT_EDMA_IRQRL_INT1  */
#define EDMA_IRQRL_INT02_ISR_C2           DUMMY_FUNC          /* Interrupt no. 55   - ivINT_EDMA_IRQRL_INT2  */
#define EDMA_IRQRL_INT03_ISR_C2           DUMMY_FUNC          /* Interrupt no. 56   - ivINT_EDMA_IRQRL_INT3  */
#define EDMA_IRQRL_INT04_ISR_C2           DUMMY_FUNC           /* Interrupt no. 57   - ivINT_EDMA_IRQRL_INT4  */
#define EDMA_IRQRL_INT05_ISR_C2           DUMMY_FUNC           /* Interrupt no. 58   - ivINT_EDMA_IRQRL_INT5  */
#define EDMA_IRQRL_INT06_ISR_C2           DUMMY_FUNC           /* Interrupt no. 59   - ivINT_EDMA_IRQRL_INT6  */
#define EDMA_IRQRL_INT07_ISR_C2           DUMMY_FUNC           /* Interrupt no. 60   - ivINT_EDMA_IRQRL_INT7  */
#define EDMA_IRQRL_INT08_ISR_C2           DUMMY_FUNC           /* Interrupt no. 61   - ivINT_EDMA_IRQRL_INT8  */
#define EDMA_IRQRL_INT09_ISR_C2           DUMMY_FUNC           /* Interrupt no. 62   - ivINT_EDMA_IRQRL_INT9  */
#define EDMA_IRQRL_INT10_ISR_C2           DUMMY_FUNC           /* Interrupt no. 63   - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_ISR_C2           DUMMY_FUNC           /* Interrupt no. 64   - ivINT_EDMA_IRQRL_INT11  */
#define EDMA_IRQRL_INT12_ISR_C2           DUMMY_FUNC           /* Interrupt no. 65   - ivINT_EDMA_IRQRL_INT12  */
#ifdef _BUILD_SPI_
#define EDMA_IRQRL_INT13_ISR_C2           DUMMY_FUNC           /* Interrupt no. 66   - ivINT_EDMA_IRQRL_INT13  */
#else
#define EDMA_IRQRL_INT13_ISR_C2           DUMMY_FUNC           /* Interrupt no. 66   - ivINT_EDMA_IRQRL_INT13  */
#endif
#define EDMA_IRQRL_INT14_ISR_C2           DUMMY_FUNC           /* Interrupt no. 67   - ivINT_EDMA_IRQRL_INT14  */
#ifdef _BUILD_SPI
#define EDMA_IRQRL_INT15_ISR_C2           DUMMY_FUNC           /* Interrupt no. 68   - ivINT_EDMA_IRQRL_INT15  */
#else
#define EDMA_IRQRL_INT15_ISR_C2           DUMMY_FUNC           /* Interrupt no. 68   - ivINT_EDMA_IRQRL_INT15  */
#endif
#define EDMA_IRQRL_INT16_ISR_C2           DMA_CH16_ISR         /* Interrupt no. 69   - ivINT_EDMA_IRQRL_INT16  */
#define EDMA_IRQRL_INT17_ISR_C2           DMA_CH17_ISR         /* Interrupt no. 70   - ivINT_EDMA_IRQRL_INT17  */
#define EDMA_IRQRL_INT18_ISR_C2           DUMMY_FUNC           /* Interrupt no. 71   - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_ISR_C2           DUMMY_FUNC           /* Interrupt no. 72   - ivINT_EDMA_IRQRL_INT19  */
#define EDMA_IRQRL_INT20_ISR_C2           DUMMY_FUNC           /* Interrupt no. 73   - ivINT_EDMA_IRQRL_INT20  */
#define EDMA_IRQRL_INT21_ISR_C2           DUMMY_FUNC           /* Interrupt no. 74   - ivINT_EDMA_IRQRL_INT21  */
#define EDMA_IRQRL_INT22_ISR_C2           DUMMY_FUNC           /* Interrupt no. 75   - ivINT_EDMA_IRQRL_INT22  */
#define EDMA_IRQRL_INT23_ISR_C2           DUMMY_FUNC           /* Interrupt no. 76   - ivINT_EDMA_IRQRL_INT23  */
#define EDMA_IRQRL_INT24_ISR_C2           DUMMY_FUNC         /* Interrupt no. 77   - ivINT_EDMA_IRQRL_INT24  */
#define EDMA_IRQRL_INT25_ISR_C2           DUMMY_FUNC         /* Interrupt no. 78   - ivINT_EDMA_IRQRL_INT25  */
#define EDMA_IRQRL_INT26_ISR_C2           DUMMY_FUNC           /* Interrupt no. 79   - ivINT_EDMA_IRQRL_INT26  */
#define EDMA_IRQRL_INT27_ISR_C2           DUMMY_FUNC           /* Interrupt no. 80   - ivINT_EDMA_IRQRL_INT27  */
#define EDMA_IRQRL_INT28_ISR_C2           DUMMY_FUNC           /* Interrupt no. 81   - ivINT_EDMA_IRQRL_INT28  */
#define EDMA_IRQRL_INT29_ISR_C2           DUMMY_FUNC           /* Interrupt no. 82   - ivINT_EDMA_IRQRL_INT29  */
#define EDMA_IRQRL_INT30_ISR_C2           DUMMY_FUNC           /* Interrupt no. 83   - ivINT_EDMA_IRQRL_INT30  */
#define EDMA_IRQRL_INT31_ISR_C2           DUMMY_FUNC           /* Interrupt no. 84   - ivINT_EDMA_IRQRL_INT31  */
/************************************************************************************************/
/*************************************** FLASH ISR **********************************************/
/************************************************************************************************/
#define FLASH_SRAM_ECC_INT_ISR_C2         DUMMY_FUNC           /* Interrupt no. 185  - ivINT_FLASH_ECC        */
/************************************************************************************************/
/*************************************** ETHERNET ISR *******************************************/
/************************************************************************************************/
#define ETH_EIR_TX_ISR_C2                 DUMMY_FUNC           /* Interrupt no. 218   -   */
#define ETH_EIR_RX_ISR_C2                 DUMMY_FUNC           /* Interrupt no. 219   -   */
#define ETH_EIR_COMB_ISR_C2               DUMMY_FUNC           /* Interrupt no. 220   -   */
/************************************************************************************************/
/*************************************** PIT-RTI ISR ********************************************/
/************************************************************************************************/
#ifdef _BUILD_PIT_
#define PIT_INT0_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 231  -        */
#define PIT_RTI_PIT0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 241  -        */
#else
#define PIT_INT0_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 231  -        */
#define PIT_RTI_PIT0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 241  -        */
#endif
/************************************************************************************************/
/*************************************** XOSC ISR ***********************************************/
/************************************************************************************************/
#define XOSC_CTL_ISR_C2                   DUMMY_FUNC          /* Interrupt no. 242   - ivFMPLL_SYNSR_LOCF  */
/************************************************************************************************/
/*************************************** SIUL ISR ***********************************************/
/************************************************************************************************/
#define SIUL2_COMB_EXT0_ISR_C2            DUMMY_FUNC          /* Interrupt no. 243   - ivINT_SIU_OSR_OVF15_0  */
#define SIUL2_COMB_EXT1_ISR_C2            DUMMY_FUNC          /* Interrupt no. 244   - ivINT_SIU_EISR_EIF0  */
/************************************************************************************************/
/*************************************** MC ISR *************************************************/
/************************************************************************************************/
#define MC_ME_SAFE_ISR_C2                 DUMMY_FUNC          /* Interrupt no. 251   -   */
#define MC_ME_MTC_ISR_C2                  DUMMY_FUNC          /* Interrupt no. 252   -   */
#define MC_ME_IMODE_ISR_C2                DUMMY_FUNC          /* Interrupt no. 253   -   */
#define MC_ME_ICONF_ISR_C2                DUMMY_FUNC          /* Interrupt no. 254   -   */
#define MC_RGM_ISR_C2                     DUMMY_FUNC          /* Interrupt no. 255   -   */
/************************************************************************************************/
/*************************************** DSPI ISR ***********************************************/
/************************************************************************************************/
#define DSPI_0_ISR_TFUF_RFOF_ISR_C2       DUMMY_FUNC          /* Interrupt no. 259  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_0_ISR_EOQF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 260  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_0_ISR_TFFF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 261  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_0_ISR_TCF_ISR_C2             DUMMY_FUNC          /* Interrupt no. 262  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_0_ISR_RFDF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 263  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDTCF_SPITCF_ISR_C2   DUMMY_FUNC          /* Interrupt no. 264  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDFFF_ISR_C2          DUMMY_FUNC          /* Interrupt no. 265  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_SPEF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 266  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_TFUF_RFOF_ISR_C2       DUMMY_FUNC          /* Interrupt no. 268  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_1_ISR_EOQF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 269  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_1_ISR_TFFF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 270  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_1_ISR_TCF_ISR_C2             DUMMY_FUNC          /* Interrupt no. 271  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_1_ISR_RFDF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 272  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDTCF_SPITCF_ISR_C2   DUMMY_FUNC          /* Interrupt no. 273  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDFFF_ISR_C2          DUMMY_FUNC          /* Interrupt no. 274  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_SPEF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 275  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_TFUF_RFOF_ISR_C2       DUMMY_FUNC          /* Interrupt no. 277  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_2_ISR_EOQF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 278  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_2_ISR_TFFF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 279  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_2_ISR_TCF_ISR_C2             DUMMY_FUNC          /* Interrupt no. 280  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_2_ISR_RFDF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 281  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDTCF_SPITCF_ISR_C2   DUMMY_FUNC          /* Interrupt no. 282  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDFFF_ISR_C2          DUMMY_FUNC          /* Interrupt no. 283  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_SPEF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 284  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_TFUF_RFOF_ISR_C2       DUMMY_FUNC          /* Interrupt no. 295  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_4_ISR_EOQF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 296  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_4_ISR_TFFF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 297  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_4_ISR_TCF_ISR_C2             DUMMY_FUNC          /* Interrupt no. 298  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_4_ISR_RFDF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 299  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDTCF_SPITCF_ISR_C2   DUMMY_FUNC          /* Interrupt no. 300  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDFFF_DSITCF_ISR_C2   DUMMY_FUNC          /* Interrupt no. 301  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_SPEF_DPEF_ISR_C2       DUMMY_FUNC          /* Interrupt no. 302  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_DDIF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 303  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_TFUF_RFOF_ISR_C2       DUMMY_FUNC          /* Interrupt no. 304  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_5_ISR_EOQF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 305  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_5_ISR_TFFF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 306  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_5_ISR_TCF_ISR_C2             DUMMY_FUNC          /* Interrupt no. 307  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_5_ISR_RFDF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 308  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDTCF_SPITCF_ISR_C2   DUMMY_FUNC          /* Interrupt no. 309  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDFFF_DSITCF_ISR_C2   DUMMY_FUNC          /* Interrupt no. 310  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_SPEF_DPEF_ISR_C2       DUMMY_FUNC          /* Interrupt no. 311  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_DDIF_ISR_C2            DUMMY_FUNC          /* Interrupt no. 312  - ivINT_DSPI_B_ISR_RFDF  */
/************************************************************************************************/
/*************************************** LINFLEX ISR ********************************************/
/************************************************************************************************/
#define LINFLEX_0_RX_COMB_ISR_C2          DUMMY_FUNC          /* Interrupt no. 376    -   */
#define LINFLEX_0_TX_COMB_ISR_C2          DUMMY_FUNC          /* Interrupt no. 377    -   */
#define LINFLEX_0_ERROR_COMB_ISR_C2       DUMMY_FUNC          /* Interrupt no. 378    -   */
#define LINFLEX_1_RX_COMB_ISR_C2          DUMMY_FUNC          /* Interrupt no. 380    -   */
#define LINFLEX_1_TX_COMB_ISR_C2          DUMMY_FUNC          /* Interrupt no. 381    -   */
#define LINFLEX_1_ERROR_COMB_ISR_C2       DUMMY_FUNC          /* Interrupt no. 382    -   */
#define LINFLEX_2_RX_COMB_ISR_C2          DUMMY_FUNC          /* Interrupt no. 384    -   */
#define LINFLEX_2_TX_COMB_ISR_C2          DUMMY_FUNC          /* Interrupt no. 385    -   */
#define LINFLEX_2_ERROR_COMB_ISR_C2       DUMMY_FUNC          /* Interrupt no. 386    -   */
#define LINFLEX_14_RX_COMB_ISR_C2         DUMMY_FUNC          /* Interrupt no. 432    -   */
#define LINFLEX_14_TX_COMB_ISR_C2         DUMMY_FUNC          /* Interrupt no. 433    -   */
#define LINFLEX_14_ERROR_COMB_ISR_C2      DUMMY_FUNC          /* Interrupt no. 434    -   */
#define LINFLEX_15_RX_COMB_ISR_C2         DUMMY_FUNC          /* Interrupt no. 436    -   */
#define LINFLEX_15_TX_COMB_ISR_C2         DUMMY_FUNC          /* Interrupt no. 437    -   */
#define LINFLEX_15_ERROR_COMB_ISR_C2      DUMMY_FUNC          /* Interrupt no. 438    -   */
/************************************************************************************************/
/**************************************** I2C ISR ***********************************************/
/************************************************************************************************/
#define I2C_IBIF_IAAS_IBAL_ISR_C2         DUMMY_FUNC          /* Interrupt no. 440    -   */
/************************************************************************************************/
/*************************************** FLEXRAY ISR ********************************************/
/************************************************************************************************/
#define FLEXRAY_LRNE_ISR_C2               DUMMY_FUNC          /* Interrupt no. 453 - ivINT_FlexRAY LRNE   */
#define FLEXRAY_LRCE_ISR_C2               DUMMY_FUNC          /* Interrupt no. 454 - ivINT_FlexRAY LRCE   */
#define FLEXRAY_FAFAIF_ISR_C2             DUMMY_FUNC          /* Interrupt no. 455 - ivINT_FlexRAY FAFAIF */
#define FLEXRAY_FAFBIF_ISR_C2             DUMMY_FUNC          /* Interrupt no. 456 - ivINT_FlexRAY FAFBIF */
#define FLEXRAY_WUPIF_ISR_C2              DUMMY_FUNC          /* Interrupt no. 457 - ivINT_FlexRAY WUPIF  */
#define FLEXRAY_PRIF_ISR_C2               DUMMY_FUNC          /* Interrupt no. 458 - ivINT_FlexRAY PRIF   */
#define FLEXRAY_CHIF_ISR_C2               DUMMY_FUNC          /* Interrupt no. 459 - ivINT_FlexRAY CHIF   */
#define FLEXRAY_TBIF_ISR_C2               DUMMY_FUNC          /* Interrupt no. 460 - ivINT_FlexRAY TBIF   */
#define FLEXRAY_RBIF_ISR_C2               DUMMY_FUNC          /* Interrupt no. 461 - ivINT_FlexRAY RBIF   */
#define FLEXRAY_MIF_ISR_C2                DUMMY_FUNC          /* Interrupt no. 462 - ivINT_FlexRAY MIF    */
/************************************************************************************************/
/*************************************** GR ISR ** **********************************************/
/************************************************************************************************/
#define GR_VD_ISR_C2                      DUMMY_FUNC          /* Interrupt no. 477   - ivGR_VD  */
/************************************************************************************************/
/*************************************** EPR ISR ************************************************/
/************************************************************************************************/
#define EPR_TEMP_ISR_C2                   DUMMY_FUNC           /* Interrupt no. 478   - ivEPR_TEMP  */
/************************************************************************************************/
/*************************************** FCCU ISR ***********************************************/
/************************************************************************************************/
#define FCCU_ALRM_STAT_ISR_C2             DUMMY_FUNC           /* Interrupt no. 488   - ivFMPLL_SYNSR_LOCF  */
#define FCCU_CFG_TO_STAT_ISR_C2           DUMMY_FUNC           /* Interrupt no. 489   - ivFMPLL_SYNSR_LOLF  */
/************************************************************************************************/
/*************************************** STCU ISR ***********************************************/
/************************************************************************************************/
#define STCU_LBIE_ISR_C2                  DUMMY_FUNC           /* Interrupt no. 494   - ivSTCU_LBIE  */
#define STCU_MBIE_ISR_C2                  DUMMY_FUNC           /* Interrupt no. 495   - ivSTCU_MBIE  */
/************************************************************************************************/
/*************************************** SAR ISR ************************************************/
/************************************************************************************************/
#define SAR_0_INT_ISR_C2                  DUMMY_FUNC           /* Interrupt no. 528    -   */
#define SAR_2_INT_ISR_C2                  DUMMY_FUNC           /* Interrupt no. 530    -   */
#define SAR_4_INT_ISR_C2                  DUMMY_FUNC           /* Interrupt no. 532    -   */
#define SAR_6_INT_ISR_C2                  DUMMY_FUNC           /* Interrupt no. 534    -   */
#define SAR_B_INT_ISR_C2                  DUMMY_FUNC           /* Interrupt no. 543    -   */
/************************************************************************************************/
/*************************************** SD ISR *************************************************/
/************************************************************************************************/
#define SD_0_INT_ISR_C2                   DUMMY_FUNC           /* Interrupt no. 544  - ivSD_0_INT  */
#define SD_3_INT_ISR_C2                   DUMMY_FUNC           /* Interrupt no. 547  - ivSD_3_INT  */
/************************************************************************************************/
/**************************************** SENT ISR **********************************************/
/************************************************************************************************/
#define SENT_0_FAST_COMB_ISR_C2           DUMMY_FUNC           /* Interrupt no. 558    - ivSENT_0_FAST_COMB  */
#define SENT_0_SLOW_COMB_ISR_C2           DUMMY_FUNC           /* Interrupt no. 559    -   */
#define SENT_0_GBL_ERROR_ISR_C2           DUMMY_FUNC           /* Interrupt no. 560    -   */
#define SENT_1_SLOW_RDY_ISR_C2            DUMMY_FUNC           /* Interrupt no. 562    -   */
#define SENT_1_FAST_RDY_ISR_C2            DUMMY_FUNC           /* Interrupt no. 561    -   */
#define SENT_1_GBL_ERROR_ISR_C2           DUMMY_FUNC           /* Interrupt no. 563    -   */
#define SENT_0_FMSG_0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 564    -   */
#define SENT_0_SMSG_0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 565    -   */
#define SENT_0_ERROR_0_ISR_C2             DUMMY_FUNC           /* Interrupt no. 566    -   */
#define SENT_0_FMSG_1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 567    -   */
#define SENT_0_SMSG_1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 568    -   */
#define SENT_0_ERROR_1_ISR_C2             DUMMY_FUNC           /* Interrupt no. 569    -   */
#define SENT_0_FMSG_2_ISR_C2              DUMMY_FUNC           /* Interrupt no. 570    -   */
#define SENT_0_SMSG_2_ISR_C2              DUMMY_FUNC           /* Interrupt no. 571    -   */
#define SENT_0_ERROR_2_ISR_C2             DUMMY_FUNC           /* Interrupt no. 572    -   */
#define SENT_0_FMSG_3_ISR_C2              DUMMY_FUNC           /* Interrupt no. 573    -   */
#define SENT_0_SMSG_3_ISR_C2              DUMMY_FUNC           /* Interrupt no. 574    -   */
#define SENT_0_ERROR_3_ISR_C2             DUMMY_FUNC           /* Interrupt no. 575    -   */
#define SENT_1_FMSG_0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 579    -   */
#define SENT_1_SMSG_0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 580    -   */
#define SENT_1_ERROR_0_ISR_C2             DUMMY_FUNC           /* Interrupt no. 581    -   */
#define SENT_1_FMSG_1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 582    -   */
#define SENT_1_SMSG_1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 583    -   */
#define SENT_1_ERROR_1_ISR_C2             DUMMY_FUNC           /* Interrupt no. 584    -   */
/************************************************************************************************/
/*************************************** PSI ISR ************************************************/
/************************************************************************************************/
#define PSI5_0_DMA_0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 612    -   */
#define PSI5_0_GEN_0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 613    -   */
#define PSI5_0_NEW_MSG_0_ISR_C2           DUMMY_FUNC           /* Interrupt no. 614    -   */
#define PSI5_0_MSG_OW_0_ISR_C2            DUMMY_FUNC           /* Interrupt no. 615    -   */
#define PSI5_0_ERROR_COMB_0_ISR_C2        DUMMY_FUNC           /* Interrupt no. 616    -   */
#define PSI5_0_GLOBAL_0_ISR_C2            DUMMY_FUNC           /* Interrupt no. 617    -   */
#define PSI5_1_DMA_0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 624    -   */
#define PSI5_1_GEN_0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 625    -   */
#define PSI5_1_NEW_MSG_0_ISR_C2           DUMMY_FUNC           /* Interrupt no. 626    -   */
#define PSI5_1_MSG_OW_0_ISR_C2            DUMMY_FUNC           /* Interrupt no. 627    -   */
#define PSI5_1_ERROR_COMB_0_ISR_C2        DUMMY_FUNC           /* Interrupt no. 628    -   */
#define PSI5_1_GLOBAL_0_ISR_C2            DUMMY_FUNC           /* Interrupt no. 629    -   */
/************************************************************************************************/
/*************************************** SIPI ISR ***********************************************/
/************************************************************************************************/
#define SIPI_ERROR_COMB_ISR_C2            DUMMY_FUNC           /* Interrupt no. 654    -   */
#define SIPI_CRC_ERROR_ISR_C2             DUMMY_FUNC           /* Interrupt no. 655    -   */
#define SIPI_CH0_RX_ISR_C2                DUMMY_FUNC           /* Interrupt no. 656    -   */
#define SIPI_CH1_RX_ISR_C2                DUMMY_FUNC           /* Interrupt no. 657    -   */
#define SIPI_CH2_RX_ISR_C2                DUMMY_FUNC           /* Interrupt no. 658    -   */
#define SIPI_CH3_RX_ISR_C2                DUMMY_FUNC           /* Interrupt no. 659    -   */
#define SIPI_EVENT_COMB_ISR_C2            DUMMY_FUNC           /* Interrupt no. 660    -   */
/************************************************************************************************/
/*************************************** LFAST ISR **********************************************/
/************************************************************************************************/
#define LFAST_0_TX_ISR_C2                 DUMMY_FUNC           /* Interrupt no. 661    -   */
#define LFAST_0_TX_ERROR_ISR_C2           DUMMY_FUNC           /* Interrupt no. 662    -   */
#define LFAST_0_RX_ISR_C2                 DUMMY_FUNC           /* Interrupt no. 663    -   */
#define LFAST_0_RX_ERROR_ISR_C2           DUMMY_FUNC           /* Interrupt no. 664    -   */
#define LFAST_0_ICLC_RX_ISR_C2            DUMMY_FUNC           /* Interrupt no. 665    -   */
/************************************************************************************************/
/*************************************** JTAG ISR ***********************************************/
/************************************************************************************************/
#define JTAG_GM_ISR_C2                    DUMMY_FUNC           /* Interrupt no. 674    -   */
#define JTAG_DC_ISR_C2                    DUMMY_FUNC           /* Interrupt no. 675    -   */
/************************************************************************************************/
/*************************************** M_TTCAN ISR ********************************************/
/************************************************************************************************/
#define M_TTCAN_LINE0_ISR_C2              DUMMY_FUNC           /* Interrupt no. 677    -   */
#define M_TTCAN_LINE1_ISR_C2              DUMMY_FUNC           /* Interrupt no. 678    -   */
#define M_TTCAN_RTMI_ISR_C2               DUMMY_FUNC           /* Interrupt no. 679    -   */
/************************************************************************************************/
/**************************************** M_CAN ISR *********************************************/
/************************************************************************************************/
#define MCAN1_LINE0_ISR_C2                DUMMY_FUNC           /* Interrupt no. 688    -   */
#define MCAN1_LINE1_ISR_C2                DUMMY_FUNC           /* Interrupt no. 689    -   */
#define MCAN2_LINE0_ISR_C2                DUMMY_FUNC           /* Interrupt no. 690    -   */
#define MCAN2_LINE1_ISR_C2                DUMMY_FUNC           /* Interrupt no. 691    -   */
/************************************************************************************************/
/*************************************** GTM ISR ************************************************/
/************************************************************************************************/
#define GTM_AEI_ISR_C2                    DUMMY_FUNC           /* Interrupt no. 706    -   */
#define GTM_ARU_NEW_DATA0_ISR_C2          DUMMY_FUNC           /* Interrupt no. 707    -   */
#define GTM_ARU_NEW_DATA1_ISR_C2          DUMMY_FUNC           /* Interrupt no. 708    -   */
#define GTM_ARU_ACC_ACK_ISR_C2            DUMMY_FUNC           /* Interrupt no. 709    -   */
#define GTM_BRC_ISR_C2                    DUMMY_FUNC           /* Interrupt no. 710    -   */
#define GTM_CMP_ISR_C2                    DUMMY_FUNC           /* Interrupt no. 711    -   */
#define GTM_SPE0_ISR_C2                   DUMMY_FUNC           /* Interrupt no. 712    -   */
#define GTM_SPE1_ISR_C2                   DUMMY_FUNC           /* Interrupt no. 713    -   */
#define GTM_PSM0_CH0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 714    -   */
#define GTM_PSM0_CH1_ISR_C2               DUMMY_FUNC           /* Interrupt no. 715    -   */
#define GTM_PSM0_CH2_ISR_C2               DUMMY_FUNC           /* Interrupt no. 716    -   */
#define GTM_PSM0_CH3_ISR_C2               DUMMY_FUNC           /* Interrupt no. 717    -   */
#define GTM_PSM0_CH4_ISR_C2               DUMMY_FUNC           /* Interrupt no. 718    -   */
#define GTM_PSM0_CH5_ISR_C2               DUMMY_FUNC           /* Interrupt no. 719    -   */
#define GTM_PSM0_CH6_ISR_C2               DUMMY_FUNC           /* Interrupt no. 720    -   */
#define GTM_PSM0_CH7_ISR_C2               DUMMY_FUNC           /* Interrupt no. 721    -   */
#define GTM_DPLL_DCGI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 722    -   */
#define GTM_DPLL_EDI_ISR_C2               DUMMY_FUNC           /* Interrupt no. 723    -   */
#define GTM_DPLL_TINI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 724    -   */
#define GTM_DPLL_TAXI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 725    -   */
#define GTM_DPLL_SISI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 726    -   */
#define GTM_DPLL_TISI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 727    -   */
#define GTM_DPLL_MSI_ISR_C2               DUMMY_FUNC           /* Interrupt no. 728    -   */
#define GTM_DPLL_MTI_ISR_C2               DUMMY_FUNC           /* Interrupt no. 729    -   */
#define GTM_DPLL_SASI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 730    -   */
#define GTM_DPLL_TASI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 731    -   */
#define GTM_DPLL_PWI_ISR_C2               DUMMY_FUNC           /* Interrupt no. 732    -   */
#define GTM_DPLL_W2I_ISR_C2               DUMMY_FUNC           /* Interrupt no. 733    -   */
#define GTM_DPLL_W1I_ISR_C2               DUMMY_FUNC           /* Interrupt no. 734    -   */
#define GTM_DPLL_GL1I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 735    -   */
#define GTM_DPLL_LL1I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 736    -   */
#define GTM_DPLL_EI_ISR_C2                DUMMY_FUNC           /* Interrupt no. 737    -   */
#define GTM_DPLL_GL2I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 738    -   */
#define GTM_DPLL_LL2I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 739    -   */
#define GTM_DPLL_TE0I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 740    -   */
#define GTM_DPLL_TE1I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 741    -   */
#define GTM_DPLL_TE2I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 742    -   */
#define GTM_DPLL_TE3I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 743    -   */
#define GTM_DPLL_TE4I_ISR_C2              DUMMY_FUNC           /* Interrupt no. 744    -   */
#define GTM_DPLL_CDTI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 745    -   */
#define GTM_DPLL_CDSI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 746    -   */
#define GTM_DPLL_TORI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 747    -   */
#define GTM_DPLL_SORI_ISR_C2              DUMMY_FUNC           /* Interrupt no. 748    -   */
#define GTM_TIM0_CH0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 749    -   */
#define GTM_TIM0_CH1_ISR_C2               DUMMY_FUNC           /* Interrupt no. 750    -   */
#define GTM_TIM0_CH2_ISR_C2               DUMMY_FUNC           /* Interrupt no. 751    -   */
#define GTM_TIM0_CH3_ISR_C2               DUMMY_FUNC           /* Interrupt no. 752    -   */
#define GTM_TIM0_CH4_ISR_C2               DUMMY_FUNC           /* Interrupt no. 753    -   */
#define GTM_TIM0_CH5_ISR_C2               DUMMY_FUNC           /* Interrupt no. 754    -   */
#define GTM_TIM0_CH6_ISR_C2               DUMMY_FUNC           /* Interrupt no. 755    -   */
#define GTM_TIM0_CH7_ISR_C2               DUMMY_FUNC           /* Interrupt no. 756    -   */
#define GTM_TIM1_CH0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 757    -   */
#define GTM_TIM1_CH1_ISR_C2               DUMMY_FUNC           /* Interrupt no. 758    -   */
#define GTM_TIM1_CH2_ISR_C2               DUMMY_FUNC           /* Interrupt no. 759    -   */
#define GTM_TIM1_CH3_ISR_C2               DUMMY_FUNC           /* Interrupt no. 760    -   */
#define GTM_TIM1_CH4_ISR_C2               DUMMY_FUNC           /* Interrupt no. 761    -   */
#define GTM_TIM1_CH5_ISR_C2               DUMMY_FUNC           /* Interrupt no. 762    -   */
#define GTM_TIM1_CH6_ISR_C2               DUMMY_FUNC           /* Interrupt no. 763    -   */
#define GTM_TIM1_CH7_ISR_C2               DUMMY_FUNC           /* Interrupt no. 764    -   */
#define GTM_TIM2_CH0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 765    -   */
#define GTM_TIM2_CH1_ISR_C2               DUMMY_FUNC           /* Interrupt no. 766    -   */
#define GTM_TIM2_CH2_ISR_C2               DUMMY_FUNC           /* Interrupt no. 767    -   */
#define GTM_TIM2_CH3_ISR_C2               DUMMY_FUNC           /* Interrupt no. 768    -   */
#define GTM_TIM2_CH4_ISR_C2               DUMMY_FUNC           /* Interrupt no. 769    -   */
#define GTM_TIM2_CH5_ISR_C2               DUMMY_FUNC           /* Interrupt no. 770    -   */
#define GTM_TIM2_CH6_ISR_C2               DUMMY_FUNC           /* Interrupt no. 771    -   */
#define GTM_TIM2_CH7_ISR_C2               DUMMY_FUNC           /* Interrupt no. 772    -   */
#define GTM_MCS0_CH0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 781    -   */
#define GTM_MCS0_CH1_ISR_C2               DUMMY_FUNC           /* Interrupt no. 782    -   */
#define GTM_MCS0_CH2_ISR_C2               DUMMY_FUNC           /* Interrupt no. 783    -   */
#define GTM_MCS0_CH3_ISR_C2               DUMMY_FUNC           /* Interrupt no. 784    -   */
#define GTM_MCS0_CH4_ISR_C2               DUMMY_FUNC           /* Interrupt no. 785    -   */
#define GTM_MCS0_CH5_ISR_C2               DUMMY_FUNC           /* Interrupt no. 786    -   */
#define GTM_MCS0_CH6_ISR_C2               DUMMY_FUNC           /* Interrupt no. 787    -   */
#define GTM_MCS0_CH7_ISR_C2               DUMMY_FUNC           /* Interrupt no. 788    -   */
#define GTM_MCS1_CH0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 789    -   */
#define GTM_MCS1_CH1_ISR_C2               DUMMY_FUNC           /* Interrupt no. 790    -   */
#define GTM_MCS1_CH2_ISR_C2               DUMMY_FUNC           /* Interrupt no. 791    -   */
#define GTM_MCS1_CH3_ISR_C2               DUMMY_FUNC           /* Interrupt no. 792    -   */
#define GTM_MCS1_CH4_ISR_C2               DUMMY_FUNC           /* Interrupt no. 793    -   */
#define GTM_MCS1_CH5_ISR_C2               DUMMY_FUNC           /* Interrupt no. 794    -   */
#define GTM_MCS1_CH6_ISR_C2               DUMMY_FUNC           /* Interrupt no. 795    -   */
#define GTM_MCS1_CH7_ISR_C2               DUMMY_FUNC           /* Interrupt no. 796    -   */
#define GTM_MCS2_CH0_ISR_C2               DUMMY_FUNC           /* Interrupt no. 797    -   */
#define GTM_MCS2_CH1_ISR_C2               DUMMY_FUNC           /* Interrupt no. 798    -   */
#define GTM_MCS2_CH2_ISR_C2               DUMMY_FUNC           /* Interrupt no. 799    -   */
#define GTM_MCS2_CH3_ISR_C2               DUMMY_FUNC           /* Interrupt no. 800    -   */
#define GTM_MCS2_CH4_ISR_C2               DUMMY_FUNC           /* Interrupt no. 801    -   */
#define GTM_MCS2_CH5_ISR_C2               DUMMY_FUNC           /* Interrupt no. 802    -   */
#define GTM_MCS2_CH6_ISR_C2               DUMMY_FUNC           /* Interrupt no. 803    -   */
#define GTM_MCS2_CH7_ISR_C2               DUMMY_FUNC           /* Interrupt no. 804    -   */
#define GTM_TOM0_CH0_1_ISR_C2             DUMMY_FUNC           /* Interrupt no. 813    -   */
#define GTM_TOM0_CH2_3_ISR_C2             DUMMY_FUNC           /* Interrupt no. 814    -   */
#define GTM_TOM0_CH4_5_ISR_C2             DUMMY_FUNC           /* Interrupt no. 815    -   */
#define GTM_TOM0_CH6_7_ISR_C2             DUMMY_FUNC           /* Interrupt no. 816    -   */
#define GTM_TOM0_CH8_9_ISR_C2             DUMMY_FUNC           /* Interrupt no. 817    -   */
#define GTM_TOM0_CH10_11_ISR_C2           DUMMY_FUNC           /* Interrupt no. 818    -   */
#define GTM_TOM0_CH12_13_ISR_C2           DUMMY_FUNC           /* Interrupt no. 819    -   */
#define GTM_TOM0_CH14_15_ISR_C2           DUMMY_FUNC           /* Interrupt no. 820    -   */
#define GTM_TOM1_CH0_1_ISR_C2             DUMMY_FUNC           /* Interrupt no. 821    -   */
#define GTM_TOM1_CH2_3_ISR_C2             DUMMY_FUNC           /* Interrupt no. 822    -   */
#define GTM_TOM1_CH4_5_ISR_C2             DUMMY_FUNC           /* Interrupt no. 823    -   */
#define GTM_TOM1_CH6_7_ISR_C2             DUMMY_FUNC           /* Interrupt no. 824    -   */
#define GTM_TOM1_CH8_9_ISR_C2             DUMMY_FUNC           /* Interrupt no. 825    -   */
#define GTM_TOM1_CH10_11_ISR_C2           DUMMY_FUNC           /* Interrupt no. 826    -   */
#define GTM_TOM1_CH12_13_ISR_C2           DUMMY_FUNC           /* Interrupt no. 827    -   */
#define GTM_TOM1_CH14_15_ISR_C2           DUMMY_FUNC           /* Interrupt no. 828    -   */
#define GTM_ATOM0_CH0_1_ISR_C2            DUMMY_FUNC           /* Interrupt no. 837    -   */
#define GTM_ATOM0_CH2_3_ISR_C2            DUMMY_FUNC           /* Interrupt no. 838    -   */
#define GTM_ATOM0_CH4_5_ISR_C2            DUMMY_FUNC           /* Interrupt no. 839    -   */
#define GTM_ATOM0_CH6_7_ISR_C2            DUMMY_FUNC           /* Interrupt no. 840    -   */
#define GTM_ATOM1_CH0_1_ISR_C2            DUMMY_FUNC           /* Interrupt no. 841    -   */
#define GTM_ATOM1_CH2_3_ISR_C2            DUMMY_FUNC           /* Interrupt no. 842    -   */
#define GTM_ATOM1_CH4_5_ISR_C2            DUMMY_FUNC           /* Interrupt no. 843    -   */
#define GTM_ATOM1_CH6_7_ISR_C2            DUMMY_FUNC           /* Interrupt no. 844    -   */
#define GTM_ATOM2_CH0_1_ISR_C2            DUMMY_FUNC           /* Interrupt no. 845    -   */
#define GTM_ATOM2_CH2_3_ISR_C2            DUMMY_FUNC           /* Interrupt no. 846    -   */
#define GTM_ATOM2_CH4_5_ISR_C2            DUMMY_FUNC           /* Interrupt no. 847    -   */
#define GTM_ATOM2_CH6_7_ISR_C2            DUMMY_FUNC           /* Interrupt no. 848    -   */
#define GTM_ATOM3_CH0_1_ISR_C2            DUMMY_FUNC           /* Interrupt no. 849    -   */
#define GTM_ATOM3_CH2_3_ISR_C2            DUMMY_FUNC           /* Interrupt no. 850    -   */
#define GTM_ATOM3_CH4_5_ISR_C2            DUMMY_FUNC           /* Interrupt no. 851    -   */
#define GTM_ATOM3_CH6_7_ISR_C2            DUMMY_FUNC           /* Interrupt no. 852    -   */
#define GTM_ERR_ISR_C2                    DUMMY_FUNC           /* Interrupt no. 931    -   */
/************************************************************************************************/
/**************************************** CCCU ISR **********************************************/
/************************************************************************************************/
#define CCCU_CSCE_CWEE_ISR_C2             DUMMY_FUNC           /* Interrupt no. 935    -   */  
/************************************************************************************************/


/* --------------------------------------------------------------------------- */
/* Interrupts level priority definition - C0 and C2 use the same priorities    */
/* --------------------------------------------------------------------------- */
/************************************************************************************************/
/*************************************** OSEK ISR PRIORITIES *************************************/
/************************************************************************************************/
#ifdef _BUILD_TASK_
#define INTC_SSCIR0_LVL                PRI_1           /* Interrupt no. 0     - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_LVL                PRI_2           /* Interrupt no. 1     - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_LVL                PRI_3           /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_LVL                PRI_4           /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_LVL                PRI_5           /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_LVL                PRI_6           /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_LVL                PRI_7           /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_LVL                PRI_8           /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_LVL                PRI_9           /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_LVL                PRI_10           /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_LVL               PRI_11          /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_LVL               PRI_12          /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_LVL               PRI_13          /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_LVL               PRI_14          /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_LVL               PRI_15          /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_LVL               PRI_16          /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_LVL               PRI_17          /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_LVL               PRI_18          /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_LVL               PRI_19          /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_LVL               PRI_20          /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_LVL               PRI_21          /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_LVL               PRI_22          /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_LVL               PRI_23          /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_LVL               PRI_24          /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_LVL               PRI_25          /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_LVL               PRI_26          /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_LVL               PRI_27          /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_LVL               PRI_28          /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_LVL               PRI_29          /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_LVL               PRI_30          /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_LVL               PRI_31          /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_LVL               PRI_32          /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#else
#define INTC_SSCIR0_LVL                PRI_0           /* Interrupt no. 0     - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_LVL                PRI_0           /* Interrupt no. 1     - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_LVL                PRI_0           /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_LVL                PRI_0           /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_LVL                PRI_0           /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_LVL                PRI_0           /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_LVL                PRI_0           /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_LVL                PRI_0           /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_LVL                PRI_0           /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_LVL                PRI_0           /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_LVL               PRI_0           /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_LVL               PRI_0           /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_LVL               PRI_0           /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_LVL               PRI_0           /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_LVL               PRI_0           /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_LVL               PRI_0           /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_LVL               PRI_0           /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_LVL               PRI_0           /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_LVL               PRI_0           /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_LVL               PRI_0           /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_LVL               PRI_0           /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_LVL               PRI_0           /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_LVL               PRI_0           /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_LVL               PRI_0           /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_LVL               PRI_0           /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_LVL               PRI_0           /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_LVL               PRI_0           /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_LVL               PRI_0           /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_LVL               PRI_0           /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_LVL               PRI_0           /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_LVL               PRI_0           /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_LVL               PRI_0           /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#endif /* _BUILD_TASK_ */
/************************************************************************************************/
/*************************************** SWT LVL ***********************************************/
/************************************************************************************************/
#define SWT0_LVL                       PRI_0           /* Interrupt no. 32    -    */
#define SWT2_LVL                       PRI_0           /* Interrupt no. 34    -   */
#define SWT3_LVL                       PRI_0           /* Interrupt no. 35    -   */
/************************************************************************************************/
/*************************************** STM LVL ***********************************************/
/************************************************************************************************/
#ifdef _BUILD_STM_
#define STM_INT_0_CIR0_LVL             PRI_9          /* Interrupt no. 36    -   */
#define STM_INT_0_CIR1_LVL             PRI_0           /* Interrupt no. 37    -   */
#define STM_INT_0_CIR2_LVL             PRI_0           /* Interrupt no. 38    -   */
#define STM_INT_0_CIR3_LVL             PRI_0           /* Interrupt no. 39    -   */
#else
#define STM_INT_0_CIR0_LVL             PRI_0           /* Interrupt no. 36    -   */
#define STM_INT_0_CIR1_LVL             PRI_0           /* Interrupt no. 37    -   */
#define STM_INT_0_CIR2_LVL             PRI_0           /* Interrupt no. 38    -   */
#define STM_INT_0_CIR3_LVL             PRI_0           /* Interrupt no. 39    -   */
#endif
#define STM_INT_2_CIR0_LVL             PRI_0           /* Interrupt no. 44    -   */
#define STM_INT_2_CIR1_LVL             PRI_0           /* Interrupt no. 45    -   */
#define STM_INT_2_CIR2_LVL             PRI_0           /* Interrupt no. 46    -   */
#define STM_INT_2_CIR3_LVL             PRI_0           /* Interrupt no. 47    -   */
/************************************************************************************************/
/*************************************** EDMA LVL ************************************************/
/************************************************************************************************/
#define EDMA_ERL_ERR31_ERR0_LVL        PRI_0           /* Interrupt no. 52   - ivINT_EDMA_ERRL_ERR31_0  */
#define EDMA_IRQRL_INT00_LVL           PRI_10           /* Interrupt no. 53   - ivINT_EDMA_IRQRL_INT0  */
#define EDMA_IRQRL_INT01_LVL           PRI_16           /* Interrupt no. 54   - ivINT_EDMA_IRQRL_INT1  */
#define EDMA_IRQRL_INT02_LVL           PRI_19           /* Interrupt no. 55   - ivINT_EDMA_IRQRL_INT2  */
#define EDMA_IRQRL_INT03_LVL           PRI_19           /* Interrupt no. 56   - ivINT_EDMA_IRQRL_INT3  */
#define EDMA_IRQRL_INT04_LVL           PRI_0           /* Interrupt no. 57   - ivINT_EDMA_IRQRL_INT4  */
#define EDMA_IRQRL_INT05_LVL           PRI_0          /* Interrupt no. 58   - ivINT_EDMA_IRQRL_INT5  */
#define EDMA_IRQRL_INT06_LVL           PRI_0           /* Interrupt no. 59   - ivINT_EDMA_IRQRL_INT6  */
#define EDMA_IRQRL_INT07_LVL           PRI_0          /* Interrupt no. 60   - ivINT_EDMA_IRQRL_INT7  */
#define EDMA_IRQRL_INT08_LVL           PRI_0           /* Interrupt no. 61   - ivINT_EDMA_IRQRL_INT8  */
#define EDMA_IRQRL_INT09_LVL           PRI_0           /* Interrupt no. 62   - ivINT_EDMA_IRQRL_INT9  */
#define EDMA_IRQRL_INT10_LVL           PRI_0           /* Interrupt no. 63   - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_LVL           PRI_0           /* Interrupt no. 64   - ivINT_EDMA_IRQRL_INT11  */
#define EDMA_IRQRL_INT12_LVL           PRI_0           /* Interrupt no. 65   - ivINT_EDMA_IRQRL_INT12  */
#define EDMA_IRQRL_INT13_LVL           PRI_0           /* Interrupt no. 66   - ivINT_EDMA_IRQRL_INT13  */
#define EDMA_IRQRL_INT14_LVL           PRI_0           /* Interrupt no. 67   - ivINT_EDMA_IRQRL_INT14  */
#define EDMA_IRQRL_INT15_LVL           PRI_0           /* Interrupt no. 68   - ivINT_EDMA_IRQRL_INT15  */
#define EDMA_IRQRL_INT16_LVL           PRI_10          /* Interrupt no. 69   - ivINT_EDMA_IRQRL_INT16  */
#define EDMA_IRQRL_INT17_LVL           PRI_16          /* Interrupt no. 70   - ivINT_EDMA_IRQRL_INT17  */
#define EDMA_IRQRL_INT18_LVL           PRI_0           /* Interrupt no. 71   - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_LVL           PRI_0           /* Interrupt no. 72   - ivINT_EDMA_IRQRL_INT19  */
#define EDMA_IRQRL_INT20_LVL           PRI_0           /* Interrupt no. 73   - ivINT_EDMA_IRQRL_INT20  */
#define EDMA_IRQRL_INT21_LVL           PRI_0           /* Interrupt no. 74   - ivINT_EDMA_IRQRL_INT21  */
#define EDMA_IRQRL_INT22_LVL           PRI_0           /* Interrupt no. 75   - ivINT_EDMA_IRQRL_INT22  */
#define EDMA_IRQRL_INT23_LVL           PRI_0           /* Interrupt no. 76   - ivINT_EDMA_IRQRL_INT23  */
#define EDMA_IRQRL_INT24_LVL           PRI_19          /* Interrupt no. 77   - ivINT_EDMA_IRQRL_INT24  */
#define EDMA_IRQRL_INT25_LVL           PRI_19          /* Interrupt no. 78   - ivINT_EDMA_IRQRL_INT25  */
#define EDMA_IRQRL_INT26_LVL           PRI_0           /* Interrupt no. 79   - ivINT_EDMA_IRQRL_INT26  */
#define EDMA_IRQRL_INT27_LVL           PRI_0           /* Interrupt no. 80   - ivINT_EDMA_IRQRL_INT27  */
#define EDMA_IRQRL_INT28_LVL           PRI_0           /* Interrupt no. 81   - ivINT_EDMA_IRQRL_INT28  */
#define EDMA_IRQRL_INT29_LVL           PRI_0           /* Interrupt no. 82   - ivINT_EDMA_IRQRL_INT29  */
#define EDMA_IRQRL_INT30_LVL           PRI_0           /* Interrupt no. 83   - ivINT_EDMA_IRQRL_INT30  */
#define EDMA_IRQRL_INT31_LVL           PRI_0           /* Interrupt no. 84   - ivINT_EDMA_IRQRL_INT31  */
/************************************************************************************************/
/*************************************** FLASH LVL ***********************************************/
/************************************************************************************************/
#define FLASH_SRAM_ECC_INT_LVL         PRI_0           /* Interrupt no. 185  - ivINT_FLASH_ECC        */
/************************************************************************************************/
/*************************************** ETHERNET LVL *******************************************/
/************************************************************************************************/
#define ETH_EIR_TX_LVL                 PRI_0          /* Interrupt no. 218   -   */
#define ETH_EIR_RX_LVL                 PRI_0          /* Interrupt no. 219   -   */
#define ETH_EIR_COMB_LVL               PRI_0          /* Interrupt no. 220   -   */
/************************************************************************************************/
/*************************************** PIT-RTI LVL **********************************************/
/************************************************************************************************/
#ifdef _BUILD_PIT_
#define PIT_INT0_PIT0_LVL              PRI_18         /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_LVL              PRI_10         /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_LVL              PRI_0          /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_LVL              PRI_0          /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_LVL              PRI_0          /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_LVL              PRI_0          /* Interrupt no. 231  -        */
#define PIT_RTI_PIT0_LVL               PRI_0          /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_LVL              PRI_14         /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_LVL              PRI_0          /* Interrupt no. 241  -        */
#else
#define PIT_INT0_PIT0_LVL              PRI_0          /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_LVL              PRI_0          /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_LVL              PRI_0          /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_LVL              PRI_0          /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_LVL              PRI_0          /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_LVL              PRI_0          /* Interrupt no. 231  -        */
#define PIT_RTI_PIT0_LVL               PRI_0          /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_LVL              PRI_0          /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_LVL              PRI_0          /* Interrupt no. 241  -        */
#endif
/************************************************************************************************/
/*************************************** XOSC LVL **********************************************/
/************************************************************************************************/
#define XOSC_CTL_LVL                   PRI_0          /* Interrupt no. 242   - ivFMPLL_SYNSR_LOCF  */
/************************************************************************************************/
/*************************************** SIUL LVL *************************************************/
/************************************************************************************************/
#define SIUL2_COMB_EXT0_LVL            PRI_0          /* Interrupt no. 243   - ivINT_SIU_OSR_OVF15_0  */
#define SIUL2_COMB_EXT1_LVL            PRI_0          /* Interrupt no. 244   - ivINT_SIU_ELVL_EIF0  */
/************************************************************************************************/
/*************************************** MC LVL *************************************************/
/************************************************************************************************/
#define MC_ME_SAFE_LVL                 PRI_0          /* Interrupt no. 251   -   */
#define MC_ME_MTC_LVL                  PRI_0          /* Interrupt no. 252   -   */
#define MC_ME_IMODE_LVL                PRI_0          /* Interrupt no. 253   -   */
#define MC_ME_ICONF_LVL                PRI_0          /* Interrupt no. 254   -   */
#define MC_RGM_LVL                     PRI_0          /* Interrupt no. 255   -   */
/************************************************************************************************/
/*************************************** DSPI LVL ***********************************************/
/************************************************************************************************/
#define DSPI_0_ISR_TFUF_RFOF_LVL       PRI_0       /* Interrupt no. 259  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_0_ISR_EOQF_LVL            PRI_0       /* Interrupt no. 260  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_0_ISR_TFFF_LVL            PRI_0       /* Interrupt no. 261  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_0_ISR_TCF_LVL             PRI_0       /* Interrupt no. 262  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_0_ISR_RFDF_LVL            PRI_0       /* Interrupt no. 263  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDTCF_SPITCF_LVL   PRI_0       /* Interrupt no. 264  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDFFF_LVL          PRI_0       /* Interrupt no. 265  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_SPEF_LVL            PRI_0       /* Interrupt no. 266  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_TFUF_RFOF_LVL       PRI_0       /* Interrupt no. 268  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_1_ISR_EOQF_LVL            PRI_0       /* Interrupt no. 269  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_1_ISR_TFFF_LVL            PRI_0       /* Interrupt no. 270  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_1_ISR_TCF_LVL             PRI_0       /* Interrupt no. 271  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_1_ISR_RFDF_LVL            PRI_0       /* Interrupt no. 272  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDTCF_SPITCF_LVL   PRI_0       /* Interrupt no. 273  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDFFF_LVL          PRI_0       /* Interrupt no. 274  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_SPEF_LVL            PRI_0       /* Interrupt no. 275  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_TFUF_RFOF_LVL       PRI_0       /* Interrupt no. 277  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_2_ISR_EOQF_LVL            PRI_0       /* Interrupt no. 278  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_2_ISR_TFFF_LVL            PRI_0       /* Interrupt no. 279  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_2_ISR_TCF_LVL             PRI_0       /* Interrupt no. 280  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_2_ISR_RFDF_LVL            PRI_0       /* Interrupt no. 281  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDTCF_SPITCF_LVL   PRI_0       /* Interrupt no. 282  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDFFF_LVL          PRI_0       /* Interrupt no. 283  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_SPEF_LVL            PRI_0       /* Interrupt no. 284  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_TFUF_RFOF_LVL       PRI_0       /* Interrupt no. 295  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_4_ISR_EOQF_LVL            PRI_0       /* Interrupt no. 296  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_4_ISR_TFFF_LVL            PRI_0       /* Interrupt no. 297  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_4_ISR_TCF_LVL             PRI_0       /* Interrupt no. 298  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_4_ISR_RFDF_LVL            PRI_0       /* Interrupt no. 299  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDTCF_SPITCF_LVL   PRI_0       /* Interrupt no. 300  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDFFF_DSITCF_LVL   PRI_0       /* Interrupt no. 301  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_SPEF_DPEF_LVL       PRI_0       /* Interrupt no. 302  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_DDIF_LVL            PRI_0       /* Interrupt no. 303  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_TFUF_RFOF_LVL       PRI_0       /* Interrupt no. 304  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_5_ISR_EOQF_LVL            PRI_0       /* Interrupt no. 305  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_5_ISR_TFFF_LVL            PRI_0       /* Interrupt no. 306  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_5_ISR_TCF_LVL             PRI_0       /* Interrupt no. 307  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_5_ISR_RFDF_LVL            PRI_0       /* Interrupt no. 308  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDTCF_SPITCF_LVL   PRI_0       /* Interrupt no. 309  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDFFF_DSITCF_LVL   PRI_0       /* Interrupt no. 310  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_SPEF_DPEF_LVL       PRI_0       /* Interrupt no. 311  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_DDIF_LVL            PRI_0       /* Interrupt no. 312  - ivINT_DSPI_B_ISR_RFDF  */
/************************************************************************************************/
/*************************************** LINFLEX LVL ********************************************/
/************************************************************************************************/
#define LINFLEX_0_RX_COMB_LVL          PRI_0       /* Interrupt no. 376    -   */
#define LINFLEX_0_TX_COMB_LVL          PRI_0       /* Interrupt no. 377    -   */
#define LINFLEX_0_ERROR_COMB_LVL       PRI_0       /* Interrupt no. 378    -   */
#define LINFLEX_1_RX_COMB_LVL          PRI_0       /* Interrupt no. 380    -   */
#define LINFLEX_1_TX_COMB_LVL          PRI_0       /* Interrupt no. 381    -   */
#define LINFLEX_1_ERROR_COMB_LVL       PRI_0       /* Interrupt no. 382    -   */
#define LINFLEX_2_RX_COMB_LVL          PRI_0       /* Interrupt no. 384    -   */
#define LINFLEX_2_TX_COMB_LVL          PRI_0       /* Interrupt no. 385    -   */
#define LINFLEX_2_ERROR_COMB_LVL       PRI_0       /* Interrupt no. 386    -   */
#define LINFLEX_14_RX_COMB_LVL         PRI_0       /* Interrupt no. 432    -   */
#define LINFLEX_14_TX_COMB_LVL         PRI_0       /* Interrupt no. 433    -   */
#define LINFLEX_14_ERROR_COMB_LVL      PRI_0       /* Interrupt no. 434    -   */
#define LINFLEX_15_RX_COMB_LVL         PRI_0       /* Interrupt no. 436    -   */
#define LINFLEX_15_TX_COMB_LVL         PRI_0       /* Interrupt no. 437    -   */
#define LINFLEX_15_ERROR_COMB_LVL      PRI_0       /* Interrupt no. 438    -   */
/************************************************************************************************/
/**************************************** I2C LVL ***********************************************/
/************************************************************************************************/
#define I2C_IBIF_IAAS_IBAL_LVL         PRI_0       /* Interrupt no. 440    -   */
/************************************************************************************************/
/*************************************** FLEXRAY LVL ********************************************/
/************************************************************************************************/
#define FLEXRAY_LRNE_LVL               PRI_0       /* Interrupt no. 453 - ivINT_FlexRAY LRNE   */
#define FLEXRAY_LRCE_LVL               PRI_0       /* Interrupt no. 454 - ivINT_FlexRAY LRCE   */
#define FLEXRAY_FAFAIF_LVL             PRI_0       /* Interrupt no. 455 - ivINT_FlexRAY FAFAIF */
#define FLEXRAY_FAFBIF_LVL             PRI_0       /* Interrupt no. 456 - ivINT_FlexRAY FAFBIF */
#define FLEXRAY_WUPIF_LVL              PRI_0       /* Interrupt no. 457 - ivINT_FlexRAY WUPIF  */
#define FLEXRAY_PRIF_LVL               PRI_0       /* Interrupt no. 458 - ivINT_FlexRAY PRIF   */
#define FLEXRAY_CHIF_LVL               PRI_0       /* Interrupt no. 459 - ivINT_FlexRAY CHIF   */
#define FLEXRAY_TBIF_LVL               PRI_0       /* Interrupt no. 460 - ivINT_FlexRAY TBIF   */
#define FLEXRAY_RBIF_LVL               PRI_0       /* Interrupt no. 461 - ivINT_FlexRAY RBIF   */
#define FLEXRAY_MIF_LVL                PRI_0       /* Interrupt no. 462 - ivINT_FlexRAY MIF    */
/************************************************************************************************/
/*************************************** GR LVL ** **********************************************/
/************************************************************************************************/
#define GR_VD_LVL                      PRI_0       /* Interrupt no. 477   - ivGR_VD  */
/************************************************************************************************/
/*************************************** EPR LVL ************************************************/
/************************************************************************************************/
#define EPR_TEMP_LVL                   PRI_0       /* Interrupt no. 478   - ivEPR_TEMP  */
/************************************************************************************************/
/*************************************** FCCU LVL ***********************************************/
/************************************************************************************************/
#define FCCU_ALRM_STAT_LVL             PRI_0       /* Interrupt no. 488   - ivFMPLL_SYNSR_LOCF  */
#define FCCU_CFG_TO_STAT_LVL           PRI_0       /* Interrupt no. 489   - ivFMPLL_SYNSR_LOLF  */
/************************************************************************************************/
/*************************************** STCU LVL ***********************************************/
/************************************************************************************************/
#define STCU_LBIE_LVL                  PRI_0       /* Interrupt no. 494   - ivSTCU_LBIE  */
#define STCU_MBIE_LVL                  PRI_0       /* Interrupt no. 495   - ivSTCU_MBIE  */
/************************************************************************************************/
/*************************************** SAR LVL ************************************************/
/************************************************************************************************/
#define SAR_0_INT_LVL                  PRI_0       /* Interrupt no. 528    -   */
#define SAR_2_INT_LVL                  PRI_0       /* Interrupt no. 530    -   */
#define SAR_4_INT_LVL                  PRI_0       /* Interrupt no. 532    -   */
#define SAR_6_INT_LVL                  PRI_0       /* Interrupt no. 534    -   */
#define SAR_B_INT_LVL                  PRI_0       /* Interrupt no. 543    -   */
/************************************************************************************************/
/*************************************** SD LVL *************************************************/
/************************************************************************************************/
#define SD_0_INT_LVL                   PRI_0       /* Interrupt no. 544  - ivSD_0_INT  */
#define SD_3_INT_LVL                   PRI_0       /* Interrupt no. 547  - ivSD_3_INT  */
/************************************************************************************************/
/**************************************** SENT LVL **********************************************/
/************************************************************************************************/
#define SENT_0_FAST_COMB_LVL           PRI_0       /* Interrupt no. 558    - ivSENT_0_FAST_COMB  */
#define SENT_0_SLOW_COMB_LVL           PRI_0       /* Interrupt no. 559    -   */
#define SENT_0_GBL_ERROR_LVL           PRI_0       /* Interrupt no. 560    -   */
#define SENT_1_SLOW_RDY_LVL            PRI_0       /* Interrupt no. 562    -   */
#define SENT_1_FAST_RDY_LVL            PRI_0       /* Interrupt no. 561    -   */
#define SENT_1_GBL_ERROR_LVL           PRI_0       /* Interrupt no. 563    -   */
#define SENT_0_FMSG_0_LVL              PRI_0       /* Interrupt no. 564    -   */
#define SENT_0_SMSG_0_LVL              PRI_0       /* Interrupt no. 565    -   */
#define SENT_0_ERROR_0_LVL             PRI_0       /* Interrupt no. 566    -   */
#define SENT_0_FMSG_1_LVL              PRI_0       /* Interrupt no. 567    -   */
#define SENT_0_SMSG_1_LVL              PRI_0       /* Interrupt no. 568    -   */
#define SENT_0_ERROR_1_LVL             PRI_0       /* Interrupt no. 569    -   */
#define SENT_0_FMSG_2_LVL              PRI_0       /* Interrupt no. 570    -   */
#define SENT_0_SMSG_2_LVL              PRI_0       /* Interrupt no. 571    -   */
#define SENT_0_ERROR_2_LVL             PRI_0       /* Interrupt no. 572    -   */
#define SENT_0_FMSG_3_LVL              PRI_0       /* Interrupt no. 573    -   */
#define SENT_0_SMSG_3_LVL              PRI_0       /* Interrupt no. 574    -   */
#define SENT_0_ERROR_3_LVL             PRI_0       /* Interrupt no. 575    -   */
#define SENT_1_FMSG_0_LVL              PRI_0       /* Interrupt no. 579    -   */
#define SENT_1_SMSG_0_LVL              PRI_0       /* Interrupt no. 580    -   */
#define SENT_1_ERROR_0_LVL             PRI_0       /* Interrupt no. 581    -   */
#define SENT_1_FMSG_1_LVL              PRI_0       /* Interrupt no. 582    -   */
#define SENT_1_SMSG_1_LVL              PRI_0       /* Interrupt no. 583    -   */
#define SENT_1_ERROR_1_LVL             PRI_0       /* Interrupt no. 584    -   */
/************************************************************************************************/
/*************************************** PSI LVL ************************************************/
/************************************************************************************************/
#define PSI5_0_DMA_0_LVL               PRI_0       /* Interrupt no. 612    -   */
#define PSI5_0_GEN_0_LVL               PRI_0       /* Interrupt no. 613    -   */
#define PSI5_0_NEW_MSG_0_LVL           PRI_0       /* Interrupt no. 614    -   */
#define PSI5_0_MSG_OW_0_LVL            PRI_0       /* Interrupt no. 615    -   */
#define PSI5_0_ERROR_COMB_0_LVL        PRI_0       /* Interrupt no. 616    -   */
#define PSI5_0_GLOBAL_0_LVL            PRI_0       /* Interrupt no. 617    -   */
#define PSI5_1_DMA_0_LVL               PRI_0       /* Interrupt no. 624    -   */
#define PSI5_1_GEN_0_LVL               PRI_0       /* Interrupt no. 625    -   */
#define PSI5_1_NEW_MSG_0_LVL           PRI_0       /* Interrupt no. 626    -   */
#define PSI5_1_MSG_OW_0_LVL            PRI_0       /* Interrupt no. 627    -   */
#define PSI5_1_ERROR_COMB_0_LVL        PRI_0       /* Interrupt no. 628    -   */
#define PSI5_1_GLOBAL_0_LVL            PRI_0       /* Interrupt no. 629    -   */
/************************************************************************************************/
/*************************************** SIPI LVL ***********************************************/
/************************************************************************************************/
#define SIPI_ERROR_COMB_LVL            PRI_0       /* Interrupt no. 654    -   */
#define SIPI_CRC_ERROR_LVL             PRI_0       /* Interrupt no. 655    -   */
#define SIPI_CH0_RX_LVL                PRI_0       /* Interrupt no. 656    -   */
#define SIPI_CH1_RX_LVL                PRI_0       /* Interrupt no. 657    -   */
#define SIPI_CH2_RX_LVL                PRI_0       /* Interrupt no. 658    -   */
#define SIPI_CH3_RX_LVL                PRI_0       /* Interrupt no. 659    -   */
#define SIPI_EVENT_COMB_LVL            PRI_0       /* Interrupt no. 660    -   */
/************************************************************************************************/
/*************************************** LFAST LVL **********************************************/
/************************************************************************************************/
#define LFAST_0_TX_LVL                 PRI_0       /* Interrupt no. 661    -   */
#define LFAST_0_TX_ERROR_LVL           PRI_0       /* Interrupt no. 662    -   */
#define LFAST_0_RX_LVL                 PRI_0       /* Interrupt no. 663    -   */
#define LFAST_0_RX_ERROR_LVL           PRI_0       /* Interrupt no. 664    -   */
#define LFAST_0_ICLC_RX_LVL            PRI_0       /* Interrupt no. 665    -   */
/************************************************************************************************/
/*************************************** JTAG LVL ***********************************************/
/************************************************************************************************/
#define JTAG_GM_LVL                    PRI_0       /* Interrupt no. 674    -   */
#define JTAG_DC_LVL                    PRI_0       /* Interrupt no. 675    -   */
/************************************************************************************************/
/*************************************** M_TTCAN LVL ********************************************/
/************************************************************************************************/
#ifdef _BUILD_CAN_
#define M_TTCAN_LINE0_LVL              PRI_10       /* Interrupt no. 677    -   */
#define M_TTCAN_LINE1_LVL              PRI_10       /* Interrupt no. 678    -   */
#define M_TTCAN_RTMI_LVL               PRI_0       /* Interrupt no. 679    -   */
#else
#define M_TTCAN_LINE0_LVL              PRI_0       /* Interrupt no. 677    -   */
#define M_TTCAN_LINE1_LVL              PRI_0       /* Interrupt no. 678    -   */
#define M_TTCAN_RTMI_LVL               PRI_0       /* Interrupt no. 679    -   */
#endif
/************************************************************************************************/
/**************************************** M_CAN LVL *********************************************/
/************************************************************************************************/
#ifdef _BUILD_CAN_
#define MCAN1_LINE0_LVL                PRI_10        /* Interrupt no. 688    -   */
#define MCAN1_LINE1_LVL                PRI_10        /* Interrupt no. 689    -   */
#define MCAN2_LINE0_LVL                PRI_15        /* Interrupt no. 690    -   */
#define MCAN2_LINE1_LVL                PRI_15        /* Interrupt no. 691    -   */
#else
#define MCAN1_LINE0_LVL                PRI_0        /* Interrupt no. 688    -   */
#define MCAN1_LINE1_LVL                PRI_0        /* Interrupt no. 689    -   */
#define MCAN2_LINE0_LVL                PRI_0        /* Interrupt no. 690    -   */
#define MCAN2_LINE1_LVL                PRI_0        /* Interrupt no. 691    -   */
#endif
/************************************************************************************************/
/*************************************** GTM LVL **********************************************/
/************************************************************************************************/
#define GTM_AEI_LVL                    PRI_0        /* Interrupt no. 706    -   */
#define GTM_ARU_NEW_DATA0_LVL          PRI_0        /* Interrupt no. 707    -   */
#define GTM_ARU_NEW_DATA1_LVL          PRI_0        /* Interrupt no. 708    -   */
#define GTM_ARU_ACC_ACK_LVL            PRI_0        /* Interrupt no. 709    -   */
#define GTM_BRC_LVL                    PRI_0        /* Interrupt no. 710    -   */
#define GTM_CMP_LVL                    PRI_0        /* Interrupt no. 711    -   */
#define GTM_SPE0_LVL                   PRI_0        /* Interrupt no. 712    -   */
#define GTM_SPE1_LVL                   PRI_0        /* Interrupt no. 713    -   */
#define GTM_PSM0_CH0_LVL               PRI_0        /* Interrupt no. 714    -   */
#define GTM_PSM0_CH1_LVL               PRI_0        /* Interrupt no. 715    -   */
#define GTM_PSM0_CH2_LVL               PRI_0        /* Interrupt no. 716    -   */
#define GTM_PSM0_CH3_LVL               PRI_0        /* Interrupt no. 717    -   */
#define GTM_PSM0_CH4_LVL               PRI_0        /* Interrupt no. 718    -   */
#define GTM_PSM0_CH5_LVL               PRI_0        /* Interrupt no. 719    -   */
#define GTM_PSM0_CH6_LVL               PRI_0        /* Interrupt no. 720    -   */
#define GTM_PSM0_CH7_LVL               PRI_0        /* Interrupt no. 721    -   */
#define GTM_DPLL_DCGI_LVL              PRI_0        /* Interrupt no. 722    -   */
#define GTM_DPLL_EDI_LVL               PRI_0        /* Interrupt no. 723    -   */
#define GTM_DPLL_TINI_LVL              PRI_0        /* Interrupt no. 724    -   */
#define GTM_DPLL_TAXI_LVL              PRI_0        /* Interrupt no. 725    -   */
#define GTM_DPLL_SISI_LVL              PRI_0        /* Interrupt no. 726    -   */
#define GTM_DPLL_TISI_LVL              PRI_0        /* Interrupt no. 727    -   */
#define GTM_DPLL_MSI_LVL               PRI_0        /* Interrupt no. 728    -   */
#define GTM_DPLL_MTI_LVL               PRI_0        /* Interrupt no. 729    -   */
#define GTM_DPLL_SASI_LVL              PRI_0        /* Interrupt no. 730    -   */
#define GTM_DPLL_TASI_LVL              PRI_0        /* Interrupt no. 731    -   */
#define GTM_DPLL_PWI_LVL               PRI_0        /* Interrupt no. 732    -   */
#define GTM_DPLL_W2I_LVL               PRI_0        /* Interrupt no. 733    -   */
#define GTM_DPLL_W1I_LVL               PRI_0        /* Interrupt no. 734    -   */
#define GTM_DPLL_GL1I_LVL              PRI_16        /* Interrupt no. 735    -   */
#define GTM_DPLL_LL1I_LVL              PRI_16        /* Interrupt no. 736    -   */
#define GTM_DPLL_EI_LVL                PRI_0        /* Interrupt no. 737    -   */
#define GTM_DPLL_GL2I_LVL              PRI_0        /* Interrupt no. 738    -   */
#define GTM_DPLL_LL2I_LVL              PRI_0        /* Interrupt no. 739    -   */
#define GTM_DPLL_TE0I_LVL              PRI_0        /* Interrupt no. 740    -   */
#define GTM_DPLL_TE1I_LVL              PRI_0        /* Interrupt no. 741    -   */
#define GTM_DPLL_TE2I_LVL              PRI_0        /* Interrupt no. 742    -   */
#define GTM_DPLL_TE3I_LVL              PRI_0        /* Interrupt no. 743    -   */
#define GTM_DPLL_TE4I_LVL              PRI_0        /* Interrupt no. 744    -   */
#define GTM_DPLL_CDTI_LVL              PRI_0        /* Interrupt no. 745    -   */
#define GTM_DPLL_CDSI_LVL              PRI_0        /* Interrupt no. 746    -   */
#define GTM_DPLL_TORI_LVL              PRI_0        /* Interrupt no. 747    -   */
#define GTM_DPLL_SORI_LVL              PRI_0        /* Interrupt no. 748    -   */
#define GTM_TIM0_CH0_LVL               PRI_16       /* Interrupt no. 749    -   */
#define GTM_TIM0_CH1_LVL               PRI_16       /* Interrupt no. 750    -   */
#define GTM_TIM0_CH2_LVL               PRI_16       /* Interrupt no. 751    -   */
#define GTM_TIM0_CH3_LVL               PRI_0        /* Interrupt no. 752    -   */
#define GTM_TIM0_CH4_LVL               PRI_0        /* Interrupt no. 753    -   */
#define GTM_TIM0_CH5_LVL               PRI_0        /* Interrupt no. 754    -   */
#define GTM_TIM0_CH6_LVL               PRI_0        /* Interrupt no. 755    -   */
#define GTM_TIM0_CH7_LVL               PRI_0        /* Interrupt no. 756    -   */
#define GTM_TIM1_CH0_LVL               PRI_16       /* Interrupt no. 757    -   */
#define GTM_TIM1_CH1_LVL               PRI_16       /* Interrupt no. 758    -   */
#define GTM_TIM1_CH2_LVL               PRI_16       /* Interrupt no. 759    -   */
#define GTM_TIM1_CH3_LVL               PRI_16       /* Interrupt no. 760    -   */
#define GTM_TIM1_CH4_LVL               PRI_16       /* Interrupt no. 761    -   */
#define GTM_TIM1_CH5_LVL               PRI_16       /* Interrupt no. 762    -   */
#define GTM_TIM1_CH6_LVL               PRI_16       /* Interrupt no. 763    -   */
#define GTM_TIM1_CH7_LVL               PRI_16       /* Interrupt no. 764    -   */
#define GTM_TIM2_CH0_LVL               PRI_16       /* Interrupt no. 765    -   */
#define GTM_TIM2_CH1_LVL               PRI_16       /* Interrupt no. 766    -   */
#define GTM_TIM2_CH2_LVL               PRI_16       /* Interrupt no. 767    -   */
#define GTM_TIM2_CH3_LVL               PRI_16       /* Interrupt no. 768    -   */
#define GTM_TIM2_CH4_LVL               PRI_16        /* Interrupt no. 769    -   */
#define GTM_TIM2_CH5_LVL               PRI_16       /* Interrupt no. 770    -   */
#define GTM_TIM2_CH6_LVL               PRI_16       /* Interrupt no. 771    -   */
#define GTM_TIM2_CH7_LVL               PRI_16       /* Interrupt no. 772    -   */
#define GTM_MCS0_CH0_LVL               PRI_16       /* Interrupt no. 781    -   */
#define GTM_MCS0_CH1_LVL               PRI_16       /* Interrupt no. 782    -   */
#define GTM_MCS0_CH2_LVL               PRI_16       /* Interrupt no. 783    -   */
#define GTM_MCS0_CH3_LVL               PRI_16       /* Interrupt no. 784    -   */
#define GTM_MCS0_CH4_LVL               PRI_0        /* Interrupt no. 785    -   */
#define GTM_MCS0_CH5_LVL               PRI_16       /* Interrupt no. 786    -   */
#define GTM_MCS0_CH6_LVL               PRI_16       /* Interrupt no. 787    -   */
#define GTM_MCS0_CH7_LVL               PRI_16       /* Interrupt no. 788    -   */
#define GTM_MCS1_CH0_LVL               PRI_16       /* Interrupt no. 789    -   */
#define GTM_MCS1_CH1_LVL               PRI_16       /* Interrupt no. 790    -   */
#define GTM_MCS1_CH2_LVL               PRI_16       /* Interrupt no. 791    -   */
#define GTM_MCS1_CH3_LVL               PRI_16       /* Interrupt no. 792    -   */
#define GTM_MCS1_CH4_LVL               PRI_0        /* Interrupt no. 793    -   */
#define GTM_MCS1_CH5_LVL               PRI_16       /* Interrupt no. 794    -   */
#define GTM_MCS1_CH6_LVL               PRI_16       /* Interrupt no. 795    -   */
#define GTM_MCS1_CH7_LVL               PRI_16       /* Interrupt no. 796    -   */
#define GTM_MCS2_CH0_LVL               PRI_16        /* Interrupt no. 797    -   */
#define GTM_MCS2_CH1_LVL               PRI_16        /* Interrupt no. 798    -   */
#define GTM_MCS2_CH2_LVL               PRI_16        /* Interrupt no. 799    -   */
#define GTM_MCS2_CH3_LVL               PRI_16        /* Interrupt no. 800    -   */
#define GTM_MCS2_CH4_LVL               PRI_16        /* Interrupt no. 801    -   */
#define GTM_MCS2_CH5_LVL               PRI_16        /* Interrupt no. 802    -   */
#define GTM_MCS2_CH6_LVL               PRI_0        /* Interrupt no. 803    -   */
#define GTM_MCS2_CH7_LVL               PRI_0        /* Interrupt no. 804    -   */
#define GTM_TOM0_CH0_1_LVL             PRI_0        /* Interrupt no. 813    -   */
#define GTM_TOM0_CH2_3_LVL             PRI_0        /* Interrupt no. 814    -   */
#define GTM_TOM0_CH4_5_LVL             PRI_0        /* Interrupt no. 815    -   */
#define GTM_TOM0_CH6_7_LVL             PRI_0        /* Interrupt no. 816    -   */
#define GTM_TOM0_CH8_9_LVL             PRI_0        /* Interrupt no. 817    -   */
#define GTM_TOM0_CH10_11_LVL           PRI_0        /* Interrupt no. 818    -   */
#define GTM_TOM0_CH12_13_LVL           PRI_0        /* Interrupt no. 819    -   */
#define GTM_TOM0_CH14_15_LVL           PRI_0        /* Interrupt no. 820    -   */
#define GTM_TOM1_CH0_1_LVL             PRI_0        /* Interrupt no. 821    -   */
#define GTM_TOM1_CH2_3_LVL             PRI_0        /* Interrupt no. 822    -   */
#define GTM_TOM1_CH4_5_LVL             PRI_0        /* Interrupt no. 823    -   */
#define GTM_TOM1_CH6_7_LVL             PRI_0        /* Interrupt no. 824    -   */
#define GTM_TOM1_CH8_9_LVL             PRI_0        /* Interrupt no. 825    -   */
#define GTM_TOM1_CH10_11_LVL           PRI_0        /* Interrupt no. 826    -   */
#define GTM_TOM1_CH12_13_LVL           PRI_0        /* Interrupt no. 827    -   */
#define GTM_TOM1_CH14_15_LVL           PRI_0        /* Interrupt no. 828    -   */
#define GTM_ATOM0_CH0_1_LVL            PRI_16       /* Interrupt no. 837    -   */
#define GTM_ATOM0_CH2_3_LVL            PRI_0        /* Interrupt no. 838    -   */
#define GTM_ATOM0_CH4_5_LVL            PRI_0        /* Interrupt no. 839    -   */
#define GTM_ATOM0_CH6_7_LVL            PRI_0        /* Interrupt no. 840    -   */
#define GTM_ATOM1_CH0_1_LVL            PRI_0        /* Interrupt no. 841    -   */
#define GTM_ATOM1_CH2_3_LVL            PRI_0        /* Interrupt no. 842    -   */
#define GTM_ATOM1_CH4_5_LVL            PRI_0        /* Interrupt no. 843    -   */
#define GTM_ATOM1_CH6_7_LVL            PRI_0        /* Interrupt no. 844    -   */
#define GTM_ATOM2_CH0_1_LVL            PRI_0        /* Interrupt no. 845    -   */
#define GTM_ATOM2_CH2_3_LVL            PRI_0        /* Interrupt no. 846    -   */
#define GTM_ATOM2_CH4_5_LVL            PRI_0        /* Interrupt no. 847    -   */
#define GTM_ATOM2_CH6_7_LVL            PRI_0        /* Interrupt no. 848    -   */
#define GTM_ATOM3_CH0_1_LVL            PRI_0        /* Interrupt no. 849    -   */
#define GTM_ATOM3_CH2_3_LVL            PRI_0        /* Interrupt no. 850    -   */
#define GTM_ATOM3_CH4_5_LVL            PRI_0        /* Interrupt no. 851    -   */
#define GTM_ATOM3_CH6_7_LVL            PRI_0        /* Interrupt no. 852    -   */
#define GTM_ERR_LVL                    PRI_0        /* Interrupt no. 931    -   */
/************************************************************************************************/
/**************************************** CCCU LVL **********************************************/
/************************************************************************************************/
#define CCCU_CSCE_CWEE_LVL             PRI_32       /* Interrupt no. 935    -   */  
/************************************************************************************************/


/* ------------------------------------------------------------------ */
/* Interrupts processor selection definition                          */
/* ------------------------------------------------------------------ */
/************************************************************************************************/
/*************************************** OSEK ISR PROCESSOR *************************************/
/************************************************************************************************/
#ifdef _BUILD_TASK_
#define INTC_SSCIR0_PRC                INTC_PSR_CORE0            /* Interrupt no. 0  - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_PRC                INTC_PSR_CORE0            /* Interrupt no. 1  - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_PRC                INTC_PSR_CORE0            /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_PRC                INTC_PSR_CORE0            /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_PRC                INTC_PSR_CORE0            /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_PRC                INTC_PSR_CORE0            /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_PRC                INTC_PSR_CORE0            /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_PRC                INTC_PSR_CORE0            /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_PRC                INTC_PSR_CORE2            /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_PRC                INTC_PSR_NOSENT            /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_PRC               INTC_PSR_NOSENT            /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_PRC               INTC_PSR_NOSENT            /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_PRC               INTC_PSR_NOSENT            /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_PRC               INTC_PSR_NOSENT            /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_PRC               INTC_PSR_NOSENT            /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_PRC               INTC_PSR_NOSENT            /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_PRC               INTC_PSR_NOSENT            /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_PRC               INTC_PSR_NOSENT            /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_PRC               INTC_PSR_NOSENT            /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_PRC               INTC_PSR_NOSENT            /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_PRC               INTC_PSR_NOSENT            /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_PRC               INTC_PSR_NOSENT            /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_PRC               INTC_PSR_NOSENT            /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_PRC               INTC_PSR_NOSENT            /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_PRC               INTC_PSR_NOSENT            /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_PRC               INTC_PSR_NOSENT            /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_PRC               INTC_PSR_NOSENT            /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_PRC               INTC_PSR_NOSENT            /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_PRC               INTC_PSR_NOSENT            /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_PRC               INTC_PSR_NOSENT            /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_PRC               INTC_PSR_CORE0             /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_PRC               INTC_PSR_CORE2             /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#else
#define INTC_SSCIR0_PRC                INTC_PSR_NOSENT            /* Interrupt no. 0     - ivINT_SSCIR0_CLR0  */
#define INTC_SSCIR1_PRC                INTC_PSR_NOSENT            /* Interrupt no. 1     - ivINT_SSCIR1_CLR1  */
#define INTC_SSCIR2_PRC                INTC_PSR_NOSENT            /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_PRC                INTC_PSR_NOSENT            /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_PRC                INTC_PSR_NOSENT            /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_PRC                INTC_PSR_NOSENT            /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_PRC                INTC_PSR_NOSENT            /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_PRC                INTC_PSR_NOSENT            /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_PRC                INTC_PSR_NOSENT            /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_PRC                INTC_PSR_NOSENT            /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_PRC               INTC_PSR_NOSENT            /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_PRC               INTC_PSR_NOSENT            /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_PRC               INTC_PSR_NOSENT            /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_PRC               INTC_PSR_NOSENT            /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_PRC               INTC_PSR_NOSENT            /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_PRC               INTC_PSR_NOSENT            /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_PRC               INTC_PSR_NOSENT            /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_PRC               INTC_PSR_NOSENT            /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_PRC               INTC_PSR_NOSENT            /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_PRC               INTC_PSR_NOSENT            /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_PRC               INTC_PSR_NOSENT            /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_PRC               INTC_PSR_NOSENT            /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_PRC               INTC_PSR_NOSENT            /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_PRC               INTC_PSR_NOSENT            /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_PRC               INTC_PSR_NOSENT            /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_PRC               INTC_PSR_NOSENT            /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_PRC               INTC_PSR_NOSENT            /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_PRC               INTC_PSR_NOSENT            /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_PRC               INTC_PSR_NOSENT            /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_PRC               INTC_PSR_NOSENT            /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_PRC               INTC_PSR_NOSENT             /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_PRC               INTC_PSR_NOSENT             /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
#endif /* _BUILD_TASK_ */
/************************************************************************************************/
/*********************************** RESERVED PROCESSOR *****************************************/
/************************************************************************************************/
#define RESERVED_PRC                   INTC_PSR_NOSENT           /* Interrupt - RESERVED  */
/************************************************************************************************/
/************************************* SWT PROCESSOR ********************************************/
/************************************************************************************************/
#define SWT0_PRC                       INTC_PSR_NOSENT           /* Interrupt no. 32    -    */
#define SWT2_PRC                       INTC_PSR_NOSENT           /* Interrupt no. 34    -   */
#define SWT3_PRC                       INTC_PSR_NOSENT           /* Interrupt no. 35    -   */
/************************************************************************************************/
/************************************ STM PROCESSOR *********************************************/
/************************************************************************************************/
#ifdef _BUILD_STM_
#define STM_INT_0_CIR0_PRC             INTC_PSR_CORE0           /* Interrupt no. 36    -   */
#define STM_INT_0_CIR1_PRC             INTC_PSR_CORE0           /* Interrupt no. 37    -   */
#define STM_INT_0_CIR2_PRC             INTC_PSR_CORE0           /* Interrupt no. 38    -   */
#define STM_INT_0_CIR3_PRC             INTC_PSR_CORE0           /* Interrupt no. 39    -   */
#else
#define STM_INT_0_CIR0_PRC             INTC_PSR_NOSENT           /* Interrupt no. 36    -   */
#define STM_INT_0_CIR1_PRC             INTC_PSR_NOSENT           /* Interrupt no. 37    -   */
#define STM_INT_0_CIR2_PRC             INTC_PSR_NOSENT           /* Interrupt no. 38    -   */
#define STM_INT_0_CIR3_PRC             INTC_PSR_NOSENT           /* Interrupt no. 39    -   */
#endif
#define STM_INT_2_CIR0_PRC             INTC_PSR_NOSENT           /* Interrupt no. 44    -   */
#define STM_INT_2_CIR1_PRC             INTC_PSR_NOSENT           /* Interrupt no. 45    -   */
#define STM_INT_2_CIR2_PRC             INTC_PSR_NOSENT           /* Interrupt no. 46    -   */
#define STM_INT_2_CIR3_PRC             INTC_PSR_NOSENT           /* Interrupt no. 47    -   */
/************************************************************************************************/
/*********************************** EDMA PROCESSOR *********************************************/
/************************************************************************************************/
#define EDMA_ERL_ERR31_ERR0_PRC        INTC_PSR_NOSENT           /* Interrupt no. 52   - ivINT_EDMA_ERRL_ERR31_0  */
#define EDMA_IRQRL_INT00_PRC           INTC_PSR_CORE2            /* Interrupt no. 53   - ivINT_EDMA_IRQRL_INT0  */
#define EDMA_IRQRL_INT01_PRC           INTC_PSR_CORE2           /* Interrupt no. 54   - ivINT_EDMA_IRQRL_INT1  */
#define EDMA_IRQRL_INT02_PRC           INTC_PSR_CORE0           /* Interrupt no. 55   - ivINT_EDMA_IRQRL_INT2  */
#define EDMA_IRQRL_INT03_PRC           INTC_PSR_CORE0           /* Interrupt no. 56   - ivINT_EDMA_IRQRL_INT3  */
#define EDMA_IRQRL_INT04_PRC           INTC_PSR_NOSENT           /* Interrupt no. 57   - ivINT_EDMA_IRQRL_INT4  */
#define EDMA_IRQRL_INT05_PRC           INTC_PSR_NOSENT           /* Interrupt no. 58   - ivINT_EDMA_IRQRL_INT5  */
#define EDMA_IRQRL_INT06_PRC           INTC_PSR_NOSENT           /* Interrupt no. 59   - ivINT_EDMA_IRQRL_INT6  */
#define EDMA_IRQRL_INT07_PRC           INTC_PSR_NOSENT           /* Interrupt no. 60   - ivINT_EDMA_IRQRL_INT7  */
#define EDMA_IRQRL_INT08_PRC           INTC_PSR_NOSENT           /* Interrupt no. 61   - ivINT_EDMA_IRQRL_INT8  */
#define EDMA_IRQRL_INT09_PRC           INTC_PSR_NOSENT           /* Interrupt no. 62   - ivINT_EDMA_IRQRL_INT9  */
#define EDMA_IRQRL_INT10_PRC           INTC_PSR_NOSENT           /* Interrupt no. 63   - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_PRC           INTC_PSR_NOSENT           /* Interrupt no. 64   - ivINT_EDMA_IRQRL_INT11  */
#define EDMA_IRQRL_INT12_PRC           INTC_PSR_NOSENT           /* Interrupt no. 65   - ivINT_EDMA_IRQRL_INT12  */
#define EDMA_IRQRL_INT13_PRC           INTC_PSR_NOSENT           /* Interrupt no. 66   - ivINT_EDMA_IRQRL_INT13  */
#define EDMA_IRQRL_INT14_PRC           INTC_PSR_NOSENT           /* Interrupt no. 67   - ivINT_EDMA_IRQRL_INT14  */
#define EDMA_IRQRL_INT15_PRC           INTC_PSR_NOSENT           /* Interrupt no. 68   - ivINT_EDMA_IRQRL_INT15  */
#define EDMA_IRQRL_INT16_PRC           INTC_PSR_CORE2            /* Interrupt no. 69   - ivINT_EDMA_IRQRL_INT16  */
#define EDMA_IRQRL_INT17_PRC           INTC_PSR_CORE2            /* Interrupt no. 70   - ivINT_EDMA_IRQRL_INT17  */
#define EDMA_IRQRL_INT18_PRC           INTC_PSR_NOSENT           /* Interrupt no. 71   - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_PRC           INTC_PSR_NOSENT           /* Interrupt no. 72   - ivINT_EDMA_IRQRL_INT19  */
#define EDMA_IRQRL_INT20_PRC           INTC_PSR_NOSENT           /* Interrupt no. 73   - ivINT_EDMA_IRQRL_INT20  */
#define EDMA_IRQRL_INT21_PRC           INTC_PSR_NOSENT           /* Interrupt no. 74   - ivINT_EDMA_IRQRL_INT21  */
#define EDMA_IRQRL_INT22_PRC           INTC_PSR_NOSENT           /* Interrupt no. 75   - ivINT_EDMA_IRQRL_INT22  */
#define EDMA_IRQRL_INT23_PRC           INTC_PSR_NOSENT           /* Interrupt no. 76   - ivINT_EDMA_IRQRL_INT23  */
#define EDMA_IRQRL_INT24_PRC           INTC_PSR_CORE0            /* Interrupt no. 77   - ivINT_EDMA_IRQRL_INT24  */
#define EDMA_IRQRL_INT25_PRC           INTC_PSR_CORE0            /* Interrupt no. 78   - ivINT_EDMA_IRQRL_INT25  */
#define EDMA_IRQRL_INT26_PRC           INTC_PSR_NOSENT           /* Interrupt no. 79   - ivINT_EDMA_IRQRL_INT26  */
#define EDMA_IRQRL_INT27_PRC           INTC_PSR_NOSENT           /* Interrupt no. 80   - ivINT_EDMA_IRQRL_INT27  */
#define EDMA_IRQRL_INT28_PRC           INTC_PSR_NOSENT           /* Interrupt no. 81   - ivINT_EDMA_IRQRL_INT28  */
#define EDMA_IRQRL_INT29_PRC           INTC_PSR_NOSENT           /* Interrupt no. 82   - ivINT_EDMA_IRQRL_INT29  */
#define EDMA_IRQRL_INT30_PRC           INTC_PSR_NOSENT           /* Interrupt no. 83   - ivINT_EDMA_IRQRL_INT30  */
#define EDMA_IRQRL_INT31_PRC           INTC_PSR_NOSENT           /* Interrupt no. 84   - ivINT_EDMA_IRQRL_INT31  */
/************************************************************************************************/
/************************************ FLASH PROCESSOR *******************************************/
/************************************************************************************************/
#define FLASH_SRAM_ECC_INT_PRC         INTC_PSR_NOSENT           /* Interrupt no. 185  - ivINT_FLASH_ECC        */
/************************************************************************************************/
/************************************ ETHERNET PROCESSOR ****************************************/
/************************************************************************************************/
#define ETH_EIR_TX_PRC                 INTC_PSR_NOSENT           /* Interrupt no. 218   -   */
#define ETH_EIR_RX_PRC                 INTC_PSR_NOSENT           /* Interrupt no. 219   -   */
#define ETH_EIR_COMB_PRC               INTC_PSR_NOSENT           /* Interrupt no. 220   -   */
/************************************************************************************************/
/*********************************** PIT-RTI PROCESSOR ******************************************/
/************************************************************************************************/
#ifdef _BUILD_PIT_
#define PIT_INT0_PIT0_PRC              INTC_PSR_CORE0            /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_PRC              INTC_PSR_CORE0           /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 231  -        */
#define PIT_RTI_PIT0_PRC               INTC_PSR_NOSENT           /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_PRC              INTC_PSR_CORE0            /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_PRC              INTC_PSR_NOSENT           /* Interrupt no. 241  -        */
#else
#define PIT_INT0_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 231  -        */
#define PIT_RTI_PIT0_PRC               INTC_PSR_NOSENT           /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_PRC              INTC_PSR_NOSENT           /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_PRC              INTC_PSR_NOSENT           /* Interrupt no. 241  -        */
#endif
/************************************************************************************************/
/*************************************** XOSC PROCESSOR *****************************************/
/************************************************************************************************/
#define XOSC_CTL_PRC                   INTC_PSR_NOSENT           /* Interrupt no. 242   - ivFMPLL_SYNSR_LOCF  */
/************************************************************************************************/
/*************************************** SIUL PROCESSOR *****************************************/
/************************************************************************************************/
#define SIUL2_COMB_EXT0_PRC            INTC_PSR_NOSENT           /* Interrupt no. 243   - ivINT_SIU_OSR_OVF15_0  */
#define SIUL2_COMB_EXT1_PRC            INTC_PSR_NOSENT           /* Interrupt no. 244   - ivINT_SIU_EPROCESSOR_EIF0  */
/************************************************************************************************/
/************************************* MC PROCESSOR *********************************************/
/************************************************************************************************/
#define MC_ME_SAFE_PRC                 INTC_PSR_NOSENT           /* Interrupt no. 251   -   */
#define MC_ME_MTC_PRC                  INTC_PSR_NOSENT           /* Interrupt no. 252   -   */
#define MC_ME_IMODE_PRC                INTC_PSR_NOSENT           /* Interrupt no. 253   -   */
#define MC_ME_ICONF_PRC                INTC_PSR_NOSENT           /* Interrupt no. 254   -   */
#define MC_RGM_PRC                     INTC_PSR_NOSENT           /* Interrupt no. 255   -   */
/************************************************************************************************/
/*********************************** DSPI PROCESSOR *********************************************/
/************************************************************************************************/
#define DSPI_0_ISR_TFUF_RFOF_PRC       INTC_PSR_NOSENT           /* Interrupt no. 259  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_0_ISR_EOQF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 260  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_0_ISR_TFFF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 261  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_0_ISR_TCF_PRC             INTC_PSR_NOSENT           /* Interrupt no. 262  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_0_ISR_RFDF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 263  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDTCF_SPITCF_PRC   INTC_PSR_NOSENT           /* Interrupt no. 264  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDFFF_PRC          INTC_PSR_NOSENT           /* Interrupt no. 265  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_SPEF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 266  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_TFUF_RFOF_PRC       INTC_PSR_NOSENT           /* Interrupt no. 268  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_1_ISR_EOQF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 269  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_1_ISR_TFFF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 270  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_1_ISR_TCF_PRC             INTC_PSR_NOSENT           /* Interrupt no. 271  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_1_ISR_RFDF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 272  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDTCF_SPITCF_PRC   INTC_PSR_NOSENT           /* Interrupt no. 273  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDFFF_PRC          INTC_PSR_NOSENT           /* Interrupt no. 274  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_SPEF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 275  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_TFUF_RFOF_PRC       INTC_PSR_NOSENT           /* Interrupt no. 277  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_2_ISR_EOQF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 278  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_2_ISR_TFFF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 279  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_2_ISR_TCF_PRC             INTC_PSR_NOSENT           /* Interrupt no. 280  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_2_ISR_RFDF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 281  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDTCF_SPITCF_PRC   INTC_PSR_NOSENT           /* Interrupt no. 282  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDFFF_PRC          INTC_PSR_NOSENT           /* Interrupt no. 283  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_SPEF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 284  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_TFUF_RFOF_PRC       INTC_PSR_NOSENT           /* Interrupt no. 295  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_4_ISR_EOQF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 296  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_4_ISR_TFFF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 297  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_4_ISR_TCF_PRC             INTC_PSR_NOSENT           /* Interrupt no. 298  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_4_ISR_RFDF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 299  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDTCF_SPITCF_PRC   INTC_PSR_NOSENT           /* Interrupt no. 300  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDFFF_DSITCF_PRC   INTC_PSR_NOSENT           /* Interrupt no. 301  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_SPEF_DPEF_PRC       INTC_PSR_NOSENT           /* Interrupt no. 302  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_DDIF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 303  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_TFUF_RFOF_PRC       INTC_PSR_NOSENT           /* Interrupt no. 304  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_5_ISR_EOQF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 305  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_5_ISR_TFFF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 306  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_5_ISR_TCF_PRC             INTC_PSR_NOSENT           /* Interrupt no. 307  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_5_ISR_RFDF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 308  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDTCF_SPITCF_PRC   INTC_PSR_NOSENT           /* Interrupt no. 309  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDFFF_DSITCF_PRC   INTC_PSR_NOSENT           /* Interrupt no. 310  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_SPEF_DPEF_PRC       INTC_PSR_NOSENT           /* Interrupt no. 311  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_DDIF_PRC            INTC_PSR_NOSENT           /* Interrupt no. 312  - ivINT_DSPI_B_ISR_RFDF  */
/************************************************************************************************/
/************************************** LINFLEX PROCESSOR ***************************************/
/************************************************************************************************/
#define LINFLEX_0_RX_COMB_PRC          INTC_PSR_NOSENT           /* Interrupt no. 376    -   */
#define LINFLEX_0_TX_COMB_PRC          INTC_PSR_NOSENT           /* Interrupt no. 377    -   */
#define LINFLEX_0_ERROR_COMB_PRC       INTC_PSR_NOSENT           /* Interrupt no. 378    -   */
#define LINFLEX_1_RX_COMB_PRC          INTC_PSR_NOSENT           /* Interrupt no. 380    -   */
#define LINFLEX_1_TX_COMB_PRC          INTC_PSR_NOSENT           /* Interrupt no. 381    -   */
#define LINFLEX_1_ERROR_COMB_PRC       INTC_PSR_NOSENT           /* Interrupt no. 382    -   */
#define LINFLEX_2_RX_COMB_PRC          INTC_PSR_NOSENT           /* Interrupt no. 384    -   */
#define LINFLEX_2_TX_COMB_PRC          INTC_PSR_NOSENT           /* Interrupt no. 385    -   */
#define LINFLEX_2_ERROR_COMB_PRC       INTC_PSR_NOSENT           /* Interrupt no. 386    -   */
#define LINFLEX_14_RX_COMB_PRC         INTC_PSR_NOSENT           /* Interrupt no. 432    -   */
#define LINFLEX_14_TX_COMB_PRC         INTC_PSR_NOSENT           /* Interrupt no. 433    -   */
#define LINFLEX_14_ERROR_COMB_PRC      INTC_PSR_NOSENT           /* Interrupt no. 434    -   */
#define LINFLEX_15_RX_COMB_PRC         INTC_PSR_NOSENT           /* Interrupt no. 436    -   */
#define LINFLEX_15_TX_COMB_PRC         INTC_PSR_NOSENT           /* Interrupt no. 437    -   */
#define LINFLEX_15_ERROR_COMB_PRC      INTC_PSR_NOSENT           /* Interrupt no. 438    -   */
/************************************************************************************************/
/************************************* I2C PROCESSOR ********************************************/
/************************************************************************************************/
#define I2C_IBIF_IAAS_IBAL_PRC         INTC_PSR_NOSENT           /* Interrupt no. 440    -   */
/************************************************************************************************/
/************************************ FLEXRAY PROCESSOR *****************************************/
/************************************************************************************************/
#define FLEXRAY_LRNE_PRC               INTC_PSR_NOSENT           /* Interrupt no. 453 - ivINT_FlexRAY LRNE   */
#define FLEXRAY_LRCE_PRC               INTC_PSR_NOSENT           /* Interrupt no. 454 - ivINT_FlexRAY LRCE   */
#define FLEXRAY_FAFAIF_PRC             INTC_PSR_NOSENT           /* Interrupt no. 455 - ivINT_FlexRAY FAFAIF */
#define FLEXRAY_FAFBIF_PRC             INTC_PSR_NOSENT           /* Interrupt no. 456 - ivINT_FlexRAY FAFBIF */
#define FLEXRAY_WUPIF_PRC              INTC_PSR_NOSENT           /* Interrupt no. 457 - ivINT_FlexRAY WUPIF  */
#define FLEXRAY_PRIF_PRC               INTC_PSR_NOSENT           /* Interrupt no. 458 - ivINT_FlexRAY PRIF   */
#define FLEXRAY_CHIF_PRC               INTC_PSR_NOSENT           /* Interrupt no. 459 - ivINT_FlexRAY CHIF   */
#define FLEXRAY_TBIF_PRC               INTC_PSR_NOSENT           /* Interrupt no. 460 - ivINT_FlexRAY TBIF   */
#define FLEXRAY_RBIF_PRC               INTC_PSR_NOSENT           /* Interrupt no. 461 - ivINT_FlexRAY RBIF   */
#define FLEXRAY_MIF_PRC                INTC_PSR_NOSENT           /* Interrupt no. 462 - ivINT_FlexRAY MIF    */
/************************************************************************************************/
/************************************* GR PROCESSOR *********************************************/
/************************************************************************************************/
#define GR_VD_PRC                      INTC_PSR_NOSENT           /* Interrupt no. 477   - ivGR_VD  */
/************************************************************************************************/
/************************************* EPR PROCESSOR ********************************************/
/************************************************************************************************/
#define EPR_TEMP_PRC                   INTC_PSR_NOSENT           /* Interrupt no. 478   - ivEPR_TEMP  */
/************************************************************************************************/
/************************************ FCCU PROCESSOR ********************************************/
/************************************************************************************************/
#define FCCU_ALRM_STAT_PRC             INTC_PSR_NOSENT           /* Interrupt no. 488   - ivFMPLL_SYNSR_LOCF  */
#define FCCU_CFG_TO_STAT_PRC           INTC_PSR_NOSENT           /* Interrupt no. 489   - ivFMPLL_SYNSR_LOLF  */
/************************************************************************************************/
/************************************ STCU PROCESSOR ********************************************/
/************************************************************************************************/
#define STCU_LBIE_PRC                  INTC_PSR_NOSENT           /* Interrupt no. 494   - ivSTCU_LBIE  */
#define STCU_MBIE_PRC                  INTC_PSR_NOSENT           /* Interrupt no. 495   - ivSTCU_MBIE  */
/************************************************************************************************/
/************************************ SAR PROCESSOR *********************************************/
/************************************************************************************************/
#define SAR_0_INT_PRC                  INTC_PSR_NOSENT           /* Interrupt no. 528    -   */
#define SAR_2_INT_PRC                  INTC_PSR_NOSENT           /* Interrupt no. 530    -   */
#define SAR_4_INT_PRC                  INTC_PSR_NOSENT           /* Interrupt no. 532    -   */
#define SAR_6_INT_PRC                  INTC_PSR_NOSENT           /* Interrupt no. 534    -   */
#define SAR_B_INT_PRC                  INTC_PSR_NOSENT           /* Interrupt no. 543    -   */
/************************************************************************************************/
/************************************* SD PROCESSOR *********************************************/
/************************************************************************************************/
#define SD_0_INT_PRC                   INTC_PSR_NOSENT           /* Interrupt no. 544  - ivSD_0_INT  */
#define SD_3_INT_PRC                   INTC_PSR_NOSENT           /* Interrupt no. 547  - ivSD_3_INT  */
/************************************************************************************************/
/************************************* SENT PROCESSOR *******************************************/
/************************************************************************************************/
#define SENT_0_FAST_COMB_PRC           INTC_PSR_NOSENT           /* Interrupt no. 558    - ivSENT_0_FAST_COMB  */
#define SENT_0_SLOW_COMB_PRC           INTC_PSR_NOSENT           /* Interrupt no. 559    -   */
#define SENT_0_GBL_ERROR_PRC           INTC_PSR_NOSENT           /* Interrupt no. 560    -   */
#define SENT_1_SLOW_RDY_PRC            INTC_PSR_NOSENT           /* Interrupt no. 562    -   */
#define SENT_1_FAST_RDY_PRC            INTC_PSR_NOSENT           /* Interrupt no. 561    -   */
#define SENT_1_GBL_ERROR_PRC           INTC_PSR_NOSENT           /* Interrupt no. 563    -   */
#define SENT_0_FMSG_0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 564    -   */
#define SENT_0_SMSG_0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 565    -   */
#define SENT_0_ERROR_0_PRC             INTC_PSR_NOSENT           /* Interrupt no. 566    -   */
#define SENT_0_FMSG_1_PRC              INTC_PSR_NOSENT           /* Interrupt no. 567    -   */
#define SENT_0_SMSG_1_PRC              INTC_PSR_NOSENT           /* Interrupt no. 568    -   */
#define SENT_0_ERROR_1_PRC             INTC_PSR_NOSENT           /* Interrupt no. 569    -   */
#define SENT_0_FMSG_2_PRC              INTC_PSR_NOSENT           /* Interrupt no. 570    -   */
#define SENT_0_SMSG_2_PRC              INTC_PSR_NOSENT           /* Interrupt no. 571    -   */
#define SENT_0_ERROR_2_PRC             INTC_PSR_NOSENT           /* Interrupt no. 572    -   */
#define SENT_0_FMSG_3_PRC              INTC_PSR_NOSENT           /* Interrupt no. 573    -   */
#define SENT_0_SMSG_3_PRC              INTC_PSR_NOSENT           /* Interrupt no. 574    -   */
#define SENT_0_ERROR_3_PRC             INTC_PSR_NOSENT           /* Interrupt no. 575    -   */
#define SENT_1_FMSG_0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 579    -   */
#define SENT_1_SMSG_0_PRC              INTC_PSR_NOSENT           /* Interrupt no. 580    -   */
#define SENT_1_ERROR_0_PRC             INTC_PSR_NOSENT           /* Interrupt no. 581    -   */
#define SENT_1_FMSG_1_PRC              INTC_PSR_NOSENT           /* Interrupt no. 582    -   */
#define SENT_1_SMSG_1_PRC              INTC_PSR_NOSENT           /* Interrupt no. 583    -   */
#define SENT_1_ERROR_1_PRC             INTC_PSR_NOSENT           /* Interrupt no. 584    -   */
/************************************************************************************************/
/************************************* PSI PROCESSOR ********************************************/
/************************************************************************************************/
#define PSI5_0_DMA_0_PRC               INTC_PSR_NOSENT           /* Interrupt no. 612    -   */
#define PSI5_0_GEN_0_PRC               INTC_PSR_NOSENT           /* Interrupt no. 613    -   */
#define PSI5_0_NEW_MSG_0_PRC           INTC_PSR_NOSENT           /* Interrupt no. 614    -   */
#define PSI5_0_MSG_OW_0_PRC            INTC_PSR_NOSENT           /* Interrupt no. 615    -   */
#define PSI5_0_ERROR_COMB_0_PRC        INTC_PSR_NOSENT           /* Interrupt no. 616    -   */
#define PSI5_0_GLOBAL_0_PRC            INTC_PSR_NOSENT           /* Interrupt no. 617    -   */
#define PSI5_1_DMA_0_PRC               INTC_PSR_NOSENT           /* Interrupt no. 624    -   */
#define PSI5_1_GEN_0_PRC               INTC_PSR_NOSENT           /* Interrupt no. 625    -   */
#define PSI5_1_NEW_MSG_0_PRC           INTC_PSR_NOSENT           /* Interrupt no. 626    -   */
#define PSI5_1_MSG_OW_0_PRC            INTC_PSR_NOSENT           /* Interrupt no. 627    -   */
#define PSI5_1_ERROR_COMB_0_PRC        INTC_PSR_NOSENT           /* Interrupt no. 628    -   */
#define PSI5_1_GLOBAL_0_PRC            INTC_PSR_NOSENT           /* Interrupt no. 629    -   */
/************************************************************************************************/
/************************************ SIPI PROCESSOR ********************************************/
/************************************************************************************************/
#define SIPI_ERROR_COMB_PRC            INTC_PSR_NOSENT           /* Interrupt no. 654    -   */
#define SIPI_CRC_ERROR_PRC             INTC_PSR_NOSENT           /* Interrupt no. 655    -   */
#define SIPI_CH0_RX_PRC                INTC_PSR_NOSENT           /* Interrupt no. 656    -   */
#define SIPI_CH1_RX_PRC                INTC_PSR_NOSENT           /* Interrupt no. 657    -   */
#define SIPI_CH2_RX_PRC                INTC_PSR_NOSENT           /* Interrupt no. 658    -   */
#define SIPI_CH3_RX_PRC                INTC_PSR_NOSENT           /* Interrupt no. 659    -   */
#define SIPI_EVENT_COMB_PRC            INTC_PSR_NOSENT           /* Interrupt no. 660    -   */
/************************************************************************************************/
/************************************ LFAST PROCESSOR *******************************************/
/************************************************************************************************/
#define LFAST_0_TX_PRC                 INTC_PSR_NOSENT           /* Interrupt no. 661    -   */
#define LFAST_0_TX_ERROR_PRC           INTC_PSR_NOSENT           /* Interrupt no. 662    -   */
#define LFAST_0_RX_PRC                 INTC_PSR_NOSENT           /* Interrupt no. 663    -   */
#define LFAST_0_RX_ERROR_PRC           INTC_PSR_NOSENT           /* Interrupt no. 664    -   */
#define LFAST_0_ICLC_RX_PRC            INTC_PSR_NOSENT           /* Interrupt no. 665    -   */
/************************************************************************************************/
/************************************ JTAG PROCESSOR ********************************************/
/************************************************************************************************/
#define JTAG_GM_PRC                    INTC_PSR_NOSENT           /* Interrupt no. 674    -   */
#define JTAG_DC_PRC                    INTC_PSR_NOSENT           /* Interrupt no. 675    -   */
/************************************************************************************************/
/*********************************** M_TTCAN PROCESSOR ******************************************/
/************************************************************************************************/
#define M_TTCAN_LINE0_PRC              INTC_PSR_CORE0           /* Interrupt no. 677    -   */
#define M_TTCAN_LINE1_PRC              INTC_PSR_CORE0           /* Interrupt no. 678    -   */
#define M_TTCAN_RTMI_PRC               INTC_PSR_NOSENT          /* Interrupt no. 679    -   */
/************************************************************************************************/
/************************************ M_CAN PROCESSOR *******************************************/
/************************************************************************************************/
#define MCAN1_LINE0_PRC                INTC_PSR_CORE0           /* Interrupt no. 688    -   */
#define MCAN1_LINE1_PRC                INTC_PSR_CORE0           /* Interrupt no. 689    -   */
#define MCAN2_LINE0_PRC                INTC_PSR_CORE0           /* Interrupt no. 690    -   */
#define MCAN2_LINE1_PRC                INTC_PSR_CORE0           /* Interrupt no. 691    -   */
/************************************************************************************************/
/************************************ GTM PROCESSOR *********************************************/
/************************************************************************************************/
#define GTM_AEI_PRC                    INTC_PSR_NOSENT           /* Interrupt no. 706    -   */
#define GTM_ARU_NEW_DATA0_PRC          INTC_PSR_NOSENT           /* Interrupt no. 707    -   */
#define GTM_ARU_NEW_DATA1_PRC          INTC_PSR_NOSENT           /* Interrupt no. 708    -   */
#define GTM_ARU_ACC_ACK_PRC            INTC_PSR_NOSENT           /* Interrupt no. 709    -   */
#define GTM_BRC_PRC                    INTC_PSR_NOSENT           /* Interrupt no. 710    -   */
#define GTM_CMP_PRC                    INTC_PSR_NOSENT           /* Interrupt no. 711    -   */
#define GTM_SPE0_PRC                   INTC_PSR_NOSENT           /* Interrupt no. 712    -   */
#define GTM_SPE1_PRC                   INTC_PSR_NOSENT           /* Interrupt no. 713    -   */
#define GTM_PSM0_CH0_PRC               INTC_PSR_NOSENT           /* Interrupt no. 714    -   */
#define GTM_PSM0_CH1_PRC               INTC_PSR_NOSENT           /* Interrupt no. 715    -   */
#define GTM_PSM0_CH2_PRC               INTC_PSR_NOSENT           /* Interrupt no. 716    -   */
#define GTM_PSM0_CH3_PRC               INTC_PSR_NOSENT           /* Interrupt no. 717    -   */
#define GTM_PSM0_CH4_PRC               INTC_PSR_NOSENT           /* Interrupt no. 718    -   */
#define GTM_PSM0_CH5_PRC               INTC_PSR_NOSENT           /* Interrupt no. 719    -   */
#define GTM_PSM0_CH6_PRC               INTC_PSR_NOSENT           /* Interrupt no. 720    -   */
#define GTM_PSM0_CH7_PRC               INTC_PSR_NOSENT           /* Interrupt no. 721    -   */
#define GTM_DPLL_DCGI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 722    -   */
#define GTM_DPLL_EDI_PRC               INTC_PSR_NOSENT           /* Interrupt no. 723    -   */
#define GTM_DPLL_TINI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 724    -   */
#define GTM_DPLL_TAXI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 725    -   */
#define GTM_DPLL_SISI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 726    -   */
#define GTM_DPLL_TISI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 727    -   */
#define GTM_DPLL_MSI_PRC               INTC_PSR_NOSENT           /* Interrupt no. 728    -   */
#define GTM_DPLL_MTI_PRC               INTC_PSR_NOSENT           /* Interrupt no. 729    -   */
#define GTM_DPLL_SASI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 730    -   */
#define GTM_DPLL_TASI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 731    -   */
#define GTM_DPLL_PWI_PRC               INTC_PSR_NOSENT           /* Interrupt no. 732    -   */
#define GTM_DPLL_W2I_PRC               INTC_PSR_NOSENT           /* Interrupt no. 733    -   */
#define GTM_DPLL_W1I_PRC               INTC_PSR_NOSENT           /* Interrupt no. 734    -   */
#define GTM_DPLL_GL1I_PRC              INTC_PSR_CORE0           /* Interrupt no. 735    -   */
#define GTM_DPLL_LL1I_PRC              INTC_PSR_CORE0           /* Interrupt no. 736    -   */
#define GTM_DPLL_EI_PRC                INTC_PSR_NOSENT           /* Interrupt no. 737    -   */
#define GTM_DPLL_GL2I_PRC              INTC_PSR_NOSENT            /* Interrupt no. 738    -   */
#define GTM_DPLL_LL2I_PRC              INTC_PSR_NOSENT           /* Interrupt no. 739    -   */
#define GTM_DPLL_TE0I_PRC              INTC_PSR_NOSENT           /* Interrupt no. 740    -   */
#define GTM_DPLL_TE1I_PRC              INTC_PSR_NOSENT           /* Interrupt no. 741    -   */
#define GTM_DPLL_TE2I_PRC              INTC_PSR_NOSENT           /* Interrupt no. 742    -   */
#define GTM_DPLL_TE3I_PRC              INTC_PSR_NOSENT           /* Interrupt no. 743    -   */
#define GTM_DPLL_TE4I_PRC              INTC_PSR_NOSENT           /* Interrupt no. 744    -   */
#define GTM_DPLL_CDTI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 745    -   */
#define GTM_DPLL_CDSI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 746    -   */
#define GTM_DPLL_TORI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 747    -   */
#define GTM_DPLL_SORI_PRC              INTC_PSR_NOSENT           /* Interrupt no. 748    -   */
#define GTM_TIM0_CH0_PRC               INTC_PSR_CORE0            /* Interrupt no. 749    -   */
#define GTM_TIM0_CH1_PRC               INTC_PSR_CORE0            /* Interrupt no. 750    -   */
#define GTM_TIM0_CH2_PRC               INTC_PSR_CORE0           /* Interrupt no. 751    -   */
#define GTM_TIM0_CH3_PRC               INTC_PSR_NOSENT           /* Interrupt no. 752    -   */
#define GTM_TIM0_CH4_PRC               INTC_PSR_NOSENT           /* Interrupt no. 753    -   */
#define GTM_TIM0_CH5_PRC               INTC_PSR_NOSENT           /* Interrupt no. 754    -   */
#define GTM_TIM0_CH6_PRC               INTC_PSR_NOSENT           /* Interrupt no. 755    -   */
#define GTM_TIM0_CH7_PRC               INTC_PSR_NOSENT           /* Interrupt no. 756    -   */
#define GTM_TIM1_CH0_PRC               INTC_PSR_CORE0            /* Interrupt no. 757    -   */
#define GTM_TIM1_CH1_PRC               INTC_PSR_CORE0            /* Interrupt no. 758    -   */
#define GTM_TIM1_CH2_PRC               INTC_PSR_CORE0            /* Interrupt no. 759    -   */
#define GTM_TIM1_CH3_PRC               INTC_PSR_CORE0            /* Interrupt no. 760    -   */
#define GTM_TIM1_CH4_PRC               INTC_PSR_CORE0            /* Interrupt no. 761    -   */
#define GTM_TIM1_CH5_PRC               INTC_PSR_CORE0            /* Interrupt no. 762    -   */
#define GTM_TIM1_CH6_PRC               INTC_PSR_CORE0            /* Interrupt no. 763    -   */
#define GTM_TIM1_CH7_PRC               INTC_PSR_CORE0            /* Interrupt no. 764    -   */
#define GTM_TIM2_CH0_PRC               INTC_PSR_CORE0            /* Interrupt no. 765    -   */
#define GTM_TIM2_CH1_PRC               INTC_PSR_CORE0            /* Interrupt no. 766    -   */
#define GTM_TIM2_CH2_PRC               INTC_PSR_CORE0            /* Interrupt no. 767    -   */
#define GTM_TIM2_CH3_PRC               INTC_PSR_CORE0            /* Interrupt no. 768    -   */
#define GTM_TIM2_CH4_PRC               INTC_PSR_CORE0            /* Interrupt no. 769    -   */
#define GTM_TIM2_CH5_PRC               INTC_PSR_CORE0            /* Interrupt no. 770    -   */
#define GTM_TIM2_CH6_PRC               INTC_PSR_CORE0            /* Interrupt no. 771    -   */
#define GTM_TIM2_CH7_PRC               INTC_PSR_CORE0            /* Interrupt no. 772    -   */
#define GTM_MCS0_CH0_PRC               INTC_PSR_CORE0            /* Interrupt no. 781    -   */
#define GTM_MCS0_CH1_PRC               INTC_PSR_CORE0            /* Interrupt no. 782    -   */
#define GTM_MCS0_CH2_PRC               INTC_PSR_CORE0            /* Interrupt no. 783    -   */
#define GTM_MCS0_CH3_PRC               INTC_PSR_CORE0            /* Interrupt no. 784    -   */
#define GTM_MCS0_CH4_PRC               INTC_PSR_NOSENT           /* Interrupt no. 785    -   */
#define GTM_MCS0_CH5_PRC               INTC_PSR_CORE0            /* Interrupt no. 786    -   */
#define GTM_MCS0_CH6_PRC               INTC_PSR_CORE0            /* Interrupt no. 787    -   */
#define GTM_MCS0_CH7_PRC               INTC_PSR_CORE0            /* Interrupt no. 788    -   */
#define GTM_MCS1_CH0_PRC               INTC_PSR_CORE0            /* Interrupt no. 789    -   */
#define GTM_MCS1_CH1_PRC               INTC_PSR_CORE0            /* Interrupt no. 790    -   */
#define GTM_MCS1_CH2_PRC               INTC_PSR_CORE0            /* Interrupt no. 791    -   */
#define GTM_MCS1_CH3_PRC               INTC_PSR_CORE0            /* Interrupt no. 792    -   */
#define GTM_MCS1_CH4_PRC               INTC_PSR_NOSENT           /* Interrupt no. 793    -   */
#define GTM_MCS1_CH5_PRC               INTC_PSR_CORE0            /* Interrupt no. 794    -   */
#define GTM_MCS1_CH6_PRC               INTC_PSR_CORE0            /* Interrupt no. 795    -   */
#define GTM_MCS1_CH7_PRC               INTC_PSR_CORE0            /* Interrupt no. 796    -   */
#define GTM_MCS2_CH0_PRC               INTC_PSR_CORE0            /* Interrupt no. 797    -   */
#define GTM_MCS2_CH1_PRC               INTC_PSR_CORE0            /* Interrupt no. 798    -   */
#define GTM_MCS2_CH2_PRC               INTC_PSR_CORE0            /* Interrupt no. 799    -   */
#define GTM_MCS2_CH3_PRC               INTC_PSR_CORE0            /* Interrupt no. 800    -   */
#define GTM_MCS2_CH4_PRC               INTC_PSR_CORE0            /* Interrupt no. 801    -   */
#define GTM_MCS2_CH5_PRC               INTC_PSR_CORE0            /* Interrupt no. 802    -   */
#define GTM_MCS2_CH6_PRC               INTC_PSR_NOSENT           /* Interrupt no. 803    -   */
#define GTM_MCS2_CH7_PRC               INTC_PSR_NOSENT           /* Interrupt no. 804    -   */
#define GTM_TOM0_CH0_1_PRC             INTC_PSR_NOSENT           /* Interrupt no. 813    -   */
#define GTM_TOM0_CH2_3_PRC             INTC_PSR_NOSENT           /* Interrupt no. 814    -   */
#define GTM_TOM0_CH4_5_PRC             INTC_PSR_NOSENT           /* Interrupt no. 815    -   */
#define GTM_TOM0_CH6_7_PRC             INTC_PSR_NOSENT           /* Interrupt no. 816    -   */
#define GTM_TOM0_CH8_9_PRC             INTC_PSR_NOSENT           /* Interrupt no. 817    -   */
#define GTM_TOM0_CH10_11_PRC           INTC_PSR_NOSENT           /* Interrupt no. 818    -   */
#define GTM_TOM0_CH12_13_PRC           INTC_PSR_NOSENT           /* Interrupt no. 819    -   */
#define GTM_TOM0_CH14_15_PRC           INTC_PSR_NOSENT           /* Interrupt no. 820    -   */
#define GTM_TOM1_CH0_1_PRC             INTC_PSR_NOSENT           /* Interrupt no. 821    -   */
#define GTM_TOM1_CH2_3_PRC             INTC_PSR_NOSENT           /* Interrupt no. 822    -   */
#define GTM_TOM1_CH4_5_PRC             INTC_PSR_NOSENT           /* Interrupt no. 823    -   */
#define GTM_TOM1_CH6_7_PRC             INTC_PSR_NOSENT           /* Interrupt no. 824    -   */
#define GTM_TOM1_CH8_9_PRC             INTC_PSR_NOSENT           /* Interrupt no. 825    -   */
#define GTM_TOM1_CH10_11_PRC           INTC_PSR_NOSENT           /* Interrupt no. 826    -   */
#define GTM_TOM1_CH12_13_PRC           INTC_PSR_NOSENT           /* Interrupt no. 827    -   */
#define GTM_TOM1_CH14_15_PRC           INTC_PSR_NOSENT           /* Interrupt no. 828    -   */
#define GTM_ATOM0_CH0_1_PRC            INTC_PSR_CORE0            /* Interrupt no. 837    -   */
#define GTM_ATOM0_CH2_3_PRC            INTC_PSR_NOSENT           /* Interrupt no. 838    -   */
#define GTM_ATOM0_CH4_5_PRC            INTC_PSR_NOSENT           /* Interrupt no. 839    -   */
#define GTM_ATOM0_CH6_7_PRC            INTC_PSR_NOSENT           /* Interrupt no. 840    -   */
#define GTM_ATOM1_CH0_1_PRC            INTC_PSR_NOSENT           /* Interrupt no. 841    -   */
#define GTM_ATOM1_CH2_3_PRC            INTC_PSR_NOSENT           /* Interrupt no. 842    -   */
#define GTM_ATOM1_CH4_5_PRC            INTC_PSR_NOSENT           /* Interrupt no. 843    -   */
#define GTM_ATOM1_CH6_7_PRC            INTC_PSR_NOSENT           /* Interrupt no. 844    -   */
#define GTM_ATOM2_CH0_1_PRC            INTC_PSR_NOSENT           /* Interrupt no. 845    -   */
#define GTM_ATOM2_CH2_3_PRC            INTC_PSR_NOSENT           /* Interrupt no. 846    -   */
#define GTM_ATOM2_CH4_5_PRC            INTC_PSR_NOSENT           /* Interrupt no. 847    -   */
#define GTM_ATOM2_CH6_7_PRC            INTC_PSR_NOSENT           /* Interrupt no. 848    -   */
#define GTM_ATOM3_CH0_1_PRC            INTC_PSR_NOSENT           /* Interrupt no. 849    -   */
#define GTM_ATOM3_CH2_3_PRC            INTC_PSR_NOSENT           /* Interrupt no. 850    -   */
#define GTM_ATOM3_CH4_5_PRC            INTC_PSR_NOSENT           /* Interrupt no. 851    -   */
#define GTM_ATOM3_CH6_7_PRC            INTC_PSR_NOSENT           /* Interrupt no. 852    -   */
#define GTM_ERR_PRC                    INTC_PSR_NOSENT           /* Interrupt no. 931    -   */
/************************************************************************************************/
/************************************* CCCU PROCESSOR *******************************************/
/************************************************************************************************/
#define CCCU_CSCE_CWEE_PRC             INTC_PSR_NOSENT           /* Interrupt no. 935    -   */  
/************************************************************************************************/



/* ------------------------------------------------------------------ */
/*     Common defines                                                   */
/* ------------------------------------------------------------------ */
#define SET_BIT_ENABLED    0x01u
#define SET_BIT_DISABLED   0x00u

#pragma ghs endnomisra

#endif /* _TASK_CFG_H_ */
