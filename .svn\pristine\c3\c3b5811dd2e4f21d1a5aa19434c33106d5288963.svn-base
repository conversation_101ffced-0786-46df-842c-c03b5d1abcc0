/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                              */
/* $Revision::        $                                                                                                        */
/* $Date::                                                $                                                                    */
/* $Author::                 $                                                                                         */
/*******************************************************************************************************************************/

#ifndef _ANALOG_BOARD_CFG_H_
#define _ANALOG_BOARD_CFG_H_

/*ANALOGIC INPUTS*/
/*********PIN ID ****** ***PCR NUMBER****/ 
//#define     IPE_CrankSens
#define     IP_IPRI_B0        59  //PD[11]
#define     IP_IPRI_B1       104  //PG[8]
#define     IP_SEC_B0         77  //PE[13]
#define     IP_SEC_B1        103  //PG[7]

#define     IP_ION_04        100  //PG[4]
#define     IP_ION_15        137  //PI[9]
#define     IP_ION_26         99  //PG[3]
#define     IP_ION_37         67  //PE[3]


/* SAR SV */
#define     NTC1              21  //PB[5]
#define     KEY_SIGNAL        23  //PB[7]
#define     NTC2             101  //PG[5]
#define     V_BATT           102  //PG[6]
#define     BOARD_SEL        105  //PG[9]
#define     I_BATT_MON       106  //PG[10]
#define     IA_BANKSEL        98  //PG[2]
#define     IDE_FS_LIVENESS   22  //PB[6]

/* SD 0 */
#define     I_BUCK_1_MON      16  //PB[0]
#define     VC_3_7_MON        17  //PB[1]
#define     VC_1_5_MON        18  //PB[2]
#define     VCOIL_1_MON	      19  //PB[3]

/* SD 3 */ 
#define     VC_2_6_MON        28  //PB[12]
#define     VC_4_8_MON        29  //PB[13]
#define     I_BUCK_2_MON      30  //PB[14]
#define     VCOIL_2_MON	      31  //PB[15]

/*ANALOG INPUTS*/
/*********PIN ID ****** ***PCR NUMBER****/
/* SD 0 */
#define I_BUCK_1_MON_CH                  (CH0)
#define I_BUCK_1_MON_PER_AN              (AN2)

#define VC_3_7_MON_CH                    (CH1)
#define VC_3_7_MON_PER_AN                (AN3)

#define VC_1_5_MON_CH                    (CH2)
#define VC_1_5_MON_PER_AN                (AN0)

#define VCOIL_1_MON_CH                   (CH3)
#define VCOIL_1_MON_PER_AN               (AN1)

/* SD 3 */ 
#define VC_2_6_MON_CH                    (CH4)
#define VC_2_6_MON_PER_AN                (AN0)

#define VC_4_8_MON_CH                    (CH5)
#define VC_4_8_MON_PER_AN                (AN1)

#define I_BUCK_2_MON_CH                  (CH6)
#define I_BUCK_2_MON_PER_AN              (AN2)

#define VCOIL_2_MON_CH                   (CH7)
#define VCOIL_2_MON_PER_AN               (AN3)

/* SAR 0 */
#define I_PRI_B0_CH                      (CH8)
#define I_PRI_B0_PER_AN                  (AN4)

#define I_SEC_B0_CH                      (CH9)
#define I_SEC_B0_PER_AN                  (AN5)

/* SAR 2 */
#define I_PRI_B1_CH                      (CH10)
#define I_PRI_B1_PER_AN                  (AN16)

#define I_SEC_B1_CH                      (CH11)
#define I_SEC_B1_PER_AN                  (AN17)

/* SAR 4 */
#define IP_ION_04_CH                     (CH12)
#define IP_ION_04_PER_AN                 (AN44)

#define IP_ION_26_CH                     (CH13)
#define IP_ION_26_PER_AN                 (AN45)

/* SAR 6 */
#define IP_ION_15_CH                     (CH14)
#define IP_ION_15_PER_AN                 (AN48)

#define IP_ION_37_CH                     (CH15)
#define IP_ION_37_PER_AN                 (AN52)

/* SAR SV */
#define NTC1_CH                          (CH16)
#define NTC1_PER_AN                      (AN35)

#define KEY_SIGNAL_CH                    (CH17)
#define KEY_SIGNAL_PER_AN                (AN21)

#define NTC2_CH                          (CH18)
#define NTC2_PER_AN                      (AN25)

#define V_BATT_CH                        (CH19)
#define V_BATT_PER_AN                    (AN24)

#define BOARD_SEL_CH                     (CH20)
#define BOARD_SEL_PER_AN                 (AN13)

#define I_BATT_MON_CH                    (CH21)
#define I_BATT_MON_PER_AN                (AN12)

#define IA_BANKSEL_CH                    (CH22)
#define IA_BANKSEL_PER_AN                (AN46)

#define IDE_FS_LIVENESS_CH               (CH23)
#define IDE_FS_LIVENESS_PER_AN           (AN20)

#endif /* _ANALOG_BOARD_CFG_H_ */

