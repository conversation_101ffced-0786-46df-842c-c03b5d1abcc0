/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TASK
**  Filename        :  task.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  SantoroR
******************************************************************************/
/*****************************************************************************
**
**                        TASK Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef __TASK_H__
#define __TASK_H__

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "sys.h"
#include "OS_api.h"
#include "Pit_out.h"
#ifdef _BUILD_SAFETYMNGR_INTC_
#include "SafetyMngr_CommLib_out.h"
#include "SafetyMngr_INTC_out.h"
#endif


/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
#define FIFO_HANDLER_DIM       10u   /* max dim. of software FIFO ISR list   */
#define SOFT_SET_INTERRUPT_NUM  8u   /* max number of software settable      */
                                     /* interrupt request                    */

#define PSR_VECTOR_DIM         1024u   /* number of effective Interrupt Request source*/ 
#define PSR_PRI_COL 0x0u       // matrix column for ISR priority      
#define PSR_PRCSEL_COL 0x1u    // matrix column for ISR processor

#define RESERVED_PRI    0x40u //Max validid priority (0x3Fu) +  1 

#define S_IRQ0  0u /* Software settable interrupts Request index  */
#define S_IRQ1  1u
#define S_IRQ2  2u
#define S_IRQ3  3u
#define S_IRQ4  4u
#define S_IRQ5  5u
#define S_IRQ6  6u
#define S_IRQ7  7u
#define S_IRQ8  8u
#define S_IRQ9  9u
#define S_IRQ10 10u
#define S_IRQ11 11u
#define S_IRQ12 12u
#define S_IRQ13 13u
#define S_IRQ14 14u
#define S_IRQ15 15u
#define S_IRQ16 16u
#define S_IRQ17 17u
#define S_IRQ18 18u
#define S_IRQ19 19u
#define S_IRQ20 20u
#define S_IRQ21 21u
#define S_IRQ22 22u
#define S_IRQ23 23u
#define S_IRQ24 24u
#define S_IRQ25 25u
#define S_IRQ26 26u
#define S_IRQ27 27u
#define S_IRQ28 28u
#define S_IRQ29 29u
#define S_IRQ30 30u
#define S_IRQ31 31u
#define LAST_S_IRQ (S_IRQ31+1u)

#define CHANNEL_ALREADY_CONFIGURED  255
#define MAX_DELAY_TIME              150000u /* usec before  */
#define MIN_DELAY_TIME              20u     /* usec */
#define MAX_DELAY_TIME_EXCEEDED     254
#define MIN_DELAY_TIME_FAILED       253

/* Interrupt vector table ISRs offset position */
/************************************************************************************************/
/*************************************** OSEK ISR ************************************************/
/************************************************************************************************/
#define INTC_SSCIR0_ISR_POS                0u /* Interrupt no. 0     - ivINT_SSCIR0_CLR0    */
#define INTC_SSCIR1_ISR_POS                1u /* Interrupt no. 1     - ivINT_SSCIR1_CLR1    */
#define INTC_SSCIR2_ISR_POS                2u /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
#define INTC_SSCIR3_ISR_POS                3u /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
#define INTC_SSCIR4_ISR_POS                4u /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
#define INTC_SSCIR5_ISR_POS                5u /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
#define INTC_SSCIR6_ISR_POS                6u /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
#define INTC_SSCIR7_ISR_POS                7u /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
#define INTC_SSCIR8_ISR_POS                8u /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
#define INTC_SSCIR9_ISR_POS                9u /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
#define INTC_SSCIR10_ISR_POS              10u /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
#define INTC_SSCIR11_ISR_POS              11u /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
#define INTC_SSCIR12_ISR_POS              12u /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
#define INTC_SSCIR13_ISR_POS              13u /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
#define INTC_SSCIR14_ISR_POS              14u /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
#define INTC_SSCIR15_ISR_POS              15u /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
#define INTC_SSCIR16_ISR_POS              16u /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
#define INTC_SSCIR17_ISR_POS              17u /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
#define INTC_SSCIR18_ISR_POS              18u /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
#define INTC_SSCIR19_ISR_POS              19u /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
#define INTC_SSCIR20_ISR_POS              20u /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
#define INTC_SSCIR21_ISR_POS              21u /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
#define INTC_SSCIR22_ISR_POS              22u /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
#define INTC_SSCIR23_ISR_POS              23u /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
#define INTC_SSCIR24_ISR_POS              24u /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
#define INTC_SSCIR25_ISR_POS              25u /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
#define INTC_SSCIR26_ISR_POS              26u /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
#define INTC_SSCIR27_ISR_POS              27u /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
#define INTC_SSCIR28_ISR_POS              28u /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
#define INTC_SSCIR29_ISR_POS              29u /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
#define INTC_SSCIR30_ISR_POS              30u /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
#define INTC_SSCIR31_ISR_POS              31u /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
/************************************************************************************************/
/*************************************** SWT ISR ***********************************************/
/************************************************************************************************/
#define SWT0_ISR_POS                      32u /* Interrupt no. 32    -   */
#define RESERVED_POS_33                   33u /* Interrupt no. 33    - RESERVED  */
#define SWT2_ISR_POS                      34u /* Interrupt no. 34    -   */
#define SWT3_ISR_POS                      35u /* Interrupt no. 35    -   */
/************************************************************************************************/
/*************************************** STM ISR ***********************************************/
/************************************************************************************************/
#define STM_INT_0_CIR0_ISR_POS            36u /* Interrupt no. 36    -   */
#define STM_INT_0_CIR1_ISR_POS            37u /* Interrupt no. 37    -   */
#define STM_INT_0_CIR2_ISR_POS            38u /* Interrupt no. 38    -   */
#define STM_INT_0_CIR3_ISR_POS            39u /* Interrupt no. 39    -   */
#define RESERVED_POS_40                   40u /* Interrupt no. 40    - RESERVED  */
#define RESERVED_POS_41                   41u /* Interrupt no. 41    - RESERVED  */
#define RESERVED_POS_42                   42u /* Interrupt no. 42    - RESERVED  */
#define RESERVED_POS_43                   43u /* Interrupt no. 43    - RESERVED  */
#define STM_INT_2_CIR0_ISR_POS            44u /* Interrupt no. 44    -   */
#define STM_INT_2_CIR1_ISR_POS            45u /* Interrupt no. 45    -   */
#define STM_INT_2_CIR2_ISR_POS            46u /* Interrupt no. 46    -   */
#define STM_INT_2_CIR3_ISR_POS            47u /* Interrupt no. 47    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_48                   48u /* Interrupt no. 48    - RESERVED  */
#define RESERVED_POS_49                   49u /* Interrupt no. 49    - RESERVED  */
#define RESERVED_POS_50                   50u /* Interrupt no. 50    - RESERVED  */
#define RESERVED_POS_51                   51u /* Interrupt no. 51    - RESERVED  */
/************************************************************************************************/
/*************************************** EDMA ISR ************************************************/
/************************************************************************************************/
#define EDMA_ERL_ERR31_ERR0_ISR_POS       52u /* Interrupt no. 52   - ivINT_EDMA_ERRL_ERR31_0  */
#define EDMA_IRQRL_INT00_ISR_POS          53u /* Interrupt no. 53   - ivINT_EDMA_IRQRL_INT0  */
#define EDMA_IRQRL_INT01_ISR_POS          54u /* Interrupt no. 54   - ivINT_EDMA_IRQRL_INT1  */
#define EDMA_IRQRL_INT02_ISR_POS          55u /* Interrupt no. 55   - ivINT_EDMA_IRQRL_INT2  */
#define EDMA_IRQRL_INT03_ISR_POS          56u /* Interrupt no. 56   - ivINT_EDMA_IRQRL_INT3  */
#define EDMA_IRQRL_INT04_ISR_POS          57u /* Interrupt no. 57   - ivINT_EDMA_IRQRL_INT4  */
#define EDMA_IRQRL_INT05_ISR_POS          58u /* Interrupt no. 58   - ivINT_EDMA_IRQRL_INT5  */
#define EDMA_IRQRL_INT06_ISR_POS          59u /* Interrupt no. 59   - ivINT_EDMA_IRQRL_INT6  */
#define EDMA_IRQRL_INT07_ISR_POS          60u /* Interrupt no. 60   - ivINT_EDMA_IRQRL_INT7  */
#define EDMA_IRQRL_INT08_ISR_POS          61u /* Interrupt no. 61   - ivINT_EDMA_IRQRL_INT8  */
#define EDMA_IRQRL_INT09_ISR_POS          62u /* Interrupt no. 62   - ivINT_EDMA_IRQRL_INT9  */
#define EDMA_IRQRL_INT10_ISR_POS          63u /* Interrupt no. 63   - ivINT_EDMA_IRQRL_INT10  */
#define EDMA_IRQRL_INT11_ISR_POS          64u /* Interrupt no. 64   - ivINT_EDMA_IRQRL_INT11  */
#define EDMA_IRQRL_INT12_ISR_POS          65u /* Interrupt no. 65   - ivINT_EDMA_IRQRL_INT12  */
#define EDMA_IRQRL_INT13_ISR_POS          66u /* Interrupt no. 66   - ivINT_EDMA_IRQRL_INT13  */
#define EDMA_IRQRL_INT14_ISR_POS          67u /* Interrupt no. 67   - ivINT_EDMA_IRQRL_INT14  */
#define EDMA_IRQRL_INT15_ISR_POS          68u /* Interrupt no. 68   - ivINT_EDMA_IRQRL_INT15  */
#define EDMA_IRQRL_INT16_ISR_POS          69u /* Interrupt no. 69   - ivINT_EDMA_IRQRL_INT16  */
#define EDMA_IRQRL_INT17_ISR_POS          70u /* Interrupt no. 70   - ivINT_EDMA_IRQRL_INT17  */
#define EDMA_IRQRL_INT18_ISR_POS          71u /* Interrupt no. 71   - ivINT_EDMA_IRQRL_INT18  */
#define EDMA_IRQRL_INT19_ISR_POS          72u /* Interrupt no. 72   - ivINT_EDMA_IRQRL_INT19  */
#define EDMA_IRQRL_INT20_ISR_POS          73u /* Interrupt no. 73   - ivINT_EDMA_IRQRL_INT20  */
#define EDMA_IRQRL_INT21_ISR_POS          74u /* Interrupt no. 74   - ivINT_EDMA_IRQRL_INT21  */
#define EDMA_IRQRL_INT22_ISR_POS          75u /* Interrupt no. 75   - ivINT_EDMA_IRQRL_INT22  */
#define EDMA_IRQRL_INT23_ISR_POS          76u /* Interrupt no. 76   - ivINT_EDMA_IRQRL_INT23  */
#define EDMA_IRQRL_INT24_ISR_POS          77u /* Interrupt no. 77   - ivINT_EDMA_IRQRL_INT24  */
#define EDMA_IRQRL_INT25_ISR_POS          78u /* Interrupt no. 78   - ivINT_EDMA_IRQRL_INT25  */
#define EDMA_IRQRL_INT26_ISR_POS          79u /* Interrupt no. 79   - ivINT_EDMA_IRQRL_INT26  */
#define EDMA_IRQRL_INT27_ISR_POS          80u /* Interrupt no. 80   - ivINT_EDMA_IRQRL_INT27  */
#define EDMA_IRQRL_INT28_ISR_POS          81u /* Interrupt no. 81   - ivINT_EDMA_IRQRL_INT28  */
#define EDMA_IRQRL_INT29_ISR_POS          82u /* Interrupt no. 82   - ivINT_EDMA_IRQRL_INT29  */
#define EDMA_IRQRL_INT30_ISR_POS          83u /* Interrupt no. 83   - ivINT_EDMA_IRQRL_INT30  */
#define EDMA_IRQRL_INT31_ISR_POS          84u /* Interrupt no. 84   - ivINT_EDMA_IRQRL_INT31  */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_85                   85u /* Interrupt no. 85    - RESERVED  */
#define RESERVED_POS_86                   86u /* Interrupt no. 86    - RESERVED  */
#define RESERVED_POS_87                   87u /* Interrupt no. 87    - RESERVED  */
#define RESERVED_POS_88                   88u /* Interrupt no. 88    - RESERVED  */
#define RESERVED_POS_89                   89u /* Interrupt no. 89    - RESERVED  */
#define RESERVED_POS_90                   90u /* Interrupt no. 90    - RESERVED  */
#define RESERVED_POS_91                   91u /* Interrupt no. 91    - RESERVED  */
#define RESERVED_POS_92                   92u /* Interrupt no. 92    - RESERVED  */
#define RESERVED_POS_93                   93u /* Interrupt no. 93    - RESERVED  */
#define RESERVED_POS_94                   94u /* Interrupt no. 94    - RESERVED  */
#define RESERVED_POS_95                   95u /* Interrupt no. 95    - RESERVED  */
#define RESERVED_POS_96                   96u /* Interrupt no. 96    - RESERVED  */
#define RESERVED_POS_97                   97u /* Interrupt no. 97    - RESERVED  */
#define RESERVED_POS_98                   98u /* Interrupt no. 98    - RESERVED  */
#define RESERVED_POS_99                   99u /* Interrupt no. 99    - RESERVED  */
#define RESERVED_POS_100                 100u /* Interrupt no. 100   - RESERVED  */
#define RESERVED_POS_101                 101u /* Interrupt no. 101   - RESERVED  */
#define RESERVED_POS_102                 102u /* Interrupt no. 102   - RESERVED  */
#define RESERVED_POS_103                 103u /* Interrupt no. 103   - RESERVED  */
#define RESERVED_POS_104                 104u /* Interrupt no. 104   - RESERVED  */
#define RESERVED_POS_105                 105u /* Interrupt no. 105   - RESERVED  */
#define RESERVED_POS_106                 106u /* Interrupt no. 106   - RESERVED  */
#define RESERVED_POS_107                 107u /* Interrupt no. 107   - RESERVED  */
#define RESERVED_POS_108                 108u /* Interrupt no. 108   - RESERVED  */
#define RESERVED_POS_109                 109u /* Interrupt no. 109   - RESERVED  */
#define RESERVED_POS_110                 110u /* Interrupt no. 110   - RESERVED  */
#define RESERVED_POS_111                 111u /* Interrupt no. 111   - RESERVED  */
#define RESERVED_POS_112                 112u /* Interrupt no. 112   - RESERVED  */
#define RESERVED_POS_113                 113u /* Interrupt no. 113   - RESERVED  */
#define RESERVED_POS_114                 114u /* Interrupt no. 114   - RESERVED  */
#define RESERVED_POS_115                 115u /* Interrupt no. 115   - RESERVED  */
#define RESERVED_POS_116                 116u /* Interrupt no. 116   - RESERVED  */
#define RESERVED_POS_117                 117u /* Interrupt no. 117   - RESERVED  */
#define RESERVED_POS_118                 118u /* Interrupt no. 118   - RESERVED  */
#define RESERVED_POS_119                 119u /* Interrupt no. 119   - RESERVED  */
#define RESERVED_POS_120                 120u /* Interrupt no. 120   - RESERVED  */
#define RESERVED_POS_121                 121u /* Interrupt no. 121   - RESERVED  */
#define RESERVED_POS_122                 122u /* Interrupt no. 122   - RESERVED  */
#define RESERVED_POS_123                 123u /* Interrupt no. 123   - RESERVED  */
#define RESERVED_POS_124                 124u /* Interrupt no. 124   - RESERVED  */
#define RESERVED_POS_125                 125u /* Interrupt no. 125   - RESERVED  */
#define RESERVED_POS_126                 126u /* Interrupt no. 126   - RESERVED  */
#define RESERVED_POS_127                 127u /* Interrupt no. 127   - RESERVED  */
#define RESERVED_POS_128                 128u /* Interrupt no. 128   - RESERVED  */
#define RESERVED_POS_129                 129u /* Interrupt no. 129   - RESERVED  */
#define RESERVED_POS_130                 130u /* Interrupt no. 130   - RESERVED  */
#define RESERVED_POS_131                 131u /* Interrupt no. 131   - RESERVED  */
#define RESERVED_POS_132                 132u /* Interrupt no. 132   - RESERVED  */
#define RESERVED_POS_133                 133u /* Interrupt no. 133   - RESERVED  */
#define RESERVED_POS_134                 134u /* Interrupt no. 134   - RESERVED  */
#define RESERVED_POS_135                 135u /* Interrupt no. 135   - RESERVED  */
#define RESERVED_POS_136                 136u /* Interrupt no. 136   - RESERVED  */
#define RESERVED_POS_137                 137u /* Interrupt no. 137   - RESERVED  */
#define RESERVED_POS_138                 138u /* Interrupt no. 138   - RESERVED  */
#define RESERVED_POS_139                 139u /* Interrupt no. 139   - RESERVED  */
#define RESERVED_POS_140                 140u /* Interrupt no. 140   - RESERVED  */
#define RESERVED_POS_141                 141u /* Interrupt no. 141   - RESERVED  */
#define RESERVED_POS_142                 142u /* Interrupt no. 142   - RESERVED  */
#define RESERVED_POS_143                 143u /* Interrupt no. 143   - RESERVED  */
#define RESERVED_POS_144                 144u /* Interrupt no. 144   - RESERVED  */
#define RESERVED_POS_145                 145u /* Interrupt no. 145   - RESERVED  */
#define RESERVED_POS_146                 146u /* Interrupt no. 146   - RESERVED  */
#define RESERVED_POS_147                 147u /* Interrupt no. 147   - RESERVED  */
#define RESERVED_POS_148                 148u /* Interrupt no. 148   - RESERVED  */
#define RESERVED_POS_149                 149u /* Interrupt no. 149   - RESERVED  */
#define RESERVED_POS_150                 150u /* Interrupt no. 150   - RESERVED  */
#define RESERVED_POS_151                 151u /* Interrupt no. 151   - RESERVED  */
#define RESERVED_POS_152                 152u /* Interrupt no. 152   - RESERVED  */
#define RESERVED_POS_153                 153u /* Interrupt no. 153   - RESERVED  */
#define RESERVED_POS_154                 154u /* Interrupt no. 154   - RESERVED  */
#define RESERVED_POS_155                 155u /* Interrupt no. 155   - RESERVED  */
#define RESERVED_POS_156                 156u /* Interrupt no. 156   - RESERVED  */
#define RESERVED_POS_157                 157u /* Interrupt no. 157   - RESERVED  */
#define RESERVED_POS_158                 158u /* Interrupt no. 158   - RESERVED  */
#define RESERVED_POS_159                 159u /* Interrupt no. 159   - RESERVED  */
#define RESERVED_POS_160                 160u /* Interrupt no. 160   - RESERVED  */
#define RESERVED_POS_161                 161u /* Interrupt no. 161   - RESERVED  */
#define RESERVED_POS_162                 162u /* Interrupt no. 162   - RESERVED  */
#define RESERVED_POS_163                 163u /* Interrupt no. 163   - RESERVED  */
#define RESERVED_POS_164                 164u /* Interrupt no. 164   - RESERVED  */
#define RESERVED_POS_165                 165u /* Interrupt no. 165   - RESERVED  */
#define RESERVED_POS_166                 166u /* Interrupt no. 166   - RESERVED  */
#define RESERVED_POS_167                 167u /* Interrupt no. 167   - RESERVED  */
#define RESERVED_POS_168                 168u /* Interrupt no. 168   - RESERVED  */
#define RESERVED_POS_169                 169u /* Interrupt no. 169   - RESERVED  */
#define RESERVED_POS_170                 170u /* Interrupt no. 170   - RESERVED  */
#define RESERVED_POS_171                 171u /* Interrupt no. 171   - RESERVED  */
#define RESERVED_POS_172                 172u /* Interrupt no. 172   - RESERVED  */
#define RESERVED_POS_173                 173u /* Interrupt no. 173   - RESERVED  */
#define RESERVED_POS_174                 174u /* Interrupt no. 174   - RESERVED  */
#define RESERVED_POS_175                 175u /* Interrupt no. 175   - RESERVED  */
#define RESERVED_POS_176                 176u /* Interrupt no. 176   - RESERVED  */
#define RESERVED_POS_177                 177u /* Interrupt no. 177   - RESERVED  */
#define RESERVED_POS_178                 178u /* Interrupt no. 178   - RESERVED  */
#define RESERVED_POS_179                 179u /* Interrupt no. 179   - RESERVED  */
#define RESERVED_POS_180                 180u /* Interrupt no. 180   - RESERVED  */
#define RESERVED_POS_181                 181u /* Interrupt no. 181   - RESERVED  */
#define RESERVED_POS_182                 182u /* Interrupt no. 182   - RESERVED  */
#define RESERVED_POS_183                 183u /* Interrupt no. 183   - RESERVED  */
#define RESERVED_POS_184                 184u /* Interrupt no. 184   - RESERVED  */
/************************************************************************************************/
/*************************************** FLASH ISR ***********************************************/
/************************************************************************************************/
#define FLASH_SRAM_ECC_INT_ISR_POS       185u /* Interrupt no. 185  - ivINT_FLASH_ECC        */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_186                 186u /* Interrupt no. 186   - RESERVED  */
#define RESERVED_POS_187                 187u /* Interrupt no. 187   - RESERVED  */
#define RESERVED_POS_188                 188u /* Interrupt no. 188   - RESERVED  */
#define RESERVED_POS_189                 189u /* Interrupt no. 189   - RESERVED  */
#define RESERVED_POS_190                 190u /* Interrupt no. 190   - RESERVED  */
#define RESERVED_POS_191                 191u /* Interrupt no. 191   - RESERVED  */
#define RESERVED_POS_192                 192u /* Interrupt no. 192   - RESERVED  */
#define RESERVED_POS_193                 193u /* Interrupt no. 193   - RESERVED  */
#define RESERVED_POS_194                 194u /* Interrupt no. 194   - RESERVED  */
#define RESERVED_POS_195                 195u /* Interrupt no. 195   - RESERVED  */
#define RESERVED_POS_196                 196u /* Interrupt no. 196   - RESERVED  */
#define RESERVED_POS_197                 197u /* Interrupt no. 197   - RESERVED  */
#define RESERVED_POS_198                 198u /* Interrupt no. 198   - RESERVED  */
#define RESERVED_POS_199                 199u /* Interrupt no. 199   - RESERVED  */
#define RESERVED_POS_200                 200u /* Interrupt no. 200   - RESERVED  */
#define RESERVED_POS_201                 201u /* Interrupt no. 201   - RESERVED  */
#define RESERVED_POS_202                 202u /* Interrupt no. 202   - RESERVED  */
#define RESERVED_POS_203                 203u /* Interrupt no. 203   - RESERVED  */
#define RESERVED_POS_204                 204u /* Interrupt no. 204   - RESERVED  */
#define RESERVED_POS_205                 205u /* Interrupt no. 205   - RESERVED  */
#define RESERVED_POS_206                 206u /* Interrupt no. 206   - RESERVED  */
#define RESERVED_POS_207                 207u /* Interrupt no. 207   - RESERVED  */
#define RESERVED_POS_208                 208u /* Interrupt no. 208   - RESERVED  */
#define RESERVED_POS_209                 209u /* Interrupt no. 209   - RESERVED  */
#define RESERVED_POS_210                 210u /* Interrupt no. 210   - RESERVED  */
#define RESERVED_POS_211                 211u /* Interrupt no. 211   - RESERVED  */
#define RESERVED_POS_212                 212u /* Interrupt no. 212   - RESERVED  */
#define RESERVED_POS_213                 213u /* Interrupt no. 213   - RESERVED  */
#define RESERVED_POS_214                 214u /* Interrupt no. 214   - RESERVED  */
#define RESERVED_POS_215                 215u /* Interrupt no. 215   - RESERVED  */
#define RESERVED_POS_216                 216u /* Interrupt no. 216   - RESERVED  */
#define RESERVED_POS_217                 217u /* Interrupt no. 217   - RESERVED  */
/************************************************************************************************/
/*************************************** ETHERNET ISR *******************************************/
/************************************************************************************************/
#define ETH_EIR_TX_ISR_POS               218u /* Interrupt no. 218   -   */
#define ETH_EIR_RX_ISR_POS               219u /* Interrupt no. 219   -   */
#define ETH_EIR_COMB_ISR_POS             220u /* Interrupt no. 220   -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_221                 221u /* Interrupt no. 221   - RESERVED  */
#define RESERVED_POS_222                 222u /* Interrupt no. 222   - RESERVED  */
#define RESERVED_POS_223                 223u /* Interrupt no. 223   - RESERVED  */
#define RESERVED_POS_224                 224u /* Interrupt no. 224   - RESERVED  */
#define RESERVED_POS_225                 225u /* Interrupt no. 225   - RESERVED  */
/************************************************************************************************/
/*************************************** PIT-RTI ISR **********************************************/
/************************************************************************************************/
#define PIT_INT0_PIT0_ISR_POS            226u /* Interrupt no. 226  -        */
#define PIT_INT1_PIT0_ISR_POS            227u /* Interrupt no. 227  -        */
#define PIT_INT2_PIT0_ISR_POS            228u /* Interrupt no. 228  -        */
#define PIT_INT3_PIT0_ISR_POS            229u /* Interrupt no. 229  -        */
#define PIT_INT4_PIT0_ISR_POS            230u /* Interrupt no. 230  -        */
#define PIT_INT5_PIT0_ISR_POS            231u /* Interrupt no. 231  -        */
#define RESERVED_POS_232                 232u /* Interrupt no. 232  - RESERVED  */
#define RESERVED_POS_233                 233u /* Interrupt no. 233  - RESERVED  */
#define RESERVED_POS_234                 234u /* Interrupt no. 234  - RESERVED  */
#define RESERVED_POS_235                 235u /* Interrupt no. 235  - RESERVED  */
#define RESERVED_POS_236                 236u /* Interrupt no. 236  - RESERVED  */
#define RESERVED_POS_237                 237u /* Interrupt no. 237  - RESERVED  */
#define RESERVED_POS_238                 238u /* Interrupt no. 238  - RESERVED  */
#define PIT_RTI_PIT0_ISR_POS             239u /* Interrupt no. 239  -        */
#define PIT_INT0_PIT1_ISR_POS            240u /* Interrupt no. 240  -        */
#define PIT_INT1_PIT1_ISR_POS            241u /* Interrupt no. 241  -        */
/************************************************************************************************/
/*************************************** XOSC ISR **********************************************/
/************************************************************************************************/
#define XOSC_CTL_ISR_POS                 242u /* Interrupt no. 242   - ivFMPLL_SYNSR_LOCF  */
/************************************************************************************************/
/*************************************** SIUL ISR *************************************************/
/************************************************************************************************/
#define SIUL2_COMB_EXT0_ISR_POS           242u /* Interrupt no. 243   - ivINT_SIU_OSR_OVF15_0  */
#define SIUL2_COMB_EXT1_ISR_POS           243u /* Interrupt no. 244   - ivINT_SIU_EISR_EIF0  */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_245                  245u /* Interrupt no. 245    - RESERVED  */
#define RESERVED_POS_246                  246u /* Interrupt no. 246    - RESERVED  */
#define RESERVED_POS_247                  247u /* Interrupt no. 247    - RESERVED  */
#define RESERVED_POS_248                  248u /* Interrupt no. 248    - RESERVED  */
#define RESERVED_POS_249                  249u /* Interrupt no. 249    - RESERVED  */
#define RESERVED_POS_250                  250u /* Interrupt no. 250    - RESERVED  */
/************************************************************************************************/
/*************************************** MC ISR *************************************************/
/************************************************************************************************/
#define MC_ME_SAFE_ISR_POS                251u /* Interrupt no. 251   -   */
#define MC_ME_MTC_ISR_POS                 252u /* Interrupt no. 252   -   */
#define MC_ME_IMODE_ISR_POS               253u /* Interrupt no. 253   -   */
#define MC_ME_ICONF_ISR_POS               254u /* Interrupt no. 254   -   */
#define MC_RGM_ISR_POS                    255u /* Interrupt no. 255   -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_256                  256u /* Interrupt no. 256    - RESERVED  */
#define RESERVED_POS_257                  257u /* Interrupt no. 257    - RESERVED  */
#define RESERVED_POS_258                  258u /* Interrupt no. 258    - RESERVED  */
/************************************************************************************************/
/*************************************** DSPI ISR ***********************************************/
/************************************************************************************************/
#define DSPI_0_ISR_TFUF_RFOF_ISR_POS      259u /* Interrupt no. 259  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_0_ISR_EOQF_ISR_POS           260u /* Interrupt no. 260  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_0_ISR_TFFF_ISR_POS           261u /* Interrupt no. 261  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_0_ISR_TCF_ISR_POS            262u /* Interrupt no. 262  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_0_ISR_RFDF_ISR_POS           263u /* Interrupt no. 263  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDTCF_SPITCF_ISR_POS  264u /* Interrupt no. 264  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_CMDFFF_ISR_POS         265u /* Interrupt no. 265  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_0_ISR_SPEF_ISR_POS           266u /* Interrupt no. 266  - ivINT_DSPI_B_ISR_RFDF  */
#define RESERVED_POS_267                  267u /* Interrupt no. 267  - RESERVED  */
#define DSPI_1_ISR_TFUF_RFOF_ISR_POS      268u /* Interrupt no. 268  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_1_ISR_EOQF_ISR_POS           269u /* Interrupt no. 269  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_1_ISR_TFFF_ISR_POS           270u /* Interrupt no. 270  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_1_ISR_TCF_ISR_POS            271u /* Interrupt no. 271  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_1_ISR_RFDF_ISR_POS           272u /* Interrupt no. 272  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDTCF_SPITCF_ISR_POS  273u /* Interrupt no. 273  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_CMDFFF_ISR_POS         274u /* Interrupt no. 274  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_1_ISR_SPEF_ISR_POS           275u /* Interrupt no. 275  - ivINT_DSPI_B_ISR_RFDF  */
#define RESERVED_POS_276                  276u /* Interrupt no. 276  - RESERVED  */
#define DSPI_2_ISR_TFUF_RFOF_ISR_POS      277u /* Interrupt no. 277  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_2_ISR_EOQF_ISR_POS           278u /* Interrupt no. 278  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_2_ISR_TFFF_ISR_POS           279u /* Interrupt no. 279  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_2_ISR_TCF_ISR_POS            280u /* Interrupt no. 280  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_2_ISR_RFDF_ISR_POS           281u /* Interrupt no. 281  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDTCF_SPITCF_ISR_POS  282u /* Interrupt no. 282  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_CMDFFF_ISR_POS         283u /* Interrupt no. 283  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_2_ISR_SPEF_ISR_POS           284u /* Interrupt no. 284  - ivINT_DSPI_B_ISR_RFDF  */
#define RESERVED_POS_285                  285u /* Interrupt no. 285  - RESERVED  */
#define RESERVED_POS_286                  286u /* Interrupt no. 286  - RESERVED  */
#define RESERVED_POS_287                  287u /* Interrupt no. 287  - RESERVED  */
#define RESERVED_POS_288                  288u /* Interrupt no. 288  - RESERVED  */
#define RESERVED_POS_289                  289u /* Interrupt no. 289  - RESERVED  */
#define RESERVED_POS_290                  290u /* Interrupt no. 290  - RESERVED  */
#define RESERVED_POS_291                  291u /* Interrupt no. 291  - RESERVED  */
#define RESERVED_POS_292                  292u /* Interrupt no. 292  - RESERVED  */
#define RESERVED_POS_293                  293u /* Interrupt no. 293  - RESERVED  */
#define RESERVED_POS_294                  294u /* Interrupt no. 294  - RESERVED  */
#define DSPI_4_ISR_TFUF_RFOF_ISR_POS      295u /* Interrupt no. 295  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_4_ISR_EOQF_ISR_POS           296u /* Interrupt no. 296  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_4_ISR_TFFF_ISR_POS           297u /* Interrupt no. 297  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_4_ISR_TCF_ISR_POS            298u /* Interrupt no. 298  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_4_ISR_RFDF_ISR_POS           299u /* Interrupt no. 299  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDTCF_SPITCF_ISR_POS  300u /* Interrupt no. 300  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_CMDFFF_DSITCF_ISR_POS  301u /* Interrupt no. 301  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_SPEF_DPEF_ISR_POS      302u /* Interrupt no. 302  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_4_ISR_DDIF_ISR_POS           303u /* Interrupt no. 303  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_TFUF_RFOF_ISR_POS      304u /* Interrupt no. 304  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
#define DSPI_5_ISR_EOQF_ISR_POS           305u /* Interrupt no. 305  - ivINT_DSPI_B_ISR_EOQF  */
#define DSPI_5_ISR_TFFF_ISR_POS           306u /* Interrupt no. 306  - ivINT_DSPI_B_ISR_TFFF  */
#define DSPI_5_ISR_TCF_ISR_POS            307u /* Interrupt no. 307  - ivINT_DSPI_B_ISR_TCF  */
#define DSPI_5_ISR_RFDF_ISR_POS           308u /* Interrupt no. 308  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDTCF_SPITCF_ISR_POS  309u /* Interrupt no. 309  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_CMDFFF_DSITCF_ISR_POS  310u /* Interrupt no. 310  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_SPEF_DPEF_ISR_POS      311u /* Interrupt no. 311  - ivINT_DSPI_B_ISR_RFDF  */
#define DSPI_5_ISR_DDIF_ISR_POS           312u /* Interrupt no. 312  - ivINT_DSPI_B_ISR_RFDF  */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_313                  313u /* Interrupt no. 313    - RESERVED  */
#define RESERVED_POS_314                  314u /* Interrupt no. 314    - RESERVED  */
#define RESERVED_POS_315                  315u /* Interrupt no. 315    - RESERVED  */
#define RESERVED_POS_316                  316u /* Interrupt no. 316    - RESERVED  */
#define RESERVED_POS_317                  317u /* Interrupt no. 317    - RESERVED  */
#define RESERVED_POS_318                  318u /* Interrupt no. 318    - RESERVED  */
#define RESERVED_POS_319                  319u /* Interrupt no. 319    - RESERVED  */
#define RESERVED_POS_320                  320u /* Interrupt no. 320    - RESERVED  */
#define RESERVED_POS_321                  321u /* Interrupt no. 321    - RESERVED  */
#define RESERVED_POS_322                  322u /* Interrupt no. 322    - RESERVED  */
#define RESERVED_POS_323                  323u /* Interrupt no. 323    - RESERVED  */
#define RESERVED_POS_324                  324u /* Interrupt no. 324    - RESERVED  */
#define RESERVED_POS_325                  325u /* Interrupt no. 325    - RESERVED  */
#define RESERVED_POS_326                  326u /* Interrupt no. 326    - RESERVED  */
#define RESERVED_POS_327                  327u /* Interrupt no. 327    - RESERVED  */
#define RESERVED_POS_328                  328u /* Interrupt no. 328    - RESERVED  */
#define RESERVED_POS_329                  329u /* Interrupt no. 329    - RESERVED  */
#define RESERVED_POS_330                  330u /* Interrupt no. 330    - RESERVED  */
#define RESERVED_POS_331                  331u /* Interrupt no. 331    - RESERVED  */
#define RESERVED_POS_332                  332u /* Interrupt no. 332    - RESERVED  */
#define RESERVED_POS_333                  333u /* Interrupt no. 333    - RESERVED  */
#define RESERVED_POS_334                  334u /* Interrupt no. 334    - RESERVED  */
#define RESERVED_POS_335                  335u /* Interrupt no. 335    - RESERVED  */
#define RESERVED_POS_336                  336u /* Interrupt no. 336    - RESERVED  */
#define RESERVED_POS_337                  337u /* Interrupt no. 337    - RESERVED  */
#define RESERVED_POS_338                  338u /* Interrupt no. 338    - RESERVED  */
#define RESERVED_POS_339                  339u /* Interrupt no. 339    - RESERVED  */
#define RESERVED_POS_340                  340u /* Interrupt no. 340    - RESERVED  */
#define RESERVED_POS_341                  341u /* Interrupt no. 341    - RESERVED  */
#define RESERVED_POS_342                  342u /* Interrupt no. 342    - RESERVED  */
#define RESERVED_POS_343                  343u /* Interrupt no. 343    - RESERVED  */
#define RESERVED_POS_344                  344u /* Interrupt no. 344    - RESERVED  */
#define RESERVED_POS_345                  345u /* Interrupt no. 345    - RESERVED  */
#define RESERVED_POS_346                  346u /* Interrupt no. 346    - RESERVED  */
#define RESERVED_POS_347                  347u /* Interrupt no. 347    - RESERVED  */
#define RESERVED_POS_348                  348u /* Interrupt no. 348    - RESERVED  */
#define RESERVED_POS_349                  349u /* Interrupt no. 349    - RESERVED  */
#define RESERVED_POS_350                  350u /* Interrupt no. 350    - RESERVED  */
#define RESERVED_POS_351                  351u /* Interrupt no. 351    - RESERVED  */
#define RESERVED_POS_352                  352u /* Interrupt no. 352    - RESERVED  */
#define RESERVED_POS_353                  353u /* Interrupt no. 353    - RESERVED  */
#define RESERVED_POS_354                  354u /* Interrupt no. 354    - RESERVED  */
#define RESERVED_POS_355                  355u /* Interrupt no. 355    - RESERVED  */
#define RESERVED_POS_356                  356u /* Interrupt no. 356    - RESERVED  */
#define RESERVED_POS_357                  357u /* Interrupt no. 357    - RESERVED  */
#define RESERVED_POS_358                  358u /* Interrupt no. 358    - RESERVED  */
#define RESERVED_POS_359                  359u /* Interrupt no. 359    - RESERVED  */
#define RESERVED_POS_360                  360u /* Interrupt no. 360    - RESERVED  */
#define RESERVED_POS_361                  361u /* Interrupt no. 361    - RESERVED  */
#define RESERVED_POS_362                  362u /* Interrupt no. 362    - RESERVED  */
#define RESERVED_POS_363                  363u /* Interrupt no. 363    - RESERVED  */
#define RESERVED_POS_364                  364u /* Interrupt no. 364    - RESERVED  */
#define RESERVED_POS_365                  365u /* Interrupt no. 365    - RESERVED  */
#define RESERVED_POS_366                  366u /* Interrupt no. 366    - RESERVED  */
#define RESERVED_POS_367                  367u /* Interrupt no. 367    - RESERVED  */
#define RESERVED_POS_368                  368u /* Interrupt no. 368    - RESERVED  */
#define RESERVED_POS_369                  369u /* Interrupt no. 369    - RESERVED  */
#define RESERVED_POS_370                  370u /* Interrupt no. 370    - RESERVED  */
#define RESERVED_POS_371                  371u /* Interrupt no. 371    - RESERVED  */
#define RESERVED_POS_372                  372u /* Interrupt no. 372    - RESERVED  */
#define RESERVED_POS_373                  373u /* Interrupt no. 373    - RESERVED  */
#define RESERVED_POS_374                  374u /* Interrupt no. 374    - RESERVED  */
#define RESERVED_POS_375                  375u /* Interrupt no. 375    - RESERVED  */
/************************************************************************************************/
/*************************************** LINFLEX ISR ********************************************/
/************************************************************************************************/
#define LINFLEX_0_RX_COMB_ISR_POS         376u /* Interrupt no. 376    -   */
#define LINFLEX_0_TX_COMB_ISR_POS         377u /* Interrupt no. 377    -   */
#define LINFLEX_0_ERROR_COMB_ISR_POS      378u /* Interrupt no. 378    -   */
#define RESERVED_POS_379                  379u /* Interrupt no. 379    - RESERVED  */
#define LINFLEX_1_RX_COMB_ISR_POS         380u /* Interrupt no. 380    -   */
#define LINFLEX_1_TX_COMB_ISR_POS         381u /* Interrupt no. 381    -   */
#define LINFLEX_1_ERROR_COMB_ISR_POS      382u /* Interrupt no. 382    -   */
#define RESERVED_POS_383                  383u /* Interrupt no. 383    - RESERVED  */
#define LINFLEX_2_RX_COMB_ISR_POS         384u /* Interrupt no. 384    -   */
#define LINFLEX_2_TX_COMB_ISR_POS         385u /* Interrupt no. 385    -   */
#define LINFLEX_2_ERROR_COMB_ISR_POS      386u /* Interrupt no. 386    -   */
#define RESERVED_POS_387                  387u /* Interrupt no. 387    - RESERVED  */
#define RESERVED_POS_388                  388u /* Interrupt no. 388    - RESERVED  */
#define RESERVED_POS_389                  389u /* Interrupt no. 389    - RESERVED  */
#define RESERVED_POS_390                  390u /* Interrupt no. 390    - RESERVED  */
#define RESERVED_POS_391                  391u /* Interrupt no. 391    - RESERVED  */
#define RESERVED_POS_392                  392u /* Interrupt no. 392    - RESERVED  */
#define RESERVED_POS_393                  393u /* Interrupt no. 393    - RESERVED  */
#define RESERVED_POS_394                  394u /* Interrupt no. 394    - RESERVED  */
#define RESERVED_POS_395                  395u /* Interrupt no. 395    - RESERVED  */
#define RESERVED_POS_396                  396u /* Interrupt no. 396    - RESERVED  */
#define RESERVED_POS_397                  397u /* Interrupt no. 397    - RESERVED  */
#define RESERVED_POS_398                  398u /* Interrupt no. 398    - RESERVED  */
#define RESERVED_POS_399                  399u /* Interrupt no. 399    - RESERVED  */
#define RESERVED_POS_400                  400u /* Interrupt no. 400    - RESERVED  */
#define RESERVED_POS_401                  401u /* Interrupt no. 401    - RESERVED  */
#define RESERVED_POS_402                  402u /* Interrupt no. 402    - RESERVED  */
#define RESERVED_POS_403                  403u /* Interrupt no. 403    - RESERVED  */
#define RESERVED_POS_404                  404u /* Interrupt no. 404    - RESERVED  */
#define RESERVED_POS_405                  405u /* Interrupt no. 405    - RESERVED  */
#define RESERVED_POS_406                  406u /* Interrupt no. 406    - RESERVED  */
#define RESERVED_POS_407                  407u /* Interrupt no. 407    - RESERVED  */
#define RESERVED_POS_408                  408u /* Interrupt no. 408    - RESERVED  */
#define RESERVED_POS_409                  409u /* Interrupt no. 409    - RESERVED  */
#define RESERVED_POS_410                  410u /* Interrupt no. 410    - RESERVED  */
#define RESERVED_POS_411                  411u /* Interrupt no. 411    - RESERVED  */
#define RESERVED_POS_412                  412u /* Interrupt no. 412    - RESERVED  */
#define RESERVED_POS_413                  413u /* Interrupt no. 413    - RESERVED  */
#define RESERVED_POS_414                  414u /* Interrupt no. 414    - RESERVED  */
#define RESERVED_POS_415                  415u /* Interrupt no. 415    - RESERVED  */
#define RESERVED_POS_416                  416u /* Interrupt no. 416    - RESERVED  */
#define RESERVED_POS_417                  417u /* Interrupt no. 417    - RESERVED  */
#define RESERVED_POS_418                  418u /* Interrupt no. 418    - RESERVED  */
#define RESERVED_POS_419                  419u /* Interrupt no. 419    - RESERVED  */
#define RESERVED_POS_420                  420u /* Interrupt no. 420    - RESERVED  */
#define RESERVED_POS_421                  421u /* Interrupt no. 421    - RESERVED  */
#define RESERVED_POS_422                  422u /* Interrupt no. 422    - RESERVED  */
#define RESERVED_POS_423                  423u /* Interrupt no. 423    - RESERVED  */
#define RESERVED_POS_424                  424u /* Interrupt no. 424    - RESERVED  */
#define RESERVED_POS_425                  425u /* Interrupt no. 425    - RESERVED  */
#define RESERVED_POS_426                  426u /* Interrupt no. 426    - RESERVED  */
#define RESERVED_POS_427                  427u /* Interrupt no. 427    - RESERVED  */
#define RESERVED_POS_428                  428u /* Interrupt no. 428    - RESERVED  */
#define RESERVED_POS_429                  429u /* Interrupt no. 429    - RESERVED  */
#define RESERVED_POS_430                  430u /* Interrupt no. 430    - RESERVED  */
#define RESERVED_POS_431                  431u /* Interrupt no. 431    - RESERVED  */
#define LINFLEX_14_RX_COMB_ISR_POS        432u /* Interrupt no. 432    -   */
#define LINFLEX_14_TX_COMB_ISR_POS        433u /* Interrupt no. 433    -   */
#define LINFLEX_14_ERROR_COMB_ISR_POS     434u /* Interrupt no. 434    -   */
#define RESERVED_POS_435                  435u /* Interrupt no. 435    - RESERVED  */
#define LINFLEX_15_RX_COMB_ISR_POS        436u /* Interrupt no. 436    -   */
#define LINFLEX_15_TX_COMB_ISR_POS        437u /* Interrupt no. 437    -   */
#define LINFLEX_15_ERROR_COMB_ISR_POS     438u /* Interrupt no. 438    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_439                  439u /* Interrupt no. 439    - RESERVED  */
/************************************************************************************************/
/*************************************** I2C ISR ***********************************************/
/************************************************************************************************/
#define I2C_IBIF_IAAS_IBAL_ISR_POS        440u /* Interrupt no. 440    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_441                  441u /* Interrupt no. 441    - RESERVED  */
#define RESERVED_POS_442                  442u /* Interrupt no. 442    - RESERVED  */
#define RESERVED_POS_443                  443u /* Interrupt no. 443    - RESERVED  */
#define RESERVED_POS_444                  444u /* Interrupt no. 444    - RESERVED  */
#define RESERVED_POS_445                  445u /* Interrupt no. 445    - RESERVED  */
#define RESERVED_POS_446                  446u /* Interrupt no. 446    - RESERVED  */
#define RESERVED_POS_447                  447u /* Interrupt no. 447    - RESERVED  */
#define RESERVED_POS_448                  448u /* Interrupt no. 448    - RESERVED  */
#define RESERVED_POS_449                  449u /* Interrupt no. 449    - RESERVED  */
#define RESERVED_POS_450                  450u /* Interrupt no. 450    - RESERVED  */
#define RESERVED_POS_451                  451u /* Interrupt no. 451    - RESERVED  */
#define RESERVED_POS_452                  452u /* Interrupt no. 452    - RESERVED  */
/************************************************************************************************/
/*************************************** FLEXRAY ISR *********************************************/
/************************************************************************************************/
#define FLEXRAY_LRNE_ISR_POS               453u /* Interrupt no. 453 - ivINT_FlexRAY LRNE   */
#define FLEXRAY_LRCE_ISR_POS               454u /* Interrupt no. 454 - ivINT_FlexRAY LRCE   */
#define FLEXRAY_FAFAIF_ISR_POS             455u /* Interrupt no. 455 - ivINT_FlexRAY FAFAIF */
#define FLEXRAY_FAFBIF_ISR_POS             456u /* Interrupt no. 456 - ivINT_FlexRAY FAFBIF */
#define FLEXRAY_WUPIF_ISR_POS              457u /* Interrupt no. 457 - ivINT_FlexRAY WUPIF  */
#define FLEXRAY_PRIF_ISR_POS               458u /* Interrupt no. 458 - ivINT_FlexRAY PRIF   */
#define FLEXRAY_CHIF_ISR_POS               459u /* Interrupt no. 459 - ivINT_FlexRAY CHIF   */
#define FLEXRAY_TBIF_ISR_POS               460u /* Interrupt no. 460 - ivINT_FlexRAY TBIF   */
#define FLEXRAY_RBIF_ISR_POS               461u /* Interrupt no. 461 - ivINT_FlexRAY RBIF   */
#define FLEXRAY_MIF_ISR_POS                462u /* Interrupt no. 462 - ivINT_FlexRAY MIF    */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_463                  463u /* Interrupt no. 463    - RESERVED  */
#define RESERVED_POS_464                  464u /* Interrupt no. 464    - RESERVED  */
#define RESERVED_POS_465                  465u /* Interrupt no. 465    - RESERVED  */
#define RESERVED_POS_466                  466u /* Interrupt no. 466    - RESERVED  */
#define RESERVED_POS_467                  467u /* Interrupt no. 467    - RESERVED  */
#define RESERVED_POS_468                  468u /* Interrupt no. 468    - RESERVED  */
#define RESERVED_POS_469                  469u /* Interrupt no. 469    - RESERVED  */
#define RESERVED_POS_470                  470u /* Interrupt no. 470    - RESERVED  */
#define RESERVED_POS_471                  471u /* Interrupt no. 471    - RESERVED  */
#define RESERVED_POS_472                  472u /* Interrupt no. 472    - RESERVED  */
#define RESERVED_POS_473                  473u /* Interrupt no. 473    - RESERVED  */
#define RESERVED_POS_474                  474u /* Interrupt no. 474    - RESERVED  */
#define RESERVED_POS_475                  475u /* Interrupt no. 475    - RESERVED  */
#define RESERVED_POS_476                  476u /* Interrupt no. 476    - RESERVED  */
/************************************************************************************************/
/*************************************** GR ISR **********************************************/
/************************************************************************************************/
#define GR_VD_ISR_POS                     477u  /* Interrupt no. 477   - ivGR_VD  */
/************************************************************************************************/
/*************************************** EPR ISR **********************************************/
/************************************************************************************************/
#define EPR_TEMP_ISR_POS                  478u  /* Interrupt no. 478   - ivEPR_TEMP  */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_479                  479u /* Interrupt no. 479    - RESERVED  */
#define RESERVED_POS_480                  480u /* Interrupt no. 480    - RESERVED  */
#define RESERVED_POS_481                  481u /* Interrupt no. 481    - RESERVED  */
#define RESERVED_POS_482                  482u /* Interrupt no. 482    - RESERVED  */
#define RESERVED_POS_483                  483u /* Interrupt no. 483    - RESERVED  */
#define RESERVED_POS_484                  484u /* Interrupt no. 484    - RESERVED  */
#define RESERVED_POS_485                  485u /* Interrupt no. 485    - RESERVED  */
#define RESERVED_POS_486                  486u /* Interrupt no. 486    - RESERVED  */
#define RESERVED_POS_487                  487u /* Interrupt no. 487    - RESERVED  */
/************************************************************************************************/
/*************************************** FCCU ISR **********************************************/
/************************************************************************************************/
#define FCCU_ALRM_STAT_ISR_POS            488u  /* Interrupt no. 488   - ivFMPLL_SYNSR_LOCF  */
#define FCCU_CFG_TO_STAT_ISR_POS          489u  /* Interrupt no. 489   - ivFMPLL_SYNSR_LOLF  */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_490                  490u /* Interrupt no. 490    - RESERVED  */
#define RESERVED_POS_491                  491u /* Interrupt no. 491    - RESERVED  */
#define RESERVED_POS_492                  492u /* Interrupt no. 492    - RESERVED  */
#define RESERVED_POS_493                  493u /* Interrupt no. 493    - RESERVED  */
/************************************************************************************************/
/*************************************** STCU ISR **********************************************/
/************************************************************************************************/
#define STCU_LBIE_ISR_POS                 494u  /* Interrupt no. 494   - ivSTCU_LBIE  */
#define STCU_MBIE_ISR_POS                 495u  /* Interrupt no. 495   - ivSTCU_MBIE  */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_496                  496u /* Interrupt no. 496    - RESERVED  */
#define RESERVED_POS_497                  497u /* Interrupt no. 497    - RESERVED  */
#define RESERVED_POS_498                  498u /* Interrupt no. 498    - RESERVED  */
#define RESERVED_POS_499                  499u /* Interrupt no. 499    - RESERVED  */
#define RESERVED_POS_500                  500u /* Interrupt no. 500    - RESERVED  */
#define RESERVED_POS_501                  501u /* Interrupt no. 501    - RESERVED  */
#define RESERVED_POS_502                  502u /* Interrupt no. 502    - RESERVED  */
#define RESERVED_POS_503                  503u /* Interrupt no. 503    - RESERVED  */
#define RESERVED_POS_504                  504u /* Interrupt no. 504    - RESERVED  */
#define RESERVED_POS_505                  505u /* Interrupt no. 505    - RESERVED  */
#define RESERVED_POS_506                  506u /* Interrupt no. 506    - RESERVED  */
#define RESERVED_POS_507                  507u /* Interrupt no. 507    - RESERVED  */
#define RESERVED_POS_508                  508u /* Interrupt no. 508    - RESERVED  */
#define RESERVED_POS_509                  509u /* Interrupt no. 509    - RESERVED  */
#define RESERVED_POS_510                  510u /* Interrupt no. 510    - RESERVED  */
#define RESERVED_POS_511                  511u /* Interrupt no. 511    - RESERVED  */
#define RESERVED_POS_512                  512u /* Interrupt no. 512    - RESERVED  */
#define RESERVED_POS_513                  513u /* Interrupt no. 513    - RESERVED  */
#define RESERVED_POS_514                  514u /* Interrupt no. 514    - RESERVED  */
#define RESERVED_POS_515                  515u /* Interrupt no. 515    - RESERVED  */
#define RESERVED_POS_516                  516u /* Interrupt no. 516    - RESERVED  */
#define RESERVED_POS_517                  517u /* Interrupt no. 517    - RESERVED  */
#define RESERVED_POS_518                  518u /* Interrupt no. 518    - RESERVED  */
#define RESERVED_POS_519                  519u /* Interrupt no. 519    - RESERVED  */
#define RESERVED_POS_520                  520u /* Interrupt no. 520    - RESERVED  */
#define RESERVED_POS_521                  521u /* Interrupt no. 521    - RESERVED  */
#define RESERVED_POS_522                  522u /* Interrupt no. 522    - RESERVED  */
#define RESERVED_POS_523                  523u /* Interrupt no. 523    - RESERVED  */
#define RESERVED_POS_524                  524u /* Interrupt no. 524    - RESERVED  */
#define RESERVED_POS_525                  525u /* Interrupt no. 525    - RESERVED  */
#define RESERVED_POS_526                  526u /* Interrupt no. 526    - RESERVED  */
#define RESERVED_POS_527                  527u /* Interrupt no. 527    - RESERVED  */
/************************************************************************************************/
/*************************************** SAR ISR **********************************************/
/************************************************************************************************/
#define SAR_0_INT_ISR_POS                 528u /* Interrupt no. 528    -   */
#define RESERVED_POS_529                  529u /* Interrupt no. 529    - RESERVED  */
#define SAR_2_INT_ISR_POS                 530u /* Interrupt no. 530    -   */
#define RESERVED_POS_531                  531u /* Interrupt no. 531    - RESERVED  */
#define SAR_4_INT_ISR_POS                 532u /* Interrupt no. 532    -   */
#define RESERVED_POS_533                  533u /* Interrupt no. 533    - RESERVED  */
#define SAR_6_INT_ISR_POS                 534u /* Interrupt no. 534    -   */
#define RESERVED_POS_535                  535u /* Interrupt no. 535    - RESERVED  */
#define RESERVED_POS_536                  536u /* Interrupt no. 536    - RESERVED  */
#define RESERVED_POS_537                  537u /* Interrupt no. 537    - RESERVED  */
#define RESERVED_POS_538                  538u /* Interrupt no. 538    - RESERVED  */
#define RESERVED_POS_539                  539u /* Interrupt no. 539    - RESERVED  */
#define RESERVED_POS_540                  540u /* Interrupt no. 540    - RESERVED  */
#define RESERVED_POS_541                  541u /* Interrupt no. 541    - RESERVED  */
#define RESERVED_POS_542                  542u /* Interrupt no. 542    - RESERVED  */
#define SAR_B_INT_ISR_POS                 543u /* Interrupt no. 543    -   */
/************************************************************************************************/
/*************************************** SD ISR **********************************************/
/************************************************************************************************/
#define SD_0_INT_ISR_POS                  544u /* Interrupt no. 544  - ivSD_0_INT  */
#define RESERVED_POS_545                  545u /* Interrupt no. 545    - RESERVED  */
#define RESERVED_POS_546                  546u /* Interrupt no. 546    - RESERVED  */
#define SD_3_INT_ISR_POS                  547u /* Interrupt no. 547  - ivSD_3_INT  */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_548                  548u /* Interrupt no. 548    - RESERVED  */
#define RESERVED_POS_549                  549u /* Interrupt no. 549    - RESERVED  */
#define RESERVED_POS_550                  550u /* Interrupt no. 550    - RESERVED  */
#define RESERVED_POS_551                  551u /* Interrupt no. 551    - RESERVED  */
#define RESERVED_POS_552                  552u /* Interrupt no. 552    - RESERVED  */
#define RESERVED_POS_553                  553u /* Interrupt no. 553    - RESERVED  */
#define RESERVED_POS_554                  554u /* Interrupt no. 554    - RESERVED  */
#define RESERVED_POS_555                  555u /* Interrupt no. 555    - RESERVED  */
#define RESERVED_POS_556                  556u /* Interrupt no. 556    - RESERVED  */
#define RESERVED_POS_557                  557u /* Interrupt no. 557    - RESERVED  */
/************************************************************************************************/
/*************************************** SENT ISR **********************************************/
/************************************************************************************************/
#define SENT_0_FAST_COMB_ISR_POS          558u /* Interrupt no. 558    - ivSENT_0_FAST_COMB  */
#define SENT_0_SLOW_COMB_ISR_POS          559u /* Interrupt no. 559    -   */
#define SENT_0_GBL_ERROR_ISR_POS          560u /* Interrupt no. 560    -   */
#define SENT_1_SLOW_RDY_ISR_POS           562u /* Interrupt no. 562    -   */
#define SENT_1_FAST_RDY_ISR_POS           561u /* Interrupt no. 561    -   */
#define SENT_1_GBL_ERROR_ISR_POS          563u /* Interrupt no. 563    -   */
#define SENT_0_FMSG_0_ISR_POS             564u /* Interrupt no. 564    -   */
#define SENT_0_SMSG_0_ISR_POS             565u /* Interrupt no. 565    -   */
#define SENT_0_ERROR_0_ISR_POS            566u /* Interrupt no. 566    -   */
#define SENT_0_FMSG_1_ISR_POS             567u /* Interrupt no. 567    -   */
#define SENT_0_SMSG_1_ISR_POS             568u /* Interrupt no. 568    -   */
#define SENT_0_ERROR_1_ISR_POS            569u /* Interrupt no. 569    -   */
#define SENT_0_FMSG_2_ISR_POS             570u /* Interrupt no. 570    -   */
#define SENT_0_SMSG_2_ISR_POS             571u /* Interrupt no. 571    -   */
#define SENT_0_ERROR_2_ISR_POS            572u /* Interrupt no. 572    -   */
#define SENT_0_FMSG_3_ISR_POS             573u /* Interrupt no. 573    -   */
#define SENT_0_SMSG_3_ISR_POS             574u /* Interrupt no. 574    -   */
#define SENT_0_ERROR_3_ISR_POS            575u /* Interrupt no. 575    -   */
#define RESERVED_POS_576                  576u /* Interrupt no. 576    - RESERVED  */
#define RESERVED_POS_577                  577u /* Interrupt no. 577    - RESERVED  */
#define RESERVED_POS_578                  578u /* Interrupt no. 578    - RESERVED  */
#define SENT_1_FMSG_0_ISR_POS             579u /* Interrupt no. 579    -   */
#define SENT_1_SMSG_0_ISR_POS             580u /* Interrupt no. 580    -   */
#define SENT_1_ERROR_0_ISR_POS            581u /* Interrupt no. 581    -   */
#define SENT_1_FMSG_1_ISR_POS             582u /* Interrupt no. 582    -   */
#define SENT_1_SMSG_1_ISR_POS             583u /* Interrupt no. 583    -   */
#define SENT_1_ERROR_1_ISR_POS            584u /* Interrupt no. 584    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_585                  585u /* Interrupt no. 585    - RESERVED  */
#define RESERVED_POS_586                  586u /* Interrupt no. 586    - RESERVED  */
#define RESERVED_POS_587                  587u /* Interrupt no. 587    - RESERVED  */
#define RESERVED_POS_588                  588u /* Interrupt no. 588    - RESERVED  */
#define RESERVED_POS_589                  589u /* Interrupt no. 589    - RESERVED  */
#define RESERVED_POS_590                  590u /* Interrupt no. 590    - RESERVED  */
#define RESERVED_POS_591                  591u /* Interrupt no. 591    - RESERVED  */
#define RESERVED_POS_592                  592u /* Interrupt no. 592    - RESERVED  */
#define RESERVED_POS_593                  593u /* Interrupt no. 593    - RESERVED  */
#define RESERVED_POS_594                  594u /* Interrupt no. 594    - RESERVED  */
#define RESERVED_POS_595                  595u /* Interrupt no. 595    - RESERVED  */
#define RESERVED_POS_596                  596u /* Interrupt no. 596    - RESERVED  */
#define RESERVED_POS_597                  597u /* Interrupt no. 597    - RESERVED  */
#define RESERVED_POS_598                  598u /* Interrupt no. 598    - RESERVED  */
#define RESERVED_POS_599                  599u /* Interrupt no. 599    - RESERVED  */
#define RESERVED_POS_600                  600u /* Interrupt no. 600    - RESERVED  */
#define RESERVED_POS_601                  601u /* Interrupt no. 601    - RESERVED  */
#define RESERVED_POS_602                  602u /* Interrupt no. 602    - RESERVED  */
#define RESERVED_POS_603                  603u /* Interrupt no. 603    - RESERVED  */
#define RESERVED_POS_604                  604u /* Interrupt no. 604    - RESERVED  */
#define RESERVED_POS_605                  605u /* Interrupt no. 605    - RESERVED  */
#define RESERVED_POS_606                  606u /* Interrupt no. 606    - RESERVED  */
#define RESERVED_POS_607                  607u /* Interrupt no. 607    - RESERVED  */
#define RESERVED_POS_608                  608u /* Interrupt no. 608    - RESERVED  */
#define RESERVED_POS_609                  609u /* Interrupt no. 609    - RESERVED  */
#define RESERVED_POS_610                  610u /* Interrupt no. 610    - RESERVED  */
#define RESERVED_POS_611                  611u /* Interrupt no. 611    - RESERVED  */
/************************************************************************************************/
/*************************************** PSI ISR **********************************************/
/************************************************************************************************/
#define PSI5_0_DMA_0_ISR_POS              612u/* Interrupt no. 612    -   */
#define PSI5_0_GEN_0_ISR_POS              613u/* Interrupt no. 613    -   */
#define PSI5_0_NEW_MSG_0_ISR_POS          614u/* Interrupt no. 614    -   */
#define PSI5_0_MSG_OW_0_ISR_POS           615u/* Interrupt no. 615    -   */
#define PSI5_0_ERROR_COMB_0_ISR_POS       616u/* Interrupt no. 616    -   */
#define PSI5_0_GLOBAL_0_ISR_POS           617u/* Interrupt no. 617    -   */
#define RESERVED_POS_618                  618u/* Interrupt no. 618    - RESERVED  */
#define RESERVED_POS_619                  619u/* Interrupt no. 619    - RESERVED  */
#define RESERVED_POS_620                  620u/* Interrupt no. 620    - RESERVED  */
#define RESERVED_POS_621                  621u/* Interrupt no. 621    - RESERVED  */
#define RESERVED_POS_622                  622u/* Interrupt no. 622    - RESERVED  */
#define RESERVED_POS_623                  623u/* Interrupt no. 623    - RESERVED  */
#define PSI5_1_DMA_0_ISR_POS              624u/* Interrupt no. 624    -   */
#define PSI5_1_GEN_0_ISR_POS              625u/* Interrupt no. 625    -   */
#define PSI5_1_NEW_MSG_0_ISR_POS          626u/* Interrupt no. 626    -   */
#define PSI5_1_MSG_OW_0_ISR_POS           627u/* Interrupt no. 627    -   */
#define PSI5_1_ERROR_COMB_0_ISR_POS       628u/* Interrupt no. 628    -   */
#define PSI5_1_GLOBAL_0_ISR_POS           629u/* Interrupt no. 629    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_630                  630u /* Interrupt no. 630    - RESERVED  */
#define RESERVED_POS_631                  631u /* Interrupt no. 631    - RESERVED  */
#define RESERVED_POS_632                  632u /* Interrupt no. 632    - RESERVED  */
#define RESERVED_POS_633                  633u /* Interrupt no. 633    - RESERVED  */
#define RESERVED_POS_634                  634u /* Interrupt no. 634    - RESERVED  */
#define RESERVED_POS_635                  635u /* Interrupt no. 635    - RESERVED  */
#define RESERVED_POS_636                  636u /* Interrupt no. 636    - RESERVED  */
#define RESERVED_POS_637                  637u /* Interrupt no. 637    - RESERVED  */
#define RESERVED_POS_638                  638u /* Interrupt no. 638    - RESERVED  */
#define RESERVED_POS_639                  639u /* Interrupt no. 639    - RESERVED  */
#define RESERVED_POS_640                  640u /* Interrupt no. 640    - RESERVED  */
#define RESERVED_POS_641                  641u /* Interrupt no. 641    - RESERVED  */
#define RESERVED_POS_642                  642u /* Interrupt no. 642    - RESERVED  */
#define RESERVED_POS_643                  643u /* Interrupt no. 643    - RESERVED  */
#define RESERVED_POS_644                  644u /* Interrupt no. 644    - RESERVED  */
#define RESERVED_POS_645                  645u /* Interrupt no. 645    - RESERVED  */
#define RESERVED_POS_646                  646u /* Interrupt no. 646    - RESERVED  */
#define RESERVED_POS_647                  647u /* Interrupt no. 647    - RESERVED  */
#define RESERVED_POS_648                  648u /* Interrupt no. 648    - RESERVED  */
#define RESERVED_POS_649                  649u /* Interrupt no. 649    - RESERVED  */
#define RESERVED_POS_650                  650u /* Interrupt no. 650    - RESERVED  */
#define RESERVED_POS_651                  651u /* Interrupt no. 651    - RESERVED  */
#define RESERVED_POS_652                  652u /* Interrupt no. 652    - RESERVED  */
#define RESERVED_POS_653                  653u /* Interrupt no. 653    - RESERVED  */
/************************************************************************************************/
/*************************************** SIPI ISR **********************************************/
/************************************************************************************************/
#define SIPI_ERROR_COMB_ISR_POS           654u /* Interrupt no. 654    -   */
#define SIPI_CRC_ERROR_ISR_POS            655u /* Interrupt no. 655    -   */
#define SIPI_CH0_RX_ISR_POS               656u /* Interrupt no. 656    -   */
#define SIPI_CH1_RX_ISR_POS               657u /* Interrupt no. 657    -   */
#define SIPI_CH2_RX_ISR_POS               658u /* Interrupt no. 658    -   */
#define SIPI_CH3_RX_ISR_POS               659u /* Interrupt no. 659    -   */
#define SIPI_EVENT_COMB_ISR_POS           660u /* Interrupt no. 660    -   */
/************************************************************************************************/
/*************************************** LFAST ISR **********************************************/
/************************************************************************************************/
#define LFAST_0_TX_ISR_POS                661u/* Interrupt no. 661    -   */
#define LFAST_0_TX_ERROR_ISR_POS          662u/* Interrupt no. 662    -   */
#define LFAST_0_RX_ISR_POS                663u/* Interrupt no. 663    -   */
#define LFAST_0_RX_ERROR_ISR_POS          664u/* Interrupt no. 664    -   */
#define LFAST_0_ICLC_RX_ISR_POS           665u/* Interrupt no. 665    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_666                  666u /* Interrupt no. 666    - RESERVED  */
#define RESERVED_POS_667                  667u /* Interrupt no. 667    - RESERVED  */
#define RESERVED_POS_668                  668u /* Interrupt no. 668    - RESERVED  */
#define RESERVED_POS_669                  669u /* Interrupt no. 669    - RESERVED  */
#define RESERVED_POS_670                  670u /* Interrupt no. 670    - RESERVED  */
#define RESERVED_POS_671                  671u /* Interrupt no. 671    - RESERVED  */
#define RESERVED_POS_672                  672u /* Interrupt no. 672    - RESERVED  */
#define RESERVED_POS_673                  673u /* Interrupt no. 673    - RESERVED  */
/************************************************************************************************/
/*************************************** JTAG ISR ***********************************************/
/************************************************************************************************/
#define JTAG_GM_ISR_POS                   674u /* Interrupt no. 674    -   */
#define JTAG_DC_ISR_POS                   675u /* Interrupt no. 675    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_676                  676u /* Interrupt no. 676    - RESERVED  */
/************************************************************************************************/
/*************************************** M_TTCAN ISR ********************************************/
/************************************************************************************************/
#define M_TTCAN_LINE0_ISR_POS             677u /* Interrupt no. 677    -   */
#define M_TTCAN_LINE1_ISR_POS             678u /* Interrupt no. 678    -   */
#define M_TTCAN_RTMI_ISR_POS              679u /* Interrupt no. 679    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_680                  680u /* Interrupt no. 680    - RESERVED  */
#define RESERVED_POS_681                  681u /* Interrupt no. 681    - RESERVED  */
#define RESERVED_POS_682                  682u /* Interrupt no. 682    - RESERVED  */
#define RESERVED_POS_683                  683u /* Interrupt no. 683    - RESERVED  */
#define RESERVED_POS_684                  684u /* Interrupt no. 684    - RESERVED  */
#define RESERVED_POS_685                  685u /* Interrupt no. 685    - RESERVED  */
#define RESERVED_POS_686                  686u /* Interrupt no. 686    - RESERVED  */
#define RESERVED_POS_687                  687u /* Interrupt no. 687    - RESERVED  */
/************************************************************************************************/
/**************************************** M_CAN ISR *********************************************/
/************************************************************************************************/
#define MCAN1_LINE0_ISR_POS               688u /* Interrupt no. 688    -   */
#define MCAN1_LINE1_ISR_POS               689u /* Interrupt no. 689    -   */
#define MCAN2_LINE0_ISR_POS               690u /* Interrupt no. 690    -   */
#define MCAN2_LINE1_ISR_POS               691u /* Interrupt no. 691    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_692                  692u /* Interrupt no. 692    - RESERVED  */
#define RESERVED_POS_693                  693u /* Interrupt no. 693    - RESERVED  */
#define RESERVED_POS_694                  694u /* Interrupt no. 694    - RESERVED  */
#define RESERVED_POS_695                  695u /* Interrupt no. 695    - RESERVED  */
#define RESERVED_POS_696                  696u /* Interrupt no. 696    - RESERVED  */
#define RESERVED_POS_697                  697u /* Interrupt no. 697    - RESERVED  */
#define RESERVED_POS_698                  698u /* Interrupt no. 698    - RESERVED  */
#define RESERVED_POS_699                  699u /* Interrupt no. 699    - RESERVED  */
#define RESERVED_POS_700                  700u /* Interrupt no. 700    - RESERVED  */
#define RESERVED_POS_701                  701u /* Interrupt no. 701    - RESERVED  */
#define RESERVED_POS_702                  702u /* Interrupt no. 702    - RESERVED  */
#define RESERVED_POS_703                  703u /* Interrupt no. 703    - RESERVED  */
#define RESERVED_POS_704                  704u /* Interrupt no. 704    - RESERVED  */
#define RESERVED_POS_705                  705u /* Interrupt no. 705    - RESERVED  */
/************************************************************************************************/
/*************************************** GTM ISR **********************************************/
/************************************************************************************************/
#define GTM_AEI_ISR_POS                   706u /* Interrupt no. 706    -   */
#define GTM_ARU_NEW_DATA0_ISR_POS         707u /* Interrupt no. 707    -   */
#define GTM_ARU_NEW_DATA1_ISR_POS         708u /* Interrupt no. 708    -   */
#define GTM_ARU_ACC_ACK_ISR_POS           709u /* Interrupt no. 709    -   */
#define GTM_BRC_ISR_POS                   710u /* Interrupt no. 710    -   */
#define GTM_CMP_ISR_POS                   711u /* Interrupt no. 711    -   */
#define GTM_SPE0_ISR_POS                  712u /* Interrupt no. 712    -   */
#define GTM_SPE1_ISR_POS                  713u /* Interrupt no. 713    -   */
#define GTM_PSM0_CH0_ISR_POS              714u /* Interrupt no. 714    -   */
#define GTM_PSM0_CH1_ISR_POS              715u /* Interrupt no. 715    -   */
#define GTM_PSM0_CH2_ISR_POS              716u /* Interrupt no. 716    -   */
#define GTM_PSM0_CH3_ISR_POS              717u /* Interrupt no. 717    -   */
#define GTM_PSM0_CH4_ISR_POS              718u /* Interrupt no. 718    -   */
#define GTM_PSM0_CH5_ISR_POS              719u /* Interrupt no. 719    -   */
#define GTM_PSM0_CH6_ISR_POS              720u /* Interrupt no. 720    -   */
#define GTM_PSM0_CH7_ISR_POS              721u /* Interrupt no. 721    -   */
#define GTM_DPLL_DCGI_ISR_POS             722u /* Interrupt no. 722    -   */
#define GTM_DPLL_EDI_ISR_POS              723u /* Interrupt no. 723    -   */
#define GTM_DPLL_TINI_ISR_POS             724u /* Interrupt no. 724    -   */
#define GTM_DPLL_TAXI_ISR_POS             725u /* Interrupt no. 725    -   */
#define GTM_DPLL_SISI_ISR_POS             726u /* Interrupt no. 726    -   */
#define GTM_DPLL_TISI_ISR_POS             727u /* Interrupt no. 727    -   */
#define GTM_DPLL_MSI_ISR_POS              728u /* Interrupt no. 728    -   */
#define GTM_DPLL_MTI_ISR_POS              729u /* Interrupt no. 729    -   */
#define GTM_DPLL_SASI_ISR_POS             730u /* Interrupt no. 730    -   */
#define GTM_DPLL_TASI_ISR_POS             731u /* Interrupt no. 731    -   */
#define GTM_DPLL_PWI_ISR_POS              732u /* Interrupt no. 732    -   */
#define GTM_DPLL_W2I_ISR_POS              733u /* Interrupt no. 733    -   */
#define GTM_DPLL_W1I_ISR_POS              734u /* Interrupt no. 734    -   */
#define GTM_DPLL_GL1I_ISR_POS             735u /* Interrupt no. 735    -   */
#define GTM_DPLL_LL1I_ISR_POS             736u /* Interrupt no. 736    -   */
#define GTM_DPLL_EI_ISR_POS               737u /* Interrupt no. 737    -   */
#define GTM_DPLL_GL2I_ISR_POS             738u /* Interrupt no. 738    -   */
#define GTM_DPLL_LL2I_ISR_POS             739u /* Interrupt no. 739    -   */
#define GTM_DPLL_TE0I_ISR_POS             740u /* Interrupt no. 740    -   */
#define GTM_DPLL_TE1I_ISR_POS             741u /* Interrupt no. 741    -   */
#define GTM_DPLL_TE2I_ISR_POS             742u /* Interrupt no. 742    -   */
#define GTM_DPLL_TE3I_ISR_POS             743u /* Interrupt no. 743    -   */
#define GTM_DPLL_TE4I_ISR_POS             744u /* Interrupt no. 744    -   */
#define GTM_DPLL_CDTI_ISR_POS             745u /* Interrupt no. 745    -   */
#define GTM_DPLL_CDSI_ISR_POS             746u /* Interrupt no. 746    -   */
#define GTM_DPLL_TORI_ISR_POS             747u /* Interrupt no. 747    -   */
#define GTM_DPLL_SORI_ISR_POS             748u /* Interrupt no. 748    -   */
#define GTM_TIM0_CH0_ISR_POS              749u /* Interrupt no. 749    -   */
#define GTM_TIM0_CH1_ISR_POS              750u /* Interrupt no. 750    -   */
#define GTM_TIM0_CH2_ISR_POS              751u /* Interrupt no. 751    -   */
#define GTM_TIM0_CH3_ISR_POS              752u /* Interrupt no. 752    -   */
#define GTM_TIM0_CH4_ISR_POS              753u /* Interrupt no. 753    -   */
#define GTM_TIM0_CH5_ISR_POS              754u /* Interrupt no. 754    -   */
#define GTM_TIM0_CH6_ISR_POS              755u /* Interrupt no. 755    -   */
#define GTM_TIM0_CH7_ISR_POS              756u /* Interrupt no. 756    -   */
#define GTM_TIM1_CH0_ISR_POS              757u /* Interrupt no. 757    -   */
#define GTM_TIM1_CH1_ISR_POS              758u /* Interrupt no. 758    -   */
#define GTM_TIM1_CH2_ISR_POS              759u /* Interrupt no. 759    -   */
#define GTM_TIM1_CH3_ISR_POS              760u /* Interrupt no. 760    -   */
#define GTM_TIM1_CH4_ISR_POS              761u /* Interrupt no. 761    -   */
#define GTM_TIM1_CH5_ISR_POS              762u /* Interrupt no. 762    -   */
#define GTM_TIM1_CH6_ISR_POS              763u /* Interrupt no. 763    -   */
#define GTM_TIM1_CH7_ISR_POS              764u /* Interrupt no. 764    -   */
#define GTM_TIM2_CH0_ISR_POS              765u /* Interrupt no. 765    -   */
#define GTM_TIM2_CH1_ISR_POS              766u /* Interrupt no. 766    -   */
#define GTM_TIM2_CH2_ISR_POS              767u /* Interrupt no. 767    -   */
#define GTM_TIM2_CH3_ISR_POS              768u /* Interrupt no. 768    -   */
#define GTM_TIM2_CH4_ISR_POS              769u /* Interrupt no. 769    -   */
#define GTM_TIM2_CH5_ISR_POS              770u /* Interrupt no. 770    -   */
#define GTM_TIM2_CH6_ISR_POS              771u /* Interrupt no. 771    -   */
#define GTM_TIM2_CH7_ISR_POS              772u /* Interrupt no. 772    -   */
#define RESERVED_POS_773                  773u /* Interrupt no. 773    - RESERVED  */
#define RESERVED_POS_774                  774u /* Interrupt no. 774    - RESERVED  */
#define RESERVED_POS_775                  775u /* Interrupt no. 775    - RESERVED  */
#define RESERVED_POS_776                  776u /* Interrupt no. 776    - RESERVED  */
#define RESERVED_POS_777                  777u /* Interrupt no. 777    - RESERVED  */
#define RESERVED_POS_778                  778u /* Interrupt no. 778    - RESERVED  */
#define RESERVED_POS_779                  779u /* Interrupt no. 779    - RESERVED  */
#define RESERVED_POS_780                  780u /* Interrupt no. 780    - RESERVED  */
#define GTM_MCS0_CH0_ISR_POS              781u /* Interrupt no. 781    -   */
#define GTM_MCS0_CH1_ISR_POS              782u /* Interrupt no. 782    -   */
#define GTM_MCS0_CH2_ISR_POS              783u /* Interrupt no. 783    -   */
#define GTM_MCS0_CH3_ISR_POS              784u /* Interrupt no. 784    -   */
#define GTM_MCS0_CH4_ISR_POS              785u /* Interrupt no. 785    -   */
#define GTM_MCS0_CH5_ISR_POS              786u /* Interrupt no. 786    -   */
#define GTM_MCS0_CH6_ISR_POS              787u /* Interrupt no. 787    -   */
#define GTM_MCS0_CH7_ISR_POS              788u /* Interrupt no. 788    -   */
#define GTM_MCS1_CH0_ISR_POS              789u /* Interrupt no. 789    -   */
#define GTM_MCS1_CH1_ISR_POS              790u /* Interrupt no. 790    -   */
#define GTM_MCS1_CH2_ISR_POS              791u /* Interrupt no. 791    -   */
#define GTM_MCS1_CH3_ISR_POS              792u /* Interrupt no. 792    -   */
#define GTM_MCS1_CH4_ISR_POS              793u /* Interrupt no. 793    -   */
#define GTM_MCS1_CH5_ISR_POS              794u /* Interrupt no. 794    -   */
#define GTM_MCS1_CH6_ISR_POS              795u /* Interrupt no. 795    -   */
#define GTM_MCS1_CH7_ISR_POS              796u /* Interrupt no. 796    -   */
#define GTM_MCS2_CH0_ISR_POS              797u /* Interrupt no. 797    -   */
#define GTM_MCS2_CH1_ISR_POS              798u /* Interrupt no. 798    -   */
#define GTM_MCS2_CH2_ISR_POS              799u /* Interrupt no. 799    -   */
#define GTM_MCS2_CH3_ISR_POS              800u /* Interrupt no. 800    -   */
#define GTM_MCS2_CH4_ISR_POS              801u /* Interrupt no. 801    -   */
#define GTM_MCS2_CH5_ISR_POS              802u /* Interrupt no. 802    -   */
#define GTM_MCS2_CH6_ISR_POS              803u /* Interrupt no. 803    -   */
#define GTM_MCS2_CH7_ISR_POS              804u /* Interrupt no. 804    -   */
#define RESERVED_POS_805                  805u /* Interrupt no. 805    - RESERVED  */
#define RESERVED_POS_806                  806u /* Interrupt no. 806    - RESERVED  */
#define RESERVED_POS_807                  807u /* Interrupt no. 807    - RESERVED  */
#define RESERVED_POS_808                  808u /* Interrupt no. 808    - RESERVED  */
#define RESERVED_POS_809                  809u /* Interrupt no. 809    - RESERVED  */
#define RESERVED_POS_810                  810u /* Interrupt no. 810    - RESERVED  */
#define RESERVED_POS_811                  811u /* Interrupt no. 811    - RESERVED  */
#define RESERVED_POS_812                  812u /* Interrupt no. 812    - RESERVED  */
#define GTM_TOM0_CH0_1_ISR_POS            813u /* Interrupt no. 813    -   */
#define GTM_TOM0_CH2_3_ISR_POS            814u /* Interrupt no. 814    -   */
#define GTM_TOM0_CH4_5_ISR_POS            815u /* Interrupt no. 815    -   */
#define GTM_TOM0_CH6_7_ISR_POS            816u /* Interrupt no. 816    -   */
#define GTM_TOM0_CH8_9_ISR_POS            817u /* Interrupt no. 817    -   */
#define GTM_TOM0_CH10_11_ISR_POS          818u /* Interrupt no. 818    -   */
#define GTM_TOM0_CH12_13_ISR_POS          819u /* Interrupt no. 819    -   */
#define GTM_TOM0_CH14_15_ISR_POS          820u /* Interrupt no. 820    -   */
#define GTM_TOM1_CH0_1_ISR_POS            821u /* Interrupt no. 821    -   */
#define GTM_TOM1_CH2_3_ISR_POS            822u /* Interrupt no. 822    -   */
#define GTM_TOM1_CH4_5_ISR_POS            823u /* Interrupt no. 823    -   */
#define GTM_TOM1_CH6_7_ISR_POS            824u /* Interrupt no. 824    -   */
#define GTM_TOM1_CH8_9_ISR_POS            825u /* Interrupt no. 825    -   */
#define GTM_TOM1_CH10_11_ISR_POS          826u /* Interrupt no. 826    -   */
#define GTM_TOM1_CH12_13_ISR_POS          827u /* Interrupt no. 827    -   */
#define GTM_TOM1_CH14_15_ISR_POS          828u /* Interrupt no. 828    -   */
#define RESERVED_POS_829                  829u /* Interrupt no. 829    - RESERVED  */
#define RESERVED_POS_830                  830u /* Interrupt no. 830    - RESERVED  */
#define RESERVED_POS_831                  831u /* Interrupt no. 831    - RESERVED  */
#define RESERVED_POS_832                  832u /* Interrupt no. 832    - RESERVED  */
#define RESERVED_POS_833                  833u /* Interrupt no. 833    - RESERVED  */
#define RESERVED_POS_834                  834u /* Interrupt no. 834    - RESERVED  */
#define RESERVED_POS_835                  835u /* Interrupt no. 835    - RESERVED  */
#define RESERVED_POS_836                  836u /* Interrupt no. 836    - RESERVED  */
#define GTM_ATOM0_CH0_1_ISR_POS           837u /* Interrupt no. 837    -   */
#define GTM_ATOM0_CH2_3_ISR_POS           838u /* Interrupt no. 838    -   */
#define GTM_ATOM0_CH4_5_ISR_POS           839u /* Interrupt no. 839    -   */
#define GTM_ATOM0_CH6_7_ISR_POS           840u /* Interrupt no. 840    -   */
#define GTM_ATOM1_CH0_1_ISR_POS           841u /* Interrupt no. 841    -   */
#define GTM_ATOM1_CH2_3_ISR_POS           842u /* Interrupt no. 842    -   */
#define GTM_ATOM1_CH4_5_ISR_POS           843u /* Interrupt no. 843    -   */
#define GTM_ATOM1_CH6_7_ISR_POS           844u /* Interrupt no. 844    -   */
#define GTM_ATOM2_CH0_1_ISR_POS           845u /* Interrupt no. 845    -   */
#define GTM_ATOM2_CH2_3_ISR_POS           846u /* Interrupt no. 846    -   */
#define GTM_ATOM2_CH4_5_ISR_POS           847u /* Interrupt no. 847    -   */
#define GTM_ATOM2_CH6_7_ISR_POS           848u /* Interrupt no. 848    -   */
#define GTM_ATOM3_CH0_1_ISR_POS           849u /* Interrupt no. 849    -   */
#define GTM_ATOM3_CH2_3_ISR_POS           850u /* Interrupt no. 850    -   */
#define GTM_ATOM3_CH4_5_ISR_POS           851u /* Interrupt no. 851    -   */
#define GTM_ATOM3_CH6_7_ISR_POS           852u /* Interrupt no. 852    -   */
#define RESERVED_POS_853                  853u /* Interrupt no. 853    - RESERVED  */
#define RESERVED_POS_854                  854u /* Interrupt no. 854    - RESERVED  */
#define RESERVED_POS_855                  855u /* Interrupt no. 855    - RESERVED  */
#define RESERVED_POS_856                  856u /* Interrupt no. 856    - RESERVED  */
#define RESERVED_POS_857                  857u /* Interrupt no. 857    - RESERVED  */
#define RESERVED_POS_858                  858u /* Interrupt no. 858    - RESERVED  */
#define RESERVED_POS_859                  859u /* Interrupt no. 859    - RESERVED  */
#define RESERVED_POS_860                  860u /* Interrupt no. 860    - RESERVED  */
#define RESERVED_POS_861                  861u /* Interrupt no. 861    - RESERVED  */
#define RESERVED_POS_862                  862u /* Interrupt no. 862    - RESERVED  */
#define RESERVED_POS_863                  863u /* Interrupt no. 863    - RESERVED  */
#define RESERVED_POS_864                  864u /* Interrupt no. 864    - RESERVED  */
#define RESERVED_POS_865                  865u /* Interrupt no. 865    - RESERVED  */
#define RESERVED_POS_866                  866u /* Interrupt no. 866    - RESERVED  */
#define RESERVED_POS_867                  867u /* Interrupt no. 867    - RESERVED  */
#define RESERVED_POS_868                  868u /* Interrupt no. 868    - RESERVED  */
#define RESERVED_POS_869                  869u /* Interrupt no. 869    - RESERVED  */
#define RESERVED_POS_870                  870u /* Interrupt no. 870    - RESERVED  */
#define RESERVED_POS_871                  871u /* Interrupt no. 871    - RESERVED  */
#define RESERVED_POS_872                  872u /* Interrupt no. 872    - RESERVED  */
#define RESERVED_POS_873                  873u /* Interrupt no. 873    - RESERVED  */
#define RESERVED_POS_874                  874u /* Interrupt no. 874    - RESERVED  */
#define RESERVED_POS_875                  875u /* Interrupt no. 875    - RESERVED  */
#define RESERVED_POS_876                  876u /* Interrupt no. 876    - RESERVED  */
#define RESERVED_POS_877                  877u /* Interrupt no. 877    - RESERVED  */
#define RESERVED_POS_878                  878u /* Interrupt no. 878    - RESERVED  */
#define RESERVED_POS_879                  879u /* Interrupt no. 879    - RESERVED  */
#define RESERVED_POS_880                  880u /* Interrupt no. 880    - RESERVED  */
#define RESERVED_POS_881                  881u /* Interrupt no. 881    - RESERVED  */
#define RESERVED_POS_882                  882u /* Interrupt no. 882    - RESERVED  */
#define RESERVED_POS_883                  883u /* Interrupt no. 883    - RESERVED  */
#define RESERVED_POS_884                  884u /* Interrupt no. 884    - RESERVED  */
#define RESERVED_POS_885                  885u /* Interrupt no. 885    - RESERVED  */
#define RESERVED_POS_886                  886u /* Interrupt no. 886    - RESERVED  */
#define RESERVED_POS_887                  887u /* Interrupt no. 887    - RESERVED  */
#define RESERVED_POS_888                  888u /* Interrupt no. 888    - RESERVED  */
#define RESERVED_POS_889                  889u /* Interrupt no. 889    - RESERVED  */
#define RESERVED_POS_890                  890u /* Interrupt no. 890    - RESERVED  */
#define RESERVED_POS_891                  891u /* Interrupt no. 891    - RESERVED  */
#define RESERVED_POS_892                  892u /* Interrupt no. 892    - RESERVED  */
#define RESERVED_POS_893                  893u /* Interrupt no. 893    - RESERVED  */
#define RESERVED_POS_894                  894u /* Interrupt no. 894    - RESERVED  */
#define RESERVED_POS_895                  895u /* Interrupt no. 895    - RESERVED  */
#define RESERVED_POS_896                  896u /* Interrupt no. 896    - RESERVED  */
#define RESERVED_POS_897                  897u /* Interrupt no. 897    - RESERVED  */
#define RESERVED_POS_898                  898u /* Interrupt no. 898    - RESERVED  */
#define RESERVED_POS_899                  899u /* Interrupt no. 899    - RESERVED  */
#define RESERVED_POS_900                  900u /* Interrupt no. 900    - RESERVED  */
#define RESERVED_POS_901                  901u /* Interrupt no. 901    - RESERVED  */
#define RESERVED_POS_902                  902u /* Interrupt no. 902    - RESERVED  */
#define RESERVED_POS_903                  903u /* Interrupt no. 903    - RESERVED  */
#define RESERVED_POS_904                  904u /* Interrupt no. 904    - RESERVED  */
#define RESERVED_POS_905                  905u /* Interrupt no. 905    - RESERVED  */
#define RESERVED_POS_906                  906u /* Interrupt no. 906    - RESERVED  */
#define RESERVED_POS_907                  907u /* Interrupt no. 907    - RESERVED  */
#define RESERVED_POS_908                  908u /* Interrupt no. 908    - RESERVED  */
#define RESERVED_POS_909                  909u /* Interrupt no. 909    - RESERVED  */
#define RESERVED_POS_910                  910u /* Interrupt no. 910    - RESERVED  */
#define RESERVED_POS_911                  911u /* Interrupt no. 911    - RESERVED  */
#define RESERVED_POS_912                  912u /* Interrupt no. 912    - RESERVED  */
#define RESERVED_POS_913                  913u /* Interrupt no. 913    - RESERVED  */
#define RESERVED_POS_914                  914u /* Interrupt no. 914    - RESERVED  */
#define RESERVED_POS_915                  915u /* Interrupt no. 915    - RESERVED  */
#define RESERVED_POS_916                  916u /* Interrupt no. 916    - RESERVED  */
#define RESERVED_POS_917                  917u /* Interrupt no. 917    - RESERVED  */
#define RESERVED_POS_918                  918u /* Interrupt no. 918    - RESERVED  */
#define RESERVED_POS_919                  919u /* Interrupt no. 919    - RESERVED  */
#define RESERVED_POS_920                  920u /* Interrupt no. 920    - RESERVED  */
#define RESERVED_POS_921                  921u /* Interrupt no. 921    - RESERVED  */
#define RESERVED_POS_922                  922u /* Interrupt no. 922    - RESERVED  */
#define RESERVED_POS_923                  923u /* Interrupt no. 923    - RESERVED  */
#define RESERVED_POS_924                  924u /* Interrupt no. 924    - RESERVED  */
#define RESERVED_POS_925                  925u /* Interrupt no. 925    - RESERVED  */
#define RESERVED_POS_926                  926u /* Interrupt no. 926    - RESERVED  */
#define RESERVED_POS_927                  927u /* Interrupt no. 927    - RESERVED  */
#define RESERVED_POS_928                  928u /* Interrupt no. 928    - RESERVED  */
#define RESERVED_POS_929                  929u /* Interrupt no. 929    - RESERVED  */
#define RESERVED_POS_930                  930u /* Interrupt no. 930    - RESERVED  */
#define GTM_ERR_ISR_POS                   931u /* Interrupt no. 931    -   */
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_932                  932u /* Interrupt no. 932    - RESERVED  */
#define RESERVED_POS_933                  933u /* Interrupt no. 933    - RESERVED  */
#define RESERVED_POS_934                  934u /* Interrupt no. 934    - RESERVED  */
/************************************************************************************************/
/**************************************** CCCU ISR *********************************************/
/************************************************************************************************/
#define CCCU_CSCE_CWEE_ISR_POS                935u /* Interrupt no. 935    -   */  
/************************************************************************************************/
/*************************************** RESERVED ***********************************************/
/************************************************************************************************/
#define RESERVED_POS_936                  936u /* Interrupt no. 936    - RESERVED  */
#define RESERVED_POS_937                  937u /* Interrupt no. 937    - RESERVED  */
#define RESERVED_POS_938                  938u /* Interrupt no. 938    - RESERVED  */
#define RESERVED_POS_939                  939u /* Interrupt no. 939    - RESERVED  */
#define RESERVED_POS_940                  940u /* Interrupt no. 940    - RESERVED  */
#define RESERVED_POS_941                  941u /* Interrupt no. 941    - RESERVED  */
#define RESERVED_POS_942                  942u /* Interrupt no. 942    - RESERVED  */
#define RESERVED_POS_943                  943u /* Interrupt no. 943    - RESERVED  */
#define RESERVED_POS_944                  944u /* Interrupt no. 944    - RESERVED  */
#define RESERVED_POS_945                  945u /* Interrupt no. 945    - RESERVED  */
#define RESERVED_POS_946                  946u /* Interrupt no. 946    - RESERVED  */
#define RESERVED_POS_947                  947u /* Interrupt no. 947    - RESERVED  */
#define RESERVED_POS_948                  948u /* Interrupt no. 948    - RESERVED  */
#define RESERVED_POS_949                  949u /* Interrupt no. 949    - RESERVED  */
#define RESERVED_POS_950                  950u /* Interrupt no. 950    - RESERVED  */
#define RESERVED_POS_951                  951u /* Interrupt no. 951    - RESERVED  */
#define RESERVED_POS_952                  952u /* Interrupt no. 952    - RESERVED  */
#define RESERVED_POS_953                  953u /* Interrupt no. 953    - RESERVED  */
#define RESERVED_POS_954                  954u /* Interrupt no. 954    - RESERVED  */
#define RESERVED_POS_955                  955u /* Interrupt no. 955    - RESERVED  */
#define RESERVED_POS_956                  956u /* Interrupt no. 956    - RESERVED  */
#define RESERVED_POS_957                  957u /* Interrupt no. 957    - RESERVED  */
#define RESERVED_POS_958                  958u /* Interrupt no. 958    - RESERVED  */
#define RESERVED_POS_959                  959u /* Interrupt no. 959    - RESERVED  */
#define RESERVED_POS_960                  960u /* Interrupt no. 960    - RESERVED  */
#define RESERVED_POS_961                  961u /* Interrupt no. 961    - RESERVED  */
#define RESERVED_POS_962                  962u /* Interrupt no. 962    - RESERVED  */
#define RESERVED_POS_963                  963u /* Interrupt no. 963    - RESERVED  */
#define RESERVED_POS_964                  964u /* Interrupt no. 964    - RESERVED  */
#define RESERVED_POS_965                  965u /* Interrupt no. 965    - RESERVED  */
#define RESERVED_POS_966                  966u /* Interrupt no. 966    - RESERVED  */
#define RESERVED_POS_967                  967u /* Interrupt no. 967    - RESERVED  */
#define RESERVED_POS_968                  968u /* Interrupt no. 968    - RESERVED  */
#define RESERVED_POS_969                  969u /* Interrupt no. 969    - RESERVED  */
#define RESERVED_POS_970                  970u /* Interrupt no. 970    - RESERVED  */
#define RESERVED_POS_971                  971u /* Interrupt no. 971    - RESERVED  */
#define RESERVED_POS_972                  972u /* Interrupt no. 972    - RESERVED  */
#define RESERVED_POS_973                  973u /* Interrupt no. 973    - RESERVED  */
#define RESERVED_POS_974                  974u /* Interrupt no. 974    - RESERVED  */
#define RESERVED_POS_975                  975u /* Interrupt no. 975    - RESERVED  */
#define RESERVED_POS_976                  976u /* Interrupt no. 976    - RESERVED  */
#define RESERVED_POS_977                  977u /* Interrupt no. 977    - RESERVED  */
#define RESERVED_POS_978                  978u /* Interrupt no. 978    - RESERVED  */
#define RESERVED_POS_979                  979u /* Interrupt no. 979    - RESERVED  */
#define RESERVED_POS_980                  980u /* Interrupt no. 980    - RESERVED  */
#define RESERVED_POS_981                  981u /* Interrupt no. 981    - RESERVED  */
#define RESERVED_POS_982                  982u /* Interrupt no. 982    - RESERVED  */
#define RESERVED_POS_983                  983u /* Interrupt no. 983    - RESERVED  */
#define RESERVED_POS_984                  984u /* Interrupt no. 984    - RESERVED  */
#define RESERVED_POS_985                  985u /* Interrupt no. 985    - RESERVED  */
#define RESERVED_POS_986                  986u /* Interrupt no. 986    - RESERVED  */
#define RESERVED_POS_987                  987u /* Interrupt no. 987    - RESERVED  */
#define RESERVED_POS_988                  988u /* Interrupt no. 988    - RESERVED  */
#define RESERVED_POS_989                  989u /* Interrupt no. 989    - RESERVED  */
#define RESERVED_POS_990                  990u /* Interrupt no. 990    - RESERVED  */
#define RESERVED_POS_991                  991u /* Interrupt no. 991    - RESERVED  */
#define RESERVED_POS_992                  992u /* Interrupt no. 992    - RESERVED  */
#define RESERVED_POS_993                  993u /* Interrupt no. 993    - RESERVED  */
#define RESERVED_POS_994                  994u /* Interrupt no. 994    - RESERVED  */
#define RESERVED_POS_995                  995u /* Interrupt no. 995    - RESERVED  */
#define RESERVED_POS_996                  996u /* Interrupt no. 996    - RESERVED  */
#define RESERVED_POS_997                  997u /* Interrupt no. 997    - RESERVED  */
#define RESERVED_POS_998                  998u /* Interrupt no. 998    - RESERVED  */
#define RESERVED_POS_999                  999u /* Interrupt no. 999    - RESERVED  */
#define RESERVED_POS_1000                1000u /* Interrupt no. 1000   - RESERVED  */
#define RESERVED_POS_1001                1001u /* Interrupt no. 1001   - RESERVED  */
#define RESERVED_POS_1002                1002u /* Interrupt no. 1002   - RESERVED  */
#define RESERVED_POS_1003                1003u /* Interrupt no. 1003   - RESERVED  */
#define RESERVED_POS_1004                1004u /* Interrupt no. 1004   - RESERVED  */
#define RESERVED_POS_1005                1005u /* Interrupt no. 1005   - RESERVED  */
#define RESERVED_POS_1006                1006u /* Interrupt no. 1006   - RESERVED  */
#define RESERVED_POS_1007                1007u /* Interrupt no. 1007   - RESERVED  */
#define RESERVED_POS_1008                1008u /* Interrupt no. 1008   - RESERVED  */
#define RESERVED_POS_1009                1009u /* Interrupt no. 1009   - RESERVED  */
#define RESERVED_POS_1010                1010u /* Interrupt no. 1010   - RESERVED  */
#define RESERVED_POS_1011                1011u /* Interrupt no. 1011   - RESERVED  */
#define RESERVED_POS_1012                1012u /* Interrupt no. 1012   - RESERVED  */
#define RESERVED_POS_1013                1013u /* Interrupt no. 1013   - RESERVED  */
#define RESERVED_POS_1014                1014u /* Interrupt no. 1014   - RESERVED  */
#define RESERVED_POS_1015                1015u /* Interrupt no. 1015   - RESERVED  */
#define RESERVED_POS_1016                1016u /* Interrupt no. 1016   - RESERVED  */
#define RESERVED_POS_1017                1017u /* Interrupt no. 1017   - RESERVED  */
#define RESERVED_POS_1018                1018u /* Interrupt no. 1018   - RESERVED  */
#define RESERVED_POS_1019                1019u /* Interrupt no. 1019   - RESERVED  */
#define RESERVED_POS_1020                1020u /* Interrupt no. 1020   - RESERVED  */
#define RESERVED_POS_1021                1021u /* Interrupt no. 1021   - RESERVED  */
#define RESERVED_POS_1022                1022u /* Interrupt no. 1022   - RESERVED  */
#define RESERVED_POS_1023                1023u /* Interrupt no. 1023   - RESERVED  */


/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* ------------------------------------------------------------------------- */
/* Macro definition for INTC Registers - (a)represents the vector            */
/*                                       index,i.e. the interrupt            */
/*                                       request sources.                    */
/* ------------------------------------------------------------------------- */
/* MISRA 2004 Rule 19.10: parameter of function-like macro must be enclosed in parentheses */
#define SET_SSCIR(a)  (INTC.SSCIR[(a)].B.SET)/* Set software Interrupt set bit   */
#define CLR_SSCIR(a)  (INTC.SSCIR[(a)].B.CLR)/* Set software Interrupt clear bit */
#define INTC_PSR_PRI(a)   (INTC.PSR[(a)].PSR.PRI.B.PRI)  /* Set INTC priority select register*/
#define INTC_PSR_PRC_SEL(a)   (INTC.PSR[(a)].PSR.PRC_SEL.B.PRC_SEL)  /* Set INTC priority select register*/

/* ------------------------------------------------------------------------- */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/

typedef void (*tFunc)(void);


typedef struct
{
  tFunc bufferISR[FIFO_HANDLER_DIM];
  uint8_T   r_index;
  uint8_T   w_index;
} tBuff;

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern tFunc  tFun;
extern const uint8_T INTC_PSRnVector[1024][2];
extern uint16_T TASK_ConfigStatus;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/

/******************************************************************************
**   Function    : RoutineISR0
**
**   Description:
**    Interrupt Service Routine no. 0.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR0(void);

/******************************************************************************
**   Function    : RoutineISR1
**
**   Description:
**    Interrupt Service Routine no. 1.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR1(void);

/******************************************************************************
**   Function    : RoutineISR2
**
**   Description:
**    Interrupt Service Routine no. 2.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR2(void);

/******************************************************************************
**   Function    : RoutineISR3
**
**   Description:
**    Interrupt Service Routine no. 3.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR3(void);

/******************************************************************************
**   Function    : RoutineISR4
**
**   Description:
**    Interrupt Service Routine no. 4.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR4(void);

/******************************************************************************
**   Function    : RoutineISR5
**
**   Description:
**    Interrupt Service Routine no. 5.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR5(void);

/******************************************************************************
**   Function    : RoutineISR6
**
**   Description:
**    Interrupt Service Routine no. 6.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR6(void);

/******************************************************************************
**   Function    : RoutineISR7
**
**   Description:
**    Interrupt Service Routine no. 7.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR7(void);

/******************************************************************************
**   Function    : RoutineISR8
**
**   Description:
**    Interrupt Service Routine no. 8.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR8(void);  

/******************************************************************************
**   Function    : RoutineISR9
**
**   Description:
**    Interrupt Service Routine no. 9.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR9(void);  

/******************************************************************************
**   Function    : RoutineISR10
**
**   Description:
**    Interrupt Service Routine no. 10.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR10(void); 

/******************************************************************************
**   Function    : RoutineISR11
**
**   Description:
**    Interrupt Service Routine no. 11.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR11(void); 

/******************************************************************************
**   Function    : RoutineISR12
**
**   Description:
**    Interrupt Service Routine no. 12.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR12(void); 

/******************************************************************************
**   Function    : RoutineISR13
**
**   Description:
**    Interrupt Service Routine no. 13.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR13(void); 

/******************************************************************************
**   Function    : RoutineISR14
**
**   Description:
**    Interrupt Service Routine no. 14.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR14(void); 

/******************************************************************************
**   Function    : RoutineISR15
**
**   Description:
**    Interrupt Service Routine no. 15.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR15(void); 

/******************************************************************************
**   Function    : RoutineISR16
**
**   Description:
**    Interrupt Service Routine no. 16.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR16(void); 

/******************************************************************************
**   Function    : RoutineISR17
**
**   Description:
**    Interrupt Service Routine no. 17.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR17(void); 

/******************************************************************************
**   Function    : RoutineISR18
**
**   Description:
**    Interrupt Service Routine no. 18.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR18(void); 

/******************************************************************************
**   Function    : RoutineISR19
**
**   Description:
**    Interrupt Service Routine no. 19.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR19(void); 

/******************************************************************************
**   Function    : RoutineISR20
**
**   Description:
**    Interrupt Service Routine no. 20.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR20(void); 

/******************************************************************************
**   Function    : RoutineISR21
**
**   Description:
**    Interrupt Service Routine no. 21.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR21(void); 

/******************************************************************************
**   Function    : RoutineISR22
**
**   Description:
**    Interrupt Service Routine no. 22.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR22(void); 

/******************************************************************************
**   Function    : RoutineISR23
**
**   Description:
**    Interrupt Service Routine no. 23.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR23(void); 

/******************************************************************************
**   Function    : RoutineISR24
**
**   Description:
**    Interrupt Service Routine no. 24.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR24(void); 

/******************************************************************************
**   Function    : RoutineISR25
**
**   Description:
**    Interrupt Service Routine no. 25.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR25(void); 

/******************************************************************************
**   Function    : RoutineISR26
**
**   Description:
**    Interrupt Service Routine no. 26.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR26(void); 

/******************************************************************************
**   Function    : RoutineISR27
**
**   Description:
**    Interrupt Service Routine no. 27.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR27(void); 

/******************************************************************************
**   Function    : RoutineISR28
**
**   Description:
**    Interrupt Service Routine no. 28.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR28(void); 

/******************************************************************************
**   Function    : RoutineISR29
**
**   Description:
**    Interrupt Service Routine no. 29.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR29(void); 

/******************************************************************************
**   Function    : RoutineISR30
**
**   Description:
**    Interrupt Service Routine no. 30.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR30(void); 

/******************************************************************************
**   Function    : RoutineISR31
**
**   Description:
**    Interrupt Service Routine no. 31.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void RoutineISR31(void); 

/******************************************************************************
**   Function    : TASK_Init
**
**   Description:
**    This function initialize .
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void TASK_Init(void);

/******************************************************************************
**   Function    : TASK_Config
**
**   Description:
**    This functions configures PSR and IVT. 
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR                      : No error
**    PERIPHERAL_ALREADY_CONFIGURED : Peripheral already configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T TASK_Config(void);

/*
** ============================================================================
**     Method      :  TASK_TaskHandlingFunctionDelayed
**
**     Description :
**         
**     Parameters  : 
**     Returns     : 
** ============================================================================
*/
int16_T TASK_TaskHandlingFunctionDelayed(TaskType tFunction, uint32_T delay);

/******************************************************************************
**   Function    : TASK_DisablePeripherals
**
**   Description:
**    This function disables peripherals. 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void TASK_DisablePeripherals(void);


/******************************************************************************
**   Function    : SetTaskDelay
**
**   Description: Uses a PIT channel of engine 0 to delay the Task activation
**    
**
**   Parameters :
**    [in] uint8_T pitChannel :Channel to be configured (on PIT engine 0)
**    [in] TaskType taskID : Task to be delayed
**    [in] uint32_T delay_us : Delay in us
**
**   Returns:
**    NO_ERROR                - No error
**    MAX_DELAY_TIME_EXCEEDED - Provided delay is greater than max delay
**    MIN_DELAY_TIME_FAILED   - Provided delay is lower than min delay
**    PERIPHERAL_BUSY         - PIT is still enabled, delay update impossible
*     PERIPHERAL_NOT_CONFIGURED : peripheral is not configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern int16_T SetTaskDelay(uint8_T pitChannel, TaskType taskID, uint32_T delay_us);

/******************************************************************************
**   Function    : Task_SetIrq_Test
**
**   Description:
**    This function checks if an Irq is set. 
**
**   Parameters :
**    [in] uint16_T IrqNumb : Irq number
**
**   Returns:
**    NO_ERROR  - Check performed correcly
**    ARG_ERROR - Argument error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#ifdef TASK_SETIRQ_TEST
int8_T Task_SetIrq_Test(uint16_T IrqNumb);
#endif


#ifdef TASK_SWSETCLRINT_TEST
void SetIRQ_to_c2(void);
void SetIRQ_to_c0(void);
void SetIRQ_c0(void);
void SetIRQ_c2(void);
#endif

/* END TASK */
#endif /* __TASK_H__ */

/****************************************************************************
 ****************************************************************************/

