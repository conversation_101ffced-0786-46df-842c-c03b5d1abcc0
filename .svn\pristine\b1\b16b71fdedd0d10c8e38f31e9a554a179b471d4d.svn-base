@ECHO OFF
REM script to start debugger for core_0.
REM GTM core will be started by practice script (spc574k72_demo_led.cmm)
REM $Id: spc574k72_demo_led_start_core0.bat 9089 2016-02-18 08:44:38Z rsagerer $

SET P1_PORT=10000
SET P2_TITLE=Trace32_MPC
SET P3_TMP=%TMP%
SET P4_SYS=C:\t32
SET P5_HELP=C:\t32\pdf
IF "%1"=="" (
  SET P6_PBI=USB
  SET P7_OPT=CORE=1
  SET P8_OPT=
  SET P9_OPT=
) ELSE (
  SET P6_PBI=NET
  SET P7_OPT=NODE=%1
  SET P8_OPT=PACKLEN=1024
  SET P9_OPT=CORE=1
)

if exist %P4_SYS%\bin\windows64\t32mppc.exe (
  SET EXE_DIR=bin\windows64
) else (
  SET EXE_DIR=bin\windows
)

start %P4_SYS%\%EXE_DIR%\t32mppc -c config_multicore.t32 %P1_PORT% %P2_TITLE% %P3_TMP% %P4_SYS% %P5_HELP% %P6_PBI% %P7_OPT% %P8_OPT% %P9_OPT% -s spc574k72_Flash.cmm
