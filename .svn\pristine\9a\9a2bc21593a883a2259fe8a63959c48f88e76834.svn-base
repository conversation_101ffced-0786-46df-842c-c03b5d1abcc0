/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           SparkPlugTest.c
 **  File Creation Date: 23-Apr-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         SparkPlugTest
 **  Model Description:  This model detects fault on spark-plug, analyzing the median value of ion signal along coil charge.
   SparkPlugTest strategy is active only if spark advance correction is stable and for a defined range of engine working points.
 **  Model Version:      1.959
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Fri Apr 23 07:29:41 2021
 **
 **  Last Saved Modification:  RoccaG - Fri Apr 23 07:11:33 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "SparkPlugTest_out.h"
#include "SparkPlugTest_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/Scheduler' */
#define SparkPlugTest_event_ev_EOA     (1)
#define SparkPlugTest_event_ev_PowerOn (0)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKDWELLINTTHRLOAD_dim          4U                        /* Referenced by:
                                                                  * '<Root>/Constant2'
                                                                  * '<S14>/Constant3'
                                                                  * '<S14>/Constant5'
                                                                  * '<S14>/Constant6'
                                                                  */

/* BKDWELLINTTHRLOAD breakpoint dimension. */
#define BKDWELLINTTHRRPM_dim           7U                        /* Referenced by:
                                                                  * '<Root>/Constant3'
                                                                  * '<S14>/Constant2'
                                                                  * '<S14>/Constant4'
                                                                  * '<S14>/Constant7'
                                                                  */

/* BKDWELLINTTHRRPM breakpoint dimension. */
#define ID_VER_SPARKPLUGTEST_DEF       1959U                     /* Referenced by: '<Root>/Scheduler' */

/* Model Version. */
#define MAX_UINT16_T                   65535U                    /* Referenced by: '<S41>/Constant2' */

/* Maximum value for uint16 type. */
#define MEDIAN_DWELL_BUFF_MAX_SIZE     5U                        /* Referenced by:
                                                                  * '<S19>/Constant4'
                                                                  * '<S20>/Constant4'
                                                                  */

/* Maximum value for median length. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_SPARKPLUGTEST_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint16_T vtDwellIntLocalBuff[5];/* '<S19>/Assignment' */

/*Calibration memory section */
/*Start of local calbration section*/
#pragma ghs section rodata=".calib"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKDWELLINTTHRLOAD[5] = { 1280U, 3840U,
  6400U, 8320U, 9600U } ;

/* DwellIntThr threshold Load brakepoints */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKDWELLINTTHRRPM[8] = { 620U, 1000U,
  1100U, 2500U, 2750U, 3000U, 3500U, 3750U } ;

/* DwellIntThr threshold rpm brakepoints */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T DWELLINTKFILT = 1638U;

/* DwelInt filter constant */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T DWELLINTMEDIANLENGTH = 0U;

/* DwellInt median length */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T SPARKPLUGFAULTDCYINC = 10U;

/* Fouled spark plug fault validated step increment per fault validation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T SPARKPLUGFAULTINC = 10U;

/* Fouled spark plug fault step increment */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SPARKPLUGFAULTTHR = 100U;

/* Fouled spark plug fault counter thr */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBDWELLINTTHR[40] = { 60U, 60U, 60U,
  60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U,
  60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U, 60U,
  60U, 60U, 60U, 60U, 60U } ;

/* DwellIntThr threshold to test fouled Spark Plugs */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TBSATHRDWELLINT[40] = { -80, -80, -80,
  -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80,
  -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80, -80,
  -80, -80, -80, -80, -80 } ;

/* SA threshold to reset DwellInt filtering */
#pragma ghs section rodata=default

/*End of calibration section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T DwellIntFilt[8];              /* '<S11>/Merge' */

/* DwellInt filtered through FOF with factor DWELLINTKFILT */
uint8_T SparkPlugFault;                /* '<S12>/Merge' */

/* Fouled Spark Plug Fault bitmask (firing order) */

/*Static test point definition*/
/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT int16_T DwellInt15bit;

/* Dwell Int on 15 bit */
STATIC_TEST_POINT uint16_T DwellIntThr;

/* Interpolation output of TBDWELLINTTHR */
STATIC_TEST_POINT uint32_T IdVer_SparkPlugTest;

/* Model Version */
STATIC_TEST_POINT uint8_T SparkPlugFaultCnt[8];

/* Fouled Spark Plug fault counter */
STATIC_TEST_POINT uint8_T SparkPlugFaultStatus[8];

/* Fouled Spark Plug fault status */
STATIC_TEST_POINT uint16_T TbDwellInt[40];

/* Buffer of DwellInt used to calculate Median */
STATIC_TEST_POINT int16_T ThrSAout;

/* Threshold on SAoutCyl to reset dwell filter */
STATIC_TEST_POINT uint8_T VtCircularBufferIndex[8];

/* Circular buffer index */
STATIC_TEST_POINT int32_T VtDwellIntFiltHR[8];

/* Dwell int median, div 2, high resolution */
STATIC_TEST_POINT uint16_T VtDwellIntMedian[8];

/* Dwell int median */

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void Spar_chartstep_c3_SparkPlugTest(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/Scheduler' */
static void Spar_chartstep_c3_SparkPlugTest(const int32_T *sfEvent)
{
  /* local block i/o variables */
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint16_T rtb_Eldor_InsertionSort1[5];
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_g;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_e;
  uint16_T rtb_Look2D_IR_U16;
  int16_T rtb_Look2D_IR_S16;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  uint16_T rtb_MinMax_e;
  boolean_T rtb_RelationalOperator_e;
  int8_T s19_iter;
  uint8_T sparkPlugFaultCnt;
  int32_T rtb_IndexVector;
  uint8_T rtb_Conversion;
  int16_T rtb_DwellInt15bit;
  uint32_T u0;

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  The aim of this block is to schedules spark plug test functionalities:
   *  1 Median for dwell integral is always calculated and updated.
   *  2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
   *  3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
   *  4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder.
   */
  /* Chart: '<Root>/Scheduler' incorporates:
   *  Constant: '<Root>/Constant'
   *  Constant: '<Root>/Constant1'
   *  Constant: '<Root>/Constant2'
   *  Constant: '<Root>/Constant3'
   *  Inport: '<Root>/FlgDSAoutDis'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/SAoutCyl'
   *
   * Block description for '<Root>/Scheduler':
   *  The aim of this block is to schedules spark plug test functionalities:
   *  1 Median for dwell integral is always calculated and updated.
   *  2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
   *  3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
   *  4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder.
   */
  /* During: Scheduler */
  /* The aim of this block is to schedules spark plug test functionalities:
     1 Median for dwell integral is always calculated and updated.
     2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
     3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
     4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder. */
  /* Entry Internal: Scheduler */
  /* Transition: '<S1>:64' */
  if ((*sfEvent) == ((int32_T)SparkPlugTest_event_ev_PowerOn)) {
    /* Outputs for Function Call SubSystem: '<S2>/Init_Median'
     *
     * Block description for '<S2>/Init_Median':
     *  Outputs initialization for median evaluation on dwell integral.
     *  Corresponding requirement has been already linked at the relative  transition implemented into Stateflow chart.
     */
    /* SignalConversion generated from: '<S6>/TbDwellInt' */
    /* Transition: '<S1>:30' */
    /* Transition: '<S1>:25':
     *  1. EISB_FCA6CYL_SW_REQ_1626: Software shall set to zero each output produced by spark plug foul... (ECU_SW_Requirements#3261)
     */
    /* Event: '<S1>:4' */
    memset((&(TbDwellInt[0])), 0, 40U * (sizeof(uint16_T)));

    /* SignalConversion generated from: '<S6>/DwellInt15bit' incorporates:
     *  Constant: '<S6>/Constant4'
     */
    DwellInt15bit = 0;

    /* End of Outputs for SubSystem: '<S2>/Init_Median' */

    /* Outputs for Function Call SubSystem: '<S2>/Init_Thresholds'
     *
     * Block description for '<S2>/Init_Thresholds':
     *  Thresholds initialization for dwell integral and actual spark advance.
     *  Corresponding requirement has been already linked at the relative transition implemented into Stateflow chart.
     */
    /* SignalConversion generated from: '<S8>/DwellIntThr' incorporates:
     *  Constant: '<S8>/Constant7'
     */
    /* Event: '<S1>:57' */
    /* Event: '<S1>:58' */
    DwellIntThr = 0U;

    /* SignalConversion generated from: '<S8>/ThrSAout' incorporates:
     *  Constant: '<S8>/Constant2'
     */
    ThrSAout = 0;

    /* End of Outputs for SubSystem: '<S2>/Init_Thresholds' */

    /* Outputs for Function Call SubSystem: '<S2>/Init_SparkPlug'
     *
     * Block description for '<S2>/Init_SparkPlug':
     *  Outputs initialization for spark plug faults management.
     */
    /* Outputs for Iterator SubSystem: '<S7>/PersistentSparkPlugFaultInit' incorporates:
     *  ForIterator: '<S24>/For Iterator'
     *
     * Block description for '<S7>/PersistentSparkPlugFaultInit':
     *  EE spark plug fault flag is enabled if the last value of spark fault counter stored in EE before last power on is greater than zero.
     *  Spark fault counter stored in EE is decremented by 1.
     */
    /* Outputs for Function Call SubSystem: '<S2>/Init_Filt'
     *
     * Block description for '<S2>/Init_Filt':
     *  Outputs initialization for filter on dwell integral median.
     *  Corresponding requirement has been already linked at the relative transition implemented into Stateflow chart.
     */
    /* Outputs for Function Call SubSystem: '<S2>/Init_Median'
     *
     * Block description for '<S2>/Init_Median':
     *  Outputs initialization for median evaluation on dwell integral.
     *  Corresponding requirement has been already linked at the relative  transition implemented into Stateflow chart.
     */
    /* Event: '<S1>:60' */
    for (rtb_IndexVector = 0; rtb_IndexVector < 8; rtb_IndexVector++) {
      /* SignalConversion generated from: '<S6>/VtDwellIntMedian' */
      VtDwellIntMedian[(rtb_IndexVector)] = 0U;

      /* SignalConversion generated from: '<S6>/VtCircularBufferIndex' */
      VtCircularBufferIndex[(rtb_IndexVector)] = 0U;

      /* SignalConversion generated from: '<S5>/DwellIntFilt' */
      DwellIntFilt[(rtb_IndexVector)] = 0U;

      /* SignalConversion generated from: '<S5>/VtdwellIntFiltHR' */
      VtDwellIntFiltHR[(rtb_IndexVector)] = 0;

      /* SignalConversion generated from: '<S7>/SparkPlugFaultCnt' */
      SparkPlugFaultCnt[(rtb_IndexVector)] = 0U;

      /* RelationalOperator: '<S24>/Relational Operator' incorporates:
       *  Constant: '<S24>/Constant2'
       *  MultiPortSwitch: '<S24>/Index Vector'
       *  SignalConversion generated from: '<S7>/SparkPlugFaultCntEE_in'
       */
      rtb_RelationalOperator_e = (((int32_T)SparkPlugFaultCntEE[(rtb_IndexVector)])
        > 0);

      /* Switch: '<S24>/Switch'
       *
       * Block requirements for '<S24>/Switch':
       *  1. EISB_FCA6CYL_SW_REQ_1385: For each "power-on --> power-off" sequence, software shall decreme... (ECU_SW_Requirements#3278)
       */
      if (rtb_RelationalOperator_e) {
        /* SignalConversion generated from: '<S7>/SparkPlugFaultCntEE' incorporates:
         *  MultiPortSwitch: '<S24>/Index Vector'
         *  SignalConversion generated from: '<S7>/SparkPlugFaultCntEE_in'
         *  Sum: '<S24>/Add'
         */
        SparkPlugFaultCntEE[(rtb_IndexVector)] = (uint16_T)(((uint32_T)
          SparkPlugFaultCntEE[(rtb_IndexVector)]) - 1U);
      } else {
        /* SignalConversion generated from: '<S7>/SparkPlugFaultCntEE' incorporates:
         *  Constant: '<S24>/Constant4'
         */
        SparkPlugFaultCntEE[(rtb_IndexVector)] = 0U;
      }

      /* End of Switch: '<S24>/Switch' */

      /* SignalConversion generated from: '<S7>/SparkPlugFaultStatus' incorporates:
       *  Switch: '<S24>/Switch1'
       */
      SparkPlugFaultStatus[(rtb_IndexVector)] = (uint8_T)
        (rtb_RelationalOperator_e ? ((uint8_T)1) : ((uint8_T)0));
    }

    /* End of Outputs for SubSystem: '<S2>/Init_Median' */
    /* End of Outputs for SubSystem: '<S2>/Init_Filt' */
    /* End of Outputs for SubSystem: '<S7>/PersistentSparkPlugFaultInit' */

    /* SignalConversion generated from: '<S7>/SparkPlugFault' incorporates:
     *  Constant: '<S25>/Constant'
     */
    SparkPlugFault = 0U;

    /* End of Outputs for SubSystem: '<S2>/Init_SparkPlug' */
    IdVer_SparkPlugTest = ID_VER_SPARKPLUGTEST_DEF;
  } else {
    /* Outputs for Function Call SubSystem: '<S2>/DwellIntMedian'
     *
     * Block description for '<S2>/DwellIntMedian':
     *  This block evaluate median on dwell integral through a circular
     *  buffer.
     *
     * Block requirements for '<S2>/DwellIntMedian':
     *  1. EISB_FCA6CYL_SW_REQ_1367: Software shall calculate the median value (i.e. VtDwellIntMedian),... (ECU_SW_Requirements#3262)
     */
    /* Assignment: '<S21>/Assignment' incorporates:
     *  Inport: '<Root>/DwellInt'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  MultiPortSwitch: '<S21>/Index Vector'
     *  MultiPortSwitch: '<S21>/Index Vector1'
     *  SignalConversion generated from: '<S4>/VtCircularBufferIndex_in'
     */
    /* Transition: '<S1>:20' */
    /* ev_EOA
       Always calculate Dwell Median  */
    /* Event: '<S1>:5' */
    TbDwellInt[((int32_T)IonAbsTdcEOA) + (((int32_T)((int8_T)
      VtCircularBufferIndex[(IonAbsTdcEOA)])) * 8)] = DwellInt[(IonAbsTdcEOA)];

    /* DataTypeConversion: '<S17>/Conversion' incorporates:
     *  Constant: '<S17>/Constant'
     */
    rtb_Conversion = (uint8_T)((((uint32_T)DWELLINTMEDIANLENGTH) << ((uint32_T)1))
      + 1U);

    /* Outputs for Iterator SubSystem: '<S4>/PrepareLocalBuffer' incorporates:
     *  ForIterator: '<S19>/For Iterator'
     *
     * Block description for '<S4>/PrepareLocalBuffer':
     *  This block updates the vector of dwell integral values used to pull out median.
     *  This block is linked to the same requirement of the parent subsystem.
     */
    for (s19_iter = 0; s19_iter < ((int8_T)rtb_Conversion); s19_iter++) {
      /* Sum: '<S19>/Add1' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  MultiPortSwitch: '<S21>/Index Vector1'
       *  SignalConversion generated from: '<S4>/VtCircularBufferIndex_in'
       */
      rtb_DwellInt15bit = (int16_T)((int32_T)(((int32_T)((int8_T)
        VtCircularBufferIndex[(IonAbsTdcEOA)])) - ((int32_T)s19_iter)));

      /* Switch: '<S19>/Switch' incorporates:
       *  Constant: '<S19>/Constant4'
       *  RelationalOperator: '<S19>/Relational Operator'
       *  Sum: '<S19>/Add'
       */
      if (rtb_DwellInt15bit < 0) {
        rtb_DwellInt15bit = (int16_T)(((int32_T)rtb_DwellInt15bit) + ((int32_T)
          ((uint8_T)MEDIAN_DWELL_BUFF_MAX_SIZE)));
      }

      /* End of Switch: '<S19>/Switch' */

      /* Assignment: '<S19>/Assignment' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Selector: '<S19>/Selector1'
       */
      vtDwellIntLocalBuff[(s19_iter)] = TbDwellInt[(((int32_T)rtb_DwellInt15bit)
        * 8) + ((int32_T)IonAbsTdcEOA)];
    }

    /* End of Outputs for SubSystem: '<S4>/PrepareLocalBuffer' */

    /* Sum: '<S20>/Add1' incorporates:
     *  Constant: '<S20>/Constant3'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  MultiPortSwitch: '<S21>/Index Vector1'
     *  SignalConversion generated from: '<S4>/VtCircularBufferIndex_in'
     */
    s19_iter = (int8_T)(((int8_T)VtCircularBufferIndex[(IonAbsTdcEOA)]) + 1);

    /* Switch: '<S20>/Switch' incorporates:
     *  Constant: '<S20>/Constant4'
     *  RelationalOperator: '<S20>/Relational Operator'
     */
    if (((int32_T)s19_iter) >= ((int32_T)((uint8_T)MEDIAN_DWELL_BUFF_MAX_SIZE)))
    {
      /* Assignment: '<S20>/Assignment1' incorporates:
       *  Constant: '<S20>/Constant5'
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      VtCircularBufferIndex[(IonAbsTdcEOA)] = 0U;
    } else {
      /* Assignment: '<S20>/Assignment1' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      VtCircularBufferIndex[(IonAbsTdcEOA)] = (uint8_T)s19_iter;
    }

    /* End of Switch: '<S20>/Switch' */

    /* S-Function (Eldor_InsertionSort): '<S22>/Eldor_InsertionSort1' */
    INSERTIONSORT( (&(vtDwellIntLocalBuff[0])), rtb_Conversion, ((uint8_T)5U),
                  &rtb_Eldor_InsertionSort1[0]);

    /* Product: '<S18>/Divide' */
    rtb_Conversion = (uint8_T)(((uint32_T)rtb_Conversion) >> ((uint32_T)1));

    /* Assignment: '<S18>/Assignment1' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  MultiPortSwitch: '<S18>/Index Vector2'
     */
    VtDwellIntMedian[(IonAbsTdcEOA)] = rtb_Eldor_InsertionSort1[rtb_Conversion];

    /* Product: '<S18>/Divide1' incorporates:
     *  MultiPortSwitch: '<S18>/Index Vector2'
     */
    DwellInt15bit = (int16_T)((uint32_T)(((uint32_T)
      rtb_Eldor_InsertionSort1[rtb_Conversion]) >> ((uint32_T)1)));

    /* End of Outputs for SubSystem: '<S2>/DwellIntMedian' */
    /* Enable spark plug test only if spark advance correction is stable */
    if (((int32_T)FlgDSAoutDis) == 0) {
      /* Transition: '<S1>:24':
       *  1. EISB_FCA6CYL_SW_REQ_1369: Software shall not execute spark plug test every time that high va... (ECU_SW_Requirements#3269)
       */
      /* Transition: '<S1>:23' */
      /*  Active spark plug test only for some engine state  */
      if ((((Rpm >= BKDWELLINTTHRRPM[0]) && (Rpm <= BKDWELLINTTHRRPM[(((uint8_T)
               BKDWELLINTTHRRPM_dim))])) && (Load >= BKDWELLINTTHRLOAD[0])) &&
          (Load <= BKDWELLINTTHRLOAD[(((uint8_T)BKDWELLINTTHRLOAD_dim))])) {
        /* Outputs for Function Call SubSystem: '<S2>/Thresholds'
         *
         * Block description for '<S2>/Thresholds':
         *  This block evaluates:
         *  - dwell integral threshold to detect spark plug fault.
         *  - spark advance threshold to enable spark plug test.
         *
         * Block requirements for '<S2>/Thresholds':
         *  1. EISB_FCA6CYL_SW_REQ_1375: Every time that engine working point (i.e. the couple engine speed... (ECU_SW_Requirements#3265)
         *  2. EISB_FCA6CYL_SW_REQ_1379: Software shall calculate an acceptable threshold (i.e. DwellIntThr... (ECU_SW_Requirements#3271)
         */
        /* S-Function (PreLookUpIdSearch_U16): '<S46>/PreLookUpIdSearch_U16' incorporates:
         *  Constant: '<S14>/Constant'
         *  Constant: '<S14>/Constant2'
         */
        /* Transition: '<S1>:38':
         *  1. EISB_FCA6CYL_SW_REQ_1370: Software shall not execute spark plug test algorithm if the engine... (ECU_SW_Requirements#3270)
         */
        /* Transition: '<S1>:42' */
        /* Event: '<S1>:8' */
        PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                              &rtb_PreLookUpIdSearch_U16_o2, Rpm,
                              &BKDWELLINTTHRRPM[0], ((uint8_T)
          BKDWELLINTTHRRPM_dim));

        /* S-Function (PreLookUpIdSearch_U16): '<S45>/PreLookUpIdSearch_U16' incorporates:
         *  Constant: '<S14>/Constant1'
         *  Constant: '<S14>/Constant3'
         */
        PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_g,
                              &rtb_PreLookUpIdSearch_U16_o2_e, Load,
                              &BKDWELLINTTHRLOAD[0], ((uint8_T)
          BKDWELLINTTHRLOAD_dim));

        /* S-Function (Look2D_IR_S16): '<S43>/Look2D_IR_S16' incorporates:
         *  Constant: '<S14>/Constant6'
         *  Constant: '<S14>/Constant7'
         *  Constant: '<S14>/Constant9'
         */
        Look2D_IR_S16( &rtb_Look2D_IR_S16, &TBSATHRDWELLINT[0],
                      rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                      ((uint8_T)BKDWELLINTTHRRPM_dim),
                      rtb_PreLookUpIdSearch_U16_o1_g,
                      rtb_PreLookUpIdSearch_U16_o2_e, ((uint8_T)
          BKDWELLINTTHRLOAD_dim));

        /* SignalConversion generated from: '<S14>/ThrSAout' */
        ThrSAout = rtb_Look2D_IR_S16;

        /* S-Function (Look2D_IR_U16): '<S44>/Look2D_IR_U16' incorporates:
         *  Constant: '<S14>/Constant4'
         *  Constant: '<S14>/Constant5'
         *  Constant: '<S14>/Constant8'
         */
        Look2D_IR_U16( &rtb_Look2D_IR_U16, &TBDWELLINTTHR[0],
                      rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                      ((uint8_T)BKDWELLINTTHRRPM_dim),
                      rtb_PreLookUpIdSearch_U16_o1_g,
                      rtb_PreLookUpIdSearch_U16_o2_e, ((uint8_T)
          BKDWELLINTTHRLOAD_dim));

        /* SignalConversion generated from: '<S14>/DwellIntThr' */
        DwellIntThr = rtb_Look2D_IR_U16;

        /* End of Outputs for SubSystem: '<S2>/Thresholds' */
        if (SAoutCyl[(IonAbsTdcEOA)] > ThrSAout) {
          /* Outputs for Function Call SubSystem: '<S2>/DWellIntFilt'
           *
           * Block description for '<S2>/DWellIntFilt':
           *  This block performs a first order filter on dwell integral median.
           *
           * Block requirements for '<S2>/DWellIntFilt':
           *  1. EISB_FCA6CYL_SW_REQ_1371: Software shall apply, cylinder by cylinder, a first order filter t... (ECU_SW_Requirements#3264)
           */
          /* SignalConversion generated from: '<S3>/dwellInt15bit' */
          /* Transition: '<S1>:44' */
          /* Transition: '<S1>:50' */
          /* Event: '<S1>:6' */
          rtb_DwellInt15bit = DwellInt15bit;

          /* SignalConversion generated from: '<S3>/resetFilt' */
          rtb_Conversion = 0U;

          /* MultiPortSwitch: '<S3>/Index Vector' incorporates:
           *  SignalConversion generated from: '<S3>/VtDwellIntFiltHR_In'
           */
          rtb_IndexVector = VtDwellIntFiltHR[(IonAbsTdcEOA)];

          /* S-Function (FOF_Reset_S16_FXP): '<S15>/FOF_Reset_S16_FXP' incorporates:
           *  Constant: '<S3>/Constant2'
           */
          FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1,
                            &rtb_FOF_Reset_S16_FXP_o2, rtb_DwellInt15bit,
                            DWELLINTKFILT, rtb_DwellInt15bit, rtb_Conversion,
                            rtb_IndexVector);

          /* MinMax: '<S3>/MinMax' */
          if (rtb_FOF_Reset_S16_FXP_o1 > 0) {
            rtb_DwellInt15bit = rtb_FOF_Reset_S16_FXP_o1;
          } else {
            rtb_DwellInt15bit = 0;
          }

          /* Assignment: '<S3>/Assignment' incorporates:
           *  Product: '<S3>/Product'
           */
          DwellIntFilt[(IonAbsTdcEOA)] = (uint16_T)((int32_T)(((int32_T)
            rtb_DwellInt15bit) * 2));

          /* Assignment: '<S3>/Assignment1' */
          VtDwellIntFiltHR[(IonAbsTdcEOA)] = rtb_FOF_Reset_S16_FXP_o2;

          /* End of Outputs for SubSystem: '<S2>/DWellIntFilt' */
          /* Transition: '<S1>:53' */
        } else {
          /* Outputs for Function Call SubSystem: '<S2>/DWellIntFilt'
           *
           * Block description for '<S2>/DWellIntFilt':
           *  This block performs a first order filter on dwell integral median.
           *
           * Block requirements for '<S2>/DWellIntFilt':
           *  1. EISB_FCA6CYL_SW_REQ_1371: Software shall apply, cylinder by cylinder, a first order filter t... (ECU_SW_Requirements#3264)
           */
          /* SignalConversion generated from: '<S3>/dwellInt15bit' */
          /* Transition: '<S1>:52':
           *  1. EISB_FCA6CYL_SW_REQ_1378: Software shall reset, cylinder by cylinder, median filter (i.e. Dw... (ECU_SW_Requirements#3266)
           */
          /* Event: '<S1>:6' */
          rtb_DwellInt15bit = DwellInt15bit;

          /* SignalConversion generated from: '<S3>/resetFilt' */
          rtb_Conversion = 1U;

          /* MultiPortSwitch: '<S3>/Index Vector' incorporates:
           *  SignalConversion generated from: '<S3>/VtDwellIntFiltHR_In'
           */
          rtb_IndexVector = VtDwellIntFiltHR[(IonAbsTdcEOA)];

          /* S-Function (FOF_Reset_S16_FXP): '<S15>/FOF_Reset_S16_FXP' incorporates:
           *  Constant: '<S3>/Constant2'
           */
          FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1,
                            &rtb_FOF_Reset_S16_FXP_o2, rtb_DwellInt15bit,
                            DWELLINTKFILT, rtb_DwellInt15bit, rtb_Conversion,
                            rtb_IndexVector);

          /* MinMax: '<S3>/MinMax' */
          if (rtb_FOF_Reset_S16_FXP_o1 > 0) {
            rtb_DwellInt15bit = rtb_FOF_Reset_S16_FXP_o1;
          } else {
            rtb_DwellInt15bit = 0;
          }

          /* Assignment: '<S3>/Assignment' incorporates:
           *  Product: '<S3>/Product'
           */
          DwellIntFilt[(IonAbsTdcEOA)] = (uint16_T)((int32_T)(((int32_T)
            rtb_DwellInt15bit) * 2));

          /* Assignment: '<S3>/Assignment1' */
          VtDwellIntFiltHR[(IonAbsTdcEOA)] = rtb_FOF_Reset_S16_FXP_o2;

          /* End of Outputs for SubSystem: '<S2>/DWellIntFilt' */
        }

        /* Outputs for Function Call SubSystem: '<S2>/SparkPlugMgm'
         *
         * Block description for '<S2>/SparkPlugMgm':
         *  This block detects spark plug faults.
         */
        /* If: '<S29>/If' incorporates:
         *  Constant: '<S32>/Constant'
         *  If: '<S32>/If'
         *  Inport: '<Root>/IonAbsTdcEOA'
         *  Inport: '<Root>/SAoutCyl'
         *  MultiPortSwitch: '<S27>/Index Vector'
         *  MultiPortSwitch: '<S27>/Index Vector1'
         *  RelationalOperator: '<S29>/Relational Operator'
         *  RelationalOperator: '<S32>/Relational Operator'
         *  SignalConversion generated from: '<S9>/DwellIntFilt'
         *  SignalConversion generated from: '<S9>/DwellIntThr'
         *  SignalConversion generated from: '<S9>/SparkPlugFaultCnt_in'
         */
        /* Transition: '<S1>:55' */
        /* Event: '<S1>:16' */
        if (DwellIntFilt[(IonAbsTdcEOA)] > DwellIntThr) {
          /* Outputs for IfAction SubSystem: '<S29>/DwellInt_Greater_Than_Threshold' incorporates:
           *  ActionPort: '<S31>/Action Port'
           *
           * Block description for '<S29>/DwellInt_Greater_Than_Threshold':
           *  This block evaluates if confirm spark plug fault detection counting
           *  spark plug fault consecutive events.
           */
          /* Sum: '<S35>/Add' incorporates:
           *  Constant: '<S35>/Constant1'
           *  MultiPortSwitch: '<S27>/Index Vector1'
           *  SignalConversion generated from: '<S9>/SparkPlugFaultCnt_in'
           */
          rtb_MinMax_e = (uint16_T)(((uint32_T)SparkPlugFaultCnt[(IonAbsTdcEOA)])
            + ((uint32_T)SPARKPLUGFAULTINC));

          /* MinMax: '<S35>/MinMax' incorporates:
           *  Constant: '<S35>/Constant'
           */
          if (SPARKPLUGFAULTTHR < rtb_MinMax_e) {
            rtb_MinMax_e = SPARKPLUGFAULTTHR;
          }

          /* End of MinMax: '<S35>/MinMax' */

          /* Switch: '<S34>/Switch' incorporates:
           *  ArithShift: '<S34>/Shift Arithmetic'
           *  Constant: '<S34>/Constant'
           *  Constant: '<S35>/Constant2'
           *  RelationalOperator: '<S35>/Relational Operator1'
           *  S-Function (sfix_bitop): '<S34>/Bitwise Operator'
           *  SignalConversion generated from: '<S9>/SparkPlugFault_in'
           *
           * Block requirements for '<S34>/Switch':
           *  1. EISB_FCA6CYL_SW_REQ_1381: Every time that spark plug counter (i.e. SparkPlugFaultCnt) reache... (ECU_SW_Requirements#3274)
           *
           * Block requirements for '<S35>/Relational Operator1':
           *  1. EISB_FCA6CYL_SW_REQ_1380: Every time that spark plug test has to be executed for the i_th cy... (ECU_SW_Requirements#3273)
           */
          if (rtb_MinMax_e >= SPARKPLUGFAULTTHR) {
            rtb_Conversion = (uint8_T)((int32_T)((1 << ((uint32_T)IonAbsTdcEOA))
              | ((int32_T)SparkPlugFault)));
          } else {
            rtb_Conversion = SparkPlugFault;
          }

          /* End of Switch: '<S34>/Switch' */

          /* DataTypeConversion: '<S35>/Conversion' */
          sparkPlugFaultCnt = (uint8_T)rtb_MinMax_e;

          /* End of Outputs for SubSystem: '<S29>/DwellInt_Greater_Than_Threshold' */
        } else {
          /* Outputs for IfAction SubSystem: '<S29>/DwellInt_Lower_Than_Threshold' incorporates:
           *  ActionPort: '<S32>/Action Port'
           *
           * Block description for '<S29>/DwellInt_Lower_Than_Threshold':
           *  This block evaluates if deletespark plug fault detection, it
           *  decrements spark plug fault consecutive events counter until it reachs
           *  0.
           */
          if (((int32_T)SparkPlugFaultCnt[(IonAbsTdcEOA)]) > 0) {
            /* Outputs for IfAction SubSystem: '<S32>/Decrement_Fault_Counter' incorporates:
             *  ActionPort: '<S36>/Action Port'
             *
             * Block description for '<S32>/Decrement_Fault_Counter':
             *  This block decrement spark plug fault counter.
             *
             * Block requirements for '<S32>/Decrement_Fault_Counter':
             *  1. EISB_FCA6CYL_SW_REQ_1382: Every time that spark plug test is executed for the i_th cylinder,... (ECU_SW_Requirements#3275)
             */
            /* If: '<S32>/If' incorporates:
             *  Inport: '<S36>/SparkPlugFault_in'
             *  MultiPortSwitch: '<S27>/Index Vector1'
             *  SignalConversion generated from: '<S9>/SparkPlugFaultCnt_in'
             *  SignalConversion generated from: '<S9>/SparkPlugFault_in'
             *  Sum: '<S36>/Add'
             */
            sparkPlugFaultCnt = (uint8_T)(((uint32_T)SparkPlugFaultCnt
              [(IonAbsTdcEOA)]) - 1U);
            rtb_Conversion = SparkPlugFault;

            /* End of Outputs for SubSystem: '<S32>/Decrement_Fault_Counter' */
          } else {
            /* Outputs for IfAction SubSystem: '<S32>/Disable_Fault' incorporates:
             *  ActionPort: '<S37>/Action Port'
             *
             * Block description for '<S32>/Disable_Fault':
             *  This block evaluates if deleting spark plug fault detection managing
             *  spark plug fault bit mask
             *
             * Block requirements for '<S32>/Disable_Fault':
             *  1. EISB_FCA6CYL_SW_REQ_1383: Every time that spark plug counter (i.e. SparkPlugFaultCnt) reache... (ECU_SW_Requirements#3276)
             */
            /* If: '<S32>/If' incorporates:
             *  ArithShift: '<S37>/Shift Arithmetic'
             *  Constant: '<S37>/Constant'
             *  Constant: '<S37>/Constant1'
             *  S-Function (sfix_bitop): '<S37>/Bitwise Operator'
             *  S-Function (sfix_bitop): '<S37>/Bitwise Operator1'
             *  SignalConversion generated from: '<S37>/SparkPlugFault'
             *  SignalConversion generated from: '<S37>/sparkPlugFaultCnt'
             *  SignalConversion generated from: '<S9>/SparkPlugFault_in'
             */
            sparkPlugFaultCnt = 0U;
            rtb_Conversion = (uint8_T)(((uint8_T)(~((uint8_T)((uint32_T)
              (((uint32_T)1) << ((uint32_T)IonAbsTdcEOA)))))) & SparkPlugFault);

            /* End of Outputs for SubSystem: '<S32>/Disable_Fault' */
          }

          /* End of Outputs for SubSystem: '<S29>/DwellInt_Lower_Than_Threshold' */
        }

        /* End of If: '<S29>/If' */

        /* Assignment: '<S26>/Assignment' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        SparkPlugFaultCnt[(IonAbsTdcEOA)] = sparkPlugFaultCnt;

        /* ArithShift: '<S40>/Shift Arithmetic' incorporates:
         *  Constant: '<S40>/Constant2'
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        sparkPlugFaultCnt = (uint8_T)((uint32_T)(((uint32_T)1) << ((uint32_T)
          IonAbsTdcEOA)));

        /* If: '<S30>/If' incorporates:
         *  Constant: '<S40>/Constant1'
         *  Constant: '<S40>/Constant3'
         *  Logic: '<S40>/Logical Operator'
         *  RelationalOperator: '<S40>/Relational Operator'
         *  RelationalOperator: '<S40>/Relational Operator1'
         *  S-Function (sfix_bitop): '<S40>/Bitwise Operator'
         *  S-Function (sfix_bitop): '<S40>/Bitwise Operator1'
         *  SignalConversion generated from: '<S9>/SparkPlugFault_in'
         */
        if (((((int32_T)SparkPlugFault) & ((int32_T)sparkPlugFaultCnt)) != 0) &&
            ((((int32_T)rtb_Conversion) & ((int32_T)sparkPlugFaultCnt)) != 0)) {
          /* Outputs for IfAction SubSystem: '<S30>/IF_EVALUATION' incorporates:
           *  ActionPort: '<S41>/Action Port'
           *
           * Block description for '<S30>/IF_EVALUATION':
           *  This block increases the fault counter to be stored in EE.
           *  Morevoer it disables the EE fault status flag. When two consecutive faults are detected, if EE fault status flag is true, then fault counter is increased by 1.
           */
          /* Sum: '<S41>/Add1' incorporates:
           *  Constant: '<S41>/Constant1'
           *  Inport: '<Root>/IonAbsTdcEOA'
           *  MultiPortSwitch: '<S27>/Index Vector2'
           *  MultiPortSwitch: '<S27>/Index Vector3'
           *  SignalConversion generated from: '<S9>/SparkPlugFaultCntEE_in'
           *  SignalConversion generated from: '<S9>/SparkPlugFaultStatus_in'
           *  Sum: '<S41>/Add'
           *
           * Block requirements for '<S41>/Add1':
           *  1. EISB_FCA6CYL_SW_REQ_1384: Every time that a spark plug fault is validated for two consecutiv... (ECU_SW_Requirements#3277)
           */
          u0 = (((uint32_T)SparkPlugFaultStatus[(IonAbsTdcEOA)]) + ((uint32_T)
                 SPARKPLUGFAULTDCYINC)) + ((uint32_T)SparkPlugFaultCntEE
            [(IonAbsTdcEOA)]);

          /* MinMax: '<S41>/MinMax' incorporates:
           *  Constant: '<S41>/Constant2'
           */
          if (u0 < MAX_UINT16_T) {
            /* DataTypeConversion: '<S41>/Conversion' */
            rtb_MinMax_e = (uint16_T)u0;
          } else {
            /* DataTypeConversion: '<S41>/Conversion' */
            rtb_MinMax_e = (uint16_T)MAX_UINT16_T;
          }

          /* End of MinMax: '<S41>/MinMax' */

          /* SignalConversion generated from: '<S41>/Constant' incorporates:
           *  Constant: '<S41>/Constant'
           */
          sparkPlugFaultCnt = 0U;

          /* End of Outputs for SubSystem: '<S30>/IF_EVALUATION' */
        } else {
          /* Outputs for IfAction SubSystem: '<S30>/ELSE_EVALUATION' incorporates:
           *  ActionPort: '<S39>/Action Port'
           *
           * Block description for '<S30>/ELSE_EVALUATION':
           *  This block holds the last value computed for EE signals, so no
           *  requirement has been linked to it.
           */
          /* SignalConversion generated from: '<S39>/ELSE_BUS_OUT' incorporates:
           *  Inport: '<Root>/IonAbsTdcEOA'
           *  MultiPortSwitch: '<S27>/Index Vector3'
           *  SignalConversion generated from: '<S9>/SparkPlugFaultStatus_in'
           */
          sparkPlugFaultCnt = SparkPlugFaultStatus[(IonAbsTdcEOA)];

          /* SignalConversion generated from: '<S39>/ELSE_BUS_OUT' incorporates:
           *  Inport: '<Root>/IonAbsTdcEOA'
           *  MultiPortSwitch: '<S27>/Index Vector2'
           *  SignalConversion generated from: '<S9>/SparkPlugFaultCntEE_in'
           */
          rtb_MinMax_e = SparkPlugFaultCntEE[(IonAbsTdcEOA)];

          /* End of Outputs for SubSystem: '<S30>/ELSE_EVALUATION' */
        }

        /* End of If: '<S30>/If' */

        /* Assignment: '<S26>/Assignment1' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        SparkPlugFaultStatus[(IonAbsTdcEOA)] = sparkPlugFaultCnt;

        /* Assignment: '<S26>/Assignment2' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        SparkPlugFaultCntEE[(IonAbsTdcEOA)] = rtb_MinMax_e;

        /* SignalConversion generated from: '<S9>/SparkPlugFault' */
        SparkPlugFault = rtb_Conversion;

        /* End of Outputs for SubSystem: '<S2>/SparkPlugMgm' */
      } else {
        /* Outputs for Function Call SubSystem: '<S2>/DWellIntFilt'
         *
         * Block description for '<S2>/DWellIntFilt':
         *  This block performs a first order filter on dwell integral median.
         *
         * Block requirements for '<S2>/DWellIntFilt':
         *  1. EISB_FCA6CYL_SW_REQ_1371: Software shall apply, cylinder by cylinder, a first order filter t... (ECU_SW_Requirements#3264)
         */
        /* SignalConversion generated from: '<S3>/dwellInt15bit' */
        /* Transition: '<S1>:40':
         *  1. EISB_FCA6CYL_SW_REQ_1429: Software shall reset, cylinder by cylinder, median filter (i.e. Dw... (ECU_SW_Requirements#3267)
         */
        /* Reset only filter  */
        /* Event: '<S1>:6' */
        rtb_DwellInt15bit = DwellInt15bit;

        /* SignalConversion generated from: '<S3>/resetFilt' */
        rtb_Conversion = 1U;

        /* MultiPortSwitch: '<S3>/Index Vector' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         *  SignalConversion generated from: '<S3>/VtDwellIntFiltHR_In'
         */
        rtb_IndexVector = VtDwellIntFiltHR[(IonAbsTdcEOA)];

        /* S-Function (FOF_Reset_S16_FXP): '<S15>/FOF_Reset_S16_FXP' incorporates:
         *  Constant: '<S3>/Constant2'
         */
        FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
                          rtb_DwellInt15bit, DWELLINTKFILT, rtb_DwellInt15bit,
                          rtb_Conversion, rtb_IndexVector);

        /* MinMax: '<S3>/MinMax' */
        if (rtb_FOF_Reset_S16_FXP_o1 > 0) {
          rtb_DwellInt15bit = rtb_FOF_Reset_S16_FXP_o1;
        } else {
          rtb_DwellInt15bit = 0;
        }

        /* Assignment: '<S3>/Assignment' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         *  Product: '<S3>/Product'
         */
        DwellIntFilt[(IonAbsTdcEOA)] = (uint16_T)((int32_T)(((int32_T)
          rtb_DwellInt15bit) * 2));

        /* Assignment: '<S3>/Assignment1' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        VtDwellIntFiltHR[(IonAbsTdcEOA)] = rtb_FOF_Reset_S16_FXP_o2;

        /* End of Outputs for SubSystem: '<S2>/DWellIntFilt' */
      }
    } else {
      /* Transition: '<S1>:32' */
    }
  }

  /* End of Chart: '<Root>/Scheduler' */
}

/*
 * Output and update for function-call system: '<Root>/Scheduler'
 * Block description for: '<Root>/Scheduler'
 *   The aim of this block is to schedules spark plug test functionalities:
 *   1 Median for dwell integral is always calculated and updated.
 *   2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
 *   3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
 *   4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder.
 */
void SparkPlugTest_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[2];
  int32_T i;

  /* Chart: '<Root>/Scheduler' incorporates:
   *  TriggerPort: '<S1>/input events'
   *
   * Block description for '<Root>/Scheduler':
   *  The aim of this block is to schedules spark plug test functionalities:
   *  1 Median for dwell integral is always calculated and updated.
   *  2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
   *  3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
   *  4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder.
   */
  for (i = 0; i < 2; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S1>:1' */
    i = (int32_T)SparkPlugTest_event_ev_PowerOn;
    Spar_chartstep_c3_SparkPlugTest(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S1>:2' */
    i = (int32_T)SparkPlugTest_event_ev_EOA;
    Spar_chartstep_c3_SparkPlugTest(&i);
  }
}

/* Model step function */
void SparkPlugTest_EOA(void)
{
  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  The aim of this block is to schedules spark plug test functionalities:
   *  1 Median for dwell integral is always calculated and updated.
   *  2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
   *  3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
   *  4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/SparkPlugTest_EOA' */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  The aim of this block is to schedules spark plug test functionalities:
   *  1 Median for dwell integral is always calculated and updated.
   *  2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
   *  3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
   *  4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder.
   */
  SparkPlugTest_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SparkPlugTest_EOA' */
}

/* Model step function */
void SparkPlugTest_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/SparkPlugTest_PowerOn' incorporates:
   *  Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  The aim of this block is to schedules spark plug test functionalities:
   *  1 Median for dwell integral is always calculated and updated.
   *  2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
   *  3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
   *  4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder.
   */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  The aim of this block is to schedules spark plug test functionalities:
   *  1 Median for dwell integral is always calculated and updated.
   *  2 First order filter on dwell median is performed only when spark advance is stable (no high variation).
   *  3 Filter is resetted to last dwell median value if engine state (rpm, load) is out of some thresholds or if actual spark advance correction is greater than a threshold.
   *  4. Spark plug test (dwell filter is compared versus an apposite threshold) is performed to detect spark faults for each cylinder.
   */
  SparkPlugTest_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SparkPlugTest_PowerOn' */
}

/* Model initialize function */
void SparkPlugTest_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T SparkPlugFault;
uint16_T DwellIntFilt[N_CYL_MAX];
void SparkPlugTest_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    DwellIntFilt[idx] = 0u;
  }

  SparkPlugFault = 0u;
}

void SparkPlugTest_PowerOn(void)
{
  SparkPlugTest_Stub();
}

void SparkPlugTest_EOA(void)
{
  SparkPlugTest_Stub();
}

#endif                                 /* _BUILD_SPARKPLUGTEST_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/