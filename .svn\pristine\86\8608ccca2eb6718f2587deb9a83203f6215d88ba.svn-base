#ifndef J2012_2007_H
#define J2012_2007_H

/* Failure Type definition according with Appendix F0 of SAE J2012 Revised DEC2007 */
#define J2012_0x00                       0x00u // NO_SUB_TYPE_INFORMATION
#define J2012_0x01                       0x01u // GENERAL_ELECTRIC_FAILURE
#define J2012_0x02                       0x02u // GENERAL_SIGNAL_FAILURE
#define J2012_0x03                       0x03u // FM_PWM_FAILURES
#define J2012_0x04                       0x04u // SYSTEM_INTERNAL_FAILURES
#define J2012_0x05                       0x05u // SYSTEM_PROGRAMMING_FAILURES
#define J2012_0x06                       0x06u // ALGORITHM_BASED_FAILURES
#define J2012_0x07                       0x07u // MECHANICAL_FAILURES
#define J2012_0x08                       0x08u // BUS_SIGNAL_MESSAGE_FAILURES
#define J2012_0x09                       0x09u // COMPONENT_FAILURES
//#define ISO_SAE_RESERVED               0x0Au
//#define ISO_SAE_RESERVED               0x0Bu
//#define ISO_SAE_RESERVED               0x0Cu
//#define ISO_SAE_RESERVED               0x0Du
//#define ISO_SAE_RESERVED               0x0Eu
//#define ISO_SAE_RESERVED               0x0Fu
//#define ISO_SAE_RESERVED               0x10u
#define J2012_0x11                       0x11u // CIRCUIT_SHORT_TO_GROUND
#define J2012_0x12                       0x12u // CIRCUIT_SHORT_TO_BATTERY
#define J2012_0x13                       0x13u // CIRCUIT_OPEN
#define J2012_0x14                       0x14u // CIRCUIT_SHORT_TO_GROUND_OR_OPEN
#define J2012_0x15                       0x15u // CIRCUIT_SHORT_TO_BATTERY_OR_OPEN
#define J2012_0x16                       0x16u // CIRCUIT_VOLTAGE_BELOW_THRESHOLD
#define J2012_0x17                       0x17u // CIRCUIT_VOLTAGE_ABOVE_THRESHOLD
#define J2012_0x18                       0x18u // CIRCUIT_CURRENT_BELOW_THRESHOLD
#define J2012_0x19                       0x19u // CIRCUIT_CURRENT_ABOVE_THRESHOLD
#define J2012_0x1A                       0x1Au // CIRCUIT_RESISTANCE_BELOW_THRESHOLD
#define J2012_0x1B                       0x1Bu // CIRCUIT_RESISTANCE_ABOVE_THRESHOLD
#define J2012_0x1C                       0x1Cu // CIRCUIT_VOLTAGE_OUT_OF_RANGE
#define J2012_0x1D                       0x1Du // CIRCUIT_CURRENT_OUT_OF_RANGE
#define J2012_0x1E                       0x1Eu // CIRCUIT_RESISTANCE_OUT_OF_RANGE
#define J2012_0x1F                       0x1Fu // CIRCUIT_INTERMITTENT
//#define ISO_SAE_RESERVED               0x20u
#define J2012_0x21                       0x21u // SIGNAL_AMPLITUDE_BELOW_MINIMUM
#define J2012_0x22                       0x22u // SIGNAL_AMPLITUDE_ABOVE_MAXIMUM
#define J2012_0x23                       0x23u // SIGNAL_STUCK_LOW
#define J2012_0x24                       0x24u // SIGNAL_STUCK_HIGH
#define J2012_0x25                       0x25u // SIGNAL_SHAPE_WAVEFORM_FAILURE
#define J2012_0x26                       0x26u // SIGNAL_RATE_OF_CHANGE_BELOW_THRESHOLD
#define J2012_0x27                       0x27u // SIGNAL_RATE_OF_CHANGE_ABOVE_THRESHOLD
#define J2012_0x28                       0x28u // SIGNAL_BIAS_LEVEL_OUT_OF_RANGE_ZERO_ADJUSTMENT_FAILURE
#define J2012_0x29                       0x29u // SIGNAL_INVALID
#define J2012_0x2A                       0x2Au // SIGNAL_STUCK_IN_RANGE
#define J2012_0x2B                       0x2Bu // SIGNAL_CROSS_COUPLED
//#define ISO_SAE_RESERVED               0x2Cu
//#define ISO_SAE_RESERVED               0x2Du
//#define ISO_SAE_RESERVED               0x2Eu
#define J2012_0x2F                       0x2Fu // SIGNAL_ERRATIC
//#define ISO_SAE_RESERVED               0x30u
#define J2012_0x31                       0x31u // NO_SIGNAL
#define J2012_0x32                       0x32u // SIGNAL_LOW_TIME_BELOW_MINIMUM
#define J2012_0x33                       0x33u // SIGNAL_LOW_TIME_ABOVE_MAXIMUM
#define J2012_0x34                       0x34u // SIGNAL_HIGH_TIME_BELOW_MINIMUM
#define J2012_0x35                       0x35u // SIGNAL_HIGH_TIME_ABOVE_MAXIMUM
#define J2012_0x36                       0x36u // SIGNAL_FREQUENCY_TOO_LOW
#define J2012_0x37                       0x37u // SIGNAL_FREQUENCY_TOO_HIGH
#define J2012_0x38                       0x38u // SIGNAL_FREQUENCY_INCORRECT
#define J2012_0x39                       0x39u // SIGNAL_HAS_TOO_FEW_PULSES
#define J2012_0x3A                       0x3Au // SIGNAL_HAS_TOO_MANY_PULSES
//#define ISO_SAE_RESERVED               0x3Bu
//#define ISO_SAE_RESERVED               0x3Cu
//#define ISO_SAE_RESERVED               0x3Du
//#define ISO_SAE_RESERVED               0x3Eu
//#define ISO_SAE_RESERVED               0x3Fu
//#define ISO_SAE_RESERVED               0x40u
#define J2012_0x41                       0x41u // GENERAL_CHECKSUM_FAILURE
#define J2012_0x42                       0x42u // GENERAL_MEMORY_FAILURE
#define J2012_0x43                       0x43u // SPECIAL_MEMORY_FAILURE
#define J2012_0x44                       0x44u // DATA_MEMORY_FAILURE
#define J2012_0x45                       0x45u // PROGRAM_MEMORY_FAILURE
#define J2012_0x46                       0x46u // CALIBRATION_PARAMETER_MEMORY_FAILURE
#define J2012_0x47                       0x47u // WATCHDOG_SAFETY_uC_FAILURE
#define J2012_0x48                       0x48u // SUPERVISION_SOFTWARE_FAILURE
#define J2012_0x49                       0x49u // INTERNAL_ELECTRONIC_FAILURE
#define J2012_0x4A                       0x4Au // INCORRECT_COMPONENT_INSTALLED
#define J2012_0x4B                       0x4Bu // OVER_TEMPERATURE
//#define ISO_SAE_RESERVED               0x4Cu
//#define ISO_SAE_RESERVED               0x4Du
//#define ISO_SAE_RESERVED               0x4Eu
//#define ISO_SAE_RESERVED               0x4Fu
//#define ISO_SAE_RESERVED               0x50u
#define J2012_0x51                       0x51u // NOT_PROGRAMMED
#define J2012_0x52                       0x52u // NOT_ACTIVATED
#define J2012_0x53                       0x53u // DEACTIVATED
#define J2012_0x54                       0x54u // MISSING_CALIBRATION
#define J2012_0x55                       0x55u // NOT_CONFIGURED
#define J2012_0x56                       0x56u // INVALID_OR_INCOMPATIBLE_CONFIGURATION
#define J2012_0x57                       0x57u // INVALID_OR_INCOMPATIBLE_SW_CONFIGURATION
//#define ISO_SAE_RESERVED               0x5Fu
//#define ISO_SAE_RESERVED               0x5Fu
//#define ISO_SAE_RESERVED               0x5Fu
//#define ISO_SAE_RESERVED               0x5Fu
//#define ISO_SAE_RESERVED               0x5Fu
//#define ISO_SAE_RESERVED               0x5Fu
//#define ISO_SAE_RESERVED               0x5Fu
//#define ISO_SAE_RESERVED               0x60u
#define J2012_0x61                       0x61u // SIGNAL_CALCULATION_FAILURE
#define J2012_0x62                       0x62u // SIGNAL_COMPARE_FAILURE
#define J2012_0x63                       0x63u // CIRCUIT_COMPONENT_PROTECTION_TIME_OUT
#define J2012_0x64                       0x64u // SIGNAL_PLAUSIBILITY_FAILURE
#define J2012_0x65                       0x65u // SIGNAL_HAS_TOO_FEW_TRANSITIONS_EVENTS
#define J2012_0x66                       0x66u // SIGNAL_HAS_TOO_MANY_TRANSITIONS_EVENTS
#define J2012_0x67                       0x67u // SIGNAL_INCORRECT_AFTER_EVENT
#define J2012_0x68                       0x68u // EVENT_INFORMATION
//#define ISO_SAE_RESERVED               0x69u
//#define ISO_SAE_RESERVED               0x6Au
//#define ISO_SAE_RESERVED               0x6Bu
//#define ISO_SAE_RESERVED               0x6Cu
//#define ISO_SAE_RESERVED               0x6Du
//#define ISO_SAE_RESERVED               0x6Eu
//#define ISO_SAE_RESERVED               0x6Fu
//#define ISO_SAE_RESERVED               0x70u
#define J2012_0x71                       0x71u // ACTUATOR_STUCK
#define J2012_0x72                       0x72u // ACTUATOR_STUCK_OPEN
#define J2012_0x73                       0x73u // ACTUATOR_STUCK_CLOSED
#define J2012_0x74                       0x74u  // ACTUATOR_SLIPPING
#define J2012_0x75                       0x75u // EMERGENCY_POSITION_NOT_REACHABLE
#define J2012_0x76                       0x76u // WRONG_MOUNTING_POSITION
#define J2012_0x77                       0x77u // COMMANDED_POSITION_NOT_REACHABLE
#define J2012_0x78                       0x78u // ALIGNMENT_OR_ADJUSTMENT_INCORRECT
#define J2012_0x79                       0x79u // MECHANICAL_LINKAGE_FAILURE
#define J2012_0x7A                       0x7Au // FLUID_LEAK_OR_SEAL_FAILURE
#define J2012_0x7B                       0x7Bu // LOW_FLUID_LEVEL
//#define ISO_SAE_RESERVED               0x7Cu
//#define ISO_SAE_RESERVED               0x7Du
//#define ISO_SAE_RESERVED               0x7Eu
//#define ISO_SAE_RESERVED               0x7Fu
//#define ISO_SAE_RESERVED               0x80u
#define J2012_0x81                       0x81u // INVALID_SERIAL_DATA_RECEIVED
#define J2012_0x82                       0x82u // ALIVE_SEQUENCE_COUNTER_INCORRECT_NOT_UPDATED
#define J2012_0x83                       0x83u // VALUE_OF_SIGNAL_PROTECTION_CALCULATION_INCORRECT
#define J2012_0x84                       0x84u // SIGNAL_BELOW_ALLOWABLE_RANGE
#define J2012_0x85                       0x85u // SIGNAL_ABOVE_ALLOWABLE_RANGE
#define J2012_0x86                       0x86u // SIGNAL_INVALID
#define J2012_0x87                       0x87u // MISSING_MESSAGE
#define J2012_0x88                       0x88u // BUS_OFF
//#define ISO_SAE_RESERVED               0x89u
//#define ISO_SAE_RESERVED               0x8Au
//#define ISO_SAE_RESERVED               0x8Bu
//#define ISO_SAE_RESERVED               0x8Cu
//#define ISO_SAE_RESERVED               0x8Du
//#define ISO_SAE_RESERVED               0x8Eu
#define J2012_0x8F                       0x8Fu // ERRATIC
//#define ISO_SAE_RESERVED               0x90u
#define J2012_0x91                       0x91u // PARAMETRIC
#define J2012_0x92                       0x92u // PERFORMANCE_OR_INCORRECT_OPERATION
#define J2012_0x93                       0x93u // NO_OPERATION
#define J2012_0x94                       0x94u // UNEXPECTED_OPERATION
#define J2012_0x95                       0x95u // INCORRECT_ASSEMBLY
#define J2012_0x96                       0x96u // COMPONENT_INTERNAL_FAILURE
#define J2012_0x97                       0x97u // COMPONENT_OR_SYSTEM_OPERATION_OBSTRUCTED_OR_BLOCKED
#define J2012_0x98                       0x98u // COMPONENT_OR_SYSTEM_OVER_TEMPERATURE
//#define ISO_SAE_RESERVED               0x99u
#define J2012_0x9A                       0x9Au // COMPONENT_OR_SYSTEM_OPERAING_CONDITIONS
//#define ISO_SAE_RESERVED               0x9Bu
//#define ISO_SAE_RESERVED               0x9Cu
//#define ISO_SAE_RESERVED               0x9Du
//#define ISO_SAE_RESERVED               0x9Eu
//#define ISO_SAE_RESERVED               0x9Fu
//#define ISO_SAE_RESERVED               0xA0u
//#define ISO_SAE_RESERVED               0xA1u
//#define ISO_SAE_RESERVED               0xA2u
//#define ISO_SAE_RESERVED               0xA3u
//#define ISO_SAE_RESERVED               0xA4u
//#define ISO_SAE_RESERVED               0xA5u
//#define ISO_SAE_RESERVED               0xA6u
//#define ISO_SAE_RESERVED               0xA7u
//#define ISO_SAE_RESERVED               0xA8u
//#define ISO_SAE_RESERVED               0xA9u
//#define ISO_SAE_RESERVED               0xAAu
//#define ISO_SAE_RESERVED               0xABu
//#define ISO_SAE_RESERVED               0xACu
//#define ISO_SAE_RESERVED               0xADu
//#define ISO_SAE_RESERVED               0xAEu
//#define ISO_SAE_RESERVED               0xAFu
//#define ISO_SAE_RESERVED               0xB0u
//#define ISO_SAE_RESERVED               0xB1u
//#define ISO_SAE_RESERVED               0xB2u
//#define ISO_SAE_RESERVED               0xB3u
//#define ISO_SAE_RESERVED               0xB4u
//#define ISO_SAE_RESERVED               0xB5u
//#define ISO_SAE_RESERVED               0xB6u
//#define ISO_SAE_RESERVED               0xB7u
//#define ISO_SAE_RESERVED               0xB8u
//#define ISO_SAE_RESERVED               0xB9u
//#define ISO_SAE_RESERVED               0xBAu
//#define ISO_SAE_RESERVED               0xBBu
//#define ISO_SAE_RESERVED               0xBCu
//#define ISO_SAE_RESERVED               0xBDu
//#define ISO_SAE_RESERVED               0xBEu
//#define ISO_SAE_RESERVED               0xBFu
//#define ISO_SAE_RESERVED               0xC0u
//#define ISO_SAE_RESERVED               0xC1u
//#define ISO_SAE_RESERVED               0xC2u
//#define ISO_SAE_RESERVED               0xC3u
//#define ISO_SAE_RESERVED               0xC4u
//#define ISO_SAE_RESERVED               0xC5u
//#define ISO_SAE_RESERVED               0xC6u
//#define ISO_SAE_RESERVED               0xC7u
//#define ISO_SAE_RESERVED               0xC8u
//#define ISO_SAE_RESERVED               0xC9u
//#define ISO_SAE_RESERVED               0xCAu
//#define ISO_SAE_RESERVED               0xCBu
//#define ISO_SAE_RESERVED               0xCCu
//#define ISO_SAE_RESERVED               0xCDu
//#define ISO_SAE_RESERVED               0xCEu
//#define ISO_SAE_RESERVED               0xCFu
//#define ISO_SAE_RESERVED               0xD0u
//#define ISO_SAE_RESERVED               0xD1u
//#define ISO_SAE_RESERVED               0xD2u
//#define ISO_SAE_RESERVED               0xD3u
//#define ISO_SAE_RESERVED               0xD4u
//#define ISO_SAE_RESERVED               0xD5u
//#define ISO_SAE_RESERVED               0xD6u
//#define ISO_SAE_RESERVED               0xD7u
//#define ISO_SAE_RESERVED               0xD8u
//#define ISO_SAE_RESERVED               0xD9u
//#define ISO_SAE_RESERVED               0xDAu
//#define ISO_SAE_RESERVED               0xDBu
//#define ISO_SAE_RESERVED               0xDCu
//#define ISO_SAE_RESERVED               0xDDu
//#define ISO_SAE_RESERVED               0xDEu
//#define ISO_SAE_RESERVED               0xDFu
//#define ISO_SAE_RESERVED               0xE0u
//#define ISO_SAE_RESERVED               0xE1u
//#define ISO_SAE_RESERVED               0xE2u
//#define ISO_SAE_RESERVED               0xE3u
//#define ISO_SAE_RESERVED               0xE4u
//#define ISO_SAE_RESERVED               0xE5u
//#define ISO_SAE_RESERVED               0xE6u
//#define ISO_SAE_RESERVED               0xE7u
//#define ISO_SAE_RESERVED               0xE8u
//#define ISO_SAE_RESERVED               0xE9u
//#define ISO_SAE_RESERVED               0xEAu
//#define ISO_SAE_RESERVED               0xEBu
//#define ISO_SAE_RESERVED               0xECu
//#define ISO_SAE_RESERVED               0xEDu
//#define ISO_SAE_RESERVED               0xEEu
//#define ISO_SAE_RESERVED               0xEFu
#define J2012_0xF0                       0xF0u  // Eldor for SIGNAL_NOT_PLAUSIBLE_0
#define J2012_0xF1                       0xF1u  // Eldor for SIGNAL_NOT_PLAUSIBLE_1
#define J2012_0xF2                       0xF2u  // Eldor for SIGNAL_NOT_PLAUSIBLE_2
#define J2012_0xF3                       0xF3u  // Eldor for SIGNAL_NOT_PLAUSIBLE_3
#define J2012_0xF4                       0xF4u  // Eldor for SIGNAL_NOT_PLAUSIBLE_4
//#define MANUFACTURE_DEFINED            0xF5u
//#define MANUFACTURE_DEFINED            0xF6u
//#define MANUFACTURE_DEFINED            0xF7u
//#define MANUFACTURE_DEFINED            0xF8u
//#define MANUFACTURE_DEFINED            0xF9u
//#define MANUFACTURE_DEFINED            0xFAu
//#define MANUFACTURE_DEFINED            0xFBu
//#define MANUFACTURE_DEFINED            0xFCu
//#define MANUFACTURE_DEFINED            0xFDu
//#define MANUFACTURE_DEFINED            0xFEu
//#define MANUFACTURE_DEFINED            0xFFu

#endif // J2012_2007_H

