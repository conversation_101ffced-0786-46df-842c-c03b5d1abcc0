
rem Generate machine code for the MultiChannel Sequencer 0 -  BANK 0: CmdIn, CmdOut, <PERSON> andAdcTrigIpri
asm-mcs.exe source\mcs0.mcs -o output\mcs0.c -odef output\mcs0.h -olbl mcs0_mem -otyp "const uint32_t" -I include -log output\mcs0.log

rem Generate machine code for the MultiChannel Sequencer 1 -  BANK 1: CmdIn, CmdOut, Buck andAdcTrigIpri 
asm-mcs.exe source\mcs1.mcs -o output\mcs1.c -odef output\mcs1.h -olbl mcs1_mem -otyp "const uint32_t" -I include -log output\mcs1.log

rem Generate machine code for the MultiChannel Sequencer 2 -  MOS, ...
asm-mcs.exe source\mcs2.mcs -o output\mcs2.c -odef output\mcs2.h -olbl mcs2_mem -otyp "const uint32_t" -I include -log output\mcs2.log

echo Removing old files.
if exist ..\tree\bios\sys\auto\*.* del ..\tree\bios\sys\auto\*.*

echo Moving generated files:
if exist output\*.h move output\*.h ..\tree\bios\sys\auto\
if exist output\*.c move output\*.c ..\tree\bios\sys\auto\

pause