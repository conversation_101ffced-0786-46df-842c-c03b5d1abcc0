/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmOut
**  Filename        :  CanMgmOut_out.h
**  Created on      :  01-dec-2020 09:37:00
**  Original author :  SantoroR
******************************************************************************/
/*****************************************************************************
**
**                        CanMgmOut Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef CANMGMOUT_OUT_H
#define CANMGMOUT_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define CAN_RON_INIT 0u
#define CAN_RON_RUNNING 1u
#define CAN_RON_DETECTED 2u
#define CAN_RON_INHERIT 3u

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint8_T IgnFault[N_CYLINDER];


/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : CanMgmOut_T5ms
**
**   Description:
**    CanMgmOut funtion called @5ms task
**
**   Parameters :
**    void
**
**   Returns:
**    void
** 
******************************************************************************/
void CanMgmOut_T5ms(void);

/******************************************************************************
**   Function    : CanMgmOut_T10ms
**
**   Description:
**    CanMgmOut funtion called @10ms task
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
******************************************************************************/
void CanMgmOut_T10ms(void);

/******************************************************************************
**   Function    : CanMgmOut_T20ms
**
**   Description:
**    CanMgmOut funtion called @20ms task
**
**   Parameters :
**    void
**
**   Returns:
**    void
** 
******************************************************************************/
void CanMgmOut_T20ms(void);

/******************************************************************************
**   Function    : CanMgmOut_T50ms
**
**   Description:
**    CanMgmOut funtion called @50ms task
**
**   Parameters :
**    void
**
**   Returns:
**    void
** 
******************************************************************************/
void CanMgmOut_T50ms(void);

/******************************************************************************
**   Function    : CanMgmOut_TDCms
**
**   Description:
**    CanMgmOut funtion called @TDC task
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgmOut_TDC(void);

/******************************************************************************
**   Function    : CANMGM_UpdateMisfAbsTdc
**
**   Description:
**    CanMgmOut funtion called @TDC task
**
**   Parameters :
**    void
**
**   Returns:
**    void
** 
******************************************************************************/
void CANMGM_UpdateMisfAbsTdc(void);

/******************************************************************************
**   Function    : CANMGM_SendTelemetry_DX_SX
**
**   Description:
**    CanMgmOut funtion called @5ms task
**    This is the Raster scheduler for telemetry frames
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CANMGM_SendTelemetry_DX_SX(void);


#endif

/****************************************************************************
 ****************************************************************************/


