/*****************************************************************************************************************/
/* $HeadURL::                                                                                                  $ */
/* $Revision::                                                                                                 $ */
/* $Date::                                                                                                     $ */
/* $Author::                                                                                                   $ */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  ADC_K2
**  Filename        :  ADC.cfg
**  Created on      :  20-lug-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifndef _ADC_K2_CFG_
#define _ADC_K2_CFG_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/

#if (BOARD_TYPE == BOARD_EISB8F_A)

/* SD Engine */
/* SD 0 */
#define SD0_ENABLE                   (SDADC_ENABLED)
#define SD0_MODE                     (SDADC_MODE_SINGLE)
#define SD0_VCOMSEL                  (SDADC_VCOMSEL_VREFN)
#define SD0_GAIN                     (SDADC_GAIN_1)
#define SD0_PDR                      (SDADC_PDR_OSR_24)
#define SD0_HPASS_ENABLE             (SDADC_HPASS_DISABLED)
#define SD0_BIAS_ENABLE              (SDADC_BIAS_DISABLED)
#define SD0_WATCHDOG_ENABLE          (SDADC_WATCHDOG_EVENT_DISABLED)
#define SD0_FIFO_ENABLE              (SDADC_FIFO_TRUE)
#define SD0_FIFO_FULL_EVENT_ENABLE   (SDADC_FIFO_FULL_EVENT_ENABLED)
#define SD0_FIFO_FULL_EVENT_TYPE     (SDADC_FIFO_FULL_EVENT_DMA)       
#define SD0_FIFO_FULL_THRESHOLD      (SDADC_FIFO_1_BYTE)
#define SD0_FIFO_OW_ENABLE           (SDADC_FIFO_OVERWRITE_ENABLED)
#define SD0_HW_TRIG_EN               (SDADC_HW_TRIGGER_DISABLED)
#define SD0_HW_TRIG_SOURCE           (SDADC_HW_TRIGGER_SOURCE_GTM)
#define SD0_HW_TRIG_EDGE             (SDADC_HW_TRIGGER_RISING_EDGE)
#define SD0_CEIL_PRI_ENABLED         (CEIL_PRI_ENABLE)
#define SD0_PRI_CEIL                 (PRI_8)
#define SD0_PRI_CEIL_CORE            (CEIL_PRI_CORE0)

/* SD 3 */
#define SD3_ENABLE                   (SDADC_ENABLED)
#define SD3_MODE                     (SDADC_MODE_SINGLE)
#define SD3_VCOMSEL                  (SDADC_VCOMSEL_VREFN)
#define SD3_GAIN                     (SDADC_GAIN_1)
#define SD3_PDR                      (SDADC_PDR_OSR_24)
#define SD3_HPASS_ENABLE             (SDADC_HPASS_DISABLED)
#define SD3_BIAS_ENABLE              (SDADC_BIAS_DISABLED)
#define SD3_WATCHDOG_ENABLE          (SDADC_WATCHDOG_EVENT_DISABLED)
#define SD3_FIFO_ENABLE              (SDADC_FIFO_TRUE)
#define SD3_FIFO_FULL_EVENT_ENABLE   (SDADC_FIFO_FULL_EVENT_ENABLED)
#define SD3_FIFO_FULL_EVENT_TYPE     (SDADC_FIFO_FULL_EVENT_DMA)       
#define SD3_FIFO_FULL_THRESHOLD      (SDADC_FIFO_1_BYTE)
#define SD3_FIFO_OW_ENABLE           (SDADC_FIFO_OVERWRITE_ENABLED)
#define SD3_HW_TRIG_EN               (SDADC_HW_TRIGGER_DISABLED)
#define SD3_HW_TRIG_SOURCE           (SDADC_HW_TRIGGER_SOURCE_GTM)
#define SD3_HW_TRIG_EDGE             (SDADC_HW_TRIGGER_FALLING_EDGE)
#define SD3_CEIL_PRI_ENABLED         (CEIL_PRI_ENABLE)
#define SD3_PRI_CEIL                 (PRI_8)
#define SD3_PRI_CEIL_CORE            (CEIL_PRI_CORE0)

/* SAR Engine */
/* SAR 0 */
#define SAR0_ENABLE                  (SARADC_ENABLED)
#define SAR0_MODE                    (SARADC_MODE_ONESHOT)
#define SAR0_OW_ENABLE               (SARADC_OW_DISABLED)
#define SAR0_NUM_CHANNELS            2U
#define SAR0_ISR_ENABLE              (SARADC_ISR_DISABLED)
#define SAR0_HW_TRIGGER_ENABLE       (SARADC_TRIGGER_DISABLED)
#define SAR0_HW_TRIGGER_EDGE         (SARADC_TRIGGER_RISING_EDGE)
#define SAR0_DMA_ENABLE              (SARADC_DMA_ENABLED)
#define SAR0_DMA_CHANNELS_NUM        2U
#define SAR0_CTRL_CRES               (SARADC_CRES_HIGH)
#define SAR0_CTRL_PRECHG             (SARADC_PRECHG)
#define SAR0_CTRL_INPSAMP            (50u)
#define SAR0_CEIL_PRI_ENABLED        (CEIL_PRI_DISABLE)
#define SAR0_PRI_CEIL                (PRI_0)
#define SAR0_PRI_CEIL_CORE           (CEIL_PRI_CORE0)

/* SAR 2 */
#define SAR2_ENABLE                  (SARADC_ENABLED)
#define SAR2_MODE                    (SARADC_MODE_ONESHOT)
#define SAR2_OW_ENABLE               (SARADC_OW_DISABLED)
#define SAR2_NUM_CHANNELS            2U
#define SAR2_ISR_ENABLE              (SARADC_ISR_DISABLED)
#define SAR2_HW_TRIGGER_ENABLE       (SARADC_TRIGGER_DISABLED)
#define SAR2_HW_TRIGGER_EDGE         (SARADC_TRIGGER_RISING_EDGE)
#define SAR2_DMA_ENABLE              (SARADC_DMA_ENABLED)
#define SAR2_DMA_CHANNELS_NUM        2U
#define SAR2_CTRL_CRES               (SARADC_CRES_HIGH)
#define SAR2_CTRL_PRECHG             (SARADC_PRECHG)
#define SAR2_CTRL_INPSAMP            (50u)
#define SAR2_CEIL_PRI_ENABLED        (CEIL_PRI_DISABLE)
#define SAR2_PRI_CEIL                (PRI_0)
#define SAR2_PRI_CEIL_CORE           (CEIL_PRI_CORE0)

/* SAR 4 */
#define SAR4_ENABLE                  (SARADC_ENABLED)
#define SAR4_MODE                    (SARADC_MODE_ONESHOT)
#define SAR4_OW_ENABLE               (SARADC_OW_DISABLED)
#define SAR4_NUM_CHANNELS            2U
#define SAR4_ISR_ENABLE              (SARADC_ISR_DISABLED)
#define SAR4_HW_TRIGGER_ENABLE       (SARADC_TRIGGER_ENABLED)
#define SAR4_HW_TRIGGER_EDGE         (SARADC_TRIGGER_RISING_EDGE)
#define SAR4_DMA_ENABLE              (SARADC_DMA_ENABLED)
#define SAR4_DMA_CHANNELS_NUM        2U
#define SAR4_CTRL_CRES               (SARADC_CRES_HIGH)
#define SAR4_CTRL_PRECHG             (SARADC_PRECHG)
#define SAR4_CTRL_INPSAMP            (SARADC_INPSAMP_100)
#define SAR4_CEIL_PRI_ENABLED        (CEIL_PRI_DISABLE)
#define SAR4_PRI_CEIL                (PRI_0)
#define SAR4_PRI_CEIL_CORE           (CEIL_PRI_CORE0)

/* SAR 6 */
#define SAR6_ENABLE                  (SARADC_ENABLED)
#define SAR6_MODE                    (SARADC_MODE_ONESHOT)
#define SAR6_OW_ENABLE               (SARADC_OW_DISABLED)
#define SAR6_NUM_CHANNELS            2U
#define SAR6_ISR_ENABLE              (SARADC_ISR_DISABLED)
#define SAR6_HW_TRIGGER_ENABLE       (SARADC_TRIGGER_ENABLED)
#define SAR6_HW_TRIGGER_EDGE         (SARADC_TRIGGER_RISING_EDGE)
#define SAR6_DMA_ENABLE              (SARADC_DMA_ENABLED)
#define SAR6_DMA_CHANNELS_NUM        2U
#define SAR6_CTRL_CRES               (SARADC_CRES_HIGH)
#define SAR6_CTRL_PRECHG             (SARADC_PRECHG)
#define SAR6_CTRL_INPSAMP            (SARADC_INPSAMP_100)
#define SAR6_CEIL_PRI_ENABLED        (CEIL_PRI_DISABLE)
#define SAR6_PRI_CEIL                (PRI_0)
#define SAR6_PRI_CEIL_CORE           (CEIL_PRI_CORE0)

/* SAR SV */
#define SARSV_ENABLE                 (SARADC_ENABLED)
#define SARSV_MODE                   (SARADC_MODE_ONESHOT)
#define SARSV_OW_ENABLE              (SARADC_OW_DISABLED)
#define SARSV_NUM_CHANNELS           8U
#define SARSV_ISR_ENABLE             (SARADC_ISR_DISABLED)
#define SARSV_HW_TRIGGER_ENABLE      (SARADC_TRIGGER_DISABLED)
#define SARSV_HW_TRIGGER_EDGE        (SARADC_TRIGGER_RISING_EDGE)
#define SARSV_DMA_ENABLE             (SARADC_DMA_DISABLED)
#define SARSV_DMA_CHANNELS_NUM       0U
#define SARSV_CTRL_CRES              (SARADC_CRES_HIGH)
#define SARSV_CTRL_PRECHG            (SARADC_PRECHG)
#define SARSV_CTRL_INPSAMP           (SARADC_INPSAMP_100)
#define SARSV_CEIL_PRI_ENABLED       (CEIL_PRI_ENABLE)
#define SARSV_PRI_CEIL               (PRI_8)
#define SARSV_PRI_CEIL_CORE          (CEIL_PRI_CORE0)


/* Channels Definition */
/* SAR0 */
#define SAR0_CH1                     (I_PRI_B0_PER_AN)
#define SAR0_CH2                     (I_SEC_B0_PER_AN)
#define SAR0_CH3                     (SARADC_UNUSED_CHANNEL)
#define SAR0_CH4                     (SARADC_UNUSED_CHANNEL)
#define SAR0_CH5                     (SARADC_UNUSED_CHANNEL)
#define SAR0_CH6                     (SARADC_UNUSED_CHANNEL)
#define SAR0_CH7                     (SARADC_UNUSED_CHANNEL)
#define SAR0_CH8                     (SARADC_UNUSED_CHANNEL)
#define SAR0_CH9                     (SARADC_UNUSED_CHANNEL)

/* SAR2 */
#define SAR2_CH1                     (I_PRI_B1_PER_AN)
#define SAR2_CH2                     (I_SEC_B1_PER_AN)
#define SAR2_CH3                     (SARADC_UNUSED_CHANNEL)
#define SAR2_CH4                     (SARADC_UNUSED_CHANNEL)
#define SAR2_CH5                     (SARADC_UNUSED_CHANNEL)
#define SAR2_CH6                     (SARADC_UNUSED_CHANNEL)

/* SAR4 */
#define SAR4_CH1                     (IP_ION_04_PER_AN)
#define SAR4_CH2                     (IP_ION_26_PER_AN)
#define SAR4_CH3                     (SARADC_UNUSED_CHANNEL)
#define SAR4_CH4                     (SARADC_UNUSED_CHANNEL)
#define SAR4_CH5                     (SARADC_UNUSED_CHANNEL)

/* SAR6 */
#define SAR6_CH1                     (IP_ION_15_PER_AN)
#define SAR6_CH2                     (IP_ION_37_PER_AN)
#define SAR6_CH3                     (SARADC_UNUSED_CHANNEL)
#define SAR6_CH4                     (SARADC_UNUSED_CHANNEL)
#define SAR6_CH5                     (SARADC_UNUSED_CHANNEL)
#define SAR6_CH6                     (SARADC_UNUSED_CHANNEL)
#define SAR6_CH7                     (SARADC_UNUSED_CHANNEL)
#define SAR6_CH8                     (SARADC_UNUSED_CHANNEL)
#define SAR6_CH9                     (SARADC_UNUSED_CHANNEL)
#define SAR6_CH10                    (SARADC_UNUSED_CHANNEL)

/* SARSV */
#define SARSV_CH1                    (I_BATT_MON_PER_AN)
#define SARSV_CH2                    (BOARD_SEL_PER_AN)
#define SARSV_CH3                    (IDE_FS_LIVENESS_PER_AN)
#define SARSV_CH4                    (KEY_SIGNAL_PER_AN)
#define SARSV_CH5                    (V_BATT_PER_AN)
#define SARSV_CH6                    (NTC2_PER_AN)
#define SARSV_CH7                    (NTC1_PER_AN)
#define SARSV_CH8                    (IA_BANKSEL_PER_AN)
#define SARSV_CH9                    (SARADC_UNUSED_CHANNEL)
#define SARSV_CH10                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH11                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH12                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH13                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH14                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH15                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH16                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH17                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH18                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH19                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH20                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH21                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH22                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH23                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH24                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH25                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH26                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH27                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH28                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH29                   (SARADC_UNUSED_CHANNEL)
#define SARSV_CH30                   (SARADC_UNUSED_CHANNEL)

/* DMA Channels Definition */
/* SAR0 */
#define SAR0_DMA_CH1                 (I_PRI_B0_PER_AN)
#define SAR0_DMA_CH2                 (I_SEC_B0_PER_AN)
#define SAR0_DMA_CH3                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR0_DMA_CH4                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR0_DMA_CH5                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR0_DMA_CH6                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR0_DMA_CH7                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR0_DMA_CH8                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR0_DMA_CH9                 (SARADC_DMA_UNUSED_CHANNEL)

/* SAR2 */
#define SAR2_DMA_CH1                 (I_PRI_B1_PER_AN)
#define SAR2_DMA_CH2                 (I_SEC_B1_PER_AN)
#define SAR2_DMA_CH3                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR2_DMA_CH4                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR2_DMA_CH5                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR2_DMA_CH6                 (SARADC_DMA_UNUSED_CHANNEL)

/* SAR4 */
#define SAR4_DMA_CH1                 (IP_ION_04_PER_AN)
#define SAR4_DMA_CH2                 (IP_ION_26_PER_AN)
#define SAR4_DMA_CH3                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR4_DMA_CH4                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR4_DMA_CH5                 (SARADC_DMA_UNUSED_CHANNEL)

/* SAR6 */
#define SAR6_DMA_CH1                 (IP_ION_15_PER_AN)
#define SAR6_DMA_CH2                 (IP_ION_37_PER_AN)
#define SAR6_DMA_CH3                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR6_DMA_CH4                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR6_DMA_CH5                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR6_DMA_CH6                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR6_DMA_CH7                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR6_DMA_CH8                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR6_DMA_CH9                 (SARADC_DMA_UNUSED_CHANNEL)
#define SAR6_DMA_CH10                (SARADC_DMA_UNUSED_CHANNEL)

/* SARSV */
#define SARSV_DMA_CH1                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH2                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH3                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH4                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH5                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH6                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH7                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH8                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH9                (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH10               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH11               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH12               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH13               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH14               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH15               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH16               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH17               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH18               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH19               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH20               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH21               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH22               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH23               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH24               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH25               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH26               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH27               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH28               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH29               (SARADC_DMA_UNUSED_CHANNEL)
#define SARSV_DMA_CH30               (SARADC_DMA_UNUSED_CHANNEL)

/* Timeout definitions */
#define SARADC_TIMEOUT               250U
#define SDADC_TIMEOUT                250U
#define SARADC_STOPTIMEOUT            20U

#endif /* BOARD_TYPE */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

#endif /* _ADC_K2_CFG_ */

/****************************************************************************
 ****************************************************************************/
