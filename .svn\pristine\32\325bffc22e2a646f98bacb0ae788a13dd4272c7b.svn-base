/**
 ******************************************************************************
 **  Filename:      CombCtrl_private.h
 **  Date:          16-Oct-2018
 **
 **  Model Version: 1.1908
 ******************************************************************************
 **/

#ifndef RTW_HEADER_CombCtrl_private_h_
#define RTW_HEADER_CombCtrl_private_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "CombCtrl.h"

/* Includes for objects with custom storage classes. */
#include "canmgmin_out.h"
#include "Ionmgm_out.h"
#include "UDS_services.h"
#include "EEmgm_out.h"
#include "Ionacq_out.h"
#include "DigIn_out.h"
#include "Loadmgm.h"
#include "SyncMgm_out.h"
#include "ionmisf_out.h"    /* Hand coded */
#include "Temp_mgm.h"
#ifndef UCHAR_MAX
#include <limits.h>
#endif

#if ( UCHAR_MAX != (0xFFU) ) || ( SCHAR_MAX != (0x7F) )
#error Code was generated for compiler with different sized uchar/char. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( USHRT_MAX != (0xFFFFU) ) || ( SHRT_MAX != (0x7FFF) )
#error Code was generated for compiler with different sized ushort/short. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( UINT_MAX != (0xFFFFFFFFU) ) || ( INT_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized uint/int. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if ( ULONG_MAX != (0xFFFFFFFFU) ) || ( LONG_MAX != (0x7FFFFFFF) )
#error Code was generated for compiler with different sized ulong/long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif

#if 0

/* Skip this size verification because of preprocessor limitation */
#if ( ULLONG_MAX != (0xFFFFFFFFFFFFFFFFULL) ) || ( LLONG_MAX != (0x7FFFFFFFFFFFFFFFLL) )
#error Code was generated for compiler with different sized ulong_long/long_long. \
Consider adjusting Test hardware word size settings on the \
Hardware Implementation pane to match your compiler word sizes as \
defined in limits.h of the compiler. Alternatively, you can \
select the Test hardware is the same as production hardware option and \
select the Enable portable word sizes option on the Code Generation > \
Verification pane for ERT based targets, which will disable the \
preprocessor word size checks.
#endif
#endif

/* Imported (extern) block parameters */
extern int32_T INJCORRCYLRLMAX;        /* Variable: INJCORRCYLRLMAX
                                        * Referenced by: '<S80>/INJCORRCYLRLMAX'
                                        * Max Rate limiter on InjCorrCyl
                                        */
extern int32_T INJCORRCYLRLMIN;        /* Variable: INJCORRCYLRLMIN
                                        * Referenced by: '<S80>/INJCORRCYLRLMIN'
                                        * Min Rate limiter on InjCorrCyl
                                        */
extern uint16_T BKRPMCYLBAL[12];       /* Variable: BKRPMCYLBAL
                                        * Referenced by: '<S23>/ENKNOCKAD1'
                                        * Breakpoints of engine speed for cylbal learning
                                        */
extern uint16_T BKRPMENINJCORR[8];     /* Variable: BKRPMENINJCORR
                                        * Referenced by: '<S82>/BKRPMENINJCORR'
                                        * Breakpoints of Rpm for inj correction enable
                                        */
extern uint16_T BKRPMLAMFIL[8];        /* Variable: BKRPMLAMFIL
                                        * Referenced by:
                                        *   '<S80>/BKRPMLAMFIL'
                                        *   '<S63>/BKRPMLAMFIL'
                                        * Breakpoints of Rpm for FFS/lambda filtering
                                        */
extern uint16_T CYLBALLEARNDUR;        /* Variable: CYLBALLEARNDUR
                                        * Referenced by:
                                        *   '<S31>/CYLBALLEARNDUR'
                                        *   '<S33>/CYLBALLEARNDUR'
                                        * Cylinder balancing learning period
                                        */
extern uint16_T RPMHYSTFN;             /* Variable: RPMHYSTFN
                                        * Referenced by: '<S63>/RPMHYSTFN'
                                        * Rpm hysteresis for FreqNorm computation
                                        */
extern uint16_T TDCSTABLOADLAMTR;      /* Variable: TDCSTABLOADLAMTR
                                        * Referenced by: '<S66>/TDCSTABLOADLAMTR'
                                        * Number of TDC to declare Load stability
                                        */
extern uint16_T TDCSTABRPMLAMTR;       /* Variable: TDCSTABRPMLAMTR
                                        * Referenced by: '<S67>/TDCSTABRPMLAMTR'
                                        * Number of TDC to declare Rpm stability
                                        */
extern uint16_T VTTDCSTABCYLBALAD[12]; /* Variable: VTTDCSTABCYLBALAD
                                        * Referenced by: '<S23>/VTTDCSTABCYLBALAD'
                                        * Number of TDCs to detect stability of Load and RpmF for CylBal learning
                                        */
extern uint16_T FORCEDCYLCORR[8];      /* Variable: FORCEDCYLCORR
                                        * Referenced by: '<S16>/FORCEDCYLCORR'
                                        * Forced cylinder correction value
                                        */
extern uint16_T MAXADDCYLBALAD;        /* Variable: MAXADDCYLBALAD
                                        * Referenced by:
                                        *   '<S37>/MAXADDCYLBALAD'
                                        *   '<S46>/MAXADDCYLBALAD'
                                        * CylBalAd gain correction saturation
                                        */
extern uint16_T MAXVFFSOUT;            /* Variable: MAXVFFSOUT
                                        * Referenced by: '<S80>/MAXVFFSOUT'
                                        * FFS gain correction saturation
                                        */
extern uint16_T THCYLBALCORRINDAD;     /* Variable: THCYLBALCORRINDAD
                                        * Referenced by: '<S31>/THCYLBALCORRINDAD'
                                        * Threshold on the mean cylinder balancing corr. to trigger the learning
                                        */
extern int16_T FFSERRDZ;               /* Variable: FFSERRDZ
                                        * Referenced by: '<S80>/Media'
                                        * FFS error deadzone polarization value
                                        */
extern int16_T VTFFSERRTHR[8];         /* Variable: VTFFSERRTHR
                                        * Referenced by: '<S80>/VTFFSERRTHR'
                                        * Cylinder in balance disable treshold
                                        */
extern uint16_T BKLOADCYLBAL[5];       /* Variable: BKLOADCYLBAL
                                        * Referenced by: '<S23>/ENKNOCKAD5'
                                        * Breakpoints of load for cylinder balancing learning
                                        */
extern uint16_T LOADENINJCORRHYST;     /* Variable: LOADENINJCORRHYST
                                        * Referenced by: '<S82>/LOADENINJCORRHYST'
                                        * Inj correction Load enable hysteresis
                                        */
extern uint16_T THRSTABLOADLAMTR;      /* Variable: THRSTABLOADLAMTR
                                        * Referenced by:
                                        *   '<S23>/THRSTABLOADLAMTR'
                                        *   '<S66>/THRSTABLOADLAMTR'
                                        * Stability range for Load
                                        */
extern uint16_T THRSTABRPMLAMTR;       /* Variable: THRSTABRPMLAMTR
                                        * Referenced by:
                                        *   '<S23>/THRSTABRPMLAMTR'
                                        *   '<S67>/THRSTABRPMLAMTR'
                                        * Stability range for Rpm
                                        */
extern uint16_T VTLOADENINJCORR[8];    /* Variable: VTLOADENINJCORR
                                        * Referenced by: '<S82>/VTLOADENINJCORR'
                                        * Inj Correction load enable
                                        */
extern uint16_T CYLBALENLAMOBJMAX;     /* Variable: CYLBALENLAMOBJMAX
                                        * Referenced by: '<S82>/CYLBALENLAMOBJMAX'
                                        * Cyl Balance enable max lambda
                                        */
extern uint16_T CYLBALENLAMOBJMIN;     /* Variable: CYLBALENLAMOBJMIN
                                        * Referenced by: '<S82>/CYLBALENLAMOBJMIN'
                                        * Cyl Balance enable min lambda
                                        */
extern uint16_T CYLBALGNAD;            /* Variable: CYLBALGNAD
                                        * Referenced by: '<S46>/CYLBALGNAD'
                                        * CylBalIndAvgCyl adaptative gain
                                        */
extern uint16_T FFSPEAKLAMBDA;         /* Variable: FFSPEAKLAMBDA
                                        * Referenced by: '<S79>/FFSPEAKLAMBDA'
                                        * Equivalent Lambda value of FFS Peak signal
                                        */
extern uint16_T MAXCYLBALADWEIGHT;     /* Variable: MAXCYLBALADWEIGHT
                                        * Referenced by: '<S46>/MAXCYLBALADWEIGHT'
                                        * Max map weight
                                        */
extern uint16_T TBCYLBALGNAD[36];      /* Variable: TBCYLBALGNAD
                                        * Referenced by: '<S46>/TBCYLBALGNAD'
                                        * Table of gains for adaptive coefficients spreading
                                        */
extern int16_T THWATCYLBALAD;          /* Variable: THWATCYLBALAD
                                        * Referenced by: '<S15>/THWATCYLBALAD'
                                        * Threshold on the coolant temperature to enable cylbal learning
                                        */
extern int16_T FREQNORMLAMTR;          /* Variable: FREQNORMLAMTR
                                        * Referenced by: '<S63>/FREQNORMLAMTR'
                                        * Normalized cutoff frequency for FFS/IntIon filtering during transient
                                        */
extern int16_T FREQRATEMAX;            /* Variable: FREQRATEMAX
                                        * Referenced by: '<S60>/FREQRATEMAX'
                                        * Maximum variation rate for FreqNorm
                                        */
extern int16_T FREQRATEMIN;            /* Variable: FREQRATEMIN
                                        * Referenced by: '<S60>/FREQRATEMIN'
                                        * Minimum variation rate for FreqNorm
                                        */
extern int16_T VTFREQNORMCUT[8];       /* Variable: VTFREQNORMCUT
                                        * Referenced by: '<S63>/VTFREQNORMCUT'
                                        * Normalized cutoff frequency for Butterworth filter
                                        */
extern uint16_T BKETACSICYLBALAD[6];   /* Variable: BKETACSICYLBALAD
                                        * Referenced by: '<S46>/BKETACSICYLBALAD'
                                        * Vector of breakpoints for the rpm and load ratios
                                        */
extern uint16_T INTION2FFS;            /* Variable: INTION2FFS
                                        * Referenced by: '<S11>/INTION2FFS'
                                        * IntIon to FFS Gain factor
                                        */
extern uint16_T PIFFSKI;               /* Variable: PIFFSKI
                                        * Referenced by: '<S80>/PIFFSKI'
                                        * PID regulator integral gain factor
                                        */
extern uint16_T PIFFSKP;               /* Variable: PIFFSKP
                                        * Referenced by: '<S80>/PIFFSKP'
                                        * PID regulator proportional gain factor
                                        */
extern uint8_T COMBCTRLINSEL;          /* Variable: COMBCTRLINSEL
                                        * Referenced by: '<S11>/COMBCTRLINSEL'
                                        * Select Input source 0 = FFS, 1 = IntIon
                                        */
extern uint8_T ENCYLBALAD;             /* Variable: ENCYLBALAD
                                        * Referenced by: '<S15>/ENCYLBALAD'
                                        * Cylinder Balancing Adaptative enable flag
                                        */
extern uint8_T ENCYLBALADCORR;         /* Variable: ENCYLBALADCORR
                                        * Referenced by: '<S15>/ENCYLBALADCORR'
                                        * Cylinder Balancing Adaptative correction enable flag
                                        */
extern uint8_T ENFILTLAMFREEZE;        /* Variable: ENFILTLAMFREEZE
                                        * Referenced by: '<S59>/ENFILTLAMFREEZE'
                                        * Enable FiltLamFreeze
                                        */
extern uint8_T ENINJCORRCYL;           /* Variable: ENINJCORRCYL
                                        * Referenced by: '<S82>/ENINJCORRCYL'
                                        * Enable INJ cylinder correction
                                        */
extern uint8_T FORCECYLCORR;           /* Variable: FORCECYLCORR
                                        * Referenced by: '<S16>/FORCECYLCORR'
                                        * Enable forced cylinder correction
                                        */
extern uint8_T FORCEFFSOBJ;            /* Variable: FORCEFFSOBJ
                                        * Referenced by: '<S82>/FORCEFFSOBJ'
                                        * force FFS correction PI 0=Torque Law, 1=no op., 2=Time history, 3= force output
                                        */
extern uint8_T LENBUFMED;              /* Variable: LENBUFMED
                                        * Referenced by:
                                        *   '<S17>/Median'
                                        *   '<S59>/LENBUFMED'
                                        * FFS median length
                                        */
extern uint8_T VTENFILBUTTER[8];       /* Variable: VTENFILBUTTER
                                        * Referenced by: '<S63>/VTENFILBUTTER'
                                        * Butterworth filtering enable (disabled == by-passed)
                                        */
extern void CombCtrl_Write_TbCylBalAd(uint16_T rtu_CylBalIndAvgCyl, uint8_T
  rtu_indr_cylbal, uint8_T rtu_indc_cylbal, int16_T rtu_eta_cylbal, int16_T
  rtu_csi_cylbal, uint16_T rtu_RtRpmCylBal, uint16_T rtu_RtLoadCylBal);
extern void CombCtrl_Init(void);
extern void CombCtrl_EOA_Init(void);
extern void CombCtrl_EOA(void);

/* Exported data declaration */

/* Declaration for custom storage class: EE_CSC */
extern uint16_T TbInjCorrCylAd[480];   /* Tables of the adaptive coefficients for all cylinders */

#endif                                 /* RTW_HEADER_CombCtrl_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.3 (R2017b)24-Jul-2017                                             *
 * Simulink 9.0 (R2017b)24-Jul-2017                                           *
 * Simulink Coder 8.13 (R2017b)24-Jul-2017                                    *
 * Embedded Coder 6.13 (R2017b)24-Jul-2017                                    *
 * Stateflow 9.0 (R2017b)24-Jul-2017                                          *
 * Fixed-Point Designer 6.0 (R2017b)24-Jul-2017                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
