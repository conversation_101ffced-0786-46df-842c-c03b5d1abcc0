/*****************************************************************************************************************/
/* $HeadURL::                                                                                                              $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  GtmEisb
**  Filename        :  Gtm_Eisb_calib.c
**  Created on      :  19-apr-2021 10:00:00
**  Original author :  AragonJ
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "gtm_eisb.h"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
#pragma ghs section rodata=".calib"

///Inductance killer threshold
CALQUAL CALQUAL_POST uint16_T VTIKILLTHR[4] =
{
    (uint16_T)(2550.0f*3.2768f),  (uint16_T)(2550.0f*3.2768f),  (uint16_T)(2550.0f*3.2768f),  (uint16_T)(2550.0f*3.2768f)
};

/// Samples discarded in EPWS mode
CALQUAL CALQUAL_POST uint8_T NISECDISCSAMPEPWS = 1U;

/// Sample Index used to find low Isec thr and calculate VtISecMin
CALQUAL CALQUAL_POST uint8_T MINISECSAMPIDX = 5U;

CALQUAL CALQUAL_POST uint8_T ENFILTTRIGIN = 1u;

CALQUAL CALQUAL_POST uint16_T MINTRIGIN2ENFILT = 200u;

/****************************************************************************
 ****************************************************************************/