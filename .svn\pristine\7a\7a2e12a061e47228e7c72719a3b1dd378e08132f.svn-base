/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonKnockEn.h
 **  Date:          13-Sep-2021
 **
 **  Model Version: 1.988
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonKnockEn_h_
#define RTW_HEADER_IonKnockEn_h_
#include <string.h>
#ifndef IonKnockEn_COMMON_INCLUDES_
# define IonKnockEn_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonKnockEn_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonKnockEn_initialize(void);

/* Exported entry point function */
extern void IonKnockEn_10ms(void);

/* Exported entry point function */
extern void IonKnockEn_EOA(void);

/* Exported entry point function */
extern void IonKnockEn_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgLoadEnKnock[8];      /* '<S3>/Merge3' */

/* Knock enabled for load condition */
extern uint8_T IonKnockEnabled;        /* '<S3>/Merge1' */

/* Flag that indicates whether IonKnock is enabled or not. */
extern uint16_T IonKnockLoadIndex;     /* '<S3>/Merge7' */

/* Index on BKLOADIONKNOCK breakpoints for pre-lookup */
extern uint16_T IonKnockLoadRatio;     /* '<S3>/Merge8' */

/* Ratio on BKLOADIONKNOCK breakpoints pre-lookup */
extern uint16_T IonKnockRpmIndex;      /* '<S3>/Merge6' */

/* Index on BKRPMIONKNOCK breakpoints for pre-lookup */
extern uint16_T IonKnockRpmRatio;      /* '<S3>/Merge2' */

/* Ratio on BKRPMIONKNOCK breakpoints pre-lookup */
extern uint16_T VtIonKnockEnableCond[8];/* '<S3>/Merge' */

/* Bitwise variable: BIT_0 = enabled by calibration; BIT_1 = enabled for Load; BIT_2 = enabled for Saout; BIT_3 = enabled for StMisf; BIT_4 = enabled for ion integral; BIT_5 = enabled for valid IonWindow; BIT_6 = enabled for DSAoutCyl; BIT_7 = enabled for NMSparkPrg[cyl]; BIT_8 = enabled for CntTdcCrk */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/Constant' : Unused code path elimination
 * Block '<S27>/Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/Data Type Propagation' : Unused code path elimination
 * Block '<S29>/Data Type Propagation' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Propagation' : Unused code path elimination
 * Block '<S25>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S26>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S26>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S26>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S27>/Reshape' : Reshape block reduction
 * Block '<S28>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S29>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S33>/Reshape' : Reshape block reduction
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonKnockEn'
 * '<S1>'   : 'IonKnockEn/EOA'
 * '<S2>'   : 'IonKnockEn/Power_On'
 * '<S3>'   : 'IonKnockEn/Subsystem'
 * '<S4>'   : 'IonKnockEn/T10ms'
 * '<S5>'   : 'IonKnockEn/EOA/EnableCond'
 * '<S6>'   : 'IonKnockEn/EOA/EvaluateConditions'
 * '<S7>'   : 'IonKnockEn/EOA/VectorAssignment'
 * '<S8>'   : 'IonKnockEn/EOA/EvaluateConditions/DisableKnock4Dwell'
 * '<S9>'   : 'IonKnockEn/EOA/EvaluateConditions/IntIon_Cond'
 * '<S10>'  : 'IonKnockEn/EOA/EvaluateConditions/LoadOn_Cond'
 * '<S11>'  : 'IonKnockEn/EOA/EvaluateConditions/OtherCondition'
 * '<S12>'  : 'IonKnockEn/EOA/EvaluateConditions/SAOut_Cond'
 * '<S13>'  : 'IonKnockEn/EOA/EvaluateConditions/DisableKnock4Dwell/IfActionSubsystem'
 * '<S14>'  : 'IonKnockEn/EOA/EvaluateConditions/DisableKnock4Dwell/IfActionSubsystem1'
 * '<S15>'  : 'IonKnockEn/EOA/EvaluateConditions/IntIon_Cond/ArrayEvaluation'
 * '<S16>'  : 'IonKnockEn/EOA/EvaluateConditions/IntIon_Cond/Chart'
 * '<S17>'  : 'IonKnockEn/EOA/EvaluateConditions/LoadOn_Cond/ArrayEvaluation'
 * '<S18>'  : 'IonKnockEn/EOA/EvaluateConditions/LoadOn_Cond/Chart'
 * '<S19>'  : 'IonKnockEn/EOA/EvaluateConditions/SAOut_Cond/ArrayEvaluation'
 * '<S20>'  : 'IonKnockEn/EOA/EvaluateConditions/SAOut_Cond/Chart'
 * '<S21>'  : 'IonKnockEn/T10ms/Index_Ratio_RonLevel'
 * '<S22>'  : 'IonKnockEn/T10ms/Integral_Threshold'
 * '<S23>'  : 'IonKnockEn/T10ms/LoadOn_Threshold'
 * '<S24>'  : 'IonKnockEn/T10ms/SA_Threshold'
 * '<S25>'  : 'IonKnockEn/T10ms/Index_Ratio_RonLevel/PreLookUpIdSearch_U1'
 * '<S26>'  : 'IonKnockEn/T10ms/Index_Ratio_RonLevel/PreLookUpIdSearch_U2'
 * '<S27>'  : 'IonKnockEn/T10ms/Integral_Threshold/Look2D_IR_U8'
 * '<S28>'  : 'IonKnockEn/T10ms/LoadOn_Threshold/ArrangeLookUpOutputLSB'
 * '<S29>'  : 'IonKnockEn/T10ms/LoadOn_Threshold/LookUp_IR_S8'
 * '<S30>'  : 'IonKnockEn/T10ms/LoadOn_Threshold/LookUp_IR_U16'
 * '<S31>'  : 'IonKnockEn/T10ms/LoadOn_Threshold/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S32>'  : 'IonKnockEn/T10ms/SA_Threshold/ArrangeLookUpOutputLSB'
 * '<S33>'  : 'IonKnockEn/T10ms/SA_Threshold/Look2D_IR_S8'
 */

/*-
 * Requirements for '<Root>': IonKnockEn
 *
 * Inherited requirements for '<Root>/Power_On':
 *  1. EISB_FCA6CYL_SW_REQ_1633: Software shall set to 0 each output produced for Enabling Conditions functionality at ECU power on. (ECU_SW_Requirements#3019)

 */
#endif                                 /* RTW_HEADER_IonKnockEn_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/