// T3210001 Fri Aug 28 15:28:08 2020

B::

TO<PERSON><PERSON><PERSON>   ON
STAT<PERSON>BAR ON
FramePOS 0.0 0.0 96. 46.
WinPAGE.RESet

WinPAGE.Create P000
WinCLEAR

WinPOS 18.286 0.0 72. 20. 0. 0. W000
Register

WinPOS 0.57143 28.667 90. 11. 14. 1. W001
WinTABS 10. 10. 25.
List

WinPOS 1.0 1.1667 85. 22. 0. 0. W002
Var.Watch

VAR.ADDWATCH %e %SL ReqSBCMode
VAR.ADDWATCH %e %SL ReqMsgOnD
VAR.ADDWATCH %e %SL FOSTOPWDT
VAR.ADDWATCH %e %SL StSBC
VAR.ADDWATCH %e %SL StSBCMode
VAR.ADDWATCH %e %SL CntSBCFrmChg
VAR.ADDWATCH %e %SL CntSBCResend
VAR.ADDWATCH %e %SL FlgSBCResend
VAR.ADDWATCH %e %SL FlgSBCWrite
VAR.ADDWATCH %e %SL CntRefreshWDT
VAR.ADDWATCH %e %SL SBCMsgIdx
VAR.ADDWATCH %e %SL SBCIndexReset
VAR.ADDWATCH %e %SL SBCIndexSearch
VAR.ADDWATCH %e %SL %hex SBCDataRxBuffer
VAR.ADDWATCH %e %SL %hex SBCDataTxBuffer
VAR.ADDWATCH %e %SL %hex SBCCustomTxBuff
VAR.ADDWATCH %e %SL %hex SBCGlobalStatusReg
VAR.ADDWATCH %e %SL %hex SBCGlobalCfgReg
VAR.ADDWATCH %e %SL %hex SBC_REG
VAR.ADDWATCH %e %SL %hex oldSBCGlobalStatusReg
VAR.ADDWATCH %e %SL idxSBCGlobalStatusReg
VAR.ADDWATCH %e %SL cntSBCGlobalStatusReg

WinPAGE.select P000

ENDDO
