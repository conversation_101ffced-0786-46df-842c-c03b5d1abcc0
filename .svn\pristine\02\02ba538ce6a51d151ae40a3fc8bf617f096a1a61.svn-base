/*****************************************************************************************************************/
/* $HeadURL::                                                                                                                                        $   */
/* $ Description:                                                                                                                                                                                    */
/* $Revision::        $                                                                                                                                                                            */
/* $Date::                                                $                                                                                                                       */
/* $Author::                         $                                                                                                                                                            */
/*****************************************************************************************************************/

#ifndef _CPU_MGM_
#define _CPU_MGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "AnalogIn_out.h"
#include "diagmgm_out.h"
#include "Intsrcmgm.h"
#include "Vsrammgm.h"
#include "cpumgm_out.h"

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
static int16_T CpuMgm_GetLastReset (uint8_T *lastReset);

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/

#endif

