/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                              */
/* $Revision::        $                                                                                                        */
/* $Date::                                                $                                                                    */
/* $Author::                         $                                                                                         */
/*******************************************************************************************************************************/

/*! \mainpage DiagMgm
 
\section intro Introduction
\brief Functions related to diagnosis

Explain in detail how this module works and what is supposed to do.  
 
*/

#ifndef _DIAGMGM_H_
#define _DIAGMGM_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "diagmgm_out.h"
#include "canmgmin_out.h"
#include "analogin_out.h"
#include "diagmgm_out.h"
#include "mathlib.h"
#include "analogin_out.h"
#include "digin_out.h"
#include "recmgm_out.h"
#include "string.h"
#include "dtc.h"
#include "SyncMgm_out.h"
#include "pwrmgm_out.h"
#include "SAE_j2012_122007.h"
#include "IonIntMgm_out.h"
#include "UDS_services.h"
#include "ionacq_out.h"
#include "IonAcqBufMgm_out.h"
#include "KnockCorrNom_out.h "
#include "timing_out.h"
#include "stub.h"         // for all _stub vars not defined yet &/o need to be defined as global or fetched with a GET_var method
#include "loadmgm_out.h"      // for Load var scope
#include "tempmgm_out.h"     // for TWater and TAir vars scope
#include "TempECUMgm_out.h" // for TempECU1, TempECU2 and TempECU3 vars scope
#include "coiltarget_out.h"  // for StPlasObj var scope
#include "cpumgm_out.h"   // for ResetType var scope
#include "msparkcmd_out.h"// for VtILeadPeak[], IPriCorrCyl and SAout vars scope
#include "Ignincmd_out.h"
#include "Ron_Detect.h"   // for RonLevelEE var scope
#include "canmgm_out.h"   // for VehSpeed var scope
#include "ionchargectrl_out.h" // for VChargeObj var scope
#include "TSparkCtrlAdat_out.h"
#include "rli.h"
#include "EnvVarConversion_out.h"
#include "active_Diag_out.h"
#include "Eemgm_out.h"
#include "Wdt_out.h"
//#include "DIAGCANMGM_Utils.h"

/*!
\defgroup PrivateDefines Private Defines
\sgroup
*/
/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define MINOR_OR_EQUAL 1u
#define MINOR          2u
#define ENV_STUB_1B    0xFFu
#define ENV_STUB_2B    0xFFFFu

#define WUC_NOT_ENDED   (0)
#define WUC_ENDED       (1)

/*!\egroup*/

/*!
\defgroup Imported Calibrations 
\sgroup
*/
/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern CALQUAL CALQUAL_POST uint8_T  TBDISDIAG[DIAG_NUMBER][TBDISDIAG_CULS];
extern CALQUAL CALQUAL_POST uint16_T THINFVBATDDIAG;
extern CALQUAL CALQUAL_POST uint16_T THSUPVBATDDIAG;
extern CALQUAL CALQUAL_POST uint16_T THRPMDIAG;
extern CALQUAL CALQUAL_POST uint8_T  VTDIAGENABLE[DIAG_NUMBER];
extern CALQUAL CALQUAL_POST uint8_T  VTSTEPINCFAULT[DIAG_NUMBER];
extern CALQUAL CALQUAL_POST uint8_T  VTSTEPDECFAULT[DIAG_NUMBER];
extern CALQUAL CALQUAL_POST int16_T  VTTHRCONFFAULT[DIAG_NUMBER];
extern CALQUAL CALQUAL_POST int8_T   VTTHRCONFPASSED[DIAG_NUMBER];
//extern CALQUAL CALQUAL_POST uint8_T  VTDISDIAGDONGLE[DIAG_NUMBER];
extern CALQUAL CALQUAL_POST uint16_T VBATTDISDIAGMINTHR;
extern CALQUAL CALQUAL_POST uint8_T  CONFDTCTHR;
extern CALQUAL CALQUAL_POST uint8_T  CONFDTCINC;
extern CALQUAL CALQUAL_POST uint8_T VTFORCEPTFAULT[DIAG_NUMBER];
extern CALQUAL CALQUAL_POST uint8_T FORCEPTFAULT10MS;
extern CALQUAL CALQUAL_POST uint8_T FORCERESETDIAG;
extern CALQUAL CALQUAL_POST uint16_T FORCERESETDIAGTHR;
/*!\egroup*/

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
static void DiagMgm_OC_Ended(void);
static void WUC_ended(void);
static void DiagMgm_ResetDiag(void);
static void UpdateTestResult(uint8_T diag_Id, typPtFault fault);
static void confirmFail(uint8_T diag_Id);
static void confirmPassed(uint8_T diag_Id);
static void DiagMgm_SetDiagStateForce10MS(uint8_T id, uint8_T fault, uint8_T *state);
static uint8_T StoredDiagPresent(uint8_T diag_Id);
#ifdef SAVE_ENV_DATA_EE 
#if (SAVE_ENV_DATA_EE == 1u)
static void Fault_Storing(uint8_T diag_Id, typPtFault fault);
static void ScaledDiagData(uint8_T pos, uint8_T diagIndex, uint16_T pCode, typPtFault pFault, uint8_T snapNum);
static void UpdateExtendedData(uint16_T pCode, typPtFault pFault, uint8_T diagIndex);
static uint8_T CheckStoredFaults(uint8_T snapNum);
static uint8_T CheckStoredSafetyFaults(uint8_T snapNum);
static uint8_T CheckSeverity(uint8_T severity, uint8_T level_check_pri, uint8_T snapNum);
static int8_T FindOldestSnap(uint8_T snapNum, uint16_T * pCode_old, uint8_T * ftb_old);
static int8_T FindOldSnapWithLowSeverity(uint8_T snapNum, uint16_T * pCode_old, uint8_T * ftb_old);
static int8_T FindOldSnapWithLowSeverity_NotPresent(uint8_T snapNum, uint16_T * pCode_old,uint8_T * ftb_old);
static uint8_T getConfirmedFTB(uint16_T pcode, uint8_T FTB);
static void setConfirmedFTB(uint16_T pcode, uint8_T FTB, uint8_T value);
#endif // (SAVE_ENV_DATA_EE == 1u)
#endif // SAVE_ENV_DATA_EE 
#endif

