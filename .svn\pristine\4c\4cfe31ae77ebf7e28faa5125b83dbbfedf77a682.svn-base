/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonKnockAirCorr.h
 **  Date:          19-Apr-2021
 **
 **  Model Version: 1.792
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonKnockAirCorr_h_
#define RTW_HEADER_IonKnockAirCorr_h_
#ifndef IonKnockAirCorr_COMMON_INCLUDES_
# define IonKnockAirCorr_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonKnockAirCorr_COMMON_INCLUDES_ */


/* Includes for objects with custom storage classes. */

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonKnockAirCorr_initialize(void);

/* Exported entry point function */
extern void IonKnockAirCorr_10ms(void);

/* Exported entry point function */
extern void IonKnockAirCorr_EOA(void);

/* Exported entry point function */
extern void IonKnockAirCorr_PowerOn(void);

/* Exported data declaration */

/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern int16_T DLoadTAir;              /* '<S3>/Merge7' */

/* Delta Load to enable knocking - applied value */
extern uint8_T FlgDLoadTAir;           /* '<S3>/Merge1' */

/* DLoadTAir correction running */
extern int16_T SATAir;                 /* '<S3>/Merge8' */

/* SA correction */

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S12>/Data Type Propagation' : Unused code path elimination
 * Block '<S13>/Data Type Duplicate' : Unused code path elimination
 * Block '<S19>/Data Type Propagation' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S22>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S12>/Reshape' : Reshape block reduction
 * Block '<S13>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S13>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S13>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S13>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S14>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S14>/Data Type Conversion2' : Eliminate redundant data type conversion
 * Block '<S14>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S14>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S19>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion' : Eliminate redundant data type conversion
 * Block '<S22>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S22>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S22>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S22>/Data Type Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonKnockAirCorr'
 * '<S1>'   : 'IonKnockAirCorr/EOA'
 * '<S2>'   : 'IonKnockAirCorr/PowerOn'
 * '<S3>'   : 'IonKnockAirCorr/Subsystem'
 * '<S4>'   : 'IonKnockAirCorr/T10ms'
 * '<S5>'   : 'IonKnockAirCorr/EOA/EOA'
 * '<S6>'   : 'IonKnockAirCorr/EOA/FC'
 * '<S7>'   : 'IonKnockAirCorr/EOA/FC/DLoad_SATAir_Calc'
 * '<S8>'   : 'IonKnockAirCorr/EOA/FC/SATAirTab_Calc'
 * '<S9>'   : 'IonKnockAirCorr/EOA/FC/DLoad_SATAir_Calc/ArrangeOutputLSB'
 * '<S10>'  : 'IonKnockAirCorr/EOA/FC/DLoad_SATAir_Calc/ArrangeOutputLSB1'
 * '<S11>'  : 'IonKnockAirCorr/EOA/FC/SATAirTab_Calc/ArrangeLookUpOutputLSB'
 * '<S12>'  : 'IonKnockAirCorr/EOA/FC/SATAirTab_Calc/Look2D_IR_S8'
 * '<S13>'  : 'IonKnockAirCorr/EOA/FC/SATAirTab_Calc/PreLookUpIdSearch_U16'
 * '<S14>'  : 'IonKnockAirCorr/EOA/FC/SATAirTab_Calc/PreLookUpIdSearch_U8'
 * '<S15>'  : 'IonKnockAirCorr/T10ms/T10ms'
 * '<S16>'  : 'IonKnockAirCorr/T10ms/UpdateCounter'
 * '<S17>'  : 'IonKnockAirCorr/T10ms/UpdateDLoad'
 * '<S18>'  : 'IonKnockAirCorr/T10ms/UpdateDLoad/ArrangeLookUpOutputLSB'
 * '<S19>'  : 'IonKnockAirCorr/T10ms/UpdateDLoad/LookUp_IR_S8'
 * '<S20>'  : 'IonKnockAirCorr/T10ms/UpdateDLoad/LookUp_IR_U16'
 * '<S21>'  : 'IonKnockAirCorr/T10ms/UpdateDLoad/LookUp_IR_U16_2'
 * '<S22>'  : 'IonKnockAirCorr/T10ms/UpdateDLoad/PreLookUpIdSearch_S16'
 * '<S23>'  : 'IonKnockAirCorr/T10ms/UpdateDLoad/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S24>'  : 'IonKnockAirCorr/T10ms/UpdateDLoad/LookUp_IR_U16_2/Data Type Conversion Inherited3'
 */

/*-
 * Requirements for '<Root>': IonKnockAirCorr
 *
 * Inherited requirements for '<Root>/PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_1634: Software shall set to 0 each output produced for Air Temperature C... (ECU_SW_Requirements#3042)

 */
#endif                                 /* RTW_HEADER_IonKnockAirCorr_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/