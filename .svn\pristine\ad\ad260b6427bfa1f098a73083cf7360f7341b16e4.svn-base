#open(my $map,  "<",  "PA_XPC56XXL_HPU_akhela_repo.map")  or die "Can't open map file.txt: $!";
#print "\n map file opened"   ;

sub checkAddress {
    my($line, $address) = @_;

    #S3210005441C00000000000000000000000040A0000040A000000000000000000000B9
    
    # get start address
    my $startAddress = hex(substr($line, 4, 8));
    
    # get number of bytes in record (-checksum - address)
    my $byte_count = hex(substr($line, 2, 2)) - 5;
    
    # printf("address to check: %08x\n", $address);
    # printf("startAddress: %08x, byte count: %d\n", $startAddress, $byte_count);    
    
    if ($address >= $startAddress && $address <= ($startAddress + $byte_count)) {
        return 1;
    } else {
        return 0;
    }
}


sub getStartSrecord {
    my($line, $address) = @_;

    #S3210005441C00000000000000000000000040A0000040A000000000000000000000B9
    
    # get start address
    my $startAddress = hex(substr($line, 4, 8));
    
    # get starting position of requested address
    my $startOffset = $address - $startAddress;
    my $offset = substr($line, ($startOffset * 2) + 12);  # = header (S3, byte count and address)
    
    # the new byte count is the length of remaining data bytes + checksum (1) + address (4)
    my $new_byte_count = (length($offset) / 2) + 4;
    
    # compose new srecord
    my $new_srecord = sprintf("S3%02X%08X%s", $new_byte_count, $address, $offset);
    
    return $new_srecord;
}

sub getLastSrecord {
    my($line, $address) = @_;

    #S3210005441C00000000000000000000000040A0000040A000000000000000000000B9
    printf("address to be checked: %08x\n", $address);
    
    # get start address
    my $startAddress = hex(substr($line, 4, 8));
    
    # get starting position of requested address
    my $endOffset = $address - $startAddress;
    my $offset = substr($line, 12, $endOffset * 2); # = header (S3, byte count and address)
    
    # the new byte count is the length of remaining data bytes + checksum (1) + address (4)
    my $new_byte_count = (length($offset) / 2) + 5;
    
    # compose new srecord
    my $new_srecord = sprintf("S3%02X%08X%sFF\n", $new_byte_count, $startAddress, $offset);
    
    #print $new_srecord;
    
    return $new_srecord;
}

# check parameter
die "Usage: PostProcessSRecMot.pl -vmemory [NO_VIRTUAL_MEMORY/VIRTUAL_MEMORY] -mergeSrec [YES/YES-i/NO] -extension [s37/hex] -multiCalibSect [number] [calib region names in map file order]\n" if $#ARGV < 7;
#check if [number] is greater than 0 other input parameters must exist
if ($#ARGV > 7 && $ARGV[7] > 0) {
#die "-multiCalibSect is 0: no Calibration Name required\n" if ( $ARGV[7] == 0);
    die "Missing definition of additional Calibration Section\n" if (($#ARGV - 7) != $ARGV[7]);
    $multiCalibSectNumber = $ARGV[7];
    print "Additional Calibration Sections are: $multiCalibSectNumber\n";
}
#save as much calib as the sections to add to ROM.Calib standard section
my $shiftIndex = 0;
while ($multiCalibSectNumber > 0) {
    @multiCalibSectionName[$multiCalibSectNumber-1] = " " . $ARGV[$#ARGV - $shiftIndex];
    print "multiCalibSectionName : $multiCalibSectionName[$multiCalibSectNumber-1]\n";
    $multiCalibSectNumber = $multiCalibSectNumber - 1;
    $shiftIndex = $shiftIndex + 1;
}
#reset multiCalibSectNumber to argument command line value
$multiCalibSectNumber = $ARGV[7];

my $vmemory_used = ($ARGV[1] eq "VIRTUAL_MEMORY") ? 1 : 0;
if ($ARGV[3] eq "YES") 
{
    $mergeSrec = 1;
} 
elsif ($ARGV[3] eq "YES-i")
{
    $mergeSrec = -1;
}
else
{
    $mergeSrec = 0;
}
my $useExtension = ($ARGV[5] eq "s37") ? 1 : 0;

use Cwd;
#$dir = getcwd();
#print ("$dir\n")  ;
$dir = "\.\\";
opendir (DIR, $dir) or die "Can not open directory $dir";
@files=grep !/^\./, readdir(DIR); #remove . and .. from readdir
foreach $fi (@files)
{
    if ($fi=~/map/)
    {
      $map = $fi;
      print "MAP FILE : $map  \n"       ;
    }
    elsif ((($fi=~/appl\.s37/)||($fi=~/appl\.hex/))&&($fi!~/merged/)&&($mergeSrec==0))
    {
      $hex = $fi;
      print "HEX FILE : $hex  \n"       ;
      @hexs = split(/\./, $hex);
      if ($useExtension)
      {
        $hexmerged = $hexs[0]."_merged"."\.s37" ;      
      }
      else
      {
        $hexmerged = $hexs[0]."_merged"."\.hex" ;
      }
      
      # @hex2s = split(/\-/, $hex);
      # $hex_orig =  $hex2s[0]."\.hex" ;
      $hex_orig =  $hex;
    }
    elsif ((($fi=~/appl2merge\.s37/)||($fi=~/appl2merge\.hex/))&&(($mergeSrec==1)||($mergeSrec ==-1)))
    {
      $appl2merge =$fi;
      print "APPL to MERGE FILE : $appl2merge  \n";
      #substring to provide %NAME%-appl_merged.hex
      @hexs = split(/\./, $appl2merge); 
      $hexs[0] = substr($hexs[0],0,-6);
      #print "hexs[0]: $hexs[0]  \n";
      if ($useExtension)
      {
        $hex =$hexs[0]."\.s37";
        $hexmerged =$hexs[0]."_merged"."\.s37";
        #print "hexmerged: $hexmerged  \n";
        #print "hex: $hex  \n";
      }
      else
      {
        $hex =$hexs[0]."\.hex";
        $hexmerged =$hexs[0]."_merged"."\.hex";
      }
    }
    elsif ((($fi=~/calib2merge\.s37/)||($fi=~/calib2merge\.hex/))&&(($mergeSrec==1)||($mergeSrec ==-1)))
    {
      $calib2merge =$fi;
      print "CALIB to MERGE FILE : $calib2merge  \n";
    }
}

$map = $dir.$map;
$hex = $dir.$hex;
$hexmerged = $dir.$hexmerged;
$hex_orig = $dir.$hex;
$appl2merge = $dir.$appl2merge;
$calib2merge = $dir.$calib2merge;


# ----------------------> Merging of the two hex files <----------------------
if ($mergeSrec==1)
{
    print "Merging $appl2merge with $calib2merge...in $hex\n" ;
    print ("$mergeSrec  fuorielse !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!\n")  ;
 } elsif ($mergeSrec==-1) {
    print "Merging $calib2merge with $appl2merge...in $hex\n" ;
 }
 
if (($mergeSrec==1)||($mergeSrec==-1))
{
    unlink($hexmerged);
    
    open (APPL2MERGE,  $appl2merge) or die "Can't open $appl2merge file: $!\n";
    open (CALIB2MERGE,  $calib2merge) or die "Can't open $calib2merge file: $!\n";
    open(MERGINGOUTFILE, ">$hex") or die "Cannot open file $hexmerged for write : $!\n";

    if ($mergeSrec==1) 
    {
        while (my $hline0 = <APPL2MERGE>) 
        {
            if ($hline0=~"S5")
            {
                $S5line = $hline0; 
                $S7line = <APPL2MERGE>;    # read both S5 and S7 line
                last;
            }
        print( MERGINGOUTFILE  $hline0); 
        }
        
        while (my $hline1 = <CALIB2MERGE>) 
        {
            if (($hline1!~"S0") && ($hline1!~"S5") && ($hline1!~"S7"))
            {
                print( MERGINGOUTFILE  $hline1);
            }                               
        }
    }
     else
     {
        while (my $hline0 = <CALIB2MERGE>) 
        {
            if ($hline0=~"S5")
            {
                $S5line = $hline0; 
                $S7line = <CALIB2MERGE>;    # read both S5 and S7 line
                last;
            }
        print( MERGINGOUTFILE  $hline0); 
        }
        
        
        while (my $hline1 = <APPL2MERGE>) 
        {
            if (($hline1!~"S0") && ($hline1!~"S5") && ($hline1!~"S7"))
            {
                print( MERGINGOUTFILE  $hline1);
            }                               
        }
     }
    
    close (APPL2MERGE);
    close (CALIB2MERGE);
    
    # write S7 record from converted hex file
    print( MERGINGOUTFILE  $S5line);         # write S5
    print( MERGINGOUTFILE  $S7line);         # write S7
    
    close (MERGINGOUTFILE);
}




open (FILE,  $map) or die "Can't open map file: $!";
#print "\n map file opened \n "   ;

    
my $calib_pattern         = " .calib " ;   
my $foc0calib_pattern     = " .foccalib_p0" ;    
my $foc1calib_pattern     = " .foccalib_p1" ;
my $rom_calib_pattern     = " .ROM.calib" ;
my $rom_foc0calib_pattern = " .ROM.foccalib_p0" ;    
my $rom_foc1calib_pattern = " .ROM.foccalib_p1" ;
my $srec_pattern          = "S3"; #"S321";
my $rom_calib_address ;
my $rom_calib_size  ;
my $rom_foc0calib_address ;
my $rom_foc0calib_size    ;
my $rom_foc1calib_address ;
my $rom_foc1calib_size   ;
my $calib_address ;
my $calib_size   ;
my $foc0calib_address ;
my $foc0calib_size   ;     
my $foc1calib_address ;
my $sdata_address;
my $sdata_size;
# ----------------------> Parsing of MAP file <----------------------

 while (my $line = <FILE>) { 

   
    @fields = split (/\s+/, $line);
   
    if ($line =~ $rom_calib_pattern){
        $rom_calib_address =   @fields[2];
        $rom_calib_size    =   @fields[3];
        print "$rom_calib_pattern,  $rom_calib_address , $rom_calib_size \n ";
    }
#check if multi Calibration regions are used and provide calculations of ROM section size including them
    $index = 0;
    while($multiCalibSectNumber>0) {
#print "multiCalibSectionName[$index]: $multiCalibSectionName[$index], in line: $line\n ";
        if ($line =~ $multiCalibSectionName[$index]){
            @rom_multiCalibSection_address[$index] =   @fields[2];
            @rom_multiCalibSection_size[$index] =   @fields[3];
            print "$multiCalibSectionName[$index], $rom_multiCalibSection_address[$index], $rom_multiCalibSection_size[$index] \n ";
            $index = $index +1;
        }
        $multiCalibSectNumber = $multiCalibSectNumber -1;
    }
#reset index and multiCalibSectNumber for next cycle
    $index = 0;
    $multiCalibSectNumber = $ARGV[7];
    
    if ($line =~$calib_pattern ){
        $calib_address =  @fields[2];
        $calib_size    =  @fields[3];
        print "$calib_pattern,  $calib_address , $calib_size \n ";
        #if (!$vmemory_used) {
            #last;
        #}
    }
    
    if ($line =~" .sdata" ){
        $sdata_address =  @fields[2];
        $sdata_size    =  @fields[3];
        print "MC sdata $sdata_address $sdata_size   \n ";
                if (!$vmemory_used) {
            last;
        }
    }
    
    if ($vmemory_used) {
        
        if ($line =~ $rom_foc0calib_pattern){
            $rom_foc0calib_address =  @fields[2];
            $rom_foc0calib_size    =   @fields[3];    
            print "$rom_foc0calib_pattern,  $rom_foc0calib_address , $rom_foc0calib_size \n ";
        }
        if ($line =~ $rom_foc1calib_pattern){
            $rom_foc1calib_address =   @fields[2];
            $rom_foc1calib_size    =   @fields[3];  
            print "$rom_foc1calib_pattern,  $rom_foc1calib_address , $rom_foc1calib_size \n ";
        }
        if ($line =~  $foc0calib_pattern){
            $foc0calib_address =   @fields[2];
            $foc0calib_size    =   @fields[3];
            print "$foc0calib_pattern,  $foc0calib_address , $foc0calib_size \n ";
        }   
        if ($line =~  $foc1calib_pattern ){
            $foc1calib_address =  @fields[2];
            $foc1calib_size    =  @fields[3]; 
            print "$foc1calib_pattern,  $foc1calib_address , $foc1calib_size \n ";
            
            last;
        } 
    }
}   

# ----------------------> creation of files for sections <----------------------
$rom_calib_address_hex =  hex $rom_calib_address;
$rom_calib_address_hex = $rom_calib_address_hex - 16;  # COMPENSATING CALIB CHECK SECTION

my $calib_address_hex = hex $calib_address;
my $sdata_address_hex = hex $sdata_address;
my $rom_calib_size_hex ;

if (!$vmemory_used) {
    $rom_calib_size_hex = $sdata_address_hex - $calib_address_hex;
}
else {
    $rom_calib_size_hex = hex $rom_calib_size ;
}
$rom_calib_size_hex = $rom_calib_size_hex +16 ; 
print "rom_calib_size_hex $rom_calib_size_hex \n";

#reset multiCalibSectNumber to argument command line value
$multiCalibSectNumber = $ARGV[7];

#calculate additional Calibration sections sizes
$additional_calib_size_hex = 0;
while($multiCalibSectNumber>0) {
    $additional_calib_size_hex = $additional_calib_size_hex + hex ($rom_multiCalibSection_size[$multiCalibSectNumber - 1] )  + hex ($rom_multiCalibSection_address[$multiCalibSectNumber - 1]);
    if ($multiCalibSectNumber > 1) {
#manage alignment between the end of a calib section and the start of a new one
        $additional_calib_size_hex = $additional_calib_size_hex - hex($rom_multiCalibSection_address[$multiCalibSectNumber - 2]) - hex($rom_multiCalibSection_size[$multiCalibSectNumber - 2]);
        print "Multiple section additional_calib_size_hex: $additional_calib_size_hex\n";
    }
    $multiCalibSectNumber = $multiCalibSectNumber -1;
}

if ($additional_calib_size_hex > 0) {
    $additional_calib_size_hex = $additional_calib_size_hex- $rom_calib_address_hex - $rom_calib_size_hex;
    print "Dimension of additional_calib_size_hex: $additional_calib_size_hex\n";
    $rom_calib_address_end_hex = $rom_calib_address_hex + $rom_calib_size_hex + $additional_calib_size_hex;
} else {
    $rom_calib_address_end_hex = $rom_calib_address_hex + $rom_calib_size_hex;
}
print "rom_calib_address_end_hex : $rom_calib_address_end_hex \n ";
print "INDIRIZZO CALIBRAZIONI FLASH : $rom_calib_address_hex "  ;
$calib_address_hex =      hex $calib_address;
$calib_address_hex = $calib_address_hex -16 ;  # COMPENSATING CALIB CHECK SECTION
print "INDIRIZZO CALIBRAZIONI RAM : $calib_address_hex "   ;
my $calib_offset =         $calib_address_hex  -  $rom_calib_address_hex;
my $foc0calib_offset;
my $foc1calib_offset;

my $hexline; 

print "GEN   CALIB RAM-ROM OFFSET       $calib_offset \n ";
 
if ($vmemory_used) { 
   
    # processing with VIRTUAL_MEMORY usage 

    $rom_foc0calib_address_hex = hex  $rom_foc0calib_address ;
    $foc0calib_address_hex  = hex  $foc0calib_address ;
    $rom_foc1calib_address_hex = hex  $rom_foc1calib_address ;
    $foc1calib_address_hex   = hex   $foc1calib_address ;
    $foc0calib_offset =  $foc0calib_address_hex - $rom_foc0calib_address_hex  ;
    $foc1calib_offset =  $foc1calib_address_hex - $rom_foc1calib_address_hex   ; 
    
    print "FOC 0 CALIB RAM-ROM OFFSET       $foc0calib_offset \n ";
    print "FOC 1 CALIB RAM-ROM OFFSET       $foc1calib_offset \n ";  

    # processing with VIRTUAL_MEMORY usage
    open (HEXFILE,  $hex) or die "Can't open hex file: $!";
    open(OUTFILEF0, ">ccpf0output.hex") or die "Cannot open ccpf0output.hex for write : $!";
    open(OUTFILEF1, ">ccpf1output.hex") or die "Cannot open ccpf1output.hex for write : $!";
    open(OUTFILEF2, ">ccpf2output.hex") or die "Cannot open ccpf2output.hex for write : $!";
    
    my $address_val;
    
    while ($hline = <HEXFILE>) 
    { 
        if (checkAddress($hline, $rom_calib_address_hex))
        { 
            $hline = getStartSrecord($hline, $rom_calib_address_hex);
            print (OUTFILEF0 $hline); 
            while ($hline = <HEXFILE>)
            {
                $address_val = hex(substr($hline, 4, 8));
                if ($address_val >= $rom_foc0calib_address_hex) 
                {
                    last; 
                }
                if (checkAddress($hline, $rom_foc0calib_address_hex)) {
                    # srecord to be changed found
                    $hline = getLastSrecord($hline, $rom_foc0calib_address_hex);
                }
                print (OUTFILEF0 $hline);
            }
            last;  
        }
    }
    close (HEXFILE);


    open (HEXFILE,  $hex) or die "Can't open hex file.txt: $!";
    print "\n hex file opened \n "   ;

    while ($hline1 = <HEXFILE>) 
    { 
        if (checkAddress($hline1, $rom_foc0calib_address_hex))
        { 
            $hline1 = getStartSrecord($hline1, $rom_foc0calib_address_hex);
            print (OUTFILEF1 $hline1); 
            while ($hline1 = <HEXFILE>)
            {
                $address_val = hex(substr($hline1, 4, 8));
                if ($address_val >= $rom_foc1calib_address_hex) 
                {
                    last; 
                }
                if (checkAddress($hline1, $rom_foc1calib_address_hex)) {
                    # srecord to be changed found
                    $hline1 = getLastSrecord($hline1, $rom_foc1calib_address_hex);
                }
                print (OUTFILEF1 $hline1);
            }
            last;  
        }
    }

    close (HEXFILE);

    open (HEXFILE,  $hex) or die "Can't open hex file.txt: $!";
    print "\n hex file opened \n "   ;

    while ($hline2 = <HEXFILE>) 
    { 
#        if (checkAddress($hline2, $rom_foc1calib_address_hex))
# 
#            $hline2 = getStartSrecord($hline2, $rom_foc1calib_address_hex);
#            print (OUTFILEF2 $hline2); 
#            while ($hline2 = <HEXFILE>)
#            {
#                if ($hline2=~"S5") 
#                {
#                 last; 
#                }   
#                print (OUTFILEF2 $hline2);
#            }
#            last;  
#        }
        if (checkAddress($hline2, $rom_foc1calib_address_hex))
        { 
            $foc1calib_size_hex = hex($foc1calib_size);
            $rom_foc1calib_stop_address = $foc1calib_size_hex + $rom_foc1calib_address_hex;    
            print "$foc1calib_size_hex\n";
            printf ("stop addess 0x%X\n", $rom_foc1calib_stop_address);

            $hline2 = getStartSrecord($hline2, $rom_foc1calib_address_hex);
            print (OUTFILEF2 $hline2); 
            while ($hline2 = <HEXFILE>)
            {
                $address_val = hex(substr($hline2, 4, 8));
                #$foc1calib_size_hex=hex($foc1calib_size);
              
                if ($address_val >= $rom_foc1calib_stop_address) 
                {
                    last; 
                }
                if (checkAddress($hline2, $rom_foc1calib_stop_address)) {
                    # srecord to be changed found
                    $hline2 = getLastSrecord($hline2, $rom_foc1calib_stop_address);
                }
                print (OUTFILEF2 $hline2);
            }
            last;  
        }

    }

    close (HEXFILE);
    close (OUTFILEF0);
    close (OUTFILEF1);
    close (OUTFILEF2);

} else {

    # processing without VIRTUAL_MEMORY usage 
    
    open (HEXFILE,  $hex) or die "Can't open hex file: $!";
    open(OUTFILEF0, ">ccpf0output.hex") or die "Cannot open ccpf0output.hex for write : $!";
        
    while ($hline = <HEXFILE>) 
    { 
     if (checkAddress($hline, $rom_calib_address_hex))
        {        
            $hline = getStartSrecord($hline, $rom_calib_address_hex);
            print (OUTFILEF0 $hline); 
            while ($hline = <HEXFILE>)
            {
                $address_val = hex(substr($hline, 4, 8));
                if ($address_val >= $rom_calib_address_end_hex) 
                {    
                    last; 
                }
                if (checkAddress($hline, $rom_calib_address_end_hex)) {
                    # srecord to be changed found
                    $hline = getLastSrecord($hline, $rom_calib_address_end_hex);
                }
                print (OUTFILEF0 $hline);
            }
            last;  
        }
if(0)
{
        if (checkAddress($hline, $rom_calib_address_hex))
        { 
            $hline = getStartSrecord($hline, $rom_calib_address_hex);
            print (OUTFILEF0 $hline);
            while ($hline = <HEXFILE>)
            {
                if ($hline =~ "S5") 
                {
                    last; 
                }   
                print (OUTFILEF0 $hline);
            }
            last;  
        }
}
    } 
    close (HEXFILE);
    close (OUTFILEF0);   
}

# ----------------------> translation ROM -> RAM <----------------------

open(OUTFILEF0, "ccpf0output.hex") or die "Cannot open ccpf0output.hex for write : $!";
open(OUTFILEF0_RAM, ">ccpf0output_ram.hex") or die "Cannot open ccpf0output.hex for write : $!";

while ($hline3 = <OUTFILEF0>) 
{
    $calib_address = hex(substr($hline3, 4, 8)) + $calib_offset;
    $calib_address = sprintf("%08X", $calib_address);
    substr ($hline3, 4, 8, $calib_address);
    print (OUTFILEF0_RAM $hline3);
}                
close (OUTFILEF0);     
close (OUTFILEF0_RAM);
unlink ("ccpf0output.hex") or warn "Could not unlink";  

  
if ($vmemory_used) {
    open(OUTFILEF1, "ccpf1output.hex") or die "Cannot open ccpf1output.hex for write : $!";
    open(OUTFILEF1_RAM, ">ccpf1output_ram.hex") or die "Cannot open ccpf1output.hex for write : $!";

    while ($hline4 = <OUTFILEF1>) 
    {
        $foc0calib_address = hex(substr($hline4, 4, 8)) + $foc0calib_offset;
        $foc0calib_address = sprintf("%08X", $foc0calib_address);
        substr ($hline4, 4, 8, $foc0calib_address);
        print (OUTFILEF1_RAM $hline4);
    }
    close (OUTFILEF1);
    close (OUTFILEF1_RAM);
    unlink ("ccpf1output.hex") or warn "Could not unlink";  

    open(OUTFILEF2, "ccpf2output.hex") or die "Cannot open ccpf2output.hex for write : $!";
    open(OUTFILEF2_RAM, ">ccpf2output_ram.hex") or die "Cannot open ccpf2output.hex for write : $!";

    while ($hline5 = <OUTFILEF2>) 
    {
        $foc1calib_address = hex(substr($hline5, 4, 8)) + $foc1calib_offset;
        $foc1calib_address = sprintf("%08X", $foc1calib_address);
        substr ($hline5, 4, 8, $foc1calib_address);
        print (OUTFILEF2_RAM $hline5);
    }  
    close (OUTFILEF2);
    close (OUTFILEF2_RAM);
    unlink ("ccpf2output.hex") or warn "Could not unlink"; 
}

# ----------------------> Merging of all temp files <----------------------

print "$hexmerged\n" ;
unlink($hexmerged);

open (HEXFILE,  $hex) or die "Can't open hex file: $!";
open(OUTFILEF3, ">>$hexmerged") or die "Cannot open file $hexmerged for write : $!";
open(F0, "ccpf0output_ram.hex") or die "Cannot open ccpf0output_ram.hex for write : $!";

while (my $hline6 = <HEXFILE>) 
{
  if ($hline6=~"S5")
  {
    $S5line = $hline6; 
    $S7line = <HEXFILE>;    # read also S7 line
    last;
  }
  print( OUTFILEF3  $hline6); 
}

while (my $hline7 = <F0>) 
{
  print( OUTFILEF3  $hline7);                               
}  

close (F0);
unlink ("ccpf0output_ram.hex") or warn "Could not unlink";

if ($vmemory_used) {
    open(F1, "ccpf1output_ram.hex") or die "Cannot open ccpf1output_ram.hex for write : $!";
    open(F2, "ccpf2output_ram.hex") or die "Cannot open ccpf2output_ram.hex for write : $!";

    while (my $hline8 = <F1>) 
    {
      print( OUTFILEF3  $hline8); 
                                    
    } 
    while ($hline9 = <F2>) 
    {
      print( OUTFILEF3  $hline9);
    } 
    
    close (F1);
    close (F2);
    
    unlink ("ccpf1output_ram.hex") or warn "Could not unlink";  
    unlink ("ccpf2output_ram.hex") or warn "Could not unlink"; 
}

# get S5 record from original hex file (output from compilation) 
open (HEXFILE_ORIG,  $hex_orig) or die "Can't open $hex_orig file: $!";
while($hline_orig = <HEXFILE_ORIG>)
{
   if($hline_orig=~"S5")
   {
       print( OUTFILEF3  $hline_orig);     # write S5
   }
}
close (HEXFILE_ORIG);

# write S7 record from converted hex file
print( OUTFILEF3  $S7line);         # write S7

close (OUTFILEF3);  
close (HEXFILE); 
