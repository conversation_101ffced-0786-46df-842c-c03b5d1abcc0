/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Vsram
**  Filename        :  Vsram_out.h
**  Created on      :  08-apr-2020 14:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifndef _VSRAM_OUT_H_
#define _VSRAM_OUT_H_

/*****************************************************************************
** INCLUDE FILES
*****************************************************************************/

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/  
#define VSRAM_CHECKSUM_ERROR      -1 


/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/


/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/


/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : VSRAM_ComputeCheckSum
**
 **   Description:
 **    Calculates the VSRAM checksum value.
 **
 **   Parameters :
 **    [out] uint32_T *checkSumVal: calculated checksum value
 **
 **   Returns:
 **    NO_ERROR - No error
**
******************************************************************************/
int16_T VSRAM_ComputeCheckSum(uint32_T *checkSumVal);

/******************************************************************************
**   Function    : VSRAM_InitAllMemory
**
**   Description:
**    initializes all the VSRAM memory to the init value
**
**   Parameters :
**    [in] uint32_T vsramInitWord: init value  
**
**   Returns:
**    NO_ERROR - No error
**
******************************************************************************/
int16_T VSRAM_InitAllMemory(uint32_T vsramInitWord);

/******************************************************************************
**   Function    : VSRAM_WriteCheckSum
**
**   Description:
**    writes the checksum value
**
**   Parameters :
**    [in] uint32_T checkSumVal: checksum value
**
**   Returns:
**    NO_ERROR - No error
**
******************************************************************************/
int16_T VSRAM_WriteCheckSum(uint32_T checkSumVal);

/******************************************************************************
**   Function    : VSRAM_ReadCheckSum
**
**   Description:
**    reads the checksum value
**
**   Parameters :
**    [out] uint32_T *checkSumVal: checksum value
**
**   Returns:
**    NO_ERROR - No error
**
******************************************************************************/
int16_T VSRAM_ReadCheckSum(uint32_T *checkSumVal);


#endif  /* _VSRAM_OUT_H_ */

/****************************************************************************
 ****************************************************************************/
