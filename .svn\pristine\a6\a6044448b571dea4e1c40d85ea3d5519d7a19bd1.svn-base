/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DMA
**  Filename        :  Dma.c
**  Created on      :  08-apr-2021 10:30:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef _BUILD_DMA_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Dma.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
uint16_T DMAConfigStatus = 0u;
uint64_T DMAChInitDone;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : DMA_Config
**
**   Description:
**    This method initializes registers of the eDMA module
**    according to this Peripheral Initialization Bean settings.
**    Call this method in user code to initialize the module.
**    By default, the method is called automatically; see "Call
**    init method" property for more details.
**
**   Parameters :
**
**   Returns:
**    NO_ERROR                      - No error
**    PERIPHERAL_ALREADY_CONFIGURED - Peripheral already configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMA_Config(void)
{
    int16_T retValue = NO_ERROR;

    if (DMAConfigStatus == 0u)
    {
        DMAConfigStatus = 1u;
        /* DMA_CR: GRP1PRI=1,GRP0PRI=0,EMLM == CLM == HALT == HOE = 0, ERGA == ERCA == EDBG = 1*/
        DMA_0.CR.R = 0x040Eu; /* eDMA Control Register */
        
        /***** eDMA Channel Priority Registers *****/
        /* DMA_DCHPRI0 */
        DMA_0.DCHPRI[DMA_CH0].R = DMA_CH0_PRI;
        /* DMA_DCHPRI1 */
        DMA_0.DCHPRI[DMA_CH1].R = DMA_CH1_PRI;
        /* DMA_DCHPRI2 */
        DMA_0.DCHPRI[DMA_CH2].R = DMA_CH2_PRI;
        /* DMA_DCHPRI3 */
        DMA_0.DCHPRI[DMA_CH3].R = DMA_CH3_PRI;
        /* DMA_DCHPRI4 */
        DMA_0.DCHPRI[DMA_CH4].R = DMA_CH4_PRI;
        /* DMA_DCHPRI5 */
        DMA_0.DCHPRI[DMA_CH5].R = DMA_CH5_PRI;
        /* DMA_DCHPRI6 */
        DMA_0.DCHPRI[DMA_CH6].R = DMA_CH6_PRI;
        /* DMA_DCHPRI7 */
        DMA_0.DCHPRI[DMA_CH7].R = DMA_CH7_PRI;
        /* DMA_DCHPRI8 */
        DMA_0.DCHPRI[DMA_CH8].R = DMA_CH8_PRI;
        /* DMA_DCHPRI9 */
        DMA_0.DCHPRI[DMA_CH9].R = DMA_CH9_PRI;
        /* DMA_DCHPRI10 */
        DMA_0.DCHPRI[DMA_CH10].R = DMA_CH10_PRI;
        /* DMA_DCHPRI11 */
        DMA_0.DCHPRI[DMA_CH11].R = DMA_CH11_PRI;
        /* DMA_DCHPRI12 */
        DMA_0.DCHPRI[DMA_CH12].R = DMA_CH12_PRI;
        /* DMA_DCHPRI13 */
        DMA_0.DCHPRI[DMA_CH13].R = DMA_CH13_PRI;
        /* DMA_DCHPRI14 */
        DMA_0.DCHPRI[DMA_CH14].R = DMA_CH14_PRI;
        /* DMA_DCHPRI15 */
        DMA_0.DCHPRI[DMA_CH15].R = DMA_CH15_PRI;
        /* DMA_DCHPRI16 */
        DMA_0.DCHPRI[DMA_CH16].R = DMA_CH16_PRI;
        /* DMA_DCHPRI17 */
        DMA_0.DCHPRI[DMA_CH17].R = DMA_CH17_PRI;
        /* DMA_DCHPRI18 */
        DMA_0.DCHPRI[DMA_CH18].R = DMA_CH18_PRI;
        /* DMA_DCHPRI19 */
        DMA_0.DCHPRI[DMA_CH19].R = DMA_CH19_PRI;
        /* DMA_DCHPRI20 */
        DMA_0.DCHPRI[DMA_CH20].R = DMA_CH20_PRI;
        /* DMA_DCHPRI21 */
        DMA_0.DCHPRI[DMA_CH21].R = DMA_CH21_PRI;
        /* DMA_DCHPRI22 */
        DMA_0.DCHPRI[DMA_CH22].R = DMA_CH22_PRI;
        /* DMA_DCHPRI23 */
        DMA_0.DCHPRI[DMA_CH23].R = DMA_CH23_PRI;
        /* DMA_DCHPRI24 */
        DMA_0.DCHPRI[DMA_CH24].R = DMA_CH24_PRI;
        /* DMA_DCHPRI25 */
        DMA_0.DCHPRI[DMA_CH25].R = DMA_CH25_PRI;
        /* DMA_DCHPRI26 */
        DMA_0.DCHPRI[DMA_CH26].R = DMA_CH26_PRI;
        /* DMA_DCHPRI27 */
        DMA_0.DCHPRI[DMA_CH27].R = DMA_CH27_PRI;
        /* DMA_DCHPRI28 */
        DMA_0.DCHPRI[DMA_CH28].R = DMA_CH28_PRI;
        /* DMA_DCHPRI29 */
        DMA_0.DCHPRI[DMA_CH29].R = DMA_CH29_PRI;
        /* DMA_DCHPRI30 */
        DMA_0.DCHPRI[DMA_CH30].R = DMA_CH30_PRI;
        /* DMA_DCHPRI31 */
        DMA_0.DCHPRI[DMA_CH31].R = DMA_CH31_PRI;
        /****                                   ****/

        /*****  eDMA Error Interrupt Registers *****/
        /* EDMA_EEIRL: EEI31=0,EEI30=0,EEI29=0,EEI28=0,EEI27=0,EEI26=0,EEI25=0,EEI24=0,EEI23=0,EEI22=0,EEI21=0,EEI20=0,EEI19=0,EEI18=0,EEI17=0,EEI16=0,EEI15=0,EEI14=0,EEI13=0,EEI12=0,EEI11=0,EEI10=0,EEI9=0,EEI8=0,EEI7=0,EEI6=0,EEI5=0,EEI4=0,EEI3=0,EEI2=0,EEI1=0,EEI0=0 */
        DMA_0.EEIL.R = 0x00u;                 /* eDMA Enable Error Interrupt Register */
        /****                                   ****/

        /*****  eDMA Enable Request Registers  *****/  
        /* EDMA_ERQRL: ERQ31=0,ERQ30=0,ERQ29=0,ERQ28=0,ERQ27=0,ERQ26=0,ERQ25=0,ERQ24=0,ERQ23=0,ERQ22=0,ERQ21=0,ERQ20=0,ERQ19=0,ERQ18=0,ERQ17=0,ERQ16=0,ERQ15=0,ERQ14=0,ERQ13=0,ERQ12=0,ERQ11=0,ERQ10=0,ERQ9=0,ERQ8=0,ERQ7=0,ERQ6=0,ERQ5=0,ERQ4=0,ERQ3=0,ERQ2=0,ERQ1=0,ERQ0=0 */
        DMA_0.ERQL.R = 0x00u;               /* eDMA Enable Request Register */
        /****                                   ****/
    }
    else
    {
        retValue = PERIPHERAL_ALREADY_CONFIGURED;
    }

    return retValue;
}

/******************************************************************************
**   Function    : DMA_Init
**
**   Description:
**     This method initializes the required eDMA module Channel 
**     according to this Peripheral Initialization Bean settings.
**     Call this method in user code to initialize the channel.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint32_T  *dest : destination address
**    [in] uint16_T  destSize : destination data transfer size
**    [in] uint16_T  destOffset : signed destination address offset  
**    [in] uint32_T  *src : source address  
**    [in] uint16_T  srcSize : source data transfer size
**    [in] uint16_T  srcOffset : signed source address offset 
**    [in] uint16_T  innerLoopSize : inner ("Minor") byte transfer count 
**    [in] uint16_T  outerLoopSize : starting ("Major") iteration count 
**    [in] uint16_T  interruptEnabled : interrupt on major loop completion 
**
**   Returns:
**    NO_ERROR                      - No error
**    PERIPHERAL_ALREADY_CONFIGURED - Peripheral already configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMA_Init (uint8_T channelNumber, uint32_T *dest, uint16_T destSize, uint16_T destOffset, uint32_T *src, uint16_T srcSize, uint16_T srcOffset, uint16_T innerLoopSize, uint16_T outerLoopSize, uint16_T interruptEnabled, uint8_T enIntHalf)
{
    int16_T retValue = NO_ERROR;
    uint32_T *pTcd;
    uint32_T i;
    uint8_T cntTcdWord;

    //DisableAllInterrupts();
    if ((((uint64_T)1 << channelNumber) & DMAChInitDone) != 0)
    {
        retValue = PERIPHERAL_ALREADY_CONFIGURED;
    }
    else
    {
        /* DMA muxing outisde */
        //retValue = DMA_Mux_Peripheral (channelNumber, DMAMUX_NORMAL_MODE) ;

        if (retValue == NO_ERROR)
        {
            DMAChInitDone |= ((uint64_T)1<<channelNumber);
            //EnableAllInterrupts(); 

            /* Cleaning Registers*/
            pTcd = (uint32_T *)(DMA_0.TCD + channelNumber);
            cntTcdWord = sizeof(struct EDMA_TCD_STD_tag) / sizeof(uint32_T);
            for (i = 0u; i < cntTcdWord; i++)
            {
                *pTcd = 0u;
                pTcd++;
            }

            /* Setup the TCD register of eDma channel */
            DMA_0.TCD[channelNumber].SADDR = (vuint32_t)src;
            DMA_0.TCD[channelNumber].DADDR = (vuint32_t)dest;

            switch (srcSize)
            {
                case SSIZE_8:
                    DMA_0.TCD[channelNumber].NBYTES = innerLoopSize * SOFF_8;
                break;

                case SSIZE_16:
                    DMA_0.TCD[channelNumber].NBYTES = innerLoopSize * SOFF_16;
                break;

                case SSIZE_32:
                    DMA_0.TCD[channelNumber].NBYTES = innerLoopSize * SOFF_32;
                break;

                case SSIZE_64:
                    DMA_0.TCD[channelNumber].NBYTES = innerLoopSize * SOFF_64;
                break;

                default:
                    DMA_0.TCD[channelNumber].NBYTES = innerLoopSize * SOFF_8;
                break;
            }

            DMA_0.TCD[channelNumber].ATTR.B.SMOD = 0;
            DMA_0.TCD[channelNumber].ATTR.B.SSIZE = srcSize;
            DMA_0.TCD[channelNumber].ATTR.B.DMOD = 0;
            DMA_0.TCD[channelNumber].ATTR.B.DSIZE = destSize;
            DMA_0.TCD[channelNumber].SOFF = srcOffset;
            DMA_0.TCD[channelNumber].DOFF = destOffset;

            if (srcOffset > 0u)
            {
                DMA_0.TCD[channelNumber].SLAST = -(uint32_T)srcOffset*innerLoopSize*outerLoopSize;  //- (srcOffset*innerLoopSize)*outerLoopSize ;   
            }
            else if (srcOffset == 0u)
            {
                DMA_0.TCD[channelNumber].SLAST = 0u;
            }
            else
            {

            }

            if (destOffset > 0u) /* After major loop change to destination addr */
            {
                DMA_0.TCD[channelNumber].DLAST_SGA = -(uint32_T)destOffset*innerLoopSize*outerLoopSize;
            }
            else if (destOffset == 0u)
            {
                DMA_0.TCD[channelNumber].DLAST_SGA = 0u;
            }
            else
            {

            }

            DMA_0.TCD[channelNumber].CITER = outerLoopSize;
            DMA_0.TCD[channelNumber].BITER = outerLoopSize;
            DMA_0.TCD[channelNumber].CSR.B.INTMAJOR = interruptEnabled;
            DMA_0.TCD[channelNumber].CSR.B.INTHALF = enIntHalf;
            DMA_0.TCD[channelNumber].CSR.B.START = 0u;
            DMA_0.TCD[channelNumber].CSR.B.DREQ = 0u;
            DMA_0.TCD[channelNumber].CSR.B.ESG = 0u;
            DMA_0.TCD[channelNumber].CSR.B.MAJORELINK = 0u;
            DMA_0.TCD[channelNumber].CSR.B.ACTIVE = 0u;
            DMA_0.TCD[channelNumber].CSR.B.DONE = 0u;
            DMA_0.TCD[channelNumber].CSR.B.MAJORLINKCH = 0u;
            DMA_0.TCD[channelNumber].CSR.B.BWC = 0u;

            /* DMA muxing outisde */
            //DMA_Mux_Enable(channelNumber);
        }
        else
        {

        }
     }
    
    return retValue;
}

/******************************************************************************
**   Function    : DMA_SetMinorLinking
**
**   Description:
**     This method links together 2 DMA channels on the inner loop 
**     completion.
**     Call this method in user code only when channels are disabled.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  linkedChannel : linked channel
**
**   Returns:
**    NO_ERROR                      - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMA_SetMinorLinking(uint8_T channelNumber, uint16_T linkedChannel) 
{
    DMA_0.TCD[channelNumber].CITER_ELINK  = 1u;
    DMA_0.TCD[channelNumber].BITER_ELINK = 1u;
    DMA_0.TCD[channelNumber].CITER = linkedChannel;
    DMA_0.TCD[channelNumber].BITER = linkedChannel; /* MC if (BITER_E_LINK==1) BITER = BITERE_LINKCH  */

    return NO_ERROR;
}

/******************************************************************************
**   Function    : DMA_SetMajorLinking
**
**   Description:
**     This method links togheter 2 DMA channels on the outer loop 
**     completion.
**     Call this method in user code only when channels are disabled.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  linkedChannel : linked channel
**
**   Returns:
**    NO_ERROR                      - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMA_SetMajorLinking(uint8_T channelNumber, uint16_T linkedChannel) 
{
    DMA_0.TCD[channelNumber].CSR.B.MAJORELINK = 1u;
    DMA_0.TCD[channelNumber].CSR.B.MAJORLINKCH = linkedChannel;

    return NO_ERROR;
}

/******************************************************************************
**   Function    : DMA_Enable
**
**   Description:
**   This method enables the acquisition through the required
**   eDMA module Channel according to the channel settings.
**   Call this method in user code to get samples from the module.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**
**   Returns:
**    NO_ERROR                      - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMA_Enable(uint8_T channelNumber) 
{
    DMA_0.SERQ.R = channelNumber;

    return NO_ERROR;
}

/******************************************************************************
**   Function    : DMA_Disable
**
**   Description:
**   This method disables the acquisition through the required
**   eDMA module Channel according to the channel settings.
**   Call this method in user code to get samples from the module.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**
**   Returns:
**    NO_ERROR                      - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMA_Disable(uint8_T channelNumber) 
{
    DMA_0.CERQ.R = channelNumber;

    return NO_ERROR;
}

/******************************************************************************
**   Function    : DMA_ClearRQ
**
**   Description:
**    This method enables the acquisition through the required
**    eDMA module Channel according to the channel settings.
**    Call this method in user code to get samples from the module.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMA_ClearRQ(uint8_T channelNumber) 
{
    DMA_0.CERQ.R = channelNumber;
}

/******************************************************************************
**   Function    : DMA_16_16_Nx_Ny_Cr
**
**   Description:
**    This method transfers 16 bit words from an N elements source 
**    to an N elements destination
**
**   Parameters :
**    [in] uint8_T sChannel : dma channel
**    [in] uint32_T sAddr : source address
**    [in] uint32_T dAddr : destination address
**    [in] uint16_T nHWord : number of words
**    [in] uint8_T bwCtrl : bandwidth control
**    [in] uint8_T enInt : interrupt on major loop completion
**    [in] uint8_T enLnk : enables channel-to-channel link
**    [in] uint8_T lChannel : channel to be linked 
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMA_16_16_Nx_Ny_Cr (uint8_T sChannel, uint32_T sAddr, uint32_T dAddr, uint16_T nHWord, uint8_T bwCtrl, uint8_T enInt, uint8_T enIntHalf, uint8_T enLnk, uint8_T lChannel)
{
    /* Direct access to the DMA.TCD[channel] subfields w/o local tmp */
    DMA_0.TCD[sChannel].SADDR = sAddr;
    DMA_0.TCD[sChannel].ATTR.B.SMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.SSIZE = 1u;
    DMA_0.TCD[sChannel].ATTR.B.DMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.DSIZE = 1u;
    DMA_0.TCD[sChannel].SOFF = 2;
    DMA_0.TCD[sChannel].NBYTES = (vuint32_T)(2u * nHWord);
    DMA_0.TCD[sChannel].SLAST = -((int32_T)(2u * nHWord));
    DMA_0.TCD[sChannel].DADDR = dAddr;
    DMA_0.TCD[sChannel].CITER_ELINK = 0u;
    DMA_0.TCD[sChannel].CITER = 1u;
    DMA_0.TCD[sChannel].DOFF = 2;
    DMA_0.TCD[sChannel].DLAST_SGA = -((int32_T)(2u * nHWord));
    DMA_0.TCD[sChannel].BITER_ELINK = 0u;
    DMA_0.TCD[sChannel].BITER = 1u;
    DMA_0.TCD[sChannel].CSR.B.BWC = bwCtrl;
    DMA_0.TCD[sChannel].CSR.B.MAJORLINKCH = lChannel;
    DMA_0.TCD[sChannel].CSR.B.DONE = 0u;
    DMA_0.TCD[sChannel].CSR.B.ACTIVE = 0u;
    DMA_0.TCD[sChannel].CSR.B.MAJORELINK = enLnk;
    DMA_0.TCD[sChannel].CSR.B.ESG = 0u;
    DMA_0.TCD[sChannel].CSR.B.DREQ = 0u;
    DMA_0.TCD[sChannel].CSR.B.INTHALF = enIntHalf;
    DMA_0.TCD[sChannel].CSR.B.INTMAJOR = enInt;
    DMA_0.TCD[sChannel].CSR.B.START = 0u;
}

/******************************************************************************
**   Function    : DMA_16_16_1x_Ny_Cr
**
**   Description:
**    This method transfers 16 bit words from a 1 element source 
**    to an N elements destination
**
**   Parameters :
**    [in] uint8_T sChannel : dma channel
**    [in] uint32_T sAddr : source address
**    [in] uint32_T dAddr : destination address
**    [in] uint16_T nHWord : number of words
**    [in] uint8_T bwCtrl : bandwidth control
**    [in] uint8_T enInt : interrupt on major loop completion
**    [in] uint8_T enLnk : enables channel-to-channel link
**    [in] uint8_T lChannel : channel to be linked
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMA_16_16_1x_Ny_Cr (uint8_T sChannel, uint32_T sAddr, uint32_T dAddr, uint16_T nHWord, uint8_T bwCtrl, uint8_T enInt, uint8_T enIntHalf, uint8_T enLnk, uint8_T lChannel)
{
    /* Direct access to the DMA.TCD[channel] subfields w/o local tmp */
    DMA_0.TCD[sChannel].SADDR = sAddr;
    DMA_0.TCD[sChannel].ATTR.B.SMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.SSIZE = 1u;
    DMA_0.TCD[sChannel].ATTR.B.DMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.DSIZE = 1u;
    DMA_0.TCD[sChannel].SOFF = 0;
    DMA_0.TCD[sChannel].NBYTES = 2u;
    DMA_0.TCD[sChannel].SLAST = 0;
    DMA_0.TCD[sChannel].DADDR = dAddr;
    DMA_0.TCD[sChannel].CITER_ELINK = 0u;
    DMA_0.TCD[sChannel].CITER = nHWord;
    DMA_0.TCD[sChannel].DOFF = 2;
    DMA_0.TCD[sChannel].DLAST_SGA = -((int32_T)(2u * nHWord));
    DMA_0.TCD[sChannel].BITER_ELINK = 0u;
    DMA_0.TCD[sChannel].BITER = nHWord;
    DMA_0.TCD[sChannel].CSR.B.BWC = bwCtrl;
    DMA_0.TCD[sChannel].CSR.B.MAJORLINKCH = lChannel;
    DMA_0.TCD[sChannel].CSR.B.DONE = 0u;
    DMA_0.TCD[sChannel].CSR.B.ACTIVE = 0u;
    DMA_0.TCD[sChannel].CSR.B.MAJORELINK = enLnk;
    DMA_0.TCD[sChannel].CSR.B.ESG = 0u;
    DMA_0.TCD[sChannel].CSR.B.DREQ = 0u;
    DMA_0.TCD[sChannel].CSR.B.INTHALF = enIntHalf;
    DMA_0.TCD[sChannel].CSR.B.INTMAJOR = enInt;
    DMA_0.TCD[sChannel].CSR.B.START = 0u;
}

/******************************************************************************
**   Function    : DMA_16_16_Nx_1y_Cr
**
**   Description:
**    This method transfers 16 bit words from an N elements source 
**    to a 1 element destination
**
**   Parameters :
**    [in] uint8_T sChannel : dma channel
**    [in] uint32_T sAddr : source address
**    [in] uint32_T dAddr : destination address
**    [in] uint16_T nHWord : number of words
**    [in] uint8_T bwCtrl : bandwidth control
**    [in] uint8_T enInt : interrupt on major loop completion
**    [in] uint8_T enLnk : enables channel-to-channel link
**    [in] uint8_T lChannel : channel to be linked 
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMA_16_16_Nx_1y_Cr (uint8_T sChannel, uint32_T sAddr, uint32_T dAddr, uint16_T nHWord, uint8_T bwCtrl, uint8_T enInt, uint8_T enIntHalf, uint8_T enLnk, uint8_T lChannel)
{
    /* Direct access to the DMA.TCD[channel] subfields w/o local tmp */
    DMA_0.TCD[sChannel].SADDR = sAddr;
    DMA_0.TCD[sChannel].ATTR.B.SMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.SSIZE = 1u;
    DMA_0.TCD[sChannel].ATTR.B.DMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.DSIZE = 1u;
    DMA_0.TCD[sChannel].SOFF = 2;
    DMA_0.TCD[sChannel].NBYTES = 2u;
    DMA_0.TCD[sChannel].SLAST = -((int32_T)(2u * nHWord));
    DMA_0.TCD[sChannel].DADDR = dAddr;
    DMA_0.TCD[sChannel].CITER_ELINK = 0u;
    DMA_0.TCD[sChannel].CITER = nHWord;
    DMA_0.TCD[sChannel].DOFF = 0;
    DMA_0.TCD[sChannel].DLAST_SGA = 0;
    DMA_0.TCD[sChannel].BITER_ELINK = 0u;
    DMA_0.TCD[sChannel].BITER = nHWord;
    DMA_0.TCD[sChannel].CSR.B.BWC = bwCtrl;
    DMA_0.TCD[sChannel].CSR.B.MAJORLINKCH = lChannel;
    DMA_0.TCD[sChannel].CSR.B.DONE = 0u;
    DMA_0.TCD[sChannel].CSR.B.ACTIVE = 0u;
    DMA_0.TCD[sChannel].CSR.B.MAJORELINK = enLnk;
    DMA_0.TCD[sChannel].CSR.B.ESG = 0u;
    DMA_0.TCD[sChannel].CSR.B.DREQ = 0u;
    DMA_0.TCD[sChannel].CSR.B.INTHALF = enIntHalf;
    DMA_0.TCD[sChannel].CSR.B.INTMAJOR = enInt;
    DMA_0.TCD[sChannel].CSR.B.START = 0u;
}

/******************************************************************************
**   Function    : DMA_32_32_Nx_Ny_Cr
**
**   Description:
**    This method transfers 32 bit words from an N elements source 
**    to an N elements destination
**
**   Parameters :
**    [in] uint8_T sChannel : dma channel
**    [in] uint32_T sAddr : source address
**    [in] uint32_T dAddr : destination address
**    [in] uint16_T nWord : number of words
**    [in] uint8_T bwCtrl : bandwidth control
**    [in] uint8_T enInt : interrupt on major loop completion
**    [in] uint8_T enLnk : enables channel-to-channel link
**    [in] uint8_T lChannel : channel to be linked 
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMA_32_32_Nx_Ny_Cr (uint8_T sChannel, uint32_T sAddr, uint32_T dAddr, uint16_T nWord, uint8_T bwCtrl, uint8_T enInt, uint8_T enIntHalf, uint8_T enLnk, uint8_T lChannel)
{
    /* Direct access to the DMA.TCD[channel] subfields w/o local tmp */
    DMA_0.TCD[sChannel].SADDR = sAddr;
    DMA_0.TCD[sChannel].ATTR.B.SMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.SSIZE = 2u;
    DMA_0.TCD[sChannel].ATTR.B.DMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.DSIZE = 2u;
    DMA_0.TCD[sChannel].SOFF = 4;
    DMA_0.TCD[sChannel].NBYTES = (vuint32_T)(4u * nWord);
    DMA_0.TCD[sChannel].SLAST = -((int32_T)(4u * nWord));
    DMA_0.TCD[sChannel].DADDR = dAddr;
    DMA_0.TCD[sChannel].CITER_ELINK = 0u;
    DMA_0.TCD[sChannel].CITER = 1u;
    DMA_0.TCD[sChannel].DOFF = 4;
    DMA_0.TCD[sChannel].DLAST_SGA = -((int32_T)(4u * nWord));
    DMA_0.TCD[sChannel].BITER_ELINK = 0u;
    DMA_0.TCD[sChannel].BITER = 1u;
    DMA_0.TCD[sChannel].CSR.B.BWC = bwCtrl;
    DMA_0.TCD[sChannel].CSR.B.MAJORLINKCH = lChannel;
    DMA_0.TCD[sChannel].CSR.B.DONE = 0u;
    DMA_0.TCD[sChannel].CSR.B.ACTIVE = 0u;
    DMA_0.TCD[sChannel].CSR.B.MAJORELINK = enLnk;
    DMA_0.TCD[sChannel].CSR.B.ESG = 0u;
    DMA_0.TCD[sChannel].CSR.B.DREQ = 0u;
    DMA_0.TCD[sChannel].CSR.B.INTHALF = enIntHalf;
    DMA_0.TCD[sChannel].CSR.B.INTMAJOR = enInt;
    DMA_0.TCD[sChannel].CSR.B.START = 0u;
}

/******************************************************************************
**   Function    : DMA_32_32_1x_Ny_Cr
**
**   Description:
**    This method transfers 32 bit words from a 1 element source 
**    to an N elements destination.
**
**   Parameters :
**    [in] uint8_T sChannel : dma channel
**    [in] uint32_T sAddr : source address
**    [in] uint32_T dAddr : destination address
**    [in] uint16_T nWord : number of words
**    [in] uint8_T bwCtrl : bandwidth control
**    [in] uint8_T enInt : interrupt on major loop completion
**    [in] uint8_T enLnk : enables channel-to-channel link
**    [in] uint8_T lChannel : channel to be linked 
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMA_32_32_1x_Ny_Cr (uint8_T sChannel, uint32_T sAddr, uint32_T dAddr, uint16_T nWord, uint8_T bwCtrl, uint8_T enInt, uint8_T enIntHalf, uint8_T enLnk, uint8_T lChannel)
{
    /* Direct access to the DMA.TCD[channel] subfields w/o local tmp */
    DMA_0.TCD[sChannel].SADDR = sAddr;
    DMA_0.TCD[sChannel].ATTR.B.SMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.SSIZE = 2u;
    DMA_0.TCD[sChannel].ATTR.B.DMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.DSIZE = 2u;
    DMA_0.TCD[sChannel].SOFF = 0;
    DMA_0.TCD[sChannel].NBYTES = 4u;
    DMA_0.TCD[sChannel].SLAST = 0;
    DMA_0.TCD[sChannel].DADDR = dAddr;
    DMA_0.TCD[sChannel].CITER_ELINK = 0u;
    DMA_0.TCD[sChannel].CITER = nWord;
    DMA_0.TCD[sChannel].DOFF = 4;
    DMA_0.TCD[sChannel].DLAST_SGA = -((int32_T)(4u * nWord));
    DMA_0.TCD[sChannel].BITER_ELINK = 0u;
    DMA_0.TCD[sChannel].BITER = nWord;
    DMA_0.TCD[sChannel].CSR.B.BWC = bwCtrl;
    DMA_0.TCD[sChannel].CSR.B.MAJORLINKCH = lChannel;
    DMA_0.TCD[sChannel].CSR.B.DONE = 0u;
    DMA_0.TCD[sChannel].CSR.B.ACTIVE = 0u;
    DMA_0.TCD[sChannel].CSR.B.MAJORELINK = enLnk;
    DMA_0.TCD[sChannel].CSR.B.ESG = 0u;
    DMA_0.TCD[sChannel].CSR.B.DREQ = 0u;
    DMA_0.TCD[sChannel].CSR.B.INTHALF = enIntHalf;
    DMA_0.TCD[sChannel].CSR.B.INTMAJOR = enInt;
    DMA_0.TCD[sChannel].CSR.B.START = 0u;
}

/******************************************************************************
**   Function    : DMA_32_32_Nx_1y_Cr
**
**   Description:
**    This method transfers 32 bit words from an N elements source 
**    to a 1 element destination
**
**   Parameters :
**    [in] uint8_T sChannel : dma channel
**    [in] uint32_T sAddr : source address
**    [in] uint32_T dAddr : destination address
**    [in] uint16_T nWord : number of words
**    [in] uint8_T bwCtrl : bandwidth control
**    [in] uint8_T enInt : interrupt on major loop completion
**    [in] uint8_T enLnk : enables channel-to-channel link
**    [in] uint8_T lChannel : channel to be linked 
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMA_32_32_Nx_1y_Cr (uint8_T sChannel, uint32_T sAddr, uint32_T dAddr, uint16_T nWord, uint8_T bwCtrl, uint8_T enInt, uint8_T enIntHalf, uint8_T enLnk, uint8_T lChannel)
{
    /* Direct access to the DMA.TCD[channel] subfields w/o local tmp */
    DMA_0.TCD[sChannel].SADDR = sAddr;
    DMA_0.TCD[sChannel].ATTR.B.SMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.SSIZE = 2u;
    DMA_0.TCD[sChannel].ATTR.B.DMOD = 0u;
    DMA_0.TCD[sChannel].ATTR.B.DSIZE = 2u;
    DMA_0.TCD[sChannel].SOFF = 4;
    DMA_0.TCD[sChannel].NBYTES = 4u;
    DMA_0.TCD[sChannel].SLAST = -((int32_T)(4u * nWord));
    DMA_0.TCD[sChannel].DADDR = dAddr;
    DMA_0.TCD[sChannel].CITER_ELINK = 0u;
    DMA_0.TCD[sChannel].CITER = nWord;
    DMA_0.TCD[sChannel].DOFF = 0;
    DMA_0.TCD[sChannel].DLAST_SGA = 0;
    DMA_0.TCD[sChannel].BITER_ELINK = 0u;
    DMA_0.TCD[sChannel].BITER = nWord;
    DMA_0.TCD[sChannel].CSR.B.BWC = bwCtrl;
    DMA_0.TCD[sChannel].CSR.B.MAJORLINKCH = lChannel;
    DMA_0.TCD[sChannel].CSR.B.DONE = 0u;
    DMA_0.TCD[sChannel].CSR.B.ACTIVE = 0u;
    DMA_0.TCD[sChannel].CSR.B.MAJORELINK = enLnk;
    DMA_0.TCD[sChannel].CSR.B.ESG = 0u;
    DMA_0.TCD[sChannel].CSR.B.DREQ = 0u;
    DMA_0.TCD[sChannel].CSR.B.INTHALF = enIntHalf;
    DMA_0.TCD[sChannel].CSR.B.INTMAJOR = enInt;
    DMA_0.TCD[sChannel].CSR.B.START = 0u;
}

#ifdef _DMA_MUX_ENABLE_
#if (_DMA_MUX_ENABLE_ == 1u)
/******************************************************************************
**   Function    : DMAMUX0_PeripheralConfig
**
**   Description:
**     This method routes peripheral source to DMAMUX channel 0 according to 
**     configuration structs.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  peripheralSource : peripheral source
**
**   Returns:
**    NO_ERROR                      - No error
**    ARG_ERROR                     - Argument error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX0_PeripheralConfig (uint8_T channelNumber, uint16_T peripheralSource)
{
    int16_T retvalue = NO_ERROR;

#ifdef DMA_TEST_SW
    DMACHMUX_0.CHCONFIG[channelNumber].R    =   DMAMUX0_ALWAYS_ON ; // Only for testing (with Software request) and channel source = Always Requestor
#else
    if (/*(channelNumber >= DMAMUX0_FIRST_CH) || */(channelNumber <= DMAMUX0_LAST_CH)) // range DMA Channels 0�7
    {
        DMACHMUX_0.CHCONFIG[channelNumber].R        = 0u;                // Clear ENBL and TRIG bits before programming
        DMACHMUX_0.CHCONFIG[channelNumber].B.SOURCE = peripheralSource; // set source (slot)
        DMACHMUX_0.CHCONFIG[channelNumber].B.TRIG   = 0u;                // triggering not available for DMAMUX_0
    }
    else // channelNumber out of range
    {
        retvalue = ARG_ERROR;
    }
#endif

    return retvalue;
}

/******************************************************************************
**   Function    : DMAMUX0_ConfigEnable
**
**   Description:
**     This method routes peripheral source to DMAMUX_0 channel x and enables 
**     it as fast as possible (to be used in ISR scenario).
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  peripheralSource : peripheral source
**
**   Returns:
**    NO_ERROR                      - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX0_ConfigEnable (uint8_T channelNumber, uint16_T peripheralSource)
{
    DMACHMUX_0.CHCONFIG[channelNumber].B.SOURCE = peripheralSource; // set source (slot)
    DMACHMUX_0.CHCONFIG[channelNumber].B.TRIG = 0u; // triggering not available for DMAMUX_0
    DMACHMUX_0.CHCONFIG[channelNumber].B.ENBL = 1u;

    return NO_ERROR ;
}

/******************************************************************************
**   Function    : DMAMUX0_Reset
**
**   Description:
**     This method resets DMAMUX0 channel x
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMAMUX0_Reset(uint8_T channelNumber)
{
    DMACHMUX_0.CHCONFIG[channelNumber].R = 0u;
}

/******************************************************************************
**   Function    : DMAMUX1_PeripheralConfig
**
**   Description:
**     This method routes peripheral source to DMAMUX channel 1 according 
**     to configuration structs.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  peripheralSource : peripheral source
**    [in] uint8_T mode : mode
**
**   Returns:
**    NO_ERROR                      - No error
**    ARG_ERROR                     - Argument error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX1_PeripheralConfig (uint8_T channelNumber, uint16_T peripheralSource, uint8_T mode)
{
    int16_T retvalue = NO_ERROR;

#ifdef DMA_TEST_SW
    DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].R    =   DMAMUX1_ALWAYS_ON ; // Only for testing (with Software request) and channel source = Always Requestor
#else
    if ((channelNumber >= DMAMUX1_FIRST_CH) || (channelNumber <= DMAMUX1_LAST_CH)) // range DMA Channels 8�15
    {
        if ((mode == DMAMUX_PERIODIC_TRIG_MODE) && (channelNumber > DMAMUX1_PERIODIC_TRIG_CH))
        {
            retvalue = ARG_ERROR;
        }
        else
        {
            DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].R        = 0u;                // Clear ENBL and TRIG bits before programming
            DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].B.SOURCE = peripheralSource; // set source (slot)
            DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].B.TRIG   = mode;              // set trigger
        }
    }
    else // channelNumber out of range
    {
        retvalue = ARG_ERROR;
    }
#endif

    return retvalue;
}

/******************************************************************************
**   Function    : DMAMUX1_ConfigEnable
**
**   Description:
**     This method routes peripheral source to DMAMUX_1 channel x and 
**     enables it as fast as possible (to be used in ISR scenario).
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  peripheralSource : peripheral source
**
**   Returns:
**    NO_ERROR                      - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX1_ConfigEnable (uint8_T channelNumber, uint16_T peripheralSource)
{
    DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].B.SOURCE = peripheralSource; // set source (slot)
    DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].B.TRIG = 0u; // triggering not available for DMAMUX_0
    DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].B.ENBL = 1u;

    return NO_ERROR ;
}

/******************************************************************************
**   Function    : DMAMUX1_Reset
**
**   Description:
**     This method resets DMAMUX1 channel x
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMAMUX1_Reset(uint8_T channelNumber )
{
    DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].R = 0u;
}

/******************************************************************************
**   Function    : DMAMUX2_PeripheralConfig
**
**   Description:
**     This method routes peripheral source to DMAMUX channel 2 according to 
**     configuration structs.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  peripheralSource : peripheral source
**    [in] uint8_T   mode : mode
**
**   Returns:
**    NO_ERROR                      - No error
**    ARG_ERROR                     - Argument error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX2_PeripheralConfig (uint8_T channelNumber, uint16_T peripheralSource, uint8_T mode)
{
    int16_T retvalue = NO_ERROR;

#ifdef DMA_TEST_SW
    DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].R    =   DMAMUX2_ALWAYS_ON; // Only for testing (with Software request) and channel source = Always Requestor
#else
    if ((channelNumber >= DMAMUX2_FIRST_CH) || (channelNumber <= DMAMUX2_LAST_CH)) // range DMA Channels 16�23
    {
        if ((mode == DMAMUX_PERIODIC_TRIG_MODE) && (channelNumber != DMAMUX2_PERIODIC_TRIG_CH))
        {
            retvalue = ARG_ERROR;
        }
        else
        {
            DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].R = 0u ; // Clear ENBL and TRIG bits before programming
            DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].B.SOURCE = peripheralSource; // set source (slot)
            DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].B.TRIG = mode; // set trigger
        }
    }
    else // channelNumber out of range
    {
        retvalue = ARG_ERROR;
    }
#endif

    return retvalue;
}

/******************************************************************************
**   Function    : DMAMUX2_ConfigEnable
**
**   Description:
**     This method routes peripheral source to DMAMUX_2 channel x 
**     and enables it as fast as possible (to be used in ISR scenario).
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  peripheralSource : peripheral source
**
**   Returns:
**    NO_ERROR                      - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX2_ConfigEnable (uint8_T channelNumber, uint16_T peripheralSource)
{
    DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].B.SOURCE = peripheralSource; // set source (slot)
    DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].B.TRIG   = 0u;                // triggering not available for DMAMUX_0
    DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].B.ENBL   = 1u;

    return NO_ERROR;
}

/******************************************************************************
**   Function    : DMAMUX2_Reset
**
**   Description:
**     This method resets DMAMUX2 channel x
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMAMUX2_Reset(uint8_T channelNumber)
{
    DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].R = 0u;
}

/******************************************************************************
**   Function    : DMAMUX3_PeripheralConfig
**
**   Description:
**     This method routes peripheral source to DMAMUX channel 3 
**     according to configuration structs.
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  peripheralSource : peripheral source
**
**   Returns:
**    NO_ERROR                      - No error
**    ARG_ERROR                     - Argument error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX3_PeripheralConfig (uint8_T channelNumber, uint16_T peripheralSource)
{
    int16_T retvalue = NO_ERROR ;

#ifdef DMA_TEST_SW
    DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].R    =   DMAMUX3_ALWAYS_ON ; // Only for testing (with Software request) and channel source = Always Requestor
#else
    if ((channelNumber >= DMAMUX3_FIRST_CH) || (channelNumber <= DMAMUX3_LAST_CH)) // range DMA Channels 24�31
    {
        DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].R        = 0u;                // Clear ENBL and TRIG bits before programming
        DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].B.SOURCE = peripheralSource; // set source (slot)
        DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].B.TRIG   = 0u;                // triggering not available for DMAMUX_3
    }
    else // channelNumber out of range
    {
        retvalue = ARG_ERROR;
    }
#endif

    return retvalue;
}

/******************************************************************************
**   Function    : DMAMUX3_ConfigEnable
**
**   Description:
**     This method routes peripheral source to DMAMUX_3 channel x 
**     and enables it as fast as possible (to be used in ISR scenario).
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**    [in] uint16_T  peripheralSource : peripheral source
**
**   Returns:
**    NO_ERROR                      - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX3_ConfigEnable (uint8_T channelNumber, uint16_T peripheralSource)
{
    DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].B.SOURCE = peripheralSource; // set source (slot)
    DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].B.TRIG   = 0u;                // triggering not available for DMAMUX_0
    DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].B.ENBL   = 1u;

    return NO_ERROR;
}

/******************************************************************************
**   Function    : DMAMUX3_Reset
**
**   Description:
**     This method resets DMAMUX3 channel x
**
**   Parameters :
**    [in] uint8_T   channelNumber : channel number
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void DMAMUX3_Reset(uint8_T channelNumber)
{
    DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].R = 0u;
}

/******************************************************************************
**   Function    : DMAMUX_Enable
**
**   Description:
**     This method enables the selected DMAMUX
**
**   Parameters :
**    [in] uint8_T   dmamuxId : dmamux id
**    [in] uint8_T   channelNumber : channel number
**    [in] uint8_T   enable : enable
**
**   Returns:
**    NO_ERROR                      - No error
**    ARG_ERROR                     - Argument error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T DMAMUX_Enable (uint8_T dmamuxId, uint8_T channelNumber, uint8_T enable)
{
    int16_T error = NO_ERROR;

    switch (dmamuxId)
    {
        case 0: // DMAMUX_0
#ifdef DMA_TEST_SW
            DMACHMUX_0.CHCONFIG[(channelNumber%DMAMUX0_FIRST_CH)].R = 0x80u | DMAMUX0_ALWAYS_ON; /* Only for testing (with Software request) and channel source = Always Requestor    */
#else
            DMACHMUX_0.CHCONFIG[(channelNumber%DMAMUX0_FIRST_CH)].B.ENBL = enable;
#endif
        break;

        case 1: // DMAMUX_1
#ifdef DMA_TEST_SW
            DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].R = 0x80u | DMAMUX1_ALWAYS_ON; /* Only for testing (with Software request) and channel source = Always Requestor    */
#else
            DMACHMUX_1.CHCONFIG[(channelNumber%DMAMUX1_FIRST_CH)].B.ENBL = enable;
#endif
        break;

        case 2: // DMAMUX_2
#ifdef DMA_TEST_SW
            DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].R = 0x80u | DMAMUX2_ALWAYS_ON; /* Only for testing (with Software request) and channel source = Always Requestor    */
#else
            DMACHMUX_2.CHCONFIG[(channelNumber%DMAMUX2_FIRST_CH)].B.ENBL = enable;
#endif
        break;

        case 3: // DMAMUX_3
#ifdef DMA_TEST_SW
            DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].R = 0x80u | DMAMUX3_ALWAYS_ON; /* Only for testing (with Software request) and channel source = Always Requestor    */
#else
            DMACHMUX_3.CHCONFIG[(channelNumber%DMAMUX3_FIRST_CH)].B.ENBL = enable;
#endif
        break;

        default:
            error = ARG_ERROR;
        break;
    }

    return error;
}

#endif //(_DMA_MUX_ENABLE_ == 1u)
#endif //_DMA_MUX_ENABLE_

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/


#endif /* _BUILD_DMA_ */

/****************************************************************************
 ****************************************************************************/
