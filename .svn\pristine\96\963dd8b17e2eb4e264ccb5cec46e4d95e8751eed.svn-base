/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_INT.h
**  Created on      :  03-Feb-2022 15:39:00
**  Original author :  Mocci A
******************************************************************************/
#ifndef SAFETYMNGR_INT_H
#define SAFETYMNGR_INT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_INTC_out.h"
#include "SafetyMngr_CommLib_out.h"
#include "task.h"

/* add here include files */

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define INTC_BASEADDR               (0xFC040000UL)
#define INTC_CPR_OFFSET             ((uint32_T)0x10UL)
#define INTC_IACKR_OFFSET           ((uint32_T)0x20UL)

#define INTC_CPR0_ADDRESS           (INTC_BASEADDR+INTC_CPR_OFFSET)
#define INTC_CPR1_ADDRESS           (INTC_CPR0_ADDRESS+(uint32_T)0x04UL)
#define INTC_CPR2_ADDRESS           (INTC_CPR1_ADDRESS+(uint32_T)0x04UL)
#define INTC_IACKR0_ADDRESS         (INTC_BASEADDR+INTC_IACKR_OFFSET)
#define INTC_IACKR1_ADDRESS         (INTC_IACKR0_ADDRESS+(uint32_T)0x04UL)
#define INTC_IACKR2_ADDRESS         (INTC_IACKR1_ADDRESS+(uint32_T)0x04UL)

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/* None */

#endif // SAFETYMNGR_INTC_H

/****************************************************************************
 ****************************************************************************/

