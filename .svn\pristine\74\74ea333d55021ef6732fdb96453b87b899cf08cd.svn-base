/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Analog In
**  Filename        :  AnalogIn.c
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Aragon J
******************************************************************************/
#ifdef _BUILD_ANALOGIN_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "analogin.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* FdbkLiveness voltage raw reading+ */
uint16_T VVFdbkLiveness = 0u;

/* Voltage ION capacitors */
uint16_T  VCharge[PMOS_NUMBER];

/* Battery voltage */
uint16_T VBattery = BATTERY_12_VOLT;

/* Battery voltage filtered */
uint16_T VBatteryF = BATTERY_12_VOLT;

/* Voltage NTC temperature ECU 1 (NTC) */
uint16_T VTempECU1 = TEMP_25_C;

/* Voltage NTC temperature ECU 2 (NTC) */
uint16_T VTempECU2 = TEMP_25_C;

/* Voltage NTC temperature ECU 3 (CPU) */
uint16_T VTempECU3 = TEMP_25_C_1;

/* Supply Coil 1 */
uint16_T  VSupplyCoil1ADC;

/* Supply Coil 2 */
uint16_T  VSupplyCoil2ADC;

/* Buck 1 */
uint16_T  ISupplyCoil1ADC;

/* Buck 2 */
uint16_T  ISupplyCoil2ADC;

/* IBattADC */
uint16_T IBattADC;

/* KeySignal voltage */
uint16_T VKeySignal;

/*  BankSel voltage */
uint16_T VBankSel = 0u;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/* CONST */
/* Channel ION charge select */
static const uint16_T AnVCharge[PMOS_NUMBER] = {VC_1_5_MON_CH, VC_2_6_MON_CH, VC_3_7_MON_CH, VC_4_8_MON_CH};
static const uint8_T SDADCVCharge[PMOS_NUMBER] = {SD0, SD3, SD0, SD3};
/// Channel ION VCap diag
static const uint8_T DiagVCap[PMOS_NUMBER] = {DIAG_VCAP_0_4, DIAG_VCAP_1_5, DIAG_VCAP_2_6, DIAG_VCAP_3_7};

/// Secondary diagnosis vector
static const uint8_T DiagSec[PMOS_NUMBER] = {DIAG_SEC_0_4, DIAG_SEC_1_5, DIAG_SEC_2_6, DIAG_SEC_3_7};

/* Voltage ION capacitors glitch */
static uint8_T  VCGlitch[PMOS_NUMBER];

/* Voltage ION capacitors */
static uint16_T  VChargeRaw[PMOS_NUMBER];

/* Voltage ION capacitors Median buffer */
static int16_T  VChargeMBuff[PMOS_NUMBER][MEDIAN_BUFF_MAX_SIZE + 1u];

/* Counter Voltage ION capacitors Median buffer */
static uint8_T  CntVCharge[PMOS_NUMBER];

/* Battery voltage raw reading */
static uint16_T VVBattery = 0u;

/*  KeySignal voltage raw reading */
static uint16_T VVKeySignal = 0u;

/// BankSel voltage raw reading
static uint16_T VVBankSel = 0u;

#ifdef TEST_CALCULATED_VALUE

/* Test SARADC Values */
static float_T NTC1_Value;
static float_T KEY_SIGNAL_Value;
static float_T NTC2_Value;
static float_T BOARD_SEL_Value;
static float_T IA_BANKSEL_Value;
static float_T IDE_FS_LIVENESS_Value;

#endif 

/* SARSV Analog values */
static uint16_T SARSV_AN20_Value;
static uint16_T SARSV_AN35_Value;
static uint16_T SARSV_AN46_Value;
static uint16_T SARSV_AN13_Value;
static uint16_T SARSV_AN25_Value;
static uint16_T SARSV_AN24_Value;
static uint16_T SARSV_AN21_Value;

/* Supply Coil 1 error counters */
static int16_T  ADCErrVCoil1;
static uint8_T  ADCErrVCoil1Cnt;

/* Supply Coil 2 error counters */
static int16_T  ADCErrVCoil2;
static uint8_T  ADCErrVCoil2Cnt;

/* Buck 1 error counters */
static int16_T  ADCErrIBuck1;
static uint32_T ADCErrIBuck1Cnt;

/* Buck 2 error counter */
static int16_T  ADCErrIBuck2;
static uint32_T ADCErrIBuck2Cnt;

/* IBattADC error counters */
static int16_T ADCErrIBatt;
static uint8_T ADCErrIBattCnt;

/* IPriADC error counters */
static uint8_T ADCErrIpri0;
static uint8_T ADCErrIpri1;

/* SARSV Error counters */
static uint8_T cntErrorAcqBoardSel;
static uint8_T cntErrorAcqLiveness;
static uint8_T cntErrorAcqKeySignal;
static uint8_T cntErrorAcqVBatt;
static uint8_T cntErrorAcqNTC2;
static uint8_T cntErrorAcqNTC1;
static uint8_T cntErrorAcqIABankSel;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : AnalogIn_Init
**
**   Description:
**    Reset the values of the module variables. The global variables (outputs) 
**    and the static variables (states) are reset to the default values by this function.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_Init(void)
{
#if (BOARD_TYPE == BOARD_EISB8F_A)
    /* Initialization of SWTrig structure */
    ADC_SWTrig_Init(NTC1_CH, NTC1_PER_AN, SAR, SARSV); //PB[5]
    ADC_SWTrig_Init(KEY_SIGNAL_CH, KEY_SIGNAL_PER_AN, SAR, SARSV); //PB[7]
    ADC_SWTrig_Init(NTC2_CH, NTC2_PER_AN, SAR, SARSV); //PG[5]
    ADC_SWTrig_Init(V_BATT_CH, V_BATT_PER_AN, SAR, SARSV); //PG[6]
    ADC_SWTrig_Init(BOARD_SEL_CH, BOARD_SEL_PER_AN, SAR, SARSV); //PG[9]
    ADC_SWTrig_Init(I_BATT_MON_CH, I_BATT_MON_PER_AN, SAR, SARSV); //PG[10]
    ADC_SWTrig_Init(IA_BANKSEL_CH, IA_BANKSEL_PER_AN, SAR, SARSV); //PG[2]
    ADC_SWTrig_Init(IDE_FS_LIVENESS_CH, IDE_FS_LIVENESS_PER_AN, SAR, SARSV); //PB[6]

    ADC_SWTrig_Init(I_BUCK_1_MON_CH, I_BUCK_1_MON_PER_AN, SD, SD0); //PB[0]
    ADC_SWTrig_Init(VC_3_7_MON_CH, VC_3_7_MON_PER_AN, SD, SD0); //PB[1]
    ADC_SWTrig_Init(VC_1_5_MON_CH, VC_1_5_MON_PER_AN, SD, SD0); //PB[2]
    ADC_SWTrig_Init(VCOIL_1_MON_CH, VCOIL_1_MON_PER_AN, SD, SD0); //PB[3]

    ADC_SWTrig_Init(VC_2_6_MON_CH, VC_2_6_MON_PER_AN, SD, SD3); //PB[12]
    ADC_SWTrig_Init(VC_4_8_MON_CH, VC_4_8_MON_PER_AN, SD, SD3); //PB[13]
    ADC_SWTrig_Init(I_BUCK_2_MON_CH, I_BUCK_2_MON_PER_AN, SD, SD3); //PB[14]
    ADC_SWTrig_Init(VCOIL_2_MON_CH, VCOIL_2_MON_PER_AN, SD, SD3); //PB[15]

    ADC_SWTrig_Init(I_PRI_B0_CH, I_PRI_B0_PER_AN, SAR, SAR0); //PD[11]
    ADC_SWTrig_Init(I_PRI_B1_CH, I_PRI_B1_PER_AN, SAR, SAR2); //PG[8]
#endif
}

/******************************************************************************
**   Function    : AnalogIn_VCapAcq
**
**   Description:
**    Capacitor voltage analog read
**
**   Parameters :
**    idx: Ion circuit index
**    reset: Reset VCharge on VChargeObj value
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_VCapAcq(uint8_T idx, uint8_T reset)
{
    uint8_T  stdiag;
    uint16_T vchargeraw_16;
    uint32_T tmp_vcharge;
    int16_T adcErrVCharge = 0;

    if (reset != 0u)
    {
        uint8_T i;
        for (i= 0u; i< PMOS_NUMBER; i++)
        {
            VChargeRaw[i] = VChargeObj[i];

            VCharge[i] = Median(&VChargeMBuff[i][0], &CntVCharge[i], VChargeRaw[i], MEDIANCAPLENGTH);
        }
    }
    else
    {
        /* Acquisizione tensione di carica condensatore ION */
        // Read raw value from the ADC (software triggered)
        adcErrVCharge = ADC_GetSampleResSoftTrig(AnVCharge[idx], &vchargeraw_16, 0U, SD0_PRI_CEIL, SD0_CEIL_PRI_ENABLED, SD0_PRI_CEIL_CORE);

        if (adcErrVCharge == NO_ERROR)
        {
            /* from SDADC to [V] scaled 2^-4 ==> 5/2^-11
               GNVCHARGE scaled 2^-9 */
            tmp_vcharge = (uint32_T)(vchargeraw_16);
            tmp_vcharge = (5U * tmp_vcharge)>>2; 
            tmp_vcharge = (tmp_vcharge * GNVCHARGE)>>18; 
            VChargeRaw[idx] = (uint16_T)(tmp_vcharge);
            //VCharge[idx] = (int16_T) NoiseGlitch_Filter(&VChargeMBuff[idx][0], &CntVCharge[idx], &VChargeMBuff[idx][MEDIAN_BUFF_MAX_SIZE], &VCGlitch[idx], (uint16_T)VChargeRaw[idx], THRVCHARGEGLITCH, MEDIANCAPLENGTH, 0u);
            VCharge[idx] = Median(&VChargeMBuff[idx][0], &CntVCharge[idx], VChargeRaw[idx], MEDIANCAPLENGTH);

            /* The following shall be re-integrated after debug phase */
            if (VtErrCION[idx] > VInfVCharge)
            {
                DiagMgm_SetDiagState(DiagVCap[idx], CIRCUIT_VOLTAGE_BELOW_THR, &stdiag);
            }
            else if (VtErrCION[idx] < VSupVCharge)
            {
                DiagMgm_SetDiagState(DiagVCap[idx], CIRCUIT_VOLTAGE_ABOVE_THR, &stdiag);
            }
            else
            {
                DiagMgm_SetDiagState(DiagVCap[idx], NO_PT_FAULT, &stdiag);
            }

            if ((uint16_T)VCharge[idx] < THRVSECDIAG)
            {
                if ((VtIShotPeak[NextIonCyl[idx]] > THRISHOTPEAKSCVBAT) && (FlgIShotTout[NextIonCyl[idx] & 0x01] == 1u))
                {
                    DiagMgm_SetDiagState(DiagSec[idx], CIRCUIT_SHORT_TO_VCC, &stdiag);
                }
                else if (VtIShotPeak[NextIonCyl[idx]] < THRISHOTPEAKSCGND)
                {
                    DiagMgm_SetDiagState(DiagSec[idx], CIRCUIT_SHORT_TO_GND, &stdiag);
                }
                else
                {
                    DiagMgm_SetDiagState(DiagSec[idx], NO_PT_FAULT, &stdiag);
                }
            }
            else
            {
                DiagMgm_SetDiagState(DiagSec[idx], NO_PT_FAULT, &stdiag);
            }
        }
    }
}

/******************************************************************************
**   Function    : AnalogIn_IBuck1_Acq
**
**   Description:
**    Buck 1 current analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_IBuck1_Acq(void)
{
    uint16_T isupplycoil1_raw;

    ADCErrIBuck1 = ADC_GetSampleResSoftTrig(I_BUCK_1_MON_CH, &isupplycoil1_raw, 0U, SD0_PRI_CEIL, SD0_CEIL_PRI_ENABLED, SD0_PRI_CEIL_CORE);
    if (ADCErrIBuck1 == NO_ERROR)
    {
        if(isupplycoil1_raw > 0x7FFFu)
        {
            ISupplyCoil1ADC = 0u;
        }
        else
        {
            ISupplyCoil1ADC = (uint16_T)(isupplycoil1_raw >> 3);
        }
    }
    else
    {
        ADCErrIBuck1Cnt++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_SupplyCoil1_Acq
**
**   Description:
**    Buck 1 Voltage analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_SupplyCoil1_Acq(void)
{
    uint16_T vsupplycoil1_raw;

    ADCErrVCoil1 = ADC_GetSampleResSoftTrig(VCOIL_1_MON_CH, &vsupplycoil1_raw, 0U, SD0_PRI_CEIL, SD0_CEIL_PRI_ENABLED, SD0_PRI_CEIL_CORE);
    if (ADCErrVCoil1 == NO_ERROR)
    {
        if(vsupplycoil1_raw > 0x7FFFu)
        {
            VSupplyCoil1ADC = 0u;
        }
        else
        {
            VSupplyCoil1ADC = (uint16_T)(vsupplycoil1_raw >> 3);
        }
    }
    else
    {
        ADCErrVCoil1Cnt++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_IBuck2_Acq
**
**   Description:
**    Buck 2 current analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_IBuck2_Acq(void)
{
    uint16_T isupplycoil2_raw;

    ADCErrIBuck2 = ADC_GetSampleResSoftTrig(I_BUCK_2_MON_CH, &isupplycoil2_raw, 0U, SD3_PRI_CEIL, SD3_CEIL_PRI_ENABLED, SD3_PRI_CEIL_CORE);
    if (ADCErrIBuck2 == NO_ERROR)
    {
        if(isupplycoil2_raw > 0x7FFFu)
        {
            ISupplyCoil2ADC = 0u;
        }
        else
        {
            ISupplyCoil2ADC = (uint16_T)(isupplycoil2_raw >> 3);
        }
    }
    else
    {
        ADCErrIBuck2Cnt++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_SupplyCoil2_Acq
**
**   Description:
**    Buck 2 Voltage analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_SupplyCoil2_Acq(void)
{
    uint16_T vsupplycoil2_raw;

    ADCErrVCoil2 = ADC_GetSampleResSoftTrig(VCOIL_2_MON_CH, &vsupplycoil2_raw, 0U, SD3_PRI_CEIL, SD3_CEIL_PRI_ENABLED, SD3_PRI_CEIL_CORE);
    if (ADCErrVCoil2 == NO_ERROR)
    {
        if(vsupplycoil2_raw > 0x7FFFu)
        {
            VSupplyCoil2ADC = 0u;
        }
        else
        {
            VSupplyCoil2ADC = (uint16_T)(vsupplycoil2_raw >> 3);
        }
    }
    else
    {
        ADCErrVCoil2Cnt++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_IBatt_Acq
**
**   Description:
**    Battery current analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_IBatt_Acq(void)
{
    uint16_T ibatt_raw;

    ADCErrIBatt = ADC_GetSampleResSoftTrig(I_BATT_MON_CH, &ibatt_raw,  0U, SARSV_PRI_CEIL, SARSV_CEIL_PRI_ENABLED, SARSV_PRI_CEIL_CORE);
    if (ADCErrIBatt == NO_ERROR)
    {
        IBattADC = ibatt_raw;
    }
    else
    {
        ADCErrIBattCnt++;
    }
}


/******************************************************************************
**   Function    : AnalogIn_IDE_FS_Liveness_Acq
**
**   Description:
**    Feedback liveness signal analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_IDE_FS_Liveness_Acq(void)
{
    if (ADC_GetSampleResSoftTrig(IDE_FS_LIVENESS_CH, &SARSV_AN20_Value, 0U, SARSV_PRI_CEIL, SARSV_CEIL_PRI_ENABLED, SARSV_PRI_CEIL_CORE) == NO_ERROR)
    {
        VVFdbkLiveness = SARSV_AN20_Value;
#ifdef TEST_CALCULATED_VALUE
        IDE_FS_LIVENESS_Value = ((float_T)SARSV_AN20_Value / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif 
    }
    else
    {
        cntErrorAcqLiveness++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_T10ms
**
**   Description:
**    10ms periodical function
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_T10ms(void)
{
}

/******************************************************************************
**   Function    : AnalogIn_T100ms
**
**   Description:
**    100ms periodical function
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_T100ms(void)
{
    /* ADC SW-triggered acquisitions */
    AnalogIn_BoardSel_Acq();

    AnalogIn_KeySignal_Acq();

    AnalogIn_VBatt_Acq();

    AnalogIn_NTC2_Acq();

    AnalogIn_NTC1_Acq();
    
    AnalogIn_IABankSel_Acq();
}

/******************************************************************************
**   Function    : Median
**
**   Description:
**    Computes arithmetical median
**
**   Parameters :
**    vtInData
**    cntI
**    inData
**    len
**
**   Returns:
**    Median value
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint16_T Median(uint16_T *vtInData, uint8_T *cntI, uint16_T inData, uint8_T len)
{
    uint16_T vtMedianLocal[MEDIAN_BUFF_MAX_SIZE];
    uint16_T median;
    uint8_T j, medianLength;
    int8_T k;

    medianLength = (uint8_T)(len << 1u) + 1u;

    /* Calcola mediana */
    vtInData[(*cntI)] = inData;

    k = (int8_T)(*cntI);

    (*cntI) = (*cntI) + 1u;
    if (((*cntI) >= medianLength) || ((*cntI) >= MEDIAN_BUFF_MAX_SIZE))
    {
        (*cntI) = 0u;
    }
    else
    {
        /* MISRA */
    }
    
    for(j = 0u; ((j < medianLength) && (j < MEDIAN_BUFF_MAX_SIZE)); j++)
    {
        vtMedianLocal[j] = vtInData[k];
        k--;
        if (k < 0)
        {
            k = min(((int8_T)medianLength - 1),((int8_T)MEDIAN_BUFF_MAX_SIZE -1));
        }
        else
        {
            /* MISRA */
        }
    }

    InsertionSort(&vtMedianLocal[0], medianLength);

    median = vtMedianLocal[(medianLength >> 1u)];
    
    return median;
}

/******************************************************************************
**   Function    : NoiseGlitch_Filter
**
**   Description:
**    Noise glitch filter
**
**   Parameters :
**    vtInData
**    cntI
**    oldData
**    glitch
**    inData
**    thrData
**    len
**    rst
**
**   Returns:
**    dataOut
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint16_T NoiseGlitch_Filter(uint16_T *vtInData, uint8_T *cntI, uint16_T *oldData, uint8_T *glitch, uint16_T inData, uint16_T thrData, uint8_T len, uint8_T rst)
{
    uint8_T j;
    uint16_T median;
    uint16_T dataOut;

    if (rst != 0u)
    {
        for (j = 0u; j < MEDIAN_BUFF_MAX_SIZE; j++)
        {
            vtInData[j] = inData;
        }
        *oldData = inData;
    }
    else { /* MISRA */ }

    median = Median(&vtInData[0], &cntI[0], inData, len);
    if (inData >= (median + thrData))
    {
        if (median >= ((*oldData) + thrData)) /* No Glitch */
        {
            dataOut = inData;
            *glitch = 0u;
        }
        else  /* Glitch */
        {
            dataOut = median;
            *glitch = 1u;
        }
    }
    else if ((inData + thrData) <= median)
    {
        if ((median + thrData) <= (*oldData)) /* No Glitch */
        {
            dataOut = inData;
            *glitch = 0u;
        }
        else /* Glitch */
        {
            dataOut = median;
            *glitch = 1u;
        }
    }
    else /* No Glitch */
    {
        dataOut = inData;
        *glitch = 0u;
    }

    (*oldData) = median;
    
    return dataOut;
}

/******************************************************************************
**   Function    : AnalogIn_LoadTest
**
**   Description:
**    Load test used for safe path test
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint16_T AnalogIn_LoadTest(uint8_T IgnIn)
{
    uint16_T retVal;
    uint16_T channel;
    uint8_T* adcErr;
    uint16_T buff[IPRI_PRELOAD_TEST];
    uint8_T i;

    if ((IgnIn & 0x01u) == 0u)
    {
        channel = I_PRI_B0_CH;
        adcErr = &ADCErrIpri0;
    }
    else
    {
        channel = I_PRI_B1_CH;
        adcErr = &ADCErrIpri1;
    }

    for(i = 0u; i < IPRI_PRELOAD_TEST; i++)
    {
        if (ADC_GetSampleResSoftTrig(channel, &buff[i], 0U, SAR0_PRI_CEIL, SAR0_CEIL_PRI_ENABLED, SAR0_PRI_CEIL_CORE) != NO_ERROR)
        {
            (*adcErr)++;
        }
        else
        {
            /* MISRA */
        }
    }
    InsertionSort(buff, IPRI_PRELOAD_TEST);
    retVal = buff[(IPRI_PRELOAD_TEST >> 1)];

    if ((IgnIn & 0x01u) == 0u)
    {
        SAR0_StopConversion();
     }
    else
    {
        SAR2_StopConversion();
     }

    return retVal;
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : AnalogIn_NTC1_Acq
**
**   Description:
**    NTC1 sensor analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void AnalogIn_NTC1_Acq(void)
{
    if (ADC_GetSampleResSoftTrig(NTC1_CH, &SARSV_AN35_Value, 0U, SARSV_PRI_CEIL, SARSV_CEIL_PRI_ENABLED, SARSV_PRI_CEIL_CORE) == NO_ERROR)
    {
        VTempECU1 = SARSV_AN35_Value; // (uint16_T)((((int32_T) (SARSV_AN35_Value) * (int32_T) (GNVTEMPECU1)) >> 8) >> 10) + (uint16_T)OFSVTEMPECU1;

#ifdef TEST_CALCULATED_VALUE
        NTC1_Value = ((float_T)SARSV_AN35_Value / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif 
    }
    else
    {
        cntErrorAcqNTC1++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_KeySignal_Acq
**
**   Description:
**    Key signal analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void AnalogIn_KeySignal_Acq(void)
{
    if (ADC_GetSampleResSoftTrig(KEY_SIGNAL_CH, &SARSV_AN21_Value, 0U, SARSV_PRI_CEIL, SARSV_CEIL_PRI_ENABLED, SARSV_PRI_CEIL_CORE) == NO_ERROR)
    {
        VVKeySignal = max(0u,SARSV_AN21_Value);
        VKeySignal = VVKeySignal;
#ifdef TEST_CALCULATED_VALUE
        KEY_SIGNAL_Value = ((float_T)SARSV_AN21_Value / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif 
    }
    else
    {
        cntErrorAcqKeySignal++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_NTC2_Acq
**
**   Description:
**    NTC2 sensor analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void AnalogIn_NTC2_Acq(void)
{
    if (ADC_GetSampleResSoftTrig(NTC2_CH, &SARSV_AN25_Value, 0U, SARSV_PRI_CEIL, SARSV_CEIL_PRI_ENABLED, SARSV_PRI_CEIL_CORE) == NO_ERROR)
    {
        VTempECU2 = SARSV_AN25_Value; // (uint16_T)((((int32_T) (SARSV_AN25_Value) * (int32_T) (GNVTEMPECU2)) >> 8) >> 10) + (uint16_T)OFSVTEMPECU2;

#ifdef TEST_CALCULATED_VALUE
        NTC2_Value = ((float_T)SARSV_AN25_Value / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif 
    }
    else
    {
        cntErrorAcqNTC2++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_VBatt_Acq
**
**   Description:
**    Battery voltage analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void AnalogIn_VBatt_Acq(void)
{
    int16_T  tmp;
    uint8_T pt_fault = NO_FAULT;
    int16_T vbatfiltin;
    int16_T vbatfiltout;
    static int32_T vbathires = 0;
    
    if (ADC_GetSampleResSoftTrig(V_BATT_CH, &SARSV_AN24_Value, 0U, SARSV_PRI_CEIL, SARSV_CEIL_PRI_ENABLED, SARSV_PRI_CEIL_CORE) == NO_ERROR)
    {
        AnalogIn_Force(&VVBattery, SARSV_AN24_Value, FOVVBATTERY);
        //AnalogIn_ReadDiagForceExt(&tmp, AN_KL30, &VVBattery, THR_D_HL, (int16_T)VINFVBATTERY, (int16_T)VSUPVBATTERY, 0, 0, DIAG_VBATTERY, GNVBATTERY, 5u, OFSVBATTERY, FLGFOVVBATTERY, FOVVBATTERY, 0u, AnVBatteryBN[idx], queue, PRI, 0u);
        tmp = AnalogIn_Conv(&VVBattery, GNVBATTERY, 5u, OFSVBATTERY);

        DiagMgm_RangeCheck_S16(&pt_fault, tmp, (int16_T)VINFVBATTERY, (int16_T)VSUPVBATTERY, 0, 0, CIRCUIT_VOLTAGE_BELOW_THR, CIRCUIT_VOLTAGE_ABOVE_THR, CIRCUIT_OPEN, THR_D_HL, DIAG_VBATTERY);

        tmp = max(0, tmp);
        VBattery = (uint16_T) tmp;

        vbatfiltin = (int16_T)(VBattery);
        FOF_Reset_S16_FXP(&vbatfiltout, &vbathires, vbatfiltin, KFILTVBATTERY, 0, 0u, vbathires);
        VBatteryF = (uint16_T)(vbatfiltout);
    }
    else
    {
        cntErrorAcqVBatt++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_BoardSel_Acq
**
**   Description:
**    BoardSel analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void AnalogIn_BoardSel_Acq(void)
{
    if (ADC_GetSampleResSoftTrig(BOARD_SEL_CH, &SARSV_AN13_Value, 0U, SARSV_PRI_CEIL, SARSV_CEIL_PRI_ENABLED, SARSV_PRI_CEIL_CORE) == NO_ERROR)
    {

#ifdef TEST_CALCULATED_VALUE
        BOARD_SEL_Value = ((float_T)SARSV_AN13_Value / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif 
    }
    else
    {
        cntErrorAcqBoardSel++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_IABankSel_Acq
**
**   Description:
**    BankSel analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void AnalogIn_IABankSel_Acq(void)
{
    if (ADC_GetSampleResSoftTrig(IA_BANKSEL_CH, &SARSV_AN46_Value, 0U, SARSV_PRI_CEIL, SARSV_CEIL_PRI_ENABLED, SARSV_PRI_CEIL_CORE) == NO_ERROR)
    {
        VVBankSel = max(0u,SARSV_AN46_Value);
        VBankSel = VVBankSel;
        
#ifdef TEST_CALCULATED_VALUE
        IA_BANKSEL_Value = ((float_T)SARSV_AN46_Value / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif 
    }
    else
    {
        cntErrorAcqIABankSel++;
    }
}

/******************************************************************************
**   Function    : AnalogIn_Force
**
**   Description:
**    Force value
**
**   Parameters :
**    varout: Pointer to output variable
**    varin: Input value
**    fovalue: forced value
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void AnalogIn_Force(int16_T *varout, uint16_T varin, const int16_T fovalue)
{
     // Force value by calibration (SCU_FUN_ANALOGIN_151)
    if (fovalue >= 0)
    {
        *varout = fovalue;
    }
    else 
    {
        *varout = (int16_T) varin;
    }
}

/******************************************************************************
**   Function    : AnalogIn_Conv
**
**   Description:
**    Converts raw value depending of
**
**   Parameters :
**    pRawValue: Raw value
**    GN: Gain
**    KDG: 
**    OFS: Offset
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T AnalogIn_Conv(uint16_T *pRawValue, const int16_T GN, const uint8_T KDG, const int16_T OFS)
{
    int16_T varout_tmp;
    
    varout_tmp = (int16_T)((((int32_T) (*pRawValue) * (int32_T) (GN)) >> KDG) >> 10) + (int16_T)OFS;

    return varout_tmp;
}

#endif // _BUILD_ANALOGIN_
/****************************************************************************
 ****************************************************************************/

