/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_IOs.h
 **  Date:          10-Sep-2021
 **
 **  Model Version: 1.432
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_IOs_h_
#define RTW_HEADER_TLE9278BQX_IOs_h_
#ifndef TLE9278BQX_IOs_COMMON_INCLUDES_
# define TLE9278BQX_IOs_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TLE9278BQX_IOs_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: ELD_EXPORT_DEFINE */
#define LT_ERROR                       1U                        /* Referenced by: '<S4>/Init' */

/* status */
#define LT_NORMAL                      0U                        /* Referenced by:
                                                                  * '<S4>/Init'
                                                                  * '<S4>/LT_NORMAL'
                                                                  */

/* status */

/* user code (top of header file) */

/* System '<Root>' */
#include "vsrammgm.h"

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void TLE9278BQX_IOs_initialize(void);

/* Exported entry point function */
extern void TLE9278BQX_IOs_Init(void);

/* Exported entry point function */
extern void TLE9278BQX_IOs_RT1(void);

/* Exported entry point function */
extern void TLE9278BQX_IOs_RT2(void);

/* Exported entry point function */
extern void TLE9278BQX_IOs_RT3(void);

/* Exported entry point function */
extern void TLE9278BQX_IOs_RT4(void);

/* Exported entry point function */
extern void TLE9278BQX_IOs_RT5(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FdbkLiveness;           /* '<S3>/Merge' */

/* Liveness feedback */
extern uint8_T StLoadTstError;         /* '<S3>/Merge1' */

/* Load test status error */


/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TLE9278BQX_IOs'
 * '<S1>'   : 'TLE9278BQX_IOs/Model Info'
 * '<S2>'   : 'TLE9278BQX_IOs/TLE9278BQX_IOs'
 * '<S3>'   : 'TLE9278BQX_IOs/TLE9278BQX_IOs/Merger'
 * '<S4>'   : 'TLE9278BQX_IOs/TLE9278BQX_IOs/fc_Init'
 * '<S5>'   : 'TLE9278BQX_IOs/TLE9278BQX_IOs/fc_RT1'
 * '<S6>'   : 'TLE9278BQX_IOs/TLE9278BQX_IOs/fc_RT2'
 * '<S7>'   : 'TLE9278BQX_IOs/TLE9278BQX_IOs/fc_RT3'
 * '<S8>'   : 'TLE9278BQX_IOs/TLE9278BQX_IOs/fc_RT4'
 * '<S9>'   : 'TLE9278BQX_IOs/TLE9278BQX_IOs/fc_RT5'
 * '<S10>'  : 'TLE9278BQX_IOs/TLE9278BQX_IOs/fc_Init/Init'
 * '<S11>'  : 'TLE9278BQX_IOs/TLE9278BQX_IOs/fc_RT2/calc_FdbkLiveness'
 */

/*-
 * Requirements for '<Root>': TLE9278BQX_IOs
 */
#endif                                 /* RTW_HEADER_TLE9278BQX_IOs_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/