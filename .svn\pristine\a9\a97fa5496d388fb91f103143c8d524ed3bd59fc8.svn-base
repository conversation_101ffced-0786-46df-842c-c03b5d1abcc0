/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           CombAdp.c
 **  File Creation Date: 15-Sep-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         CombAdp
 **  Model Description:  The aim of this module is to calculate an adaptive correction strategy for cylinder balancing injection correction.
   The cylinder balancing nominal correction (calculated by CombBal) is corrected through a corrective gain calculated by CombAdp.
 **  Model Version:      1.1042
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Wed Sep 15 11:59:11 2021
 **
 **  Last Saved Modification:  RoccaG - Wed Sep 15 11:57:48 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "CombAdp_out.h"
#include "CombAdp_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKETACSICYLBALAD_dim           5U                        /* Referenced by: '<S10>/BKETACSIKNOCK_dim' */

/* Length for breakpoint BKETACSICYLBALAD -1 */
#define BKLOADCYLBAL_dim               4U                        /* Referenced by:
                                                                  * '<S1>/AdaptiveCorrectionMgm'
                                                                  * '<S8>/ENKNOCKAD6'
                                                                  */

/* Length for breakpoint BKLOADCYLBAL -1 */
#define BKRPMCYLBAL_dim                11U                       /* Referenced by:
                                                                  * '<S1>/AdaptiveCorrectionMgm'
                                                                  * '<S7>/ENKNOCKAD3'
                                                                  * '<S8>/ENKNOCKAD3'
                                                                  * '<S15>/BKRPMCYLBAL_dim'
                                                                  */

/* Length for breakpoint BKRPMCYLBAL -1 */
#define ID_VER_COMBADP_DEF             11041U                    /* Referenced by: '<S2>/Constant7' */

/* Model Version. */
#define MAX_ADSTEP_CORR                32767                     /* Referenced by: '<S6>/ONE4' */

/* Maximum adaptative correction for each calculation step */
#define MAX_INJ_CORR_OFF               32767U                    /* Referenced by: '<S6>/ONE' */

/* Offset for maximum allowed value for InjCorrCylAd signal (calculated according calibration MAXADDCYLBALAD) */
#define MAX_RPM_DIV_100                100U                      /* Referenced by: '<S8>/MAX_RPM' */

/* Normalization on rpm, used to define steady state. */
#define MIN_ADSTEP_CORR                -32768                    /* Referenced by: '<S6>/ONE5' */

/* Minimum adaptative correction for each calculation step */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */

/* System '<Root>' */
#ifdef _BUILD_COMBADP_

/* Add a check for each important define */
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint16_T IdxLoadCylBal;         /* '<S16>/PreLookUpIdSearch_U16' */

/* Index for pre-lookup interpolation on engine load. */
static uint16_T IdxRpmCylBal;          /* '<S17>/PreLookUpIdSearch_U16' */

/* Index for pre-lookup interpolation on engine speed. */
static uint16_T RtLoadCylBal;          /* '<S16>/Data Type Conversion1' */

/* Ratio for pre-lookup interpolation on engine load. */
static uint16_T RtRpmCylBal;           /* '<S17>/Data Type Conversion1' */

/* Ratio for pre-lookup interpolation on engine speed. */
static uint16_T loadStabSignal;        /* '<S19>/Memory1' */

/* Old engine load signal for stability */
static uint16_T loadStabTimer;         /* '<S19>/Memory' */

/* Timer for engine stability */
static uint16_T rpmStabSignal;         /* '<S18>/Memory1' */

/* Old engine speed signal for stability */
static uint16_T rpmStabTimer;          /* '<S18>/Memory' */

/* Timer for rpm stability */
static uint8_T stab_load_state;        /* '<S3>/Merge7' */

/* State for load stability */
static uint8_T stab_rpm_state;         /* '<S3>/Merge2' */

/* State for rpm stability */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKETACSICYLBALAD[6] = { 0U, 23665U,
  29799U, 35390U, 41753U, 65529U } ;   /* Referenced by:
                                        * '<S6>/BKETACSICYLBALAD'
                                        * '<S6>/BKETACSICYLBALAD1'
                                        */

/* Vector of breakpoints for the rpm and load ratios */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADCYLBAL[5] = { 1920U, 2560U,
  3200U, 3840U, 4480U } ;              /* Referenced by: '<S8>/ENKNOCKAD5' */

/* Breakpoints of engine load for cylbal learning */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMCYLBAL[12] = { 800U, 950U, 1000U,
  1050U, 1100U, 1500U, 2000U, 2500U, 3000U, 3500U, 3750U, 4000U } ;/* Referenced by: '<S8>/ENKNOCKAD1' */

/* Breakpoints of engine speed for cylbal learning */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CYLBALGNAD = 1024U;/* Referenced by: '<S6>/CYLBALGNAD' */

/* CylBalIndAvgCyl adaptive gain */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CYLBALLEARNDUR = 50U;/* Referenced by:
                                                                * '<S1>/ENCYLBALADCORR1'
                                                                * '<S9>/KNOCKLEARNDUR'
                                                                */

/* Cylinder balancing learning period */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENCYLBALAD = 1U;/* Referenced by: '<S5>/ENCYLBALAD' */

/* Cylinder Balancing Adaptive enable flag */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENCYLBALADCORR = 1U;/* Referenced by:
                                                              * '<S1>/ENCYLBALADCORR'
                                                              * '<S5>/ENCYLBALADCORR'
                                                              */

/* Cylinder Balancing Adaptive correction enable flag */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXCYLBALADWEIGHT = 4096U;
                                   /* Referenced by: '<S6>/MAXCYLBALADWEIGHT' */

/* Max map weight */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBCYLBALGNAD[36] = { 1024U, 1901U,
  2031U, 2034U, 1904U, 1024U, 1901U, 3529U, 3771U, 3778U, 3536U, 1901U, 2031U,
  3771U, 4028U, 4036U, 3778U, 2031U, 2034U, 3778U, 4036U, 4044U, 3785U, 2034U,
  1904U, 3536U, 3778U, 3785U, 3543U, 1904U, 1024U, 1901U, 2031U, 2034U, 1904U,
  1024U } ;                            /* Referenced by: '<S6>/TBCYLBALGNAD' */

/* Table of gains for adaptive coefficients spreading */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THCYLBALCORRINDAD = 33U;
                                     /* Referenced by: '<S1>/ENCYLBALADCORR2' */

/* Threshold on the mean cylinder balancing corr. to trigger the learning */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABLOADADPTR = 640U;
                                    /* Referenced by: '<S8>/THRSTABLOADLAMTR' */

/* Stability range for Load */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABRPMADPTR = 640U;
                                     /* Referenced by: '<S8>/THRSTABRPMLAMTR' */

/* Stability range for Engine speed */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T THWATCYLBALAD = 1200;/* Referenced by: '<S5>/THWATCYLBALAD' */

/* Threshold on the coolant temperature to enable cylbal learning */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTTDCSTABCYLBALAD[12] = { 100U, 120U,
  125U, 130U, 135U, 150U, 160U, 170U, 180U, 190U, 195U, 200U } ;
                                   /* Referenced by: '<S8>/VTTDCSTABCYLBALAD' */

/* Number of TDCs to detect stability of Load and RpmF for CylBal learning */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T InjCorrCylAd;                 /* '<S3>/Merge1' */

/* Adaptative correction for cylinder balancing */
uint16_T InjCorrCylAdNorm;             /* '<S3>/Merge3' */

/* Normalization factor for adaptative correction on cylinder balancing PI and output */
uint8_T VtCylBalTrigAdat[8];           /* '<S3>/Merge15' */

/* Cylinder balancing adaptativity Trigger */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T CylBalIndAvgCyl;/* '<S3>/Merge14' */

/* Mean Cylinder balancing Individual correction */
STATIC_TEST_POINT uint8_T FStabLoadCylBalAd;/* '<S3>/Merge5' */

/* Load stable flag for cylinder balancing */
STATIC_TEST_POINT uint8_T FStabRpmCylBalAd;/* '<S3>/Merge4' */

/* RpmF stable flag for cylinder balancing */
STATIC_TEST_POINT uint8_T FlgSteadyStateAd;/* '<S3>/Merge6' */

/* Steady state flag for cylinder balancing adaptive correction */
STATIC_TEST_POINT uint32_T IdVer_CombAdp;/* '<S2>/Constant7' */

/* Model Version */
STATIC_TEST_POINT uint16_T VtCntCylBalLearn[8];/* '<S3>/Merge11' */

/* Cylinder balancing learning counter */
STATIC_TEST_POINT uint32_T VtCylBalIndSum[8];/* '<S3>/Merge13' */

/* Cylinder balancing accumulator */
STATIC_TEST_POINT uint8_T VtCylBalLearnState[8];/* '<S3>/Merge12' */

/* Cylinder balancing learning state 0 = Normal, 1 = Learning */
STATIC_TEST_POINT uint8_T VtEndCylBalLearn[8];/* '<S3>/Merge10' */

/* Cylinder balancing adaptativity learning end */
STATIC_TEST_POINT uint16_T VtInjCorrCylAd[8];/* '<S3>/Merge9' */

/* Cylinder FFS Correction Adaptive */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<S1>/EvalAdCorr'
 * Block description for: '<S1>/EvalAdCorr'
 *   This block calculates the right correction term for a single cell of
 *   adaptive table and updates it.
 */
void CombAdp_EvalAdCorr(uint16_T rtu_CylBalIndAvgCyl, uint16_T rtu_eta, uint16_T
  rtu_csi, uint8_T rtu_indr, uint8_T rtu_indc, uint16_T rtu_RtRpmCylBal,
  uint16_T rtu_RtLoadCylBal)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_U16_U16_U16;
  uint16_T rtb_Conversion;
  int32_T rtb_MinMax4;
  uint32_T rtb_MinMax2;
  int32_T rtb_MinMax1;

  /* S-Function (Look2D_U16_U16_U16): '<S13>/Look2D_U16_U16_U16' incorporates:
   *  Constant: '<S10>/BKETACSIKNOCK_dim'
   *  Constant: '<S6>/BKETACSICYLBALAD'
   *  Constant: '<S6>/BKETACSICYLBALAD1'
   *  Constant: '<S6>/TBCYLBALGNAD'
   */
  Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBCYLBALGNAD[0], rtu_RtRpmCylBal,
                     &BKETACSICYLBALAD[0], ((uint8_T)BKETACSICYLBALAD_dim),
                     rtu_RtLoadCylBal, &BKETACSICYLBALAD[0], ((uint8_T)
    BKETACSICYLBALAD_dim));

  /* Product: '<S6>/Product2' incorporates:
   *  Product: '<S6>/Product1'
   */
  rtb_Conversion = (uint16_T)((((((uint32_T)rtu_eta) * ((uint32_T)rtu_csi)) >>
    ((uint32_T)6)) * ((uint32_T)rtb_Look2D_U16_U16_U16)) >> ((uint32_T)12));

  /* MinMax: '<S6>/MinMax' incorporates:
   *  Constant: '<S6>/MAXCYLBALADWEIGHT'
   */
  if (rtb_Conversion >= MAXCYLBALADWEIGHT) {
    rtb_Conversion = MAXCYLBALADWEIGHT;
  }

  /* End of MinMax: '<S6>/MinMax' */

  /* Product: '<S11>/Divide' incorporates:
   *  Constant: '<S6>/CYLBALGNAD'
   *  Constant: '<S6>/ONE2'
   *  Product: '<S12>/Divide'
   *  Product: '<S6>/Product3'
   *  Product: '<S6>/Product4'
   *  Sum: '<S6>/Sum2'
   */
  rtb_MinMax4 = ((((((int32_T)rtu_CylBalIndAvgCyl) - 32768) * ((int32_T)
    CYLBALGNAD)) / 2048) * ((int32_T)rtb_Conversion)) / 2048;

  /* MinMax: '<S6>/MinMax3' incorporates:
   *  Constant: '<S6>/ONE4'
   */
  if (rtb_MinMax4 >= MAX_ADSTEP_CORR) {
    rtb_MinMax4 = MAX_ADSTEP_CORR;
  }

  /* MinMax: '<S6>/MinMax4' incorporates:
   *  Constant: '<S6>/ONE5'
   *  MinMax: '<S6>/MinMax3'
   */
  if (rtb_MinMax4 <= MIN_ADSTEP_CORR) {
    rtb_MinMax4 = MIN_ADSTEP_CORR;
  }

  /* CCaller: '<S6>/GetTbKnockAdEE' */
  rtb_Conversion = Get_TbInjCorrAdEE(rtu_indr, rtu_indc);

  /* Product: '<S6>/Product' incorporates:
   *  Constant: '<S6>/ONE3'
   *  MinMax: '<S6>/MinMax4'
   *  Sum: '<S6>/Sum3'
   */
  rtb_MinMax2 = ((((uint32_T)((int32_T)(rtb_MinMax4 + 32768))) * ((uint32_T)
    rtb_Conversion)) >> ((uint32_T)15));

  /* Sum: '<S6>/Sum5' incorporates:
   *  Constant: '<S6>/MAXADDCYLBALAD'
   *  Constant: '<S6>/ONE'
   */
  rtb_MinMax4 = (int32_T)((uint32_T)(((uint32_T)MAXADDCYLBALAD) + ((uint32_T)
    ((uint16_T)MAX_INJ_CORR_OFF))));

  /* MinMax: '<S6>/MinMax1' */
  if (((uint32_T)rtb_MinMax4) < rtb_MinMax2) {
    rtb_MinMax1 = rtb_MinMax4;
  } else {
    rtb_MinMax1 = (int32_T)rtb_MinMax2;
  }

  /* End of MinMax: '<S6>/MinMax1' */

  /* Sum: '<S6>/Sum1' incorporates:
   *  Constant: '<S6>/MAXADDCYLBALAD1'
   *  Constant: '<S6>/ONE1'
   */
  rtb_MinMax4 = (int32_T)((uint32_T)(32768U - ((uint32_T)MAXADDCYLBALAD)));

  /* MinMax: '<S6>/MinMax2' */
  if (((uint32_T)rtb_MinMax1) > ((uint32_T)rtb_MinMax4)) {
    rtb_MinMax4 = rtb_MinMax1;
  }

  /* End of MinMax: '<S6>/MinMax2' */

  /* CCaller: '<S6>/Set_TbInjCorrCylAd' */
  Set_TbInjCorrAdEE(rtu_indr, rtu_indc, (uint16_T)rtb_MinMax4);
}

/*
 * Output and update for function-call system: '<S1>/Interpolate_TbInjCorrCylAd'
 * Block description for: '<S1>/Interpolate_TbInjCorrCylAd'
 *   This block is used to inteporlate ee table TbInjCorrCylAd.
 */
void Comb_Interpolate_TbInjCorrCylAd(uint16_T rtu_IdxRpmCylBal, uint16_T
  rtu_RtRpmCylBal, uint16_T rtu_IdxLoadCylBal, uint16_T rtu_RtLoadCylBal,
  uint8_T rtu_IonAbsTdcEOA, uint16_T *rty_InjAdaptiveCorr)
{
  /* CCaller: '<S7>/Interpolate_TbInjCorrCylAd' incorporates:
   *  Constant: '<S7>/Constant5'
   *  Constant: '<S7>/ENKNOCKAD3'
   *  Product: '<S7>/Product'
   *  Sum: '<S7>/Add'
   *  Sum: '<S7>/Add1'
   */
  *rty_InjAdaptiveCorr = Interpolate_TbInjCorrAdEE((uint16_T)((((uint32_T)
    ((uint8_T)(((uint32_T)((uint8_T)BKRPMCYLBAL_dim)) + 1U))) * ((uint32_T)
    rtu_IonAbsTdcEOA)) + ((uint32_T)rtu_IdxRpmCylBal)), rtu_RtRpmCylBal,
    rtu_IdxLoadCylBal, rtu_RtLoadCylBal);
}

/*
 * Output and update for function-call system: '<Root>/Reset'
 * Block description for: '<Root>/Reset'
 *   This subsytem reset model output at power-on event and at no_sync event.
 */
void CombAdp_Reset(void)
{
  int32_T i;
  for (i = 0; i < 8; i++) {
    /* SignalConversion generated from: '<S2>/VtCylBalTrigAdat' */
    VtCylBalTrigAdat[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/VtInjCorrCylAd' */
    VtInjCorrCylAd[(i)] = 32768U;

    /* SignalConversion generated from: '<S2>/VtEndCylBalLearn' */
    VtEndCylBalLearn[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/VtCntCylBalLearn' */
    VtCntCylBalLearn[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/VtCylBalLearnState' */
    VtCylBalLearnState[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/VtCylBalIndSum' */
    VtCylBalIndSum[(i)] = 0U;
  }

  /* SignalConversion generated from: '<S2>/InjCorrCylAd' incorporates:
   *  Constant: '<S2>/Constant'
   */
  InjCorrCylAd = 32768U;

  /* SignalConversion generated from: '<S2>/InjCorrCylAdNorm' incorporates:
   *  Constant: '<S2>/Constant2'
   */
  InjCorrCylAdNorm = 32768U;

  /* SignalConversion generated from: '<S2>/FStabRpmCylBalAd' incorporates:
   *  Constant: '<S2>/Constant1'
   */
  FStabRpmCylBalAd = 0U;

  /* SignalConversion generated from: '<S2>/FStabLoadCylBalAd' incorporates:
   *  Constant: '<S2>/Constant4'
   */
  FStabLoadCylBalAd = 0U;

  /* SignalConversion generated from: '<S2>/FlgSteadyStateAd' incorporates:
   *  Constant: '<S2>/Constant5'
   */
  FlgSteadyStateAd = 0U;

  /* SignalConversion generated from: '<S2>/CylBalIndAvgCyl' incorporates:
   *  Constant: '<S2>/Constant13'
   */
  CylBalIndAvgCyl = 0U;

  /* SignalConversion generated from: '<S2>/stab_rpm_state' incorporates:
   *  Constant: '<S2>/Constant3'
   */
  stab_rpm_state = 0U;

  /* SignalConversion generated from: '<S2>/stab_load_stable' incorporates:
   *  Constant: '<S2>/Constant6'
   */
  stab_load_state = 0U;

  /* Constant: '<S2>/Constant7' */
  IdVer_CombAdp = ID_VER_COMBADP_DEF;
}

/* Model step function */
void CombAdp_EOA(void)
{
  /* local block i/o variables */
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint16_T rtb_SigStab_o3_k;
  uint16_T rtb_SigStab_o4_n;
  uint16_T rtb_LookUp_IR_U16;
  uint8_T rtb_SigStab_o1;
  uint8_T rtb_SigStab_o2;
  uint8_T rtb_SigStab_o1_n;
  uint8_T rtb_SigStab_o2_k;
  uint8_T k;
  int8_T rtb_VtEndCylBalLearn[8];
  uint16_T rtb_VtCntCylBalLearn[8];
  boolean_T rtb_EnableAdpCorr;
  uint16_T rtb_VtCntCylBalLearn_c[8];
  int8_T rtb_VtCylBalLearnState_f[8];
  int8_T rtb_VtCylBalTrigAdat_p[8];
  int8_T rtb_VtEndCylBalLearn_g[8];
  uint32_T rtb_VtCylBalIndSum_m[8];
  uint16_T rtb_VtInjCorrCylAd_p[8];
  uint8_T rtb_stab_rpm_state;
  uint8_T rtb_stab_load_state;
  uint16_T rtb_Memory;
  uint16_T rtb_Product1;
  uint8_T indr;
  uint16_T Interpolate_TbInjCorrCylAd_b;
  uint32_T Add;
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/CombAdp_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  This block perform the adaptative strategy on injection correction at
   *  end-of-acquisition event.
   */
  for (i = 0; i < 8; i++) {
    /* SignalConversion generated from: '<S1>/VtEndCylBalLearn_old' */
    rtb_VtEndCylBalLearn[i] = (int8_T)VtEndCylBalLearn[(i)];

    /* SignalConversion generated from: '<S1>/VtCntCylBalLearn_old' */
    rtb_VtCntCylBalLearn[i] = VtCntCylBalLearn[(i)];
  }

  /* SignalConversion generated from: '<S1>/sstab_rpm_old' */
  rtb_stab_rpm_state = stab_rpm_state;

  /* SignalConversion generated from: '<S1>/sstab_load_old' */
  rtb_stab_load_state = stab_load_state;

  /* Logic: '<S5>/Logical Operator3' incorporates:
   *  Constant: '<S5>/Constant5'
   *  Constant: '<S5>/ENCYLBALAD'
   *  Constant: '<S5>/ENCYLBALADCORR'
   *  Constant: '<S5>/THWATCYLBALAD'
   *  DataTypeConversion: '<S5>/Conversion'
   *  DataTypeConversion: '<S5>/Conversion1'
   *  Inport: '<Root>/FlgEOL'
   *  Inport: '<Root>/TWater'
   *  RelationalOperator: '<S5>/Relational Operator5'
   *  RelationalOperator: '<S5>/Relational Operator6'
   */
  rtb_EnableAdpCorr = ((((((int32_T)ENCYLBALAD) != 0) && (((int32_T)
    ENCYLBALADCORR) != 0)) && (((int32_T)FlgEOL) == 0)) && (TWater >
    THWATCYLBALAD));

  /* Product: '<S8>/Product1' incorporates:
   *  Constant: '<S8>/MAX_RPM'
   *  Inport: '<Root>/RpmF'
   */
  rtb_Product1 = (uint16_T)((((uint32_T)RpmF) << ((uint32_T)7)) / ((uint32_T)
    ((uint8_T)MAX_RPM_DIV_100)));

  /* S-Function (PreLookUpIdSearch_U16): '<S17>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S8>/ENKNOCKAD1'
   *  Constant: '<S8>/ENKNOCKAD3'
   */
  PreLookUpIdSearch_U16( (&(IdxRpmCylBal)), &rtb_LookUp_IR_U16, RpmF,
                        &BKRPMCYLBAL[0], ((uint8_T)BKRPMCYLBAL_dim));

  /* DataTypeConversion: '<S17>/Data Type Conversion1' */
  RtRpmCylBal = rtb_LookUp_IR_U16;

  /* S-Function (LookUp_IR_U16): '<S20>/LookUp_IR_U16' incorporates:
   *  Constant: '<S15>/BKRPMCYLBAL_dim'
   *  Constant: '<S8>/VTTDCSTABCYLBALAD'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTTDCSTABCYLBALAD[0], IdxRpmCylBal,
                RtRpmCylBal, ((uint8_T)BKRPMCYLBAL_dim));

  /* Memory: '<S18>/Memory1' */
  rtb_Memory = rpmStabSignal;

  /* Memory: '<S18>/Memory' */
  RtLoadCylBal = rpmStabTimer;

  /* S-Function (SigStab): '<S18>/SigStab' incorporates:
   *  Constant: '<S8>/Constant'
   *  Constant: '<S8>/THRSTABRPMLAMTR'
   */
  SigStab( &rtb_SigStab_o1, &rtb_SigStab_o2, &rtb_SigStab_o3, &rtb_SigStab_o4,
          rtb_Product1, ((uint8_T)0U), THRSTABRPMADPTR, rtb_LookUp_IR_U16,
          rtb_stab_rpm_state, rtb_Memory, RtLoadCylBal);

  /* Memory: '<S19>/Memory1' */
  RtLoadCylBal = loadStabSignal;

  /* Memory: '<S19>/Memory' */
  rtb_Memory = loadStabTimer;

  /* S-Function (SigStab): '<S19>/SigStab' incorporates:
   *  Constant: '<S8>/Constant1'
   *  Constant: '<S8>/THRSTABLOADLAMTR'
   */
  SigStab( &rtb_SigStab_o1_n, &rtb_SigStab_o2_k, &rtb_SigStab_o3_k,
          &rtb_SigStab_o4_n, Load, ((uint8_T)0U), THRSTABLOADADPTR,
          rtb_LookUp_IR_U16, rtb_stab_load_state, RtLoadCylBal, rtb_Memory);

  /* DataTypeConversion: '<S8>/Conversion2' incorporates:
   *  DataTypeConversion: '<S8>/Conversion'
   *  DataTypeConversion: '<S8>/Conversion1'
   *  Logic: '<S8>/Logical Operator2'
   */
  rtb_stab_rpm_state = (uint8_T)(((((int32_T)rtb_SigStab_o1) != 0) && (((int32_T)
    rtb_SigStab_o1_n) != 0)) ? 1 : 0);

  /* S-Function (PreLookUpIdSearch_U16): '<S16>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S8>/ENKNOCKAD5'
   *  Constant: '<S8>/ENKNOCKAD6'
   */
  PreLookUpIdSearch_U16( (&(IdxLoadCylBal)), (&(RtLoadCylBal)), Load,
                        &BKLOADCYLBAL[0], ((uint8_T)BKLOADCYLBAL_dim));

  /* Chart: '<S1>/AdaptiveCorrectionMgm' incorporates:
   *  Constant: '<S1>/ENCYLBALADCORR'
   *  Constant: '<S1>/ENCYLBALADCORR1'
   *  Constant: '<S1>/ENCYLBALADCORR2'
   *  Constant: '<S9>/KNOCKLEARNDUR'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Product: '<S9>/Product'
   *  SignalConversion generated from: '<S1>/CylBalIndAvgCyl_old'
   *  SignalConversion generated from: '<S1>/VtCntCylBalLearn_old'
   *  SignalConversion generated from: '<S1>/VtCylBalIndSum_old'
   *  SignalConversion generated from: '<S1>/VtCylBalLearnState_old'
   *  SignalConversion generated from: '<S1>/VtCylBalTrigAdat_old'
   *  SignalConversion generated from: '<S1>/VtEndCylBalLearn_old'
   *  SignalConversion generated from: '<S1>/VtInjCorrCylAd_old'
   *
   * Block description for '<S1>/AdaptiveCorrectionMgm':
   *  This stateflow manage adaptive learning.
   *  Learning is activate for a tunable period when engine is defined in steady state. Then an average of the individual cylinder knock correction (SAKCorrInd) over the learning period is done and
   *  a fraction of the average value is stored in a table interpolated on engine speed and load. A
   *  dead band is also used in this case to avoid updating the table for too small corrections.
   */
  /* Gateway: EOA/AdaptiveCorrectionMgm */
  /* During: EOA/AdaptiveCorrectionMgm */
  /* This stateflow manage adaptive learning.
     Learning is activate for a tunable period when engine is defined in steady state. Then the average of the individual cylinder injection correction (InjCorrCyl) over the learning period is done and
     a fraction of the average value is stored in a table addressed by engine speed and load. A
     dead band is also to avoid updating the table for too small corrections.
     In case of enabling of adaptive correction, then the stateflow calculates also a normalization factor (InCorrCylAdNorm) for nominal correction calculated by CombBal software component. */
  /* Entry Internal: EOA/AdaptiveCorrectionMgm */
  /* Transition: '<S4>:2' */
  /*  Assign old value to each chart's output to optimize code  */
  rtb_Product1 = CylBalIndAvgCyl;
  for (i = 0; i < 8; i++) {
    rtb_VtCntCylBalLearn_c[i] = VtCntCylBalLearn[(i)];
    rtb_VtEndCylBalLearn_g[i] = (int8_T)VtEndCylBalLearn[(i)];
    rtb_VtCylBalLearnState_f[i] = (int8_T)VtCylBalLearnState[(i)];
    rtb_VtCylBalIndSum_m[i] = VtCylBalIndSum[(i)];
    rtb_VtCylBalTrigAdat_p[i] = (int8_T)VtCylBalTrigAdat[(i)];
    rtb_VtInjCorrCylAd_p[i] = VtInjCorrCylAd[(i)];
  }

  if (((int32_T)ENCYLBALADCORR) == 1) {
    /* Outputs for Function Call SubSystem: '<S1>/Interpolate_TbInjCorrCylAd'
     *
     * Block description for '<S1>/Interpolate_TbInjCorrCylAd':
     *  This block is used to inteporlate ee table TbInjCorrCylAd.
     */
    /* Transition: '<S4>:218' */
    /* Transition: '<S4>:221' */
    /* Event: '<S4>:228' */
    Comb_Interpolate_TbInjCorrCylAd(IdxRpmCylBal, RtRpmCylBal, IdxLoadCylBal,
      RtLoadCylBal, IonAbsTdcEOA, &Interpolate_TbInjCorrCylAd_b);

    /* End of Outputs for SubSystem: '<S1>/Interpolate_TbInjCorrCylAd' */
    rtb_Memory = Interpolate_TbInjCorrCylAd_b;
    rtb_VtInjCorrCylAd_p[IonAbsTdcEOA] = Interpolate_TbInjCorrCylAd_b;

    /* Transition: '<S4>:226' */
  } else {
    /* Transition: '<S4>:225' */
    rtb_Memory = 32768U;
    rtb_VtInjCorrCylAd_p[IonAbsTdcEOA] = 32768U;
  }

  /* Transition: '<S4>:227' */
  if (rtb_EnableAdpCorr) {
    /* Transition: '<S4>:6' */
    /* Transition: '<S4>:8' */
    /* Adaptive correction enabled  */
    if (((int32_T)rtb_stab_rpm_state) == 1) {
      /* Transition: '<S4>:10' */
      /* Transition: '<S4>:13' */
      if (rtb_VtEndCylBalLearn[IonAbsTdcEOA] != 0) {
        /* Transition: '<S4>:49' */
        /* Transition: '<S4>:51' */
        rtb_VtCylBalLearnState_f[IonAbsTdcEOA] = 0;
        rtb_VtCntCylBalLearn_c[IonAbsTdcEOA] = 0U;
        rtb_VtEndCylBalLearn_g[IonAbsTdcEOA] = 0;

        /* Transition: '<S4>:53' */
        /* Transition: '<S4>:61' */
      } else {
        /* Transition: '<S4>:44' */
        rtb_VtCntCylBalLearn_c[IonAbsTdcEOA] = (uint16_T)((int32_T)(((int32_T)
          rtb_VtCntCylBalLearn[IonAbsTdcEOA]) + 1));
        if (rtb_VtCntCylBalLearn_c[IonAbsTdcEOA] >= CYLBALLEARNDUR) {
          /* Transition: '<S4>:56' */
          /* Transition: '<S4>:58' */
          rtb_VtEndCylBalLearn_g[IonAbsTdcEOA] = 1;

          /* Transition: '<S4>:61' */
        } else {
          /* Transition: '<S4>:60' */
          rtb_VtEndCylBalLearn_g[IonAbsTdcEOA] = 0;
          rtb_VtCylBalLearnState_f[IonAbsTdcEOA] = 1;
        }
      }

      /* Transition: '<S4>:62' */
      /* Transition: '<S4>:45' */
      /* Transition: '<S4>:40' */
    } else {
      /* Transition: '<S4>:16' */
      /* Transition: '<S4>:211' */
      for (rtb_stab_load_state = 0U; rtb_stab_load_state < N_CYLINDER;
           rtb_stab_load_state = (uint8_T)((int32_T)(((int32_T)
              rtb_stab_load_state) + 1))) {
        /* Transition: '<S4>:21' */
        /* Transition: '<S4>:25' */
        rtb_VtCylBalLearnState_f[rtb_stab_load_state] = 0;
        rtb_VtCntCylBalLearn_c[rtb_stab_load_state] = 0U;
        rtb_VtEndCylBalLearn_g[rtb_stab_load_state] = 0;

        /* Transition: '<S4>:27' */
        /* Transition: '<S4>:28' */
      }

      /* Transition: '<S4>:29' */
    }

    /* Transition: '<S4>:64' */
    /*  Calculate correction  */
    if (rtb_VtCylBalLearnState_f[IonAbsTdcEOA] == 1) {
      /* Outputs for Function Call SubSystem: '<S4>/CALCULATE_CORRECTION.calculateAvg'
       *
       * Block description for '<S4>/CALCULATE_CORRECTION.calculateAvg':
       *  This block calculates the average value of nominal injection
       *  correction (about cylinder balancing strategy).
       */
      /* Sum: '<S9>/Add' incorporates:
       *  Inport: '<Root>/InjCorrCyl'
       */
      /* Transition: '<S4>:67' */
      /* Transition: '<S4>:69' */
      /* Simulink Function 'calculateAvg': '<S4>:84' */
      Add = rtb_VtCylBalIndSum_m[IonAbsTdcEOA] + ((uint32_T)InjCorrCyl);

      /* End of Outputs for SubSystem: '<S4>/CALCULATE_CORRECTION.calculateAvg' */
      rtb_VtCylBalIndSum_m[IonAbsTdcEOA] = Add;

      /* Outputs for Function Call SubSystem: '<S4>/CALCULATE_CORRECTION.calculateAvg'
       *
       * Block description for '<S4>/CALCULATE_CORRECTION.calculateAvg':
       *  This block calculates the average value of nominal injection
       *  correction (about cylinder balancing strategy).
       */
      rtb_Product1 = (uint16_T)(Add / ((uint32_T)CYLBALLEARNDUR));

      /* End of Outputs for SubSystem: '<S4>/CALCULATE_CORRECTION.calculateAvg' */
      /* Transition: '<S4>:76' */
    } else {
      /* Transition: '<S4>:72' */
      rtb_VtCylBalIndSum_m[IonAbsTdcEOA] = 0U;
      rtb_Product1 = 0U;
    }

    /* Transition: '<S4>:92' */
    if (rtb_VtEndCylBalLearn_g[IonAbsTdcEOA] == 1) {
      /* Transition: '<S4>:96' */
      /* Transition: '<S4>:98' */
      i = 32768 - ((int32_T)rtb_Product1);
      if ((i > ((int32_T)THCYLBALCORRINDAD)) || (i < (-((int32_T)
             THCYLBALCORRINDAD)))) {
        /* Transition: '<S4>:100' */
        /* Transition: '<S4>:102' */
        rtb_VtCylBalTrigAdat_p[IonAbsTdcEOA] = 1;

        /* Transition: '<S4>:108' */
      } else {
        /* Transition: '<S4>:104' */
        rtb_VtCylBalTrigAdat_p[IonAbsTdcEOA] = 0;
      }

      /* Transition: '<S4>:109' */
    } else {
      /* Transition: '<S4>:107' */
      rtb_VtCylBalTrigAdat_p[IonAbsTdcEOA] = 0;
    }

    /* Transition: '<S4>:110' */
    /*  Assign adaptive correction to EE table  */
    /* Transition: '<S4>:130' */
    rtb_stab_load_state = (uint8_T)((int32_T)((((int32_T)((uint8_T)
      BKRPMCYLBAL_dim)) + 1) * ((int32_T)IonAbsTdcEOA)));
    if (rtb_VtCylBalTrigAdat_p[IonAbsTdcEOA] == 1) {
      /* Transition: '<S4>:134' */
      /* Transition: '<S4>:138' */
      k = 1U;
      indr = (uint8_T)(IdxRpmCylBal + ((uint16_T)rtb_stab_load_state));
      Interpolate_TbInjCorrCylAd_b = (uint16_T)((65536U - ((uint32_T)RtRpmCylBal))
        >> ((uint32_T)6));

      /* Outputs for Function Call SubSystem: '<S1>/EvalAdCorr'
       *
       * Block description for '<S1>/EvalAdCorr':
       *  This block calculates the right correction term for a single cell of
       *  adaptive table and updates it.
       */
      /* Event: '<S4>:197' */
      CombAdp_EvalAdCorr(rtb_Product1, (uint16_T)((65536U - ((uint32_T)
        RtLoadCylBal)) >> ((uint32_T)6)), Interpolate_TbInjCorrCylAd_b, indr,
                         (uint8_T)IdxLoadCylBal, RtRpmCylBal, RtLoadCylBal);

      /* End of Outputs for SubSystem: '<S1>/EvalAdCorr' */
      if (IdxLoadCylBal < ((uint16_T)((uint8_T)BKLOADCYLBAL_dim))) {
        /* Transition: '<S4>:159' */
        /* Transition: '<S4>:161' */
        k = 2U;

        /* Outputs for Function Call SubSystem: '<S1>/EvalAdCorr'
         *
         * Block description for '<S1>/EvalAdCorr':
         *  This block calculates the right correction term for a single cell of
         *  adaptive table and updates it.
         */
        /*  indr=  IdxRpmCylBal+ofsr; */
        /*  eta= (1-RtRpmCylBal);  */
        /* Event: '<S4>:197' */
        CombAdp_EvalAdCorr(rtb_Product1, (uint16_T)(((uint32_T)RtLoadCylBal) >>
          ((uint32_T)6)), Interpolate_TbInjCorrCylAd_b, indr, (uint8_T)((int32_T)
          (((int32_T)IdxLoadCylBal) + 1)), RtRpmCylBal, RtLoadCylBal);

        /* End of Outputs for SubSystem: '<S1>/EvalAdCorr' */
        /* Transition: '<S4>:164' */
      } else {
        /* Transition: '<S4>:163' */
      }

      /* Transition: '<S4>:166' */
      if (IdxRpmCylBal < ((uint16_T)((uint8_T)BKRPMCYLBAL_dim))) {
        /* Transition: '<S4>:171' */
        /* Transition: '<S4>:175' */
        k = (uint8_T)((int32_T)(((int32_T)k) + 1));
        indr = (uint8_T)((int32_T)((((int32_T)IdxRpmCylBal) + ((int32_T)
          rtb_stab_load_state)) + 1));
        Interpolate_TbInjCorrCylAd_b = (uint16_T)(((uint32_T)RtRpmCylBal) >>
          ((uint32_T)6));

        /* Outputs for Function Call SubSystem: '<S1>/EvalAdCorr'
         *
         * Block description for '<S1>/EvalAdCorr':
         *  This block calculates the right correction term for a single cell of
         *  adaptive table and updates it.
         */
        /* Event: '<S4>:197' */
        CombAdp_EvalAdCorr(rtb_Product1, (uint16_T)((65536U - ((uint32_T)
          RtLoadCylBal)) >> ((uint32_T)6)), Interpolate_TbInjCorrCylAd_b, indr,
                           (uint8_T)IdxLoadCylBal, RtRpmCylBal, RtLoadCylBal);

        /* End of Outputs for SubSystem: '<S1>/EvalAdCorr' */
        if (((int32_T)k) == 3) {
          /* Outputs for Function Call SubSystem: '<S1>/EvalAdCorr'
           *
           * Block description for '<S1>/EvalAdCorr':
           *  This block calculates the right correction term for a single cell of
           *  adaptive table and updates it.
           */
          /* Transition: '<S4>:180' */
          /* Transition: '<S4>:184' */
          /* indr=IdxRpmCylBal+1+ofsr; */
          /*  eta=RtRpmCylBal; */
          /* Event: '<S4>:197' */
          CombAdp_EvalAdCorr(rtb_Product1, (uint16_T)(((uint32_T)RtLoadCylBal) >>
            ((uint32_T)6)), Interpolate_TbInjCorrCylAd_b, indr, (uint8_T)
                             ((int32_T)(((int32_T)IdxLoadCylBal) + 1)),
                             RtRpmCylBal, RtLoadCylBal);

          /* End of Outputs for SubSystem: '<S1>/EvalAdCorr' */
          /* Transition: '<S4>:185' */
        } else {
          /* Transition: '<S4>:182' */
        }

        /* Transition: '<S4>:186' */
      } else {
        /* Transition: '<S4>:173' */
      }

      /* Transition: '<S4>:178' */
    } else {
      /* Transition: '<S4>:177' */
    }

    /* Transition: '<S4>:187' */
    /* Transition: '<S4>:127' */
  } else {
    /* Transition: '<S4>:120' */
  }

  /* Transition: '<S4>:128' */
  if (rtb_VtCylBalTrigAdat_p[IonAbsTdcEOA] == 1) {
    /* Outputs for Function Call SubSystem: '<S1>/Interpolate_TbInjCorrCylAd'
     *
     * Block description for '<S1>/Interpolate_TbInjCorrCylAd':
     *  This block is used to inteporlate ee table TbInjCorrCylAd.
     */
    /* Transition: '<S4>:231' */
    /* Transition: '<S4>:233' */
    /* Event: '<S4>:228' */
    Comb_Interpolate_TbInjCorrCylAd(IdxRpmCylBal, RtRpmCylBal, IdxLoadCylBal,
      RtLoadCylBal, IonAbsTdcEOA, &Interpolate_TbInjCorrCylAd_b);

    /* End of Outputs for SubSystem: '<S1>/Interpolate_TbInjCorrCylAd' */
    /* Transition: '<S4>:236' */
  } else {
    /* Transition: '<S4>:235' */
    Interpolate_TbInjCorrCylAd_b = 32768U;
  }

  /* End of Chart: '<S1>/AdaptiveCorrectionMgm' */

  /* SignalConversion generated from: '<S1>/CylBalIndAvgCyl' */
  /* Transition: '<S4>:238' */
  CylBalIndAvgCyl = rtb_Product1;

  /* SignalConversion generated from: '<S1>/FStabLoadCylBalAd' */
  FStabLoadCylBalAd = rtb_SigStab_o1_n;

  /* SignalConversion generated from: '<S1>/FStabRpmCylBalAd' */
  FStabRpmCylBalAd = rtb_SigStab_o1;

  /* SignalConversion generated from: '<S1>/FlgSteadyStateAd' */
  FlgSteadyStateAd = rtb_stab_rpm_state;

  /* SignalConversion generated from: '<S1>/InjCorrCylAd' */
  InjCorrCylAd = rtb_Memory;

  /* SignalConversion generated from: '<S1>/InjCorrCylAdNorm' */
  InjCorrCylAdNorm = Interpolate_TbInjCorrCylAd_b;
  for (i = 0; i < 8; i++) {
    /* SignalConversion generated from: '<S1>/VtCntCylBalLearn' */
    VtCntCylBalLearn[(i)] = rtb_VtCntCylBalLearn_c[i];

    /* SignalConversion generated from: '<S1>/VtCylBalIndSum' */
    VtCylBalIndSum[(i)] = rtb_VtCylBalIndSum_m[i];

    /* SignalConversion generated from: '<S1>/VtCylBalLearnState' */
    VtCylBalLearnState[(i)] = (uint8_T)rtb_VtCylBalLearnState_f[i];

    /* SignalConversion generated from: '<S1>/VtCylBalTrigAdat' */
    VtCylBalTrigAdat[(i)] = (uint8_T)rtb_VtCylBalTrigAdat_p[i];

    /* SignalConversion generated from: '<S1>/VtEndCylBalLearn' */
    VtEndCylBalLearn[(i)] = (uint8_T)rtb_VtEndCylBalLearn_g[i];

    /* SignalConversion generated from: '<S1>/VtInjCorrCylAd' */
    VtInjCorrCylAd[(i)] = rtb_VtInjCorrCylAd_p[i];
  }

  /* SignalConversion generated from: '<S1>/stab_load_stable' */
  stab_load_state = rtb_SigStab_o2_k;

  /* SignalConversion generated from: '<S1>/stab_rpm_state' */
  stab_rpm_state = rtb_SigStab_o2;

  /* Update for Memory: '<S18>/Memory1' */
  rpmStabSignal = rtb_SigStab_o3;

  /* Update for Memory: '<S18>/Memory' */
  rpmStabTimer = rtb_SigStab_o4;

  /* Update for Memory: '<S19>/Memory1' */
  loadStabSignal = rtb_SigStab_o3_k;

  /* Update for Memory: '<S19>/Memory' */
  loadStabTimer = rtb_SigStab_o4_n;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombAdp_EOA' */
}

/* Model step function */
void CombAdp_NoSync(void)
{
  /* Outputs for Function Call SubSystem: '<Root>/Reset'
   *
   * Block description for '<Root>/Reset':
   *  This subsytem reset model output at power-on event and at no_sync
   *  event.
   */
  /* RootInportFunctionCallGenerator generated from: '<Root>/CombAdp_NoSync' */
  CombAdp_Reset();

  /* End of Outputs for SubSystem: '<Root>/Reset' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombAdp_NoSync' */
}

/* Model step function */
void CombAdp_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/CombAdp_PowerOn' incorporates:
   *  SubSystem: '<Root>/Reset'
   *
   * Block description for '<Root>/Reset':
   *  This subsytem reset model output at power-on event and at no_sync
   *  event.
   */
  CombAdp_Reset();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombAdp_PowerOn' */
}

/* Model initialize function */
void CombAdp_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint16_T InjCorrCylAd;
uint16_T InjCorrCylAdNorm;
uint8_T VtCylBalTrigAdat[N_CYL_MAX];
uint16_T TbInjCorrAdEE[480];
void CombAdp_Stub(void)
{
  InjCorrCylAd = 32768u;               /* 1 lsb 2^-15 */
  InjCorrCylAdNorm = 32768u;           /* 1 lsb 2^-15 */
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    VtCylBalTrigAdat[idx] = 0;
  }

  for (idx=0;idx<480;idx++) {
    TbInjCorrAdEE[idx] = 32768u;       /* 1 LSB 2^-15 */
  }
}

void CombAdp_EOA(void)
{
  CombAdp_Stub();
}

void CombAdp_PowerOn(void)
{
  CombAdp_Stub();
}

void CombAdp_NoSync(void)
{
  CombAdp_Stub();
}

#endif                                 /* _BUILD_COMBADP_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/