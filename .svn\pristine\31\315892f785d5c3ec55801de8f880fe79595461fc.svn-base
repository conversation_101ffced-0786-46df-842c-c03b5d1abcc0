/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      LivenessMgm.h
 **  Date:          13-Dec-2021
 **
 **  Model Version: 1.119
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_LivenessMgm_h_
#define RTW_HEADER_LivenessMgm_h_
#ifndef LivenessMgm_COMMON_INCLUDES_
# define LivenessMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* LivenessMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void LivenessMgm_initialize(void);

/* Exported entry point function */
extern void LivenessMgm_PowerOn(void);

/* Exported entry point function */
extern void LivenessMgm_T5ms(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern boolean_T LevelLiveness;        /* '<S2>/Merge1' */

/* Liveness logical level */
extern uint8_T LevelLivenessFbk;       /* '<S2>/Merge' */

/* Status of Liveness feedback */
extern uint8_T LivenessDuty;           /* '<S2>/Merge3' */

/* Liveness duty */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S7>/Data Type Duplicate' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'LivenessMgm'
 * '<S1>'   : 'LivenessMgm/SetupData'
 * '<S2>'   : 'LivenessMgm/Subsystem'
 * '<S3>'   : 'LivenessMgm/T5ms'
 * '<S4>'   : 'LivenessMgm/T5ms/DiagMachine'
 * '<S5>'   : 'LivenessMgm/T5ms/Dio_WriteChannel'
 * '<S6>'   : 'LivenessMgm/T5ms/LevelLiveness_mgm'
 * '<S7>'   : 'LivenessMgm/T5ms/DiagMachine/SetDiagState'
 */

/*-
 * Requirements for '<Root>': LivenessMgm
 */
#endif                                 /* RTW_HEADER_LivenessMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/