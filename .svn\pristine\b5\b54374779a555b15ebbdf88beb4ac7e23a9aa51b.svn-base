% Importa i file dall'oscilloscopio
clc
clear all

dir_in=     '2dmat\';
dir_out=    'csv\';
mtfield = {'RearCurrent','RearStroke','FrontStroke','AccRearBody','FrontCurrent','AccFrontBody','IMUAccY','YawRate','RollRate','IMUAccX','RPM','Throttle','FrontWheelSpeed','RearWheelSpeed','FrontBrakePressure','RearBrakePressure','IMUAccZ','FrontPWM','RearPWM','FrontSetCurrent','RearSetCurrent';...
    '2','4','4','4','2','4','10','10','10','10','20','20','10','10','10','10','10','2','2','4','4'}; % Sample times of signals
flgsinglefiles = 1;

files=      dir([dir_in,'\*.mat']);
nfiles=     length(files);
h = waitbar(0,'Please wait...');

for indf=1:nfiles  % Analizza tutti i file nella directory
    
    % Creates filenames
    NFileIn = files(indf).name;
    NFileOut = [dir_out,strtok(NFileIn,'.'),'.csv'];
    NFileTmp1 = [dir_out,'tmp1.txt'];
    NFileTmp2 = [dir_out,'tmp2.txt'];
    
    disp(['Loading ',NFileIn,'...']);
    a = load([dir_in NFileIn]); % Load .mat input file
    afi = fieldnames(a);
    lafi = length(afi);
    M = zeros(100000,lafi); % Initializes the matrix of signals
    fid1 = fopen(NFileTmp1, 'w'); % Open temporary file with signals names.
    
    % Analyzes all signals in the structure and fills in the matrix M
    for indc = 1:lafi
        disp(['Processing signal ',afi{indc}]);
        fprintf(fid1, '%s', [afi{indc},',']);   % Writes the signals names
        td2 = 1000/eval(['a.',afi{indc},'.sampling']);  % Sample times in ms of 2d recordings
        myfield = eval(['a.',afi{indc},'.data']);   % Signal indc-th samples
        ttot = td2*(length(myfield)-1); % Total time of recording of signal indc-th
        vtd2 = 0:td2:ttot;  % Vector time (2d) of signal indc-th
        indf = cellfind(mtfield(1,:),afi(indc));    % Finds the index of signal indc-th in mtfield
        if indf~=-1
            disp(['    Acknowledged signal ',afi{indc}])
            tfield = str2double(mtfield{2,indf});
            vtfield = 0:tfield:ttot;    % Vector time (original) of signal indc-th
            myfield = interp1(vtd2,myfield,vtfield);    % Reinterpolation of signal indc-th
            M(1,indc) = tfield; % First row with the sampling times
            M(2:length(myfield)+1,indc) = myfield;  % Writes reinterpolated samples of signal indc-th
            if flgsinglefiles == 1
                NFileSingle = [dir_out,afi{indc},'.csv'];
                disp(['Writing single file ',NFileSingle])
                S = zeros(length(vtfield),2);
                S(:,1) = vtfield;
                S(:,2) = myfield;
                csvwrite(NFileSingle,S);  % writes single files
            end
        end
        h = waitbar((indc-1)/lafi);
    end
    disp(['Writing complete file ',NFileOut])
    csvwrite(NFileTmp2,M);  % writes the whole matrix M in one shot
    fprintf(fid1, '\n');    % Terminates the signals names row
    fclose('all'); % Closes temporary file with signals names.
    dos(['copy ',NFileTmp1,'+',NFileTmp2,' ',NFileOut]);    % Inserts the signals names row at the beginning of the output file.
    h = waitbar(1);
    
    % Removes temporary files
    dos(['del ',NFileTmp1]);
    dos(['del ',NFileTmp2]);
    close(h);
end