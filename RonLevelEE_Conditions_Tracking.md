# RonLevelEE Conditions Tracking

This document tracks all conditions and locations where the variable `<PERSON><PERSON><PERSON><PERSON><PERSON>` is set or modified in the codebase.

## Summary

`<PERSON><PERSON>evelEE` is a critical variable that represents the RON (Research Octane Number) level stored in EEPROM. It is modified in multiple locations under various conditions throughout the codebase.

## Variable Declaration and Initialization

### 1. EEPROM Declaration
**File:** `tree\EEPCOM\RonDetectEst_eep.c`
**Line:** 51
```c
uint8_T RonLevelEE = ((uint8_T)0U);    /* '<S9>/Merge25' */
```
**Condition:** Initial declaration with default value of 0

### 2. Stub Function Initialization
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Line:** 1688
```c
void RonDetectEst_Stub(void)
{
  RonLevelEE = 0u;
  // ... other initializations
}
```
**Condition:** Stub function sets <PERSON><PERSON><PERSON><PERSON><PERSON> to 0

## Main Assignment Conditions in RonDetectEst.c

### 3. 100ms Function - Delay State Assignment
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_100ms()`
**Lines:** 327
```c
RonLevelEE = RonDetectEst_DW.Delay_DSTATE;
```
**Condition:** Assigns the delayed state value to RonLevelEE

### 4. 5ms Function - Forced Level or Cross Level
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_5ms()`
**Lines:** 354-359
```c
if (FORCEDRONLEVEL <= ((uint8_T)MAX_RONLEVEL_EXP)) {
    RonLevelEE = FORCEDRONLEVEL;
} else {
    RonLevelEE = RonLevelCross;
}
```
**Conditions:**
- If `FORCEDRONLEVEL <= MAX_RONLEVEL_EXP`: Set to `FORCEDRONLEVEL`
- Otherwise: Set to `RonLevelCross`

### 5. Fast Detection - Direct Assignment
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_Fast()`
**Line:** 881
```c
RonLevelEE = (uint8_T)u1_0;
```
**Condition:** Direct assignment during fast detection when certain conditions are met

### 6. Fast Detection - Maximum Value Selection
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_Fast()`
**Lines:** 910-917
```c
idx = 0U;
RonLevelEE = (uint8_T)u1_0;
while (idx <= nsamp) {
    u1_1 = VtRonLevel[VtRonTAir_tmp_tmp - ((int32_T)idx)];
    if (RonLevelEE <= u1_1) {
        RonLevelEE = u1_1;
    }
    idx = (uint8_T)((int32_T)(((int32_T)idx) + 1));
}
```
**Condition:** Iterates through samples and sets RonLevelEE to the maximum value found

### 7. Fast Detection - Forced Level Override
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_Fast()`
**Lines:** 931-935
```c
if (FORCEDRONLEVEL <= ((uint8_T)MAX_RONLEVEL_EXP)) {
    RonLevelEE = FORCEDRONLEVEL;
}
```
**Condition:** If `FORCEDRONLEVEL <= MAX_RONLEVEL_EXP`: Override with `FORCEDRONLEVEL`

### 8. Recovery Function
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_Recovery()`
**Line:** 1248
```c
RonLevelEE = RONLEVELREC;
```
**Condition:** Sets RonLevelEE to recovery value `RONLEVELREC`

### 9. Slow Detection - Decrease RON Level
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_Slow()`
**Lines:** 1460-1466
```c
if (RonLevelEE > ((uint8_T)MIN_RONLEVEL)) {
    RonLevelEE = (uint8_T)((int32_T)(((int32_T)RonLevelEE) - 1));
    VtDRonLevelSusp[(IDDRonLevelSusp)] = -1;
    // Reset counters and arrays
}
```
**Conditions:**
- `CntRonSuspNeg >= VTRONSUSPNEGD[(IDSuspPos)]`
- `CntRonSuspRunEE >= RONSUSPRUN`
- `RonLevelEE > MIN_RONLEVEL`
**Action:** Decrements RonLevelEE by 1

### 10. Slow Detection - Increase RON Level
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_Slow()`
**Lines:** 1544
```c
RonLevelEE = (uint8_T)((int32_T)(((int32_T)RonLevelEE) + 1));
```
**Conditions:**
- `CntRonSuspPos >= VTRONSUSPPOSD[(IDSuspPos)]`
- `CntRonSuspRunEE >= RONSUSPRUN`
- `RonLevelEE < MAX_RONLEVEL`
**Action:** Increments RonLevelEE by 1

### 11. Slow Detection - Forced Level Override
**File:** `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
**Function:** `RonDetectEst_Slow()`
**Lines:** 1624-1627
```c
if (FORCEDRONLEVEL <= ((uint8_T)MAX_RONLEVEL_EXP)) {
    RonLevelEE = FORCEDRONLEVEL;
}
```
**Condition:** If `FORCEDRONLEVEL <= MAX_RONLEVEL_EXP`: Override with `FORCEDRONLEVEL`

## Related Variable Assignments in Other Files

### 12. RonDetectFuel - Local Variable Assignment
**File:** `tree\APPLICATION\RONDETECTFUEL\RonDetectFuel.c`
**Note:** This file contains assignments to `RonDetectFuel_DW.RonLevelEE_g` (local variable), not the global `RonLevelEE`

### 13. RonDetectCross - Stub Function
**File:** `tree\APPLICATION\RONDETECTCROSS\RonDetectCross.c`
**Line:** 1009
```c
RonLevelCross = RonLevelEE;  // Uses RonLevelEE as input, doesn't modify it
```

## Key Constants and Thresholds

- `FORCEDRONLEVEL`: Forced RON level value
- `MAX_RONLEVEL_EXP`: Maximum expected RON level
- `MIN_RONLEVEL`: Minimum RON level
- `MAX_RONLEVEL`: Maximum RON level
- `RONLEVELREC`: Recovery RON level value
- `RONSUSPRUN`: Suspension run threshold
- `VTRONSUSPNEGD[]`: Negative suspension thresholds array
- `VTRONSUSPPOSD[]`: Positive suspension thresholds array

## Function Call Hierarchy

1. **RonDetectEst_100ms()**: Called every 100ms for periodic updates
2. **RonDetectEst_5ms()**: Called every 5ms for frequent updates
3. **RonDetectEst_Fast()**: Called for fast detection algorithms
4. **RonDetectEst_Slow()**: Called for slow detection algorithms
5. **RonDetectEst_Recovery()**: Called during recovery scenarios
6. **RonDetectEst_Stub()**: Called in stub/test mode

## Detailed Condition Analysis

### Priority of Assignments

The `RonLevelEE` variable follows a specific priority hierarchy:

1. **Highest Priority - Forced Level**: `FORCEDRONLEVEL` overrides all other values when valid
2. **Recovery Mode**: `RONLEVELREC` is used during recovery operations
3. **Adaptive Detection**: Slow detection increments/decrements based on knock detection
4. **Fast Detection**: Uses maximum value selection from sample history
5. **Cross-Check**: Uses `RonLevelCross` when forced level is invalid
6. **Default**: Initialized to 0

### Conditional Logic Flow

#### Fast Detection Logic (RonDetectEst_Fast)
```
IF (specific conditions met for direct assignment)
    RonLevelEE = calculated_value
ELSE IF (CntRonRun < RONSAMPLES)
    // Use limited sample size
    RonLevelEE = max_value_from_samples
ELSE
    // Use full sample size
    RonLevelEE = max_value_from_full_history

// Always check for forced override
IF (FORCEDRONLEVEL <= MAX_RONLEVEL_EXP)
    RonLevelEE = FORCEDRONLEVEL
```

#### Slow Detection Logic (RonDetectEst_Slow)
```
// Decrease condition
IF (CntRonSuspNeg >= threshold AND CntRonSuspRunEE >= RONSUSPRUN AND RonLevelEE > MIN_RONLEVEL)
    RonLevelEE = RonLevelEE - 1
    Reset_counters_and_arrays()

// Increase condition
IF (CntRonSuspPos >= threshold AND CntRonSuspRunEE >= RONSUSPRUN AND RonLevelEE < MAX_RONLEVEL)
    RonLevelEE = RonLevelEE + 1
    Reset_counters_and_arrays()

// Always check for forced override
IF (FORCEDRONLEVEL <= MAX_RONLEVEL_EXP)
    RonLevelEE = FORCEDRONLEVEL
```

### State Dependencies

The `RonLevelEE` assignments depend on several state variables:
- `CntRonSuspNeg`: Negative suspension counter
- `CntRonSuspPos`: Positive suspension counter
- `CntRonSuspRunEE`: Suspension run counter
- `CntRonRun`: Run counter
- `VtRonLevel[]`: RON level history array
- `IDSuspPos`: Suspension position index

### EEPROM Storage

`RonLevelEE` is stored in EEPROM (ID8) and persists across power cycles. The variable is part of the ELD_EE_INTERFACE storage class, indicating it's critical for engine operation continuity.

## Testing and Validation

### Stub Mode Behavior
In stub/test mode, `RonLevelEE` is always set to 0, providing a known baseline for testing.

### Cross-Component Integration
- **RonDetectSA**: Reads `RonLevelEE` for spark advance calculations
- **RonDetectCross**: Uses `RonLevelEE` for cross-bank validation
- **RonDetectFuel**: Maintains local copy for fuel-related RON detection

## Critical Safety Considerations

1. **Bounds Checking**: All assignments respect MIN_RONLEVEL and MAX_RONLEVEL limits
2. **Forced Override**: FORCEDRONLEVEL provides emergency override capability
3. **Recovery Mode**: RONLEVELREC ensures safe operation during fault recovery
4. **Counter Reset**: Counters are reset when RON level changes to prevent oscillation

## Summary Table of All RonLevelEE Assignment Conditions

| Function | Line | Condition | Assignment | Priority |
|----------|------|-----------|------------|----------|
| EEPROM Init | 51 | System initialization | `RonLevelEE = 0U` | Lowest |
| Stub | 1688 | Test/stub mode | `RonLevelEE = 0u` | Test only |
| 100ms | 327 | Periodic update | `RonLevelEE = Delay_DSTATE` | Medium |
| 5ms | 356 | Forced level valid | `RonLevelEE = FORCEDRONLEVEL` | Highest |
| 5ms | 358 | Forced level invalid | `RonLevelEE = RonLevelCross` | Medium |
| Fast | 881 | Direct assignment | `RonLevelEE = calculated_value` | Medium |
| Fast | 910-917 | Max from samples | `RonLevelEE = max(samples)` | Medium |
| Fast | 935 | Forced override | `RonLevelEE = FORCEDRONLEVEL` | Highest |
| Recovery | 1248 | Recovery mode | `RonLevelEE = RONLEVELREC` | High |
| Slow | 1466 | Decrease condition | `RonLevelEE = RonLevelEE - 1` | Medium |
| Slow | 1544 | Increase condition | `RonLevelEE = RonLevelEE + 1` | Medium |
| Slow | 1627 | Forced override | `RonLevelEE = FORCEDRONLEVEL` | Highest |

## Condition Dependencies Summary

### Decrease Conditions (Slow Detection)
- `CntRonSuspNeg >= VTRONSUSPNEGD[IDSuspPos]`
- `CntRonSuspRunEE >= RONSUSPRUN`
- `RonLevelEE > MIN_RONLEVEL`

### Increase Conditions (Slow Detection)
- `CntRonSuspPos >= VTRONSUSPPOSD[IDSuspPos]`
- `CntRonSuspRunEE >= RONSUSPRUN`
- `RonLevelEE < MAX_RONLEVEL`

### Override Conditions (All Functions)
- `FORCEDRONLEVEL <= MAX_RONLEVEL_EXP`

### Sample-Based Conditions (Fast Detection)
- Sample history availability in `VtRonLevel[]`
- `CntRonRun` vs `RONSAMPLES` comparison
- Maximum value selection algorithm

## File Locations Summary

1. **Primary Implementation**: `tree\APPLICATION\RONDETECTEST\RonDetectEst.c`
2. **EEPROM Declaration**: `tree\EEPCOM\RonDetectEst_eep.c`
3. **Header Declaration**: `tree\APPLICATION\COMMON\RonDetectEst_out.h`
4. **Cross-Reference Usage**: `tree\APPLICATION\RONDETECTCROSS\RonDetectCross.c`
5. **Fuel Detection Usage**: `tree\APPLICATION\RONDETECTFUEL\RonDetectFuel.c`
6. **Spark Advance Usage**: `tree\APPLICATION\RONDETECTSA\RonDetectSA.c`
