/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_PIT.h
**  Created on      :  07-Feb-2022 12:22:00
**  Original author :  Mocci A
******************************************************************************/

#ifndef SAFETYMNGR_PIT_H
#define SAFETYMNGR_PIT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_PIT_out.h"
#include "SafetyMngr_CommLib_out.h"
#include "timing_out.h"
#include "pit_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_PIT_RegCheck
**
**   Description:
**    This function reads PIT registers and compares them with expected ones.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
static void SafetyMngr_PIT_RegCheck(void);


#endif // SAFETYMNGR_PIT_H

/****************************************************************************
 ****************************************************************************/
