// MULTI Server Script
// Freescale MPC55xx demo board setup script for MPServ

// reset
target rst

// Whether or not to check for VLE
$vle_check = 1

// Whether or not to check for 16-bit port size on external SRAM
$bus_check = 1

// Whether or not to enable cache
$cache_enable = 0

// Which chip select to use for external SRAM
// This is determined by the SRAM_SEL jumper
// 1-2 closed: CS0  (default)
// 2-3 closed: CS1
$chip_select = 0

// The starting address for the external 512K SRAM
// Starting at 0x3ff80000 makes it adjacent to the internal 64K, making
// for one contiguous 576K block.
$baseAddr = 0x3ff80000
substitute target bAddr = %EVAL{$baseAddr}

// Initialize vector pointers to something that exists
// Currently, use internal SRAM
target reg ivpr 0x40000000
target reg ivor0 0x10
target reg ivor1 0x20
target reg ivor2 0x30
target reg ivor3 0x40
target reg ivor4 0x50
target reg ivor5 0x60
target reg ivor6 0x70
target reg ivor7 0x80
target reg ivor8 0x90
target reg ivor9 0xa0
target reg ivor10 0xb0
target reg ivor11 0xc0
target reg ivor12 0xd0
target reg ivor13 0xe0
target reg ivor14 0xf0
target reg ivor15 0x100
target reg ivor32 0x110
target reg ivor33 0x120
target reg ivor34 0x130

// Fill exception vectors with branch to self
target mf 0x40000000i 0x200 0x48000000

// Setup the TLB to map the internal 64K SRAM
target tlbw 3 0x40000000 0x120003f 0x40000000 0x1

// Setup the TLB to map the external 512K SRAM
target tlbw 4 0x$$bAddr 0x128003f 0x$$bAddr 0x1


// Initializes the external bus
// Configure external SRAM

// Enable flash configuration registers
target tlbw 5 0xc3f80000 0x120003f 0xc3f80000 0x1

// Set up the pins
// Address bus PCR 4 - 27 43
// configure address bus pins
target mw 0xc3f90048 0x04400440
target mw 0xc3f9004c 0x04400440
target mw 0xc3f90050 0x04400440
target mw 0xc3f90054 0x04400440
target mw 0xc3f90058 0x04400440
target mw 0xc3f9005c 0x04400440
target mw 0xc3f90060 0x04400440
target mw 0xc3f90064 0x04400440
target mw 0xc3f90068 0x04400440
target mw 0xc3f9006c 0x04400440
target mw 0xc3f90070 0x04400440
target mw 0xc3f90074 0x04400440

// Data bus PCR 28-59
// configure data bus pins
target mw 0xc3f90078 0x04400440
target mw 0xc3f9007c 0x04400440
target mw 0xc3f90080 0x04400440
target mw 0xc3f90084 0x04400440
target mw 0xc3f90088 0x04400440
target mw 0xc3f9008c 0x04400440
target mw 0xc3f90090 0x04400440
target mw 0xc3f90094 0x04400440

// These next 8 are not required for 16-bit address bus.
target mw 0xc3f90098 0x04400440
target mw 0xc3f9009c 0x04400440
target mw 0xc3f900a0 0x04400440
target mw 0xc3f900a4 0x04400440
target mw 0xc3f900a8 0x04400440
target mw 0xc3f900ac 0x04400440
target mw 0xc3f900b0 0x04400440
target mw 0xc3f900b4 0x04400440

// config minimum bus control pins
// RD/WR  & BDIP PCR 62/63
target mw 0xc3f900bc 0x04400440

// WE[0-4] PCR 64-67
target mw 0xc3f900c0 0x04430443
target mw 0xc3f900c4 0x04430443

// OE & TS
target mw 0xc3f900c8 0x04430443

// configure the chip selects
// CS[0-3]
target mw 0xc3f90040 0x04430443
target mw 0xc3f90044 0x04430443

// Set up Memory Controller CS0 or CS1 for external SRAM
// See comments at top of file for which chip select to use
if ($chip_select == 0)  {
    memwrite 4 0xc3f84010 ($baseAddr+0x41)
    memwrite 4 0xc3f84014 0xfff800F0
} else {
    memwrite 4 0xc3f84018 ($baseAddr+0x41)
    memwrite 4 0xc3f8401c 0xfff800F0
}

// CLKOUT
//PCR 229
target mw 2 0xc3f9020a 0x02c0


// Enable internal flash memory
target tlbw 1 0x00000000 0x138003f 0x00000000 0x1

// Check for 16-bit port size.
// The MPC5553 demo board has ethernet, and the ethernet controller uses
// D16 to D31, leaving D0 to D16 for the external SRAM.  So for the external
// SRAM to operate correctly, it needs to be configured with a 16-bit port
// size.  We determine if this is needed by writing to memory and reading
// the value back.
if ($bus_check == 1)  {
    memwrite 4 ($baseAddr+0x20) 0xdeadbeef
    $myData = *(int*)($baseAddr+0x20)
    // Check that first half of word matches, but second half does not.
    if (($myData != 0xdeadbeef) && (($myData & 0xffff0000) == 0xdead0000))  {
	print "Configuring External SRAM with 16-bit port size."

	// Set the PS bit in EBI_BR0
	memwrite 4 0xc3f84010 ($baseAddr+0x841)
    }
}

// The next few lines enable the unified L1 cache.
if ($cache_enable == 1)  {
    target rw l1csr0 0x3
}

// Check for VLE.
// If the .vletext exists, we will assume that the entire program is built
// in VLE mode, and set both internal and external SRAMs accordingly.
// If different/additional pages need to have VLE enabled, this is where
// to do it.
if ($vle_check == 1)  {
    if ($M_sec_exists(".vletext"))  {
    
	print "Configuring TLB pages for VLE."
	
	// Turn on VLE bit for internal SRAM
	target tlbw 3 0x40000000 0x320003f 0x40000000 0x01
    
	// Fill exception vectors with VLE branch to self
	target mf 0x40000000 0x200 0xe800e800
    
	// Turn on VLE bit for external SRAM
	target tlbw 4 0x$$bAddr 0x328003f 0x$$bAddr 0x1
    
    }
}
