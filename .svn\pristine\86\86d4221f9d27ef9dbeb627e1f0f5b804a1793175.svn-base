/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           SyncMgm.c
 **  File Creation Date: 17-May-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         SyncMgm
 **  Model Description:  The target of this module is to calculate the engine speed, set the status of the synchronization and perform three types of diagnosis related to the quality of the sensor signal.
 **  Model Version:      1.163
 **  Model Author:       PanettaM - Fri Oct 01 12:01:59 2021
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: MarottaR - Tue May 17 08:35:19 2022
 **
 **  Last Saved Modification:  MarottaR - Tue May 17 08:33:35 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "SyncMgm_out.h"
#include "SyncMgm_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_SYNCMGM_DEF             1163U                     /* Referenced by: '<S11>/Constant10' */

/* ID model version define */
#define INIT_CNTABSTDC                 0U                        /* Referenced by: '<S10>/Constant4' */

/* Init value for the tdc counter */
#define MAX_ULONG                      4294967295U               /* Referenced by: '<S4>/Constant2' */

/* Maximum unsigned longword value */
#define ONE                            1U                        /* Referenced by:
                                                                  * '<S1>/AbsTdc_mgm'
                                                                  * '<S2>/DiagSync_LL_mgm'
                                                                  * '<S4>/Constant6'
                                                                  * '<S5>/RpmDiagnosis_mgm'
                                                                  */

/* Define for value 1. */
#define ZERO                           0U                        /* Referenced by:
                                                                  * '<S3>/DiagSync_SA_mgm'
                                                                  * '<S5>/RpmDiagnosis_mgm'
                                                                  * '<S10>/Constant13'
                                                                  * '<S10>/Constant8'
                                                                  */

/* Define for 0 value */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_SYNCMGM_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint8_T tdc_angle_idx;          /* '<S1>/AbsTdc_mgm' */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T DECRESYNCSAOUT = 0U;
                                      /* Referenced by: '<S3>/DECRESYNCSAOUT' */

/* Counter increment for SA out of range */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENENDSTFLGBYRPM = 1;/* Referenced by: '<S9>/Constant12' */

/* Enable calculation of the flag EndStartFlg by engine speed evaluation */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENRESYNCSAOUT = 1;/* Referenced by: '<S3>/ENRESYNCSAOUT' */

/* Resync for SA out of range enabled */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENRPMCANNOSYNC = 1;/* Referenced by: '<S10>/Constant6' */

/* Enable use of RpmCAN to initialize engine speed signal after a loss of synchronization. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T INCRESYNCSAOUT = 1U;
                                      /* Referenced by: '<S3>/INCRESYNCSAOUT' */

/* Counter increment for SA out of range */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T KRPMF = 1457U;/* Referenced by: '<S9>/Constant7' */

/* Engine Speed Filtering coefficent */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXCNTNOSYNC = 20U;/* Referenced by:
                                                              * '<S2>/Constant'
                                                              * '<S3>/MAXCNTNOSYNC'
                                                              */

/* Max value for CntExNoSync for diagnosis */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T MAXCNTRESYNCSAOUT = 7U;
                                   /* Referenced by: '<S3>/MAXCNTRESYNCSAOUT' */

/* Max value for CntForceResyncSAout for diagnosis */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T MINCNTPHASEDTDC = 4U;/* Referenced by: '<S4>/Constant3' */

/* Minimum number of TDC events occurred for each cylinder to set related FlgAbsTdc */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T RPMCANERROR = 1000U;/* Referenced by: '<S5>/Constant2' */

/* Rpm threshold for abs(RpmCAN-Rpm) error diagnosis */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T RPMENGSTART = 600U;/* Referenced by: '<S9>/Constant16' */

/* Rpm threshold for engine start flag */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T SYNCHIGNOREVDRPMCAN = 1;/* Referenced by:
                                                                    * '<S5>/Constant1'
                                                                    * '<S10>/Constant7'
                                                                    */

/* Ignore VDRpmCAN in case RpmCAN is in use due to StSync != SYNCH */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T THRCNTFAULTCAMEDGE = 10U;/* Referenced by: '<S5>/Constant' */

/* Threshold for engine speed no signal counter over that the diagnosis is called. */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T AbsTdc;                        /* '<S8>/Merge17' */

/* Tdc absolute index */
uint32_T CntAbsTdc;                    /* '<S8>/Merge16' */

/* Free running TDC counter */
uint8_T CntForceResyncSAout;           /* '<S8>/Merge15' */

/* Counter for SAout resync strategy */
uint16_T CntNoSyncNOsts;               /* '<S8>/Merge5' */

/* NoSync Counter not due to start and stop strategy */
boolean_T EndStartFlg;                 /* '<S8>/Merge3' */

/* End of Start flag */
uint8_T FlgAbsTdc[8];                  /* '<S8>/Merge2' */

/* Flag to indicate that AbsTdc has been recognized since the phase recognized */
uint8_T FlgSyncPhased;                 /* '<S8>/Merge' */

/* Synchronization & Phased flag */
uint16_T Rpm;                          /* '<S8>/Merge6' */

/* Engine speed */
uint16_T RpmF;                         /* '<S8>/Merge8' */

/* Filtered Rpm */
enum_StSync StSync;                    /* '<S8>/Merge1' */

/* Synchronization State */
enum_Sync_Engsts Sync_Engsts;          /* '<S8>/Merge11' */

/* Start & Stop Status old value */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint8_T CntFaultCamEdge;/* '<S8>/Merge13' */

/* Engine speed fault counter */
STATIC_TEST_POINT boolean_T FlgDisDiagSync;/* '<S8>/Merge10' */

/* Flag to disable CntNoSyncNOsts increment */
STATIC_TEST_POINT uint32_T IdVer_SyncMgm;/* '<S8>/Merge4' */

/* ID model version */
STATIC_TEST_POINT enum_PtFault PtFaultRpm;/* '<S8>/Merge14' */

/* Punctual engine speed fault  */
STATIC_TEST_POINT enum_PtFault PtFaultSync;/* '<S8>/Merge12' */

/* Punctual losso of synchronization fault  */
STATIC_TEST_POINT boolean_T RpmCanInUse;/* '<S8>/Merge7' */

/* Rpm is retrieved from CAN signal */
STATIC_TEST_POINT uint32_T RpmFilter;  /* '<S8>/Merge9' */

/* Engine speed filtered at high resolution */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<Root>/setupData'
 * Block description for: '<Root>/setupData'
 *   Initialization of the main variables.
 */
void SyncMgm_setupData(void)
{
  int32_T i;

  /* SignalConversion generated from: '<S10>/FlgAbsTdc' */
  for (i = 0; i < 8; i++) {
    FlgAbsTdc[(i)] = 0U;
  }

  /* End of SignalConversion generated from: '<S10>/FlgAbsTdc' */

  /* SignalConversion generated from: '<S10>/StSync' incorporates:
   *  Constant: '<S10>/Constant10'
   */
  StSync = NO_SYNCH;

  /* SignalConversion generated from: '<S10>/FlgSyncPhased' incorporates:
   *  Constant: '<S10>/Constant1'
   */
  FlgSyncPhased = 0U;

  /* Logic: '<S10>/Logical Operator1' incorporates:
   *  Constant: '<S10>/Constant13'
   *  Constant: '<S10>/Constant6'
   *  Constant: '<S10>/Constant7'
   *  Constant: '<S10>/Constant8'
   *  Inport: '<Root>/KeySignal'
   *  Inport: '<Root>/VDRpmCAN'
   *  Logic: '<S10>/Logical Operator'
   *  Logic: '<S10>/Logical Operator2'
   *  RelationalOperator: '<S10>/Relational Operator'
   *  RelationalOperator: '<S10>/Relational Operator1'
   */
  RpmCanInUse = (((ENRPMCANNOSYNC) && (KeySignal > ((uint8_T)ZERO))) &&
                 ((VDRpmCAN > ((uint8_T)ZERO)) || (SYNCHIGNOREVDRPMCAN)));

  /* Switch: '<S10>/Switch' incorporates:
   *  Constant: '<S10>/Constant9'
   *  Inport: '<Root>/RpmCAN'
   */
  if (RpmCanInUse) {
    RpmF = RpmCAN;
  } else {
    RpmF = 0U;
  }

  /* End of Switch: '<S10>/Switch' */

  /* DataTypeConversion: '<S10>/Data Type Conversion1' */
  RpmFilter = (((uint32_T)RpmF) << ((uint32_T)14));

  /* SignalConversion generated from: '<S10>/Rpm' */
  Rpm = RpmF;

  /* SignalConversion generated from: '<S10>/FlgDisDiagSync' incorporates:
   *  Constant: '<S10>/Constant14'
   */
  FlgDisDiagSync = false;

  /* SignalConversion generated from: '<S10>/AbsTdc' incorporates:
   *  Constant: '<S10>/Constant5'
   */
  AbsTdc = 0U;

  /* SignalConversion generated from: '<S10>/CntAbsTdc' incorporates:
   *  Constant: '<S10>/Constant4'
   */
  CntAbsTdc = INIT_CNTABSTDC;

  /* DataTypeConversion: '<S10>/Data Type Conversion3' incorporates:
   *  Inport: '<Root>/EngstsCAN'
   */
  Sync_Engsts = EngstsCAN;
}

/*
 * Output and update for function-call system: '<Root>/setup_scheduler'
 * Block description for: '<Root>/setup_scheduler'
 *   This block manages the initialization of the variables.
 *   In detail, when the function call SYNCMGM_Init is invoked the blocks setupData and setupDiagnosisData are called,
 *   instead, when the SYNCMGM_ResetVars is invoked only the setupData block is executed.
 */
void SyncMgm_setup_scheduler(int32_T controlPortIdx)
{
  int8_T rtb_call_Init[2];
  int32_T i;

  /* Outputs for Function Call SubSystem: '<Root>/setup_scheduler' incorporates:
   *  TriggerPort: '<S12>/call_Init'
   *
   * Block description for '<Root>/setup_scheduler':
   *  This block manages the initialization of the variables.
   *  In detail, when the function call SYNCMGM_Init is invoked the blocks setupData and setupDiagnosisData are called,
   *  instead, when the SYNCMGM_ResetVars is invoked only the setupData block is executed.
   */
  for (i = 0; i < 2; i++) {
    rtb_call_Init[i] = 0;
  }

  rtb_call_Init[controlPortIdx] = 2;

  /* Outputs for Enabled SubSystem: '<S12>/Init_split' incorporates:
   *  EnablePort: '<S27>/SYNCMGM_Init'
   *
   * Block description for '<S12>/Init_split':
   *  When the function call SYNCMGM_Init is invoked the blocks setupData
   *  and setupDiagnosisData are called.
   */
  /* RelationalOperator: '<S12>/Relational Operator' incorporates:
   *  Constant: '<S12>/Constant'
   */
  if (rtb_call_Init[0] > 0) {
    /* S-Function (fcncallgen): '<S27>/call_SYNCMGM_Init' incorporates:
     *  SubSystem: '<Root>/setupData'
     *
     * Block description for '<Root>/setupData':
     *  Initialization of the main variables.
     *
     * Block requirements for '<Root>/setupData':
     *  1. EISB_FCA6CYL_SW_REQ_2555: The software shall set the variable StSync at NO_SYNCH and the fla... (ECU_SW_Requirements#9990)
     *  2. EISB_FCA6CYL_SW_REQ_2567: The software shall calculate on temporal event the engine speed si... (ECU_SW_Requirements#9993)
     */
    SyncMgm_setupData();

    /* S-Function (fcncallgen): '<S27>/call_SYNCMGM_Init' incorporates:
     *  SubSystem: '<Root>/setupDiagnosisData'
     *
     * Block description for '<Root>/setupDiagnosisData':
     *  Initialization of the variables for the diagnosis functions.
     */
    /* SignalConversion generated from: '<S11>/IdVer_SyncMgm' incorporates:
     *  Constant: '<S11>/Constant10'
     */
    IdVer_SyncMgm = ID_VER_SYNCMGM_DEF;

    /* SignalConversion generated from: '<S11>/CntNoSyncNOsts' incorporates:
     *  Constant: '<S11>/Constant'
     */
    CntNoSyncNOsts = 0U;

    /* SignalConversion generated from: '<S11>/PtFaultSync' incorporates:
     *  Constant: '<S11>/Constant1'
     *  Constant: '<S11>/Constant3'
     *  SignalConversion generated from: '<S11>/PtFaultRpm'
     */
    PtFaultSync = NO_PT_FAULT;

    /* SignalConversion generated from: '<S11>/CntFaultCamEdge' incorporates:
     *  Constant: '<S11>/Constant2'
     */
    CntFaultCamEdge = 0U;

    /* SignalConversion generated from: '<S11>/PtFaultRpm' */
    PtFaultRpm = PtFaultSync;

    /* SignalConversion generated from: '<S11>/CntForceResyncSAout' incorporates:
     *  Constant: '<S11>/Constant4'
     */
    CntForceResyncSAout = 0U;

    /* SignalConversion generated from: '<S11>/EndStartFlg' incorporates:
     *  Constant: '<S11>/Constant5'
     */
    EndStartFlg = false;

    /* End of Outputs for S-Function (fcncallgen): '<S27>/call_SYNCMGM_Init' */
  }

  /* End of RelationalOperator: '<S12>/Relational Operator' */
  /* End of Outputs for SubSystem: '<S12>/Init_split' */

  /* Outputs for Enabled SubSystem: '<S12>/resetVars' incorporates:
   *  EnablePort: '<S28>/SYNCMGM_ResetVars'
   *
   * Block description for '<S12>/resetVars':
   *  When the function call SYNCMGM_ReSync is invoked only the block
   *  setupData is executed.
   */
  /* RelationalOperator: '<S12>/Relational Operator1' incorporates:
   *  Constant: '<S12>/Constant1'
   */
  if (rtb_call_Init[1] > 0) {
    /* Outputs for Function Call SubSystem: '<Root>/setupData'
     *
     * Block description for '<Root>/setupData':
     *  Initialization of the main variables.
     *
     * Block requirements for '<Root>/setupData':
     *  1. EISB_FCA6CYL_SW_REQ_2555: The software shall set the variable StSync at NO_SYNCH and the fla... (ECU_SW_Requirements#9990)
     *  2. EISB_FCA6CYL_SW_REQ_2567: The software shall calculate on temporal event the engine speed si... (ECU_SW_Requirements#9993)
     */
    /* S-Function (fcncallgen): '<S28>/call_SYNCMGM_ResetVars' */
    SyncMgm_setupData();

    /* End of Outputs for S-Function (fcncallgen): '<S28>/call_SYNCMGM_ResetVars' */
    /* End of Outputs for SubSystem: '<Root>/setupData' */
  }

  /* End of RelationalOperator: '<S12>/Relational Operator1' */
  /* End of Outputs for SubSystem: '<S12>/resetVars' */
}

/* Model step function */
void SYNCMGM_AbsTdcMgm(void)
{
  int32_T tmp;
  int32_T tmp_0;
  int32_T tmp_1;
  int32_T exitg1;

  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_AbsTdcMgm' incorporates:
   *  SubSystem: '<Root>/AbsTdcMgm'
   *
   * Block description for '<Root>/AbsTdcMgm':
   *  Calculation of the current cylinder: variable AbsTdc.
   *
   * Block requirements for '<Root>/AbsTdcMgm':
   *  1. EISB_FCA6CYL_SW_REQ_2558: The software shall calculate the current cylinder (variable AbsTdc... (ECU_SW_Requirements#9992)
   */
  /* Chart: '<S1>/AbsTdc_mgm' */
  /* Gateway: AbsTdcMgm/AbsTdc_mgm */
  /* During: AbsTdcMgm/AbsTdc_mgm */
  /* Calculate the AbsTdc as the index of the TDC_ANGLE table column (row is fixed by the bank in use) pointing the element, which defines with the next one (considering the table circularity, so that the next element of the last one is the first one) an angles range containing the current engine position (considering also the circularity of the engine positions on 720 degrees).  */
  /* Entry Internal: AbsTdcMgm/AbsTdc_mgm */
  /* Transition: '<S13>:2' */
  tdc_angle_idx = 0U;
  do {
    exitg1 = 0;
    if (tdc_angle_idx < ((uint8_T)(((uint16_T)N_CYLINDER) - ((uint16_T)ONE)))) {
      /* Transition: '<S13>:4' */
      tmp = (int32_T)FlgBankSel;
      tmp_0 = ((int32_T)((uint32_T)(((uint32_T)tdc_angle_idx) << ((uint32_T)1))))
        + tmp;
      tmp_1 = ((int32_T)tdc_angle_idx) + ((int32_T)((uint16_T)ONE));
      tmp += tmp_1 * 2;
      if (((TdcAngle >= TDC_ANGLE[(tmp_0)]) && (TdcAngle < TDC_ANGLE[(tmp)])) ||
          ((TDC_ANGLE[(tmp_0)] >= TDC_ANGLE[(tmp)]) && ((TdcAngle >= TDC_ANGLE
             [(tmp_0)]) || (TdcAngle < TDC_ANGLE[(tmp)])))) {
        /* Transition: '<S13>:6' */
        /* Transition: '<S13>:12' */
        exitg1 = 1;
      } else {
        /* Transition: '<S13>:8' */
        tdc_angle_idx = (uint8_T)tmp_1;
      }
    } else {
      /* Transition: '<S13>:10' */
      exitg1 = 1;
    }
  } while (exitg1 == 0);

  /* SignalConversion generated from: '<S1>/AbsTdc' incorporates:
   *  Chart: '<S1>/AbsTdc_mgm'
   */
  /* Transition: '<S13>:14' */
  AbsTdc = tdc_angle_idx;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_AbsTdcMgm' */
}

/* Model step function */
void SYNCMGM_DiagSync_LL(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState_j;
  uint8_T rtb_DataTypeConversion_d;

  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_DiagSync_LL' incorporates:
   *  SubSystem: '<Root>/DiagSync_LL'
   *
   * Block description for '<Root>/DiagSync_LL':
   *  Diagnosis of signal frequency incorrect.
   *
   * Block requirements for '<Root>/DiagSync_LL':
   *  1. EISB_FCA6CYL_SW_REQ_2541: The test for diagnosis line DIAG_SYNC with symptom SIGNAL_FREQUENC... (ECU_SW_Requirements#9597)
   */
  /* Merge: '<S8>/Merge12' incorporates:
   *  Chart: '<S2>/DiagSync_LL_mgm'
   *  SignalConversion generated from: '<S2>/PtFaultSync'
   */
  /* Gateway: DiagSync_LL/DiagSync_LL_mgm */
  /* During: DiagSync_LL/DiagSync_LL_mgm */
  /* If there are at least unexpected MAXCNTNOSYNC loss of synchronization events when engine is running, the diagnosis is called. */
  /* Entry Internal: DiagSync_LL/DiagSync_LL_mgm */
  /* Transition: '<S15>:2' */
  PtFaultSync = NO_PT_FAULT;

  /* Chart: '<S2>/DiagSync_LL_mgm' incorporates:
   *  Constant: '<S2>/Constant'
   *  Inport: '<Root>/EDriveSts'
   *  Inport: '<Root>/StDiag'
   *  SignalConversion generated from: '<S2>/CntNoSyncNOsts_old'
   */
  if (((((uint32_T)StDiag[(DIAG_SYNC)]) != FAULT) && (!FlgDisDiagSync)) &&
      (((uint32_T)EDriveSts) == OFF)) {
    /* Transition: '<S15>:4' */
    if ((CntNoSyncNOsts + ((uint16_T)ONE)) < MAXCNTNOSYNC) {
      /* Transition: '<S15>:6' */
      CntNoSyncNOsts = (uint16_T)(CntNoSyncNOsts + ((uint16_T)ONE));
    } else {
      /* Transition: '<S15>:9' */
      /*  Set diagnosis */
      CntNoSyncNOsts = MAXCNTNOSYNC;

      /* Merge: '<S8>/Merge12' incorporates:
       *  SignalConversion generated from: '<S2>/PtFaultSync'
       */
      PtFaultSync = SIGNAL_FREQUENCY_INCORRECT;

      /* Outputs for Function Call SubSystem: '<S2>/DiagMgm_SetDiagState'
       *
       * Block description for '<S2>/DiagMgm_SetDiagState':
       *  Call the diagnostic machine
       */
      /* DataTypeConversion: '<S14>/Data Type Conversion' */
      /* Event: '<S15>:20' */
      rtb_DataTypeConversion_d = SIGNAL_FREQUENCY_INCORRECT;

      /* S-Function (DiagMgm_SetDiagState): '<S16>/DiagMgm_SetDiagState' incorporates:
       *  Constant: '<S14>/Constant1'
       */
      DiagMgm_SetDiagState( DIAG_SYNC, rtb_DataTypeConversion_d,
                           &rtb_DiagMgm_SetDiagState_j);

      /* End of Outputs for SubSystem: '<S2>/DiagMgm_SetDiagState' */
    }

    /* Transition: '<S15>:23' */
  } else {
    /* Transition: '<S15>:10' */
  }

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_DiagSync_LL' */
}

/* Model step function */
void SYNCMGM_DiagSync_SA(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState_n;
  uint8_T rtb_DataTypeConversion_k;
  int32_T tmp;
  enum_PtFault tmp_0;

  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_DiagSync_SA' incorporates:
   *  SubSystem: '<Root>/DiagSync_SA'
   *
   * Block description for '<Root>/DiagSync_SA':
   *  Diagnosis of resync for spark advance out of range.
   *
   * Block requirements for '<Root>/DiagSync_SA':
   *  1. EISB_FCA6CYL_SW_REQ_2547: The test for diagnosis line DIAG_SYNC with symptom SIGNAL_INVALID ... (ECU_SW_Requirements#9598)
   */
  /* Merge: '<S8>/Merge12' incorporates:
   *  Chart: '<S3>/DiagSync_SA_mgm'
   *  SignalConversion generated from: '<S3>/PtFaultSync'
   */
  /* Gateway: DiagSync_SA/DiagSync_SA_mgm */
  /* During: DiagSync_SA/DiagSync_SA_mgm */
  /* This diagnosis is called when the re-sync for is performed due to the spark adavnce not correctly actuated. */
  /* Entry Internal: DiagSync_SA/DiagSync_SA_mgm */
  /* Transition: '<S19>:2' */
  PtFaultSync = NO_PT_FAULT;

  /* Chart: '<S3>/DiagSync_SA_mgm' incorporates:
   *  Constant: '<S3>/DECRESYNCSAOUT'
   *  Constant: '<S3>/ENRESYNCSAOUT'
   *  Constant: '<S3>/INCRESYNCSAOUT'
   *  Constant: '<S3>/MAXCNTNOSYNC'
   *  Constant: '<S3>/MAXCNTRESYNCSAOUT'
   *  Inport: '<Root>/PtFault'
   *  Inport: '<Root>/StDiag'
   *  Inport: '<Root>/TestStatus'
   *  SignalConversion generated from: '<S3>/CntForceResyncSAout_old'
   */
  if (ENRESYNCSAOUT) {
    /* Transition: '<S19>:24' */
    if (((uint32_T)StDiag[(DIAG_SYNC)]) != FAULT) {
      /* Transition: '<S19>:26' */
      tmp = ((int32_T)CntForceResyncSAout) + ((int32_T)INCRESYNCSAOUT);
      if (tmp < ((int32_T)MAXCNTRESYNCSAOUT)) {
        /* Transition: '<S19>:28' */
        CntForceResyncSAout = (uint8_T)tmp;
      } else {
        /* Merge: '<S8>/Merge12' incorporates:
         *  SignalConversion generated from: '<S3>/PtFaultSync'
         */
        /* Transition: '<S19>:30' */
        PtFaultSync = SIGNAL_INVALID;
        CntForceResyncSAout = MAXCNTRESYNCSAOUT;

        /* Outputs for Function Call SubSystem: '<S3>/DiagMgm_SetDiagState'
         *
         * Block description for '<S3>/DiagMgm_SetDiagState':
         *  Call the diagnostic machine
         */
        /* DataTypeConversion: '<S18>/Data Type Conversion' */
        /* Event: '<S19>:20' */
        rtb_DataTypeConversion_k = SIGNAL_INVALID;

        /* S-Function (DiagMgm_SetDiagState): '<S21>/DiagMgm_SetDiagState' incorporates:
         *  Constant: '<S18>/Constant1'
         */
        DiagMgm_SetDiagState( DIAG_SYNC, rtb_DataTypeConversion_k,
                             &rtb_DiagMgm_SetDiagState_n);

        /* End of Outputs for SubSystem: '<S3>/DiagMgm_SetDiagState' */
      }

      /* Transition: '<S19>:52' */
      /* Transition: '<S19>:50' */
      /* Transition: '<S19>:51' */
    } else {
      /* Transition: '<S19>:36' */
      if ((DECRESYNCSAOUT > ((uint8_T)ZERO)) && (CntForceResyncSAout >
           DECRESYNCSAOUT)) {
        /* Transition: '<S19>:39' */
        CntForceResyncSAout = (uint8_T)(CntForceResyncSAout - DECRESYNCSAOUT);

        /* Transition: '<S19>:51' */
      } else {
        /* Transition: '<S19>:41' */
        if (DECRESYNCSAOUT > ((uint8_T)ZERO)) {
          /* Transition: '<S19>:44' */
          /* Transition: '<S19>:10' */
          CntForceResyncSAout = ((uint8_T)ZERO);
        } else {
          /* Transition: '<S19>:45' */
          /* Transition: '<S19>:51' */
        }
      }
    }
  } else {
    /* Transition: '<S19>:54' */
    /* Transition: '<S19>:10' */
    CntForceResyncSAout = ((uint8_T)ZERO);
  }

  /* Transition: '<S19>:56' */
  tmp_0 = PtFault[(DIAG_SYNC)];
  if (((CntForceResyncSAout == ((uint8_T)ZERO)) && (((uint32_T)StDiag[(DIAG_SYNC)])
        == FAULT)) && (((uint32_T)tmp_0) == SIGNAL_INVALID)) {
    /* Outputs for Function Call SubSystem: '<S3>/DiagMgm_ResetOneDiag'
     *
     * Block description for '<S3>/DiagMgm_ResetOneDiag':
     *  Reset the diagnostic machine
     */
    /* S-Function (DiagMgm_ResetOneDiag): '<S20>/DiagMgm_ResetOneDiag' incorporates:
     *  Constant: '<S17>/Constant1'
     */
    /* Transition: '<S19>:59' */
    /* Event: '<S19>:69' */
    DiagMgm_ResetOneDiag( DIAG_SYNC);

    /* End of Outputs for SubSystem: '<S3>/DiagMgm_ResetOneDiag' */
    /* Transition: '<S19>:63' */
  } else {
    /* Transition: '<S19>:61' */
    if (((((uint32_T)TestStatus[(DIAG_SYNC)]) != DIAG_TEST_FINISHED) &&
         (CntNoSyncNOsts < MAXCNTNOSYNC)) && (((uint32_T)tmp_0) == NO_PT_FAULT))
    {
      /* Merge: '<S8>/Merge12' incorporates:
       *  SignalConversion generated from: '<S3>/PtFaultSync'
       */
      /* Transition: '<S19>:64' */
      PtFaultSync = NO_PT_FAULT;

      /* Outputs for Function Call SubSystem: '<S3>/DiagMgm_SetDiagState'
       *
       * Block description for '<S3>/DiagMgm_SetDiagState':
       *  Call the diagnostic machine
       */
      /* DataTypeConversion: '<S18>/Data Type Conversion' */
      /* Event: '<S19>:20' */
      rtb_DataTypeConversion_k = NO_PT_FAULT;

      /* S-Function (DiagMgm_SetDiagState): '<S21>/DiagMgm_SetDiagState' incorporates:
       *  Constant: '<S18>/Constant1'
       */
      DiagMgm_SetDiagState( DIAG_SYNC, rtb_DataTypeConversion_k,
                           &rtb_DiagMgm_SetDiagState_n);

      /* End of Outputs for SubSystem: '<S3>/DiagMgm_SetDiagState' */
    } else {
      /* Transition: '<S19>:66' */
    }
  }

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_DiagSync_SA' */
}

/* Model step function */
void SYNCMGM_FlgAbsTdc(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_FlgAbsTdc' incorporates:
   *  SubSystem: '<Root>/FlgAbsTdc_calc'
   *
   * Block description for '<Root>/FlgAbsTdc_calc':
   *  Increasing of the TDC counter CntAbsTdc and set of the flag FlgAbsTdc,
   *  which is performed on current AbsTdc cylinder when a number of
   *  MINCNTPHASEDTDC TDC events is seen on this (this means that
   *  CntAbsTdc>(MINCNTPHASEDTDC-1)*N_CYLINDER).
   *
   * Block requirements for '<Root>/FlgAbsTdc_calc':
   *  1. EISB_FCA6CYL_SW_REQ_2557: Each angular event the software shall increase the counter CntAbsT... (ECU_SW_Requirements#9991)
   */
  /* Switch: '<S4>/Switch2' incorporates:
   *  Constant: '<S4>/Constant2'
   *  Constant: '<S4>/Constant4'
   *  RelationalOperator: '<S4>/Relational Operator2'
   *  SignalConversion generated from: '<S4>/CntAbsTdc_old'
   *  Sum: '<S4>/Add'
   */
  if (CntAbsTdc < MAX_ULONG) {
    CntAbsTdc = CntAbsTdc + 1U;
  }

  /* End of Switch: '<S4>/Switch2' */

  /* Switch: '<S4>/Switch1' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Constant: '<S4>/Constant3'
   *  Constant: '<S4>/Constant6'
   *  DataTypeConversion: '<S4>/Data Type Conversion'
   *  Product: '<S4>/Divide'
   *  RelationalOperator: '<S4>/Relational Operator1'
   *  Sum: '<S4>/Sum'
   */
  if (CntAbsTdc > (((uint32_T)((uint8_T)(((uint16_T)MINCNTPHASEDTDC) -
          ((uint16_T)ONE)))) * ((uint32_T)N_CYLINDER))) {
    /* Assignment: '<S4>/Assignment' incorporates:
     *  Constant: '<S4>/Constant5'
     */
    FlgAbsTdc[(AbsTdc)] = 1U;
  }

  /* End of Switch: '<S4>/Switch1' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_FlgAbsTdc' */
}

/* Model step function */
void SYNCMGM_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_Init' incorporates:
   *  SubSystem: '<Root>/setup_scheduler'
   *
   * Block description for '<Root>/setup_scheduler':
   *  This block manages the initialization of the variables.
   *  In detail, when the function call SYNCMGM_Init is invoked the blocks setupData and setupDiagnosisData are called,
   *  instead, when the SYNCMGM_ResetVars is invoked only the setupData block is executed.
   */
  SyncMgm_setup_scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_Init' */
}

/* Model step function */
void SYNCMGM_ResetVars(void)
{
  /* Outputs for Function Call SubSystem: '<Root>/setup_scheduler'
   *
   * Block description for '<Root>/setup_scheduler':
   *  This block manages the initialization of the variables.
   *  In detail, when the function call SYNCMGM_Init is invoked the blocks setupData and setupDiagnosisData are called,
   *  instead, when the SYNCMGM_ResetVars is invoked only the setupData block is executed.
   */
  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_ResetVars' */
  SyncMgm_setup_scheduler(1);

  /* End of Outputs for SubSystem: '<Root>/setup_scheduler' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_ResetVars' */
}

/* Model step function */
void SYNCMGM_RpmDiagnosis(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState;
  uint8_T rtb_DataTypeConversion;
  int32_T u;

  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_RpmDiagnosis' incorporates:
   *  SubSystem: '<Root>/RpmDiagnosis'
   *
   * Block description for '<Root>/RpmDiagnosis':
   *  Diagnosis for invalid engine speed signal.
   */
  /* Gateway: RpmDiagnosis/RpmDiagnosis_mgm */
  /* During: RpmDiagnosis/RpmDiagnosis_mgm */
  /* In case of engine speed calculated (Rpm_Calc) is 0, but engine is running, or is not close to the engine speed from CAN, than the diagnosis is called with sympthom NO_SIGNAL in the first case or INVALID_SIGNAL in the second case. */
  /* Entry Internal: RpmDiagnosis/RpmDiagnosis_mgm */
  /* Transition: '<S23>:2' */
  PtFaultRpm = NO_PT_FAULT;

  /* Inport: '<Root>/RpmCalc' */
  if (RpmCalc == ((uint16_T)((uint8_T)ZERO))) {
    /* SignalConversion generated from: '<S5>/CntFaultCamEdge_old' incorporates:
     *  Constant: '<S5>/Constant'
     */
    /* Transition: '<S23>:4' */
    if (CntFaultCamEdge < THRCNTFAULTCAMEDGE) {
      /* Transition: '<S23>:6' */
      CntFaultCamEdge = (uint8_T)(((uint16_T)CntFaultCamEdge) + ((uint16_T)ONE));
    } else {
      /* Transition: '<S23>:7' */
    }

    /* End of SignalConversion generated from: '<S5>/CntFaultCamEdge_old' */
    /* Transition: '<S23>:26' */
  } else {
    /* Transition: '<S23>:10' */
    CntFaultCamEdge = ((uint8_T)ZERO);
  }

  /* Constant: '<S5>/Constant' */
  if (CntFaultCamEdge >= THRCNTFAULTCAMEDGE) {
    /* Transition: '<S23>:9'
     * Requirements for Transition: '<S23>:9':
     *  1. EISB_FCA6CYL_SW_REQ_1617: The test for diagnosis line DIAG_RPM with symptom NO_SIGNAL  shall... (ECU_SW_Requirements#2955)
     */
    PtFaultRpm = NO_SIGNAL;
  } else {
    /* Inport: '<Root>/FlgDiagRpmDis' incorporates:
     *  Constant: '<S5>/Constant1'
     *  Inport: '<Root>/VDRpmCAN'
     */
    /* Transition: '<S23>:25' */
    if ((FlgDiagRpmDis == ((uint8_T)ZERO)) && ((VDRpmCAN != ((uint8_T)ZERO)) ||
         (SYNCHIGNOREVDRPMCAN))) {
      /* Inport: '<Root>/RpmCAN' incorporates:
       *  Inport: '<Root>/RpmCalc'
       */
      /* Transition: '<S23>:31'
       * Requirements for Transition: '<S23>:31':
       *  1. EISB_FCA6CYL_SW_REQ_1487: The test for diagnosis line DIAG_RPM with symptom SIGNAL_INVALID s... (ECU_SW_Requirements#2954)
       */
      u = ((int32_T)RpmCAN) - ((int32_T)RpmCalc);
      if (u < 0) {
        u = -u;
      }

      /* Constant: '<S5>/Constant2' */
      if (u > ((int32_T)RPMCANERROR)) {
        /* Transition: '<S23>:34' */
        PtFaultRpm = SIGNAL_INVALID;
      } else {
        /* Transition: '<S23>:39' */
      }

      /* End of Constant: '<S5>/Constant2' */
      /* Transition: '<S23>:38' */
    } else {
      /* Transition: '<S23>:32' */
    }

    /* End of Inport: '<Root>/FlgDiagRpmDis' */
  }

  /* Chart: '<S5>/RpmDiagnosis_mgm' incorporates:
   *  SubSystem: '<S5>/DiagMgm_SetDiagState'
   *
   * Block description for '<S5>/DiagMgm_SetDiagState':
   *  Call the diagnostic machine
   */
  /* DataTypeConversion: '<S22>/Data Type Conversion' */
  /* Transition: '<S23>:28' */
  /* Event: '<S23>:20' */
  rtb_DataTypeConversion = (uint8_T)PtFaultRpm;

  /* S-Function (DiagMgm_SetDiagState): '<S24>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S22>/Constant1'
   */
  DiagMgm_SetDiagState( DIAG_RPM, rtb_DataTypeConversion,
                       &rtb_DiagMgm_SetDiagState);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_RpmDiagnosis' */
}

/* Model step function */
void SYNCMGM_SetFlgSyncPhased(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_SetFlgSyncPhased' incorporates:
   *  SubSystem: '<Root>/SetFlgSyncPhased'
   *
   * Block description for '<Root>/SetFlgSyncPhased':
   *  Set the variable FlgSyncPhased.
   *
   * Block requirements for '<Root>/SetFlgSyncPhased':
   *  1. EISB_FCA6CYL_SW_REQ_2556: The software shall set the flag FlgSyncPhased at true, when the phasing is recognized. (ECU_SW_Requirements#9989)
   */
  /* SignalConversion generated from: '<S6>/FlgSyncPhased' incorporates:
   *  Constant: '<S6>/Constant10'
   */
  FlgSyncPhased = 1U;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_SetFlgSyncPhased' */
}

/* Model step function */
void SYNCMGM_SetStSync(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_SetStSync' incorporates:
   *  SubSystem: '<Root>/SetStSync'
   *
   * Block description for '<Root>/SetStSync':
   *  Set the variable StSync at the status SYNCH, which means that the
   *  system is synchronized.
   *
   * Block requirements for '<Root>/SetStSync':
   *  1. EISB_FCA6CYL_SW_REQ_2554: The software shall set the variable StSync at SYNCH, when the sync... (ECU_SW_Requirements#9988)
   */
  /* SignalConversion generated from: '<S7>/StSync' incorporates:
   *  Constant: '<S7>/Constant10'
   */
  StSync = SYNCH;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_SetStSync' */
}

/* Model step function */
void SYNCMGM_T5ms(void)
{
  int16_T rtb_Conversion2;
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  uint8_T rtb_Rpm_j;

  /* RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_T5ms' incorporates:
   *  SubSystem: '<Root>/T5ms'
   *
   * Block description for '<Root>/T5ms':
   *  Calculation of the punctual engine speed from the CAN signal, if it's valid (otherwise, by the calculated engine speed), filtering of this by a FOF and evaluation of the loss of synchronization diagnosis enabling.
   *  Moreover, this block calculates the flag of the end of the engine starting.
   */
  /* Switch: '<S9>/Switch2' incorporates:
   *  Constant: '<S9>/Constant1'
   *  Constant: '<S9>/Constant2'
   *  Constant: '<S9>/Constant3'
   *  Constant: '<S9>/Constant4'
   *  Constant: '<S9>/Constant5'
   *  Inport: '<Root>/EngstsCAN'
   *  Logic: '<S9>/Logical Operator3'
   *  Logic: '<S9>/Logical Operator4'
   *  RelationalOperator: '<S9>/Relational Operator2'
   *  RelationalOperator: '<S9>/Relational Operator3'
   *  RelationalOperator: '<S9>/Relational Operator4'
   *  RelationalOperator: '<S9>/Relational Operator5'
   *  SignalConversion generated from: '<S9>/FlgDisDiagSync_old'
   *  SignalConversion generated from: '<S9>/Sync_Engsts_old'
   *
   * Block requirements for '<S9>/Switch2':
   *  1. EISB_FCA6CYL_SW_REQ_2569: The software shall calculate a flag (i.e. FlgDisDiagSync) to disab... (ECU_SW_Requirements#9995)
   */
  if ((ENGSTS_SHUTDOWN == ((uint32_T)EngstsCAN)) && (ENGSTS_ON == ((uint32_T)
        Sync_Engsts))) {
    FlgDisDiagSync = true;
  } else {
    FlgDisDiagSync = (((ENGSTS_ON == ((uint32_T)Sync_Engsts)) || (ENGSTS_ON !=
      ((uint32_T)EngstsCAN))) && (FlgDisDiagSync));
  }

  /* End of Switch: '<S9>/Switch2' */

  /* Logic: '<S9>/Logical Operator1' incorporates:
   *  Constant: '<S9>/Constant13'
   *  Constant: '<S9>/Constant6'
   *  Inport: '<Root>/RpmCAN'
   *  Inport: '<Root>/VDRpmCAN'
   *  RelationalOperator: '<S9>/Relational Operator1'
   *  RelationalOperator: '<S9>/Relational Operator6'
   */
  RpmCanInUse = ((((int32_T)RpmCAN) > 0) && (((int32_T)VDRpmCAN) > 0));

  /* Switch: '<S9>/Switch' incorporates:
   *  Inport: '<Root>/RpmCAN'
   *  Inport: '<Root>/RpmCalc'
   *
   * Block requirements for '<S9>/Switch':
   *  1. EISB_FCA6CYL_SW_REQ_2567: The software shall calculate on temporal event the engine speed si... (ECU_SW_Requirements#9993)
   */
  if (RpmCanInUse) {
    Rpm = RpmCAN;
  } else {
    Rpm = RpmCalc;
  }

  /* End of Switch: '<S9>/Switch' */

  /* Switch: '<S9>/Switch1' incorporates:
   *  Constant: '<S9>/Constant11'
   *  Constant: '<S9>/Constant12'
   *  Constant: '<S9>/Constant16'
   *  Constant: '<S9>/Constant17'
   *  Inport: '<Root>/StFunc'
   *  Logic: '<S9>/Logical Operator2'
   *  Logic: '<S9>/Logical Operator5'
   *  RelationalOperator: '<S9>/Relational Operator10'
   *  RelationalOperator: '<S9>/Relational Operator8'
   *  RelationalOperator: '<S9>/Relational Operator9'
   *  SignalConversion generated from: '<S9>/EndStartFlg_old'
   *
   * Block requirements for '<S9>/Switch1':
   *  1. EISB_FCA6CYL_SW_REQ_2571: The software shall set the flag of end of engine starting (i.e. En... (ECU_SW_Requirements#9996)
   */
  if (ENENDSTFLGBYRPM) {
    EndStartFlg = ((Rpm > RPMENGSTART) || ((EndStartFlg) && (((int32_T)Rpm) > 0)));
  } else {
    EndStartFlg = (((uint32_T)StFunc) == FUNC_ENG_RUN);
  }

  /* End of Switch: '<S9>/Switch1' */

  /* DataTypeConversion: '<S25>/Conversion2' */
  rtb_Conversion2 = (int16_T)Rpm;

  /* DataTypeConversion: '<S25>/Conversion4' incorporates:
   *  SignalConversion generated from: '<S9>/RpmFilter_old'
   */
  rtb_FOF_Reset_S16_FXP_o2 = (int32_T)RpmFilter;

  /* DataTypeConversion: '<S25>/Conversion5' */
  rtb_FOF_Reset_S16_FXP_o1 = (int16_T)Rpm;

  /* Switch: '<S9>/Switch4' incorporates:
   *  Constant: '<S9>/Constant8'
   *  RelationalOperator: '<S9>/Relational Operator7'
   */
  rtb_Rpm_j = (uint8_T)((((uint32_T)StSync) != SYNCH) ? 1 : 0);

  /* S-Function (FOF_Reset_S16_FXP): '<S25>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S9>/Constant7'
   *
   * Block requirements for '<S9>/Constant7':
   *  1. EISB_FCA6CYL_SW_REQ_2568: The software shall filter on temporal event the engine speed calcu... (ECU_SW_Requirements#9994)
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
                    rtb_FOF_Reset_S16_FXP_o1, KRPMF, rtb_Conversion2, rtb_Rpm_j,
                    rtb_FOF_Reset_S16_FXP_o2);

  /* SignalConversion generated from: '<S9>/RpmFilter' incorporates:
   *  DataTypeConversion: '<S25>/Conversion7'
   */
  RpmFilter = (uint32_T)rtb_FOF_Reset_S16_FXP_o2;

  /* SignalConversion generated from: '<S9>/RpmF' incorporates:
   *  DataTypeConversion: '<S26>/Conversion'
   */
  RpmF = (uint16_T)rtb_FOF_Reset_S16_FXP_o1;

  /* DataTypeConversion: '<S9>/Data Type Conversion2' incorporates:
   *  Inport: '<Root>/EngstsCAN'
   */
  Sync_Engsts = EngstsCAN;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/SYNCMGM_T5ms' */
}

/* Model initialize function */
void SyncMgm_initialize(void)
{
  /* SystemInitialize for Merge: '<S8>/Merge11' */
  Sync_Engsts = ENGSTS_OFF;

  /* SystemInitialize for Merge: '<S8>/Merge1' */
  StSync = NO_SYNCH;

  /* SystemInitialize for Merge: '<S8>/Merge12' incorporates:
   *  Merge: '<S8>/Merge14'
   */
  PtFaultSync = NO_PT_FAULT;

  /* SystemInitialize for Merge: '<S8>/Merge14' */
  PtFaultRpm = PtFaultSync;
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T FlgSyncPhased;
uint8_T StSync;
uint8_T FlgAbsTdc[8];
uint8_T Sync_Engsts;
uint8_T AbsTdc;
uint16_T Rpm;
uint16_T RpmF;
uint32_T CntAbsTdc;
void SyncMgm_initialize(void);
void SYNCMGM_AbsTdcMgm(void);
void SYNCMGM_DiagSync_LL(void);
void SYNCMGM_DiagSync_SA(void);
void SYNCMGM_FlgAbsTdc(void);
void SYNCMGM_Init(void);
void SYNCMGM_ResetVars(void);
void SYNCMGM_RpmDiagnosis(void);
void SYNCMGM_SetFlgSyncPhased(void);
void SYNCMGM_SetStSync(void);
void SYNCMGM_T5ms(void);
void SyncMgm_initialize(void)
{
}

void SYNCMGM_AbsTdcMgm(void)
{
}

void SYNCMGM_DiagSync_LL(void)
{
}

void SYNCMGM_DiagSync_SA(void)
{
}

void SYNCMGM_FlgAbsTdc(void)
{
}

void SYNCMGM_Init(void)
{
  uint8_T i;
  FlgSyncPhased= 0U;
  StSync= 0U;
  for (i = 0; i < 8; i++) {
    FlgAbsTdc[(i)] = 0U;
  }

  Sync_Engsts= 0U;
  AbsTdc= 0U;
  Rpm= 0U;
  RpmF= 0U;
  CntAbsTdc= 0U;
}

void SYNCMGM_ResetVars(void)
{
}

void SYNCMGM_RpmDiagnosis(void)
{
}

void SYNCMGM_SetFlgSyncPhased(void)
{
}

void SYNCMGM_SetStSync(void)
{
}

void SYNCMGM_T5ms(void)
{
}

#endif                                 /* _BUILD_SYNCMGM_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/