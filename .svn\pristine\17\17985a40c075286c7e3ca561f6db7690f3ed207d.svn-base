/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_icm.c
 * @brief   SPC5xx GTM ICM driver code.
 *
 * @addtogroup ICM
 * @{
 */

#include "gtm.h"
//#include <irq.h> MC

#if (SPC5_GTM_USE_ICM == TRUE) || defined(__DOXYGEN__)

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/**
 * @brief   ICM0 driver identifier.
 */
#if (SPC5_GTM_USE_ICM0 == TRUE) || defined(__DOXYGEN__)
GTM_ICMDriver ICMD1;
#endif

/*===========================================================================*/
/* Driver local variables and types.                                         */
/*===========================================================================*/


/*===========================================================================*/
/* Driver local functions.                                                   */
/*===========================================================================*/


/*===========================================================================*/
/* Driver interrupt handlers.                                                */
/*===========================================================================*/


/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/

/**
 * @brief   Low level GTM ICM driver initialisation.
 *
 * @init
 */
void gtm_icmInit(void) {

#if (SPC5_GTM_USE_ICM0 == TRUE)
	/* Set Register access pointer */
	ICMD1.icm = &(GTM_ICM);
#endif /* SPC5_GTM_USE_ICM0 */
}


/**
 * @brief   Get ICM event.
 *
 * @param[in] icmd       GTM ICM driver pointer
 *
 * @param[in] device     GTM Submodule identifier
 *
 * @return ICM event
 *
 * @sa
 * SPC5_GTM_ICM_ATOM0, SPC5_GTM_ICM_ATOM1, SPC5_GTM_ICM_ATOM2, SPC5_GTM_ICM_ATOM3,
 * <br>
 * SPC5_GTM_ICM_TOM0, SPC5_GTM_ICM_TOM1
 *
 * @api
 */
uint32_t gtm_icmGetEvent(GTM_ICMDriver *icmd, uint32_t device) {
	uint32_t event;

	switch(device) {
	case SPC5_GTM_ICM_TIM0:
	case SPC5_GTM_ICM_TIM1:
	case SPC5_GTM_ICM_TIM2:
	case SPC5_GTM_ICM_TIM3:
		event = (icmd->icm->IRQG_2.R & (0x000000FFUL << ((device - SPC5_GTM_ICM_TIM0) * SPC5_GTM_TIM_CHANNELS)));
		break;
#if (SPC5_HAS_GTM_IP_343 == TRUE || SPC5_HAS_GTM_IP_344 == TRUE)
	case SPC5_GTM_ICM_TIM4:
	case SPC5_GTM_ICM_TIM5:
	case SPC5_GTM_ICM_TIM6:
	case SPC5_GTM_ICM_TIM7:
		event = (icmd->icm->IRQG_3.R & (0x000000FFUL << ((device - SPC5_GTM_ICM_TIM4) * SPC5_GTM_TIM_CHANNELS)));
		break;
#endif
	case SPC5_GTM_ICM_MCS0:
	case SPC5_GTM_ICM_MCS1:
	case SPC5_GTM_ICM_MCS2:
	case SPC5_GTM_ICM_MCS3:
		event = (icmd->icm->IRQG_4.R & (0x000000FFUL << ((device - SPC5_GTM_ICM_MCS0) * SPC5_GTM_MCS_CHANNELS)));
		break;
#if (SPC5_HAS_GTM_IP_343 == TRUE || SPC5_HAS_GTM_IP_344 == TRUE)
	case SPC5_GTM_ICM_MCS4:
	case SPC5_GTM_ICM_MCS5:
	case SPC5_GTM_ICM_MCS6:
	case SPC5_GTM_ICM_MCS7:
		event = (icmd->icm->IRQG_5.R & (0x000000FFUL << ((device - SPC5_GTM_ICM_MCS4) * SPC5_GTM_MCS_CHANNELS)));
		break;
#endif
	case SPC5_GTM_ICM_TOM0:
	case SPC5_GTM_ICM_TOM1:
		event = (icmd->icm->IRQG_6.R & (0x0000FFFFUL << ((device - SPC5_GTM_ICM_TOM0) * SPC5_GTM_TOM_CHANNELS)));
		break;
#if (SPC5_HAS_GTM_IP_343 == TRUE || SPC5_HAS_GTM_IP_344 == TRUE)
	case SPC5_GTM_ICM_TOM2:
	case SPC5_GTM_ICM_TOM3:
		event = (icmd->icm->IRQG_7.R & (0x0000FFFFUL << ((device - SPC5_GTM_ICM_TOM2) * SPC5_GTM_TOM_CHANNELS)));
		break;
#endif
#if 0
	case SPC5_GTM_ICM_TOM4:
	case SPC5_GTM_ICM_TOM5:
		event = (icmd->icm->IRQG_8.R & (0x0000FFFFUL << ((device - SPC5_GTM_ICM_TOM4) * SPC5_GTM_TOM_CHANNELS)));
		break;
#endif
	case SPC5_GTM_ICM_ATOM0:
	case SPC5_GTM_ICM_ATOM1:
	case SPC5_GTM_ICM_ATOM2:
	case SPC5_GTM_ICM_ATOM3:
		event = (icmd->icm->IRQG_9.R & (0x000000FFUL << ((device - SPC5_GTM_ICM_ATOM0) * SPC5_GTM_ATOM_CHANNELS)));
		break;
#if (SPC5_HAS_GTM_IP_343 == TRUE || SPC5_HAS_GTM_IP_344 == TRUE)
	case SPC5_GTM_ICM_ATOM4:
	case SPC5_GTM_ICM_ATOM5:
	case SPC5_GTM_ICM_ATOM6:
	case SPC5_GTM_ICM_ATOM7:
		event = (icmd->icm->IRQG_10.R & (0x000000FFUL << ((device - SPC5_GTM_ICM_ATOM4) * SPC5_GTM_ATOM_CHANNELS)));
		break;
#endif
#if 0
	case SPC5_GTM_ICM_ATOM8:
	case SPC5_GTM_ICM_ATOM9:
	case SPC5_GTM_ICM_ATOM10:
	case SPC5_GTM_ICM_ATOM11:
		event = (icmd->icm->IRQG_11.R & (0x000000FFUL << ((device - SPC5_GTM_ICM_ATOM8) * SPC5_GTM_ATOM_CHANNELS)));
		break;
#endif
	default:
		event = 0;
		break;
	}

	return event;
}

#endif /* SPC5_GTM_USE_ICM */

/** @} */
