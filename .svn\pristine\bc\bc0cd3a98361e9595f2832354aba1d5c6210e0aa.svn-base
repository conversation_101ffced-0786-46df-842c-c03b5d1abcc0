/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Com.h
 **  Date:          24-Jan-2024
 **
 **  Model Version: 1.879
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Com_h_
#define RTW_HEADER_TLE9278BQX_Com_h_
#ifndef TLE9278BQX_Com_COMMON_INCLUDES_
# define TLE9278BQX_Com_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TLE9278BQX_Com_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

 
/* Enumerated types definition */
typedef uint8_T enum_StSBCSafeTest;
#define SF_NORMAL                      ((enum_StSBCSafeTest)0U)  /* Default value */
#define SF_ABNORMAL                    ((enum_StSBCSafeTest)1U)
#define SF_TRANS_EXCEPTION             ((enum_StSBCSafeTest)10U)
#define SF_SM_INTC                     ((enum_StSBCSafeTest)11U)
#define SF_SM_ADC                      ((enum_StSBCSafeTest)12U)
#define SF_SM_INIT1                    ((enum_StSBCSafeTest)13U)
#define SF_SM_INIT2                    ((enum_StSBCSafeTest)14U)
#define SF_SM_FCCU1                    ((enum_StSBCSafeTest)15U)
#define SF_SM_FCCU2                    ((enum_StSBCSafeTest)16U)
#define SF_SM_MCU1                     ((enum_StSBCSafeTest)17U)
#define SF_SM_MCU2                     ((enum_StSBCSafeTest)18U)
#define SF_SM_MCU3                     ((enum_StSBCSafeTest)19U)
#define SF_NO_COM                      ((enum_StSBCSafeTest)2U)
#define SF_SM_MCU4                     ((enum_StSBCSafeTest)20U)
#define SF_SM_MCU5                     ((enum_StSBCSafeTest)21U)
#define SF_SM_MCU6                     ((enum_StSBCSafeTest)22U)
#define SF_SM_MCU7                     ((enum_StSBCSafeTest)23U)
#define SF_SM_MCU8                     ((enum_StSBCSafeTest)24U)
#define SF_SM_PIT1                     ((enum_StSBCSafeTest)25U)
#define SF_SM_PIT2                     ((enum_StSBCSafeTest)26U)
#define SF_SM_FCCU3                    ((enum_StSBCSafeTest)27U)
#define SF_SM_FCCU4                    ((enum_StSBCSafeTest)28U)
#define SF_SM_SCLK                     ((enum_StSBCSafeTest)29U)
#define SF_WDT_EXCEPTION               ((enum_StSBCSafeTest)3U)
#define SF_SM_LCLK                     ((enum_StSBCSafeTest)30U)
#define SF_SM_FCCU5                    ((enum_StSBCSafeTest)31U)
#define SF_SM_FCCU6                    ((enum_StSBCSafeTest)32U)
#define SF_SM_FCCU7                    ((enum_StSBCSafeTest)33U)
#define SF_SM_FCCU8                    ((enum_StSBCSafeTest)34U)
#define SF_SM_FCCU9                    ((enum_StSBCSafeTest)35U)
#define SF_PARITY_EXCEPTION            ((enum_StSBCSafeTest)36U)
#define SF_SM_CMU                      ((enum_StSBCSafeTest)37U)
#define SF_WDT_EXPIRED                 ((enum_StSBCSafeTest)4U)
#define SF_EL_FAILURE                  ((enum_StSBCSafeTest)5U)
#define SF_DATA_EXCEPTION              ((enum_StSBCSafeTest)6U)
#define SF_CORE1_EXCEPTION             ((enum_StSBCSafeTest)7U)
#define SF_CORE2_EXCEPTION             ((enum_StSBCSafeTest)8U)
#define SF_GTM_EXCEPTION               ((enum_StSBCSafeTest)9U)
typedef uint8_T enum_StSBC;
#define SBC_MODE_INIT                  ((enum_StSBC)0U)          /* Default value */
#define SBC_MODE_NORMAL                ((enum_StSBC)1U)
#define SBC_MODE_STOP                  ((enum_StSBC)2U)
#define SBC_MODE_SLEEP                 ((enum_StSBC)3U)
#define SBC_MODE_RESET                 ((enum_StSBC)4U)
#define SBC_DEBUG_DATA                 ((enum_StSBC)5U)
#define SBC_MODE_BYPASS                ((enum_StSBC)6U)
#define SBC_MODE_EXPIRED               ((enum_StSBC)7U)
 
/* Block signals and states (default storage) for system '<S4>/SBC_Compose_Data' */
typedef struct {
  uint32_T CCCodeBlock;                /* '<S22>/C//C++ Code Block' */
  uint32_T CCCodeBlock1;               /* '<S22>/C//C++ Code Block1' */
  int16_T SPI_TxRx;                    /* '<S23>/SPI_TxRx' */
  uint8_T idx;                         /* '<S20>/Iterator' */
} DW_SBC_Compose_Data_TLE9278BQ_T;

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  DW_SBC_Compose_Data_TLE9278BQ_T SBC_Compose_Data;/* '<S4>/SBC_Compose_Data' */
  uint32_T CCCodeBlock;                /* '<S25>/C//C++ Code Block' */
  uint32_T oldCntRefreshWDT;           /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint32_T oldReqMsgOnD;               /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint32_T oldKeyReqMsgOnD;            /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint16_T cntSBCResend;               /* '<S24>/Chart_Resend_Data' */
  uint16_T CCCodeBlock_l;              /* '<S28>/C//C++ Code Block' */
  uint8_T RunBkgWDT_n;                 /* '<S26>/TLE9278BQX_SendData_Machine' */
  enum_StSBC StSbc;                    /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint8_T flgInitDone;                 /* '<S24>/Chart_Resend_Data' */
  uint8_T is_active_c14_TLE9278BQX_Com;/* '<S26>/TLE9278BQX_SendData_Machine' */
  uint8_T is_SBC_MODE_CTRL;            /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint8_T syncKeyWDTStop;              /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint8_T oldCntSBCWrite;              /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint8_T cntTO;                       /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint8_T tstWDTExpired;               /* '<S26>/TLE9278BQX_SendData_Machine' */
  uint8_T flgIvorEE;                   /* '<S26>/TLE9278BQX_SendData_Machine' */
  enum_StSBC stSbc;                    /* '<S26>/TLE9278BQX_SendData_Machine' */
} DW_TLE9278BQX_Com_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_TLE9278BQX_Com_T TLE9278BQX_Com_DW;

/* Model entry point functions */
extern void TLE9278BQX_Com_initialize(void);

/* Exported entry point function */
extern void TLE9278BQX_Com_Bkg(void);

/* Exported entry point function */
extern void TLE9278BQX_Com_Init(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T CntTrgSBCRes;           /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Enable WDT Machine */
extern uint16_T FlgSBCFOEn;            /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Force FO */
extern uint8_T FlgSBCProgErr;          /* '<S24>/Chart_Resend_Data' */

/* Message error */
extern uint8_T RunBkgWDT;              /* '<S3>/Merge1' */

/* Enable WDT Machine */
extern uint8_T SBCMsgIdx;              /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Message router */
extern int16_T SBCSpiError;            /* '<S20>/Logical Operator' */

/* SPI Communication error */
extern uint16_T SetAdcSel;             /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Select Adc SBC source: 0=Wk & VBatt; 1=VBatt */
extern uint16_T SetSBCCan1;            /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Can01 status setting */
extern uint16_T SetSBCSysStat;         /* '<S26>/TLE9278BQX_SendData_Machine' */

/* SBC System status setting */
extern enum_StSBC StSBC;               /* '<S3>/Merge2' */

/* SBC Status */
extern enum_StSBCSafeTest StSBCSafeTest;/* '<S26>/TLE9278BQX_SendData_Machine' */

/* Sefe status */


/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TLE9278BQX_Com'
 * '<S1>'   : 'TLE9278BQX_Com/Model Info'
 * '<S2>'   : 'TLE9278BQX_Com/TLE9278BQX_Com'
 * '<S3>'   : 'TLE9278BQX_Com/TLE9278BQX_Com/Merger'
 * '<S4>'   : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg'
 * '<S5>'   : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Init'
 * '<S6>'   : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Compose_Data'
 * '<S7>'   : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine'
 * '<S8>'   : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_InitRoutine1'
 * '<S9>'   : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_InitRoutine2'
 * '<S10>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_InitRoutine3'
 * '<S11>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_InitRoutine4'
 * '<S12>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_InitRoutine5'
 * '<S13>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Cfg'
 * '<S14>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Diag'
 * '<S15>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Get'
 * '<S16>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_IvorEE'
 * '<S17>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Mgm'
 * '<S18>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Prs'
 * '<S19>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_WDT_Clear'
 * '<S20>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Compose_Data/FIFO_Mgm'
 * '<S21>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Compose_Data/FIFO_Mgm/Iterator'
 * '<S22>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Compose_Data/FIFO_Mgm/SPI_TxRx'
 * '<S23>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Compose_Data/FIFO_Mgm/SPI_TxRx/Subsystem'
 * '<S24>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/Calc_Resend_Data'
 * '<S25>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_EECntSBCResend'
 * '<S26>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData'
 * '<S27>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/Calc_Resend_Data/Chart_Resend_Data'
 * '<S28>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_EECntSBCResend/Write_EECntSBCResend'
 * '<S29>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine'
 * '<S30>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Diag/body'
 * '<S31>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Diag/data'
 * '<S32>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Diag/fc_seq_call'
 * '<S33>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Mgm/body'
 * '<S34>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Mgm/data'
 * '<S35>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Mgm/fc_seq_call'
 * '<S36>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Prs/body'
 * '<S37>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Prs/data'
 * '<S38>'  : 'TLE9278BQX_Com/TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Prs/fc_seq_call'
 */

/*-
 * Requirements for '<Root>': TLE9278BQX_Com
 */
#endif                                 /* RTW_HEADER_TLE9278BQX_Com_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/