/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Pit
**  Filename        :  Pit.h
**  Created on      :  12-mag-2020 14:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifndef PIT_H
#define PIT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Pit_out.h"
#include "OS_api.h"
#include "OS_alarms.h"
#include "sys.h"
#include "task.h"
#include "TasksDefs.h"
#ifdef _TEST_PIT_
#include "Digio_out.h"
#endif

#ifdef _OSEK_
extern CTRCBS   OsCounters[OSNUMCTRS];
extern AlmCBS   OsAlmTable[OSNUMALMS];
#endif

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
#define PIT_US_TO_TICK(us) ((us)*PER_CLK)

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static int16_T PIT_Config_Eng0(void);
static int16_T PIT_Config_Eng1(void);

#endif // PIT_H

/****************************************************************************
 ****************************************************************************/
