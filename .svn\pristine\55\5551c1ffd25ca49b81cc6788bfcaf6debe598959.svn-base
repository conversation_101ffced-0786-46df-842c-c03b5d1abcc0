/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonKnockPower.h
 **  Date:          21-Jul-2023
 **
 **  Model Version: 1.1403
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonKnockPower_h_
#define RTW_HEADER_IonKnockPower_h_
#include <string.h>
#ifndef IonKnockPower_COMMON_INCLUDES_
# define IonKnockPower_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonKnockPower_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonKnockPower_initialize(void);

/* Exported entry point function */
extern void IonKnockPower_EOA(void);

/* Exported entry point function */
extern void IonKnockPower_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern int16_T DSAIon;                 /* '<S9>/Merge1' */

/* Delta between the implemented advance and the expected nominal advance */
extern int16_T DThPeakCyl;             /* '<S11>/Merge1' */

/* ThPeakCyl divided for ThPeakNom */
extern int16_T DeltaKnockNPow[8];      /* '<S10>/Merge11' */

/* Estimated knock intensity minus the target  */
extern uint32_T KnockInt[8];           /* '<S10>/Merge8' */

/* Estimated knock Intensity */
extern uint32_T KnockPowNorm;          /* '<S10>/Merge1' */

/* Knocking power */
extern int16_T SATotCyl;               /* '<S9>/Merge14' */

/* SA Tot Cyl = SARon + SAKnockCyl */
extern uint32_T ThrIntKnock;           /* '<S10>/Merge9' */

/* Knock Intensity threshold */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S22>/Data Type Duplicate' : Unused code path elimination
 * Block '<S21>/Data Type Duplicate' : Unused code path elimination
 * Block '<S21>/Data Type Propagation' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Duplicate' : Unused code path elimination
 * Block '<S34>/Data Type Duplicate' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate' : Unused code path elimination
 * Block '<S41>/Data Type Duplicate' : Unused code path elimination
 * Block '<S44>/Data Type Duplicate' : Unused code path elimination
 * Block '<S42>/Data Type Duplicate' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate' : Unused code path elimination
 * Block '<S52>/Data Type Duplicate' : Unused code path elimination
 * Block '<S50>/Data Type Duplicate' : Unused code path elimination
 * Block '<S50>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S50>/Data Type Propagation' : Unused code path elimination
 * Block '<S56>/Data Type Propagation' : Unused code path elimination
 * Block '<S15>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S15>/Data Type Conversion2' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion' : Eliminate redundant data type conversion
 * Block '<S31>/Reshape' : Reshape block reduction
 * Block '<S32>/Conversion' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion' : Eliminate redundant data type conversion
 * Block '<S40>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion' : Eliminate redundant data type conversion
 * Block '<S48>/Reshape' : Reshape block reduction
 * Block '<S49>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion' : Eliminate redundant data type conversion
 * Block '<S49>/Reshape' : Reshape block reduction
 * Block '<S50>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S50>/Reshape' : Reshape block reduction
 * Block '<S55>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S56>/Reshape' : Reshape block reduction
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonKnockPower'
 * '<S1>'   : 'IonKnockPower/IonKockPower_FC'
 * '<S2>'   : 'IonKnockPower/Scheduler'
 * '<S3>'   : 'IonKnockPower/IonKockPower_FC/GainEOA'
 * '<S4>'   : 'IonKnockPower/IonKockPower_FC/InitGain'
 * '<S5>'   : 'IonKnockPower/IonKockPower_FC/InitKnockIntEOA'
 * '<S6>'   : 'IonKnockPower/IonKockPower_FC/InitSATotCyl'
 * '<S7>'   : 'IonKnockPower/IonKockPower_FC/KnockIntEOA'
 * '<S8>'   : 'IonKnockPower/IonKockPower_FC/SATotCylEOA'
 * '<S9>'   : 'IonKnockPower/IonKockPower_FC/Subsystem'
 * '<S10>'  : 'IonKnockPower/IonKockPower_FC/Subsystem1'
 * '<S11>'  : 'IonKnockPower/IonKockPower_FC/Subsystem2'
 * '<S12>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/Convert_To_uA'
 * '<S13>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm'
 * '<S14>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_ThPeakNom'
 * '<S15>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/IfActionSubsystem'
 * '<S16>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/Subsystem'
 * '<S17>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2'
 * '<S18>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_DThPeakCyl'
 * '<S19>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_KFiltDThPeak'
 * '<S20>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_KnockPowNormGain'
 * '<S21>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_DThPeakCyl/FOF_Reset_S16_FXP'
 * '<S22>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_DThPeakCyl/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S23>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_KFiltDThPeak/LookUp_IR_U1'
 * '<S24>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_KFiltDThPeak/LookUp_IR_U16'
 * '<S25>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_KFiltDThPeak/LookUp_IR_U1/Data Type Conversion Inherited3'
 * '<S26>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_KFiltDThPeak/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S27>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_KnockPowNormGain/IfActionSubsystem'
 * '<S28>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_KnockPowNorm/calc_ThPeakNomTot2/calc_KnockPowNormGain/IfActionSubsystem1'
 * '<S29>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_ThPeakNom/Reset_ThPeakNomTot'
 * '<S30>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_ThPeakNom/ThPeak_Model'
 * '<S31>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_ThPeakNom/ThPeak_Model/Look2D_IR_U16'
 * '<S32>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_ThPeakNom/ThPeak_Model/LookUp_U16_S16'
 * '<S33>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_ThPeakNom/ThPeak_Model/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S34>'  : 'IonKnockPower/IonKockPower_FC/GainEOA/calc_ThPeakNom/ThPeak_Model/LookUp_U16_S16/Data Type Conversion Inherited3'
 * '<S35>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/Calc_KnockIntCyl'
 * '<S36>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockIntensity'
 * '<S37>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockThreshold'
 * '<S38>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/SparkAdvanceCorrection'
 * '<S39>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/Calc_KnockIntCyl/IfActionSubsystem'
 * '<S40>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/Calc_KnockIntCyl/IfActionSubsystem1'
 * '<S41>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/Calc_KnockIntCyl/IfActionSubsystem/LookUp_U32_U1'
 * '<S42>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/Calc_KnockIntCyl/IfActionSubsystem/LookUp_U32_U32'
 * '<S43>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/Calc_KnockIntCyl/IfActionSubsystem/LookUp_U32_U1/Data Type Conversion Inherited3'
 * '<S44>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/Calc_KnockIntCyl/IfActionSubsystem/LookUp_U32_U32/Data Type Conversion Inherited3'
 * '<S45>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/Calc_KnockIntCyl/IfActionSubsystem1/SquareRoot'
 * '<S46>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockIntensity/Filter'
 * '<S47>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockIntensity/Filter/ArrangeOutputLSB'
 * '<S48>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockThreshold/Look2D_IR_U1'
 * '<S49>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockThreshold/Look2D_IR_U16'
 * '<S50>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockThreshold/Look2D_U8_S16_S16'
 * '<S51>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockThreshold/Look2D_IR_U1/Data Type Conversion Inherited1'
 * '<S52>'  : 'IonKnockPower/IonKockPower_FC/KnockIntEOA/KnockThreshold/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S53>'  : 'IonKnockPower/IonKockPower_FC/SATotCylEOA/IfActionSubsystem'
 * '<S54>'  : 'IonKnockPower/IonKockPower_FC/SATotCylEOA/IfActionSubsystem2'
 * '<S55>'  : 'IonKnockPower/IonKockPower_FC/SATotCylEOA/IfActionSubsystem2/ArrangeLookUpOutputLSB'
 * '<S56>'  : 'IonKnockPower/IonKockPower_FC/SATotCylEOA/IfActionSubsystem2/Look2D_IR_S8'
 */

/*-
 * Requirements for '<Root>': IonKnockPower
 */
#endif                                 /* RTW_HEADER_IonKnockPower_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/