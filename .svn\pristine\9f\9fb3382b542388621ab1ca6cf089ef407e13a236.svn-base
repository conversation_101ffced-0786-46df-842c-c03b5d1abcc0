/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    SPC574K_GTM_IRQ.h
 * @brief   SPC574K GTM IRQ.
 *
 * @addtogroup GTM
 * @{
 */
#ifndef _SPC574K_GTM_IRQ_H_
#define _SPC574K_GTM_IRQ_H_

#include "gtm_cfg.h"
//#include <irq.h>  MC

/*lint -e621*/

/* PSM0 IRQ */
/**
 * @name   SPC5 GTM PSM Interrupt Priority
 * @{
 */
/**
 * @brief  PSM Interrupt Priority
 */
#if !defined(SPC5_GTM_PSM_INT_PRIORITY) || defined(__DOXYGEN__)
#define SPC5_GTM_PSM_INT_PRIORITY              12
#endif
/** @} */
#define SPC5_PSM0_0_HANDLER                    vector714
#define SPC5_PSM0_0_INT_NUMBER                 714
#define SPC5_PSM0_0_INT_PRIORITY               SPC5_GTM_PSM_INT_PRIORITY

#define SPC5_PSM0_1_HANDLER                    vector715
#define SPC5_PSM0_1_INT_NUMBER                 715
#define SPC5_PSM0_1_INT_PRIORITY               SPC5_GTM_PSM_INT_PRIORITY

#define SPC5_PSM0_2_HANDLER                    vector716
#define SPC5_PSM0_2_INT_NUMBER                 716
#define SPC5_PSM0_2_INT_PRIORITY               SPC5_GTM_PSM_INT_PRIORITY

#define SPC5_PSM0_3_HANDLER                    vector717
#define SPC5_PSM0_3_INT_NUMBER                 717
#define SPC5_PSM0_3_INT_PRIORITY               SPC5_GTM_PSM_INT_PRIORITY

#define SPC5_PSM0_4_HANDLER                    vector718
#define SPC5_PSM0_4_INT_NUMBER                 718
#define SPC5_PSM0_4_INT_PRIORITY               SPC5_GTM_PSM_INT_PRIORITY

#define SPC5_PSM0_5_HANDLER                    vector719
#define SPC5_PSM0_5_INT_NUMBER                 719
#define SPC5_PSM0_5_INT_PRIORITY               SPC5_GTM_PSM_INT_PRIORITY

#define SPC5_PSM0_6_HANDLER                    vector720
#define SPC5_PSM0_6_INT_NUMBER                 720
#define SPC5_PSM0_6_INT_PRIORITY               SPC5_GTM_PSM_INT_PRIORITY

#define SPC5_PSM0_7_HANDLER                    vector721
#define SPC5_PSM0_7_INT_NUMBER                 721
#define SPC5_PSM0_7_INT_PRIORITY               SPC5_GTM_PSM_INT_PRIORITY

#if 0 //MC
IRQ_HANDLER(SPC5_PSM0_0_HANDLER);
IRQ_HANDLER(SPC5_PSM0_1_HANDLER);
IRQ_HANDLER(SPC5_PSM0_2_HANDLER);
IRQ_HANDLER(SPC5_PSM0_3_HANDLER);
IRQ_HANDLER(SPC5_PSM0_4_HANDLER);
IRQ_HANDLER(SPC5_PSM0_5_HANDLER);
IRQ_HANDLER(SPC5_PSM0_6_HANDLER);
IRQ_HANDLER(SPC5_PSM0_7_HANDLER);
#endif

/* DPLL IRQ */
/**
 * @name   SPC5 GTM DPLL Interrupt Priority
 * @{
 */
/**
 * @brief  DPLL Interrupt Priority
 */
#if !defined(SPC5_GTM_DPLL_INT_PRIORITY) || defined(__DOXYGEN__)
#define SPC5_GTM_DPLL_INT_PRIORITY                                             14
#endif
/** @} */

#define SPC5_DPLL0_TRIGGER_DIRECTION_CHANGE_HANDLER                            vector722
#define SPC5_DPLL0_TRIGGER_DIRECTION_CHANGE_INT_NUMBER                         722
#define SPC5_DPLL0_TRIGGER_DIRECTION_CHANGE_INT_PRIORITY                       SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_ENABLE_DISABLE_HANDLER                                      vector723
#define SPC5_DPLL0_ENABLE_DISABLE_INT_NUMBER                                   723
#define SPC5_DPLL0_ENABLE_DISABLE_INT_PRIORITY                                 SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_MIN_HOLD_TIME_VIOLATION_HANDLER                     vector724
#define SPC5_DPLL0_TRIGGER_MIN_HOLD_TIME_VIOLATION_INT_NUMBER                  724
#define SPC5_DPLL0_TRIGGER_MIN_HOLD_TIME_VIOLATION_INT_PRIORITY                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_MAX_HOLD_TIME_VIOLATION_HANDLER                     vector725
#define SPC5_DPLL0_TRIGGER_MAX_HOLD_TIME_VIOLATION_INT_NUMBER                  725
#define SPC5_DPLL0_TRIGGER_MAX_HOLD_TIME_VIOLATION_INT_PRIORITY                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_STATE_INACTIVE_SLOPE_DETECTED_HANDLER                       vector726
#define SPC5_DPLL0_STATE_INACTIVE_SLOPE_DETECTED_INT_NUMBER                    726
#define SPC5_DPLL0_STATE_INACTIVE_SLOPE_DETECTED_INT_PRIORITY                  SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_INACTIVE_SLOPE_DETECTED_HANDLER                     vector727
#define SPC5_DPLL0_TRIGGER_INACTIVE_SLOPE_DETECTED_INT_NUMBER                  727
#define SPC5_DPLL0_TRIGGER_INACTIVE_SLOPE_DETECTED_INT_PRIORITY                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_MISSING_STATE_HANDLER                                       vector728
#define SPC5_DPLL0_MISSING_STATE_INT_NUMBER                                    728
#define SPC5_DPLL0_MISSING_STATE_INT_PRIORITY                                  SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_MISSING_TRIGGER_HANDLER                                     vector729
#define SPC5_DPLL0_MISSING_TRIGGER_INT_NUMBER                                  729
#define SPC5_DPLL0_MISSING_TRIGGER_INT_PRIORITY                                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_STATE_ACTIVE_SLOPE_DETECTED_HANDLER                         vector730
#define SPC5_DPLL0_STATE_ACTIVE_SLOPE_DETECTED_INT_NUMBER                      730
#define SPC5_DPLL0_STATE_ACTIVE_SLOPE_DETECTED_INT_PRIORITY                    SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_ACTIVE_SLOPE_DETECTION_HANDLER                      vector731
#define SPC5_DPLL0_TRIGGER_ACTIVE_SLOPE_DETECTION_INT_NUMBER                   731
#define SPC5_DPLL0_TRIGGER_ACTIVE_SLOPE_DETECTION_INT_PRIORITY                 SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_PLAUSIBILITY_WINDOW_REQUEST_VIOLATION_HANDLER       vector732
#define SPC5_DPLL0_TRIGGER_PLAUSIBILITY_WINDOW_REQUEST_VIOLATION_INT_NUMBER    732
#define SPC5_DPLL0_TRIGGER_PLAUSIBILITY_WINDOW_REQUEST_VIOLATION_INT_PRIORITY  SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_2_HANDLER                           vector733
#define SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_2_INT_NUMBER                        733
#define SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_2_INT_PRIORITY                      SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_1C_1B_HANDLER                       vector734
#define SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_1C_1B_INT_NUMBER                    734
#define SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_1C_1B_INT_PRIORITY                  SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_SUB_INC1_GET_LOCK_HANDLER                                   vector735
#define SPC5_DPLL0_SUB_INC1_GET_LOCK_INT_NUMBER                                735
#define SPC5_DPLL0_SUB_INC1_GET_LOCK_INT_PRIORITY                              SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_SUB_INC1_LOSS_LOCK_HANDLER                                  vector736
#define SPC5_DPLL0_SUB_INC1_LOSS_LOCK_INT_NUMBER                               736
#define SPC5_DPLL0_SUB_INC1_LOSS_LOCK_INT_PRIORITY                             SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_ERROR_INTERRUPT_HANDLER                                     vector737
#define SPC5_DPLL0_ERROR_INTERRUPT_INT_NUMBER                                  736
#define SPC5_DPLL0_ERROR_INTERRUPT_INT_PRIORITY                                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_SUB_INC2_GET_LOCK_HANDLER                                   vector738
#define SPC5_DPLL0_SUB_INC2_GET_LOCK_INT_NUMBER                                738
#define SPC5_DPLL0_SUB_INC2_GET_LOCK_INT_PRIORITY                              SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_SUB_INC2_LOSS_LOCK_HANDLER                                  vector739
#define SPC5_DPLL0_SUB_INC2_LOSS_LOCK_INT_NUMBER                               739
#define SPC5_DPLL0_SUB_INC2_LOSS_LOCK_INT_PRIORITY                             SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_EVENT_0_HANDLER                                     vector740
#define SPC5_DPLL0_TRIGGER_EVENT_0_INT_NUMBER                                  740
#define SPC5_DPLL0_TRIGGER_EVENT_0_INT_PRIORITY                                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_EVENT_1_HANDLER                                     vector741
#define SPC5_DPLL0_TRIGGER_EVENT_1_INT_NUMBER                                  741
#define SPC5_DPLL0_TRIGGER_EVENT_1_INT_PRIORITY                                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_EVENT_2_HANDLER                                     vector742
#define SPC5_DPLL0_TRIGGER_EVENT_2_INT_NUMBER                                  742
#define SPC5_DPLL0_TRIGGER_EVENT_2_INT_PRIORITY                                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_EVENT_3_HANDLER                                     vector743
#define SPC5_DPLL0_TRIGGER_EVENT_3_INT_NUMBER                                  743
#define SPC5_DPLL0_TRIGGER_EVENT_3_INT_PRIORITY                                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_EVENT_4_HANDLER                                     vector744
#define SPC5_DPLL0_TRIGGER_EVENT_4_INT_NUMBER                                  744
#define SPC5_DPLL0_TRIGGER_EVENT_4_INT_PRIORITY                                SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_CALCULATED_DURATION_HANDLER                         vector745
#define SPC5_DPLL0_TRIGGER_CALCULATED_DURATION_INT_NUMBER                      745
#define SPC5_DPLL0_TRIGGER_CALCULATED_DURATION_INT_PRIORITY                    SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_STATE_CALCULATED_DURATION_HANDLER                           vector746
#define SPC5_DPLL0_STATE_CALCULATED_DURATION_INT_NUMBER                        746
#define SPC5_DPLL0_STATE_CALCULATED_DURATION_INT_PRIORITY                      SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_TRIGGER_OUT_OF_RANGE_HANDLER                                vector747
#define SPC5_DPLL0_TRIGGER_OUT_OF_RANGE_INT_NUMBER                             747
#define SPC5_DPLL0_TRIGGER_OUT_OF_RANGE_INT_PRIORITY                           SPC5_GTM_DPLL_INT_PRIORITY

#define SPC5_DPLL0_STATE_OUT_OF_RANGE_HANDLER                                  vector748
#define SPC5_DPLL0_STATE_OUT_OF_RANGE_INT_NUMBER                               748
#define SPC5_DPLL0_STATE_OUT_OF_RANGE_INT_PRIORITY                             SPC5_GTM_DPLL_INT_PRIORITY

#if 0 //MC
IRQ_HANDLER(SPC5_DPLL0_ENABLE_DISABLE_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_MIN_HOLD_TIME_VIOLATION_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_MAX_HOLD_TIME_VIOLATION_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_STATE_INACTIVE_SLOPE_DETECTED_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_INACTIVE_SLOPE_DETECTED_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_MISSING_STATE_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_MISSING_TRIGGER_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_STATE_ACTIVE_SLOPE_DETECTED_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_ACTIVE_SLOPE_DETECTION_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_PLAUSIBILITY_WINDOW_REQUEST_VIOLATION_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_2_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_1C_1B_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_SUB_INC1_GET_LOCK_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_SUB_INC1_LOSS_LOCK_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_ERROR_INTERRUPT_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_SUB_INC2_GET_LOCK_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_SUB_INC2_LOSS_LOCK_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_EVENT_0_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_EVENT_1_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_EVENT_2_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_EVENT_3_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_EVENT_4_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_CALCULATED_DURATION_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_STATE_CALCULATED_DURATION_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_TRIGGER_OUT_OF_RANGE_HANDLER);
IRQ_HANDLER(SPC5_DPLL0_STATE_OUT_OF_RANGE_HANDLER);
#endif

/* TIM IRQ */
/**
 * @name   SPC5 GTM TIM Interrupt Priority
 * @{
 */
/**
 * @brief  TIM Interrupt Priority
 */
#if !defined(SPC5_GTM_TIM_INT_PRIORITY) || defined(__DOXYGEN__)
#define SPC5_GTM_TIM_INT_PRIORITY              14
#endif
/** @} */
#define SPC5_TIM0_0_HANDLER                    vector749
#define SPC5_TIM0_0_INT_NUMBER                 749
#define SPC5_TIM0_0_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM0_1_HANDLER                    vector750
#define SPC5_TIM0_1_INT_NUMBER                 750
#define SPC5_TIM0_1_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM0_2_HANDLER                    vector751
#define SPC5_TIM0_2_INT_NUMBER                 751
#define SPC5_TIM0_2_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM0_3_HANDLER                    vector752
#define SPC5_TIM0_3_INT_NUMBER                 752
#define SPC5_TIM0_3_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM0_4_HANDLER                    vector753
#define SPC5_TIM0_4_INT_NUMBER                 753
#define SPC5_TIM0_4_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM0_5_HANDLER                    vector754
#define SPC5_TIM0_5_INT_NUMBER                 754
#define SPC5_TIM0_5_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM0_6_HANDLER                    vector755
#define SPC5_TIM0_6_INT_NUMBER                 755
#define SPC5_TIM0_6_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM0_7_HANDLER                    vector756
#define SPC5_TIM0_7_INT_NUMBER                 756
#define SPC5_TIM0_7_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM1_0_HANDLER                    vector757
#define SPC5_TIM1_0_INT_NUMBER                 757
#define SPC5_TIM1_0_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM1_1_HANDLER                    vector758
#define SPC5_TIM1_1_INT_NUMBER                 758
#define SPC5_TIM1_1_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM1_2_HANDLER                    vector759
#define SPC5_TIM1_2_INT_NUMBER                 759
#define SPC5_TIM1_2_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM1_3_HANDLER                    vector760
#define SPC5_TIM1_3_INT_NUMBER                 760
#define SPC5_TIM1_3_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM1_4_HANDLER                    vector761
#define SPC5_TIM1_4_INT_NUMBER                 761
#define SPC5_TIM1_4_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM1_5_HANDLER                    vector762
#define SPC5_TIM1_5_INT_NUMBER                 762
#define SPC5_TIM1_5_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM1_6_HANDLER                    vector763
#define SPC5_TIM1_6_INT_NUMBER                 763
#define SPC5_TIM1_6_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM1_7_HANDLER                    vector764
#define SPC5_TIM1_7_INT_NUMBER                 764
#define SPC5_TIM1_7_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM2_0_HANDLER                    vector765
#define SPC5_TIM2_0_INT_NUMBER                 765
#define SPC5_TIM2_0_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM2_1_HANDLER                    vector766
#define SPC5_TIM2_1_INT_NUMBER                 767
#define SPC5_TIM2_1_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM2_2_HANDLER                    vector767
#define SPC5_TIM2_2_INT_NUMBER                 767
#define SPC5_TIM2_2_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM2_3_HANDLER                    vector768
#define SPC5_TIM2_3_INT_NUMBER                 768
#define SPC5_TIM2_3_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM2_4_HANDLER                    vector769
#define SPC5_TIM2_4_INT_NUMBER                 769
#define SPC5_TIM2_4_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM2_5_HANDLER                    vector770
#define SPC5_TIM2_5_INT_NUMBER                 770
#define SPC5_TIM2_5_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM2_6_HANDLER                    vector771
#define SPC5_TIM2_6_INT_NUMBER                 771
#define SPC5_TIM2_6_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#define SPC5_TIM2_7_HANDLER                    vector772
#define SPC5_TIM2_7_INT_NUMBER                 772
#define SPC5_TIM2_7_INT_PRIORITY               SPC5_GTM_TIM_INT_PRIORITY

#if 0 //MC
IRQ_HANDLER(SPC5_TIM0_0_HANDLER);
IRQ_HANDLER(SPC5_TIM0_1_HANDLER);
IRQ_HANDLER(SPC5_TIM0_2_HANDLER);
IRQ_HANDLER(SPC5_TIM0_3_HANDLER);
IRQ_HANDLER(SPC5_TIM0_4_HANDLER);
IRQ_HANDLER(SPC5_TIM0_5_HANDLER);
IRQ_HANDLER(SPC5_TIM0_6_HANDLER);
IRQ_HANDLER(SPC5_TIM0_7_HANDLER);

IRQ_HANDLER(SPC5_TIM1_0_HANDLER);
IRQ_HANDLER(SPC5_TIM1_1_HANDLER);
IRQ_HANDLER(SPC5_TIM1_2_HANDLER);
IRQ_HANDLER(SPC5_TIM1_3_HANDLER);
IRQ_HANDLER(SPC5_TIM1_4_HANDLER);
IRQ_HANDLER(SPC5_TIM1_5_HANDLER);
IRQ_HANDLER(SPC5_TIM1_6_HANDLER);
IRQ_HANDLER(SPC5_TIM1_7_HANDLER);

IRQ_HANDLER(SPC5_TIM2_0_HANDLER);
IRQ_HANDLER(SPC5_TIM2_1_HANDLER);
IRQ_HANDLER(SPC5_TIM2_2_HANDLER);
IRQ_HANDLER(SPC5_TIM2_3_HANDLER);
IRQ_HANDLER(SPC5_TIM2_4_HANDLER);
IRQ_HANDLER(SPC5_TIM2_5_HANDLER);
IRQ_HANDLER(SPC5_TIM2_6_HANDLER);
IRQ_HANDLER(SPC5_TIM2_7_HANDLER);
#endif

/* MCS0 IRQ */
/**
 * @name   SPC5 GTM MCS Interrupt Priority
 * @{
 */
/**
 * @brief  MCS Interrupt Priority
 */
#if !defined(SPC5_GTM_MCS_INT_PRIORITY) || defined(__DOXYGEN__)
#define SPC5_GTM_MCS_INT_PRIORITY              16
#endif
/** @} */
#define SPC5_MCS0_0_HANDLER                    vector781
#define SPC5_MCS0_0_INT_NUMBER                 781
#define SPC5_MCS0_0_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS0_1_HANDLER                    vector782
#define SPC5_MCS0_1_INT_NUMBER                 782
#define SPC5_MCS0_1_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS0_2_HANDLER                    vector783
#define SPC5_MCS0_2_INT_NUMBER                 783
#define SPC5_MCS0_2_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS0_3_HANDLER                    vector784
#define SPC5_MCS0_3_INT_NUMBER                 784
#define SPC5_MCS0_3_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS0_4_HANDLER                    vector785
#define SPC5_MCS0_4_INT_NUMBER                 785
#define SPC5_MCS0_4_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS0_5_HANDLER                    vector786
#define SPC5_MCS0_5_INT_NUMBER                 786
#define SPC5_MCS0_5_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS0_6_HANDLER                    vector787
#define SPC5_MCS0_6_INT_NUMBER                 787
#define SPC5_MCS0_6_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS0_7_HANDLER                    vector788
#define SPC5_MCS0_7_INT_NUMBER                 788
#define SPC5_MCS0_7_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

/* MCS1 IRQ */
#define SPC5_MCS1_0_HANDLER                    vector789
#define SPC5_MCS1_0_INT_NUMBER                 789
#define SPC5_MCS1_0_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS1_1_HANDLER                    vector790
#define SPC5_MCS1_1_INT_NUMBER                 790
#define SPC5_MCS1_1_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS1_2_HANDLER                    vector791
#define SPC5_MCS1_2_INT_NUMBER                 791
#define SPC5_MCS1_2_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS1_3_HANDLER                    vector792
#define SPC5_MCS1_3_INT_NUMBER                 792
#define SPC5_MCS1_3_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS1_4_HANDLER                    vector793
#define SPC5_MCS1_4_INT_NUMBER                 793
#define SPC5_MCS1_4_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS1_5_HANDLER                    vector794
#define SPC5_MCS1_5_INT_NUMBER                 794
#define SPC5_MCS1_5_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS1_6_HANDLER                    vector795
#define SPC5_MCS1_6_INT_NUMBER                 795
#define SPC5_MCS1_6_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS1_7_HANDLER                    vector796
#define SPC5_MCS1_7_INT_NUMBER                 796
#define SPC5_MCS1_7_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

/* MCS2 IRQ */
#define SPC5_MCS2_0_HANDLER                    vector797
#define SPC5_MCS2_0_INT_NUMBER                 797
#define SPC5_MCS2_0_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS2_1_HANDLER                    vector798
#define SPC5_MCS2_1_INT_NUMBER                 798
#define SPC5_MCS2_1_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS2_2_HANDLER                    vector799
#define SPC5_MCS2_2_INT_NUMBER                 799
#define SPC5_MCS2_2_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS2_3_HANDLER                    vector800
#define SPC5_MCS2_3_INT_NUMBER                 800
#define SPC5_MCS2_3_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS2_4_HANDLER                    vector801
#define SPC5_MCS2_4_INT_NUMBER                 801
#define SPC5_MCS2_4_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS2_5_HANDLER                    vector802
#define SPC5_MCS2_5_INT_NUMBER                 802
#define SPC5_MCS2_5_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS2_6_HANDLER                    vector803
#define SPC5_MCS2_6_INT_NUMBER                 803
#define SPC5_MCS2_6_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#define SPC5_MCS2_7_HANDLER                    vector804
#define SPC5_MCS2_7_INT_NUMBER                 804
#define SPC5_MCS2_7_INT_PRIORITY               SPC5_GTM_MCS_INT_PRIORITY

#if 0 //MC
IRQ_HANDLER(SPC5_MCS0_0_HANDLER);
IRQ_HANDLER(SPC5_MCS0_1_HANDLER);
IRQ_HANDLER(SPC5_MCS0_2_HANDLER);
IRQ_HANDLER(SPC5_MCS0_3_HANDLER);
IRQ_HANDLER(SPC5_MCS0_4_HANDLER);
IRQ_HANDLER(SPC5_MCS0_5_HANDLER);
IRQ_HANDLER(SPC5_MCS0_6_HANDLER);
IRQ_HANDLER(SPC5_MCS0_7_HANDLER);

IRQ_HANDLER(SPC5_MCS1_0_HANDLER);
IRQ_HANDLER(SPC5_MCS1_1_HANDLER);
IRQ_HANDLER(SPC5_MCS1_2_HANDLER);
IRQ_HANDLER(SPC5_MCS1_3_HANDLER);
IRQ_HANDLER(SPC5_MCS1_4_HANDLER);
IRQ_HANDLER(SPC5_MCS1_5_HANDLER);
IRQ_HANDLER(SPC5_MCS1_6_HANDLER);
IRQ_HANDLER(SPC5_MCS1_7_HANDLER);

IRQ_HANDLER(SPC5_MCS2_0_HANDLER);
IRQ_HANDLER(SPC5_MCS2_1_HANDLER);
IRQ_HANDLER(SPC5_MCS2_2_HANDLER);
IRQ_HANDLER(SPC5_MCS2_3_HANDLER);
IRQ_HANDLER(SPC5_MCS2_4_HANDLER);
IRQ_HANDLER(SPC5_MCS2_5_HANDLER);
IRQ_HANDLER(SPC5_MCS2_6_HANDLER);
IRQ_HANDLER(SPC5_MCS2_7_HANDLER);
#endif

/**
 * @name   SPC5 GTM TOM Interrupt Priority
 * @{
 */
/**
 * @brief  TOM Interrupt Priority
 */
#if !defined(SPC5_GTM_TOM_INT_PRIORITY) || defined(__DOXYGEN__)
#define SPC5_GTM_TOM_INT_PRIORITY              15
#endif
/** @} */

/* TOM0 Channel 0 & Channel 1 IRQs */
#define SPC5_TOM0_EVENT0_HANDLER               vector813
#define SPC5_TOM0_EVENT0_INT_NUMBER            813
#define SPC5_TOM0_EVENT0_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM0 Channel 2 & Channel 3 IRQs */
#define SPC5_TOM0_EVENT1_HANDLER               vector814
#define SPC5_TOM0_EVENT1_INT_NUMBER            814
#define SPC5_TOM0_EVENT1_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM0 Channel 4 & Channel 5 IRQs */
#define SPC5_TOM0_EVENT2_HANDLER               vector815
#define SPC5_TOM0_EVENT2_INT_NUMBER            815
#define SPC5_TOM0_EVENT2_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM0 Channel 6 & Channel 7 IRQs */
#define SPC5_TOM0_EVENT3_HANDLER               vector816
#define SPC5_TOM0_EVENT3_INT_NUMBER            816
#define SPC5_TOM0_EVENT3_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM0 Channel 8 & Channel 9 IRQs */
#define SPC5_TOM0_EVENT4_HANDLER               vector817
#define SPC5_TOM0_EVENT4_INT_NUMBER            817
#define SPC5_TOM0_EVENT4_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM0 Channel 10 & Channel 11 IRQs */
#define SPC5_TOM0_EVENT5_HANDLER               vector818
#define SPC5_TOM0_EVENT5_INT_NUMBER            818
#define SPC5_TOM0_EVENT5_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM0 Channel 12 & Channel 13 IRQs */
#define SPC5_TOM0_EVENT6_HANDLER               vector819
#define SPC5_TOM0_EVENT6_INT_NUMBER            819
#define SPC5_TOM0_EVENT6_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM0 Channel 14 & Channel 15 IRQs */
#define SPC5_TOM0_EVENT7_HANDLER               vector820
#define SPC5_TOM0_EVENT7_INT_NUMBER            820
#define SPC5_TOM0_EVENT7_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM1 Channel 0 & Channel 1 IRQs */
#define SPC5_TOM1_EVENT0_HANDLER               vector821
#define SPC5_TOM1_EVENT0_INT_NUMBER            821
#define SPC5_TOM1_EVENT0_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM1 Channel 2 & Channel 3 IRQs */
#define SPC5_TOM1_EVENT1_HANDLER               vector822
#define SPC5_TOM1_EVENT1_INT_NUMBER            822
#define SPC5_TOM1_EVENT1_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM1 Channel 4 & Channel 5 IRQs */
#define SPC5_TOM1_EVENT2_HANDLER               vector823
#define SPC5_TOM1_EVENT2_INT_NUMBER            823
#define SPC5_TOM1_EVENT2_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM1 Channel 6 & Channel 7 IRQs */
#define SPC5_TOM1_EVENT3_HANDLER               vector824
#define SPC5_TOM1_EVENT3_INT_NUMBER            824
#define SPC5_TOM1_EVENT3_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM1 Channel 8 & Channel 9 IRQs */
#define SPC5_TOM1_EVENT4_HANDLER               vector825
#define SPC5_TOM1_EVENT4_INT_NUMBER            825
#define SPC5_TOM1_EVENT4_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM1 Channel 10 & Channel 11 IRQs */
#define SPC5_TOM1_EVENT5_HANDLER               vector826
#define SPC5_TOM1_EVENT5_INT_NUMBER            826
#define SPC5_TOM1_EVENT5_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM1 Channel 12 & Channel 13 IRQs */
#define SPC5_TOM1_EVENT6_HANDLER               vector827
#define SPC5_TOM1_EVENT6_INT_NUMBER            827
#define SPC5_TOM1_EVENT6_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

/* TOM1 Channel 14 & Channel 15 IRQs */
#define SPC5_TOM1_EVENT7_HANDLER               vector828
#define SPC5_TOM1_EVENT7_INT_NUMBER            828
#define SPC5_TOM1_EVENT7_INT_PRIORITY          SPC5_GTM_TOM_INT_PRIORITY

#if 0
IRQ_HANDLER(SPC5_TOM0_EVENT0_HANDLER);
IRQ_HANDLER(SPC5_TOM0_EVENT1_HANDLER);
IRQ_HANDLER(SPC5_TOM0_EVENT2_HANDLER);
IRQ_HANDLER(SPC5_TOM0_EVENT3_HANDLER);
IRQ_HANDLER(SPC5_TOM0_EVENT4_HANDLER);
IRQ_HANDLER(SPC5_TOM0_EVENT5_HANDLER);
IRQ_HANDLER(SPC5_TOM0_EVENT6_HANDLER);
IRQ_HANDLER(SPC5_TOM0_EVENT7_HANDLER);

IRQ_HANDLER(SPC5_TOM1_EVENT0_HANDLER);
IRQ_HANDLER(SPC5_TOM1_EVENT1_HANDLER);
IRQ_HANDLER(SPC5_TOM1_EVENT2_HANDLER);
IRQ_HANDLER(SPC5_TOM1_EVENT3_HANDLER);
IRQ_HANDLER(SPC5_TOM1_EVENT4_HANDLER);
IRQ_HANDLER(SPC5_TOM1_EVENT5_HANDLER);
IRQ_HANDLER(SPC5_TOM1_EVENT6_HANDLER);
IRQ_HANDLER(SPC5_TOM1_EVENT7_HANDLER);
#endif

/**
 * @name   SPC5 GTM ATOM Interrupt Priority
 * @{
 */
/**
 * @brief  ATOM Interrupt Priority
 */
#if !defined(SPC5_GTM_ATOM_INT_PRIORITY) || defined(__DOXYGEN__)
#define SPC5_GTM_ATOM_INT_PRIORITY             16
#endif
/** @} */
/* ATOM0 Channel 0 & Channel 1 IRQs */
#define SPC5_ATOM0_EVENT0_HANDLER              vector837
#define SPC5_ATOM0_EVENT0_INT_NUMBER           837
#define SPC5_ATOM0_EVENT0_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM0 Channel 2 & Channel 3 IRQs */
#define SPC5_ATOM0_EVENT1_HANDLER              vector838
#define SPC5_ATOM0_EVENT1_INT_NUMBER           838
#define SPC5_ATOM0_EVENT1_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM0 Channel 4 & Channel 5 IRQs */
#define SPC5_ATOM0_EVENT2_HANDLER              vector839
#define SPC5_ATOM0_EVENT2_INT_NUMBER           839
#define SPC5_ATOM0_EVENT2_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM0 Channel 6 & Channel 7 IRQs */
#define SPC5_ATOM0_EVENT3_HANDLER              vector840
#define SPC5_ATOM0_EVENT3_INT_NUMBER           840
#define SPC5_ATOM0_EVENT3_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM1 Channel 0 & Channel 1 IRQs */
#define SPC5_ATOM1_EVENT0_HANDLER              vector841
#define SPC5_ATOM1_EVENT0_INT_NUMBER           841
#define SPC5_ATOM1_EVENT0_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM1 Channel 2 & Channel 3 IRQs */
#define SPC5_ATOM1_EVENT1_HANDLER              vector842
#define SPC5_ATOM1_EVENT1_INT_NUMBER           842
#define SPC5_ATOM1_EVENT1_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM1 Channel 4 & Channel 5 IRQs */
#define SPC5_ATOM1_EVENT2_HANDLER              vector843
#define SPC5_ATOM1_EVENT2_INT_NUMBER           843
#define SPC5_ATOM1_EVENT2_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM1 Channel 6 & Channel 7 IRQs */
#define SPC5_ATOM1_EVENT3_HANDLER              vector844
#define SPC5_ATOM1_EVENT3_INT_NUMBER           844
#define SPC5_ATOM1_EVENT3_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM2 Channel 0 & Channel 1 IRQs */
#define SPC5_ATOM2_EVENT0_HANDLER              vector845
#define SPC5_ATOM2_EVENT0_INT_NUMBER           845
#define SPC5_ATOM2_EVENT0_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM2 Channel 2 & Channel 3 IRQs */
#define SPC5_ATOM2_EVENT1_HANDLER              vector846
#define SPC5_ATOM2_EVENT1_INT_NUMBER           846
#define SPC5_ATOM2_EVENT1_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM2 Channel 4 & Channel 5 IRQs */
#define SPC5_ATOM2_EVENT2_HANDLER              vector847
#define SPC5_ATOM2_EVENT2_INT_NUMBER           847
#define SPC5_ATOM2_EVENT2_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM2 Channel 6 & Channel 7 IRQs */
#define SPC5_ATOM2_EVENT3_HANDLER              vector848
#define SPC5_ATOM2_EVENT3_INT_NUMBER           848
#define SPC5_ATOM2_EVENT3_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM3 Channel 0 & Channel 1 IRQs */
#define SPC5_ATOM3_EVENT0_HANDLER              vector849
#define SPC5_ATOM3_EVENT0_INT_NUMBER           849
#define SPC5_ATOM3_EVENT0_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM3 Channe2 0 & Channel 3 IRQs */
#define SPC5_ATOM3_EVENT1_HANDLER              vector850
#define SPC5_ATOM3_EVENT1_INT_NUMBER           850
#define SPC5_ATOM3_EVENT1_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM3 Channel 4 & Channel 5 IRQs */
#define SPC5_ATOM3_EVENT2_HANDLER              vector851
#define SPC5_ATOM3_EVENT2_INT_NUMBER           851
#define SPC5_ATOM3_EVENT2_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

/* ATOM3 Channel 6 & Channel 7 IRQs */
#define SPC5_ATOM3_EVENT3_HANDLER              vector852
#define SPC5_ATOM3_EVENT3_INT_NUMBER           852
#define SPC5_ATOM3_EVENT3_INT_PRIORITY         SPC5_GTM_ATOM_INT_PRIORITY

#if 0
IRQ_HANDLER(SPC5_ATOM0_EVENT0_HANDLER);
IRQ_HANDLER(SPC5_ATOM0_EVENT1_HANDLER);
IRQ_HANDLER(SPC5_ATOM0_EVENT2_HANDLER);
IRQ_HANDLER(SPC5_ATOM0_EVENT3_HANDLER);

IRQ_HANDLER(SPC5_ATOM1_EVENT0_HANDLER);
IRQ_HANDLER(SPC5_ATOM1_EVENT1_HANDLER);
IRQ_HANDLER(SPC5_ATOM1_EVENT2_HANDLER);
IRQ_HANDLER(SPC5_ATOM1_EVENT3_HANDLER);

IRQ_HANDLER(SPC5_ATOM2_EVENT0_HANDLER);
IRQ_HANDLER(SPC5_ATOM2_EVENT1_HANDLER);
IRQ_HANDLER(SPC5_ATOM2_EVENT2_HANDLER);
IRQ_HANDLER(SPC5_ATOM2_EVENT3_HANDLER);

IRQ_HANDLER(SPC5_ATOM3_EVENT0_HANDLER);
IRQ_HANDLER(SPC5_ATOM3_EVENT1_HANDLER);
IRQ_HANDLER(SPC5_ATOM3_EVENT2_HANDLER);
IRQ_HANDLER(SPC5_ATOM3_EVENT3_HANDLER);
#endif

/**
 * @name   SPC5 GTM BRC Interrupt Priority
 * @{
 */
/**
 * @brief  BRC Interrupt Priority
 */
#if !defined(SPC5_GTM_BRC_INT_PRIORITY) || defined(__DOXYGEN__)
#define SPC5_GTM_BRC_INT_PRIORITY              15
#endif
/** @} */
/* BRC IRQ */
#define SPC5_BRC_HANDLER                       vector710
#define SPC5_BRC_INT_NUMBER                    710
#define SPC5_BRC_INT_PRIORITY                  SPC5_GTM_BRC_INT_PRIORITY

#if 0 //MC
IRQ_HANDLER(SPC5_BRC_HANDLER);
#endif

/*lint +e621*/
#endif /* _SPC574K_GTM_IRQ_H_ */

/** @} */
