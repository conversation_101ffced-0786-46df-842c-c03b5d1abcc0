/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_cmu.c
 * @brief   SPC5xx GTM CMU low level driver code.
 *
 * @addtogroup CMU
 * @{
 */

#include "gtm.h"
#include "clock.h"
 
/**
 * @brief   GTM CMU driver identifier.
 */
GTM_CMUDriver CMUD1;
/**
 * @brief   Low level GTM CMU driver initialization.
 *
 * @init
 */

void gtm_cmuInit(void) {

	CMUD1.cmu = &GTM_CMU;

	/* Disable all Clock - all internal clock counters will be reset */
	CMUD1.cmu->CLK_EN.R = 0x00000000UL;

	/*
	 *  Configure the Global Clock Divider
	 *
	 *  The CMU hardware alters the content of CMU_GCLK_NUM and CMU_GCLK_DEN
	 *  automatically to 0x1, if CMU_GCLK_NUM is specified less than CMU_GCLK_DEN or
	 *  one of the values is specified with a value zero.
	 *  Thus, a secure way for altering the values is writing twice to the register
	 *  CMU_GCLK_NUM followed by a single write to register CMU_GCLK_DEN.
	 *
	 */
	CMUD1.cmu->GCLK_NUM.R = SPC5_GTM_CMU_GCLK_NUM;
	CMUD1.cmu->GCLK_NUM.R = SPC5_GTM_CMU_GCLK_NUM;
	CMUD1.cmu->GCLK_DEN.R = SPC5_GTM_CMU_GCLK_DEN;

	/*  Configure EGU subunit (External Clock Generation Unit) */
#if(SPC5_GTM_CMU_EGU_EN_ECLK0 == TRUE)
	CMUD1.cmu->GTM_CMU_ECLK_REG(0,NUM).R = SPC5_GTM_CMU_EGU_ECLK0_NUM;
	CMUD1.cmu->GTM_CMU_ECLK_REG(0,DEN).R = SPC5_GTM_CMU_EGU_ECLK0_DEN;

#endif

#if(SPC5_GTM_CMU_EGU_EN_ECLK1 == TRUE)
	CMUD1.cmu->GTM_CMU_ECLK_REG(1,NUM).R = SPC5_GTM_CMU_EGU_ECLK1_NUM;
	CMUD1.cmu->GTM_CMU_ECLK_REG(1,DEN).R = SPC5_GTM_CMU_EGU_ECLK1_DEN;
#endif

#if(SPC5_GTM_CMU_EGU_EN_ECLK2 == TRUE)
	CMUD1.cmu->GTM_CMU_ECLK_REG(2,NUM).R = SPC5_GTM_CMU_EGU_ECLK2_NUM;
	CMUD1.cmu->GTM_CMU_ECLK_REG(2,DEN).R = SPC5_GTM_CMU_EGU_ECLK2_DEN;
#endif

	/*Configure the FXU (Fixed Clock  Generation) subunit */
#if(SPC5_GTM_CMU_FXCLK_ENABLE == TRUE)
	CMUD1.cmu->FXCLK_CTRL.R = (uint32_t)(SPC5_GTM_CMU_FXCLK_SEL);
#endif

	/* Configure the CFGU (Configurable Clock  Generation) subunit */
#if(SPC5_GTM_CMU_CFGU_EN_CLK0 == TRUE)
	CMUD1.cmu->CLK_0_CTRL.R = SPC5_GTM_CMU_CFGU_CLK_DIV0 - 1U;
#endif

#if(SPC5_GTM_CMU_CFGU_EN_CLK1 == TRUE)
	CMUD1.cmu->CLK_1_CTRL.R = SPC5_GTM_CMU_CFGU_CLK_DIV1 - 1U;
#endif

#if(SPC5_GTM_CMU_CFGU_EN_CLK2 == TRUE)
	CMUD1.cmu->CLK_2_CTRL.R = SPC5_GTM_CMU_CFGU_CLK_DIV2 - 1U;
#endif

#if(SPC5_GTM_CMU_CFGU_EN_CLK3 == TRUE)
	CMUD1.cmu->CLK_3_CTRL.R = SPC5_GTM_CMU_CFGU_CLK_DIV3 - 1U;
#endif

#if(SPC5_GTM_CMU_CFGU_EN_CLK4 == TRUE)
	CMUD1.cmu->CLK_4_CTRL.R = SPC5_GTM_CMU_CFGU_CLK_DIV4 - 1U;
#endif

#if(SPC5_GTM_CMU_CFGU_EN_CLK5 == TRUE)
	CMUD1.cmu->CLK_5_CTRL.R = SPC5_GTM_CMU_CFGU_CLK_DIV5 - 1U;
#endif

#if(SPC5_GTM_CMU_CFGU_EN_CLK6 == TRUE)
    	GTM_CMU.CLK_6_CTRL.R = SPC5_GTM_CMU_CFGU_CLK_DIV6 - 1U;
#elif(SPC5_GTM_CMU_CFGU_CLK6_SEL == TRUE)
    	CMUD1.cmu->CLK_6_CTRL.R = SPC5_GTM_CMU_CFGU_CLKx_SEL_BIT;
#endif

#if(SPC5_GTM_CMU_CFGU_EN_CLK7 == TRUE)
    	GTM_CMU.CLK_7_CTRL.R = SPC5_GTM_CMU_CFGU_CLK_DIV7 - 1U;
#elif(SPC5_GTM_CMU_CFGU_CLK7_SEL == TRUE)
    	CMUD1.cmu->CLK_7_CTRL.R = SPC5_GTM_CMU_CFGU_CLKx_SEL_BIT;
#endif
 }

 /**
 * @brief   Start CMU peripheral
 *
 * @param[in] cmud           GTM CMU driver pointer
 *
 * @api
 */
void gtm_cmuStart(GTM_CMUDriver *cmud) {
	/*Enable All selected Clocks*/
	cmud->cmu->CLK_EN.R = SPC5_GTM_CMU_CLK_EN;
	cmud->cmu_gclk_en = SPC5_GTM_CMU_CLK_EN;
}

/**
 * @brief   Stop CMU peripheral
 *
 * @param[in] cmud           GTM CMU driver pointer
 *
 * @api
 */
void gtm_cmuStop(GTM_CMUDriver *cmud) {
	/*Disable All Clocks*/
	cmud->cmu->CLK_EN.R = 0x00555555;
	cmud->cmu_gclk_en = 0x0;
}


static uint32_t gtm_cmuGetGCLK(GTM_CMUDriver *cmud) {
	uint32_t num;
	uint32_t den;
	uint32_t sys_clk;

	num = cmud->cmu->GCLK_NUM.R;
	den = cmud->cmu->GCLK_DEN.R;
	sys_clk = SPC5_GTM_CMU_SYSTEM_CLOCK;

	return ((den * sys_clk) / num);
}

static uint32_t gtm_cmuGetExternalClock(GTM_CMUDriver *cmud, uint8_t ex_index) {
	uint32_t sys_clk;
	uint32_t num;
	uint32_t den;
	uint32_t value;

	switch (ex_index) {
	case 0:
		if (cmud->cmu->CLK_EN.B.EN_ECLK0 == 3U) {
			sys_clk = SPC5_GTM_CMU_SYSTEM_CLOCK;
			num = cmud->cmu->GTM_CMU_ECLK_REG(0,NUM).R;
			den = cmud->cmu->GTM_CMU_ECLK_REG(0,DEN).R;

			value = ((den * sys_clk) / (2UL * num));
		} else {
			value = 0UL;
		}
		break;
	case 1:
		if (cmud->cmu->CLK_EN.B.EN_ECLK1 == 3U) {
			sys_clk = SPC5_GTM_CMU_SYSTEM_CLOCK;
			num = cmud->cmu->GTM_CMU_ECLK_REG(1,NUM).R;
			den = cmud->cmu->GTM_CMU_ECLK_REG(1,DEN).R;

			value = ((den * sys_clk) / (2UL * num));
		} else {
			value = 0UL;
		}
		break;
	case 2:
		if (cmud->cmu->CLK_EN.B.EN_ECLK2 == 3U) {
			sys_clk = SPC5_GTM_CMU_SYSTEM_CLOCK;
			num = cmud->cmu->GTM_CMU_ECLK_REG(2,NUM).R;
			den = cmud->cmu->GTM_CMU_ECLK_REG(2,DEN).R;

			value = ((den * sys_clk) / (2UL * num));
		} else {
			value = 0UL;
		}
		break;
	default:
		value = 0UL;
	}

	return value;
}

static uint32_t gtm_cmuGetCFGUClock(GTM_CMUDriver *cmud, uint8_t cmu_clk) {
	uint32_t sys_clk;
	uint32_t count;
	uint32_t value;

	sys_clk = gtm_cmuGetGCLK(cmud);

	switch (cmu_clk) {
	case 0:
		if (cmud->cmu->CLK_EN.B.EN_CLK0 == 3U) {
			count = (cmud->cmu->CLK_0_CTRL.R + 1UL);
			value = sys_clk / count;
		} else {
			value = 0UL;
		}
		break;
	case 1:
		if (cmud->cmu->CLK_EN.B.EN_CLK1 == 3U) {
			count = (cmud->cmu->CLK_1_CTRL.R + 1UL);
			value = sys_clk / count;
		} else {
			value = 0UL;
		}
		break;
	case 2:
		if (cmud->cmu->CLK_EN.B.EN_CLK2 == 3U) {
			count = (cmud->cmu->CLK_2_CTRL.R + 1UL);
			value = sys_clk / count;
		} else {
			value = 0UL;
		}
		break;
	case 3:
		if (cmud->cmu->CLK_EN.B.EN_CLK3 == 3U) {
			count = (cmud->cmu->CLK_3_CTRL.R + 1UL);
			value = sys_clk / count;
		} else {
			value = 0UL;
		}
		break;
	case 4:
		if (cmud->cmu->CLK_EN.B.EN_CLK4 == 3U) {
			count = (cmud->cmu->CLK_4_CTRL.R + 1UL);
			value = sys_clk / count;
		} else {
			value = 0UL;
		}
		break;
	case 5:
		if (cmud->cmu->CLK_EN.B.EN_CLK5 == 3U) {
			count = (cmud->cmu->CLK_5_CTRL.R + 1UL);
			value = sys_clk / count;
		} else {
			value = 0UL;
		}
		break;
	case 6:
		if (cmud->cmu->CLK_EN.B.EN_CLK6 == 3U) {
			count = (cmud->cmu->CLK_6_CTRL.R + 1UL);
			value = sys_clk / count;
		} else {
			value = 0UL;
		}
		break;
	case 7:
		if (cmud->cmu->CLK_EN.B.EN_CLK7 == 3U) {
			count = (cmud->cmu->CLK_7_CTRL.R + 1UL);
			value = sys_clk / count;
		} else {
			value = 0UL;
		}
		break;
	default:
		value = 0UL;
	}

	return value;
}

static uint32_t gtm_cmuGetFixedClock(GTM_CMUDriver *cmud, uint8_t fx_clk) {
	uint32_t clk_source;
	uint32_t fxclk_sel;
	uint32_t sys_clk;
	uint32_t fx_clock;


	fxclk_sel = cmud->cmu->FXCLK_CTRL.R;
	sys_clk = gtm_cmuGetGCLK(cmud);

	switch(fxclk_sel) {
	case 0:
		clk_source =  sys_clk;
		break;
	case 1:
		clk_source = gtm_cmuGetCFGUClock(cmud, 0U);
		break;
	case 2:
		clk_source = gtm_cmuGetCFGUClock(cmud, 1U);
		break;
	case 3:
		clk_source = gtm_cmuGetCFGUClock(cmud, 2U);
		break;
	case 4:
		clk_source = gtm_cmuGetCFGUClock(cmud, 3U);
		break;
	case 5:
		clk_source = gtm_cmuGetCFGUClock(cmud, 4U);
		break;
	case 6:
		clk_source = gtm_cmuGetCFGUClock(cmud, 5U);
		break;
	case 7:
		clk_source = gtm_cmuGetCFGUClock(cmud, 6U);
		break;
	case 8:
		clk_source = gtm_cmuGetCFGUClock(cmud, 7U);
		break;
	default:
		clk_source = 0;
	}

	switch (fx_clk){
	case 0:
		fx_clock = clk_source; /* 2^0 */
		break;
	case 1:
		fx_clock = clk_source / 16UL; /* 2^4 */
		break;
	case 2:
		fx_clock = clk_source / 256UL; /* 2^8 */
		break;
	case 3:
		fx_clock = clk_source / 4096UL; /* 2^12 */
		break;
	case 4:
		fx_clock = clk_source / 65536UL; /* 2^16 */
		break;
	default:
		fx_clock = 0UL;
		break;
	}

	return fx_clock;
}

/**
 * @brief   Return a Clock Frequency
 *
 * @param[in] cmud           GTM CMU driver pointer
 *
 * @param[in] clock_source   GTM CMU clock source type (cfgu, fixed and external clock subunit)
 *
 * @param[in] clock_index    GTM CMU clock index (index of the subunit clock)
 *
 * @return clock frequency
 *
 * @sa
 * SPC5_GTM_CMU_CFGU_CLK, SPC5_GTM_CMU_FXU_CLK, SPC5_GTM_CMU_EGU_CLK, SPC5_GMT_CMU_GLOBAL_CLK
 * <br>
 * <br>
 * SPC5_GTM_CMU_CLK0, SPC5_GTM_CMU_CLK1, SPC5_GTM_CMU_CLK2, SPC5_GTM_CMU_CLK3, SPC5_GTM_CMU_CLK4, SPC5_GTM_CMU_CLK5,
 * SPC5_GTM_CMU_CLK6, SPC5_GTM_CMU_CLK7
 * <br>
 * <br>
 * SPC5_GTM_CMU_FXCLK0, SPC5_GTM_CMU_FXCLK1, SPC5_GTM_CMU_FXCLK2, SPC5_GTM_CMU_FXCLK3, SPC5_GTM_CMU_FXCLK4
 * <br>
 * <br>
 * SPC5_GTM_CMU_EXCLK0, SPC5_GTM_CMU_EXCLK1, SPC5_GTM_CMU_EXCLK2
 *
 * @api
 */
uint32_t gtm_cmuGetClock(GTM_CMUDriver *cmud, uint32_t clock_source, uint8_t clock_index) {
	uint32_t clock;

	switch(clock_source){
	case SPC5_GMT_CMU_GLOBAL_CLK:
		clock = gtm_cmuGetGCLK(cmud);
		break;
	case SPC5_GTM_CMU_CFGU_CLK:
		clock = gtm_cmuGetCFGUClock(cmud, clock_index);
		break;
	case SPC5_GTM_CMU_FXU_CLK:
		clock = gtm_cmuGetFixedClock(cmud, clock_index);
		break;
	case SPC5_GTM_CMU_EGU_CLK:
		clock = gtm_cmuGetExternalClock(cmud, clock_index);
		break;
	default:
		clock = 0UL;
		break;
	}

	return clock;
}
/** @} */

