/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Clock
**  Filename        :  clock.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "clock.h"
/* No other .h files shall be added here */


/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/

/******************************************************************************
**   Function    : clockInit
**
**   Description:
**    Clock initialization.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void clockInit(void) {

  /* The system is switched in the final run mode (default is RUN0 mode).*/
  if (SPCSetRunMode(SPC5_FINAL_RUNMODE) == CLOCK_FAILED) {
    SPC5_CLOCK_FAILURE_HOOK();
  }
}

/******************************************************************************
**   Function    : SPCSetRunMode
**
**   Description:
**    Switches the system to the specified run mode.
**
**   Parameters :
**    [in] uint8_T mode : one of the possible run modes
**
**   Returns:
**    CLOCK_SUCCESS         - if the switch operation has been completed.
**    CLOCK_FAILED          - if the switch operation failed.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint8_T SPCSetRunMode(uint8_T mode) {
uint8_T retVal = CLOCK_FAILED;
uint32_T cntTmeOut = 0u;

  /* Clearing status register bits */
  MC_ME.IS.R = 0x3FU;

  /* Starts a transition process.*/
  MC_ME.MCTL.R = SPC5_ME_MCTL_MODE(mode) | SPC5_ME_MCTL_KEY;
  MC_ME.MCTL.R = SPC5_ME_MCTL_MODE(mode) | SPC5_ME_MCTL_KEY_INV;

  /* Waits for the mode switch or an error condition.*/
  while ((MC_ME.IS.R == 0U) && (cntTmeOut < SPC_SET_RUN_MODE_TOUT)) {
    cntTmeOut++;
  }
  /* Check if no error during mode switch */
  if (MC_ME.IS.B.I_MTC == 1U) {
    retVal = CLOCK_SUCCESS;
  } else {
    retVal = CLOCK_FAILED;
  }

  return retVal;
    
}

/******************************************************************************
**   Function    : SPCSetPeripheralClockMode
**
**   Description:
**    Changes the clock mode of a peripheral.
**
**   Parameters :
**    [in] uint32_T n : index of the PCTL register
**    [in] uint32_T pctl : new value for the PCTL register
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPCSetPeripheralClockMode(uint32_T n, uint32_T pctl) {
  uint8_T mode;

  MC_ME.PCTL[n].R = (uint8_T)pctl;
  mode = (uint8_T)MC_ME.MCTL.B.TARGET_MODE;
  if(SPCSetRunMode(mode) == CLOCK_FAILED) {
    SPC5_CLOCK_FAILURE_HOOK();
  }
}

/******************************************************************************
**   Function    : SPCSetPerClockMode_MiniBoot
**
**   Description:
**    Changes the clock mode of a peripheral.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPCSetPerClockMode_MiniBoot(void) {
  uint8_T mode;

#ifdef _BUILD_PORT_
  /* SIUL */
  MC_ME.PCTL[SPC5_SIUL2_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC2;
  MC_ME.PCTL[SPC5_SIUL2_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  
#endif

#ifdef _BUILD_PIT_
  /* PIT0 */
  MC_ME.PCTL[SPC5_PIT0_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_PIT0_PCTL].B.LP_CFG = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  /* PIT1 */
  MC_ME.PCTL[SPC5_PIT1_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_PIT1_PCTL].B.LP_CFG = (uint8_T)SPC5_ME_PCTL_LP_PC2;

#endif

#ifdef _BUILD_SPI_
  /* DSPI5 */
  MC_ME.PCTL[SPC5_DSPI5_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_DSPI5_PCTL].B.LP_CFG = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  
#endif

#ifdef _BUILD_CAN_
  /* MCAN1 */
  MC_ME.PCTL[SPC5_CAN_SUB_0_M_CAN_1_PTCL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_CAN_SUB_0_M_CAN_1_PTCL].B.LP_CFG = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  /* MCAN2 */
  MC_ME.PCTL[SPC5_CAN_SUB_0_M_CAN_2_PTCL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_CAN_SUB_0_M_CAN_2_PTCL].B.LP_CFG = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  /* CAN_RAM_CTRL */
  MC_ME.PCTL[SPC5_CAN_SUB_0_RAM_PTCL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_CAN_SUB_0_RAM_PTCL].B.LP_CFG = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  /* TTCAN */
  MC_ME.PCTL[SPC5_TTCAN_PTCL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_TTCAN_PTCL].B.LP_CFG = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  
#endif  
  
  mode = (uint8_T)MC_ME.MCTL.B.TARGET_MODE;
  if(SPCSetRunMode(mode) == CLOCK_FAILED) {
    SPC5_CLOCK_FAILURE_HOOK();
  }
}

/******************************************************************************
**   Function    : SPCSetPerClockMode_Appl
**
**   Description:
**    Changes the clock mode of a peripheral.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPCSetPerClockMode_Appl(void) {
  uint8_T mode;

#ifdef _BUILD_GTM_
  /* GTM */
  MC_ME.PCTL[SPC5_GTMINT_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_GTMINT_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;

#endif

#ifdef _BUILD_ADC_

#if (BOARD_TYPE == BOARD_EISB8F_A) /* SAR0/2/4/6 managed by STM GTM driver */
  /* SARADCB_SV */
  MC_ME.PCTL[SPC5_SARADC12_SV_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_SARADC12_SV_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;

  /* SDADC0 */
  MC_ME.PCTL[SPC5_SDADC0_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_SDADC0_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  
  /* SDADC3*/
  MC_ME.PCTL[SPC5_SDADC3_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_SDADC3_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;

  /* SARADC0 */
  MC_ME.PCTL[SPC5_SARADC12_0_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_SARADC12_0_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  /* SARADC2 */
  MC_ME.PCTL[SPC5_SARADC12_2_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_SARADC12_2_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  /* SARADC4 */
  MC_ME.PCTL[SPC5_SARADC12_4_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_SARADC12_4_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;
  /* SARADC6 */
  MC_ME.PCTL[SPC5_SARADC12_6_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_SARADC12_6_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;

#endif /* BOARD_TYPE */
#endif /* _BUILD_ADC _ */

#ifdef _BUILD_DMA_
  /* DMAMUX_0  */
  MC_ME.PCTL[SPC5_EDMA_MUX_PCTL].B.RUN_CFG = (uint8_T)SPC5_ME_PCTL_RUN_PC1;
  MC_ME.PCTL[SPC5_EDMA_MUX_PCTL].B.LP_CFG  = (uint8_T)SPC5_ME_PCTL_LP_PC2;

#endif  

  mode = (uint8_T)MC_ME.MCTL.B.TARGET_MODE;
  if(SPCSetRunMode(mode) == CLOCK_FAILED) {
    SPC5_CLOCK_FAILURE_HOOK();
  }
}

/******************************************************************************
**   Function    : SPCGetSystemClock
**
**   Description:
**    Returns the system clock under the current run mode.
**
**   Parameters :
**    void
**
**   Returns:
**    uint32_T retVal: the system clock in Hertz.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint32_T SPCGetSystemClock(void) {
  uint32_T retVal = 0u;
  uint32_T sysclk;
  sysclk = MC_ME.GS.B.S_SYSCLK;

  switch (sysclk) {
  case SPC5_ME_GS_SYSCLK_IRC:
    retVal =  SPC5_IRC_CLK / (MC_CGM.SC_DC[0].B.DIV + 1UL);
    break;
  case SPC5_ME_GS_SYSCLK_XOSC:
    retVal = SPC5_XOSC_CLK / (MC_CGM.SC_DC[0].B.DIV + 1UL);
    break;
  case SPC5_ME_GS_SYSCLK_PLL0PHI:
    retVal = SPC5_PLL0_PHI_CLK / (MC_CGM.SC_DC[0].B.DIV + 1UL);
    break;
  case SPC5_ME_GS_SYSCLK_PLL1PHI:
    retVal = SPC5_PLL1_PHI_CLK / (MC_CGM.SC_DC[0].B.DIV + 1UL);
    break;
  default:
    retVal = 0u;
    break;
  }

  return retVal;

}

/******************************************************************************
**   Function    : SPCGetPeripheralClock
**
**   Description:
**    Returns the peripheral clock.
**
**   Parameters :
**    [in] peripheral_t peripheral : identifier of the peripheral related to 
**                                   the clock to be returned.
**
**   Returns:
**    uint32_T retVal: the peripheral clock in Hertz.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint32_T SPCGetPeripheralClock(peripheral_t peripheral) {
uint32_T retVal = 0u;

  switch (peripheral) {
  case SPC5_PERIPHERAL_PIT0:
    retVal = SPC5_PER_CLK;
    break;
  default:
    retVal = 0u;
    break;
  }

return retVal;
}

/******************************************************************************
**   Function    : SPCSetPerClkDiv_AC0
**
**   Description:
**    Configures the Auxiliary clock 0 divider. The peripherals connected to it
**    are defined by an ID.
**    NOTE: if MC_CGM_AC0_DC.DE is 0 (clock disabled) this API enables it
**          to perform MC_CGM_AC0_DC.DIV change and keeps it enabled.
**
**   Parameters :
**    uint8_T ac0PerClkID : ID of the peripheral connected to the AUX clock.
**    uint32_T ac0DcRegValue : MC_CGM_AC0_DC register value. 
**
**   Returns:
**    NO_ERROR : configuration correctly executed
**    ARG_ERROR : Input arguments outside the boundaries
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T SPCSetPerClkDiv_AC0(uint8_T ac0PerClkID, uint32_T ac0DcRegValue)
{
    int16_T retCode = NO_ERROR;
    uint32_T divValue = (ac0DcRegValue & SPC5_MC_CGM_AC0_DC_DE_MASK)+1U; /*from RM: clock divisor=DIV+1*/
    
    if (ac0PerClkID > SPC5_AC0_DC_MAX_PER_ID)
    {
        retCode=ARG_ERROR;
    }
    else if ((divValue > SPC5_AC0_DC_DIV_MAXVALUE)||(divValue < SPC5_AC0_DC_DIV_MINVALUE))
    {
        retCode=ARG_ERROR;
    }
    else 
    {
        MC_CGM.AC0_DC[ac0PerClkID].R = (ac0DcRegValue<<SPC5_MC_CGM_AC0_DC_DIV_OFFSET); /* Setting DE and DIV*/
    }
    return retCode;


}
/****************************************************************************
 ****************************************************************************/

