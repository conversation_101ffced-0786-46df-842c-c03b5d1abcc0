/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_PIT.c
**  Created on      :  07-Feb-2022 12:22:00
**  Original author :  Mocci A
******************************************************************************/


#ifdef _BUILD_SAFETYMNGR_PIT_

#ifndef _BUILD_SAFETYMNGR_
#error PIT Safety Module enabled without _BUILD_SAFETYMNGR_ macro enabled
#endif /*  _BUILD_DIAGCANMGM_ */
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_PIT.h"

/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
//static uint32_T PIT_triggers_old;
#ifdef _TEST_PIT_TRIGGER_MINMAX_
static uint32_T PIT_triggers_Diff_MAX = 10u;
static uint32_T PIT_triggers_Diff_MIN = 10u;
#endif


/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_PIT_Init
**
**   Description:
**    This function initializes internal variables for PIT safety mechanism.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SafetyMngr_PIT_Init(void)
{
    /* code construction start */

    /* code construction end */
}

/******************************************************************************
**   Function    : SafetyMngr_PIT_Check10ms
**
**   Description:
**    This function implements safety mechanism for PIT peripheral according to 
**    "AN4446 - SPC574K72xx safety manual" par. "3.3.15 Periodic Interrupt Timer 
"     (PIT)"
""    It checks PIT0 CH0 ISR trigger and compares it with an expected 
**    value within.a timing window, and if two values do not mach, a safety 
**    mechanism if performed
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SafetyMngr_PIT_Check10ms(void)
{
    /* code construction start */
vuint32_T PIT_triggers_new;
static uint32_T PIT_triggers_old;
uint32_T PIT_triggers_ovfl;
uint32_T PIT_triggers_ovfl2;
uint32_T PIT_triggers_Diff = 0u;

    /*
    3.3.15 Periodic Interrupt Timer (PIT)
    Assumption: PIT operations (for example, the number of periodic triggers) are checked
    and compared against the expected values every FTTI.
    */

    /* Get PIT number of ISRs at check start */
    PIT_triggers_new = PIT_1ms_ISR_cnt;


    if (PIT_triggers_old < PIT_triggers_new)
    {
        PIT_triggers_Diff = (PIT_triggers_new - PIT_triggers_old);
        if(( PIT_triggers_Diff >= (PIT_TRG_EXPECTED - 1u)) && (PIT_triggers_Diff <= (PIT_TRG_EXPECTED + 1u)))
        {
            /* counted PIT ISRs are equals to expected ones */
        }
        else
        {
//            SafetyMngr_ReportError((uint32_T)SAFE_ERR_PIT_ISR_COUNT_MISMATCH,FALSE);
        }
#ifdef _TEST_PIT_TRIGGER_MINMAX_
        if (PIT_triggers_Diff > PIT_triggers_Diff_MAX)
        {
            PIT_triggers_Diff_MAX = PIT_triggers_Diff;
        }
        if (PIT_triggers_Diff < PIT_triggers_Diff_MIN)
        {
            PIT_triggers_Diff_MIN = PIT_triggers_Diff;
        }
#endif
    }
    else // (PIT_triggers_old >= PIT_triggers_new)
    {
        PIT_triggers_ovfl = MAX_uint32_T - PIT_triggers_old;
        PIT_triggers_ovfl2 = PIT_triggers_ovfl + PIT_triggers_new + 1u;
        if ((PIT_triggers_ovfl2 >= (PIT_TRG_EXPECTED -1u)) && (PIT_triggers_ovfl2 <= (PIT_TRG_EXPECTED +1u)))
        {
           /* counted PIT ISRs are equals to expected ones */
        }
        else
        {
            SafetyMngr_ReportError((uint32_T)SAFE_ERR_PIT_ISR_OVFCOUNT_MISMATCH,FALSE);
        }
    }

    /* update counters for next loop */
    PIT_triggers_old = PIT_triggers_new;

    /*
    3.3.15 Periodic Interrupt Timer (PIT)
    If not covered by other means, software shall read back the PIT configuration and compare
    it with the expected configuration (for example, check for enabled channels and compared
    values, and so on.).
    */
    SafetyMngr_PIT_RegCheck();

    /* code construction end */

}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_PIT_Check10ms
**
**   Description:
**    This function reads PIT registers and compares them with expected ones; 
**    in case of mismatch a safety correction is applied
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SafetyMngr_PIT_RegCheck(void)
{
    /* code construction start */
uint32_T lLDVAL;
uint32_T lTCTRL;

    lLDVAL = PIT_0.TIMER[PIT0_CHANNEL0].LDVAL.R;
    lTCTRL = PIT_0.TIMER[PIT0_CHANNEL0].TCTRL.R;

    if ((lLDVAL != PIT0_TIMEOUT0) || (lTCTRL != PIT_COUNT_ISR))
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_PIT_WRONG_CONFIGURATION,FALSE);
    }
    else
    {
        /* Correct configuration */
    }

    /* code construction end */

}



#endif // _BUILD_SAFETYMNGR_PIT_
/****************************************************************************
 ****************************************************************************/

