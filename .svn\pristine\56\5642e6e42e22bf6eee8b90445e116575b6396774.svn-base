/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef _BUILD_FLASHMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
/* */

/*-----------------------------------*
 * CALIBRATIONS
 *-----------------------------------*/
#pragma ghs section rodata=".calib"

//time to start crc count in the background task (ms)
CALQUAL CALQUAL_POST uint16_T TESTCRCPERIODCNT =  600u;   // (600*100) ms

// waiting time before restarting the new partial CRC calculation
CALQUAL CALQUAL_POST uint8_T  WAITCNT =  100u;


/****************************************************************************
 ****************************************************************************/

#endif /* _BUILD_FLASHMGM_ */
