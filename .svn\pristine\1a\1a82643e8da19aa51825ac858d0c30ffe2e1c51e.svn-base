/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
#ifndef _MSPARKCMD_EEP_C_
#define _MSPARKCMD_EEP_C_

#include "msparkcmd_out.h"

///EE Max SA Err
uint8_T EESACmdInLevErrNoMax = 0u;
/* Counter of ETPU errors */
uint16_T EESACmdInLevErrSum = 0u;

#endif /* _MSPARKCMD_EEP_C_ */

