/*****************************************************************************************************************/
/* $HeadURL:: https://172.26.1.29/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_RS_35_PI_0204/tree/DD/CANMGMI#$  */
/* $Revision:: 141485                                                                                         $  */
/* $Date:: 2020-12-18 10:06:12 +0100 (ven, 18 dic 2020)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmIn
**  Filename        :  CanMgmIn_BR_calib.c
**  Created on      :  07-jul-2023 09:37:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_CANMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "canmgmin_BR.h"

#pragma ghs section rodata=".calib"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
/// Battery voltage lower threshold to enable CAN diagnosis
CALQUAL CALQUAL_POST uint16_T CANVBATTHRMIN = 0u;     //(uint16_T)(6.5*16); Messa a zero temporaneamente

CALQUAL CALQUAL_POST uint8_T THRCNTRPMCANBLOCKED  = 4u;

// Timeout to disable DIAG_RPM after a gear change
CALQUAL CALQUAL_POST uint8_T TIMDIAGRPMDIS = 1000u/10u;

CALQUAL CALQUAL_POST int16_T TAIRREC = (60 * 16);

CALQUAL CALQUAL_POST int16_T TWATERREC = (110 * 16);

CALQUAL CALQUAL_POST int16_T BKTOILPOIL[BKTOILPOIL_dim] = 
{
    20*16, 40*16, 80*16, 100*16, 120*16
};

CALQUAL CALQUAL_POST uint16_T BKRPMPOIL[BKRPMPOIL_dim] = 
{
    1000u, 1500u, 2000u, 2500u, 3000u, 3500u, 4000u, 4500u, 5000u, 6000u, 7000u, 8000u
};

CALQUAL CALQUAL_POST uint16_T TBPOIL[BKTOILPOIL_dim*BKRPMPOIL_dim] = 
{
    568U,     568U,     568U,     407U,     294U,
    801U,     801U,     801U,     566U,     448U,
    1029U,    1029U,    1029U,    737U,     545U,
    1229U,    1229U,    1229U,    899U,     655U,
    1329U,    1329U,    1329U,    1044U,    758U,
    1359U,    1359U,    1359U,    1167U,    860U,
    1349U,    1349U,    1349U,    1198U,    919U,
    1341U,    1341U,    1341U,    1213U,    932U,
    1324U,    1324U,    1324U,    1196U,    952U,
    1288U,    1288U,    1288U,    1126U,    909U,
    1236U,    1236U,    1236U,    1065U,    876U,
    1216U,    1216U,    1216U,    1085U,    881U
};

CALQUAL CALQUAL_POST uint16_T LOADREC = (100u * 128u);

CALQUAL CALQUAL_POST uint8_T ENGATEWAY = 0u;

CALQUAL CALQUAL_POST int16_T THDELTALOADCAN = (100*128);

CALQUAL CALQUAL_POST uint8_T THDEBPTFAULTKEYSIG = 3u;

CALQUAL CALQUAL_POST uint8_T LOADCANVALASREC = 0u;
CALQUAL CALQUAL_POST uint8_T TAIRCANVALASREC = 0u;
CALQUAL CALQUAL_POST uint8_T TWATERCANVALASREC = 0u;

CALQUAL CALQUAL_POST uint8_T ENCRCALIVETEST = 0u; /* Enable test on alive counters and CRC */

CALQUAL CALQUAL_POST uint8_T NEWLOADSCALING = 0u;

CALQUAL CALQUAL_POST int8_T FOCYLBALCLREQ = -1;

CALQUAL CALQUAL_POST int8_T FOAFRCLACTIVE = -1;

#endif /* _BUILD_CANMGM_ */
/****************************************************************************
 ****************************************************************************/

