/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Timing
**  Filename        :  Timing_calib.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  SantoroR
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
#pragma ghs section rodata=".calib" 

//Enable timing debug
CALQUAL CALQUAL_POST uint8_T ENTESTTIMING = 0u;

//Enable timing ISR debug
CALQUAL CALQUAL_POST uint8_T ENTESTISRTIMING = 0u;

/****************************************************************************
 ****************************************************************************/


