/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  Flash_asynch.c
**  Created on      :  08-apr-2021 12:01:00
**  Original author :  CarboniM
******************************************************************************/

#ifdef  _BUILD_FLASH_

#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1u)
#pragma ghs section text=FLASH_TEXTRAM_SECTION
#endif

#pragma ghs startnomisra
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Flash.h"

#ifdef FLASHERASECBK
extern uint32_T FlashEraseCallback_cnt;
#endif

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : FlashErase_Asynch
**
**   Description:
**    Calls the FlashErase and check the completation of the execution by calling the FlashCheckStatus
**                              
**   Parameters :
**    [in] uint32_T blkslctLow : bit-mapped value to indicate the array blocks in low address space belonging to the range address
**    [in] uint32_T blkslctMiddle : bit-mapped value to indicate the array blocks in middle address space belonging to the range address
**    [in] uint32_T blkslctHigh : bit-mapped value to indicate the array blocks in high address space belonging to the range address
**    [in] NLARGE_BLOCK_SEL blkslctLarge : number of bloks of large section
**    [in] uint8_T callback_module : callback function
**
**   Returns:
**    C55_OK                       - Successful completion
**    C55_ERROR_ERASE_OPTION       - Invalid erase option
**    C55_ERROR_BUSY               - New erase operation cannot be performed 
**                                   because there is program/erase sequence 
**                                   in progress on the Flash module
**    C55_ERROR_FACTORY_OP         - The factory erase could not be performed
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint32_T FlashErase_Asynch(uint32_T blkslctLow, uint32_T blkslctMiddle, uint32_T blkslctHigh, NLARGE_BLOCK_SEL blkslctLarge, uint8_T callback_module)
{
uint32_T returnCode = C55_OK;
uint32_T opResult;
CONTEXT_DATA dummyCtxData; // Unused for C55_MODE_OP_ERASE

     returnCode = ((PFLASHERASE)FlashErase_C)( &ssdConfig_BK0A0, C55_ERASE_MAIN, blkslctLow, blkslctMiddle, blkslctHigh, blkslctLarge);
    
     if (C55_OK != returnCode)
     {
        
     } else { // returnCode == C55_OK 
  
         /* Call FlashCheckStatus() to check status of the progress */
         while (C55_INPROGRESS == ((PFLASHCHECKSTATUS)FlashCheckStatus_C)(&ssdConfig_BK0A0,C55_MODE_OP_ERASE,&opResult,&dummyCtxData))
         {
         
#ifdef FLASHERASECBK
            FlashEraseCallback(callback_module);
#endif
         }
#ifdef FLASHERASECBK
         FlashEraseCallback_cnt = 0u;
#endif    
         if (C55_OK != opResult)
         {
            returnCode = opResult;
         }
     }

     return returnCode;

}

/******************************************************************************
**   Function    : FlashProgram_Asynch
**
**   Description:
**    Calls the FlashProgram and check the completation of the execution by calling the FlashCheckStatus,
**    that calls FlashProgram one more time for multi-programmable size.                                
**
**   Parameters :
**    [in] uint32_T dest : destination address to be programmed
**    [in] uint32_T size : size of the flash region to be programmed
**    [in] uint32_T source : source program buffer address
**
**   Returns:
**    C55_OK                       - Successful completion
**    C55_ERROR_ALTERNATE          - This error occurs when user wants to perform 
**                                   factory program via the alternate interface
**    C55_ERROR_ALIGNMENT          - This error indicates that dest/size/source 
**                                   isn’t properly aligned
**    C55_ERROR_BUSY               - There is program operation is in progress or 
**                                   erase operation is going on and not in suspended state
**    C55_ERROR_FACTORY_OP         - The factory program could not be performed 
**                                   due to the data at the 'diary' location in the 
**                                   UTest NVM contains at least one zero
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint32_T FlashProgram_Asynch( uint32_T dest, uint32_T size, uint32_T source)
{
uint32_T returnCode = C55_OK;
uint32_T opResult;

    /* start FLASH programming... */
    returnCode = ((PFLASHPROGRAM)FlashProgram_C)(&ssdConfig_BK0A0, FALSE, dest, size, source, &pgmCtxData);
    if ( C55_OK != returnCode ){

    } 
    else
    {    
        /* Call FlashCheckStatus() to check status of the progress */
        while (C55_INPROGRESS == ((PFLASHCHECKSTATUS)FlashCheckStatus_C)(&ssdConfig_BK0A0,C55_MODE_OP_PROGRAM,&opResult,&pgmCtxData))
        {

#ifdef FLASHPRGCBK
            FlashProgramCallback();   
#endif
        }
    
        if (C55_OK != opResult)
        {
            returnCode = opResult;
        }
    }

    return returnCode;

}

/******************************************************************************
**   Function    : FlashProgramVerify_Asynch
**
**   Description:
**    Calls the ProgramVerify and check the completation of the execution by calling the FlashCheckStatus,
**    that calls ProgramVerify one more time for large size. 
**
**   Parameters :
**    [in] uint32_T address : address to be verified in flash memory
**    [in] uint32_T size : size of the flash region to be verify
**    [in] uint32_T source : verify source buffer address
**
**   Returns:
**    C55_OK                       - Successful completion
**    C55_ERROR_ALIGNMENT          - This error indicates that dest/size/source 
**                                   isn’t properly aligned
**    C55_ERROR_VERIFY             - There is a mismatch between destination 
**                                   and source data
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint32_T FlashProgramVerify_Asynch( uint32_T address, uint32_T size, uint32_T source)
{    
uint32_T returnCode = C55_OK;
uint32_T failAddress;
uint32_T failData;
uint32_T failSource;
uint32_T opResult;

    returnCode = ((PPROGRAMVERIFY)ProgramVerify_C)(&ssdConfig_BK0A0, address, size, source, &failAddress, &failData, &failSource, &pvCtxData);
    if ( C55_OK != returnCode ){

    } else { 
        /* Call FlashCheckStatus() to check status of the progress */
        while (C55_INPROGRESS == ((PFLASHCHECKSTATUS)FlashCheckStatus_C)(&ssdConfig_BK0A0,C55_MODE_OP_PROGRAM_VERIFY,&opResult,&pvCtxData))
        {

#ifdef FLASHPRGVERCBK
            FlashProgramVerifyCallback();   
#endif

        }

        if (C55_OK != opResult)
        {
            returnCode = opResult;
        }
    }
   
    return returnCode;
}

/******************************************************************************
**   Function    : FlashBlankCheck_Asynch
**
**   Description:
**    Calls the BlankCheck and check the completation of the execution by calling the FlashCheckStatus,
**    that calls ProgramVerify one more time for large size. 
**
**   Parameters :
**    [in] uint32_T address : start address of the range to be checked
**    [in] uint32_T size : size of the range to be checked
**
**   Returns:
**    C55_OK                       - Successful completion
**    C55_ERROR_ALIGNMENT          - The dest, size provided by user 
**                                   is not aligned by word
**    C55_ERROR_NOT_BLANK          - There is a non-blank area within 
**                                   targeted Flash range
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint32_T FlashBlankCheck_Asynch( uint32_T address, uint32_T size)
{    
uint32_T returnCode = C55_OK;
uint32_T failAddress;    
uint32_T failData;
uint32_T opResult;

    returnCode = ((PBLANKCHECK)BlankCheck_C)( &ssdConfig_BK0A0, address, size, &failAddress, &failData, &bcCtxData);
    if ( C55_OK != returnCode){

    } else { 
    
        /* Call FlashCheckStatus() to check status of the progress */
        while (C55_INPROGRESS == ((PFLASHCHECKSTATUS)FlashCheckStatus_C)(&ssdConfig_BK0A0,C55_MODE_OP_BLANK_CHECK,&opResult,&bcCtxData))
        {

#ifdef FLASHBLKCHKCBK
            FlashBlankCheckCallback();   
#endif
        }
    
        if (C55_OK != opResult)
        {
            returnCode = opResult;
        }
    }

    return returnCode;
}

/******************************************************************************
**   Function    : FlashChecksum_Asynch
**
**   Description:
**    Calls the Checksum and check the completation of the execution by calling the FlashCheckStatus,
**    that calls Checksum one more time for large size. 
**
**   Parameters :
**    [in] uint32_T dest : destination address to be summed in flash memory
**    [in] uint32_T size : size of the flash region to check sum
**    [out] uint32_T sum : return sum value
**
**   Returns:
**    C55_OK                       - Successful completion
**    C55_ERROR_ALIGNMENT          - This error indicates that dest/size 
**                                   isn’t properly aligned
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#ifdef USE_FLASHCHKSUM_ST_DRIVER
uint32_T FlashChecksum_Asynch(uint32_T dest, uint32_T size, uint32_T* sum)
{
uint32_T returnCode = C55_OK;
uint32_T opResult;

    returnCode =((PCHECKSUM)CheckSum_C)( &ssdConfig_BK0A0, dest, size, sum, &csCtxData);
    if ( C55_OK != returnCode ){

    } else {

        /* Call FlashCheckStatus() to check status of the progress */
        while (C55_INPROGRESS == ((PFLASHCHECKSTATUS)FlashCheckStatus_C)(&ssdConfig_BK0A0,C55_MODE_OP_CHECK_SUM,&opResult,&csCtxData))
        {

#ifdef FLASHCHKSUMCBK
            FlashChecksumCallback();   
#endif

        }

        if (C55_OK != opResult)
        {
            returnCode = opResult;
        }
    }

    return returnCode;
}
#endif

#pragma ghs endnomisra

#endif /*  _BUILD_FLASH_ */

/****************************************************************************
 ****************************************************************************/
