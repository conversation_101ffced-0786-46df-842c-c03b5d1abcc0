/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonAcqBufMgm.h
 **  Date:          21-Jan-2022
 **
 **  Model Version: 1.1794
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonAcqBufMgm_h_
#define RTW_HEADER_IonAcqBufMgm_h_
#include <string.h>
#ifndef IonAcqBufMgm_COMMON_INCLUDES_
# define IonAcqBufMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonAcqBufMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: ELD_EXPORT_DEFINE */
#define MAX_ION_CURRENT                1048560U                  /* Referenced by: '<S1>/BufferCopy' */

/* Maximum value for IonBuffer [uA]. */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonAcqBufMgm_initialize(void);

/* Exported entry point function */
extern void IonAcqBufMgm_EOA(void);

/* Exported entry point function */
extern void IonAcqBufMgm_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T DwellSampCyl[8];        /* '<S52>/Merge12' */

/* Number of samples corresponding to dwell time */
extern uint8_T IonAbsTdcEOA;           /* '<S38>/Merge4' */

/* Cylinder for last acquisition - task variable */
extern uint8_T IonDTEOA;               /* '<S38>/Merge1' */

/* Ion Acquisition step time - task variable */
extern uint16_T IonDThetaEOA;          /* '<S38>/Merge12' */

/* Ion Acquisition step angle - task variable */
extern uint16_T IonDThetaInvEOA;       /* '<S38>/Merge9' */

/* Inverse for ion Acquisition step angle - task variable */
extern uint16_T IonGainEOA;            /* '<S38>/Merge5' */

/* IonGain - task variable */
extern uint8_T KnockAbsCycles;         /* '<S38>/Merge7' */

/* Absolute engine cycle counter for knock analysis */
extern uint16_T NSampIonSpike;         /* '<S9>/Merge11' */

/* NSampIonSpike */
extern uint16_T NSampleMaxEOA;         /* '<S38>/Merge2' */

/* Ion Acquisition Max Sample Counter - task variable */
extern uint16_T NSampleStartEOA;       /* '<S38>/Merge3' */

/* Ion Acquisition Start Sample Counter - task variable */
extern uint16_T NrStartIonDMAIdxEOA;   /* '<S38>/Merge6' */

/* Near start ion index */
extern uint8_T PtFaultChannel[8];      /* '<S9>/Merge2' */

/* Ion channels punctual fault. */
extern uint16_T StartSpark;            /* '<S52>/Merge1' */

/* StartSpark sample index */
extern uint16_T StopOffsetSearch;      /* '<S52>/Merge3' */

/* Stop offset search sample index */
extern uint16_T VtStartSpark[8];       /* '<S52>/Merge2' */

/* Sample index corresponding to the Spark start */
extern uint8_T VtStopOffsetSearch[8];  /* '<S52>/Merge4' */

/* Sample index corresponding to the Stop offset search */

/*Memory section for Output interface using special allocation*/

/* Declaration for custom storage class: ELD_OUT_INTERFACE_MAP */
extern uint32_T IonBuffer[800];        /* '<S9>/Merge1' */

/* Ion current Buffer */
extern uint16_T IonBufferV[800];       /* '<S9>/Merge12' */

/* Ion Current Voltage Buffer */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S12>/Data Type Duplicate' : Unused code path elimination
 * Block '<S28>/Data Type Duplicate' : Unused code path elimination
 * Block '<S29>/Data Type Duplicate' : Unused code path elimination
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Duplicate' : Unused code path elimination
 * Block '<S34>/Data Type Duplicate' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/Data Type Duplicate' : Unused code path elimination
 * Block '<S49>/Data Type Duplicate' : Unused code path elimination
 * Block '<S48>/Data Type Duplicate' : Unused code path elimination
 * Block '<S48>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S17>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S27>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion' : Eliminate redundant data type conversion
 * Block '<S48>/Reshape' : Reshape block reduction
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonAcqBufMgm'
 * '<S1>'   : 'IonAcqBufMgm/BufferMgm_Subsystem'
 * '<S2>'   : 'IonAcqBufMgm/Latch_Subsystem'
 * '<S3>'   : 'IonAcqBufMgm/PhaseParams_Subsystem'
 * '<S4>'   : 'IonAcqBufMgm/Task_Supervisior'
 * '<S5>'   : 'IonAcqBufMgm/BufferMgm_Subsystem/BufferCopy'
 * '<S6>'   : 'IonAcqBufMgm/BufferMgm_Subsystem/BufferInit'
 * '<S7>'   : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeDetection'
 * '<S8>'   : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters'
 * '<S9>'   : 'IonAcqBufMgm/BufferMgm_Subsystem/Subsystem1'
 * '<S10>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/BufferCopy/Convert_mV2uA'
 * '<S11>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/BufferCopy/Diagnosis'
 * '<S12>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/BufferCopy/Diagnosis/SetDiagState'
 * '<S13>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeDetection/EraseSpike.currentFittingParam'
 * '<S14>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeDetection/EraseSpike.voltageFittingParam'
 * '<S15>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeDetection/SpikeDetection.calculateGradient'
 * '<S16>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeAngleSelection'
 * '<S17>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold'
 * '<S18>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/StartIndexForSpikeSearch'
 * '<S19>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_S16'
 * '<S20>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U1'
 * '<S21>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U16'
 * '<S22>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U2'
 * '<S23>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U3'
 * '<S24>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U4'
 * '<S25>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U5'
 * '<S26>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U6'
 * '<S27>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/PreLookUpIdSearch_U16'
 * '<S28>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S29>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U1/Data Type Conversion Inherited3'
 * '<S30>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S31>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U2/Data Type Conversion Inherited3'
 * '<S32>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U3/Data Type Conversion Inherited3'
 * '<S33>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U4/Data Type Conversion Inherited3'
 * '<S34>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U5/Data Type Conversion Inherited3'
 * '<S35>'  : 'IonAcqBufMgm/BufferMgm_Subsystem/SpikeParameters/SpikeThreshold/LookUp_IR_U6/Data Type Conversion Inherited3'
 * '<S36>'  : 'IonAcqBufMgm/Latch_Subsystem/LatchInit'
 * '<S37>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch'
 * '<S38>'  : 'IonAcqBufMgm/Latch_Subsystem/Subsystem1'
 * '<S39>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/Counters'
 * '<S40>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/DMAIndex'
 * '<S41>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/IonGainEvaluation'
 * '<S42>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/Latch'
 * '<S43>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/Counters/CounterMgm'
 * '<S44>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/IonGainEvaluation/SwitchCaseActionSubsystem'
 * '<S45>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/IonGainEvaluation/SwitchCaseActionSubsystem1'
 * '<S46>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/IonGainEvaluation/SwitchCaseActionSubsystem3'
 * '<S47>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/IonGainEvaluation/SwitchCaseActionSubsystem4'
 * '<S48>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/IonGainEvaluation/SwitchCaseActionSubsystem4/Look2D_U16_U16_U16'
 * '<S49>'  : 'IonAcqBufMgm/Latch_Subsystem/SignalLatch/IonGainEvaluation/SwitchCaseActionSubsystem4/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 * '<S50>'  : 'IonAcqBufMgm/PhaseParams_Subsystem/PhaseParamsEvaluation'
 * '<S51>'  : 'IonAcqBufMgm/PhaseParams_Subsystem/PhaseParamsInit'
 * '<S52>'  : 'IonAcqBufMgm/PhaseParams_Subsystem/Subsystem1'
 */

/*-
 * Requirements for '<Root>': IonAcqBufMgm
 */
#endif                                 /* RTW_HEADER_IonAcqBufMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * signal_blocks                                                              *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/