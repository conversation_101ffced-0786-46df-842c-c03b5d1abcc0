/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TSparkCtrlAdat.h
 **  Date:          23-Jul-2021
 **
 **  Model Version: 1.795
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TSparkCtrlAdat_h_
#define RTW_HEADER_TSparkCtrlAdat_h_
#ifndef TSparkCtrlAdat_COMMON_INCLUDES_
# define TSparkCtrlAdat_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TSparkCtrlAdat_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void TSparkCtrlAdat_initialize(void);

/* Exported entry point function */
extern void TSparkCtrlAdat_EOA(void);

/* Exported entry point function */
extern void TSparkCtrlAdat_EOA_PreCalc(void);

/* Exported entry point function */
extern void TSparkCtrlAdat_Init(void);

/* Exported entry point function */
extern void TSparkCtrlAdat_T10ms(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_EE_INTERFACE */
extern uint16_T VtIPriCorrAdEE[8];     /* '<S1>/Merge' */

/* TSpark Primary current adaptive gain correction stored in Permanent Memory */
extern uint16_T VtSparkPlugFactorEE[8];/* '<S1>/Merge1' */

/* Spark Plug Life Factor 0 = new stored in Permanent Memory */
extern uint16_T VtTSparkNomOffAdEE[8]; /* '<S1>/Merge13' */

/* Offset to be applied at the Nominal TSpark stored in Permanent Memory */

/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T TSparkNomTot;          /* '<S3>/Product' */

/* Nominal Spark duration corrected with gain output of VTTSPARKNOMSACORR  */
extern uint16_T VtIPriCorr[8];         /* '<S1>/Merge3' */

/* TSpark Primary current gain correction */
extern uint16_T VtTSparkFilt[8];       /* '<S1>/Merge8' */

/* Filtered TSpak */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S36>/Data Type Duplicate' : Unused code path elimination
 * Block '<S34>/Data Type Duplicate' : Unused code path elimination
 * Block '<S40>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S42>/Data Type Duplicate' : Unused code path elimination
 * Block '<S41>/Data Type Duplicate' : Unused code path elimination
 * Block '<S41>/Data Type Propagation' : Unused code path elimination
 * Block '<S45>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S46>/Data Type Duplicate' : Unused code path elimination
 * Block '<S44>/Data Type Duplicate' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate' : Unused code path elimination
 * Block '<S50>/Data Type Duplicate' : Unused code path elimination
 * Block '<S50>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S24>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion' : Eliminate redundant data type conversion
 * Block '<S39>/Reshape' : Reshape block reduction
 * Block '<S41>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion' : Eliminate redundant data type conversion
 * Block '<S43>/Reshape' : Reshape block reduction
 * Block '<S44>/Conversion' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion' : Eliminate redundant data type conversion
 * Block '<S50>/Reshape' : Reshape block reduction
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TSparkCtrlAdat'
 * '<S1>'   : 'TSparkCtrlAdat/MergeSignals'
 * '<S2>'   : 'TSparkCtrlAdat/eoa'
 * '<S3>'   : 'TSparkCtrlAdat/eoaPrecalc'
 * '<S4>'   : 'TSparkCtrlAdat/init'
 * '<S5>'   : 'TSparkCtrlAdat/t10ms'
 * '<S6>'   : 'TSparkCtrlAdat/eoa/MergeIPriCorr'
 * '<S7>'   : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond'
 * '<S8>'   : 'TSparkCtrlAdat/eoa/TSparkErr_Calc'
 * '<S9>'   : 'TSparkCtrlAdat/eoa/TSparkFactorCalc'
 * '<S10>'  : 'TSparkCtrlAdat/eoa/TSparkPercCalc'
 * '<S11>'  : 'TSparkCtrlAdat/eoa/TSparkPi_calc'
 * '<S12>'  : 'TSparkCtrlAdat/eoa/TSpark_Corr'
 * '<S13>'  : 'TSparkCtrlAdat/eoa/VtSparkFilt_Calc'
 * '<S14>'  : 'TSparkCtrlAdat/eoa/eoa_adapt_mgm'
 * '<S15>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/Reset_TSparkCtrl'
 * '<S16>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/stabLoad_calc'
 * '<S17>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/stabRpm_calc'
 * '<S18>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/Reset_TSparkCtrl/Compare To Zero'
 * '<S19>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/Reset_TSparkCtrl/Compare To Zero1'
 * '<S20>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/Reset_TSparkCtrl/Compare To Zero2'
 * '<S21>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/Reset_TSparkCtrl/Compare To Zero3'
 * '<S22>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/Reset_TSparkCtrl/Compare To Zero4'
 * '<S23>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/Reset_TSparkCtrl/Compare To Zero6'
 * '<S24>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/stabLoad_calc/Steady_State_Detect'
 * '<S25>'  : 'TSparkCtrlAdat/eoa/TSparkCtrl_EnCond/stabRpm_calc/Steady_State_Detect'
 * '<S26>'  : 'TSparkCtrlAdat/eoa/TSparkErr_Calc/Dead Zone Dynamic'
 * '<S27>'  : 'TSparkCtrlAdat/eoa/TSparkFactorCalc/Compare To Zero'
 * '<S28>'  : 'TSparkCtrlAdat/eoa/TSparkPercCalc/open_loop_calc'
 * '<S29>'  : 'TSparkCtrlAdat/eoa/TSparkPercCalc/open_loop_freezing'
 * '<S30>'  : 'TSparkCtrlAdat/eoa/TSparkPercCalc/open_loop_calc/Compare To Zero'
 * '<S31>'  : 'TSparkCtrlAdat/eoa/TSparkPi_calc/TSparkKiKpInterp'
 * '<S32>'  : 'TSparkCtrlAdat/eoa/TSparkPi_calc/TSparkKiKpInterp/LookUp_IR_U1'
 * '<S33>'  : 'TSparkCtrlAdat/eoa/TSparkPi_calc/TSparkKiKpInterp/LookUp_IR_U16'
 * '<S34>'  : 'TSparkCtrlAdat/eoa/TSparkPi_calc/TSparkKiKpInterp/PreLookUpIdSearch_S16'
 * '<S35>'  : 'TSparkCtrlAdat/eoa/TSparkPi_calc/TSparkKiKpInterp/LookUp_IR_U1/Data Type Conversion Inherited3'
 * '<S36>'  : 'TSparkCtrlAdat/eoa/TSparkPi_calc/TSparkKiKpInterp/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S37>'  : 'TSparkCtrlAdat/eoa/TSpark_Corr/TSpark_corr_calc'
 * '<S38>'  : 'TSparkCtrlAdat/eoa/TSpark_Corr/calc_aged_factor'
 * '<S39>'  : 'TSparkCtrlAdat/eoa/TSpark_Corr/calc_aged_factor/Look2D_U16_U16_U16'
 * '<S40>'  : 'TSparkCtrlAdat/eoa/TSpark_Corr/calc_aged_factor/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 * '<S41>'  : 'TSparkCtrlAdat/eoa/VtSparkFilt_Calc/FOF_Reset_S16_FXP'
 * '<S42>'  : 'TSparkCtrlAdat/eoa/VtSparkFilt_Calc/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S43>'  : 'TSparkCtrlAdat/eoaPrecalc/Look2D_U16_U16_U16'
 * '<S44>'  : 'TSparkCtrlAdat/eoaPrecalc/LookUp_U16_S16'
 * '<S45>'  : 'TSparkCtrlAdat/eoaPrecalc/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 * '<S46>'  : 'TSparkCtrlAdat/eoaPrecalc/LookUp_U16_S16/Data Type Conversion Inherited3'
 * '<S47>'  : 'TSparkCtrlAdat/t10ms/ForcedValues_Calc'
 * '<S48>'  : 'TSparkCtrlAdat/t10ms/TSparkCtrlAdat_T10ms_mgm'
 * '<S49>'  : 'TSparkCtrlAdat/t10ms/reset_ctrl'
 * '<S50>'  : 'TSparkCtrlAdat/t10ms/ForcedValues_Calc/Look2D_U16_U16_U16'
 * '<S51>'  : 'TSparkCtrlAdat/t10ms/ForcedValues_Calc/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 */

/*-
 * Requirements for '<Root>': TSparkCtrlAdat
 *
 * Inherited requirements for '<S2>/TSparkErr_Calc':
 *  1. EISB_FCA6CYL_SW_REQ_246: Closed loop error shall be estimated for each coil, using the corr... (ECU_SW_Requirements#324)
 *
 * Inherited requirements for '<S2>/TSparkPi_calc':
 *  1. EISB_FCA6CYL_SW_REQ_532: The software shall implement a spark duration control for each coil. (ECU_SW_Requirements#319)
 *
 * Inherited requirements for '<S2>/VtSparkFilt_Calc':
 *  1. EISB_FCA6CYL_SW_REQ_245: The spark duration time feedback (signal VtTSparkMedian, calculate... (ECU_SW_Requirements#323)

 */
#endif                                 /* RTW_HEADER_TSparkCtrlAdat_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/