/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgm
**  Filename        :  CanMgm_out.h
**  Created on      :  30-nov-2020 09:37:00
**  Original author :  SantoroR
******************************************************************************/
/*****************************************************************************
**
**                        CanMgm Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef CANMGM_OUT_H
#define CANMGM_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
///This is a public define 
#define CANSEND_STOP        0u
#define CANSEND_WAITSTOP    1u
#define CANSEND_START       2u
#define MAX_DLC     8u
#define VB_SIZE     16u

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint16_T CntNoDiagAfterKeyOn;
extern uint8_T  DCTState;
extern uint8_T  GearPos;

#if ((CAN_TYPE == CAN_F173) || ((CAN_TYPE == CAN_F171)))
extern uint16_T InfoGpfEE;
extern uint8_T InfoGpfValidDataEE;
#endif
extern uint8_T  IgnitionCutOffDx;
extern uint8_T  IgnitionCutOffSx;
extern uint8_T  ActivePhaseSearch;

extern uint8_T BrokenFuseSX;
extern uint8_T BrokenFuseDX;

extern uint8_T RstFlashed;

extern uint8_T StCanSendEn;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : CanMgm_RunCanDiag
**
**   Description:
**    Manages CAN errors counters
**    For each error type a single counter is used.
**    The counter for a specific error is incremented when the 'error' value is 
**    equal to the corresponding error.
**    The counter is reset to 0 when the 'error' value is not equal to the 
**    corresponding error.
**    All counters are saturated to avoid overflow.
**
**   Parameters :
**    int16_T error: error type
**    uint8_T *buffEmpty: pointer to buffer empty error counter
**    uint8_T *busOff: pointer to bus off error counter
**    uint8_T *buffOverRun: pointer to buffer overrun error counter
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_RunCanDiag(int16_T error, uint8_T *buffEmpty, uint8_T *busOff, uint8_T *buffOverRun);

/******************************************************************************
**   Function    : CanMgm_RunCanDiagAdv
**
**   Description:
**    Manages CAN errors counters
**    For each error type a single counter is used.
**    The counter for a specific error is incremented when the 'error' value is 
**    equal to the corresponding error.
**    The counter is reset to 0 when the 'error' value is not equal to the 
**    corresponding error.
**    All counters are saturated to avoid overflow.
**
**   Parameters :
**    int16_T error: error type
**    uint8_T *buffEmpty: pointer to buffer empty error counter
**    uint8_T *busOff: pointer to bus off error counter
**    uint8_T *buffOverRun: pointer to buffer overrun error counter
**    uint8_T *crcError: pointer to crc error counter
**    uint8_T *cntError: pointer to cnt error counter
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_RunCanDiagAdv(int16_T error, uint8_T *buffEmpty, uint8_T *busOff, uint8_T *buffOverRun, uint8_T * crcError, uint8_T * cntError);

/******************************************************************************
**   Function    : CANMGM_Var_Diag
**
**   Description:
**    Manages CAN Valid data
**    Manages signal Diagnosis, Recovery value 
**
**   Parameters :
**    int32_T rawvar: raw input value
**    int32_T oldvar: old output value
**    uint8_T useCanValAsRec: Flag signaling if the recovery value should be the input value
**    int32_T recvar: calibration recovery value
**    int32_T *outvar: output value pointer
**    uint8_T valid: valid data signal
**    uint8_T diagline: diagnostic line number
**
**   Returns:
**    void
**
******************************************************************************/
void CANMGM_Var_Diag(int32_T rawvar, int32_T oldvar, uint8_T useCanValAsRec, int32_T recvar, int32_T *outvar, uint8_T valid, uint8_T diagline);

/******************************************************************************
**   Function    : CANMGM_VarDxSx_Diag
**
**   Description:
**    Manages CAN Valid data and delta beween bank signals
**    Manages signal Diagnosis, Recovery value 
**
**   Parameters :
**    int32_T rawvardx: raw input value for dx bank
**    int32_T rawvarsx: raw input value for sx bank
**    int32_T oldvar: old output value
**    uint8_T useCanValAsRec: Flag signaling if the recovery value should be the input value
**    int32_T recvar: calibration recovery value
**    int32_T *outvar: output value pointer
**    uint8_T validdx: valid data signal for dx bank
**    uint8_T validsx: valid data signal for sx bank
**    int32_T deltavar: Max delta between rawdx/sx values
**    uint8_T diagline: diagnostic line number
**
**   Returns:
**    void
**
******************************************************************************/
void CANMGM_VarDxSx_Diag(int32_T rawvardx, int32_T rawvarsx, int32_T oldvar, uint8_T useCanValAsRec, int32_T recvar, int32_T *outvar, uint8_T validdx, uint8_T validsx, int32_T deltavar, uint8_T diagline);

/******************************************************************************
**   Function    : CANMGM_RpmCANBlocked
**
**   Description:
**    Manages blocked Rpm signal received on CAN
**
**   Parameters :
**    uint16_T rpm: raw input rpm signal
**    uint16_T rpmCAN: raw input rpm signal from CAN
**    uint16_T rpmCANold_in: old rpmCAN value
**    uint16_T *rpmCANold_out: rpmOldCAN output pointer
**    uint8_T cnt_in: input value counter 
**    uint8_T *cnt_out: output counter value pointer
**    uint8_T thrcnt: Blocked Rpm counter threshold
**    uint8_T *flagblocked: output flag for blocked Rpm
**
**   Returns:
**    void
**
******************************************************************************/
void CANMGM_RpmCANBlocked(uint16_T rpm, uint16_T rpmCAN, uint16_T rpmCANold_in, uint16_T *rpmCANold_out, uint8_T cnt_in, uint8_T *cnt_out, uint8_T thrcnt, uint8_T *flagblocked);

/******************************************************************************
**   Function    : CanMgm_CntNoDiag
**
**   Description:
**    Decrement or set No diagnosis counter used after keyOn
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CntNoDiag(void);

/******************************************************************************
**   Function    : CanMgm_CalcCvn
**
**   Description:
**    Calculates CVN from crc16 signal 
**
**   Parameters :
**    uint32_T *cvnstr: pointer to output CVN string
**    uint16_T crc16: crc16 input value
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CalcCvn(uint32_T *cvnstr, uint16_T crc16);

/******************************************************************************
**   Function    : CanMgm_DebounceTDC
**
**   Description:
**    Debounce Ingnition cutoff signal received from CAN
**
**   Parameters :
**    uint8_T ionAbsTdc
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_DebounceTDC(uint8_T ionAbsTdc);

/******************************************************************************
**   Function    : CanMgm_CalcBrokenFuse
**
**   Description:
**    Calculates Broken fuse flags
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CalcBrokenFuse(void);

/******************************************************************************
**   Function    : CanMgm_CanState
**
**   Description:
**    Calculates StCanSendEn
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_CanState(void);

/******************************************************************************
**   Function    : CanMgm_SetClearRstFlashed
**
**   Description:
**    Calculates RstFlashed
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CanMgm_SetClearRstFlashed(uint8_T value);
/***************************************************************************/
//   Function    :   CalcCvn
//
//   Description:    
/*! \brief Calculate CVN
*/
//
//  Parameters and Returns:
/*! 
\param void
\returns int16_T error code
*/
//  Notes:        
/*!
This function performs the following macro operations:
- Cleans the message data structure before filling in the requested bits
- Packs the variables to be sent into the message structure
- Invokes the BIOS function to send the message and record the error code
*/
/**************************************************************************/
void CalcCvn(uint32_T *cvnstr, uint16_T crc16);

#endif

/****************************************************************************
 ****************************************************************************/


