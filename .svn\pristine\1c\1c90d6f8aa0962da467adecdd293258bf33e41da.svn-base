/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  IonAcq
**  Filename        :  IonAcq.h
**  Created on      :  06-apr-2021 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifndef _IONACQ_H_
#define _IONACQ_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"
#include "ionacq_out.h"
#include "syncmgm_out.h"
#include "diagmgm_out.h"
#include "ionacqcircmgm_out.h"
#include "Ignincmd_out.h"
#include "IonAcqParEval_out.h"
#include "gtm_eisb_out.h"
#include "Digio_out.h"
#include "Sync_out.h"
/* Tasks */
#include "OS_api.h"
#include "tasksdefs.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/* None */

#endif

/****************************************************************************
 ****************************************************************************/

