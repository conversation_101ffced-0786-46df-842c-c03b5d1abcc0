/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_eisb.c
 * @brief   SPC5xx GTM low level driver code.
 *
 * @addtogroup GTM
 * @{
 */

#include "gtm_eisb.h"

#ifdef USE_CRANK
//#include <crank_emulation.h>
//#include <crank_em_cfg.h>
#endif

extern unsigned long mcs0_mem[];
extern unsigned long mcs1_mem[];
extern unsigned long mcs2_mem[];

#define USE_SARADC TRUE
#define USE_CRANK  TRUE
#define GTM_CYL_SYM_SEQ 1u  //simulation of sequential cylinders
#define GTM_CYL_SYM_PAR 2u  //simulation of parallel banks
#define TEST_GTM_CYL_SYM GTM_CYL_SYM_SEQ


#define EDGE_MASK 0x01000000
#define BUCK_EN_ISR_TRIGGER_BIT      11  /* MCS0 TRIGGER.11 */
#define BUCK_PHASE_ISR_TRIGGER_BIT   12  /* MCS0 TRIGGER.12 */

#define ENABLE_MOS_SWITCH_ISEC /* P-MOS on when buck enable stops */
#define TIMEOUT_IRQ      0x10 /* TODET_IRQ_EN bit */

typedef enum {
    TIMEOUT_DISABLED = 0,
    TIMEOUT_EN_RE    = 1,
    TIMEOUT_EN_FE    = 2,
    TIMEOUT_EN_BOTH  = 3
}timeout_mode_t;

typedef enum {
    COMMAND_OUT_ISR_CLEAN                 = 0,
    COMMAND_OUT_ISR_OPEN                  = 1,
    COMMAND_OUT_ISR_CLOSE                 = 2,
    COMMAND_OUT_ISR_EPWS_CLOSE            = 3,
    COMMAND_OUT_ISR_EPWS_PH1_CLOSE        = 4,
    COMMAND_OUT_ISR_EPWS_PH2_CLOSE        = 5,
    COMMAND_OUT_ISR_EPWS_PH3_CLOSE        = 6,
    COMMAND_OUT_ISR_EPWS_PH4_CLOSE        = 7,
    COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE = 8,
    COMMAND_OUT_ISR_EPWS_TIME_OUT         = 9
}command_out_isr_t;

typedef enum {
    BUCK_ENABLE_ISR_CLEAN = 0,
    BUCK_ENABLE_ISR_OPEN  = 1,
    BUCK_ENABLE_ISR_CLOSE = 2
}buck_enable_isr_t;

typedef enum {
    BUCK_ENABLE_ON    = 1,
    BUCK_ENABLE_OFF   = 2,
    BUCK_REMODULATION = 4
}buck_signal_t;

typedef enum {
    PSM_DATA_CYL0 = 8, //trigger bit of MCS0
    PSM_DATA_CYL1 = 8, //trigger bit of MCS1 
    PSM_DATA_CYL2 = 9, //trigger bit of MCS0
    PSM_DATA_CYL3 = 9, //trigger bit of MCS1
    PSM_DATA_CYL4 = 10,//trigger bit of MCS0
    PSM_DATA_CYL5 = 10,//trigger bit of MCS1
    PSM_DATA_CYL6 = 4, //trigger bit of MCS0
    PSM_DATA_CYL7 = 4  //trigger bit of MCS1
}psm_data_t;

/************************************/
/*                                  */
/************************************/
/* Trigger bit for PSM updates */
uint32_T cyl_psm_tb[8] = { PSM_DATA_CYL0, PSM_DATA_CYL1, PSM_DATA_CYL2, PSM_DATA_CYL3, 
                           PSM_DATA_CYL4, PSM_DATA_CYL5, PSM_DATA_CYL6, PSM_DATA_CYL7};

vuint32_t *cyl0_isr_var;
vuint32_t *cyl1_isr_var;
vuint32_t *cyl2_isr_var;
vuint32_t *cyl3_isr_var;
vuint32_t *cyl4_isr_var;
vuint32_t *cyl5_isr_var;
vuint32_t *cyl6_isr_var;
vuint32_t *cyl7_isr_var;

vuint32_t *buck0_primary_adc_sample;
vuint32_t *buck1_primary_adc_sample;
vuint32_t *cylinder_primary_b0;
vuint32_t *cylinder_primary_b1;

vuint32_t *buck_step_isr_var;
vuint32_t *current_cylinder;
vuint32_t *buck1_step_isr_var;
vuint32_t *buck1_current_cylinder;

uint32_T timeout0;
uint32_T timeout1;
uint32_T timeout2;
uint32_T timeout3;
uint32_T timeout4;
uint32_T timeout5;
uint32_T timeout6;
uint32_T timeout7;

///Secondary peak current acquisition
int16_T  VtIShotPeak[N_CYL_MAX];
/// Effective Dwell time
uint32_T EffDwellTime[N_CYL_MAX];

uint8_T FlgIShotTout[N_CHANNEL];
/* Secondary current test threshold */
static uint16_T VtISecThr[N_CYL_MAX] = {0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U};
/* Secondary current test threshold to enable EPWS */
static uint16_T VtISecThrEPWS[N_CYL_MAX] = {0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U};
/* Last secondary current before p-mos reopen */
static uint16_T VtISecMin[N_CYL_MAX] = {5000U, 5000U, 5000U, 5000U, 5000U, 5000U, 5000U, 5000U};
/* Primary current peak */
uint16_T VtILeadPeak[N_CYL_MAX] = {0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U};
/* Ion circuit associated to each cylinder */
static uint8_T  Cyl2PMOS[N_CYL_MAX] = {0U, 1U, 2U, 3U, 0U, 1U, 2U, 3U};
/* VCap sampling point */
static uint8_T VCapSample = 0U;
/* EPWS Status */
uint8_T EPWSStatus[N_CYL_MAX] = {0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U};
/* PMOS able to open */
static uint8_T FlgPmosOpen[N_CYL_MAX] = {1u, 1u, 1u, 1u, 1u, 1u, 1u, 1u};

static uint8_T EPWSBank0 = EPWS_DISABLE;
static uint8_T EPWSBank1 = EPWS_DISABLE;
static uint8_T EPWSTimeout[N_CYLINDER];

static uint32_T AbsTimeOutOpen[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
static uint32_T AbsTimeOutClose[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};

#pragma ghs section bss=".bss_c0"
uint16_T ion0_4_buffer[ION0_4_N_SAMPLE];
uint16_T ion1_5_buffer[ION1_5_N_SAMPLE];
uint16_T ion2_6_buffer[ION2_6_N_SAMPLE];
uint16_T ion3_7_buffer[ION3_7_N_SAMPLE];
#pragma ghs section bss=default

ion_Channel_T NextIonCyl[N_CHANNEL_ADC];

#define B0_N_SAMPLE 20
#define B0_N_HALFSAMPLE (B0_N_SAMPLE/2)
static uint16_T pri_b0_buffer[B0_N_SAMPLE];
#define B1_N_SAMPLE 20
#define B1_N_HALFSAMPLE (B1_N_SAMPLE/2)
static uint16_T pri_b1_buffer[B1_N_SAMPLE];

#define ISEC0_N_SAMPLE 16
#define ISEC0_N_HALFSAMPLE (ISEC0_N_SAMPLE/2)
static uint16_T isec_b0_buffer[ISEC0_N_SAMPLE];
#define ISEC1_N_SAMPLE 16
#define ISEC1_N_HALFSAMPLE (ISEC1_N_SAMPLE/2)
static uint16_T isec_b1_buffer[ISEC1_N_SAMPLE];

uint32_T IgnAngle[N_CYL_MAX];

uint32_T Debug_IPRI0 = 0U;
uint32_T Debug_ISEC0 = 0U;
uint32_T Debug_IPRI1 = 0U;
uint32_T Debug_ISEC1 = 0U;
uint8_T  flg_pri_b0_check = 0U;
uint8_T  flg_pri_b1_check = 0U;
uint16_T pri_b0_db_cnt = 0u;
uint16_T sec_b0_db_cnt = 0u;
uint16_T pri_b1_db_cnt = 0u;
uint16_T sec_b1_db_cnt = 0u;
uint16_T max_ipri_b0 = 0U;
uint16_T max_ipri_b1 = 0U;
static volatile uint8_T Cnt_isec_b0 = 0U;
static volatile uint8_T Cnt_isec_b1 = 0U;
#define  CNT_DISCARD_ISEC 3U
/* The following #define shall be ((ISEC_ADC_DURATION/ISEC0_ADC_PERIOD)/ISEC0_N_SAMPLE)-1 */
#define  CNT_FORCE_SWPMOS_ISEC 100u

static uint8_T CntDiscardIsecBank0 = CNT_DISCARD_ISEC;
static uint8_T CntDiscardIsecBank1 = CNT_DISCARD_ISEC;

static uint8_T  Cnt_isec_b0_thr = 0U;
static uint8_T  Cnt_isec_b1_thr = 0U;

#ifdef EN_DEBUG_PRI_SEC
static uint8_T DebugFlgPriSec;
static uint8_T DebugFlgPriTest;
static uint8_T DebugFlgSecTest;
static uint8_T DebugFlgPriTest_b1;
static uint8_T DebugFlgSecTest_b1;

#pragma ghs section bss=".bss_c2"
static uint32_T DebugPrib0Buffer[DEBUG_N_SAMPLE_TEST];
static uint32_T DebugPrib1Buffer[DEBUG_N_SAMPLE_TEST];
static uint32_T DebugSecb0Buffer[DEBUG_N_SAMPLE_TEST];
static uint32_T DebugSecb1Buffer[DEBUG_N_SAMPLE_TEST];
#pragma ghs section bss=default

static uint16_T IdxPrib0;
static uint16_T IdxPrib1;
static uint16_T IdxSecb0;
static uint16_T IdxSecb1;
#endif

static uint16_T ThrIPriVChargeAcq = 246U; // 3.003[A] * 4096 / 50
uint16_T idx_buff = 0U;
uint16_T idx_buff_b1 = 0U;
uint16_T idx_buff_sec = 0U;
uint16_T idx_buff_sec_b1 = 0U;

uint16_T VtNrStartIonDMAIdx[N_CYL_MAX];
uint8_T VtDmaRunning[2];

const uint8_T VtCylToMcs2[4] = {12 , 13 ,14, 15};
const uint8_T VtCylToDmaIonChan[4] = {DMA_CH3, DMA_CH24, DMA_CH2, DMA_CH25};
const uint8_T VtCylToAtomChan[4] = {ATOM_CHANNEL1, ATOM_CHANNEL3, ATOM_CHANNEL5, ATOM_CHANNEL7};
const uint8_T VtCylToDmaChan[2] = {DMA_CH1, DMA_CH17};

#ifdef _BUILD_IGN_
const uint8_T TaskSparkOnId[N_CYL_MAX] = {(uint8_T)SparkTOnID0, (uint8_T)SparkTOnID1, (uint8_T)SparkTOnID2, (uint8_T)SparkTOnID3, (uint8_T)SparkTOnID4, (uint8_T)SparkTOnID5, (uint8_T)SparkTOnID6, (uint8_T)SparkTOnID7};
const uint8_T TaskSparkOffId[N_CYL_MAX] = {(uint8_T)SparkAOffID0, (uint8_T)SparkAOffID1, (uint8_T)SparkAOffID2, (uint8_T)SparkAOffID3, (uint8_T)SparkAOffID4, (uint8_T)SparkAOffID5, (uint8_T)SparkAOffID6, (uint8_T)SparkAOffID7};
const uint8_T TaskSparkBuckEnId[N_CYL_MAX] = {(uint8_T)SparkEnBuckID0, (uint8_T)SparkEnBuckID1, (uint8_T)SparkEnBuckID0, (uint8_T)SparkEnBuckID1, (uint8_T)SparkEnBuckID0, (uint8_T)SparkEnBuckID1, (uint8_T)SparkEnBuckID0, (uint8_T)SparkEnBuckID1};
#endif

#ifdef _TEST_GTM_
uint8_T Eisb_StartCmdSim = 0u;
#endif

#define ENABLE_EPWS  1u
#define DISABLE_EPWS 0u
#define FAST_DISCH_EN   1u
#define FAST_DISCH_DIS  0u

/* Spark length measure */
uint32_T SparkLength[N_CYL_MAX];
/* Spark event start time */
uint32_T TrailEdge[N_CYL_MAX];
/* Spark event stop time */
uint32_T LeadEdge[N_CYL_MAX];
/* Counter of ign commands waiting for spark event */
uint8_T VtCntIgnWaitSpEvnt[N_CHANNEL_SPK_EV];
/* Cylinder in use for GTM diagnosis routine */
uint8_T SparkCyl;

static uint8_T  SparkDurationCylSel[N_CHANNEL_SPK_EV] = {N_CYLINDER, N_CYLINDER};
static uint32_T SparkAngleDuration[N_CYL_MAX];
static SPARK_EVENT_PHASE  SparkDurationPhase[N_CHANNEL_SPK_EV] = {SPEV_RISING_EDGE_PHASE, SPEV_RISING_EDGE_PHASE};

static uint32_T sparkanglestart;
static uint32_T sparkangleend;

#ifdef TEST_GTM_UPDATE_PSM
#define TEST_PSM_SETVAL_NMB 2u
#define TEST_PSM_SETVAL0 0u
#define TEST_PSM_SETVAL1 1u
#define TEST_PSM_DUTY_20USEC 20u
#define TEST_PSM_DUTY_0USEC   0u
#define TEST_PSM_PERIOD_20USEC 20u
#define TEST_PSM_PERIOD_80USEC 20u

uint8_T Debug_TestGtmUpdate[8] = {TEST_PSM_SETVAL0, TEST_PSM_SETVAL1, TEST_PSM_SETVAL1, TEST_PSM_SETVAL0,
                                  TEST_PSM_SETVAL0, TEST_PSM_SETVAL1, TEST_PSM_SETVAL1, TEST_PSM_SETVAL0};
uint32_T CmdOutDelay[TEST_PSM_SETVAL_NMB] = {1500u, 100u};
uint32_T BuckEnDelay[TEST_PSM_SETVAL_NMB] = {100u,  1000u};
uint32_T PmosTime[TEST_PSM_SETVAL_NMB]    = {100u,  800u};
uint32_T Nmos_Delay[TEST_PSM_SETVAL_NMB]  = {600u,  200u};
uint32_T DurationPh1[TEST_PSM_SETVAL_NMB] = {400u, 480u};
uint32_T DurationPh2[TEST_PSM_SETVAL_NMB] = {240u, 80u};
uint32_T DurationPh3[TEST_PSM_SETVAL_NMB] = {320u, 240u};
uint32_T DurationPh4[TEST_PSM_SETVAL_NMB] = {800u, 560u};
uint32_T DurationPh5[TEST_PSM_SETVAL_NMB] = {320u, 480u};
uint32_T DurationPh6[TEST_PSM_SETVAL_NMB] = {480u, 240u};
uint32_T DurationPh7[TEST_PSM_SETVAL_NMB] = {560u, 720u};
#endif

void Gtm_Eisb_Tim_Tout_Disable(void)
{
    /* Cylinders (0..7) */
    TIMD2.tim->CH0_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable timeout */
    TIMD2.tim->CH0_CTRL.B.TOCTRL = 0;

    TIMD2.tim->CH1_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable timeout */
    TIMD2.tim->CH1_CTRL.B.TOCTRL = 0;

    TIMD2.tim->CH2_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable timeout */
    TIMD2.tim->CH2_CTRL.B.TOCTRL = 0;

    TIMD2.tim->CH3_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable timeout */
    TIMD2.tim->CH3_CTRL.B.TOCTRL = 0;

    TIMD2.tim->CH4_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable timeout */
    TIMD2.tim->CH4_CTRL.B.TOCTRL = 0;

    TIMD2.tim->CH5_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable timeout */
    TIMD2.tim->CH5_CTRL.B.TOCTRL = 0;

    TIMD2.tim->CH6_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable timeout */
    TIMD2.tim->CH6_CTRL.B.TOCTRL = 0;

    TIMD2.tim->CH7_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable timeout */
    TIMD2.tim->CH7_CTRL.B.TOCTRL = 0;

    /* EPWS timeout Cylinders (0..7)*/
    
    TIMD3.tim->CH0_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable epws timeout */
    TIMD3.tim->CH0_CTRL.B.TOCTRL = 0;

    TIMD3.tim->CH1_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable epws timeout */
    TIMD3.tim->CH1_CTRL.B.TOCTRL = 0;

    TIMD3.tim->CH2_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable epws timeout */
    TIMD3.tim->CH2_CTRL.B.TOCTRL = 0;

    TIMD3.tim->CH3_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable epws timeout */
    TIMD3.tim->CH3_CTRL.B.TOCTRL = 0;

    TIMD3.tim->CH4_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable epws timeout */
    TIMD3.tim->CH4_CTRL.B.TOCTRL = 0;

    TIMD3.tim->CH5_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable epws timeout */
    TIMD3.tim->CH5_CTRL.B.TOCTRL = 0;

    TIMD3.tim->CH6_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable epws timeout */
    TIMD3.tim->CH6_CTRL.B.TOCTRL = 0;

    TIMD3.tim->CH7_IRQ_EN.B.TODET_IRQ_EN = 0; /* disable epws timeout */
    TIMD3.tim->CH7_CTRL.B.TOCTRL = 0;

}


void Gtm_Eisb_Config(void)
{

    isbInit(&EISB);
    isbStart(&EISB, &eisb_config);

    gtm_cmuStart(&CMUD1);
    gtm_tbuStart(&TBUD1);


    //gtm_tomStart(); //MC, added for BUCK_BOOST_SW start
#if (SPC5_GTM_USE_TOM0 == TRUE) //MC, added for BUCK_BOOST_SW start
#if (SPC5_GTM_TOM0_USE_CHANNEL9 == TRUE)
    gtm_tomEnable(&TOMD1, TOM_CHANNEL9);
#if (SPC5_GTM_TOM0_CHANNEL9_OUTPUT == TRUE)
    gtm_tomOutputEnable(&TOMD1, TOM_CHANNEL9);
#endif
#endif
    /* Trigger TOM0 all channels */
    TOMD1.tom->GTM_TOM_TGC_REG(0,GLB_CTRL).R = 1U;
    TOMD1.tom->GTM_TOM_TGC_REG(1,GLB_CTRL).R = 1U;
#endif


    /*
     * clk_src for bank 0 = SPC5_GTM_TIM1_CH0_TCS - SPC5_GTM_TIM1_CH2_TCS - clk_Src = SPC5_GTM_TIM1_CH4_TCS
     */
    timeout0 = getTimeoutValue(SPC5_GTM_TIM1_CH0_TCS, 4000); /* 4ms */
    timeout1 = getTimeoutValue(SPC5_GTM_TIM1_CH1_TCS, 4000); /* 4ms */
    timeout2 = getTimeoutValue(SPC5_GTM_TIM1_CH2_TCS, 4000); /* 4ms */
    timeout3 = getTimeoutValue(SPC5_GTM_TIM1_CH3_TCS, 4000); /* 4ms */
    timeout4 = getTimeoutValue(SPC5_GTM_TIM1_CH4_TCS, 4000); /* 4ms */
    timeout5 = getTimeoutValue(SPC5_GTM_TIM1_CH5_TCS, 4000); /* 4ms */
    timeout6 = getTimeoutValue(SPC5_GTM_TIM1_CH6_TCS, 4000); /* 4ms */
    timeout7 = getTimeoutValue(SPC5_GTM_TIM1_CH7_TCS, 4000); /* 4ms */

#ifdef USE_CRANK
    /* Start the crank emulation */
    //crankemStart(&CRANKEMD, &crank_emulation_config);
    //manage DPLL, Reference Position and Crank Event
    isbCrankStart(&EISB);
#endif

    initMCS_BANK0(&MCSD1);
    initMCS_BANK1(&MCSD2);

    gtm_brcStart(&BRCD1);

    /********************************************************
     *
     * MCS2 MOS
     *
     *********************************************************/
    initMCS2_MOS_IPRI_ISEC(&MCSD3);
#if 0
    /* Reset the RAM and wait for action complete */
    gtm_mcsResetRAM(&MCSD3);
    while (gtm_mcsGetResetRAM(&MCSD3) == 1U) {
        ;
    }

    /* Set round-robin scheduler policy */
    gtm_mcsSetScheduler(&MCSD3, SPC5_GTM_MCS_SCHEDULER_ROUND_ROBIN);

    /* Load the MCS program */
    gtm_mcsLoadProgram(&MCSD3, mcs2_mem, SIZE_MCS2_MEM, OFFSET_MCS2_MEM);

    gtm_mcsEnableChannel(&MCSD3, MCS_CHANNEL0);

    gtm_mcsSetTriggerBit(&MCSD3, MCS_CHANNEL0);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL0, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_0); //PMOS0_4
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL0);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_1); //NMOS0_4
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL1);

    gtm_mcsEnableChannel(&MCSD3, MCS_CHANNEL1);

    gtm_mcsSetTriggerBit(&MCSD3, MCS_CHANNEL1);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL2, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_2); //PMOS1_5
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL2);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_3); //NMOS1_5
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL3);

    gtm_mcsEnableChannel(&MCSD3, MCS_CHANNEL2);

    gtm_mcsSetTriggerBit(&MCSD3, MCS_CHANNEL2);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL4, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_4); //PMOS2_6
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL4);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_5); //NMOS2_6
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL5);

    gtm_mcsEnableChannel(&MCSD3, MCS_CHANNEL3);

    gtm_mcsSetTriggerBit(&MCSD3, MCS_CHANNEL3);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL6, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_6); //PMOS3_7
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL6);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_7); //NMOS3_7
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL7);


    /* Enable Current IPRI - ISEC Channels */
    gtm_mcsEnableChannel(&MCSD3, MCS_CHANNEL4);
    gtm_mcsEnableChannel(&MCSD3, MCS_CHANNEL5);
#endif


    /******************** MCS2 END **********************************/

     /*** OUTPUT0 Signal ***/
    gtm_atomSetDataSource(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_WRITE_ADDRESS_MCS0_MCS0_1);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL0); /* Output Signal */
    /***************************/

    /*** OUTPUT1 Signal ***/
    gtm_atomSetDataSource(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_WRITE_ADDRESS_MCS1_MCS1_1);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL1); /* Output Signal */
    /***************************/

    /*** OUTPUT2 Signal ***/
    gtm_atomSetDataSource(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_WRITE_ADDRESS_MCS0_MCS0_2);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL2); /* Output Signal */
    /***************************/

    /*** OUTPUT3 Signal ***/
    gtm_atomSetDataSource(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_WRITE_ADDRESS_MCS1_MCS1_2);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL3); /* Output Signal */
    /***************************/

    /*** OUTPUT4 Signal ***/
    gtm_atomSetDataSource(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_WRITE_ADDRESS_MCS0_MCS0_3);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL4); /* Output Signal */
    /***************************/

    /*** OUTPUT5 Signal ***/
    gtm_atomSetDataSource(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_WRITE_ADDRESS_MCS1_MCS1_3);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL5); /* Output Signal */
    /***************************/

    /*** OUTPUT6 Signal ***/
    gtm_atomSetDataSource(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_WRITE_ADDRESS_MCS0_MCS0_4);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL6); /* Output Signal */
    /***************************/

    /*** OUTPUT7 Signal ***/
    gtm_atomSetDataSource(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_WRITE_ADDRESS_MCS1_MCS1_4);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL7); /* Output Signal */
    /***************************/

    
    /*** IGNITION COIL SUPPLY Signal BUCK0 ***/
    gtm_atomSetDataSource(&ATOMD1, ATOM_CHANNEL2, SPC5_GTM_WRITE_ADDRESS_MCS0_MCS0_22);
    gtm_atomStart(&ATOMD1, ATOM_CHANNEL2); /* Coil Supply Signal */
    /***************************/

    /*** IGNITION COIL SUPPLY BUCK0 ENABLE Signal ***/
    gtm_atomSetDataSource(&ATOMD1, ATOM_CHANNEL5, SPC5_GTM_WRITE_ADDRESS_MCS0_MCS0_23);  //MC: was ATOMD4, ATOM_CHANNEL0
    gtm_atomStart(&ATOMD1, ATOM_CHANNEL5); /* Coil Supply Enable Signal */   //MC: was ATOMD4, ATOM_CHANNEL0
    /***************************/

    /*** IGNITION COIL SUPPLY Signal BUCK1***/
    gtm_atomSetDataSource(&ATOMD1, ATOM_CHANNEL3, SPC5_GTM_WRITE_ADDRESS_MCS1_MCS1_22);
    gtm_atomStart(&ATOMD1, ATOM_CHANNEL3); /* Coil Supply Signal */
    /***************************/

    /*** IGNITION COIL SUPPLY BUCK1 ENABLE Signal ***/
    gtm_atomSetDataSource(&ATOMD4, ATOM_CHANNEL7, SPC5_GTM_WRITE_ADDRESS_MCS1_MCS1_23); //MC: was ATOM_CHANNEL1
    gtm_atomStart(&ATOMD4, ATOM_CHANNEL7); /* Coil Supply Enable Signal */ //MC: was ATOM_CHANNEL1
    /***************************/

    PORT_ConfigAfterBuckDiag();
    Gtm_Eisb_ConfigAfterBuckDiag();

    if (StSBC == SBC_MODE_BYPASS)
    {
        isbSetState(&EISB, ISB_BYPASSED);
    }

    gtm_mcsSetTriggerBit(&MCSD1, MCS_CHANNEL0); /* Cylinder 0 */
    gtm_mcsSetTriggerBit(&MCSD2, MCS_CHANNEL0); /* Cylinder 1 */
    gtm_mcsSetTriggerBit(&MCSD1, MCS_CHANNEL1); /* Cylinder 2 */
    gtm_mcsSetTriggerBit(&MCSD2, MCS_CHANNEL1); /* Cylinder 3 */
    gtm_mcsSetTriggerBit(&MCSD1, MCS_CHANNEL2); /* Cylinder 4 */
    gtm_mcsSetTriggerBit(&MCSD2, MCS_CHANNEL2); /* Cylinder 5 */
    gtm_mcsSetTriggerBit(&MCSD1, MCS_CHANNEL3); /* Cylinder 6 */
    gtm_mcsSetTriggerBit(&MCSD2, MCS_CHANNEL3); /* Cylinder 7 */
    gtm_mcsSetTriggerBit(&MCSD1, MCS_CHANNEL6); /* Coil management - buck enable bank0 */
    gtm_mcsSetTriggerBit(&MCSD1, MCS_CHANNEL7); /* Coil management - buck bank0 */
    gtm_mcsSetTriggerBit(&MCSD2, MCS_CHANNEL6); /* Coil management - buck enable bank1*/
    gtm_mcsSetTriggerBit(&MCSD2, MCS_CHANNEL7); /* Coil management - buck bank1*/
    gtm_mcsSetTriggerBit(&MCSD1, MCS_CHANNEL5); /* Primary Current  bank0 */
    gtm_mcsSetTriggerBit(&MCSD2, MCS_CHANNEL5); /* Primary Current  bank1 */
    gtm_mcsSetTriggerBit(&MCSD3, MCS_CHANNEL4); /* Bank0 Current */
    gtm_mcsSetTriggerBit(&MCSD3, MCS_CHANNEL5); /* Bank1 Current */

#ifdef _BUILD_DMA_
    DMAMUX0_ConfigEnable(DMA_CH0, DMAMUX0_ADC_SAR_0_EOC);
    DMA_16_16_1x_Ny_Cr(DMA_CH0, (uint32_T)&(SARADC_0.ICDR[I_PRI_B0_PER_AN].R)+2, (uint32_T)&pri_b0_buffer, B0_N_SAMPLE, DMA_BWC_LOW, DMA_EN_INT, DMA_EN_INT_HALF, DMA_NO_LNK, 0u);
    DMA_Enable(DMA_CH0);
#endif

    gtm_atomSetDataSource(&ATOMD1, ATOM_CHANNEL4, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_8);
    gtm_atomStart(&ATOMD1, ATOM_CHANNEL4); /* ATOM0_4 ADC Trigger */


    SARADC_Start(SAR0);
#ifdef _BUILD_DMA_
    DMAMUX2_ConfigEnable(DMA_CH16, DMAMUX2_ADC_SAR_2_EOC);
    DMA_16_16_1x_Ny_Cr(DMA_CH16, (uint32_T)&(SARADC_2.ICDR[I_PRI_B1_PER_AN].R)+2, (uint32_T)&pri_b1_buffer, B1_N_SAMPLE, DMA_BWC_LOW, DMA_EN_INT, DMA_EN_INT_HALF, DMA_NO_LNK, 0u);
    DMA_Enable(DMA_CH16);
#endif


    gtm_atomSetDataSource(&ATOMD1, ATOM_CHANNEL6, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_9);
    gtm_atomStart(&ATOMD1, ATOM_CHANNEL6); /* ATOM0_6 ADC Trigger */


    SARADC_Start(SAR2);
#ifdef _BUILD_DMA_
    DMAMUX0_ConfigEnable(DMA_CH3, DMAMUX0_ADC_SAR_4_EOC);
    DMA_16_16_1x_Ny_Cr(DMA_CH3, (uint32_T)&(SARADC_4.ICDR[IP_ION_04_PER_AN].R)+2, (uint32_T)&ion0_4_buffer, ION0_4_N_SAMPLE, DMA_BWC_LOW, DMA_EN_INT, DMA_NO_INT_HALF, DMA_NO_LNK, 0u);
    DMA_Enable(DMA_CH3);
#endif
    SARADC_SetChannel(SAR4, IP_ION_04_PER_AN);


    gtm_tomOutputEnable(&TOMD1, TOM_CHANNEL6);


    SARADC_Start(SAR4);
#ifdef _BUILD_DMA_
    DMAMUX3_ConfigEnable(DMA_CH24, DMAMUX3_ADC_SAR_6_EOC);
    DMA_16_16_1x_Ny_Cr(DMA_CH24, (uint32_T)&(SARADC_6.ICDR[IP_ION_15_PER_AN].R)+2, (uint32_T)&ion1_5_buffer, ION1_5_N_SAMPLE, DMA_BWC_LOW, DMA_EN_INT, DMA_NO_INT_HALF, DMA_NO_LNK, 0u);
    DMA_Enable(DMA_CH24);
#endif
    SARADC_SetChannel(SAR6, IP_ION_15_PER_AN);


    gtm_tomOutputEnable(&TOMD1, TOM_CHANNEL7);


    SARADC_Start(SAR6);


    gtm_timStart(&TIMD2, TIM_CHANNEL0);    /* Input  Signal     CYL 0 */
    gtm_timStart(&TIMD2, TIM_CHANNEL1);    /* Input  Signal     CYL 1 */
    gtm_timStart(&TIMD2, TIM_CHANNEL2);    /* Input  Signal     CYL 2 */
    gtm_timStart(&TIMD2, TIM_CHANNEL3);    /* Input  Signal     CYL 3 */
    gtm_timStart(&TIMD2, TIM_CHANNEL4);    /* Input  Signal     CYL 4 */
    gtm_timStart(&TIMD2, TIM_CHANNEL5);    /* Input  Signal     CYL 5 */
    gtm_timStart(&TIMD2, TIM_CHANNEL6);    /* Input  Signal     CYL 6 */
    gtm_timStart(&TIMD2, TIM_CHANNEL7);    /* Input  Signal     CYL 7 */
    
    /* EPWS Timeout channels */
    gtm_timStart(&TIMD3, TIM_CHANNEL0);
    gtm_timStart(&TIMD3, TIM_CHANNEL1);
    gtm_timStart(&TIMD3, TIM_CHANNEL2);
    gtm_timStart(&TIMD3, TIM_CHANNEL3);
    gtm_timStart(&TIMD3, TIM_CHANNEL4);
    gtm_timStart(&TIMD3, TIM_CHANNEL5);
    gtm_timStart(&TIMD3, TIM_CHANNEL6);
    gtm_timStart(&TIMD3, TIM_CHANNEL7);

#ifdef _TEST_GTM_
    Eisb_StartCmdSim = 1u;
#endif

}



/******************************************************************************
**   Function    : Gtm_Eisb_TDC
**
**   Description:
**    
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void Gtm_Eisb_TDC(void)
{
    if(AbsTdc == 0u)
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL2);
    }
    else if(AbsTdc == 1u)
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL3);
    }
    else if(AbsTdc == 2u)
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL4);
    }
    else if(AbsTdc == 3u)
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL5);
    }
#if (N_CYLINDER == 8U)
    else if(AbsTdc == 4u)
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL6);
    }
    else if(AbsTdc == 5u)
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL7);
    }
    else if(AbsTdc == 6u)
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL0);
    }
    else /* if(AbsTdc == 7u) */
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL1);
    }
#elif (N_CYLINDER == 6U)
    else if(AbsTdc == 4u)
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL0);
    }
    else /* if(AbsTdc == 5u) */
    {
        gtm_timStart(&TIMD2,TIM_CHANNEL1);
    }
#else
#error Wrong configuration
#endif

}



/******************************************************************************
**   Function    : Gtm_Eisb_NoSync
**
**   Description:
**    
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void Gtm_Eisb_NoSync(void)
{
    gtm_timStart(&TIMD2,TIM_CHANNEL0);
    gtm_timStart(&TIMD2,TIM_CHANNEL1);
    gtm_timStart(&TIMD2,TIM_CHANNEL2);
    gtm_timStart(&TIMD2,TIM_CHANNEL3);
    gtm_timStart(&TIMD2,TIM_CHANNEL4);
    gtm_timStart(&TIMD2,TIM_CHANNEL5);
    gtm_timStart(&TIMD2,TIM_CHANNEL6);
    gtm_timStart(&TIMD2,TIM_CHANNEL7);
}

#ifdef _TEST_GTM_
void Gtm_Eisb_StartCmdSim(void)
{
static uint8_T Eisb_StartCmdSimCnt = 0u;
    
    if (Eisb_StartCmdSim == 1u)
    {   
        Eisb_StartCmdSimCnt++;

        switch (Eisb_StartCmdSimCnt)
        {
#if (TEST_GTM_CYL_SYM == GTM_CYL_SYM_SEQ)
            case 1u:       
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL0); /* Simulation Signal CYL 0 */ //MC: was ATOM_CHANNEL7       
            break;
            case 5u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL1); /* Simulation Signal CYL 1 */ //MC: was ATOM_CHANNEL4                   
            break;
            case 9u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL2); /* Simulation Signal CYL 2 */
            break;           
            case 13u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL3); /* Simulation Signal CYL 3 */
            break;
            case 17u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL4); /* Simulation Signal CYL 4 */
            break;
            case 21u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL5); /* Simulation Signal CYL 5 */
            break;
            case 25u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL6); /* Simulation Signal CYL 6 */
            break;
            case 29u:
                gtm_atomStart(&ATOMD1, ATOM_CHANNEL7); /* Simulation Signal CYL 7 */
                Eisb_StartCmdSim = 2u;
                Eisb_StartCmdSimCnt = 0u;
            break;
            
#elif (TEST_GTM_CYL_SYM == GTM_CYL_SYM_PAR)
            case 1u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL0); /* Simulation Signal CYL 0 */ //MC: was ATOM_CHANNEL7       
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL1); /* Simulation Signal CYL 1 */ //MC: was ATOM_CHANNEL4           
            break;

            case 5u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL2); /* Simulation Signal CYL 2 */
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL3); /* Simulation Signal CYL 3 */
            break;

            case 9u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL4); /* Simulation Signal CYL 4 */
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL5); /* Simulation Signal CYL 5 */
            break;

            case 13u:
                gtm_atomStart(&ATOMD4, ATOM_CHANNEL6); /* Simulation Signal CYL 6 */
                gtm_atomStart(&ATOMD1, ATOM_CHANNEL7); /* Simulation Signal CYL 7 */
                Eisb_StartCmdSim = 2u;
                Eisb_StartCmdSimCnt = 0u;
            break;

#endif
            default:
            break;
        }
    }
#ifdef _TEST_GTM_CRANK_SETEVENT_TEST_
    else if (Eisb_StartCmdSim == 2u)
    { 
        Eisb_StartCmdSimCnt++;

        switch (Eisb_StartCmdSimCnt)
        {
            case 20u:
               setEventOnTooth(3u, EVENT_ACTION_PERIODIC, event_tooth_callback);
            break;
            
            case 40u:
               setEventOnAngle(2500u, EVENT_ACTION_PERIODIC, event_tooth_callback);
            break;            

            default:
            break;
        }
    }
#endif
    else
    {}
}
#endif


/*
 * =================================================================
 * --------------------- TIMEOUT CALLBACK --------------------------
 * =================================================================
 */
uint32_T cyl_timeout_cb_cnt[8]; 

void cyl0_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    cyl_timeout_cb_cnt[0]++;
    /* PMOS0_4 */
    gtm_mcsSetTriggerBit(&MCSD3, 12);
    /* NMOS0_4 */
    mos_stop(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_ATOM2_CHANNEL1_ARU_ENABLE);
    DMA_Stop(DMA_CH0);
}

void cyl1_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    cyl_timeout_cb_cnt[1]++;
    /* PMOS1_5 */
    gtm_mcsSetTriggerBit(&MCSD3, 13);
    /* NMOS1_5 */
    mos_stop(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_ATOM2_CHANNEL3_ARU_ENABLE);
    DMA_Stop(DMA_CH16);
}

void cyl2_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    cyl_timeout_cb_cnt[2]++;
    /* PMOS2_6 */
    gtm_mcsSetTriggerBit(&MCSD3, 14);
    /* NMOS2_6 */
    mos_stop(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_ATOM2_CHANNEL5_ARU_ENABLE);
    DMA_Stop(DMA_CH0);
}

void cyl3_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    cyl_timeout_cb_cnt[3]++;
    /* PMOS3_7 */
    gtm_mcsSetTriggerBit(&MCSD3, 15);
    /* NMOS3_7 */
    mos_stop(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_ATOM2_CHANNEL7_ARU_ENABLE);
    DMA_Stop(DMA_CH16);
}

void cyl4_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    cyl_timeout_cb_cnt[4]++;
    /* PMOS0_4 */
    gtm_mcsSetTriggerBit(&MCSD3, 12);
    /* NMOS0_4 */
    mos_stop(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_ATOM2_CHANNEL1_ARU_ENABLE);
    DMA_Stop(DMA_CH0);
}

void cyl5_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    cyl_timeout_cb_cnt[5]++;
    /* PMOS1_5 */
    gtm_mcsSetTriggerBit(&MCSD3, 13);
    /* NMOS1_5 */
    mos_stop(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_ATOM2_CHANNEL3_ARU_ENABLE);
    DMA_Stop(DMA_CH16);
}

void cyl6_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    cyl_timeout_cb_cnt[6]++;
    /* PMOS2_6 */
    gtm_mcsSetTriggerBit(&MCSD3, 14);
    /* NMOS2_6 */
    mos_stop(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_ATOM2_CHANNEL5_ARU_ENABLE);
    DMA_Stop(DMA_CH0);
}

void cyl7_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    cyl_timeout_cb_cnt[7]++;
    /* PMOS3_7 */
    gtm_mcsSetTriggerBit(&MCSD3, 15);
    /* NMOS3_7 */
    mos_stop(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_ATOM2_CHANNEL7_ARU_ENABLE);
    DMA_Stop(DMA_CH16);
}

void cyl0_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    /*Reconfigure Command output ATOM signal in SOMI mode */
    gtm_atomStop(&ATOMD2, ATOM_CHANNEL0);
    gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL0);
    EPWSTimeout[0] = 1u;
}

void cyl1_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    /*Reconfigure Command output ATOM signal in SOMI mode */
    gtm_atomStop(&ATOMD2, ATOM_CHANNEL1);
    gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL1);
    EPWSTimeout[1] = 1u;
}

void cyl2_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    /*Reconfigure Command output ATOM signal in SOMI mode */
    gtm_atomStop(&ATOMD2, ATOM_CHANNEL2);
    gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL2);
    EPWSTimeout[2] = 1u;

}

void cyl3_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    /*Reconfigure Command output ATOM signal in SOMI mode */
    gtm_atomStop(&ATOMD2, ATOM_CHANNEL3);
    gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL3);
    EPWSTimeout[3] = 1u;
}

void cyl4_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    /*Reconfigure Command output ATOM signal in SOMI mode */
    gtm_atomStop(&ATOMD2, ATOM_CHANNEL4);
    gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL4);
    EPWSTimeout[4] = 1u;
}

void cyl5_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    /*Reconfigure Command output ATOM signal in SOMI mode */
    gtm_atomStop(&ATOMD2, ATOM_CHANNEL5);
    gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL5);
    EPWSTimeout[5] = 1u;
}

void cyl6_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    /*Reconfigure Command output ATOM signal in SOMI mode */
    gtm_atomStop(&ATOMD2, ATOM_CHANNEL6);
    gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL6);
    EPWSTimeout[6] = 1u;
}

void cyl7_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel) {
    gtm_timSetTimeoutMode(timd, channel, TIMEOUT_DISABLED);
    gtm_timDisableInt(timd, channel, TIMEOUT_IRQ);
    /*Reconfigure Command output ATOM signal in SOMI mode */
    gtm_atomStop(&ATOMD2, ATOM_CHANNEL7);
    gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
    gtm_atomStart(&ATOMD2, ATOM_CHANNEL7);
    EPWSTimeout[7] = 1u;
}


/*
 * =================================================================
 * ----------------------- EDGE CALLBACK ---------------------------
 * =================================================================
 */

uint32_T cyl_edge_cb_cnt[8][2];

void cyl0_edge_cb(GTM_TIMDriver *timd, uint8_t channel) {
    static uint8_T enRisigEdgeDet0 = 0u;
    (void) channel;

    if (((timd->tim->CH0_GPR0.R & EDGE_MASK) == 0x01000000) && (enRisigEdgeDet0 != 0u)) { /* rising edge detected */
        enRisigEdgeDet0 = 0u;
        
        IgnInCmd_RiseEdge(0u, (uint32_T)timd->tim->CH0_GPR0.B.GPR0);

        /* Disable TIM to avoid unwanted commands */
        if(EffDwellTrigTime[0] > MINTRIGIN2ENFILT)
        {
            Gtm_Eisb_DisableTimCh(&TIMD2, TIM_CHANNEL0);
        }
        
        ActivateTask(TaskSparkOffId[0]);
    }
    if ((timd->tim->CH0_GPR0.R & EDGE_MASK) == 0x0) { /* falling edge detected */
        enRisigEdgeDet0 = 1u;
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL0);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL0);      
        IgnInCmd_FallingEdge(0u, (uint32_T)timd->tim->CH0_GPR0.B.GPR0);

        if (isbGetState(&EISB) == ISB_WAIT_FIRST_TRIG){
            isbSetState(&EISB, ISB_RUNNING);
        }

        ActivateTask(TaskSparkOnId[0]);
    }
}

void cyl1_edge_cb(GTM_TIMDriver *timd, uint8_t channel) {
    static uint8_T enRisigEdgeDet1 = 0u;
    (void) channel;

    if (((timd->tim->CH1_GPR0.R & EDGE_MASK) == 0x01000000) && (enRisigEdgeDet1 != 0u)) { /* rising edge detected */
        enRisigEdgeDet1 = 0u;
        
        IgnInCmd_RiseEdge(1u, (uint32_T)timd->tim->CH1_GPR0.B.GPR0);

        /* Disable TIM to avoid unwanted commands */
        if(EffDwellTrigTime[1] > MINTRIGIN2ENFILT)
        {
            Gtm_Eisb_DisableTimCh(&TIMD2, TIM_CHANNEL1);
        }
        ActivateTask(TaskSparkOffId[1]);
    }
    if ((timd->tim->CH1_GPR0.R & EDGE_MASK) == 0x0) { /* falling edge detected */
        enRisigEdgeDet1 = 1u;
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL1);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL1); 
        IgnInCmd_FallingEdge(1u, (uint32_T)timd->tim->CH1_GPR0.B.GPR0);

        if (isbGetState(&EISB) == ISB_WAIT_FIRST_TRIG){
            isbSetState(&EISB, ISB_RUNNING);
        }

        ActivateTask(TaskSparkOnId[1]);
    }
}

void cyl2_edge_cb(GTM_TIMDriver *timd, uint8_t channel) {
    static uint8_T enRisigEdgeDet2 = 0u;
    (void) channel;

    if (((timd->tim->CH2_GPR0.R & EDGE_MASK) == 0x01000000) && (enRisigEdgeDet2 != 0u)) { /* rising edge detected */
        enRisigEdgeDet2 = 0u;
        
        IgnInCmd_RiseEdge(2u, (uint32_T)timd->tim->CH2_GPR0.B.GPR0);

        /* Disable TIM to avoid unwanted commands */
        if(EffDwellTrigTime[2] > MINTRIGIN2ENFILT)
        {
            Gtm_Eisb_DisableTimCh(&TIMD2, TIM_CHANNEL2);
        }
        
        ActivateTask(TaskSparkOffId[2]);
    }
    if ((timd->tim->CH2_GPR0.R & EDGE_MASK) == 0x0) { /* falling edge detected */
        enRisigEdgeDet2 = 1u;
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL2);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL2); 
        IgnInCmd_FallingEdge(2u, (uint32_T)timd->tim->CH2_GPR0.B.GPR0);

        if (isbGetState(&EISB) == ISB_WAIT_FIRST_TRIG){
            isbSetState(&EISB, ISB_RUNNING);
        }

        ActivateTask(TaskSparkOnId[2]);
    }
}

void cyl3_edge_cb(GTM_TIMDriver *timd, uint8_t channel) {
    static uint8_T enRisigEdgeDet3 = 0u;
    (void) channel;


    if (((timd->tim->CH3_GPR0.R & EDGE_MASK) == 0x01000000) && (enRisigEdgeDet3 != 0u)) { /* rising edge detected */
        enRisigEdgeDet3 = 0u;
        
        IgnInCmd_RiseEdge(3u, (uint32_T)timd->tim->CH3_GPR0.B.GPR0);

        /* Disable TIM to avoid unwanted commands */
        if(EffDwellTrigTime[3] > MINTRIGIN2ENFILT)
        {
            Gtm_Eisb_DisableTimCh(&TIMD2, TIM_CHANNEL3);
        }
        
        ActivateTask(TaskSparkOffId[3]);
    }
    if ((timd->tim->CH3_GPR0.R & EDGE_MASK) == 0x0) { /* falling edge detected */
        enRisigEdgeDet3 = 1u;
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL3);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL3); 
        IgnInCmd_FallingEdge(3u, (uint32_T)timd->tim->CH3_GPR0.B.GPR0);

        if (isbGetState(&EISB) == ISB_WAIT_FIRST_TRIG){
            isbSetState(&EISB, ISB_RUNNING);
        }

        ActivateTask(TaskSparkOnId[3]);
    }
}

void cyl4_edge_cb(GTM_TIMDriver *timd, uint8_t channel) {
    static uint8_T enRisigEdgeDet4 = 0u;
    (void) channel;

    if (((timd->tim->CH4_GPR0.R & EDGE_MASK) == 0x01000000) && (enRisigEdgeDet4 != 0u)) { /* rising edge detected */
        enRisigEdgeDet4 = 0u;
        
        IgnInCmd_RiseEdge(4u, (uint32_T)timd->tim->CH4_GPR0.B.GPR0);

        /* Disable TIM to avoid unwanted commands */
        if(EffDwellTrigTime[4] > MINTRIGIN2ENFILT)
        {
            Gtm_Eisb_DisableTimCh(&TIMD2, TIM_CHANNEL4);
        }
                
        ActivateTask(TaskSparkOffId[4]);
    }
    if ((timd->tim->CH4_GPR0.R & EDGE_MASK) == 0x0) { /* falling edge detected */
        enRisigEdgeDet4 = 1u;
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL4);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL4); 
        IgnInCmd_FallingEdge(4u, (uint32_T)timd->tim->CH4_GPR0.B.GPR0);

        if (isbGetState(&EISB) == ISB_WAIT_FIRST_TRIG){
            isbSetState(&EISB, ISB_RUNNING);
        }

        ActivateTask(TaskSparkOnId[4]);
    }
}

void cyl5_edge_cb(GTM_TIMDriver *timd, uint8_t channel) {
    static uint8_T enRisigEdgeDet5 = 0u;
    (void) channel;

    if (((timd->tim->CH5_GPR0.R & EDGE_MASK) == 0x01000000) && (enRisigEdgeDet5 != 0u)) { /* rising edge detected */
        enRisigEdgeDet5 = 0u;
        
        IgnInCmd_RiseEdge(5u, (uint32_T)timd->tim->CH5_GPR0.B.GPR0);

        /* Disable TIM to avoid unwanted commands */
        if(EffDwellTrigTime[5] > MINTRIGIN2ENFILT)
        {
            Gtm_Eisb_DisableTimCh(&TIMD2, TIM_CHANNEL5);
        }
        
        ActivateTask(TaskSparkOffId[5]);
    }
    if ((timd->tim->CH5_GPR0.R & EDGE_MASK) == 0x0) { /* falling edge detected */
        enRisigEdgeDet5 = 1u;
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL5);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL5); 
        IgnInCmd_FallingEdge(5u, (uint32_T)timd->tim->CH5_GPR0.B.GPR0);

        if (isbGetState(&EISB) == ISB_WAIT_FIRST_TRIG){
            isbSetState(&EISB, ISB_RUNNING);
        }

        ActivateTask(TaskSparkOnId[5]);
    }
}

void cyl6_edge_cb(GTM_TIMDriver *timd, uint8_t channel) {
    static uint8_T enRisigEdgeDet6 = 0u;
    (void) channel;

    if (((timd->tim->CH6_GPR0.R & EDGE_MASK) == 0x01000000) && (enRisigEdgeDet6 != 0u)) { /* rising edge detected */
        enRisigEdgeDet6 = 0u;
        
        IgnInCmd_RiseEdge(6u, (uint32_T)timd->tim->CH6_GPR0.B.GPR0);

        /* Disable TIM to avoid unwanted commands */
        if(EffDwellTrigTime[6] > MINTRIGIN2ENFILT)
        {
            Gtm_Eisb_DisableTimCh(&TIMD2, TIM_CHANNEL6);
        }
        
        ActivateTask(TaskSparkOffId[6]);
    }
    if ((timd->tim->CH6_GPR0.R & EDGE_MASK) == 0x0) { /* falling edge detected */
        enRisigEdgeDet6 = 1u;
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL6);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL6); 
        IgnInCmd_FallingEdge(6u, (uint32_T)timd->tim->CH6_GPR0.B.GPR0);

        if (isbGetState(&EISB) == ISB_WAIT_FIRST_TRIG){
            isbSetState(&EISB, ISB_RUNNING);
        }

        ActivateTask(TaskSparkOnId[6]);
    }
}

void cyl7_edge_cb(GTM_TIMDriver *timd, uint8_t channel) {
    static uint8_T enRisigEdgeDet7 = 0u;
    (void) channel;

    if (((timd->tim->CH7_GPR0.R & EDGE_MASK) == 0x01000000) && (enRisigEdgeDet7 != 0u)) { /* rising edge detected */
        enRisigEdgeDet7 = 0u;
        
        IgnInCmd_RiseEdge(7u, (uint32_T)timd->tim->CH7_GPR0.B.GPR0);

        /* Disable TIM to avoid unwanted commands */
        if(EffDwellTrigTime[7] > MINTRIGIN2ENFILT)
        {
            Gtm_Eisb_DisableTimCh(&TIMD2, TIM_CHANNEL7);
        }
        
        ActivateTask(TaskSparkOffId[7]);
    }
    if ((timd->tim->CH7_GPR0.R & EDGE_MASK) == 0x0) { /* falling edge detected */
        enRisigEdgeDet7 = 1u;
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL7);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL7); 
        IgnInCmd_FallingEdge(7u, (uint32_T)timd->tim->CH7_GPR0.B.GPR0);

        if (isbGetState(&EISB) == ISB_WAIT_FIRST_TRIG){
            isbSetState(&EISB, ISB_RUNNING);
        }

        ActivateTask(TaskSparkOnId[7]);
    }
}


/*
 * =================================================================
 * ----------------------- MCS CALLBACK ----------------------------
 * =================================================================
 */
#ifdef __ghs__
#pragma ghs ZO
#endif

void cyl0_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    uint32_T output_isr_num = *cyl0_isr_var;
    uint32_T epws_timeout = 0;
    const uint8_T cylinder = 0u;

    *cyl0_isr_var = COMMAND_OUT_ISR_CLEAN;

    switch(output_isr_num) {
    case COMMAND_OUT_ISR_OPEN:
        AbsTimeOutOpen[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        Gtm_Eisb_StartSparkDurationMeasure(0);
        gtm_timSetTimeoutValue(&TIMD2, TIM_CHANNEL0, timeout0);
        gtm_timEnableInt(&TIMD2, TIM_CHANNEL0, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL0, TIMEOUT_EN_FE);
        break;
    case COMMAND_OUT_ISR_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH1, 0u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL0, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL0, TIMEOUT_IRQ);
        effDwellTimeCalc(cylinder);
        EPWSBank0 = EPWS_DISABLE;
        break;
    case COMMAND_OUT_ISR_EPWS_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH1, 0u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL0, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL0, TIMEOUT_IRQ);
        epws_timeout = getTimeoutValue(SPC5_GTM_TIM2_CH0_TCS, EISB.config->cylinder_config[channel]->epws_timeout);
        gtm_timSetTimeoutValue(&TIMD3, TIM_CHANNEL0, epws_timeout);
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL0);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE);
        gtm_atomSetCM1TimeBaseSource(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2);
        gtm_timEnableInt(&TIMD3, TIM_CHANNEL0, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL0, TIMEOUT_EN_BOTH);
        effDwellTimeCalc(cylinder);
        EPWSBank0 = EPWS_ENABLE;
        EPWSTimeout[cylinder] = 0u;
        break;
    case COMMAND_OUT_ISR_EPWS_PH1_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH2_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH3_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH4_CLOSE:
        break;
    case COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE:
        /* Disable EPWS Timeout */
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL0, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD3, TIM_CHANNEL0, TIMEOUT_IRQ);
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL0);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL0);
        EPWS_MosDelayConfig(0u,0u);
        break;
    case COMMAND_OUT_ISR_EPWS_TIME_OUT:
        EPWS_MosDelayConfig(0u,1u);
        break;
    default:
        *cyl0_isr_var = COMMAND_OUT_ISR_CLEAN;
        break;
    }
}

void cyl1_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    uint32_T output_isr_num = *cyl1_isr_var;
    uint32_T epws_timeout = 0;
    const uint8_T cylinder = 1u;

    *cyl1_isr_var = COMMAND_OUT_ISR_CLEAN;
    
    switch(output_isr_num) {
    case COMMAND_OUT_ISR_OPEN:
        AbsTimeOutOpen[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        Gtm_Eisb_StartSparkDurationMeasure(1);
        gtm_timSetTimeoutValue(&TIMD2, TIM_CHANNEL1, timeout1);
        gtm_timEnableInt(&TIMD2, TIM_CHANNEL1, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL1, TIMEOUT_EN_FE);
        break;
    case COMMAND_OUT_ISR_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH17, 1u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL1, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL1, TIMEOUT_IRQ);
        effDwellTimeCalc(cylinder);
        EPWSBank1 = EPWS_DISABLE;
        break;
    case COMMAND_OUT_ISR_EPWS_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH17, 1u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL1, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL1, TIMEOUT_IRQ);
        epws_timeout = getTimeoutValue(SPC5_GTM_TIM2_CH1_TCS, EISB.config->cylinder_config[channel]->epws_timeout);
        gtm_timSetTimeoutValue(&TIMD3, TIM_CHANNEL1, epws_timeout);
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL1);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE);
        gtm_atomSetCM1TimeBaseSource(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2);
        gtm_timEnableInt(&TIMD3, TIM_CHANNEL1, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL1, TIMEOUT_EN_BOTH);
        effDwellTimeCalc(cylinder);
        EPWSBank1 = EPWS_ENABLE;
        EPWSTimeout[cylinder] = 0u;
        break;
    case COMMAND_OUT_ISR_EPWS_PH1_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH2_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH3_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH4_CLOSE:
        break;
    case COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE:
        /* Disable EPWS Timeout */
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL1, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD3, TIM_CHANNEL1, TIMEOUT_IRQ);
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL1);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL1);
        EPWS_MosDelayConfig(1u,0u);
        break;
    case COMMAND_OUT_ISR_EPWS_TIME_OUT:
        EPWS_MosDelayConfig(1u,1u);
        break;
    default:
        *cyl1_isr_var = COMMAND_OUT_ISR_CLEAN;
        break;
    }
}

void cyl2_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    uint32_T output_isr_num = *cyl2_isr_var;
    uint32_T epws_timeout = 0;
    const uint8_T cylinder = 2u;

    *cyl2_isr_var = COMMAND_OUT_ISR_CLEAN;

    switch(output_isr_num) {
    case COMMAND_OUT_ISR_OPEN:
        AbsTimeOutOpen[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        Gtm_Eisb_StartSparkDurationMeasure(2);
        gtm_timSetTimeoutValue(&TIMD2, TIM_CHANNEL2, timeout2);
        gtm_timEnableInt(&TIMD2, TIM_CHANNEL2, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL2, TIMEOUT_EN_FE);
        break;
    case COMMAND_OUT_ISR_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH1, 2u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL2, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL2, TIMEOUT_IRQ);
        effDwellTimeCalc(cylinder);
        EPWSBank0 = EPWS_DISABLE;
        break;
    case COMMAND_OUT_ISR_EPWS_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH1, 2u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL2, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL2, TIMEOUT_IRQ);
        epws_timeout = getTimeoutValue(SPC5_GTM_TIM2_CH2_TCS, EISB.config->cylinder_config[channel]->epws_timeout);
        gtm_timSetTimeoutValue(&TIMD3, TIM_CHANNEL2, epws_timeout);
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL2);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE);
        gtm_atomSetCM1TimeBaseSource(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2);
        gtm_timEnableInt(&TIMD3, TIM_CHANNEL2, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL2, TIMEOUT_EN_BOTH);
        effDwellTimeCalc(cylinder);
        EPWSBank0 = EPWS_ENABLE;
        EPWSTimeout[cylinder] = 0u;
        break;
    case COMMAND_OUT_ISR_EPWS_PH1_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH2_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH3_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH4_CLOSE:
        break;
    case COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE:
        /* Disable EPWS Timeout */
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL2, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD3, TIM_CHANNEL2, TIMEOUT_IRQ);
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL2);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL2);
        EPWS_MosDelayConfig(0u,0u);
        break;
    case COMMAND_OUT_ISR_EPWS_TIME_OUT:
        EPWS_MosDelayConfig(0u,1u);
        break;
    default:
        *cyl2_isr_var = COMMAND_OUT_ISR_CLEAN;
        break;
    }
}

void cyl3_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    uint32_T output_isr_num = *cyl3_isr_var;
    uint32_T epws_timeout = 0;
    const uint8_T cylinder = 3u;

    *cyl3_isr_var = COMMAND_OUT_ISR_CLEAN;
    
    switch(output_isr_num) {
    case COMMAND_OUT_ISR_OPEN:
        AbsTimeOutOpen[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        Gtm_Eisb_StartSparkDurationMeasure(3);
        gtm_timSetTimeoutValue(&TIMD2, TIM_CHANNEL3, timeout3);
        gtm_timEnableInt(&TIMD2, TIM_CHANNEL3, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL3, TIMEOUT_EN_FE);
        break;
    case COMMAND_OUT_ISR_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH17, 3u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL3, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL3, TIMEOUT_IRQ);
        effDwellTimeCalc(cylinder);
        EPWSBank1 = EPWS_DISABLE;
        break;
    case COMMAND_OUT_ISR_EPWS_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH17, 3u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL3, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL3, TIMEOUT_IRQ);
        epws_timeout = getTimeoutValue(SPC5_GTM_TIM2_CH3_TCS, EISB.config->cylinder_config[channel]->epws_timeout);
        gtm_timSetTimeoutValue(&TIMD3, TIM_CHANNEL3, epws_timeout);
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL3);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE);
        gtm_atomSetCM1TimeBaseSource(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2);
        gtm_timEnableInt(&TIMD3, TIM_CHANNEL3, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL3, TIMEOUT_EN_BOTH);       
        effDwellTimeCalc(cylinder);
        EPWSBank1 = EPWS_ENABLE;
        EPWSTimeout[cylinder] = 0u;
        break;
    case COMMAND_OUT_ISR_EPWS_PH1_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH2_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH3_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH4_CLOSE:
        break;
    case COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE:
        /* Disable EPWS Timeout */
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL3, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD3, TIM_CHANNEL3, TIMEOUT_IRQ);
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL3);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL3);
        EPWS_MosDelayConfig(1u,0u);
        break;
    case COMMAND_OUT_ISR_EPWS_TIME_OUT:
        EPWS_MosDelayConfig(1u,1u);
        break;
    default:
        *cyl3_isr_var = COMMAND_OUT_ISR_CLEAN;
        break;
    }
}

void cyl4_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    uint32_T output_isr_num = *cyl4_isr_var;
    uint32_T epws_timeout = 0;
    const uint8_T cylinder = 4u;

    *cyl4_isr_var = COMMAND_OUT_ISR_CLEAN;

    switch(output_isr_num) {
    case COMMAND_OUT_ISR_OPEN:
        AbsTimeOutOpen[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        Gtm_Eisb_StartSparkDurationMeasure(4);
        gtm_timSetTimeoutValue(&TIMD2, TIM_CHANNEL4, timeout4);
        gtm_timEnableInt(&TIMD2, TIM_CHANNEL4, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL4, TIMEOUT_EN_FE);
        break;
    case COMMAND_OUT_ISR_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH1, 4u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL4, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL4, TIMEOUT_IRQ);
        effDwellTimeCalc(cylinder);
        EPWSBank0 = EPWS_DISABLE;
        break;
    case COMMAND_OUT_ISR_EPWS_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH1, 4u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL4, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL4, TIMEOUT_IRQ);
        epws_timeout = getTimeoutValue(SPC5_GTM_TIM2_CH4_TCS, EISB.config->cylinder_config[channel]->epws_timeout);
        gtm_timSetTimeoutValue(&TIMD3, TIM_CHANNEL4, epws_timeout);
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL4);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE);
        gtm_atomSetCM1TimeBaseSource(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2);
        gtm_timEnableInt(&TIMD3, TIM_CHANNEL4, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL4, TIMEOUT_EN_BOTH);
        effDwellTimeCalc(cylinder);
        EPWSBank0 = EPWS_ENABLE;
        EPWSTimeout[cylinder] = 0u;
        break;
    case COMMAND_OUT_ISR_EPWS_PH1_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH2_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH3_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH4_CLOSE:
        break;
    case COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE:
        /* Disable EPWS Timeout */
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL4, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD3, TIM_CHANNEL4, TIMEOUT_IRQ);
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL4);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL4);
        EPWS_MosDelayConfig(0u,0u);
        break;
    case COMMAND_OUT_ISR_EPWS_TIME_OUT:
        EPWS_MosDelayConfig(0u,1u);
        break;
    default:
        *cyl4_isr_var = COMMAND_OUT_ISR_CLEAN;
        break;
    }
}

void cyl5_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    uint32_T output_isr_num = *cyl5_isr_var;
    uint32_T epws_timeout = 0;
    const uint8_T cylinder = 5u;

    *cyl5_isr_var = COMMAND_OUT_ISR_CLEAN;
    
    switch(output_isr_num) {
    case COMMAND_OUT_ISR_OPEN:
        AbsTimeOutOpen[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        Gtm_Eisb_StartSparkDurationMeasure(5);
        gtm_timSetTimeoutValue(&TIMD2, TIM_CHANNEL5, timeout5);
        gtm_timEnableInt(&TIMD2, TIM_CHANNEL5, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL5, TIMEOUT_EN_FE);
        break;
    case COMMAND_OUT_ISR_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH17, 5u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL5, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL5, TIMEOUT_IRQ);
        effDwellTimeCalc(cylinder);
        EPWSBank1 = EPWS_DISABLE;
        break;
    case COMMAND_OUT_ISR_EPWS_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH17, 5u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL5, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL5, TIMEOUT_IRQ);
        epws_timeout = getTimeoutValue(SPC5_GTM_TIM2_CH5_TCS, EISB.config->cylinder_config[channel]->epws_timeout);
        gtm_timSetTimeoutValue(&TIMD3, TIM_CHANNEL5, epws_timeout);
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL5);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE);
        gtm_atomSetCM1TimeBaseSource(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2);
        gtm_timEnableInt(&TIMD3, TIM_CHANNEL5, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL5, TIMEOUT_EN_BOTH);     
        effDwellTimeCalc(cylinder);
        EPWSBank1 = EPWS_ENABLE;
        EPWSTimeout[cylinder] = 0u;
        break;
    case COMMAND_OUT_ISR_EPWS_PH1_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH2_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH3_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH4_CLOSE:
        break;
    case COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE:
        /* Disable EPWS Timeout */
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL5, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD3, TIM_CHANNEL5, TIMEOUT_IRQ);
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL5);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL5);
        EPWS_MosDelayConfig(1u,0u);
        break;
    case COMMAND_OUT_ISR_EPWS_TIME_OUT:
        EPWS_MosDelayConfig(1u,1u);
        break;
    default:
        *cyl5_isr_var = COMMAND_OUT_ISR_CLEAN;
        break;
    }
}

void cyl6_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    uint32_T output_isr_num = *cyl6_isr_var;
    uint32_T epws_timeout = 0;
    const uint8_T cylinder = 6u;

    *cyl6_isr_var = COMMAND_OUT_ISR_CLEAN;

    switch(output_isr_num) {
    case COMMAND_OUT_ISR_OPEN:
        AbsTimeOutOpen[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        Gtm_Eisb_StartSparkDurationMeasure(6);
        gtm_timSetTimeoutValue(&TIMD2, TIM_CHANNEL6, timeout6);
        gtm_timEnableInt(&TIMD2, TIM_CHANNEL6, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL6, TIMEOUT_EN_FE);
        break;
    case COMMAND_OUT_ISR_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH1,6u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL6, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL6, TIMEOUT_IRQ);
        effDwellTimeCalc(cylinder);
        EPWSBank0 = EPWS_DISABLE;
        break;
    case COMMAND_OUT_ISR_EPWS_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH1, 6u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL6, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL6, TIMEOUT_IRQ);
        epws_timeout = getTimeoutValue(SPC5_GTM_TIM2_CH6_TCS, EISB.config->cylinder_config[channel]->epws_timeout);
        gtm_timSetTimeoutValue(&TIMD3, TIM_CHANNEL6, epws_timeout);
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL6);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE);
        gtm_atomSetCM1TimeBaseSource(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2);
        gtm_timEnableInt(&TIMD3, TIM_CHANNEL6, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL6, TIMEOUT_EN_BOTH);       
        effDwellTimeCalc(cylinder);
        EPWSBank0 = EPWS_ENABLE;
        EPWSTimeout[cylinder] = 0u;
        break;
    case COMMAND_OUT_ISR_EPWS_PH1_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH2_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH3_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH4_CLOSE:
        break;
    case COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE:
        /* Disable EPWS Timeout */
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL6, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD3, TIM_CHANNEL6, TIMEOUT_IRQ);
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL6);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL6);
        EPWS_MosDelayConfig(0u,0u);
        break;
    case COMMAND_OUT_ISR_EPWS_TIME_OUT:
        EPWS_MosDelayConfig(0u,1u);
        break;
    default:
        *cyl6_isr_var = COMMAND_OUT_ISR_CLEAN;
        break;
    }
}

void cyl7_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    uint32_T output_isr_num = *cyl7_isr_var;
    uint32_T epws_timeout = 0;
    const uint8_T cylinder = 7u;

    *cyl7_isr_var = COMMAND_OUT_ISR_CLEAN;
    
    switch(output_isr_num) {
    case COMMAND_OUT_ISR_OPEN:
        AbsTimeOutOpen[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        Gtm_Eisb_StartSparkDurationMeasure(7);
        gtm_timSetTimeoutValue(&TIMD2, TIM_CHANNEL7, timeout7);
        gtm_timEnableInt(&TIMD2, TIM_CHANNEL7, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL7, TIMEOUT_EN_FE);
        break;
    case COMMAND_OUT_ISR_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH17, 7u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL7, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL7, TIMEOUT_IRQ);
        effDwellTimeCalc(cylinder);
        EPWSBank1 = EPWS_DISABLE;
        break;
    case COMMAND_OUT_ISR_EPWS_CLOSE:
        AbsTimeOutClose[cylinder] = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL2);
        IgnAngle[cylinder] = GetCurrentAngle();
        DMA_ReconfigSec(DMA_CH17, 7u);
        gtm_timSetTimeoutMode(&TIMD2, TIM_CHANNEL7, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD2, TIM_CHANNEL7, TIMEOUT_IRQ);
        epws_timeout = getTimeoutValue(SPC5_GTM_TIM2_CH7_TCS, EISB.config->cylinder_config[channel]->epws_timeout);
        gtm_timSetTimeoutValue(&TIMD3, TIM_CHANNEL7, epws_timeout);
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL7);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE);
        gtm_atomSetCM1TimeBaseSource(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2);
        gtm_timEnableInt(&TIMD3, TIM_CHANNEL7, TIMEOUT_IRQ);
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL7, TIMEOUT_EN_BOTH);
        effDwellTimeCalc(cylinder);
        EPWSBank1 = EPWS_ENABLE;
        EPWSTimeout[cylinder] = 0u;
        break;
    case COMMAND_OUT_ISR_EPWS_PH1_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH2_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH3_CLOSE:
    case COMMAND_OUT_ISR_EPWS_PH4_CLOSE:
        break;
    case COMMAND_OUT_ISR_EPWS_LAST_PULSE_CLOSE:
        /* Disable EPWS Timeout */
        gtm_timSetTimeoutMode(&TIMD3, TIM_CHANNEL7, TIMEOUT_DISABLED);
        gtm_timDisableInt(&TIMD3, TIM_CHANNEL7, TIMEOUT_IRQ);
        /*Reconfigure Command output ATOM signal in SOMI mode */
        gtm_atomStop(&ATOMD2, ATOM_CHANNEL7);
        gtm_atomSetMode(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE);
        gtm_atomStart(&ATOMD2, ATOM_CHANNEL7);
        EPWS_MosDelayConfig(1u,0u);
        break;
    case COMMAND_OUT_ISR_EPWS_TIME_OUT:
        EPWS_MosDelayConfig(1u,1u);
        break;
    default:
        *cyl7_isr_var = COMMAND_OUT_ISR_CLEAN;
        break;
    }
}


void buck0_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    (void)mcsd;
    (void)channel;
}

uint32_T b0_open=0;
uint32_T b0_close = 0;

static void mos_stop(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t aru_enable)
{
    gtm_atomStop(atomd, channel);
    gtm_atomARUEnable(atomd, channel, FALSE);
    gtm_atomARUEnable(atomd, channel, aru_enable);
    gtm_atomStart(atomd, channel);
}

void buck0_en_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    (void)channel;
    uint32_T cylinder;

    cylinder = *current_cylinder;

    if (gtm_mcsGetTriggerBit(mcsd, 11))/* BUCK_EN OPEN */
    {
        /* DMA Configuration primary current */
        DMA_ReconfigPri(DMA_CH0);
        
        CylPlaMOSRes_idx = cylinder;
        CylPlaMOS_idx = Cyl2PMOS[cylinder];
        b0_open++;

        gtm_mcsClearTriggerBit(mcsd, 11);
        ActivateTask(TaskSparkBuckEnId[cylinder]);
    }
    else if (gtm_mcsGetTriggerBit(mcsd, 12)) /* BUCK_EN CLOSE */
    {
        b0_close++;
        
#ifndef ENABLE_MOS_SWITCH_ISEC
        switch(cylinder) {
        case 0:
        case 4:
            gtm_mcsSetTriggerBit(&MCSD3, 12);
            /* NMOS0_4 */
            mos_stop(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_ATOM2_CHANNEL1_ARU_ENABLE);
            break;
        case 2:
        case 6:
            gtm_mcsSetTriggerBit(&MCSD3, 14);
            /* NMOS2_6 */
            mos_stop(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_ATOM2_CHANNEL5_ARU_ENABLE);
            break;
        default:
            break;
        }
#endif

#ifdef TEST_GTM_UPDATE_PSM
    if (Debug_TestGtmUpdate[cylinder] == 1) {
        IGN_HGISet_CmdOutDelay(cylinder, CmdOutDelay[TEST_PSM_SETVAL1]);
        IGN_HGISet_BuckEnDelay(cylinder, VtDelayBkEn[cylinder]);
        IGN_HGISet_BuckRefChargePh(cylinder,TEST_PSM_DUTY_20USEC, DurationPh1[TEST_PSM_SETVAL1], TEST_PSM_DUTY_0USEC ,DurationPh2[TEST_PSM_SETVAL1], TEST_PSM_DUTY_20USEC,DurationPh3[TEST_PSM_SETVAL1], TEST_PSM_DUTY_0USEC, DurationPh4[TEST_PSM_SETVAL1]);
        IGN_HGISet_BuckRefDisChargePh(cylinder, TEST_PSM_DUTY_20USEC,  DurationPh5[TEST_PSM_SETVAL1] , TEST_PSM_DUTY_0USEC,  DurationPh6[TEST_PSM_SETVAL1], TEST_PSM_DUTY_20USEC,  DurationPh7[TEST_PSM_SETVAL1]);
        IGN_HGISet_MosTime(cylinder, PmosTime[TEST_PSM_SETVAL1], Nmos_Delay[TEST_PSM_SETVAL1]); 
        IGN_HGISet_BuckRefPeriod(cylinder, TEST_PSM_PERIOD_80USEC);
        IGN_HGISet_EnableEpws(cylinder, ENABLE_EPS);
        Debug_TestGtmUpdate[cylinder] = 0;
    } else {
        IGN_HGISet_CmdOutDelay(cylinder, CmdOutDelay[TEST_PSM_SETVAL0]);
        IGN_HGISet_BuckEnDelay(cylinder, VtDelayBkEn[cylinder]);
        IGN_HGISet_BuckRefChargePh(cylinder, TEST_PSM_DUTY_0USEC, DurationPh1[TEST_PSM_SETVAL0], TEST_PSM_DUTY_20USEC, DurationPh2[TEST_PSM_SETVAL0], TEST_PSM_DUTY_0USEC, DurationPh3[TEST_PSM_SETVAL0] , TEST_PSM_DUTY_20USEC,DurationPh4[TEST_PSM_SETVAL0]);
        IGN_HGISet_BuckRefDisChargePh(cylinder, TEST_PSM_DUTY_0USEC, DurationPh5[TEST_PSM_SETVAL0], TEST_PSM_DUTY_20USEC, DurationPh6[TEST_PSM_SETVAL0] , TEST_PSM_DUTY_0USEC, DurationPh7[TEST_PSM_SETVAL0]);
        IGN_HGISet_MosTime(cylinder, PmosTime[TEST_PSM_SETVAL0], Nmos_Delay[TEST_PSM_SETVAL0]);
        IGN_HGISet_BuckRefPeriod(cylinder, TEST_PSM_PERIOD_20USEC);
        IGN_HGISet_EnableEpws(cylinder, DISABLE_EPS);
        Debug_TestGtmUpdate[cylinder] = 1;
    }

#endif

        /* Set parameters for next actuation */
        Gtm_Eisb_SetParams(cylinder);

        if ((VtStPlasObj[cylinder] == PLAS_ST_SEL) || (VtStPlasObj[cylinder] == PLAS_ION_ST_SEL))
        {
            CntDiscardIsecBank0 = NISECDISCSAMPEPWS;
            IGN_HGISet_EpwsStatus(cylinder, ENABLE_EPWS);
            Gtm_Eisb_SetEpwsParams(cylinder);
        }
        else
        {
            CntDiscardIsecBank0 = 0u;
            IGN_HGISet_EpwsStatus(cylinder, DISABLE_EPWS);
        }
        
        isbSetupCylinder(&EISB, EISB.config, cylinder);
        gtm_mcsSetTriggerBit(mcsd, cyl_psm_tb[cylinder]);
        gtm_mcsClearTriggerBit(mcsd, 12);
    }
}


void buck1_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    (void)mcsd;
    (void)channel;
}

uint32_t b1_open=0;
uint32_t b1_close = 0;
void buck1_en_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    (void)channel;
    uint32_T cylinder;

    cylinder = *buck1_current_cylinder;

    if (gtm_mcsGetTriggerBit(mcsd, 11)) /* BUCK_EN OPEN */
    {
        /* DMA Configuration primary current */
        DMA_ReconfigPri(DMA_CH16);
        
        CylPlaMOSRes_idx = cylinder;
        CylPlaMOS_idx = Cyl2PMOS[cylinder];

        b1_open++;
        
        gtm_mcsClearTriggerBit(mcsd, 11);
        ActivateTask(TaskSparkBuckEnId[cylinder]);
    } 
    else if (gtm_mcsGetTriggerBit(mcsd, 12)) /* BUCK_EN CLOSE */
    {
        b1_close++;

#ifndef ENABLE_MOS_SWITCH_ISEC
        switch(cylinder)
        {
            case 1:
            case 5:
                /* PMOS1_5 */
                gtm_mcsSetTriggerBit(&MCSD3, 13);
                /* NMOS1_5 */
                mos_stop(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_ATOM2_CHANNEL3_ARU_ENABLE);   
                break;
            case 3:
            case 7:
                /* PMOS3_7 */
                gtm_mcsSetTriggerBit(&MCSD3, 15);
                /* NMOS3_7 */
                mos_stop(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_ATOM2_CHANNEL7_ARU_ENABLE);   
                break;
            default:
                break;
        }
#endif

#ifdef TEST_GTM_UPDATE_PSM
    if (Debug_TestGtmUpdate[cylinder] == 1) {
        IGN_HGISet_CmdOutDelay(cylinder, CmdOutDelay[TEST_PSM_SETVAL1]);
        IGN_HGISet_BuckEnDelay(cylinder, VtDelayBkEn[cylinder]);
        IGN_HGISet_BuckRefChargePh(cylinder,TEST_PSM_DUTY_20USEC, DurationPh1[TEST_PSM_SETVAL1], TEST_PSM_DUTY_0USEC ,DurationPh2[TEST_PSM_SETVAL1], TEST_PSM_DUTY_20USEC,DurationPh3[TEST_PSM_SETVAL1], TEST_PSM_DUTY_0USEC, DurationPh4[TEST_PSM_SETVAL1]);
        IGN_HGISet_BuckRefDisChargePh(cylinder, TEST_PSM_DUTY_20USEC,  DurationPh5[TEST_PSM_SETVAL1] , TEST_PSM_DUTY_0USEC,  DurationPh6[TEST_PSM_SETVAL1], TEST_PSM_DUTY_20USEC,  DurationPh7[TEST_PSM_SETVAL1]);
        IGN_HGISet_MosTime(cylinder, PmosTime[TEST_PSM_SETVAL1], Nmos_Delay[TEST_PSM_SETVAL1]);            
        Debug_TestGtmUpdate[cylinder] = 0;
    } else {
        IGN_HGISet_CmdOutDelay(cylinder, CmdOutDelay[TEST_PSM_SETVAL0]);
        IGN_HGISet_BuckEnDelay(cylinder, VtDelayBkEn[cylinder]);
        IGN_HGISet_BuckRefChargePh(cylinder, TEST_PSM_DUTY_0USEC, DurationPh1[TEST_PSM_SETVAL0], TEST_PSM_DUTY_20USEC, DurationPh2[TEST_PSM_SETVAL0], TEST_PSM_DUTY_0USEC, DurationPh3[TEST_PSM_SETVAL0] , TEST_PSM_DUTY_20USEC,DurationPh4[TEST_PSM_SETVAL0]);
        IGN_HGISet_BuckRefDisChargePh(cylinder, TEST_PSM_DUTY_0USEC, DurationPh5[TEST_PSM_SETVAL0], TEST_PSM_DUTY_20USEC, DurationPh6[TEST_PSM_SETVAL0] , TEST_PSM_DUTY_0USEC, DurationPh7[TEST_PSM_SETVAL0]);
        IGN_HGISet_MosTime(cylinder, PmosTime[TEST_PSM_SETVAL0], Nmos_Delay[TEST_PSM_SETVAL0]);
        Debug_TestGtmUpdate[cylinder] = 1;
    }
    
#endif

        /* Set parameters for next actuation */
        Gtm_Eisb_SetParams(cylinder);

        if ((VtStPlasObj[cylinder] == PLAS_ST_SEL) || (VtStPlasObj[cylinder] == PLAS_ION_ST_SEL))
        {
            CntDiscardIsecBank1 = NISECDISCSAMPEPWS;
            IGN_HGISet_EpwsStatus(cylinder, ENABLE_EPWS);
            Gtm_Eisb_SetEpwsParams(cylinder);
        }
        else
        {
            CntDiscardIsecBank1 = 0u;
            IGN_HGISet_EpwsStatus(cylinder, DISABLE_EPWS);
        }
        
        isbSetupCylinder(&EISB, EISB.config, cylinder);
        gtm_mcsSetTriggerBit(mcsd, cyl_psm_tb[cylinder]);
        gtm_mcsClearTriggerBit(mcsd, 12);
    }
}

static uint32_T threshold_out;
static uint32_T cyl_threshold_out;

void buck0_prim_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    (void)mcsd;
    (void)channel;

    cyl_threshold_out = *cylinder_primary_b0;
    threshold_out = *buck0_primary_adc_sample;
    *buck0_primary_adc_sample = 0;
}

void buck1_prim_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    (void)mcsd;
    (void)channel;

    cyl_threshold_out = *cylinder_primary_b1;
    threshold_out = *buck1_primary_adc_sample;
    *buck1_primary_adc_sample = 0;
}


/* Callback associated to the MCS2_CH0 - MOS0-4 */
void mcs2_ch0_cb(GTM_MCSDriver *mcsd, uint8_t channel)
{
    uint32_T cylinder;
    
    (void)mcsd;
    (void)channel;
    
    cylinder = *current_cylinder;

    NextIonCyl[ION_CHANNEL_A] = (ion_Channel_T)cylinder;
    /* Select threshold for misfire detection */
    MSparkCmd_ThrMisfPlaSel(NextIonCyl[ION_CHANNEL_A]);
    
    IonAcq_SparkOn(cylinder, ION_CHANNEL_A);
    
    SARADC_Stop(SAR4);
    DMA_Disable(DMA_CH2);
    DMA_16_16_1x_Ny_Cr(DMA_CH3, (uint32_T)&(SARADC_4.ICDR[IP_ION_04_PER_AN].R)+2, (uint32_T)&ion0_4_buffer, NSampleMaxPrg[ION_CHANNEL_A], DMA_BWC_LOW, DMA_EN_INT, DMA_NO_INT_HALF, DMA_NO_LNK, 0u);
    DMA_Enable(DMA_CH3);
    SARADC_SetChannel(SAR4, IP_ION_04_PER_AN);
    SARADC_Start(SAR4);

    if (NSampleMaxPrg[ION_CHANNEL_A] >= 1u)
    {
        gtm_tomSetCompareReg1(&TOMD1, TOM_CHANNEL6, IonDTPrg[ION_CHANNEL_A] * 10u);
        gtm_tomEnable(&TOMD1, TOM_CHANNEL6);
    }
    else
    {
        NSparkDeleted++;
        NSparkDelType = 6u;
    }
}

/* Callback associated to the MCS2_CH1 - MOS1-5 */
void mcs2_ch1_cb(GTM_MCSDriver *mcsd, uint8_t channel) 
{
    uint32_T cylinder;

    (void)mcsd;
    (void)channel;
    
    cylinder = *buck1_current_cylinder;
    
    NextIonCyl[ION_CHANNEL_B] = (ion_Channel_T)cylinder;
    /* Select threshold for misfire detection */
    MSparkCmd_ThrMisfPlaSel(NextIonCyl[ION_CHANNEL_B]);
    
    IonAcq_SparkOn(cylinder, ION_CHANNEL_B);
    

    SARADC_Stop(SAR6);
    DMA_Disable(DMA_CH25);
    DMA_16_16_1x_Ny_Cr(DMA_CH24, (uint32_T)&(SARADC_6.ICDR[IP_ION_15_PER_AN].R)+2, (uint32_T)&ion1_5_buffer, NSampleMaxPrg[ION_CHANNEL_B], DMA_BWC_LOW, DMA_EN_INT, DMA_NO_INT_HALF, DMA_NO_LNK, 0u);
    DMA_Enable(DMA_CH24);
    SARADC_SetChannel(SAR6, IP_ION_15_PER_AN);
    SARADC_Start(SAR6);

    if (NSampleMaxPrg[ION_CHANNEL_B] >= 1u)
    {
        gtm_tomSetCompareReg1(&TOMD1, TOM_CHANNEL7, IonDTPrg[ION_CHANNEL_B] * 10u);
        gtm_tomEnable(&TOMD1, TOM_CHANNEL7);
    }
    else
    {
        NSparkDeleted++;
        NSparkDelType = 6u;
    }
}

/* Callback associated to the MCS2_CH2 - MOS2-6 */
void mcs2_ch2_cb(GTM_MCSDriver *mcsd, uint8_t channel) 
{
    uint32_T cylinder;
    
    (void)mcsd;
    (void)channel;
    
    cylinder = *current_cylinder;
    
    NextIonCyl[ION_CHANNEL_C] = (ion_Channel_T)cylinder;
    /* Select threshold for misfire detection */
    MSparkCmd_ThrMisfPlaSel(NextIonCyl[ION_CHANNEL_C]);
    
    IonAcq_SparkOn(cylinder, ION_CHANNEL_C);
    

    SARADC_Stop(SAR4);
    DMA_Disable(DMA_CH3);
    DMA_16_16_1x_Ny_Cr(DMA_CH2, (uint32_T)&(SARADC_4.ICDR[IP_ION_26_PER_AN].R)+2, (uint32_T)&ion2_6_buffer, NSampleMaxPrg[ION_CHANNEL_C], DMA_BWC_LOW, DMA_EN_INT, DMA_NO_INT_HALF, DMA_NO_LNK, 0u);
    DMA_Enable(DMA_CH2);
    SARADC_SetChannel(SAR4, IP_ION_26_PER_AN);
    SARADC_Start(SAR4);

    if (NSampleMaxPrg[ION_CHANNEL_C] >= 1u)
    {
        gtm_tomSetCompareReg1(&TOMD1, TOM_CHANNEL6, IonDTPrg[ION_CHANNEL_C] * 10u);
        gtm_tomEnable(&TOMD1, TOM_CHANNEL6);
    }
    else
    {
        NSparkDeleted++;
        NSparkDelType = 6u;
    }
}

/* Callback associated to the MCS2_CH3 - MOS3-7 */
void mcs2_ch3_cb(GTM_MCSDriver *mcsd, uint8_t channel)  
{
    uint32_T cylinder;
    
    (void)mcsd;
    (void)channel;
    
    cylinder = *buck1_current_cylinder;
    
    NextIonCyl[ION_CHANNEL_D] = (ion_Channel_T)cylinder;
    /* Select threshold for misfire detection */
    MSparkCmd_ThrMisfPlaSel(NextIonCyl[ION_CHANNEL_D]);
    
    IonAcq_SparkOn(cylinder, ION_CHANNEL_D);
    

    SARADC_Stop(SAR6);
    DMA_Disable(DMA_CH24);
    DMA_16_16_1x_Ny_Cr(DMA_CH25, (uint32_T)&(SARADC_6.ICDR[IP_ION_37_PER_AN].R)+2, (uint32_T)&ion3_7_buffer, NSampleMaxPrg[ION_CHANNEL_D], DMA_BWC_LOW, DMA_EN_INT, DMA_NO_INT_HALF, DMA_NO_LNK, 0u);
    DMA_Enable(DMA_CH25);
    SARADC_SetChannel(SAR6, IP_ION_37_PER_AN);
    SARADC_Start(SAR6);
    
    if (NSampleMaxPrg[ION_CHANNEL_D] >= 1u)
    {
        gtm_tomSetCompareReg1(&TOMD1, TOM_CHANNEL7, IonDTPrg[ION_CHANNEL_D] * 10u);
        gtm_tomEnable(&TOMD1, TOM_CHANNEL7);
    }
    else
    {
        NSparkDeleted++;
        NSparkDelType = 6u;
    }
}

/* Callback associated to the MCS2_CH4 - Current management */
void mcs2_4_ipri0_isec_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    (void)channel;
    uint32_t trg_vect;

    trg_vect = gtm_mcsGetTrigger(mcsd);


    if (trg_vect & 0x100) { /* TRG.8 IPRI0 */
        gtm_mcsClearTriggerBit(mcsd, 8);
    }
    if (trg_vect & 0x200) { /* TRG.9 ISEC0 */
        gtm_mcsClearTriggerBit(mcsd, 9);
    }

}

void mcs2_5_ipri1_isec_cb(GTM_MCSDriver *mcsd, uint8_t channel) {
    (void)channel;
    uint32_t trg_vect;

    trg_vect = gtm_mcsGetTrigger(mcsd);

    if (trg_vect & 0x400) { /* TRG.10 IPRI1 */
        gtm_mcsClearTriggerBit(mcsd, 10);
    }
    if (trg_vect & 0x800) { /* TRG.11 ISEC1 */
        gtm_mcsClearTriggerBit(mcsd, 11);
    }

}

/*
 * =================================================================
 * ----------------- SARADC ION DMA CALLBACK -----------------------
 * =================================================================
 */

#pragma ghs section text=".vletext_c2"

void ipri_b0_saradcconf_ch4_ipri_b0_dma(void) {

    uint32_T ipri_acc32;
    uint16_T ipri_acc16;
    uint8_T  idx;    
    uint8_T  idx_db = 0u ; 

    if ((pri_b0_db_cnt%2u)!= 0u)
    {
        idx_db = B0_N_HALFSAMPLE ;
    }
    pri_b0_db_cnt++;

    Debug_IPRI0++;
    ipri_acc32 = 0U;

    if (flg_pri_b0_check == 0U)
    {
        /* Reset peak value */
        max_ipri_b0 = 0U;

        flg_pri_b0_check = 1U;
    }

    if(flg_pri_b0_check == 1U)
    {
        /* Max value search */
        for(idx = idx_db; idx < (idx_db + B0_N_HALFSAMPLE); idx++)
        {
            ipri_acc32 = ipri_acc32 + pri_b0_buffer[idx];
        }

        ipri_acc16 = (uint16_T)(ipri_acc32/B0_N_HALFSAMPLE);
        
        if(ipri_acc16 > max_ipri_b0)
        {
            max_ipri_b0 = ipri_acc16;
        }
    }

#ifdef  EN_DEBUG_PRI_SEC
    /* Debug */
    if (DebugFlgPriTest)
    {
        for (idx = idx_db; idx < (idx_db + B0_N_HALFSAMPLE); idx++)
        {
            DebugPrib0Buffer[idx_buff] = pri_b0_buffer[idx];
            idx_buff++;
        }

        if (idx_buff == DEBUG_N_SAMPLE_TEST)
        {
            DebugFlgPriTest = 0u;
            idx_buff = 0u;
        }
    }
#endif
    
}

void ipri_b1_saradcconf_ch16_ipri_b1_dma(void) {

    uint32_T ipri_acc32;
    uint16_T ipri_acc16;
    uint8_T  idx;    
    uint8_T  idx_db = 0u ; 

    if ((pri_b1_db_cnt%2u)!= 0u)
    {
        idx_db = B1_N_HALFSAMPLE ;
    }
    pri_b1_db_cnt++;
      
    Debug_IPRI1++;
    ipri_acc32 = 0U;

    if (flg_pri_b1_check == 0U)
    {
        /* Reset peak value */
        max_ipri_b1 = 0;

        flg_pri_b1_check = 1U;
    }

    if(flg_pri_b1_check == 1U)
    {
        /* Max value search */
         for(idx = idx_db; idx < (idx_db + B1_N_HALFSAMPLE); idx++)
        {
            ipri_acc32 = ipri_acc32 + pri_b1_buffer[idx];
        }

        ipri_acc16 = (uint16_T)(ipri_acc32/B1_N_HALFSAMPLE);
        
        if(ipri_acc16 > max_ipri_b1)
        {
            max_ipri_b1 = ipri_acc16;
        }
    }

#ifdef  EN_DEBUG_PRI_SEC
    /* Debug */
    if (DebugFlgPriTest_b1)
    {
        for (idx = idx_db; idx < (idx_db + B1_N_HALFSAMPLE); idx++)
        {
            DebugPrib1Buffer[idx_buff_b1] = pri_b1_buffer[idx];
            idx_buff_b1++;
        }

        if (idx_buff_b1 == DEBUG_N_SAMPLE_TEST)
        {
            DebugFlgPriTest_b1 = 0u;
            idx_buff_b1 = 0u;
        }
    }
#endif
    
}


void isec_b0_saradcconf_ch5_isec_b0_dma(void) {

    uint8_T idx;
    uint32_T cylinder;
    uint16_T vtiKillThr;
    uint8_T  idx_db = 0u;
    uint8_T epwsbank0 = EPWSBank0;
    uint8_T cntDiscardIsecBank0;
    uint8_T cnt_isec_b0;
    uint16_T localIsecBuf[ISEC0_N_HALFSAMPLE];
    uint16_T minISec;
    uint8_T i;

    cylinder = *current_cylinder;
    if (cylinder < N_CYLINDER)
    {
        cntDiscardIsecBank0 = CntDiscardIsecBank0;
        cnt_isec_b0 = Cnt_isec_b0;

        if ((sec_b0_db_cnt%2u)!= 0u)
        {
            idx_db = ISEC0_N_HALFSAMPLE ;
        }
        sec_b0_db_cnt++;

        /* Set VCharge target */
        idx = cylinder & 0x03U;
        VtISecThr[cylinder] = (VtThrCrgCION[idx]/4); /* scaled in SDADC points */

        /* Secondary threshold to enable EPWS */
        VtISecThrEPWS[cylinder] = VtThrPalPulseOn[cylinder] / 4u; /* scaled in SDADC points */
        
        Debug_ISEC0++;

        for(i = 0u; i < ISEC0_N_HALFSAMPLE; i++)
        {
            localIsecBuf[i] = isec_b0_buffer[idx_db +i];
        }
        InsertionSort(localIsecBuf, ISEC0_N_HALFSAMPLE);

        minISec = localIsecBuf[MINISECSAMPIDX];

        /* Primary buffer check */
        if(flg_pri_b0_check == 1U)
        {
            /* Store IPri peak value */
            VtILeadPeak[cylinder] = max_ipri_b0 << 2u;

            /* Store ISec peak value */
            VtIShotPeak[cylinder] = localIsecBuf[ISEC0_N_HALFSAMPLE-1];

            flg_pri_b0_check = 0U;
            pri_b0_db_cnt = 0u;
            MSparkCmd_ISecDma(cylinder);
        }
        
#ifdef ENABLE_MOS_SWITCH_ISEC
        if (Cnt_isec_b0 < MAX_uint8_T)
        {
            Cnt_isec_b0++;
        }
        else
        {
            Cnt_isec_b0 = MAX_uint8_T;
        }
        
        /* Discard the first buffers */
        if (cnt_isec_b0 >= cntDiscardIsecBank0)
        {
            /* EPWS Management */
            if ((minISec < VtISecThrEPWS[cylinder]) && (epwsbank0 == EPWS_ENABLE) && (EPWSTimeout[cylinder] == 0u))
            {
                switch (cylinder)
                {
                    case 0:
                        gtm_atomStart_inl(&ATOMD2, ATOM_CHANNEL0);
                        gtm_mcsSetTriggerBit_inl(&MCSD1, CYL0_1_TRIG_BIT);
                        break;
                     case 2:
                        gtm_atomStart_inl(&ATOMD2, ATOM_CHANNEL2);
                        gtm_mcsSetTriggerBit_inl(&MCSD1, CYL2_3_TRIG_BIT);
                        break;
                     case 4:
                         gtm_atomStart_inl(&ATOMD2, ATOM_CHANNEL4);
                         gtm_mcsSetTriggerBit_inl(&MCSD1, CYL4_5_TRIG_BIT);
                        break;
                     case 6:
                        gtm_atomStart_inl(&ATOMD2, ATOM_CHANNEL6);
                        gtm_mcsSetTriggerBit_inl(&MCSD1, CYL6_7_TRIG_BIT);
                        break;
                    default:
                        break;
                }
                
                EPWSBank0 = EPWS_WAIT_LAST_PULSE;
                epwsbank0 = EPWSBank0;
            }
            else 
            {
                if (VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_EN)
                {
                    vtiKillThr = VTIKILLTHR[Cyl2PMOS[cylinder]] >> 2u;
                }
                
                if((epwsbank0 == EPWS_DISABLE) ||(epwsbank0 == EPWS_LAST_PULSE_ACTIVATED))
                {
                    FlgIShotTout[0] = cnt_isec_b0 > CNT_FORCE_SWPMOS_ISEC;
                    if ((minISec < VtISecThr[cylinder]) || (FlgIShotTout[0] == 1u))
                    {
                        switch(cylinder)
                        {
                            case 0:
                            case 4:
                                if (FlgPmosOpen[cylinder] == 1u)
                                {
                                    /* PMOS0_4 */
                                    gtm_mcsSetTriggerBit_inl(&MCSD3, 12);
                                    FlgPmosOpen[cylinder] = 0u;
                                }
                                if (((VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_EN) && (minISec < vtiKillThr)) || (VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_DIS) || (FlgIShotTout[0] == 1u))
                                {
                                    /* NMOS0_4 */
                                    VtNrStartIonDMAIdx[cylinder] = DMA_0.TCD[DMA_CH3].CITER;
                                    Gtm_Eisb_MosStopInl(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_ATOM2_CHANNEL1_ARU_ENABLE);
                                    /* Store trigger value */
                                    VtISecMin[cylinder] = minISec;
                                    DMA_Stop(DMA_CH1);
                                    VtDmaRunning[0] = 0u;
                                }
                                break;
                            case 2:
                            case 6:
                                if (FlgPmosOpen[cylinder] == 1u)
                                {
                                    /* PMOS2_6 */
                                    gtm_mcsSetTriggerBit_inl(&MCSD3, 14);
                                    FlgPmosOpen[cylinder] = 0u;
                                }
                                if (((VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_EN) && (minISec < vtiKillThr)) || (VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_DIS) || (FlgIShotTout[0] == 1u))
                                {
                                    /* NMOS2_6 */
                                    VtNrStartIonDMAIdx[cylinder] = DMA_0.TCD[DMA_CH2].CITER;
                                    Gtm_Eisb_MosStopInl(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_ATOM2_CHANNEL5_ARU_ENABLE);
                                    /* Store trigger value */
                                    VtISecMin[cylinder] = minISec;
                                    DMA_Stop(DMA_CH1);
                                    VtDmaRunning[0] = 0u;
                                }
                                break;
                            default:
                                break;
                        }
                        Cnt_isec_b0_thr = cnt_isec_b0;
                    }
                }
            }
        }
    }
#endif
#ifdef  EN_DEBUG_PRI_SEC    
    /* Debug */
    if (DebugFlgSecTest)
    {
        for (idx = idx_db; idx < (idx_db + ISEC0_N_HALFSAMPLE); idx++)
        {
            DebugSecb0Buffer[idx_buff_sec] = isec_b0_buffer[idx];
            idx_buff_sec++;
        }

        if (idx_buff_sec == DEBUG_N_SAMPLE_TEST)
        {
            DebugFlgSecTest = 0u;
            idx_buff_sec = 0u;
        }
    }
            
#endif
    
}


void isec_b1_saradcconf_ch17_isec_b1_dma(void) {

    uint8_T idx;
    uint32_T cylinder;
    uint16_T vtiKillThr;
    uint8_T  idx_db = 0u;
    uint8_T epwsbank1 = EPWSBank1;
    uint8_T cntDiscardIsecBank1;
    uint8_T cnt_isec_b1;
    uint16_T localIsecBuf[ISEC1_N_HALFSAMPLE];
    uint16_T minISec;
    uint8_T i;

    cylinder = *buck1_current_cylinder;
    
    if (cylinder < N_CYLINDER)
    {
        cntDiscardIsecBank1 = CntDiscardIsecBank1;
        cnt_isec_b1 = Cnt_isec_b1;

        if ((sec_b1_db_cnt%2u)!= 0u)
        {
            idx_db = ISEC1_N_HALFSAMPLE ;
        }
        sec_b1_db_cnt++;

        /* Set VCharge target */
        idx = cylinder & 0x03U;
        VtISecThr[cylinder] = (VtThrCrgCION[idx]/4);
        
        /* Secondary threshold to enable EPWS */
        VtISecThrEPWS[cylinder] = VtThrPalPulseOn[cylinder] / 4u; /* scaled in SDADC points */

        Debug_ISEC1++;

        for(i = 0u; i < ISEC1_N_HALFSAMPLE; i++)
        {
            localIsecBuf[i] = isec_b1_buffer[idx_db +i];
        }

        InsertionSort(localIsecBuf, ISEC1_N_HALFSAMPLE);

        minISec = localIsecBuf[MINISECSAMPIDX];
        
        /* Primary buffer check */
        if(flg_pri_b1_check == 1U)
        {
            /* Store IPri peak value */
            VtILeadPeak[cylinder] = max_ipri_b1 << 2u;

            /* Store ISec peak value */
            VtIShotPeak[cylinder] = localIsecBuf[ISEC1_N_HALFSAMPLE-1];;
            flg_pri_b1_check = 0U;
            pri_b1_db_cnt = 0u;
            MSparkCmd_ISecDma(cylinder);
        }
        
#ifdef ENABLE_MOS_SWITCH_ISEC
        if (Cnt_isec_b1 < MAX_uint8_T)
        {
            Cnt_isec_b1++;
        }
        else
        {
            Cnt_isec_b1 = MAX_uint8_T;
        }

        if (cnt_isec_b1 >= cntDiscardIsecBank1)
        {
            /* EPWS Management */
            if ((minISec < VtISecThrEPWS[cylinder]) && (epwsbank1 == EPWS_ENABLE) && (EPWSTimeout[cylinder] == 0u))
            {
                switch (cylinder)
                {
                    case 1:
                        gtm_atomStart_inl(&ATOMD2, ATOM_CHANNEL1);
                        gtm_mcsSetTriggerBit_inl(&MCSD2, CYL0_1_TRIG_BIT);
                        break;
                     case 3:
                        gtm_atomStart_inl(&ATOMD2, ATOM_CHANNEL3);
                        gtm_mcsSetTriggerBit_inl(&MCSD2, CYL2_3_TRIG_BIT);
                        break;
                     case 5:
                         gtm_atomStart_inl(&ATOMD2, ATOM_CHANNEL5);
                         gtm_mcsSetTriggerBit_inl(&MCSD2, CYL4_5_TRIG_BIT);
                        break;
                     case 7:
                        gtm_atomStart_inl(&ATOMD2, ATOM_CHANNEL7);
                        gtm_mcsSetTriggerBit_inl(&MCSD2, CYL6_7_TRIG_BIT);
                        break;
                    default:
                        break;
                }
                
                EPWSBank1 = EPWS_WAIT_LAST_PULSE;
                epwsbank1 = EPWSBank1;
            }
            else 
            {
                if (VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_EN)
                {
                    vtiKillThr = VTIKILLTHR[Cyl2PMOS[cylinder]] >> 2u;
                }
                
                if((epwsbank1 == EPWS_DISABLE) ||(epwsbank1 == EPWS_LAST_PULSE_ACTIVATED))
                {
                    FlgIShotTout[1] = cnt_isec_b1 > CNT_FORCE_SWPMOS_ISEC;
                    if ((minISec < VtISecThr[cylinder]) || (FlgIShotTout[1] == 1u))
                    {
                        switch(cylinder)
                        {
                            case 1:
                            case 5:
                                if (FlgPmosOpen[cylinder] == 1u)
                                {
                                    /* PMOS1_5 */
                                    gtm_mcsSetTriggerBit_inl(&MCSD3, 13);
                                    FlgPmosOpen[cylinder] = 0u;
                                }
                                if (((VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_EN) && (minISec < vtiKillThr)) || (VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_DIS) || (FlgIShotTout[1] == 1u))
                                {
                                    /* NMOS1_5 */
                                    VtNrStartIonDMAIdx[cylinder] = DMA_0.TCD[DMA_CH24].CITER;
                                    Gtm_Eisb_MosStopInl(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_ATOM2_CHANNEL3_ARU_ENABLE);
                                    /* Store trigger value */
                                    VtISecMin[cylinder] = minISec;
                                    DMA_Stop(DMA_CH17);
                                    VtDmaRunning[1] = 0u;
                                }
                                break;
                            case 3:
                            case 7:
                                if (FlgPmosOpen[cylinder] == 1u)
                                {
                                    /* PMOS3_7 */
                                    gtm_mcsSetTriggerBit_inl(&MCSD3, 15);
                                    FlgPmosOpen[cylinder] = 0u;
                                }
                                if (((VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_EN) && (minISec < vtiKillThr)) || (VtFlgCIonDisc[Cyl2PMOS[cylinder]] == FAST_DISCH_DIS) || (FlgIShotTout[1] == 1u))
                                {
                                    /* NMOS3_7 */
                                    VtNrStartIonDMAIdx[cylinder] = DMA_0.TCD[DMA_CH25].CITER;
                                    Gtm_Eisb_MosStopInl(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_ATOM2_CHANNEL7_ARU_ENABLE);
                                    /* Store trigger value */
                                    VtISecMin[cylinder] = minISec;
                                    DMA_Stop(DMA_CH17);
                                    VtDmaRunning[1] = 0u;
                                }
                                break;
                            default:
                                break;
                        }
                        Cnt_isec_b1_thr = cnt_isec_b1;
                    }
                }
            }    
        }
    }

#endif
#ifdef  EN_DEBUG_PRI_SEC    
    /* Debug */
    if (DebugFlgSecTest_b1)
    {
        for (idx = idx_db; idx < (idx_db + ISEC0_N_HALFSAMPLE); idx++)
        {
            DebugSecb1Buffer[idx_buff_sec_b1] = isec_b1_buffer[idx];
            idx_buff_sec_b1++;
        }

        // DebugPrib0Buffer[IdxPrib0] = ipri_acc16;
        // DebugPrib0Buffer[IdxPrib0] = pri_b0_buffer;
        // IdxPrib0++;
        if (idx_buff_sec_b1 == DEBUG_N_SAMPLE_TEST)
        {
            DebugFlgSecTest_b1 = 0u;
            idx_buff_sec_b1 = 0u;
        }
    }
#endif
}

#pragma ghs section text=default


/*****************************************/
/****     ION DMA Call Back          *****/
/*****************************************/

void ion0_4_saradcconf_ch44_dma_ion0_4(void) {

    gtm_tomDisable(&TOMD1, TOM_CHANNEL6);

    IonAcq_EndOfAcq(ION_CHANNEL_A, (uint8_T)NextIonCyl[ION_CHANNEL_A]);

    Gtm_Eisb_MosCheck((uint8_T)NextIonCyl[ION_CHANNEL_A]);
}

void ion1_5_saradcconf_ch48_dma_ion1_5(void) {

    gtm_tomDisable(&TOMD1, TOM_CHANNEL7);

    IonAcq_EndOfAcq(ION_CHANNEL_B, (uint8_T)NextIonCyl[ION_CHANNEL_B]);

    Gtm_Eisb_MosCheck((uint8_T)NextIonCyl[ION_CHANNEL_B]);
}

void ion2_6_saradcconf_ch45_dma_ion2_6(void) {

    gtm_tomDisable(&TOMD1, TOM_CHANNEL6);

    IonAcq_EndOfAcq(ION_CHANNEL_C, (uint8_T)NextIonCyl[ION_CHANNEL_C]);

    Gtm_Eisb_MosCheck((uint8_T)NextIonCyl[ION_CHANNEL_C]);
}


void ion3_7_saradcconf_ch52_dma_ion3_7(void) {

    gtm_tomDisable(&TOMD1, TOM_CHANNEL7);

    IonAcq_EndOfAcq(ION_CHANNEL_D, (uint8_T)NextIonCyl[ION_CHANNEL_D]);

    Gtm_Eisb_MosCheck((uint8_T)NextIonCyl[ION_CHANNEL_D]);
}

void Gtm_Eisb_TomEnable(void)
{
    gtm_cmuStart(&CMUD1);

    /* Output Enable */
    gtm_tomOutputEnable(&TOMD1, TOM_CHANNEL2);
    gtm_tomOutputEnable(&TOMD1, TOM_CHANNEL3);
    /* Tom start */
    gtm_tomEnable(&TOMD1, TOM_CHANNEL2);
    gtm_tomEnable(&TOMD1, TOM_CHANNEL3);

    /* Trigger TOM0 0 to 7 channels */
    TOMD1.tom->GTM_TOM_TGC_REG(0,GLB_CTRL).R = 1U;
}

/******************************************************************************
**   Function    : spark_cb_ch0
**
**   Description:
**    Callback function for spark trigger edge
**
**   Parameters :
**    [in]  GTM_TIMDriver *timd, uint8_t channel
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void spark_cb_ch0(GTM_TIMDriver *timd, uint8_T channel)
{
    Gtm_Eisb_Spark_cb(timd,channel);
    /* Reset counter */
    VtCntIgnWaitSpEvnt[0] = 0u;
}

/******************************************************************************
**   Function    : spark_cb_ch1
**
**   Description:
**    Callback function for spark trigger edge
**
**   Parameters :
**    [in]  GTM_TIMDriver *timd, uint8_t channel
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void spark_cb_ch1(GTM_TIMDriver *timd, uint8_T channel)
{
    Gtm_Eisb_Spark_cb(timd,channel);
    /* Reset counter */
    VtCntIgnWaitSpEvnt[1] = 0u;
}

void DMA_StopGlob(uint8_T DMA_CH) {
    if (DMA_CH == DMA_CH0 || DMA_CH == DMA_CH1)
    {
        SAR0_StopConversion();
        DMA_Disable(DMA_CH);
        DMAMUX0_Reset(DMA_CH);
    }
    else if (DMA_CH == DMA_CH16 || DMA_CH == DMA_CH17)
    {
        SAR2_StopConversion();
        DMA_Disable(DMA_CH);
        DMAMUX2_Reset(DMA_CH);
    }
}

/*
 * =================================================================
 * --------------------- LOCAL FUNCTIONS ---------------------------
 * =================================================================
 */
/*
 * =================================================================
 * -------------------- CRANK EVENT CALLBACK -----------------------
 * =================================================================
 */
static uint8_T Debug_event_tooth_callback = 0u;
static void event_tooth_callback(uint32_t ticks) {
    (void) ticks;
    Debug_event_tooth_callback++;
    //LED2_TOGGLE();
}

static uint32_T getTimeoutValue(uint8_T clk_src, uint32_T timeout) {
    uint32_T clock;
    uint32_T value;

    clock = gtm_cmuGetClock(&CMUD1, SPC5_GTM_CMU_CFGU_CLK, clk_src);
    value = (((timeout * clock) / (1000 * 1000)) - 1);

    return value;
}

/*
 * Initializes the MCS.
 */
static void initMCS_BANK0(GTM_MCSDriver *mcsd) {
    uint32_t mcs0_base;

    /* Reset the RAM and wait for action complete */
    gtm_mcsResetRAM(mcsd);
    while (gtm_mcsGetResetRAM(mcsd) == 1U) {
        ;
    }

    /* Set round-robin scheduler policy */
    gtm_mcsSetScheduler(mcsd, SPC5_GTM_MCS_SCHEDULER_ROUND_ROBIN);

    /* Load the MCS program */
    gtm_mcsLoadProgram(mcsd, mcs0_mem, SIZE_MCS0_MEM, OFFSET_MCS0_MEM);

    mcs0_base = gtm_mcsGetRAMBaseAddress(mcsd);

    cyl0_isr_var = (vuint32_t *)(mcs0_base + (LABEL_MCS0_MEM_CYL0_INT_VAR << 2));
    cyl2_isr_var = (vuint32_t *)(mcs0_base + (LABEL_MCS0_MEM_CYL2_INT_VAR << 2));
    cyl4_isr_var = (vuint32_t *)(mcs0_base + (LABEL_MCS0_MEM_CYL4_INT_VAR << 2));
    cyl6_isr_var = (vuint32_t *)(mcs0_base + (LABEL_MCS0_MEM_CYL6_INT_VAR << 2));

    buck_step_isr_var   = (vuint32_t *)(mcs0_base + (LABEL_MCS0_MEM_BUCK0_STEP_ISR_VAR << 2));
    current_cylinder    = (vuint32_t *)(mcs0_base + (LABEL_MCS0_MEM_BUCK0_CURRENT_CYLINDER << 2));

    buck0_primary_adc_sample = (vuint32_t *)(mcs0_base + (LABEL_MCS0_MEM_BUCK0_PRIMARY_SAMPLE << 2));
    cylinder_primary_b0 = (vuint32_t *)(mcs0_base + (LABEL_MCS0_MEM_PRI_B0_CYL_TH << 2));

    /* Enable the channel Cylinder 0 */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL0);

    /* Enable the channel Cylinder 2 */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL1);

    /* Enable the channel Cylinder 4 */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL2);

    /* Enable the channel Cylinder 6 */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL3);


    /* Enable the mcs primary winding current channel */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL5);

    /* Enable the mcs buck enable channel */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL6);

    /* Enable the mcs coil channel */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL7);
}

static void initMCS_BANK1(GTM_MCSDriver *mcsd) {
    uint32_t mcs1_base;

    /* Reset the RAM and wait for action complete */
    gtm_mcsResetRAM(mcsd);
    while (gtm_mcsGetResetRAM(mcsd) == 1U) {
        ;
    }

    /* Set round-robin scheduler policy */
    gtm_mcsSetScheduler(mcsd, SPC5_GTM_MCS_SCHEDULER_ROUND_ROBIN);

    /* Load the MCS program */
    gtm_mcsLoadProgram(mcsd, mcs1_mem, SIZE_MCS1_MEM, OFFSET_MCS1_MEM);

    mcs1_base = gtm_mcsGetRAMBaseAddress(mcsd);

    cyl1_isr_var = (vuint32_t *)(mcs1_base + (LABEL_MCS1_MEM_CYL1_INT_VAR << 2));
    cyl3_isr_var = (vuint32_t *)(mcs1_base + (LABEL_MCS1_MEM_CYL3_INT_VAR << 2));
    cyl5_isr_var = (vuint32_t *)(mcs1_base + (LABEL_MCS1_MEM_CYL5_INT_VAR << 2));
    cyl7_isr_var = (vuint32_t *)(mcs1_base + (LABEL_MCS1_MEM_CYL7_INT_VAR << 2));

    buck1_step_isr_var   = (vuint32_t *)(mcs1_base + (LABEL_MCS1_MEM_BUCK1_STEP_ISR_VAR << 2));
    buck1_current_cylinder    = (vuint32_t *)(mcs1_base + (LABEL_MCS1_MEM_BUCK1_CURRENT_CYLINDER << 2));

    buck1_primary_adc_sample = (vuint32_t *)(mcs1_base + (LABEL_MCS1_MEM_BUCK1_PRIMARY_SAMPLE << 2));
    cylinder_primary_b1 = (vuint32_t *)(mcs1_base + (LABEL_MCS1_MEM_PRI_B1_CYL_TH << 2));


    /* Enable the channel Cylinder 1 */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL0);

    /* Enable the channel Cylinder 3 */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL1);

    /* Enable the channel Cylinder 5 */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL2);

    /* Enable the channel Cylinder 7 */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL3);

    /* Enable the mcs primary winding current channel */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL5);

    /* Enable the mcs buck1 enable channel */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL6);

    /* Enable the mcs buck1 channel */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL7);
}

static void initMCS2_MOS_IPRI_ISEC(GTM_MCSDriver *mcsd) {
    /* Reset the RAM and wait for action complete */
    gtm_mcsResetRAM(mcsd);
    while (gtm_mcsGetResetRAM(mcsd) == 1U) {
        ;
    }

    /* Set round-robin scheduler policy */
    gtm_mcsSetScheduler(mcsd, SPC5_GTM_MCS_SCHEDULER_ROUND_ROBIN);

    /* Load the MCS program */
    gtm_mcsLoadProgram(mcsd, mcs2_mem, SIZE_MCS2_MEM, OFFSET_MCS2_MEM);

    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL0);

    gtm_mcsSetTriggerBit(mcsd, MCS_CHANNEL0);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL0, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_0); //PMOS0_4
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL0);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_1); //NMOS0_4
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL1);

    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL1);

    gtm_mcsSetTriggerBit(mcsd, MCS_CHANNEL1);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL2, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_2); //PMOS1_5
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL2);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_3); //NMOS1_5
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL3);

    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL2);

    gtm_mcsSetTriggerBit(mcsd, MCS_CHANNEL2);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL4, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_4); //PMOS2_6
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL4);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_5); //NMOS2_6
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL5);

    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL3);

    gtm_mcsSetTriggerBit(mcsd, MCS_CHANNEL3);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL6, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_6); //PMOS3_7
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL6);

    gtm_atomSetDataSource(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_WRITE_ADDRESS_MCS2_MCS2_7); //NMOS3_7
    gtm_atomStart(&ATOMD3, ATOM_CHANNEL7);


    /* Enable Current IPRI - ISEC Channels */
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL4);
    gtm_mcsEnableChannel(mcsd, MCS_CHANNEL5);
}

/******************************************************************************
**   Function    : DMA_ReconfigPri
**
**   Description:
**    DMA configuration used to start the acquisition of primary current
**
**   Parameters :
**    [in]  DMA Channel number
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void DMA_ReconfigPri(uint8_T DMA_CH) {
    if (DMA_CH == DMA_CH0)
    {
        SAR0_DMADisable();
        DMAMUX0_ConfigEnable(DMA_CH, DMAMUX0_ADC_SAR_0_EOC);
        DMA_16_16_1x_Ny_Cr(DMA_CH, (uint32_T)&(SARADC_0.ICDR[I_PRI_B0_PER_AN].R)+2, (uint32_T)&pri_b0_buffer, B0_N_SAMPLE, DMA_BWC_LOW, DMA_EN_INT, DMA_EN_INT_HALF, DMA_NO_LNK, 0u);
        DMA_Enable(DMA_CH);
        SAR0_ConfigScanSingleCh(I_PRI_B0_PER_AN);
        SAR0_StartConversion();
    }
    else if (DMA_CH == DMA_CH16)
    {
        SAR2_DMADisable();
        DMAMUX2_ConfigEnable(DMA_CH,DMAMUX2_ADC_SAR_2_EOC);
        DMA_16_16_1x_Ny_Cr(DMA_CH, (uint32_T)&(SARADC_2.ICDR[I_PRI_B1_PER_AN].R)+2, (uint32_T)&pri_b1_buffer, B1_N_SAMPLE, DMA_BWC_LOW, DMA_EN_INT, DMA_EN_INT_HALF, DMA_NO_LNK, 0u);
        DMA_Enable(DMA_CH);
        SAR2_ConfigScanSingleCh(I_PRI_B1_PER_AN);
        SAR2_StartConversion();
    }
}

/******************************************************************************
**   Function    : DMA_ReconfigSec
**
**   Description:
**    DMA reconfiguration used to start the acquisition of secondary current
**
**   Parameters :
**    [in]  DMA Channel number
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void DMA_ReconfigSec(uint8_T DMA_CH, uint8_T cyl) {
    if (DMA_CH == DMA_CH1)
    {
        SAR0_StopConversion();
        DMA_Disable(DMA_CH0);
        DMAMUX0_Reset(DMA_CH0);
        DMAMUX0_ConfigEnable(DMA_CH, DMAMUX0_ADC_SAR_0_EOC);
        DMA_16_16_1x_Ny_Cr(DMA_CH, (uint32_T)&(SARADC_0.ICDR[I_SEC_B0_PER_AN].R)+2, (uint32_T)&isec_b0_buffer, ISEC0_N_SAMPLE, DMA_BWC_LOW, DMA_EN_INT, DMA_EN_INT_HALF, DMA_NO_LNK, 0u);
        DMA_Enable(DMA_CH);
        SAR0_ConfigScanSingleCh(I_SEC_B0_PER_AN);
        Cnt_isec_b0 = 0U;
        FlgPmosOpen[cyl] = 1u;
        sec_b0_db_cnt = 0u;
        VtDmaRunning[0] = 1u;
        SAR0_StartConversion();
    }
    else if (DMA_CH == DMA_CH17)
    {
        SAR2_StopConversion();
        DMA_Disable(DMA_CH16);
        DMAMUX2_Reset(DMA_CH16);
        DMAMUX2_ConfigEnable(DMA_CH,DMAMUX2_ADC_SAR_2_EOC);
        DMA_16_16_1x_Ny_Cr(DMA_CH, (uint32_T)&(SARADC_2.ICDR[I_SEC_B1_PER_AN].R)+2, (uint32_T)&isec_b1_buffer, ISEC1_N_SAMPLE, DMA_BWC_LOW, DMA_EN_INT, DMA_EN_INT_HALF, DMA_NO_LNK, 0u);
        DMA_Enable(DMA_CH);
        SAR2_ConfigScanSingleCh(I_SEC_B1_PER_AN);
        Cnt_isec_b1 = 0U;
        FlgPmosOpen[cyl] = 1u;
        sec_b1_db_cnt = 0u;
        VtDmaRunning[1] = 1u;
        SAR2_StartConversion();
    }
}

/******************************************************************************
**   Function    : DMA_Stop
**
**   Description:
**    DMA configuration used to stop the acquisition of the secondary current
**
**   Parameters :
**    [in]  DMA Channel number
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#pragma ghs section text=".vletext_c2"
static void DMA_Stop(uint8_T DMA_CH) {

    if (DMA_CH == DMA_CH0 || DMA_CH == DMA_CH1)
    {
        SAR0_StopConversion();
	    DMA_Disable(DMA_CH);
        DMAMUX0_Reset(DMA_CH);
    }
    else if (DMA_CH == DMA_CH16 || DMA_CH == DMA_CH17)
    {
        SAR2_StopConversion();
		DMA_Disable(DMA_CH);
        DMAMUX2_Reset(DMA_CH);
    }
}
#pragma ghs section text=default

/******************************************************************************
**   Function    : Gtm_Eisb_SetParams
**
**   Description:
**    Set EISB parameters
**
**   Parameters :
**    cylinder
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void Gtm_Eisb_SetParams(uint8_T cylinder)
{
    uint16_T dutyCharge[4];
    uint16_T durationCharge[3];
    uint16_T dutyDischarge[3];
    uint16_T durationDischarge[2];

    dutyCharge[0] = ((TbRiseBkDutyOut[cylinder] * BUCK_CH_D2_PERIOD) >> 7)/ 100u;
    dutyCharge[1] = ((TbRiseBkDutyOut[cylinder + 8] * BUCK_CH_D3_PERIOD) >> 7)/ 100u;
    dutyCharge[2] = ((TbRiseBkDutyOut[cylinder + 16] * BUCK_CH_D4_PERIOD) >> 7)/ 100u;
    dutyCharge[3] = ((TbRiseBkDutyOut[cylinder + 24] * BUCK_CH_D5_PERIOD) >> 7)/ 100u;

    if ((cylinder & 0x01) == 0u)
    {
        dutyDischarge[0] = ((TbFallBkDutyOut[0] * BUCK_DISCH_D6_PERIOD) >> 7)/ 100u;
        dutyDischarge[1] = ((TbFallBkDutyOut[2] * BUCK_DISCH_D7_PERIOD) >> 7)/ 100u;
        dutyDischarge[2] = ((TbFallBkDutyOut[4] * BUCK_DISCH_D8_PERIOD) >> 7)/ 100u;
    }
    else
    {
        dutyDischarge[0] = ((TbFallBkDutyOut[1] * BUCK_DISCH_D6_PERIOD) >> 7)/ 100u;
        dutyDischarge[1] = ((TbFallBkDutyOut[3] * BUCK_DISCH_D7_PERIOD) >> 7)/ 100u;
        dutyDischarge[2] = ((TbFallBkDutyOut[5] * BUCK_DISCH_D8_PERIOD) >> 7)/ 100u;
    }
    
    durationCharge[0] = VtDelayBkF1[cylinder] + VtDeltaBkEn[cylinder];
    durationCharge[1] = VtDelayBkF2[cylinder];
    durationCharge[2] = VtDelayBkF3[cylinder];
    
    durationDischarge[0] = VtDelayBkDisc[cylinder];
    durationDischarge[1] = VtTSlopeBk[cylinder];
    
    IGN_HGISet_CmdOutDelay(cylinder, VtDeltaBkEn[cylinder]);
    IGN_HGISet_BuckEnDelay(cylinder, VtDelayBkEn[cylinder]);
    IGN_HGISet_BuckRefChargePh(cylinder, dutyCharge[0], durationCharge[0], dutyCharge[1], durationCharge[1], dutyCharge[2], durationCharge[2], dutyCharge[3], durationDischarge[0]);
    IGN_HGISet_BuckRefDisChargePh(cylinder, dutyDischarge[0], durationDischarge[1], dutyDischarge[1], durationDischarge[1], dutyDischarge[2], durationDischarge[1]);
    IGN_HGISet_MosTime(cylinder, VtTMKWindow[cylinder], MOS_TIMEOUT);
}

/******************************************************************************
**   Function    : Gtm_Eisb_SetEpwsParams
**
**   Description:
**    Set EISB parameters in EPWS mode
**
**   Parameters :
**    cylinder
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void Gtm_Eisb_SetEpwsParams(uint8_T cylinder)
{
    uint8_T pulseNmb;
    pulseNmb = 1u;
    
    IGN_HGISet_EpwsLastPulseDur(cylinder, VtTOnLastPlaPulseOL[cylinder]);
    IGN_HGISet_EpwsTimeOut(cylinder, ToPlaOn);
    for (uint8_T epws_phase = 0u; epws_phase < EPWS_PHASE_NUM; epws_phase++)
    {
        if (epws_phase == 3u)
        {
            if (VtNPlaPulseOL[cylinder] < 4u)
            {
                VtNPlaPulseOL[cylinder] = 4u;
            }
            pulseNmb = (uint8_T)(VtNPlaPulseOL[cylinder] - 3u);
        }
        
        IGN_HGISet_EpwsPhase(cylinder, epws_phase, pulseNmb, VtTOffPlaPulseOL[cylinder] + VtTOnPlaPulseOL[cylinder], VtTOnPlaPulseOL[cylinder]);
    }
}

/******************************************************************************
**   Function    : effDwellTimeCalc(cyl)
**
**   Description:
**    EffDwellTime calculation 
**
**   Parameters :
**    [in]  cylinder
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void effDwellTimeCalc(const uint8_T cyl)
{
    if(AbsTimeOutClose[cyl] > AbsTimeOutOpen[cyl])
    {
        EffDwellTime[cyl] = AbsTimeOutClose[cyl] - AbsTimeOutOpen[cyl];
    }
    else
    {
        EffDwellTime[cyl] = AbsTimeOutClose[cyl] + (MAX_uint24_T - AbsTimeOutOpen[cyl]);
    }
}

/******************************************************************************
**   Function    : EPWS_MosConfig
**
**   Description:
**    Configuration of the PNMOS delay in case of EPWS
**
**   Parameters :
**    [in]  uint8_T bank
**    [in]  uint8_T timeout
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void EPWS_MosDelayConfig(uint8_T bank, uint8_T timeout)
{
    if (bank == 0u)
    {
        Cnt_isec_b0 = 0u;
        CntDiscardIsecBank0 = NISECDISCSAMPEPWS;
        if (timeout == 0u)
        {
            EPWSBank0 = EPWS_LAST_PULSE_ACTIVATED;
        }
        else if (timeout == 1u)
        {
            EPWSBank0 = EPWS_DISABLE;
        }
    } 
    else if (bank == 1u)
    {
        Cnt_isec_b1 = 0u;
        CntDiscardIsecBank1 = NISECDISCSAMPEPWS;
        if (timeout == 0u)
        {
            EPWSBank1 = EPWS_LAST_PULSE_ACTIVATED;
        }
        else if (timeout == 1u)
        {
            EPWSBank1 = EPWS_DISABLE;
        }
    }
}

/******************************************************************************
**   Function    : Gtm_Eisb_ConfigAfterBuckDiag
**
**   Description:
**    Set cylinder parameters after buck diagnosis at power on
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void Gtm_Eisb_ConfigAfterBuckDiag(void)
{
    for (uint8_T cylinder = 0u; cylinder < N_CYLINDER; cylinder++)
    {
        Gtm_Eisb_SetParams(cylinder);
    }

    isbSetup(&EISB);
}

/******************************************************************************
**   Function    : Gtm_Eisb_DisableTimCh
**
**   Description:
**    Set cylinder parameters after buck diagnosis at power on
**
**   Parameters :
**    timd
**    channel
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void Gtm_Eisb_DisableTimCh(GTM_TIMDriver *timd, uint8_T channel)
{
    if((ENFILTTRIGIN != 0u) && (FlgSyncPhased == 1u))
    {
        /* TIM STOP */
        gtm_timStop(timd, channel);
        /* TIM interrupt clear */
        gtm_timAckInt(timd, channel, SPC5_GTM_TIM_IRQ_STATUS_NEW_VALUE);
    }
}

/******************************************************************************
**   Function    : Gtm_Eisb_StartSparkDurationMeasure
**
**   Description:
**    spark event duration measurement
**
**   Parameters :
**    [in]  uint8_T cyl
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void Gtm_Eisb_StartSparkDurationMeasure(uint8_T cyl)
{
    uint8_T channel;
    uint8_T timChannel;
    channel = cyl & 0x01u;
    
    /* Increment the counter */
    if(VtCntIgnWaitSpEvnt[channel] < MAX_uint8_T)
    {
        VtCntIgnWaitSpEvnt[channel]++;
    }
    
    if (SparkDurationCylSel[channel] == N_CYLINDER)
    {
        if (cyl < N_CYLINDER)
        {
            timChannel = (channel == 0u) ? TIM_CHANNEL1 : TIM_CHANNEL2;
            /* TIM channel stop */
            gtm_timStop(&TIMD1, timChannel);
            /* TIM interrupt clear */
            gtm_timAckInt(&TIMD1, timChannel, SPC5_GTM_TIM_IRQ_STATUS_NEW_VALUE);
            SparkDurationCylSel[channel] = cyl;
            SparkLength[SparkDurationCylSel[channel]] = 0;
            SparkDurationPhase[channel] = SPEV_FALLING_EDGE_PHASE;
            gtm_timStart(&TIMD1, timChannel);
        }
    }
}

/******************************************************************************
**   Function    : Gtm_Eisb_Spark_cb
**
**   Description:
**    Callback function for spark trigger edge
**
**   Parameters :
**    [in]  GTM_TIMDriver *timd, uint8_t channel
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void Gtm_Eisb_Spark_cb(GTM_TIMDriver *timd, uint8_T channel)
{
    uint32_T tmp;
    uint32_T TbuSTatus,lockstate;
    uint32_T mask;

    TbuSTatus = TBUD1.tbu->CHEN.B.ENDIS_CH1;
    lockstate = DPLLD1.dpll->STATUS.B.LOCK1;
    tmp = gtm_tbuGetClockChannel(&TBUD1, TBU_CHANNEL0);
    mask = (channel == 1u) ? (timd->tim->GTM_CH_REG(1,GPR0).R & TIM_EDGE_MASK) : (timd->tim->GTM_CH_REG(2,GPR0).R & TIM_EDGE_MASK);

    if (SparkDurationPhase[channel - 1] == SPEV_RISING_EDGE_PHASE)
    {
        if (mask == TIM_EDGE_RISING) /* rising edge detected */
        {
            LeadEdge[SparkDurationCylSel[channel - 1]] = (channel == 1u) ? timd->tim->GTM_CH_REG(1,GPR0).B.GPR0 : timd->tim->GTM_CH_REG(2,GPR0).B.GPR0;
            if((TbuSTatus == TBU0_CHANNEL1_ENABLED) && (lockstate  == 1))
            {
                tmp = (channel == 1u) ? timd->tim->CH1_GPR1.R : timd->tim->CH2_GPR1.R;
                tmp &= 0x00FFFFFFu; /* round 24 bit */
                sparkangleend = tmp;
                if (sparkangleend < sparkanglestart)
                {
                    SparkAngleDuration[SparkDurationCylSel[channel - 1]] = 0xFFFFFF - sparkanglestart  + sparkangleend;
                }
                else
                {
                    SparkAngleDuration[SparkDurationCylSel[channel - 1]] =  (sparkangleend - sparkanglestart ) & 0xFFFFFF;
                }
            }

            if (LeadEdge[SparkDurationCylSel[channel - 1]] <  TrailEdge[SparkDurationCylSel[channel - 1]])
            {
                SparkLength[SparkDurationCylSel[channel - 1]] = 0xFFFFFF - TrailEdge[SparkDurationCylSel[channel - 1]] + LeadEdge[SparkDurationCylSel[channel - 1]];
            }
            else
            {
                SparkLength[SparkDurationCylSel[channel - 1]] = (uint32_T)(LeadEdge[SparkDurationCylSel[channel - 1]] - TrailEdge[SparkDurationCylSel[channel - 1]]) & 0xFFFFFF;
            }

            //End of the acquisition
            gtm_timStop(&TIMD1, channel);
            SparkDurationCylSel[channel - 1] = N_CYLINDER;

        }
        else
        {
            //ERROR CONDITION TIM STOP
            gtm_timStop(&TIMD1, channel);
            SparkDurationCylSel[channel - 1] = N_CYLINDER;
        }
    }
    else if (SparkDurationPhase[channel - 1] == SPEV_FALLING_EDGE_PHASE)
    {
        if ((mask) == TIM_EDGE_FALLING) /* falling edge detected */
        {
            /* Update Input command pin level */
            MSparkCmd_PostIgnitionParameter(SparkDurationCylSel[channel - 1]);
            
            TrailEdge[SparkDurationCylSel[channel - 1]] =  (channel == 1u) ? timd->tim->GTM_CH_REG(1,GPR0).B.GPR0 : timd->tim->GTM_CH_REG(2,GPR0).B.GPR0;
            if((TbuSTatus == TBU0_CHANNEL1_ENABLED) && (lockstate  == 1))
            {
                tmp = (channel == 1u) ? timd->tim->CH1_GPR1.R : timd->tim->CH2_GPR1.R; /* round 24 bit */
                tmp &= 0x00FFFFFFu;
                sparkanglestart = tmp;
            }

            SparkDurationPhase[channel - 1] = SPEV_RISING_EDGE_PHASE;

            /* Spark event task */
            SparkCyl = SparkDurationCylSel[channel - 1];
            ActivateTask((TaskType)TaskSparkEvID);
        }
        else
        {
            //ERROR CONDITION TIM STOP
            gtm_timStop(&TIMD1, channel);
            SparkDurationCylSel[channel - 1] = N_CYLINDER;
        }
    }
    else
    {
        //ERROR CONDITION TIM STOP
        gtm_timStop(&TIMD1, channel);
        SparkDurationCylSel[channel - 1] = N_CYLINDER;
    }
}

 /******************************************************************************
 **   Function    : Gtm_Eisb_MosCheck
 **
 **   Description:
 **    Check if MOS signals are set correctly at the EOA event
 **
 **   Parameters :
 **    cylinder
 **
 **   Returns:
 **    void
 **
 **   SW Requirements:
 **    NA
 **
 **   Implementation Notes:
 **
 **   EA GUID:
 ******************************************************************************/
static void Gtm_Eisb_MosCheck(uint8_T cylinder)
{
    if (VtDmaRunning[cylinder & 0x01u] == 1u)
    {
        if (FlgPmosOpen[cylinder] == 1u)
        {
            gtm_mcsSetTriggerBit_inl(&MCSD3, VtCylToMcs2[Cyl2PMOS[cylinder]]);
            FlgPmosOpen[cylinder] = 0u;
        }

        VtNrStartIonDMAIdx[cylinder] = DMA_0.TCD[VtCylToDmaIonChan[Cyl2PMOS[cylinder]]].CITER;
        Gtm_Eisb_MosStopInl(&ATOMD3, VtCylToAtomChan[Cyl2PMOS[cylinder]], TRUE);
        DMA_Stop(VtCylToDmaChan[cylinder & 0x01u]);
        VtDmaRunning[cylinder & 0x01u] = 0u;;
    }
}

