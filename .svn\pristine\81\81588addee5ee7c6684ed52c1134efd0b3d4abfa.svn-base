/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                              */
/* $Revision::        $                                                                                                        */
/* $Date::                                                $                                                                    */
/* $Author::                         $                                                                                         */
/*******************************************************************************************************************************/
#ifndef _DMAMUX_CFG_H_
#define _DMAMUX_CFG_H_
/* Precompiler defines for DMAMUX module */

/* DMAMUX enable/disable */
#define _DMA_MUX_ENABLE_         1u //0 for disabling

/* DMAmux number of muxer */
#define DMAMUX0                 0u
#define DMAMUX1                 1u
#define DMAMUX2                 2u
#define DMAMUX3                 3u

/* Peripheral source routes to DMAMUX channel 0 */
//#define DMAMUX0_RESERVED         0u
#define DMAMUX0_ADC_SAR_0_EOC    1u
//#define DMAMUX0_RESERVED         2u
#define DMAMUX0_ADC_SAR_B_EOC    3u
#define DMAMUX0_ADC_SD_0_EOC     4u
#define DMAMUX0_DSPI_0_RX        5u
#define DMAMUX0_DSPI_0_TX        6u
#define DMAMUX0_DSPI_4_RX        7u
#define DMAMUX0_DSPI_4_TX        8u
//#define DMAMUX0_RESERVED         9u
#define DMAMUX0_ADC_SAR_4_EOC   10u
#define DMAMUX0_ADC_SD_3_EOC    11u
#define DMAMUX0_M_CAN_1         12u
#define DMAMUX0_M_CAN_2         13u
#define DMAMUX0_SENT_0_RX_FAST  14u
#define DMAMUX0_SENT_0_RX_SLOW  15u
#define DMAMUX0_LINFLEX_0_RX    16u
#define DMAMUX0_LINFLEX_0_TX    17u
#define DMAMUX0_LINFLEX_14_RX   18u
#define DMAMUX0_DSPI_0_CMD      19u
#define DMAMUX0_DSPI_4_CMD      20u
//#define DMAMUX0_RESERVED        21u
//#define DMAMUX0_RESERVED        22u
//#define DMAMUX0_RESERVED        23u
//#define DMAMUX0_RESERVED        24u
//#define DMAMUX0_RESERVED        25u
//#define DMAMUX0_RESERVED        26u
//#define DMAMUX0_RESERVED        27u
//#define DMAMUX0_RESERVED        28u
//#define DMAMUX0_RESERVED        29u
//#define DMAMUX0_RESERVED        30u
//#define DMAMUX0_RESERVED        31u
//#define DMAMUX0_RESERVED        32u
//#define DMAMUX0_RESERVED        33u
//#define DMAMUX0_RESERVED        34u
//#define DMAMUX0_RESERVED        35u
//#define DMAMUX0_RESERVED        36u
//#define DMAMUX0_RESERVED        37u
//#define DMAMUX0_RESERVED        38u
//#define DMAMUX0_RESERVED        39u
//#define DMAMUX0_RESERVED        40u
//#define DMAMUX0_RESERVED        41u
//#define DMAMUX0_RESERVED        42u
//#define DMAMUX0_RESERVED        43u
//#define DMAMUX0_RESERVED        44u
//#define DMAMUX0_RESERVED        45u
//#define DMAMUX0_RESERVED        46u
//#define DMAMUX0_RESERVED        47u
//#define DMAMUX0_RESERVED        48u
//#define DMAMUX0_RESERVED        49u
//#define DMAMUX0_RESERVED        50u
//#define DMAMUX0_RESERVED        51u
//#define DMAMUX0_RESERVED        52u
//#define DMAMUX0_RESERVED        53u
//#define DMAMUX0_RESERVED        54u
//#define DMAMUX0_RESERVED        55u
//#define DMAMUX0_RESERVED        56u
//#define DMAMUX0_RESERVED        57u
//#define DMAMUX0_RESERVED        58u
//#define DMAMUX0_RESERVED        59u
//#define DMAMUX0_RESERVED        60u
//#define DMAMUX0_RESERVED        61u
//#define DMAMUX0_RESERVED        62u
#define DMAMUX0_ALWAYS_ON       63u

/* Peripheral source routes to DMAMUX channel 1 */
//#define DMAMUX1_RESERVED         0u
//#define DMAMUX1_RESERVED         1u
//#define DMAMUX1_RESERVED         2u
#define DMAMUX1_LINFLEX_0_RX     3u
#define DMAMUX1_LINFLEX_0_TX     4u
#define DMAMUX1_LINFLEX_1_RX     5u
#define DMAMUX1_LINFLEX_1_TX     6u
#define DMAMUX1_LINFLEX_14_RX    7u
#define DMAMUX1_LINFLEX_14_TX    8u
#define DMAMUX1_SENT_0_RX_FAST   9u
#define DMAMUX1_SENT_0_RX_SLOW  10u
#define DMAMUX1_SIPI_CH0        11u
#define DMAMUX1_SIPI_CH1        12u
#define DMAMUX1_SIPI_CH2        13u
#define DMAMUX1_SIPI_CH3        14u
#define DMAMUX1_SIUL2_REQ0      15u
#define DMAMUX1_SIUL2_REQ1      16u
#define DMAMUX1_GTM_TIM0_IRQ0   17u
#define DMAMUX1_GTM_TIM0_IRQ1   18u
#define DMAMUX1_GTM_TIM0_IRQ2   19u
#define DMAMUX1_GTM_TIM0_IRQ3   20u
#define DMAMUX1_GTM_TIM0_IRQ4   21u
#define DMAMUX1_GTM_TIM0_IRQ5   22u
#define DMAMUX1_GTM_TIM0_IRQ6   23u
#define DMAMUX1_GTM_TIM0_IRQ7   24u
#define DMAMUX1_GTM_TOM0_IRQ0   25u
#define DMAMUX1_GTM_TOM0_IRQ1   26u
#define DMAMUX1_GTM_TOM0_IRQ2   27u
#define DMAMUX1_GTM_TOM0_IRQ3   28u
#define DMAMUX1_GTM_TOM0_IRQ4   29u
#define DMAMUX1_GTM_TOM0_IRQ5   30u
#define DMAMUX1_GTM_TOM0_IRQ6   31u
#define DMAMUX1_GTM_TOM0_IRQ7   32u
#define DMAMUX1_GTM_ATOM0_IRQ0  33u
#define DMAMUX1_GTM_ATOM0_IRQ1  34u
#define DMAMUX1_GTM_ATOM0_IRQ2  35u
#define DMAMUX1_GTM_ATOM0_IRQ3  36u
#define DMAMUX1_GTM_MCS0_IRQ0   37u
#define DMAMUX1_GTM_MCS0_IRQ1   38u
#define DMAMUX1_GTM_MCS0_IRQ2   39u
#define DMAMUX1_GTM_MCS0_IRQ3   40u
#define DMAMUX1_GTM_MCS0_IRQ4   41u
#define DMAMUX1_GTM_MCS0_IRQ5   42u
#define DMAMUX1_GTM_MCS0_IRQ6   43u
#define DMAMUX1_GTM_MCS0_IRQ7   44u
#define DMAMUX1_LINFLEX_15_RX   45u
#define DMAMUX1_LINFLEX_15_TX   46u
#define DMAMUX1_DSPI_5_RX       47u
#define DMAMUX1_DSPI_5_TX       48u
#define DMAMUX1_DSPI_5_CMD      49u
//#define DMAMUX1_RESERVED        50u
//#define DMAMUX1_RESERVED        51u
//#define DMAMUX1_RESERVED        52u
#define DMAMUX1_DSPI_0_RX       53u
#define DMAMUX1_DSPI_0_TX       54u
#define DMAMUX1_ADC_SAR_0_EOC   55u
#define DMAMUX1_ADC_SAR_4_EOC   56u
#define DMAMUX1_ADC_SD_3_EOC    57u
//#define DMAMUX1_RESERVED        58u
//#define DMAMUX1_ALWAYS_ON       59u
//#define DMAMUX1_ALWAYS_ON       60u
//#define DMAMUX1_ALWAYS_ON       61u
//#define DMAMUX1_ALWAYS_ON       62u
#define DMAMUX1_ALWAYS_ON       63u

/* Peripheral source routes to DMAMUX channel 2 */
//#define DMAMUX2_RESERVED             0u
#define DMAMUX2_ADC_SAR_2_EOC        1u
//#define DMAMUX2_RESERVED             2u
#define DMAMUX2_DSPI_1_RX            3u
#define DMAMUX2_DSPI_1_TX            4u
#define DMAMUX2_SENT_1_RX_FAST       5u
#define DMAMUX2_SENT_1_RX_SLOW       6u
#define DMAMUX2_PSI5_0_CH0_RX_PSI5   7u
#define DMAMUX2_PSI5_0_CH0_RX_SMC    8u
#define DMAMUX2_SIUL2_REQ2           9u
#define DMAMUX2_SIUL2_REQ4          10u
#define DMAMUX2_GTM_PSM0_IRQ0       11u
#define DMAMUX2_GTM_PSM0_IRQ1       12u
#define DMAMUX2_GTM_PSM0_IRQ2       13u
#define DMAMUX2_GTM_PSM0_IRQ3       14u
#define DMAMUX2_GTM_TIM1_IRQ0       15u
#define DMAMUX2_GTM_TIM1_IRQ1       16u
#define DMAMUX2_GTM_TIM1_IRQ2       17u
#define DMAMUX2_GTM_TIM1_IRQ3       18u
#define DMAMUX2_GTM_TOM1_IRQ0       19u
#define DMAMUX2_GTM_TOM1_IRQ1       20u
#define DMAMUX2_GTM_TOM1_IRQ2       21u
#define DMAMUX2_GTM_TOM1_IRQ3       22u
#define DMAMUX2_GTM_ATOM1_IRQ0      23u
#define DMAMUX2_GTM_ATOM1_IRQ1      24u
#define DMAMUX2_GTM_MCS1_IRQ0       25u
#define DMAMUX2_GTM_MCS1_IRQ1       26u
#define DMAMUX2_GTM_MCS1_IRQ2       27u
#define DMAMUX2_GTM_MCS1_IRQ3       28u
#define DMAMUX2_GTM_TIM2_IRQ0       29u
#define DMAMUX2_GTM_TIM2_IRQ1       30u
#define DMAMUX2_GTM_TIM2_IRQ2       31u
#define DMAMUX2_GTM_TIM2_IRQ3       32u
#define DMAMUX2_GTM_ATOM2_IRQ0      33u
#define DMAMUX2_GTM_ATOM2_IRQ1      34u
#define DMAMUX2_GTM_MCS2_IRQ0       35u
#define DMAMUX2_GTM_MCS2_IRQ1       36u
#define DMAMUX2_GTM_MCS2_IRQ2       37u
#define DMAMUX2_GTM_MCS2_IRQ3       38u
#define DMAMUX2_GTM_ATOM3_IRQ0      39u
#define DMAMUX2_GTM_ATOM3_IRQ1      40u
//#define DMAMUX2_RESERVED            41u
#define DMAMUX2_DSPI_1_CMD          42u
#define DMAMUX2_DSPI_2_RX           43u
#define DMAMUX2_DSPI_2_TX           44u
#define DMAMUX2_LINFLEX_2_RX        45u
#define DMAMUX2_LINFLEX_2_TX        46u
#define DMAMUX2_GTM_SPE0            47u
#define DMAMUX2_GTM_SPE1            48u
//#define DMAMUX2_RESERVED            49u
//#define DMAMUX2_RESERVED            50u
//#define DMAMUX2_RESERVED            51u
//#define DMAMUX2_RESERVED            52u
//#define DMAMUX2_RESERVED            53u
//#define DMAMUX2_RESERVED            54u
//#define DMAMUX2_RESERVED            55u
//#define DMAMUX2_RESERVED            56u
//#define DMAMUX2_RESERVED            57u
//#define DMAMUX2_RESERVED            58u
//#define DMAMUX2_RESERVED            59u
//#define DMAMUX2_RESERVED            60u
//#define DMAMUX2_RESERVED            61u
//#define DMAMUX2_RESERVED            62u
#define DMAMUX2_ALWAYS_ON           63u

/* Peripheral source routes to DMAMUX channel 3 */
//#define DMAMUX3_RESERVED             0u
//#define DMAMUX3_RESERVED             1u
#define DMAMUX3_DSPI_2_RX            2u
#define DMAMUX3_DSPI_2_TX            3u
#define DMAMUX3_LINFLEX_2_RX         4u
#define DMAMUX3_LINFLEX_2_TX         5u
#define DMAMUX3_I2C_0_RX             6u
#define DMAMUX3_I2C_0_TX             7u
#define DMAMUX3_PSI5_1_CH0_RX_PSI5   8u
#define DMAMUX3_PSI5_1_CH0_RX_SMC    9u
#define DMAMUX3_SIUL2_REQ5          10u
#define DMAMUX3_GTM_PSM0_IRQ4       11u
#define DMAMUX3_GTM_PSM0_IRQ5       12u
#define DMAMUX3_GTM_PSM0_IRQ6       13u
#define DMAMUX3_GTM_PSM0_IRQ7       14u
#define DMAMUX3_GTM_TIM1_IRQ4       15u
#define DMAMUX3_GTM_TIM1_IRQ5       16u
#define DMAMUX3_GTM_TIM1_IRQ6       17u
#define DMAMUX3_GTM_TIM1_IRQ7       18u
#define DMAMUX3_GTM_TOM1_IRQ4       19u
#define DMAMUX3_GTM_TOM1_IRQ5       20u
#define DMAMUX3_GTM_TOM1_IRQ6       21u
#define DMAMUX3_GTM_TOM1_IRQ7       22u
#define DMAMUX3_GTM_ATOM1_IRQ2      23u
#define DMAMUX3_GTM_ATOM1_IRQ3      24u
#define DMAMUX3_GTM_MCS1_IRQ4       25u
#define DMAMUX3_GTM_MCS1_IRQ5       26u
#define DMAMUX3_GTM_MCS1_IRQ6       27u
#define DMAMUX3_GTM_MCS1_IRQ7       28u
#define DMAMUX3_GTM_TIM2_IRQ4       29u
#define DMAMUX3_GTM_TIM2_IRQ5       30u
#define DMAMUX3_GTM_TIM2_IRQ6       31u
#define DMAMUX3_GTM_TIM2_IRQ7       32u
#define DMAMUX3_GTM_ATOM2_IRQ2      33u
#define DMAMUX3_GTM_ATOM2_IRQ3      34u
#define DMAMUX3_GTM_MCS2_IRQ4       35u
#define DMAMUX3_GTM_MCS2_IRQ5       36u
#define DMAMUX3_GTM_MCS2_IRQ6       37u
#define DMAMUX3_GTM_MCS2_IRQ7       38u
#define DMAMUX3_GTM_ATOM3_IRQ2      39u
#define DMAMUX3_GTM_ATOM3_IRQ3      40u
#define DMAMUX3_SIUL2_REQ8          41u
//#define DMAMUX3_RESERVED            42u
#define DMAMUX3_ADC_SD_3_EOC        43u
#define DMAMUX3_ADC_SAR_6_EOC       44u
#define DMAMUX3_DSPI_2_CMD          45u
#define DMAMUX3_DSPI_1_RX           46u
#define DMAMUX3_DSPI_1_TX           47u
#define DMAMUX3_ADC_SAR_2_EOC       48u
//#define DMAMUX3_RESERVED            49u
//#define DMAMUX3_RESERVED            50u
//#define DMAMUX3_RESERVED            51u
//#define DMAMUX3_RESERVED            52u
//#define DMAMUX3_RESERVED            53u
//#define DMAMUX3_RESERVED            54u
//#define DMAMUX3_RESERVED            55u
//#define DMAMUX3_RESERVED            56u
//#define DMAMUX3_RESERVED            57u
//#define DMAMUX3_RESERVED            58u
//#define DMAMUX3_RESERVED            59u
//#define DMAMUX3_RESERVED            60u
//#define DMAMUX3_RESERVED            61u
//#define DMAMUX3_RESERVED            62u
#define DMAMUX3_ALWAYS_ON           63u

/* DMAMUX channel usage according to Figure 20. DMA channel multiplexing on ch ******* "DMA channel multiplexing block diagram" */
#define DMAMUX0_FIRST_CH           0u // first available channel for DMA mux 0
#define DMAMUX0_LAST_CH            7u // Last available channel for DMA mux 0
#define DMAMUX1_FIRST_CH           8u // first available channel for DMA mux 1
#define DMAMUX1_LAST_CH           15u // Last available channel for DMA mux 1
#define DMAMUX1_PERIODIC_TRIG_CH   4u // last PIT channel used by the DMA mux 1 (range PIT_0_4)
#define DMAMUX2_FIRST_CH          16u // first available channel for DMA mux 2
#define DMAMUX2_LAST_CH           23u // Last available channel for DMA mux 2
#define DMAMUX2_PERIODIC_TRIG_CH   5u // unique PIT channel used by the DMA mux 2
#define DMAMUX3_FIRST_CH          24u // first available channel for DMA mux 0
#define DMAMUX3_LAST_CH           31u // Last available channel for DMA mux 0

/* DMAMUX channel trigger Enable */
#define DMAMUX_NORMAL_MODE         0x0u
#define DMAMUX_PERIODIC_TRIG_MODE  0x1u





#endif /*_DMAMUX_CFG_H_*/
