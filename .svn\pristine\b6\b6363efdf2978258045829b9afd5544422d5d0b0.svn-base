/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonChargeCtrl.h
 **  Date:          29-Nov-2021
 **
 **  Model Version: 1.372
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonChargeCtrl_h_
#define RTW_HEADER_IonChargeCtrl_h_
#ifndef IonChargeCtrl_COMMON_INCLUDES_
# define IonChargeCtrl_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonChargeCtrl_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: ELD_EXPORT_DEFINE */
#define BKLOADVCRGION_dim              8U                        /* Referenced by:
                                                                  * '<S1>/BKLOADVCRGION_dim'
                                                                  * '<S1>/BKLOADVCRGION_dim1'
                                                                  * '<S6>/BKLOADVCRGION_dim'
                                                                  * '<S17>/BKLOADVCRGION_dim'
                                                                  * '<S23>/BKLOADVCRGION_dim'
                                                                  */

/* Last index of BKLOADVCRGION breakpoint */
#define BKRPMVCRGION_dim               9U                        /* Referenced by:
                                                                  * '<S1>/BKRPMVCRGION_dim'
                                                                  * '<S1>/BKRPMVCRGION_dim1'
                                                                  * '<S6>/BKRPMVCRGION_dim'
                                                                  */

/* Last index of BKRPMVCRGION breakpoint */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonChargeCtrl_initialize(void);

/* Exported entry point function */
extern void IonChargeCtrl_Init(void);

/* Exported entry point function */
extern void IonChargeCtrl_T5ms(void);

/* Exported entry point function */
extern void IonChargeCtrl_Tdc(void);

/* Exported data declaration */

/*Exported calibration memory section */
/*Init of exported calibrations section*/

/* Declaration for custom storage class: ELD_EXPORT_CALIBRATION */
extern CALQUAL CALQUAL_POST uint16_T BKLOADVCRGION[9];/* Referenced by:
                                                       * '<S1>/BKLOADVCRGION'
                                                       * '<S17>/BKLOADVCRGION'
                                                       */

/* Load VChargeObj breakpoint */
extern CALQUAL CALQUAL_POST uint16_T BKRPMVCRGION[10];/* Referenced by: '<S1>/BKRPMVCRGION' */

/* Rpm VchargeObj breakpoint */

/*End of exported calibrations section*/


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T VChargeObj[4];         /* '<S4>/Merge20' */

/* ION capacitor voltage target */
extern uint16_T VChargeObjNom;         /* '<S4>/Merge15' */

/* ION capacitor nominal voltage target coming from the table TBVCRGIONOBJ */
extern int16_T VCrgObjOffMinMax[4];    /* '<S4>/Merge16' */

/* Ion charge capacitor Obj saturation offset */
extern int16_T VInfVCharge;            /* '<S4>/Merge13' */

/* Diagnosis high threshold on the VCharge error */
extern int16_T VSupVCharge;            /* '<S4>/Merge14' */

/* Diagnosis high threshold on the VCharge error */
extern int16_T VtErrCION[4];           /* '<S4>/Merge5' */

/* Error input Acq ION capacitor */
extern boolean_T VtFlgCIonDisc[4];     /* '<S4>/Merge1' */

/* Enable discharge control */
extern boolean_T VtFlgVCrgObjSatMax[4];/* '<S4>/Merge17' */

/* Ion charge capacitor Obj saturation max flag */
extern boolean_T VtFlgVCrgObjSatMin[4];/* '<S4>/Merge18' */

/* Ion charge capacitor Obj saturation min flag */
extern boolean_T VtSelCIONOL[4];       /* '<S4>/Merge23' */

/* Flag to Enable OL */
extern uint16_T VtThrCrgCION[4];       /* '<S4>/Merge2' */

/* Threshold to start ION charge capacitor */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S11>/Constant' : Unused code path elimination
 * Block '<S13>/Data Type Duplicate' : Unused code path elimination
 * Block '<S11>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Constant' : Unused code path elimination
 * Block '<S14>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Data Type Duplicate' : Unused code path elimination
 * Block '<S15>/Data Type Duplicate' : Unused code path elimination
 * Block '<S8>/Data Type Duplicate' : Unused code path elimination
 * Block '<S9>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Duplicate' : Unused code path elimination
 * Block '<S34>/Data Type Duplicate' : Unused code path elimination
 * Block '<S38>/Constant' : Unused code path elimination
 * Block '<S44>/Data Type Duplicate' : Unused code path elimination
 * Block '<S38>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Constant' : Unused code path elimination
 * Block '<S45>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate' : Unused code path elimination
 * Block '<S48>/Data Type Duplicate' : Unused code path elimination
 * Block '<S49>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Propagation' : Unused code path elimination
 * Block '<S50>/Constant' : Unused code path elimination
 * Block '<S51>/Data Type Duplicate' : Unused code path elimination
 * Block '<S50>/Data Type Duplicate' : Unused code path elimination
 * Block '<S53>/Data Type Duplicate' : Unused code path elimination
 * Block '<S54>/Data Type Duplicate' : Unused code path elimination
 * Block '<S55>/Data Type Duplicate' : Unused code path elimination
 * Block '<S56>/Data Type Duplicate' : Unused code path elimination
 * Block '<S11>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S11>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S11>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S11>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S11>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S11>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S11>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion' : Eliminate redundant data type conversion
 * Block '<S11>/Reshape' : Reshape block reduction
 * Block '<S12>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion' : Eliminate redundant data type conversion
 * Block '<S12>/Reshape' : Reshape block reduction
 * Block '<S7>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S7>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S7>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S7>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S7>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S7>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S7>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S7>/Reshape' : Reshape block reduction
 * Block '<S8>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S8>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S8>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S9>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S9>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S9>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion' : Eliminate redundant data type conversion
 * Block '<S25>/Reshape' : Reshape block reduction
 * Block '<S32>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion' : Eliminate redundant data type conversion
 * Block '<S33>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S33>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S33>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S33>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion' : Eliminate redundant data type conversion
 * Block '<S38>/Reshape' : Reshape block reduction
 * Block '<S39>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion' : Eliminate redundant data type conversion
 * Block '<S39>/Reshape' : Reshape block reduction
 * Block '<S46>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S46>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S47>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S49>/Conversion' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S50>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion' : Eliminate redundant data type conversion
 * Block '<S50>/Reshape' : Reshape block reduction
 * Block '<S53>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S53>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S53>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S53>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S54>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S54>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S54>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S54>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S55>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S55>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S55>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S56>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S56>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S56>/Data Type Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonChargeCtrl'
 * '<S1>'   : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm'
 * '<S2>'   : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm'
 * '<S3>'   : 'IonChargeCtrl/IonChargeCtrl_setup'
 * '<S4>'   : 'IonChargeCtrl/Merge'
 * '<S5>'   : 'IonChargeCtrl/Model Info'
 * '<S6>'   : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/Calc_VCharge_DiagThreshold'
 * '<S7>'   : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/Look2D_IR_U16'
 * '<S8>'   : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/PreLookUpIdSearch_U1'
 * '<S9>'   : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/PreLookUpIdSearch_U16'
 * '<S10>'  : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/VChargeVect_calc'
 * '<S11>'  : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/Calc_VCharge_DiagThreshold/Look2D_IR_S1'
 * '<S12>'  : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/Calc_VCharge_DiagThreshold/Look2D_IR_S16'
 * '<S13>'  : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/Calc_VCharge_DiagThreshold/Look2D_IR_S1/Data Type Conversion Inherited1'
 * '<S14>'  : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/Calc_VCharge_DiagThreshold/Look2D_IR_S16/Data Type Conversion Inherited1'
 * '<S15>'  : 'IonChargeCtrl/IonChargeCtrl_T5ms_mgm/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S16>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge'
 * '<S17>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_Ratio'
 * '<S18>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Indexing'
 * '<S19>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Select_OL_CL'
 * '<S20>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/Disabled_Ctrl'
 * '<S21>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/Merge'
 * '<S22>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign'
 * '<S23>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/Calc_ThrCrgCION_OL'
 * '<S24>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL'
 * '<S25>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/Calc_ThrCrgCION_OL/Look2D_IR_U16'
 * '<S26>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/Calc_ThrCrgCION_OL/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S27>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Calc_ErrCION'
 * '<S28>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control'
 * '<S29>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Discharge_Control'
 * '<S30>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Invert_PIErrCION'
 * '<S31>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Scale_Threshold'
 * '<S32>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Calc_ErrCION/LookUp_IR_S16_1'
 * '<S33>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Calc_ErrCION/PreLookUpIdSearch_S16_1'
 * '<S34>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Calc_ErrCION/PreLookUpIdSearch_S16_2'
 * '<S35>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Calc_ErrCION/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S36>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Anti_wind_up'
 * '<S37>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Calc_Clamp'
 * '<S38>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Look2D_IR_S1'
 * '<S39>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Look2D_IR_S16'
 * '<S40>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/MaxSat'
 * '<S41>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/RescalSignedIntRightShift'
 * '<S42>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/RescalSignedIntRightShift1'
 * '<S43>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Saturation Dynamic'
 * '<S44>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Look2D_IR_S1/Data Type Conversion Inherited1'
 * '<S45>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/Look2D_IR_S16/Data Type Conversion Inherited1'
 * '<S46>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/MaxSat/LookUp_IR_U1'
 * '<S47>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/MaxSat/LookUp_IR_U16'
 * '<S48>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/MaxSat/LookUp_IR_U1/Data Type Conversion Inherited3'
 * '<S49>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Charge_Control/MaxSat/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S50>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Discharge_Control/Look2D_IR_S1'
 * '<S51>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Discharge_Control/Look2D_IR_S1/Data Type Conversion Inherited1'
 * '<S52>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_ION_Charge/PI_Assign/PI_CTRL/Scale_Threshold/RescalSignedIntRightShift'
 * '<S53>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_Ratio/PreLookUpIdSearch_S1'
 * '<S54>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_Ratio/PreLookUpIdSearch_S16'
 * '<S55>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_Ratio/PreLookUpIdSearch_U1'
 * '<S56>'  : 'IonChargeCtrl/IonChargeCtrl_Tdc_mgm/Calc_Ratio/PreLookUpIdSearch_U16'
 */

/*-
 * Requirements for '<Root>': IonChargeCtrl
 *
 * Inherited requirements for '<Root>/IonChargeCtrl_setup':
 *  1. EISB_FCA6CYL_SW_REQ_1911: At the PowerOn event the software shall reset the variables as fol... (ECU_SW_Requirements#7785)
 *
 * Inherited requirements for '<S16>/Disabled_Ctrl':
 *  1. EISB_FCA6CYL_SW_REQ_1914: The software shall give the possibility to disable the ION Capacit... (ECU_SW_Requirements#7794)

 */
#endif                                 /* RTW_HEADER_IonChargeCtrl_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/