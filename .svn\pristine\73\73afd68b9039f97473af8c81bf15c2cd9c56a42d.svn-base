/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonKnockState.h
 **  Date:          22-Apr-2021
 **
 **  Model Version: 1.1381
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonKnockState_h_
#define RTW_HEADER_IonKnockState_h_
#include <string.h>
#ifndef IonKnockState_COMMON_INCLUDES_
# define IonKnockState_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonKnockState_COMMON_INCLUDES_ */

typedef uint8_T enum_KnockState;
#define NO_KNOCK                       ((enum_KnockState)0U)     /* Default value */
#define WAIT_KNOCK                     ((enum_KnockState)1U)
#define ACTIVE_KNOCK                   ((enum_KnockState)2U)
#define WAIT_HEAVY_KNOCK               ((enum_KnockState)3U)
#define HEAVY_KNOCK                    ((enum_KnockState)4U)

/* Includes for objects with custom storage classes. */

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonKnockState_initialize(void);

/* Exported entry point function */
extern void IonKnockState_EOA(void);

/* Exported entry point function */
extern void IonKnockState_PowerOn(void);

/* Exported data declaration */

/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgHeavyKnock[8];       /* '<S3>/Merge1' */

/* Acquisition circuit switching flag for heavy-knock detection */
extern enum_KnockState KnockState[8];  /* '<S3>/Merge' */

/* Knock state */

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S8>/Data Type Duplicate' : Unused code path elimination
 * Block '<S8>/Data Type Propagation' : Unused code path elimination
 * Block '<S16>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Data Type Propagation' : Unused code path elimination
 * Block '<S8>/Conversion' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S14>/Data Type Conversion2' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonKnockState'
 * '<S1>'   : 'IonKnockState/EOA'
 * '<S2>'   : 'IonKnockState/Init'
 * '<S3>'   : 'IonKnockState/Subsystem'
 * '<S4>'   : 'IonKnockState/EOA/CreaeKnockState'
 * '<S5>'   : 'IonKnockState/EOA/CreateCounter'
 * '<S6>'   : 'IonKnockState/EOA/CreaeKnockState/Heavy_Knock_Threshold'
 * '<S7>'   : 'IonKnockState/EOA/CreaeKnockState/KnockStateMgm'
 * '<S8>'   : 'IonKnockState/EOA/CreaeKnockState/Heavy_Knock_Threshold/LookUp_U8_U16'
 * '<S9>'   : 'IonKnockState/EOA/CreateCounter/Step_Cnt_Logic'
 * '<S10>'  : 'IonKnockState/EOA/CreateCounter/StrategyDisabled_Or_WrongCalibration'
 * '<S11>'  : 'IonKnockState/EOA/CreateCounter/StrategyOn'
 * '<S12>'  : 'IonKnockState/EOA/CreateCounter/Subsystem'
 * '<S13>'  : 'IonKnockState/EOA/CreateCounter/Subsystem1'
 * '<S14>'  : 'IonKnockState/EOA/CreateCounter/calc_Counter'
 * '<S15>'  : 'IonKnockState/EOA/CreateCounter/calc_Counter1'
 * '<S16>'  : 'IonKnockState/EOA/CreateCounter/calc_Counter/SaturationDynamic'
 */

/*-
 * Requirements for '<Root>': IonKnockState
 *
 * Inherited requirements for '<Root>/Init':
 *  1. EISB_FCA6CYL_SW_REQ_1650: Software shall set to 0 each output produced for Knock State functionality at ECU power on. (ECU_SW_Requirements#3114)
 *
 * Inherited requirements for '<S5>/StrategyDisabled_Or_WrongCalibration':
 *  1. EISB_FCA6CYL_SW_REQ_1202: Every time that calibration  KNOCKPERCFILT is equal to 0 or is gre... (ECU_SW_Requirements#1211)
 *
 * Inherited requirements for '<S5>/StrategyOn':
 *  1. EISB_FCA6CYL_SW_REQ_1201: Every time that the calibration KNOCKPERCFILT is greater than 0 an... (ECU_SW_Requirements#1210)
 *
 * Inherited requirements for '<S5>/calc_Counter':
 *  1. EISB_FCA6CYL_SW_REQ_1190: Every time that knock detection strategy is enabled and DeltaKnock... (ECU_SW_Requirements#1212)
 *  2. EISB_FCA6CYL_SW_REQ_1193: Every time that knock detection strategy is enabled and DeltaKnock... (ECU_SW_Requirements#1213)
 *
 * Inherited requirements for '<S5>/calc_Counter1':
 *  1. EISB_FCA6CYL_SW_REQ_1651: Software shall set to 0 each output produced for Knock State funct... (ECU_SW_Requirements#3115)

 */
#endif                                 /* RTW_HEADER_IonKnockState_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_win_target                                                       *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/