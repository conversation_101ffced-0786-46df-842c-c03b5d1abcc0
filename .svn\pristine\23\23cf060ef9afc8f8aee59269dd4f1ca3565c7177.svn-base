/****************************************************************************
*
* Copyright © 2018-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_psm_cfg.c
 * @brief   (GTM-IP) PSM Driver configuration code.
 *
 * @addtogroup PSM
 * @{
 */
#include "gtm_cfg.h"

#if (SPC5_GTM_USE_PSM == TRUE) || defined(__DOXYGEN__)

#include "gtm_psm_cfg.h"

/*===========================================================================*/
/* Driver local definitions.                                                 */
/*===========================================================================*/

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/* ---- PSM0   Callbacks       ---- */
GTM_PSM_Channel_Callbacks gtm_psm0_channel0_callbacks = {
	NULL,
	NULL,
	NULL,
	NULL
};

GTM_PSM_Channel_Callbacks gtm_psm0_channel1_callbacks = {
	NULL,
	NULL,
	NULL,
	NULL
};

GTM_PSM_Channel_Callbacks gtm_psm0_channel2_callbacks = {
	NULL,
	NULL,
	NULL,
	NULL
};

GTM_PSM_Channel_Callbacks gtm_psm0_channel3_callbacks = {
	NULL,
	NULL,
	NULL,
	NULL
};

GTM_PSM_Channel_Callbacks gtm_psm0_channel4_callbacks = {
	NULL,
	NULL,
	NULL,
	NULL
};

GTM_PSM_Channel_Callbacks gtm_psm0_channel5_callbacks = {
	NULL,
	NULL,
	NULL,
	NULL
};

GTM_PSM_Channel_Callbacks gtm_psm0_channel6_callbacks = {
	NULL,
	NULL,
	NULL,
	NULL
};

GTM_PSM_Channel_Callbacks gtm_psm0_channel7_callbacks = {
	NULL,
	NULL,
	NULL,
	NULL
};

GTM_PSM_Channel_Callbacks *gtm_psm0_callbacks[SPC5_GTM_PSM_CHANNELS] = {
	&gtm_psm0_channel0_callbacks,
	&gtm_psm0_channel1_callbacks,
	&gtm_psm0_channel2_callbacks,
	&gtm_psm0_channel3_callbacks,
	&gtm_psm0_channel4_callbacks,
	&gtm_psm0_channel5_callbacks,
	&gtm_psm0_channel6_callbacks,
	&gtm_psm0_channel7_callbacks
};
/* ---- ---------------------- ---- */

/*===========================================================================*/
/* Driver local types.                                                       */
/*===========================================================================*/

/*===========================================================================*/
/* Driver local variables.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Driver local functions.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/

#endif /* SPC5_GTM_USE_PSM */

/** @} */
