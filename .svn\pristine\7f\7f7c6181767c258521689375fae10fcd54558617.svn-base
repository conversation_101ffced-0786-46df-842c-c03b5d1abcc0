/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

/*! \mainpage ModuleName
 
\section intro Introduction
\brief A brief description of what this module does 

Explain in detail how this module works and what is supposed to do.  
 
*/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "OS_errors.h"
#include "OS_resources.h"
#include "OS_api.h"
#include "OS_exec_ctrl.h"
#include "mpc5500_spr_macros.h"

#ifdef _OSEK_

/*!
\defgroup PublicVariables Public Variables 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
///These variables can be used also by other SW modules 
extern ResCBS   OsResTable[OSNUMRES];

/* Resource Configuration table         */
const ResCfg  OsResCfgTable[OSNUMRES]=
{
    { RES_1ID, RES_1_PRI },
    { RES_2ID, RES_2_PRI },
    { RES_3ID, RES_3_PRI },
    { RES_4ID, RES_4_PRI },
    { RES_5ID, RES_5_PRI },
    { RES_6ID, RES_6_PRI }
    
};
/*!\egroup*/

/*!
\defgroup PrivateVariables Private Variables 
\sgroup
*/
/*-----------------------------------*
 * PRIVATE VARIABLE DEFINITIONS
 *-----------------------------------*/
///These variables can be used only by this module

/*!\egroup*/


/*!
\defgroup PublicFunctions Public Functions 
\sgroup
*/
/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   GetResource
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
StatusType GetResource(ResourceType _ResID)
{
    TaskType currentTaskId;
        uint32_T MSR_EE_Status;

    OSrtiSetServiceWatch(OSServiceId_GetResource);
    MSR_EE_Status=(getSpecReg32MSR())&(0x8000);
    if (MSR_EE_Status != 0)
    {
        DisableAllInterrupts();
    }
    OSRESCURRPRI(_ResID) = INTC_CUR_PRI();
    if ((uint8_T)INTC_CUR_PRI() < OSRESPRI(_ResID))
    {
        __mbar();
        __isync();
        INTC_CUR_PRI() = OSRESPRI(_ResID); 
    }
    if (MSR_EE_Status != 0)
    {
        EnableAllInterrupts();
    }
    OSRESSTATE(_ResID)   = RES_LOCKED;
    GetTaskID(&currentTaskId);
    OSRESTASKID(_ResID)  = currentTaskId;
    OSrtiServiceWatchOnExit(OSServiceId_GetResource);

    return( E_OK );
}

/***************************************************************************/
//   Function    :   ReleaseResource
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
StatusType ReleaseResource(ResourceType _ResID)
{
        uint32_T MSR_EE_Status;

    OSrtiSetServiceWatch(OSServiceId_ReleaseResource);
    MSR_EE_Status=(getSpecReg32MSR())&(0x8000);
    if (MSR_EE_Status != 0)
    {
        DisableAllInterrupts();
    }
    __mbar();
    INTC_CUR_PRI() = OSRESCURRPRI(_ResID);
    if (MSR_EE_Status != 0) 
    {
        EnableAllInterrupts();
    }
    OSRESSTATE(_ResID)  = RES_FREE;
    OSRESTASKID(_ResID) = NULLTASK;    
    OSRESCURRPRI(_ResID)= DEFAULT_CURR_PRI;
    OSrtiServiceWatchOnExit(OSServiceId_ReleaseResource);
        
    return( E_OK );
}

#endif // _OSEK_

/****************************************************************************
 ****************************************************************************/

