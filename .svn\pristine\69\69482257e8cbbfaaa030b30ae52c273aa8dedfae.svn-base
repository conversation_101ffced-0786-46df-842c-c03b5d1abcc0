/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef __ACTIVE_DIAG_H__
#define __ACTIVE_DIAG_H__


/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "active_diag_out.h"
#include "diagcanmgm.h"
#include "OS_api.h"
#include "Port_out.h"
#include "Digio_out.h"
#include "Pit_out.h"
#include "TasksDefs.h"     //MAX_NUM_TASK

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define N_IGN_PULSE_DIAG        (1u)
#define SATURATION_DIAG         (25u)
#define IGN_PERIOD              (200u)   // 200*5msTask = 1sec
#define IGN_TIME                (4000u)  // 4000*5msTask = 20sec
#define IGN_SYSTEMATIC_ERROR_US (18u)    // about 200us more
#define STOP_TIME_IGN           (1200u - IGN_SYSTEMATIC_ERROR_US)  // 1.5ms

#define BUCK_A  0u
#define BUCK_B  1u


/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
///
typedef enum{
    IGN_COIL_0 = 0u,
    IGN_COIL_1 = 1u,
    IGN_COIL_2 = 2u,
    IGN_COIL_3 = 3u,
    IGN_COIL_4 = 4u,
    IGN_COIL_5 = 5u,
    IGN_COIL_6 = 6u,
    IGN_COIL_7 = 7u,
    IGN_COIL_NUMBER = 8u
}ignCoil_t;

///
typedef enum{                                 //  [Other chans on Pin] [PCR[Nxxx]] [EVB pin]
    IGN_GPIO_COIL_0 =  OP_IgnDrv_1,  //    ETPUA20           |    134    |   W1
    IGN_GPIO_COIL_1 =  OP_IgnDrv_2,  //    ETPUA18           |    132    |   W3
    IGN_GPIO_COIL_2 =  OP_IgnDrv_3,  //    ETPUA10           |    124    |   AA4
    IGN_GPIO_COIL_3 =  OP_IgnDrv_4,  //    ETPUA16           |    130    |   Y2
    IGN_GPIO_COIL_4 =  OP_IgnDrv_5,  //    ETPUA17           |    131    |   Y1
    IGN_GPIO_COIL_5 =  OP_IgnDrv_6,  //    ETPUA19           |    138    |   W1
    IGN_GPIO_COIL_6 =  OP_IgnDrv_7,  //    ETPUA21           |    135    |   N4
    IGN_GPIO_COIL_7 =  OP_IgnDrv_8,  //    ETPUA11           |    139    |   AB4
    IGN_GPIO_COIL_ERROR = 0xFFu
}IgnCoilGpio_t;

#pragma ghs startnomisra // MISRA 2004 Rule 18.4 union not allowed
///

typedef union { /* Pad Configuration Registers */
    vuint32_t R;
    struct {
        vuint32_t:2;
        vuint32_t OERC:2;
        vuint32_t:1;
        vuint32_t ODC:3;
        vuint32_t SMC:1;
        vuint32_t APC:1;
        vuint32_t ILS:2;
        vuint32_t IBE:1;
        vuint32_t HYS:1;
        vuint32_t WPDE:1;
        vuint32_t WPUE:1;
        vuint32_t INV:1;
        vuint32_t:7;
        vuint32_t SSS:8;
    } B;
}MSCR_IO_DIAG;

#pragma ghs endnomisra

typedef struct {
    MSCR_IO_DIAG siuMscrPrevConf;    // Initial mscr configuration: it will be restored at the end of the Ign job
    IgnCoilGpio_t gpioPinUsed;  // Gpio used with Ign jobs
    uint8_T ch;
}IgnExecutingInfo_t;


/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : 
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
static void Ign_ActiveDiag_5ms( diagIO_IGNStruct *IgnUsed);


/******************************************************************************
**   Function    : 
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
static void IOIgn_channelConfigurationSave(ignCoil_t coil);


/******************************************************************************
**   Function    : 
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
static void IOIgn_channelConfigurationRestore(void);


#endif // __ACTIVE_DIAG_H__
