/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

/*! \mainpage EEPROM

\section intro Software module for emulation process of EEPROM in Flash memory.
\brief  EEPROM memory, that is erasable by byte or word, is often used in automotive ECU. This type of memory is not available on MPC5xxx but its functionality 
may be emulated with Software and the block-erasable Flash memory. 

EEPROM can typically be both programmed and erased on byte or word base. This flexibility for program and erase operations makes it suitable for storage of application variables that must be maintained when power is removed from the application (RAM data will be lost). Flash memory can also be programmed on a byte or word base, but has the restriction that every write implies a block erase.
In EEPROM emulation data is organized by packet, every data block is stored along with its own  header.
When a data block is going to be updated, the driver must first check if an old version of the same data block is available and then stores the new one after invalidating the older. If enough space is available in EEPROM block, the packet can be written to Flash starting at the first blank location. Otherwise, the software must copy the back up of the lasts valid packets from the second half EEPROM area, writing them after erasing the EEPROM block.
When a data block is to be read, the driver must first check if previously stored data is not corrupted, and then searches for the last available data block. The EEPROM area is divided into two parts for redundancy purpose; this solution allows the presence of a valid block even in case of a sudden power shutdown. Each time the data are being saved in the first half, all the information must also be duplicated in the second half. Each time the data is being read,  only the first part is taken into account if the data correctness is verified (header CRC  and payload CRC must be OK); if the information in the first half area is incorrect, the data will be read from the second half, and if valid, it will be returned; otherwise, it will be returned an error code.
An easy structure of two pointer tables and two pointers to the first free memory cell (one for each half area) is intended to quickly access the ID blocks stored and to write the payload as soon as possible. At the startup, all the EEPROM area is skimmed to complete the pointer tables information; after compiling this information, each read is really fast, as we know where the needed information is (if correct). Even the information update is faster because we don’t have to skim all the memory to invalidate the former block; in addition, the first free memory cell is well known, and this can save an extra amount of time. 
The two pages don’t have the same starting address. While the first half is addressed form the first available address, the second one is addressed from its half. This solution creates a gap between the two halves, allowing the desirable situation of independent page overflow events. As one EEPROM page is full, It will be erased and all the valid data will be copied from the other half, starting from the first address. The half page misalignment allows only one erase-and-dump at a certain time, which is a safe condition, as this updating will end in a short amount of time. A power failure during this phase will affect only one half of the page (maybe it results erased or with some incorrect data). Even the impossible situation of both the halves simultaneously full is treated by the appropriate module, with the aim of strengthen the software, without losing a considerable amount of data.

 */

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "ee.h"
#include "Flash_out.h"
#include "OS_exec_ctrl.h"
#include "sys.h"
#include <string.h>
#ifdef EE_RECOVERY_SETWDTFLASHMODE
#ifdef _BUILD_MC33904_U3_
#include "Dspi_out.h"
#include "Wdt_out.h"
#include "MC33904U3_out.h"
#endif
#endif

#ifdef _BUILD_WDT_
#include "wdt.h"
#else 
#ifdef _BUILD_SWT_
#include "swt.h"
#endif
#endif

#ifdef EE_RECOVERY_ENABLE
#ifdef _BUILD_RECOVERY_
#ifdef EE_RECOVERY_MEMORY_CHECK_ENABLE
#include "recovery.h"
#endif
#endif
#endif

#ifdef _BUILD_EEPROM_
#pragma ghs nowarning 32 
#pragma ghs nowarning 513 
#pragma ghs nowarning 42
#pragma ghs nowarning 167


/*!
\defgroup PublicVariables Public Variables 
\sgroup
 */
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
///Flag for error detection on EEPROM block 0
uint8_T EE_errorOnBlock0=0u;
///Flag for error detection on EEPROM block 1
uint8_T EE_errorOnBlock1=0u;
///Function pointer to Setup function to execute, depending on memory corruption detection  
int16_T (*EE_SetupToDo)(void);
//Number of invalidated instances of IDs on EEPROM block 0
uint16_T EE_InvalidateID_bl0[EE_ID_TOT_NUMBER];
//Number of invalidated instances of IDs on EEPROM block 1
uint16_T EE_InvalidateID_bl1[EE_ID_TOT_NUMBER];
//Result of physical recovery on EEPROM block 0
int8_T EE_Recovery_FlashBlock0;
//Result of physical recovery on EEPROM block 1
int8_T EE_Recovery_FlashBlock1;


/*!\egroup*/
/*!
\defgroup PrivateVariables Private Variables 
\sgroup
 */
/*-----------------------------------*
 * PRIVATE VARIABLE DEFINITIONS
 *-----------------------------------*/
///These variables can be used only by this module 
///Index next start access
static uint32_T  EE_DataPtr0;
///Index next start access
static uint32_T  EE_DataPtr1;
///Configuration Status for EEPROM module
static uint8_T   EE_ConfigStatus = 0u;
///Table of IDs addresses for EEPROM block 0
static uint32_T  EE_IDtable_bl0[EE_ID_TOT_NUMBER];
///Table of IDs addresses for EEPROM block 1
static uint32_T  EE_IDtable_bl1[EE_ID_TOT_NUMBER];
///Next free address in EEPROM block 0
static uint8_T * EE_blk0_free_cell;
///Next free address in EEPROM block 1
static uint8_T * EE_blk1_free_cell;
///Array of RAM adresses for EEPROM IDs
static const uint32_T EE_RamIdAddressArray[EE_ID_TOT_NUMBER]=
{
#ifdef EE_ID0_USED
        (uint32_T)(&__EE_ID0_START) ,
#else
        0 ,
#endif
#ifdef EE_ID1_USED
        (uint32_T)(&__EE_ID1_START) ,
#else
        0 ,
#endif
#ifdef EE_ID2_USED
        (uint32_T)(&__EE_ID2_START) ,
#else
        0 ,
#endif
#ifdef EE_ID3_USED
        (uint32_T)(&__EE_ID3_START) ,
#else
        0 ,
#endif
#ifdef EE_ID4_USED
        (uint32_T)(&__EE_ID4_START) ,
#else
        0 ,
#endif
#ifdef EE_ID5_USED
        (uint32_T)(&__EE_ID5_START),
#else
        0 ,
#endif
#ifdef EE_ID6_USED
        (uint32_T)(&__EE_ID6_START) ,
#else
        0 ,
#endif
#ifdef EE_ID7_USED
        (uint32_T)(&__EE_ID7_START) ,
#else
        0 ,
#endif
#ifdef EE_ID8_USED
        (uint32_T)(&__EE_ID8_START) ,
#else
        0 ,
#endif
#ifdef EE_ID9_USED
        (uint32_T)(&__EE_ID9_START) ,
#else
        0 ,
#endif
#ifdef EE_ID10_USED
        (uint32_T)(&__EE_ID10_START) ,
#else
        0 ,
#endif
#ifdef EE_ID11_USED
        (uint32_T)(&__EE_ID11_START)
#else
        0
#endif
};
///Array of size for EEPROM IDs in RAM
static uint32_T EE_RamIdSizeArray[EE_ID_TOT_NUMBER] =
{
#ifdef EE_ID0_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID1_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID2_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID3_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID4_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID5_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID6_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID7_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID8_USED
        0u,
#else
        0xFFFFu,
#endif
#ifdef EE_ID9_USED
        0u,
#else
        0xFFFFu,
#endif  
#ifdef EE_ID10_USED
        0u,
#else
        0xFFFFu,
#endif 
#ifdef EE_ID11_USED
        0u,
#else
        0xFFFFu,
#endif 

};


/*!\egroup*/


/*!
\defgroup PublicFunctions Public Functions 
\sgroup
 */
/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   EE_Recovery
//
//   Description:    
/*! \brief 
This method performs the recovery of block with errors identified from EE_errorOnBlock0/1: if only a block is affected from error performs  
the EE_Flush of this block, if both block are affected from error ehis function performs their erase
 */
//
//  Parameters and Returns:
/*! 
\returns  EEPROM error code : 
NO_ERROR                                      operation OK
EE_RECOVERY_ERROR_BLK0            block 0 recovered                 
EE_RECOVERY_ERROR_BLK1            block 1 recovered
EE_RECOVERY_ERROR_ALL_BLK       blocks 0 and 1 recovered        
 */
//  Notes:        
/*!
This function is based on error reporting done by EE_Setup() or EE_ControlRecord() functions
 */
/**************************************************************************/
#ifdef EE_RECOVERY_ENABLE
int16_T EE_Recovery(const char_T* ID_version)
{
    uint8_T error_block = EE_NO_RECOVERY;
    int16_T res = NO_ERROR;
    int16_T resErase = NO_ERROR;

#ifdef _BUILD_RECOVERY_
#ifdef EE_RECOVERY_MEMORY_CHECK_ENABLE
    if(EE_flashErrorOnBlock0 != NO_ERROR)
   {
        res = (int16_T)((int8_T)EE_RECOVERY_ERROR_FLASH_BLOCK0); /*DISABLE RECOVERY IN CASE OF FLASH ERROR ON EE*/
    }
    else if(EE_flashErrorOnBlock1 != NO_ERROR)
    {
        res = (int16_T)((int8_T)EE_RECOVERY_ERROR_FLASH_BLOCK1);/*DISABLE RECOVERY IN CASE OF FLASH ERROR ON EE*/
    }
    else /* No physical errors have been found. Checking logical errors... */
#endif
    {
#endif //_BUILD_RECOVERY_

        if ((EE_errorOnBlock0 == 0u) && (EE_errorOnBlock1 == 0u))
        {
            error_block=EE_NO_RECOVERY; /* none error has been deteched by EE_ReadRecord */
        }
        else if((EE_errorOnBlock0 != 0u) && (EE_errorOnBlock1 == 0u))
        {
            error_block=EE_RECOVERY_BLK0; /* one (or more) error has been deteched by EE_ReadRecord on block 0 */
        }
        else if ((EE_errorOnBlock0 == 0u) && (EE_errorOnBlock1 != 0u))
        {
            error_block=EE_RECOVERY_BLK1; /* one (or more) error has been deteched by EE_ReadRecord on block 1 */
        }
        else
        {
            error_block=EE_ERASE_ALL; /*one (or more) error has been deteched by EE_ReadRecord on both bloks */
        }


        switch(error_block)
        {
        case EE_NO_RECOVERY :
        {
            break;
        }
        case EE_RECOVERY_BLK0:
        {
            EE_Flush(EE_PAGE_0,FLASH_BLOCKING,ID_version);

            if(EE_errorOnBlock0 == 0u)
            {
                res=EE_RECOVERY_ERROR_BLK0;      // Error recovered on page 0
            }
            else
            {
                res= EE_RECOVERY_ERROR_BLK0_FAIL;// Error not recovered on page 0
            }
            break;
        }

        case EE_RECOVERY_BLK1:
        {
            EE_Flush(EE_PAGE_1,FLASH_BLOCKING,ID_version);

            if(EE_errorOnBlock1 == 0u)
            {
                res = EE_RECOVERY_ERROR_BLK1;        // Error recovered on page 1
            }
            else
            {
                res = EE_RECOVERY_ERROR_BLK1_FAIL;   // Error not recovered on page 0
            }

            break;
        }

        case EE_ERASE_ALL:
        {
            /* both blocks are corrupted and they need to be fully erased */

            resErase = EE_Clean();

            if(resErase == NO_ERROR)
            {
                EE_errorOnBlock0 = 0u;
                EE_errorOnBlock1 = 0u;
                res = EE_RECOVERY_ERROR_ALL_BLK;    // Errors recovered on both blocks page 1
            }
            else
            {
                /*keep blocks on error*/
                res = EE_RECOVERY_ERROR_ALL_BLK_FAIL; // Errors not recovered on both blocks page 1
            }
            break;
        }

        default:
        {
            break;

        }
        }
#ifdef _BUILD_RECOVERY_
    }
#endif    
    return res;
}

#endif
/***************************************************************************/
//   Function    :   EE_RestoreInvalidateId
//
//   Description:    
/*! \brief  Restores an instance of EE ID previously invalidated, from an EEPROM block
 */
//
//  Parameters and Returns:
/*! 
\param ID:EEPROM_Page to parse
\param ID: ID EE to be restored
\param ID_inv_nmb: instance of ID EE to be restored
\returns EEPROM error code : 
EE_BLANK                            EEPROM empty
EE_SCANNING_COMPLETED             No errors, scanning ok
EE_ERROR                                       Error during memory scan 
 */
//  Notes:        
/*!

 */
/**************************************************************************/
int16_T EE_RestoreInvalidateId(uint8_T EEPROM_Page, uint8_T ID, uint16_T ID_inv_nmb)
{
    int16_T res = NO_ERROR;
    uint32_T * pTemp =  NULL;
    static uint8_T *EE_blk0_cell = NULL;
    static uint8_T *EE_blk1_cell = NULL;
    int16_T resBlankCheck = NO_ERROR;
    static uint32_T pID_Invalidate;

    if(EEPROM_Page == EE_PAGE_0)
    {
        EE_blk0_cell = (uint8_T *)EEPROM_BLK0_START();
        res = EE_BlkSeeking_ID((uint8_T**) &EE_blk0_cell , (uint8_T*)EEPROM_BLK0_END(), ID , ID_inv_nmb, &pID_Invalidate);
    }
    else if(EEPROM_Page == EE_PAGE_1 )
         {
            resBlankCheck = FLASH_BlankCheck(EEPROM_BLK1_START(),EEPROM_BLK1_HALF_BLOCK()-EEPROM_BLK1_START());
            if (resBlankCheck != NO_ERROR)
            {
                EE_blk1_cell = (uint8_T*)(EEPROM_BLK1_START());
            } 
            else
            {
                EE_blk1_cell = (uint8_T*)(EEPROM_BLK1_HALF_BLOCK());
            }            

            res = EE_BlkSeeking_ID((uint8_T**) &EE_blk1_cell, (uint8_T*)EEPROM_BLK1_END(), ID , ID_inv_nmb, &pID_Invalidate );
         }
         else
         {
         }
    
    if (res == EE_ID_INV_DETECTED)
    {
        switch (ID)
        {
#ifdef EE_ID0_USED
            case EE_ID0:
                EE_loadID((uint32_T*)&__EE_ID0_START, (uint16_T)(((uint32_T)&__EE_ID0_END)-((uint32_T)&__EE_ID0_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID1_USED
            case EE_ID1:
                EE_loadID((uint32_T*)&__EE_ID1_START, (uint16_T)(((uint32_T)&__EE_ID1_END)-((uint32_T)&__EE_ID1_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID2_USED
            case EE_ID2:
                EE_loadID((uint32_T*)&__EE_ID2_START, (uint16_T)(((uint32_T)&__EE_ID2_END)-((uint32_T)&__EE_ID2_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID3_USED
            case EE_ID3:
                EE_loadID((uint32_T*)&__EE_ID3_START, (uint16_T)(((uint32_T)&__EE_ID3_END)-((uint32_T)&__EE_ID3_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID4_USED
            case EE_ID4:
                EE_loadID((uint32_T*)&__EE_ID4_START, (uint16_T)(((uint32_T)&__EE_ID4_END)-((uint32_T)&__EE_ID4_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID5_USED
            case EE_ID5:
                EE_loadID((uint32_T*)&__EE_ID5_START, (uint16_T)(((uint32_T)&__EE_ID5_END)-((uint32_T)&__EE_ID5_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID6_USED
            case EE_ID6:
                EE_loadID((uint32_T*)&__EE_ID6_START, (uint16_T)(((uint32_T)&__EE_ID6_END)-((uint32_T)&__EE_ID6_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID7_USED
            case EE_ID7:
                EE_loadID((uint32_T*)&__EE_ID7_START, (uint16_T)(((uint32_T)&__EE_ID7_END)-((uint32_T)&__EE_ID7_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID8_USED
            case EE_ID8:
                EE_loadID((uint32_T*)&__EE_ID8_START, (uint16_T)(((uint32_T)&__EE_ID8_END)-((uint32_T)&__EE_ID8_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID9_USED
            case EE_ID9:
                EE_loadID((uint32_T*)&__EE_ID9_START, (uint16_T)(((uint32_T)&__EE_ID9_END)-((uint32_T)&__EE_ID9_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID10_USED
            case EE_ID10:
                EE_loadID((uint32_T*)&__EE_ID10_START, (uint16_T)(((uint32_T)&__EE_ID10_END)-((uint32_T)&__EE_ID10_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif
#ifdef EE_ID11_USED
            case EE_ID11:
                EE_loadID((uint32_T*)&__EE_ID11_START, (uint16_T)(((uint32_T)&__EE_ID11_END)-((uint32_T)&__EE_ID11_START)), EE_DO_NOT_CHECK_VERSION, ( t_EEBlockDescriptionVar *)pID_Invalidate , "") ;
                break;
#endif

            default:
                break;
        }
    }

return res;

}




/***************************************************************************/
//   Function    :   EE_Setup
//
//   Description:    
/*! \brief Checks the EE simulation block, filling the pointer tables and setting the first free block memory
 */
//
//  Parameters and Returns:
/*! 
\returns EEPROM error code : 
EE_BLANK                            EEPROM empty
EE_SCANNING_COMPLETED  EEPROM not blank, pointer tables correctly updated
EE_ERROR                           Error found during EEPROM scanning
 */
//  Notes:        
/*!

 */
/**************************************************************************/

int16_T EE_Setup(void)
{
    int16_T res=NO_ERROR;
    int16_T res0=NO_ERROR;
    int16_T res1=NO_ERROR;
    int16_T resBlankCheck = NO_ERROR;
    uint16_T i;

    if(EE_ConfigStatus != 0u)
    {
        res = PERIPHERAL_ALREADY_CONFIGURED;
    }
    else
    {

        for (i=0u; i<EE_ID_TOT_NUMBER; i++)
        {
            EE_IDtable_bl0[i] = EE_IDTABLE_INIT;
            EE_IDtable_bl1[i] = EE_IDTABLE_INIT;
        }

        /* pointers to first free cell are being initialized */
        EE_blk0_free_cell = (uint8_T *)EEPROM_BLK0_START();

        resBlankCheck = FLASH_BlankCheck(EEPROM_BLK1_START(),EEPROM_BLK1_HALF_BLOCK()-EEPROM_BLK1_START());

        if (resBlankCheck != NO_ERROR)
        {
            EE_blk1_free_cell = (uint8_T*)(EEPROM_BLK1_START());
        } 
        else
        {
            EE_blk1_free_cell = (uint8_T*)(EEPROM_BLK1_HALF_BLOCK());
        }

        resBlankCheck = FLASH_BlankCheck(EEPROM_START(),EEPROM_END()-EEPROM_START());

        if(resBlankCheck != NO_ERROR) /* EEPROM is not fully empty */
        {
            /* trying to fill the EE_IDtable */
            res0 = EE_BlkSeeking((uint8_T**) &EE_blk0_free_cell, (uint8_T*)EEPROM_BLK0_END(), EE_IDtable_bl0);
            if (res0 == EE_ERROR){EE_errorOnBlock0 = 1u; res = EE_ERROR;}

            res1 = EE_BlkSeeking((uint8_T**) &EE_blk1_free_cell, (uint8_T*)EEPROM_BLK1_END(), EE_IDtable_bl1);
            if (res1 == EE_ERROR){EE_errorOnBlock1 = 1u; res = EE_ERROR;}

            if (res != EE_ERROR)
            { 
                res = res0 | res1 ;
            }

            if(res != EE_SCANNING_COMPLETED)
            {
                res = EE_ERROR;
            }
        }
        else
        {
            res = EE_BLANK;
        }

        EE_ConfigStatus = 1u;

        EE_DataPtr0 = (uint32_T)EE_blk0_free_cell;
        EE_DataPtr1 = (uint32_T)EE_blk1_free_cell;
    }

    return res;
}


/***************************************************************************/
//   Function    :   EE_CorruptedSetup
//
//   Description:    
/*! \brief Checks the EE simulation block, filling the pointer tables and setting the first free block memory. Executed when 
memory corruption has been detected by RECOVERY module, and then is necessary to inhibit eeprom block or full eeprom memory array.
 */
//
//  Parameters and Returns:
/*! 
\returns EEPROM error code : 
to add
 */
//  Notes:        
/*!
This function it is executed if flash memory used to emulate eeprom if it is physically corrupted and not recoverable.
Acces to flash block that is corrupted is w/r inhibited so that EEPROB can be used with only one block or completely 
not accessible. The execution of this funtion is strictly related to RECOVERY software module that detects error 
and tries physical recovery of electrically corrupted flash block.
 */
/**************************************************************************/
#ifdef EE_RECOVERY_ENABLE
#ifdef _BUILD_RECOVERY_
#ifdef EE_RECOVERY_MEMORY_CHECK_ENABLE
int16_T EE_CorruptedSetup(void)
{
    int16_T resBlankCheck = NO_ERROR;
    int16_T resDump = NO_ERROR;
    int16_T res = NO_ERROR; /* Probable Flash Error in act. If unmanaged here,
     * default values are loaded in RAM in EEMGM_PowerOn
     */
    int16_T  res0=NO_ERROR;
    int16_T  res1=NO_ERROR;
    int8_T   resFerr0 =NO_ERROR;
    int8_T   resFerr1 =NO_ERROR;
    int16_T resErase = NO_ERROR;
    uint16_T i;
    uint32_T StartEEBlock_tmp;
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFFu;

    //WDT_PeriodicServiceRoutine()

    for (i=0u; i < EE_ID_TOT_NUMBER; i++)
    {
        EE_IDtable_bl0[i] = EE_IDTABLE_INIT;
        EE_IDtable_bl1[i] = EE_IDTABLE_INIT;
    }

    EE_blk0_free_cell = (uint8_T *)EEPROM_BLK0_START();         /* default start address for a write on a blank EE block0 */
    EE_blk1_free_cell = (uint8_T *)EEPROM_BLK1_HALF_BLOCK();    /* default start address for a write on a blank EE block1 */

    /******* Configure separately both block if they seems FLASH error-free *******/

    if(EE_flashErrorOnBlock0 == NO_ERROR) /* EE_blk0 hasn't been declared corrupted by Ivor2 EE recovery handler */
    {
        /* pointers to first free cell are being initialized */
        EE_blk0_free_cell = (uint8_T *)EEPROM_BLK0_START();

        /*check if EE_blk is written*/
        resBlankCheck = FLASH_BlankCheck(EEPROM_START(),(EEPROM_SIZE()>>1));

        if(resBlankCheck != NO_ERROR) /* found something inside */
        {
            res0 = EE_BlkSeeking((uint8_T**) &EE_blk0_free_cell, (uint8_T*)EEPROM_BLK0_END(), EE_IDtable_bl0);
            if (res0 == EE_ERROR)
            {
                EE_errorOnBlock0 = 1u;
            }
        }
        res0 = NO_ERROR;
    }
    else
    {
        /*ERROR MANAGED LATER*/
    }

    if(EE_flashErrorOnBlock1 == NO_ERROR) /* EE_blk1 hasn't been declared corrupted by Ivor2 EE recovery handler */
    {
        /* Init Block 1 */
        EE_blk1_free_cell = (uint8_T*)(EEPROM_BLK1_START());

        /*check if EE_blk is written*/
        resBlankCheck = FLASH_BlankCheck(EEPROM_BLK1_START(),(EEPROM_SIZE()>>1));

        if(resBlankCheck != NO_ERROR) /* found something inside */
        {
            resBlankCheck = FLASH_BlankCheck(EEPROM_BLK1_START(),EEPROM_BLK1_HALF_BLOCK()-EEPROM_BLK1_START());
            if (resBlankCheck != NO_ERROR)
            {
                EE_blk1_free_cell = (uint8_T*)(EEPROM_BLK1_START());
            } 
            else
            {
                EE_blk1_free_cell = (uint8_T*)(EEPROM_BLK1_HALF_BLOCK());
            }

            res1 = EE_BlkSeeking((uint8_T**) &EE_blk1_free_cell, (uint8_T*)EEPROM_BLK1_END(), EE_IDtable_bl1);
            if (res1 == EE_ERROR)
            {
                EE_errorOnBlock1 = 1u;
            }
        }

        if(EE_blk1_free_cell == (uint8_T*)EEPROM_BLK1_START())
        {
            /* Relocate Block1 ptr if the Block is blank or some error found in 
               the first recognized block by blockSeeking */
            EE_blk1_free_cell = (uint8_T*)EEPROM_BLK1_HALF_BLOCK();
        }

        res1 = NO_ERROR;
    }
    else
    {
        /*ERROR MANAGED LATER*/
    }


    /****** Flash error managing, if any declared *********************************/

    if(EE_flashErrorOnBlock0 == EE_IVOR2_ERROR)
    {
        /* PAGE 0 Should not be used anymore */

        for (i=0u; i<EE_ID_TOT_NUMBER; i++)
        {
            EE_IDtable_bl0[i] = (uint32_T)((int32_T)EE_IVOR2_ERROR);
        }

        //        EE_errorOnBlock0 = (uint8_T) EE_IVOR2_ERROR;
        
        resFerr0 = EE_IVOR2_ERROR;
    }
    else if(EE_flashErrorOnBlock0 == EE_DO_COPY_FROM_OTHER_PAGE)
    {
        if(EE_flashErrorOnBlock1 == NO_ERROR)
        {
            /*PAGE_0 is ERASED: controlled on checkFaultyAddrFromLastIvor2()*/

            if((FlashFaultStruct.EE_Dump_From_Page0_Req == 1u)&&(FlashFaultStruct.EE_Dump_From_Page1_Req == 1u))
            {
                EE_flashErrorOnBlock0 = NO_ERROR;
                EE_flashErrorOnBlock1 = NO_ERROR;
            
                resFerr0 = EE_DO_COPY_FROM_OTHER_PAGE_FAIL;
                resFerr1 = EE_DO_COPY_FROM_OTHER_PAGE_FAIL;
                FlashFaultStruct.EE_Dump_From_Page0_Req = 0u ;
                FlashFaultStruct.EE_Dump_From_Page1_Req = 0u ;
                FlashFaultStruct.lastFaultyFlashLogicalIdx = (uint8_T)FLASH_FAULT_IDX_DEFAULT;
                VSRAMMGM_StoreIvor2Data();
                VSRAMMGM_Update();
            }
            else
            {
                uint8_T* pBackupAddr;

#ifdef EE_RECOVERY_SETWDTFLASHMODE
                EE_Watchdog_Flash_Mode();
#endif
                pBackupAddr = (uint8_T*)((uint32_T)EEPROM_BLK0_START());
                resDump = EE_DumpLastBlocks(&pBackupAddr, EE_IDtable_bl0, EE_IDtable_bl1, FLASH_BLOCKING);
                EE_blk0_free_cell = pBackupAddr;

                if(resDump == EE_ERROR) /* some error while programming */
                {
                    /* Declare Corrupted block */
                    EE_flashErrorOnBlock0 = EE_IVOR2_ERROR;
                    //              EE_errorOnBlock0 = (uint8_T) EE_IVOR2_ERROR;

                    for (i=0u; i<EE_ID_TOT_NUMBER; i++)
                    {
                        EE_IDtable_bl0[i] = (uint32_T)((int32_T)EE_IVOR2_ERROR);
                    }

                    FlashFaultStruct.EE_Dump_From_Page1_Req = 0u;
                    VSRAMMGM_StoreIvor2Data();
                    VSRAMMGM_Update(); /*To update VsRam checksum*/

                    resFerr0 = EE_DO_COPY_FROM_OTHER_PAGE_FAIL; 
                }
                else
                {
                    EE_flashErrorOnBlock0 = NO_ERROR;
                    resFerr0 = EE_DO_COPY_FROM_OTHER_PAGE_OK;

                    FlashFaultStruct.lastFaultyFlashLogicalIdx = (uint8_T)FLASH_FAULT_IDX_DEFAULT;
                    FlashFaultStruct.EE_Dump_From_Page1_Req = 0u;
                    VSRAMMGM_StoreIvor2Data();
                    VSRAMMGM_Update(); /*To update VsRam checksum*/

                    /* now it is necessary to dump page 0 to page 1 because we have to keep the natural distances between  */
                    /* the space occupied by page 0 and page 1                                                                                         */
                    StartEEBlock_tmp =  EEPROM_BLK1_START();
                    do
                    {
                        DisableAllInterrupts();
#ifdef EE_SERVE_WATCHDOG
                        EE_Watchdog_Set_Serve();
#endif
                        resErase = FLASH_Erase(StartEEBlock_tmp,(EEPROM_SIZE() >> 1), callback );
#ifdef EE_SERVE_WATCHDOG
                        EE_Watchdog_Cfg_Serve();
#endif
                        EnableAllInterrupts();

                        i++;
                    }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));

                    if (resErase != NO_ERROR)
                    {
                        // return EE_ERASE_NOT_CORRECT;
                        //res = EE_ERASE_NOT_CORRECT;
                    }
                    else
                    {
                        pBackupAddr = (uint8_T*)((uint32_T)EEPROM_BLK1_HALF_BLOCK());

                        res |= EE_DumpLastBlocks(&pBackupAddr,EE_IDtable_bl1, EE_IDtable_bl0, FLASH_BLOCKING);

                        EE_blk1_free_cell = pBackupAddr;
                    }
                    //              EE_errorOnBlock0 = 0;
                }

#ifdef EE_RECOVERY_RESETREQWDT
                EE_RecFlashB0Stored = resFerr0;
                EE_RecFlashB1Stored = resFerr1;
                EE_RecFlashReset = 1u;
                VSRAMMGM_Update();
                EE_Watchdog_ResetReq();
#endif

            }
        }
    }
    else
    {
        /*Misra 14.10*/
    }

    if(EE_flashErrorOnBlock1 == EE_IVOR2_ERROR)
    {
        /* PAGE 1 Should not be used anymore */
        for (i = 0u; i < EE_ID_TOT_NUMBER; i++)
        {
            EE_IDtable_bl1[i] = (uint32_T)((int32_T)EE_IVOR2_ERROR);
        }

        //        EE_errorOnBlock1 = (uint8_T)EE_IVOR2_ERROR;
        resFerr1 = EE_IVOR2_ERROR;
    }
    else if(EE_flashErrorOnBlock1 == EE_DO_COPY_FROM_OTHER_PAGE)
    {
        if(EE_flashErrorOnBlock0 == NO_ERROR)
        {
            /*PAGE_1 is ERASED: controlled on checkFaultyAddrFromLastIvor2()*/

            if((FlashFaultStruct.EE_Dump_From_Page0_Req == 1u)&&(FlashFaultStruct.EE_Dump_From_Page1_Req == 1u))
            {
                EE_flashErrorOnBlock0 = NO_ERROR;
                EE_flashErrorOnBlock1 = NO_ERROR;
            
                resFerr0 = EE_DO_COPY_FROM_OTHER_PAGE_FAIL;
                resFerr1 = EE_DO_COPY_FROM_OTHER_PAGE_FAIL;
                FlashFaultStruct.EE_Dump_From_Page0_Req = 0u ;
                FlashFaultStruct.EE_Dump_From_Page1_Req = 0u ;
                FlashFaultStruct.lastFaultyFlashLogicalIdx = (uint8_T)FLASH_FAULT_IDX_DEFAULT;
                VSRAMMGM_StoreIvor2Data();
                VSRAMMGM_Update();
            }
            else
            {

                uint8_T* pBackupAddr;

#ifdef EE_RECOVERY_SETWDTFLASHMODE
                EE_Watchdog_Flash_Mode();
#endif

                pBackupAddr = (uint8_T*)((uint32_T)EEPROM_BLK1_HALF_BLOCK());
                resDump = EE_DumpLastBlocks(&pBackupAddr, EE_IDtable_bl1, EE_IDtable_bl0, FLASH_BLOCKING);
                EE_blk1_free_cell = pBackupAddr;

                if(resDump == EE_ERROR) /* some error while programming */
                {
                    /* Declare Corrupted block */
                    EE_flashErrorOnBlock1 = EE_IVOR2_ERROR;
                    //              EE_errorOnBlock1 = (uint8_T)EE_IVOR2_ERROR;


                    for (i = 0u; i < EE_ID_TOT_NUMBER; i++)
                    {
                        EE_IDtable_bl1[i] = (uint32_T)((int32_T)EE_IVOR2_ERROR);
                    }

                    FlashFaultStruct.EE_Dump_From_Page0_Req = 0u;
                    VSRAMMGM_StoreIvor2Data();
                    VSRAMMGM_Update(); /*To update VsRam checksum*/

                    resFerr1 = EE_DO_COPY_FROM_OTHER_PAGE_FAIL;
                }
                else
                {
                    EE_flashErrorOnBlock1 = NO_ERROR;
                    resFerr1 = EE_DO_COPY_FROM_OTHER_PAGE_OK;

                    FlashFaultStruct.lastFaultyFlashLogicalIdx = (uint8_T)FLASH_FAULT_IDX_DEFAULT;
                    FlashFaultStruct.EE_Dump_From_Page0_Req = 0u;
                    VSRAMMGM_StoreIvor2Data();
                    VSRAMMGM_Update(); /*To update VsRam checksum*/

                    /* now it is necessary to dump page 0 to page 1 because we have to keep the natural distances between  */
                    /* the space occupied by page 0 and page 1                                                                                         */
                    StartEEBlock_tmp =  EEPROM_BLK0_START();
                    do
                    {
                        DisableAllInterrupts();
#ifdef EE_SERVE_WATCHDOG
                        EE_Watchdog_Set_Serve();
#endif
                        resErase = FLASH_Erase(StartEEBlock_tmp,(EEPROM_SIZE() >> 1), callback );
#ifdef EE_SERVE_WATCHDOG
                        EE_Watchdog_Cfg_Serve();
#endif
                        EnableAllInterrupts();

                        i++;
                    }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));

                    if (resErase != NO_ERROR)
                    {
                        // return EE_ERASE_NOT_CORRECT;
                        res = EE_ERASE_NOT_CORRECT;
                    }
                    else
                    {
                        pBackupAddr = (uint8_T*)((uint32_T)EEPROM_BLK0_START());

                        res |= EE_DumpLastBlocks(&pBackupAddr,EE_IDtable_bl0, EE_IDtable_bl1, FLASH_BLOCKING);

                        EE_blk0_free_cell = pBackupAddr;
                    }
                    //              EE_errorOnBlock1 = 0;
                }

#ifdef EE_RECOVERY_RESETREQWDT
                EE_RecFlashB0Stored = resFerr0;
                EE_RecFlashB1Stored = resFerr1;
                EE_RecFlashReset = 1u;
                VSRAMMGM_Update();                
                EE_Watchdog_ResetReq();
#endif
            }
        }
    }
    else
    {
        /*Misra 14.10*/
    }

    EE_ConfigStatus = 1u;

    EE_Recovery_FlashBlock0 = resFerr0;
    EE_Recovery_FlashBlock1 = resFerr1;
    
    //    WDT_PeriodicServiceRoutine()

    return res; /* (res!=EE_BLANK) && (res!=PERIPHERAL_NOT_CONFIGURED) TO 
                   correctly execute the EEMGM_PowerOn
     */
}
#endif //EE_RECOVERY_MEMORY_CHECK_ENABLE
#endif //_BUILD_RECOVERY_
#endif //EE_RECOVERY_ENABLE
/***************************************************************************/
//   Function    :   EE_ControlRecord
//
//   Description:    
/*! \brief This method performs the record Id validity, checking header checksum, data size and version. 
Error cases determinate the setting of global variables EE_errorOnBlock0/1, requiring a subsequent logical recovery.
 */
//
//  Parameters and Returns:
/*! 
\param  dataRecord       :   Pointer to RAM data
\param  dataRecordSize :   Data  RAM size
\param  ID_p0              :  Data identifier
\param  checkVersion    :  Enabling check version
\returns EEPROM error code : 
NO_ERROR                                        operation OK
PERIPHERAL_NOT_CONFIGURED         operation not allowed
EE_CHECK_VERSION_FAULT               check version failed                 
ERROR_CHECKSUM                            header/ data don’t mach
EE_WRONG_DATA_RECORD_SIZE       data size don’t mach        
EE_ID_UNAVAILABLE                          requested ID block not found
EE_ERROR_ON_BOTH_BLOCKS            Error on block 0 different from error on block1    
 */
//  Notes:        
/*!
Set Global variables EE_errorOnBlock0/1 to 1 if error is detected influences next logical recovery process and erase-dump process.
EE_ControlRecord() shall be called after EE_Setup() and before EE_Recovery() and for each EEprom ID 
 */
/**************************************************************************/
#ifdef EE_RECOVERY_ENABLE
#ifdef EE_RECOVERY_CONTROL_RECORD
int16_T EE_ControlRecord (uint32_T dataRecordSize, uint16_T ID_p0, uint16_T checkVersion, const char_T* ID_p0_vers)
{
    t_EEBlockDescriptionVar    *pBlockDesc;
    int16_T     res  = NO_ERROR;
    int16_T     res0 = NO_ERROR;
    int16_T     res1 = NO_ERROR;
    uint32_T     chk0 = 0u;
    uint32_T     chk1 = 0u;

    if(EE_ConfigStatus != 0u)
    {
        chk0 = EE_IDtable_bl0[ID_p0];
        chk1 = EE_IDtable_bl1[ID_p0];

        /*CHECK ID ON BLOCK 0*/

#ifdef _BUILD_RECOVERY_
#ifdef EE_RECOVERY_MEMORY_CHECK_ENABLE
        if(EE_flashErrorOnBlock0 == NO_ERROR)
#endif
#endif
        {

            if ((chk0 & EE_ERROR_MASK) == 0u)
            {
                pBlockDesc = (t_EEBlockDescriptionVar *)EE_IDtable_bl0[ID_p0];

                if(EE_CheckVersion(pBlockDesc, checkVersion, ID_p0_vers )!= NO_ERROR)
                {
                    res0 |= EE_CHECK_VERSION_FAULT;
                }
                else if(EE_CheckBlockDescriptor(pBlockDesc) != NO_ERROR)
                {
                    res0 |= ERROR_CHECKSUM;
                }
                else
                {
                    if((EE_CheckBlockData(pBlockDesc)==NO_ERROR))
                    {
                        /* DO NOT SET ERROR FOR SIZE TEST */
                        res0 |= NO_ERROR;
                    }
                    else
                    {
                        res0 |= ERROR_CHECKSUM;
                    }
                }

            }
            else if(chk0 != (uint32_T)(-1))
            {
                res0 |= ERROR_CHECKSUM;
            }
            else
            {
                /*MISRA 2004 Rule 14.10*/
            }

            if(res0 != NO_ERROR)
            {
                EE_errorOnBlock0=1u;
            }

            /*CHECK ID ON BLOCK 1*/

        }

#ifdef _BUILD_RECOVERY_
#ifdef EE_RECOVERY_MEMORY_CHECK_ENABLE
        if(EE_flashErrorOnBlock1 == NO_ERROR)
#endif
#endif
        {
            if ((chk1 & EE_ERROR_MASK) == 0u)
            {
                pBlockDesc = (t_EEBlockDescriptionVar *)EE_IDtable_bl1[ID_p0];

                if(EE_CheckVersion(pBlockDesc, checkVersion, ID_p0_vers )!= NO_ERROR)
                {
                    res1 |= EE_CHECK_VERSION_FAULT;
                }
                else if(EE_CheckBlockDescriptor(pBlockDesc) != NO_ERROR)
                {
                    res1 |= ERROR_CHECKSUM;
                }
                else
                {
                    if((EE_CheckBlockData( pBlockDesc)==NO_ERROR))
                    {
                        /* DO NOT SET ERROR FOR SIZE TEST */
                        res1 |= NO_ERROR;
                        }
                    else
                    {
                        res1 |= ERROR_CHECKSUM;
                    }
                }
            }
            else if(chk1 != (uint32_T)(-1)) //EE_BlkSeeking eventually sets an error check
            {
                res1 |= ERROR_CHECKSUM;
            }
            else
            {
                /*MISRA 2004 Rule 14.10*/
            }

            if(res1 != NO_ERROR)
            {
                EE_errorOnBlock1=1u;
            }
        }

        if (((chk0 & EE_ERROR_MASK) != 0u) && ((chk1 & EE_ERROR_MASK) != 0u))
        {

            if ((chk0 == (uint32_T)ERROR_CHECKSUM) && (chk1 == (uint32_T)ERROR_CHECKSUM))
            {
                res = ERROR_CHECKSUM;
                EE_errorOnBlock1=1u;
                EE_errorOnBlock0=1u;
            }
            else
            {
                res = EE_ID_UNAVAILABLE;
            }
        }
        else
        {
            if((res0 != NO_ERROR) && (res1 != NO_ERROR))
            {
                if (res0 != res1)
                {
                    res = EE_ERROR_ON_BOTH_BLOCKS;
                }
                else
                {
                    res = res0; // in this case res0 == res1 and both on the same error
                }
            }
            else
            {
                if(res0!=NO_ERROR)
                {
                    res = res0;
                }
                else
                {
                    res = res1;
                }

            }

        }
    }
    else
    {
        res = PERIPHERAL_NOT_CONFIGURED;
    }

    return res;
}
#endif
#endif



/***************************************************************************/
//   Function    :   EE_ReadRecord
//
//   Description:    
/*! \brief This method is used to read the last correctly programmed record with ID identifier to be copied in RAM, 
after the CRC validation, using the pointer tables
 */
//
//  Parameters and Returns:
/*! 
\param dataRecord :        Pointer to RAM data
\param dataRecordSize :   Data  RAM size
\param ID_p1 :                Data identifier 
\param  checkVersion :     Enabling check version
\returns  EEPROM error code :
NO_ERROR                                       operation OK
ERROR_CHECKSUM                           header/data don’t mach
EE_CHECK_VERSION_FAULT              check version failed
EE_ID_UNAVAILABLE                         ID don’t found in EEPROM
EE_WRONG_DATA_RECORD_SIZE ID  not matching with the data specified
EE_ERROR_ON_BOTH_BLOCKS           Error on block 0 different from error on block1    
 */
//  Notes:        
/*!
 */
/**************************************************************************/
int16_T EE_ReadRecord(uint32_T* dataRecord, uint16_T dataRecordSize, uint16_T ID_p1, uint16_T checkVersion, const char_T* ID_p1_vers)

{
    t_EEBlockDescriptionVar     *pBlockDesc;
    int16_T      res = NO_ERROR;
    int16_T      res0 = NO_ERROR;
    int16_T      res1 = NO_ERROR;
    uint32_T      chk0;
    uint32_T      chk1;


    if(EE_ConfigStatus == 0u)
    {
        res = PERIPHERAL_NOT_CONFIGURED;
    }
    else
    {
#if 0 /* EE_ReadRecord Flux Optimization */
        res0 = EE_ID_UNAVAILABLE;

        chk0 = EE_IDtable_bl0[ID_p1];
        chk1 = EE_IDtable_bl1[ID_p1];

        if ( ((chk0 & EE_ERROR_MASK) | EE_errorOnBlock0) == 0)
        {
            pBlockDesc = (uint8_T *)EE_IDtable_bl0[ID_p1];
            res0 = EE_loadID(dataRecord,dataRecordSize,checkVersion,pBlockDesc);

        }

        if(res0 != NO_ERROR)
        {
            if (((chk1 & EE_ERROR_MASK) | EE_errorOnBlock1) == 0)
            {
                pBlockDesc = (uint8_T *)EE_IDtable_bl1[ID_p1];
                res = EE_loadID(dataRecord,dataRecordSize,checkVersion,pBlockDesc);

                if((res != NO_ERROR) && (res != res0))
                {
                    res = EE_ERROR_ON_BOTH_BLOCKS;
                }
                else
                {
                    /* res = error on blk1 */
                }
            }
            else
            {
                /* if the block address of the requested ID ia unavailable, return with the appropriate error code */
                if ((chk0 == ERROR_CHECKSUM) && (chk1 == ERROR_CHECKSUM))
                {
                    res = ERROR_CHECKSUM;
                }
                else if((EE_flashErrorOnBlock0 != NO_ERROR)||(EE_flashErrorOnBlock1 != NO_ERROR))
                {
                    res |= EE_IVOR2_ERROR;
                }
                else
                {
                    res = EE_ID_UNAVAILABLE;
                }
            }
        }
        else
        {
            res = NO_ERROR;
        }
#else  //EE_ReadRecord_Optimization

        chk0 = EE_IDtable_bl0[ID_p1];
        chk1 = EE_IDtable_bl1[ID_p1];

        if ( ((chk0 & EE_ERROR_MASK) | EE_errorOnBlock0) == 0u)
        {
            pBlockDesc = (t_EEBlockDescriptionVar *)EE_IDtable_bl0[ID_p1];
        }
        else if (((chk1 & EE_ERROR_MASK) | EE_errorOnBlock1) == 0u)
        {
            pBlockDesc = (t_EEBlockDescriptionVar *)EE_IDtable_bl1[ID_p1];
        }
        else
        { /* if the block address of the requested ID ia unavailable, return with the appropriate error code */
            if ((chk0 == (uint32_T)ERROR_CHECKSUM) && (chk1 == (uint32_T)ERROR_CHECKSUM))
            {
                res = ERROR_CHECKSUM;
            }
            else
            {
                res = EE_ID_UNAVAILABLE;
            }
        }
        if (res == NO_ERROR)
        {
            /*load data from block 0*/
            res0 = EE_loadID(dataRecord,dataRecordSize,checkVersion,pBlockDesc,ID_p1_vers);

            /*if error on record on block 0 found*/
            if(res0 != NO_ERROR)
            {
                pBlockDesc = (t_EEBlockDescriptionVar *)EE_IDtable_bl1[ID_p1];
                /*load data from block 1*/
                res1 = EE_loadID(dataRecord,dataRecordSize,checkVersion,pBlockDesc,ID_p1_vers );  

                if((res1!=NO_ERROR) && (res1!=res0))
                {
                    res = EE_ERROR_ON_BOTH_BLOCKS;
                } 
                else 
                {
                    res = res1;
                }

            }
            else
            {
                res=res0;
            }
        }
#endif
    }

    return res;
}


/***************************************************************************/
//   Function    :   EE_UpdateRecord
//
//   Description:    
/*! \brief This method performs the record writing to Flash starting at the first blank location in the EEPROM blocks 
(first block and redundancy block)
 */
//
//  Parameters and Returns:
/*! 
\param  dataRecord :        Pointer to RAM data 
\param  dataRecordSize :  Data  RAM size 
\param  ID_p2 :                Data identifier 
\returns  EEPROM error code :
NO_ERROR                          operation OK
EE_SIZE_NOT_ALIGNED       alignment error in checksum calculation
EE_ERROR                           data not correctly stored on FLASH
 */
//  Notes:        
/*!
 */
/**************************************************************************/
int16_T EE_UpdateRecord(uint32_T  * dataRecord ,uint32_T dataRecordSize, uint16_T ID_p2, uint32_T* padding_P2, const char_T* ID_p2_vers)
{
    uint8_T    *pBackupAddr;
    uint8_T    eraseConditionBlk0 = 0u;
    uint8_T    eraseConditionBlk1 = 0u;
    int16_T    res = NO_ERROR;
    int16_T    resVal = NO_ERROR;
    int16_T    resErase = NO_ERROR;
    t_EEBlockDescriptionVar  currentBlockDescriptor;
    uint8_T    i;
    uint8_T    ee_status;
#ifdef FLASH_TYPE
#if (FLASH_TYPE != C55)
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFFu;
#else
    uint8_T callback = FLASH_ERASE_NO_CBK;
#endif
#endif



    if(EE_ConfigStatus == 0u)
    {
        resVal = PERIPHERAL_NOT_CONFIGURED;
    }
    else
    { 
        /*Preparing data*/       
        resVal = EE_BlkLoadData(ID_p2,dataRecord,dataRecordSize,&currentBlockDescriptor,padding_P2,ID_p2_vers );

        if (resVal == NO_ERROR)
        {         
            if( (EE_errorOnBlock0==0u) && (EE_errorOnBlock1==0u) ) /*normal case, both pages OK*/
            {

                /****************CHECK BLK1****************************/
                eraseConditionBlk1 = EE_checkBlk1(ID_p2, dataRecordSize);

                /****************CHECK BLK0****************************/
                eraseConditionBlk0 = EE_checkBlk0(ID_p2, dataRecordSize);

                if((eraseConditionBlk0==0u) && (eraseConditionBlk1==0u))
                {
                    ee_status =  UPDATE_BLK0_BLK1;  /*normal update*/
                }
                else if ((eraseConditionBlk0==0u) && (eraseConditionBlk1==1u))
                {
                    ee_status =  UPDATE_BLK0_BLK1FULL; /*update block0, erase block1, dump block0->1*/
                }
                else if ((eraseConditionBlk0==1u) && (eraseConditionBlk1==0u))
                {
                    ee_status =  UPDATE_BLK1_BLK0FULL; /*update block1, erase block0, dump block1->0*/
                }
                else
                {
                    ee_status =  EEPROM_FULL; /*erase blk1, dump block0->1, update blk1*/
                }
            }
#ifdef EE_RECOVERY_ENABLE
            else if ((EE_errorOnBlock0==1u) && (EE_errorOnBlock1==0u))/*block 0 inhibited*/
            {

                /****************CHECK BLK1****************************/
                eraseConditionBlk1 = EE_checkBlk1(ID_p2, dataRecordSize);

                if(eraseConditionBlk1==0u)
                {
                    ee_status =  UPDATE_BLK1_BLK0INHIBITED;  /*normal update, only block 1*/
                }
                else 
                {
                    ee_status =  UPDATE_BLK1FULL_BLK0INHIBITED; /*erase block1, update with RAM var*/
                }

            }
            else if ((EE_errorOnBlock0==0u) && (EE_errorOnBlock1==1u)) /**block 1 inhibited*/
            {
                /****************CHECK BLK0****************************/
                eraseConditionBlk0 = EE_checkBlk0(ID_p2, dataRecordSize);

                if(eraseConditionBlk0==0u)
                {
                    ee_status =  UPDATE_BLK0_BLK1INHIBITED;  /*normal update only block 0*/
                }
                else 
                {
                    ee_status =  UPDATE_BLK0FULL_BLK1INHIBITED; /*erase block0, update with RAM var*/
                }

            }
            else /*ALL EEPROM INHIBITED*/
            {
                ee_status = EEPROM_INHIBITED;
            }
#endif //EE_RECOVERY_ENABLE


            switch(ee_status)
            {
            case UPDATE_BLK0_BLK1 :
            {
                /*UPDATE BLK 0*/
                res |= EE_BlkProgramAndVerify( (uint32_T)EE_blk0_free_cell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);

                res |= EE_BlkProgramAndVerify(((uint32_T)EE_blk0_free_cell+sizeof(t_EEBlockDescriptionVar)),(uint32_T)dataRecordSize,(uint32_T)dataRecord);

                EE_IDtable_bl0[ID_p2] = (uint32_T)EE_blk0_free_cell;
                EE_blk0_free_cell = EE_blk0_free_cell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;

                /*UPDATE BLK 1*/

                res |= EE_BlkProgramAndVerify( (uint32_T)EE_blk1_free_cell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);

                res |= EE_BlkProgramAndVerify(((uint32_T)EE_blk1_free_cell +sizeof(t_EEBlockDescriptionVar)),(uint32_T)dataRecordSize,(uint32_T)dataRecord);
                EE_IDtable_bl1[ID_p2] = (uint32_T)EE_blk1_free_cell;
                EE_blk1_free_cell = EE_blk1_free_cell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;

                break;
            }
            case UPDATE_BLK0_BLK1FULL :
            {
                res |= EE_BlkProgramAndVerify( (uint32_T)EE_blk0_free_cell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);

                res |= EE_BlkProgramAndVerify(((uint32_T)EE_blk0_free_cell+sizeof(t_EEBlockDescriptionVar)),(uint32_T)dataRecordSize,(uint32_T)dataRecord);

                EE_IDtable_bl0[ID_p2] = (uint32_T)EE_blk0_free_cell;
                EE_blk0_free_cell = EE_blk0_free_cell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;

                i = 0u;

                do
                {

                    resErase = EE_BlkErase(EEPROM_BLK1_START(),EEPROM_BLK1_END()-EEPROM_BLK1_START(), callback);

                    i++;
                }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));

                if (resErase != NO_ERROR)
                {
                    resVal = EE_ERASE_NOT_CORRECT;
                }
                else
                {
                    pBackupAddr = (uint8_T*)((uint32_T)EEPROM_BLK1_START());

                    /* resetting the EEPROM block 1 ID table  */
                    for (i=0u; i<EE_ID_TOT_NUMBER; i++)
                    {
                        EE_IDtable_bl1[i] = EE_IDTABLE_INIT;
                    }

                    res |= EE_DumpLastBlocks(&pBackupAddr,EE_IDtable_bl1, EE_IDtable_bl0, FLASH_BLOCKING);
                    EE_blk1_free_cell = pBackupAddr;
                }

                break;
            }
            case UPDATE_BLK1_BLK0FULL :
            {
                /* Programming ID header on page 1*/
                res |= EE_BlkProgramAndVerify((uint32_T)EE_blk1_free_cell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);
                /* Programming ID Data on page 1 */
                res |= EE_BlkProgramAndVerify(((uint32_T)EE_blk1_free_cell+sizeof(t_EEBlockDescriptionVar)),(uint32_T)dataRecordSize,(uint32_T)dataRecord);

                EE_IDtable_bl1[ID_p2] = (uint32_T)EE_blk1_free_cell;
                /* Updating pointer to next free cell on page 1*/
                EE_blk1_free_cell = EE_blk1_free_cell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;

                i = 0u;

                do
                {
                    resErase = EE_BlkErase(EEPROM_BLK0_START(),EEPROM_BLK0_END()-EEPROM_BLK0_START(), callback);
                    i++;
                }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));

                if (resErase != NO_ERROR)
                {
                    resVal = EE_ERASE_NOT_CORRECT;
                }
                else
                {

                    /* resetting the EEPROM block 0 ID table  */
                    for (i=0u; i<EE_ID_TOT_NUMBER; i++)
                    {
                        EE_IDtable_bl0[i] = EE_IDTABLE_INIT;
                    }

                    pBackupAddr = (uint8_T*)((uint32_T)EEPROM_BLK0_START());
                    res |= EE_DumpLastBlocks(&pBackupAddr, EE_IDtable_bl0, EE_IDtable_bl1, FLASH_BLOCKING);
                    EE_blk0_free_cell = pBackupAddr;
                }
                break;
            }
            case EEPROM_FULL :
            {

                /* invalidate payload in EEPROM block 0 */
                EE_BlkInvalidateData((t_EEBlockDescriptionVar *)EE_IDtable_bl0[ID_p2]);
                /*update table after invalidating*/
                EE_IDtable_bl0[ID_p2] = EE_IDTABLE_INIT;

                /* Erase blk1*/
                i = 0u;
                do
                {
                    resErase = EE_BlkErase(EEPROM_BLK1_START(),EEPROM_BLK1_END()-EEPROM_BLK1_START(), callback);

                    i++;
                }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));


                if (resErase != NO_ERROR)
                {
                    resVal = EE_ERASE_NOT_CORRECT;
                }
                else
                {
                    pBackupAddr = (uint8_T*)((uint32_T)EEPROM_BLK1_START());

                    /* resetting the EEPROM block 1 ID table  */
                    for (i=0u; i<EE_ID_TOT_NUMBER; i++)
                    {
                        EE_IDtable_bl1[i] = EE_IDTABLE_INIT;
                    }

                    /* Dumping valid blocks from page page 0 to page 1 */
                    res |= EE_DumpLastBlocks(&pBackupAddr,EE_IDtable_bl1, EE_IDtable_bl0, FLASH_BLOCKING);
                    /* Updating pointer to next free cell on page 1*/
                    EE_blk1_free_cell = pBackupAddr;
                    /*updating id table 1*/
                    EE_IDtable_bl1[ID_p2] = (uint32_T)EE_blk1_free_cell;
                    /*Actual update on eeprom blk1*/
                    /* Programming ID header on page 1*/
                    res |= EE_BlkProgramAndVerify((uint32_T)EE_blk1_free_cell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);
                    /* Programming ID Data on page 1*/
                    res |= EE_BlkProgramAndVerify(((uint32_T)EE_blk1_free_cell+sizeof(t_EEBlockDescriptionVar)),(uint32_T)dataRecordSize,(uint32_T)dataRecord);
                    /* Updating pointer to next free cell on page 1*/
                    EE_blk1_free_cell = EE_blk1_free_cell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;

                }
                break;
            }
#ifdef EE_RECOVERY_ENABLE
            case UPDATE_BLK1_BLK0INHIBITED :
            {
                res |= EE_BlkProgramAndVerify( (uint32_T)EE_blk1_free_cell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);

                res |= EE_BlkProgramAndVerify(((uint32_T)EE_blk1_free_cell+sizeof(t_EEBlockDescriptionVar)),(uint32_T)dataRecordSize,(uint32_T)dataRecord);

                EE_IDtable_bl1[ID_p2] = (uint32_T)EE_blk1_free_cell;

                EE_blk1_free_cell = EE_blk1_free_cell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;

                break;
            }
            case UPDATE_BLK1FULL_BLK0INHIBITED :
            {

                /*ERASE*/
                i = 0u;
                do
                {

                    resErase = EE_BlkErase(EEPROM_BLK1_START(),EEPROM_BLK1_END()-EEPROM_BLK1_START(), callback);

                    i++;
                }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));

                /*Reprogram ALL IDs with its Ram value of IDs*/
                res |= EE_FillBlockWithRamData(EE_PAGE_1,ID_p2_vers );

                break;
            }
            case UPDATE_BLK0_BLK1INHIBITED :
            {

                res |= EE_BlkProgramAndVerify( (uint32_T)EE_blk0_free_cell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);

                res |= EE_BlkProgramAndVerify(((uint32_T)EE_blk0_free_cell+sizeof(t_EEBlockDescriptionVar)),(uint32_T)dataRecordSize,(uint32_T)dataRecord);

                EE_IDtable_bl0[ID_p2] = (uint32_T)EE_blk0_free_cell;

                EE_blk0_free_cell = EE_blk0_free_cell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;

                break;
            }
            case UPDATE_BLK0FULL_BLK1INHIBITED :
            {
                i = 0u;
                do
                {
                    resErase = EE_BlkErase(EEPROM_BLK0_START(),EEPROM_BLK0_END()-EEPROM_BLK0_START(), callback);

                    i++;
                }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));

                /*Reprogram ALL IDs with its Ram value of IDs*/
                res |= EE_FillBlockWithRamData(EE_PAGE_0, ID_p2_vers);

                break;
            }
            case EEPROM_INHIBITED :
            {
                /*DO NOTHING*/
                break;
            }
#endif  //EE_RECOVERY_ENABLE
            default : {break;}
            }

        }

        EE_DataPtr0 = (uint32_T)EE_blk0_free_cell;
        EE_DataPtr1 = (uint32_T)EE_blk1_free_cell;
    }

    if ((res != NO_ERROR) && (resVal == NO_ERROR))
    {
        resVal = EE_ERROR;
    }

    return resVal;
}



/***************************************************************************/
//   Function    :   EE_InvalidateRecord
//
//   Description:    
/*! \brief This method performs the invalidation of the data ID block requested
 */
//
//  Parameters and Returns:
/*! 
\param ID_p3 : ID to be invalidated
\returns EEPROM error code :
NO_ERROR                           operation OK
EE_ERROR                           FLASH erase not correctly executed
EE_ID_UNAVAILABLE            requested ID block not found
 */
//  Notes:        
/*!
 */
/**************************************************************************/
int16_T EE_InvalidateRecord(uint16_T ID_p3)
{
    t_EEBlockDescriptionVar  tempBlockDescriptor;
    t_EEBlockDescriptionVar                  *pBlockDesc;
    int16_T                  res1 = NO_ERROR;
    int16_T                  res2 = NO_ERROR;
    int16_T                  resVal = NO_ERROR;
    boolean_T   flgTableFound = FALSE;

    if(EE_ConfigStatus == 0u)
    {
        resVal = PERIPHERAL_NOT_CONFIGURED;
    }

    if (resVal == NO_ERROR)
    {
        /* we have to invalidate the req blocks in both EEPROM half-areas */
        if ((EE_IDtable_bl0[ID_p3] & EE_ERROR_MASK) == 0u)
        {
            flgTableFound = TRUE;
            pBlockDesc = (t_EEBlockDescriptionVar *)EE_IDtable_bl0[ID_p3];
            tempBlockDescriptor = *pBlockDesc;
            tempBlockDescriptor.EElast = 0u;
            DisableAllInterrupts();
            res1 = FLASH_Program( (uint32_T)pBlockDesc,sizeof(t_EEBlockDescriptionVar),(uint32_T)&tempBlockDescriptor);
            EnableAllInterrupts();

            EE_IDtable_bl0[ID_p3] = EE_IDTABLE_INIT;
        }

        if ((EE_IDtable_bl1[ID_p3] & EE_ERROR_MASK) == 0u)
        {
            flgTableFound = TRUE;
            pBlockDesc = (t_EEBlockDescriptionVar *)EE_IDtable_bl1[ID_p3];
            tempBlockDescriptor = *(t_EEBlockDescriptionVar*)pBlockDesc;
            tempBlockDescriptor.EElast = 0u;
            DisableAllInterrupts();
            res2 = FLASH_Program( (uint32_T)pBlockDesc,sizeof(t_EEBlockDescriptionVar),(uint32_T)&tempBlockDescriptor);
            EnableAllInterrupts();

            EE_IDtable_bl1[ID_p3] = EE_IDTABLE_INIT;
        }

        if (((res1 == NO_ERROR) || (res2 == NO_ERROR)) && (flgTableFound == TRUE))
        {
            resVal = NO_ERROR;
        }
        else
        {
            if (flgTableFound == TRUE)
            {
                resVal = EE_ERROR;
            }
            else
            {
                resVal = EE_ID_UNAVAILABLE;
            }
        }
    }

    return resVal;
}

#ifdef  EE_RECOVERY_EXTERNALIZE_FLUSH
/***************************************************************************/
//   Function    :   EE_Flush_ext
//
//   Description:    
/*! \brief This method externalizes EE_Flush
In this case, the EEPROM page must be invalidated in order to try a copy from the correct page
then, the correct page must be also invalidated and copied from the formerly wrong page,
to avoid misalignment between the two pages
 */
//
//  Parameters and Returns:
/*! 
\param  EEPROM_Page :       page block wrong
\param  blockingStatus :      FLASH_BLOCKING or FLASH_NOT_BLOCKING  used for enable/disable interrupts in flashing operations (dump)                            
\returns EEPROM error code :
NO_ERROR                                 operation OK
EE_ERROR                                  the page is not EE_PAGE_0 or EE_PAGE_1
EE_ERASE_NOT_CORRECT           EEPROM erase not correctly finished
 */
/***************************************************************************/
int16_T  EE_Flush_ext(uint8_T EEPROM_Page,uint8_T blockingStatus, const char_T* ID_version)
{
int16_T  res;

    res = EE_Flush(EEPROM_Page,blockingStatus, ID_version);

    return res;
}
#endif
/*!\egroup*/
/*!
\defgroup PrivateFunctions Private Functions 
\sgroup
*/
/*==================================================================================================
                                   PRIVATE FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   EE_Flush
//
//   Description:    
/*! \brief This method performs the invalidation of the page data block. 
In this case, the EEPROM page must be invalidated in order to try a copy from the correct page
then, the correct page must be also invalidated and copied from the formerly wrong page,
to avoid misalignment between the two pages
 */
//
//  Parameters and Returns:
/*! 
\param  EEPROM_Page :       page block wrong
\param  blockingStatus :      FLASH_BLOCKING or FLASH_NOT_BLOCKING  used for enable/disable interrupts in flashing operations (dump)                            
\returns EEPROM error code :
NO_ERROR                                 operation OK
EE_ERROR                                  the page is not EE_PAGE_0 or EE_PAGE_1
EE_ERASE_NOT_CORRECT           EEPROM erase not correctly finished
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_Flush(uint8_T EEPROM_Page,uint8_T blockingStatus, const char_T* ID_version)
{
    uint8_T     *pBackupAddr;
    int16_T     resErase = NO_ERROR;
    int16_T     res      = NO_ERROR;
    int16_T     resBlankCheck = NO_ERROR;
    uint8_T     i = 0u;
    uint8_T     *EE_Other_blk_free_cell_tmp;
    uint32_T    StartEEBlock_tmp;
    uint32_T    *EE_IDtable_tmp;
    uint32_T    EndOtherEEBlock_tmp;
    uint32_T    *EE_OtherIDtable_tmp;
#ifdef FLASH_TYPE
#if (FLASH_TYPE != C55)
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFFu;
#else
    uint8_T callback = FLASH_ERASE_NO_CBK;
#endif
#endif
    if ((EEPROM_Page != EE_PAGE_0) && (EEPROM_Page != EE_PAGE_1))
    {
        res = EE_ERROR;
    }
    else
    { /* Error in EEPROM page 0 */ 
        EE_blk0_free_cell = (uint8_T*)(EEPROM_BLK0_START());
        EE_blk1_free_cell = (uint8_T*)(EEPROM_BLK1_HALF_BLOCK());

        for (i=0u; i<EE_ID_TOT_NUMBER; i++)
        {   /* resetting the ptr ID table */
            EE_IDtable_bl0[i] = EE_IDTABLE_INIT;
            EE_IDtable_bl1[i] = EE_IDTABLE_INIT;
        }

        if (EEPROM_Page == EE_PAGE_0)
        {
            StartEEBlock_tmp = EEPROM_BLK0_START();
            EndOtherEEBlock_tmp = EEPROM_BLK1_END();
            EE_IDtable_tmp = EE_IDtable_bl0;
            EE_OtherIDtable_tmp = EE_IDtable_bl1;
        }
        else
        {
            EE_Other_blk_free_cell_tmp = (uint8_T *)EEPROM_BLK0_START();
            StartEEBlock_tmp = EEPROM_BLK1_START();
            EndOtherEEBlock_tmp = EEPROM_BLK0_END();
            EE_IDtable_tmp = EE_IDtable_bl1;
            EE_OtherIDtable_tmp = EE_IDtable_bl0;
        }

        do
        {
            DisableAllInterrupts();
#ifdef EE_SERVE_WATCHDOG
            EE_Watchdog_Set_Serve();
#endif
            resErase = FLASH_Erase(StartEEBlock_tmp,(EEPROM_SIZE() >> 1), callback);
#ifdef EE_SERVE_WATCHDOG
            EE_Watchdog_Cfg_Serve();
#endif
            EnableAllInterrupts();

            i++;

        }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));

        if (resErase != NO_ERROR)
        {
            res = EE_ERASE_NOT_CORRECT;
        }
        else
        {
            if (EEPROM_Page == EE_PAGE_0)
            {
                resBlankCheck = FLASH_BlankCheck(EEPROM_BLK1_START(),EEPROM_BLK1_HALF_BLOCK()-EEPROM_BLK1_START());
                if (resBlankCheck != NO_ERROR)
                {
                    EE_Other_blk_free_cell_tmp = (uint8_T*)(EEPROM_BLK1_START());
                }
                else
                {
                    EE_Other_blk_free_cell_tmp = (uint8_T*)(EEPROM_BLK1_HALF_BLOCK());
                }
            }

            res |= EE_BlkSeeking((uint8_T**) &EE_Other_blk_free_cell_tmp, (uint8_T*)EndOtherEEBlock_tmp, EE_OtherIDtable_tmp);
            if((res == NO_ERROR) || (res == EE_SCANNING_COMPLETED))
            {    
                res = NO_ERROR;
            }
            /* dumping the page 1 to page 0 */
            if (EEPROM_Page == EE_PAGE_1)
            {
                pBackupAddr = (uint8_T*)(EEPROM_BLK1_HALF_BLOCK());
            }
            else
            {
                pBackupAddr = (uint8_T*)(StartEEBlock_tmp);
            }

            res |= EE_DumpLastBlocks(&pBackupAddr, EE_IDtable_tmp, EE_OtherIDtable_tmp, blockingStatus);

#ifdef EE_RECOVERY_ENABLE
            /* Control on just written blocks */
            EE_errorOnBlock0=0u;
            EE_errorOnBlock1=0u;
#ifdef EE_RECOVERY_CONTROL_RECORD
#ifdef EE_ID0_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID0_END - (uint32_T)&__EE_ID0_START, EE_ID0, EE_CHECK_VERSION,  ID_version);
#endif
#ifdef EE_ID1_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID1_END - (uint32_T)&__EE_ID1_START, EE_ID1, EE_CHECK_VERSION, (ID_version+sizeof(uint32_T)));
#endif
#ifdef EE_ID2_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID2_END - (uint32_T)&__EE_ID2_START, EE_ID2, EE_CHECK_VERSION,(ID_version+(EE_ID2*sizeof(uint32_T))));
#endif
#ifdef EE_ID3_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID3_END - (uint32_T)&__EE_ID3_START, EE_ID3, EE_CHECK_VERSION,(ID_version+(EE_ID3*sizeof(uint32_T))));
#endif
#ifdef EE_ID4_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID4_END - (uint32_T)&__EE_ID4_START, EE_ID4, EE_CHECK_VERSION,(ID_version+(EE_ID4*sizeof(uint32_T))));
#endif
#ifdef EE_ID5_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID5_END - (uint32_T)&__EE_ID5_START, EE_ID5, EE_CHECK_VERSION,(ID_version+(EE_ID5*sizeof(uint32_T))));
#endif
#ifdef EE_ID6_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID6_END - (uint32_T)&__EE_ID6_START, EE_ID6, EE_CHECK_VERSION,(ID_version+(EE_ID6*sizeof(uint32_T))));
#endif
#ifdef EE_ID7_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID7_END - (uint32_T)&__EE_ID7_START, EE_ID7, EE_CHECK_VERSION,(ID_version+(EE_ID7*sizeof(uint32_T))));
#endif
#ifdef EE_ID8_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID8_END - (uint32_T)&__EE_ID8_START, EE_ID8, EE_CHECK_VERSION,(ID_version+(EE_ID8*sizeof(uint32_T))));
#endif
#ifdef EE_ID9_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID9_END - (uint32_T)&__EE_ID9_START, EE_ID9, EE_CHECK_VERSION,(ID_version+(EE_ID9*sizeof(uint32_T))));
#endif
#ifdef EE_ID10_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID10_END - (uint32_T)&__EE_ID10_START, EE_ID10, EE_CHECK_VERSION,(ID_version+(EE_ID10*sizeof(uint32_T))));
#endif
#ifdef EE_ID11_USED
            res |= EE_ControlRecord((uint32_T)&__EE_ID11_END - (uint32_T)&__EE_ID11_START, EE_ID11, EE_CHECK_VERSION,(ID_version+(EE_ID11*sizeof(uint32_T))));
#endif

            /* We stop here to force failure after dumping check.
               TEST 48.1 Test_EEPROM_1_3.xls; 
               at breakpoint we force EE_errorOnBlock0 = 1
               Comment this line to test failure on block 1 */        


            /* If the ID is not valid in any of the two pages, eg ID3,ID5 or ID7 invalidated after a dwn on KWP, this
                    should rightly be lacking in both pages, after a subsequent dumping and controls */

            if (res == EE_ID_UNAVAILABLE)
            {
                res = NO_ERROR;
            }
#endif  //EE_RECOVERY_CONTROL_RECORD
#endif  //EE_RECOVERY_ENABLE

            if (EEPROM_Page == EE_PAGE_0)
            {
                EE_blk0_free_cell = pBackupAddr;
            }
            else
            {
                EE_blk1_free_cell = pBackupAddr;
            }

            if((res == NO_ERROR) && (EE_errorOnBlock0 == 0u) && (EE_errorOnBlock1 == 0u))
            {
                for (i=0u; i<EE_ID_TOT_NUMBER; i++)
                {   /* resetting the ptr ID table */
                    EE_OtherIDtable_tmp[i] = EE_IDTABLE_INIT;
                }

                /* now it is necessary to dump page 0 to page 1 because we have to keep the natural distances between  *
                 * the space occupied by page 0 and page 1                                                                                         */
                if (EEPROM_Page == EE_PAGE_0)
                {
                    EE_Other_blk_free_cell_tmp = (uint8_T *)EEPROM_BLK0_START();
                    StartEEBlock_tmp = EEPROM_BLK1_START();
                    EndOtherEEBlock_tmp = EEPROM_BLK0_END();
                    EE_IDtable_tmp = EE_IDtable_bl1;
                    EE_OtherIDtable_tmp = EE_IDtable_bl0;
                }
                else
                {
                    EE_Other_blk_free_cell_tmp = (uint8_T *)EEPROM_BLK1_START();
                    StartEEBlock_tmp = EEPROM_BLK0_START();
                    EndOtherEEBlock_tmp = EEPROM_BLK1_END();
                    EE_IDtable_tmp = EE_IDtable_bl0;
                    EE_OtherIDtable_tmp = EE_IDtable_bl1;
                }

                do
                {
                    DisableAllInterrupts();
#ifdef EE_SERVE_WATCHDOG
                    EE_Watchdog_Set_Serve();
#endif
                    resErase = FLASH_Erase(StartEEBlock_tmp,(EEPROM_SIZE() >> 1), callback);
#ifdef EE_SERVE_WATCHDOG
                    EE_Watchdog_Cfg_Serve();
#endif
                    EnableAllInterrupts();

                    i++;
                }while ((resErase != NO_ERROR) && (i < EE_ERASE_RETRY));

                if (resErase != NO_ERROR)
                {
                    // return EE_ERASE_NOT_CORRECT;
                    res = EE_ERASE_NOT_CORRECT;
                }
                else
                {

                    /* dumping the page 0 to page 1 */
                    if (EEPROM_Page == EE_PAGE_0)
                    {
                        pBackupAddr = (uint8_T*)((uint32_T)EEPROM_BLK1_HALF_BLOCK());
                    }
                    else
                    {
                        pBackupAddr = (uint8_T*)(StartEEBlock_tmp);
                    }

                    res |= EE_DumpLastBlocks(&pBackupAddr,EE_IDtable_tmp, EE_OtherIDtable_tmp, blockingStatus);

#ifdef EE_RECOVERY_ENABLE
                    /* Control on just written blocks */
                    EE_errorOnBlock0=0u;
                    EE_errorOnBlock1=0u;
#ifdef EE_RECOVERY_CONTROL_RECORD
#ifdef EE_ID0_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID0_END - (uint32_T)&__EE_ID0_START, EE_ID0, EE_CHECK_VERSION, ID_version);
#endif
#ifdef EE_ID1_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID1_END - (uint32_T)&__EE_ID1_START, EE_ID1, EE_CHECK_VERSION,  (ID_version+sizeof(uint32_T)));
#endif
#ifdef EE_ID2_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID2_END - (uint32_T)&__EE_ID2_START, EE_ID2, EE_CHECK_VERSION, (ID_version+(EE_ID2*sizeof(uint32_T))));
#endif
#ifdef EE_ID3_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID3_END - (uint32_T)&__EE_ID3_START, EE_ID3, EE_CHECK_VERSION, (ID_version+(EE_ID3*sizeof(uint32_T))));
#endif
#ifdef EE_ID4_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID4_END - (uint32_T)&__EE_ID4_START, EE_ID4, EE_CHECK_VERSION, (ID_version+(EE_ID4*sizeof(uint32_T))));
#endif
#ifdef EE_ID5_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID5_END - (uint32_T)&__EE_ID5_START, EE_ID5, EE_CHECK_VERSION, (ID_version+(EE_ID5*sizeof(uint32_T))));
#endif
#ifdef EE_ID6_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID6_END - (uint32_T)&__EE_ID6_START, EE_ID6, EE_CHECK_VERSION, (ID_version+(EE_ID6*sizeof(uint32_T))));
#endif
#ifdef EE_ID7_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID7_END - (uint32_T)&__EE_ID7_START, EE_ID7, EE_CHECK_VERSION, (ID_version+(EE_ID7*sizeof(uint32_T))));
#endif
#ifdef EE_ID8_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID8_END - (uint32_T)&__EE_ID8_START, EE_ID8, EE_CHECK_VERSION, (ID_version+(EE_ID8*sizeof(uint32_T))));
#endif
#ifdef EE_ID9_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID9_END - (uint32_T)&__EE_ID9_START, EE_ID9, EE_CHECK_VERSION,(ID_version+(EE_ID9*sizeof(uint32_T))));
#endif
#ifdef EE_ID10_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID10_END - (uint32_T)&__EE_ID10_START, EE_ID10, EE_CHECK_VERSION,(ID_version+(EE_ID10*sizeof(uint32_T))));
#endif
#ifdef EE_ID11_USED
                    res |= EE_ControlRecord((uint32_T)&__EE_ID11_END - (uint32_T)&__EE_ID11_START, EE_ID11, EE_CHECK_VERSION,(ID_version+(EE_ID11*sizeof(uint32_T))));
#endif

                    /* We stop here to force failure after dumping check.
                        TEST 48.1 Test_EEPROM_1_3.xls; 
                        at breakpoint we force EE_errorOnBlock1 = 1
                        Comment this line to test failure on block 0 */        
                    //stopHere();

                    /* If the ID is not valid in any of the two pages, eg ID3,ID5 or ID7 invalidated after a dwn on KWP, this
                    should rightly be lacking in both pages, after a subsequent dumping and controls */
                    if (res == EE_ID_UNAVAILABLE)
                    {
                        res = NO_ERROR;
                    }
#endif
#endif  //EE_RECOVERY_ENABLE

                    if (EEPROM_Page == EE_PAGE_0)
                    {

                        EE_blk1_free_cell = pBackupAddr;
                    }
                    else
                    {
                        EE_blk0_free_cell = pBackupAddr;

                    }
                }
            }
        }



    }
    /* in case of  */
    return res;
}



/***************************************************************************/
//   Function    :   EE_Clean
//
//   Description:    
/*! \brief This method performs the erasing of the Flash EEPROM emulation area 
 */
//
//  Parameters and Returns:
/*! 
\returns EEPROM error code :
NO_ERROR                                operation OK
EE_ERASE_NOT_CORRECT         FLASH erase not correctly executed
 */
//  Notes:        
/*!
\defgroup PrivateFunctions Private Functions 
\sgroup
 */
/**************************************************************************/
static int16_T EE_Clean(void)
{
    int16_T res=NO_ERROR;
    uint16_T i;
#ifdef FLASH_TYPE
#if (FLASH_TYPE != C55)
    pFuncCallback_tag callback = (pFuncCallback_tag)0xFFFFFFFFu;
#else
    uint8_T callback = FLASH_ERASE_NO_CBK;
#endif
#endif

    if(EE_ConfigStatus == 0u)
    {
        res = PERIPHERAL_NOT_CONFIGURED;
    }
    else
    {

        i = 0u;

        do
        {
            DisableAllInterrupts();
#ifdef EE_SERVE_WATCHDOG
            EE_Watchdog_Set_Serve();
#endif
            res = FLASH_Erase(EEPROM_START(),EEPROM_END()-EEPROM_START(), callback);
#ifdef EE_SERVE_WATCHDOG
            EE_Watchdog_Cfg_Serve();
#endif
            EnableAllInterrupts();

            i++;
        }while ((res != NO_ERROR) && (i < EE_ERASE_RETRY));

        if (res != NO_ERROR)
        {
            res = EE_ERASE_NOT_CORRECT;
        }
        else
        {
            res |= FLASH_BlankCheck(EEPROM_START(),EEPROM_END()-EEPROM_START());

            if(res == NO_ERROR)
            {
                for (i=0u; i<EE_ID_TOT_NUMBER; i++)
                {
                    EE_IDtable_bl0[i] = EE_IDTABLE_INIT;
                    EE_IDtable_bl1[i] = EE_IDTABLE_INIT;
                }

                /* pointers to first free cell are being initialized */
                EE_blk0_free_cell = (uint8_T *)EEPROM_BLK0_START();
                EE_blk1_free_cell = (uint8_T*)(EEPROM_BLK1_HALF_BLOCK());
            }
            else
            {
                res = EE_ERASE_NOT_CORRECT;
            }
        }
    }
    return res;
}

/***************************************************************************/
//   Function    :   EE_CheckBlockDescriptor
//
//   Description:    
/*! \brief This method performs the check of the header of IDx
 */
//
//  Parameters and Returns:
/*! 
\param pBlockDesc: pointer to IDx header structure
\returns  EEPROM error code :
NO_ERROR               IDx ok
EE_ERROR                generic error
ERROR_CHECKSUM   wrong header checksum 
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_CheckBlockDescriptor(t_EEBlockDescriptionVar *pBlockDesc)
{
    EE_checksum_t checksum;
    int16_T resVal = NO_ERROR;
    uint32_T * addressBlock = NULL;

    addressBlock = pBlockDesc;

    if((EE_CalculateChecksum(addressBlock,
            (sizeof(t_EEBlockDescriptionVar)-((2u*sizeof(EE_checksum_t))+sizeof(uint32_T))),
            &checksum)) != 0 )
    {
        resVal = EE_ERROR;
    }
    else
    {
        if(pBlockDesc->EEheaderChecksum == checksum)
        {
            resVal = NO_ERROR;
        }
        else
        {
            resVal = ERROR_CHECKSUM;
        }
    }
    return resVal;
}

/***************************************************************************/
//   Function    :   EE_CheckBlockData
//
//   Description:    
/*! \brief This method performs the check of the data of IDx
 */
//
//  Parameters and Returns:
/*! 
\param pBlockDesc: pointer to IDx header structure
\returns  EEPROM error code :
NO_ERROR               IDx ok
EE_ERROR                generic error
ERROR_CHECKSUM   wrong data checksum 
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_CheckBlockData(t_EEBlockDescriptionVar *pBlockDesc)
{
    EE_checksum_t checksum;
    int16_T resVal = NO_ERROR;
    uint32_T * addressBlock = NULL;

    addressBlock = pBlockDesc;


    if((EE_CalculateChecksum(addressBlock+(sizeof(t_EEBlockDescriptionVar)/sizeof(uint32_T)), pBlockDesc->EEsize, &checksum)) != 0 )
    {
        resVal = EE_ERROR;
    }
    else
    {
        if(pBlockDesc->EEdataChecksum == checksum)
        {
            resVal = NO_ERROR;
        }
        else
        {
            resVal = ERROR_CHECKSUM;
        }
    }
    return resVal;
}


/***************************************************************************/
//   Function    :   EE_CalculateChecksum
//
//   Description:    
/*! \brief This method performs the checksum calculation
 */
//
//  Parameters and Returns:
/*! 
\param pData:       pointer to data for which checksum shall be calculated 
\param size:          size in bytes of memory for which checksum shall be calculated 
\param checksum: checksum calculated
\returns  EEPROM error code :
NO_ERROR                       calculation ok
EE_SIZE_NOT_ALIGNED    calculation not performed, wrong size
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_CalculateChecksum(uint32_T* pData, uint32_T size, EE_checksum_t *checksum)
{
    uint32_T *pWord = pData;
    int16_T resVal = NO_ERROR;

    if((size & 0x03u) != 0u )
    {
        resVal = EE_SIZE_NOT_ALIGNED;
    }
    else
    {
        *checksum = 0u;
        size >>= 2;

        while(size != 0u)
        {
            *checksum += *pWord;
            pWord++;
            size--;
        }
    }

    return resVal;
}


/***************************************************************************/
//   Function    :   EE_GetNextBlock
//
//   Description:    
/*! \brief This method searches  next block 
 */
//
//  Parameters and Returns:
/*! 
\param pBlockDesc: pointer to array of block descriptors
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_GetNextBlock (t_EEBlockDescriptionVar ** pBlockDesc)
{
    uint32_T * addressBlock = NULL;

    addressBlock = *pBlockDesc;
    addressBlock += ((sizeof(t_EEBlockDescriptionVar) + (*pBlockDesc)->EEsize))/sizeof(uint32_T);

    *pBlockDesc = addressBlock;

    return NO_ERROR;
}


/***************************************************************************/
//   Function    :   EE_DumpLastBlocks
//
//   Description:    
/*! \brief This method dumps for the last available blocks from source to dest 
 */
//
//  Parameters and Returns:
/*! 
\param destAddr:         destination adress 
\param IDtable_dst:     destination ID pointer  table
\param IDtable_src:     source ID pointer  table
\param blockingStatus: flash blocking status during erase/program (FLASH_BLOCKING or FLASH_NOT_BLOCKING : interrupts disabled or not disabled)
\returns  EEPROM error code :
NO_ERROR           dump ok
EE_ERROR            error occurred, dump not performed
 */
//  Notes:        
/*!
Write here a more detailed description of this private function 
 */
/**************************************************************************/
static int16_T EE_DumpLastBlocks(uint8_T **destAddr, uint32_T IDtable_dst[], uint32_T IDtable_src[], uint8_T blockingStatus)
{
    uint8_T i = 0u;
    int16_T res = NO_ERROR;
    t_EEBlockDescriptionVar *sourceAddr;

    while(i<EE_ID_TOT_NUMBER)
    {
        if ((IDtable_src[i] & EE_ERROR_MASK) == 0u)
        {
            sourceAddr = (t_EEBlockDescriptionVar *)(IDtable_src[i]);

            if(EE_CheckBlockDescriptor(sourceAddr)==NO_ERROR)
            {
                if((sourceAddr->EElast)==(uint32_T)-1)
                {
                    if (blockingStatus == FLASH_BLOCKING)
                    {
                        DisableAllInterrupts();
                    }
                    res |= FLASH_Program( (uint32_T)*destAddr,sizeof(t_EEBlockDescriptionVar) + (sourceAddr->EEsize) ,(uint32_T)sourceAddr);
                    if (blockingStatus == FLASH_BLOCKING)
                    {
                        EnableAllInterrupts();
                        DisableAllInterrupts();
                    }
                    res |= FLASH_ProgramVerify( (uint32_T)*destAddr,sizeof(t_EEBlockDescriptionVar) + (sourceAddr->EEsize) ,(uint32_T)sourceAddr);
                    if (blockingStatus == FLASH_BLOCKING)
                    {
                        EnableAllInterrupts();
                    }
                    IDtable_dst[sourceAddr->EEid] = (uint32_T)(*destAddr);
                    *destAddr += sizeof(t_EEBlockDescriptionVar) + (sourceAddr->EEsize);
                }
            }
        }
        i++;
    }
    if (res != NO_ERROR)
    {
        res = EE_ERROR;
    }
    return res;
}


/***************************************************************************/
//   Function    :   EE_CheckVersion
//
//   Description:    
/*! \brief This method performs the check of  the version
 */
//
//  Parameters and Returns:
/*! 
\param pBlockDesc:    pointer to IDx for which version has to be checked
\param checkVersion: enable/disable version check (EE_CHECK_VERSION, EE_DO_NOT_CHECK_VERSION)
\returns  EEPROM error code :
NO_ERROR                                     dump ok
EE_CHECK_VERSION_FAULT            wrong version
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T  EE_CheckVersion (t_EEBlockDescriptionVar *pBlockDesc,uint16_T checkVersion,const char_T* ID_p1_vers)
{
    uint8_T i=0u;
    int16_T resVal = NO_ERROR;

    if (checkVersion != EE_DO_NOT_CHECK_VERSION)
    {
        for (i=0u; (i<sizeof(uint32_T)) && (resVal == NO_ERROR);i++)
        {
            if( ID_p1_vers[i] != (char_T)(pBlockDesc->EEcheckVersion[i]))
            {
                resVal = EE_CHECK_VERSION_FAULT;
            }
        }
    }
    return resVal;
}



/***************************************************************************/
//   Function    :   EE_BlkSeeking
//
//   Description:    
/*! \brief This method checks all the ID blocks currently contained in the Flash memory range addressed
 */
//
//  Parameters and Returns:
/*! 
\param pData: address to start from 
\param pEndEEBlock: end address 
\param EE_IDtable: ID pointer table to be updated
\returns EEPROM error code :
EE_SCANNING_COMPLETED             No errors, scanning ok, ID pointer tables correctly updated
EE_ERROR                                      Error during memory scan 
ERROR_CHECKSUM                         checksum error during generic ID scan
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_BlkSeeking(uint8_T** pData,
        uint8_T* pEndEEBlock,
        uint32_T  *EE_IDtable)
{
    t_EEBlockDescriptionVar  *pDataTmp;
    int16_T res=NO_ERROR;
    uint32_T EEidTmp;

    pDataTmp = *pData;

    do
    {
        if (((t_EEBlockDescriptionVar*)(pDataTmp))->EEsynchWord != EE_SYNCH_WORD)
        {
            if(FLASH_BlankCheck((uint32_T)pDataTmp, (uint32_T)pEndEEBlock - (uint32_T)pDataTmp) == NO_ERROR)
            {
                res = EE_SCANNING_COMPLETED;
            }
            else
            {
                res = EE_ERROR;
            }
        }
        else
        {
            res |= EE_CheckBlockDescriptor((t_EEBlockDescriptionVar*)pDataTmp);  // Header checksum error

            if(res == NO_ERROR)
            { 
                res = EE_CheckBlockData((t_EEBlockDescriptionVar*)pDataTmp);        // Data checksum error
            }

            if(res != NO_ERROR)
            {   
                EEidTmp = ((t_EEBlockDescriptionVar*)(pDataTmp))->EEid ;
                /* check plalusibilty of ID , in order to avoid a memory  access error */
                if (EEidTmp < EE_ID_TOT_NUMBER)
                {
                    /* ERROR_CHECKSUM case: I save the error in the place of the ID I presume correct */
                    EE_IDtable[((t_EEBlockDescriptionVar*)(pDataTmp))->EEid] = (uint32_T)ERROR_CHECKSUM;
                    res = ERROR_CHECKSUM;  /*  MC Modified to comply MISRA Rule 14.7  */
                }
                else
                {
                    res = EE_ERROR;
                }
            }
            else
            {
                EEidTmp = ((t_EEBlockDescriptionVar*)(pDataTmp))->EEid ;
                /* check plalusibilty of ID , in order to avoid a memory  access error */
                if (EEidTmp < EE_ID_TOT_NUMBER)
                {
                    /* if no header CRC error, control the header value */
                    if(((t_EEBlockDescriptionVar*)(pDataTmp))->EElast == (uint32_T)-1)
                    {
                        EE_IDtable[((t_EEBlockDescriptionVar*)(pDataTmp))->EEid] = (uint32_T)pDataTmp;
                    }
                    else
                    {
                        /* Increments the counter of invalidate ID in the EEPROM block*/
                        if (pDataTmp > (uint8_T*)EEPROM_BLK0_END())
                        {
                            EE_InvalidateID_bl1[((t_EEBlockDescriptionVar*)(pDataTmp))->EEid] +=1U;
                        }
                        else
                        {
                            EE_InvalidateID_bl0[((t_EEBlockDescriptionVar*)(pDataTmp))->EEid] +=1U;
                        }
                        
                    }
                }
                else
                {
                }
                
            }


            EE_GetNextBlock((t_EEBlockDescriptionVar**)&pDataTmp);
            *pData = pDataTmp;
        }

        /*  MISRA Rule 14.6  */
        if ((res == EE_SCANNING_COMPLETED )||(res == ERROR_CHECKSUM)||(res == EE_ERROR))
        {
            break;
        }
        else
        {
        }
    }
    while(pDataTmp<= (pEndEEBlock-(sizeof(t_EEBlockDescriptionVar))));

    return res;
}


/***************************************************************************/
//   Function    :   EE_BlkSeeking_Id
//
//   Description:    
/*! \brief This method sets the pointer to the requested invalidated instance of ID
 */
//
//  Parameters and Returns:
/*! 
\param pData: address to start from 
\param pEndEEBlock: end address 
\param ID: ID EE to be detected
\returns EEPROM error code :
EE_SCANNING_COMPLETED             No errors, scanning ok
EE_ERROR                                       Error during memory scan 
EE_ID_INV_DETECTED                     EE ID instance detected
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_BlkSeeking_ID(uint8_T** pData,
        uint8_T* pEndEEBlock,
        uint8_T ID,
        uint16_T ID_inv_nmb,
        uint32_T  *pId_inv)
{
    t_EEBlockDescriptionVar  *pDataTmp;
    int16_T res=NO_ERROR;
    uint16_T cnt_ID_inv = 0U ;
    
    pDataTmp = *pData;

    do
    {
        if (((t_EEBlockDescriptionVar*)(pDataTmp))->EEsynchWord != EE_SYNCH_WORD)
        {
            if(FLASH_BlankCheck((uint32_T)pDataTmp, (uint32_T)pEndEEBlock - (uint32_T)pDataTmp) == NO_ERROR)
            {
                res = EE_SCANNING_COMPLETED;
            }
            else
            {
                res = EE_ERROR;
            }
        }
        else
        {
            if (((((t_EEBlockDescriptionVar*)(pDataTmp))->EEid) == ID)&& (((t_EEBlockDescriptionVar*)(pDataTmp))->EElast != (uint32_T)-1) )
            {
                if (cnt_ID_inv == ID_inv_nmb)
                {
                    *pId_inv = (uint32_T)pDataTmp ;

                    res = EE_ID_INV_DETECTED;
                }
               
                cnt_ID_inv++;
            }

            EE_GetNextBlock((t_EEBlockDescriptionVar**)&pDataTmp);
            *pData = pDataTmp;
        }

        /*  MISRA Rule 14.6  */
        if ((res == EE_SCANNING_COMPLETED )||(res == EE_ID_INV_DETECTED)||(res == EE_ERROR))
        {
            break;
        }
        else
        {
        }
    }
    while(pDataTmp<= (pEndEEBlock-(sizeof(t_EEBlockDescriptionVar))));

    return res;
}




/***************************************************************************/
//   Function    :   EE_FillBlockWithRamData
//
//   Description:    This method reprogram ALL IDs with its Ram value
/*! \brief 
 */
//
//  Parameters and Returns:
/*! 
\param PageValid: EEPROM page ( EE_PAGE_0 or EE_PAGE_1)
\returns EEPROM error code :
NO_ERROR   operation correctly performed
EE_ERROR    error occurred, operation not performed
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_FillBlockWithRamData(uint8_T PageValid, const char_T* ID_vers )
{
    uint32_T  * dataRecord;
    uint32_T dataRecordSize;
    int16_T resVal = NO_ERROR;
    uint8_T i;
    uint8_T * freeCell;
    uint8_T idIndex;
    t_EEBlockDescriptionVar currentBlockDescriptor;

#ifdef EE_ID0_USED
    EE_RamIdSizeArray[0] =((uint32_T)(&__EE_ID0_END)-(uint32_T)(&__EE_ID0_START)); /*Id_0*/
#endif
#ifdef EE_ID1_USED
    EE_RamIdSizeArray[1] =((uint32_T)(&__EE_ID1_END)-(uint32_T)(&__EE_ID1_START)); /*Id_1*/
#endif
#ifdef EE_ID2_USED
    EE_RamIdSizeArray[2] =((uint32_T)(&__EE_ID2_END)-(uint32_T)(&__EE_ID2_START)); /*Id_2*/
#endif
#ifdef EE_ID3_USED
    EE_RamIdSizeArray[3] =((uint32_T)(&__EE_ID3_END)-(uint32_T)(&__EE_ID3_START)); /*Id_3*/
#endif
#ifdef EE_ID4_USED
    EE_RamIdSizeArray[4] =((uint32_T)(&__EE_ID4_END)-(uint32_T)(&__EE_ID4_START)); /*Id_4*/
#endif
#ifdef EE_ID5_USED
    EE_RamIdSizeArray[5] =((uint32_T)(&__EE_ID5_END)-(uint32_T)(&__EE_ID5_START)); /*Id_5*/
#endif
#ifdef EE_ID6_USED
    EE_RamIdSizeArray[6] =((uint32_T)(&__EE_ID6_END)-(uint32_T)(&__EE_ID6_START)); /*Id_6*/
#endif
#ifdef EE_ID7_USED
    EE_RamIdSizeArray[7] =((uint32_T)(&__EE_ID7_END)-(uint32_T)(&__EE_ID7_START)); /*Id_7*/
#endif
#ifdef EE_ID8_USED
    EE_RamIdSizeArray[8] =((uint32_T)(&__EE_ID8_END)-(uint32_T)(&__EE_ID8_START)); /*Id_8*/
#endif
#ifdef EE_ID9_USED
    EE_RamIdSizeArray[9] =((uint32_T)(&__EE_ID9_END)-(uint32_T)(&__EE_ID9_START)); /*Id_9*/
#endif    
#ifdef EE_ID10_USED
    EE_RamIdSizeArray[10] =((uint32_T)(&__EE_ID10_END)-(uint32_T)(&__EE_ID10_START)); /*Id_10*/
#endif   
#ifdef EE_ID11_USED
    EE_RamIdSizeArray[11] =((uint32_T)(&__EE_ID11_END)-(uint32_T)(&__EE_ID11_START)); /*Id_11*/
#endif  

    if(PageValid == EE_PAGE_0)
    {
        EE_blk0_free_cell = (uint8_T *) EEPROM_BLK0_START();
        freeCell = EE_blk0_free_cell;
    }
    else if(PageValid == EE_PAGE_1)
    {
        EE_blk1_free_cell = (uint8_T *) EEPROM_BLK1_START();
        freeCell = EE_blk1_free_cell;
    }
    else
    {
        //return EE_ERROR;   MC Modified to comply MISRA Rule 14.7 
        resVal = EE_ERROR; 
    }

    if(resVal != EE_ERROR)
    {
        for(idIndex=0u; idIndex < EE_ID_TOT_NUMBER; idIndex++)
        {
            if(EE_RamIdSizeArray[idIndex]!=0xFFFFu)
            {
                /*writing actual RAM ID */
                dataRecord = (uint32_T*)EE_RamIdAddressArray[idIndex];
                dataRecordSize = EE_RamIdSizeArray[idIndex];

                /* BlockDescriptor Settings */
                currentBlockDescriptor.EEsynchWord = EE_SYNCH_WORD;
                currentBlockDescriptor.EElast=(uint32_T)-1;  
                for (i=0u; i<EE_PADDING_WORDS;i++)
                {
                    currentBlockDescriptor.padding [i] = 0u;
                }
                for (i=0u; i<sizeof(uint32_T);i++)
                {
                    currentBlockDescriptor.EEcheckVersion[i]= ID_vers[i];
                }
                currentBlockDescriptor.EEid = idIndex;    
                currentBlockDescriptor.EEsize = dataRecordSize;
                resVal |= EE_CalculateChecksum(&currentBlockDescriptor,
                        (sizeof(t_EEBlockDescriptionVar)-(2u*sizeof(EE_checksum_t))-sizeof(uint32_T)),
                        &(currentBlockDescriptor.EEheaderChecksum));
                resVal|= EE_CalculateChecksum(dataRecord,dataRecordSize,&(currentBlockDescriptor.EEdataChecksum));

                /* EE programming */
                DisableAllInterrupts();
                resVal |= FLASH_Program( (uint32_T)freeCell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);
                EnableAllInterrupts();
                DisableAllInterrupts();
                resVal |= FLASH_ProgramVerify( (uint32_T)freeCell,sizeof(t_EEBlockDescriptionVar),(uint32_T)&currentBlockDescriptor);
                EnableAllInterrupts();
                DisableAllInterrupts();
                resVal |= FLASH_Program(((uint32_T)freeCell +sizeof(t_EEBlockDescriptionVar)),dataRecordSize,(uint32_T)dataRecord);
                EnableAllInterrupts();
                DisableAllInterrupts();
                resVal |= FLASH_ProgramVerify(((uint32_T)freeCell +sizeof(t_EEBlockDescriptionVar)),dataRecordSize,(uint32_T)dataRecord);
                EnableAllInterrupts();


                if(PageValid == EE_PAGE_0)
                {
                    EE_IDtable_bl0[idIndex] = (uint32_T)freeCell;
                    freeCell = freeCell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;
                    EE_blk0_free_cell = freeCell;
                }
                else /* EE_PAGE_1 */
                {
                    EE_IDtable_bl1[idIndex] = (uint32_T)freeCell;
                    freeCell = freeCell + sizeof(t_EEBlockDescriptionVar) + dataRecordSize;
                    EE_blk1_free_cell = freeCell;
                }
            }

        }
    }
    else
    {

    }

    return resVal;
}


/***************************************************************************/
//   Function    :   EE_loadID
//
//   Description:    
/*! \brief This method loads and checks IDx contents during reading operation
 */
//
//  Parameters and Returns:
/*! 
\param dataRecord:  Pointer to RAM data
\param dataRecordSize:  Data  RAM size
\param checkVersion:  Enabling check version
\param pBlockDesc: pointer to IDx for which version has to be checked
\returns EEPROM error code :
NO_ERROR                                   load ok
EE_CHECK_VERSION_FAULT          wrong version detected
ERROR_CHECKSUM                       wrong checksum detected
EE_WRONG_DATA_RECORD_SIZE  wrong size 
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_loadID(uint32_T *dataRecord, uint16_T dataRecordSize, uint16_T checkVersion, t_EEBlockDescriptionVar *pBlockDesc, const char_T* ID_vers)
{
    int16_T  res = NO_ERROR;
    uint32_T * addressBlock = NULL;

    addressBlock = pBlockDesc;    

    if(EE_CheckVersion(pBlockDesc, checkVersion, ID_vers)!= NO_ERROR)
    {
        EE_BlkInvalidateData(pBlockDesc);
        res = EE_CHECK_VERSION_FAULT;
    }
    else if(EE_CheckBlockDescriptor(pBlockDesc) != NO_ERROR)
    {
        res = ERROR_CHECKSUM;
    }
    else
    {
        if((EE_CheckBlockData( pBlockDesc)==NO_ERROR))
        {
            if(pBlockDesc->EEsize == dataRecordSize)
            {
                addressBlock += sizeof(t_EEBlockDescriptionVar)/sizeof(uint32_T);  
                memcpy((void*)dataRecord,(const void *)addressBlock, pBlockDesc->EEsize);
            }
            else
            {
                EE_BlkInvalidateData(pBlockDesc);
                res = EE_WRONG_DATA_RECORD_SIZE;
            }
        }
        else
        {
            res = ERROR_CHECKSUM;
        }
    }

    return res;

}



/***************************************************************************/
//   Function    :   EE_BlkErase
//
//   Description:    
/*! \brief This function erases an EEPROM block
 */
//
//  Parameters and Returns:
/*! 
\param _address:  address where perform flash erase 
\param _size:        size in bytes of memory to erase
\param _callback:  flash erase callback
\returns EEPROM error code :
NO_ERROR no error, erase correctly performed
EE_ERROR  error during erase phase
 */
//  Notes:        
/*!
Flash erase is always performed for entire flash blocks
 */
/**************************************************************************/
static int16_T EE_BlkErase(uint32_T _address, 
        uint32_T _size,
#if (FLASH_TYPE != C55)
        void(*_callback)(void))
#else
        uint8_T _callback)
#endif
{
    int16_T errorCode = NO_ERROR;

    DisableAllInterrupts();
#ifdef EE_SERVE_WATCHDOG
    EE_Watchdog_Set_Serve();
#endif
    errorCode = FLASH_Erase(_address,_size, _callback);
#ifdef EE_SERVE_WATCHDOG
    EE_Watchdog_Cfg_Serve();
#endif

    EnableAllInterrupts();

    if(errorCode != NO_ERROR)
    {
        errorCode = EE_ERROR; 
    }
    else
    {
        errorCode = NO_ERROR;   
    }

    return  errorCode;
}


/***************************************************************************/
//   Function    :   EE_BlkProgramAndVerify
//
//   Description:    
/*! \brief This functions performes Flash program and verifies if it has been correclty performed
 */
//
//  Parameters and Returns:
/*! 
\param _dest: destination address where perform flash program 
\param _size: size in bytes of data to write 
\param _source: source address of data to  write
\returns EEPROM error code :
NO_ERROR no error, program correctly performed
EE_ERROR  error during program phase
 */
//  Notes:        
/*!
Write here a more detailed description of this private function 
 */
/**************************************************************************/
static int16_T EE_BlkProgramAndVerify(uint32_T _dest,
        uint32_T _size,
        uint32_T _source)
{
    int16_T errorCode = NO_ERROR;

    DisableAllInterrupts();
    errorCode |= FLASH_Program(_dest,_size,_source);
    EnableAllInterrupts();

    DisableAllInterrupts();
    errorCode |= FLASH_ProgramVerify(_dest,_size,_source);
    EnableAllInterrupts();

    if(errorCode != NO_ERROR)
    {
        errorCode = EE_ERROR; 
    }
    else
    {
        errorCode = NO_ERROR;   
    }

    return errorCode;

}



/***************************************************************************/
//   Function    :   EE_BlkLoadData
//
//   Description:    
/*! \brief This method perepares IDx contents during update operation
 */
//
//  Parameters and Returns:
/*! 
\param ID_p4:               IDx to write 
\param dataRecord:       data to write
\param dataRecordSize: size of data to write 
\param pBlockDesc:       header of IDx to write
\returns EEPROM error code :
same as EE_CalculateChecksum()
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_BlkLoadData(uint16_T ID_p4,
        uint32_T  * dataRecord,
        uint32_T dataRecordSize,
        t_EEBlockDescriptionVar *pBlockDesc,
        uint32_T* padding_p4 ,
        const char_T* ID_vers)
{
    uint8_T i;
    int16_T errorCode = NO_ERROR;

    pBlockDesc->EEsynchWord=EE_SYNCH_WORD;
    pBlockDesc->EEid=ID_p4;
    pBlockDesc->EElast=(uint32_T)-1;
    pBlockDesc->EEsize=dataRecordSize;

    for (i=0u; i<EE_PADDING_WORDS;i++)
    {
        pBlockDesc->padding[i] = padding_p4[i];
    }

    for (i=0u; i<sizeof(uint32_T);i++)
    {
        pBlockDesc->EEcheckVersion[i]=ID_vers[i];
    }

    errorCode |= EE_CalculateChecksum(pBlockDesc, 
            (sizeof(t_EEBlockDescriptionVar)- (2u*sizeof(EE_checksum_t))-sizeof(uint32_T)),
            &(pBlockDesc->EEheaderChecksum));

    errorCode |= EE_CalculateChecksum(dataRecord,
            dataRecordSize,
            &(pBlockDesc->EEdataChecksum));

    return errorCode;
}


/***************************************************************************/
//   Function    :   EE_BlkInvalidateData
//
//   Description:    
/*! \brief This method invalidates a block descriptor before update operation
 */
//
//  Parameters and Returns:
/*! 
\param pBlockDesc: pointer to IDx block descriptor 
\returns EEPROM error code :
NO_ERROR  invalidation correctly performed
EE_ERROR   error during Flash program phase
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static int16_T EE_BlkInvalidateData(t_EEBlockDescriptionVar * pBlockDesc)
{
    int16_T errorCode = NO_ERROR;
    t_EEBlockDescriptionVar  tempBlockDescriptor;


    tempBlockDescriptor = *(t_EEBlockDescriptionVar*)pBlockDesc;
    tempBlockDescriptor.EElast = 0u;
    DisableAllInterrupts();
    errorCode |= FLASH_Program((uint32_T)pBlockDesc, sizeof(t_EEBlockDescriptionVar),(uint32_T)&tempBlockDescriptor);
    EnableAllInterrupts();

    if(errorCode != NO_ERROR)
    {
        errorCode = EE_ERROR; 
    }
    else
    {
        errorCode = NO_ERROR;   
    }

    return errorCode;
}




/***************************************************************************/
//   Function    :   EE_checkBlk0
//
//   Description:    
/*! \brief This functions checks if in block 0 there is enough space to write new IDx during update
 */
//
//  Parameters and Returns:
/*! 
\param ID_p : ID to update 
\param dataRecordSize : size of data to write
\returns t_eraseConditionBlk0 : if equal 1 block 0 does not have enough space and needs to be erased befor update
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static uint8_T EE_checkBlk0(uint16_T ID_p, uint32_T dataRecordSize)
{
    uint8_T               t_eraseConditionBlk0 = 0u;
    t_EEBlockDescriptionVar               *pBlock0Desc;

    /* is it enough the space we have on blk0? if not it must be erased */
    if(EE_blk0_free_cell> ((uint8_T*) EEPROM_BLK0_END()-(sizeof(t_EEBlockDescriptionVar)+dataRecordSize)))
    {
        t_eraseConditionBlk0 = 1u;
    }
    /*check if last record written has any error*/

    if ((EE_IDtable_bl0[ID_p] & EE_ERROR_MASK) == 0u)
    {
        pBlock0Desc = (t_EEBlockDescriptionVar *)EE_IDtable_bl0[ID_p];
        if(EE_CheckBlockDescriptor((t_EEBlockDescriptionVar*)pBlock0Desc) != NO_ERROR)
        {
            t_eraseConditionBlk0 = 1u;
        }

        if((((t_EEBlockDescriptionVar*)(pBlock0Desc))->EElast == (uint32_T)-1) && (t_eraseConditionBlk0 == 0u))// || (t_eraseConditionBlk1 == 1)))
        { 
            /* invalidate payload in EEPROM block 0 */
            EE_BlkInvalidateData(pBlock0Desc);
            /*update table after invalidating, it will be update with new value after actual update*/
            EE_IDtable_bl0[ID_p] = EE_IDTABLE_INIT; 
        }

    }

    return t_eraseConditionBlk0;

}



/***************************************************************************/
//   Function    :   EE_checkBlk1
//
//   Description:    
/*! \brief This functions checks if in block 1 there is enough space to write new IDx during update
 */
//
//  Parameters and Returns:
/*! 
\param ID_p : ID to update 
\param dataRecordSize : size of data to write
\returns t_eraseConditionBlk1 : if equal 1 block 1 does not have enough space and needs to be erased befor update
 */
//  Notes:        
/*!
 */
/**************************************************************************/
static uint8_T EE_checkBlk1(uint16_T ID_p, uint32_T dataRecordSize)
{
    uint8_T               t_eraseConditionBlk1 = 0u;
    t_EEBlockDescriptionVar               *pBlock1Desc;

    /* is it enough the space we have on blk1? if not it must be erased */
    if(EE_blk1_free_cell > ((uint8_T*) EEPROM_BLK1_END()-(sizeof(t_EEBlockDescriptionVar)+dataRecordSize)))
    {
        t_eraseConditionBlk1 = 1u;
    }
    /*check if last record written has any error*/
    if ((EE_IDtable_bl1[ID_p] & EE_ERROR_MASK) == 0u)
    {
        pBlock1Desc = (t_EEBlockDescriptionVar *)EE_IDtable_bl1[ID_p];
        if(EE_CheckBlockDescriptor((t_EEBlockDescriptionVar*)pBlock1Desc) != NO_ERROR)
        {
            t_eraseConditionBlk1 = 1u;
        }

        /* in this case we can skip this operation if eraseConditionBlk1 = 0 */
        if((((t_EEBlockDescriptionVar*)(pBlock1Desc))->EElast == (uint32_T)-1 ) && (t_eraseConditionBlk1 == 0u))
        { 
            /* invalidate payload in EEPROM block 1 */
            EE_BlkInvalidateData(pBlock1Desc);
            /*update table after invalidating, it will be update with new value after actual update*/
            EE_IDtable_bl1[ID_p] = EE_IDTABLE_INIT; 
        }
    }

    return t_eraseConditionBlk1;


}




/***************************************************************************/
//   Function    :   EE_Watchdog_Set_Serve
//
//   Description:    
/*! \brief Set (max) timeout and serve Watchdog, useful during EEPROM phases that needs long time (erase flash memory)
 */
//
//  Parameters and Returns:
/*! 
 */
//  Notes:        
/*!
 */
/**************************************************************************/
#ifdef EE_SERVE_WATCHDOG
static void EE_Watchdog_Set_Serve(void)
{
#ifdef _BUILD_WDT_
    WDT_SetTimeout(TCR_WP_1600ms, TCR_WPEXT_1600ms);
    WDT_PeriodicServiceRoutine()
#else
#ifdef _BUILD_SWT_
    SWT_SetTimeout(SWT_PERIOD_1600ms);
    SWT_PeriodicServiceRoutine()
#else
#endif
#endif
}
#endif

/***************************************************************************/
//   Function    :   EE_Watchdog_Cfg_Serve
//
//   Description:    
/*! \brief Configure as default and serve Watchdog 
 */
//
//  Parameters and Returns:
/*! 
 */
//  Notes:        
/*!
 */
/**************************************************************************/
#ifdef EE_SERVE_WATCHDOG
static void EE_Watchdog_Cfg_Serve(void)
{
#ifdef _BUILD_WDT_
    WDT_Config();
    WDT_PeriodicServiceRoutine()
#else
#ifdef _BUILD_SWT_
    SWT_SetTimeout(SWT_TIMEOUT);
    SWT_PeriodicServiceRoutine()
#else
#endif
#endif
}
#endif


/**************************************************************************/
#ifdef EE_RECOVERY_SETWDTFLASHMODE
static void EE_Watchdog_Flash_Mode(void)
{
#ifdef _BUILD_MC33904_U3_
    if (GetSPIResFlag(SPI_CH_B_EN) == SPI_BUSY)
    {
        ResetSPIResFlag(SPI_CH_B_EN);
    }
    WaitExtWDTSyncISRTurnOff();
#else

#endif
}
#endif

/**************************************************************************/
#ifdef EE_RECOVERY_RESETREQWDT
static void EE_Watchdog_ResetReq(void)
{
#ifdef _BUILD_MC33904_U3_
    Spi_MC33904_U3_Reset();
#else

#endif
}
#endif

/*!\egroup*/
#pragma ghs endnowarning /* warning #32-D */
#pragma ghs endnowarning /* warning #513-D */ 
#pragma ghs endnowarning /* warning #42-D */
#pragma ghs endnowarning /* warning #167-D */

#endif //_BUILD_EEPROM_

/****************************************************************************
 ****************************************************************************/

