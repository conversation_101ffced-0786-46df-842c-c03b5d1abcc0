/**
 ******************************************************************************
 **  Filename:      CombCtrl_types.h
 **  Date:          16-Oct-2018
 **
 **  Model Version: 1.1908
 ******************************************************************************
 **/

#ifndef RTW_HEADER_CombCtrl_types_h_
#define RTW_HEADER_CombCtrl_types_h_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#ifndef SS_INT64
#define SS_INT64                       46
#endif

#ifndef SS_UINT64
#define SS_UINT64                      47
#endif
#endif                                 /* RTW_HEADER_CombCtrl_types_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.3 (R2017b)24-Jul-2017                                             *
 * Simulink 9.0 (R2017b)24-Jul-2017                                           *
 * Simulink Coder 8.13 (R2017b)24-Jul-2017                                    *
 * Embedded Coder 6.13 (R2017b)24-Jul-2017                                    *
 * Stateflow 9.0 (R2017b)24-Jul-2017                                          *
 * Fixed-Point Designer 6.0 (R2017b)24-Jul-2017                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
