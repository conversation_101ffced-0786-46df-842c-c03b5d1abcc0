/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#include "rtwtypes.h"


#pragma ghs section data=".ee_id1_data"

#ifdef _TEST_EEPROM_
uint32_T Prova_ID1[8] = {  
                                 0x11111111u, 0x11111111u, 0x11111111u, 0x11111111u,
                                 0x11111111u, 0x11111111u, 0x11111111u, 0x11111111u 
                              };
#endif

// Declare here all the variables to be stored in EEPROM with ID1
///Dummy_EE ID1
uint32_T EEDummyID1_00 = 0u;
uint32_T EEDummyID1_01 = 0u;
uint32_T EEDummyID1_02 = 0u;
uint32_T EEDummyID1_03 = 0u;

#ifdef _BUILD_DIAGCANMGM_ 
#pragma ghs startnomisra
#include "../DIAGCANMGM/src/DiagCanMgm_Ferrari_eepID1.c"
#pragma ghs endnomisra
#endif 

/* EOF EEPROM */

