/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      RonDetectEst.h
 **  Date:          20-May-2022
 **
 **  Model Version: 1.1107
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_RonDetectEst_h_
#define RTW_HEADER_RonDetectEst_h_
#ifndef RonDetectEst_COMMON_INCLUDES_
# define RonDetectEst_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* RonDetectEst_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: ELD_EXPORT_DEFINE */
#define MAX_RONLEVEL_EXP               4U                        /* Referenced by:
                                                                  * '<S3>/RonLevel_selector'
                                                                  * '<S8>/BackGround_Mode_Chart'
                                                                  * '<S10>/CNTRONDETECT2'
                                                                  * '<S11>/CNTRONDETECT2'
                                                                  */

/* Maximum allowed level for ron detection. */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void RonDetectEst_initialize(void);

/* Exported entry point function */
extern void RonDetectEst_100ms(void);

/* Exported entry point function */
extern void RonDetectEst_5ms(void);

/* Exported entry point function */
extern void RonDetectEst_BackgroundInit(void);

/* Exported entry point function */
extern void RonDetectEst_EOA(void);

/* Exported entry point function */
extern void RonDetectEst_Fast(void);

/* Exported entry point function */
extern void RonDetectEst_MonitoringInit(void);

/* Exported entry point function */
extern void RonDetectEst_PowerOn(void);

/* Exported entry point function */
extern void RonDetectEst_Recovery(void);

/* Exported entry point function */
extern void RonDetectEst_ResetSecStartRun(void);

/* Exported entry point function */
extern void RonDetectEst_Slow(void);

/* Exported data declaration */

/*Exported calibration memory section */
/*Init of exported calibrations section*/

/* Declaration for custom storage class: ELD_EXPORT_CALIBRATION */
extern CALQUAL CALQUAL_POST uint16_T BKLOADSARON[9];/* Referenced by: '<S12>/BKLOADSARON' */

/* Breakpoints of load for ron SARon calculation */

/*End of exported calibrations section*/


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_EE_INTERFACE */
extern uint16_T CntRonSuspEE;          /* '<S9>/Merge28' */

/* Number of ron level change after detection */
extern uint16_T CntRonSuspRunEE;       /* '<S9>/Merge26' */

/* Number of background test executed after ron level detection */
extern uint8_T IDRonSuspRunEE;         /* '<S9>/Merge27' */

/* VtDRonLevelSuspRun index */
extern uint8_T RonLevelEE;             /* '<S9>/Merge25' */

/* RON level stored in EE */

/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T CntDRon0;               /* '<S9>/Merge2' */

/* Number of run without RonLevel correction */
extern uint8_T CntRonMaxInc;           /* '<S9>/Merge3' */

/* Run completed with RonLevel = MAX and positive correction */
extern uint8_T CntRonMinDec;           /* '<S9>/Merge4' */

/* Run completed with RonLevel = 0 and negative correction */
extern uint8_T CntRonRun;              /* '<S9>/Merge5' */

/* RON test counter */
extern uint8_T CntRonSuspNeg;          /* '<S9>/Merge6' */

/* Number of background run stopped with negative ron changes */
extern uint8_T CntRonSuspPos;          /* '<S9>/Merge7' */

/* Number of background run stopped with positive ron changes */
extern uint8_T CntRonSuspZero;         /* '<S9>/Merge8' */

/* Number of background run with neutral ron changes */
extern uint8_T FlgRonDetect;           /* '<S9>/Merge1' */

/* Flag that declares ron detection */
extern uint8_T FlgRonMaxInc;           /* '<S9>/Merge9' */

/* CntRonMaxInc over threshold */
extern uint8_T FlgRonMinDec;           /* '<S9>/Merge10' */

/* CntRonMinDec over threshold */
extern uint8_T RonLevelUsed;           /* '<S9>/Merge' */

/* RON level used for SARon calculation */
extern int8_T VtDRonLevelSusp[10];     /* '<S9>/Merge11' */

/* RonLevel changing history in background mode */
extern int8_T VtDRonLevelSuspRun[30];  /* '<S9>/Merge12' */

/* RonLevel vector correction in background mode */
extern uint8_T VtRonLevel[20];         /* '<S9>/Merge13' */

/* RonLevel vector for each run */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S17>/Data Type Conversion1' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Conversion1' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Conversion1' : Unused code path elimination
 * Block '<S17>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S17>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S23>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'RonDetectEst'
 * '<S1>'   : 'RonDetectEst/BackgroundInit'
 * '<S2>'   : 'RonDetectEst/EOA'
 * '<S3>'   : 'RonDetectEst/FAST_DETECTION'
 * '<S4>'   : 'RonDetectEst/MonitoringInit'
 * '<S5>'   : 'RonDetectEst/PowerOn'
 * '<S6>'   : 'RonDetectEst/Recovery'
 * '<S7>'   : 'RonDetectEst/ResetSecStartRun'
 * '<S8>'   : 'RonDetectEst/SLOW_DETECTION'
 * '<S9>'   : 'RonDetectEst/Subsystem'
 * '<S10>'  : 'RonDetectEst/T100ms'
 * '<S11>'  : 'RonDetectEst/T5ms'
 * '<S12>'  : 'RonDetectEst/EOA/RonLevelUsed_calc'
 * '<S13>'  : 'RonDetectEst/FAST_DETECTION/CounterUpdate'
 * '<S14>'  : 'RonDetectEst/FAST_DETECTION/DeltaRonLevel'
 * '<S15>'  : 'RonDetectEst/FAST_DETECTION/InputFiltering'
 * '<S16>'  : 'RonDetectEst/FAST_DETECTION/RonLevel_selector'
 * '<S17>'  : 'RonDetectEst/FAST_DETECTION/DeltaRonLevel/PreLookUpIdSearch_S16'
 * '<S18>'  : 'RonDetectEst/FAST_DETECTION/InputFiltering/RescalSignedIntRightShift'
 * '<S19>'  : 'RonDetectEst/FAST_DETECTION/InputFiltering/RescalSignedIntRightShift1'
 * '<S20>'  : 'RonDetectEst/SLOW_DETECTION/BackGround_Mode_Chart'
 * '<S21>'  : 'RonDetectEst/SLOW_DETECTION/DeltaRonLevel'
 * '<S22>'  : 'RonDetectEst/SLOW_DETECTION/BackGround_Mode_Chart/idpos_calc'
 * '<S23>'  : 'RonDetectEst/SLOW_DETECTION/BackGround_Mode_Chart/idpos_calc/PreLookUpIdSearch_U16'
 * '<S24>'  : 'RonDetectEst/SLOW_DETECTION/DeltaRonLevel/PreLookUpIdSearch_S16'
 */

/*-
 * Requirements for '<Root>': RonDetectEst
 *
 * Inherited requirements for '<Root>/BackgroundInit':
 *  1. EISB_FCA6CYL_SW_REQ_2814: Software shall provide a reset functionality named RonDetectEst_Ba... (ECU_SW_Requirements#12228)
 *
 * Inherited requirements for '<Root>/EOA':
 *  1. EISB_FCA6CYL_SW_REQ_2820: At each task EOA, software shall set the signal RonLevelUsed (i.e.... (ECU_SW_Requirements#12234)
 *
 * Inherited requirements for '<Root>/MonitoringInit':
 *  1. EISB_FCA6CYL_SW_REQ_2815: Software shall provide a reset functionality named RonDetectEst_Mo... (ECU_SW_Requirements#12229)
 *
 * Inherited requirements for '<Root>/PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_2813: Software shall initialize, at power-on event any output with the e... (ECU_SW_Requirements#12227)
 *
 * Inherited requirements for '<Root>/Recovery':
 *  1. EISB_FCA6CYL_SW_REQ_2819: Software shall provide a function call named RonDetectEst_Recovery... (ECU_SW_Requirements#12233)
 *
 * Inherited requirements for '<Root>/ResetSecStartRun':
 *  1. EISB_FCA6CYL_SW_REQ_2818: Software shall provide a function call named RonDetectEst_ResetSec... (ECU_SW_Requirements#12232)
 *
 * Inherited requirements for '<Root>/T100ms':
 *  1. EISB_FCA6CYL_SW_REQ_2816: At each task 100ms, software shall set the signal used to store RO... (ECU_SW_Requirements#12230)
 *
 * Inherited requirements for '<Root>/T5ms':
 *  1. EISB_FCA6CYL_SW_REQ_2817: At each task 5ms, software shall set the signal used to store RON ... (ECU_SW_Requirements#12231)

 */
#endif                                 /* RTW_HEADER_RonDetectEst_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/