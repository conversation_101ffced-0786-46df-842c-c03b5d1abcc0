/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      RonDetectFuel.h
 **  Date:          28-May-2025
 **
 **  Model Version: 1.1020
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_RonDetectFuel_h_
#define RTW_HEADER_RonDetectFuel_h_
#ifndef RonDetectFuel_COMMON_INCLUDES_
# define RonDetectFuel_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* RonDetectFuel_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

 
/* Enumerated types definition */
typedef uint8_T enum_StRonRefuelChart;
#define WAITING_REFUEL                 ((enum_StRonRefuelChart)0U) /* Default value */
#define REFUEL_DETECTED                ((enum_StRonRefuelChart)1U)
#define WAIT_REFUEL_SUSP_ENABLED       ((enum_StRonRefuelChart)2U)
#define RON_DET_COMPLETED              ((enum_StRonRefuelChart)3U)
 
/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  uint32_T CntAbsTdcRON_d;             /* '<S4>/T100ms_Chart' */
  uint8_T RonLevelUsed_m;              /* '<S4>/T100ms_Chart' */
  uint8_T RonLevelEE_g;                /* '<S4>/T100ms_Chart' */
  uint8_T FlgRonStoredEE_e;            /* '<S4>/T100ms_Chart' */
  uint8_T RefuelDetectedRON_b;         /* '<S4>/T100ms_Chart' */
  uint8_T FlgResetStRon_l;             /* '<S4>/T100ms_Chart' */
  uint8_T EnSARonOutput_a;             /* '<S4>/T100ms_Chart' */
  uint8_T is_active_c5_RonDetectFuel;  /* '<S4>/T100ms_Chart' */
  uint8_T is_c5_RonDetectFuel;         /* '<S4>/T100ms_Chart' */
} DW_RonDetectFuel_T;

/* External outputs (root outports fed by signals with default storage) */
typedef struct {
  uint32_T CntAbsTdcRON;               /* '<Root>/CntAbsTdcRON' */
} ExtY_RonDetectFuel_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_RonDetectFuel_T RonDetectFuel_DW;

/* External outputs (root outports fed by signals with default storage) */
extern ExtY_RonDetectFuel_T RonDetectFuel_Y;

/* Model entry point functions */
extern void RonDetectFuel_initialize(void);

/* Exported entry point function */
extern void RonDetectFuel_100ms(void);

/* Exported entry point function */
extern void RonDetectFuel_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern int32_T CntAbsTdc;              /* '<Root>/CntAbsTdc' */

/* CntAbsTdc counter RefuelDetectRON get set */
extern uint8_T EcmEisb2RxFlg;          /* '<Root>/EcmEisb2RxFlg' */

/* Internal latched refuel detected flag */
extern uint8_T EnSARonOutput;          /* '<S2>/Merge1' */

/* SARon Output enable flag */
extern uint8_T FlgResetStRon;          /* '<S2>/FlgResetStRon' */

/* Reset State machine Ron */
extern uint8_T RefuelDetected;         /* '<Root>/RefuelDetected' */

/* Internal latched refuel detected flag */
extern uint8_T RefuelDetectedRON;      /* '<S2>/RefuelDetectedRON' */

/* Internal latched refuel detected flag */
extern uint8_T RonLevelFuel;           /* '<Root>/RonLevelFuel' */

/* RON level stored in EE */
extern uint8_T RonLevelUsed;           /* '<S2>/RonLevelUsed' */

/* RON level used for SARon calculation */
extern enum_StRonRefuelChart StRonRefuelChart;/* '<S2>/Merge' */

/* CntAbsTdc counter RefuelDetectRON get set */


/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'RonDetectFuel'
 * '<S1>'   : 'RonDetectFuel/Init_fcn'
 * '<S2>'   : 'RonDetectFuel/Merge'
 * '<S3>'   : 'RonDetectFuel/RonDetectFuel_Scheduler'
 * '<S4>'   : 'RonDetectFuel/T100ms_fcn'
 * '<S5>'   : 'RonDetectFuel/T100ms_fcn/T100ms_Chart'
 */

/*-
 * Requirements for '<Root>': RonDetectFuel
 */
#endif                                 /* RTW_HEADER_RonDetectFuel_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/