/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_icm.h
 * @brief   SPC5xx GTM ICM header file.
 *
 * @addtogroup ICM
 * @{
 */

#ifndef _GTM_ICM_H_
#define _GTM_ICM_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/**
 * @name    ICM definitions
 * @{
 */
#define SPC5_GTM_ICM_ARU                    0UL
#define SPC5_GTM_ICM_BRC                    1UL
#define SPC5_GTM_ICM_AEI                    2UL
#define SPC5_GTM_ICM_MAP                    3UL
#define SPC5_GTM_ICM_CMP                    4UL
#define SPC5_GTM_ICM_SPE                    5UL
#define SPC5_GTM_ICM_PSM0                   6UL
#define SPC5_GTM_ICM_PSM1                   7UL

#define SPC5_GTM_ICM_DPLL                   8UL

#define SPC5_GTM_ICM_TIM0                   9UL
#define SPC5_GTM_ICM_TIM1                   10UL
#define SPC5_GTM_ICM_TIM2                   11UL
#define SPC5_GTM_ICM_TIM3                   12UL
#define SPC5_GTM_ICM_TIM4                   13UL
#define SPC5_GTM_ICM_TIM5                   14UL
#define SPC5_GTM_ICM_TIM6                   15UL
#define SPC5_GTM_ICM_TIM7                   16UL

#define SPC5_GTM_ICM_MCS0                   17UL
#define SPC5_GTM_ICM_MCS1                   18UL
#define SPC5_GTM_ICM_MCS2                   19UL
#define SPC5_GTM_ICM_MCS3                   20UL
#define SPC5_GTM_ICM_MCS4                   21UL
#define SPC5_GTM_ICM_MCS5                   22UL
#define SPC5_GTM_ICM_MCS6                   23UL
#define SPC5_GTM_ICM_MCS7                   24UL

#define SPC5_GTM_ICM_TOM0                   25UL
#define SPC5_GTM_ICM_TOM1                   26UL
#define SPC5_GTM_ICM_TOM2                   27UL
#define SPC5_GTM_ICM_TOM3                   28UL
#define SPC5_GTM_ICM_TOM4                   29UL
#define SPC5_GTM_ICM_TOM5                   30UL

#define SPC5_GTM_ICM_ATOM0                  31UL
#define SPC5_GTM_ICM_ATOM1                  32UL
#define SPC5_GTM_ICM_ATOM2                  33UL
#define SPC5_GTM_ICM_ATOM3                  34UL
#define SPC5_GTM_ICM_ATOM4                  35UL
#define SPC5_GTM_ICM_ATOM5                  36UL
#define SPC5_GTM_ICM_ATOM6                  37UL
#define SPC5_GTM_ICM_ATOM7                  38UL
#define SPC5_GTM_ICM_ATOM8                  39UL
#define SPC5_GTM_ICM_ATOM9                  40UL
#define SPC5_GTM_ICM_ATOM10                 41UL
#define SPC5_GTM_ICM_ATOM11                 42UL


/* TOM defines */
#define SPC5_GTM_ICM_TOM0_CHANNEL_0         0x00000001UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_1         0x00000002UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_2         0x00000004UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_3         0x00000008UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_4         0x00000010UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_5         0x00000020UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_6         0x00000040UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_7         0x00000080UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_8         0x00000100UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_9         0x00000200UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_10        0x00000400UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_11        0x00000800UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_12        0x00001000UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_13        0x00002000UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_14        0x00004000UL
#define SPC5_GTM_ICM_TOM0_CHANNEL_15        0x00008000UL

#define SPC5_GTM_ICM_TOM1_CHANNEL_0         0x00010000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_1         0x00020000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_2         0x00040000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_3         0x00080000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_4         0x00100000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_5         0x00200000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_6         0x00400000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_7         0x00800000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_8         0x01000000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_9         0x02000000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_10        0x04000000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_11        0x08000000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_12        0x10000000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_13        0x20000000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_14        0x40000000UL
#define SPC5_GTM_ICM_TOM1_CHANNEL_15        0x80000000UL

#define SPC5_GTM_ICM_TOM2_CHANNEL_0         0x00000001UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_1         0x00000002UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_2         0x00000004UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_3         0x00000008UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_4         0x00000010UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_5         0x00000020UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_6         0x00000040UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_7         0x00000080UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_8         0x00000100UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_9         0x00000200UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_10        0x00000400UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_11        0x00000800UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_12        0x00001000UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_13        0x00002000UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_14        0x00004000UL
#define SPC5_GTM_ICM_TOM2_CHANNEL_15        0x00008000UL

#define SPC5_GTM_ICM_TOM3_CHANNEL_0         0x00010000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_1         0x00020000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_2         0x00040000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_3         0x00080000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_4         0x00100000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_5         0x00200000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_6         0x00400000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_7         0x00800000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_8         0x01000000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_9         0x02000000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_10        0x04000000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_11        0x08000000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_12        0x10000000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_13        0x20000000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_14        0x40000000UL
#define SPC5_GTM_ICM_TOM3_CHANNEL_15        0x80000000UL

#define SPC5_GTM_ICM_TOM4_CHANNEL_0         0x00000001UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_1         0x00000002UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_2         0x00000004UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_3         0x00000008UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_4         0x00000010UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_5         0x00000020UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_6         0x00000040UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_7         0x00000080UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_8         0x00000100UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_9         0x00000200UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_10        0x00000400UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_11        0x00000800UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_12        0x00001000UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_13        0x00002000UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_14        0x00004000UL
#define SPC5_GTM_ICM_TOM4_CHANNEL_15        0x00008000UL

#define SPC5_GTM_ICM_TOM5_CHANNEL_0         0x00010000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_1         0x00020000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_2         0x00040000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_3         0x00080000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_4         0x00100000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_5         0x00200000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_6         0x00400000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_7         0x00800000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_8         0x01000000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_9         0x02000000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_10        0x04000000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_11        0x08000000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_12        0x10000000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_13        0x20000000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_14        0x40000000UL
#define SPC5_GTM_ICM_TOM5_CHANNEL_15        0x80000000UL

/* ATOM defines */
#define SPC5_GTM_ICM_ATOM0_CHANNEL_0        0x00000001UL
#define SPC5_GTM_ICM_ATOM0_CHANNEL_1        0x00000002UL
#define SPC5_GTM_ICM_ATOM0_CHANNEL_2        0x00000004UL
#define SPC5_GTM_ICM_ATOM0_CHANNEL_3        0x00000008UL
#define SPC5_GTM_ICM_ATOM0_CHANNEL_4        0x00000010UL
#define SPC5_GTM_ICM_ATOM0_CHANNEL_5        0x00000020UL
#define SPC5_GTM_ICM_ATOM0_CHANNEL_6        0x00000040UL
#define SPC5_GTM_ICM_ATOM0_CHANNEL_7        0x00000080UL

#define SPC5_GTM_ICM_ATOM1_CHANNEL_0        0x00000100UL
#define SPC5_GTM_ICM_ATOM1_CHANNEL_1        0x00000200UL
#define SPC5_GTM_ICM_ATOM1_CHANNEL_2        0x00000400UL
#define SPC5_GTM_ICM_ATOM1_CHANNEL_3        0x00000800UL
#define SPC5_GTM_ICM_ATOM1_CHANNEL_4        0x00001000UL
#define SPC5_GTM_ICM_ATOM1_CHANNEL_5        0x00002000UL
#define SPC5_GTM_ICM_ATOM1_CHANNEL_6        0x00004000UL
#define SPC5_GTM_ICM_ATOM1_CHANNEL_7        0x00008000UL

#define SPC5_GTM_ICM_ATOM2_CHANNEL_0        0x00010000UL
#define SPC5_GTM_ICM_ATOM2_CHANNEL_1        0x00020000UL
#define SPC5_GTM_ICM_ATOM2_CHANNEL_2        0x00040000UL
#define SPC5_GTM_ICM_ATOM2_CHANNEL_3        0x00080000UL
#define SPC5_GTM_ICM_ATOM2_CHANNEL_4        0x00100000UL
#define SPC5_GTM_ICM_ATOM2_CHANNEL_5        0x00200000UL
#define SPC5_GTM_ICM_ATOM2_CHANNEL_6        0x00400000UL
#define SPC5_GTM_ICM_ATOM2_CHANNEL_7        0x00800000UL

#define SPC5_GTM_ICM_ATOM3_CHANNEL_0        0x01000000UL
#define SPC5_GTM_ICM_ATOM3_CHANNEL_1        0x02000000UL
#define SPC5_GTM_ICM_ATOM3_CHANNEL_2        0x04000000UL
#define SPC5_GTM_ICM_ATOM3_CHANNEL_3        0x08000000UL
#define SPC5_GTM_ICM_ATOM3_CHANNEL_4        0x10000000UL
#define SPC5_GTM_ICM_ATOM3_CHANNEL_5        0x20000000UL
#define SPC5_GTM_ICM_ATOM3_CHANNEL_6        0x40000000UL
#define SPC5_GTM_ICM_ATOM3_CHANNEL_7        0x80000000UL

#define SPC5_GTM_ICM_ATOM4_CHANNEL_0        0x00000001UL
#define SPC5_GTM_ICM_ATOM4_CHANNEL_1        0x00000002UL
#define SPC5_GTM_ICM_ATOM4_CHANNEL_2        0x00000004UL
#define SPC5_GTM_ICM_ATOM4_CHANNEL_3        0x00000008UL
#define SPC5_GTM_ICM_ATOM4_CHANNEL_4        0x00000010UL
#define SPC5_GTM_ICM_ATOM4_CHANNEL_5        0x00000020UL
#define SPC5_GTM_ICM_ATOM4_CHANNEL_6        0x00000040UL
#define SPC5_GTM_ICM_ATOM4_CHANNEL_7        0x00000080UL

#define SPC5_GTM_ICM_ATOM5_CHANNEL_0        0x00000100UL
#define SPC5_GTM_ICM_ATOM5_CHANNEL_1        0x00000200UL
#define SPC5_GTM_ICM_ATOM5_CHANNEL_2        0x00000400UL
#define SPC5_GTM_ICM_ATOM5_CHANNEL_3        0x00000800UL
#define SPC5_GTM_ICM_ATOM5_CHANNEL_4        0x00001000UL
#define SPC5_GTM_ICM_ATOM5_CHANNEL_5        0x00002000UL
#define SPC5_GTM_ICM_ATOM5_CHANNEL_6        0x00004000UL
#define SPC5_GTM_ICM_ATOM5_CHANNEL_7        0x00008000UL

#define SPC5_GTM_ICM_ATOM6_CHANNEL_0        0x00010000UL
#define SPC5_GTM_ICM_ATOM6_CHANNEL_1        0x00020000UL
#define SPC5_GTM_ICM_ATOM6_CHANNEL_2        0x00040000UL
#define SPC5_GTM_ICM_ATOM6_CHANNEL_3        0x00080000UL
#define SPC5_GTM_ICM_ATOM6_CHANNEL_4        0x00100000UL
#define SPC5_GTM_ICM_ATOM6_CHANNEL_5        0x00200000UL
#define SPC5_GTM_ICM_ATOM6_CHANNEL_6        0x00400000UL
#define SPC5_GTM_ICM_ATOM6_CHANNEL_7        0x00800000UL

#define SPC5_GTM_ICM_ATOM7_CHANNEL_0        0x01000000UL
#define SPC5_GTM_ICM_ATOM7_CHANNEL_1        0x02000000UL
#define SPC5_GTM_ICM_ATOM7_CHANNEL_2        0x04000000UL
#define SPC5_GTM_ICM_ATOM7_CHANNEL_3        0x08000000UL
#define SPC5_GTM_ICM_ATOM7_CHANNEL_4        0x10000000UL
#define SPC5_GTM_ICM_ATOM7_CHANNEL_5        0x20000000UL
#define SPC5_GTM_ICM_ATOM7_CHANNEL_6        0x40000000UL
#define SPC5_GTM_ICM_ATOM7_CHANNEL_7        0x80000000UL

#define SPC5_GTM_ICM_ATOM8_CHANNEL_0        0x00000001UL
#define SPC5_GTM_ICM_ATOM8_CHANNEL_1        0x00000002UL
#define SPC5_GTM_ICM_ATOM8_CHANNEL_2        0x00000004UL
#define SPC5_GTM_ICM_ATOM8_CHANNEL_3        0x00000008UL
#define SPC5_GTM_ICM_ATOM8_CHANNEL_4        0x00000010UL
#define SPC5_GTM_ICM_ATOM8_CHANNEL_5        0x00000020UL
#define SPC5_GTM_ICM_ATOM8_CHANNEL_6        0x00000040UL
#define SPC5_GTM_ICM_ATOM8_CHANNEL_7        0x00000080UL

#define SPC5_GTM_ICM_ATOM9_CHANNEL_0        0x00000100UL
#define SPC5_GTM_ICM_ATOM9_CHANNEL_1        0x00000200UL
#define SPC5_GTM_ICM_ATOM9_CHANNEL_2        0x00000400UL
#define SPC5_GTM_ICM_ATOM9_CHANNEL_3        0x00000800UL
#define SPC5_GTM_ICM_ATOM9_CHANNEL_4        0x00001000UL
#define SPC5_GTM_ICM_ATOM9_CHANNEL_5        0x00002000UL
#define SPC5_GTM_ICM_ATOM9_CHANNEL_6        0x00004000UL
#define SPC5_GTM_ICM_ATOM9_CHANNEL_7        0x00008000UL

#define SPC5_GTM_ICM_ATOM10_CHANNEL_0       0x00010000UL
#define SPC5_GTM_ICM_ATOM10_CHANNEL_1       0x00020000UL
#define SPC5_GTM_ICM_ATOM10_CHANNEL_2       0x00040000UL
#define SPC5_GTM_ICM_ATOM10_CHANNEL_3       0x00080000UL
#define SPC5_GTM_ICM_ATOM10_CHANNEL_4       0x00100000UL
#define SPC5_GTM_ICM_ATOM10_CHANNEL_5       0x00200000UL
#define SPC5_GTM_ICM_ATOM10_CHANNEL_6       0x00400000UL
#define SPC5_GTM_ICM_ATOM10_CHANNEL_7       0x00800000UL

#define SPC5_GTM_ICM_ATOM11_CHANNEL_0       0x01000000UL
#define SPC5_GTM_ICM_ATOM11_CHANNEL_1       0x02000000UL
#define SPC5_GTM_ICM_ATOM11_CHANNEL_2       0x04000000UL
#define SPC5_GTM_ICM_ATOM11_CHANNEL_3       0x08000000UL
#define SPC5_GTM_ICM_ATOM11_CHANNEL_4       0x10000000UL
#define SPC5_GTM_ICM_ATOM11_CHANNEL_5       0x20000000UL
#define SPC5_GTM_ICM_ATOM11_CHANNEL_6       0x40000000UL
#define SPC5_GTM_ICM_ATOM11_CHANNEL_7       0x80000000UL
/** @} */

/*===========================================================================*/
/* Driver data structures and types.                                         */
/*===========================================================================*/

/**
 * @brief Type of a structure representing a (GTM-IP) ICM driver.
 */
typedef struct GTM_ICMDriver GTM_ICMDriver;

/**
 * @brief   Structure representing a (GTM) ICM driver.
 */
struct GTM_ICMDriver {

  /**
   * @brief Pointer to the (GTM) ICM registers block.
   */
	volatile GTM_ICM_TAG *icm;

  /**
   * @brief Pointer for application private data.
   */
    void *priv;
};


/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_ICM0 == TRUE) && !defined(__DOXYGEN__)
extern GTM_ICMDriver ICMD1;
#endif

#ifdef __cplusplus
extern "C" {
#endif

extern void gtm_icmInit(void);
extern uint32_t gtm_icmGetEvent(GTM_ICMDriver *icmd, uint32_t device);

#ifdef __cplusplus
}
#endif

#endif /* _GTM_ICM_H_ */
/** @} */
