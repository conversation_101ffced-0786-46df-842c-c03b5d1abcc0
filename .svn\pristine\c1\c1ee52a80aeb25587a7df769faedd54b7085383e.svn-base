VERSION ""


NS_ : 
	NS_DESC_
	CM_
	BA_DEF_
	BA_
	VAL_
	CAT_DEF_
	CAT_
	FILTER
	BA_DEF_DEF_
	EV_DATA_
	ENVVAR_DATA_
	SGTYPE_
	SGTYPE_VAL_
	BA_DEF_SGTYPE_
	BA_SGTYPE_
	SIG_TYPE_REF_
	VAL_TABLE_
	SIG_GROUP_
	SIG_VALTYPE_
	SIGTYPE_VALTYPE_
	BO_TX_BU_
	BA_DEF_REL_
	BA_REL_
	BA_DEF_DEF_REL_
	BU_SG_REL_
	BU_EV_REL_
	BU_BO_REL_
	SG_MUL_VAL_

BS_:

BU_: Vector_XXX


BO_ 4 PRELOAD: 8 Vector__XXX
 SG_ DCvolt : 0|32@1+ (0.0009765625,0) [0|0] "" Vector__XXX
 SG_ PLApos : 32|32@1- (0.0009765625,0) [0|0] "" Vector__XXX

BO_ 3 DAC_READ: 4 Vector__XXX
 SG_ RearPwm : 16|16@1+ (0.00152587890625,0) [0|0] "" Vector__XXX
 SG_ FrontPwm : 0|16@1+ (0.00152587890625,0) [0|0] ""  Vector_XXX

BO_ 2 DAC_Fast: 4 Vector_XXX
 SG_ RearCurrent : 16|16@1+ (0.739746,-30) [0|0] "" Vector__XXX
 SG_ FrontCurrent : 0|16@1+ (0.739746,-30) [0|0] "" Vector__XXX

BO_ 1 DAC: 8 Vector_XXX
 SG_ RearStroke : 48|16@1- (-0.028062428,106.91) [-1E+018|1E+028] "" Vector__XXX
 SG_ FrontStroke : 32|16@1- (0.063576009,-37.09) [-100000000000|1E+018] "" Vector__XXX
 SG_ AccRearBody : 16|16@1- (-0.091968759,183.94) [-1E+027|1E+029] "" Vector__XXX
 SG_ AccFrontBody : 0|16@1- (-0.091968759,183.94) [-1E+026|1E+049] "" Vector__XXX




