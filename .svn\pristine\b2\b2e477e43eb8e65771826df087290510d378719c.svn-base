/******************************************************************************************************************************/
/* $HeadURL::                                                                                                             $   */
/* $ Description:                                                                                                             */
/* $Revision::        $                                                                                                       */
/* $Date::                                                $                                                                   */
/* $Author::                         $                                                                                        */
/******************************************************************************************************************************/

// -----------------------------------------------------------------------------
// INCLUDE FILES


#include "sys.h"

#include "ccp.h"
#include "ccp_can_interface.h"

#include "os_resources.h"
#include "Mcan_out.h"
#include "TTcan_out.h"
#include "Utils_out.h"

#define RES_MY_CCP  (RES_SCHEDULER)

#ifdef  BUILD_DD_

#ifdef  _BUILD_CCP_

#pragma ghs startnomisra

#define CALROM_ADDR 0x0
#define CALRAM_ADDR 0x3ffc000

// -----------------------------------------------------------------------------
// VARIABLES and Structs
// -----------------------------------------------------------------------------
uint32_T g_ccp_dto_id;        // global DTO-ID
uint32_T g_ccp_cro_id;        // global CRO-ID

static uint8_T ccpCalPage;
static uint8_T ccpSendRetCode;
extern uint8_T  buffsel;
extern volatile uint8_T txBufFull[3];

#ifdef _ENABLE_CACHE_
uint8_T cntNOPdelay = CCPCNTNOPTXDELAY;
#endif
// -----------------------------------------------------------------------------
// SENDING an CRM-DTO when receiving an CRO
// -----------------------------------------------------------------------------


#define MAX_TX_ERROR   100U



uint8_T ccpSend(uint8_T *msg_p)
{
    /* GK changed to have only one exit point (MISRA) */
    uint8_T ret;

    static uint8_T CCPCANcnterr=0U;
    static uint32_T My_CCPCANcnterr=0U;

    if (GetBuffer() != CCP_NO_ERROR)
    {
        My_CCPCANcnterr++;
        if (CCPCANcnterr>MAX_TX_ERROR)
        {
            ccp.SessionStatus &= (uint8_T)~SS_CONNECTED;
#ifdef CCP_DAQ
            ccpStopAllDaq();
#endif
#ifdef CCP_SEED_KEY
            ccp.ProtectionStatus = 0; /* Clear Protection Status */
#endif
            ret = CAN_BUSOFF;  
        } else {
            CCPCANcnterr++; 
            ret = CAN_TX_BUSY;
        }
    } else {
        CCPCANcnterr = 0U;

#if (CCP_USE_ENGCAN == CCP_USE_MCAN)
        ccpSendRetCode = (uint8_T)CAN_TxData(CCP_CAN,(uint16_T)buffsel,msg_p);  //new method 2
#elif (CCP_USE_ENGCAN == CCP_USE_TTCAN)
        ccpSendRetCode = (uint8_T)TTCAN_TxData(CCP_CAN,(uint16_T)buffsel,msg_p);  //new method 2
#else
#error ENGINE CAN not defined!!!
#endif
#ifdef _ENABLE_CACHE_
        for (int k = 0; k < cntNOPdelay; k++){
            UTILS_nop();
        }
#endif
        ret = ccpSendRetCode;
    }

    return (ret);
}


// -----------------------------------------------------------------------------
// CAN TRANSMIT (Data Frame)
// -----------------------------------------------------------------------------
uint8_T ccpTxCrmPossible( void )
{
    return ccpSendRetCode;
}

// -----------------------------------------------------------------------------
// CONVERT pointer
// -----------------------------------------------------------------------------
uint8_T * ccpGetPointer( uint8_T addr_ext, uint32_T addr )        // get Pointer into
{                                                            // normal C


    return (uint8_T *) addr;

    //   se esistono due tipi di memoria a cui accedere
}
// -----------------------------------------------------------------------------
/*----------------------------------------------------------------------------*/
/* Calibration RAM/ROM Selection */

void ccpSetCalPage( uint32_T a )
{
    ccpCalPage = (uint8_T)a;

    if (ccpCalPage==1U) /* RAM */
    { 
    }
    else /* ROM */
    {             
    }

}


uint32_T ccpGetCalPage( void )
{
    return (uint32_T)ccpCalPage;
}



void ccpInitCalPage( void ) {

    uint8_T *p1 = (uint8_T *)CALROM_ADDR;
    uint8_T *p2 = (uint8_T *)CALRAM_ADDR;
    uint32_T i;
    for (i=0U;i<0x4000U;i++)
    {
        *p2 = *p1;
        p1++;
        p2++;
    }
}


void CCP_PeriodicMonitorTask100ms(void)
{
    int16_T CCPCanStatus;

    // GetResource(RES_CCP);
    ccpDaq(CCP_EVENT_CHANNEL_3);
    //  ReleaseResource(RES_CCP);
#if (CCP_USE_ENGCAN == CCP_USE_MCAN)
    /* Diagnosi cella can CCP */
    CCPCanStatus = CAN_GetStatus(CCP_CAN);
    if(CCPCanStatus == CAN_BUSOFF)
    {
        CAN_BusOffRecovery(CCP_CAN);
    } 
#elif (CCP_USE_ENGCAN == CCP_USE_TTCAN)
    CCPCanStatus = TTCAN_GetStatus(CCP_CAN);
    if(CCPCanStatus == CAN_BUSOFF)
    {
        TTCAN_BusOffRecovery(CCP_CAN);
    } 
#else
#error CAN engine not defined
#endif
}


void CCP_PeriodicMonitorTask10ms(void)
{

    //  GetResource(RES_CCP);
    ccpDaq(CCP_EVENT_CHANNEL_2);
    //  ReleaseResource(RES_CCP);

}


void CCP_PeriodicMonitorTask5ms(void)
{

    //  GetResource(RES_CCP);
    ccpDaq(CCP_EVENT_CHANNEL_1);
    //  ReleaseResource(RES_CCP);

}


void CCP_PeriodicMonitorTask4ms(void)
{

    //   GetResource(RES_MY_CCP);
    ccpDaq(CCP_EVENT_CHANNEL_4);
    //   ReleaseResource(RES_MY_CCP);

    //       ccpSendCallBack();
}

void CCP_MonitorEventEOA(void)
{

}


#pragma ghs endnomisra


#endif // BUILD_CCP_

#endif  //BUILD_DD_
