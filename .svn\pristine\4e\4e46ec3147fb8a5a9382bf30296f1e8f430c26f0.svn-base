/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_EZ_04_SMECC/tree/APPLICATION#$ */
/* $Revision: 164860 $                                                                                             */
/* $Date: 2021-06-11 17:17:26 +0200 (ven, 11 giu 2021) $                                                         */
/* $Author: SantoroR $                                                                              */
/*******************************************************************************************************************/
#ifdef  _BUILD_SAFETYMNGR_FLASHCHECK_

#include "rtwtypes.h"
#include "FlashCheckSM_MCU_r_xx.h"

/* Flash partition 0 patterns for safety checks */
const uint32_T Pattern1_rww0 = FLASH_PATTERN_1;
const uint32_T Pattern2_rww0 = FLASH_PATTERN_2;
const uint32_T Pattern3_rww0 = FLASH_PATTERN_3;
const uint32_T Pattern4_rww0 = FLASH_PATTERN_4;

#endif /* _BUILD_SAFETYMNGR_FLASHCHECK_ */

