#ifndef _FLASH_EISB6C_CFG_H_
#define _FLASH_EISB6C_CFG_H_

#define C90LC 1u
#define C90FL 2u
#define C55   3u

/* Flash Driver execution from RAM */
#define FLASH_DRIVER_EXECUTE_FROM_RAM   1u
#ifdef  FLASH_DRIVER_EXECUTE_FROM_RAM 
#define FLASH_TEXTRAM_SECTION ".vletext_RAM"
#endif

/* Flash callbacks activation  */
#define FLASHERASECBK
//#define FLASHPRGCBK
//#define FLASHPRGVERCBK
//#define FLASHBLKCHKCBK
//#define FLASHCHKSUMCBK

/* Flash Erase Callback Module Request */
#define FLASH_ERASE_NO_CBK        0u
// #define FLASH_ERASE_DIAG_CBK      1u // Unused in Appl

/* CACHE inhibition in case of Driver execution from RAM - NOTE: cache shall be enable in normal running */
#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1u)
#ifdef _ENABLE_CACHE_
#define USE_CACHE_INHIBITION            1u
#else
#define USE_CACHE_INHIBITION            0u
#endif
#else
#define USE_CACHE_INHIBITION            0u
#endif


/* Type of Flash Used */
#define FLASH_TYPE (C55) 


#define CODE_FLASH_BANK_ARRAY   1u   // Valori Ammissibili: 1 , 3
                                    // Da definire sulla base della flash
                                    //(3 Array su Monaco, 1 Array su Pictus - Leopard, 1 su Andorra 2A, 1 "Logico" su Andorra 4A)
                                    //in uso e sugli array da utilizzare effettivamente


/*--------------------------------------------------------------------------*/
/*                      Code FLASH configuration                            */
/*--------------------------------------------------------------------------*/
/* NB: definire address space e array da usare coerentemente alla definizione di CODE_FLASH_BANK_ARRAY */

/* Used Address Space of Code Flash (depend by memory cut)*/
#define USE_LOW_ADDR_SPACE      1u
#define USE_MID_ADDR_SPACE      0u
#define USE_LARGE_ADDR_SPACE    1u
#define USE_HIGH_ADDR_SPACE     1u

/* Used Bank Array of Code Flash (depend by memory cut)*/
#define USE_BANK0_ARRAY0     1u
#define USE_BANK1_ARRAY1     0u
#define USE_BANK1_ARRAY2     0u

/* Password to unlock space array */
#define FLASH_LMLR_PASSWORD             0xA1A11111u  /* Low/Mid address lock enabled password */
#define FLASH_HLR_PASSWORD              0xB2B22222u  /* High address lock enabled password */
#define FLASH_SLMLR_PASSWORD            0xC3C33333u  /* Secondary low and middle address lock enabled password */

#define C90FL_REG_BASE_BK0A0            0xC3F88000u
#define MAIN_ARRAY_BASE_BK0A0           0x00000000u
#define SHADOW_ROW_BASE_BK0A0           0x00FFC000u
#define SHADOW_ROW_SIZE_BK0A0           0x00004000u
#define FLASH_PAGE_SIZE_BK0A0           (C90FL_PAGE_SIZE_08)

#define C55_REG_BASE                    0xFFFE0000UL
#define MAIN_ARRAY_BASE                 0x00404000UL
#define UTEST_ARRAY_BASE                0x00400000UL
#define UTEST_ARRAY_SIZE                0x00004000UL

#define C55_PROGRAMMABLE_SIZE           0x80U

#define BUFFER_SIZE_BYTE                0x1000U


#if (USE_LOW_ADDR_SPACE==1u)

/* Low space block 0 - Cannot be accessed */
#define OFFSET_LOW_BLOCK0               0x00404000u /* Offset of low block 1 */
#define LOW_BLOCK0_SIZE                 0x4000u     /* 16KB size */
#define LAST_LOW_BLOCK0_ADDR            (OFFSET_LOW_BLOCK0+LOW_BLOCK0_SIZE)

/* Low space block 1 */
#define OFFSET_LOW_BLOCK1               0x00FC0000u /* Offset of low block 1 */
#define LOW_BLOCK1_SIZE                 0x4000u     /* 16KB size */
#define LAST_LOW_BLOCK1_ADDR            (OFFSET_LOW_BLOCK1+LOW_BLOCK1_SIZE)

/* Low space block 2 */
#define OFFSET_LOW_BLOCK2               0x00FC4000u /* Offset of low block 2 */
#define LOW_BLOCK2_SIZE                 0x4000u     /* 16KB size */
#define LAST_LOW_BLOCK2_ADDR            (OFFSET_LOW_BLOCK2+LOW_BLOCK2_SIZE)

/* Low space block 3 */
#define OFFSET_LOW_BLOCK3               0x00FC8000u /* Offset of low block 3 */
#define LOW_BLOCK3_SIZE                 0x4000u     /* 16KB size */
#define LAST_LOW_BLOCK3_ADDR            (OFFSET_LOW_BLOCK3+LOW_BLOCK3_SIZE)

/* Low space block 4 */
#define OFFSET_LOW_BLOCK4               0x00FCC000u /* Offset of low block 4 */
#define LOW_BLOCK4_SIZE                 0x4000u     /* 16KB size */
#define LAST_LOW_BLOCK4_ADDR            (OFFSET_LOW_BLOCK4+LOW_BLOCK4_SIZE)

/* Low space block 5 */
#define OFFSET_LOW_BLOCK5               0x00FD0000u /* Offset of low block 5 */
#define LOW_BLOCK5_SIZE                 0x8000u     /* 32KB size */
#define LAST_LOW_BLOCK5_ADDR            (OFFSET_LOW_BLOCK5+LOW_BLOCK5_SIZE)

/* Low space block 6 */
#define OFFSET_LOW_BLOCK6               0x00FD8000u /* Offset of low block 6 */
#define LOW_BLOCK6_SIZE                 0x8000u     /* 32KB size */
#define LAST_LOW_BLOCK6_ADDR            (OFFSET_LOW_BLOCK6+LOW_BLOCK6_SIZE)

/* Low space block 7 */
#define OFFSET_LOW_BLOCK7               0x00FE0000u /* Offset of low block 7 */
#define LOW_BLOCK7_SIZE                 0x10000u    /* 64KB size */
#define LAST_LOW_BLOCK7_ADDR            (OFFSET_LOW_BLOCK7+LOW_BLOCK7_SIZE)

/* Low space block 8 */
#define OFFSET_LOW_BLOCK8               0x00FF0000u /* Offset of low block 8 */
#define LOW_BLOCK8_SIZE                 0x10000u    /* 64KB size */
#define LAST_LOW_BLOCK8_ADDR            (OFFSET_LOW_BLOCK8+LOW_BLOCK8_SIZE)

#define FIRST_LOW_ADDRESS                (OFFSET_LOW_BLOCK1)
#define LAST_LOW_ADDRESS                 (LAST_LOW_BLOCK8_ADDR-1u)


#if (USE_LARGE_ADDR_SPACE == 1u)

/* 256K space block 1 */
#define OFFSET_K256_BLOCK1               0x01000000u /* Offset of 256K block 1 */
#define K256_BLOCK1_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK1_ADDR            (OFFSET_K256_BLOCK1+K256_BLOCK1_SIZE)

/* 256K space block 2 */
#define OFFSET_K256_BLOCK2               0x01040000u /* Offset of 256K block 2 */
#define K256_BLOCK2_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK2_ADDR            (OFFSET_K256_BLOCK2+K256_BLOCK2_SIZE)

/* 256K space block 3 */
#define OFFSET_K256_BLOCK3               0x01080000u /* Offset of 256K block 3 */
#define K256_BLOCK3_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK3_ADDR            (OFFSET_K256_BLOCK3+K256_BLOCK3_SIZE)

/* 256K space block 4 */
#define OFFSET_K256_BLOCK4               0x010C0000u /* Offset of 256K block 4 */
#define K256_BLOCK4_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK4_ADDR            (OFFSET_K256_BLOCK4+K256_BLOCK4_SIZE)

/* 256K space block 5 */
#define OFFSET_K256_BLOCK5               0x01100000u /* Offset of 256K block 5 */
#define K256_BLOCK5_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK5_ADDR            (OFFSET_K256_BLOCK5+K256_BLOCK5_SIZE)

/* 256K space block 6 */
#define OFFSET_K256_BLOCK6               0x01140000u /* Offset of 256K block 6 */
#define K256_BLOCK6_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK6_ADDR            (OFFSET_K256_BLOCK6+K256_BLOCK6_SIZE)

/* 256K space block 7 */
#define OFFSET_K256_BLOCK7               0x01180000u /* Offset of 256K block 7 */
#define K256_BLOCK7_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK7_ADDR            (OFFSET_K256_BLOCK7+K256_BLOCK7_SIZE)

/* 256K space block 8 */
#define OFFSET_K256_BLOCK8               0x011C0000u /* Offset of 256K block 8 */
#define K256_BLOCK8_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK8_ADDR            (OFFSET_K256_BLOCK8+K256_BLOCK8_SIZE)

/* 256K space block 9 */
#define OFFSET_K256_BLOCK9               0x01200000u /* Offset of 256K block 9 */
#define K256_BLOCK9_SIZE                 0x40000u    /* 256KB size */
#define LAST_K256_BLOCK9_ADDR            (OFFSET_K256_BLOCK9+K256_BLOCK9_SIZE)


#define LAST_LARGE_ADDRESS                 (LAST_K256_BLOCK9_ADDR-1u)

#endif

// CODE FLASH
#define FIRST_CODE_FLASH_ADDRESS        (FIRST_LOW_ADDRESS)
#define LAST_CODE_FLASH_ADDRESS         (LAST_LARGE_ADDRESS+1u)


#if (USE_HIGH_ADDR_SPACE == 1u)  //DATA FLASH

/* High space block 1 */
#define OFFSET_HIGH_BLOCK1              0x00800000u /* Offset of high block 0 */
#define HIGH_BLOCK1_SIZE                0x4000u     /* 16KB size */
#define LAST_HIGH_BLOCK1_ADDR           (OFFSET_HIGH_BLOCK1+HIGH_BLOCK1_SIZE)

/* High space block 2 */
#define OFFSET_HIGH_BLOCK2              0x00804000u /* Offset of high block 1 */
#define HIGH_BLOCK2_SIZE                0x4000u     /* 16KB size */
#define LAST_HIGH_BLOCK2_ADDR           (OFFSET_HIGH_BLOCK2+HIGH_BLOCK2_SIZE)

/* High space block 3 */
#define OFFSET_HIGH_BLOCK3              0x00808000u /* Offset of high block 2 */
#define HIGH_BLOCK3_SIZE                0x4000u     /* 16KB size */
#define LAST_HIGH_BLOCK3_ADDR           (OFFSET_HIGH_BLOCK3+HIGH_BLOCK3_SIZE)

/* High space block 4 */
#define OFFSET_HIGH_BLOCK4              0x0080C000u /* Offset of high block 3 */
#define HIGH_BLOCK4_SIZE                0x4000u     /* 16KB size */
#define LAST_HIGH_BLOCK4_ADDR           (OFFSET_HIGH_BLOCK4+HIGH_BLOCK4_SIZE)

#define LAST_HIGH_ADDRESS               (LAST_HIGH_BLOCK4_ADDR-1u)

#pragma ghs startnomisra
/*--------------------------------------------------------------------------*/
/*                      Data FLASH configuration                            */
/*--------------------------------------------------------------------------*/
/* Low space block 0 */
#define DATA_OFFSET_LOW_BLOCK0          OFFSET_HIGH_BLOCK1  /* Offset of low block 0 */
#define DATA_LOW_BLOCK0_SIZE            HIGH_BLOCK1_SIZE      /* 16KB size */

/* Low space block 1 */
#define DATA_OFFSET_LOW_BLOCK1          OFFSET_HIGH_BLOCK2  /* Offset of low block 1, in same partition with block 0 */
#define DATA_LOW_BLOCK1_SIZE            HIGH_BLOCK2_SIZE      /* 16KB size */

/* Low space block 2 */
#define DATA_OFFSET_LOW_BLOCK2          OFFSET_HIGH_BLOCK3  /* Offset of low block 2, in same partition with block 0 and 1 */
#define DATA_LOW_BLOCK2_SIZE            HIGH_BLOCK3_SIZE      /* 16KB size */

/* Low space block 3 */
#define DATA_OFFSET_LOW_BLOCK3          OFFSET_HIGH_BLOCK4  /* Offset of low block 3, in same partition with block 0, 1 and 2 */
#define DATA_LOW_BLOCK3_SIZE            HIGH_BLOCK4_SIZE      /* 16KB size */

#define FIRST_DATA_FLASH_ADDRESS        (DATA_OFFSET_LOW_BLOCK0)
#define LAST_DATA_FLASH_ADDRESS         (DATA_OFFSET_LOW_BLOCK3+DATA_LOW_BLOCK3_SIZE) 
#pragma ghs endnomisra

#endif

#endif

#endif

