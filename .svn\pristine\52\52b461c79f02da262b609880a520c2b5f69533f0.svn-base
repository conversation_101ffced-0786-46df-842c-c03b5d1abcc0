/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_INTC.c
**  Created on      :  03-Feb-2022 15:39:00
**  Original author :  Mocci A
******************************************************************************/
#ifdef _BUILD_SAFETYMNGR_INTC_

#ifndef _BUILD_SAFETYMNGR_
#error INTC Safety Module enabled without _BUILD_SAFETYMNGR_ macro enabled
#endif /*  _BUILD_DIAGCANMGM_ */

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_INTC.h"

/******************************************************************************
**   Function    : SafetyMngr_INTC_CHECK_CORE
**
**   Description:
**    This function:
*      - checks the correctness of core selection for ISR servicing (ISR_CHECK_CORE),
**     - checks the correctness of the ISR priority (ISR_CHECK_PRIORITY),
**     - has to be called for every ISR (safety and not),
**     - covers par. "3.2 ISR_CHECK_PRIORITY" and "3.3 ISR_CHECK_CORE" of 
**       "AN4446 - SPC574K72xx safety manual".
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**
**   Implementation Notes:
**
**   EA GUID: 
******************************************************************************/
void SafetyMngr_INTC_CheckCtx(uint16_T isrVectPos)
{
    /* code construction start */
uint32_T CoreId = (uint32_T) 0xFFu;
uint8_T CoreInUse = INTC_PSR_NOSENT;
uint32_T CurrentPriority = PRI_0;

    /* Get Processor ID Register 286 */
    CoreId = getSpecReg32SPR_PIR(); 
    if (CoreId == 0u)
    {
        CoreInUse = INTC_PSR_CORE0;
    }
    else if (CoreId == 2u)
    {
        CoreInUse = INTC_PSR_CORE2;
    }
    else
    {
        /* unreachable nest */
    }

    /* perform Core and Priority checks */
    if (CoreInUse != INTC_PSR_NOSENT) // if Interrupt request is actually sent to the core
    {
#ifdef _BUILD_ISR_CHECK_CORE_
        if (CoreInUse == (uint32_T)INTC_PSRnVector[isrVectPos][1])
        {
            /* ISR_CHECK_CORE is OK */
#ifdef _BUILD_ISR_CHECK_PRIO_
            CurrentPriority = INTC.CPR[CoreId].B.PRI; //coreId 0,1,2,3 with core0 and core2 in use and core1/3 not defined
            if (CurrentPriority == (uint32_T)INTC_PSRnVector[isrVectPos][0])
            {
                /* ISR_CHECK_PRIORITY is OK */
            }
            else
            {
                SafetyMngr_ReportError(SAFE_ERR_INTC_WRONGINIT, FALSE);
            }
#endif // _BUILD_ISR_CHECK_PRIO_
        }
        else
        {
            SafetyMngr_ReportError(SAFE_ERR_INTC_CONTEXT, FALSE);
        }
#endif // _BUILD_ISR_CHECK_CORE_
    }
    else // in this case a Priority for the ISR has been set but no one handler is linked to the relative ISR, error in configuration
    {
        /* unreachable nest */
    }

    /* code construction end */
}

/******************************************************************************
**   Function    : SafetyMngr_INTC_CheckPeriodic
**
**   Description:
**    checks if IRQ flags in peripherals are all cleared
**
**   Parameters :
**    [inout] uint32_T * regs : Array that contains the values of the register that has to be checked.
**    [inout] uint32_T * regMasks : Array with mask values that has to be used for register check.
**    [in] uint8_T  regNum :  be used for register check.
**
**   Returns:
**    void
**
**   SW Requirements:
**
**   Implementation Notes:
**
**   EA GUID: 
******************************************************************************/
void SafetyMngr_INTC_CheckPeriodic(uint32_T * regs, uint32_T * regMasks, uint8_T regNum)
{
    /* code construction start */
#if 0
/** @brief Function that checks if IRQ flags in peripherals are all cleared.
 *  @param regs     Array that contains the values of the register that has to be checked.
 *  @param regMasks Array with mask values that has to be used for register check.
 *  @param regNum   Number of registers expected.
 *
 *  This function has to be called every FTTI to verify that there are no pending interrupts
 *  that is that every interrupt is executing correctly.
 *  This function covers SM_MCU_4_26 requirement.
 *
 *  @note the size of regs and regMasks has to be the same, that is regNum.
 */
    safe_error_id_t err = (safe_error_id_t)0UL;

    if ((NULL == regs) || (NULL == regMasks))
    {
        err = SAFE_ERR_INTC_PERIODICARG; /* Invalid argument */
    }
    else
    {
        for (uint8_T regIdx = 0U; regIdx < regNum; regIdx++)
        {
            if ((regs[regIdx] & regMasks[regIdx]) != 0UL)
            {
                err = SAFE_ERR_INTC_PERIODICCALL;
            }
        }
    }

    if (err > 0UL)
    {
        SafetyMngr_ReportError(err, FALSE);
    }
#endif

    /* code construction end */
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/* None */

#endif // _BUILD_SAFETYMNGR_INTC_
/****************************************************************************
 ****************************************************************************/

