/******************************************************************************************************************************/
/* $HeadURL::                                                                                                            $   */
/* $ Description:                                                                                                             */
/* $Revision::    $                                                                                                       */
/* $Date::                                                $                                                                   */
/* $Author::                         $                                                                                        */
/******************************************************************************************************************************/

#ifndef _I5F61D_CONFIG_H_
#define _I5F61D_CONFIG_H_

/* Engine configuration */
#define MV_AGUSTA_3C            (16)
#define IBOX_FT_4C              (20)
#define ML_P15_8C               (21)
#define FE_173_8C               (22)
#define FE_171_6C               (23)
#define LA_LB74x_12C            (24)
#define I1_CNH_6C               (25)
#define AM_AMG_6C               (26)
#define CH_4C                   (27)
#define CH_6C                   (28)
#define BR_16C                  (29)

/* McLaren config */
#define CAN_P15                 (1)
#define CAN_F173                (2)
#define CAN_F171                (3)
#define CAN_LB74x               (4)
#define CAN_INEF                (5)
#define CAN_AMG6C               (6)
#define CAN_CH4C                (7)
#define CAN_CH6C                (8)
#define CAN_TESTCFG             (9)
#define CAN_BR16C               (10)

#define ENGINE_TYPE             (FE_171_6C)
#define CAN_TYPE                (CAN_F173)

/*Time Base Configuration*/
#define MS_1    1
#define MS_5    5
#define TIME_BASE_ (MS_1)

/* Cache enable */
#define _ENABLE_CACHE_

/*Trace debug enable*/
#define _ENABLE_TRACE_

/* Target Configuration */
#define MPC5554                 (1)   // MPC5554
#define MPC5534                 (2)   // MPC5534/MPC5533
#define MPC563XM                (3)   // MPC563xM
#define MPC5643L                (4)   // MPC5643L
#define MPC560XP                (5)   // MPC560xP
#define MPC5642A                (6)   // MPC5642A
#define MPC5644A                (7)   // MPC5644A
#define SPC574K2                (8)   // SPC574K2
#define SPC574K2_CUT24          (9)   // SPC574K2 CUT2.4


#define BOARD_R1                (1)
#define BOARD_IBOX              (11)   /* ECU IBOX         */
#define BOARD_EISB_A            (12)   /* ECU EISB PROTO A */
#define BOARD_EISB_B            (13)   /* ECU EISB PROTO B */
#define BOARD_EISB_C            (14)   /* ECU EISB PROTO C */
#define BOARD_EISB6C_A          (15)   /* ECU EISB6C CHR PROTO A */
#define BOARD_EISB8F_A          (16)   /* ECU EISB8C FER PROTO A */

#define TARGET_TYPE             (SPC574K2_CUT24)
#define XOSC_MHZ                (40)

/* Board Configuration */
#define BOARD_TYPE              (BOARD_EISB8F_A)

//#define TEST_FCA_7Z00071



#define APP_TAG_PREF        "I5F61D-"
#define ELDOR_ECU_HW_NUM    "H0240603   "
#define ELDOR_ECU_HW_VER    0x00u

#define VBATT12V_HILEV          (160U)   // 10*16

/*************************************************/
/*   Precompiler defines for modules inclusion   */
/*************************************************/
#define BUILD_BIOS_
#define BUILD_DD_
#define BUILD_APP_
/**************************************************/

//#define _TEST_ADC_
//#define  _TEST_EEPROM_
//#define OSTASKQUEUEMAX_TEST
//#define _TEST_FLASH_
//#define _TEST_CACHE_
//#define _TEST_PORT_
//#define _TEST_PIT_

 /* Precompiler defines for BIOS modules inclusion */
#ifdef BUILD_BIOS_
#define _BUILD_PORT_          //EISB ON
#define _BUILD_ADC_        //EISB ON 
#define _BUILD_CAN_        //EISB ON
#define _BUILD_DIGIO_      //EISB ON
#define _BUILD_DMA_        //EISB ON
#define _BUILD_FLASH_      //EISB ON
#define _BUILD_EEPROM_     //EISB ON
#define _BUILD_UTILS_      //EISB ON
//#define _BUILD_MATHLIB_    //EISB ON
#define _BUILD_SPI_          //EISB OFF
#define _BUILD_SYS_        //EISB ON
#define _BUILD_GTM_
#define _BUILD_TASK_       //EISB ON
#define _BUILD_TIMING_     //EISB ON
#define _BUILD_IGN_        //EISB ON
#define _BUILD_VSRAM_      //EISB ON
#define _BUILD_RECOVERY_   //EISB ON
#ifdef _BUILD_RECOVERY_
#define _BUILD_RAM_RECOVERY_          //EISB ON
#define _BUILD_CORE0MEM_RECOVERY_     //EISB ON
//#define _BUILD_RECOVERY_IVOR2_TEST_   //EISB OFF
#define _BUILD_PERIPHERAL_RECOVERY_
#ifdef _BUILD_RECOVERY_IVOR2_TEST_      //EISB OFF
//#define DEBUG_RECOVERY_RAM_ECC        //EISB OFF
#endif
#endif
#define _BUILD_PIT_     //EISB ON
#define _BUILD_STM_
#define _BUILD_IGN_LOAD_TEST_
//RS added only in this branch for testing purposes
//#define _TEST_IVOR2_
//#define _TEST_RAM_ECC_
//#define FORCE_ECC_ERROR_BY_DMA_READ
#endif //BUILD_BIOS_

#define _BUILD_SAFETYMNGR_
/* Precompiler defines for Safety modules inclusion */
#ifdef _BUILD_SAFETYMNGR_
#define _BUILD_SAFETYMNGR_INTC_
#ifdef _BUILD_SAFETYMNGR_INTC_
#define _BUILD_ISR_CHECK_CORE_      // enable ISR_CHECK_CORE mechanism
#define _BUILD_ISR_CHECK_PRIO_      // enable ISR_CHECK_PRIORITY mechanism
//#define _BUILD_INTC_CHECK_TRIG_SET_ // disbale INT_CHECK_TRIGGER_SET mechanism until its behavior is totally clear
#endif // _BUILD_SAFETYMNGR_INTC_
//#define _BUILD_SAFETYMNGR_ADC_
//#define _BUILD_SAFETYMNGR_FCCU_
//#define _BUILD_SAFETYMNGR_CACHE_
//#define _BUILD_SAFETYMNGR_CMUCHECK_
#define _BUILD_SAFETYMNGR_PIT_
#define _BUILD_SAFETYMNGR_FLASHCHECK_

#define SM_EN_DEBUG_ERRCNT /* define to enable SafetyMngr error counter*/

#define _BUILD_SAFETYMNGR_RAMCHECK_
#ifdef _BUILD_SAFETYMNGR_RAMCHECK_
#define SRAM_CHECK_ENABLE
//#define I_MEM_0_CHECK_ENABLE
#define D_MEM_0_CHECK_ENABLE
#define I_MEM_2_CHECK_ENABLE
//#define D_MEM_2_CHECK_ENABLE
#endif /* _BUILD_SAFETYMNGR_RAMCHECK_ */
#endif /* _BUILD_SAFETYMNGR_ */

/* Precompiler defines for DD modules inclusion */
#ifdef BUILD_DD_
#define _BUILD_TEMPMGM_
#define _BUILD_CCP_
//#define _BUILD_SPIMGM_
#define _BUILD_VSRAMMGM_
#define _BUILD_ANALOGIN_
#define _BUILD_DIGIN_
//#define _BUILD_DIGOUT_
#define _BUILD_EEMGM_
#define _BUILD_INTSRCMGM_
#define _BUILD_DIAGCANMGM_
#define _BUILD_TPE_
//#define _BUILD_KWP_ //take undefined if UDS is defined as diagnositc application protocol
#define _BUILD_UDS_ //take undefined if KWP is defined as diagnositc application protocol
//#define _BUILD_EXT_WDT_
//#define _BUILD_BUCKMGM_
#define _BUILD_MSPARKCMD_
//#define _BUILD_RELAYMGM_
#define _BUILD_IONACQ_
#define _BUILD_FLASHMGM_
#define _BUILD_TLE9278BQX_CFG_
#define _BUILD_TLE9278BQX_IOS_
#define _BUILD_TLE9278BQX_MGM_
#define _BUILD_TLE9278BQX_COM_
#define _BUILD_TLE9278BQX_GET_
#define _BUILD_TLE9278BQX_PRS_
#define _BUILD_TLE9278BQX_DIAG_
#define _BUILD_WDT_SBC_
#define _BUILD_IGNINCMD_
#define _BUILD_BUCKDIAGMGM_
#endif /* BUILD_DD_ */

/* Precompiler defines for APPLICATION modules inclusion */
#ifdef BUILD_APP_
#define _BUILD_DIAGMGM_
#define _BUILD_RECMGM_
//#define _BUILD_EE_WRITE_
#define _BUILD_CANMGM_
#define _BUILD_PWRMGM_
//#define _BUILD_SAFETY3MGM_
//#define _BUILD_CPUMGM_
#define _BUILD_DTC_
#define _BUILD_RLI_
#define _BUILD_ACTIVE_DIAG_
//#define SPE_OPTIMIZATION - MC
#define _BUILD_KNOCKCORRADP_
#define _BUILD_KNOCKCORRMGM_
#define _BUILD_KNOCKCORRNOM_
#define _BUILD_KNOCKCORRTOT_
#define _BUILD_LOADMGM_
#define _BUILD_COILANGPATTERN_
#define _BUILD_COILTIMPATTERN_
#define _BUILD_TEMPECUMGM_
#define _BUILD_IONCHARGECTRL_
#define _BUILD_COILTARGET_
#define _BUILD_RONDETECTCNT_
#define _BUILD_RONDETECTCROSS_
#define _BUILD_RONDETECTEN_
#define _BUILD_RONDETECTEST_
#define _BUILD_RONDETECTFUEL_
#define _BUILD_RONDETECTMGM_
#define _BUILD_RONDETECTSA_
#define _BUILD_TSPARKCTRLADAT_
#define _BUILD_LIVENESSMGM_
#define _BUILD_IONACQBUFMGM_
#define _BUILD_IONACQBUFREC_
#define _BUILD_IONACQCIRCMGM_
#define _BUILD_IONACQPAREVAL_
#define _BUILD_IONPHASEMGM_
#define _BUILD_IONINTMGM_
#define _BUILD_IONDWELLMGM_
#define _BUILD_IONKNOCKAIRCORR_
#define _BUILD_IONKNOCKEN_
#define _BUILD_IONKNOCKFFT_
#define _BUILD_IONKNOCKINT_
#define _BUILD_IONKNOCKPOWER_
#define _BUILD_IONKNOCKSPIKEDET_
#define _BUILD_IONKNOCKSTATE_
#define _BUILD_P2NOISEDETECT_
#define _BUILD_IONMISF_
#define _BUILD_MISFTHRMGM_
#define _BUILD_MKNOCKDET_
#define _BUILD_SPARKPLUGTEST_
#define _BUILD_SYNCMGM_
#endif /* BUILD_APP_ */

/* target specific diagnosis */

#define _OSEK_
//#define _BUILD_CHECKSUMVALIDATION_
#define _BUILD_DEVELOPMENT_ /* defined only in "Dev" configuration !!! */

//#define USE_EEPROM_SWUPDATE        // if defined, eeprom ID4 is used to exchange data for sw updates in spite of VSRAM
//#define USE_VSRAM_SWUPDATE        // MC - if defined, VSRAM is used to exchange data for sw updates in spite of eeprom ID4
//#define USE_ERASE_CALLBACK       // if defined, a callback is used during erase opertion. NOTE: it is strongly project dependent


/* Supplier ID  released by Filippo Magri from maserati on 03/02/2017 */
#define SUPPLIER_ID_MSB 0x00u
#define SUPPLIER_ID_LSB 0x71u


#define HWNUM_COMPATIBILITY_CHECK // if def, HWVersion and Number are write in EEPROM in order to be check by the Boot during next SW updates to avoid jumps to application sw NOT hw compliant
/// Do not store snapshots in error memory 
#define SAVE_ENV_DATA_EE 1u

#include "mpc5634m_config.h"

/****************************************************************************
     Peripherals defines 
 ****************************************************************************/


#endif /*_I5F81D_CONFIG_H_*/
