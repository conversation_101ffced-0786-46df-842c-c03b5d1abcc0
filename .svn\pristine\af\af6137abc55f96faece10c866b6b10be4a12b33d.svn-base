/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#include "rtwtypes.h"


#pragma ghs section data=".ee_id6_data"

#ifdef _TEST_EEPROM_
/*  MC Test Struct  */
uint32_T Prova_ID6[8] = {  
                            0x66666666u, 0x66666666u, 0x66666666u, 0x66666666u,
                            0x66666666u, 0x66666666u, 0x66666666u, 0x66666666u
                        };
#endif

// Declare here all the variables to be stored in EEPROM with ID6
///Dummy_EE ID6
uint32_T EEDummyID6_00 = 0u;
uint32_T EEDummyID6_01 = 0u;
uint32_T EEDummyID6_02 = 0u;
uint32_T EEDummyID6_03 = 0u;

#ifdef _BUILD_INTSRCMGM_
#pragma ghs startnomisra
#include "intsrcmgm_eep.c"
#pragma ghs endnomisra
#else 
#warning NO INTSRCMGM
#endif


/* EOF EEPROM */

