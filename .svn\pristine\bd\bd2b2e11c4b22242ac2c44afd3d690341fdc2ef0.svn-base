/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Tpe
**  Filename        :  Tpe_out.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  MocciA
******************************************************************************/
/*****************************************************************************
**
**                        TPE Description
**
**  TPE (Transport Protocol Entity) Sw module implement layer 3 (Network Layer) and layer 4 (Transport Layer) of OSI stack.
**  It provides interfaces to manage incoming/outcoming data packets from/to diagnostic tester using a client/server 
**  architecture where the client is the external tester and the server is the ECU.
**  ISO spec reference:
**      1. ISO 15765-2:2011(E),
**      2. ISO 15765-4:2011,
**      3. Fiat norm 07274_E_Ed4.
******************************************************************************/

#ifndef __TPE_OUT_H
#define __TPE_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "typedefs.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/// TPE CAN buffer size
#define TPE_CANBUFF_SIZE              (8U)

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/// External tool request type, according to ISO 15765-2:2011
typedef enum
{
    PHYSICAL   = 0,
    FUNCTIONAL 
} tpeReqType_T;

/// TPE internal state machine status according to Fiat norm 07274_E_Ed4.
typedef enum
{
    TPE_IDLE = 0,               // Both RX and TX TPE state
    TPE_RECEIVING,              // only RX TPE state
    TPE_WAIT_CONNECT,           // only TX TPE state
    TPE_SEND_BLOCK,             // only TX TPE state
    TPE_WAIT_FLOWCONTROL,       // only TX TPE state
    TPE_WAIT_IDLE,              // only TX TPE sub-state, where TPE waits for confirmation from the DataLink Layer (CAN) each time a frame has been requested for transmission
    TPE_WAIT_WAIT_CONNECT,      // only TX TPE sub-state, where TPE waits for confirmation from the DataLink Layer (CAN) each time a frame has been requested for transmission
    TPE_WAIT_SEND_BLOCK,        // only TX TPE sub-state, where TPE waits for confirmation from the DataLink Layer (CAN) each time a frame has been requested for transmission
    TPE_WAIT_WAIT_FLOW_CONTROL, // only TX TPE sub-state, where TPE waits for confirmation from the DataLink Layer (CAN) each time a frame has been requested for transmission
} tpeStatus_T;

/// Rx data structure
typedef struct __tpeRx_t
{
    tpeStatus_T status;                          // RxMngr status: TPE_IDLE, TPE_RECEIVING
    uint16_T    tpdu_len;                        // Received data len => T_DataIND.DataLength
    uint8_T     tpdu_data[TPE_TPDU_RXBUFF_SIZE]; // Received data buffer => T_DataIND.Data
    uint16_T    num_data_rcv;                    // Number of reveived data
    uint16_T    next_seg;                        // Next segment number
    uint32_T    seg_counter;                     // Segment counter
    uint8_T     FC_data[TPE_CANBUFF_SIZE];       // Buffer for TPDU response (flow control)
    uint8_T     rx_buf;                          // Received data valid flag
    uint8_T     CR_timer;                        // Time until reception of next consecutive frame
}tpeRx_t;

/// Tx data structure
typedef struct __tpeTx_t
{
    tpeStatus_T status;                          // TxMngr status: TPE_IDLE, TPE_WAIT_CONNECT, TPE_SEND_BLOCK, TPE_WAIT_FLOWCONTROL
    uint16_T     num_seg_snd;                     // Number of segments
    uint16_T     block_size;                      // Block size
    uint8_T     ST_timer;                        // ST timer: on different segments reception (segmentation)
    uint8_T     ST_timeout;                      // Segmentation timeout  
    uint8_T     CN_timer;                        // CN timer: timeout on first frame flow control (connection)
    uint8_T     FC_timer;                        // FC timer: timeout on intermediate frames flow control  (flowcontrol)
    uint16_T     seg_counter;                     // Segmentation counter
    uint8_T     next_seg;                        // Next segmentation number
    uint16_T    tpdu_len;                        // Trasmit data len => T_DataREQ.DataLength
    uint8_T     tpdu_data[TPE_TPDU_TXBUFF_SIZE]; // Trasmit data buffer => T_DataREQ.Data
    uint8_T     error;                           // Send error code
}tpeTx_t;

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/// Global Struct for TPE reception context
extern tpeRx_t * T_DataIND_ptr;
/// Global Struct for TPE transmission context
extern tpeTx_t * T_DataREQ_ptr;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : tpeInit
**
**   Description:
**    This method initializes Transport Layer module
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void tpeInit(void);

/******************************************************************************
**   Function    : tpeReceive
**
**   Description:
**    This method handles incoming PDUs
**
**   Parameters :
**    uint8_T *data,
**    uint16_T *data_len
**
**   Returns:
**    uint8_T error
**
******************************************************************************/
extern uint8_T tpeReceive(uint8_T *data, uint16_T *data_len);

/******************************************************************************
**   Function    : tpeSend
**
**   Description:
**    This method handles outcoming PDUs
**
**   Parameters :
**    uint8_T *data,
**    uint16_T *data_len
**
**   Returns:
**    uint8_T error
**
******************************************************************************/
extern uint8_T tpeSend(uint8_T *data, uint16_T data_len);

/******************************************************************************
**   Function    : tpeSendStatus
**
**   Description:
**    This method returns PDUs transmission status
**
**   Parameters :
**    void
**
**   Returns:
**    uint8_T status
**
******************************************************************************/
extern uint8_T tpeSendStatus(void);

/******************************************************************************
**   Function    : tpeReceiveStatus
**
**   Description:
**    This method returns PDUs reception status
**
**   Parameters :
**    void
**
**   Returns:
**    uint8_T status
**
******************************************************************************/
extern uint8_T tpeReceiveStatus(void);

/******************************************************************************
**   Function    : tpeSetCommStatus
**
**   Description:
**    This method sets Transport Protocol status
**
**   Parameters :
**    uint8_T status
**
**   Returns:
**    void
**
******************************************************************************/
extern void tpeSetCommStatus(uint8_T status);

/******************************************************************************
**   Function    : tpeGetCommStatus
**
**   Description:
**    This method gets Transport Protocol status
**
**   Parameters :
**    void
**
**   Returns:
**    uint8_T status
**
******************************************************************************/
extern uint8_T tpeGetCommStatus(void);

/******************************************************************************
**   Function    : tpeTickTimer
**
**   Description:
**    This method refreshes periodically Transport Protocol internal counters
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void tpeTickTimer(void);

/******************************************************************************
**   Function    : tpeGetReqType
**
**   Description:
**    This method returns last external request type (unicast or broadcast)
**
**   Parameters :
**    void
**
**   Returns:
**    tpeReqType_T reqType
**
******************************************************************************/
extern tpeReqType_T tpeGetReqType(void);
#endif //__TPE_OUT_H

