/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  SYS
**  Filename        :  GTM_HostInterface.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  SantoroR
******************************************************************************/
#ifdef _BUILD_GTM_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/

#include "Timing.h"
#include "OS_api.h"
//#include "digio.h"
//#include "digitalin.h"
#pragma ghs startnomisra

#include "../auto/mcs0.h"
#include "../auto/mcs1.h"
#include "../auto/mcs2.h"

#include "../auto/mcs0.c"
#include "../auto/mcs1.c"
#include "../auto/mcs2.c"

#include "Gtm.h"

#include "sys.h"
#include "Sync_out.h"
#include "msparkcmd_out.h"
#include "Utils_out.h"


extern void spc5_gtm_tim_interrupt_channel_handler(GTM_TIMDriver *timd, uint8_t channel);
extern void spc5_gtm_mcs_interrupt_channel_handler(GTM_MCSDriver *mcsd, uint8_t channel);
extern void spc5_gtm_atom_interrupt_channel_handler(GTM_ATOMDriver *atomd, uint8_t channel);

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/

uint16_T Debug_Tim0_ch0_Isr;
uint16_T Debug_Tim0_ch1_Isr;
uint16_T Debug_Tim0_ch2_Isr;
uint16_T Debug_Tim1_Isr[8];
uint16_T Debug_Tim2_Isr[8];
uint16_T Debug_MCS0_Isr[8];
uint16_T Debug_MCS1_Isr[8];
uint16_T Debug_MCS2_Isr[8];

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/


/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : GTM_Tim0_Channel0_IntHandler
**
**   Description:
**    GTM Tim0 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim0_Channel0_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM0_CH0_ISR_POS); // Interrupt no. 749
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL0);
    Debug_Tim0_ch0_Isr;
}

/******************************************************************************
**   Function    : GTM_Tim0_Channel1_IntHandler
**
**   Description:
**    GTM Tim0 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim0_Channel1_IntHandler(void)
{
    spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL1);
    Debug_Tim0_ch1_Isr++;
}

/******************************************************************************
**   Function    : GTM_Tim0_Channel2_IntHandler
**
**   Description:
**    GTM Tim0 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim0_Channel2_IntHandler(void)
{
    spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL2);
    Debug_Tim0_ch2_Isr++;
}

/******************************************************************************
**   Function    : GTM_Tim1_Channel0_IntHandler
**
**   Description:
**    GTM Tim1 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim1_Channel0_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM1_CH0_ISR_POS); // Interrupt no. 757
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL0);
    Debug_Tim1_Isr[TIM_CHANNEL0]++;
}

/******************************************************************************
**   Function    : GTM_Tim1_Channel1_IntHandler
**
**   Description:
**    GTM Tim1 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim1_Channel1_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM1_CH1_ISR_POS); // Interrupt no. 758
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL1);
    Debug_Tim1_Isr[TIM_CHANNEL1]++;
}

/******************************************************************************
**   Function    : GTM_Tim1_Channel2_IntHandler
**
**   Description:
**    GTM Tim1 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim1_Channel2_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM1_CH2_ISR_POS); // Interrupt no. 759
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL2);
    Debug_Tim1_Isr[TIM_CHANNEL2]++;
}

/******************************************************************************
**   Function    : GTM_Tim1_Channel3_IntHandler
**
**   Description:
**    GTM Tim1 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim1_Channel3_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM1_CH3_ISR_POS); // Interrupt no. 760
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL3);
    Debug_Tim1_Isr[TIM_CHANNEL3]++;
}

/******************************************************************************
**   Function    : GTM_Tim1_Channel4_IntHandler
**
**   Description:
**    GTM Tim1 Channel4 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim1_Channel4_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM1_CH4_ISR_POS); // Interrupt no. 761
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL4);
    Debug_Tim1_Isr[TIM_CHANNEL4]++;
}

/******************************************************************************
**   Function    : GTM_Tim1_Channel5_IntHandler
**
**   Description:
**    GTM Tim1 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim1_Channel5_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM1_CH5_ISR_POS); // Interrupt no. 762
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL5);
    Debug_Tim1_Isr[TIM_CHANNEL5]++;
}

/******************************************************************************
**   Function    : GTM_Tim1_Channel6_IntHandler
**
**   Description:
**    GTM Tim1 Channel6 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim1_Channel6_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM1_CH6_ISR_POS); // Interrupt no. 763
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL6);
    Debug_Tim1_Isr[TIM_CHANNEL6]++;
}

/******************************************************************************
**   Function    : GTM_Tim1_Channel7_IntHandler
**
**   Description:
**    GTM Tim1 Channel7 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim1_Channel7_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM1_CH7_ISR_POS); // Interrupt no. 764
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL7);
    Debug_Tim1_Isr[TIM_CHANNEL7]++;
}


/******************************************************************************
**   Function    : GTM_Tim2_Channel0_IntHandler
**
**   Description:
**    GTM Tim2 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim2_Channel0_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM2_CH0_ISR_POS); // Interrupt no. 765
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL0);
    Debug_Tim2_Isr[TIM_CHANNEL0]++;
}

/******************************************************************************
**   Function    : GTM_Tim2_Channel1_IntHandler
**
**   Description:
**    GTM Tim2 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim2_Channel1_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM2_CH1_ISR_POS); // Interrupt no. 766
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL1);
    Debug_Tim2_Isr[TIM_CHANNEL1]++;
}

/******************************************************************************
**   Function    : GTM_Tim2_Channel2_IntHandler
**
**   Description:
**    GTM Tim2 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim2_Channel2_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM2_CH2_ISR_POS); // Interrupt no. 767
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL2);
    Debug_Tim2_Isr[TIM_CHANNEL2]++;
}

/******************************************************************************
**   Function    : GTM_Tim2_Channel3_IntHandler
**
**   Description:
**    GTM Tim2 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim2_Channel3_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM2_CH3_ISR_POS); // Interrupt no. 768
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL3);
    Debug_Tim2_Isr[TIM_CHANNEL3]++;
}

/******************************************************************************
**   Function    : GTM_Tim2_Channel4_IntHandler
**
**   Description:
**    GTM Tim2 Channel4 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim2_Channel4_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM2_CH4_ISR_POS); // Interrupt no. 769
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL4);
    Debug_Tim2_Isr[TIM_CHANNEL4]++;
}

/******************************************************************************
**   Function    : GTM_Tim2_Channel5_IntHandler
**
**   Description:
**    GTM Tim2 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim2_Channel5_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM2_CH5_ISR_POS); // Interrupt no. 770
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL5);
    Debug_Tim2_Isr[TIM_CHANNEL5]++;
}

/******************************************************************************
**   Function    : GTM_Tim2_Channel6_IntHandler
**
**   Description:
**    GTM Tim2 Channel6 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim2_Channel6_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM2_CH6_ISR_POS); // Interrupt no. 771
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL6);
    Debug_Tim2_Isr[TIM_CHANNEL6]++;
}

/******************************************************************************
**   Function    : GTM_Tim2_Channel7_IntHandler
**
**   Description:
**    GTM Tim2 Channel7 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Tim2_Channel7_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_TIM2_CH7_ISR_POS); // Interrupt no. 772
#endif

    spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL7);
    Debug_Tim2_Isr[TIM_CHANNEL7]++;
}


/******************************************************************************
**   Function    : GTM_Mcs0_Channel0_IntHandler
**
**   Description:
**    GTM Mcs0 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs0_Channel0_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS0_CH0_ISR_POS); // Interrupt no. 781
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD1, MCS_CHANNEL0);
    Debug_MCS0_Isr[MCS_CHANNEL0]++;
}

/******************************************************************************
**   Function    : GTM_Mcs0_Channel1_IntHandler
**
**   Description:
**    GTM Mcs0 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs0_Channel1_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS0_CH1_ISR_POS); // Interrupt no. 782
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD1, MCS_CHANNEL1);
    Debug_MCS0_Isr[MCS_CHANNEL1]++;
}

/******************************************************************************
**   Function    : GTM_Mcs0_Channel2_IntHandler
**
**   Description:
**    GTM Mcs0 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs0_Channel2_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS0_CH2_ISR_POS); // Interrupt no. 783
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD1, MCS_CHANNEL2);
    Debug_MCS0_Isr[MCS_CHANNEL2]++;
}


/******************************************************************************
**   Function    : GTM_Mcs0_Channel3_IntHandler
**
**   Description:
**    GTM Mcs0 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs0_Channel3_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS0_CH3_ISR_POS); // Interrupt no. 784
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD1, MCS_CHANNEL3);
    Debug_MCS0_Isr[MCS_CHANNEL3]++;
}

/******************************************************************************
**   Function    : GTM_Mcs0_Channel5_IntHandler
**
**   Description:
**    GTM Mcs0 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs0_Channel5_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS0_CH5_ISR_POS); // Interrupt no. 786
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD1, MCS_CHANNEL5);
    Debug_MCS0_Isr[MCS_CHANNEL5]++;
}

/******************************************************************************
**   Function    : GTM_Mcs0_Channel6_IntHandler
**
**   Description:
**    GTM Mcs0 Channel6 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs0_Channel6_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS0_CH6_ISR_POS); // Interrupt no. 787
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD1, MCS_CHANNEL6);
    Debug_MCS0_Isr[MCS_CHANNEL6]++;
}

/******************************************************************************
**   Function    : GTM_Mcs0_Channel7_IntHandler
**
**   Description:
**    GTM Mcs0 Channel7 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs0_Channel7_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS0_CH7_ISR_POS); // Interrupt no. 788
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD1, MCS_CHANNEL7);
    Debug_MCS0_Isr[MCS_CHANNEL7]++;
}

/******************************************************************************
**   Function    : GTM_Mcs1_Channel0_IntHandler
**
**   Description:
**    GTM Mcs1 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs1_Channel0_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS1_CH0_ISR_POS); // Interrupt no. 789
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD2, MCS_CHANNEL0);
    Debug_MCS1_Isr[MCS_CHANNEL0]++;
}

/******************************************************************************
**   Function    : GTM_Mcs1_Channel1_IntHandler
**
**   Description:
**    GTM Mcs1 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs1_Channel1_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS1_CH1_ISR_POS); // Interrupt no. 790
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD2, MCS_CHANNEL1);
    Debug_MCS1_Isr[MCS_CHANNEL1]++;
}

/******************************************************************************
**   Function    : GTM_Mcs1_Channel2_IntHandler
**
**   Description:
**    GTM Mcs1 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs1_Channel2_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS1_CH2_ISR_POS); // Interrupt no. 791
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD2, MCS_CHANNEL2);
    Debug_MCS1_Isr[MCS_CHANNEL2]++;
}

/******************************************************************************
**   Function    : GTM_Mcs1_Channel3_IntHandler
**
**   Description:
**    GTM Mcs1 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs1_Channel3_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS1_CH3_ISR_POS); // Interrupt no. 792
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD2, MCS_CHANNEL3);
    Debug_MCS1_Isr[MCS_CHANNEL3]++;
}

/******************************************************************************
**   Function    : GTM_Mcs1_Channel5_IntHandler
**
**   Description:
**    GTM Mcs1 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs1_Channel5_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS1_CH5_ISR_POS); // Interrupt no. 794
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD2, MCS_CHANNEL5);
    Debug_MCS1_Isr[MCS_CHANNEL5]++;
}

/******************************************************************************
**   Function    : GTM_Mcs1_Channel6_IntHandler
**
**   Description:
**    GTM Mcs1 Channel6 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs1_Channel6_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS1_CH6_ISR_POS); // Interrupt no. 795
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD2, MCS_CHANNEL6);
    Debug_MCS1_Isr[MCS_CHANNEL6]++;
}

/******************************************************************************
**   Function    : GTM_Mcs1_Channel7_IntHandler
**
**   Description:
**    GTM Mcs1 Channel7 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs1_Channel7_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS1_CH7_ISR_POS); // Interrupt no. 796
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD2, MCS_CHANNEL7);
    Debug_MCS1_Isr[MCS_CHANNEL7]++;
}

/******************************************************************************
**   Function    : GTM_Mcs2_Channel0_IntHandler
**
**   Description:
**    GTM Mcs2 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs2_Channel0_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS2_CH0_ISR_POS); // Interrupt no. 797
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD3, MCS_CHANNEL0);
    Debug_MCS2_Isr[MCS_CHANNEL0]++;
}

/******************************************************************************
**   Function    : GTM_Mcs2_Channel1_IntHandler
**
**   Description:
**    GTM Mcs2 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs2_Channel1_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS2_CH1_ISR_POS); // Interrupt no. 798
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD3, MCS_CHANNEL1);
    Debug_MCS2_Isr[MCS_CHANNEL1]++;
}

/******************************************************************************
**   Function    : GTM_Mcs2_Channel2_IntHandler
**
**   Description:
**    GTM Mcs2 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs2_Channel2_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS2_CH2_ISR_POS); // Interrupt no. 799
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD3, MCS_CHANNEL2);
    Debug_MCS2_Isr[MCS_CHANNEL2]++;
}

/******************************************************************************
**   Function    : GTM_Mcs2_Channel3_IntHandler
**
**   Description:
**    GTM Mcs2 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs2_Channel3_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS2_CH3_ISR_POS); // Interrupt no. 800
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD3, MCS_CHANNEL3);
    Debug_MCS2_Isr[MCS_CHANNEL2]++;
}

/******************************************************************************
**   Function    : GTM_Mcs2_Channel4_IntHandler
**
**   Description:
**    GTM Mcs2 Channel4 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs2_Channel4_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS2_CH4_ISR_POS); // Interrupt no. 801
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD3, MCS_CHANNEL4);
    Debug_MCS2_Isr[MCS_CHANNEL4]++;
}

/******************************************************************************
**   Function    : GTM_Mcs2_Channel5_IntHandler
**
**   Description:
**    GTM Mcs2 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Mcs2_Channel5_IntHandler(void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_MCS2_CH5_ISR_POS); // Interrupt no. 802
#endif

    spc5_gtm_mcs_interrupt_channel_handler(&MCSD3, MCS_CHANNEL5);
    Debug_MCS2_Isr[MCS_CHANNEL5]++;
}

/******************************************************************************
**   Function    : GTM_Atom0_Ch0_Ch1_IntHandler
**
**   Description:
**    GTM Atom0 Ch0 - Ch1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void GTM_Atom0_Ch0_Ch1_IntHandler(void)
{
uint32_T event;

#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(GTM_ATOM0_CH0_1_ISR_POS); // Interrupt no. 837
#endif

    event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM0);

    if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_0) != 0UL) {
        spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL0);
    }

    if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_1) != 0UL) {
        spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL1);
    }

}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/


#pragma ghs endnomisra

#endif // _BUILD_GTM_ 

/****************************************************************************
 ****************************************************************************/
