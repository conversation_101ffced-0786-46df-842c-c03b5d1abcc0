/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
#ifndef _OS_ALARMS_H_
#define _OS_ALARMS_H_
#include "OS_api.h"

#ifdef _OSEK_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/

/*!
\defgroup PublicDefines Public Defines
\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
///This is a public define 
#define OSMAXALLOWEDVALUE_highRateTimer       ((TickType)(0xFFFFFFFFu))
#define OSTICKSPERBASE_highRateTimer          1 

/* #define NULLCOUNTER        0xFF */

#define OSNUMALMS 4u
#define OSNUMCTRS 1u
#define DeclareCounter(CntrName)     CntrName##_CntID

/******************************************************************************/
/******************* ALARM DATA FIELD MACROS *******************************/
#define OSALMTASKID(ctrId)      (OsAlmTable[(ctrId)].TaskId)
#define OSALMCNTRID(ctrId)      (OsAlmTable[(ctrId)].cntrId)
#define OSALMEXPNVAL(ctrId)     (OsAlmTable[(ctrId)].delta)
#define OSALMCYCLE(ctrId)         (OsAlmTable[(ctrId)].cycle)
#define OSALMSTATE(ctrId)         (OsAlmTable[(ctrId)].almState)
#define OSALMACTION(ctrId)      (OsAlmTable[(ctrId)].action)

/******************************************************************************/

/*!\egroup*/
/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
///This is a public typedef 
typedef uint32_T     TickType;
typedef TickType*  TickRefType;        /* OSEK: Reference to counter value */

typedef enum {

    ALM_LOCKED = 0x00,             /* Alarm is occupied by a task  */
    ALM_FREE   = 0x01             /* Alarm is available           */
}
AlarmState;



/* Counter configuration table       */
enum CounterID {
    RateTimer4Ms_CntID = TIMING_HIGH_RATE,
    RateTimer10Ms_CntID = TIMING_MIDDLE_RATE,
    RateTimer100Ms_CntID = TIMING_LOW_RATE,
    RateTimer2Ms_CntID = TIMING_VHIGH_RATE,
    RateTimer1Ms_CntID = 0,
    NULLCOUNTER = 0xFF
};

typedef enum CounterID     CntrType;  


struct TagCTRCfg
{
    TickType        maxallowedvalue; 
    TickType        ticksperbase;    /* conversion constant  */
/*    TickType        mincycle;  */    /* minimum period value for alarm   */
};
typedef struct TagCTRCfg    CTRCfg;  /* Counter configuration table  */

/* Counter control block             */
struct TagCTRCBS                      /* counter control block (node)         */
{                                                                        
/*  Configuration Data  */
     CntrType      CntrID;
   TickType       value;            /* current value of counter */
   const CTRCfg   info;             /* counter properties                   */
};

typedef struct TagCTRCBS     CTRCBS;   /* Counter configuration table  */

/******************************************************************************/

/******************************************************************************/
/******************* ALARM CONFIG BLOCK DATA STRUCTURE *********************/
/* Alarm block structure */

typedef void (*ptrFcn)(void *f);

struct TagAlmCfg
{
    TaskType        TaskId;  /* Task index to be activate  */
    CntrType        cntrId;    /* attached Counter ID  */
    ptrFcn          action;
};
/******************************************************************************/
/******************* ALARM CONTROL BLOCK DATA STRUCTURE ********************/
/* Alarm control block structure */
struct TagAlmCBS
{
    TaskType       TaskId;      /* Task index to be activate  */
    CntrType       cntrId;
    TickType       delta;    /* Alarms' expiration value   */
    TickType       cycle;    /* period value for cyclic alarm    */
    AlarmState     almState;
    ptrFcn         action;

};
/******************************************************************************/
/************************** ALARM TABLES DEFINITION ************************/
typedef struct TagAlmCfg    AlmCfg;           /* Alarm configuration table    */
typedef struct TagAlmCBS    AlmCBS;           /* Alarm control block    */

typedef AlmCBS*             AlarmType;
typedef CTRCfg              AlarmBaseType;

typedef AlarmBaseType*      AlarmBaseRefType;
/*!\egroup*/



extern const AlmCfg   OsAlmCfgTable[OSNUMALMS];
extern const uint8_T   OsAlmCfgCycleTable[OSNUMALMS];

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
StatusType  GetAlarmBase( AlarmType almId, AlarmBaseRefType l_info );
StatusType  GetAlarm( AlarmType almId, TickRefType tick );
StatusType  SetRelAlarm( AlarmType almId, TickType increment, TickType l_cycle );
StatusType  SetAbsAlarm( AlarmType almId, TickType start, TickType l_cycle );
StatusType  CancelAlarm( AlarmType almId );

#endif // _OSEK_


#endif /* _OS_ALARMS_H_ */

/****************************************************************************
 ****************************************************************************/



