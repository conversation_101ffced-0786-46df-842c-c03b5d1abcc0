/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_aru_cfg.h
 * @brief   GTM ARU Driver configuration macros and structures.
 *
 * @addtogroup ARU
 * @{
 */

#include "gtm_aru.h"

#ifndef _GTM_ARU_CFG_H_
#define _GTM_ARU_CFG_H_

#define SPC5_GTM_ARU_IRQ_MODE                    SPC5_GTM_ARU_IRQ_MODE_LEVEL

#define SPC5_GTM_ARU_INT_NEW_DATA_0_ENABLED      FALSE
#define SPC5_GTM_ARU_INT_NEW_DATA_0_CALLBACK     

#define SPC5_GTM_ARU_INT_NEW_DATA_1_ENABLED      FALSE
#define SPC5_GTM_ARU_INT_NEW_DATA_1_CALLBACK     

#define SPC5_GTM_ARU_INT_ACCESS_DONE_ENABLED     FALSE
#define SPC5_GTM_ARU_INT_ACCESS_DONE_CALLBACK    

#endif /* _GTM_ARU_CFG_H_ */

/** @} */
