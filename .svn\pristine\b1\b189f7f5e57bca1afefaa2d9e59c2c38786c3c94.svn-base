/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tim_cfg.c
 * @brief   (GTM-IP) TIM Driver configuration code.
 *
 * @addtogroup TIM
 * @{
 */
#include "gtm_cfg.h"

#if (SPC5_GTM_USE_TIM == TRUE) || defined(__DOXYGEN__)

#include "gtm_tim_cfg.h"

/*===========================================================================*/
/* Driver local definitions.                                                 */
/*===========================================================================*/

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/* **** TIM0 ****  */
GTM_TIM_Channel_Callbacks gtm_tim0_channel0_callbacks = {
        tdn_new_value_cb,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim0_channel1_callbacks = {
        spark_cb_ch0,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim0_channel2_callbacks = {
        spark_cb_ch1,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim0_channel3_callbacks = {
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim0_channel4_callbacks = {
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim0_channel5_callbacks = {
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim0_channel6_callbacks = {
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim0_channel7_callbacks = {
        NULL,
        NULL,
        NULL,
        NULL,
        NULL,
        NULL
};



GTM_TIM_Channel_Callbacks *gtm_tim0_callbacks[SPC5_GTM_TIM_CHANNELS] = {
    &gtm_tim0_channel0_callbacks,
    &gtm_tim0_channel1_callbacks,
    &gtm_tim0_channel2_callbacks,
    &gtm_tim0_channel3_callbacks,
    &gtm_tim0_channel4_callbacks,
    &gtm_tim0_channel5_callbacks,
    &gtm_tim0_channel6_callbacks,
    &gtm_tim0_channel7_callbacks
};


/* **** TIM1 ****  */
GTM_TIM_Channel_Callbacks gtm_tim1_channel0_callbacks = {
        cyl0_edge_cb,
        NULL,
        cyl0_timeout_cb,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim1_channel1_callbacks = {
        cyl1_edge_cb,
        NULL,
        cyl1_timeout_cb,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim1_channel2_callbacks = {
        cyl2_edge_cb,
        NULL,
        cyl2_timeout_cb,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim1_channel3_callbacks = {
        cyl3_edge_cb,
        NULL,
        cyl3_timeout_cb,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim1_channel4_callbacks = {
        cyl4_edge_cb,
        NULL,
        cyl4_timeout_cb,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim1_channel5_callbacks = {
        cyl5_edge_cb,
        NULL,
        cyl5_timeout_cb,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim1_channel6_callbacks = {
        cyl6_edge_cb,
        NULL,
        cyl6_timeout_cb,
        NULL,
        NULL,
        NULL
};
GTM_TIM_Channel_Callbacks gtm_tim1_channel7_callbacks = {
        cyl7_edge_cb,
        NULL,
        cyl7_timeout_cb,
        NULL,
        NULL,
        NULL
};



GTM_TIM_Channel_Callbacks *gtm_tim1_callbacks[SPC5_GTM_TIM_CHANNELS] = {
    &gtm_tim1_channel0_callbacks,
    &gtm_tim1_channel1_callbacks,
    &gtm_tim1_channel2_callbacks,
    &gtm_tim1_channel3_callbacks,
    &gtm_tim1_channel4_callbacks,
    &gtm_tim1_channel5_callbacks,
    &gtm_tim1_channel6_callbacks,
    &gtm_tim1_channel7_callbacks
};


/* **** TIM2 ****  */
GTM_TIM_Channel_Callbacks gtm_tim2_channel0_callbacks = {
		NULL,
		NULL,
		cyl0_epws_timeout_cb,
		NULL,
		NULL,
		NULL
};
GTM_TIM_Channel_Callbacks gtm_tim2_channel1_callbacks = {
		NULL,
		NULL,
		cyl1_epws_timeout_cb,
		NULL,
		NULL,
		NULL
};
GTM_TIM_Channel_Callbacks gtm_tim2_channel2_callbacks = {
		NULL,
		NULL,
		cyl2_epws_timeout_cb,
		NULL,
		NULL,
		NULL
};
GTM_TIM_Channel_Callbacks gtm_tim2_channel3_callbacks = {
		NULL,
		NULL,
		cyl3_epws_timeout_cb,
		NULL,
		NULL,
		NULL
};
GTM_TIM_Channel_Callbacks gtm_tim2_channel4_callbacks = {
		NULL,
		NULL,
		cyl4_epws_timeout_cb,
		NULL,
		NULL,
		NULL
};
GTM_TIM_Channel_Callbacks gtm_tim2_channel5_callbacks = {
		NULL,
		NULL,
		cyl5_epws_timeout_cb,
		NULL,
		NULL,
		NULL
};
GTM_TIM_Channel_Callbacks gtm_tim2_channel6_callbacks = {
		NULL,
		NULL,
		cyl6_epws_timeout_cb,
		NULL,
		NULL,
		NULL
};
GTM_TIM_Channel_Callbacks gtm_tim2_channel7_callbacks = {
		NULL,
		NULL,
		cyl7_epws_timeout_cb,
		NULL,
		NULL,
		NULL
};



GTM_TIM_Channel_Callbacks *gtm_tim2_callbacks[SPC5_GTM_TIM_CHANNELS] = {
	&gtm_tim2_channel0_callbacks,
	&gtm_tim2_channel1_callbacks,
	&gtm_tim2_channel2_callbacks,
	&gtm_tim2_channel3_callbacks,
	&gtm_tim2_channel4_callbacks,
	&gtm_tim2_channel5_callbacks,
	&gtm_tim2_channel6_callbacks,
    &gtm_tim2_channel7_callbacks
};


/* **** TIM3 ****  */
/* **** TIM4 ****  */
/* **** TIM5 ****  */



/*===========================================================================*/
/* Driver local types.                                                       */
/*===========================================================================*/

/*===========================================================================*/
/* Driver local variables.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Driver local functions.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/

#endif /* SPC5_GTM_USE_TIM */

/** @} */
