/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Diag.h
 **  Date:          25-Oct-2022
 **
 **  Model Version: 1.462
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Diag_h_
#define RTW_HEADER_TLE9278BQX_Diag_h_
#ifndef TLE9278BQX_Diag_COMMON_INCLUDES_
# define TLE9278BQX_Diag_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TLE9278BQX_Diag_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  uint32_T CCCodeBlock;                /* '<S10>/C//C++ Code Block' */
  uint32_T CCCodeBlock1;               /* '<S10>/C//C++ Code Block1' */
  uint32_T CCCodeBlock_f;              /* '<S7>/C//C++ Code Block' */
  uint32_T CCCodeBlock1_f;             /* '<S7>/C//C++ Code Block1' */
  uint32_T CCCodeBlock_c;              /* '<S11>/C//C++ Code Block' */
  uint32_T CCCodeBlock1_b;             /* '<S11>/C//C++ Code Block1' */
  uint32_T CCCodeBlock_e;              /* '<S12>/C//C++ Code Block' */
  uint32_T CCCodeBlock1_fs;            /* '<S12>/C//C++ Code Block1' */
  uint16_T CntSBCFault_n;              /* '<S8>/SBC_Diag_Mgm' */
  uint8_T eeCntSbcUV;                  /* '<S12>/Chart_UV' */
  uint8_T eeSbcUV;                     /* '<S12>/Chart_UV' */
  uint8_T CCCodeBlock_k;               /* '<S39>/C//C++ Code Block' */
  uint8_T CCCodeBlock_j;               /* '<S38>/C//C++ Code Block' */
  uint8_T eeCntSbcSC;                  /* '<S11>/Chart_SC' */
  uint8_T eeSbcSC;                     /* '<S11>/Chart_SC' */
  uint8_T CCCodeBlock_g;               /* '<S36>/C//C++ Code Block' */
  uint8_T CCCodeBlock_o;               /* '<S35>/C//C++ Code Block' */
  uint8_T eeCntSbcOT;                  /* '<S10>/Chart_OT' */
  uint8_T eeSbcOT;                     /* '<S10>/Chart_OT' */
  uint8_T CCCodeBlock_er;              /* '<S33>/C//C++ Code Block' */
  uint8_T CCCodeBlock_k2;              /* '<S32>/C//C++ Code Block' */
  uint8_T ptFault;                     /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultWdt;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultCpu;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultGtm;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultSM1;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultSM2;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultSM3;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultSM4;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultSM5;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultSM6;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultSM7;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T ptFaultSM8;                  /* '<S8>/SBC_Diag_Mgm' */
  uint8_T eeCntSbcCAN;                 /* '<S7>/Chart_CAN' */
  uint8_T eeSbcCAN;                    /* '<S7>/Chart_CAN' */
  uint8_T CCCodeBlock_ch;              /* '<S15>/C//C++ Code Block' */
  uint8_T CCCodeBlock_ok;              /* '<S14>/C//C++ Code Block' */
  uint8_T Memory_PreviousInput;        /* '<S9>/Memory' */
  uint8_T wk;                          /* '<S12>/Chart_UV' */
  uint8_T wk_c;                        /* '<S11>/Chart_SC' */
  uint8_T wk_c4;                       /* '<S10>/Chart_OT' */
  uint8_T initDone;                    /* '<S8>/SBC_Diag_Mgm' */
  uint8_T wk_i;                        /* '<S7>/Chart_CAN' */
} DW_TLE9278BQX_Diag_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_TLE9278BQX_Diag_T TLE9278BQX_Diag_DW;

/* Model entry point functions */
extern void TLE9278BQX_Diag_initialize(void);

/* Exported entry point function */
extern void TLE9278BQX_Diag_Bkg(void);

/* Exported entry point function */
extern void TLE9278BQX_Diag_Init(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgSBCICsWrong;         /* '<S8>/SBC_Diag_Mgm' */

/* SBC Component model version wrong */
extern uint8_T FlguCTransient;         /* '<S9>/Logical Operator1' */

/* Transient fault discovered */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S19>/Data Type Duplicate' : Unused code path elimination
 * Block '<S20>/Data Type Duplicate' : Unused code path elimination
 * Block '<S21>/Data Type Duplicate' : Unused code path elimination
 * Block '<S22>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/Data Type Duplicate' : Unused code path elimination
 * Block '<S28>/Data Type Duplicate' : Unused code path elimination
 * Block '<S29>/Data Type Duplicate' : Unused code path elimination
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TLE9278BQX_Diag'
 * '<S1>'   : 'TLE9278BQX_Diag/Model Info'
 * '<S2>'   : 'TLE9278BQX_Diag/TLE9278BQX_Diag'
 * '<S3>'   : 'TLE9278BQX_Diag/TLE9278BQX_Diag/Merger'
 * '<S4>'   : 'TLE9278BQX_Diag/TLE9278BQX_Diag/TP'
 * '<S5>'   : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg'
 * '<S6>'   : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Init'
 * '<S7>'   : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/CAN'
 * '<S8>'   : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag'
 * '<S9>'   : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Latch_Transient'
 * '<S10>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/OT'
 * '<S11>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/SC'
 * '<S12>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/UV'
 * '<S13>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/CAN/Chart_CAN'
 * '<S14>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/CAN/fc_EECntSbcCAN'
 * '<S15>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/CAN/fc_EESbcCAN'
 * '<S16>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/SBC_Diag_Mgm'
 * '<S17>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag1'
 * '<S18>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2'
 * '<S19>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag1/SetDiagState'
 * '<S20>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag1/SetDiagState1'
 * '<S21>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag1/SetDiagState2'
 * '<S22>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag1/SetDiagState3'
 * '<S23>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2/SetDiagState10'
 * '<S24>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2/SetDiagState11'
 * '<S25>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2/SetDiagState4'
 * '<S26>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2/SetDiagState5'
 * '<S27>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2/SetDiagState6'
 * '<S28>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2/SetDiagState7'
 * '<S29>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2/SetDiagState8'
 * '<S30>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/Calc_Diag/Set_Diag2/SetDiagState9'
 * '<S31>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/OT/Chart_OT'
 * '<S32>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/OT/fc_EECntSbcOT'
 * '<S33>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/OT/fc_EESbcOT'
 * '<S34>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/SC/Chart_SC'
 * '<S35>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/SC/fc_EECntSbcSC'
 * '<S36>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/SC/fc_EESbcSC'
 * '<S37>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/UV/Chart_UV'
 * '<S38>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/UV/fc_EECntSbcUV'
 * '<S39>'  : 'TLE9278BQX_Diag/TLE9278BQX_Diag/fc_Bkg/UV/fc_EESbcUV'
 */

/*-
 * Requirements for '<Root>': TLE9278BQX_Diag
 */
#endif                                 /* RTW_HEADER_TLE9278BQX_Diag_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/