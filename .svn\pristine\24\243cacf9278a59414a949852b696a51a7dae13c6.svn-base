/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/


#ifdef _BUILD_VSRAMMGM_

/*! \mainpage VSRAMMGM

\section intro Introduction

VSRAM is a particular region of RAM memory used to store/load data before/then a reset event. Normally, it used
during SW updates to keep trace of communication parameters, errors during flashing operation, diagnostic session
etc etc. 
It is divided in two regions: first one, SHARED region, contains data shared between <PERSON>ot and <PERSON>pp<PERSON>, whereas the second
one, NOT SHARED, contains data used only by boot or by application level.
VSRAMMGM API provides functions and interfaces to manage VSRAM memory region for freescale's microcontrollers
This API is used to exchange information between Application and Boot layers. 
 */


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/

#include "vsrammgm.h"
#include "Vsram_out.h"

/*!
\defgroup PublicVariables Public Variables 
\sgroup
 */
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/// VSRAM internal state (0 in case of no error, -1 otherwise) 
int16_T VsramState;
/*!\egroup*/

/*!
\defgroup PublicFunctions Public Functions 
\sgroup
 */
/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   VSRAMMGM_Update
//
//   Description:  This method updates VSRAM content and checksum  
/*! \brief This method updates VSRAM content and checksum
 */
//
//  Parameters: void
//  Returns: int16_T error code
/*! 
\returns  int16_T error code
 */
//  Notes:        
/*!
No notes
 */
/**************************************************************************/
int16_T VSRAMMGM_Update(void)
{
    int16_T result = NO_ERROR;
    uint32_T newCheckSum;

    VSRAMMGM_StoreToSharedMemory();

    result = VSRAM_ComputeCheckSum(&newCheckSum);

    if (result == NO_ERROR) {
        result = VSRAM_WriteCheckSum(newCheckSum);
    }

    return result;
}

/***************************************************************************/
//   Function    :   VSRAMMGM_Verify
//
//   Description:  This method checks VSRAM checksum and,if correct, loads its content  
/*! \brief This method checks VSRAM checksum and,if correct, loads its content
 */
//
//  Parameters: void
//  Returns: int16_T error code
/*! 
\returns  int16_T error code
 */
//  Notes:        
/*!
No notes
 */
/**************************************************************************/
int16_T VSRAMMGM_Verify(void)
{
    int16_T result = NO_ERROR;
    uint32_T oldCheckSum;
    uint32_T newCheckSum;

    result = VSRAM_ComputeCheckSum(&newCheckSum);

    if (result == NO_ERROR) {
        result = VSRAM_ReadCheckSum(&oldCheckSum);

        if (oldCheckSum != newCheckSum)
        {
            result = VSRAM_CHECKSUM_ERROR;
        }
        else
        {
            VSRAMMGM_LoadFromSharedMemory();
        }
    }
    return result;
}

/***************************************************************************/
//   Function    :   VSRAMMGM_Init
//
//   Description: This method initializes VSRAM content    
/*! \brief This method initializes VSRAM content 
 */
//
//  Parameters: uint32_T vsramInitWord 
//  Returns: int16_T error code
/*!
\param vsramInitWord all VSRAM location shall be initialized to this value
\returns  int16_T error code
 */
//  Notes:        
/*!
No notes
 */
/**************************************************************************/
int16_T VSRAMMGM_Init(uint32_T vsramInitWord)
{
    int16_T result= NO_ERROR;

    result = VSRAM_InitAllMemory(vsramInitWord);

    return result;

}

/*!\egroup*/

#endif  /* _BUILD_VSRAMMGM_ */
