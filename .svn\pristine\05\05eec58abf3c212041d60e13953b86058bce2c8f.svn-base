/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           CoilTimPattern.c
 **  File Creation Date: 19-Jul-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         CoilTimPattern
 **  Model Description:  The aim of this module is to calculate parameters for SSD algorithm and recovery.
 **  Model Version:      1.937
 **  Model Author:       MarottaR - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: MarottaR - Mon Jul 19 12:00:06 2021
 **
 **  Last Saved Modification:  MarottaR - Mon Jul 19 11:52:35 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "CoilTimPattern_out.h"
#include "CoilTimPattern_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/CoilTimPattern_Scheduler' */
#define Co_event_CoilTimPattern_PowerOn (0)
#define CoilT_event_CoilTimPattern_10ms (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_COILTIMPATTERN_DEF      1937U                     /* Referenced by: '<Root>/CoilTimPattern_Scheduler' */

/* ID model version define */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_COILTIMPATTERN_

/* Add a check for each important define */
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_CONST */
static const uint8_T K_CONV_RPM_TO_DEG = 6U;/* Referenced by: '<S5>/K_COMV' */
static const uint16_T K_US = 40000U;   /* Referenced by: '<S5>/K_US' */
static const int16_T MAX_DEGTOUTPULSEIN = 8000;/* Referenced by:
                                                * '<S2>/MAX_DEGTOUTPULSEIN'
                                                * '<S5>/MAX_DEGTOUTPULSEIN'
                                                */
static const uint8_T PAR_25 = 25U;     /* Referenced by: '<S5>/PAR_25' */

/*Init of local calibrations section*/
#pragma ghs section rodata=".calib"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T DBPNMKWINDOW = 50;/* Referenced by:
                                                            * '<S2>/DBPNMKWINDOW'
                                                            * '<S4>/DBPNMKWINDOW'
                                                            */

/* MK Window DB PNMOS */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T DEGTOUTPULSEIN = 0U;
                                      /* Referenced by: '<S5>/DEGTOUTPULSEIN' */

/* Degree of timeout PulseIn */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRLEADDIAG = 13107U;/* Referenced by:
                                                                * '<S2>/THRLEADDIAG'
                                                                * '<S4>/THRLEADDIAG'
                                                                */

/* Primary overcurrent threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TIMDBNPMOS = 70U;/* Referenced by:
                                                            * '<S2>/TIMDBNPMOS'
                                                            * '<S4>/TIMDBNPMOS'
                                                            */

/* NMOS Off to PMOS On Dead band after shot */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TIMDBSHOT = 100U;/* Referenced by:
                                                            * '<S2>/TIMDBSHOT'
                                                            * '<S4>/TIMDBSHOT'
                                                            */

/* Acq Dead band after shot */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TIMPLASAMPLE = 12U;/* Referenced by:
                                                              * '<S2>/TIMPLASAMPLE'
                                                              * '<S4>/TIMPLASAMPLE'
                                                              */

/* High frequency Sample time */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TOUTLASTSLOPE = 2500U;/* Referenced by:
                                                                 * '<S2>/TOUTLASTSLOPE'
                                                                 * '<S4>/TOUTLASTSLOPE'
                                                                 */

/* Buck Slope last timeout */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TOUTPLAON = 2500U;/* Referenced by:
                                                             * '<S2>/TOUTPLAON'
                                                             * '<S4>/TOUTPLAON'
                                                             */

/* Plasma threshold timeout */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TOUTPULSEIN = 2000U;/* Referenced by: '<S5>/TOUTPULSEIN' */

/* Input trigger timeout */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TOUTSHOTOUT = 2800U;/* Referenced by:
                                                               * '<S2>/TOUTSHOTOUT'
                                                               * '<S4>/TOUTSHOTOUT'
                                                               */

/* Shot threshold out timeout */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TOUTSLOPE = 250U;/* Referenced by:
                                                            * '<S2>/TOUTSLOPE'
                                                            * '<S4>/TOUTSLOPE'
                                                            */

/* Buck Slope timeout */
#pragma ghs section rodata=default

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
int16_T DegTOutPulseIn;                /* '<S3>/Merge8' */

/* Timeout pulse in */
int16_T TDbMKW;                        /* '<S3>/Merge9' */

/* Dead band MKW */
uint16_T TDbNPMOS;                     /* '<S3>/Merge2' */

/* Dead band NMOS Off to PMOS On */
uint16_T TDbShot;                      /* '<S3>/Merge1' */

/* Dead band to evaluate Shot */
uint16_T TPlaSample;                   /* '<S3>/Merge' */

/* High frequency current time sampe */
uint16_T ThLeadDiag;                   /* '<S3>/Merge3' */

/* Threshold to primary diagnosis */
uint16_T ToLstSlope;                   /* '<S3>/Merge6' */

/* Buck last slopetimeout */
uint16_T ToPlaOn;                      /* '<S3>/Merge7' */

/* Plasma threshold timeout */
uint16_T ToShotOut;                    /* '<S3>/Merge4' */

/* TimeOut Shot threshold out */
uint16_T ToSlope;                      /* '<S3>/Merge5' */

/* Buck slopetimeout */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_CoilTimPattern;/* '<Root>/CoilTimPattern_Scheduler' */

/* Model Version */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void Coi_chartstep_c3_CoilTimPattern(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/CoilTimPattern_Scheduler' */
static void Coi_chartstep_c3_CoilTimPattern(const int32_T *sfEvent)
{
  uint32_T rtb_Divide2;
  int32_T rtb_DataTypeConversion2;

  /* Chart: '<Root>/CoilTimPattern_Scheduler'
   *
   * Block description for '<Root>/CoilTimPattern_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn and 10ms
   *  task.
   */
  /* Chart: '<Root>/CoilTimPattern_Scheduler' incorporates:
   *  Constant: '<S2>/MAX_DEGTOUTPULSEIN'
   *  Constant: '<S2>/THRLEADDIAG'
   *  Constant: '<S2>/TIMDBNPMOS'
   *  Constant: '<S2>/TIMDBSHOT'
   *  Constant: '<S2>/TIMPLASAMPLE'
   *  Constant: '<S2>/TOUTLASTSLOPE'
   *  Constant: '<S2>/TOUTPLAON'
   *  Constant: '<S2>/TOUTSHOTOUT'
   *  Constant: '<S2>/TOUTSLOPE'
   *  Constant: '<S4>/THRLEADDIAG'
   *  Constant: '<S4>/TIMDBNPMOS'
   *  Constant: '<S4>/TIMDBSHOT'
   *  Constant: '<S4>/TIMPLASAMPLE'
   *  Constant: '<S4>/TOUTLASTSLOPE'
   *  Constant: '<S4>/TOUTPLAON'
   *  Constant: '<S4>/TOUTSHOTOUT'
   *  Constant: '<S4>/TOUTSLOPE'
   *
   * Block description for '<Root>/CoilTimPattern_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn and 10ms
   *  task.
   */
  /* During: CoilTimPattern_Scheduler */
  /* Entry Internal: CoilTimPattern_Scheduler */
  /* Transition: '<S1>:2' */
  if ((*sfEvent) == ((int32_T)Co_event_CoilTimPattern_PowerOn)) {
    /* Outputs for Function Call SubSystem: '<Root>/Init_fcn'
     *
     * Block description for '<Root>/Init_fcn':
     *  This block performs initialization of parameters for SSD and recovery
     *  modality.
     */
    /* MinMax: '<S2>/MinMax14' incorporates:
     *  Constant: '<S2>/DBPNMKWINDOW'
     *  Constant: '<S2>/PLACTRLTIMSAT'
     */
    /* Transition: '<S1>:4' */
    /* Transition: '<S1>:8' */
    /* Event: '<S1>:18' */
    if (DBPNMKWINDOW < PLACTRLTIMSAT) {
      TDbMKW = DBPNMKWINDOW;
    } else {
      TDbMKW = PLACTRLTIMSAT;
    }

    /* End of MinMax: '<S2>/MinMax14' */
    TPlaSample = TIMPLASAMPLE;
    TDbShot = TIMDBSHOT;
    TDbNPMOS = TIMDBNPMOS;
    ThLeadDiag = THRLEADDIAG;
    ToShotOut = TOUTSHOTOUT;
    ToSlope = TOUTSLOPE;
    ToLstSlope = TOUTLASTSLOPE;
    ToPlaOn = TOUTPLAON;
    DegTOutPulseIn = 8000;

    /* End of Outputs for SubSystem: '<Root>/Init_fcn' */
    IdVer_CoilTimPattern = ID_VER_COILTIMPATTERN_DEF;

    /* Transition: '<S1>:12' */
  } else {
    /* Outputs for Function Call SubSystem: '<Root>/fcn_10ms'
     *
     * Block description for '<Root>/fcn_10ms':
     *  This block performs parameters calculation for SSD and recovery
     *  modality for 10ms task.
     */
    /* Product: '<S5>/Divide3' incorporates:
     *  Inport: '<Root>/Rpm'
     *  Product: '<S5>/Product4'
     */
    /* Transition: '<S1>:6' */
    /* CoilTimPattern_10ms */
    /* Transition: '<S1>:13' */
    /* Event: '<S1>:19' */
    rtb_Divide2 = (((uint32_T)Rpm) * 6U) / 25U;

    /* MinMax: '<S5>/MinMax6' */
    if (rtb_Divide2 <= 1U) {
      rtb_Divide2 = 1U;
    }

    /* End of MinMax: '<S5>/MinMax6' */

    /* Product: '<S5>/Divide2' incorporates:
     *  Constant: '<S5>/DEGTOUTPULSEIN'
     *  Product: '<S5>/Product3'
     */
    rtb_Divide2 = (((uint32_T)DEGTOUTPULSEIN) * 40000U) / rtb_Divide2;

    /* DataTypeConversion: '<S5>/Data Type Conversion2' incorporates:
     *  Constant: '<S5>/TOUTPULSEIN'
     */
    rtb_DataTypeConversion2 = (int32_T)((uint32_T)(((uint32_T)TOUTPULSEIN) <<
      ((uint32_T)2)));

    /* MinMax: '<S5>/MinMax4' */
    if (rtb_Divide2 <= ((uint32_T)rtb_DataTypeConversion2)) {
      rtb_Divide2 = (uint32_T)rtb_DataTypeConversion2;
    }

    /* MinMax: '<S5>/MinMax5' incorporates:
     *  MinMax: '<S5>/MinMax4'
     */
    if (rtb_Divide2 >= 32000U) {
      rtb_Divide2 = 32000U;
    }

    /* End of MinMax: '<S5>/MinMax5' */

    /* DataTypeConversion: '<S5>/Data Type Conversion' */
    DegTOutPulseIn = (int16_T)((uint32_T)(rtb_Divide2 >> ((uint32_T)2)));

    /* MinMax: '<S4>/MinMax14' incorporates:
     *  Constant: '<S4>/DBPNMKWINDOW'
     *  Constant: '<S4>/PLACTRLTIMSAT'
     */
    if (DBPNMKWINDOW < PLACTRLTIMSAT) {
      TDbMKW = DBPNMKWINDOW;
    } else {
      TDbMKW = PLACTRLTIMSAT;
    }

    /* End of MinMax: '<S4>/MinMax14' */
    TPlaSample = TIMPLASAMPLE;
    TDbShot = TIMDBSHOT;
    TDbNPMOS = TIMDBNPMOS;
    ThLeadDiag = THRLEADDIAG;
    ToShotOut = TOUTSHOTOUT;
    ToSlope = TOUTSLOPE;
    ToLstSlope = TOUTLASTSLOPE;
    ToPlaOn = TOUTPLAON;

    /* End of Outputs for SubSystem: '<Root>/fcn_10ms' */
  }

  /* End of Chart: '<Root>/CoilTimPattern_Scheduler' */
  /* Transition: '<S1>:15' */
}

/*
 * Output and update for function-call system: '<Root>/CoilTimPattern_Scheduler'
 * Block description for: '<Root>/CoilTimPattern_Scheduler'
 *   Model scheduler. This stateflow manage events of PowerOn and 10ms task.
 */
void CoilTi_CoilTimPattern_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[2];
  int32_T i;

  /* Chart: '<Root>/CoilTimPattern_Scheduler' incorporates:
   *  TriggerPort: '<S1>/input events'
   *
   * Block description for '<Root>/CoilTimPattern_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn and 10ms
   *  task.
   */
  for (i = 0; i < 2; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: CoilTimPattern_Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S1>:16' */
    i = (int32_T)Co_event_CoilTimPattern_PowerOn;
    Coi_chartstep_c3_CoilTimPattern(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S1>:17' */
    i = (int32_T)CoilT_event_CoilTimPattern_10ms;
    Coi_chartstep_c3_CoilTimPattern(&i);
  }
}

/* Model step function */
void CoilTimPattern_10ms(void)
{
  /* Chart: '<Root>/CoilTimPattern_Scheduler'
   *
   * Block description for '<Root>/CoilTimPattern_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn and 10ms
   *  task.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/CoilTimPattern_10ms' */

  /* Chart: '<Root>/CoilTimPattern_Scheduler'
   *
   * Block description for '<Root>/CoilTimPattern_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn and 10ms
   *  task.
   */
  CoilTi_CoilTimPattern_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CoilTimPattern_10ms' */
}

/* Model step function */
void CoilTimPattern_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/CoilTimPattern_PowerOn' incorporates:
   *  Chart: '<Root>/CoilTimPattern_Scheduler'
   *
   * Block description for '<Root>/CoilTimPattern_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn and 10ms
   *  task.
   */

  /* Chart: '<Root>/CoilTimPattern_Scheduler'
   *
   * Block description for '<Root>/CoilTimPattern_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn and 10ms
   *  task.
   */
  CoilTi_CoilTimPattern_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CoilTimPattern_PowerOn' */
}

/* Model initialize function */
void CoilTimPattern_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint16_T TPlaSample;
uint16_T TDbShot;
uint16_T TDbNPMOS;
uint16_T ThLeadDiag;
uint16_T ToShotOut;
uint16_T ToSlope;
uint16_T ToLstSlope;
uint16_T ToPlaOn;
int16_T DegTOutPulseIn;
int16_T TDbMKW;
void CoilTimPattern_Stub(void)
{
  TPlaSample = 0u;
  TDbShot = 0u;
  TDbNPMOS = 0u;
  ThLeadDiag = 0u;
  ToShotOut = 0u;
  ToSlope = 0u;
  ToLstSlope = 0u;
  ToPlaOn = 0u;
  DegTOutPulseIn = 0;
  TDbMKW = 0;
}

void CoilTimPattern_PowerOn(void)
{
  CoilTimPattern_Stub();
}

void CoilTimPattern_10ms(void)
{
  CoilTimPattern_Stub();
}

#endif                                 /* _BUILD_COILTIMPATTERN_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/