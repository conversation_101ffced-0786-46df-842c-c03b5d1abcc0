/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                              */
/* $Revision::        $                                                                                                        */
/* $Date::                                                $                                                                    */
/* $Author::                         $                                                                                         */
/*******************************************************************************************************************************/

/*! \mainpage DiagMgm
 
\section intro Introduction
\brief Functions related to diagnosis

Explain in detail how this module works and what is supposed to do.  
 
*/

/*-----------------------------------*
* INCLUDE FILES
*-----------------------------------*/
#include "diagmgm.h"

#ifdef _BUILD_DIAGMGM_

/*!
\defgroup PublicVariables Public Variables
\brief Global variables (output).

This group contains the variables with a \a global scope that are exported for the other modules
They represent the \a output of the module, according with the SW architecture.

\sgroup
*/
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/// Array of punctual faults. Cells contain the fault type.
typPtFault  PtFault[DIAG_NUMBER];
uint8_T TestStatus[DIAG_NUMBER]; // TEST_NOT_COMPLETED, TEST_COMPLETED
uint8_T TestResult[DIAG_NUMBER]; // NO_RESULT, FAILED, PASSED
uint8_T UpdateFailedOCCnt[DIAG_NUMBER]; // 0-> Keep old value, 1-> Increment Failed OCs counter
uint8_T ActiveFault[ACTIVEFAULT_DIM + 1u];
/// Array of diagnostic line id that are in fault storage
uint8_T StoredDiag[DIAG_FAULT_LENGTH];
/// Array of symptom of the diagnostic line in the corresponding index of StoredDiag
uint8_T StoredFault[DIAG_FAULT_LENGTH];
/// Index of StoredFault/StoredDiag arrays
uint8_T StoredDiagIdx;

uint8_T TValidKnockCyl = 0u;
uint16_T IntIonMeanCyl = 0u;

uint8_T IdActFault = 0u;

uint8_T VtSMDiagCode[N_VT_SM_DIAG_CODE];

#ifdef SAVE_ENV_DATA_EE 
#if (SAVE_ENV_DATA_EE == 1)
uint8_T SnapshotType[DIAG_NUMBER] = 
{
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_T_AIR               0 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_T_WATER             1 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_LOAD                2 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_RPM                 3 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_CAMLEVEL            4 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_FLGBANKSEL          5 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_VBATTERY            6 */
    0u,                                                   /*  DIAG_DUMMY               7 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_ADC                 8 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_CH_A            9 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_CH_B           10 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_0              11 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_1              12 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_2              13 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_3              14 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_4              15 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_5              16 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_6              17 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_7              18 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_VBAT_CIRCUIT       19 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_COIL_0             20 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_COIL_1             21 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_COIL_2             22 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_COIL_3             23 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_COIL_4             24 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_COIL_5             25 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_COIL_6             26 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_COIL_7             27 */
    0u,                                                   /*  DIAG_ELDOR_SW           28 */
    BASE | SAFETY,                                        /*  DIAG_WDT                29 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_PRI_A              30 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_PRI_B              31 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_LIVENESS           32 */
    0u,                                                   /*  DIAG_BANK_MISF          33 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_TEMP_ECU_1         34 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_TEMP_ECU_2         35 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_PRIVATE_CAN        36 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_CAN_NODE_1         37 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_CPU                38 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_EEPROM             39 */
    BASE | KNOCK,                                         /*  DIAG_KNOCK_COH_0        40 */
    BASE | KNOCK,                                         /*  DIAG_KNOCK_COH_1        41 */
    BASE | KNOCK,                                         /*  DIAG_KNOCK_COH_2        42 */
    BASE | KNOCK,                                         /*  DIAG_KNOCK_COH_3        43 */
    BASE | KNOCK,                                         /*  DIAG_KNOCK_COH_4        44 */
    BASE | KNOCK,                                         /*  DIAG_KNOCK_COH_5        45 */
    BASE | KNOCK,                                         /*  DIAG_KNOCK_COH_6        46 */
    BASE | KNOCK,                                         /*  DIAG_KNOCK_COH_7        47 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_SYNC               48 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_TEMP_ECU_3         49 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SPARK_EV_A         50 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SPARK_EV_B         51 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_BUCK_A             52 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_BUCK_B             53 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_TRIGGER_0          54 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_TRIGGER_1          55 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_TRIGGER_2          56 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_TRIGGER_3          57 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_TRIGGER_4          58 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_TRIGGER_5          59 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_TRIGGER_6          60 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_TRIGGER_7          61 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_OL_0            62 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_OL_1            63 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_OL_2            64 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_OL_3            65 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_OL_4            66 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_OL_5            67 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_OL_6            68 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_OL_7            69 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_CAN_NODE_OVER_RUN  70 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_VEHICLE_CAN        71 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_0_4            72 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_1_5            73 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_2_6            74 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_SEC_3_7            75 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_VCAP_0_4           76 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_VCAP_1_5           77 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_VCAP_2_6           78 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_VCAP_3_7           79 */
    0u,                                                   /*  DIAG_IGN                80 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_VCOIL_A_MON        81 */
    BASE | COIL_PRI_BUCK_TRIG,                            /*  DIAG_VCOIL_B_MON        82 */
    0u,                                                   /*  DIAG_FREE_83            83 */
    0u,                                                   /*  DIAG_FREE_84            84 */
    0u,                                                   /*  DIAG_BOARD_SEL          85 */
    0u,                                                   /*  DIAG_FREE_86            86 */
    0u,                                                   /*  DIAG_FREE_87            87 */
    BASE | CAN_LVNSS_TEMPECU,                             /*  DIAG_KEYSIGNAL          88 */
    BASE | SAFETY,                                        /*  DIAG_GTM                89 */
    BASE | SAFETY,                                        /*  DIAG_TLE9278BQX         90 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_CH_A           91 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_CH_B           92 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_CH_C           93 */
    BASE | ION_SEC_SPARK_CAP,                             /*  DIAG_ION_CH_D           94 */
    0u                                                    /*  DIAG_FREE_95            95 */
};
#endif
#endif


/* Following vars should be externalized in a _out.h file!!! */
extern uint8_T FlgAnyAbsentPri;
extern int16_T EETempECUMax1;
extern int16_T EETempECUMax2;
extern int16_T EETempECUMax3;
extern uint16_T CntNoSyncNOsts;
extern uint16_T ChPeakCyl;
extern int16_T DThPeakCyl;
extern int16_T VtIShotPeak[N_CYL_MAX];
extern uint8_T VtRonLevel[20];
extern uint16_T VtTSparkFilt[N_CYL_MAX];

/*!\egroup*/


/*!
\defgroup PrivateVariables Private Variables
\brief Variables with module scope

\sgroup
*/
/*-----------------------------------*
 * PRIVATE VARIABLE DEFINITIONS
 *-----------------------------------*/
/// ?

/*!\egroup*/
/*!
\defgroup CCPTestPoint CCP TestPoint\brief Test points that can be read via CCP

These variables can be read from CCP tool. For this reason they don't have 
the static attribute even if they are private because otherwise it couldn't be possible to access them from CCP tool.
A fake label (CCPTEST is defined as: "#define CCPTEST ") is therefore added to  
remember the private nature of them even if they could be used everywhere in the code.
\sgroup
*/
/*-----------------------------------*
 * CCP TESTPOINT DEFINITIONS
 *-----------------------------------*/

/// Flag to disable a diagnosis when the battery voltage is out of range
static CCPTEST boolean_T FlgDisDiagVBat = FALSE;

static CCPTEST uint32_T IntIonMean[N_CYL_MAX];
static CCPTEST uint16_T CntIntIonMean[N_CYL_MAX];
static CCPTEST uint64_T timerValidKnockCyl[N_CYL_MAX];
static CCPTEST uint8_T stdiag_kcoh[N_CYL_MAX];
static CCPTEST uint16_T pCode_oldest;
static CCPTEST uint8_T ftb_oldest;
static CCPTEST uint8_T old_WUCycleVal = WUC_NOT_ENDED;
/*!\egroup*/

/*!
\defgroup PublicFunctions Public Functions 
\sgroup
*/
/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   DiagMgm_Init
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void DiagMgm_Init(void)
{
    uint8_T i,k;

    AbsCntWUC++;

    for(i=0 ; i < DIAG_NUMBER; i++)
    {
        PtFault[i] = NO_PT_FAULT;
        if ((VTDIAGENABLE[i] == ENABLE_RESET_PWON) || (VTDIAGENABLE[i] == ENABLE_RESET_IGNON) ||
            (VTDIAGENABLE[i] == DIAG_DISABLED))
        {
            StDiag[i] = NO_FAULT;
            DiagCntEE[i] = 0; //MCUS-105, if VTDIAGENABLE[i] == 1, DiagCntEE[i] keeps stored value, otherwise it is resetted
        }

        /* First time */
        DTCStatusEE[i].BF.testFailed = 0u;
        DTCStatusEE[i].BF.testFailedThisOperationCycle = 0u;
        DTCStatusEE[i].BF.testNotCompletedThisOperationCycle = 1u;
    }

#if 0
    memset(TestStatus,DIAG_TEST_INIT,sizeof(TestStatus));
    memset(TestResult,NO_RESULT,sizeof(TestResult));
    memset(UpdateFailedOCCnt, 0,sizeof(UpdateFailedOCCnt));
    memset(ActiveFault,255,sizeof(ActiveFault));
#endif

    for (uint8_T j = 0U; j < sizeof(TestStatus); j++){
        TestStatus[j] = DIAG_TEST_INIT;
    }

    for (uint8_T j = 0U; j < sizeof(TestResult); j++){
        TestResult[j] = NO_RESULT;
    }

    for (uint8_T j = 0U; j < sizeof(UpdateFailedOCCnt); j++){
        UpdateFailedOCCnt[j] = 0u;
    }

        for (uint8_T j = 0U; j < sizeof(ActiveFault); j++){
        ActiveFault[j] = 255u;
    }

    // Reset index of ActiveFault
    IdActFault = 0u;

    for(k=0 ; k < DIAG_FAULT_LENGTH; k++)
    {
        /* Reset StoredDiag */
        StoredDiag[k] = NO_STORED_DIAG;
        /* Reset StoredFault */
        StoredFault[k] = 255u;
    }

    StoredDiagIdx = 0u;
    old_WUCycleVal = WUC_NOT_ENDED;

} // end DiagMgm_Init()

/***************************************************************************/
//   Function    :   DiagMgm_T10ms
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void DiagMgm_T10ms(void)
{
    static uint8_T stecu_old = ECU_PWR_ON;
    static uint8_T stign_old = IGN_OFF;
    static uint8_T oldForceResetDiag = 0u;
    uint8_T id;
    uint8_T *stDiag;
    
    for (id = 0u; id < DIAG_NUMBER; id++)
    {
        if ((VTFORCEPTFAULT[id] != 0xFFu) && (FORCEPTFAULT10MS != 0u))
        {
            DiagMgm_SetDiagStateForce10MS(id, VTFORCEPTFAULT[id], &stDiag);
        }
        else { /* MISRA */ }
    }
    
    if ((VBatteryF < THINFVBATDDIAG) || (VBatteryF > THSUPVBATDDIAG))
    {
        FlgDisDiagVBat = TRUE;
    }
    else
    {
        FlgDisDiagVBat = FALSE;
    }

    /* DEVALIDAZIONE ERRORE */
    if ((StEcu == ECU_PWR_LATCH) && (stecu_old == ECU_SERVICE_OFF))
    {
        DiagMgm_OC_Ended();
    }
    else
    {
        /* DO NOTHING */
    }
    if ((StIgn == IGN_ON) && (stign_old != IGN_ON))
    {
        DiagMgm_ResetDiag();
    }
    else
    {
        /* DO NOTHING */
    }
    
    stecu_old = StEcu;
    stign_old = StIgn;

#ifdef _BUILD_WDT_SBC_
    if (Rpm <= FORCERESETDIAGTHR)
    {
        if((FORCERESETDIAG != 0u) && (oldForceResetDiag == 0u))
        {
            WDT_SetPendingOperation(WDT_PENDING_DIAG_CLEAR_BYCAL);
        }
        oldForceResetDiag = FORCERESETDIAG;
    }


#endif
}


/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void DiagMgm_100ms(void)
{
    uint8_T id;

    if ((FlgDongle == 1) && (CntNoDiagDongle == 0) && (DisDiagKWP1 == 1))
    {
        /* Dongle presente AND Timeout dal key-on trascorso AND LDD abilitato da KWP2000 */
        for (id=0; (id < DIAG_NUMBER); id++)
        {
            if ((StDiag[id] != NO_FAULT) /*&& (VTDISDIAGDONGLE[id] != 0)*/)
            {
                /* Diagnosi id validata o in validazione AND LDD abilitato da calibrazione */
                DiagMgm_ResetOneDiag(id);
            }
            else
            {
                /* RESET DIAGNOSI NON CONSENTITO */
            }
        }
    }
    else
    {
        /* RESET DIAGNOSI NON CONSENTITO */
    }
}

/***************************************************************************/
//   Function    :   DiagMgm_ResetOneDiag
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param id: diagmnostic line identifier 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void DiagMgm_ResetOneDiag(uint8_T id)
{
    PtFault[id] = NO_PT_FAULT;
    StDiag[id] = NO_FAULT;
#ifdef _BUILD_DTC_
    resetDTCStatus(id);
#endif
}

/***************************************************************************/
//   Function    :   DiagMgm_ResetOneDiagIgnOffOn
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param id: diagmnostic line identifier 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void DiagMgm_ResetOneDiagIgnOffOn(uint8_T id)
{
    PtFault[id] = NO_PT_FAULT;
    StDiag[id] = NO_FAULT;
        /* AM T.B.D. */
#if 0
    resetDTCStatusIgnOffOn(id);
#endif
}



/***************************************************************************/
//   Function    :   DiagMgm_SetDiagState
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void DiagMgm_SetDiagState(uint8_T id, typPtFault fault, uint8_T *state)
{
    uint8_T   j;
    uint8_T   vtautodiagdisable = 0u;
    uint8_T   dtcSetting = 1u;

#ifdef _BUILD_DIAGCANMGM_
    dtcSetting =  getEnDTCSetting();
#endif

    if (dtcSetting == 1u)//enable by default
    {
        if (id < DIAG_NUMBER)
        {
            vtautodiagdisable = 0u;

            if ((VTFORCEPTFAULT[id] != 0xFFu) && (FORCEPTFAULT10MS != 0u))
            {
                /* PtFault forced every 10ms periodicity */
            }
            else if ((StEcu == ECU_ON) || (StEcu == ECU_PWR_ON) || (StEcu == ECU_SERVICE_OFF))
            {
                if ((VTFORCEPTFAULT[id] != 0xFFu) && (FORCEPTFAULT10MS == 0u))
                {
                    /* PtFault forced with nominal task periodicity */
                    PtFault[id] = VTFORCEPTFAULT[id];
                }
                else
                {
                    /* No PtFault forcing */
                    PtFault[id] = fault;
                }

                for (j = 0u; ((j < TBDISDIAG_CULS) && (vtautodiagdisable == 0u) && (TBDISDIAG[id][j] != 255u)) ;j++)
                {
                    if (TBDISDIAG[id][j] < DIAG_NUMBER)
                    {
                        if (DiagCntEE[TBDISDIAG[id][j]] > 0u)
                        {
                            vtautodiagdisable = StDiag[TBDISDIAG[id][j]];
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == FICT_DIAG_NUMBER)
                    {
                        if (FlgDisDiagVBat == TRUE)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 1u))
                    {
                        if (FlgEOL == 1u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 2u))
                    {
                        if (Rpm < THRPMDIAG)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 3u))
                    {
                        if (FlgDisDiagCAN != 0u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 4u))
                    {
                        if((BEngineFuelCutoffStatusCAN != 0u) || (DesCutOffCAN != 0u))
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 5u))
                    {
                        if (KeyEnDiagSignal == 0u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
#ifdef WATER_DETECTION_EN
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 6u))
                    {
                        if (WaterDetected != 0u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
#endif
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 7u))
                    {
                        if (IgnitionCutOffDx != 0u)
                        {
                            vtautodiagdisable = FAULT_FILTERING;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 8u))
                    {
                        if (IgnitionCutOffSx != 0u)
                        {
                            vtautodiagdisable = FAULT_FILTERING;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
#ifdef _BUILD_ACTIVE_DIAG_
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 9u))
                    {
                        if((Ign0.Ign_Flag_Busy != FREE)
                         ||(Ign1.Ign_Flag_Busy != FREE)
                         ||(Ign2.Ign_Flag_Busy != FREE)
                         ||(Ign3.Ign_Flag_Busy != FREE)
                         ||(Ign4.Ign_Flag_Busy != FREE)
                         ||(Ign5.Ign_Flag_Busy != FREE)
#if (N_CYLINDER == 8u)
                         ||(Ign6.Ign_Flag_Busy != FREE)
                         ||(Ign7.Ign_Flag_Busy != FREE)
#endif
                           )
                        {
                            vtautodiagdisable = FAULT_FILTERING;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
#endif
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 10u))
                    {
                        if (VtSelCIONOL[0] != 0u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 11u))
                    {
                        if (VtSelCIONOL[1] != 0u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 12u))
                    {
                        if (VtSelCIONOL[2] != 0u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 13u))
                    {
                        if (VtSelCIONOL[3] != 0u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 14u))
                    {
                        if (ActivePhaseSearch != 0u)
                        {
                            vtautodiagdisable = FAULT;
                        }
                        else
                        {
                            vtautodiagdisable = NO_FAULT;
                        }
                    }
                    else
                    {
                        /* Nothing */
                    }
                }

                if (VTDIAGENABLE[id] != DIAG_DISABLED)
                {
                    if (vtautodiagdisable == NO_FAULT)
                    {
                        // Automa parallelo test UDS
                        UpdateTestResult(id, PtFault[id]);
                    }
                    else
                    {
                        /* Freeze Diagnosis. */
                    }
                }
                else
                {
                    StDiag[id] = NO_FAULT;
#ifdef _BUILD_DTC_
                    resetDTCStatus(id);
#endif
                }

                // fine automa StDiag
                *state = StDiag[id];

            }  
            else 
            {
                *state = NO_FAULT;
            }
        }
        else
        {
            *state = NO_FAULT;
        }
    }
    else
    {
        /* DO NOTHING, normal DTC settings has been disabled using $85$02 KWP; re-enable it using $85$01*/
    }
} // DiagMgm_SetDiagState


/***************************************************************************/
//   Function    :   DiagMgm_RangeCheck_S16
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void DiagMgm_RangeCheck_S16(typPtFault *fault, int16_T input, int16_T min_input, int16_T max_input, int16_T med_lo_input, int16_T med_hi_input,
                                       typPtFault min_error, typPtFault max_error, typPtFault med_error, uint8_T STR, uint8_T DIAG)
{
    uint8_T st_diag;
    
    switch (STR)
    {
        case THR_D_HL: /* Validation Fault bidirectional */
        {
            if (input < min_input) 
            {  
                *fault = min_error;
                DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
            }
            else if (input > max_input)  
            {  
                *fault = max_error;
                DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
            }
            else if ((med_lo_input < input) && (input < med_hi_input))  
            {  
                *fault = med_error;
                DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
            }
            else
            {
                *fault = NO_PT_FAULT;
                DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
            }
        }
        break;

        case THR_D_L: /* Only LOW and OL Fault validation */
        {
            if (input < min_input) 
            {  
                *fault = min_error;
                DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
            }
            else if ((med_lo_input < input) && (input < med_hi_input))  
            {  
                *fault = med_error;
                DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
            }
            else
            {
                if (PtFault[DIAG] == max_error)
                {
                    *fault = max_error;
                }
                else
                {
                    *fault = NO_PT_FAULT;
                    DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
                }
            }
        }
        break;

        case THR_D_H: /* Only HI and OL Fault validation */
        {
            if (input > max_input)  
            {  
                *fault = max_error;
                DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
            }
            else if ((med_lo_input < input) && (input < med_hi_input))  
            {  
                *fault = med_error;
                DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
            }
            else
            {
                if (PtFault[DIAG] == min_error)
                {
                    *fault = min_error;
                }
                else
                {
                    *fault = NO_PT_FAULT;
                    DiagMgm_SetDiagState(DIAG, *fault, &st_diag);
                }
            }
        }
        break;

        default:
        {
            *fault = 255u;
        }
        break;
    }
}


/***************************************************************************/
//   Function    :   DiagMgm_EOA
//
//   Description:    
/*! \brief Diag management at EOA
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void DiagMgm_EOA(void)
{
    uint64_T diagmgmabstime, mstime;
    uint8_T  diag_id;
    typStDiag local_diag;

    /* Decodifica iso-StDiag */
    if (stdiag_kcoh[IonAbsTdcEOA] == NO_FAULT)
    {
        /* NO_FAULT */
        if(FlgCntKnockCohInc[IonAbsTdcEOA] != 0u)
        {
            /* NO_FAULT --> FAULT */
            stdiag_kcoh[IonAbsTdcEOA] = FAULT;
            TValidKnockCyl = 0u;
            IntIonMeanCyl = IntIon[IonAbsTdcEOA];

#ifdef _BUILD_KNOCKCORRNOM_
            /* Gestione diagnosi */
            if(CntKnockCohEE[IonAbsTdcEOA] >= THRCNTKNOCKCOH)
            {
                /* CHIAMATA MACCHINA DI DIAGNOSI */
                diag_id = DIAG_KNOCK_COH_0 + IonAbsTdcEOA;
                DiagMgm_SetDiagState(diag_id, SIGNAL_INVALID, &local_diag);
            }
            else
            {
                /* DO NOTHING */
            }
#endif
        }
        else
        {
            if(KCohDiagCnt[IonAbsTdcEOA] > 0u)
            {
                /* NO_FAULT --> FAULT_FILTERING */
                stdiag_kcoh[IonAbsTdcEOA] = FAULT_FILTERING;
                TIMING_GetAbsTimer(&(timerValidKnockCyl[IonAbsTdcEOA]));
                IntIonMean[IonAbsTdcEOA] = IntIonMean[IonAbsTdcEOA] + IntIon[IonAbsTdcEOA];
                CntIntIonMean[IonAbsTdcEOA] = 1u;
            }
            else
            {
                /* DO NOTHING */
            }
        }
    }
    else if (stdiag_kcoh[IonAbsTdcEOA] == FAULT_FILTERING)
    {
        /* FAULT_FILTERING */
        if(FlgCntKnockCohInc[IonAbsTdcEOA] != 0u)
        {
            /* FAULT_FILTERING --> FAULT */
            stdiag_kcoh[IonAbsTdcEOA] = FAULT;
            TIMING_GetAbsTimer(&(diagmgmabstime));
            TIMING_TicksToMilliSeconds((diagmgmabstime - timerValidKnockCyl[IonAbsTdcEOA]), &(mstime));
            mstime = mstime/100u; /* RES 0.1s */
            if(mstime > MAX_uint8_T)
            {
                TValidKnockCyl = MAX_uint8_T;
            }
            else
            {
                TValidKnockCyl = (uint8_T)mstime;
            }
            
            if(CntIntIonMean[IonAbsTdcEOA] < MAX_uint16_T)
            {
                IntIonMean[IonAbsTdcEOA] = IntIonMean[IonAbsTdcEOA] + IntIon[IonAbsTdcEOA];
                CntIntIonMean[IonAbsTdcEOA] = CntIntIonMean[IonAbsTdcEOA] + 1u;
            }
            else
            {
                /* DO NOTHING */
            }
            IntIonMeanCyl = (uint16_T)IntIonMean[IonAbsTdcEOA]/CntIntIonMean[IonAbsTdcEOA];

#ifdef _BUILD_KNOCKCORRNOM_
            /* Gestione diagnosi */
            if(CntKnockCohEE[IonAbsTdcEOA] >= THRCNTKNOCKCOH)
            {
                /* CHIAMATA MACCHINA DI DIAGNOSI */
                diag_id = DIAG_KNOCK_COH_0 + IonAbsTdcEOA;
                DiagMgm_SetDiagState(diag_id, SIGNAL_INVALID, &local_diag);
            }
            else
            {
                /* DO NOTHING */
            }
#endif
        }
        else
        {
            if(KCohDiagCnt[IonAbsTdcEOA] > 0u)
            {
                /* FAULT_FILTERING --> FAULT_FILTERING */
                if(CntIntIonMean[IonAbsTdcEOA] < MAX_uint16_T)
                {
                    IntIonMean[IonAbsTdcEOA] = IntIonMean[IonAbsTdcEOA] + IntIon[IonAbsTdcEOA];
                    CntIntIonMean[IonAbsTdcEOA] = CntIntIonMean[IonAbsTdcEOA] + 1u;
                }
                else
                {
                    /* DO NOTHING */
                }
            }
            else
            {
                /* FAULT_FILTERING --> NO_FAULT */
                stdiag_kcoh[IonAbsTdcEOA] = NO_FAULT;
                timerValidKnockCyl[IonAbsTdcEOA] = 0u;
                IntIonMean[IonAbsTdcEOA] = 0u;
                CntIntIonMean[IonAbsTdcEOA] = 0u;
            }
        }
    }
    else
    {
        /* FAULT */
        /* DO NOTHING */
    }
}


/***************************************************************************/
//   Function    :   EraseFaultsScanTool
//
//   Description:    
/*! \brief EraseFaults
*/
//
//  Parameters and Returns:
/*! 
\param 
\returns uint16_T
 -1 error
  0 no error
*/
//  Notes:        
/*!
Resets message signals
*/
/**************************************************************************/
int16_T EraseFaultsScanTool(void)
{
    int16_T retval = NO_ERROR;
    int16_T res;
    uint8_T i;
    
    EEMGM_SetEventID(EE_INVALIDATE_DIAG);
    res = EEMGM_EETaskCmd();

    if ((res != NO_ERROR) && (res != EE_ID_UNAVAILABLE)) //check error
    {
        retval = -1;
    }
    else
    {
        /* Resetting DTC Status to its default value */
#ifdef _BUILD_DTC_
        for(i = 0u; i < DIAG_NUMBER; i++)
        {
            resetDTCStatus(i);
        }
#endif
        /* Reset Chrono Stack and EventCounterEE line-by-line */
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
#ifdef SAVE_ENV_DATA_EE 
#if (SAVE_ENV_DATA_EE == 1u)
            memset((void*)&(DTCSnapshotEE_1[i]), 0, sizeof(DTCSnapshotEE_1[i]));
            memset((void*)&(DTCSnapshotEE_2[i]), 0, sizeof(DTCSnapshotEE_2[i]));
            /* Reset EventCounterEE */
            memset((void*)&(EventCounterEE[i]), 0, sizeof(uint8_T));
#endif
#endif
            /* Reset StoredDiag */
            StoredDiag[i] = 255u;
            /* Reset StoredFault */
            StoredFault[i] = 255u;
        }
        /* Reset StoredDiag/StoredFault index */
        StoredDiagIdx = 0u;
        memset(ActiveFault,255,sizeof(ActiveFault));
        // Reset index of ActiveFault
        IdActFault = 0u;
        retval = NO_ERROR;
    }
    return retval;
}


/*==================================================================================================
                                       PRIVATE FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   DiagMgm_OC_Ended
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
static void DiagMgm_OC_Ended(void)
{
uint8_T i;
uint8_T k;

    for (i = 0; i < DIAG_NUMBER; i++)
    {
        if (DTCStatusEE[i].BF.testFailedThisOperationCycle == 0u)
        {
            /* TestFailedThisOC[i] has been set one time at least */
            if (FailedOCCntEE[i] > 0u) 
            {
                FailedOCCntEE[i]--;
            }
            else
            {
                /* MISRA 14.10 */
            }

            /* 
            EISB-609 
            For Ferrari projects confirmedDTC bit shall be reset after 40 warmup cycles with no fault found 
            */
#if 0
            /* Reset confirmedDTC if FailedOCCntEE = 0 for the requested diagnostic line */
            if (FailedOCCntEE[i] == 0u)
            {
                if (DTCStatusEE[i].BF.confirmedDTC == 1u)
                {
                    DTCStatusEE[i].BF.confirmedDTC = 0u;
                }
                else
                {
                    /* MISRA 14.10 */
                }
            }
            else
            {
                /* MISRA 14.10 */
            }
#endif
            /* Reset pendingDTC if testFailedThisOperationCycle = 0 for the requested diagnostic line */
            if (DTCStatusEE[i].BF.pendingDTC == 1u)
            {
                DTCStatusEE[i].BF.pendingDTC = 0u;
            }
            else
            {
                /* MISRA 14.10 */
            }

            if((DTCStatusEE[i].BF.pendingDTC == 0u) && (DTCStatusEE[i].BF.confirmedDTC == 0u) && (DTCStatusEE[i].BF.testFailedSinceLastClear == 0u))
            {
                //resetDTCStatus(i); MCUS-105, counters shall not be resetted but stored in the error memory
            }
        }
        else
        {
            /* MISRA 14.10 */
        }
    }

    if(WarmingUpCycleCAN == WUC_ENDED)
    {
        if(old_WUCycleVal == WUC_NOT_ENDED)
        {
            WUC_ended();
            old_WUCycleVal = WUC_ENDED;
        }
        else
        {
            //nothing
        }
    }
    else
    {
        //nothing
    }

}

/***************************************************************************/
//   Function    :   WUC_ended
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
static void WUC_ended(void)
{
uint8_T k;
#ifdef SAVE_ENV_DATA_EE 
#if (SAVE_ENV_DATA_EE == 1u)
    /* EventCounter management */
    for (k = 0; k < DIAG_FAULT_LENGTH; k++)
    {
        /* Scanning 1st Chrono Stack */
        if(DTCStatusEE[DTCSnapshotEE_1[k].diagIdx].BF.testFailed == 0u)
        {
            if(EventCounterEE[k] > 0u)
            {
                EventCounterEE[k] = EventCounterEE[k] - 1u;

                DTCExtendedEE[k].EventCounter = EventCounterEE[k];

                if (EventCounterEE[k] == 0u)
                {
                    /* Removing 'saved' snapshot from 1st Chrono Stack*/
                    memset((void*)&(DTCSnapshotEE_1[k]), 0, sizeof(DTCSnapshotEE_1[k]));
                    /* Removing 'saved' snapshot from 2nd Chrono Stack*/
                    memset((void*)&(DTCSnapshotEE_2[k]), 0, sizeof(DTCSnapshotEE_2[k]));
                    /* Removing 'saved' extended from Chrono Stack*/
                    memset((void*)&(DTCExtendedEE[k]), 0, sizeof(DTCExtendedEE[k]));
                }
                else
                {
                    /* MISRA 14.10 - NOTHING TO DO */
                }
            }
             else
            {
                /* MISRA 14.10 - NOTHING TO DO */
            }
        }
        else
        {
            /* MISRA 14.10 - NOTHING TO DO */
        }
    }
#endif
#endif
}

/***************************************************************************/
//   Function    :   DiagMgm_ResetDiag
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
static void DiagMgm_ResetDiag(void)
{
#if 0
    uint8_T id; 
    /* AM TBD */
    for(id = 0; id < DIAG_NUMBER; id++)
    {
        if (VTDIAGENABLE[id] == ENABLE_RESET_IGNON)
        {
            PtFault[id] = NO_PT_FAULT;
            StDiag[id] = NO_FAULT;

            resetDTCStatusIgnOffOn(id);
        }
        else
        {
          //nothing;
        }
    }
#endif
}

/***************************************************************************/
//   Function    :   UpdateTestResult
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: diag_Id, diagnostic line identifier 
\param param2: fault, detected fault type
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
static void UpdateTestResult(uint8_T diag_Id, typPtFault fault)
{
    uint8_T j, actdiagFound;
    
    switch(TestStatus[diag_Id])
    {
        case DIAG_TEST_INIT:
        {
            if (fault != NO_PT_FAULT)
            {
                DiagCntEE[diag_Id] += VTSTEPINCFAULT[diag_Id];
                if (DiagCntEE[diag_Id] >= VTTHRCONFFAULT[diag_Id])
                {
                    if ((StoredDiagPresent(diag_Id) == 0u) && (StDiag[diag_Id] != FAULT))
                    {
                        StoredDiag[StoredDiagIdx] = diag_Id;
                        StoredFault[StoredDiagIdx] = fault;
                        StoredDiagIdx++;
                        if(StoredDiagIdx >= DIAG_FAULT_LENGTH)
                        {
                            StoredDiagIdx = 0u;
                        }
                    }
                    confirmFail(diag_Id);
//                    DtcSymptomEE[diag_Id] = J2012FailureConverter(fault);
                    DtcSymptomEE[diag_Id] = fault;
                    StDiag[diag_Id] = FAULT;
                }
                else
                {
                    // Filtering
                    TestStatus[diag_Id] = DIAG_TEST_FILTERING;
                    StDiag[diag_Id] = FAULT_FILTERING;
                }

                actdiagFound = 0u;
                for (j=0u;((j<=ACTIVEFAULT_DIM) && (actdiagFound == 0u));j++)
                {
                    if (ActiveFault[j] == diag_Id)
                    {
                        actdiagFound = 1;
                    }
                }
                if (actdiagFound==0)
                {
                    ActiveFault[IdActFault] = diag_Id;
                    if (IdActFault < ACTIVEFAULT_DIM)
                    {
                        IdActFault++;
                    }
                    else
                    {
                        IdActFault = 0;
                    }
                }
            }
            else
            {
                DiagCntEE[diag_Id] -= VTSTEPDECFAULT[diag_Id];
                if (DiagCntEE[diag_Id] <= VTTHRCONFPASSED[diag_Id])
                {
                    confirmPassed(diag_Id);
                    StDiag[diag_Id] = NO_FAULT;
                }
                else
                {
                    // Filtering 
                    TestStatus[diag_Id] = DIAG_TEST_FILTERING;
                }
            }
        }
        break;

        case DIAG_TEST_FILTERING:
        {
            if (fault != NO_PT_FAULT)
            {
                if (DiagCntEE[diag_Id] < 0)
                {
                    DiagCntEE[diag_Id] = VTSTEPINCFAULT[diag_Id];
                }
                else
                {
                    DiagCntEE[diag_Id] += VTSTEPINCFAULT[diag_Id];
                }

                if (DiagCntEE[diag_Id] >= VTTHRCONFFAULT[diag_Id])
                {
                    if ((StoredDiagPresent(diag_Id) == 0u) && (StDiag[diag_Id] != FAULT))
                    {
                        StoredDiag[StoredDiagIdx] = diag_Id;
                        StoredFault[StoredDiagIdx] = fault;
                        StoredDiagIdx++;
                        if(StoredDiagIdx >= DIAG_FAULT_LENGTH)
                        {
                            StoredDiagIdx = 0u;
                        }
                    }
                    confirmFail(diag_Id);
//                    DtcSymptomEE[diag_Id] = J2012FailureConverter(fault);
                    DtcSymptomEE[diag_Id] = fault;
                    StDiag[diag_Id] = FAULT;
                }
                else
                {
                    /* Filtering */
                    StDiag[diag_Id] = FAULT_FILTERING;
                }
                actdiagFound = 0u;
                for (j=0u;((j<=ACTIVEFAULT_DIM) && (actdiagFound == 0u));j++)
                {
                    if (ActiveFault[j] == diag_Id)
                    {
                        actdiagFound = 1;
                    }
                }
                if (actdiagFound==0)
                {
                    ActiveFault[IdActFault] = diag_Id;
                    if (IdActFault < ACTIVEFAULT_DIM)
                    {
                        IdActFault++;
                    }
                    else
                    {
                        IdActFault = 0;
                    }
                }
            }
            else
            {
                DiagCntEE[diag_Id] -= VTSTEPDECFAULT[diag_Id];
                if (DiagCntEE[diag_Id] <= VTTHRCONFPASSED[diag_Id])
                {
                    confirmPassed(diag_Id);
                    StDiag[diag_Id] = NO_FAULT;
                }
                else
                {
                    /* Filtering */
                }
            }
        }
        break;

        case DIAG_TEST_FINISHED:
        {
            if (fault != NO_PT_FAULT)
            {
                /* Fault counter update */
                if (DiagCntEE[diag_Id] < 0)
                {
                    DiagCntEE[diag_Id] = VTSTEPINCFAULT[diag_Id];
                }
                else
                {
                    DiagCntEE[diag_Id] += VTSTEPINCFAULT[diag_Id];
                }

                if (DiagCntEE[diag_Id] >= VTTHRCONFFAULT[diag_Id])
                {
                    DiagCntEE[diag_Id] = VTTHRCONFFAULT[diag_Id];
                    if (TestResult[diag_Id] == PASSED)
                    {
                        confirmFail(diag_Id);
//                        DtcSymptomEE[diag_Id] = J2012FailureConverter(fault);
                        DtcSymptomEE[diag_Id] = fault;
                    }
                    else { /* MISRA */ }
                    if ((StoredDiagPresent(diag_Id) == 0u) && (StDiag[diag_Id] != FAULT))
                    {
                        StoredDiag[StoredDiagIdx] = diag_Id;
                        StoredFault[StoredDiagIdx] = fault;
                        StoredDiagIdx++;
                        if(StoredDiagIdx >= DIAG_FAULT_LENGTH)
                        {
                            StoredDiagIdx = 0u;
                        }
                    }
                    StDiag[diag_Id] = FAULT;
                }
                else
                {
                    if (StDiag[diag_Id] == NO_FAULT)
                    {
                        StDiag[diag_Id] = FAULT_FILTERING;
                    }
                    else { /* MISRA */ }
                }
                actdiagFound = 0u;
                for (j=0u;((j<=ACTIVEFAULT_DIM) && (actdiagFound == 0u));j++)
                {
                    if (ActiveFault[j] == diag_Id)
                    {
                        actdiagFound = 1;
                    }
                }
                if (actdiagFound==0)
                {
                    ActiveFault[IdActFault] = diag_Id;
                    if (IdActFault < ACTIVEFAULT_DIM)
                    {
                        IdActFault++;
                    }
                    else
                    {
                        IdActFault = 0;
                    }
                }
            }
            else
            {
                DiagCntEE[diag_Id] -= VTSTEPDECFAULT[diag_Id];
                if (DiagCntEE[diag_Id] <= VTTHRCONFPASSED[diag_Id])
                {
                    DiagCntEE[diag_Id] = VTTHRCONFPASSED[diag_Id];
                    if (TestResult[diag_Id] == FAILED)
                    {
                        confirmPassed(diag_Id);
                    }
                    else { /* MISRA */ }
                    StDiag[diag_Id] = NO_FAULT;
                }
                else
                {
                    /* Filtering */
                }
            }
        }
        break;
        
        default:
        {
            /* None */
        }
        break;
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void confirmFail(uint8_T diag_Id)
{
    uint16_T tmp_failedcnt;
    
    DiagCntEE[diag_Id] = VTTHRCONFFAULT[diag_Id];
    if(UpdateFailedOCCnt[diag_Id] == 0u)
    {
        /* Gestione FailedOCCntEE */
        if (FailedOCCntEE[diag_Id] < CONFDTCTHR)
        {
            tmp_failedcnt = FailedOCCntEE[diag_Id] + CONFDTCINC;
            
            if(tmp_failedcnt < (uint16_T)CONFDTCTHR)
            {
                FailedOCCntEE[diag_Id] = (uint8_T)tmp_failedcnt;
            }
            else
            {
                FailedOCCntEE[diag_Id] = CONFDTCTHR;
            }
        }
        else { /* MISRA */ }
        UpdateFailedOCCnt[diag_Id] = 1u; //increment shall be done only one time for OC
    }

    TestStatus[diag_Id] = DIAG_TEST_FINISHED;
    TestResult[diag_Id] = FAILED;

    /* Gestione testFailed, bit0 */
    DTCStatusEE[diag_Id].BF.testFailed = 1u;

    /* Gestione testFailedThisOperationCycle, bit1 */
    DTCStatusEE[diag_Id].BF.testFailedThisOperationCycle = 1u;

    /* Gestione pendingDTC, bit2 */
    DTCStatusEE[diag_Id].BF.pendingDTC = 1u;

    /* Gestione ConfirmedDTC, bit3 */
    if (FailedOCCntEE[diag_Id] >= CONFDTCTHR)
    {
        DTCStatusEE[diag_Id].BF.confirmedDTC = 1u;
#ifdef SAVE_ENV_DATA_EE 
#if (SAVE_ENV_DATA_EE == 1u)
        Fault_Storing(diag_Id, PtFault[diag_Id]);
#endif
#endif
}

    /* Gestione testNotCompletedSinceLastClear, bit4 */
    DTCStatusEE[diag_Id].BF.testNotCompletedSinceLastClear = 0u;

    /* Gestione testFailedSinceLastClear, bit5 */
    DTCStatusEE[diag_Id].BF.testFailedSinceLastClear = 1u;

    /* Gestione testNotCompletedThisOC, bit6 */
    DTCStatusEE[diag_Id].BF.testNotCompletedThisOperationCycle = 0u;

    /* Gestione warningIndicatorRequested, bit7 */
    DTCStatusEE[diag_Id].BF.warningIndicatorRequested = 0u;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void confirmPassed(uint8_T diag_Id)
{
    TestStatus[diag_Id] = DIAG_TEST_FINISHED;
    TestResult[diag_Id] = PASSED;
    DiagCntEE[diag_Id] = VTTHRCONFPASSED[diag_Id];

    /* Gestione testFailed, bit0 */
    DTCStatusEE[diag_Id].BF.testFailed = 0u;

    /* Gestione testFailedThisOperationCycle, bit1 */
    /* UNCHANGED */

    /* Gestione pendingDTC, bit2 */
    /* UNCHANGED */

    /* Gestione confirmedDTC, bit3 */
    /* UNCHANGED */

    /* Gestione testNotCompletedSinceLastClear, bit4 */
    DTCStatusEE[diag_Id].BF.testNotCompletedSinceLastClear = 0u;

    /* Gestione testFailedSinceLastClear, bit5 */
    /* UNCHANGED */

    /* Gestione testNotCompletedThisOC, bit6 */
    DTCStatusEE[diag_Id].BF.testNotCompletedThisOperationCycle = 0u;

    /* Gestione warningIndicatorRequested, bit7 */
    DTCStatusEE[diag_Id].BF.warningIndicatorRequested = 0u;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void DiagMgm_SetDiagStateForce10MS(uint8_T id, uint8_T fault, uint8_T *state)
{
    uint8_T   j;
    uint8_T   vtautodiagdisable = 0u;

    PtFault[id] = fault;
    
    for (j = 0u; ((j < TBDISDIAG_CULS) && (vtautodiagdisable == 0u) && (TBDISDIAG[id][j] != 255u)); j++)
    {
        if (TBDISDIAG[id][j] < DIAG_NUMBER)
        {
            vtautodiagdisable = StDiag[TBDISDIAG[id][j]];
        }
        else if (TBDISDIAG[id][j] == FICT_DIAG_NUMBER)
        {
            if (FlgDisDiagVBat == TRUE)
            {
                vtautodiagdisable = FAULT;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
        else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 1u))
        {
            if (FlgEOL == 1u)
            {
                vtautodiagdisable = FAULT;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
        else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 2u))
        {
            if (Rpm < THRPMDIAG)
            {
                vtautodiagdisable = FAULT;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
        else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 3u))
        {
            if (FlgDisDiagCAN != 0u)
            {
                vtautodiagdisable = FAULT;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
        else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 4u))
        {
            if (BEngineFuelCutoffStatusCAN != 0u)
            {
                vtautodiagdisable = FAULT;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
        else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 5u))
        {
            if (KeyEnDiagSignal == 0u)
            {
                vtautodiagdisable = FAULT;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
#ifdef WATER_DETECTION_EN
        else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 6u))
        {
            if (WaterDetected != 0u)
            {
                vtautodiagdisable = FAULT;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
#endif

        else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 7u))
        {
            if (IgnitionCutOffDx != 0u)
            {
                vtautodiagdisable = FAULT_FILTERING;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
        else if (TBDISDIAG[id][j] == (FICT_DIAG_NUMBER + 8u))
        {
            if (IgnitionCutOffSx != 0u)
            {
                vtautodiagdisable = FAULT_FILTERING;
            }
            else
            {
                vtautodiagdisable = NO_FAULT;
            }
        }
        else
        {
            /* Nothing */
        }
    }

    if (VTDIAGENABLE[id] != DIAG_DISABLED)
    {
        if (vtautodiagdisable == NO_FAULT)
        {
            // Automa parallelo test UDS
            UpdateTestResult(id, PtFault[id]);
        }
        else
        {
            if (vtautodiagdisable == FAULT_FILTERING)
            {
                /* Freeze Diagnosis. */
            }
            else
            {
                StDiag[id] = NO_FAULT;
            }
        }
    }
    else
    {
        StDiag[id] = NO_FAULT;
    }

    // fine automa StDiag
    *state = StDiag[id];
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static uint8_T StoredDiagPresent(uint8_T diag_Id)
{
uint8_T res = 0u;
uint8_T i = 0u;

    for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
    {
        if (StoredDiag[i] == diag_Id)
        {
            res = 1u; // diag_Id already stored into StoredDiag
        }
    }

    return (res);
}
#ifdef SAVE_ENV_DATA_EE 
#if (SAVE_ENV_DATA_EE == 1u)
/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
static void Fault_Storing(uint8_T diag_Id, typPtFault fault)
{
uint8_T i;
int8_T dtc_found = -1;
uint8_T flg_first_found = 0;
int8_T  replace = -2;
int8_T  free = -2;
uint16_T diagnosis;
uint16_T diagnosis_stored;
typPtFault fault_stored;
uint8_T index;
uint8_T severity;
uint8_T oldConfFTB;
static uint8_T UpdateSnapNum;



    diagnosis = DIAGNOSTICLINES[diag_Id][0];
    severity = DTCSEVERITY[diag_Id];

    oldConfFTB = getConfirmedFTB(DIAGNOSTICLINES[diag_Id][0],fault);

    if ((DTCStatusEE[diag_Id].BF.testFailed == 1u) && (DTCStatusEE[diag_Id].BF.confirmedDTC == 1u) && (oldConfFTB == 0u))
    {
        UpdateSnapNum = SNAP1;
    }
    else if((DTCStatusEE[diag_Id].BF.testFailed == 1u) && (DTCStatusEE[diag_Id].BF.confirmedDTC == 1u) && (oldConfFTB == 1u))
    {
        UpdateSnapNum = SNAP2;
    }
    else
    {
        /* MISRA 14.10 */
    }

    if(diagnosis != NULL_DTC)
    {
        for (i = 0; i < DIAG_FAULT_LENGTH; i++)
        {

            if(UpdateSnapNum == SNAP1)
            {
                diagnosis_stored = DTCSnapshotEE_1[i].pcode;
                fault_stored = DTCSnapshotEE_1[i].fault;
            }
            else if (UpdateSnapNum == SNAP2)
            {
                diagnosis_stored = DTCSnapshotEE_2[i].pcode;
                fault_stored = DTCSnapshotEE_2[i].fault;
            }
            else
            {
                /* MISRA 14.10 */
            }

            if ((diagnosis == diagnosis_stored) && (fault_stored == fault))                                                        // Update error cell - es_1
            {

                dtc_found = i;
                //replace = -1;
                //free = -1;

            }
            else
            {
                if((diagnosis_stored == NULL_DTC) && (!flg_first_found))                             // insert mew DTC in an empty cell - es_2
                {
                    free = i;
                    flg_first_found = 1;
                }
                else
                {
                    /* The Stored Faults are all present? */
                    if (CheckStoredFaults(UpdateSnapNum) == TRUE)
                    {
                        /* New DTC is fit for Safety ?*/
                        if (severity == CHKI) // YES Severity Mask == 0x80
                        {
                            /* Are there errors with priority <= than new fault? */
                            if(CheckSeverity(severity, MINOR_OR_EQUAL, UpdateSnapNum) == TRUE)
                            {
                                /* Are there errors with priority < than new fault? */
                                if(CheckSeverity(severity, MINOR, UpdateSnapNum) == TRUE)
                                {
                                    /* Overwrite the oldest of lowest priority cell */
                                    replace = FindOldSnapWithLowSeverity(UpdateSnapNum,&pCode_oldest,&ftb_oldest);                        // at least one fault has lower priority - es_5
                                }
                                else
                                {
                                    /* Overwrite the oldest cell */
                                    replace = FindOldestSnap(UpdateSnapNum,&pCode_oldest,&ftb_oldest);                                     // all stored faults have same priority -  es_4
                                }
                            }
                            else
                            {
                                /* Do not store fault */                                            // all stored faults have higher priority - es_3
                            }
                        }
                        else // NO Severity Mask != 0x80
                        {
                            /* Are there errors with priority < than new fault? */
                            if(CheckSeverity(severity, MINOR, UpdateSnapNum) == TRUE)
                            {
                                /* Overwrite the oldest of lowest priority cell */
                                replace = FindOldSnapWithLowSeverity(UpdateSnapNum, &pCode_oldest, &ftb_oldest);                            // at least one fault has lower priority - es_7
                            }
                            else
                            {
                                /* Do not store fault */                                            //All stored faults have same or higher priority - es_6
                            }
                        }
                    }
                    else // Stored faults ARE NOT all present
                    {
                        /* New DTC is fit for Safety ?*/
                        if (severity == CHKI) // YES Severity Mask == 0x80
                        {
                            /* Overwrite the oldest of lowest priority among not present errors */
                            replace = FindOldSnapWithLowSeverity_NotPresent(UpdateSnapNum, &pCode_oldest,&ftb_oldest);                     // es_10
                        }
                        else // NO Severity Mask != 0x80
                        {
                            if(CheckStoredSafetyFaults(UpdateSnapNum) == TRUE)
                            {
                                /* Do not store fault */                                            // es_8
                            }
                            else
                            {
                                /* Overwrite the oldest of lowest priority among not present errors */
                                replace = FindOldSnapWithLowSeverity_NotPresent(UpdateSnapNum, &pCode_oldest,&ftb_oldest);                 // es_9
                            }
                        }
                    }
                }
            }
        }

        /* New DTC Storing or old DTC update */
        if(dtc_found >= 0) // dtc found
        {
            //type = DTCTYPE[diag_Id];
            index = diag_Id;
            //if(EventCounterEE[dtc_found] != THRDIAGWUC)
            //{
                //Freezed Frame Data has to be updated the first
                //time the error is present, if it was not present.
                ScaledDiagData(dtc_found, index, diagnosis, fault, UpdateSnapNum);
            //}
            //else
            //{
                //nothing;
            //}
//            DiagflgId[dtc_found].diagId = diag_Id;
//            DiagflgId[dtc_found].flag = DIAG_ON;
//            DRVC_runningMgm(dtc_found);
//            WUC_runningMgm(dtc_found);
//            StoredDiag[dtc_found] = diag_Id;
//            StoredFault[dtc_found] = fault;
        }
        else if(free >= 0)                   /* free location found */
        {
            /* New DTC storing operations */
            //type = DTCTYPE[diag_Id];
            index = diag_Id;
//            DiagDataFault[free].value = l_dtcCode;
            ScaledDiagData(free, index, diagnosis, fault, UpdateSnapNum);
//            DiagflgId[free].diagId = diag_Id;
//            DiagflgId[free].flag = DIAG_ON;
//            StoredDiag[free] = diag_Id;
//            StoredFault[free] = fault;
//            DRVC_runningMgm(free);
//            WUC_runningMgm(free);
        }
        else if (replace >= 0)     /* replacing a not active fault */ 
        {
            /* Replacing oldest DTC with the New one  */
            //type = DTCTYPE[diag_Id];
            index = diag_Id;
//            DiagDataFault[replace].value = l_dtcCode;
            ScaledDiagData(replace, index, diagnosis, fault, UpdateSnapNum);
//            DiagflgId[replace].diagId = diag_Id;
//            DiagflgId[replace].flag = DIAG_ON;
//            StoredDiag[replace] = diag_Id;
//            StoredFault[replace] = fault;
//            DRVC_runningMgm(replace);
//            WUC_runningMgm(replace);
        }
        else
        {
            /*nothing*/
        }
    }
    else
    {
        /* nothing */
    }

    /* Save actual value of the confirmedDTC status bit for the next Driving Cycle */
    //for(i = 0u; i < DIAG_NUMBER; i++)
    //{
    //    confirmedDTC_oldEE[i] = (uint8_T)DTCStatusEE[i].BF.confirmedDTC;
    //}

    setConfirmedFTB(DIAGNOSTICLINES[diag_Id][0],fault,(uint8_T)DTCStatusEE[diag_Id].BF.confirmedDTC);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * Implementation notes:
 * pos = DTCSnapshotEE_x array index
 * diagIdx = diagnostic line index DIAGNOSTICLINES table
 * pCode = diagnostic line fault,
 * pFault = fault type
 *--------------------------------------------------------------------------*/
static void ScaledDiagData(uint8_T pos, uint8_T diagIndex, uint16_T pCode, typPtFault pFault, uint8_T snapNum)
{
uint8_T offset_snapdata = 0u;
uint8_T snapType;
uint8_T i;

    /* Update the error cells */
    if(snapNum == SNAP1)
    {
        DTCSnapshotEE_1[pos].pcode                  = pCode;
        DTCSnapshotEE_1[pos].fault                  = pFault;
        DTCSnapshotEE_1[pos].diagIdx                = diagIndex;
        DTCSnapshotEE_1[pos].timestamp              = SecondsWorkTime;
        DTCSnapshotEE_1[pos].severity               = DTCSEVERITY[diagIndex];
        DTCSnapshotEE_1[pos].diagSts.CF             = DTCStatusEE[DTCSnapshotEE_1[pos].diagIdx].CF;
        snapType                                    = SnapshotType[DTCSnapshotEE_1[pos].diagIdx];
    }
    else if (snapNum == SNAP2)
    {
        DTCSnapshotEE_2[pos].pcode                  = pCode;
        DTCSnapshotEE_2[pos].fault                  = pFault;
        DTCSnapshotEE_2[pos].diagIdx                = diagIndex;
        DTCSnapshotEE_2[pos].timestamp              = SecondsWorkTime;
        DTCSnapshotEE_2[pos].severity               = DTCSEVERITY[diagIndex];
        DTCSnapshotEE_2[pos].diagSts.CF             = DTCStatusEE[DTCSnapshotEE_2[pos].diagIdx].CF;
        snapType                                    = SnapshotType[DTCSnapshotEE_2[pos].diagIdx];
    }
    else {/* MISRA 14.10 */}

    if((snapType & BASE) == BASE)
    {
        DTC_Base_T DTC_Base;
        /// array container of VtRonLevel samples for diagnosis purposes (samples: VtRonLevel (CntRonRun-1), VtRonLevel (CntRonRun-2), VtRonLevel (CntRonRun-3) and VtRonLevel (CntRonRun-4))
        uint8_T diagVtRonLevel[RONSAMPLESNUM];
        
        DTC_Base.EcuTimeStamps               = ECUTimeStamps;
        DTC_Base.EcuTimeStampsFromKeyON      = ECUTimeStampsFromKeyOn;
        DTC_Base.KeyOnCnt                    = AbsCntWUC;
        //DTC_Base.dtcSts                      = DTCStatusEE[diagIndex];
        DTC_Base.Hour                        = ((uint8_T)(Hour1 << 4u))|Hour2;
        DTC_Base.Minutes                     = ((uint8_T)(Minute1 << 4u))|Minute2;
        DTC_Base.Day                         = ((uint8_T)(Day1 << 4u))|Day2;
        DTC_Base.Month                       = ((uint8_T)(Month1 << 4u))|Month2;
        DTC_Base.Year                        = ((uint8_T)(Year3 << 4u))|Year4;
        DTC_Base.RpmCalc                     = RpmCalc;
        DTC_Base.RpmCAN                      = __KwpConv_Rpm(RpmCAN);
        DTC_Base.Load                        = __KwpConv_Load(Load);
        DTC_Base.TotOdometerCAN              = __KwpConv_TotOdometer(TotOdometerCAN);
        DTC_Base.TWater                      = __KwpConv_TWater(TWater);
        DTC_Base.TAir                        = __KwpConv_TAir(TAir);
        DTC_Base.TempECU                     = __KwpConv_TempECU(TempECU);
        DTC_Base.VBattery                    = __KwpConv_VBattery(VBattery);
        DTC_Base.GasPos                      = __KwpConv_GasPos(GasPos);
        DTC_Base.DCTState                    = DCTState;
        DTC_Base.ILeadObj                    = __KwpConv_ILeadObj(ILeadObj);
        DTC_Base.IPriCorrCyl                 = __KwpConv_IPriCorrCyl(IPriCorrCyl);
        DTC_Base.SAout                       = __KwpConv_SAout(SAout);
        DTC_Base.VehSpeed                    = __KwpConv_VehSpeed(VehSpeed);
        DTC_Base.Seconds1stRunTime           = ((SecondsFirstRunTime > 0xFFFFu) ? 0xFFFFu : SecondsFirstRunTime);
        DTC_Base.Snap6002.BF.RONLevelEE      = RonLevelEE;
        getRONValFromCircBuff(VtRonLevel, diagVtRonLevel, CntRonRun);
        DTC_Base.Snap6002.BF.RONLevel_1      = diagVtRonLevel[3];
        DTC_Base.Snap6002.BF.RONLevel_2      = diagVtRonLevel[2];
        DTC_Base.Snap6002.BF.RONLevel_3      = diagVtRonLevel[1];
        DTC_Base.Snap6002.BF.RONLevel_4      = diagVtRonLevel[0];
        DTC_Base.Snap6003.BF.ResetType       = ResetType;
        DTC_Base.Snap6003.BF.StPlasObj       = StPlasObj;
        DTC_Base.Snap6003.BF.KeySignal       = KeySignal;
        DTC_Base.Snap6003.BF.FlgBankSel      = FlgBankSel;
        DTC_Base.Snap6003.BF.GearPos         = GearPos;
        DTC_Base.EESACmdInLevErrNoMAX        = EESACmdInLevErrNoMax;
        DTC_Base.EESACmdInLevErrSum          = EESACmdInLevErrSum;

        /* moving DTC_Base into snapshot data array */
        if (snapNum == SNAP1)
        {
            memcpy((void *)&DTCSnapshotEE_1[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Base, sizeof(DTC_Base));
        }
        else if (snapNum == SNAP2)
        {
            memcpy((void *)&DTCSnapshotEE_2[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Base, sizeof(DTC_Base));
        }
        else {/* MISRA 14.10 */}

        offset_snapdata += sizeof(DTC_Base);

    }

    if((snapType & CAN_LVNSS_TEMPECU) == CAN_LVNSS_TEMPECU)
    {
        DTC_Can_Lvnss_Temp_T DTC_Can_Lvnss_Temp;
        
        DTC_Can_Lvnss_Temp.FlgAnyAbsentPri                = FlgAnyAbsentPri;
        DTC_Can_Lvnss_Temp.FlgAnyAbsentVeh                = FlgAnyAbsentVeh;
        DTC_Can_Lvnss_Temp.VVFdbkLiveness                 = __KwpConv_VVFdbkLiveness(VVFdbkLiveness);
        DTC_Can_Lvnss_Temp.EETempECUMax1                  = __KwpConv_EETempECUMax(EETempECUMax1);
        DTC_Can_Lvnss_Temp.EETempECUMax2                  = __KwpConv_EETempECUMax(EETempECUMax2);
        DTC_Can_Lvnss_Temp.EETempECUMax3                  = __KwpConv_EETempECUMax(EETempECUMax3);
        DTC_Can_Lvnss_Temp.VVTempECU1                     = __KwpConv_VVTempECU(VTempECU1);
        DTC_Can_Lvnss_Temp.VVTempECU2                     = __KwpConv_VVTempECU(VTempECU2);
        DTC_Can_Lvnss_Temp.VVTempECU3                     = __KwpConv_VVTempECU(VTempECU3);
        DTC_Can_Lvnss_Temp.CntRpmCAN                      = CntRpmCAN;
        DTC_Can_Lvnss_Temp.CntNoSyncNOsts                 = CntNoSyncNOsts;
        DTC_Can_Lvnss_Temp.LoadSx                         = __KwpConv_Load(LoadSxCAN);
        DTC_Can_Lvnss_Temp.LoadDx                         = __KwpConv_Load(LoadDxCAN);
        DTC_Can_Lvnss_Temp.Snap6005.BF.VDLoadSxCAN       = VDLoadSxCAN;
        DTC_Can_Lvnss_Temp.Snap6005.BF.VDLoadDxCAN       = VDLoadDxCAN;
        DTC_Can_Lvnss_Temp.Snap6005.BF.VDRpmCAN          = VDRpmCAN;
        DTC_Can_Lvnss_Temp.Snap6005.BF.VDTAirCAN         = VDTAirCAN;
        DTC_Can_Lvnss_Temp.Snap6005.BF.VDTWaterCAN       = VDTWaterCAN;
        DTC_Can_Lvnss_Temp.Snap6005.BF.VDGasPosCAN       = VDGasPosCAN;
        DTC_Can_Lvnss_Temp.Snap6005.BF.FlgSyncPhased     = FlgSyncPhased;
        DTC_Can_Lvnss_Temp.Snap6007.BF.NTeethDeleted     = ((NTeethDeleted > 7u) ? 7u : NTeethDeleted);
        DTC_Can_Lvnss_Temp.Snap6007.BF.StSync            = (StSync > 3u) ? 3u : StSync;
        DTC_Can_Lvnss_Temp.Snap6007.BF.LastSyncError     = (LastSyncError > 7u) ? 7u : LastSyncError;
        DTC_Can_Lvnss_Temp.VBankSel                      = __KwpConv_VBankSel(VBankSel);
        DTC_Can_Lvnss_Temp.cntForceResyncSAout           = CntForceResyncSAout;
        DTC_Can_Lvnss_Temp.SAoutCyl_0                    = SAoutCyl[0];
        DTC_Can_Lvnss_Temp.SAoutCyl_1                    = SAoutCyl[1];
        DTC_Can_Lvnss_Temp.SAoutCyl_2                    = SAoutCyl[2];
        DTC_Can_Lvnss_Temp.SAoutCyl_3                    = SAoutCyl[3];
        DTC_Can_Lvnss_Temp.SAoutCyl_4                    = SAoutCyl[4];
        DTC_Can_Lvnss_Temp.SAoutCyl_5                    = SAoutCyl[5];
        DTC_Can_Lvnss_Temp.SAoutCyl_6                    = SAoutCyl[6];
        DTC_Can_Lvnss_Temp.SAoutCyl_7                    = SAoutCyl[7];
        DTC_Can_Lvnss_Temp.VtIonKnockEnableCond_0        = VtIonKnockEnableCond[0];
        DTC_Can_Lvnss_Temp.VtIonKnockEnableCond_1        = VtIonKnockEnableCond[1];
        DTC_Can_Lvnss_Temp.VtIonKnockEnableCond_2        = VtIonKnockEnableCond[2];
        DTC_Can_Lvnss_Temp.VtIonKnockEnableCond_3        = VtIonKnockEnableCond[3];
        DTC_Can_Lvnss_Temp.VtIonKnockEnableCond_4        = VtIonKnockEnableCond[4];
        DTC_Can_Lvnss_Temp.VtIonKnockEnableCond_5        = VtIonKnockEnableCond[5];
        DTC_Can_Lvnss_Temp.VtIonKnockEnableCond_6        = VtIonKnockEnableCond[6];
        DTC_Can_Lvnss_Temp.VtIonKnockEnableCond_7        = VtIonKnockEnableCond[7];

        /* moving DTC_Can_Lvnss_Temp into snapshot data array */
        if (snapNum == SNAP1)
        {
            memcpy((void *)&DTCSnapshotEE_1[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Can_Lvnss_Temp, sizeof(DTC_Can_Lvnss_Temp));
        }
        else if (snapNum == SNAP2)
        {
            memcpy((void *)&DTCSnapshotEE_2[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Can_Lvnss_Temp, sizeof(DTC_Can_Lvnss_Temp));
        }
        else {/* MISRA 14.10 */}

        offset_snapdata += sizeof(DTC_Can_Lvnss_Temp);
    }
    else 
    {
        /* MISRA 14.10 */
    }

    if((snapType & ION_SEC_SPARK_CAP) == ION_SEC_SPARK_CAP)
    {
        DTC_Ion_Sec_Spark_Cap_T DTC_Ion_Sec_Spark_Cap;
        
        DTC_Ion_Sec_Spark_Cap.ThPeakCyl                      = __KwpConv_ThPeakCyl(ThPeakCyl);
        DTC_Ion_Sec_Spark_Cap.ChPeakCyl                      = __KwpConv_ChPeakCyl(ChPeakCyl);
        DTC_Ion_Sec_Spark_Cap.DThPeakCyl                     = __KwpConv_DThPeakCyl(DThPeakCyl);
        DTC_Ion_Sec_Spark_Cap.DwellIntCyl                    = __KwpConv_DwellIntCyl(DwellIntCyl);
        DTC_Ion_Sec_Spark_Cap.Start_ionCyl                   = Start_ionCyl;
        DTC_Ion_Sec_Spark_Cap.StartChIonCyl                  = StartChIonCyl;
        DTC_Ion_Sec_Spark_Cap.Snap6004.BF.IonSelectCyl      = IonSelectCyl;
        DTC_Ion_Sec_Spark_Cap.VCharge_0                      = __KwpConv_VCharge(VCharge[0]);
        DTC_Ion_Sec_Spark_Cap.VCharge_1                      = __KwpConv_VCharge(VCharge[1]);
        DTC_Ion_Sec_Spark_Cap.VCharge_2                      = __KwpConv_VCharge(VCharge[2]);
        DTC_Ion_Sec_Spark_Cap.VCharge_3                      = __KwpConv_VCharge(VCharge[3]);
        DTC_Ion_Sec_Spark_Cap.SparkLength_0                  = __KwpConv_SparkLength(SparkLength[0]);
        DTC_Ion_Sec_Spark_Cap.SparkLength_1                  = __KwpConv_SparkLength(SparkLength[1]);
        DTC_Ion_Sec_Spark_Cap.SparkLength_2                  = __KwpConv_SparkLength(SparkLength[2]);
        DTC_Ion_Sec_Spark_Cap.SparkLength_3                  = __KwpConv_SparkLength(SparkLength[3]);
        DTC_Ion_Sec_Spark_Cap.SparkLength_4                  = __KwpConv_SparkLength(SparkLength[4]);
        DTC_Ion_Sec_Spark_Cap.SparkLength_5                  = __KwpConv_SparkLength(SparkLength[5]);
        DTC_Ion_Sec_Spark_Cap.SparkLength_6                  = __KwpConv_SparkLength(SparkLength[6]);
        DTC_Ion_Sec_Spark_Cap.SparkLength_7                  = __KwpConv_SparkLength(SparkLength[7]);
        DTC_Ion_Sec_Spark_Cap.VtIShotPeak_0                  = __KwpConv_VtIShotPeak(VtIShotPeak[0]);
        DTC_Ion_Sec_Spark_Cap.VtIShotPeak_1                  = __KwpConv_VtIShotPeak(VtIShotPeak[1]);
        DTC_Ion_Sec_Spark_Cap.VtIShotPeak_2                  = __KwpConv_VtIShotPeak(VtIShotPeak[2]);
        DTC_Ion_Sec_Spark_Cap.VtIShotPeak_3                  = __KwpConv_VtIShotPeak(VtIShotPeak[3]);
        DTC_Ion_Sec_Spark_Cap.VtIShotPeak_4                  = __KwpConv_VtIShotPeak(VtIShotPeak[4]);
        DTC_Ion_Sec_Spark_Cap.VtIShotPeak_5                  = __KwpConv_VtIShotPeak(VtIShotPeak[5]);
        DTC_Ion_Sec_Spark_Cap.VtIShotPeak_6                  = __KwpConv_VtIShotPeak(VtIShotPeak[6]);
        DTC_Ion_Sec_Spark_Cap.VtIShotPeak_7                  = __KwpConv_VtIShotPeak(VtIShotPeak[7]);
        DTC_Ion_Sec_Spark_Cap.IntIon_0                       = IntIon[0];
        DTC_Ion_Sec_Spark_Cap.IntIon_1                       = IntIon[1];
        DTC_Ion_Sec_Spark_Cap.IntIon_2                       = IntIon[2];
        DTC_Ion_Sec_Spark_Cap.IntIon_3                       = IntIon[3];
        DTC_Ion_Sec_Spark_Cap.IntIon_4                       = IntIon[4];
        DTC_Ion_Sec_Spark_Cap.IntIon_5                       = IntIon[5];
        DTC_Ion_Sec_Spark_Cap.IntIon_6                       = IntIon[6];
        DTC_Ion_Sec_Spark_Cap.IntIon_7                       = IntIon[7];
        DTC_Ion_Sec_Spark_Cap.VtILeadPeak_0                  = __KwpConv_VtILeadPeak(VtILeadPeak[0]);
        DTC_Ion_Sec_Spark_Cap.VtILeadPeak_1                  = __KwpConv_VtILeadPeak(VtILeadPeak[1]);
        DTC_Ion_Sec_Spark_Cap.VtILeadPeak_2                  = __KwpConv_VtILeadPeak(VtILeadPeak[2]);
        DTC_Ion_Sec_Spark_Cap.VtILeadPeak_3                  = __KwpConv_VtILeadPeak(VtILeadPeak[3]);
        DTC_Ion_Sec_Spark_Cap.VtILeadPeak_4                  = __KwpConv_VtILeadPeak(VtILeadPeak[4]);
        DTC_Ion_Sec_Spark_Cap.VtILeadPeak_5                  = __KwpConv_VtILeadPeak(VtILeadPeak[5]);
        DTC_Ion_Sec_Spark_Cap.VtILeadPeak_6                  = __KwpConv_VtILeadPeak(VtILeadPeak[6]);
        DTC_Ion_Sec_Spark_Cap.VtILeadPeak_7                  = __KwpConv_VtILeadPeak(VtILeadPeak[7]);
        DTC_Ion_Sec_Spark_Cap.VtIPriCorr_0                   = __KwpConv_VtIPriCorr(VtIPriCorr[0]);
        DTC_Ion_Sec_Spark_Cap.VtIPriCorr_1                   = __KwpConv_VtIPriCorr(VtIPriCorr[1]);
        DTC_Ion_Sec_Spark_Cap.VtIPriCorr_2                   = __KwpConv_VtIPriCorr(VtIPriCorr[2]);
        DTC_Ion_Sec_Spark_Cap.VtIPriCorr_3                   = __KwpConv_VtIPriCorr(VtIPriCorr[3]);
        DTC_Ion_Sec_Spark_Cap.VtIPriCorr_4                   = __KwpConv_VtIPriCorr(VtIPriCorr[4]);
        DTC_Ion_Sec_Spark_Cap.VtIPriCorr_5                   = __KwpConv_VtIPriCorr(VtIPriCorr[5]);
        DTC_Ion_Sec_Spark_Cap.VtIPriCorr_6                   = __KwpConv_VtIPriCorr(VtIPriCorr[6]);
        DTC_Ion_Sec_Spark_Cap.VtIPriCorr_7                   = __KwpConv_VtIPriCorr(VtIPriCorr[7]);
        DTC_Ion_Sec_Spark_Cap.VtTSparkFilt_0                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[0]);
        DTC_Ion_Sec_Spark_Cap.VtTSparkFilt_1                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[1]);
        DTC_Ion_Sec_Spark_Cap.VtTSparkFilt_2                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[2]);
        DTC_Ion_Sec_Spark_Cap.VtTSparkFilt_3                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[3]);
        DTC_Ion_Sec_Spark_Cap.VtTSparkFilt_4                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[4]);
        DTC_Ion_Sec_Spark_Cap.VtTSparkFilt_5                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[5]);
        DTC_Ion_Sec_Spark_Cap.VtTSparkFilt_6                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[6]);
        DTC_Ion_Sec_Spark_Cap.VtTSparkFilt_7                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[7]);
        DTC_Ion_Sec_Spark_Cap.VChargeObj_0                   = VChargeObj[0];
        DTC_Ion_Sec_Spark_Cap.VChargeObj_1                   = VChargeObj[1];
        DTC_Ion_Sec_Spark_Cap.VChargeObj_2                   = VChargeObj[2];
        DTC_Ion_Sec_Spark_Cap.VChargeObj_3                   = VChargeObj[3];
        DTC_Ion_Sec_Spark_Cap.FlgIShotTout_0                 = FlgIShotTout[0];
        DTC_Ion_Sec_Spark_Cap.FlgIShotTout_1                 = FlgIShotTout[1];
        DTC_Ion_Sec_Spark_Cap.VtOLSecInt_0                   = VtOLSecInt[0];
        DTC_Ion_Sec_Spark_Cap.VtOLSecInt_1                   = VtOLSecInt[1];
        DTC_Ion_Sec_Spark_Cap.VtOLSecInt_2                   = VtOLSecInt[2];
        DTC_Ion_Sec_Spark_Cap.VtOLSecInt_3                   = VtOLSecInt[3];
        DTC_Ion_Sec_Spark_Cap.VtOLSecInt_4                   = VtOLSecInt[4];
        DTC_Ion_Sec_Spark_Cap.VtOLSecInt_5                   = VtOLSecInt[5];
        DTC_Ion_Sec_Spark_Cap.VtOLSecInt_6                   = VtOLSecInt[6];
        DTC_Ion_Sec_Spark_Cap.VtOLSecInt_7                   = VtOLSecInt[7];
        DTC_Ion_Sec_Spark_Cap.VtIonKnockEnableCond_0         = VtIonKnockEnableCond[0];
        DTC_Ion_Sec_Spark_Cap.VtIonKnockEnableCond_1         = VtIonKnockEnableCond[1];
        DTC_Ion_Sec_Spark_Cap.VtIonKnockEnableCond_2         = VtIonKnockEnableCond[2];
        DTC_Ion_Sec_Spark_Cap.VtIonKnockEnableCond_3         = VtIonKnockEnableCond[3];
        DTC_Ion_Sec_Spark_Cap.VtIonKnockEnableCond_4         = VtIonKnockEnableCond[4];
        DTC_Ion_Sec_Spark_Cap.VtIonKnockEnableCond_5         = VtIonKnockEnableCond[5];
        DTC_Ion_Sec_Spark_Cap.VtIonKnockEnableCond_6         = VtIonKnockEnableCond[6];
        DTC_Ion_Sec_Spark_Cap.VtIonKnockEnableCond_7         = VtIonKnockEnableCond[7];

        /* moving DTC_Ion_Sec_Spark_Cap into snapshot data array */
        if (snapNum == SNAP1)
        {
            memcpy((void *)&DTCSnapshotEE_1[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Ion_Sec_Spark_Cap, sizeof(DTC_Ion_Sec_Spark_Cap));
        }
        else if (snapNum == SNAP2)
        {
            memcpy((void *)&DTCSnapshotEE_2[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Ion_Sec_Spark_Cap, sizeof(DTC_Ion_Sec_Spark_Cap));
        }
        else {/* MISRA 14.10 */}

        offset_snapdata += sizeof(DTC_Ion_Sec_Spark_Cap);
    }
    else
    {
        /* MISRA 14.10 */
    }

    if((snapType & COIL_PRI_BUCK_TRIG) == COIL_PRI_BUCK_TRIG)
    {
        DTC_Coil_Pri_Buck_Trig_T DTC_Coil_Pri_Buck_Trig;
    
        DTC_Coil_Pri_Buck_Trig.CntIGNTrgInOn_0                = (uint8_T)(CntIGNTrgInOn[0] & 0xFFu);
        DTC_Coil_Pri_Buck_Trig.CntIGNTrgInOn_1                = (uint8_T)(CntIGNTrgInOn[1] & 0xFFu);
        DTC_Coil_Pri_Buck_Trig.CntIGNTrgInOn_2                = (uint8_T)(CntIGNTrgInOn[2] & 0xFFu);
        DTC_Coil_Pri_Buck_Trig.CntIGNTrgInOn_3                = (uint8_T)(CntIGNTrgInOn[3] & 0xFFu);
        DTC_Coil_Pri_Buck_Trig.CntIGNTrgInOn_4                = (uint8_T)(CntIGNTrgInOn[4] & 0xFFu);
        DTC_Coil_Pri_Buck_Trig.CntIGNTrgInOn_5                = (uint8_T)(CntIGNTrgInOn[5] & 0xFFu);
        DTC_Coil_Pri_Buck_Trig.CntIGNTrgInOn_6                = (uint8_T)(CntIGNTrgInOn[6] & 0xFFu);
        DTC_Coil_Pri_Buck_Trig.CntIGNTrgInOn_7                = (uint8_T)(CntIGNTrgInOn[7] & 0xFFu);
        DTC_Coil_Pri_Buck_Trig.EffDwellTime_0                 = __KwpConv_EffDwellTime(EffDwellTime[0]);
        DTC_Coil_Pri_Buck_Trig.EffDwellTime_1                 = __KwpConv_EffDwellTime(EffDwellTime[1]);
        DTC_Coil_Pri_Buck_Trig.EffDwellTime_2                 = __KwpConv_EffDwellTime(EffDwellTime[2]);
        DTC_Coil_Pri_Buck_Trig.EffDwellTime_3                 = __KwpConv_EffDwellTime(EffDwellTime[3]);
        DTC_Coil_Pri_Buck_Trig.EffDwellTime_4                 = __KwpConv_EffDwellTime(EffDwellTime[4]);
        DTC_Coil_Pri_Buck_Trig.EffDwellTime_5                 = __KwpConv_EffDwellTime(EffDwellTime[5]);
        DTC_Coil_Pri_Buck_Trig.EffDwellTime_6                 = __KwpConv_EffDwellTime(EffDwellTime[6]);
        DTC_Coil_Pri_Buck_Trig.EffDwellTime_7                 = __KwpConv_EffDwellTime(EffDwellTime[7]);
        DTC_Coil_Pri_Buck_Trig.VBuck_0                        = __KwpConv_VBuck(VBuck[0][0]);
        DTC_Coil_Pri_Buck_Trig.VBuck_4                        = __KwpConv_VBuck(VBuck[0][4]);
        DTC_Coil_Pri_Buck_Trig.VBuck_5                        = __KwpConv_VBuck(VBuck[1][0]);
        DTC_Coil_Pri_Buck_Trig.VBuck_9                        = __KwpConv_VBuck(VBuck[1][4]);
        DTC_Coil_Pri_Buck_Trig.VBuck_10                       = __KwpConv_VBuck(VBuck[2][0]);
        DTC_Coil_Pri_Buck_Trig.VBuck_14                       = __KwpConv_VBuck(VBuck[2][4]);
        DTC_Coil_Pri_Buck_Trig.VBuck_15                       = __KwpConv_VBuck(VBuck[3][0]);
        DTC_Coil_Pri_Buck_Trig.VBuck_19                       = __KwpConv_VBuck(VBuck[3][4]);
        DTC_Coil_Pri_Buck_Trig.VBuck_20                       = __KwpConv_VBuck(VBuck[4][0]);
        DTC_Coil_Pri_Buck_Trig.VBuck_24                       = __KwpConv_VBuck(VBuck[4][4]);
        DTC_Coil_Pri_Buck_Trig.VBuck_25                       = __KwpConv_VBuck(VBuck[5][0]);
        DTC_Coil_Pri_Buck_Trig.VBuck_29                       = __KwpConv_VBuck(VBuck[5][4]);
        DTC_Coil_Pri_Buck_Trig.VBuck_30                       = __KwpConv_VBuck(VBuck[6][0]);
        DTC_Coil_Pri_Buck_Trig.VBuck_34                       = __KwpConv_VBuck(VBuck[6][4]);
        DTC_Coil_Pri_Buck_Trig.VBuck_35                       = __KwpConv_VBuck(VBuck[7][0]);
        DTC_Coil_Pri_Buck_Trig.VBuck_39                       = __KwpConv_VBuck(VBuck[7][4]);
        DTC_Coil_Pri_Buck_Trig.VtILeadPeak_0                  = __KwpConv_VtILeadPeak(VtILeadPeak[0]);
        DTC_Coil_Pri_Buck_Trig.VtILeadPeak_1                  = __KwpConv_VtILeadPeak(VtILeadPeak[1]);
        DTC_Coil_Pri_Buck_Trig.VtILeadPeak_2                  = __KwpConv_VtILeadPeak(VtILeadPeak[2]);
        DTC_Coil_Pri_Buck_Trig.VtILeadPeak_3                  = __KwpConv_VtILeadPeak(VtILeadPeak[3]);
        DTC_Coil_Pri_Buck_Trig.VtILeadPeak_4                  = __KwpConv_VtILeadPeak(VtILeadPeak[4]);
        DTC_Coil_Pri_Buck_Trig.VtILeadPeak_5                  = __KwpConv_VtILeadPeak(VtILeadPeak[5]);
        DTC_Coil_Pri_Buck_Trig.VtILeadPeak_6                  = __KwpConv_VtILeadPeak(VtILeadPeak[6]);
        DTC_Coil_Pri_Buck_Trig.VtILeadPeak_7                  = __KwpConv_VtILeadPeak(VtILeadPeak[7]);
        DTC_Coil_Pri_Buck_Trig.VtIPriCorr_0                   = __KwpConv_VtIPriCorr(VtIPriCorr[0]);
        DTC_Coil_Pri_Buck_Trig.VtIPriCorr_1                   = __KwpConv_VtIPriCorr(VtIPriCorr[1]);
        DTC_Coil_Pri_Buck_Trig.VtIPriCorr_2                   = __KwpConv_VtIPriCorr(VtIPriCorr[2]);
        DTC_Coil_Pri_Buck_Trig.VtIPriCorr_3                   = __KwpConv_VtIPriCorr(VtIPriCorr[3]);
        DTC_Coil_Pri_Buck_Trig.VtIPriCorr_4                   = __KwpConv_VtIPriCorr(VtIPriCorr[4]);
        DTC_Coil_Pri_Buck_Trig.VtIPriCorr_5                   = __KwpConv_VtIPriCorr(VtIPriCorr[5]);
        DTC_Coil_Pri_Buck_Trig.VtIPriCorr_6                   = __KwpConv_VtIPriCorr(VtIPriCorr[6]);
        DTC_Coil_Pri_Buck_Trig.VtIPriCorr_7                   = __KwpConv_VtIPriCorr(VtIPriCorr[7]);
        DTC_Coil_Pri_Buck_Trig.VtTSparkFilt_0                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[0]);
        DTC_Coil_Pri_Buck_Trig.VtTSparkFilt_1                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[1]);
        DTC_Coil_Pri_Buck_Trig.VtTSparkFilt_2                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[2]);
        DTC_Coil_Pri_Buck_Trig.VtTSparkFilt_3                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[3]);
        DTC_Coil_Pri_Buck_Trig.VtTSparkFilt_4                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[4]);
        DTC_Coil_Pri_Buck_Trig.VtTSparkFilt_5                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[5]);
        DTC_Coil_Pri_Buck_Trig.VtTSparkFilt_6                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[6]);
        DTC_Coil_Pri_Buck_Trig.VtTSparkFilt_7                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[7]);
        DTC_Coil_Pri_Buck_Trig.ISupplyCoil1ADC                = ISupplyCoil1ADC;
        DTC_Coil_Pri_Buck_Trig.VSupplyCoil1ADC                = VSupplyCoil1ADC;
        DTC_Coil_Pri_Buck_Trig.IBattADC                       = IBattADC;
        DTC_Coil_Pri_Buck_Trig.ISupplyCoil2ADC                = ISupplyCoil2ADC;
        DTC_Coil_Pri_Buck_Trig.VSupplyCoil2ADC                = VSupplyCoil2ADC;
        DTC_Coil_Pri_Buck_Trig.VtIonKnockEnableCond_0         = VtIonKnockEnableCond[0];
        DTC_Coil_Pri_Buck_Trig.VtIonKnockEnableCond_1         = VtIonKnockEnableCond[1];
        DTC_Coil_Pri_Buck_Trig.VtIonKnockEnableCond_2         = VtIonKnockEnableCond[2];
        DTC_Coil_Pri_Buck_Trig.VtIonKnockEnableCond_3         = VtIonKnockEnableCond[3];
        DTC_Coil_Pri_Buck_Trig.VtIonKnockEnableCond_4         = VtIonKnockEnableCond[4];
        DTC_Coil_Pri_Buck_Trig.VtIonKnockEnableCond_5         = VtIonKnockEnableCond[5];
        DTC_Coil_Pri_Buck_Trig.VtIonKnockEnableCond_6         = VtIonKnockEnableCond[6];
        DTC_Coil_Pri_Buck_Trig.VtIonKnockEnableCond_7         = VtIonKnockEnableCond[7];

        /* moving DTC_Coil_Pri_Buck_Trig into snapshot data array */
        if (snapNum == SNAP1)
        {
            memcpy((void *)&DTCSnapshotEE_1[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Coil_Pri_Buck_Trig, sizeof(DTC_Coil_Pri_Buck_Trig));
        }
        else if (snapNum == SNAP2)
        {
            memcpy((void *)&DTCSnapshotEE_2[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Coil_Pri_Buck_Trig, sizeof(DTC_Coil_Pri_Buck_Trig));
        }
        else {/* MISRA 14.10 */}

        offset_snapdata += sizeof(DTC_Coil_Pri_Buck_Trig);
    }
    else
    {
        /* MISRA 14.10 */
    }

    if((snapType & KNOCK) == KNOCK)
    {
        DTC_Knock_T DTC_Knock;
    
        DTC_Knock.KCohDiagCnt_0                  = KCohDiagCnt[0];
        DTC_Knock.KCohDiagCnt_1                  = KCohDiagCnt[1];
        DTC_Knock.KCohDiagCnt_2                  = KCohDiagCnt[2];
        DTC_Knock.KCohDiagCnt_3                  = KCohDiagCnt[3];
        DTC_Knock.KCohDiagCnt_4                  = KCohDiagCnt[4];
        DTC_Knock.KCohDiagCnt_5                  = KCohDiagCnt[5];
        DTC_Knock.KCohDiagCnt_6                  = KCohDiagCnt[6];
        DTC_Knock.KCohDiagCnt_7                  = KCohDiagCnt[7];
        DTC_Knock.CntKnockCohEE_0                = CntKnockCohEE[0];
        DTC_Knock.CntKnockCohEE_1                = CntKnockCohEE[1];
        DTC_Knock.CntKnockCohEE_2                = CntKnockCohEE[2];
        DTC_Knock.CntKnockCohEE_3                = CntKnockCohEE[3];
        DTC_Knock.CntKnockCohEE_4                = CntKnockCohEE[4];
        DTC_Knock.CntKnockCohEE_5                = CntKnockCohEE[5];
        DTC_Knock.CntKnockCohEE_6                = CntKnockCohEE[6];
        DTC_Knock.CntKnockCohEE_7                = CntKnockCohEE[7];
        DTC_Knock.KnockInt_0                     = __KwpConv_KnockInt(KnockInt[0]);
        DTC_Knock.KnockInt_1                     = __KwpConv_KnockInt(KnockInt[1]);
        DTC_Knock.KnockInt_2                     = __KwpConv_KnockInt(KnockInt[2]);
        DTC_Knock.KnockInt_3                     = __KwpConv_KnockInt(KnockInt[3]);
        DTC_Knock.KnockInt_4                     = __KwpConv_KnockInt(KnockInt[4]);
        DTC_Knock.KnockInt_5                     = __KwpConv_KnockInt(KnockInt[5]);
        DTC_Knock.KnockInt_6                     = __KwpConv_KnockInt(KnockInt[6]);
        DTC_Knock.KnockInt_7                     = __KwpConv_KnockInt(KnockInt[7]);
        DTC_Knock.VtILeadPeak_0                  = __KwpConv_VtILeadPeak(VtILeadPeak[0]);
        DTC_Knock.VtILeadPeak_1                  = __KwpConv_VtILeadPeak(VtILeadPeak[1]);
        DTC_Knock.VtILeadPeak_2                  = __KwpConv_VtILeadPeak(VtILeadPeak[2]);
        DTC_Knock.VtILeadPeak_3                  = __KwpConv_VtILeadPeak(VtILeadPeak[3]);
        DTC_Knock.VtILeadPeak_4                  = __KwpConv_VtILeadPeak(VtILeadPeak[4]);
        DTC_Knock.VtILeadPeak_5                  = __KwpConv_VtILeadPeak(VtILeadPeak[5]);
        DTC_Knock.VtILeadPeak_6                  = __KwpConv_VtILeadPeak(VtILeadPeak[6]);
        DTC_Knock.VtILeadPeak_7                  = __KwpConv_VtILeadPeak(VtILeadPeak[7]);
        DTC_Knock.VtIPriCorr_0                   = __KwpConv_VtIPriCorr(VtIPriCorr[0]);
        DTC_Knock.VtIPriCorr_1                   = __KwpConv_VtIPriCorr(VtIPriCorr[1]);
        DTC_Knock.VtIPriCorr_2                   = __KwpConv_VtIPriCorr(VtIPriCorr[2]);
        DTC_Knock.VtIPriCorr_3                   = __KwpConv_VtIPriCorr(VtIPriCorr[3]);
        DTC_Knock.VtIPriCorr_4                   = __KwpConv_VtIPriCorr(VtIPriCorr[4]);
        DTC_Knock.VtIPriCorr_5                   = __KwpConv_VtIPriCorr(VtIPriCorr[5]);
        DTC_Knock.VtIPriCorr_6                   = __KwpConv_VtIPriCorr(VtIPriCorr[6]);
        DTC_Knock.VtIPriCorr_7                   = __KwpConv_VtIPriCorr(VtIPriCorr[7]);
        DTC_Knock.VtTSparkFilt_0                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[0]);
        DTC_Knock.VtTSparkFilt_1                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[1]);
        DTC_Knock.VtTSparkFilt_2                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[2]);
        DTC_Knock.VtTSparkFilt_3                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[3]);
        DTC_Knock.VtTSparkFilt_4                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[4]);
        DTC_Knock.VtTSparkFilt_5                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[5]);
        DTC_Knock.VtTSparkFilt_6                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[6]);
        DTC_Knock.VtTSparkFilt_7                 = __KwpConv_VtTSparkFilt(VtTSparkFilt[7]);
        DTC_Knock.VtIonKnockEnableCond_0         = VtIonKnockEnableCond[0];
        DTC_Knock.VtIonKnockEnableCond_1         = VtIonKnockEnableCond[1];
        DTC_Knock.VtIonKnockEnableCond_2         = VtIonKnockEnableCond[2];
        DTC_Knock.VtIonKnockEnableCond_3         = VtIonKnockEnableCond[3];
        DTC_Knock.VtIonKnockEnableCond_4         = VtIonKnockEnableCond[4];
        DTC_Knock.VtIonKnockEnableCond_5         = VtIonKnockEnableCond[5];
        DTC_Knock.VtIonKnockEnableCond_6         = VtIonKnockEnableCond[6];
        DTC_Knock.VtIonKnockEnableCond_7         = VtIonKnockEnableCond[7];

        /* moving DTC_Knock into snapshot data array */
        if (snapNum == SNAP1)
        {
            memcpy((void *)&DTCSnapshotEE_1[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Knock, sizeof(DTC_Knock));
        }
        else if (snapNum == SNAP2)
        {
            memcpy((void *)&DTCSnapshotEE_2[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Knock, sizeof(DTC_Knock));
        }
        else {/* MISRA 14.10 */}

        offset_snapdata += sizeof(DTC_Knock);
    }
    else
    {
        /* MISRA 14.10 */
    }

    if((snapType & SAFETY) == SAFETY)
    {
        DTC_Safety_T DTC_Safety;
    
        DTC_Safety.SACmdInLevErrNo                = SACmdInLevErrNo;
        DTC_Safety.SACmdInLevCirBuff_0            = SACmdInLevCirBuff[0];
        DTC_Safety.SACmdInLevCirBuff_1            = SACmdInLevCirBuff[1];
        DTC_Safety.SACmdInLevCirBuff_2            = SACmdInLevCirBuff[2];
        DTC_Safety.SACmdInLevCirBuff_3            = SACmdInLevCirBuff[3];
        DTC_Safety.SACmdInLevCirBuff_4            = SACmdInLevCirBuff[4];
        DTC_Safety.SACmdInLevCirBuff_5            = SACmdInLevCirBuff[5];
        DTC_Safety.SACmdInLevCirBuff_6            = SACmdInLevCirBuff[6];
        DTC_Safety.SACmdInLevCirBuff_7            = SACmdInLevCirBuff[7];
        DTC_Safety.StWdt                          = StWdt;
        DTC_Safety.StSBCSafeTest                  = StSBCSafeTest;
        DTC_Safety.SBCSysStat                     = SBCSysStat;
        DTC_Safety.StSBC                          = StSBC;
        DTC_Safety.StSBCMode                      = StSBCMode;
        DTC_Safety.EESbcOT                        = EESbcOT;
        DTC_Safety.EESbcSC                        = EESbcSC;
        DTC_Safety.EESbcUV                        = EESbcUV;
        DTC_Safety.EESbcCAN                       = EESbcCAN;
        DTC_Safety.EECntSbcUV                     = EECntSbcUV;
        DTC_Safety.EECntSbcSC                     = EECntSbcSC;
        DTC_Safety.EECntSbcOT                     = EECntSbcOT;
        DTC_Safety.EECntSbcCAN                    = EECntSbcCAN;
        DTC_Safety.VtIonKnockEnableCond_0         = VtIonKnockEnableCond[0];
        DTC_Safety.VtIonKnockEnableCond_1         = VtIonKnockEnableCond[1];
        DTC_Safety.VtIonKnockEnableCond_2         = VtIonKnockEnableCond[2];
        DTC_Safety.VtIonKnockEnableCond_3         = VtIonKnockEnableCond[3];
        DTC_Safety.VtIonKnockEnableCond_4         = VtIonKnockEnableCond[4];
        DTC_Safety.VtIonKnockEnableCond_5         = VtIonKnockEnableCond[5];
        DTC_Safety.VtIonKnockEnableCond_6         = VtIonKnockEnableCond[6];
        DTC_Safety.VtIonKnockEnableCond_7         = VtIonKnockEnableCond[7];

        /* moving DTC_Safety into snapshot data array */
        if (snapNum == SNAP1)
        {
            memcpy((void *)&DTCSnapshotEE_1[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Safety, sizeof(DTC_Safety));
        }
        else if (snapNum == SNAP2)
        {
            memcpy((void *)&DTCSnapshotEE_2[pos].DTCSnapshotData[offset_snapdata],(void *) &DTC_Safety, sizeof(DTC_Safety));
        }
        else {/* MISRA 14.10 */}

        offset_snapdata += sizeof(DTC_Safety);
    }
    else
    {
        /* MISRA 14.10 */
    }

    for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
    {
        if (DTCSnapshotEE_1[i].pcode == pCode) // entry found in the error memory
        {
            if (DTCSnapshotEE_1[i].fault != pFault) // actual fault is different from the stored one 
            {
                DTCSnapshotEE_1[i].diagSts.BF.testFailed = 0u;
            }
        }
        if (DTCSnapshotEE_2[i].pcode == pCode) // entry found in the error memory
        {
            if (DTCSnapshotEE_2[i].fault != pFault) // actual fault is different from the stored one 
            {
                DTCSnapshotEE_2[i].diagSts.BF.testFailed = 0u;
            }
        }
    }

    /* Extended Data shall be stored every time */
    UpdateExtendedData(pCode,pFault,diagIndex);
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * UpdateExtendedData - This function searches for free locations/update old location
 *                      in the ExtendedData memory entries.
 *
 * Implementation notes:
 *--------------------------------------------------------------------------*/
static void UpdateExtendedData(uint16_T pCode, typPtFault pFault, uint8_T diagIndex)
{
uint8_T i;
int8_T dtc_found = -1;
int8_T dtc_old = -1;
int8_T free = -1;


    /* check if low priority DTC shall be deleted/updated */
    if (pCode_oldest != NULL_DTC)
    {
        for(i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if (DTCExtendedEE[i].pcode == pCode_oldest)
            {
                if (DTCExtendedEE[i].fault == ftb_oldest)
                {
                    /* pCode already stored on location i */
                    dtc_old = i;
                    break;
                }
            }
        }
    }

    /* Check if pCode is already stored... */
    for(i = 0u; i < DIAG_FAULT_LENGTH; i++)
    {
        if (DTCExtendedEE[i].pcode == pCode)
        {
            if (DTCExtendedEE[i].fault == pFault)
            {
                /* pCode already stored on location i */
                dtc_found = i;
                break;
            }
        }
    }

    /* ...if not stored scan memory for a free location...*/
    if (dtc_found == -1) // DTC not found
    {
        for(i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if (DTCExtendedEE[i].pcode == NULL_DTC)
            {
                /* free location found */
                free = i;
                break;
            }
        }
    }

    if (dtc_old >= 0)
    {
        EventCounterEE[dtc_old] = THRDIAGWUC; //max event counter.
        DTCExtendedEE[dtc_old].pcode                        = pCode;
        DTCExtendedEE[dtc_old].fault                        = pFault;
        DTCExtendedEE[dtc_old].diagIdx                      = diagIndex;
        DTCExtendedEE[dtc_old].EventCounter                 = EventCounterEE[dtc_old];
        DTCExtendedEE[dtc_old].WarningLampSwitchOffCycles   = WarningLampSwitchOffCycles_stub;
    }

    if (dtc_found >= 0)
    {
        EventCounterEE[dtc_found] = THRDIAGWUC; //max event counter.
        DTCExtendedEE[dtc_found].pcode                        = pCode;
        DTCExtendedEE[dtc_found].fault                        = pFault;
        DTCExtendedEE[dtc_found].diagIdx                      = diagIndex;
        DTCExtendedEE[dtc_found].EventCounter                 = EventCounterEE[dtc_found];
        DTCExtendedEE[dtc_found].WarningLampSwitchOffCycles   = WarningLampSwitchOffCycles_stub;
    }

    if (free >= 0)
    {
        EventCounterEE[free] = THRDIAGWUC; //max event counter.
        DTCExtendedEE[free].pcode                        = pCode;
        DTCExtendedEE[free].fault                        = pFault;
        DTCExtendedEE[free].diagIdx                      = diagIndex;
        DTCExtendedEE[free].EventCounter                 = EventCounterEE[free];
        DTCExtendedEE[free].WarningLampSwitchOffCycles   = WarningLampSwitchOffCycles_stub;
    }
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * CheckStoredFaults - This function searches error memory entries with 
 *                 testFailed OR confirmedDTC status bits equals to 0.
 *
 * Implementation notes:
 *--------------------------------------------------------------------------*/
static uint8_T CheckStoredFaults(uint8_T snapNum)
{
uint8_T res = TRUE;
uint8_T i;

    for (i = 0u; ((i < DIAG_FAULT_LENGTH) && (res == TRUE)); i++)
    {
        if (snapNum == SNAP1)
        {
            if (DTCSnapshotEE_1[i].pcode != NULL_DTC)
            {
                //if ((DTCSnapshotEE_1[i].dtcSts.BF.testFailed != 1u) && (DTCSnapshotEE_1[i].dtcSts.BF.confirmedDTC != 1u))
                if (DTCStatusEE[DTCSnapshotEE_1[i].diagIdx].BF.testFailed != 1u)
                {
                    res = FALSE;
                }
            }
        }
        else if (snapNum == SNAP2)
        {
            if (DTCSnapshotEE_2[i].pcode != NULL_DTC)
            {
                //if ((DTCSnapshotEE_2[i].dtcSts.BF.testFailed != 1u) && (DTCSnapshotEE_2[i].dtcSts.BF.confirmedDTC != 1u))
                if (DTCStatusEE[DTCSnapshotEE_2[i].diagIdx].BF.testFailed != 1u)
                {
                    res = FALSE;
                }
            }
        }
        else
        {
            /* MISRA 14.10 */
        }
    }

    return res;
}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * CheckStoredSafetyFaults - This function searches error memory entries not
 *                 present at time of request (i.e. testFailed == 0) and severity
 *                 different from CHKI - Check Immediatly
 *
 * Implementation notes:
 *--------------------------------------------------------------------------*/
static uint8_T CheckStoredSafetyFaults(uint8_T snapNum)
{
uint8_T res = TRUE;
uint8_T i;

    for (i = 0u; ((i < DIAG_FAULT_LENGTH) && (res == TRUE)); i++)
    {
        if (snapNum == SNAP1)
        {
            if (DTCSnapshotEE_1[i].pcode != NULL_DTC)
            {
                //if (DTCSt DTCSnapshotEE_1[i].dtcSts.BF.testFailed != 1u) // Fault not present
                if (DTCStatusEE[DTCSnapshotEE_1[i].diagIdx].BF.testFailed != 1u) // Fault not present
                {
                    if(DTCSEVERITY[DTCSnapshotEE_1[i].diagIdx] != CHKI)
                    {
                        res = FALSE;
                    }
                }
            }
        }
        else if (snapNum == SNAP2)
        {
            if (DTCSnapshotEE_2[i].pcode != NULL_DTC)
            {
                //if (DTCSnapshotEE_2[i].dtcSts.BF.testFailed != 1u) // Fault not present
                if (DTCStatusEE[DTCSnapshotEE_2[i].diagIdx].BF.testFailed != 1u) // Fault not present
                {
                    if(DTCSEVERITY[DTCSnapshotEE_2[i].diagIdx] != CHKI)
                    {
                        res = FALSE;
                    }
                }
            }
        }
        else
        {
            /* MISRA 14.10 */
        }
    }

    return res;
}


/*--------------------------------------------------------------------------*
 * Function name - Function description
 *
 * CheckSeverity - This function searches error memory entries with severity 
 *                 value less or lessORequal (depending on level_check_pri 
 *                 parameter) to a given value (severity parameter).
 *
 * Implementation notes:
 *--------------------------------------------------------------------------*/
static uint8_T CheckSeverity(uint8_T severity, uint8_T level_check_pri, uint8_T snapNum)
{
uint8_T res = FALSE;
uint8_T i;

    for (i = 0u; ((i < DIAG_FAULT_LENGTH) && (res == FALSE)); i++)
    {
        if (level_check_pri == MINOR_OR_EQUAL)
        {
            if (snapNum == SNAP1)
            {
                if(DTCSnapshotEE_1[i].severity <= severity)
                {
                    res = TRUE;
                }
            }
            else if (snapNum == SNAP2)
            {
                if(DTCSnapshotEE_2[i].severity <= severity)
                {
                    res = TRUE;
                }
            }
            else {/* MISRA 14.10 */}
        }
        else if (level_check_pri == MINOR)
        {
            if (snapNum == SNAP1)
            {
                if(DTCSnapshotEE_1[i].severity < severity)
                {
                    res = TRUE;
                }
            }
            else if (snapNum == SNAP2)
            {
                if(DTCSnapshotEE_2[i].diagIdx < severity)
                {
                    res = TRUE;
                }
            }
            else {/* MISRA 14.10 */}
        }
        else
        {
            /* MISRA 14.10 */
        }
    }

    return res;

}

/*--------------------------------------------------------------------------*
 * Function name - Function description
 * FindOldestSnap - This function searches the oldest snapshot regardless
 *                  DTC severity
*
* Implementation notes:
* In case of one or more snapshot with the same timestamp and severity, last
* one feasible index is returned.
*--------------------------------------------------------------------------*/
static int8_T FindOldestSnap(uint8_T snapNum, uint16_T * pCode_old, uint8_T * ftb_old)
{
uint8_T i;
int8_T idx = -2;
uint32_T timestamp;

    /* init timestamp value with saturated values */
    timestamp = MAX_uint32_T;

    for (i  = 0u; i < DIAG_FAULT_LENGTH; i++)
    {
        if (snapNum == SNAP1)
        {
            if(DTCSnapshotEE_1[i].timestamp <= timestamp)
            {
                idx = i;
                timestamp = DTCSnapshotEE_1[i].timestamp;
                *pCode_old = DTCSnapshotEE_1[i].pcode;
                *ftb_old = DTCSnapshotEE_1[i].fault;
            }
        }
        else if (snapNum == SNAP2)
        {
            if(DTCSnapshotEE_2[i].timestamp <= timestamp)
            {
                idx = i;
                timestamp = DTCSnapshotEE_2[i].timestamp;
                *pCode_old = DTCSnapshotEE_2[i].pcode;
                *ftb_old = DTCSnapshotEE_2[i].fault;
            }
        }
        else
        {
            /* MISRA 14.10 */
        }
    }

    return idx;
}


/*--------------------------------------------------------------------------*
 * Function name - Function description
 * FindOldSnapWithLowSeverity - This function searches the oldest of lowest
 *                 severity snapshot.
 *
 * Implementation notes:
 * In case of one or more snapshot with the same timestamp and severity, last
 * one feasible index is returned.
 *--------------------------------------------------------------------------*/
static int8_T FindOldSnapWithLowSeverity(uint8_T snapNum, uint16_T * pCode_old,uint8_T * ftb_old)
{
uint8_T i;
int8_T idx = -2;
uint32_T timestamp;
uint8_T severity;

    /* init timestamp and pri values with saturated values */
    timestamp = MAX_uint32_T;
    severity = MAX_uint8_T;

    if(snapNum == SNAP1)
    {
        /* scanning 1st error memory for finding stored DTC with low priority */
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if(DTCSnapshotEE_1[i].severity <= severity)
            {
                severity = DTCSnapshotEE_1[i].severity;
            }
            else 
            {
                /* MISRA 14.10 */
            }
        }

        /* scanning 1st error memory for finding the oldest DTC with low priority found in the previus scan */
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if(DTCSnapshotEE_1[i].severity == severity)
            {
                if (DTCSnapshotEE_1[i].timestamp <= timestamp)
                {
                    idx = i;
                    timestamp = DTCSnapshotEE_1[i].timestamp;
                    *pCode_old = DTCSnapshotEE_1[i].pcode;
                    *ftb_old = DTCSnapshotEE_1[i].fault;
                }
                else
                {
                    /* MISRA 14.10 */
                }
            }
            else
            {
                /* MISRA 14.10 */
            }
        }
    }
    else if(snapNum == SNAP2)
    {
        /* scanning 2nd error memory for finding stored DTC with low priority */
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if(DTCSnapshotEE_2[i].severity <= severity)
            {
                severity = DTCSnapshotEE_2[i].severity;
            }
            else 
            {
                /* MISRA 14.10 */
            }
        }

        /* scanning 2nd error memory for finding the oldest DTC with low priority found in the previus scan */
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if(DTCSnapshotEE_2[i].severity == severity)
            {
                if (DTCSnapshotEE_2[i].timestamp <= timestamp) 
                {
                    idx = i;
                    timestamp = DTCSnapshotEE_2[i].timestamp;
                    *pCode_old = DTCSnapshotEE_2[i].pcode;
                    *ftb_old = DTCSnapshotEE_2[i].fault;
                }
                else
                {
                    /* MISRA 14.10 */
                }
            }
            else
            {
                /* MISRA 14.10 */
            }
        }
    }
    else
    {
        /* MISRA 14.10 */
    }

    return idx;
}


/*--------------------------------------------------------------------------*
 * Function name - Function description
 * FindOldSnapWithLowSeverity_NotPresent - This function searches the oldest
 *                 of lowest severity among not present errors (stored in 
 *                 error memory but with testFailed bit of DTCStatus equal 
 *                 to 0)
 *
 * Implementation notes:
 * In case of one or more snapshot with the same timestamp and severity, last
 * one feasible index is returned.
 *--------------------------------------------------------------------------*/
static int8_T FindOldSnapWithLowSeverity_NotPresent(uint8_T snapNum, uint16_T * pCode_old,uint8_T * ftb_old)
{
uint8_T i;
int8_T idx = -2;
uint32_T timestamp;
uint8_T severity;

    /* init timestamp and pri values with saturated values */
    timestamp = MAX_uint32_T;
    severity = MAX_uint8_T;

    if(snapNum == SNAP1)
    {
        /* scanning 1st error memory for finding stored DTC with low priority */
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if(DTCStatusEE[DTCSnapshotEE_1[i].diagIdx].BF.testFailed != 1u)
            {
                if(DTCSnapshotEE_1[i].severity <= severity)
                {
                    severity = DTCSnapshotEE_1[i].severity;
                }
                else
                {
                    /* MISRA 14.10 */
                }
            }
            else
            {
                /* MISRA 14.10 */
            }
        }

        /* scanning 1st error memory for finding the oldest DTC with low priority found in the previus scan and not present at the moment (testFailed of DTC Status mask = 0)*/
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if(DTCStatusEE[DTCSnapshotEE_1[i].diagIdx].BF.testFailed != 1u)
            {
                if(DTCSnapshotEE_1[i].severity == severity)
                {
                    if (DTCSnapshotEE_1[i].timestamp <= timestamp)
                    {
                        idx = i;
                        timestamp = DTCSnapshotEE_1[i].timestamp;
                        *pCode_old = DTCSnapshotEE_1[i].pcode;
                        *ftb_old = DTCSnapshotEE_1[i].fault;
                    }
                    else
                    {
                        /* MISRA 14.10 */
                    }
                }
                else
                {
                    /* MISRA 14.10 */
                }
            }
            else
            {
                /* MISRA 14.10 */
            }
        }
    }
    else if(snapNum == SNAP2)
    {
        /* scanning 2nd error memory for finding stored DTC with low priority */
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if(DTCStatusEE[DTCSnapshotEE_2[i].diagIdx].BF.testFailed != 1u)
            {
                if(DTCSnapshotEE_2[i].severity <= severity)
                {
                    severity = DTCSnapshotEE_2[i].severity;
                }
                else
                {
                    /* MISRA 14.10 */
                }
            }
            else
            {
                /* MISRA 14.10 */
            }
        }

        /* scanning 2nd error memory for finding the oldest DTC with low priority found in the previus scan and not present at the moment (testFailed of DTC Status mask = 0)*/
        for (i = 0u; i < DIAG_FAULT_LENGTH; i++)
        {
            if(DTCStatusEE[DTCSnapshotEE_2[i].diagIdx].BF.testFailed != 1u)
            {
                if(DTCSnapshotEE_2[i].severity == severity)
                {
                    if (DTCSnapshotEE_2[i].timestamp <= timestamp) 
                    {
                        idx = i;
                        timestamp = DTCSnapshotEE_2[i].timestamp;
                        *pCode_old = DTCSnapshotEE_2[i].pcode;
                        *ftb_old = DTCSnapshotEE_2[i].fault;
                    }
                    else
                    {
                        /* MISRA 14.10 */
                    }
                }
                else
                {
                    /* MISRA 14.10 */
                }
            }
            else
            {
                /* MISRA 14.10 */
            }
        }
    }
    else
    {
        /* MISRA 14.10 */
    }

    return idx;
}

static uint8_T getConfirmedFTB(uint16_T pcode, uint8_T FTB)
{
uint8_T i,k;
uint8_T res;

    for (i = 0u;i < DIAG_NUMBER; i++)
    {
        if (pcode == DIAGNOSTICLINES[i][0])
        {
            for (k = 0u; k < MAX_FAULT_FOR_LINE; k++)
            {
                if (FTB == DIAGNOSTICLINES[i][k+1])
                {
                    switch(k)
                    {
                        case 0:
                            res = confirmedDTC_oldEE[i].BF.confFTB0;
                            break;
                        case 1:
                            res = confirmedDTC_oldEE[i].BF.confFTB1;
                            break;
                        case 2:
                            res = confirmedDTC_oldEE[i].BF.confFTB2;
                            break;
                        case 3:
                            res = confirmedDTC_oldEE[i].BF.confFTB3;
                            break;
                        case 4: // EI2FE-309
                            res = confirmedDTC_oldEE[i].BF.confFTB4;
                            break;

                        default:
                            break;
                    }
                }
            }
        }
    }

    return res;
}

static void setConfirmedFTB(uint16_T pcode, uint8_T FTB, uint8_T value)
{
uint8_T i,k;

    for (i = 0u;i < DIAG_NUMBER; i++)
    {
        if (pcode == DIAGNOSTICLINES[i][0])
        {
            for (k = 0u; k < MAX_FAULT_FOR_LINE; k++)
            {
                if (FTB == DIAGNOSTICLINES[i][k+1])
                {
                    switch(k)
                    {
                        case 0:
                            confirmedDTC_oldEE[i].BF.confFTB0 = value;
                            break;
                        case 1:
                            confirmedDTC_oldEE[i].BF.confFTB1 = value;
                            break;
                        case 2:
                            confirmedDTC_oldEE[i].BF.confFTB2 = value;
                            break;
                        case 3:
                            confirmedDTC_oldEE[i].BF.confFTB3 = value;
                            break;
                        case 4: // EI2FE-309
                            confirmedDTC_oldEE[i].BF.confFTB4 = value;
                            break;
                        default:
                            break;
                    }
                }
            }
        }
    }

    return;
}
#endif
#endif
#else
void DiagMgm_SetDiagState(uint8_T id, typPtFault fault, uint8_T *state)
{
}

#endif // _BUILD_DIAGMGM_

