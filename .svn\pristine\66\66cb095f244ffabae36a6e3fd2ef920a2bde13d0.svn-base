/*
 * File: mul_s32_s32_s32_sr18.h
 *
 * Code generated for Simulink model 'IONChargeCtrl'.
 *
 * Model version                  : 1.123
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Mon Feb 05 15:05:01 2018
 */

#ifndef SHARE_mul_s32_s32_s32_sr18
#define SHARE_mul_s32_s32_s32_sr18
#include "rtwtypes.h"

extern int32_T mul_s32_s32_s32_sr18(int32_T a, int32_T b);

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
