/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#include "rtwtypes.h"


#pragma ghs section data=".ee_id8_data"

#ifdef _TEST_EEPROM_
uint32_T Prova_ID8[8] = {  
                            0x88888888u, 0x88888888u, 0x88888888u, 0x88888888u,
                            0x88888888u, 0x88888888u, 0x88888888u, 0x88888888u 
                        };
#endif

// Declare here all the variables to be stored in EEPROM with ID8
///Dummy_EE ID8
uint32_T EEDummyID8_00 = 0u;
uint32_T EEDummyID8_01 = 0u;
uint32_T EEDummyID8_02 = 0u;
uint32_T EEDummyID8_03 = 0u;

#ifdef _BUILD_RONDETECTCROSS_
#pragma ghs startnomisra
    #include "rondetectcross_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_RONDETECTFUEL_
#pragma ghs startnomisra
    #include "rondetectfuel_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_RONDETECTMGM_
#pragma ghs startnomisra
    #include "rondetectmgm_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_RONDETECTEST_
#pragma ghs startnomisra
    #include "rondetectest_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_MKNOCKDET_
#pragma ghs startnomisra
    #include "MKnockDet_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_SPARKPLUGTEST_
#pragma ghs startnomisra
    #include "SparkPlugTest_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_CANMGM_
#pragma ghs startnomisra
    #include "canmgm_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_COMBADP_
#pragma ghs startnomisra
    #include "combadp_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_TIMING_
#pragma ghs startnomisra
    #include "timing_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_DIAGCANMGM_
#pragma ghs startnomisra
    #include "DiagCanMgm_Ferrari_eepID8.c"
#pragma ghs endnomisra
#endif


/* EOF EEPROM */

