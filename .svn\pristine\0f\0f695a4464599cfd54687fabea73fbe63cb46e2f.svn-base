/*
 * Trial License - for use to evaluate programs for possible purchase as
 * an end-user only.
 *
 * File: div_nzp_ssu32_floor.c
 *
 * Code generated for Simulink model 'PlaCtrl'.
 *
 * Model version                  : 1.149
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Thu Sep 19 09:27:14 2019
 */

#include "rtwtypes.h"
#include "div_nzp_ssu32_floor.h"

int32_T div_nzp_ssu32_floor(int32_T numerator, uint32_T denominator)
{
  uint32_T absNumerator;
  uint32_T tempAbsQuotient;
  boolean_T quotientNeedsNegation;
  absNumerator = (numerator < 0) ? ((~((uint32_T)numerator)) + 1U) : ((uint32_T)
    numerator);
  quotientNeedsNegation = (numerator < 0);
  tempAbsQuotient = absNumerator / denominator;
  if (quotientNeedsNegation) {
    absNumerator %= denominator;
    if (absNumerator > 0U) {
      tempAbsQuotient++;
    }
  }

  return quotientNeedsNegation ? (-((int32_T)tempAbsQuotient)) : ((int32_T)
    tempAbsQuotient);
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
