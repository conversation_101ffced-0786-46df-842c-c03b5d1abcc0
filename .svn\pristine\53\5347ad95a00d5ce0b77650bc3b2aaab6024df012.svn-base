/**************************************************************************/
/* FILE NAME: mpc5554_spr.h                  COPYRIGHT (c) Freescale 2004 */
/* VERSION:  1.00                                 All Rights Reserved     */
/*                                                                        */
/* DESCRIPTION:                                                           */
/* This file contain all of the SPR register and bit field definitions    */
/* for the MPC5554. There are no address assignments for the SPR          */
/* registers. Instead, the bit field structures are only assigned.        */
/* SPR register numbers are also assigned.                                */
/*========================================================================*/
/* UPDATE HISTORY                                                         */
/* REV      AUTHOR      DATE       DESCRIPTION OF CHANGE                  */
/* ---   -----------  ---------    ---------------------                  */
/* 0.01  G<PERSON> Jackson   13/Nov/03    Initial version of file for SPR        */
/*                                 registers in the MPC5554.              */
/*                                  Based on SoC version 0.7.             */
/* 1.0   G. Jackson   23/Jan/04    Replaced MASnVAL with MASnCVAL to      */
/*                                  maintain unique function defintions.  */
/* 1.1   G. Jackson   19/Jul/04    #endif note placed after _MPC5554_SPR_ */
/*                                  #ifndef and #define.                  */
/*                                 Changed structures to typedefs that    */
/*                                  would be instantiated by customer     */
/*                                  definition later in code.             */
/* 1.2   G.Jackson    14/Sep/04    Added SPR_ prefixes for unique names.  */
/**************************************************************************/

/* >>>>NOTE! this file describes fixed special purpose registers.  */
/*           Please do not edit it directly!<<<<                   */

#ifndef _MPC5554_SPR_
#define _MPC5554_SPR_
/* This ifndef has a corresponding #endif at the bottom of this file  */
/*  so that it will only be included once.                            */
#pragma ghs startnomisra
#include "rtwtypes.h"

#ifdef  __cplusplus
extern "C" {
#endif

/********************************************/
/* Example instantiation and use:           */
/*  union CRVAL my_cr;                      */
/*  my_cr.B.CR0 = 1;                        */
/*  my_cr.R = 0x10000000                    */
/********************************************/



/****************************************************************************/
/*                   CPU REGISTERS: General Registers                       */
/****************************************************************************/
        union SPR_CRVAL {
            vuint32_T R;
            struct {
                vuint32_T CR0:4;
                vuint32_T CR1:4;
                vuint32_T CR2:4;
                vuint32_T CR3:4;
                vuint32_T CR4:4;
                vuint32_T CR5:4;
                vuint32_T CR6:4;
                vuint32_T CR7:4;
            } B;
        };
        
        union SPR_LRVAL {
            vuint32_T R;
            struct {
                vuint32_T LINKADDRESS:32;
            } B;
        };
        
        union SPR_CTRVAL {
            vuint32_T R;
            struct {
                vuint32_T COUNTVALUE:32;
            } B;
        };
        
        union SPR_XERVAL {
            vuint32_T R;
            struct {
                vuint32_T SO:1;
                vuint32_T OV:1;
                vuint32_T CA:1;
                vuint32_T :22;
                vuint32_T BYTECNT:7;
            } B;
        }; 
        
        
/****************************************************************************/
/*                   CPU REGISTERS: Processor Control Registers             */
/****************************************************************************/
        union SPR_MSRVAL {
            vuint32_T R;
            struct {
                vuint32_T :5;
                vuint32_T UCLE:1;
                vuint32_T SPE:1;
                vuint32_T :6;
                vuint32_T WE:1;
                vuint32_T CE:1;
                vuint32_T :1;
                vuint32_T EE:1;
                vuint32_T PR:1;
                vuint32_T FP:1;
                vuint32_T ME:1;
                vuint32_T FE0:1;
                vuint32_T :1;
                vuint32_T DE:1;
                vuint32_T FE1:1;
                vuint32_T :2;
                vuint32_T IS:1;
                vuint32_T DS:1;
                vuint32_T :4;
            } B;
        };  
        
        union SPR_PIRVAL {
            vuint32_T R;
            struct {
                vuint32_T :24;
                vuint32_T ID:8;
            } B;
        };
        
        union SPR_PVRVAL {
            vuint32_T R;
            struct {
                vuint32_T MFGID:4;
                vuint32_T :2;
                vuint32_T TYPE:6;
                vuint32_T VER:4;
                vuint32_T MGBUSE:8;
                vuint32_T MJRREV:4;
                vuint32_T MGBID:4;
            } B;
        };  
        
         union SPR_SVRVAL {
            vuint32_T R;
            struct {
                vuint32_T SYSVER:32;
            } B;
        };   
        
        union SPR_HID0VAL {
            vuint32_T R;
            struct {
                vuint32_T EMCP:1;
                vuint32_T :5;
                vuint32_T BPRED:2;
                vuint32_T DOZE:1;
                vuint32_T NAP:1;
                vuint32_T SLEEP:1;
                vuint32_T :3;
                vuint32_T ICR:1;
                vuint32_T NHR:1;
                vuint32_T :1;
                vuint32_T TBEN:1;
                vuint32_T SEL_TBCLK:1;
                vuint32_T DCLREE:1;
                vuint32_T DCLRCE:1;
                vuint32_T CICLRDE:1;
                vuint32_T MCCLRDE:1;
                vuint32_T DAPUEN:1;
                vuint32_T :8;
            } B;
        };  
        
        union SPR_HID1VAL {
            vuint32_T R;
            struct {
                vuint32_T :24;
                vuint32_T ATS:1;
                vuint32_T :7;
            } B;
        };
        
/****************************************************************************/
/*                   CPU REGISTERS: TIMERS                                  */
/****************************************************************************/
        
        union SPR_TBLVAL {
            vuint32_T R;
            struct {
                vuint32_T TBLVALUE:32;
            } B;
        };  
        
        union SPR_TBUVAL {
            vuint32_T R;
            struct {
                vuint32_T TBUVALUE:32;
            } B;
        };
        
        union SPR_TCRVAL {
            vuint32_T R;
            struct {
                vuint32_T WP:2;
                vuint32_T WRC:2;
                vuint32_T WIE:1;
                vuint32_T DIE:1;
                vuint32_T FP:2;
                vuint32_T FIE:1;
                vuint32_T ARE:1;
                vuint32_T :1;
                vuint32_T WPEXT:4;
                vuint32_T FPEXT:4;
                vuint32_T :13;
            } B;
        };      
        
        union SPR_TSRVAL {
            vuint32_T R;
            struct {
                vuint32_T ENW:1;
                vuint32_T WIS:1;
                vuint32_T WRS:2;
                vuint32_T DIS:1;
                vuint32_T FIS:1;
                vuint32_T :26;
            } B;
        };
        
        
        union SPR_DECVAL {
            vuint32_T R;
            struct {
                vuint32_T DECVALUE:32;
            } B;
        };
        
        union SPR_DECARVAL {
            vuint32_T R;
            struct {
                vuint32_T DECARVALUE:32;
            } B;
        };
        
        
        
/****************************************************************************/
/*                   CPU REGISTERS: MMU                                     */
/****************************************************************************/
        union SPR_PID0VAL {
            vuint32_T R;
            struct {
                vuint32_T :24;
                vuint32_T PID:8;
            } B;
        };
  
        union SPR_MMUCSR0VAL {
            vuint32_T R;
            struct {
                vuint32_T :30;
                vuint32_T TLBCAM_FI:1;
                vuint32_T:1;
            } B;
        };
        
        union SPR_MMUCFGVAL {
            vuint32_T R;
            struct {
                vuint32_T:17;
                vuint32_T NPIDS:4;
                vuint32_T PIDSIZE:5;
                vuint32_T:2;
                vuint32_T NLTBS:2;
                vuint32_T MAVN:2;
            } B;
        };
        
        union SPR_TLB0CFGVAL {
            vuint32_T R;
            struct {
                vuint32_T ASSOC:8;
                vuint32_T MINSIZE:4;
                vuint32_T MAXSIZE:4;
                vuint32_T IPROT:1;
                vuint32_T AVAIL:1;
                vuint32_T :2;
                vuint32_T NENTRY:12;
            } B;
        };
        
        union SPR_TLB1CFGVAL {
            vuint32_T R;
            struct {
                vuint32_T ASSOC:8;
                vuint32_T MINSIZE:4;
                vuint32_T MAXSIZE:4;
                vuint32_T IPROT:1;
                vuint32_T AVAIL:1;
                vuint32_T :2;
                vuint32_T NENTRY:12;
            } B;
        };
    
        union SPR_MAS0CVAL {
            vuint32_T R;
            struct {
                vuint32_T:2;
                vuint32_T TLBSEL:2;
                vuint32_T:7;
                vuint32_T ESELCAM:5;
                vuint32_T:11;
                vuint32_T NVCAM:5;
            } B;
        };
  
        union SPR_MAS1CVAL {
            vuint32_T R;
            struct {
                vuint32_T VALID:1;
                vuint32_T IPROT:1;
                vuint32_T:6;
                vuint32_T TID:8;
                vuint32_T:3;
                vuint32_T TS:1;
                vuint32_T TSIZ:4;
                vuint32_T:8;
            } B;
        };
    
        union SPR_MAS2CVAL {
            vuint32_T R;
            struct {
                vuint32_T EPN:20;  /* Effective Page Number            */
                vuint32_T:7;
                vuint32_T W:1;     /* Write through required;          */
                                   /*  0=write back; 1=write through   */
                vuint32_T I:1;     /* Cache Inhibit; 0=not inhibited   */
                vuint32_T M:1;     /* Memory coherence; 0=not required */
                vuint32_T G:1;     /* Gaurded; 0=not gaurded           */
                vuint32_T E:1;     /* Endianess; 0=Big; 1=Little       */
            } B;
        };
  
        union SPR_MAS3CVAL {
            vuint32_T R;
            struct {
                vuint32_T RPN:20;   /* Real Page Number              */
                vuint32_T:2;
                vuint32_T U0:1;     /* User bits [0:3]               */
                vuint32_T U1:1;
                vuint32_T U2:1;
                vuint32_T U3:1;
                vuint32_T UX:1;     /* Permission bits               */
                vuint32_T SX:1;
                vuint32_T UW:1;
                vuint32_T SW:1;           
                vuint32_T UR:1;
                vuint32_T SR:1;
            } B;
        };
 
        union SPR_MAS4CVAL {
            vuint32_T R;
            struct {
                vuint32_T:2;
                vuint32_T TLBSELD:2;
                vuint32_T:10;
                vuint32_T TIDSELD:2;
                vuint32_T:4;
                vuint32_T TSIZED:4;
                vuint32_T :3;
                vuint32_T WD:1;
                vuint32_T ID:1;
                vuint32_T MD:1;
                vuint32_T GD:1;
                vuint32_T ED:1;
            } B;
        };
   
        union SPR_MAS6CVAL {
            vuint32_T R;
            struct {
                vuint32_T:8;
                vuint32_T SPID:8;
                vuint32_T:15;
                vuint32_T SAS:1;
            } B;
        };
        
/****************************************************************************/
/*                   CPU REGISTERS: CACHE                                   */
/****************************************************************************/
        union SPR_L1CFG0VAL {   /* Read only register */
            vuint32_T R;
            struct {
                vuint32_T CARCH:2;  /* Cache Architecture; 01=Unified      */
                vuint32_T CWPA:1;   /* Cache way partitioning available =1 */
                vuint32_T CFAHA:1;  /* Cache Flush by all avail; 0=not     */ 
                vuint32_T CFISWA:1; /* Cache Flush Inv by set & way avail=1 */
                vuint32_T :2;
                vuint32_T CBSIZE:2; /* Block Size 00=32 bytes         */
                vuint32_T CREPL:2;  /* Replacement Policy 10=pseudo round robin */
                vuint32_T CLA:1;    /* Line locking APU; 1=avail      */
                vuint32_T CPA:1;    /* Parity available  1=avail      */
                vuint32_T CNWAY:8;  /* Num of ways; 0x03=4way, 0x07=8way */
                vuint32_T CSIZE:11; /* Size; 0x008=8KB, 0x010=16KB,0x020=32KB */
            } B;
        };              /* Read only register */
        
        
        union SPR_L1CSR0VAL {
            vuint32_T R;
            struct {
                vuint32_T WID:4;  
                vuint32_T WDD:4;
                vuint32_T AWD:1;
                vuint32_T AWDD:1;
                vuint32_T :1;
                vuint32_T CWM:1;
                vuint32_T DPP:1;
                vuint32_T DSB:1;
                vuint32_T DSTR:1;
                vuint32_T CPE:1;
                vuint32_T :5;
                vuint32_T CUL:1;
                vuint32_T CLO:1;
                vuint32_T CLFC:1;
                vuint32_T :5;
                vuint32_T CABT:1;
                vuint32_T CINV:1;
                vuint32_T CE:1;
            } B;
        };
        
        union SPR_L1FINV0VAL {
            vuint32_T R;
            struct {
                vuint32_T :5;
                vuint32_T CWAY:3;
                vuint32_T :12;
                vuint32_T CSET:7;
                vuint32_T :3;
                vuint32_T CCMD:2;
            } B;
        };        
        
/****************************************************************************/
/*                   CPU REGISTERS: APU                                     */
/****************************************************************************/
        union SPR_SPEFSCRVAL {  /* Status and Control of SPE instructions */
            vuint32_T R;
            struct {
                vuint32_T SOVH:1;
                vuint32_T OVH:1;
                vuint32_T FGH:1;
                vuint32_T FXH:1;
                vuint32_T FINVH:1;
                vuint32_T FDBZH:1;
                vuint32_T FUNFH:1;
                vuint32_T FOVFH:1;
                vuint32_T :2;
                vuint32_T FINXS:1;
                vuint32_T FINVS:1;
                vuint32_T FDBZS:1;
                vuint32_T FUNFS:1;
                vuint32_T FOVFS:1;
                vuint32_T MODE:1;
                vuint32_T SOV:1;
                vuint32_T OV:1;
                vuint32_T FG:1;
                vuint32_T FX:1;
                vuint32_T FINV:1;
                vuint32_T FDBZ:1;
                vuint32_T FUNF:1;
                vuint32_T FOVF:1;
                vuint32_T :1;
                vuint32_T FINXE:1;
                vuint32_T FINVE:1;
                vuint32_T FDBZE:1;
                vuint32_T FUNFE:1;
                vuint32_T FOVFE:1;
                vuint32_T FRMC:2;
            } B;
        }; 
        
/****************************************************************************/
/*                   CPU REGISTERS: Exception Handling/Control Registers    */
/****************************************************************************/
        union SPR_SPRGVAL { /* There are [8] entries for this tag */
            vuint32_T R;
            struct {
                vuint32_T SPRDATA:32;
            } B;
        };
        
        union SPR_USPRG0VAL {
            vuint32_T R;
            struct {
                vuint32_T USPR0DATA:32;
            } B;
        };
        
       union SPR_SRR0VAL {
            vuint32_T R;
            struct {
                vuint32_T NXTADDR:32;
            } B;
        }; 
        
       union SPR_SRR1VAL {
            vuint32_T R;
            struct {
                vuint32_T MSRSTATE:32;
            } B;
        }; 
        
        union SPR_CSRR0VAL {
            vuint32_T R;
            struct {
                vuint32_T CRITNXTADDR:32;
            } B;
        }; 
        
       union SPR_CSRR1VAL {
            vuint32_T R;
            struct {
                vuint32_T CRITMSRSTATE:32;
            } B;
        }; 
        
        union SPR_DSRR0VAL {
            vuint32_T R;
            struct {
                vuint32_T DBGNXTADDR:32;
            } B;
        }; 
        
       union SPR_DSRR1VAL {
            vuint32_T R;
            struct {
                vuint32_T DBGMSRSTATE:32;
            } B;
        }; 
        
       union SPR_DEARVAL {
            vuint32_T R;
            struct {
                vuint32_T DATEFADDR:16;
                vuint32_T :16;
            } B;
        }; 
        
        union SPR_ESRVAL {
            vuint32_T R;
            struct {
                vuint32_T :4;
                vuint32_T PIL:1;
                vuint32_T PPR:1;
                vuint32_T PTR:1;
                vuint32_T FP:1;
                vuint32_T ST:1;
                vuint32_T :1;
                vuint32_T DLK:1;
                vuint32_T ILK:1;
                vuint32_T AP:1;
                vuint32_T PUO:1;
                vuint32_T BO:1;
                vuint32_T PIE:1;
                vuint32_T :8;
                vuint32_T SPE:1;
                vuint32_T :6;
                vuint32_T XTE:1;
            } B;
        };
        
        
        union SPR_MCSRVAL {
            vuint32_T R;
            struct {
                vuint32_T MCP:1;
                vuint32_T IC_DPERR:1;
                vuint32_T :2;
                vuint32_T EXCP_ERR:1;
                vuint32_T IC_TPERR:1;
                vuint32_T :1;
                vuint32_T IC_LKERR:1;
                vuint32_T :3;
                vuint32_T NMI:1;
                vuint32_T MAV:1;
                vuint32_T MEA:1;
                vuint32_T :1;
                vuint32_T IF:1;
                vuint32_T LD:1;
                vuint32_T ST:1;
                vuint32_T G:1;
                vuint32_T :8;
                vuint32_T BUS_IRERR:1;
                vuint32_T BUS_DRERR:1;
                vuint32_T BUS_WRERR:1;
                vuint32_T :2;
            } B;
        };
        
       union SPR_IVPRVAL {
            vuint32_T R;
            struct {
                vuint32_T VECBASE:16;
                vuint32_T :16;
            } B;
        }; 
        
 /* Note: IVOR0 is not supported by the MPC5554 */       
        union SPR_IVORVAL {  /* There are [16] entries for this tag */
            vuint32_T R;
            struct {
                vuint32_T :16;
                vuint32_T VECOFFSET:12;
                vuint32_T :4;
            } B;
        };
        
        union SPR_IVOR32VAL {
            vuint32_T R;
            struct {
                vuint32_T :16;
                vuint32_T VECOFFSET:12;
                vuint32_T :4;
            } B;
        };
        
        union SPR_IVOR33VAL {
            vuint32_T R;
            struct {
                vuint32_T :16;
                vuint32_T VECOFFSET:12;
                vuint32_T :4;
            } B;
        };
        
        union SPR_IVOR34VAL {
            vuint32_T R;
            struct {
                vuint32_T :16;
                vuint32_T VECOFFSET:12;
                vuint32_T :4;
            } B;
        }; 
        
/****************************************************************************/
/*                   CPU REGISTERS: DEBUG                                     */
/****************************************************************************/
        union SPR_DBCR1VAL {
            vuint32_T R;
            struct {
                vuint32_T IAC1US:2;
                vuint32_T IAC1ER:2;
                vuint32_T IAC2US:2;
                vuint32_T IAC2ER:2;
                vuint32_T IAC12M:2;
                vuint32_T :6;
                vuint32_T IAC3US:2;
                vuint32_T IAC3ER:2;
                vuint32_T IAC4US:2;
                vuint32_T IAC4ER:2;
                vuint32_T IAC34M:2;
                vuint32_T :6;
            } B;
        };
        
        union SPR_DBCR2VAL {
            vuint32_T R;
            struct {
                vuint32_T DIAC1US:2;
                vuint32_T DIAC1ER:2;
                vuint32_T DIAC2US:2;
                vuint32_T DIAC2ER:2;
                vuint32_T DIAC12M:2;
                vuint32_T DAC1LNK:2;
                vuint32_T DAC2LNK:2;
                vuint32_T :20;
            } B;
        };
        
         union SPR_DBCR3VAL {
            vuint32_T R;
            struct {
                vuint32_T DEVT1C1:1;
                vuint32_T DEVT2C1:1;
                vuint32_T ICMPC1:1;
                vuint32_T IAC1C1:1;
                vuint32_T IAC2C1:1;
                vuint32_T IAC3C1:1;
                vuint32_T IAC4C1:1;
                vuint32_T DAC1RC1:1;
                vuint32_T DAC1WC1:1;
                vuint32_T DAC2RC1:1;
                vuint32_T DAC2WC1:1;
                vuint32_T IRPTC1:1;
                vuint32_T RETC1:1;
                vuint32_T DEVT1C2:1;
                vuint32_T DEVT2C2:1;
                vuint32_T ICMPC2:1;
                vuint32_T IAC1C2:1;
                vuint32_T IAC2C2:1;
                vuint32_T IAC3C2:1;
                vuint32_T IAC4C2:1;
                vuint32_T DAC1RC2:1;
                vuint32_T DAC1WC2:1;
                vuint32_T DAC2RC2:1;
                vuint32_T DAC2WC2:1;
                vuint32_T DEVT1T1:1;
                vuint32_T DEVT2T1:1;
                vuint32_T IAC1T1:1;
                vuint32_T IAC3T1:1;
                vuint32_T DAC1RT1:1;
                vuint32_T DAC1WT1:1;
                vuint32_T CNT2T1:1;
                vuint32_T CONFIG:1;
            } B;
        };
        
        union SPR_DBSRVAL {
            vuint32_T R;
            struct {
                vuint32_T IDE:1;
                vuint32_T UDE:1;
                vuint32_T MRR:2;
                vuint32_T ICMP:1;
                vuint32_T BRT:1;
                vuint32_T IRPT:1;
                vuint32_T TRAP:1;
                vuint32_T IAC1:1;
                vuint32_T IAC2:1;
                vuint32_T IAC3:1;
                vuint32_T IAC4:1;
                vuint32_T DAC1R:1;
                vuint32_T DAC1W:1;
                vuint32_T DAC2R:1;
                vuint32_T DAC2W:1;
                vuint32_T RET:1;
                vuint32_T :4;
                vuint32_T DEVT1:1;
                vuint32_T DEVT2:1;
                vuint32_T DCNT1:1;
                vuint32_T DCNT2:1;
                vuint32_T CIRPT:1;
                vuint32_T CRET:1;
                vuint32_T :4;
                vuint32_T CNT1RG:1;
            } B;
        }; 
        
        union SPR_DBCNTVAL {
            vuint32_T R;
            struct {
                vuint32_T CNT1:16;
                vuint32_T CNT2:16;
           } B;
        };
        
        union SPR_IAC1VAL {
            vuint32_T R;
            struct {
                vuint32_T INSTADDR:30;
                vuint32_T :2;
           } B;
        };
        
        union SPR_IAC2VAL {
            vuint32_T R;
            struct {
                vuint32_T INSTADDR:30;
                vuint32_T :2;
           } B;
        };
        
        union SPR_IAC3VAL {
            vuint32_T R;
            struct {
                vuint32_T INSTADDR:30;
                vuint32_T :2;
           } B;
        };
        
        union SPR_IAC4VAL {
            vuint32_T R;
            struct {
                vuint32_T INSTADDR:30;
                vuint32_T :2;
           } B;
        };
        
        
        union SPR_DAC1VAL {
            vuint32_T R;
            struct {
                vuint32_T DATTADDR:32;
           } B;
        };
        
        union SPR_DAC2VAL {
            vuint32_T R;
            struct {
                vuint32_T DATTADDR:32;
           } B;
        };
        
        
             
/*****************************************************/
/* Define instances of modules                       */
/*  with special register numbers                    */
/*****************************************************/

/* The CR register does not have an SPR# */
/* The GPR registers do not have an SPR# */
/* The MSR register does not have an SPR# */

#define SPR_LR          8u
#define SPR_CTR         9u
#define SPR_XER         1u

#define SPR_PIR       286u
#define SPR_PVR       287u
#define SPR_SVR      1023u
#define SPR_HID0     1008u
#define SPR_HID1     1009u

#define SPR_TBL       284u
#define SPR_TBU       285u
#define SPR_TCR       340u
#define SPR_TSR       336u
#define SPR_DEC        22u
#define SPR_DECAR      54u

#define SPR_PID0       48u
#define SPR_MMUCSR0  1012u
#define SPR_MMUCFG   1015u
#define SPR_TLB0CFG   688u
#define SPR_TLB1CFG   689u
#define SPR_MAS0      624u
#define SPR_MAS1      625u
#define SPR_MAS2      626u
#define SPR_MAS3      627u
#define SPR_MAS4      628u
#define SPR_MAS6      630u

#define SPR_L1CFG0    515u
#define SPR_L1CSR0   1010u
#define SPR_L1CSR1   1011u
#define SPR_L1FINV0  1016u

#define SPR_SPEFSCR   512u

#define SPR_SPRG0     272u
#define SPR_SPRG1     273u
#define SPR_SPRG2     274u
#define SPR_SPRG3     275u
#define SPR_SPRG4     276u
#define SPR_SPRG5     277u
#define SPR_SPRG6     278u
#define SPR_SPRG7     279u
#define SPR_USPRG0    256u
#define SPR_SRR0       26u
#define SPR_SRR1       27u
#define SPR_CSRR0      58u
#define SPR_CSRR1      59u
#define SPR_DSRR0     574u
#define SPR_DSRR1     575u
#define SPR_ESR        62u
#define SPR_MCSR      572u
#define SPR_DEAR       61u
#define SPR_IVPR       63u
#define SPR_IVOR0     400u
#define SPR_IVOR1     401u
#define SPR_IVOR2     402u
#define SPR_IVOR3     403u
#define SPR_IVOR4     404u
#define SPR_IVOR5     405u
#define SPR_IVOR6     406u
#define SPR_IVOR7     407u
#define SPR_IVOR8     408u
#define SPR_IVOR9     409u
#define SPR_IVOR10    410u
#define SPR_IVOR11    411u
#define SPR_IVOR12    412u
#define SPR_IVOR13    413u
#define SPR_IVOR14    414u
#define SPR_IVOR15    415u
#define SPR_IVOR32    528u
#define SPR_IVOR33    529u
#define SPR_IVOR34    530u

#define SPR_DBCR0     308u
#define SPR_DBCR1     309u
#define SPR_DBCR2     310u
#define SPR_DBCR3     561u
#define SPR_DBSR      304u
#define SPR_DBCNT     562u
#define SPR_IAC1      312u
#define SPR_IAC2      313u
#define SPR_IAC3      314u
#define SPR_IAC4      315u
#define SPR_DAC1      316u
#define SPR_DAC2      317u



#ifdef  __cplusplus
}
#endif 
#pragma ghs endnomisra
#endif  /* ends inclusion of #ifndef __MPC5554_SPR_ for one instantiation */

/***********************************************************************/
/*                                                                     */
/* Copyright:                                                          */
/*   FREESCALE, INC. All Rights Reserved.                              */
/*  You are hereby granted a copyright license to use, modify, and     */
/*  distribute the SOFTWARE so long as this entire notice is           */
/*  retained without alteration in any modified and/or redistributed   */
/*  versions, and that such modified versions are clearly identified   */
/*  as such. No licenses are granted by implication, estoppel or       */
/*  otherwise under any patents or trademarks of Freescale, Inc. This  */
/*  software is provided on an "AS IS" basis and without warranty.     */
/*                                                                     */
/*  To the maximum extent permitted by applicable law, FREESCALE       */
/*  DISCLAIMS ALL WARRANTIES WHETHER EXPRESS OR IMPLIED, INCLUDING     */
/*  IMPLIED WARRANTIES OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR  */
/*  PURPOSE AND ANY WARRANTY AGAINST INFRINGEMENT WITH REGARD TO THE   */
/*  SOFTWARE (INCLUDING ANY MODIFIED VERSIONS THEREOF) AND ANY         */
/*  ACCOMPANYING WRITTEN MATERIALS.                                    */
/*                                                                     */
/*  To the maximum extent permitted by applicable law, IN NO EVENT     */
/*  SHALL FREESCALE BE LIABLE FOR ANY DAMAGES WHATSOEVER (INCLUDING    */
/*  WITHOUT LIMITATION, DAMAGES FOR LOSS OF BUSINESS PROFITS, BUSINESS */
/*  INTERRUPTION, LOSS OF BUSINESS INFORMATION, OR OTHER PECUNIARY     */
/*  LOSS) ARISING OF THE USE OR INABILITY TO USE THE SOFTWARE.         */
/*                                                                     */
/*  Freescale assumes no responsibility for the maintenance and        */
/*  support of this software                                           */
/***********************************************************************/
