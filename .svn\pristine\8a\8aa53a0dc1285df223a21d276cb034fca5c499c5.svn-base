/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  FLASH
**  Filename        :  Flash.h
**  Created on      :  08-apr-2021 12:01:00
**  Original author :  CarboniM
******************************************************************************/
#ifndef _FLASH_H_
#define _FLASH_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Flash_out.h"
#include "sys.h"
#include "ssd_types.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* Indication of code flash bank */
#define FLASH_CODE_BANK         (0)
/* Indication of data flash bank */ 
#define FLASH_DATA_BANK         (1)



extern SSD_CONFIG ssdConfig_BK0A0;
extern CONTEXT_DATA pgmCtxData;
extern CONTEXT_DATA dummyCtxData;
extern CONTEXT_DATA pgmCtxData;
extern CONTEXT_DATA bcCtxData;
extern CONTEXT_DATA pvCtxData;
extern CONTEXT_DATA csCtxData;

#pragma ghs startnomisra
#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1u)
extern unsigned short FlashErase_C[];
extern unsigned short FlashCheckStatus_C[];
extern unsigned short BlankCheck_C[];
extern unsigned short FlashProgram_C[];
extern unsigned short ProgramVerify_C[];
extern unsigned short CheckSum_C[];
#else
extern const unsigned short FlashErase_C[];
extern const unsigned short FlashCheckStatus_C[];
extern const unsigned short BlankCheck_C[];
extern const unsigned short FlashProgram_C[];
extern const unsigned short ProgramVerify_C[];
extern const unsigned short CheckSum_C[];
#endif
#pragma ghs endnomisra

uint32_T FlashErase_Asynch(uint32_T blkslctLow, uint32_T blkslctMiddle, uint32_T blkslctHigh, NLARGE_BLOCK_SEL blkslctLarge, uint8_T callback_module);
uint32_T FlashProgram_Asynch( uint32_T dest, uint32_T size, uint32_T source);
uint32_T FlashBlankCheck_Asynch( uint32_T address, uint32_T size);
uint32_T FlashProgramVerify_Asynch( uint32_T address, uint32_T size, uint32_T source);
uint32_T FlashChecksum_Asynch(uint32_T dest, uint32_T size, uint32_T* sum);
void FlashEraseCallback(uint8_T callback_module);
void FlashProgramCallback(void);  
void FlashProgramVerifyCallback(void);
void FlashBlankCheckCallback(void);
void FlashChecksumCallback(void);

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static int16_T FlashCheckFlashBank(uint32_T start, uint32_T size, uint8_T *flashBank, PSSD_CONFIG *pSSDConfig);
static void FlashDataCheckMemIndex(uint32_T start, uint32_T size, uint32_T* blkslctLow, uint32_T* blkslctMiddle, uint32_T* blkslctHigh, NLARGE_BLOCK_SEL* blkslctLarge);
static void FlashCheckMemIndex(uint32_T start, uint32_T size, uint32_T* blkslctLow,uint32_T* blkslctMiddle,uint32_T* blkslctHigh, NLARGE_BLOCK_SEL* blkslctLarge);
static int16_T FlashUnlockBlocks(PSSD_CONFIG pSSDConfig, const uint32_T blkslctLow, const uint32_T blkslctMiddle, const uint32_T blkslctHigh, const NLARGE_BLOCK_SEL blkslctLarge);
static int16_T FlashRelockBlocks(PSSD_CONFIG pSSDConfig, const uint32_T blkslctLow, const uint32_T blkslctMiddle, const uint32_T blkslctHigh, const NLARGE_BLOCK_SEL blkslctLarge);

#endif /* _FLASH_H_ */

/****************************************************************************
 ****************************************************************************/
