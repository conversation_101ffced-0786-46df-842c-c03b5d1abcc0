/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef EEPROM_H
#define EEPROM_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "ee_out.h"


/*!
\defgroup PrivateDefines Private Defines
\sgroup
*/
/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
///This is a private define 
/*defines to select update strategy*/
/*blocks not inhibited*/
#define UPDATE_BLK0_BLK1                0u
#define UPDATE_BLK0_BLK1FULL            1u
#define UPDATE_BLK1_BLK0FULL            2u
#define EEPROM_FULL                     3u
/*error on block 0  */
#define UPDATE_BLK1_BLK0INHIBITED       4u
#define UPDATE_BLK1FULL_BLK0INHIBITED   5u
/*error on block 1 */
#define UPDATE_BLK0_BLK1INHIBITED       6u
#define UPDATE_BLK0FULL_BLK1INHIBITED   7u
/*error on block 0 & 1 */
#define EEPROM_INHIBITED                8u

/*defines for recovery strategy*/
#define EE_NO_RECOVERY    (0u)
#define EE_RECOVERY_BLK0  (1u) 
#define EE_RECOVERY_BLK1  (2u) 
#define EE_ERASE_ALL      (3u) 

#define EE_ERASE_RETRY    (3u)


/*!\egroup*/

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
static int16_T  EE_CheckBlockDescriptor(t_EEBlockDescriptionVar *pBlockDesc);
static int16_T  EE_CheckBlockData(t_EEBlockDescriptionVar *pBlockDesc);
static int16_T  EE_CalculateChecksum(uint32_T* pData, uint32_T size, EE_checksum_t *checksum);
static int16_T  EE_GetNextBlock (t_EEBlockDescriptionVar ** pBlockDesc);
static int16_T  EE_DumpLastBlocks(uint8_T **destAddr, uint32_T IDtable_dst[], uint32_T IDtable_src[], uint8_T blockingStatus);
static int16_T  EE_CheckVersion (t_EEBlockDescriptionVar *pBlockDesc,uint16_T checkVersion, const char_T* ID_p1_vers);
static int16_T  EE_BlkSeeking(uint8_T** pData, uint8_T* pEndEEBlock, uint32_T *EE_IDtable);
static int16_T  EE_BlkSeeking_ID(uint8_T** pData, uint8_T* pEndEEBlock, uint8_T ID, uint16_T ID_inv_nmb, uint32_T  *pId_inv);
static int16_T  EE_FillBlockWithRamData(uint8_T PageValid, const char_T* ID_vers);
static int16_T  EE_loadID(uint32_T *dataRecord, uint16_T dataRecordSize, uint16_T checkVersion, t_EEBlockDescriptionVar *pBlockDesc, const char_T* ID_vers);
#if (FLASH_TYPE != C55)
static int16_T  EE_BlkErase(uint32_T _address, uint32_T _size, void(*_callback)(void));
#else
static int16_T  EE_BlkErase(uint32_T _address, uint32_T _size, uint8_T _callback);
#endif
static int16_T  EE_BlkProgramAndVerify(uint32_T _dest, uint32_T _size, uint32_T _source);
static int16_T  EE_BlkLoadData(uint16_T ID_p4, uint32_T  * dataRecord, uint32_T dataRecordSize, t_EEBlockDescriptionVar *pBlockDesc, uint32_T* padding_p4 , const char_T* ID_vers);
static int16_T  EE_BlkInvalidateData(t_EEBlockDescriptionVar * pBlockDesc);
static uint8_T  EE_checkBlk0(uint16_T ID_p, uint32_T dataRecordSize);
static uint8_T  EE_checkBlk1(uint16_T ID_p, uint32_T dataRecordSize);
static void     EE_Watchdog_Set_Serve(void);
static void     EE_Watchdog_Cfg_Serve(void);
static void     EE_Watchdog_Flash_Mode(void);
static void     EE_Watchdog_ResetReq(void);
static int16_T  EE_Clean(void);
static int16_T  EE_Flush(uint8_T EEPROM_Page,uint8_T blockingStatus, const char_T* ID_version);



#endif // EEPROM_H

/****************************************************************************
 ****************************************************************************/


