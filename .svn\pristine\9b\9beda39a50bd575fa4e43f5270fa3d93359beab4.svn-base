/*****************************************************************************************************************/
/* $HeadURL::                                                                                              $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Port
**  Filename        :  Port_out.h
**  Created on      :  07-mag-2020 15:05:00
**  Original author :  CarboniM
******************************************************************************/
/*****************************************************************************
**
**                        Port Description
**
**  Port module configures the pins functionalities and characteristics.
**  
******************************************************************************/

#ifndef PORT_OUT_H
#define PORT_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"
#include "sys.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/

#define PORT_PinConfigReset_ext(pinNumber, SSS, OUT, OUTSAFE,  INPUT, WP, initLevel)   \
		{\
			SIUL2.MSCR_IO[(pinNumber)].R = ((SSS) | (OUT) | (OUTSAFE) | (WP) | (INPUT));   \
			SIUL2.GPDO[(pinNumber)].R = (initLevel); \
		}


#define PORT_MuxConfig_CmdIn_ext(pinNumber, mscr_mux, sss)   \
		{\
			SIUL2.MSCR_MUX[(mscr_mux)].R = (sss);	\
			SIUL2.MSCR_IO[(pinNumber)].B.IBE = 1u;	\
			SIUL2.MSCR_IO[(pinNumber)].B.WPUE = 0u; \
		}

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : PORT_Init
**
**   Description:
**    Configures the pins defined in the pinout document.
**    Called at startup.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void PORT_Init(void);

/******************************************************************************
**   Function    : PORT_Pre_Init
**
**   Description:
**    Configures the pins defined in the pinout document.
**    Called at startup.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void PORT_Pre_Init(void);

/******************************************************************************
**   Function    : PORT_DisableDSPIPort
**
**   Description:
**    Sets in GPIO mode the pins used by DSPI_5 CS 0.
**    Called before SPI config.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void PORT_DisableDSPIPort(void);

extern void PORT_StopIGNOut(uint8_T cylinder);
extern void PORT_DisableBuckEn(uint8_T bank);
extern void PORT_EnableBuckEn(uint8_T bank);
extern void PORT_DisableBuckRef(uint8_T bank);
extern void PORT_ConfigAfterBuckDiag(void);
extern void PORT_ConfigBuckRefTom(void);
extern void PORT_ConfigBuckDiag(void);
extern void PORT_BuckDiagRec(uint8_T buckIdx);
extern void PORT_IgnOffRec(uint8_T cyl);
extern void PORT_EnableBuckRef(uint8_T bank);
extern void PORT_SetMaxBuckRef(uint8_T bank);
inline void Port_IvorSafetyDisable(void);


inline void Port_IvorSafetyDisable(void)
{
	// Disable CmdOut generation
	PORT_PinConfigReset_ext(OP_IgnDrv_1, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_PinConfigReset_ext(OP_IgnDrv_2, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_PinConfigReset_ext(OP_IgnDrv_3, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_PinConfigReset_ext(OP_IgnDrv_4, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_PinConfigReset_ext(OP_IgnDrv_5, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_PinConfigReset_ext(OP_IgnDrv_6, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_PinConfigReset_ext(OP_IgnDrv_7, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_PinConfigReset_ext(OP_IgnDrv_8, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	// Disable CmdIn detection
	PORT_PinConfigReset_ext(IP_IgnCmd_1, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_MuxConfig_CmdIn_ext(IP_IgnCmd_1, MSCR_MUX_INPUT1_PIN_TIM1_0, SSS_0)
	PORT_PinConfigReset_ext(IP_IgnCmd_2, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_MuxConfig_CmdIn_ext(IP_IgnCmd_2, MSCR_MUX_INPUT2_PIN_TIM1_1, SSS_0)
	PORT_PinConfigReset_ext(IP_IgnCmd_3, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_MuxConfig_CmdIn_ext(IP_IgnCmd_3, MSCR_MUX_INPUT3_PIN_TIM1_2, SSS_0)
	PORT_PinConfigReset_ext(IP_IgnCmd_4, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_MuxConfig_CmdIn_ext(IP_IgnCmd_4, MSCR_MUX_INPUT4_PIN_TIM1_3, SSS_0)
	PORT_PinConfigReset_ext(IP_IgnCmd_5, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_MuxConfig_CmdIn_ext(IP_IgnCmd_5, MSCR_MUX_INPUT5_PIN_TIM1_4, SSS_0)
	PORT_PinConfigReset_ext(IP_IgnCmd_6, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_MuxConfig_CmdIn_ext(IP_IgnCmd_6, MSCR_MUX_INPUT6_PIN_TIM1_5, SSS_0)
	PORT_PinConfigReset_ext(IP_IgnCmd_7, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_MuxConfig_CmdIn_ext(IP_IgnCmd_7, MSCR_MUX_Input7_PIN_TIM1_6, SSS_0)
	PORT_PinConfigReset_ext(IP_IgnCmd_8, SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_DISABLE, OUT_HIGH)
	PORT_MuxConfig_CmdIn_ext(IP_IgnCmd_8, MSCR_MUX_Input8_PIN_TIM1_7, SSS_0)
	// Set Liveness high
	PORT_PinConfigReset_ext(OP_Liveness, SSS_0, OUT_PP_WEAK, MCSR_SMC_DONTDISABLEPIN,  INPUT_DISABLE, MCSR_WPUE_ENABLE, OUT_HIGH)
}


#endif // PORT_OUT_H

/****************************************************************************
 ****************************************************************************/


