/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgm
**  Filename        :  CanMgm.h
**  Created on      :  01-dec-2020 09:37:00
**  Original author :  SantoroR
******************************************************************************/

#ifndef CANMGM_H
#define CANMGM_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Canmgm_out.h"
#include "Mcan_out.h"
#include "DigIn_out.h"
#include "Diagmgm_out.h"
#include "RecMgm_out.h"
#include "pwrmgm_out.h"
#include "Mathlib.h"
#include "Utils_out.h"
#include "Canmgmin_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST uint16_T CANSENDWAITSTART;
extern CALQUAL CALQUAL_POST uint16_T CANSENDWAITSTOP;
extern CALQUAL CALQUAL_POST uint16_T TNOCANDIAGAFTKEYON;
extern CALQUAL CALQUAL_POST uint8_T THDEBIGNITIONCUTOFF;
extern CALQUAL CALQUAL_POST uint8_T THDEBACTIVEPHASESEARCH;

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/

#endif  // CANMGM_H

/****************************************************************************
 ****************************************************************************/


