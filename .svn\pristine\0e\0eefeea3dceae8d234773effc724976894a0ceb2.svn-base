/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DiagCanMgm
**  Filename        :  DiagCanMgm_calib.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  MocciA
******************************************************************************/
#ifdef _BUILD_DIAGCANMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "../include/diagcanmgm_Ferrari.h"
#include "rtwtypes.h"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
#pragma ghs section rodata=".calib"

CALQUAL CALQUAL_POST uint8_T CALDRAWNUMB[ECU_DRAW_NUMBER_LEN]      = "50543254   ";                      // calib. draw number
CALQUAL CALQUAL_POST uint8_T CALHOMOLNUMB[HOMOLOGATION_NUMBER_LEN] = {"      "};                         // calib. homolagatin number
CALQUAL CALQUAL_POST uint8_T CALISOCODE[ISO_CODE_LEN]              = {0x00u,0x58u,0x50u,0x28u,0x16u};    // calib. iso code

/// New Eldor ID "ECU Calibration Version" 16 Byte ASCII
CALQUAL CALQUAL_POST uint8_T ELDORCALVER[ELDOR_ECU_CAL_VER_LEN]    = {"HELLO WORLD     "};              // UDS 0xF1F1


#endif // _BUILD_DIAGCANMGM_

// Force EOL (=1) or leave unchanged (=0)
CALQUAL CALQUAL_POST uint8_T    FORCEEOL = 0u;
/****************************************************************************
 ****************************************************************************/
