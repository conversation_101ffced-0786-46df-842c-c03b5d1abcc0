/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tom.h
 * @brief   SPC5xx GTM TOM header file.
 *
 * @addtogroup TOM
 * @{
 */

#ifndef _GTM_TOM_H_
#define _GTM_TOM_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"

/*lint -e621*/
/**
 * @name    TOM definitions
 * @{
 */

/** Number of channels */
#define SPC5_GTM_TOM_CHANNELS                                16U

/** TOM channel 0 identifier */
#define TOM_CHANNEL0                                         0U
/** TOM channel 1 identifier */
#define TOM_CHANNEL1                                         1U
/** TOM channel 2 identifier */
#define TOM_CHANNEL2                                         2U
/** TOM channel 3 identifier */
#define TOM_CHANNEL3                                         3U
/** TOM channel 4 identifier */
#define TOM_CHANNEL4                                         4U
/** TOM channel 5 identifier */
#define TOM_CHANNEL5                                         5U
/** TOM channel 6 identifier */
#define TOM_CHANNEL6                                         6U
/** TOM channel 7 identifier */
#define TOM_CHANNEL7                                         7U
/** TOM channel 8 identifier */
#define TOM_CHANNEL8                                         8U
/** TOM channel 9 identifier */
#define TOM_CHANNEL9                                         9U
/** TOM channel 10 identifier */
#define TOM_CHANNEL10                                        10U
/** TOM channel 11 identifier */
#define TOM_CHANNEL11                                        11U
/** TOM channel 12 identifier */
#define TOM_CHANNEL12                                        12U
/** TOM channel 13 identifier */
#define TOM_CHANNEL13                                        13U
/** TOM channel 14 identifier */
#define TOM_CHANNEL14                                        14U
/** TOM channel 15 identifier */
#define TOM_CHANNEL15                                        15U


/** TOM Host Trigger mode */
#define SPC5_GTM_TOM_TRIGGER_MODE_HOST                       1U

/** TOM Clock Source Fixed Clock 0 */
#define SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK0                 0U
/** TOM Clock Source Fixed Clock 1 */
#define SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK1                 1U
/** TOM Clock Source Fixed Clock 2 */
#define SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK2                 2U
/** TOM Clock Source Fixed Clock 3 */
#define SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK3                 3U
/** TOM Clock Source Fixed Clock 4 */
#define SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK4                 4U

/** TOM Signal Level Low */
#define SPC5_GTM_TOM_SIGNAL_LEVEL_LOW                        0U
/** TOM Signal Level High */
#define SPC5_GTM_TOM_SIGNAL_LEVEL_HIGH                       1U

/** TOM IRQ CCU0 Interrupt Notified */
#define SPC5_GTM_TOM_IRQ_STATUS_CCU0                         1UL
/** TOM IRQ CCU1 Interrupt Notified */
#define SPC5_GTM_TOM_IRQ_STATUS_CCU1                         2UL

/** TOM IRQ Enable CCU0 interrupt */
#define SPC5_GTM_TOM_IRQ_ENABLE_CCU0                         1UL
/** TOM IRQ Enable CCU1 interrupt */
#define SPC5_GTM_TOM_IRQ_ENABLE_CCU1                         2UL

/** TOM IRQ Force CCU0 interrupt */
#define SPC5_GTM_TOM_IRQ_FORCE_INT_CCU0                      1UL
/** TOM IRQ Force CCU1 interrupt */
#define SPC5_GTM_TOM_IRQ_FORCE_INT_CCU1                      2UL

/** TOM IRQ Mode Level */
#define SPC5_GTM_TOM_IRQ_MODE_LEVEL                          0U
/** TOM IRQ Mode Pulse */
#define SPC5_GTM_TOM_IRQ_MODE_PULSE                          1U
/** TOM IRQ Mode Pulse-Notify */
#define SPC5_GTM_TOM_IRQ_MODE_PULSE_NOTIFY                   2U
/** TOM IRQ Mode Single-Pulse Mode */
#define SPC5_GTM_TOM_IRQ_MODE_SINGLE_PULSE                   3U

/** TOM IRQ notify interrupt to ICM as Normal */
#define SPC5_GTM_TOM_INT_MODE_NORMAL                         1U
/** TOM IRQ notify interrupt to ICM as Error */
#define SPC5_GTM_TOM_INT_MODE_ERROR                          2U
/** TOM IRQ notify interrupt to ICM as Normal and Error */
#define SPC5_GTM_TOM_INT_MODE_NORMAL_AND_ERROR               3U

/** @} */

/**
 * @brief Type of a structure representing a (GTM-IP) TOM driver.
 */
typedef struct GTM_TOMDriver GTM_TOMDriver;

/**
 * @brief   (GTM-IP) TOM notification callback type.
 *
 * @param[in] tomd      pointer to the @p TOMDriver object triggering the callback
 * @param[in] channel   channel triggering the callback
 */
typedef void (*gtm_tom_callback_t)(GTM_TOMDriver *tomd, uint8_t channel);

/**
 * @brief Type of a structure representing a (GTM-IP) TOM channel callbacks.
 */
typedef struct GTM_TOM_Channel_Callbacks GTM_TOM_Channel_Callbacks;

/**
 * @brief   Structure representing an TOM Channel callbacks
 */
struct GTM_TOM_Channel_Callbacks {
  /**
   * @brief CCU0 channel callback function.
   */
	gtm_tom_callback_t ccu0;

  /**
   * @brief CCU1 channel callback function.
   */
	gtm_tom_callback_t ccu1;
};

/**
 * @brief   Structure representing a (GTM) TOM driver.
 */
struct GTM_TOMDriver {

  /**
   * @brief Pointer to the (GTM) TOM registers block.
   */
	volatile GTM_TOM_TAG *tom;

  /**
   * @brief Pointer to the (GTM) ICM driver.
   */
	struct GTM_ICMDriver *icmd;

  /**
   * @brief Interrupts callbacks.
   */
	GTM_TOM_Channel_Callbacks **callbacks;

  /**
   * @brief Pointer for application private data.
   */
	void *priv;
};


/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_TOM0 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TOMDriver TOMD1;
#endif

#if (SPC5_GTM_USE_TOM1 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TOMDriver TOMD2;
#endif

#if (SPC5_GTM_USE_TOM2 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TOMDriver TOMD3;
#endif

#if (SPC5_GTM_USE_TOM3 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TOMDriver TOMD4;
#endif


#ifdef __cplusplus
extern "C" {
#endif
extern void gtm_tomInit(void);
extern void gtm_tomStart(void);
extern void gtm_tomStop(void);

extern void gtm_tomSet(GTM_TOMDriver *tomd, uint8_t channel);
extern void gtm_tomUnset(GTM_TOMDriver *tomd, uint8_t channel);

extern void gtm_tomEnable(GTM_TOMDriver *tomd, uint8_t channel);
extern void gtm_tomDisable(GTM_TOMDriver *tomd, uint8_t channel);

extern void gtm_tomOutputEnable(GTM_TOMDriver *tomd, uint8_t channel);
extern void gtm_tomOutputDisable(GTM_TOMDriver *tomd, uint8_t channel);

extern void gtm_tomSetSourceClock(GTM_TOMDriver *tomd, uint8_t channel, uint8_t clock);
extern void gtm_tomSetSignalLevel(GTM_TOMDriver *tomd, uint8_t channel, uint8_t level);

extern void gtm_tomTrigger(GTM_TOMDriver *tomd, uint8_t channel, uint8_t mode);

extern void gtm_tomSetCompareReg1(GTM_TOMDriver *tomd, uint8_t channel, uint32_t value);
extern void gtm_tomSetCompareReg2(GTM_TOMDriver *tomd, uint8_t channel, uint32_t value);

extern void gtm_tomSetIRQMode(GTM_TOMDriver *tomd, uint8_t channel, uint8_t mode);

extern void gtm_tomEnableInt(GTM_TOMDriver *tomd, uint8_t channel, uint32_t int_num);

extern void gtm_tomNotifyInt(GTM_TOMDriver *tomd, uint8_t channel, uint32_t int_num);
extern void gtm_tomAckInt(GTM_TOMDriver *tomd, uint8_t channel, uint32_t int_num);
extern void gtm_tomDisableInt(GTM_TOMDriver *tomd, uint8_t channel, uint32_t int_num);

extern uint32_t gtm_tomGetIntStatus(GTM_TOMDriver *tomd, uint8_t channel);
extern uint32_t gtm_tomGetIntEnabled(GTM_TOMDriver *tomd, uint8_t channel);

extern void gtm_tomUpdateAll(GTM_TOMDriver *tomd, uint8_t channel, uint8_t clock, uint32_t period, uint32_t duty);
extern void gtm_tomUpdate_Duty_async(GTM_TOMDriver *tomd, uint8_t channel, uint32_t duty);
extern void gtm_tomUpdate_Duty_sync(GTM_TOMDriver *tomd, uint8_t channel, uint32_t duty);
#ifdef __cplusplus
}
#endif

/*lint +e621*/
#endif /* _GTM_TOM_H_ */
/** @} */
