/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  IgnInCmd
**  Filename        :  IgnInCmd.h
**  Created on      :  31-mar-2021 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifndef IGNINCMD_H_
#define IGNINCMD_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "ignincmd_out.h"
#include "MathLib.h"
#include "gtm_eisb_out.h"
#include "SyncMgm_out.h"
#include "SyncMgmCfg_out.h"
#include  "../../bios/isb/include/Crank_event.h"
#include "msparkcmd_out.h"
#include "Buckdiagmgm_out.h"
#include "DigIn_out.h"
#include "ETPU_VrsDefs.h"
#include "Canmgmin_out.h"
#include "crank_isb_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define MIN_NEG_DELTA 100u
#define BKRPMDSA_dim 7u

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */


/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL uint16_T BKRPMDSA[BKRPMDSA_dim];
extern CALQUAL int8_T VTDSAOUTMAX[BKRPMDSA_dim];
extern CALQUAL uint8_T VTNCYCLEDIS[BKRPMDSA_dim];

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : IgnInCmd_PhaseCheck
**
**   Description:
**    Test Phase of TDN signal
**
**   Parameters :
**    cyl: Input cylinder index (0-7)
**    currentAngle: angle read @end of charge trig in input signal
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void IgnInCmd_PhaseCheck(uint8_T cyl, uint32_T currentAngle);

#endif
