/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Get_private.h
 **  Date:          13-Jul-2022
 **
 **  Model Version: 1.469
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Get_private_h_
#define RTW_HEADER_TLE9278BQX_Get_private_h_
#include "rtwtypes.h"
#include "TLE9278BQX_Get_out.h"

/* Includes for objects with custom storage classes. */
#include "TLE9278BQX_Mgm_out.h"
/* Includes for extra files. */
#include "TLE9278BQX_Cfg_out.h"
#include "TLE9278BQX_Com_out.h"
#include "TLE9278BQX_Prs_out.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/
extern void TLE9278BQX_Get_M_S_CTRL(void);
extern void TLE9278BQX_Get_HW_CTRL_0(void);
extern void TLE9278BQX_Get_WD_CTRL(void);
extern void TLE9278BQX_Get_BUS_CTRL_0(void);
extern void TLE9278BQX_Get_WK_CTRL_0(void);
extern void TLE9278BQX_Get_WK_CTRL_1(void);
extern void TLE9278BQX_Get_WK_PUPD_CTRL(void);
extern void TLE9278BQX_Get_BUS_CTRL_2(void);
extern void TLE9278BQX_Get_BUS_CTRL_3(void);
extern void TLE9278BQX_Get_TIMER_CTRL_0(void);
extern void TLE9278BQX_Get_HW_CTRL_1(void);
extern void TLE9278BQX_Get_SYS_STAT_CTRL(void);
extern void TLE9278BQX_Get_SUP_STAT_1(void);
extern void TLE9278BQX_Get_SUP_STAT_0(void);
extern void TLE9278BQX_Get_THERM_STAT(void);
extern void TLE9278BQX_Get_DEV_STAT(void);
extern void TLE9278BQX_Get_BUS_STAT_0(void);
extern void TLE9278BQX_Get_WK_STAT_0(void);
extern void TLE9278BQX_Get_WK_LVL_STAT(void);
extern void TLE9278BQX_Get_WK_STAT_2(void);
extern void TLE9278BQX_Get_BUS_STAT_2(void);
extern void TLE9278BQX_Get_BUS_STAT_3(void);
extern void TLE9278BQX_Get_SMPS_STAT(void);
extern void TLE9278BQX_Get_ADC_STAT(void);
extern void TLE9278BQX_Get_FAM_PROD_STAT(void);
extern void TLE9278BQX_Get_default(void);
extern void TLE9278BQX_Get_fc_Bkg(void);
extern void TLE9278BQX_Get_fc_Init_Start(void);
extern void TLE9278BQX_Get_fc_Init(void);

#endif                                /* RTW_HEADER_TLE9278BQX_Get_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/