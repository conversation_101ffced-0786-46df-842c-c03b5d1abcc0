/************************************************************************************/
/* $HeadURL$   */
/* $ Description:                                                                   */
/* $Revision::        $                                                             */
/* $Date::                                                $                         */
/* $Author::                             $                                          */
/************************************************************************************/

#ifndef _RECMGM_H_
#define _RECMGM_H_
/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/

#include "string.h"
#include "rtwtypes.h"
#include "diagmgm_out.h"
#include "recmgm_out.h"
#include "SparkPlugTest_out.h"
#include "KnockCorrNom_out.h"

/*!
\defgroup PrivateDefines Private Defines
\brief Defines with module scope

\sgroup
*/
/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/// Number of rows of TBPRIOREC (equal to ceil(N_REC/8))
#define ROW_P_REC (((N_REC) >> 3) + 1u)


/*!\egroup*/

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
static void FillActiveRec(uint8_T id_rec);

/*******************************************************************
* Function Name : UpdateVtExtDiag
* Description: It fills VtExtDiag array 
* Parameters: none
* Return(s): void
* Funtion(s) called: none
* Funtion caller(s): RecMgm_T10ms
* NOTES: none
*******************************************************************/
static void UpdateVtExtDiag(void);

static void RecMgm_T10ms_body(void);

/*-----------------------------------*
 * IMPORTED CALIBRATION
 *-----------------------------------*/
extern CALQUAL CALQUAL_POST uint8_T  TBACTREC[ROW_REC][N_REC];
extern CALQUAL CALQUAL_POST uint8_T  TBFLGRECETRIP[ROW_REC][N_REC];
extern CALQUAL CALQUAL_POST uint8_T  TBPRIOREC[ROW_P_REC][N_REC];
extern CALQUAL CALQUAL_POST uint8_T  EXTDIAGEN;
extern CALQUAL CALQUAL_POST uint8_T  VTFORCEEXTDIAG[EXT_DIAG_NUMB];

#endif

