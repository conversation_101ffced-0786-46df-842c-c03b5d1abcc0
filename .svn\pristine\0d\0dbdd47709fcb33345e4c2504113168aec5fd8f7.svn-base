/****************************************************************************
*
* Copyright © 2018-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_psm_cfg.h
 * @brief   GTM PSM (FIFO, AFD, A2F) Driver configuration macros and structures.
 *
 * @addtogroup PSM
 * @{
 */

#ifndef _GTM_PSM_CFG_H_
#define _GTM_PSM_CFG_H_

#include "gtm_psm.h"

/*lint -e621*/

#define SPC5_GTM_PSM0_DIRECT_MEMORY_ACCESS_BY_CPU       TRUE

/* ---- PSM Channel 0 Settings ---- */
#define SPC5_GTM_PSM0_USE_CHANNEL0                      TRUE
#define SPC5_GTM_PSM0_CHANNEL0_OPERATION_MODE           SPC5_GTM_PSM_OPERATION_MODE_NORMAL_FIFO
#define SPC5_GTM_PSM0_CHANNEL0_RAM_ACCESS_PRIORITY      SPC5_GTM_PSM_RAM_ACCESS_PRIORITY_FIFO
#define SPC5_GTM_PSM0_CHANNEL0_IRQ_MODE                 SPC5_GTM_PSM_IRQ_MODE_LEVEL
#define SPC5_GTM_PSM0_CHANNEL0_INT_EMPTY_ENABLED        FALSE
#define SPC5_GTM_PSM0_CHANNEL0_INT_EMPTY_MODE           SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL0_INT_FULL_ENABLED         FALSE
#define SPC5_GTM_PSM0_CHANNEL0_INT_FULL_MODE            SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL0_INT_LOWER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL0_INT_LOWER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL0_INT_UPPER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL0_INT_UPPER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL0_DMA_HYSTERESIS_ENABLED   FALSE
#define SPC5_GTM_PSM0_CHANNEL0_DMA_HYSTERESIS_DIRECTION SPC5_GTM_PSM_DMA_HYSTERESIS_DIRECTION_READ
#define SPC5_GTM_PSM0_CHANNEL0_MEMORY_SIZE              0
#define SPC5_GTM_PSM0_CHANNEL0_MEMORY_UPPER_WM          0
#define SPC5_GTM_PSM0_CHANNEL0_MEMORY_LOWER_WM          0
#define SPC5_GTM_PSM0_CHANNEL0_STREAM_MODE              SPC5_GTM_PSM_STREAM_MODE_BOTH_WORDS
#define SPC5_GTM_PSM0_CHANNEL0_STREAM_DIRECTION         SPC5_GTM_PSM_STREAM_DIRECTION_FIFO_TO_ARU
#define SPC5_GTM_PSM0_CHANNEL0_STREAM_ROUTING           SPC5_GTM_WRITE_ADDRESS_ARU_ARU_ACCESS
/* ---- ---------------------- ---- */

/* ---- PSM Channel 1 Settings ---- */
#define SPC5_GTM_PSM0_USE_CHANNEL1                      TRUE
#define SPC5_GTM_PSM0_CHANNEL1_OPERATION_MODE           SPC5_GTM_PSM_OPERATION_MODE_NORMAL_FIFO
#define SPC5_GTM_PSM0_CHANNEL1_RAM_ACCESS_PRIORITY      SPC5_GTM_PSM_RAM_ACCESS_PRIORITY_FIFO
#define SPC5_GTM_PSM0_CHANNEL1_IRQ_MODE                 SPC5_GTM_PSM_IRQ_MODE_LEVEL
#define SPC5_GTM_PSM0_CHANNEL1_INT_EMPTY_ENABLED        FALSE
#define SPC5_GTM_PSM0_CHANNEL1_INT_EMPTY_MODE           SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL1_INT_FULL_ENABLED         FALSE
#define SPC5_GTM_PSM0_CHANNEL1_INT_FULL_MODE            SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL1_INT_LOWER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL1_INT_LOWER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL1_INT_UPPER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL1_INT_UPPER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL1_DMA_HYSTERESIS_ENABLED   FALSE
#define SPC5_GTM_PSM0_CHANNEL1_DMA_HYSTERESIS_DIRECTION SPC5_GTM_PSM_DMA_HYSTERESIS_DIRECTION_READ
#define SPC5_GTM_PSM0_CHANNEL1_MEMORY_SIZE              0
#define SPC5_GTM_PSM0_CHANNEL1_MEMORY_UPPER_WM          0
#define SPC5_GTM_PSM0_CHANNEL1_MEMORY_LOWER_WM          0
#define SPC5_GTM_PSM0_CHANNEL1_STREAM_MODE              SPC5_GTM_PSM_STREAM_MODE_BOTH_WORDS
#define SPC5_GTM_PSM0_CHANNEL1_STREAM_DIRECTION         SPC5_GTM_PSM_STREAM_DIRECTION_FIFO_TO_ARU
#define SPC5_GTM_PSM0_CHANNEL1_STREAM_ROUTING           SPC5_GTM_WRITE_ADDRESS_ARU_ARU_ACCESS
/* ---- ---------------------- ---- */

/* ---- PSM Channel 2 Settings ---- */
#define SPC5_GTM_PSM0_USE_CHANNEL2                      TRUE
#define SPC5_GTM_PSM0_CHANNEL2_OPERATION_MODE           SPC5_GTM_PSM_OPERATION_MODE_NORMAL_FIFO
#define SPC5_GTM_PSM0_CHANNEL2_RAM_ACCESS_PRIORITY      SPC5_GTM_PSM_RAM_ACCESS_PRIORITY_FIFO
#define SPC5_GTM_PSM0_CHANNEL2_IRQ_MODE                 SPC5_GTM_PSM_IRQ_MODE_LEVEL
#define SPC5_GTM_PSM0_CHANNEL2_INT_EMPTY_ENABLED        FALSE
#define SPC5_GTM_PSM0_CHANNEL2_INT_EMPTY_MODE           SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL2_INT_FULL_ENABLED         FALSE
#define SPC5_GTM_PSM0_CHANNEL2_INT_FULL_MODE            SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL2_INT_LOWER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL2_INT_LOWER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL2_INT_UPPER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL2_INT_UPPER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL2_DMA_HYSTERESIS_ENABLED   FALSE
#define SPC5_GTM_PSM0_CHANNEL2_DMA_HYSTERESIS_DIRECTION SPC5_GTM_PSM_DMA_HYSTERESIS_DIRECTION_READ
#define SPC5_GTM_PSM0_CHANNEL2_MEMORY_SIZE              0
#define SPC5_GTM_PSM0_CHANNEL2_MEMORY_UPPER_WM          0
#define SPC5_GTM_PSM0_CHANNEL2_MEMORY_LOWER_WM          0
#define SPC5_GTM_PSM0_CHANNEL2_STREAM_MODE              SPC5_GTM_PSM_STREAM_MODE_BOTH_WORDS
#define SPC5_GTM_PSM0_CHANNEL2_STREAM_DIRECTION         SPC5_GTM_PSM_STREAM_DIRECTION_FIFO_TO_ARU
#define SPC5_GTM_PSM0_CHANNEL2_STREAM_ROUTING           SPC5_GTM_WRITE_ADDRESS_ARU_ARU_ACCESS
/* ---- ---------------------- ---- */

/* ---- PSM Channel 3 Settings ---- */
#define SPC5_GTM_PSM0_USE_CHANNEL3                      TRUE
#define SPC5_GTM_PSM0_CHANNEL3_OPERATION_MODE           SPC5_GTM_PSM_OPERATION_MODE_NORMAL_FIFO
#define SPC5_GTM_PSM0_CHANNEL3_RAM_ACCESS_PRIORITY      SPC5_GTM_PSM_RAM_ACCESS_PRIORITY_FIFO
#define SPC5_GTM_PSM0_CHANNEL3_IRQ_MODE                 SPC5_GTM_PSM_IRQ_MODE_LEVEL
#define SPC5_GTM_PSM0_CHANNEL3_INT_EMPTY_ENABLED        FALSE
#define SPC5_GTM_PSM0_CHANNEL3_INT_EMPTY_MODE           SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL3_INT_FULL_ENABLED         FALSE
#define SPC5_GTM_PSM0_CHANNEL3_INT_FULL_MODE            SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL3_INT_LOWER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL3_INT_LOWER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL3_INT_UPPER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL3_INT_UPPER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL3_DMA_HYSTERESIS_ENABLED   FALSE
#define SPC5_GTM_PSM0_CHANNEL3_DMA_HYSTERESIS_DIRECTION SPC5_GTM_PSM_DMA_HYSTERESIS_DIRECTION_READ
#define SPC5_GTM_PSM0_CHANNEL3_MEMORY_SIZE              0
#define SPC5_GTM_PSM0_CHANNEL3_MEMORY_UPPER_WM          0
#define SPC5_GTM_PSM0_CHANNEL3_MEMORY_LOWER_WM          0
#define SPC5_GTM_PSM0_CHANNEL3_STREAM_MODE              SPC5_GTM_PSM_STREAM_MODE_BOTH_WORDS
#define SPC5_GTM_PSM0_CHANNEL3_STREAM_DIRECTION         SPC5_GTM_PSM_STREAM_DIRECTION_FIFO_TO_ARU
#define SPC5_GTM_PSM0_CHANNEL3_STREAM_ROUTING           SPC5_GTM_WRITE_ADDRESS_ARU_ARU_ACCESS
/* ---- ---------------------- ---- */

/* ---- PSM Channel 4 Settings ---- */
#define SPC5_GTM_PSM0_USE_CHANNEL4                      TRUE
#define SPC5_GTM_PSM0_CHANNEL4_OPERATION_MODE           SPC5_GTM_PSM_OPERATION_MODE_NORMAL_FIFO
#define SPC5_GTM_PSM0_CHANNEL4_RAM_ACCESS_PRIORITY      SPC5_GTM_PSM_RAM_ACCESS_PRIORITY_FIFO
#define SPC5_GTM_PSM0_CHANNEL4_IRQ_MODE                 SPC5_GTM_PSM_IRQ_MODE_LEVEL
#define SPC5_GTM_PSM0_CHANNEL4_INT_EMPTY_ENABLED        FALSE
#define SPC5_GTM_PSM0_CHANNEL4_INT_EMPTY_MODE           SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL4_INT_FULL_ENABLED         FALSE
#define SPC5_GTM_PSM0_CHANNEL4_INT_FULL_MODE            SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL4_INT_LOWER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL4_INT_LOWER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL4_INT_UPPER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL4_INT_UPPER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL4_DMA_HYSTERESIS_ENABLED   FALSE
#define SPC5_GTM_PSM0_CHANNEL4_DMA_HYSTERESIS_DIRECTION SPC5_GTM_PSM_DMA_HYSTERESIS_DIRECTION_READ
#define SPC5_GTM_PSM0_CHANNEL4_MEMORY_SIZE              0
#define SPC5_GTM_PSM0_CHANNEL4_MEMORY_UPPER_WM          0
#define SPC5_GTM_PSM0_CHANNEL4_MEMORY_LOWER_WM          0
#define SPC5_GTM_PSM0_CHANNEL4_STREAM_MODE              SPC5_GTM_PSM_STREAM_MODE_BOTH_WORDS
#define SPC5_GTM_PSM0_CHANNEL4_STREAM_DIRECTION         SPC5_GTM_PSM_STREAM_DIRECTION_FIFO_TO_ARU
#define SPC5_GTM_PSM0_CHANNEL4_STREAM_ROUTING           SPC5_GTM_WRITE_ADDRESS_ARU_ARU_ACCESS
/* ---- ---------------------- ---- */

/* ---- PSM Channel 5 Settings ---- */
#define SPC5_GTM_PSM0_USE_CHANNEL5                      TRUE
#define SPC5_GTM_PSM0_CHANNEL5_OPERATION_MODE           SPC5_GTM_PSM_OPERATION_MODE_NORMAL_FIFO
#define SPC5_GTM_PSM0_CHANNEL5_RAM_ACCESS_PRIORITY      SPC5_GTM_PSM_RAM_ACCESS_PRIORITY_FIFO
#define SPC5_GTM_PSM0_CHANNEL5_IRQ_MODE                 SPC5_GTM_PSM_IRQ_MODE_LEVEL
#define SPC5_GTM_PSM0_CHANNEL5_INT_EMPTY_ENABLED        FALSE
#define SPC5_GTM_PSM0_CHANNEL5_INT_EMPTY_MODE           SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL5_INT_FULL_ENABLED         FALSE
#define SPC5_GTM_PSM0_CHANNEL5_INT_FULL_MODE            SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL5_INT_LOWER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL5_INT_LOWER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL5_INT_UPPER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL5_INT_UPPER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL5_DMA_HYSTERESIS_ENABLED   FALSE
#define SPC5_GTM_PSM0_CHANNEL5_DMA_HYSTERESIS_DIRECTION SPC5_GTM_PSM_DMA_HYSTERESIS_DIRECTION_READ
#define SPC5_GTM_PSM0_CHANNEL5_MEMORY_SIZE              0
#define SPC5_GTM_PSM0_CHANNEL5_MEMORY_UPPER_WM          0
#define SPC5_GTM_PSM0_CHANNEL5_MEMORY_LOWER_WM          0
#define SPC5_GTM_PSM0_CHANNEL5_STREAM_MODE              SPC5_GTM_PSM_STREAM_MODE_BOTH_WORDS
#define SPC5_GTM_PSM0_CHANNEL5_STREAM_DIRECTION         SPC5_GTM_PSM_STREAM_DIRECTION_FIFO_TO_ARU
#define SPC5_GTM_PSM0_CHANNEL5_STREAM_ROUTING           SPC5_GTM_WRITE_ADDRESS_ARU_ARU_ACCESS
/* ---- ---------------------- ---- */

/* ---- PSM Channel 6 Settings ---- */
#define SPC5_GTM_PSM0_USE_CHANNEL6                      TRUE
#define SPC5_GTM_PSM0_CHANNEL6_OPERATION_MODE           SPC5_GTM_PSM_OPERATION_MODE_NORMAL_FIFO
#define SPC5_GTM_PSM0_CHANNEL6_RAM_ACCESS_PRIORITY      SPC5_GTM_PSM_RAM_ACCESS_PRIORITY_FIFO
#define SPC5_GTM_PSM0_CHANNEL6_IRQ_MODE                 SPC5_GTM_PSM_IRQ_MODE_LEVEL
#define SPC5_GTM_PSM0_CHANNEL6_INT_EMPTY_ENABLED        FALSE
#define SPC5_GTM_PSM0_CHANNEL6_INT_EMPTY_MODE           SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL6_INT_FULL_ENABLED         FALSE
#define SPC5_GTM_PSM0_CHANNEL6_INT_FULL_MODE            SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL6_INT_LOWER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL6_INT_LOWER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL6_INT_UPPER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL6_INT_UPPER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL6_DMA_HYSTERESIS_ENABLED   FALSE
#define SPC5_GTM_PSM0_CHANNEL6_DMA_HYSTERESIS_DIRECTION SPC5_GTM_PSM_DMA_HYSTERESIS_DIRECTION_READ
#define SPC5_GTM_PSM0_CHANNEL6_MEMORY_SIZE              0
#define SPC5_GTM_PSM0_CHANNEL6_MEMORY_UPPER_WM          0
#define SPC5_GTM_PSM0_CHANNEL6_MEMORY_LOWER_WM          0
#define SPC5_GTM_PSM0_CHANNEL6_STREAM_MODE              SPC5_GTM_PSM_STREAM_MODE_BOTH_WORDS
#define SPC5_GTM_PSM0_CHANNEL6_STREAM_DIRECTION         SPC5_GTM_PSM_STREAM_DIRECTION_FIFO_TO_ARU
#define SPC5_GTM_PSM0_CHANNEL6_STREAM_ROUTING           SPC5_GTM_WRITE_ADDRESS_ARU_ARU_ACCESS
/* ---- ---------------------- ---- */

/* ---- PSM Channel 7 Settings ---- */
#define SPC5_GTM_PSM0_USE_CHANNEL7                      TRUE
#define SPC5_GTM_PSM0_CHANNEL7_OPERATION_MODE           SPC5_GTM_PSM_OPERATION_MODE_NORMAL_FIFO
#define SPC5_GTM_PSM0_CHANNEL7_RAM_ACCESS_PRIORITY      SPC5_GTM_PSM_RAM_ACCESS_PRIORITY_FIFO
#define SPC5_GTM_PSM0_CHANNEL7_IRQ_MODE                 SPC5_GTM_PSM_IRQ_MODE_LEVEL
#define SPC5_GTM_PSM0_CHANNEL7_INT_EMPTY_ENABLED        FALSE
#define SPC5_GTM_PSM0_CHANNEL7_INT_EMPTY_MODE           SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL7_INT_FULL_ENABLED         FALSE
#define SPC5_GTM_PSM0_CHANNEL7_INT_FULL_MODE            SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL7_INT_LOWER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL7_INT_LOWER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL7_INT_UPPER_WM_ENABLED     FALSE
#define SPC5_GTM_PSM0_CHANNEL7_INT_UPPER_WM_MODE        SPC5_GTM_PSM_INT_MODE_NORMAL
#define SPC5_GTM_PSM0_CHANNEL7_DMA_HYSTERESIS_ENABLED   FALSE
#define SPC5_GTM_PSM0_CHANNEL7_DMA_HYSTERESIS_DIRECTION SPC5_GTM_PSM_DMA_HYSTERESIS_DIRECTION_READ
#define SPC5_GTM_PSM0_CHANNEL7_MEMORY_SIZE              0
#define SPC5_GTM_PSM0_CHANNEL7_MEMORY_UPPER_WM          0
#define SPC5_GTM_PSM0_CHANNEL7_MEMORY_LOWER_WM          0
#define SPC5_GTM_PSM0_CHANNEL7_STREAM_MODE              SPC5_GTM_PSM_STREAM_MODE_BOTH_WORDS
#define SPC5_GTM_PSM0_CHANNEL7_STREAM_DIRECTION         SPC5_GTM_PSM_STREAM_DIRECTION_FIFO_TO_ARU
#define SPC5_GTM_PSM0_CHANNEL7_STREAM_ROUTING           SPC5_GTM_WRITE_ADDRESS_ARU_ARU_ACCESS
/* ---- ---------------------- ---- */

/* Interrupts callbacks */
extern GTM_PSM_Channel_Callbacks *gtm_psm0_callbacks[SPC5_GTM_PSM_CHANNELS];
extern GTM_PSM_Channel_Callbacks gtm_psm0_channel0_callbacks;
extern GTM_PSM_Channel_Callbacks gtm_psm0_channel1_callbacks;
extern GTM_PSM_Channel_Callbacks gtm_psm0_channel2_callbacks;
extern GTM_PSM_Channel_Callbacks gtm_psm0_channel3_callbacks;
extern GTM_PSM_Channel_Callbacks gtm_psm0_channel4_callbacks;
extern GTM_PSM_Channel_Callbacks gtm_psm0_channel5_callbacks;
extern GTM_PSM_Channel_Callbacks gtm_psm0_channel6_callbacks;
extern GTM_PSM_Channel_Callbacks gtm_psm0_channel7_callbacks;
/* ---- ---------------------- ---- */

/*lint +e621*/
#endif /* _GTM_PSM_CFG_H_ */

/** @} */
