/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_cfg.h
 * @brief   GTM Driver configuration macros and structures.
 *
 * @addtogroup GTM
 * @{
 */

#ifndef _GTM_CFG_H_
#define _GTM_CFG_H_

#if 0 //MC
#if !defined(FALSE) || defined(__DOXYGEN__)
#define FALSE                               0U
#endif

#if !defined(TRUE) || defined(__DOXYGEN__)
#define TRUE                                1U
#endif
#endif
#define FALSE                               0U
#define TRUE                                1U

#define SPC5_HAS_GTM_IP_101                 FALSE
#define SPC5_HAS_GTM_IP_122                 TRUE
#define SPC5_GTM_REV                        0x12215000UL

#define SPC5_HAS_GTM_IP_343                 FALSE
#define SPC5_HAS_GTM_IP_344                 FALSE


#define SPC5_GTM_CMU_SYSTEM_CLOCK           80000000UL

#define SPC5_GTM_CMU_GCLK_NUM               1UL
#define SPC5_GTM_CMU_GCLK_DEN               1UL

/*
 * EGU subunit
*/ 
#define SPC5_GTM_CMU_EGU_EN_ECLK0           FALSE
#define SPC5_GTM_CMU_EGU_EN_ECLK1           FALSE
#define SPC5_GTM_CMU_EGU_EN_ECLK2           FALSE

#define SPC5_GTM_CMU_EGU_ECLK0_NUM          1UL
#define SPC5_GTM_CMU_EGU_ECLK0_DEN          1UL
#define SPC5_GTM_CMU_EGU_ECLK1_NUM          1UL
#define SPC5_GTM_CMU_EGU_ECLK1_DEN          1UL
#define SPC5_GTM_CMU_EGU_ECLK2_NUM          1UL
#define SPC5_GTM_CMU_EGU_ECLK2_DEN          1UL
/*
 * CFGU subunit
 */
#define SPC5_GTM_CMU_CFGU_EN_CLK0           TRUE
#define SPC5_GTM_CMU_CFGU_CLK_DIV0          1UL

#define SPC5_GTM_CMU_CFGU_EN_CLK1           TRUE
#define SPC5_GTM_CMU_CFGU_CLK_DIV1          2UL

#define SPC5_GTM_CMU_CFGU_EN_CLK2           TRUE
#define SPC5_GTM_CMU_CFGU_CLK_DIV2          4UL

#define SPC5_GTM_CMU_CFGU_EN_CLK3           TRUE
#define SPC5_GTM_CMU_CFGU_CLK_DIV3          8UL

#define SPC5_GTM_CMU_CFGU_EN_CLK4           TRUE
#define SPC5_GTM_CMU_CFGU_CLK_DIV4          10UL

#define SPC5_GTM_CMU_CFGU_EN_CLK5           TRUE
#define SPC5_GTM_CMU_CFGU_CLK_DIV5          80UL

#define SPC5_GTM_CMU_CFGU_EN_CLK6           TRUE
#define SPC5_GTM_CMU_CFGU_CLK_DIV6          80UL

#define SPC5_GTM_CMU_CFGU_EN_CLK7           TRUE
#define SPC5_GTM_CMU_CFGU_CLK_DIV7          1600UL

/*
 * CMU_CLK_6_CTRL register
 * if clkX_sel is selected the
 * CLK_CNT (SPC5_GTM_CMU_CFGU_CLK_DIV6) bit field is ignored
 * and only the CLKX_SEL (CMU_CLK_6_CTRL[7]) bit is set
 * else the CLK_CNT will be set with SPC5_GTM_CMU_CFGU_CLK_DIV6 
 */
#define SPC5_GTM_CMU_CFGU_CLK6_SEL          FALSE

/*
 * CMU_CLK_7_CTRL register
 * if clkX_sel is selected the
 * CLK_CNT (SPC5_GTM_CMU_CFGU_CLK_DIV7) bit field is ignored
 * and only the CLKX_SEL (CMU_CLK_7_CTRL[7]) bit is set
 * else the CLK_CNT will be set with SPC5_GTM_CMU_CFGU_CLK_DIV7
 */
#define SPC5_GTM_CMU_CFGU_CLK7_SEL          FALSE

/*
 * FXU subunit
*/ 
#define SPC5_GTM_CMU_FXCLK_ENABLE           TRUE
//#define SPC5_GTM_CMU_FXCLK_SEL              SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK5
#define SPC5_GTM_CMU_FXCLK_SEL              SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK3


#define SPC5_GTM_CMU_CLK_EN                 (0UL | SPC5_GTM_CMU_CLK0_ENABLED | SPC5_GTM_CMU_CLK1_ENABLED | SPC5_GTM_CMU_CLK2_ENABLED | SPC5_GTM_CMU_CLK3_ENABLED | SPC5_GTM_CMU_CLK4_ENABLED | SPC5_GTM_CMU_CLK5_ENABLED | SPC5_GTM_CMU_CLK6_ENABLED | SPC5_GTM_CMU_CLK7_ENABLED | SPC5_GTM_CMU_FXCLK_ENABLED)


/* GTM INTERRUPT PRIORITY */
#define SPC5_GTM_TOM_INT_PRIORITY           15U
#define SPC5_GTM_TIM_INT_PRIORITY           15U
#define SPC5_GTM_MCS_INT_PRIORITY           15U
#define SPC5_GTM_ARU_INT_PRIORITY           15U
#define SPC5_GTM_PSM_INT_PRIORITY           15U
#define SPC5_GTM_BRC_INT_PRIORITY           15U
#define SPC5_GTM_DPLL_INT_PRIORITY          15U
#define SPC5_GTM_ATOM_INT_PRIORITY          15U


/* Auxiliary Source signal for TIM module */
#define SPC5_GTM_TIM0_AUXIN_CH0             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM0_AUXIN_CH1             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM0_AUXIN_CH2             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM0_AUXIN_CH3             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM0_AUXIN_CH4             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM0_AUXIN_CH5             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM0_AUXIN_CH6             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM0_AUXIN_CH7             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM0_AUXIN_SRC             (0UL | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL0) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL1) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL2) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL3) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL4) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL5) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL6) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL7))

#define spc5_gtm_AUXIN_SRC_TIM0_init(){ \
   GTM_SET_TIM_AUX_IN_SRC(0, SPC5_GTM_TIM0_AUXIN_SRC); \
}
 
#define SPC5_GTM_TIM1_AUXIN_CH0             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM1_AUXIN_CH1             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM1_AUXIN_CH2             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM1_AUXIN_CH3             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM1_AUXIN_CH4             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM1_AUXIN_CH5             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM1_AUXIN_CH6             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM1_AUXIN_CH7             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM1_AUXIN_SRC             (0UL | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL0) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL1) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL2) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL3) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL4) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL5) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL6) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL7))

#define spc5_gtm_AUXIN_SRC_TIM1_init(){ \
   GTM_SET_TIM_AUX_IN_SRC(1, SPC5_GTM_TIM1_AUXIN_SRC); \
}
 
#define SPC5_GTM_TIM2_AUXIN_CH0             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM2_AUXIN_CH1             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM2_AUXIN_CH2             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM2_AUXIN_CH3             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM2_AUXIN_CH4             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM2_AUXIN_CH5             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM2_AUXIN_CH6             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM2_AUXIN_CH7             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM2_AUXIN_SRC             (0UL | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL0) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL1) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL2) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL3) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL4) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL5) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL6) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL7))

#define spc5_gtm_AUXIN_SRC_TIM2_init(){ \
   GTM_SET_TIM_AUX_IN_SRC(2, SPC5_GTM_TIM2_AUXIN_SRC); \
}
 
#define SPC5_GTM_TIM3_AUXIN_CH0             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM3_AUXIN_CH1             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM3_AUXIN_CH2             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM3_AUXIN_CH3             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM3_AUXIN_CH4             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM3_AUXIN_CH5             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM3_AUXIN_CH6             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM3_AUXIN_CH7             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM3_AUXIN_SRC             (0UL | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL0) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL1) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL2) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL3) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL4) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL5) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL6) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL7))

#define spc5_gtm_AUXIN_SRC_TIM3_init(){ \
   GTM_SET_TIM_AUX_IN_SRC(3, SPC5_GTM_TIM3_AUXIN_SRC); \
}
 
#define SPC5_GTM_TIM4_AUXIN_CH0             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM4_AUXIN_CH1             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM4_AUXIN_CH2             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM4_AUXIN_CH3             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM4_AUXIN_CH4             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM4_AUXIN_CH5             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM4_AUXIN_CH6             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM4_AUXIN_CH7             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM4_AUXIN_SRC             (0UL | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL0) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL1) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL2) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL3) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL4) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL5) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL6) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL7))

#define spc5_gtm_AUXIN_SRC_TIM4_init(){ \
   GTM_SET_TIM_AUX_IN_SRC(4, SPC5_GTM_TIM4_AUXIN_SRC); \
}
 
#define SPC5_GTM_TIM5_AUXIN_CH0             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM5_AUXIN_CH1             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM5_AUXIN_CH2             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM5_AUXIN_CH3             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM5_AUXIN_CH4             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM5_AUXIN_CH5             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM5_AUXIN_CH6             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM5_AUXIN_CH7             SPC5_GTM_IP_TIM_AUXIN_TOM
#define SPC5_GTM_TIM5_AUXIN_SRC             (0UL | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL0) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL1) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL2) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL3) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL4) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL5) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL6) | (SPC5_GTM_IP_TIM_AUXIN_TOM << TIM_CHANNEL7))

#define spc5_gtm_AUXIN_SRC_TIM5_init(){ \
   GTM_SET_TIM_AUX_IN_SRC(5, SPC5_GTM_TIM5_AUXIN_SRC); \
}
 
#define gtm_aux_in_src(){ \
  spc5_gtm_AUXIN_SRC_TIM0_init(); \
  spc5_gtm_AUXIN_SRC_TIM1_init(); \
  spc5_gtm_AUXIN_SRC_TIM2_init(); \
}

/* GTM ICM Configuration Settings */
#define SPC5_GTM_USE_ICM                    TRUE
#define SPC5_GTM_USE_ICM0                   TRUE

/* GTM TOM Configuration Settings */
#define SPC5_GTM_USE_TOM                    TRUE
#define SPC5_GTM_USE_TOM0                   TRUE
#define SPC5_GTM_USE_TOM1                   FALSE
#define SPC5_GTM_USE_TOM2                   FALSE
#define SPC5_GTM_USE_TOM3                   FALSE

/* GTM TIM Configuration Settings */
#define SPC5_GTM_USE_TIM                    TRUE
#define SPC5_GTM_USE_TIM0                   TRUE
#define SPC5_GTM_USE_TIM1                   TRUE
#define SPC5_GTM_USE_TIM2                   TRUE
#define SPC5_GTM_USE_TIM3                   FALSE
#define SPC5_GTM_USE_TIM4                   FALSE
#define SPC5_GTM_USE_TIM5                   FALSE

/* GTM PSM Configuration Settings */
#define SPC5_GTM_USE_PSM                    TRUE
#define SPC5_GTM_USE_PSM0                   TRUE

/* GTM ARU Configuration Settings */
#define SPC5_GTM_USE_ARU                    TRUE

/* GTM MCS Configuration Settings */
#define SPC5_GTM_USE_MCS                    TRUE
#define SPC5_GTM_USE_MCS0                   TRUE
#define SPC5_GTM_USE_MCS1                   TRUE
#define SPC5_GTM_USE_MCS2                   TRUE
#define SPC5_GTM_USE_MCS3                   FALSE
#define SPC5_GTM_USE_MCS4                   FALSE

/* GTM MAP Configuration Settings */
#define SPC5_GTM_USE_MAP                    FALSE

/* GTM ATOM Configuration Settings */
#define SPC5_GTM_USE_ATOM                   TRUE
#define SPC5_GTM_USE_ATOM0                  TRUE
#define SPC5_GTM_USE_ATOM1                  TRUE
#define SPC5_GTM_USE_ATOM2                  TRUE
#define SPC5_GTM_USE_ATOM3                  TRUE
#define SPC5_GTM_USE_ATOM4                  FALSE
#define SPC5_GTM_USE_ATOM5                  FALSE

/* GTM DPLL Configuration Settings */
#define SPC5_GTM_USE_DPLL                   TRUE
#define SPC5_GTM_USE_DPLL0                  TRUE

/* GTM TBU Configuration Settings */
#define SPC5_GTM_USE_TBU                    TRUE
#define SPC5_GTM_USE_TBU0                   TRUE

/* GTM DTM Configuration Settings */
#define SPC5_GTM_USE_DTM                    FALSE
#define SPC5_GTM_USE_CDTM0                  FALSE
#define SPC5_GTM_USE_CDTM1                  FALSE
#define SPC5_GTM_USE_CDTM2                  FALSE
#define SPC5_GTM_USE_CDTM3                  FALSE
#define SPC5_GTM_USE_CDTM4                  FALSE
#define SPC5_GTM_USE_CDTM5                  FALSE

/* GTM BRC Configuration Settings */
#define SPC5_GTM_USE_BRC                    TRUE

#endif /* _GTM_CFG_H_ */

/** @} */
