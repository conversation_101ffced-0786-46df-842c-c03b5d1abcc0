/****************************************************************************
*
* Copyright © 2018-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_mcs_cfg.c
 * @brief   (GTM-IP) MCS Driver configuration code.
 *
 * @addtogroup MCS
 * @{
 */
#include "gtm_cfg.h"

#if (SPC5_GTM_USE_MCS == TRUE) || defined(__DOXYGEN__)

#include "gtm_mcs_cfg.h"

/*===========================================================================*/
/* Driver local definitions.                                                 */
/*===========================================================================*/

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/* ---- MCS 0 Callbacks       ---- */
GTM_MCS_Channel_Callbacks gtm_mcs0_channel0_callbacks = {
	cyl0_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs0_channel1_callbacks = {
	cyl2_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs0_channel2_callbacks = {
	cyl4_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs0_channel3_callbacks = {
	cyl6_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs0_channel5_callbacks = {
	buck0_prim_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs0_channel6_callbacks = {
	buck0_en_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs0_channel7_callbacks = {
	buck0_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks *gtm_mcs0_callbacks[SPC5_GTM_MCS_CHANNELS] = {
	&gtm_mcs0_channel0_callbacks,
	&gtm_mcs0_channel1_callbacks,
	&gtm_mcs0_channel2_callbacks,
	&gtm_mcs0_channel3_callbacks,
	NULL,
	&gtm_mcs0_channel5_callbacks,
	&gtm_mcs0_channel6_callbacks,
	&gtm_mcs0_channel7_callbacks
};
/* ---- ---------------------- ---- */

/* ---- MCS 1 Callbacks       ---- */
GTM_MCS_Channel_Callbacks gtm_mcs1_channel0_callbacks = {
	cyl1_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs1_channel1_callbacks = {
	cyl3_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs1_channel2_callbacks = {
	cyl5_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs1_channel3_callbacks = {
	cyl7_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs1_channel5_callbacks = {
	buck1_prim_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs1_channel6_callbacks = {
	buck1_en_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs1_channel7_callbacks = {
	buck1_mcs_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks *gtm_mcs1_callbacks[SPC5_GTM_MCS_CHANNELS] = {
	&gtm_mcs1_channel0_callbacks,
	&gtm_mcs1_channel1_callbacks,
	&gtm_mcs1_channel2_callbacks,
	&gtm_mcs1_channel3_callbacks,
	NULL,
	&gtm_mcs1_channel5_callbacks,
	&gtm_mcs1_channel6_callbacks,
	&gtm_mcs1_channel7_callbacks
};
/* ---- ---------------------- ---- */

/* ---- MCS 2 Callbacks       ---- */
GTM_MCS_Channel_Callbacks gtm_mcs2_channel0_callbacks = {
	mcs2_ch0_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs2_channel1_callbacks = {
	mcs2_ch1_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs2_channel2_callbacks = {
	mcs2_ch2_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs2_channel3_callbacks = {
	mcs2_ch3_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs2_channel4_callbacks = {
	mcs2_4_ipri0_isec_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks gtm_mcs2_channel5_callbacks = {
	mcs2_5_ipri1_isec_cb,
	NULL,
	NULL
};

GTM_MCS_Channel_Callbacks *gtm_mcs2_callbacks[SPC5_GTM_MCS_CHANNELS] = {
	&gtm_mcs2_channel0_callbacks,
	&gtm_mcs2_channel1_callbacks,
	&gtm_mcs2_channel2_callbacks,
	&gtm_mcs2_channel3_callbacks,
	&gtm_mcs2_channel4_callbacks,
	&gtm_mcs2_channel5_callbacks,
	NULL,
	NULL
};
/* ---- ---------------------- ---- */

/* ---- MCS 3 Callbacks       ---- */
/* ---- MCS 4 Callbacks       ---- */

/*===========================================================================*/
/* Driver local types.                                                       */
/*===========================================================================*/

/*===========================================================================*/
/* Driver local variables.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Driver local functions.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/

#endif /* SPC5_GTM_USE_MCS */

/** @} */
