/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           MisfThrMgm.c
 **  File Creation Date: 20-Jul-2023
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         MisfThrMgm
 **  Model Description:  This module calculates the misfire thresholds to detect cases for no combustions, bad and partial combustions.
 **  Model Version:      1.597
 **  Model Author:       Raffaele Marotta - Mon Feb 04 13:16:50 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: MarottaR - Thu Jul 20 16:50:17 2023
 **
 **  Last Saved Modification:  MarottaR - Thu Jul 20 16:49:08 2023
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "MisfThrMgm_out.h"
#include "MisfThrMgm_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BIT0                           1U                        /* Referenced by: '<S7>/Constant4' */

/* BIT0 value */
#define BIT1                           2U                        /* Referenced by: '<S7>/Constant5' */

/* BIT1 value */
#define BIT2                           4U                        /* Referenced by: '<S7>/Constant17' */

/* BIT2 value */
#define BKMISFLOAD_dim                 8U                        /* Referenced by: '<S5>/BKMISFLOAD_dim' */

/* Number of elements of BKMISFLOAD */
#define BKMISFPLALOAD_dim              8U                        /* Referenced by: '<S5>/BKMISFPLALOAD_dim' */

/* Length of BKMISFPLALOAD */
#define BKMISFPLARPM_dim               11U                       /* Referenced by: '<S5>/BKMISFPLARPM_dim' */

/* Length of BKMISFPLARPM */
#define BKMISFRPM_dim                  11U                       /* Referenced by: '<S5>/BKMISFRPM_dim ' */

/* Length of BKMISFRPM */
#define BKSAGAINMISF_dim               7U                        /* Referenced by: '<S7>/Constant21' */

/* Last index of VTSAGAINMISF array */
#define BKTAIRIONMISF_dim              3U                        /* Referenced by: '<S5>/BKTAIRIONMISF_dim ' */

/* Length of BKTAIRIONMISF */
#define BKTWATIONMISF_dim              5U                        /* Referenced by: '<S5>/BKTWATIONMISF_dim' */

/* Length of BKTWATIONMISF */
#define ID_VER_MISFTHRMGM_DEF          1597U                     /* Referenced by: '<S1>/ID_VER_MISFTHRMGM_DEF' */

/* ID model version define */
#define PARAM_2                        2U                        /* Referenced by: '<S7>/Constant12' */

/* Parameter 2 */
#define PARAM_3                        3U                        /* Referenced by: '<S7>/Constant14' */

/* Parameter 3 */
#define PARAM_4                        4U                        /* Referenced by: '<S7>/Constant15' */

/* Parameter 4 */
#define VTGAINRONMISF_dim              4U                        /* Referenced by: '<S7>/Constant3' */

/* Last index of VTGAINRONMISF array */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_MISFTHRMGM_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint16_T misfthrmgm_counter;    /* '<S10>/Memory' */
static uint8_T ronlevsim_tmp;          /* '<S10>/Memory1' */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BADCOMBPER = 1024U;/* Referenced by: '<S6>/BADCOMBPER' */

/* Bad Combustion Threshold Gain */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKMISFLOAD[9] = { 2560U, 3840U, 5120U,
  6400U, 7680U, 8960U, 10240U, 11520U, 12800U } ;/* Referenced by: '<S5>/BKMISFLOAD' */

/* LoadMisf Breakpoint vector for TBMISFTHR */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKMISFPLALOAD[9] = { 2560U, 3840U,
  5120U, 6400U, 7680U, 8960U, 10240U, 11520U, 12800U } ;/* Referenced by: '<S5>/BKMISFPLALOAD' */

/* LoadMisf Breakpoint vector for TBMISFTHRPLA */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKMISFPLARPM[12] = { 600U, 800U, 1000U,
  1500U, 2000U, 2500U, 3000U, 3500U, 4000U, 4500U, 5000U, 5500U } ;/* Referenced by: '<S5>/BKMISFPLARPM' */

/* Rpm Breakpoint vector for TBMISFTHRPLA */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKMISFRPM[12] = { 600U, 800U, 1000U,
  1500U, 2000U, 2500U, 3000U, 3500U, 4000U, 4500U, 5000U, 5500U } ;/* Referenced by: '<S5>/BKMISFRPM' */

/* Rpm Breakpoint vector for TBMISFTHR */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKSAGAINMISF[8] = { -480, -400, -320,
  -160, -80, 0, 32, 80 } ;             /* Referenced by: '<S7>/Constant22' */

/* Spark advance breakpoints vector for VTSAGAINMISF */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKTAIRIONMISF[4] = { 0, 320, 640, 960 }
;                                      /* Referenced by: '<S5>/BKTAIRIONMISF' */

/* Inlet air temperature breakpoint vector for TBCORRTHRMISF */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKTWATIONMISF[6] = { -480, 0, 640, 960,
  1280, 1600 } ;                       /* Referenced by: '<S5>/BKTWATIONMISF' */

/* Coolant temperature breakpoints vector for TBCORRTHRMISF */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MISFTHRCOMPDELAY = 10U;/* Referenced by: '<S7>/Constant13' */

/* Compensation level confirmation delay */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MISFTHRCOMPLEV0 = 0;/* Referenced by: '<S7>/Constant16' */

/* SA threshod for misfthr compensation level 1 */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MISFTHRCOMPLEV1 = -80;/* Referenced by: '<S7>/Constant7' */

/* SA threshod for misfthr compensation level 2 */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MISFTHRCOMPLEV2 = -160;/* Referenced by: '<S7>/Constant10' */

/* SA threshod for misfthr compensation level 3 */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MISFTHRCOMPLEV3 = -240;/* Referenced by: '<S7>/Constant11' */

/* SA threshod for misfthr compensation level 4 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T MISFTHRNEWCOMPEN = 0U;/* Referenced by: '<S7>/Constant1' */

/* Use new misf thr compensation (bit 0 - enable startegy, bit 1 add DSAEcu, bit 2 - add SAknockCyl)  */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T PARCOMBPER = 1024U;/* Referenced by: '<S6>/PARCOMBPER' */

/* Partial Misfire Threshold Gain */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TBCORRTHRMISF[24] = { 128U, 128U, 128U,
  128U, 128U, 128U, 128U, 128U, 128U, 128U, 128U, 128U, 128U, 128U, 128U, 128U,
  128U, 128U, 128U, 128U, 128U, 128U, 128U, 128U } ;/* Referenced by: '<S5>/TBCORRTHRMISF' */

/* Table of gain to calculate the misfire threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBMISFTHR[108] = { 25U, 25U, 25U, 30U,
  30U, 30U, 35U, 40U, 40U, 30U, 30U, 35U, 35U, 35U, 40U, 40U, 60U, 80U, 38U, 60U,
  80U, 95U, 100U, 105U, 110U, 130U, 150U, 55U, 100U, 132U, 135U, 138U, 145U,
  150U, 170U, 220U, 86U, 135U, 141U, 188U, 199U, 210U, 213U, 245U, 265U, 203U,
  221U, 223U, 235U, 271U, 367U, 380U, 382U, 390U, 230U, 231U, 250U, 252U, 360U,
  383U, 385U, 391U, 398U, 239U, 285U, 287U, 293U, 381U, 383U, 385U, 395U, 400U,
  289U, 290U, 293U, 304U, 382U, 383U, 385U, 395U, 401U, 304U, 305U, 307U, 309U,
  385U, 385U, 385U, 396U, 402U, 304U, 305U, 310U, 310U, 385U, 385U, 395U, 400U,
  410U, 305U, 315U, 330U, 360U, 390U, 390U, 390U, 405U, 415U } ;/* Referenced by: '<S5>/TBMISFTHR' */

/* Misfire Threshold Table */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBMISFTHRPLA[108] = { 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U } ;                   /* Referenced by: '<S5>/TBMISFTHRPLA' */

/* Misfire Threshold Table in Plasma */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T USEDSAION = 0U;/* Referenced by: '<S7>/Constant18' */

/* If USEDSAION is equal to 1 RonLevSimSA = DSAIon.
   If USEDSAION is equal to 0 RonLevSimSA depends on the algorithm. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T USEGAINMISF = 0U;/* Referenced by: '<S7>/Constant19' */

/* If USEGAINMISF is equal to 1 MisfThrComp depends on VTSAGAINMISF.
   If USEGAINMISF is equal to 0 MisfThrComp depends on VTGAINRONMISF. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTGAINRONMISF[5] = { 128U, 154U, 179U,
  205U, 218U } ;                       /* Referenced by: '<S7>/Constant' */

/* MisfThr gain for ron level */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTSAGAINMISF[8] = { 128U, 128U, 128U,
  128U, 128U, 128U, 128U, 128U } ;     /* Referenced by: '<S7>/Constant20' */

/* MisfThr gain for spark advance */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T BadCombThr;                   /* '<S2>/Merge2' */

/* Bad Combustion threshold */
uint16_T NoCombThr;                    /* '<S2>/Merge1' */

/* No combustion threshold */
uint16_T ParCombThr;                   /* '<S2>/Merge3' */

/* Partial combustion threshold */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_MisfThrMgm;/* '<S1>/ID_VER_MISFTHRMGM_DEF' */

/* ID model version */
STATIC_TEST_POINT uint16_T MisfThr;    /* '<S2>/Merge4' */

/* Actual misfuel threshold */
STATIC_TEST_POINT uint16_T MisfThrComp;/* '<S7>/Product' */

/* Misfire threshold with compensation applied */
STATIC_TEST_POINT uint16_T MisfThrPla; /* '<S17>/Conversion' */

/* Misfire threshold in Plasma */
STATIC_TEST_POINT int16_T RonLevSimSA; /* '<S7>/Switch3' */

/* Spark advance correction used to estimate ron level. */
STATIC_TEST_POINT uint8_T RonLevelSimulated;/* '<S2>/Merge6' */

/* Simulated Ron Level */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<Root>/Init'
 * Block description for: '<Root>/Init'
 *   Initialization of the variables at the PowerOn and in case of loss of
 *   synchronization event.
 */
void MisfThrMgm_Init(void)
{
  /* SignalConversion generated from: '<S1>/BadCombThr_Init' incorporates:
   *  Constant: '<S1>/zero'
   */
  BadCombThr = 0U;

  /* SignalConversion generated from: '<S1>/MisfThr_Init' incorporates:
   *  Constant: '<S1>/zero'
   */
  MisfThr = 0U;

  /* SignalConversion generated from: '<S1>/NoCombThr_Init' incorporates:
   *  Constant: '<S1>/zero'
   */
  NoCombThr = 0U;

  /* SignalConversion generated from: '<S1>/ParCombThr_Init' incorporates:
   *  Constant: '<S1>/zero'
   */
  ParCombThr = 0U;

  /* SignalConversion generated from: '<S1>/RonLevelSimulated_Init' incorporates:
   *  Constant: '<S1>/zero1'
   */
  RonLevelSimulated = 0U;

  /* Constant: '<S1>/ID_VER_MISFTHRMGM_DEF' */
  IdVer_MisfThrMgm = ID_VER_MISFTHRMGM_DEF;
}

/* Model step function */
void MisfThrMgm_EOA(void)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_U16_U16_U16;
  uint16_T rtb_Look2D_U16_U16_U16_k;
  uint16_T rtb_Look2D_U8_S16_S16;
  uint16_T rtb_LookUp_U8_S16;
  uint16_T rtb_NoCombThr;
  uint8_T rtb_Switch2;
  uint8_T rtb_ronlevsim_tmp;
  uint16_T rtb_Switch8;
  uint8_T rtb_Switch;
  uint8_T rtb_Switch9;
  int16_T tmp;
  int16_T tmp_0;
  uint16_T tmp_1;

  /* RootInportFunctionCallGenerator generated from: '<Root>/MisfThrMgm_EOA' incorporates:
   *  SubSystem: '<Root>/calc_EOA'
   *
   * Block description for '<Root>/calc_EOA':
   *  Calculation of the misfire thresholds on the angular event.
   */
  /* S-Function (Look2D_U16_U16_U16): '<S13>/Look2D_U16_U16_U16' incorporates:
   *  Constant: '<S5>/BKMISFLOAD'
   *  Constant: '<S5>/BKMISFLOAD_dim'
   *  Constant: '<S5>/BKMISFRPM'
   *  Constant: '<S5>/BKMISFRPM_dim '
   *  Constant: '<S5>/TBMISFTHR'
   *
   * Block requirements for '<S5>/TBMISFTHR':
   *  1. EISB_FCA6CYL_SW_REQ_1461: The software shall estimate the misfire threshold in base of Plasm... (ECU_SW_Requirements#3221)
   */
  Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBMISFTHR[0], LoadMisf,
                     &BKMISFLOAD[0], ((uint8_T)BKMISFLOAD_dim), Rpm, &BKMISFRPM
                     [0], ((uint8_T)BKMISFRPM_dim));

  /* S-Function (Look2D_U16_U16_U16): '<S14>/Look2D_U16_U16_U16' incorporates:
   *  Constant: '<S5>/BKMISFPLALOAD'
   *  Constant: '<S5>/BKMISFPLALOAD_dim'
   *  Constant: '<S5>/BKMISFPLARPM'
   *  Constant: '<S5>/BKMISFPLARPM_dim'
   *  Constant: '<S5>/TBMISFTHRPLA'
   *
   * Block requirements for '<S5>/TBMISFTHRPLA':
   *  1. EISB_FCA6CYL_SW_REQ_1461: The software shall estimate the misfire threshold in base of Plasm... (ECU_SW_Requirements#3221)
   */
  Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16_k, &TBMISFTHRPLA[0], LoadMisf,
                     &BKMISFPLALOAD[0], ((uint8_T)BKMISFPLALOAD_dim), Rpm,
                     &BKMISFPLARPM[0], ((uint8_T)BKMISFPLARPM_dim));

  /* DataTypeConversion: '<S17>/Conversion' */
  MisfThrPla = rtb_Look2D_U16_U16_U16_k;

  /* Outputs for Atomic SubSystem: '<S3>/Calc_Comp'
   *
   * Block description for '<S3>/Calc_Comp':
   *  Calculates compensated threshold (MisfThrComp) using RonLevel or
   *  SimulatedRonLevel.
   */
  /* Switch: '<S7>/Switch3' incorporates:
   *  Constant: '<S7>/Constant1'
   *  Constant: '<S7>/Constant18'
   *  Constant: '<S7>/Constant5'
   *  Inport: '<Root>/DSAIon'
   *  RelationalOperator: '<S7>/Relational Operator5'
   *  S-Function (sfix_bitop): '<S7>/Bitwise Operator1'
   *  Sum: '<S7>/Add'
   *  Switch: '<S7>/Switch1'
   *
   * Block requirements for '<S7>/Constant1':
   *  1. EISB_FCA6CYL_SW_REQ_1770: The software shall calculate the amount of the spark advance corre... (ECU_SW_Requirements#5744)
   *  2. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
   *
   * Block requirements for '<S7>/Relational Operator5':
   *  1. EISB_FCA6CYL_SW_REQ_1770: The software shall calculate the amount of the spark advance corre... (ECU_SW_Requirements#5744)
   *
   * Block requirements for '<S7>/Add':
   *  1. EISB_FCA6CYL_SW_REQ_1770: The software shall calculate the amount of the spark advance corre... (ECU_SW_Requirements#5744)
   */
  if (((int32_T)USEDSAION) != 0) {
    RonLevSimSA = DSAIon;
  } else {
    if ((MISFTHRNEWCOMPEN & ((uint8_T)BIT1)) >= ((uint8_T)BIT1)) {
      /* Switch: '<S7>/Switch1' incorporates:
       *  Inport: '<Root>/DSAEcu'
       */
      tmp = DSAEcu;
    } else {
      /* Switch: '<S7>/Switch1' incorporates:
       *  Constant: '<S7>/Constant2'
       */
      tmp = 0;
    }

    /* Switch: '<S7>/Switch4' incorporates:
     *  Constant: '<S11>/Constant'
     *  Constant: '<S7>/Constant1'
     *  Constant: '<S7>/Constant17'
     *  Constant: '<S7>/Constant6'
     *  Constant: '<S8>/boolean11'
     *  Constant: '<S8>/boolean13'
     *  Constant: '<S8>/boolean2'
     *  Constant: '<S8>/boolean5'
     *  Constant: '<S8>/boolean6'
     *  Constant: '<S8>/boolean7'
     *  Inport: '<Root>/SAKnockCyl'
     *  Inport: '<Root>/StDiag'
     *  Inport: '<Root>/VtRec'
     *  Logic: '<S7>/Logical Operator'
     *  Logic: '<S8>/Logical Operator2'
     *  Logic: '<S8>/Logical Operator3'
     *  RelationalOperator: '<S11>/Compare'
     *  RelationalOperator: '<S7>/Relational Operator6'
     *  RelationalOperator: '<S8>/Relational Operator10'
     *  RelationalOperator: '<S8>/Relational Operator3'
     *  RelationalOperator: '<S8>/Relational Operator4'
     *  RelationalOperator: '<S8>/Relational Operator5'
     *  S-Function (sfix_bitop): '<S7>/Bitwise Operator2'
     *  Selector: '<S8>/Selector_Outold12'
     *  Selector: '<S8>/Selector_Outold4'
     *  Selector: '<S8>/Selector_Outold5'
     *  Selector: '<S8>/Selector_Outold6'
     *  Selector: '<S8>/Selector_Outold7'
     *
     * Block requirements for '<S7>/Constant1':
     *  1. EISB_FCA6CYL_SW_REQ_1770: The software shall calculate the amount of the spark advance corre... (ECU_SW_Requirements#5744)
     *  2. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
     *
     * Block requirements for '<S7>/Logical Operator':
     *  1. EISB_FCA6CYL_SW_REQ_1770: The software shall calculate the amount of the spark advance corre... (ECU_SW_Requirements#5744)
     *
     * Block requirements for '<S8>/Logical Operator2':
     *  1. EISB_FCA6CYL_SW_REQ_1770: The software shall calculate the amount of the spark advance corre... (ECU_SW_Requirements#5744)
     */
    if (((((int32_T)VtRec[(REC_KNOCKCORR_OFF_0)]) == 0) || ((((StDiag[(DIAG_RPM)]
             != FAULT) && (StDiag[(DIAG_CAMLEVEL)] != FAULT)) && (StDiag
            [(DIAG_VBATTERY)] != FAULT)) && (StDiag[(DIAG_SYNC)] != FAULT))) &&
        ((MISFTHRNEWCOMPEN & ((uint8_T)BIT2)) >= ((uint8_T)BIT2))) {
      tmp_0 = SAKnockCyl;
    } else {
      tmp_0 = 0;
    }

    /* End of Switch: '<S7>/Switch4' */
    RonLevSimSA = (int16_T)(tmp + tmp_0);
  }

  /* End of Switch: '<S7>/Switch3' */

  /* Switch: '<S7>/Switch5' incorporates:
   *  Constant: '<S7>/Constant10'
   *  Constant: '<S7>/Constant11'
   *  Constant: '<S7>/Constant15'
   *  Constant: '<S7>/Constant7'
   *  RelationalOperator: '<S7>/Relational Operator1'
   *  RelationalOperator: '<S7>/Relational Operator2'
   *  RelationalOperator: '<S7>/Relational Operator3'
   *  Switch: '<S7>/Switch7'
   *  Switch: '<S7>/Switch8'
   *
   * Block requirements for '<S7>/Constant10':
   *  1. EISB_FCA6CYL_SW_REQ_1769: The software shall estimate the ron level (RonLevelSimulated) by c... (ECU_SW_Requirements#5745)
   *
   * Block requirements for '<S7>/Constant11':
   *  1. EISB_FCA6CYL_SW_REQ_1769: The software shall estimate the ron level (RonLevelSimulated) by c... (ECU_SW_Requirements#5745)
   *
   * Block requirements for '<S7>/Constant7':
   *  1. EISB_FCA6CYL_SW_REQ_1769: The software shall estimate the ron level (RonLevelSimulated) by c... (ECU_SW_Requirements#5745)
   */
  if (RonLevSimSA <= MISFTHRCOMPLEV3) {
    rtb_ronlevsim_tmp = ((uint8_T)PARAM_4);
  } else if (RonLevSimSA <= MISFTHRCOMPLEV2) {
    /* Switch: '<S7>/Switch8' incorporates:
     *  Constant: '<S7>/Constant14'
     */
    rtb_ronlevsim_tmp = ((uint8_T)PARAM_3);
  } else if (RonLevSimSA <= MISFTHRCOMPLEV1) {
    /* Switch: '<S7>/Switch7' incorporates:
     *  Constant: '<S7>/Constant12'
     *  Switch: '<S7>/Switch8'
     */
    rtb_ronlevsim_tmp = ((uint8_T)PARAM_2);
  } else {
    /* Switch: '<S7>/Switch7' incorporates:
     *  Constant: '<S7>/Constant16'
     *  RelationalOperator: '<S7>/Relational Operator'
     *  Switch: '<S7>/Switch8'
     *
     * Block requirements for '<S7>/Constant16':
     *  1. EISB_FCA6CYL_SW_REQ_1769: The software shall estimate the ron level (RonLevelSimulated) by c... (ECU_SW_Requirements#5745)
     */
    rtb_ronlevsim_tmp = (uint8_T)((RonLevSimSA <= MISFTHRCOMPLEV0) ? 1 : 0);
  }

  /* End of Switch: '<S7>/Switch5' */

  /* Outputs for Atomic SubSystem: '<S7>/Turn_On_delay'
   *
   * Block description for '<S7>/Turn_On_delay':
   *  Turn on delay
   */
  /* Switch: '<S10>/Switch8' incorporates:
   *  Constant: '<S10>/Constant12'
   *  Memory: '<S10>/Memory1'
   *  RelationalOperator: '<S10>/Relational Operator4'
   */
  if (rtb_ronlevsim_tmp == ronlevsim_tmp) {
    /* Switch: '<S10>/Switch1' incorporates:
     *  Constant: '<S10>/Constant3'
     *  Constant: '<S7>/Constant13'
     *  Memory: '<S10>/Memory'
     *  RelationalOperator: '<S10>/Relational Operator1'
     *  Sum: '<S10>/Add'
     *
     * Block requirements for '<S7>/Constant13':
     *  1. EISB_FCA6CYL_SW_REQ_1769: The software shall estimate the ron level (RonLevelSimulated) by c... (ECU_SW_Requirements#5745)
     */
    if (misfthrmgm_counter >= MISFTHRCOMPDELAY) {
      rtb_Switch8 = misfthrmgm_counter;
    } else {
      rtb_Switch8 = (uint16_T)(((uint32_T)misfthrmgm_counter) + 1U);
    }

    /* End of Switch: '<S10>/Switch1' */
  } else {
    rtb_Switch8 = 0U;
  }

  /* End of Switch: '<S10>/Switch8' */

  /* Update for Memory: '<S10>/Memory' */
  misfthrmgm_counter = rtb_Switch8;

  /* Update for Memory: '<S10>/Memory1' */
  ronlevsim_tmp = rtb_ronlevsim_tmp;

  /* Switch: '<S7>/Switch2' incorporates:
   *  Constant: '<S7>/Constant13'
   *  RelationalOperator: '<S10>/Relational Operator3'
   *  SignalConversion generated from: '<S3>/RonLevelSimulated_old'
   *
   * Block requirements for '<S7>/Switch2':
   *  1. EISB_FCA6CYL_SW_REQ_1769: The software shall estimate the ron level (RonLevelSimulated) by c... (ECU_SW_Requirements#5745)
   *
   * Block requirements for '<S7>/Constant13':
   *  1. EISB_FCA6CYL_SW_REQ_1769: The software shall estimate the ron level (RonLevelSimulated) by c... (ECU_SW_Requirements#5745)
   */
  if (rtb_Switch8 >= MISFTHRCOMPDELAY) {
    rtb_Switch2 = rtb_ronlevsim_tmp;
  } else {
    rtb_Switch2 = RonLevelSimulated;
  }

  /* End of Switch: '<S7>/Switch2' */
  /* End of Outputs for SubSystem: '<S7>/Turn_On_delay' */

  /* Switch: '<S7>/Switch9' incorporates:
   *  Constant: '<S7>/Constant'
   *  Constant: '<S7>/Constant1'
   *  Constant: '<S7>/Constant19'
   *  Constant: '<S7>/Constant3'
   *  Constant: '<S7>/Constant4'
   *  DataTypeConversion: '<S7>/Data Type Conversion'
   *  Inport: '<Root>/RonLevelUsed'
   *  MinMax: '<S7>/MinMax'
   *  RelationalOperator: '<S7>/Relational Operator4'
   *  S-Function (sfix_bitop): '<S7>/Bitwise Operator'
   *  Selector: '<S7>/Selector'
   *  Switch: '<S7>/Switch'
   *
   * Block requirements for '<S7>/Constant':
   *  1. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
   *
   * Block requirements for '<S7>/Constant1':
   *  1. EISB_FCA6CYL_SW_REQ_1770: The software shall calculate the amount of the spark advance corre... (ECU_SW_Requirements#5744)
   *  2. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
   *
   * Block requirements for '<S7>/Relational Operator4':
   *  1. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
   *
   * Block requirements for '<S7>/Switch':
   *  1. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
   */
  if (((int32_T)USEGAINMISF) != 0) {
    /* S-Function (LookUp_U8_S16): '<S9>/LookUp_U8_S16' incorporates:
     *  Constant: '<S7>/Constant20'
     *  Constant: '<S7>/Constant21'
     *  Constant: '<S7>/Constant22'
     */
    LookUp_U8_S16( &rtb_LookUp_U8_S16, &VTSAGAINMISF[0], DSAIon, &BKSAGAINMISF[0],
                  ((uint8_T)BKSAGAINMISF_dim));
    rtb_Switch9 = (uint8_T)(((uint32_T)rtb_LookUp_U8_S16) >> ((uint32_T)8));
  } else {
    if ((MISFTHRNEWCOMPEN & ((uint8_T)BIT0)) >= ((uint8_T)BIT0)) {
      /* Switch: '<S7>/Switch'
       *
       * Block requirements for '<S7>/Switch':
       *  1. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
       */
      rtb_Switch = rtb_Switch2;
    } else if (RonLevelUsed < ((uint8_T)VTGAINRONMISF_dim)) {
      /* MinMax: '<S7>/MinMax' incorporates:
       *  Inport: '<Root>/RonLevelUsed'
       *  Switch: '<S7>/Switch'
       *
       * Block requirements for '<S7>/Switch':
       *  1. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
       */
      rtb_Switch = RonLevelUsed;
    } else {
      /* Switch: '<S7>/Switch' incorporates:
       *  Constant: '<S7>/Constant3'
       *  MinMax: '<S7>/MinMax'
       *
       * Block requirements for '<S7>/Switch':
       *  1. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
       */
      rtb_Switch = ((uint8_T)VTGAINRONMISF_dim);
    }

    rtb_Switch9 = VTGAINRONMISF[(rtb_Switch)];
  }

  /* End of Switch: '<S7>/Switch9' */
  /* End of Outputs for SubSystem: '<S3>/Calc_Comp' */

  /* Switch: '<S5>/Switch1' incorporates:
   *  Constant: '<S12>/Constant'
   *  Inport: '<Root>/MisfAbsTdc'
   *  Inport: '<Root>/VtThrMisfPlaSel'
   *  RelationalOperator: '<S12>/Compare'
   *  Selector: '<S5>/Selector_Outold4'
   *
   * Block requirements for '<S5>/Switch1':
   *  1. EISB_FCA6CYL_SW_REQ_1461: The software shall estimate the misfire threshold in base of Plasm... (ECU_SW_Requirements#3221)
   */
  if (((int32_T)VtThrMisfPlaSel[(MisfAbsTdc)]) == 0) {
    tmp_1 = rtb_Look2D_U16_U16_U16;
  } else {
    tmp_1 = MisfThrPla;
  }

  /* End of Switch: '<S5>/Switch1' */

  /* Outputs for Atomic SubSystem: '<S3>/Calc_Comp'
   *
   * Block description for '<S3>/Calc_Comp':
   *  Calculates compensated threshold (MisfThrComp) using RonLevel or
   *  SimulatedRonLevel.
   */
  /* Product: '<S7>/Product'
   *
   * Block requirements for '<S7>/Product':
   *  1. EISB_FCA6CYL_SW_REQ_1465: The software shall correct the misfire threshold multiplying it by... (ECU_SW_Requirements#3222)
   */
  MisfThrComp = (uint16_T)((((uint32_T)rtb_Switch9) * ((uint32_T)tmp_1)) >>
    ((uint32_T)7));

  /* End of Outputs for SubSystem: '<S3>/Calc_Comp' */

  /* S-Function (Look2D_U8_S16_S16): '<S15>/Look2D_U8_S16_S16' incorporates:
   *  Constant: '<S5>/BKTAIRIONMISF'
   *  Constant: '<S5>/BKTAIRIONMISF_dim '
   *  Constant: '<S5>/BKTWATIONMISF'
   *  Constant: '<S5>/BKTWATIONMISF_dim'
   *  Constant: '<S5>/TBCORRTHRMISF'
   *
   * Block requirements for '<S5>/TBCORRTHRMISF':
   *  1. EISB_FCA6CYL_SW_REQ_1462: The software shall estimate the threshold NoCombThr, multiplying t... (ECU_SW_Requirements#3223)
   */
  Look2D_U8_S16_S16( &rtb_Look2D_U8_S16_S16, &TBCORRTHRMISF[0], TWater,
                    &BKTWATIONMISF[0], ((uint8_T)BKTWATIONMISF_dim), TAir,
                    &BKTAIRIONMISF[0], ((uint8_T)BKTAIRIONMISF_dim));

  /* Product: '<S6>/Product3'
   *
   * Block requirements for '<S6>/Product3':
   *  1. EISB_FCA6CYL_SW_REQ_1462: The software shall estimate the threshold NoCombThr, multiplying t... (ECU_SW_Requirements#3223)
   */
  rtb_NoCombThr = (uint16_T)((((uint32_T)MisfThrComp) * ((uint32_T)
    rtb_Look2D_U8_S16_S16)) >> ((uint32_T)15));

  /* SignalConversion generated from: '<S3>/NoCombThr' */
  NoCombThr = rtb_NoCombThr;

  /* Product: '<S6>/Product' incorporates:
   *  Constant: '<S6>/BADCOMBPER'
   *
   * Block requirements for '<S6>/Product':
   *  1. EISB_FCA6CYL_SW_REQ_1463: The software shall estimate the threshold BadCombThr as result of ... (ECU_SW_Requirements#3224)
   *
   * Block requirements for '<S6>/BADCOMBPER':
   *  1. EISB_FCA6CYL_SW_REQ_1463: The software shall estimate the threshold BadCombThr as result of ... (ECU_SW_Requirements#3224)
   */
  BadCombThr = (uint16_T)((((uint32_T)rtb_NoCombThr) * ((uint32_T)BADCOMBPER)) >>
    ((uint32_T)10));

  /* Product: '<S6>/Product1' incorporates:
   *  Constant: '<S6>/PARCOMBPER'
   *
   * Block requirements for '<S6>/Product1':
   *  1. EISB_FCA6CYL_SW_REQ_1464: The software shall estimate the threshold ParCombThr as result of ... (ECU_SW_Requirements#3225)
   *
   * Block requirements for '<S6>/PARCOMBPER':
   *  1. EISB_FCA6CYL_SW_REQ_1464: The software shall estimate the threshold ParCombThr as result of ... (ECU_SW_Requirements#3225)
   */
  ParCombThr = (uint16_T)((((uint32_T)rtb_NoCombThr) * ((uint32_T)PARCOMBPER)) >>
    ((uint32_T)10));

  /* SignalConversion generated from: '<S3>/RonLevelSimulated_EOA' */
  RonLevelSimulated = rtb_Switch2;

  /* SignalConversion generated from: '<S3>/MisfThr' */
  MisfThr = rtb_Look2D_U16_U16_U16;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/MisfThrMgm_EOA' */
}

/* Model step function */
void MisfThrMgm_NoSync(void)
{
  /* Outputs for Function Call SubSystem: '<Root>/Init'
   *
   * Block description for '<Root>/Init':
   *  Initialization of the variables at the PowerOn and in case of loss of
   *  synchronization event.
   *
   * Block requirements for '<Root>/Init':
   *  1. EISB_FCA6CYL_SW_REQ_1773: The software shall initialize the strategy each PowerOn and loss o... (ECU_SW_Requirements#5742)
   */
  /* RootInportFunctionCallGenerator generated from: '<Root>/MisfThrMgm_NoSync' */
  MisfThrMgm_Init();

  /* End of Outputs for SubSystem: '<Root>/Init' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/MisfThrMgm_NoSync' */
}

/* Model step function */
void MisfThrMgm_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/MisfThrMgm_PowerOn' incorporates:
   *  SubSystem: '<Root>/Init'
   *
   * Block description for '<Root>/Init':
   *  Initialization of the variables at the PowerOn and in case of loss of
   *  synchronization event.
   *
   * Block requirements for '<Root>/Init':
   *  1. EISB_FCA6CYL_SW_REQ_1773: The software shall initialize the strategy each PowerOn and loss o... (ECU_SW_Requirements#5742)
   */
  MisfThrMgm_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/MisfThrMgm_PowerOn' */
}

/* Model initialize function */
void MisfThrMgm_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else                                  /*_BUILD_MisfThrMgm_*/

uint16_T BadCombThr;
uint16_T NoCombThr;
uint16_T ParCombThr;
void MisfThrMgm_PowerOn(void)
{
  BadCombThr= 0U;
  NoCombThr= 0U;
  ParCombThr= 0U;
}

void MisfThrMgm_NoSync(void)
{
}

void MisfThrMgm_EOA(void)
{
}

void MisfThrMgm_initialize(void)
{
}

#endif                                 /* _BUILD_MisfThrMgm_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * aerospace_blockset                                                         *
 * aerospace_toolbox                                                          *
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/