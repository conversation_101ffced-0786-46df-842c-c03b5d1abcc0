/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tom_cfg.h
 * @brief   GTM TOM Driver configuration macros and structures.
 *
 * @addtogroup TOM
 * @{
 */

#ifndef _GTM_TOM_CFG_H_
#define _GTM_TOM_CFG_H_

#include "gtm_tom.h"

/*lint -e621*/

/* Interrupts callbacks */
extern GTM_TOM_Channel_Callbacks *gtm_tom0_callbacks[SPC5_GTM_TOM_CHANNELS];

/*
 * ---- TOM 0 Settings ----
 */
/* ---- TOM0 Channel 0 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL0                   FALSE
#define SPC5_GTM_TOM0_CHANNEL0_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL0_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL0_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL0_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL0_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL0_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL0_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL0_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL0_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL0_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel0_callbacks;

/* ---- TOM0 Channel 1 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL1                   FALSE
#define SPC5_GTM_TOM0_CHANNEL1_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL1_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL1_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL1_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL1_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL1_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL1_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL1_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL1_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL1_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel1_callbacks;

/* ---- TOM0 Channel 2 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL2                   TRUE
#define SPC5_GTM_TOM0_CHANNEL2_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL2_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_TOM0_CHANNEL2_OUTPUT                TRUE
#define SPC5_GTM_TOM0_CHANNEL2_PERIOD                67UL
#define SPC5_GTM_TOM0_CHANNEL2_DUTY_CYCLE            40UL
#define SPC5_GTM_TOM0_CHANNEL2_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL2_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL2_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL2_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL2_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel2_callbacks;

/* ---- TOM0 Channel 3 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL3                   TRUE
#define SPC5_GTM_TOM0_CHANNEL3_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL3_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_TOM0_CHANNEL3_OUTPUT                TRUE
#define SPC5_GTM_TOM0_CHANNEL3_PERIOD                67UL
#define SPC5_GTM_TOM0_CHANNEL3_DUTY_CYCLE            40UL
#define SPC5_GTM_TOM0_CHANNEL3_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL3_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL3_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL3_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL3_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel3_callbacks;

/* ---- TOM0 Channel 4 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL4                   FALSE
#define SPC5_GTM_TOM0_CHANNEL4_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL4_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL4_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL4_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL4_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL4_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL4_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL4_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL4_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL4_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel4_callbacks;

/* ---- TOM0 Channel 5 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL5                   FALSE
#define SPC5_GTM_TOM0_CHANNEL5_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL5_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL5_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL5_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL5_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL5_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL5_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL5_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL5_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL5_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel5_callbacks;

/* ---- TOM0 Channel 6 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL6                   TRUE
#define SPC5_GTM_TOM0_CHANNEL6_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL6_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_TOM0_CHANNEL6_OUTPUT                TRUE
#define SPC5_GTM_TOM0_CHANNEL6_PERIOD                100
#define SPC5_GTM_TOM0_CHANNEL6_DUTY_CYCLE            50
#define SPC5_GTM_TOM0_CHANNEL6_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL6_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL6_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL6_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL6_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel6_callbacks;

/* ---- TOM0 Channel 7 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL7                   TRUE
#define SPC5_GTM_TOM0_CHANNEL7_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL7_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_TOM0_CHANNEL7_OUTPUT                TRUE
#define SPC5_GTM_TOM0_CHANNEL7_PERIOD                100
#define SPC5_GTM_TOM0_CHANNEL7_DUTY_CYCLE            50
#define SPC5_GTM_TOM0_CHANNEL7_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL7_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL7_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL7_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL7_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel7_callbacks;

/* ---- TOM0 Channel 8 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL8                   FALSE
#define SPC5_GTM_TOM0_CHANNEL8_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL8_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL8_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL8_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL8_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL8_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL8_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL8_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL8_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL8_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel8_callbacks;

/* ---- TOM0 Channel 9 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL9                   TRUE
#define SPC5_GTM_TOM0_CHANNEL9_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_CMU_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL9_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_TOM0_CHANNEL9_OUTPUT                TRUE
#define SPC5_GTM_TOM0_CHANNEL9_PERIOD                67
#define SPC5_GTM_TOM0_CHANNEL9_DUTY_CYCLE            30
#define SPC5_GTM_TOM0_CHANNEL9_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL9_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL9_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL9_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL9_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel9_callbacks;

/* ---- TOM0 Channel 10 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL10                   FALSE
#define SPC5_GTM_TOM0_CHANNEL10_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL10_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL10_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL10_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL10_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL10_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL10_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL10_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL10_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL10_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel10_callbacks;

/* ---- TOM0 Channel 11 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL11                   FALSE
#define SPC5_GTM_TOM0_CHANNEL11_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL11_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL11_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL11_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL11_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL11_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL11_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL11_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL11_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL11_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel11_callbacks;

/* ---- TOM0 Channel 12 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL12                   FALSE
#define SPC5_GTM_TOM0_CHANNEL12_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL12_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL12_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL12_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL12_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL12_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL12_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL12_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL12_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL12_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel12_callbacks;

/* ---- TOM0 Channel 13 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL13                   FALSE
#define SPC5_GTM_TOM0_CHANNEL13_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL13_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL13_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL13_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL13_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL13_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL13_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL13_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL13_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL13_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel13_callbacks;

/* ---- TOM0 Channel 14 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL14                   FALSE
#define SPC5_GTM_TOM0_CHANNEL14_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL14_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL14_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL14_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL14_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL14_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL14_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL14_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL14_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL14_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel14_callbacks;

/* ---- TOM0 Channel 15 Settings ---- */
#define SPC5_GTM_TOM0_USE_CHANNEL15                   FALSE
#define SPC5_GTM_TOM0_CHANNEL15_CLOCK_SOURCE          SPC5_GTM_TOM_CLOCK_SOURCE_FXCLK0
#define SPC5_GTM_TOM0_CHANNEL15_SIGNAL_LEVEL          SPC5_GTM_TOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_TOM0_CHANNEL15_OUTPUT                FALSE
#define SPC5_GTM_TOM0_CHANNEL15_PERIOD                0UL
#define SPC5_GTM_TOM0_CHANNEL15_DUTY_CYCLE            0UL
#define SPC5_GTM_TOM0_CHANNEL15_IRQ_MODE              SPC5_GTM_TOM_IRQ_MODE_LEVEL
#define SPC5_GTM_TOM0_CHANNEL15_INT_CCU0_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL15_INT_CCU0_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
#define SPC5_GTM_TOM0_CHANNEL15_INT_CCU1_ENABLED      FALSE
#define SPC5_GTM_TOM0_CHANNEL15_INT_CCU1_MODE         SPC5_GTM_TOM_INT_MODE_NORMAL
/* ---- -------------- ---- */

extern GTM_TOM_Channel_Callbacks gtm_tom0_channel15_callbacks;

#define SPC5_GTM_TOM1_USE_CHANNEL0                    FALSE
#define SPC5_GTM_TOM1_CHANNEL0_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL0_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL1                    FALSE
#define SPC5_GTM_TOM1_CHANNEL1_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL1_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL2                    FALSE
#define SPC5_GTM_TOM1_CHANNEL2_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL2_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL3                    FALSE
#define SPC5_GTM_TOM1_CHANNEL3_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL3_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL4                    FALSE
#define SPC5_GTM_TOM1_CHANNEL4_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL4_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL5                    FALSE
#define SPC5_GTM_TOM1_CHANNEL5_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL5_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL6                    FALSE
#define SPC5_GTM_TOM1_CHANNEL6_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL6_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL7                    FALSE
#define SPC5_GTM_TOM1_CHANNEL7_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL7_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL8                    FALSE
#define SPC5_GTM_TOM1_CHANNEL8_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL8_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL9                    FALSE
#define SPC5_GTM_TOM1_CHANNEL9_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL9_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL10                    FALSE
#define SPC5_GTM_TOM1_CHANNEL10_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL10_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL11                    FALSE
#define SPC5_GTM_TOM1_CHANNEL11_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL11_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL12                    FALSE
#define SPC5_GTM_TOM1_CHANNEL12_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL12_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL13                    FALSE
#define SPC5_GTM_TOM1_CHANNEL13_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL13_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL14                    FALSE
#define SPC5_GTM_TOM1_CHANNEL14_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL14_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM1_USE_CHANNEL15                    FALSE
#define SPC5_GTM_TOM1_CHANNEL15_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM1_CHANNEL15_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL0                    FALSE
#define SPC5_GTM_TOM2_CHANNEL0_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL0_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL1                    FALSE
#define SPC5_GTM_TOM2_CHANNEL1_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL1_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL2                    FALSE
#define SPC5_GTM_TOM2_CHANNEL2_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL2_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL3                    FALSE
#define SPC5_GTM_TOM2_CHANNEL3_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL3_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL4                    FALSE
#define SPC5_GTM_TOM2_CHANNEL4_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL4_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL5                    FALSE
#define SPC5_GTM_TOM2_CHANNEL5_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL5_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL6                    FALSE
#define SPC5_GTM_TOM2_CHANNEL6_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL6_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL7                    FALSE
#define SPC5_GTM_TOM2_CHANNEL7_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL7_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL8                    FALSE
#define SPC5_GTM_TOM2_CHANNEL8_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL8_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL9                    FALSE
#define SPC5_GTM_TOM2_CHANNEL9_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL9_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL10                    FALSE
#define SPC5_GTM_TOM2_CHANNEL10_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL10_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL11                    FALSE
#define SPC5_GTM_TOM2_CHANNEL11_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL11_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL12                    FALSE
#define SPC5_GTM_TOM2_CHANNEL12_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL12_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL13                    FALSE
#define SPC5_GTM_TOM2_CHANNEL13_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL13_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL14                    FALSE
#define SPC5_GTM_TOM2_CHANNEL14_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL14_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM2_USE_CHANNEL15                    FALSE
#define SPC5_GTM_TOM2_CHANNEL15_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM2_CHANNEL15_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL0                    FALSE
#define SPC5_GTM_TOM3_CHANNEL0_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL0_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL1                    FALSE
#define SPC5_GTM_TOM3_CHANNEL1_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL1_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL2                    FALSE
#define SPC5_GTM_TOM3_CHANNEL2_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL2_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL3                    FALSE
#define SPC5_GTM_TOM3_CHANNEL3_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL3_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL4                    FALSE
#define SPC5_GTM_TOM3_CHANNEL4_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL4_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL5                    FALSE
#define SPC5_GTM_TOM3_CHANNEL5_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL5_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL6                    FALSE
#define SPC5_GTM_TOM3_CHANNEL6_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL6_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL7                    FALSE
#define SPC5_GTM_TOM3_CHANNEL7_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL7_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL8                    FALSE
#define SPC5_GTM_TOM3_CHANNEL8_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL8_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL9                    FALSE
#define SPC5_GTM_TOM3_CHANNEL9_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL9_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL10                    FALSE
#define SPC5_GTM_TOM3_CHANNEL10_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL10_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL11                    FALSE
#define SPC5_GTM_TOM3_CHANNEL11_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL11_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL12                    FALSE
#define SPC5_GTM_TOM3_CHANNEL12_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL12_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL13                    FALSE
#define SPC5_GTM_TOM3_CHANNEL13_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL13_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL14                    FALSE
#define SPC5_GTM_TOM3_CHANNEL14_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL14_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */

#define SPC5_GTM_TOM3_USE_CHANNEL15                    FALSE
#define SPC5_GTM_TOM3_CHANNEL15_INT_CCU0_ENABLED       FALSE
#define SPC5_GTM_TOM3_CHANNEL15_INT_CCU1_ENABLED       FALSE
/* ---- -------------- ---- */


/*lint +e621*/
#endif /* _GTM_TOM_CFG_H_ */
/** @} */
