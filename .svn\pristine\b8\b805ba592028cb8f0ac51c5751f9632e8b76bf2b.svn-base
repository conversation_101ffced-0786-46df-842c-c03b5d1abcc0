/****************************************************************************
*
* Copyright © 2015-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com.
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY – NOT FOR USE IN PRODUCTION
*****************************************************************************/

#ifndef _CLOCK_CFG_H_
#define _CLOCK_CFG_H_

#include "sys.h"

#pragma ghs startnomisra

/*
 * Clock parameter settings.
 */
#define SPC5_XOSC_DISABLED                  FALSE
#define SPC5_XOSC_CLK                       40000000U
#define SPC5_OSC_BYPASS                     FALSE
#define SPC5_NO_INIT                        FALSE
#define SPC5_ALLOW_OVERCLOCK                FALSE
#define SPC5_ENABLE_CLOCKOUT0               TRUE
#define SPC5_ENABLE_CLOCKOUT1               TRUE
#define SPC5_DISABLE_WATCHDOG               TRUE
//#define SPC5_CLEAR_FCCU_RGM                 TRUE
#define SPC5_CLOCK_FAILURE_HOOK()           SYS_SwRST() /* Sys_Halt() */
#define SPC5_ME_ME_BITS                     (0UL | SPC5_ME_ME_RESET | SPC5_ME_ME_SAFE | SPC5_ME_ME_DRUN | SPC5_ME_ME_RUN0 | SPC5_ME_ME_RUN1 | SPC5_ME_ME_RUN2 | SPC5_ME_ME_RUN3 | SPC5_ME_ME_HALT0 | SPC5_ME_ME_STOP0)
#define SPC5_ME_SAFE_MC_BITS                (0UL | SPC5_ME_MC_PWRLVL(0) | SPC5_ME_MC_PDO | SPC5_ME_MC_MVRON | SPC5_ME_MC_FLAON_NORMAL | SPC5_ME_MC_IRCON | SPC5_ME_MC_SYSCLK_IRC)
#define SPC5_ME_DRUN_MC_BITS                (0UL | SPC5_ME_MC_PWRLVL(0) | SPC5_ME_MC_MVRON | SPC5_ME_MC_FLAON_NORMAL | SPC5_ME_MC_PLL1ON | SPC5_ME_MC_PLL0ON | SPC5_ME_MC_XOSCON | SPC5_ME_MC_IRCON | SPC5_ME_MC_SYSCLK_PLL1PHI)
#define SPC5_ME_RUN0_MC_BITS                (0UL | SPC5_ME_MC_PWRLVL(0) | SPC5_ME_MC_MVRON | SPC5_ME_MC_FLAON_NORMAL | SPC5_ME_MC_PLL1ON | SPC5_ME_MC_PLL0ON | SPC5_ME_MC_XOSCON | SPC5_ME_MC_IRCON | SPC5_ME_MC_SYSCLK_PLL1PHI)
#define SPC5_ME_RUN1_MC_BITS                (0UL | SPC5_ME_MC_PWRLVL(0) | SPC5_ME_MC_MVRON | SPC5_ME_MC_FLAON_NORMAL | SPC5_ME_MC_IRCON | SPC5_ME_MC_SYSCLK_IRC)
#define SPC5_ME_RUN2_MC_BITS                (0UL | SPC5_ME_MC_PWRLVL(0) | SPC5_ME_MC_MVRON | SPC5_ME_MC_FLAON_NORMAL | SPC5_ME_MC_IRCON | SPC5_ME_MC_SYSCLK_IRC)
#define SPC5_ME_RUN3_MC_BITS                (0UL | SPC5_ME_MC_PWRLVL(0) | SPC5_ME_MC_MVRON | SPC5_ME_MC_FLAON_NORMAL | SPC5_ME_MC_IRCON | SPC5_ME_MC_SYSCLK_IRC)
#define SPC5_ME_HALT0_MC_BITS               (0UL | SPC5_ME_MC_PWRLVL(0) | SPC5_ME_MC_MVRON | SPC5_ME_MC_FLAON_LP | SPC5_ME_MC_IRCON | SPC5_ME_MC_SYSCLK_IRC)
#define SPC5_ME_STOP0_MC_BITS               (0UL | SPC5_ME_MC_PWRLVL(0) | SPC5_ME_MC_MVRON | SPC5_ME_MC_FLAON_PD | SPC5_ME_MC_IRCON | SPC5_ME_MC_SYSCLK_IRC)
#define SPC5_ME_MC_IS_XOSCON                TRUE
#define SPC5_ME_RUN_PC3_BITS                (0UL)
#define SPC5_ME_RUN_PC4_BITS                (0UL)
#define SPC5_ME_RUN_PC5_BITS                (0UL)
#define SPC5_ME_RUN_PC6_BITS                (0UL)
#define SPC5_ME_RUN_PC7_BITS                (0UL)
#define SPC5_ME_LP_PC4_BITS                 (0UL)
#define SPC5_ME_LP_PC5_BITS                 (0UL)
#define SPC5_ME_LP_PC6_BITS                 (0UL)
#define SPC5_ME_LP_PC7_BITS                 (0UL)
#define SPC5_FINAL_RUNMODE                  SPC5_RUNMODE_RUN0

#define SPC5_SYS_CLK                        160000000U
#define SPC5_CORE0_CLK                      160000000U
#define SPC5_CORE2_CLK                      80000000U
#define SPC5_PER_CLK                        80000000U
#define SPC5_PLL0_PHI_CLK                  240000000U 
#define SPC5_PLL1_PHI_CLK                  160000000U               

//MC, added:
#define SPC5_PERIPHERAl_CLK                 80000000U
#define SPC5_SDADC_CLK                      16000000U
#define SPC5_SARADC_CLK                     14120000U
#define SPC5_DSPI_CLK                       40000000U
#define SPC5_RTI_CLK                        10000000U
#define SPC5_MCAN_CLK                       40000000U
#define SPC5_TTCAN_CLK                  SPC5_MCAN_CLK
#define SPC_PLLO_PHI                       240000000U
#define SPC_PLLO_PHI1                       48000000U
#define SPC_PLL1_PHI                       160000000U
#define SPC_DSPI_CLK                       40000000U

#pragma ghs endnomisra

#endif /* _CLOCK_CFG_H_ */

