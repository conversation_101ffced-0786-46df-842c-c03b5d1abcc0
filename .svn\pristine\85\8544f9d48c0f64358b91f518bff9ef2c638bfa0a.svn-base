/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  IGNLoadTest
**  Filename        :  dspi.c
**  Created on      :  10-sep-2021 14:04:00
**  Original author :  LanaL
******************************************************************************/

#include "rtwtypes.h"   /* used also if not defined _BUILD_IGN_LOAD_TEST_ */

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "IGNLoadTest.h"

#ifdef _BUILD_IGN_LOAD_TEST_

uint16_T IPri[N_CYL_MAX]; // primary data.
volatile uint8_T IgnLoadTestDone;

static const uint8_T IgnInCmd[N_CYL_MAX] = {IP_IgnCmd_1, IP_IgnCmd_2, IP_IgnCmd_3, IP_IgnCmd_4, IP_IgnCmd_5, IP_IgnCmd_6, IP_IgnCmd_7, IP_IgnCmd_8};

uint8_T IGNBuckTest(void)
{
    uint8_T retVal;

    retVal = BuckDiagMgm_IgnBuckTest();

    return retVal;
}

uint8_T IGNLoadTest(void)
{
    uint8_T retVal;
    uint16_T cmdOutTestDelay;

    cmdOutTestDelay = CMDOUTTESTDELAY;

    DMA_StopGlob(DMA_CH0);
    DMA_StopGlob(DMA_CH1);
    DMA_StopGlob(DMA_CH2);
    DMA_StopGlob(DMA_CH3);
    
    DMA_StopGlob(DMA_CH16);
    DMA_StopGlob(DMA_CH17);

    
    DMA_StopGlob(DMA_CH24);
    DMA_StopGlob(DMA_CH25);

    /* Digital Output */
    #ifdef IP_IgnCmd_1
    IGNLoadTest_SimCmdOut(0u);
    #endif
    
    #ifdef IP_IgnCmd_1
    IGNLoadTest_SimCmdOut(1u);
    #endif
    
    TIMING_SetDelay(cmdOutTestDelay);
    
    #ifdef IP_IgnCmd_1
    IGNLoadTest_SimCmdOut(0u);
    #endif

    #ifdef IP_IgnCmd_2
    IGNLoadTest_SimCmdOut(1u);
    #endif

    TIMING_SetDelay(cmdOutTestDelay);

    #ifdef IP_IgnCmd_3
    IGNLoadTest_SimCmdOut(2u);
    #endif

    #ifdef IP_IgnCmd_4
    IGNLoadTest_SimCmdOut(3u);
    #endif

    TIMING_SetDelay(cmdOutTestDelay);

    #ifdef IP_IgnCmd_5
    IGNLoadTest_SimCmdOut(4u);
    #endif

    #ifdef IP_IgnCmd_6
    IGNLoadTest_SimCmdOut(5u);
    #endif

    TIMING_SetDelay(cmdOutTestDelay);

    #ifdef IP_IgnCmd_7
    IGNLoadTest_SimCmdOut(6u);
    #endif

    #ifdef IP_IgnCmd_8
    IGNLoadTest_SimCmdOut(7u);
    #endif

    retVal = IGNLoadTest_ISR();

    return retVal;
}

static uint8_T IGNLoadTest_ISR(void)
{
    uint8_T stdiag;
    uint8_T cylSelTst;
    uint8_T retVal = LT_NORMAL;

    for (cylSelTst = 0u; cylSelTst < N_CYLINDER; cylSelTst++)
    {
        if (IPri[cylSelTst] < THRLOADTST)
        {
            retVal = LT_ERROR;
        }
        else
        {
            /* MISRA */
        }
    }

    IgnLoadTestDone = 1u;

    return retVal;
}


static void IGNLoadTest_SimCmdOut(uint8_T cyl)
{
    uint16_T cmdOutDwell;

    cmdOutDwell = CMDOUTTESTDWELL;

    if (cyl < N_CYLINDER)
    {
        PORT_PinConfigReset(IgnInCmd[cyl], SSS_0, OUT_OD_MED, MCSR_SMC_DONTDISABLEPIN, INPUT_DISABLE, MCSR_WPUE_DISABLE, OUT_LOW);

        TIMING_SetDelay(cmdOutDwell); // Dwell time [us]

        IPri[cyl] = AnalogIn_LoadTest(cyl);
        IPri[cyl] = (uint16_T)(IPri[cyl] << 2);

        PORT_PinConfigReset(IgnInCmd[cyl], SSS_0, OUT_DISABLE, MCSR_SMC_DONTDISABLEPIN, INPUT_WEAK_PULLUP, MCSR_WPUE_ENABLE, OUT_HIGH);
    }
    else
    {
        /* Error */
    }
}

#else

uint8_T IGNBuckTest(void)
{
    return LT_NORMAL;
}

uint8_T IGNLoadTest(void)
{
    return LT_NORMAL;
}

#endif

