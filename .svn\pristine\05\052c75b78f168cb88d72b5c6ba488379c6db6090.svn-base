/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
#ifndef _CPU_EEP_C_
#define _CPU_EEP_C_

#include "Cpumgm_out.h"

///EE Max Cpu LoadPerc
uint16_T EEMaxCpuLoadPerc = 0u;
///EE Task queue counter
uint8_T  EEOsTaskListMaxElem[SOFT_SET_INTERRUPT_NUM] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
///EE Counter Cpu LoadPerc
uint16_T EECntCpuLoadPerc = 0u;

#endif /* _CPU_EEP_C_ */

