/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      KnockCorrAdp.h
 **  Date:          28-May-2021
 **
 **  Model Version: 1.1298
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_KnockCorrAdp_h_
#define RTW_HEADER_KnockCorrAdp_h_
#include <string.h>
#ifndef KnockCorrAdp_COMMON_INCLUDES_
# define KnockCorrAdp_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* KnockCorrAdp_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  uint16_T Memory1_PreviousInput;      /* '<S32>/Memory1' */
  uint16_T Memory_PreviousInput;       /* '<S32>/Memory' */
  uint16_T Memory1_PreviousInput_f;    /* '<S33>/Memory1' */
  uint16_T Memory_PreviousInput_e;     /* '<S33>/Memory' */
} DW_KnockCorrAdp_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_KnockCorrAdp_T KnockCorrAdp_DW;

/* Model entry point functions */
extern void KnockCorrAdp_initialize(void);

/* Exported entry point function */
extern void KnockCorrAdp_10ms(void);

/* Exported entry point function */
extern void KnockCorrAdp_EOA(void);

/* Exported entry point function */
extern void KnockCorrAdp_NoSync(void);

/* Exported entry point function */
extern void KnockCorrAdp_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern int16_T DeltaSAKnockCorrAd[8];  /* '<S3>/Merge1' */

/* Delta knock correction for protection */
extern uint16_T KnockRecGain;          /* '<S3>/Merge' */

/* Gain for knock correction during recovery */
extern int16_T SAKnockCorrAd[8];       /* '<S3>/Merge2' */

/* Adaptive knock correction on spark advance */
extern uint8_T TrigKnockAdat[8];       /* '<S3>/Merge3' */

/* Trigger for knock learning */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S19>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S27>/Data Type Duplicate' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion' : Eliminate redundant data type conversion
 * Block '<S23>/Reshape' : Reshape block reduction
 * Block '<S26>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion1' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'KnockCorrAdp'
 * '<S1>'   : 'KnockCorrAdp/KnockCorrAdp_Eval'
 * '<S2>'   : 'KnockCorrAdp/KnockCorrAdp_Scheduler'
 * '<S3>'   : 'KnockCorrAdp/KnockCorrAdp_Eval/Merge'
 * '<S4>'   : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode'
 * '<S5>'   : 'KnockCorrAdp/KnockCorrAdp_Eval/Recovery_Mode'
 * '<S6>'   : 'KnockCorrAdp/KnockCorrAdp_Eval/Reset'
 * '<S7>'   : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionMgm'
 * '<S8>'   : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate'
 * '<S9>'   : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/EnablingCondition'
 * '<S10>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/Zones_Learn'
 * '<S11>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionMgm/CALCULATE_CORRECTION.calculateAvg'
 * '<S12>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell'
 * '<S13>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/CalculateAdaptiveCorrection'
 * '<S14>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/LowerSaturation'
 * '<S15>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/Term_i_AdaptiveCorrection'
 * '<S16>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/UpperSaturation'
 * '<S17>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/LowerSaturation/LookUp 1-D VTDELTAKCORRMIN'
 * '<S18>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/LowerSaturation/LookUp 1-D VTDELTAKCORRMIN/LookUp_IR_S16'
 * '<S19>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/LowerSaturation/LookUp 1-D VTDELTAKCORRMIN/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S20>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/Term_i_AdaptiveCorrection/ArrangeLSB'
 * '<S21>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/Term_i_AdaptiveCorrection/ArrangeLSB1'
 * '<S22>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/Term_i_AdaptiveCorrection/LookUp 2-D TBGNAD'
 * '<S23>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/Term_i_AdaptiveCorrection/LookUp 2-D TBGNAD/Look2D_U16_U16_U16'
 * '<S24>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/Term_i_AdaptiveCorrection/LookUp 2-D TBGNAD/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 * '<S25>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/UpperSaturation/LookUp 1-D VTDELTAKCORRMIN'
 * '<S26>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/UpperSaturation/LookUp 1-D VTDELTAKCORRMIN/LookUp_IR_S16'
 * '<S27>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/AdaptiveCorrectionSingleCell/UpperSaturation/LookUp 1-D VTDELTAKCORRMIN/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S28>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/CalculateAdaptiveCorrection/CalculateSaKnockCorrAd'
 * '<S29>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/CalculateAdaptiveCorrection/KeepSaKnockCorrAd'
 * '<S30>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionUpdate/CalculateAdaptiveCorrection/CalculateSaKnockCorrAd/ArrangeLookUpOutputLSB'
 * '<S31>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/Zones_Learn/LookUp_VTTDCSTABKNOCK'
 * '<S32>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/Zones_Learn/Signal_Stability1'
 * '<S33>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/Zones_Learn/Signal_Stability2'
 * '<S34>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/Zones_Learn/LookUp_VTTDCSTABKNOCK/LookUp_IR_U16'
 * '<S35>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/NormalMode/Zones_Learn/LookUp_VTTDCSTABKNOCK/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S36>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/Recovery_Mode/AssignRecoveryValues'
 * '<S37>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/Recovery_Mode/GainCalculation'
 * '<S38>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/Recovery_Mode/NominalAdaptiveCorrection'
 * '<S39>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/Recovery_Mode/AssignRecoveryValues/ArrangeLSB'
 * '<S40>'  : 'KnockCorrAdp/KnockCorrAdp_Eval/Recovery_Mode/NominalAdaptiveCorrection/ArrangeLookUpOutputLSB'
 */

/*-
 * Requirements for '<Root>': KnockCorrAdp
 */
#endif                                 /* RTW_HEADER_KnockCorrAdp_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/