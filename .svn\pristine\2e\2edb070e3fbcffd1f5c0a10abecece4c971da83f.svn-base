/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  BuckDiagMgm
**  Filename        :  BuckDiagMgm.c
**  Created on      :  31-mar-2021 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifdef _BUILD_BUCKDIAGMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "buckdiagmgm.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
static uint8_T VtStBuckFault[BUCK_NUMBER] = {NO_BUCK_FAULT, NO_BUCK_FAULT};
static uint8_T FlgVBuckCheck[BUCK_NUMBER] = {0u, 0u};

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : BuckDiagMgm_Init
**
**   Description:
**    Reset the values of the module variables. The global variables (outputs) 
**    and the static variables (states) are reset to the default values by this function.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void BuckDiagMgm_Init(void)
{
    VtStBuckFault[0] = NO_BUCK_FAULT;
    VtStBuckFault[1] = NO_BUCK_FAULT;

    FlgVBuckCheck[0] = 0u;
    FlgVBuckCheck[1] = 0u;
}

/******************************************************************************
**   Function    : BuckDiagMm_Batt
**
**   Description:
**    Rising edge Trigger In event
**
**   Parameters :
**    cyl:      cylinder index
**    riseEdgeTime: absolute rise time
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void BuckDiagMgm_Batt(void)
{
    uint8_T stdiag;

    if((FlgVBuckCheck[0] == 1u) || (FlgVBuckCheck[1] == 1u))
    {
        if (StDiag[DIAG_BUCK_A] != FAULT)
        {
            /* Buck 0 voltage acquisition */
            AnalogIn_SupplyCoil1_Acq();

            if (VSupplyCoil1ADC > THRVSUPPLYCOILDIAG)
            {
                VtStBuckFault[0] = BATT_MON_FAULT_OFF;

                while (StDiag[DIAG_BUCK_A] != FAULT)
                {
                    /* Set buck diagnosis */
                    DiagMgm_SetDiagState(DIAG_BUCK_A, CIRCUIT_SHORT_TO_VCC, &stdiag);
                }
            }
            else
            {
                /* Set buck diagnosis */
                DiagMgm_SetDiagState(DIAG_BUCK_A, NO_PT_FAULT, &stdiag);
            }
        }

        if (StDiag[DIAG_BUCK_B] != FAULT)
        {
            /* Buck 1 voltage acquisition */
            AnalogIn_SupplyCoil2_Acq();

            if (VSupplyCoil2ADC > THRVSUPPLYCOILDIAG)
            {
                VtStBuckFault[1] = BATT_MON_FAULT_OFF;

                while (StDiag[DIAG_BUCK_B] != FAULT)
                {
                    /* Set buck diagnosis */
                    DiagMgm_SetDiagState(DIAG_BUCK_B, CIRCUIT_SHORT_TO_VCC, &stdiag);
                }
            }
            else
            {
                /* Set buck diagnosis */
                DiagMgm_SetDiagState(DIAG_BUCK_B, NO_PT_FAULT, &stdiag);
            }
        }

        if (StDiag[DIAG_BUCK_A] == FAULT)
        {
            PORT_BuckDiagRec(0u);
        }
        else
        {
            /* MISRA */
        }

        if (StDiag[DIAG_BUCK_B] == FAULT)
        {
            PORT_BuckDiagRec(1u);
        }
        else
        {
            /* MISRA */
        }

        if ((StDiag[DIAG_BUCK_A] != FAULT) || (StDiag[DIAG_BUCK_B] != FAULT))
        {
            PORT_EnableBuckEn(0u);
            PORT_EnableBuckEn(1u);
        }
        else
        {
            /* MISRA */
        }

        FlgVBuckCheck[0] = 0u;
        FlgVBuckCheck[1] = 0u;
    }
    else
    {
        /* MISRA */
    }
}

/******************************************************************************
**   Function    : BuckDiagMgm_RunCheck
**
**   Description:
**    Rising edge Trigger In event
**
**   Parameters :
**    cyl:      cylinder index
**    riseEdgeTime: absolute rise time
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void BuckDiagMgm_RunCheck(uint8_T buckIdx)
{
    if ((StDiag[DIAG_BUCK_A] != FAULT) || (StDiag[DIAG_BUCK_B] != FAULT))
    {
        /* Check if buck was previously disabled */
        if(VtStBuckFault[buckIdx] == BUCK_MON_FAULT_OFF)
        {
            PORT_EnableBuckEn(0u);
            PORT_EnableBuckEn(1u);
            VtStBuckFault[buckIdx] = BUCK_MON_FAULT_ON;
        }
        else if(VtStBuckFault[buckIdx] == BATT_MON_FAULT_OFF)
        {
            PORT_EnableBuckEn(0u);
            PORT_EnableBuckEn(1u);
            VtStBuckFault[buckIdx] = BATT_MON_FAULT_ON;
        }
        else
        {
            /* DO NOTHING */
        }

        /* Buck diagnosis */
        if((buckIdx == 0u) && (StDiag[DIAG_BUCK_A] != FAULT))
        {
            BuckDiagMgm_Buck0();
        }
        else if ((buckIdx == 1u) && (StDiag[DIAG_BUCK_B] != FAULT))
        {
            BuckDiagMgm_Buck1();
        }
        else
        {
            /* MISRA */
        }
    }
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : BuckDiagMgm_Buck0
**
**   Description:
**    Rising edge Trigger In event
**
**   Parameters :
**    cyl:      cylinder index
**    riseEdgeTime: absolute rise time
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void BuckDiagMgm_Buck0(void)
{
    uint8_T  stdiag;
    uint16_T thrIsupplyCoil;
    uint16_T thrIbatt;

    LookUp_U16_U16(&thrIsupplyCoil, &VTTHRISUPPLYCOILDIAG[0], Rpm, &BKTHRISUPPLYCOILDIAG[0], BKTHRISUPPLYCOILDIAG_dim - 1u);

    /* Buck current acquisition */
    AnalogIn_IBuck1_Acq();
    /* Check threshold */
    if (ISupplyCoil1ADC > thrIsupplyCoil)
    {
        /* Buck Voltage shall not be checked */
        FlgVBuckCheck[0] = 0u;
        /* Set buck diagnosis */
        DiagMgm_SetDiagState(DIAG_BUCK_A, CIRCUIT_SHORT_TO_GND, &stdiag);
    }
    else
    {
        LookUp_U16_U16(&thrIbatt, &VTTHRIBATTDIAG[0], Rpm, &BKTHRIBATTDIAG[0], BKTHRIBATTDIAG_dim - 1u);

        /* Batt current acquisition */
        AnalogIn_IBatt_Acq();
        if (IBattADC >= thrIbatt)
        {
            /* Disable buck 0 */
            PORT_DisableBuckEn(0u);
            /* Disable buck 1 */
            PORT_DisableBuckEn(1u);
            /* Buck Voltage shall be checked */
            FlgVBuckCheck[0] = 1u;
        }
        else
        {
            DiagMgm_SetDiagState(DIAG_BUCK_A, NO_PT_FAULT, &stdiag);
        }
    }

    if (StDiag[DIAG_BUCK_A] == FAULT)
    {
        VtStBuckFault[0] = BUCK_MON_FAULT_OFF;
        PORT_BuckDiagRec(0U);
    }
    else
    {
        /* MISRA */
    }
}

/******************************************************************************
**   Function    : BuckDiagMgm_Buck1
**
**   Description:
**    Rising edge Trigger In event
**
**   Parameters :
**    cyl:      cylinder index
**    riseEdgeTime: absolute rise time
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void BuckDiagMgm_Buck1(void)
{
    uint8_T  stdiag;
    uint16_T thrIsupplyCoil;
    uint16_T thrIbatt;

    LookUp_U16_U16(&thrIsupplyCoil, &VTTHRISUPPLYCOILDIAG[0], Rpm, &BKTHRISUPPLYCOILDIAG[0], BKTHRISUPPLYCOILDIAG_dim - 1u);

    /* Buck current acquisition */
    AnalogIn_IBuck2_Acq();
    /* Check threshold */
    if (ISupplyCoil2ADC > thrIsupplyCoil)
    {
        /* Buck Voltage shall not be checked */
        FlgVBuckCheck[1] = 0u;
        /* Set buck diagnosis */
        DiagMgm_SetDiagState(DIAG_BUCK_B, CIRCUIT_SHORT_TO_GND, &stdiag);
    }
    else
    {
        LookUp_U16_U16(&thrIbatt, &VTTHRIBATTDIAG[0], Rpm, &BKTHRIBATTDIAG[0], BKTHRIBATTDIAG_dim - 1u);

        /* Batt current acquisition */
        AnalogIn_IBatt_Acq();
        if (IBattADC >= thrIbatt)
        {
            /* Disable buck 0 */
            PORT_DisableBuckEn(0u);
            /* Disable buck 1 */
            PORT_DisableBuckEn(1u);
            /* Buck Voltage shall be checked */
            FlgVBuckCheck[1] = 1u;
        }
        else
        {
            DiagMgm_SetDiagState(DIAG_BUCK_B, NO_PT_FAULT, &stdiag);
        }
    }

    if (StDiag[DIAG_BUCK_B] == FAULT)
    {
        VtStBuckFault[1] = BUCK_MON_FAULT_OFF;
        PORT_BuckDiagRec(1u);
    }
    else
    {
        /* MISRA */
    }
}

/******************************************************************************
**   Function    : BuckDiagMgm_5ms
**
**   Description:
**    Diagnosis at 5ms task
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void BuckDiagMgm_5ms(void)
{
    static uint8_T buckIdx = 0u;

    buckIdx = buckIdx & 0x01u;

    if ((StDiag[DIAG_BUCK_A] != FAULT) && (buckIdx == 0u))
    {
        BuckDiagMgm_PeriodicCheck(buckIdx);
    }
    else
    {
        /* MISRA */
    }

    if ((StDiag[DIAG_BUCK_B] != FAULT) && (buckIdx == 1u))
    {
        BuckDiagMgm_PeriodicCheck(buckIdx);
    }
    else
    {
        /* MISRA */
    }

    buckIdx++;
}

/******************************************************************************
**   Function    : BuckDiagMgm_IgnBuckTest
**
**   Description:
**    Diagnosis at power on
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
uint8_T BuckDiagMgm_IgnBuckTest(void)
{
    uint8_T retVal = LT_NORMAL;
    uint8_T cntIbattDiag = 0u;
    uint8_T stdiag;
    uint16_T thrIsupplyCoil;
    uint16_T thrIbatt;
    uint16_T cntBuckDiagLoop = 0u;

    // Buck ref set to 100%
    PORT_ConfigBuckDiag();

    TIMING_SetDelay(600u); // 600us

    LookUp_U16_U16(&thrIsupplyCoil, &VTTHRISUPPLYCOILDIAG[0], Rpm, &BKTHRISUPPLYCOILDIAG[0], BKTHRISUPPLYCOILDIAG_dim - 1u);

    while (((TestResult[DIAG_BUCK_A] == NO_RESULT) || (TestResult[DIAG_BUCK_B] == NO_RESULT)) && (cntBuckDiagLoop < MAX_BUCK_DIAG_LOOP))
    {
        cntBuckDiagLoop++;
        
        if (TestResult[DIAG_BUCK_A] == NO_RESULT)
        {
            /* Buck current acquisition */
            AnalogIn_IBuck1_Acq();
            if (ISupplyCoil1ADC > thrIsupplyCoil)
            {
                DiagMgm_SetDiagState(DIAG_BUCK_A, CIRCUIT_SHORT_TO_GND, &stdiag);
            }
            else
            {
                DiagMgm_SetDiagState(DIAG_BUCK_A, NO_PT_FAULT, &stdiag);
            }
        }

        if (TestResult[DIAG_BUCK_B] == NO_RESULT)
        {
            AnalogIn_IBuck2_Acq();
            if (ISupplyCoil2ADC > thrIsupplyCoil)
            {
                DiagMgm_SetDiagState(DIAG_BUCK_B, CIRCUIT_SHORT_TO_GND, &stdiag);
            }
            else
            {
                DiagMgm_SetDiagState(DIAG_BUCK_B, NO_PT_FAULT, &stdiag);
            }
        }
        
        TIMING_SetDelay(5u);
    }

    if (StDiag[DIAG_BUCK_A] == FAULT)
    {
        PORT_BuckDiagRec(0u);
        retVal = LT_ERROR;
    }
    else
    {
        /* MISRA */
    }

    if (StDiag[DIAG_BUCK_B] == FAULT)
    {
        PORT_BuckDiagRec(1u);
        retVal = LT_ERROR;
    }
    else
    {
        /* MISRA */
    }

    if (retVal != LT_ERROR)
    {
        DiagMgm_ResetOneDiag(DIAG_BUCK_A);
        DiagMgm_ResetOneDiag(DIAG_BUCK_B);

        // TOM0 Outputs start
        Gtm_Eisb_TomEnable();

        // TOM0 Channels 2 - 3 set as source for buck ref signals
        PORT_ConfigBuckRefTom();

        TIMING_SetDelay(1000u); // 1000us

        thrIbatt = THRIBATTPWRON;

        for (uint8_T i = 0u; i < IBATT_SAMPLES; i++)
        {
            /* Batt current acquisition */
            AnalogIn_IBatt_Acq();

            if (IBattADC >= thrIbatt)
            {
                cntIbattDiag++;
            }
        
            TIMING_SetDelay(30u);
         }
        
        if (cntIbattDiag >= SAMPLES_CONFAULT)
        {
            /* Disable buck 0 */
            PORT_DisableBuckEn(0u);
            /* Disable buck 1 */
            PORT_DisableBuckEn(1u);

            while (((TestResult[DIAG_BUCK_A] == NO_RESULT) || (TestResult[DIAG_BUCK_B] == NO_RESULT)) && (cntBuckDiagLoop < MAX_BUCK_DIAG_LOOP))
            {
                cntBuckDiagLoop++;
                
                if (TestResult[DIAG_BUCK_A] == NO_RESULT)
                {
                    /* Buck 0 voltage acquisition */
                    AnalogIn_SupplyCoil1_Acq();
                    if (VSupplyCoil1ADC > THRVSUPPLYCOILDIAG)
                    {
                        DiagMgm_SetDiagState(DIAG_BUCK_A, CIRCUIT_SHORT_TO_VCC, &stdiag);
                    }
                    else
                    {
                        DiagMgm_SetDiagState(DIAG_BUCK_A, NO_PT_FAULT, &stdiag);
                    }
                }

                if (TestResult[DIAG_BUCK_B] == NO_RESULT)
                {
                    /* Buck 1 voltage acquisition */
                    AnalogIn_SupplyCoil2_Acq();
                    if (VSupplyCoil2ADC > THRVSUPPLYCOILDIAG)
                    {
                        DiagMgm_SetDiagState(DIAG_BUCK_B, CIRCUIT_SHORT_TO_VCC, &stdiag);
                    }
                    else
                    {
                        DiagMgm_SetDiagState(DIAG_BUCK_B, NO_PT_FAULT, &stdiag);
                    }
                }

                TIMING_SetDelay(5u);
            }

            if (StDiag[DIAG_BUCK_A] == FAULT)
            {
                PORT_BuckDiagRec(0u);
                retVal = LT_ERROR;
            }
            else
            {
                /* MISRA */
            }

            if (StDiag[DIAG_BUCK_B] == FAULT)
            {
                PORT_BuckDiagRec(1u);
                retVal = LT_ERROR;
            }
            else
            {
                /* MISRA */
            }
        }
        else
        {
            /* MISRA */
        }
    }
    else
    {
        /* MISRA */
    }

    if ((StDiag[DIAG_BUCK_A] != FAULT) || (StDiag[DIAG_BUCK_B] != FAULT))
    {
        PORT_EnableBuckEn(0u);
        PORT_EnableBuckEn(1u);
    }
    else
    {
        /* MISRA */
    }

    return retVal;
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : BuckDiagMgm_PeriodicCheck
**
**   Description:
**    Periodic check in case of no trigger
**
**   Parameters :
**    BuckIdx
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void BuckDiagMgm_PeriodicCheck(uint8_T buckIdx)
{
    if ((Rpm == 0u) && (RpmCAN == 0u) && (FlgTrigStall != 0u) && (EngstsCAN == ENGSTS_OFF) && (FlgIoliIGN == 0u))
    {
        BuckDiagMgm_Batt();
        BuckDiagMgm_RunCheck(buckIdx);
    }
    else
    {
        /* No hace Nada */
    }
}



#endif // _BUILD_BUCKDIAGMGM_

