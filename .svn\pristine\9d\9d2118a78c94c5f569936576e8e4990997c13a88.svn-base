/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Startup
**  Filename        :  app_tag.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "sys.h"
#include "App_tag.h"

#pragma ghs nowarning 32 

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
extern uint32_T __2APP_START;
extern uint32_T __2APP_SIZE;



const BlockDescription app_tag =
{
    0x3u,
    (uint32_T)&__2APP_START,
    (uint32_T)&__2APP_SIZE,
    APP_TAG_PREF APP_VER,
    {0u,0u,0u,0u,0u,0u,0u,0u,0u},               // padding, few bytes are used by AppTagEPK.exe
    ELDOR_ECU_HW_NUM,                           // HW number value for HW compatibility checks during UDS reprogrammings
    ELDOR_ECU_HW_VER,                           // HW version value for HW compatibility checks during UDS reprogrammings
    0xFFFFFFFFu,                                // validSecMemRegion, 0x00000000 -> security check PASSED, 0xFFFFFFFF -> security check NOT PASSED
    0x1u,                                       // blockVersion Data Timestamp of the block code
    0xFFFFFFFFu,                                // validMemoryRegion   Burner block code Identifier
    0u,                                         // blockChecksum      Block Data Checksum (CRC32)
    0u                                          // blockStatusOK       Block Status Integrity: 0x0 = status OK; (! 0x0) = status NOT OK
};
#pragma ghs endnowarning /* warning #32-D: expression must have arithmetic type */
 
/****************************************************************************
 ****************************************************************************/
