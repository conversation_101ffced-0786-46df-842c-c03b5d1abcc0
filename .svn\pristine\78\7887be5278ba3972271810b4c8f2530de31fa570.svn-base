/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef DIGIN_H
#define DIGIN_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "Digio_out.h"
#include "Utils_out.h"
#include "OS_api.h"
#include "tasksdefs.h"
#include "digin_out.h"
#include "analogin_out.h"


/*!
\defgroup PrivateDefines Private Defines
\brief Defines with module scope

\sgroup
*/
/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
#define ENGINE_TYPE_C8   0u
#define ENGINE_TYPE_NEF  1u
#define ENGINE_TYPE_C9   2u
#define ENGINE_TYPE_C13  3u
#define MAX_ENG_TYPE     4u

//#define DISABLE_KEY_SIGNAL

/* Compile-time check */
#if (ENGINE_TYPE == I1_CNH_6C) && (N_BANKS != MAX_ENG_TYPE)
#error ERROR: wrong max engine type definition
#endif
/*!\egroup*/

/*!
\defgroup PrivateTypedef Private Typedefs
\brief Types with module scope

\sgroup
*/
/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
///This is a private typedef 
//typedef integer_T int; 

/*!\egroup*/


/*!
\defgroup PrivateInline Private Inline Functions
\brief Inline functions with module scope

\sgroup
*/
/*-----------------------------------*
 * PRIVATE INLINE FUNCTIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   Inline function name
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this inline function 
*/
/**************************************************************************/
//static inline void privateInlineFunction(void)
//{
    //Remember to put some comments inside function
    //In this moment YOU and GOD know how this code works.
    //Within six months only GOD will.      
//}
/*!\egroup*/

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern  CALQUAL CALQUAL_POST uint8_T     THDEBKEYSIGNAL;
extern  CALQUAL CALQUAL_POST uint8_T     THDEBKEYENDIAGSIGNAL;
extern  CALQUAL CALQUAL_POST uint8_T     FLGFOKEYSIGNAL;
extern  CALQUAL CALQUAL_POST uint8_T     FOKEYSIGNAL;

extern  CALQUAL CALQUAL_POST uint8_T     THDEBBANKSEL;
extern  CALQUAL CALQUAL_POST uint8_T     FLGFOBANKSEL;
extern  CALQUAL CALQUAL_POST uint8_T     FOBANKSEL;

extern  CALQUAL CALQUAL_POST uint8_T     ENCANENGTYPE;
extern  CALQUAL CALQUAL_POST uint8_T     FORCEENGINETYPERUN;

extern CALQUAL CALQUAL_POST uint16_T  VMIDLOVBANKSEL;
extern CALQUAL CALQUAL_POST uint16_T  VMIDHIVBANKSEL;

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */
/* Example:
static void privateFunction(void)
*/

#endif
/****************************************************************************
 ****************************************************************************/


