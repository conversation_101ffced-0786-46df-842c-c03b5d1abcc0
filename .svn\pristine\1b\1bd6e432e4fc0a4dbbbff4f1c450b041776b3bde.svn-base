/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  STM
**  Filename        :  STM.c
**  Created on      :  09-Feb-2022 11:00:00
**  Original author :  MocciA
******************************************************************************/
#ifdef _BUILD_STM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "stm.h"

/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/// Peripheral configuration status
uint16_T STM_ConfigStatus;
uint16_T STM_EnableStatus;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : STM_Config
**
**   Description:
**    Performs configuration of STM module
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - peripheral configuration correctly executed
**    PERIPHERAL_ALREADY_CONFIGURED - peripheral was already configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T STM_Config(void)
{
int16_T retCode = NO_ERROR;

    if (STM_ConfigStatus == 0u)
    {
        /* STM Module configuration */
        //STM_0.CR.R = 0x00000003u;
        STM_0.CR.R |= STM_CR_FRZ_EN;              // freeze in debug mode
        //STM_0.CR.R |= STM_CR_TEN_EN;               enable module

        /* STM Channels configuration */
#ifdef STM_CH0_ENABLE
#if (STM_CH0_ENABLE == 1u)
        retCode |= STM_ChannelConfig(STM_CHANNEL0,STM_TIMEOUT0);
#endif //(STM_CH0_ENABLE == 1u)
#endif //STM_CH0_ENABLE

#ifdef STM_CH1_ENABLE
#if (STM_CH1_ENABLE == 1u)
        retCode |= STM_ChannelConfig(STM_CHANNEL1,STM_TIMEOUT1);
#endif //(STM_CH1_ENABLE == 1u)
#endif //STM_CH1_ENABLE

#ifdef STM_CH2_ENABLE
#if (STM_CH2_ENABLE == 1u)
            retCode |= STM_ChannelConfig(STM_CHANNEL2,STM_TIMEOUT2);
#endif //(STM_CH2_ENABLE == 1u)
#endif //STM_CH2_ENABLE

#ifdef STM_CH3_ENABLE
#if (STM_CH3_ENABLE == 1u)
            retCode |= STM_ChannelConfig(STM_CHANNEL3,STM_TIMEOUT3);
#endif //(STM_CH3_ENABLE == 1u)
#endif //STM_CH3_ENABLE

        STM_ConfigStatus = 1u;
    }
    else
    {
        retCode = PERIPHERAL_ALREADY_CONFIGURED;
    }

    return (retCode);
}

/******************************************************************************
**   Function    : STM_Enable
**
**   Description:
**    Performs configuration of STM module
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - peripheral configuration correctly executed
**    PERIPHERAL_NOT_CONFIGURED - peripheral was not configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T STM_Enable(void)
{
int16_T retCode = NO_ERROR;

    if (STM_ConfigStatus == 1u)
    {
        /* STM Module enable */
        STM_0.CR.R |= STM_CR_TEN_EN;              // enable module
        STM_EnableStatus = 1u;
    }
    else
    {
        retCode = PERIPHERAL_NOT_CONFIGURED;
    }
    return (retCode);
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : STM_ChannelConfig
**
**   Description:
**    Performs configuration of STM channels 
**
**   Parameters :
**    Ch - STM channel number
**
**   Returns:
**    NO_ERROR - channel configuration correctly executed
**    ARG_ERROR - channel number not supported
**    PERIPHERAL_NOT_CONFIGURED - peripheral was not configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T STM_ChannelConfig(uint8_T Ch, uint32_T compareValue)
{
int16_T retCode = NO_ERROR;


    if (Ch < STM_NUM_OF_CHANNELS) //avaialble channels 0,1,2,3
    {
        STM_0.Channel[Ch].CMP.R = compareValue;     // set STM channel Ch compare value
        STM_0.Channel[Ch].CIR.R = STM_CIR_CIF_EN;   // clear STM channel Ch interrupt flag
        STM_0.Channel[Ch].CCR.R = STM_CCR_CEN_EN;   // enable STM channel Ch
    }
    else
    {
        retCode = ARG_ERROR;
    }

    return (retCode);
}

#endif //_BUILD_STM_

/****************************************************************************
 ****************************************************************************/

