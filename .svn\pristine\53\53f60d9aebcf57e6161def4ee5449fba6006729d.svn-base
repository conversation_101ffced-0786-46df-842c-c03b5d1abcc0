/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/
/**
 * @file    gtm_common.h
 * @brief   SPC5xx GTM common header file.
 *
 * @addtogroup GTM
 *
 * @{
 */
#ifndef _GTM_COMMON_H_
#define _GTM_COMMON_H_
/**
 * @name    Generic Macro
 * @{
 */
#define CLK_DISABLE    0x0UL
#define CLK_RESET      0x1UL
#define CLK_ENABLE     0x2UL
#define CLK_ENABLED    0x3UL /* ignore write access */


#define ENABLE_FIELD  0x1UL

#if 0 //MC
#if !defined(FALSE) || defined(__DOXYGEN__)
#define FALSE                               0U
#endif

#if !defined(TRUE) || defined(__DOXYGEN__)
#define TRUE                                1U
#endif
#endif
/** @} */

#endif /* _GTM_COMMON_H_ */
/** @} */

