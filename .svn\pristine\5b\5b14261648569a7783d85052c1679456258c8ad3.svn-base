#************************************************************************/
#* FILE NAME: __start_Z4_GHS.s                                            */
#*                                                                        */
#* DESCRIPTION:                                                           */
#* This file contains functions for core 0 -Z4  assembly configuration.   */
#=========================================================================*/
.section .init_c0,"axv" // The "axv" generates symbols for debug

#.align      2
.globl __start_c0
.type __start_c0, @function

.extern main
.extern __SP0_INIT   
.extern __SP0_END    
.extern __STACK0_SIZE
.extern __IRAM0_START
.extern __IRAM0_END
.extern __DRAM0_START
.extern __DRAM0_END
.extern _DRAM0_INIT_START
.extern _DRAM0_INIT_SIZE 
.extern __KEYWORD1
.extern __KEYWORD2
.extern __IV_ADDR_C0
.equ      __SRAMTEST_OFFSET, 4  // number of byte offset to next data address; DO NOT MODIFY

//BUCSR registers definitions
.equ BUCSR_BPEN,              0x00000001
.equ BUCSR_BPRED_MASK,        0x00000006
.equ BUCSR_BPRED_0,           0x00000000
.equ BUCSR_BPRED_1,           0x00000002
.equ BUCSR_BPRED_2,           0x00000004
.equ BUCSR_BPRED_3,           0x00000006
.equ BUCSR_BALLOC_MASK,       0x00000030
.equ BUCSR_BALLOC_0,          0x00000000
.equ BUCSR_BALLOC_1,          0x00000010
.equ BUCSR_BALLOC_2,          0x00000020
.equ BUCSR_BALLOC_3,          0x00000030
.equ BUCSR_BALLOC_BFI,        0x00000200
// MSR register definitions
.equ MSR_UCLE,                0x04000000
.equ MSR_SPE,                 0x02000000
.equ MSR_WE,                  0x00040000
.equ MSR_CE,                  0x00020000
.equ MSR_EE,                  0x00008000
.equ MSR_PR,                  0x00004000
.equ MSR_FP,                  0x00002000
.equ MSR_ME,                  0x00001000
.equ MSR_FE0,                 0x00000800
.equ MSR_DE,                  0x00000200
.equ MSR_FE1,                 0x00000100
.equ MSR_IS,                  0x00000020
.equ MSR_DS,                  0x00000010
.equ MSR_RI,                  0x00000002


// Set MSR value default (DEMO ST)
.equ MSR_DEFAULT,   (MSR_SPE | MSR_WE | MSR_CE | MSR_ME)
// Set MSR ELDOR (from EISB Andorra) 
.equ MSR_ELDOR,   (MSR_DE)


//Set to enable IRAM0 and DRAM0 test for core 0 (z4)
.equ RAM0_TEST, 1

//VLE ISA section
.vle

//**************************************************************************/
// FUNCTION     : __start_c0                                               */
// PURPOSE      : core0(z4) activation                                     */
// INPUT NOTES  :                                                          */
// RETURN NOTES : None                                                     */
// WARNING      : Registers used:                                          */
//**************************************************************************/
__start_c0:
        
    //**************************/
    //* Core0Init              */
    //**************************/
    e_bl cfg_sprZ4

    //**************************/
    //* IVPR0 Init             */
    //**************************/
    e_bl _ivinit0

    //*********************************************************************/ 
    //* Core0 IRAM0 and DRAM0 clearing,                                   */
    //* this device requires a write to all RAM location in               */
    //* order to initialize the ECC detection hardware, this is going to  */  
    //* slow down the startup but there is no way around.                 */
    //*********************************************************************/
    e_bl cfg_z4IRAM      //Write to all IRAM Core 0 locations for ECC functionality
    e_bl cfg_z4DRAM      //Write to all DRAM Core 0 locations for ECC functionality 

.if RAM0_TEST
    //*********************************************************************/
    //* Core0 IRAM0 and DRAM0 testing                                     */
    //* wriring and reading test keyword                                  */
    //*                                                                   */  
    //*********************************************************************/
    e_bl cfg_z4IRAMtest   // Run this function if IRAM0 test is required.
    e_bl cfg_z4DRAMtest   // Run this function if DRAM0 test is required.    
.endif

    //*********************************************************************/
    //* Stack setup                                                       */
    //*********************************************************************/
    e_bl cfg_STACK_c0

    //*********************************************************************/
    //* Small sections registers initialization                           */
    //*********************************************************************/
    e_bl cfg_PNTRS_c0


    //*********************************************************************/
    //* Main program invocation                                           */
    //*********************************************************************/
    e_bl  main

////////////////////////////////////////////////////////////////////////////


//**************************************************************************/
// FUNCTION     : cfg_sprZ4                                                */
// PURPOSE      : This function initializes to 0 the core Z4 SPRs          */
// INPUT NOTES  :                                                          */
// RETURN NOTES : None                                                     */
// WARNING      : Registers used:                                          */
//**************************************************************************/
.align      2
cfg_sprZ4:

xor    r0,  r0,  r0
xor    r1,  r1,  r1
xor    r2,  r2,  r2
xor    r3,  r3,  r3
xor    r4,  r4,  r4
xor    r5,  r5,  r5
xor    r6,  r6,  r6
xor    r7,  r7,  r7
xor    r8,  r8,  r8
xor    r9,  r9,  r9
xor   r10, r10, r10
xor   r11, r11, r11
xor   r12, r12, r12
xor   r13, r13, r13
xor   r14, r14, r14
xor   r15, r15, r15
xor   r16, r16, r16
xor   r17, r17, r17
xor   r18, r18, r18
xor   r19, r19, r19
xor   r20, r20, r20
xor   r21, r21, r21
xor   r22, r22, r22
xor   r23, r23, r23
xor   r24, r24, r24
xor   r25, r25, r25
xor   r26, r26, r26
xor   r27, r27, r27
xor   r28, r28, r28
xor   r29, r29, r29
xor   r30, r30, r30
xor   r31, r31, r31

se_blr 
// End of cfg_sprZ4


//**************************************************************************/
// FUNCTION     : _ivinit0                                                 */
// PURPOSE      : Core 0 exception vectors initialization                  */
// INPUT NOTES  :                                                          */
// RETURN NOTES : None                                                     */
// WARNING      : Registers used:                                          */
//**************************************************************************/
.align      2
_ivinit0:

    //MSR initialization; MC-Port from Demo
    e_lis   r5, MSR_ELDOR@h
    e_or2i  r5, MSR_ELDOR@l
    mtMSR   r5

    //IVPR initialization
    e_lis  r5, __IV_ADDR_C0@h   // IVPR = address base (24 MS bits)
    e_or2i r5, __IV_ADDR_C0@l   // IVPR = address base (24 MS bits)
    mtIVPR r5
   
se_blr

//********************************************************************************/
// FUNCTION     : cfg_STACK                                                      */
// PURPOSE      : This function initializes a 8K Stack region.                   */
//                After the stack and the MPU                                    */
//                entries are established, C Code can be used in the             */
//                 application.                                                  */
// INPUT NOTES  : __STACK0_SIZE, __SP0_INIT, __SP0_END, (defined in linker file) */
// RETURN NOTES : None                                                           */
// WARNING      : Registers used: R1(to set stack ptr),R5                        */
//********************************************************************************/
cfg_STACK_c0:

// Load size of Cache to be used for Stack (8K)
    e_lis   r5, __STACK0_SIZE@h       // Stack size is 8KBytes
    e_or2i   r5,  __STACK0_SIZE@l
// Each stack lock instruction covers 32 bytes, so divide the input parameter
    e_srwi  r5, r5, 5                // Shift the contents of R5 right by 5 bits (size/32)
    mtctr r5                       // locations per cache line loaded to the CTR (SPR 9) register
    
// Point R5 to just past the DRAM0. Set in the Linker file.   
    e_lis   r5, __SP0_END@h    
    e_or2i   r5, __SP0_END@l
                  
// Set the stack pointer
    e_lis   r1, (__SP0_INIT-0x10)@h
    e_or2i   r1, (__SP0_INIT-0x10)@l

    se_blr
// End of cfg_STACK_c0

//*****************************************************************************/
// FUNCTION     : cfg_PNTRS                                                   */
// PURPOSE      : This function initializes register pointers for small data  */
//                 (.sbss) in R13 and small data2 (.sdata2) in R2.            */
//                                                                            */
// INPUT NOTES  : _SDA_BASE_, _SDA2_BASE_ (defined by the linker EABI)        */
// RETURN NOTES : None                                                        */
// WARNING      : Registers used: R13(to set .sdata pointer ),                */
//                                R2 (to set .sdata2 pointer)                 */
//                 The BASE addresses are offset by 0x8000 for CW, GHS, P&E   */
//                 and offset by 0x7FF0 for Diab to simplify access to small  */
//                 data.                                                      */
//*****************************************************************************/
cfg_PNTRS_c0:

// Set the small data (.sbss) pointer
    e_lis   r13, (_SDA_BASE_)@h
    e_or2i   r13, (_SDA_BASE_)@l

// Set the small data2 (.sdata2) pointer
    e_lis   r2, (_SDA2_BASE_)@h
    e_or2i   r2, (_SDA2_BASE_)@l

    se_blr
// End of cfg_PNTRS


//*************************************************************************/
// FUNCTION     : cfg_z4IRAM                                              */
// PURPOSE      : This function initializes the core 0 IRAM               */
//                by writing 64 bitvalues to every SRAM location.         */
//                This will set the  initial ECC (Error Correction Code). */ 
// INPUT NOTES  :                                                         */
// RETURN NOTES : None                                                    */
// WARNING      : Registers used: R5                                      */
//*************************************************************************/
cfg_z4IRAM:

// base address of the internal z4 IRAM
    e_lis   r4,__IRAM0_START@h
    e_or2i  r4, __IRAM0_START@l
// end address of the internal z4 IRAM
    e_lis   r5,__IRAM0_END@h
    e_or2i  r5,__IRAM0_END@l

    iram2_loop:
        cmpl cr0, r4, r5
        se_bge      .iram2clrend

        e_stmw  r16,0(r4)       // write all SPR registers from r16 to r31 to L2RAM
        e_add16i  r4,r4,64      // increment the ram ptr
        e_b  iram2_loop         // loop for 64k of L2RAM

    .iram2clrend:
    se_blr     
// End of cfg_z4IRAM

//*************************************************************************/
// FUNCTION     : cfg_z4DRAM                                              */
// PURPOSE      : This function initializes the core 0 DRAM               */
//                by writing 64 bitvalues to every SRAM location.        */
//                This will set the  initial ECC (Error Correction Code). */ 
// INPUT NOTES  : INT_SRAM_BASE, INT_SRAM_64BYTSEGS (INT_SRAM_SIZE >> 6) */
// RETURN NOTES : None                                                    */
// WARNING      : Registers used: R5                                      */
//*************************************************************************/
cfg_z4DRAM:
    // base address of the internal z4 DRAM
        e_lis   r4,__SP0_END@h   // __SP0_END = first DRAM address
        e_or2i  r4, __SP0_END@l
    // end address of the internal z4 DRAM
        e_lis   r5,__DRAM0_END@h
        e_or2i  r5,__DRAM0_END@l
    
        dram2_loop:
            cmpl cr0, r4, r5
            se_bge      .dram2clrend
    
            e_stmw  r16,0(r4)     // write all SPR registers from r16 to r31 to L2RAM
            e_add16i  r4,r4,64    // increment the ram ptr
            e_b  dram2_loop       // loop for 64k of L2RAM
    
        .dram2clrend:
        se_blr  

// End of cfg_z4DRAM

.if RAM0_TEST 
//*****************************************************************************/
// FUNCTION     : cfg_z4IRAMtest                                               */
// PURPOSE      : This function                                               */
//                                                                            */
//                                                                            */
// INPUT NOTES  : This function performs IRAM4 test                           */
//                                                                            */
//                __KEYWORD1        --  defined below                         */
//                __KEYWORD2        --  defined below                         */
//                __SRAM_SIZE       --  defined below                         */
//                __SRAM_START_ADDR --  defined below                         */
//                __SRAMTEST_OFFSET -- (=4) defined below                     */
//                                                                            */
// RETURN NOTES : None                                                        */
// WARNING      : Registers used: R12 -- set SRAM pointer                     */
//                                R11 -- set SRAM pointer                     */
//                                R10 -- set SRAM pointer                     */
//                                R9  -- hold SRAM size                       */ 
//                                R7  -- loop counter working register        */
//                                R6  -- hold keywords loop counter threshold */
//                                R5  -- hold actual keyword to test          */
//                                R4  --    hold the SRAM copy data           */                          
//                                R3  -- 2nd loop counter working register    */
//*****************************************************************************/
cfg_z4IRAMtest:
    // CHECK  __SRAM_SIZE1

        mfLR  r7
    // Set GPR9 to the count of the RAM load size
        
        e_lis    r9, __IRAM0_SIZE@ha          // Load upper SRAM load size (// of bytes) into R9
        e_add2i. r9, __IRAM0_SIZE@l           // Load lower SRAM load size into R9
                                              //  The "." sets the condition flag
        e_beq _func_exit                      // Exit cfg_SRAMtest if size is zero

        
        e_lis   r5, __KEYWORD1@ha             // Load upper address of first Keyword into R5
        e_add16i  r5,r5, __KEYWORD1@l         // Load lower address of first Keyword into R5

        e_lis   r10, __IRAM0_START@ha         // Load address of first SRAM load into R10
        e_add16i  r10,r10, __IRAM0_START@l    // Load lower address of SRAM load into R10
        e_add16i  r10,r10, -__SRAMTEST_OFFSET // Decrement address to prepare for _writeKeyLoop    

        mr r11,r10
        
        e_bl SramCheckFunction
        
        e_lis   r5, __KEYWORD2@ha             // Load upper address of first Keyword into R5
        e_add16i  r5,r5, __KEYWORD2@l         // Load lower address of first Keyword into R5

        e_lis   r10, __IRAM0_START@ha         // Load address of first SRAM load into R10
        e_add16i  r10,r10, __IRAM0_START@l    // Load lower address of SRAM load into R10
        e_add16i  r10,r10, -__SRAMTEST_OFFSET // Decrement address to prepare for _writeKeyLoop    

        mr r11,r10

        e_bl SramCheckFunction

        e_lis   r12, __IRAM0_START@ha         // Load address of first SRAM load into R10
        e_add16i  r12,r12, __IRAM0_START@l    // Load lower address of SRAM load into R10
        e_add16i  r12,r12, -__SRAMTEST_OFFSET // Decrement address to prepare for _writeKeyLoop    

        e_bl SramClearMemory

    _func_exit:
        mtLR  r7
        se_blr

// End of cfg_z4IRAMtest


//*****************************************************************************/
// FUNCTION     : cfg_z4DRAMtest                                              */
// PURPOSE      : This function                                               */
//                                                                            */
//                                                                            */
// INPUT NOTES  : This function performs IRAM0 test                           */
//                                                                            */
//                __KEYWORD1        --  defined below                         */
//                __KEYWORD2        --  defined below                         */
//                __SRAM_SIZE       --  defined below                         */
//                __SRAM_START_ADDR --  defined below                         */
//                __SRAMTEST_OFFSET -- (=4) defined below                     */
//                                                                            */
// RETURN NOTES : None                                                        */
// WARNING      : Registers used: R12 -- set SRAM pointer                     */
//                                R11 -- set SRAM pointer                     */
//                                R10 -- set SRAM pointer                     */
//                                R9  -- hold SRAM size                       */ 
//                                R7  -- loop counter working register        */
//                                R6  -- hold keywords loop counter threshold */
//                                R5  -- hold actual keyword to test          */
//                                R4  --    hold the SRAM copy data           */                          
//                                R3  -- 2nd loop counter working register    */
//*****************************************************************************/
cfg_z4DRAMtest:

    mfLR  r7
// Set GPR9 to the count of the RAM load size
    
    e_lis    r9, _DRAM0_INIT_SIZE@ha       // Load upper SRAM load size (// of bytes) into R9
    e_add2i. r9, _DRAM0_INIT_SIZE@l        // Load lower SRAM load size into R9
                                           //  The "." sets the condition flag
    e_beq _func_exit                       // Exit cfg_SRAMtest if size is zero

    
    e_lis   r5, __KEYWORD1@ha              // Load upper address of first Keyword into R5
    e_add16i  r5,r5, __KEYWORD1@l          // Load lower address of first Keyword into R5

    e_lis   r10, _DRAM0_INIT_START@ha      // Load address of first SRAM load into R10
    e_add16i  r10,r10, _DRAM0_INIT_START@l // Load lower address of SRAM load into R10
    e_add16i  r10, r10, -__SRAMTEST_OFFSET // Decrement address to prepare for _writeKeyLoop    

    mr r11,r10
    
    e_bl SramCheckFunction
    
    e_lis   r5, __KEYWORD2@ha              // Load upper address of first Keyword into R5
    e_add16i  r5,r5, __KEYWORD2@l          // Load lower address of first Keyword into R5

    e_lis   r10, _DRAM0_INIT_START@ha      // Load address of first SRAM load into R10
    e_add16i  r10,r10, _DRAM0_INIT_START@l // Load lower address of SRAM load into R10
    e_add16i  r10,r10, -__SRAMTEST_OFFSET  // Decrement address to prepare for _writeKeyLoop    

    mr r11,r10

    e_bl SramCheckFunction

    e_lis   r12, _DRAM0_INIT_START@ha      // Load address of first SRAM load into R10
    e_add16i  r12,r12, _DRAM0_INIT_START@l // Load lower address of SRAM load into R10
    e_add16i  r12,r12, -__SRAMTEST_OFFSET  // Decrement address to prepare for _writeKeyLoop    

    e_bl SramClearMemory

_func_exit_1:
    mtLR  r7
    se_blr


// End of cfg_SRAMtest

//**********************************************************************
//                  CHECK FUNCTION:SramCheckFunction
//
//
//**********************************************************************
SramCheckFunction:

    mtctr  r9                            // Store number of bytes to be moved in spr CTR 

_writeKeyLoop:
    e_stwu   r5, __SRAMTEST_OFFSET(r10)  // Store R5 data word into SRAM at R10 and update SRAM address 
    e_bdnz   _writeKeyLoop               // Branch if more bytes to load from SRAM

    mr r10,r11
    mtctr   r9                           // Store number of bytes to be moved in spr CTR


_verifyKeyLoop:
    e_lwzu   r4, __SRAMTEST_OFFSET(r10)  // Load data word at R4 into R10,incrementing (update) SRAM address
    cmplw  r5,r4                         // Check if stored word is equal to test keyword
    e_bne    _error                      // if not jump to SRAM test failure section
    e_bdnz   _verifyKeyLoop              // Branch if more bytes to load from SRAM
    e_b _endloop

_error:
    nop
    //e_bl SYS_SRAM_Test_FailureRoutine  MC, da valutare in BT_MV
    e_bl _error  //MC, loop
_endloop:    
    se_blr


// End of SramCheckFunction


//**********************************************************************
//                  CHECK FUNCTION:SramClearMemory
//
//
//**********************************************************************

SramClearMemory:
        
    e_lis   r5, __CLEAR_KEYWORD@ha       // Load upper address of Clear Keyword into R5
    e_add16i  r5,r5, __CLEAR_KEYWORD@l   // Load lower address of Clear Keyword into R5
    
    mtctr r9                             // Store of bytes to be moved in spr CTR

_ClearMemoryLoop:
    e_stwu   r5, __SRAMTEST_OFFSET(r12)  // Store R5 data word into SRAM at R12 and update SRAM address 
    e_bdnz   _ClearMemoryLoop            // Branch if more bytes to load from ROM
//end _ClearMemoryLoop

se_blr


// End of SramClearMemory
.endif

