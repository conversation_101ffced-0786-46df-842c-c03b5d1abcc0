/* Linker Directive file for ECU target, with SPC574K */
MEMORY
{
  /* Code Flash - 2.5 MB Internal Flash, organized in several memory regions. */
  flash_rcw          :  org = 0x00FC0000, len = 0x1C  
  miniboot_flash     :  org = 0x00FC001C, len = 0x00007FE4  /*   32 KB */  
  boot_flash         :  org = 0x00FC8000, len = 0x00028000  /*  160 KB */  
  calibration_flash  :  org = 0x00FF0000, len = 0x00010000  /*   64 KB */  
  application_flash  :  org = 0x01000000, len = 0x00200000  /* 2048 KB */  
  boot_backup        :  org = 0x01200000, len = 0x00040000  /*  256 KB */

  /* Data flash - 64 KB Internal Flash*/
  EEPROM_flash       :  org = 0x00800000, len = 0x00010000  /* 64 KB */ 

  /* Internal SRAM - 64KB for general purpose */
  int_sram           :   org = 0x40000000,  len = 0x10000   /* 64 KB of general purpose RAM  */
  
  /* Core 0 - z4   */  
  /* Instruction memory - 16KB  */
  iram0              : org = 0x50000000, len = 0x4000  /* 16k */
  /* Data memory - 64KB */
  stack_core0        : org = 0x50800000, len = 0x2000  /*  8k */
  dram0              : org = 0x50802000, len = 0xE000 /* 64k */
 
  /* Core 2 - z2   */  
  /* Instruction memory -16KB */
  iram2              : org = 0x52000000, len = 0x4000 /* 16k */
  /* Data memory - 48K */  
  stack_core2        : org = 0x52800000, len = 0x2000 /*  8k */   
  dram2              : org = 0x52802000, len = 0xA000 /* 40k */
}


SECTIONS
{
__FLASH_START = ADDR(miniboot_flash);
__FLASH_END = ADDR(application_flash) + SIZEOF(application_flash);

__MINIBOOT_START = ADDR(miniboot_flash);
__MINIBOOT_SIZE = SIZEOF(miniboot_flash);
__BOOT_START = ADDR(boot_flash);
__BOOT_SIZE = SIZEOF(boot_flash);
__EEPROM_START = ADDR(EEPROM_flash);
__EEPROM_SIZE = SIZEOF(EEPROM_flash);
__EEPROM_END = __EEPROM_START + __EEPROM_SIZE;
__CALIB_ROM_START = ADDR(calibration_flash);
__CALIB_ROM_SIZE = SIZEOF(calibration_flash);
__APP_START = ADDR(application_flash);
__APP_SIZE = SIZEOF(application_flash);
__BACKUP_START = ADDR(boot_backup);
__BACKUP_SIZE = SIZEOF(boot_backup);

//__CALIB_RAM_START = ADDR(calib_ram); /* This value is computed by sections linking */
//__CALIB_RAM_SIZE = SIZEOF(calib_ram);  /* This value is static and it is the max */
//__CALIB_RAM_END = __CALIB_RAM_START + __CALIB_RAM_SIZE; /* This value is computed by sections linking */

//__RAM_START = ADDR(stack_ram);
__RAM_START = ADDR(int_sram);

/* __RAM_END = fine della RAM a disposizione */
//__RAM_END =ADDR(stack_ram) + SIZEOF(stack_ram) + SIZEOF(int_sram); // + SIZEOF(calib_ram);
__RAM_END =ADDR(int_sram) + SIZEOF(int_sram); // + SIZEOF(calib_ram);

//IRAM Core 0 - z4
__IRAM0_START = ADDR(iram0);
__IRAM0_END   = ADDR(iram0) + SIZEOF(iram0);
__IRAM0_SIZE  = SIZEOF(iram0)/4;
//DRAM Core 0 - z4
__DRAM0_START = ADDR(dram0);
__DRAM0_END   = ADDR(dram0) + SIZEOF(dram0);
__DRAM0_SIZE  = sizeof(dram0)/4;
//IRAM Core 2 - z2
__IRAM2_START = ADDR(iram2);
__IRAM2_END   = ADDR(iram2) + SIZEOF(iram2);
__IRAM2_SIZE  = SIZEOF(iram2)/4;
//DRAM Core 2 - z2
__DRAM2_START = ADDR(dram2);
__DRAM2_END   = ADDR(dram2) + SIZEOF(dram2);
__DRAM2_SIZE  = sizeof(dram2)/4;

__GLOBAL_START = 0x00FF0000;  /*calib start */
__GLOBAL_END = ADDR(application_flash) + SIZEOF(application_flash) ; /*app end*/
__HEAP_SIZE = 0x10;

__VSRAM_SIZE   = 0x200;

//__TOTAL_AVAILABLE_RAM = SIZEOF(stack_ram) + SIZEOF(int_sram); /* dovrebbe essere calcolata: __RAM_END - __RAM_START */ 
__TOTAL_AVAILABLE_RAM = SIZEOF(int_sram) ;

/* ROM data */
    .rcw  : { *(.rcw) } > flash_rcw

/* miniboot section */
    /* NOTHING */

/* BOOT section */
   /* NOTHING */

/* calibration_flash section */

    .calib_check LOAD(.) : {
                            calib_checkVersion.o (.rodata)
                           } > calibration_flash 
    //__LOCAL_CALIB_RAM_START = ADDR(calibration_flash);

    .calib  LOAD(.) : {} >.

    .= ((. + 63) & ~63); /* align memory region end */
    __CALIB_ROM_END = . ; 
 

    //.= ((. + 7) & ~7); /* align memory region end */

    //.ROM.calib            ROM(.calib):>.
    
    //.= ((. + 63) & ~63); /* align memory region end */
    //__CALIB_ROM_END = . ; 

    /* Region Tagging */
    . = __CALIB_ROM_START+__CALIB_ROM_SIZE - SIZEOF(.calib_tag);
    .calib_tag LOAD(.) : {calib_tag.o (.rodata)} >.
    __CCP_CALIB_ROM_END = . ;   
    
    __CALIB_TAG_START = ADDR(.calib_tag);
    __CALIB_TAG_SIZE = SIZEOF(.calib_tag);
    
/* ROM data */    
    .get_app_startup   LOAD(.):  {
                            get_app_startup.o (.rodata)
                                 } > application_flash

    .app_flash LOAD(.) : {
    recovery.o (.vletext)
    recovery.o (.rodata)
    } > .

    .app_lib ALIGN(0x08): {
                            libstartup.a (ind_mcpy.o) (.vletext)
                            libstartup.a(ind_mset.o) (.vletext)
                               } > .
    .init LOAD(.) ALIGN(0x08): {
                            *(.init)
                               } > .
    .init_c0 LOAD(.) ALIGN(0x08): {
                            *(.init)
                               } > .
    .flash_data : {} > .
    //.xcptn  LOAD(.) ALIGN(0x40)   : {}  > .
    .xcptn_c0  LOAD(.) ALIGN(0x100)   : {}  > .
    .xcptn_c2  LOAD(.) ALIGN(0x100)   : {}  > .

    .vletext     LOAD(.) ALIGN(0x08): {}  > .
 	
    .rodata   LOAD(.) ALIGN(0x08): {
         *(.rdata) 
         *(.rodata) 
         } > .  
    .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
    __DATA_ROM =.;         /* Start of .data in ROM */   
//         . = . + SIZEOF(.data); /* make space for .data */
     .ROM.data		ROM(.data): > .

     /*******************************************************/
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID0_DATA_START = .;
         .ROM.ee_id0_data		ROM(.ee_id0_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
	 
         __EE_ID0_DATA_END = .;

/*******************************************************/
		 .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
		 __EE_ID1_DATA_START = .;
		 .ROM.ee_id1_data		ROM(.ee_id1_data):>.
		 .= ((. + 7) & ~7); /* align 8 bytes */
		 __EE_ID1_DATA_END = .;

		 .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
		 __EE_ID2_DATA_START = .;
		 .ROM.ee_id2_data		ROM(.ee_id2_data):>.
		 .= ((. + 7) & ~7); /* align 8 bytes */
		 __EE_ID2_DATA_END = .;

/*******************************************************/

         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID3_DATA_START = .;
         .ROM.ee_id3_data		ROM(.ee_id3_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID3_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID4_DATA_START = .;
         .ROM.ee_id4_data		ROM(.ee_id4_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID4_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID5_DATA_START = .;
         .ROM.ee_id5_data		ROM(.ee_id5_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID5_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID6_DATA_START = .;
         .ROM.ee_id6_data		ROM(.ee_id6_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID6_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID7_DATA_START = .;
         .ROM.ee_id7_data		ROM(.ee_id7_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID7_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID8_DATA_START = .;
         .ROM.ee_id8_data		ROM(.ee_id8_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID8_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID9_DATA_START = .;
         .ROM.ee_id9_data		ROM(.ee_id9_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID9_DATA_END = .;
     
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID10_DATA_START = .;
         .ROM.ee_id10_data	   ROM(.ee_id10_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID10_DATA_END = .;
      
         .= ((. + 7) & ~7); /* align __DATA_ROM to .data start */
         __EE_ID11_DATA_START = .;
         .ROM.ee_id11_data	   ROM(.ee_id11_data):>.
         .= ((. + 7) & ~7); /* align 8 bytes */
         __EE_ID11_DATA_END = .;

/*******************************************************/

      __SDATA_ROM = .;        /* Start of .sdata in ROM */
//         . = . + SIZEOF(.sdata); /* make space for .sdata */
     .ROM.sdata		ROM(.sdata): > .

     __DATA_ROM_END = .;  /* end of __DATA_ROM */
     
     _VLETEXTIMM_ROM = .;
      .ROM.vletext_RAM  ROM(.vletext_RAM): > .
     _VLETEXTIMM_ROM_END = .;     

     _PATTERNSRAMIMM_ROM = .;
      .ROM.pattern_sram  ROM(.pattern_sram): > .
     _PATTERNSRAMIMM_ROM_END = .; 	 
     
    .= ((. + 7) & ~7); /* align */
    __DATA_C0_ROM =.;   /* Start of .data_C0 in ROM */   
     .ROM.dataC0	ROM(.data_c0): > . 
    __DATA_C0_ROM_END = .;  /* end of __DATA_C0_ROM */     
     
    .= ((. + 7) & ~7); /* align */
    __DATA_C2_ROM =.;         /* Start of .data in ROM */   
     .ROM.dataC2	ROM(.data_c2): > . 
    __DATA_C2_ROM_END = .;  /* end of __DATA_C2_ROM */      
  
    .= ((. + 7) & ~7); /* align */
    __INST_C0_ROM =.;   /* Start of .vletext_c0 in ROM */   
     .ROM.vletext_c0    ROM(.vletext_c0): > . 
    __INST_C0_ROM_END = .;  /* end of __INST_C0_ROM */  
 
    .= ((. + 7) & ~7); /* align */
    __INST_C2_ROM =.;   /* Start of .vletext_c2 in ROM */   
     .ROM.vletext_c2    ROM(.vletext_c2): > . 
    __INST_C2_ROM_END = .;  /* end of __INST_C2_ROM */  
 
    .ctors LOAD(.): {} > .
    .dtors LOAD(.): {} > .

    extab ALIGN(0x10): {} > .
   .isrvectbl_c0 LOAD(.)  ALIGN(0x1000) : {} > .
   .isrvectbl_c2 LOAD(.)  ALIGN(0x1000) : {} > .
   .isrpritbl LOAD(.)  ALIGN(0x1000) : {} > .
    extabindex LOAD(.) : {} > .

    .= ((. + 63) & ~63); /* align memory region end */
    __APP_CODE_END = . ;
    
    . = __APP_START+__APP_SIZE - SIZEOF(.app_tag) - SIZEOF(.pattern_rww1);
    __PATTERN_FLASH_RWW1_START = .;
    .pattern_rww1   LOAD(.) ALIGN(0x08): {
         *(.pattern_rww1) 
     } > . 
     __PATTERN_FLASH_RWW1_END = .;
     __PATTERN_FLASH_RWW1_SIZE = __PATTERN_FLASH_RWW1_END - __PATTERN_FLASH_RWW1_START;


    /* Region Tagging */
    . = __APP_START+__APP_SIZE - SIZEOF(.app_tag);
    __APP_TAG_START = .;
    __APP_TAG_SIZE  = SIZEOF(.app_tag);
    .app_tag LOAD(.) : {app_tag.o (.rodata)} > .
    __APP_END = .;
    __PATTERN_RWW1_PLUS_TAG_SIZE =__PATTERN_FLASH_RWW1_SIZE + SIZEOF(.app_tag);

/* SRAM data */

    __VSRAM_START = ADDR(int_sram);
    .vsram :{
                      vsram_checksum.o (.bss)
                      vsram_shared_content.o  (.bss)
                      vsram_content.o  (.bss)
            } > int_sram
    __VSRAM_END = __VSRAM_START + __VSRAM_SIZE;
    .= __VSRAM_END;
  
    __PATTERN_SRAM_START = .;
	.pattern_sram: {} > .
    __PATTERN_SRAM_END = .;
	__PATTERN_SRAM_SIZE = __PATTERN_SRAM_END - __PATTERN_SRAM_START;
    
    .= ((. + 7) & ~7); /* align 8 bytes */
    .sdata  /*LOAD(__SDATA_ROM)*/ ALIGN(0x08): {} >.
    .= ((. + 7) & ~7); /* align 8 bytes */
    .sbss   : {} >.
    .= ((. + 7) & ~7); /* align 8 bytes */
    .sdata2 : {} >.
    .= ((. + 7) & ~7); /* align 8 bytes */
    .sbss2  : {} >.
   .= ((. + 7) & ~7); /* align 8 bytes */
    .heap   : {} >.
   .= ((. + 7) & ~7); /* align 8 bytes */
				

                       
    .= ((. + 31) & ~31); /* align memory region end */
    .data   /*LOAD(__DATA_ROM)*/ ALIGN(0x08): {} >.

    .= ((. + 7) & ~7); /* align 8 bytes */

    __EE_ID0_START = .;
    .ee_id0_data  :{
                              ee_ID0.o (.ee_id0_data)
                   } >.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID0_END = .;

    __EE_ID1_START = .;
    .ee_id1_data :{
                              ee_ID1.o (.ee_id1_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID1_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID2_START = .;
    .ee_id2_data  :{
                              ee_ID2.o (.ee_id2_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID2_END = .;
    
    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID3_START = .;
    .ee_id3_data :{
                              ee_ID3.o (.ee_id3_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID3_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID4_START = .;
    .ee_id4_data :{
                              ee_ID4.o (.ee_id4_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID4_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID5_START = .;
    .ee_id5_data :{
                              ee_ID5.o (.ee_id5_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID5_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID6_START = .;
    .ee_id6_data :{
                              ee_ID6.o (.ee_id6_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID6_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID7_START = .;
    .ee_id7_data :{
                              ee_ID7.o (.ee_id7_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID7_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID8_START = .;
    .ee_id8_data :{
                              ee_ID8.o (.ee_id8_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID8_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID9_START = .;
    .ee_id9_data :{
                              ee_ID9.o (.ee_id9_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID9_END = .;

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID10_START = .;
    .ee_id10_data :{
                              ee_ID10.o (.ee_id10_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID10_END = .;


    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID11_START = .;
    .ee_id11_data :{
                              ee_ID11.o (.ee_id11_data)
                   }>.

    .= ((. + 7) & ~7); /* align 8 bytes */
    __EE_ID11_END = .;

    .bss    : {} > .
    .= ((. + 31) & ~31); /* align 8 bytes */
    
    .vletext_RAM   : {} > .
    .= ((. + 31) & ~31); /* align 8 bytes */
    
__END_OF_RAM = .;

    /*** IRAM0 - z4 ***/  
    .vletext_c0     LOAD(.) ALIGN(0x08): {} > iram0

    /*** DRAM0 - z4 ***/  
    .stack_c0  : {} > stack_core0 
    /* Data */    
    .= ((. + 31) & ~31); /* align memory region end */
    .data_c0   /*LOAD(__DATA_C0_ROM)*/ ALIGN(0x08): {
                                                     DMEM0_CheckSM_MCU_pattern.o (.rodata)
                                                    } > dram0
     .= ((. + 7) & ~7); /* align 8 bytes */   
    .bss_c0    : {} >.    
    
    /*** IRAM2 - z2 ***/  
     .vletext_c2 LOAD(.): {
                      IMEM2_CheckSM_MCU_pattern.o (.rodata)
                     } > iram2

    /*** DRAM2 - z2 ***/      
    .stack_c2  : {} > stack_core2
    .data_c2   /*LOAD(__DATA_C2_ROM)*/ ALIGN(0x08): {} > dram2
    .= ((. + 7) & ~7); /* align 8 bytes */
    .bss_c2    : {} >. 

/* Stack Address Parameters*/
//__SP_INIT      = ADDR(stack_ram) + SIZEOF(stack_ram); 
//__SP_END       = ADDR(stack_ram); 
//__STACK_SIZE   = SIZEOF(stack_ram);  

__SP0_INIT      = ADDR(stack_core0) + SIZEOF(stack_core0); 
__SP0_END       = ADDR(stack_core0); 
__STACK0_SIZE   = SIZEOF(stack_core0);

__SP2_INIT      = ADDR(stack_core2) + SIZEOF(stack_core2); 
__SP2_END       = ADDR(stack_core2); 
__STACK2_SIZE   = SIZEOF(stack_core2);

/* SRAM Address Parameters */

__SRAM_CPY_START = ADDR(.data);
__ROM_COPY_SIZE  = (SIZEOF(.data) + SIZEOF(.sdata));
__SRAM_LOAD      = ADDR(.heap);
__SRAM_LOAD_SIZE = (SIZEOF(.flash_data)/4);

/* VLETEXT_RAM Text Initializzation parameter */
__VLETEXTRAM_TEXT_CPY_START = ADDR(.vletext_RAM);
__VLETEXTRAM_COPY_SIZE  = SIZEOF(.vletext_RAM);

/* DRAM2 Data Initializzation parameter */
__DRAM2_DATA_CPY_START = ADDR(.data_c2);
__DATAC2_COPY_SIZE  = SIZEOF(.data_c2);

/* DRAM0 Data Initializzation parameter */
__DRAM0_DATA_CPY_START = ADDR(.data_c0);
__DATAC0_COPY_SIZE  = SIZEOF(.data_c0);

/* IRAM0 Text Initializzation parameter */
__IRAM0_TEXT_CPY_START = ADDR(.vletext_c0);
__TEXTC0_COPY_SIZE  = SIZEOF(.vletext_c0);

/* IRAM2 Text Initializzation parameter */
__IRAM2_TEXT_CPY_START = ADDR(.vletext_c2);
__TEXTC2_COPY_SIZE  = SIZEOF(.vletext_c2);

/* The EABI defines the location of _SDA_BASE_ and _SDA2_BASE_  */
/*  cfg_PNTRS places _SDA_BASE_ into R13 and _SDA2_BASE into R2 */
/*_SDA_BASE_ = ADDR(.sdata) + 0x8000 (+0x7FF0 for WindRiver)    */
/*_SDA2_BASE_ = ADDR(.sdata2) + 0x8000 (+0x7FF0 for WindRiver)  */

/* Interrupt Handler Parameters */
//__IV_ADDR      = ADDR(.xcptn);

/* Interrupt Handler Parameters */
__IV_ADDR_C0      = ADDR(.xcptn_c0);

/* Interrupt Handler Parameters */
__IV_ADDR_C2      = ADDR(.xcptn_c2);


/******* VSRAM Address Parameters *******/
  __KEYWORD1     = 0x55555555;
  __KEYWORD2     = 0xAAAAAAAA;
  __TEST_KEYWORD = 0x5A5A5A5A;
  __CLEAR_KEYWORD= 0x00000000;
  __VSRAM_SRAM_START =  __VSRAM_START;
  __VSRAM_SRAM_SIZE  =  (__VSRAM_SIZE/4);
  __VSRAM_SRAM_END   =  __VSRAM_END;
  __VSRAM_START_ADDR =  __VSRAM_SRAM_START ;
  
  __SRAM_START_ADDR   = ADDR(int_sram);
  __SRAM_SIZE         = (SIZEOF(int_sram)/4);
  __SRAM_START_ADDR_1 = (__SRAM_START_ADDR + __VSRAM_SIZE);
  __SRAM_SIZE_1       = (__SRAM_SIZE - __VSRAM_SRAM_SIZE);
  __SRAM_END_ADDR     = (__SRAM_START_ADDR + SIZEOF(int_sram));

  //DRAM0 Label for init
  _DRAM0_INIT_START = ADDR(stack_core0);
  _DRAM0_INIT_SIZE  = __DRAM0_SIZE + (__STACK0_SIZE/4); 

  //DRAM2 Label for init
  _DRAM2_INIT_START = ADDR(stack_core2);
  _DRAM2_INIT_SIZE  = __DRAM2_SIZE + (__STACK2_SIZE/4);  


/***************************************/ 

/***************************************/ 
/*         A2L Modified labels         */

  __2APP_START = __APP_START;
  __2APP_END = __APP_END;
  __2APP_SIZE = __2APP_END - __2APP_START;
  __3CALIB_ROM_START = __CALIB_ROM_START;
  __3CALIB_ROM_END = __CCP_CALIB_ROM_END;   
  __3CALIB_ROM_SIZE = __3CALIB_ROM_END - __3CALIB_ROM_START;
  __CALIB_ROM_OFFLINE_START = __CALIB_ROM_START;
  __CALIB_ROM_OFFLINE_END = __CALIB_ROM_END;

  __CALIB_DATA_START =  __CALIB_ROM_START;
  __CALIB_DATA_SIZE = __CALIB_ROM_END - __CALIB_ROM_START;
  __APP_CODE_SIZE = (__APP_CODE_END - __APP_START);
  
  __APP_CODE_INCA_SIZE =  (__APP_TAG_START - __CALIB_ROM_END);
  __CALIB_CODE_INCA_SIZE =  (__CALIB_ROM_END - __CALIB_ROM_START);
/***************************************/ 

/***************************************/ 
/*         KWP labels                  */
  
  __KWP_START_BOOT_ADD = ADDR(boot_flash);
  __KWP_CODE_BOOT_SIZE = SIZEOF(boot_flash);
  __KWP_START_APPL_ADD = ADDR(calibration_flash);
  __KWP_START_CALIB_ADD = ADDR(calibration_flash);
  __KWP_CODE_CALIB_SIZE = SIZEOF(calibration_flash);
  __KWP_CODE_APPL_SIZE = (__APP_SIZE + __KWP_CODE_CALIB_SIZE);

  __UDS_START_BOOT_ADD = __KWP_START_BOOT_ADD;
  __UDS_CODE_BOOT_SIZE = __KWP_CODE_BOOT_SIZE;
  __UDS_START_APPL_ADD = __KWP_START_APPL_ADD;
  __UDS_START_CALIB_ADD =__KWP_START_CALIB_ADD;
  __UDS_CODE_APPL_SIZE  = __KWP_CODE_APPL_SIZE;
  __UDS_CODE_CALIB_SIZE = __KWP_CODE_CALIB_SIZE;



/***************************************/ 


/* These special symbols mark the bounds of RAM and ROM memory. */
/* They are used by the MULTI debugger.                         */

//    __ghs_ramstart  = MEMADDR(stack_ram);
//    __ghs_ramend    = MEMENDADDR(int_sram);
//    __ghs_romstart  = MEMADDR(miniboot_flash);
//    __ghs_romend    = MEMENDADDR(application_flash);

//    __ghs_rambootcodestart = 0;           /* zero for ROM image */
//    __ghs_rambootcodeend = 0;             /* zero for ROM image */
//    __ghs_rombootcodestart = MEMADDR(miniboot_flash);
//    __ghs_rombootcodeend = MEMENDADDR(miniboot_flash);

/* Metrowerks Code Warrior compiler address designations 
_stack_addr = ADDR(stack_ram)+SIZEOF(stack_ram);
_stack_end = ADDR(stack_ram);
_heap_addr = ADDR(.bss)+SIZEOF(.bss);
_heap_end = ADDR(int_sram)+SIZEOF(int_sram);
_init_addr = ADDR(.init);
_init_end = ADDR(.init)+SIZEOF(.init);
*/

}
