/*******************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_EZ_04_SMECC/tree/APPLICATION#$ */
/* $Revision: 164860 $                                                                                             */
/* $Date: 2021-06-11 17:17:26 +0200 (ven, 11 giu 2021) $                                                         */
/* $Author: SantoroR $                                                                              */
/*******************************************************************************************************************/
#ifdef  I_MEM_2_CHECK_ENABLE

#include "rtwtypes.h"
#include "RamCheckSM_MCU_4_xx.h"

/*IMEM2 patterns for safety checks*/
const uint32_T Pattern1_imem2 = RAM_PATTERN_1;
const uint32_T Pattern2_imem2 = RAM_PATTERN_2;
const uint32_T Pattern3_imem2 = RAM_PATTERN_3;
const uint32_T Pattern4_imem2 = RAM_PATTERN_4;

#endif  /* I_MEM_2_CHECK_ENABLE */

