/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#include "rtwtypes.h"

#pragma ghs section data=".ee_id0_data"

#ifdef _TEST_EEPROM_
uint32_T Prova_ID0[8] = {  
                                 0x00000000u, 0x00000000u, 0x00000000u, 0x00000000u,
                                 0x00000000u, 0x00000000u, 0x00000000u, 0x00000000u 
                              };
#endif

// Declare here all the variables to be stored in EEPROM with ID0
///Dummy_EE ID0
uint32_T EEDummyID0_00 = 0u;
uint32_T EEDummyID0_01 = 0u;
uint32_T EEDummyID0_02 = 0u;
uint32_T EEDummyID0_03 = 0u;

#ifdef _BUILD_SYNCMGM_
#pragma ghs startnomisra
#include "syncmgm_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_KNOCKCORRADP_
#pragma ghs startnomisra
#include "KnockCorrAdp_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_KNOCKCORRNOM_
#pragma ghs startnomisra
#include "KnockCorrNom_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_TSPARKCTRLADAT_
#pragma ghs startnomisra
#include "tsparkctrladat_eep.c"
#pragma ghs endnomisra
#endif


/* EOF EEPROM */

