/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonKnockInt.h
 **  Date:          06-Sep-2021
 **
 **  Model Version: 1.1416
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonKnockInt_h_
#define RTW_HEADER_IonKnockInt_h_
#include <string.h>
#ifndef IonKnockInt_COMMON_INCLUDES_
# define IonKnockInt_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonKnockInt_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonKnockInt_initialize(void);

/* Exported entry point function */
extern void IonKnockInt_10ms(void);

/* Exported entry point function */
extern void IonKnockInt_EOA(void);

/* Exported entry point function */
extern void IonKnockInt_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T BaseFreq;              /* '<S6>/Merge13' */

/* FFT Base frequency */
extern uint32_T FftMag2Int1;           /* '<S6>/Merge1' */

/* FFT first band magnitude^2 sum */
extern uint32_T FftMag2Int2;           /* '<S6>/Merge2' */

/* FFT second band magnitude^2 sum */
extern uint32_T FftMag2Int3;           /* '<S6>/Merge3' */

/* FFT third band magnitude^2 sum */
extern uint32_T FftPeak[64];           /* '<S6>/Merge' */

/* Fft Magnitude^2 */
extern uint16_T IonKnockEthPercIndex;  /* '<S6>/Merge8' */

/* Index for interpolation on BKETHPERCKNOCK table */
extern uint16_T IonKnockEthPercRatio;  /* '<S6>/Merge9' */

/* Ratio for interpolation on BKETHPERCKNOCK table */
extern uint32_T KnPowNormFft;          /* '<S6>/Merge7' */

/* FFT Knocking power */
extern uint16_T VtFftMag2Int1[8];      /* '<S6>/Merge4' */

/* FFT first band magnitude^2 sum per cyl */
extern uint16_T VtFftMag2Int2[8];      /* '<S6>/Merge5' */

/* FFT second band magnitude^2 sum */
extern uint16_T VtFftMag2Int3[8];      /* '<S6>/Merge6' */

/* FFT third band magnitude^2 sum */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S29>/Data Type Duplicate' : Unused code path elimination
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 * Block '<S34>/Data Type Duplicate' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S42>/Data Type Duplicate' : Unused code path elimination
 * Block '<S42>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S45>/Data Type Duplicate' : Unused code path elimination
 * Block '<S45>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S48>/Data Type Duplicate' : Unused code path elimination
 * Block '<S48>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S14>/Conversion' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S23>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion' : Eliminate redundant data type conversion
 * Block '<S27>/Reshape' : Reshape block reduction
 * Block '<S28>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S28>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion' : Eliminate redundant data type conversion
 * Block '<S28>/Reshape' : Reshape block reduction
 * Block '<S34>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S35>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S35>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S35>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S35>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S36>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S36>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S33>/Conversion' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S48>/Conversion4' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonKnockInt'
 * '<S1>'   : 'IonKnockInt/IonKnockInt_Tasks'
 * '<S2>'   : 'IonKnockInt/Scheduler'
 * '<S3>'   : 'IonKnockInt/IonKnockInt_Tasks/Integral10ms'
 * '<S4>'   : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA'
 * '<S5>'   : 'IonKnockInt/IonKnockInt_Tasks/IntegralPowerOn'
 * '<S6>'   : 'IonKnockInt/IonKnockInt_Tasks/Subsystem3'
 * '<S7>'   : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Bands_Integral'
 * '<S8>'   : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation'
 * '<S9>'   : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/NormGain'
 * '<S10>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/VectorAssignment'
 * '<S11>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Bands_Integral/Calculate_KnPowNormFft'
 * '<S12>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Bands_Integral/Range_Management'
 * '<S13>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Bands_Integral/calc_FftPeak'
 * '<S14>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Bands_Integral/calc_FftPeak/Calculate_Integral'
 * '<S15>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Bands_Integral/calc_FftPeak/Calculate_Magnitude'
 * '<S16>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/First_Interval'
 * '<S17>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/IntegralWeight'
 * '<S18>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/PreCalc'
 * '<S19>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Second_Interval'
 * '<S20>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Third_Interval'
 * '<S21>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/First_Interval/ID_Freq_Range'
 * '<S22>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/First_Interval/ID_Freq_Start'
 * '<S23>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/First_Interval/ID_Freq_Range/INTERPOLATE_U16_U16'
 * '<S24>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/First_Interval/ID_Freq_Range/IfActionSubsystem'
 * '<S25>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/First_Interval/ID_Freq_Range/IfActionSubsystem1'
 * '<S26>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/First_Interval/ID_Freq_Start/INTERPOLATE_U16_U16'
 * '<S27>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/IntegralWeight/Look2D_IR_U1'
 * '<S28>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/IntegralWeight/Look2D_IR_U16'
 * '<S29>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/IntegralWeight/Look2D_IR_U1/Data Type Conversion Inherited1'
 * '<S30>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/IntegralWeight/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S31>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/PreCalc/BaseFrequency'
 * '<S32>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/PreCalc/PreLookup'
 * '<S33>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/PreCalc/RpmIndex'
 * '<S34>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/PreCalc/PreLookup/PreLookUpIdSearch_U1'
 * '<S35>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/PreCalc/PreLookup/PreLookUpIdSearch_U16'
 * '<S36>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/PreCalc/RpmIndex/BINARY_SEARCH_U1'
 * '<S37>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Second_Interval/ID_Freq_Range'
 * '<S38>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Second_Interval/ID_Freq_Start'
 * '<S39>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Second_Interval/ID_Freq_Range/INTERPOLATE_U16_U16'
 * '<S40>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Second_Interval/ID_Freq_Range/IfActionSubsystem'
 * '<S41>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Second_Interval/ID_Freq_Range/IfActionSubsystem1'
 * '<S42>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Second_Interval/ID_Freq_Start/INTERPOLATE_U16_U16'
 * '<S43>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Third_Interval/ID_Freq_Range'
 * '<S44>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Third_Interval/ID_Freq_Start'
 * '<S45>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Third_Interval/ID_Freq_Range/INTERPOLATE_U16_U16'
 * '<S46>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Third_Interval/ID_Freq_Range/IfActionSubsystem'
 * '<S47>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Third_Interval/ID_Freq_Range/IfActionSubsystem1'
 * '<S48>'  : 'IonKnockInt/IonKnockInt_Tasks/IntegralEOA/Base_Freq_Calculation/Third_Interval/ID_Freq_Start/INTERPOLATE_U16_U16'
 */

/*-
 * Requirements for '<Root>': IonKnockInt
 */
#endif                                 /* RTW_HEADER_IonKnockInt_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/