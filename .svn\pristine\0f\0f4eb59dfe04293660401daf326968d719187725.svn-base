/*
 * File: mul_s32_s32_s32_sr30.c
 *
 * Code generated for Simulink model 'CoilTarget'.
 *
 * Model version                  : 1.112
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Wed Mar 01 12:00:28 2017
 */

#include "rtwtypes.h"
#include "mul_wide_s32.h"
#include "mul_s32_s32_s32_sr30.h"

int32_T mul_s32_s32_s32_sr30(int32_T a, int32_T b)
{
  uint32_T u32_chi;
  uint32_T u32_clo;
  mul_wide_s32(a, b, &u32_chi, &u32_clo);
  u32_clo = (u32_chi << 2U) | (u32_clo >> 30U);
  return (int32_T)u32_clo;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
