/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB6C/Appl/branches/EISB6C_RS_15_CANMGM/tree/DD/MSPA#$  */
/* $Revision:: 161389                                                                                         $  */
/* $Date:: 2021-05-14 18:04:04 +0200 (ven, 14 mag 2021)                                                       $  */
/* $Author:: GirasoleG                                                                                        $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MSparkCmd
**  Filename        :  MSparkCmd.h
**  Created on      :  30-mar-2021 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifndef MSPARKCMD_H_
#define MSPARKCMD_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Msparkcmd_out.h"
#include "diagmgm_out.h"
#include "CoilTarget_out.h"
#include "CoilAngPattern_out.h"
#include "CoilTimPattern_out.h"
#include "Gtm_eisb_out.h"
#include "TSParkCtrlAdat_out.h"
#include "SyncMgm_out.h"
#include "digin_out.h"
#include "Digio_out.h"
#include "ignincmd_out.h"
#include "IonPhaseMgm_out.h"
#include "IonIntMgm_out.h"
#include "IonDwellMgm_out.h"
#include "Port_out.h"
#include "Recmgm_out.h"
#include "utils_out.h"
#include "mathlib.h"
#include "ionacq_out.h"
#include "IonAcqBufMgm_out.h"
#include "IonMisf_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST uint8_T GNMINEFFTTM;
extern CALQUAL CALQUAL_POST uint8_T GNMAXEFFTTM;
extern CALQUAL CALQUAL_POST uint16_T THRILPOLDIAG;
extern CALQUAL CALQUAL_POST uint8_T ENIONPHASEMISF;
extern CALQUAL CALQUAL_POST uint16_T THRILPSPDIAG;
extern CALQUAL CALQUAL_POST uint16_T THRINTSTARTFOUND;
extern CALQUAL CALQUAL_POST int16_T THRPEAKISEC;
extern CALQUAL CALQUAL_POST uint8_T EPWSMISFEN;
extern CALQUAL CALQUAL_POST int16_T TIMFIXEDTRIGIN;
extern CALQUAL CALQUAL_POST uint8_T THRCNTSPEVNT;
extern CALQUAL CALQUAL_POST uint8_T CMDINLEVELOFF;
extern CALQUAL CALQUAL_POST uint8_T SACMDINLEVERRNOTHR;
extern CALQUAL CALQUAL_POST uint8_T SACMDINLEVERRTESTEN ;
extern CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF0;
extern CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF1;
extern CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF2;
extern CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF3;
extern CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF4;
extern CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF5;
extern CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF6;
extern CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF7;
extern CALQUAL CALQUAL_POST uint8_T SACMDINLEVERRCYLES;

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : MSparkCmd_DiagSecOl
**
**   Description:
**    All the vectors are indexed using MisfAbsTdc
**    - Manage DIAG_SEC_OL
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void MSparkCmd_DiagSecOl(void);

/******************************************************************************
**   Function    : MSparkCmd_DiagTrigRec
**
**   Description:
**    Trigger recovery test
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void MSparkCmd_DiagTrigRec(void);

/******************************************************************************
**   Function    : MSparkCmd_DiagIonChannel
**
**   Description:
**    Ion channel diagnostic check
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void MSparkCmd_DiagIonChannel(void);

#endif
