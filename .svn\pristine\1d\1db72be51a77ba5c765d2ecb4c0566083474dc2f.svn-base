/**************************************************************************/
/* FILE NAME: mpc5500_ccdcfg.h               COPYRIGHT (c) Freescale 2004 */
/*                                                All Rights Reserved     */
/* DESCRIPTION:                                                           */
/* This file contains prototypes and definitions for the MPC5500          */
/*     C functions.                                                       */
/*     Additions to this file should be made only if additional defines   */
/*     are needed.                                                        */
/*     New definitions will need to be unique for this file and for the   */
/*     assembly equates in mpc5500_defs.inc.                              */
/*========================================================================*/
/*                                                                        */
/* REV      AUTHOR        DATE       DESCRIPTION OF CHANGE                */
/* ---   -----------   -----------   ---------------------                */
/* 0.1   <PERSON><PERSON>   8/Oct/03    Initial version.                     */
/* 0.2   G. Jackson     13/Nov/03    Converted for reconfiguration file.  */
/* 0.3   G. Jackson     02/Dec/03    Set up global Pbridge addresses in   */
/*                                     MMU to cover all addresses.        */
/* 0.4   G. Jackson     15/Apr/04    Removed references to application    */
/*                                     code.                              */
/* 0.5   G. Jackson     29/Apr/04    Removed user definitions to          */
/*                                     mpc5500_usrccdcfg.h                */
/* 0.6   G. Jackson     13/May/04    Added EBI ORn address mask entries   */
/* 0.7   G. Jackson     05/Jun/04    Added fields and prototypes for      */
/*                                      the PCRs                          */
/* 1.0   G. Jackson     30/Jun/04    Added fields for external bus enable */
/**************************************************************************/

#ifndef _MPC5500_CCDCFG_H_
#define _MPC5500_CCDCFG_H_

#pragma ghs startnomisra

#include "rtwtypes.h"

#ifdef  __cplusplus
extern "C" {
#endif


/*************************************************************/
/*************************************************************/
/* The user should not modify any of the definitions below   */


/**************************************************************************/
/*                            Definitions                                 */
/**************************************************************************/

/**************** External Bus Configuration Control ************/
/* A "YES" will set up the external bus  */
#define EXTERNAL_BUS_EN_NO  0
#define EXTERNAL_BUS_EN_YES 1

/*  Define Data Bus Port Size */
#define DATA_PORT_16 16
#define DATA_PORT_32 32

/**************** XCLKS initialization constants *****************/
/* Fields for changing the XCLKS */
#define XCLK_CHNG_NO  0x0
#define XCLK_CHNG_YES 0x1

/******* PBRIDGE and XBAR initialization constants ***********/
/* Fields for changing the PBRIDGE and XBAR */
#define PBRG_XBAR_CHNG_NO  0x0
#define PBRG_XBAR_CHNG_YES 0x1


/* Fields for Engineering clock division (ENGDIV[0:5]) factor */
#define ENGDIV_2   0x00000100
#define ENGDIV_4   0x00000200
#define ENGDIV_8   0x00000400
#define ENGDIV_10  0x00000500
#define ENGDIV_16  0x00000800
#define ENGDIV_32  0x00001000
#define ENGDIV_64  0x00002000
#define ENGDIV_128 0x00003F00

/* Fields for External Bust Tap Select (EBTS) signals hold time */
#define EBTS_HOLD_NO 0x00000000  /* 0x0; Zero hold     */
#define EBTS_HOLD_YS 0x00000004  /* 0x1; non-zero hold */


/* Fields for External bus division (EBDF[0:1]) factor */
#define EBDF_DIV_2 0x00000001
#define EBDF_DIV_4 0x00000003

/**************** SIU initialization constants *****************/

/* Definitions for SIU_PCR (Pad Configuration Register) */
/* The PCRs are 16-bit registers */
/* Constants for the Data Byte Field Designation */
#define DATA_BYT_MSB  0        /* Data[0:15] active  */
#define DATA_BYT_LSB 16        /* Data[16:31] active */
/* Fields for Pin Assignment (PA) */
#define PA_GPIO  0x0000    /* 0b000 */
#define PA_PRIM  0x0400    /* 0b001 */
#define PA_ALT1  0x0800    /* 0b010 Use only for 3 or 4 pin function */
#define PA_PRIM2 0x0C00    /* 0b011 Use only for 3 or 4 pin function */
#define PA_ALT2  0x1000    /* 0b100 Use only for 4 pin function      */
/* Fields for Output Buffer Enable (OBE) */
#define OBE_NO   0x0000    /* 0     */
#define OBE_YS   0x0200    /* 1  Output buffer function */
/* Fields for Input Buffer Enable (IBE) */
#define IBE_NO   0x0000    /* 0     */
#define IBE_YS   0x0100    /* 1  Input buffer function */
/* Fields for Drive Strength Control (DSC) */
#define DSC_10PF 0x0000   /* 0b00 10 pF drive strength */
#define DSC_20PF 0x0040   /* 0b01 20 pF drive strength */
#define DSC_30PF 0x0080   /* 0b10 30 pF drive strength */
#define DSC_50PF 0x00C0   /* 0b11 50 pF drive strength */
/* Fields for Open Drain Enable (ODE) */
#define ODE_DIS  0x0000   /* 0 Open drain output disabled */
#define ODE_EN   0x0020   /* 1 Open drain output enabled  */
/* Fields for Input Hysteresis (HYS) */
#define HYS_DIS  0x0000   /* 0 Input hysteresis disabled  */
#define HYS_EN   0x0010   /* 1 Input hysteresis enabled   */
/* Fields for Slew Rate Control (SRC) */
#define SRC_MIN  0x0000   /* 0b00 Slew rate control minimum */
#define SRC_MED  0x0004   /* 0b01 Slew rate control medium  */
#define SRC_MAX  0x000C   /* 0b11 Slew rate control maximum */
/* Fields for Weak Pull Enable (WPE) */
#define WPE_DIS  0x0000   /* 0 Weak pull disabled */
#define WPE_EN   0x0002   /* 1 Weak pull enabled  */
/* Fields for Weak Pull Select (WPS) */
#define WPS_DN   0x0000   /* 0 Weak pull select down */
#define WPS_UP   0x0001   /* 1 Weak pull select up   */



/**************** EBI initialization constants *****************/

/* Definitions for EBI_MCR (Module Configuration Register) */
/* Fields for SIZEN (Size Enable) */
#define SIZEN_TSIZ_01  0x00000000
#define SIZEN_SIZE     0x04000000

/* Fields for SIZE */
#define SIZE_XFR_32    0x00000000
#define SIZE_XFR_8     0x01000000
#define SIZE_XFR_16    0x02000000

/* Fields for Automatic CLKOUT Gating Enable (ACGE) */
#define ACGE_DISABLED 0x00000000
#define ACGE_ENABLED  0x00008000

/* Fields for External Master Mode (EXTM) */
#define EXTM_INACTIVE 0x00000000 /*single master mode */
#define EXTM_ACTIVE   0x00004000

/* Fields for External Arbitration (EARB) */
#define EARB_INTERNAL 0x00000000
#define EARB_EXTERNAL 0x00002000

/* Fields for External Arbitration Request Prority (EARP) */
#define EARP_MCU      0x00000000
#define EARP_EQUAL    0x00000800
#define EARP_EXTERNAL 0x00001800

/* Fields for Module disable mode (MDIS) */
#define MDIS_INACTIVE 0x00000000 /* Module disable mode is inactive */
#define MDIS_ACTIVE   0x00000040 /* Module disable mode is active   */

/* Fields for Data Bus Mode (DBM) */
#define DBUS_32       0x00000000
#define DBUS_16       0x00000001

/* Definitions for EBI_BRn (Base Registers where n = 0,1,2,3) */

/* Fields for the Base Address (BA) */
#define BA_20000  0x20000000   /* 0x20000 (Base address)                  */
#define BA_20800  0x20800000   /* 0x20800 (Base +   8 Meg of space)       */
#define BA_30000  0x30000000   /* 0x30000 (Base + 268 M address location) */
#define BA_3FF80  0x3FF80000   /* 0x3FF80 (Top of external space - 512K)  */

/* Fields for the Port Size (PS) */
#define PS_32BIT  0x000000000
#define PS_16BIT  0x000000800

/* Fields for the Burst Length (BL) */
#define BL_8WRD  0x00000000
#define BL_4WRD  0x00000040

/* Fields for the Write Enable/Byte Select (WEBS) */
#define WEBS_WE   0x00000000
#define WEBS_BE   0x00000020

/* Fields for the Toggle Burst Data In Progress (TBDIP)*/
#define TBDIP_BURST   0x00000000
#define TBDIP_BEFORE  0x00000010

/* Fields for the Burst Inhibit (BI) */
#define BI_ENABLE   0x00000000
#define BI_DISABLE  0x00000002

/* Fields for the Valid bit (V) */
#define V_INVALID 0x00000000
#define V_VALID   0x00000001

/* Definitions for EBI_ORn (Option Registers where n = 0,1,2,3) */

/* Fields for the Address Mask (AM) */
#define AM_512M  0xE0000000 /* 0xE0000; 512M space */
#define AM_256M  0xF0000000 /* 0xF0000; 256M space */
#define AM_128M  0xF8000000 /* 0xF8000; 128M space */
#define AM_64M   0xFC000000 /* 0xFC000;  64M space */
#define AM_32M   0xFE000000 /* 0xFE000;  32M space */
#define AM_16M   0xFF000000 /* 0xFF000;  16M space */
#define AM_8M    0xFF800000 /* 0xFF800;   8M space */
#define AM_4M    0xFFC00000 /* 0xFFC00;   4M space */
#define AM_2M    0xFFE00000 /* 0xFFE00;   2M space */
#define AM_1M    0xFFF00000 /* 0xFFF00;   1M space */
#define AM_512K  0xFFF80000 /* 0xFFF80; 512K space */
#define AM_256K  0xFFFC0000 /* 0xFFFC0; 256K space */
#define AM_128K  0xFFFE0000 /* 0xFFFE0; 128K space */
#define AM_64K   0xFFFF0000 /* 0xFFFF0;  64K space */
#define AM_32K   0xFFFF8000 /* 0xFFFF8;  32K space */

/* Fields for the Cycle length (SCY) */
#define OR_SCY_0   0x00000000
#define OR_SCY_1   0x00000010
#define OR_SCY_2   0x00000020
#define OR_SCY_3   0x00000030
#define OR_SCY_4   0x00000040
#define OR_SCY_5   0x00000050
#define OR_SCY_6   0x00000060
#define OR_SCY_7   0x00000070
#define OR_SCY_8   0x00000080
#define OR_SCY_9   0x00000090
#define OR_SCY_10  0x000000A0
#define OR_SCY_11  0x000000B0
#define OR_SCY_12  0x000000C0
#define OR_SCY_13  0x000000D0
#define OR_SCY_14  0x000000E0
#define OR_SCY_15  0x000000F0

/* Fields for the Burst Cycle length (BSCY) */
#define OR_BSCY_0   0x00000000
#define OR_BSCY_1   0x00000002
#define OR_BSCY_2   0x00000004
#define OR_BSCY_3   0x00000006

/* Fields for Chip Selects */

#define CS_0  0
#define CS_1  1
#define CS_2  2
#define CS_3  3

/**************** PBRIDGE initialization constants *****************/

/* Definitions for PBRIDGE_A and PBRIDGE_B */

/* Fields for the Master Privilege Control Register (MPCR) */
#define MPCR_DEFAULT  0x77770000

/* Fields for the PACRx and OPACRx registers */
#define PACR_DEFAULT  0x44444444
#define OPACR_DEFAULT 0x44444444

/**************** XBAR initialization constants *****************/

/* Definitions for the XBAR */

/* Fields for the Master Priority registers (XBAR_MPRn) */
/*        where n = 0,1,3,6,7                           */
#define MPR_DEFAULT   0x00000210
#define MST2_PRI_HI   0x00000000
#define MST2_PRI_MED  0x00000100
#define MST2_PRI_LO   0x00000200
#define MST1_PRI_HI   0x00000000
#define MST1_PRI_MED  0x00000010
#define MST1_PRI_LO   0x00000020
#define MST0_PRI_HI   0x00000000
#define MST0_PRI_MED  0x00000001
#define MST0_PRI_LO   0x00000002

/* Fields for the Slave General Purpose Control register (XBAR_SGPCRn) */
/*        where n = 0,1,3,6,7                                          */
#define SGPCR_DEFAULT 0x00000000
#define RO_RW         0x00000000
#define RO_RO         0x80000000
#define ARB_FX        0x00000000
#define ARB_RROBIN    0x00000100
#define PCTL_PARKMST  0x00000000
#define PCTL_LASTMST  0x00000010
#define PCTL_NOMST    0x00000020
#define PARK_MSTPRT0  0x00000000
#define PARK_MSTPRT1  0x00000001
#define PARK_MSTPRT2  0x00000002

#define OUTPUT_BUFFER_ENABLE 0x0200u
#define INPUT_BUFFER_ENABLE  0x0100u
#define OUTPUT_MODE          0x0200u
#define INPUT_MODE           0x0100u
#define READBACK_ENABLE      0x0100u

/* Fields used for Input 
#define ENABLE_HYSTERESIS  0x0010u
#define DISABLE_HYSTERESIS 0x0000u*/

/* treat WPE and WPS as 1 field for weak pull configuration */
#define WEAK_PULL_UP      0x0003u
#define WEAK_PULL_DOWN    0x0002u
#define WEAK_PULL_DISABLE 0x0000u

/* Fields used for Output */
/* DSC field - drive strangth control */
#define DRIVE_STRENGTH_10PF 0x0000u
#define DRIVE_STRENGTH_20PF 0x0040u
#define DRIVE_STRENGTH_30PF 0x0080u
#define DRIVE_STRENGTH_50PF 0x00C0u

#define OUTPUT_DRAIN_ENABLE 0x0020

/* SRC field - slew rate control */
#define MINIMUM_SLEW_RATE 0x0000u
#define MEDIUM_SLEW_RATE  0x0004u
#define MAXIMUM_SLEW_RATE 0x000Cu

/* Pin Name assignments */
#define PIN_EMIOS10 189u
#define PIN_EMIOS11 190u
#define PIN_EMIOS12 191u
#define PIN_EMIOS13 192u

#define PIN_HI 1u
#define PIN_LO 0u

/**************************************************************************/
/*                                                                        */
/* Definition for needed Special Purpose registers                        */
/*                                                                        */
/*                                                                        */
/*  CPU Special Purpose Registers (SPR's) are defined in mpc5554_spr.h    */
/*                                                                        */
/**************************************************************************/
/*                       Function Prototypes                              */
/**************************************************************************/

/* Overall initialization function prototype */
void cfg_mpc5500ccd(void);

/**************************************************************************/
/*                       C Code Prototypes                                */
/**************************************************************************/

/* This function configures PBRIDGE_A and PBRIDGE_B    */
void cfg_PBRIDGE(void);

/* This function configures Slave ports and Master priorities */
/*     in the XBAR                                            */
void cfg_XBAR(void);

#ifdef  __cplusplus
}
#endif

#pragma ghs endnomisra

#endif /* _MPC5500_CCDCFG_H_ */
