/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           P2NoiseDetect.c
 **  File Creation Date: 06-Sep-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         P2NoiseDetect
 **  Model Description:  This model detects the presence of noise on ion signal generated by cranking motor.
   If a noise is detected, the model has also to calculate a new spark advance correction, that will override the one due to knock.
 **  Model Version:      1.1345
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Mon Sep 06 15:53:43 2021
 **
 **  Last Saved Modification:  RoccaG - Mon Sep 06 15:51:52 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "P2NoiseDetect_out.h"
#include "P2NoiseDetect_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/Scheduler' */
#define P2N_event_P2NoiseDetect_PowerOn (0)
#define P2Nois_event_P2NoiseDetect_10ms (2)
#define P2Noise_event_P2NoiseDetect_EOA (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKLOADP2NOISEREC_dim           2U                        /* Referenced by: '<S14>/Constant4' */

/* BKLOADP2NOISEREC breakpoint dimension. */
#define BKRPMP2NOISEREC_dim            7U                        /* Referenced by:
                                                                  * '<S14>/Constant2'
                                                                  * '<S15>/Constant2'
                                                                  */

/* BKRPMP2NOISEREC breakpoint dimension. */
#define ID_VER_P2NOISEDETECT_DEF       11345U                    /* Referenced by: '<Root>/Scheduler' */

/* Model Version. */
#define MIN_INT_32                     MIN_int32_T               /* Referenced by: '<S5>/Constant' */

/* Minimum value for int32 type. */
#define MIN_P2NOISE_CORR               -32768                    /* Referenced by: '<S14>/Constant6' */

/* Minimum allowed value for VtP2NoiseDetRec */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_P2NOISEDETECT_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADP2NOISEREC[3] = { 5120U, 8960U,
  14080U } ;                           /* Referenced by: '<S14>/Constant3' */

/* Breakpoints of load for TBP2NOISEREC */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMP2NOISEREC[8] = { 1000U, 2000U,
  3000U, 4000U, 5000U, 6000U, 8000U, 9000U } ;/* Referenced by:
                                               * '<S14>/Constant1'
                                               * '<S15>/Constant1'
                                               */

/* Breakpoints of engine speed for TBP2NOISEREC */
CALQUAL_PRE CALQUAL CALQUAL_POST int32_T P2NOISERMAX = 384;/* Referenced by: '<S5>/Constant1' */

/* P2Noise rec SA correction Ratelimiter MAX rate */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBP2NOISEREC[24] = { 54, 54, 54, 54, 54,
  54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54, 54 } ;/* Referenced by: '<S14>/Constant5' */

/* Table of steps to reduce the SA in case of P2 noise detected */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTP2NOISECYLEN[8] = { 1U, 1U, 1U, 1U,
  1U, 1U, 1U, 1U } ;                   /* Referenced by: '<S10>/Constant1' */

/* Enable flag to activate P2 Noise detection flag recovery */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTP2NOISEDETTHR[8] = { 3U, 3U, 3U, 3U,
  3U, 3U, 3U, 3U } ;                   /* Referenced by: '<S15>/Constant' */

/* FFT magnitude integral P2 Engine Noise detection threshold */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T VtP2NoiseDetFlg[8];            /* '<S2>/Merge' */

/* P2 engine Noise detected correction active */
int16_T VtP2NoiseDetRec[8];            /* '<S2>/Merge1' */

/* P2 engine Noise correction  */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_P2NoiseDetect;/* '<Root>/Scheduler' */

/* Model Version */
STATIC_TEST_POINT uint16_T P2NoiseDetThr;/* '<S2>/Merge3' */

/* Output of VTP2NOISEDETTHR */
STATIC_TEST_POINT int32_T VtP2NoiseCorrRL[8];/* '<S2>/Merge2' */

/* P2 engine Noise correction increased res for rate limiter */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void P2No_chartstep_c4_P2NoiseDetect(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/Scheduler' */
static void P2No_chartstep_c4_P2NoiseDetect(const int32_T *sfEvent)
{
  /* local block i/o variables */
  int32_T rtb_RateLimiter_S32;
  uint16_T rtb_LookUp_U16_U16;
  int16_T rtb_Look2D_S8_U16_U16;
  uint8_T rtb_P2NoiseDetFlg;
  int32_T rtb_P2NoiseCorrRL_k;
  int32_T rtb_Conversion1;

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */
  /* Chart: '<Root>/Scheduler' incorporates:
   *  Inport: '<Root>/Rpm'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */
  /* During: Scheduler */
  /* Model scheduler. Resets model outputs at 10ms event if engine speed is 0. */
  /* Entry Internal: Scheduler */
  /* Transition: '<S1>:31' */
  switch (*sfEvent) {
   case P2N_event_P2NoiseDetect_PowerOn:
    /* Outputs for Function Call SubSystem: '<Root>/Task_PowerOn'
     *
     * Block description for '<Root>/Task_PowerOn':
     *  This block performs outputs initialization. No requirement is linked
     *  to this subsystem because they are linked to previous Stateflow.
     */
    /* SignalConversion generated from: '<S4>/VtP2NoiseDetFlg' */
    /* Transition: '<S1>:8' */
    /* Transition: '<S1>:19':
     *  1. EISB_FCA6CYL_SW_REQ_1652: Software shall set to 0 each output produced for Noise Detect functionality at ECU power on. (ECU_SW_Requirements#3129)
     */
    /* Event: '<S1>:4' */
    memset((&(VtP2NoiseDetFlg[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S4>/VtP2NoiseDetRec' */
    memset((&(VtP2NoiseDetRec[0])), 0, (sizeof(int16_T)) << 3U);

    /* SignalConversion generated from: '<S4>/VtP2NoiseCorrRL' */
    memset((&(VtP2NoiseCorrRL[0])), 0, (sizeof(int32_T)) << 3U);

    /* SignalConversion generated from: '<S4>/P2NoiseDetThr' incorporates:
     *  Constant: '<S4>/Constant3'
     */
    P2NoiseDetThr = 0U;

    /* End of Outputs for SubSystem: '<Root>/Task_PowerOn' */
    IdVer_P2NoiseDetect = ID_VER_P2NOISEDETECT_DEF;
    break;

   case P2Nois_event_P2NoiseDetect_10ms:
    /* Transition: '<S1>:33' */
    /* Transition: '<S1>:24' */
    if (((int32_T)Rpm) == 0) {
      /* Outputs for Function Call SubSystem: '<Root>/Task_PowerOn'
       *
       * Block description for '<Root>/Task_PowerOn':
       *  This block performs outputs initialization. No requirement is linked
       *  to this subsystem because they are linked to previous Stateflow.
       */
      /* SignalConversion generated from: '<S4>/VtP2NoiseDetFlg' */
      /* Transition: '<S1>:39' */
      /* Transition: '<S1>:27':
       *  1. EISB_FCA6CYL_SW_REQ_1653: Software shall set to 0 each output produced for Noise Detect func... (ECU_SW_Requirements#3130)
       */
      /* Event: '<S1>:4' */
      memset((&(VtP2NoiseDetFlg[0])), 0, (sizeof(uint8_T)) << 3U);

      /* SignalConversion generated from: '<S4>/VtP2NoiseDetRec' */
      memset((&(VtP2NoiseDetRec[0])), 0, (sizeof(int16_T)) << 3U);

      /* SignalConversion generated from: '<S4>/VtP2NoiseCorrRL' */
      memset((&(VtP2NoiseCorrRL[0])), 0, (sizeof(int32_T)) << 3U);

      /* SignalConversion generated from: '<S4>/P2NoiseDetThr' incorporates:
       *  Constant: '<S4>/Constant3'
       */
      P2NoiseDetThr = 0U;

      /* End of Outputs for SubSystem: '<Root>/Task_PowerOn' */
    } else {
      /* Transition: '<S1>:41' */
    }
    break;

   default:
    /* Outputs for Function Call SubSystem: '<Root>/Task_EOA'
     *
     * Block description for '<Root>/Task_EOA':
     *  This block performs EOA runnable.
     */
    /* If: '<S10>/If' incorporates:
     *  Constant: '<S10>/Constant'
     *  Constant: '<S10>/Constant1'
     *  Constant: '<S10>/Constant2'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Inport: '<Root>/IonKnockEnabled'
     *  Logic: '<S10>/LogicalOperator'
     *  MultiPortSwitch: '<S10>/Index Vector'
     *  RelationalOperator: '<S10>/Relational Operator1'
     *  RelationalOperator: '<S10>/Relational Operator2'
     *
     * Block requirements for '<S10>/LogicalOperator':
     *  1. EISB_FCA6CYL_SW_REQ_1206: Software shall enable the noise detect strategy, for the i_th cyli... (ECU_SW_Requirements#1223)
     */
    /* Transition: '<S1>:10' */
    /*  P2NoiseDetect_EOA  */
    /* Event: '<S1>:5' */
    if ((((int32_T)IonKnockEnabled) == 0) || (((int32_T)VTP2NOISECYLEN
          [(IonAbsTdcEOA)]) == 0)) {
      /* Outputs for IfAction SubSystem: '<S6>/StrategyDisabled' incorporates:
       *  ActionPort: '<S11>/ActionPort'
       *
       * Block description for '<S6>/StrategyDisabled':
       *  This block resets outputs when the noise detection strategy is
       *  disabled.
       */
      /* SignalConversion generated from: '<S11>/P2NoiseCorrRL' incorporates:
       *  Constant: '<S11>/Constant2'
       */
      rtb_P2NoiseCorrRL_k = 0;

      /* DataTypeConversion: '<S11>/Conversion2' incorporates:
       *  MultiPortSwitch: '<S11>/Index Vector2'
       *  SignalConversion generated from: '<S3>/VtP2NoiseDetRec_old'
       */
      rtb_Conversion1 = ((int32_T)VtP2NoiseDetRec[(IonAbsTdcEOA)]) * 32;

      /* SignalConversion generated from: '<S11>/P2NoiseDetFlg' incorporates:
       *  Constant: '<S11>/Constant1'
       */
      rtb_P2NoiseDetFlg = 0U;

      /* SignalConversion generated from: '<S11>/P2NoiseDetThr' incorporates:
       *  Constant: '<S11>/Constant3'
       */
      P2NoiseDetThr = 0U;

      /* End of Outputs for SubSystem: '<S6>/StrategyDisabled' */
    } else {
      /* Outputs for IfAction SubSystem: '<S6>/StrategyEnabled' incorporates:
       *  ActionPort: '<S12>/ActionPort'
       *
       * Block description for '<S6>/StrategyEnabled':
       *  This block performs noise detection and spark advance calculation.
       */
      /* S-Function (LookUp_U16_U16): '<S19>/LookUp_U16_U16' incorporates:
       *  Constant: '<S15>/Constant'
       *  Constant: '<S15>/Constant1'
       *  Constant: '<S15>/Constant2'
       */
      LookUp_U16_U16( &rtb_LookUp_U16_U16, &VTP2NOISEDETTHR[0], Rpm,
                     &BKRPMP2NOISEREC[0], ((uint8_T)BKRPMP2NOISEREC_dim));

      /* If: '<S15>/If' incorporates:
       *  Inport: '<Root>/KnockInt'
       *  Inport: '<Root>/ThrIntKnock'
       *  Inport: '<Root>/VtFftMag2Int3'
       *  Logic: '<S15>/Logical Operator'
       *  Logic: '<S15>/Logical Operator1'
       *  MultiPortSwitch: '<S15>/Index Vector'
       *  MultiPortSwitch: '<S15>/Index Vector1'
       *  RelationalOperator: '<S15>/Relational Operator1'
       *  RelationalOperator: '<S15>/Relational Operator2'
       */
      if ((KnockInt[(IonAbsTdcEOA)] < ThrIntKnock) || (VtFftMag2Int3
           [(IonAbsTdcEOA)] < rtb_LookUp_U16_U16)) {
        /* Outputs for IfAction SubSystem: '<S12>/NoiseNotDetected' incorporates:
         *  ActionPort: '<S16>/ActionPort'
         *
         * Block description for '<S12>/NoiseNotDetected':
         *  This block evaluates output when noise is not detected.
         */
        /* SignalConversion generated from: '<S16>/P2NoiseCorrRL' incorporates:
         *  Constant: '<S16>/Constant2'
         */
        rtb_P2NoiseCorrRL_k = 0;

        /* DataTypeConversion: '<S16>/Conversion2' incorporates:
         *  MultiPortSwitch: '<S16>/IndexVector2'
         *  SignalConversion generated from: '<S3>/VtP2NoiseDetRec_old'
         */
        rtb_Conversion1 = ((int32_T)VtP2NoiseDetRec[(IonAbsTdcEOA)]) * 32;

        /* SignalConversion generated from: '<S16>/P2NoiseDetFlg' incorporates:
         *  Constant: '<S16>/Constant1'
         */
        rtb_P2NoiseDetFlg = 0U;

        /* End of Outputs for SubSystem: '<S12>/NoiseNotDetected' */
      } else {
        /* Outputs for IfAction SubSystem: '<S12>/NoiseDetected' incorporates:
         *  ActionPort: '<S14>/ActionPort'
         *
         * Block description for '<S12>/NoiseDetected':
         *  This block calculates a spark advance correction in case of noise detection through a look-up table.
         *  The starting value for spark advance correction is limited by the spark advance correction evaluated by knock detection strategy.
         */
        /* S-Function (Look2D_S8_U16_U16): '<S18>/Look2D_S8_U16_U16' incorporates:
         *  Constant: '<S14>/Constant1'
         *  Constant: '<S14>/Constant2'
         *  Constant: '<S14>/Constant3'
         *  Constant: '<S14>/Constant4'
         *  Constant: '<S14>/Constant5'
         *
         * Block requirements for '<S14>/Constant5':
         *  1. EISB_FCA6CYL_SW_REQ_1208: Every time that software detects an electrical noise for the i_th ... (ECU_SW_Requirements#1225)
         */
        Look2D_S8_U16_U16( &rtb_Look2D_S8_U16_U16, &TBP2NOISEREC[0], Load,
                          &BKLOADP2NOISEREC[0], ((uint8_T)BKLOADP2NOISEREC_dim),
                          RpmF, &BKRPMP2NOISEREC[0], ((uint8_T)
          BKRPMP2NOISEREC_dim));

        /* DataTypeConversion: '<S14>/Conversion' */
        rtb_P2NoiseCorrRL_k = ((int32_T)rtb_Look2D_S8_U16_U16) + -15360;

        /* Switch: '<S14>/IF_3_Logic' incorporates:
         *  Constant: '<S14>/Constant7'
         *  DataTypeConversion: '<S14>/Conversion2'
         *  MultiPortSwitch: '<S14>/Index Vector1'
         *  MultiPortSwitch: '<S14>/Index Vector2'
         *  RelationalOperator: '<S14>/Relational Operator1'
         *  SignalConversion generated from: '<S3>/VtP2NoiseDetFlg_old'
         *  SignalConversion generated from: '<S3>/VtP2NoiseDetRec_old'
         */
        if (((int32_T)VtP2NoiseDetFlg[(IonAbsTdcEOA)]) == 0) {
          /* DataTypeConversion: '<S14>/Conversion1' incorporates:
           *  Inport: '<Root>/SAKnock'
           *  MultiPortSwitch: '<S14>/Index Vector3'
           */
          rtb_Conversion1 = ((int32_T)SAKnock[(IonAbsTdcEOA)]) * 32;

          /* MinMax: '<S14>/Max' incorporates:
           *  Constant: '<S14>/Constant6'
           */
          if (rtb_Conversion1 <= MIN_P2NOISE_CORR) {
            rtb_Conversion1 = MIN_P2NOISE_CORR;
          }

          /* MinMax: '<S14>/MinMax' incorporates:
           *  MinMax: '<S14>/Max'
           *
           * Block requirements for '<S14>/MinMax':
           *  1. EISB_FCA6CYL_SW_REQ_1210: Every time that software detects an electrical noise for the i_th ... (ECU_SW_Requirements#1226)
           */
          if (rtb_P2NoiseCorrRL_k < rtb_Conversion1) {
            rtb_Conversion1 = rtb_P2NoiseCorrRL_k;
          }

          /* End of MinMax: '<S14>/MinMax' */
        } else {
          rtb_Conversion1 = ((int32_T)VtP2NoiseDetRec[(IonAbsTdcEOA)]) * 32;
        }

        /* End of Switch: '<S14>/IF_3_Logic' */

        /* SignalConversion generated from: '<S14>/P2NoiseDetFlg' incorporates:
         *  Constant: '<S14>/Constant'
         *
         * Block requirements for '<S14>/Constant':
         *  1. EISB_FCA6CYL_SW_REQ_1207: Software shall detect an electrical noise,cylinder by cylinder, ev... (ECU_SW_Requirements#1224)
         */
        rtb_P2NoiseDetFlg = 1U;

        /* End of Outputs for SubSystem: '<S12>/NoiseDetected' */
      }

      /* End of If: '<S15>/If' */

      /* SignalConversion generated from: '<S12>/P2NoiseDetThr' */
      P2NoiseDetThr = rtb_LookUp_U16_U16;

      /* End of Outputs for SubSystem: '<S6>/StrategyEnabled' */
    }

    /* End of If: '<S10>/If' */

    /* S-Function (RateLimiter_S32): '<S8>/RateLimiter_S32' incorporates:
     *  Constant: '<S5>/Constant'
     *  Constant: '<S5>/Constant1'
     */
    RateLimiter_S32( &rtb_RateLimiter_S32, rtb_P2NoiseCorrRL_k, rtb_Conversion1,
                    MIN_INT_32, P2NOISERMAX);

    /* Assignment: '<S5>/Assignment' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    VtP2NoiseDetFlg[(IonAbsTdcEOA)] = rtb_P2NoiseDetFlg;

    /* Assignment: '<S5>/Assignment1' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    VtP2NoiseCorrRL[(IonAbsTdcEOA)] = rtb_P2NoiseCorrRL_k;

    /* Assignment: '<S5>/Assignment2' incorporates:
     *  DataTypeConversion: '<S7>/Conversion2'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Product: '<S7>/Divide'
     *
     * Block requirements for '<S5>/Assignment2':
     *  1. EISB_FCA6CYL_SW_REQ_1431: Every time that software detects an electrical noise for the i_th ... (ECU_SW_Requirements#3135)
     */
    VtP2NoiseDetRec[(IonAbsTdcEOA)] = (int16_T)(rtb_RateLimiter_S32 / 32);

    /* End of Outputs for SubSystem: '<Root>/Task_EOA' */
    break;
  }

  /* End of Chart: '<Root>/Scheduler' */
}

/*
 * Output and update for function-call system: '<Root>/Scheduler'
 * Block description for: '<Root>/Scheduler'
 *   Model scheduler. Resets model outputs at 10ms event if engine speed is 0.
 */
void P2NoiseDetect_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[3];
  int32_T i;

  /* Chart: '<Root>/Scheduler' incorporates:
   *  TriggerPort: '<S1>/input events'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */
  for (i = 0; i < 3; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S1>:2' */
    i = (int32_T)P2N_event_P2NoiseDetect_PowerOn;
    P2No_chartstep_c4_P2NoiseDetect(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S1>:3' */
    i = (int32_T)P2Noise_event_P2NoiseDetect_EOA;
    P2No_chartstep_c4_P2NoiseDetect(&i);
  }

  if (rtb_inputevents[2U] == 2) {
    /* Event: '<S1>:21' */
    i = (int32_T)P2Nois_event_P2NoiseDetect_10ms;
    P2No_chartstep_c4_P2NoiseDetect(&i);
  }
}

/* Model step function */
void P2NoiseDetect_10ms(void)
{
  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/P2NoiseDetect_10ms' */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */
  P2NoiseDetect_Scheduler(2);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/P2NoiseDetect_10ms' */
}

/* Model step function */
void P2NoiseDetect_EOA(void)
{
  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/P2NoiseDetect_EOA' */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */
  P2NoiseDetect_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/P2NoiseDetect_EOA' */
}

/* Model step function */
void P2NoiseDetect_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/P2NoiseDetect_PowerOn' incorporates:
   *  Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  Model scheduler. Resets model outputs at 10ms event if engine speed is
   *  0.
   */
  P2NoiseDetect_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/P2NoiseDetect_PowerOn' */
}

/* Model initialize function */
void P2NoiseDetect_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T VtP2NoiseDetFlg[N_CYL_MAX];
int16_T VtP2NoiseDetRec[N_CYL_MAX];
void P2NoiseDetect_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    VtP2NoiseDetFlg[idx] = 0u;
    VtP2NoiseDetRec[idx] = 0;
  }
}

void P2NoiseDetect_PowerOn(void)
{
  P2NoiseDetect_Stub();
}

void P2NoiseDetect_EOA(void)
{
  P2NoiseDetect_Stub();
}

void P2NoiseDetect_10ms(void)
{
  P2NoiseDetect_Stub();
}

#endif

/* _BUILD_P2NOISEDETECT_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/