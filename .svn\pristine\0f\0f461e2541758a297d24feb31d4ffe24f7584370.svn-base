/*****************************************************************************************************************/
/* $HeadURL::                                                                                                              $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  IgnInCmd
**  Filename        :  IgnInCmd_calib.c
**  Created on      :  31-mar-2021 12:01:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_IGNINCMD_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "ignincmd.h"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
#pragma ghs section rodata=".calib"

///Rpm breakpoint for FlgDSAoutDis calculation
CALQUAL uint16_T BKRPMDSA[BKRPMDSA_dim] = 
{
   1000u,   2000u,   2500u,   3000u,   5000u,   7000u,   9000u
};

///DSAoutCyl threshold for knock startegy disable [deg]
CALQUAL int8_T VTDSAOUTMAX[BKRPMDSA_dim] = 
{
 60, 60, 60, 50, 46, 40, 32
};

///Number of cycles for knocking disabling after DSAoutCyl > VTDSAOUTMAX [counter]
CALQUAL uint8_T VTNCYCLEDIS[BKRPMDSA_dim] = 
{
    5u,    9u,   11u,   13u,   19u,   24u,   30u
};

#endif // _BUILD_IGNINCMD_
/****************************************************************************
 ****************************************************************************/

 
