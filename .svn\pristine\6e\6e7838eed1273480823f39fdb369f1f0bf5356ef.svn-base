/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_PIT_out.h
**  Created on      :  07-Feb-2022 12:22:00
**  Original author :  Mocci A
******************************************************************************/
/*****************************************************************************
**
**                        SafetyMngr_PIT Description
**
**  PIT operations (number of periodic triggers) are checked and compared against 
**  the expected values each FTTI (5msec). This includes comparing the number of 
**  generated triggers within a given time period (500usec plus band guard) using 
**  another timer (Timing module, PIT1 CH0 as free modulo counter) as reference 
**  and, in case of failures:
**   - reading back PIT configuration (check for enabled channels, load values, 
**     etc.),
**   - perforing ECU reset.
******************************************************************************/
#ifndef SAFETYMNGR_PIT_OUT_H
#define SAFETYMNGR_PIT_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RTWtypes.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/// number of PIT0CH0 triggers (OS sheduler timing resolution) within PIT_CHECK_TIMEOUT window
#define PIT_TRG_EXPECTED 10u

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/

/******************************************************************************
**   Function    : SafetyMngr_PIT_Init
**
**   Description:
**    This function initializes internal variables for PIT safety mechanism.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_PIT_Init(void);

/******************************************************************************
**   Function    : SafetyMngr_INTC_CheckMCME
**
**   Description:
**    This function periodically checks PIT safety mechanism.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_PIT_Check10ms(void);

#endif // SAFETYMNGR_PIT_OUT_H

/****************************************************************************
 ****************************************************************************/

