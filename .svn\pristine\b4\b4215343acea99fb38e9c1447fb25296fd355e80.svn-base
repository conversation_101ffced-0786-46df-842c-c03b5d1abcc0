/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  STM
**  Filename        :  STM_events.c
**  Created on      :  10-Feb-2022 10:45:00
**  Original author :  MocciA
******************************************************************************/
#ifdef _BUILD_STM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "stm.h"
#ifdef _TEST_STM_
#include "Timing.h"
#endif

/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/// STM0 CH0 Isr36 occurences counter
static uint32_T STM_CH0_Isr36_cnt;
/// STM0 CH1 Isr37 occurences counter
static uint32_T STM_CH1_Isr37_cnt;
/// STM0 CH2 Isr38 occurences counter
static uint32_T STM_CH2_Isr38_cnt;
/// STM0 CH3 Isr39 occurences counter
static uint32_T STM_CH3_Isr39_cnt;
#ifdef _TEST_STM_
/// debug vars for STM_CHx channel
static uint16_T Debug_STM_CH_Ovf[STM_NUM_OF_CHANNELS] = {0u,0u,0u,0u};
static uint64_T Debug_STM_CH_CmpValue[STM_NUM_OF_CHANNELS] = {0u,0u,0u,0u};
static uint16_T Debug_STM_CH_ISR_Cnt[STM_NUM_OF_CHANNELS] = {0u,0u,0u,0u};
static uint16_T Debug_STM_CH_Ovf2[STM_NUM_OF_CHANNELS] = {0u,0u,0u,0u};
static uint64_T StmCh0_IsrTime;
static uint64_T StmCh0_IsrTimeUsec;
static uint64_T StmCh0_IsrPeriod;
static uint64_T StmCh0_IsrPeriod_MAX = 10000u;
static uint64_T StmCh0_IsrPeriod_MIN = 10000u;
static uint64_T StmCh0_IsrTimeOld;
#endif // _TEST_STM_
/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : STM_CH0_Isr36
**
**   Description:
**    Interrupt request due to a match on the STM channel 0.
**    This function implements the time period independent from the PIT for
**    safety mechanism according to "AN4446 - SPC574K72xx safety manual",
**    par. "3.3.15 Periodic Interrupt Timer (PIT)"
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void STM_CH0_Isr36(void)
{
    STM_CH0_Isr36_cnt++;

#ifdef STM_CH0_ENABLE
#if (STM_CH0_ENABLE == 1u)

uint64_T cmpValueSum;

    cmpValueSum = STM_0.Channel[STM_CH0].CMP.B.CMP;
    cmpValueSum += STM_TIMEOUT0;

#ifdef _BUILD_SAFETYMNGR_PIT_
#ifdef _TEST_STM_
    TIMING_GetAbsTimer(&StmCh0_IsrTime);
    TIMING_TicksToMicroSeconds(StmCh0_IsrTime, &StmCh0_IsrTimeUsec);
#endif // _TEST_STM_
    SafetyMngr_PIT_Check10ms();
#endif // _BUILD_SAFETYMNGR_PIT_

#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(STM_INT_0_CIR0_ISR_POS); // Interrupt no. 36

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (STM_0.Channel[STM_CH0].CIR.R != STM_CIR_CIF_EN)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_


#ifdef _TEST_STM_
    Debug_STM_CH_ISR_Cnt[STM_CH0]++;
    Debug_STM_CH_CmpValue[STM_CH0] = cmpValueSum;
#endif // _TEST_STM_

    if (cmpValueSum < MAX_uint32_T)
    {
        /* Set STM channel Ch compare value */
        STM_0.Channel[STM_CH0].CMP.R = (uint32_T)cmpValueSum;

#ifdef _TEST_STM_
        if (Debug_STM_CH_Ovf[STM_CH0] > 0u)
        {
            Debug_STM_CH_Ovf2[STM_CH0]++;
        }
#endif // _TEST_STM_
    }
    else
    {
        /* Set STM channel Ch compare value */
        STM_0.Channel[STM_CH0].CMP.R = (uint32_T) (cmpValueSum - MAX_uint32_T);
#ifdef _TEST_STM_
        Debug_STM_CH_Ovf[STM_CH0]++;
#endif // _TEST_STM_
    }

#ifdef _TEST_STM_
    StmCh0_IsrPeriod = StmCh0_IsrTimeUsec - StmCh0_IsrTimeOld;

    if ((StmCh0_IsrPeriod > StmCh0_IsrPeriod_MAX) && (StmCh0_IsrTimeOld != 0u))
    {
        StmCh0_IsrPeriod_MAX = StmCh0_IsrPeriod;
    }

    if ((StmCh0_IsrPeriod < StmCh0_IsrPeriod_MIN)&& (StmCh0_IsrTimeOld != 0u))
    {
        StmCh0_IsrPeriod_MIN = StmCh0_IsrPeriod;
    }

    /* Update old value for next cycle */
    StmCh0_IsrTimeOld = StmCh0_IsrTimeUsec;
#endif // _TEST_STM_

#endif // (STM_CH0_ENABLE == 1u)
#endif //  STM_CH0_ENABLE

    /* clear STM channel Channel 0 interrupt flag (w1c) */
    STM_0.Channel[STM_CH0].CIR.R = STM_CIR_CIF_EN;

    /* Add your code here (eg Activate task, if needed) */

}

/******************************************************************************
**   Function    : STM_CH1_Isr37
**
**   Description:
**    Interrupt request due to a match on the STM channel 1
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void STM_CH1_Isr37(void)
{
    STM_CH1_Isr37_cnt++;

#ifdef STM_CH1_ENABLE
#if (STM_CH1_ENABLE == 1u)

uint64_T cmpValueSum;

    cmpValueSum = STM_0.Channel[STM_CH1].CMP.B.CMP;
    cmpValueSum += STM_TIMEOUT1;

#ifdef _TEST_STM_
    Debug_STM_CH_ISR_Cnt[STM_CH1]++;
    Debug_STM_CH_CmpValue[STM_CH1] = cmpValueSum;
#endif // _TEST_STM_

    if (cmpValueSum < MAX_uint32_T)
    {
        STM_0.Channel[STM_CH1].CMP.R = (uint32_T)cmpValueSum;     // set STM channel Ch compare value
#ifdef _TEST_STM_
        if (Debug_STM_CH_Ovf[STM_CH1] > 0u)
        {
            Debug_STM_CH_Ovf2[STM_CH1]++;
        }
#endif // _TEST_STM_
    }
    else
    {
#ifdef _TEST_STM_
        Debug_STM_CH_Ovf[STM_CH1]++;
#endif // _TEST_STM_
        STM_0.Channel[STM_CH1].CMP.R = (uint32_T) (cmpValueSum - MAX_uint32_T);
    }

#endif // (STM_CH1_ENABLE == 1u)
#endif //  STM_CH1_ENABLE

    /* clear STM channel Channel 0 interrupt flag (w1c) */
    STM_0.Channel[STM_CH1].CIR.R = STM_CIR_CIF_EN;

    /* Add your code here (eg Activate task, if needed) */

}


/******************************************************************************
**   Function    : STM_CH2_Isr38
**
**   Description:
**    Interrupt request due to a match on the STM channel 2
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void STM_CH2_Isr38(void)
{
    STM_CH2_Isr38_cnt++;

#ifdef STM_CH2_ENABLE
#if (STM_CH2_ENABLE == 1u)

uint64_T cmpValueSum;

    cmpValueSum = STM_0.Channel[STM_CH2].CMP.B.CMP;
    cmpValueSum += STM_TIMEOUT2;

#ifdef _TEST_STM_
    Debug_STM_CH_ISR_Cnt[STM_CH2]++;
    Debug_STM_CH_CmpValue[STM_CH2] = cmpValueSum;
#endif // _TEST_STM_

    if (cmpValueSum < MAX_uint32_T)
    {
        STM_0.Channel[STM_CH2].CMP.R = (uint32_T)cmpValueSum;     // set STM channel Ch compare value
#ifdef _TEST_STM_
        if (Debug_STM_CH_Ovf[STM_CH2] > 0u)
        {
            Debug_STM_CH_Ovf2[STM_CH2]++;
        }
#endif // _TEST_STM_
    }
    else
    {
#ifdef _TEST_STM_
        Debug_STM_CH_Ovf[STM_CH2]++;
#endif // _TEST_STM_
        STM_0.Channel[STM_CH2].CMP.R = (uint32_T) (cmpValueSum - MAX_uint32_T);
    }

#endif // (STM_CH2_ENABLE == 1u)
#endif //  STM_CH2_ENABLE

    /* clear STM channel Channel 0 interrupt flag (w1c) */
    STM_0.Channel[STM_CH2].CIR.R = STM_CIR_CIF_EN;

    /* Add your code here (eg Activate task, if needed) */

}


/******************************************************************************
**   Function    : STM_CH3_Isr39
**
**   Description:
**    Interrupt request due to a match on the STM channel 3
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void STM_CH3_Isr39(void)
{
    STM_CH3_Isr39_cnt++;

#ifdef STM_CH3_ENABLE
#if (STM_CH3_ENABLE == 1u)

uint64_T cmpValueSum;

    cmpValueSum = STM_0.Channel[STM_CH3].CMP.B.CMP;
    cmpValueSum += STM_TIMEOUT3;

#ifdef _TEST_STM_
    Debug_STM_CH_ISR_Cnt[STM_CH3]++;
    Debug_STM_CH_CmpValue[STM_CH3] = cmpValueSum;
#endif // _TEST_STM_

    if (cmpValueSum < MAX_uint32_T)
    {
        STM_0.Channel[STM_CH3].CMP.R = (uint32_T)cmpValueSum;     // set STM channel Ch compare value
#ifdef _TEST_STM_
        if (Debug_STM_CH_Ovf[STM_CH3] > 0u)
        {
            Debug_STM_CH_Ovf2[STM_CH3]++;
        }
#endif // _TEST_STM_
    }
    else
    {
#ifdef _TEST_STM_
        Debug_STM_CH_Ovf[STM_CH3]++;
#endif // _TEST_STM_
        STM_0.Channel[STM_CH3].CMP.R = (uint32_T) (cmpValueSum - MAX_uint32_T);
    }

#endif // (STM_CH3_ENABLE == 1u)
#endif //  STM_CH3_ENABLE

    /* clear STM channel Channel 0 interrupt flag (w1c) */
    STM_0.Channel[STM_CH3].CIR.R = STM_CIR_CIF_EN;

    /* Add your code here (eg Activate task, if needed) */

}

 #endif //_BUILD_PIT_

/****************************************************************************
 ****************************************************************************/

