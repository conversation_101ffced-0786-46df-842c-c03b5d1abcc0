/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TTCAN
**  Filename        :  TTcan_test.c
**  Created on      :  21-lug-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef  _BUILD_CAN_
#ifdef  _TEST_TTCAN_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "TTcan.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
#define BUFFER_RX_POLLING     0
#define STANDARD_SIZE       8U

// #define TEST_TTCAN

#ifdef TEST_TTCAN
t_ttcanMSG RxFrameC1;
t_ttcanMSG RxFrameC2;
uint8_T VectC1[STANDARD_SIZE] = {0x1u, 0x2u, 0x3u, 0x4u, 0x5u, 0x6u, 0x7u, 0x0u};
uint8_T VectC2[STANDARD_SIZE] = {0xDEu, 0xADu, 0xBEu, 0xEFu, 0xABu, 0xCDu, 0xEFu, 0x0u};
static void CAN_TTCAN_Tests(void);
#endif

uint32_T cnt_tt_tmp = 0U;
static uint8_T Flg_ttcan_test_TX = 0u;
static uint8_T Flg_ttcan_disable = 0u;
static uint8_T Flg_ttcan_reset_rx = 0u;
static uint8_T Flg_ttcan_reset_tx = 0u;
static uint8_T Flg_ttcan_test = 0U;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : TTCAN_Test
**
**   Description:
**    Tests selected TTCAN Channels by define.
**
******************************************************************************/
void TTCAN_Test(void) {

    if (Flg_ttcan_test == 0U) {
        /* TTCAN Config and Enable */
        //TTCAN_Config();
        
#pragma ghs startnomisra

#ifdef TEST_TTCAN
        TTCAN_Enable(MCAN_ENG_C); //MCAN_3
        TTCAN_EngineEnableReceive(MCAN_ENG_C); //MCAN_3 - M_TTCAN_0
#endif
#pragma ghs endnomisra

        Flg_ttcan_test = 1U;
        Flg_ttcan_test_TX = 1U;
    }

    if (Flg_ttcan_test_TX == 1u)
    {
#ifdef  TEST_TTCAN
        CAN_TTCAN_Tests();
#endif
    }

    if (Flg_ttcan_disable == 1u)
    {
#ifdef  TEST_TTCAN
        TTCAN_Disable(MCAN_ENG_C);
        Flg_ttcan_disable = 0u;
#endif
    }

    if (Flg_ttcan_reset_tx == 1u)
    {
#ifdef  TEST_TTCAN
        TTCAN_ResetBufferTx(MCAN_ENG_C, 0u);
        Flg_ttcan_reset_tx = 0u;
#endif
    }

    if (Flg_ttcan_reset_rx == 1u)
    {
#ifdef  TEST_TTCAN
        TTCAN_ResetBufferRx(MCAN_ENG_C, 0u, 0u);
        Flg_ttcan_reset_rx = 0u;
#endif
    }
}

/******************************************************************************
**   Function    : CAN_TTCAN_Tests
**
**   Description:
**    Tests for TTCAN.
**
******************************************************************************/
#ifdef TEST_TTCAN
static void CAN_TTCAN_Tests(void) {
    
    VectC1[7]++;

    /* First message on TT_MCAN */
    TTCAN_TxData(MCAN_ENG_C, 0U, &VectC1[0]);

    for (cnt_tt_tmp = 0U; cnt_tt_tmp < 10000U; cnt_tt_tmp++) {

    }


    /* Last message on TT_MCAN */
#ifdef CAN_CHC_BUF31_TX
#if (CAN_CHC_BUF31_TX == CAN_BUFF_ENABLE)
    VectC2[7]++;

    TTCAN_TxDataOptimized(MCAN_ENG_C, 31U, &VectC2[0], STANDARD_SIZE);

    for (cnt_tt_tmp = 0U; cnt_tt_tmp < 10000U; cnt_tt_tmp++) {

    }
#endif
#endif



#if (MCAN_3_RX_INT_LINE == CAN_RX_INT_DISABLE)
    while (TTCAN_ReceiveMsg(MCAN_ENG_C, 0U, &RxFrameC1) != 0) {

    }
#endif
}
#endif

#endif /* __TEST_TTCAN_ */
#endif /* _BUILD_CAN_ */

