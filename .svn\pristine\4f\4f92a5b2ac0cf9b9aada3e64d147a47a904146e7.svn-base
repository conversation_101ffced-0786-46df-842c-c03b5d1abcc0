/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Clock
**  Filename        :  clock.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/


#ifndef _CLOCK_H_
#define _CLOCK_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "sys.h"
//#include "irq.h"
#include "clock_cfg.h"

#pragma ghs startnomisra

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* Common constants */
#define CLOCK_SUCCESS                       0U
#define CLOCK_FAILED                        1U

/* Internal clock sources */
#define SPC5_IRC_CLK                        16000000U


/* ME_GS register bits definitions */
#define SPC5_ME_GS_SYSCLK_MASK              (15UL << 0)
#define SPC5_ME_GS_SYSCLK_IRC               (0UL << 0)
#define SPC5_ME_GS_SYSCLK_XOSC              (1UL << 0)
#define SPC5_ME_GS_SYSCLK_PLL0PHI           (2UL << 0)
#define SPC5_ME_GS_SYSCLK_PLL1PHI           (4UL << 0)


/* ME_ME register bits definitions */
#define SPC5_ME_ME_RESET                    (1UL << 0)
#define SPC5_ME_ME_SAFE                     (1UL << 2)
#define SPC5_ME_ME_DRUN                     (1UL << 3)
#define SPC5_ME_ME_RUN0                     (1UL << 4)
#define SPC5_ME_ME_RUN1                     (1UL << 5)
#define SPC5_ME_ME_RUN2                     (1UL << 6)
#define SPC5_ME_ME_RUN3                     (1UL << 7)
#define SPC5_ME_ME_HALT0                    (1UL << 8)
#define SPC5_ME_ME_STOP0                    (1UL << 10)
/**  */

/* ME_xxx_MC registers bits definitions */
#define SPC5_ME_MC_SYSCLK_MASK              (15UL << 0)
#define SPC5_ME_MC_SYSCLK(n)                (((uint32_T)(n)) << 0)
#define SPC5_ME_MC_SYSCLK_IRC               SPC5_ME_MC_SYSCLK(0)
#define SPC5_ME_MC_SYSCLK_XOSC              SPC5_ME_MC_SYSCLK(1)
#define SPC5_ME_MC_SYSCLK_PLL0PHI           SPC5_ME_MC_SYSCLK(2)
#define SPC5_ME_MC_SYSCLK_PLL1PHI           SPC5_ME_MC_SYSCLK(4)
#define SPC5_ME_MC_SYSCLK_DISABLED          SPC5_ME_MC_SYSCLK(15)
#define SPC5_ME_MC_IRCON                    (1UL << 4)
#define SPC5_ME_MC_XOSCON                   (1UL << 5)
#define SPC5_ME_MC_PLL0ON                   (1UL << 6)
#define SPC5_ME_MC_PLL1ON                   (1UL << 7)
#define SPC5_ME_MC_FLAON_MASK               (3UL << 16)
#define SPC5_ME_MC_FLAON(n)                 (((uint32_T)(n)) << 16)
#define SPC5_ME_MC_FLAON_PD                 SPC5_ME_MC_FLAON(1)
#define SPC5_ME_MC_FLAON_LP                 SPC5_ME_MC_FLAON(2)
#define SPC5_ME_MC_FLAON_NORMAL             SPC5_ME_MC_FLAON(3)
#define SPC5_ME_MC_MVRON                    (1UL << 20)
#define SPC5_ME_MC_PDO                      (1UL << 23)
#define SPC5_ME_MC_PWRLVL_MASK              (7UL << 28)
#define SPC5_ME_MC_PWRLVL(n)                (((uint32_T)(n)) << 28)
/**  */

/* ME_MCTL register bits definitions */
#define SPC5_ME_MCTL_KEY                    0x5AF0UL
#define SPC5_ME_MCTL_KEY_INV                0xA50FUL
#define SPC5_ME_MCTL_MODE_MASK              (15UL << 28)
#define SPC5_ME_MCTL_MODE(n)                (((uint32_T)(n)) << 28)
/**  */

/* ME_RUN_PCx registers bits definitions */
#define SPC5_ME_RUN_PC_SAFE                 (1UL << 2)
#define SPC5_ME_RUN_PC_DRUN                 (1UL << 3)
#define SPC5_ME_RUN_PC_RUN0                 (1UL << 4)
#define SPC5_ME_RUN_PC_RUN1                 (1UL << 5)
#define SPC5_ME_RUN_PC_RUN2                 (1UL << 6)
#define SPC5_ME_RUN_PC_RUN3                 (1UL << 7)
/**  */

/* ME_LP_PCx registers bits definitions */
#define SPC5_ME_LP_PC_HALT0                 (1UL << 8)
#define SPC5_ME_LP_PC_STOP0                 (1UL << 10)
/**  */

/* ME_PCTL registers bits definitions */
#define SPC5_ME_PCTL_RUN_MASK               (7UL << 0)
#define SPC5_ME_PCTL_RUN(n)                 (((uint8_T)(n)) << 0)
#define SPC5_ME_PCTL_LP_MASK                (7UL << 3)
#define SPC5_ME_PCTL_LP(n)                  (((uint8_T)(n)) << 3)

#define SPC5_ME_PCTL_RUN_PCO                0u
#define SPC5_ME_PCTL_RUN_PC1                1u
#define SPC5_ME_PCTL_RUN_PC2                2u
#define SPC5_ME_PCTL_RUN_PC3                3u
#define SPC5_ME_PCTL_RUN_PC4                4u
#define SPC5_ME_PCTL_RUN_PC5                5u
#define SPC5_ME_PCTL_RUN_PC6                6u
#define SPC5_ME_PCTL_RUN_PC7                7u

#define SPC5_ME_PCTL_LP_PCO                 0u
#define SPC5_ME_PCTL_LP_PC1                 1u
#define SPC5_ME_PCTL_LP_PC2                 2u
#define SPC5_ME_PCTL_LP_PC3                 3u
#define SPC5_ME_PCTL_LP_PC4                 4u
#define SPC5_ME_PCTL_LP_PC5                 5u
#define SPC5_ME_PCTL_LP_PC6                 6u
#define SPC5_ME_PCTL_LP_PC7                 7u
/**  */


//EZ, added:
#define SPC5_AC0_DC_MAX_PER_ID              4U /*max identificative of peripherals connected to AUX_CLK_0*/
#define SPC5_AC0_DC_DIV_MAXVALUE            128U
#define SPC5_AC0_DC_DIV_MINVALUE            1U

#define SPC5_AC0_DC0_PER_CLK_ID             0U
#define SPC5_AC0_DC1_SD_CLK_ID              1U
#define SPC5_AC0_DC2_SAR_CLK_ID             2U
#define SPC5_AC0_DC3_DSPI_CLK0_ID           3U
#define SPC5_AC0_DC4_DSPI_LIN_CLK1_ID       4U

#define SPC5_MC_CGM_AC0_DC_DE_MASK          0x7FFFU
#define SPC5_MC_CGM_AC0_DC_DIV_OFFSET       0x10U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_1        0x8000U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_2        0x8001U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_3        0x8002U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_4        0x8003U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_5        0x8004U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_6        0x8005U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_7        0x8006U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_8        0x8007U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_9        0x8008U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_10       0x8009U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_11       0x800AU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_12       0x800BU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_13       0x800CU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_14       0x800DU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_15       0x800EU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_16       0x800FU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_17       0x8010U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_18       0x8011U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_19       0x8012U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_20       0x8013U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_21       0x8014U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_22       0x8015U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_23       0x8016U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_24       0x8017U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_25       0x8018U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_26       0x8019U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_27       0x801AU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_28       0x801BU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_29       0x801CU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_30       0x801DU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_31       0x801EU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_32       0x801FU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_33       0x8020U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_34       0x8021U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_35       0x8022U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_36       0x8023U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_37       0x8024U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_38       0x8025U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_39       0x8026U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_40       0x8027U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_41       0x8028U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_42       0x8029U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_43       0x802AU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_44       0x802BU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_45       0x802CU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_46       0x802DU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_47       0x802EU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_48       0x802FU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_49       0x8030U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_50       0x8031U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_51       0x8032U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_52       0x8033U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_53       0x8034U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_54       0x8035U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_55       0x8036U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_56       0x8037U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_57       0x8038U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_58       0x8039U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_59       0x803AU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_60       0x803BU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_61       0x803CU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_62       0x803DU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_63       0x803EU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_64       0x803FU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_65       0x8040U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_66       0x8041U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_67       0x8042U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_68       0x8043U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_69       0x8044U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_70       0x8045U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_71       0x8046U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_72       0x8047U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_73       0x8048U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_74       0x8049U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_75       0x804AU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_76       0x804BU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_77       0x804CU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_78       0x804DU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_79       0x804EU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_80       0x804FU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_81       0x8050U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_82       0x8051U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_83       0x8052U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_84       0x8053U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_85       0x8054U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_86       0x8055U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_87       0x8056U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_88       0x8057U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_89       0x8058U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_90       0x8059U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_91       0x805AU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_92       0x805BU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_93       0x805CU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_94       0x805DU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_95       0x805EU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_96       0x805FU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_97       0x8060U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_98       0x8061U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_99       0x8062U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_100      0x8063U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_101      0x8064U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_102      0x8065U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_103      0x8066U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_104      0x8067U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_105      0x8068U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_106      0x8069U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_107      0x806AU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_108      0x806BU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_109      0x806CU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_110      0x806DU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_111      0x806EU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_112      0x806FU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_113      0x8070U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_114      0x8071U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_115      0x8072U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_116      0x8073U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_117      0x8074U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_118      0x8075U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_119      0x8076U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_120      0x8077U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_121      0x8078U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_122      0x8079U
#define SPC5_MC_CGM_AC0_DC_DIVIDER_123      0x807AU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_124      0x807BU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_125      0x807CU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_126      0x807DU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_127      0x807EU
#define SPC5_MC_CGM_AC0_DC_DIVIDER_128      0x807FU




/* SSCM_ERROR register bits definitions */
#define SPC5_SSCM_ERROR_RAE                 (1U << 0)
#define SPC5_SSCM_ERROR_PAE                 (1U << 1)


/* Run modes */
#define SPC5_RUNMODE_SAFE                   2U
#define SPC5_RUNMODE_DRUN                   3U
#define SPC5_RUNMODE_RUN0                   4U
#define SPC5_RUNMODE_RUN1                   5U
#define SPC5_RUNMODE_RUN2                   6U
#define SPC5_RUNMODE_RUN3                   7U
#define SPC5_RUNMODE_HALT0                  8U
#define SPC5_RUNMODE_STOP0                  10U


/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
#define SPC_SET_RUN_MODE_TOUT               5000u

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/


/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
typedef enum {
  SPC5_PERIPHERAL_PIT0 = 0
} peripheral_t;


/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : clockInit
**
**   Description:
**    Clock initialization.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void clockInit(void);

/******************************************************************************
**   Function    : SPCSetRunMode
**
**   Description:
**    Switches the system to the specified run mode.
**
**   Parameters :
**    [in] uint8_T mode : one of the possible run modes
**
**   Returns:
**    CLOCK_SUCCESS         - if the switch operation has been completed.
**    CLOCK_FAILED          - if the switch operation failed.
**
******************************************************************************/
uint8_T SPCSetRunMode(uint8_T mode);

/******************************************************************************
**   Function    : SPCSetPeripheralClockMode
**
**   Description:
**    Changes the clock mode of a peripheral.
**
**   Parameters :
**    [in] uint32_T n : index of the PCTL register
**    [in] uint32_T pctl : new value for the PCTL register
**
**   Returns:
**    void
**
******************************************************************************/
void SPCSetPeripheralClockMode(uint32_T n, uint32_T pctl);

/******************************************************************************
**   Function    : SPCSetPerClockMode_MiniBoot
**
**   Description:
**    Changes the clock mode of a peripheral.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SPCSetPerClockMode_MiniBoot(void);

/******************************************************************************
**   Function    : SPCSetPerClockMode_Appl
**
**   Description:
**    Changes the clock mode of a peripheral.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SPCSetPerClockMode_Appl(void);

/******************************************************************************
**   Function    : SPCGetSystemClock
**
**   Description:
**    Returns the system clock under the current run mode.
**
**   Parameters :
**    void
**
**   Returns:
**    uint32_T retVal: the system clock in Hertz.
**
******************************************************************************/
uint32_T SPCGetSystemClock(void);

/******************************************************************************
**   Function    : SPCGetPeripheralClock
**
**   Description:
**    Returns the peripheral clock.
**
**   Parameters :
**    [in] peripheral_t peripheral : identifier of the peripheral related to 
**                                   the clock to be returned.
**
**   Returns:
**    uint32_T retVal: the peripheral clock in Hertz.
**
******************************************************************************/
uint32_T SPCGetPeripheralClock(peripheral_t peripheral);

/******************************************************************************
**   Function    : SPCSetPerClkDiv_AC0
**
**   Description:
**    Configures the Auxiliary clock 0 divider. The peripherals connected to it
**    are defined by an ID.
**    NOTE: if MC_CGM_AC0_DC.DE is 0 (clock disabled) this API enables it
**          to perform MC_CGM_AC0_DC.DIV change and keeps it enabled.
**
**   Parameters :
**    uint8_T ac0PerClkID : ID of the peripheral connected to the AUX clock.
**    uint32_T ac0DcRegValue : MC_CGM_AC0_DC register value. 
**
**   Returns:
**    NO_ERROR : configuration correctly executed
**    ARG_ERROR : Input arguments outside the boundaries
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T SPCSetPerClkDiv_AC0(uint8_T ac0PerClkID, uint32_T ac0DcRegValue);

#pragma ghs endnomisra

#endif /* _CLOCK_H_ */

/****************************************************************************
 ****************************************************************************/
