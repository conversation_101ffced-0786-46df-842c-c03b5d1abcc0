/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  COMMON
**  Filename        :  events.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/
#ifndef _EVENTS_H_
#define _EVENTS_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "TasksDefs.h"

#include "GTM_HostInterface.h"

#include "Mcan_out.h"
#include "TTcan_out.h"
#ifdef _BUILD_STM_
#include "stm_out.h"
#endif
/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */
/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/ 


void FMPLL_LOCF_ISR(void);

void FMPLL_LOLF_ISR(void);


#endif //_EVENTS_H_

/****************************************************************************
 ****************************************************************************/

