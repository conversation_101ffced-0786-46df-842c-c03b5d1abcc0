/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonKnockInt.c
 **  File Creation Date: 06-Sep-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonKnockInt
 **  Model Description:  This model performs the integral of the power spectrum of ion signal inside three frequency bands.
   Frequency bands are calculated according engine speed and ion sampling time.
   IonKnockInt is triggered by End Of Acquisition event.
 **  Model Version:      1.1416
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Mon Sep 06 14:54:45 2021
 **
 **  Last Saved Modification:  RoccaG - Mon Sep 06 14:53:39 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonKnockInt_out.h"
#include "IonKnockInt_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/Scheduler' */
#define IonKn_event_IonKnockInt_PowerOn (0)
#define IonKnockI_event_IonKnockInt_EOA (1)
#define IonKnock_event_IonKnockInt_10ms (2)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKETHPERCKNOCK_dim             4U                        /* Referenced by:
                                                                  * '<S17>/Constant7'
                                                                  * '<S17>/Constant8'
                                                                  * '<S32>/Constant12'
                                                                  */

/* BKETHPERCKNOCK breakpoint dimension. */
#define BKRPMKNOCK4F_dim               6U                        /* Referenced by:
                                                                  * '<S17>/Constant3'
                                                                  * '<S17>/Constant4'
                                                                  * '<S32>/Constant2'
                                                                  * '<S33>/Constant2'
                                                                  */

/* BKRPMKNOCK4F breakpoint dimension. */
#define FFT_SAMPLE_D2_M1               63U                       /* Referenced by:
                                                                  * '<S21>/Constant1'
                                                                  * '<S22>/Constant1'
                                                                  * '<S37>/Constant1'
                                                                  * '<S38>/Constant1'
                                                                  * '<S43>/Constant1'
                                                                  * '<S44>/Constant1'
                                                                  */

/* Number of ion samples used for FFT, divided 2, minus 1. */
#define ID_VER_IONKNOCKINT_DEF         11416U                    /* Referenced by: '<S5>/Constant18' */

/* Model Version. */
#define MAX_MAGNITUDE                  (4294967295LL)            /* Referenced by: '<S15>/Constant' */

/* Maximum value for magnitude (Lsb 2^-10) */
#define MAX_MAGNITUDE_LR               (1073741824LL)            /* Referenced by:
                                                                  * '<S15>/Constant1'
                                                                  * '<S15>/Constant2'
                                                                  */

/* Maximum value for magnitude (Lsb 2^-8) */
#define MAX_UINT_16                    65535U                    /* Referenced by:
                                                                  * '<S10>/Constant'
                                                                  * '<S10>/Constant1'
                                                                  * '<S10>/Constant2'
                                                                  */

/* Max value for unit16 type. */
#define MAX_UINT_32                    4294967295U               /* Referenced by:
                                                                  * '<S11>/Constant'
                                                                  * '<S11>/Constant11'
                                                                  * '<S14>/Constant'
                                                                  */

/* Max value for unit32 type. */
#define MICRO_TO_SECONDS               1000000U                  /* Referenced by: '<S31>/Constant5' */

/* Conversion factor from us to s. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONKNOCKINT_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

#if(FFT_SAMPLE != 128)
#error This code was generated with a different sample number!
#endif

#if(ION_DT_MIN != 8)
#error This code was generated with a different minimum value for ion sampling time!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKETHPERCKNOCK[5] = { 0U, 10U, 20U,
  50U, 85U } ;                         /* Referenced by: '<S32>/Constant13' */

/* EthPerc bkp */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMKNOCK4F[7] = { 2000U, 3100U,
  4500U, 5000U, 5500U, 6500U, 7500U } ;/* Referenced by:
                                        * '<S32>/Constant'
                                        * '<S33>/Constant'
                                        * '<S33>/Constant3'
                                        * '<S33>/Constant4'
                                        */

/* Rpm breakpoint vector for FFT freq. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T POWNORM = 10U;/* Referenced by: '<S9>/Constant4' */

/* Knock power rescaling factor */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TB1STFRQWEIGHT[35] = { 307U, 0U, 0U,
  205U, 205U, 102U, 0U, 307U, 0U, 0U, 205U, 205U, 102U, 0U, 307U, 0U, 0U, 205U,
  205U, 102U, 0U, 307U, 0U, 0U, 205U, 205U, 102U, 0U, 307U, 0U, 0U, 205U, 205U,
  102U, 0U } ;                         /* Referenced by: '<S17>/Constant1' */

/* FFT Power 1st band weigth */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TB2NDFRQWEIGHT[35] = { 717U, 1024U,
  1024U, 819U, 819U, 922U, 1024U, 717U, 1024U, 1024U, 819U, 819U, 922U, 1024U,
  717U, 1024U, 1024U, 819U, 819U, 922U, 1024U, 717U, 1024U, 1024U, 819U, 819U,
  922U, 1024U, 717U, 1024U, 1024U, 819U, 819U, 922U, 1024U } ;/* Referenced by: '<S17>/Constant2' */

/* FFT Power 2nd band weigth */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VT1STFRQSTART[7] = { 4500U, 4500U,
  4000U, 5250U, 5500U, 5000U, 4500U } ;/* Referenced by: '<S8>/Constant' */

/* First band starting frequency */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VT1STFRQSTOP[7] = { 7500U, 7500U,
  6000U, 7250U, 7500U, 7000U, 7500U } ;/* Referenced by: '<S8>/Constant1' */

/* First band ending frequency */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VT2NDFRQSTART[7] = { 11000U, 10000U,
  10000U, 10800U, 10500U, 11000U, 10000U } ;/* Referenced by: '<S8>/Constant2' */

/* Second band starting frequency */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VT2NDFRQSTOP[7] = { 13600U, 20000U,
  20000U, 14200U, 14500U, 14000U, 20000U } ;/* Referenced by: '<S8>/Constant3' */

/* Second band ending frequency */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VT3NDFRQSTART[7] = { 11000U, 10000U,
  10000U, 10800U, 10500U, 11000U, 10000U } ;/* Referenced by: '<S8>/Constant4' */

/* Third band starting frequency */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VT3NDFRQSTOP[7] = { 13600U, 20000U,
  20000U, 14200U, 14500U, 14000U, 20000U } ;/* Referenced by: '<S8>/Constant5' */

/* Third band ending frequency */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T BaseFreq;                     /* '<S6>/Merge13' */

/* FFT Base frequency */
uint32_T FftMag2Int1;                  /* '<S6>/Merge1' */

/* FFT first band magnitude^2 sum */
uint32_T FftMag2Int2;                  /* '<S6>/Merge2' */

/* FFT second band magnitude^2 sum */
uint32_T FftMag2Int3;                  /* '<S6>/Merge3' */

/* FFT third band magnitude^2 sum */
uint32_T FftPeak[64];                  /* '<S6>/Merge' */

/* Fft Magnitude^2 */
uint16_T IonKnockEthPercIndex;         /* '<S6>/Merge8' */

/* Index for interpolation on BKETHPERCKNOCK table */
uint16_T IonKnockEthPercRatio;         /* '<S6>/Merge9' */

/* Ratio for interpolation on BKETHPERCKNOCK table */
uint32_T KnPowNormFft;                 /* '<S6>/Merge7' */

/* FFT Knocking power */
uint16_T VtFftMag2Int1[8];             /* '<S6>/Merge4' */

/* FFT first band magnitude^2 sum per cyl */
uint16_T VtFftMag2Int2[8];             /* '<S6>/Merge5' */

/* FFT second band magnitude^2 sum */
uint16_T VtFftMag2Int3[8];             /* '<S6>/Merge6' */

/* FFT third band magnitude^2 sum */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T IdFrqRange1;/* '<S6>/Merge16' */

/* First band range frequency index */
STATIC_TEST_POINT uint16_T IdFrqRange2;/* '<S6>/Merge18' */

/* Second band range frequency index */
STATIC_TEST_POINT uint16_T IdFrqRange3;/* '<S6>/Merge12' */

/* Third band range frequency index */
STATIC_TEST_POINT uint16_T IdFrqStart1;/* '<S6>/Merge15' */

/* First band starting frequency index */
STATIC_TEST_POINT uint16_T IdFrqStart2;/* '<S6>/Merge17' */

/* Second band starting frequency index */
STATIC_TEST_POINT uint16_T IdFrqStart3;/* '<S6>/Merge19' */

/* Third band starting frequency index */
STATIC_TEST_POINT uint32_T IdVer_IonKnockInt;/* '<S5>/Constant18' */

/* Model Version */
STATIC_TEST_POINT uint32_T IonSampFreq;/* '<S6>/Merge14' */

/* Ion sampling frequency */
STATIC_TEST_POINT uint16_T Weight1stBand;/* '<S6>/Merge10' */

/* First band gain */
STATIC_TEST_POINT uint16_T Weight2ndBand;/* '<S6>/Merge11' */

/* Second band gain */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void IonKno_chartstep_c4_IonKnockInt(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<S13>/Calculate_Integral'
 * Block description for: '<S13>/Calculate_Integral'
 *   This block sums the normalized square magnitude to the old value of the power spectrum integral.
 *   It is called more time inside the for_loop implemented by "Range_Management" chart in order to perfom the power spectrum integral for each frequency band.
 *   The chart stores the old value and decides when trigger this calculation and which band is under evaluation.
 */
void IonKnockInt_Calculate_Integral(uint32_T rtu_NormGain, uint32_T
  rtu_Magnitude, uint32_T rtu_oldIntegral, uint32_T *rty_newIntegral)
{
  uint32_T rtb_Add1;
  uint32_T rtb_Divide_o;

  /* Sum: '<S14>/Add1' incorporates:
   *  Constant: '<S14>/Constant'
   */
  rtb_Add1 = MAX_UINT_32 - rtu_oldIntegral;

  /* Product: '<S14>/Divide' */
  rtb_Divide_o = rtu_Magnitude / rtu_NormGain;

  /* MinMax: '<S14>/MinMax' */
  if (rtb_Add1 < rtb_Divide_o) {
    rtb_Divide_o = rtb_Add1;
  }

  /* End of MinMax: '<S14>/MinMax' */

  /* Sum: '<S14>/Add' */
  *rty_newIntegral = rtu_oldIntegral + rtb_Divide_o;
}

/* Function for Chart: '<Root>/Scheduler' */
static void IonKno_chartstep_c4_IonKnockInt(const int32_T *sfEvent)
{
  /* local block i/o variables */
  uint32_T rtb_BINARYSEARCH_U16_o1;
  uint32_T rtb_BINARYSEARCH_U16_o2;
  uint16_T rtb_Look2D_IR_U16;
  uint16_T rtb_Look2D_IR_U16_d;
  uint16_T rtb_INTERPOLATE_U16_U16;
  uint16_T rtb_INTERPOLATE_U16_U16_c;
  uint16_T rtb_INTERPOLATE_U16_U16_d;
  uint16_T rtb_INTERPOLATE_U16_U16_k;
  uint16_T rtb_INTERPOLATE_U16_U16_ki;
  uint16_T rtb_INTERPOLATE_U16_U16_dt;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_a;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_k;
  uint64_T rtb_Product;
  uint64_T rtb_Conversion1;
  int64_T rtb_Product_k;
  uint64_T rtb_Product1;
  uint64_T rtb_Conversion2;
  int64_T rtb_Product1_l;
  uint16_T rtb_Selector;
  uint16_T rtb_Selector1;
  uint16_T rtb_Rpm_value_left;
  uint16_T rtb_Rpm_value_right;
  uint32_T Conversion;
  uint32_T Conversion1;
  uint32_T Add;
  uint8_T tmp;

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */
  /* Chart: '<Root>/Scheduler' incorporates:
   *  Constant: '<S5>/Constant18'
   *  Inport: '<Root>/IonKnockEnabled'
   *  Inport: '<Root>/Rpm'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */
  /* During: Scheduler */
  /* This char schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled. */
  /* Entry Internal: Scheduler */
  /* Transition: '<S2>:39' */
  switch (*sfEvent) {
   case IonKn_event_IonKnockInt_PowerOn:
    /* Outputs for Function Call SubSystem: '<S1>/IntegralPowerOn'
     *
     * Block description for '<S1>/IntegralPowerOn':
     *  Outputs reset. Requirements are linked to previous Stateflow.
     */
    /* SignalConversion generated from: '<S5>/FftPeak' */
    /* Transition: '<S2>:8' */
    /* Transition: '<S2>:19'
     * Requirements for Transition: '<S2>:19':
     *  1. EISB_FCA6CYL_SW_REQ_1741: Software shall set to 0 each output produced to define frequency bands after ECU power on (ECU_SW_Requirements#4304)
     */
    /* Event: '<S2>:36' */
    memset((&(FftPeak[0])), 0, (sizeof(uint32_T)) << 6U);

    /* SignalConversion generated from: '<S5>/VtFftMag2Int1' */
    memset((&(VtFftMag2Int1[0])), 0, (sizeof(uint16_T)) << 3U);

    /* SignalConversion generated from: '<S5>/VtFftMag2Int2' */
    memset((&(VtFftMag2Int2[0])), 0, (sizeof(uint16_T)) << 3U);

    /* SignalConversion generated from: '<S5>/VtFftMag2Int3' */
    memset((&(VtFftMag2Int3[0])), 0, (sizeof(uint16_T)) << 3U);

    /* SignalConversion generated from: '<S5>/FftMag2Int1' incorporates:
     *  Constant: '<S5>/Constant10'
     */
    FftMag2Int1 = 0U;

    /* SignalConversion generated from: '<S5>/FftMag2Int2' incorporates:
     *  Constant: '<S5>/Constant11'
     */
    FftMag2Int2 = 0U;

    /* SignalConversion generated from: '<S5>/FftMag2Int3' incorporates:
     *  Constant: '<S5>/Constant12'
     */
    FftMag2Int3 = 0U;

    /* SignalConversion generated from: '<S5>/KnPowNormFft' incorporates:
     *  Constant: '<S5>/Constant23'
     */
    KnPowNormFft = 0U;

    /* SignalConversion generated from: '<S5>/IonKnockEthPercIndex' incorporates:
     *  Constant: '<S5>/Constant24'
     */
    IonKnockEthPercIndex = 0U;

    /* SignalConversion generated from: '<S5>/IonKnockEthPercRatio' incorporates:
     *  Constant: '<S5>/Constant25'
     */
    IonKnockEthPercRatio = 0U;

    /* SignalConversion generated from: '<S5>/Weight1stBand' incorporates:
     *  Constant: '<S5>/Constant5'
     */
    Weight1stBand = 0U;

    /* SignalConversion generated from: '<S5>/Weight2ndBand' incorporates:
     *  Constant: '<S5>/Constant6'
     */
    Weight2ndBand = 0U;

    /* SignalConversion generated from: '<S5>/IonSampFreq' incorporates:
     *  Constant: '<S5>/Constant7'
     */
    IonSampFreq = 0U;

    /* Product: '<S22>/Divide' incorporates:
     *  Constant: '<S5>/Constant8'
     *  SignalConversion generated from: '<S5>/IdFrqStart1'
     *
     * Block requirements for '<S22>/Divide':
     *  1. EISB_FCA6CYL_SW_REQ_1129: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1159)
     */
    IdFrqStart1 = 0U;

    /* SignalConversion generated from: '<S5>/IdFrqRange1' incorporates:
     *  Constant: '<S5>/Constant9'
     */
    IdFrqRange1 = 0U;

    /* Product: '<S38>/Divide' incorporates:
     *  Constant: '<S5>/Constant13'
     *  SignalConversion generated from: '<S5>/IdFrqStart2'
     *
     * Block requirements for '<S38>/Divide':
     *  1. EISB_FCA6CYL_SW_REQ_1132: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1161)
     */
    IdFrqStart2 = 0U;

    /* SignalConversion generated from: '<S5>/IdFrqRange2' incorporates:
     *  Constant: '<S5>/Constant14'
     */
    IdFrqRange2 = 0U;

    /* Product: '<S44>/Divide' incorporates:
     *  Constant: '<S5>/Constant15'
     *  SignalConversion generated from: '<S5>/IdFrqStart3'
     *
     * Block requirements for '<S44>/Divide':
     *  1. EISB_FCA6CYL_SW_REQ_1134: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1163)
     */
    IdFrqStart3 = 0U;

    /* SignalConversion generated from: '<S5>/IdFrqRange3' incorporates:
     *  Constant: '<S5>/Constant16'
     */
    IdFrqRange3 = 0U;

    /* SignalConversion generated from: '<S5>/BaseFreq' incorporates:
     *  Constant: '<S5>/Constant17'
     */
    BaseFreq = 0U;
    IdVer_IonKnockInt = ID_VER_IONKNOCKINT_DEF;

    /* End of Outputs for SubSystem: '<S1>/IntegralPowerOn' */
    break;

   case IonKnock_event_IonKnockInt_10ms:
    /* Transition: '<S2>:41' */
    /* Transition: '<S2>:24' */
    if (((int32_T)Rpm) == 0) {
      /* Outputs for Function Call SubSystem: '<S1>/Integral10ms'
       *
       * Block description for '<S1>/Integral10ms':
       *  Reset integral for third band (noise detection) if engine speed is 0.
       */
      /* SignalConversion generated from: '<S3>/VtFftMag2Int3' */
      /* Transition: '<S2>:43' */
      /* Transition: '<S2>:25' */
      /* Event: '<S2>:35' */
      memset((&(VtFftMag2Int3[0])), 0, (sizeof(uint16_T)) << 3U);

      /* End of Outputs for SubSystem: '<S1>/Integral10ms' */
    } else {
      /* Transition: '<S2>:45' */
    }
    break;

   default:
    /* Transition: '<S2>:10' */
    /*  IonKnockInt_EOA */
    if (((int32_T)IonKnockEnabled) != 0) {
      /* Outputs for Function Call SubSystem: '<S1>/IntegralEOA'
       *
       * Block description for '<S1>/IntegralEOA':
       *  This block performs the integral of power spectrum inside three
       *  frequency bands.
       */
      /* Switch: '<S9>/Switch' incorporates:
       *  Constant: '<S9>/Constant'
       *  Constant: '<S9>/Constant1'
       *  Constant: '<S9>/Constant3'
       *  Inport: '<Root>/NSampIonPower'
       *  Logic: '<S9>/LogicalOperator'
       *  RelationalOperator: '<S9>/RelationalOperator'
       *  RelationalOperator: '<S9>/RelationalOperator1'
       */
      /* Transition: '<S2>:12' */
      /* Transition: '<S2>:15' */
      /* Event: '<S2>:37' */
      if ((NSampIonPower > FFT_SAMPLE) || (((int32_T)NSampIonPower) == 0)) {
        rtb_Rpm_value_left = FFT_SAMPLE;
      } else {
        rtb_Rpm_value_left = NSampIonPower;
      }

      /* End of Switch: '<S9>/Switch' */

      /* MinMax: '<S9>/MinMax' incorporates:
       *  Constant: '<S9>/Constant4'
       */
      if (((int32_T)POWNORM) > 1) {
        rtb_Rpm_value_right = POWNORM;
      } else {
        rtb_Rpm_value_right = 1U;
      }

      /* End of MinMax: '<S9>/MinMax' */

      /* DataTypeConversion: '<S9>/Conversion' incorporates:
       *  Constant: '<S9>/Constant2'
       *  Product: '<S9>/Product'
       *  Product: '<S9>/Product1'
       *
       * Block requirements for '<S9>/Product1':
       *  1. EISB_FCA6CYL_SW_REQ_1167: In order to obtain the power on FFT length, software shall multipl... (ECU_SW_Requirements#1186)
       */
      Conversion = ((((uint32_T)((uint16_T)(((uint32_T)rtb_Rpm_value_left) *
        ((uint32_T)FFT_SAMPLE_DIV2)))) * ((uint32_T)rtb_Rpm_value_right)) >>
                    ((uint64_T)6));

      /* S-Function (ELDOR_BINARYSEARCH_U16): '<S36>/BINARYSEARCH_U16' incorporates:
       *  Constant: '<S33>/Constant'
       *  Constant: '<S33>/Constant2'
       */
      BINARYSEARCH_U16( &rtb_BINARYSEARCH_U16_o1, &rtb_BINARYSEARCH_U16_o2, Rpm,
                       &BKRPMKNOCK4F[0], ((uint8_T)BKRPMKNOCK4F_dim));

      /* Selector: '<S22>/Selector' incorporates:
       *  Constant: '<S8>/Constant'
       */
      rtb_Selector = VT1STFRQSTART[(int32_T)rtb_BINARYSEARCH_U16_o1];

      /* Selector: '<S22>/Selector1' incorporates:
       *  Constant: '<S8>/Constant'
       */
      rtb_Selector1 = VT1STFRQSTART[(int32_T)rtb_BINARYSEARCH_U16_o2];

      /* Selector: '<S33>/Selector2' incorporates:
       *  Constant: '<S33>/Constant3'
       */
      rtb_Rpm_value_left = BKRPMKNOCK4F[(int32_T)rtb_BINARYSEARCH_U16_o1];

      /* Selector: '<S33>/Selector3' incorporates:
       *  Constant: '<S33>/Constant4'
       */
      rtb_Rpm_value_right = BKRPMKNOCK4F[(int32_T)rtb_BINARYSEARCH_U16_o2];

      /* S-Function (INTERPOLATE_U16_U16): '<S26>/INTERPOLATE_U16_U16' */
      INTERPOLATE_U16_U16( &rtb_INTERPOLATE_U16_U16, rtb_Selector, rtb_Selector1,
                          Rpm, rtb_Rpm_value_left, rtb_Rpm_value_right);

      /* MinMax: '<S31>/MinMax' incorporates:
       *  Constant: '<S31>/Constant6'
       *  Inport: '<Root>/IonDTEOA'
       */
      if (IonDTEOA > ION_DT_MIN) {
        tmp = IonDTEOA;
      } else {
        tmp = ION_DT_MIN;
      }

      /* End of MinMax: '<S31>/MinMax' */

      /* Product: '<S31>/Divide1' incorporates:
       *  Constant: '<S31>/Constant5'
       */
      IonSampFreq = MICRO_TO_SECONDS / ((uint32_T)tmp);

      /* Product: '<S31>/Divide' incorporates:
       *  Constant: '<S31>/Constant1'
       *
       * Block requirements for '<S31>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1130: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1158)
       */
      BaseFreq = (uint16_T)(IonSampFreq / ((uint32_T)FFT_SAMPLE));

      /* Product: '<S22>/Divide'
       *
       * Block requirements for '<S22>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1129: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1159)
       */
      IdFrqStart1 = (uint16_T)(((uint32_T)rtb_INTERPOLATE_U16_U16) / ((uint32_T)
        BaseFreq));

      /* MinMax: '<S22>/MinMax' incorporates:
       *  Constant: '<S22>/Constant1'
       */
      if (IdFrqStart1 >= ((uint16_T)FFT_SAMPLE_D2_M1)) {
        /* Product: '<S22>/Divide'
         *
         * Block requirements for '<S22>/Divide':
         *  1. EISB_FCA6CYL_SW_REQ_1129: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1159)
         */
        IdFrqStart1 = ((uint16_T)FFT_SAMPLE_D2_M1);
      }

      /* Selector: '<S21>/Selector' incorporates:
       *  Constant: '<S8>/Constant1'
       */
      rtb_Selector = VT1STFRQSTOP[(int32_T)rtb_BINARYSEARCH_U16_o1];

      /* Selector: '<S21>/Selector1' incorporates:
       *  Constant: '<S8>/Constant1'
       */
      rtb_Selector1 = VT1STFRQSTOP[(int32_T)rtb_BINARYSEARCH_U16_o2];

      /* S-Function (INTERPOLATE_U16_U16): '<S23>/INTERPOLATE_U16_U16' */
      INTERPOLATE_U16_U16( &rtb_INTERPOLATE_U16_U16_c, rtb_Selector,
                          rtb_Selector1, Rpm, rtb_Rpm_value_left,
                          rtb_Rpm_value_right);

      /* Product: '<S21>/Divide'
       *
       * Block requirements for '<S21>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1131: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1160)
       */
      rtb_Selector = (uint16_T)(((uint32_T)rtb_INTERPOLATE_U16_U16_c) /
        ((uint32_T)BaseFreq));

      /* MinMax: '<S21>/MinMax' incorporates:
       *  Constant: '<S21>/Constant1'
       */
      if (rtb_Selector >= ((uint16_T)FFT_SAMPLE_D2_M1)) {
        rtb_Selector = ((uint16_T)FFT_SAMPLE_D2_M1);
      }

      /* If: '<S21>/If' incorporates:
       *  MinMax: '<S21>/MinMax'
       *  MinMax: '<S22>/MinMax'
       *  RelationalOperator: '<S21>/RelationalOperator'
       */
      if (rtb_Selector <= IdFrqStart1) {
        /* Outputs for IfAction SubSystem: '<S21>/IfActionSubsystem1' incorporates:
         *  ActionPort: '<S25>/Action Port'
         *
         * Block description for '<S21>/IfActionSubsystem1':
         *  This block reset the length of the frequency band in case of error.
         */
        /* SignalConversion generated from: '<S25>/IdFrqRange' incorporates:
         *  Constant: '<S25>/Constant'
         */
        IdFrqRange1 = 0U;

        /* End of Outputs for SubSystem: '<S21>/IfActionSubsystem1' */
      } else {
        /* Outputs for IfAction SubSystem: '<S21>/IfActionSubsystem' incorporates:
         *  ActionPort: '<S24>/Action Port'
         *
         * Block description for '<S21>/IfActionSubsystem':
         *  This block calculates the length of the FFT frequency band.
         */
        /* Sum: '<S24>/Add' */
        IdFrqRange1 = (uint16_T)(((uint32_T)rtb_Selector) - ((uint32_T)
          IdFrqStart1));

        /* End of Outputs for SubSystem: '<S21>/IfActionSubsystem' */
      }

      /* End of If: '<S21>/If' */

      /* Selector: '<S38>/Selector' incorporates:
       *  Constant: '<S8>/Constant2'
       */
      rtb_Selector = VT2NDFRQSTART[(int32_T)rtb_BINARYSEARCH_U16_o1];

      /* Selector: '<S38>/Selector1' incorporates:
       *  Constant: '<S8>/Constant2'
       */
      rtb_Selector1 = VT2NDFRQSTART[(int32_T)rtb_BINARYSEARCH_U16_o2];

      /* S-Function (INTERPOLATE_U16_U16): '<S42>/INTERPOLATE_U16_U16' */
      INTERPOLATE_U16_U16( &rtb_INTERPOLATE_U16_U16_d, rtb_Selector,
                          rtb_Selector1, Rpm, rtb_Rpm_value_left,
                          rtb_Rpm_value_right);

      /* Product: '<S38>/Divide'
       *
       * Block requirements for '<S38>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1132: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1161)
       */
      IdFrqStart2 = (uint16_T)(((uint32_T)rtb_INTERPOLATE_U16_U16_d) /
        ((uint32_T)BaseFreq));

      /* MinMax: '<S38>/MinMax' incorporates:
       *  Constant: '<S38>/Constant1'
       */
      if (IdFrqStart2 >= ((uint16_T)FFT_SAMPLE_D2_M1)) {
        /* Product: '<S38>/Divide'
         *
         * Block requirements for '<S38>/Divide':
         *  1. EISB_FCA6CYL_SW_REQ_1132: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1161)
         */
        IdFrqStart2 = ((uint16_T)FFT_SAMPLE_D2_M1);
      }

      /* Selector: '<S37>/Selector' incorporates:
       *  Constant: '<S8>/Constant3'
       */
      rtb_Selector = VT2NDFRQSTOP[(int32_T)rtb_BINARYSEARCH_U16_o1];

      /* Selector: '<S37>/Selector1' incorporates:
       *  Constant: '<S8>/Constant3'
       */
      rtb_Selector1 = VT2NDFRQSTOP[(int32_T)rtb_BINARYSEARCH_U16_o2];

      /* S-Function (INTERPOLATE_U16_U16): '<S39>/INTERPOLATE_U16_U16' */
      INTERPOLATE_U16_U16( &rtb_INTERPOLATE_U16_U16_k, rtb_Selector,
                          rtb_Selector1, Rpm, rtb_Rpm_value_left,
                          rtb_Rpm_value_right);

      /* Product: '<S37>/Divide'
       *
       * Block requirements for '<S37>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1133: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1162)
       */
      rtb_Selector = (uint16_T)(((uint32_T)rtb_INTERPOLATE_U16_U16_k) /
        ((uint32_T)BaseFreq));

      /* MinMax: '<S37>/MinMax' incorporates:
       *  Constant: '<S37>/Constant1'
       */
      if (rtb_Selector >= ((uint16_T)FFT_SAMPLE_D2_M1)) {
        rtb_Selector = ((uint16_T)FFT_SAMPLE_D2_M1);
      }

      /* If: '<S37>/If' incorporates:
       *  MinMax: '<S37>/MinMax'
       *  MinMax: '<S38>/MinMax'
       *  RelationalOperator: '<S37>/RelationalOperator'
       */
      if (rtb_Selector <= IdFrqStart2) {
        /* Outputs for IfAction SubSystem: '<S37>/IfActionSubsystem1' incorporates:
         *  ActionPort: '<S41>/Action Port'
         *
         * Block description for '<S37>/IfActionSubsystem1':
         *  This block reset the length of the frequency band in case of error.
         */
        /* SignalConversion generated from: '<S41>/IdFrqRange' incorporates:
         *  Constant: '<S41>/Constant'
         */
        IdFrqRange2 = 0U;

        /* End of Outputs for SubSystem: '<S37>/IfActionSubsystem1' */
      } else {
        /* Outputs for IfAction SubSystem: '<S37>/IfActionSubsystem' incorporates:
         *  ActionPort: '<S40>/Action Port'
         *
         * Block description for '<S37>/IfActionSubsystem':
         *  This block calculates the length of the FFT frequency band.
         */
        /* Sum: '<S40>/Add' */
        IdFrqRange2 = (uint16_T)(((uint32_T)rtb_Selector) - ((uint32_T)
          IdFrqStart2));

        /* End of Outputs for SubSystem: '<S37>/IfActionSubsystem' */
      }

      /* End of If: '<S37>/If' */

      /* Selector: '<S44>/Selector' incorporates:
       *  Constant: '<S8>/Constant4'
       */
      rtb_Selector = VT3NDFRQSTART[(int32_T)rtb_BINARYSEARCH_U16_o1];

      /* Selector: '<S44>/Selector1' incorporates:
       *  Constant: '<S8>/Constant4'
       */
      rtb_Selector1 = VT3NDFRQSTART[(int32_T)rtb_BINARYSEARCH_U16_o2];

      /* S-Function (INTERPOLATE_U16_U16): '<S48>/INTERPOLATE_U16_U16' */
      INTERPOLATE_U16_U16( &rtb_INTERPOLATE_U16_U16_ki, rtb_Selector,
                          rtb_Selector1, Rpm, rtb_Rpm_value_left,
                          rtb_Rpm_value_right);

      /* Product: '<S44>/Divide'
       *
       * Block requirements for '<S44>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1134: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1163)
       */
      IdFrqStart3 = (uint16_T)(((uint32_T)rtb_INTERPOLATE_U16_U16_ki) /
        ((uint32_T)BaseFreq));

      /* MinMax: '<S44>/MinMax' incorporates:
       *  Constant: '<S44>/Constant1'
       */
      if (IdFrqStart3 >= ((uint16_T)FFT_SAMPLE_D2_M1)) {
        /* Product: '<S44>/Divide'
         *
         * Block requirements for '<S44>/Divide':
         *  1. EISB_FCA6CYL_SW_REQ_1134: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1163)
         */
        IdFrqStart3 = ((uint16_T)FFT_SAMPLE_D2_M1);
      }

      /* Selector: '<S43>/Selector' incorporates:
       *  Constant: '<S8>/Constant5'
       */
      rtb_Selector = VT3NDFRQSTOP[(int32_T)rtb_BINARYSEARCH_U16_o1];

      /* Selector: '<S43>/Selector1' incorporates:
       *  Constant: '<S8>/Constant5'
       */
      rtb_Selector1 = VT3NDFRQSTOP[(int32_T)rtb_BINARYSEARCH_U16_o2];

      /* S-Function (INTERPOLATE_U16_U16): '<S45>/INTERPOLATE_U16_U16' */
      INTERPOLATE_U16_U16( &rtb_INTERPOLATE_U16_U16_dt, rtb_Selector,
                          rtb_Selector1, Rpm, rtb_Rpm_value_left,
                          rtb_Rpm_value_right);

      /* Product: '<S43>/Divide'
       *
       * Block requirements for '<S43>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1135: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1164)
       */
      rtb_Selector = (uint16_T)(((uint32_T)rtb_INTERPOLATE_U16_U16_dt) /
        ((uint32_T)BaseFreq));

      /* MinMax: '<S43>/MinMax' incorporates:
       *  Constant: '<S43>/Constant1'
       */
      if (rtb_Selector >= ((uint16_T)FFT_SAMPLE_D2_M1)) {
        rtb_Selector = ((uint16_T)FFT_SAMPLE_D2_M1);
      }

      /* If: '<S43>/If' incorporates:
       *  MinMax: '<S43>/MinMax'
       *  MinMax: '<S44>/MinMax'
       *  RelationalOperator: '<S43>/RelationalOperator'
       */
      if (rtb_Selector <= IdFrqStart3) {
        /* Outputs for IfAction SubSystem: '<S43>/IfActionSubsystem1' incorporates:
         *  ActionPort: '<S47>/Action Port'
         *
         * Block description for '<S43>/IfActionSubsystem1':
         *  This block reset the length of the frequency band in case of error.
         */
        /* SignalConversion generated from: '<S47>/IdFrqRange' incorporates:
         *  Constant: '<S47>/Constant'
         */
        IdFrqRange3 = 0U;

        /* End of Outputs for SubSystem: '<S43>/IfActionSubsystem1' */
      } else {
        /* Outputs for IfAction SubSystem: '<S43>/IfActionSubsystem' incorporates:
         *  ActionPort: '<S46>/Action Port'
         *
         * Block description for '<S43>/IfActionSubsystem':
         *  This block calculates the length of the FFT frequency band.
         */
        /* Sum: '<S46>/Add' */
        IdFrqRange3 = (uint16_T)(((uint32_T)rtb_Selector) - ((uint32_T)
          IdFrqStart3));

        /* End of Outputs for SubSystem: '<S43>/IfActionSubsystem' */
      }

      /* End of If: '<S43>/If' */

      /* S-Function (PreLookUpIdSearch_U16): '<S34>/PreLookUpIdSearch_U16' incorporates:
       *  Constant: '<S32>/Constant'
       *  Constant: '<S32>/Constant2'
       */
      PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                            &rtb_PreLookUpIdSearch_U16_o2, Rpm, &BKRPMKNOCK4F[0],
                            ((uint8_T)BKRPMKNOCK4F_dim));

      /* DataTypeConversion: '<S32>/Data Type Conversion' incorporates:
       *  Inport: '<Root>/EthPerc'
       */
      rtb_Rpm_value_left = (uint16_T)EthPerc;

      /* S-Function (PreLookUpIdSearch_U16): '<S35>/PreLookUpIdSearch_U16' incorporates:
       *  Constant: '<S32>/Constant12'
       *  Constant: '<S32>/Constant13'
       */
      PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_a,
                            &rtb_PreLookUpIdSearch_U16_o2_k, rtb_Rpm_value_left,
                            &BKETHPERCKNOCK[0], ((uint8_T)BKETHPERCKNOCK_dim));

      /* S-Function (Look2D_IR_U16): '<S28>/Look2D_IR_U16' incorporates:
       *  Constant: '<S17>/Constant1'
       *  Constant: '<S17>/Constant4'
       *  Constant: '<S17>/Constant8'
       *
       * Block requirements for '<S17>/Constant1':
       *  1. EISB_FCA6CYL_SW_REQ_1165: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1190)
       */
      Look2D_IR_U16( &rtb_Look2D_IR_U16, &TB1STFRQWEIGHT[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKRPMKNOCK4F_dim), rtb_PreLookUpIdSearch_U16_o1_a,
                    rtb_PreLookUpIdSearch_U16_o2_k, ((uint8_T)BKETHPERCKNOCK_dim));

      /* S-Function (Look2D_IR_U16): '<S27>/Look2D_IR_U16' incorporates:
       *  Constant: '<S17>/Constant2'
       *  Constant: '<S17>/Constant3'
       *  Constant: '<S17>/Constant7'
       *
       * Block requirements for '<S17>/Constant2':
       *  1. EISB_FCA6CYL_SW_REQ_1165: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1190)
       */
      Look2D_IR_U16( &rtb_Look2D_IR_U16_d, &TB2NDFRQWEIGHT[0],
                    rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                    ((uint8_T)BKRPMKNOCK4F_dim), rtb_PreLookUpIdSearch_U16_o1_a,
                    rtb_PreLookUpIdSearch_U16_o2_k, ((uint8_T)BKETHPERCKNOCK_dim));

      /* Outputs for Atomic SubSystem: '<S4>/Bands_Integral'
       *
       * Block description for '<S4>/Bands_Integral':
       *  The integral of power spectrum is here calculated for the three frequency bands previously defined.
       *  Finally a weighted sum is performed for the first two band integral.
       */
      /* Chart: '<S7>/Range_Management'
       *
       * Block description for '<S7>/Range_Management':
       *  This stateflow performs a for-iteration on fft spectrum in order to calculate the power integral within the three frequency bands previously defined.
       *  It produces also a test point with the square magnitude of each frequency.
       */
      /* Gateway: IonKnockInt_Tasks/IntegralEOA/Bands_Integral/Range_Management */
      /* During: IonKnockInt_Tasks/IntegralEOA/Bands_Integral/Range_Management */
      /* This stateflow performs a for-iteration on fft spectrum in order to calculate the power integral within the three frequency bands previously defined.
         It produces also a test point with the square magnitude of each frequency. */
      /* Entry Internal: IonKnockInt_Tasks/IntegralEOA/Bands_Integral/Range_Management */
      /* Transition: '<S12>:66' */
      rtb_Rpm_value_left = 0U;
      FftMag2Int1 = 0U;
      FftMag2Int2 = 0U;
      FftMag2Int3 = 0U;

      /* SignalConversion generated from: '<S4>/FftPeak' incorporates:
       *  Chart: '<S7>/Range_Management'
       *
       * Block description for '<S7>/Range_Management':
       *  This stateflow performs a for-iteration on fft spectrum in order to calculate the power integral within the three frequency bands previously defined.
       *  It produces also a test point with the square magnitude of each frequency.
       */
      memset((&(FftPeak[0])), 0, (sizeof(uint32_T)) << 6U);

      /* Chart: '<S7>/Range_Management' incorporates:
       *  MinMax: '<S22>/MinMax'
       *  MinMax: '<S38>/MinMax'
       *  MinMax: '<S44>/MinMax'
       *  SignalConversion generated from: '<S4>/FftPeak'
       *
       * Block description for '<S7>/Range_Management':
       *  This stateflow performs a for-iteration on fft spectrum in order to calculate the power integral within the three frequency bands previously defined.
       *  It produces also a test point with the square magnitude of each frequency.
       */
      while (rtb_Rpm_value_left < FFT_SAMPLE_DIV2) {
        /* Outputs for Function Call SubSystem: '<S13>/Calculate_Magnitude'
         *
         * Block description for '<S13>/Calculate_Magnitude':
         *  This block calculates the square magnitude for each frequency defined
         *  by FFT length.
         *
         * Block requirements for '<S13>/Calculate_Magnitude':
         *  1. EISB_FCA6CYL_SW_REQ_1161: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1185)
         */
        /* Product: '<S15>/Product' incorporates:
         *  Inport: '<Root>/RealData'
         */
        /* Transition: '<S12>:67' */
        /* Transition: '<S12>:27' */
        /* Event: '<S12>:69' */
        rtb_Product_k = ((int64_T)RealData[(rtb_Rpm_value_left)]) * ((int64_T)
          RealData[(rtb_Rpm_value_left)]);

        /* Product: '<S15>/Product1' incorporates:
         *  Inport: '<Root>/ImgData'
         */
        rtb_Product1_l = ((int64_T)ImgData[(rtb_Rpm_value_left)]) * ((int64_T)
          ImgData[(rtb_Rpm_value_left)]);

        /* MinMax: '<S15>/MinMax1' incorporates:
         *  Constant: '<S15>/Constant1'
         */
        if (rtb_Product_k >= MAX_MAGNITUDE_LR) {
          rtb_Product_k = MAX_MAGNITUDE_LR;
        }

        /* End of MinMax: '<S15>/MinMax1' */

        /* MinMax: '<S15>/MinMax2' incorporates:
         *  Constant: '<S15>/Constant2'
         */
        if (rtb_Product1_l >= MAX_MAGNITUDE_LR) {
          rtb_Product1_l = MAX_MAGNITUDE_LR;
        }

        /* End of MinMax: '<S15>/MinMax2' */

        /* Sum: '<S15>/Add' incorporates:
         *  DataTypeConversion: '<S15>/Conversion3'
         *  DataTypeConversion: '<S15>/Conversion4'
         */
        rtb_Product_k = (rtb_Product_k * 4LL) + (rtb_Product1_l * 4LL);

        /* MinMax: '<S15>/MinMax' incorporates:
         *  Constant: '<S15>/Constant'
         */
        if (rtb_Product_k < MAX_MAGNITUDE) {
          /* DataTypeConversion: '<S15>/Conversion1' */
          Conversion1 = (uint32_T)rtb_Product_k;
        } else {
          /* DataTypeConversion: '<S15>/Conversion1' */
          Conversion1 = (uint32_T)MAX_MAGNITUDE;
        }

        /* End of MinMax: '<S15>/MinMax' */
        /* End of Outputs for SubSystem: '<S13>/Calculate_Magnitude' */
        if ((rtb_Rpm_value_left >= IdFrqStart1) && (rtb_Rpm_value_left <
             (IdFrqStart1 + IdFrqRange1))) {
          /* Outputs for Function Call SubSystem: '<S13>/Calculate_Integral'
           *
           * Block description for '<S13>/Calculate_Integral':
           *  This block sums the normalized square magnitude to the old value of the power spectrum integral.
           *  It is called more time inside the for_loop implemented by "Range_Management" chart in order to perfom the power spectrum integral for each frequency band.
           *  The chart stores the old value and decides when trigger this calculation and which band is under evaluation.
           *
           * Block requirements for '<S13>/Calculate_Integral':
           *  1. EISB_FCA6CYL_SW_REQ_1162: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1187)
           *  2. EISB_FCA6CYL_SW_REQ_1163: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1188)
           *  3. EISB_FCA6CYL_SW_REQ_1164: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1189)
           */
          /* Transition: '<S12>:28' */
          /* Transition: '<S12>:30'
           * Requirements for Transition: '<S12>:30':
           *  1. EISB_FCA6CYL_SW_REQ_1162: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1187)
           */
          /* Event: '<S12>:8' */
          IonKnockInt_Calculate_Integral(Conversion, Conversion1, FftMag2Int1,
            &Add);

          /* End of Outputs for SubSystem: '<S13>/Calculate_Integral' */
          FftMag2Int1 = Add;

          /* Transition: '<S12>:62' */
          /* Transition: '<S12>:63' */
          /* Transition: '<S12>:64' */
        } else {
          /* Transition: '<S12>:34' */
          if ((rtb_Rpm_value_left >= IdFrqStart2) && (rtb_Rpm_value_left <
               (IdFrqStart2 + IdFrqRange2))) {
            /* Outputs for Function Call SubSystem: '<S13>/Calculate_Integral'
             *
             * Block description for '<S13>/Calculate_Integral':
             *  This block sums the normalized square magnitude to the old value of the power spectrum integral.
             *  It is called more time inside the for_loop implemented by "Range_Management" chart in order to perfom the power spectrum integral for each frequency band.
             *  The chart stores the old value and decides when trigger this calculation and which band is under evaluation.
             *
             * Block requirements for '<S13>/Calculate_Integral':
             *  1. EISB_FCA6CYL_SW_REQ_1162: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1187)
             *  2. EISB_FCA6CYL_SW_REQ_1163: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1188)
             *  3. EISB_FCA6CYL_SW_REQ_1164: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1189)
             */
            /* Transition: '<S12>:36' */
            /* Transition: '<S12>:38'
             * Requirements for Transition: '<S12>:38':
             *  1. EISB_FCA6CYL_SW_REQ_1163: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1188)
             */
            /* Event: '<S12>:8' */
            IonKnockInt_Calculate_Integral(Conversion, Conversion1, FftMag2Int2,
              &Add);

            /* End of Outputs for SubSystem: '<S13>/Calculate_Integral' */
            FftMag2Int2 = Add;

            /* Transition: '<S12>:63' */
            /* Transition: '<S12>:64' */
          } else {
            /* Transition: '<S12>:41' */
            if ((rtb_Rpm_value_left >= IdFrqStart3) && (rtb_Rpm_value_left <
                 (IdFrqStart3 + IdFrqRange3))) {
              /* Outputs for Function Call SubSystem: '<S13>/Calculate_Integral'
               *
               * Block description for '<S13>/Calculate_Integral':
               *  This block sums the normalized square magnitude to the old value of the power spectrum integral.
               *  It is called more time inside the for_loop implemented by "Range_Management" chart in order to perfom the power spectrum integral for each frequency band.
               *  The chart stores the old value and decides when trigger this calculation and which band is under evaluation.
               *
               * Block requirements for '<S13>/Calculate_Integral':
               *  1. EISB_FCA6CYL_SW_REQ_1162: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1187)
               *  2. EISB_FCA6CYL_SW_REQ_1163: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1188)
               *  3. EISB_FCA6CYL_SW_REQ_1164: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1189)
               */
              /* Transition: '<S12>:44' */
              /* Transition: '<S12>:46'
               * Requirements for Transition: '<S12>:46':
               *  1. EISB_FCA6CYL_SW_REQ_1164: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1189)
               */
              /* Event: '<S12>:8' */
              IonKnockInt_Calculate_Integral(Conversion, Conversion1,
                FftMag2Int3, &Add);

              /* End of Outputs for SubSystem: '<S13>/Calculate_Integral' */
              FftMag2Int3 = Add;

              /* Transition: '<S12>:64' */
            } else {
              /* Transition: '<S12>:48' */
            }
          }
        }

        /* Transition: '<S12>:61' */
        FftPeak[(rtb_Rpm_value_left)] = Conversion1;

        /* Transition: '<S12>:70' */
        rtb_Rpm_value_left = (uint16_T)((int32_T)(((int32_T)rtb_Rpm_value_left)
          + 1));
      }

      /* Product: '<S11>/Product' */
      /* Transition: '<S12>:72' */
      rtb_Product = ((((uint64_T)FftMag2Int1) * ((uint64_T)rtb_Look2D_IR_U16)) >>
                     ((uint64_T)11));

      /* DataTypeConversion: '<S11>/Conversion1' incorporates:
       *  Constant: '<S11>/Constant'
       */
      rtb_Conversion1 = (uint64_T)MAX_UINT_32;

      /* Product: '<S11>/Product1' */
      rtb_Product1 = ((((uint64_T)FftMag2Int2) * ((uint64_T)rtb_Look2D_IR_U16_d))
                      >> ((uint64_T)11));

      /* DataTypeConversion: '<S11>/Conversion2' incorporates:
       *  Constant: '<S11>/Constant11'
       */
      rtb_Conversion2 = (uint64_T)MAX_UINT_32;

      /* MinMax: '<S11>/MinMax' */
      if (rtb_Product < rtb_Conversion1) {
        rtb_Conversion1 = rtb_Product;
      }

      /* End of MinMax: '<S11>/MinMax' */

      /* MinMax: '<S11>/MinMax1' */
      if (rtb_Product1 < rtb_Conversion2) {
        rtb_Conversion2 = rtb_Product1;
      }

      /* End of MinMax: '<S11>/MinMax1' */

      /* Sum: '<S11>/Add' incorporates:
       *  DataTypeConversion: '<S11>/Conversion3'
       *  DataTypeConversion: '<S11>/Conversion4'
       *
       * Block requirements for '<S11>/Add':
       *  1. EISB_FCA6CYL_SW_REQ_1165: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1190)
       */
      KnPowNormFft = ((((uint32_T)rtb_Conversion1) + ((uint32_T)rtb_Conversion2))
                      >> ((uint64_T)3));

      /* End of Outputs for SubSystem: '<S4>/Bands_Integral' */

      /* SignalConversion generated from: '<S4>/IonKnockEthPercIndex' */
      IonKnockEthPercIndex = rtb_PreLookUpIdSearch_U16_o1_a;

      /* SignalConversion generated from: '<S4>/IonKnockEthPercRatio' */
      IonKnockEthPercRatio = rtb_PreLookUpIdSearch_U16_o2_k;

      /* SignalConversion generated from: '<S4>/Weight1stBand' */
      Weight1stBand = rtb_Look2D_IR_U16;

      /* SignalConversion generated from: '<S4>/Weight2ndBand' */
      Weight2ndBand = rtb_Look2D_IR_U16_d;

      /* DataTypeConversion: '<S10>/Conversion' */
      Conversion = (FftMag2Int1 >> ((uint64_T)8));

      /* MinMax: '<S10>/MinMax' incorporates:
       *  Constant: '<S10>/Constant'
       *  DataTypeConversion: '<S10>/Conversion6'
       */
      if (Conversion < ((uint32_T)((uint16_T)MAX_UINT_16))) {
        /* Assignment: '<S10>/Assignment1' incorporates:
         *  DataTypeConversion: '<S10>/Conversion3'
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        VtFftMag2Int1[(IonAbsTdcEOA)] = (uint16_T)Conversion;
      } else {
        /* Assignment: '<S10>/Assignment1' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        VtFftMag2Int1[(IonAbsTdcEOA)] = ((uint16_T)MAX_UINT_16);
      }

      /* End of MinMax: '<S10>/MinMax' */

      /* DataTypeConversion: '<S10>/Conversion1' */
      Conversion = (FftMag2Int2 >> ((uint64_T)8));

      /* MinMax: '<S10>/MinMax1' incorporates:
       *  Constant: '<S10>/Constant1'
       *  DataTypeConversion: '<S10>/Conversion7'
       */
      if (Conversion < ((uint32_T)((uint16_T)MAX_UINT_16))) {
        /* Assignment: '<S10>/Assignment2' incorporates:
         *  DataTypeConversion: '<S10>/Conversion4'
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        VtFftMag2Int2[(IonAbsTdcEOA)] = (uint16_T)Conversion;
      } else {
        /* Assignment: '<S10>/Assignment2' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        VtFftMag2Int2[(IonAbsTdcEOA)] = ((uint16_T)MAX_UINT_16);
      }

      /* End of MinMax: '<S10>/MinMax1' */

      /* DataTypeConversion: '<S10>/Conversion2' */
      Conversion = (FftMag2Int3 >> ((uint64_T)8));

      /* MinMax: '<S10>/MinMax2' incorporates:
       *  Constant: '<S10>/Constant2'
       *  DataTypeConversion: '<S10>/Conversion8'
       */
      if (Conversion < ((uint32_T)((uint16_T)MAX_UINT_16))) {
        /* Assignment: '<S10>/Assignment3' incorporates:
         *  DataTypeConversion: '<S10>/Conversion5'
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        VtFftMag2Int3[(IonAbsTdcEOA)] = (uint16_T)Conversion;
      } else {
        /* Assignment: '<S10>/Assignment3' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        VtFftMag2Int3[(IonAbsTdcEOA)] = ((uint16_T)MAX_UINT_16);
      }

      /* End of MinMax: '<S10>/MinMax2' */
      /* End of Outputs for SubSystem: '<S1>/IntegralEOA' */
    } else {
      /* Outputs for Function Call SubSystem: '<S1>/IntegralPowerOn'
       *
       * Block description for '<S1>/IntegralPowerOn':
       *  Outputs reset. Requirements are linked to previous Stateflow.
       */
      /* SignalConversion generated from: '<S5>/FftPeak' */
      /* Transition: '<S2>:17'
       * Requirements for Transition: '<S2>:17':
       *  1. EISB_FCA6CYL_SW_REQ_1748: Software shall set to 0 each output produced to define frequency b... (ECU_SW_Requirements#4305)
       */
      /* Event: '<S2>:36' */
      memset((&(FftPeak[0])), 0, (sizeof(uint32_T)) << 6U);

      /* SignalConversion generated from: '<S5>/VtFftMag2Int1' */
      memset((&(VtFftMag2Int1[0])), 0, (sizeof(uint16_T)) << 3U);

      /* SignalConversion generated from: '<S5>/VtFftMag2Int2' */
      memset((&(VtFftMag2Int2[0])), 0, (sizeof(uint16_T)) << 3U);

      /* SignalConversion generated from: '<S5>/VtFftMag2Int3' */
      memset((&(VtFftMag2Int3[0])), 0, (sizeof(uint16_T)) << 3U);

      /* SignalConversion generated from: '<S5>/FftMag2Int1' incorporates:
       *  Constant: '<S5>/Constant10'
       */
      FftMag2Int1 = 0U;

      /* SignalConversion generated from: '<S5>/FftMag2Int2' incorporates:
       *  Constant: '<S5>/Constant11'
       */
      FftMag2Int2 = 0U;

      /* SignalConversion generated from: '<S5>/FftMag2Int3' incorporates:
       *  Constant: '<S5>/Constant12'
       */
      FftMag2Int3 = 0U;

      /* SignalConversion generated from: '<S5>/KnPowNormFft' incorporates:
       *  Constant: '<S5>/Constant23'
       */
      KnPowNormFft = 0U;

      /* SignalConversion generated from: '<S5>/IonKnockEthPercIndex' incorporates:
       *  Constant: '<S5>/Constant24'
       */
      IonKnockEthPercIndex = 0U;

      /* SignalConversion generated from: '<S5>/IonKnockEthPercRatio' incorporates:
       *  Constant: '<S5>/Constant25'
       */
      IonKnockEthPercRatio = 0U;

      /* SignalConversion generated from: '<S5>/Weight1stBand' incorporates:
       *  Constant: '<S5>/Constant5'
       */
      Weight1stBand = 0U;

      /* SignalConversion generated from: '<S5>/Weight2ndBand' incorporates:
       *  Constant: '<S5>/Constant6'
       */
      Weight2ndBand = 0U;

      /* SignalConversion generated from: '<S5>/IonSampFreq' incorporates:
       *  Constant: '<S5>/Constant7'
       */
      IonSampFreq = 0U;

      /* Product: '<S22>/Divide' incorporates:
       *  Constant: '<S5>/Constant8'
       *  SignalConversion generated from: '<S5>/IdFrqStart1'
       *
       * Block requirements for '<S22>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1129: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1159)
       */
      IdFrqStart1 = 0U;

      /* SignalConversion generated from: '<S5>/IdFrqRange1' incorporates:
       *  Constant: '<S5>/Constant9'
       */
      IdFrqRange1 = 0U;

      /* Product: '<S38>/Divide' incorporates:
       *  Constant: '<S5>/Constant13'
       *  SignalConversion generated from: '<S5>/IdFrqStart2'
       *
       * Block requirements for '<S38>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1132: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1161)
       */
      IdFrqStart2 = 0U;

      /* SignalConversion generated from: '<S5>/IdFrqRange2' incorporates:
       *  Constant: '<S5>/Constant14'
       */
      IdFrqRange2 = 0U;

      /* Product: '<S44>/Divide' incorporates:
       *  Constant: '<S5>/Constant15'
       *  SignalConversion generated from: '<S5>/IdFrqStart3'
       *
       * Block requirements for '<S44>/Divide':
       *  1. EISB_FCA6CYL_SW_REQ_1134: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1163)
       */
      IdFrqStart3 = 0U;

      /* SignalConversion generated from: '<S5>/IdFrqRange3' incorporates:
       *  Constant: '<S5>/Constant16'
       */
      IdFrqRange3 = 0U;

      /* SignalConversion generated from: '<S5>/BaseFreq' incorporates:
       *  Constant: '<S5>/Constant17'
       */
      BaseFreq = 0U;
      IdVer_IonKnockInt = ID_VER_IONKNOCKINT_DEF;

      /* End of Outputs for SubSystem: '<S1>/IntegralPowerOn' */
    }
    break;
  }

  /* End of Chart: '<Root>/Scheduler' */
}

/*
 * Output and update for function-call system: '<Root>/Scheduler'
 * Block description for: '<Root>/Scheduler'
 *   This char schedules IonKnockFFT module and disables EOA runnable if knock
 *   detection strategy is not enabled.
 */
void IonKnockInt_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[3];
  int32_T i;

  /* Chart: '<Root>/Scheduler' incorporates:
   *  TriggerPort: '<S2>/input events'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */
  for (i = 0; i < 3; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S2>:2' */
    i = (int32_T)IonKn_event_IonKnockInt_PowerOn;
    IonKno_chartstep_c4_IonKnockInt(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S2>:3' */
    i = (int32_T)IonKnockI_event_IonKnockInt_EOA;
    IonKno_chartstep_c4_IonKnockInt(&i);
  }

  if (rtb_inputevents[2U] == 2) {
    /* Event: '<S2>:21' */
    i = (int32_T)IonKnock_event_IonKnockInt_10ms;
    IonKno_chartstep_c4_IonKnockInt(&i);
  }
}

/* Model step function */
void IonKnockInt_10ms(void)
{
  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockInt_10ms' */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */
  IonKnockInt_Scheduler(2);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockInt_10ms' */
}

/* Model step function */
void IonKnockInt_EOA(void)
{
  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockInt_EOA' */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */
  IonKnockInt_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockInt_EOA' */
}

/* Model step function */
void IonKnockInt_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockInt_PowerOn' incorporates:
   *  Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This char schedules IonKnockFFT module and disables EOA runnable if
   *  knock detection strategy is not enabled.
   */
  IonKnockInt_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockInt_PowerOn' */
}

/* Model initialize function */
void IonKnockInt_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint32_T FftMag2Int1;
uint32_T FftMag2Int2;
uint32_T FftMag2Int3;
uint32_T KnPowNormFft;
uint16_T IonKnockEthPercIndex;
uint16_T IonKnockEthPercRatio;
uint16_T BaseFreq;
uint16_T VtFftMag2Int1[N_CYL_MAX];
uint16_T VtFftMag2Int2[N_CYL_MAX];
uint16_T VtFftMag2Int3[N_CYL_MAX];
uint32_T FftPeak[FFT_SAMPLE_DIV2];
void IonKnockInt_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    VtFftMag2Int1[idx] = 0u;
    VtFftMag2Int2[idx] = 0u;
    VtFftMag2Int3[idx] = 0u;
  }

  for (idx=0;idx<FFT_SAMPLE_DIV2;idx++) {
    FftPeak[idx] = 0u;
  }

  FftMag2Int1 = 0u;
  FftMag2Int2 = 0u;
  FftMag2Int3 = 0u;
  KnPowNormFft = 0u;
  IonKnockEthPercIndex = 0u;
  IonKnockEthPercRatio = 0u;
  BaseFreq = 0u;
}

void IonKnockInt_PowerOn(void)
{
  IonKnockInt_Stub();
}

void IonKnockInt_EOA(void)
{
  IonKnockInt_Stub();
}

void IonKnockInt_10ms(void)
{
  IonKnockInt_Stub();
}

#endif                                 /* _BUILD_IONKNOCKINT_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/