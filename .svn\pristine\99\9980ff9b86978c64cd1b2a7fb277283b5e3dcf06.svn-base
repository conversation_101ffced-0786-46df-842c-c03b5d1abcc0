/****************************************************************************
*
* Copyright © 2019-2020 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
*
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/
#ifndef _CRANK_DEFS_H_
#define _CRANK_DEFS_H_

//#include <crank_emulation.h> MC
//#include <crank_em_cfg.h>

/*
 * Use Crank emulator defines.
 */
#define NOMINAL_TEETH           (N_TEETH)
#define MISSING_TEETH           (N_GAP_TEETH)

/*
 * Multiplier resolution:
 *    1 : 1/10th degree
 *   10 : 1/100th degree
 */
#define MULTIPLIER_RESOLUTION      (10U)
#define TOOTH_MULTIPLIER          (MULTIPLIER_RESOLUTION * NOMINAL_TEETH)


#define NOMINAL_TEETH_HALF_SCALE      (NOMINAL_TEETH)
#define NOMINAL_TEETH_FULL_SCALE      (2U * NOMINAL_TEETH_HALF_SCALE)

#define SUBINC_PER_FULL_SCALE       (NOMINAL_TEETH_FULL_SCALE * TOOTH_MULTIPLIER)

#endif /* _CRANK_DEFS_H_ */
