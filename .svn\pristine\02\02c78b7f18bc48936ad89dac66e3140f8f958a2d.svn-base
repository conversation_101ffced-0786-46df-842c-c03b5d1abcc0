/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#pragma ghs nowarning 550 /* warning #550-D: entity-kind "entity" was set but never used */


/* include AK_OSEK */
#include "OS_resources.h"
#include "OS_errors.h"
#include "OS_api.h"
#include "OS_hooks.h"
#include "tasksdefs.h"

/* include BIOS */
#include "Digio_out.h"
#include "Pit_out.h"
#include "Timing.h"
#include "wdt.h"
#include "sys.h"
#include "mathlib.h"
#include "Utils_out.h"
#include "extirq.h"
#include "task.h"
#include "mpc5500_spr_macros.h"
#include "wdt_out.h"
#include "WDT_wrapper_out.h"
#include "Ivor_c0.h"
#include "../sys/auto/mcs2.h"
#include "Gtm_eisb_out.h"

/* include DD */
#include "digin_out.h"
#include "analogin_out.h"
#ifdef _BUILD_PORT_
#include "Port_out.h"
#endif
#ifdef _BUILD_ADC_
#include "Adc_out.h"
#endif
#ifdef  _BUILD_CAN_
#include "Mcan_out.h"
#include "TTcan_out.h"
#endif

#ifdef  _BUILD_FLASH_
#include "Flash_out.h"
#endif /* _BUILD_FLASH_ */



#ifdef  _BUILD_CANMGM_
#include "Canmgmin_out.h"
#include "Canmgmout_out.h"
#include "Canmgm_out.h"
#endif
#ifdef _BUILD_CCP_
#include "ccp.h"
#endif
#ifdef _BUILD_SPI_
#include "Dspi_out.h"
#endif
#ifdef _BUILD_SPIMGM_
#include "spimgm.h"
#endif
#ifdef _BUILD_EEMGM_  
#include "eemgm_out.h"
#endif
#ifdef _BUILD_VSRAMMGM_  
#include "vsrammgm.h"
#endif
#ifdef _BUILD_DIAGCANMGM_
#include "diagcanmgm.h"
#endif
#ifdef _BUILD_TPE_
#include "tpe_out.h"
#endif
#ifdef _BUILD_PWRMGM_
#include "pwrmgm_out.h"
#endif
//#ifdef _BUILD_SYNCMGM_
#include "syncmgm_out.h"
//#endif
#ifdef _BUILD_TEMPECUMGM_
#include "TempECUMgm_out.h"
#endif

#ifdef _BUILD_COILTARGET_
#include "coiltarget_out.h"
#endif
#ifdef _BUILD_COILANGPATTERN_
#include "CoilAngPattern_out.h"
#endif
#ifdef _BUILD_COILTIMPATTERN_
#include "CoilTimPattern_out.h"
#endif
#ifdef _BUILD_IONACQ_
#include "ionacq_out.h"
#endif
#ifdef _BUILD_RELAYMGM_
#include "RelayMgm_out.h"
#endif
#ifdef _BUILD_IGN_
#include "IgnHEInterface.h"
#endif
#ifdef _BUILD_TLE9278BQX_COM_
#include "TLE9278BQX_Com_out.h"
#endif

#ifdef _BUILD_FLASHMGM_
#include "flashmgm_out.h"
#endif

#include "temp_mgm.h"

/* include APPLICATION */
#ifdef _BUILD_DIAGMGM_
#include "diagmgm_out.h"
#endif
#ifdef _BUILD_RECMGM_
#include "recmgm_out.h"
#endif

#ifdef _BUILD_MSPARKCMD_
#include "msparkcmd_out.h"
#else
#ifdef _BUILD_IGN_
#include "msparkcmd_out.h"
#endif
#endif

#ifdef _BUILD_IGNINCMD_
#include "ignincmd_out.h"
#endif

#ifdef _BUILD_BUCKDIAGMGM_
#include "buckdiagmgm_out.h"
#endif

#ifdef _BUILD_BUCKMGM_
#include "buckmgm.h"
#endif

#ifdef _BUILD_WDT_SBC_
#include "WDT_out.h"
#endif

#ifdef _BUILD_LOADMGM_
#include "loadmgm_out.h"
#endif

#ifdef _BUILD_IONCHARGECTRL_
#include "ionchargectrl_out.h"
#endif

#ifdef _BUILD_IONPHASEMGM_
#include "ionphasemgm_out.h"
#endif

#ifdef _BUILD_IONINTMGM_
#include "ionintmgm_out.h"
#endif

#ifdef _BUILD_IONDWELLMGM_
#include "iondwellmgm_out.h"
#endif

#ifdef _BUILD_IONACQBUFMGM_
#include "ionacqbufmgm_out.h"
#endif
#ifdef _BUILD_IONACQCIRCMGM_
#include "ionacqcircmgm_out.h"
#endif
#ifdef _BUILD_IONACQPAREVAL_
#include "ionacqpareval_out.h"
#endif
#ifdef _BUILD_IONACQBUFREC_
#include "ionacqbufrec_out.h"
#endif

#ifdef _BUILD_MISFTHRMGM_
#include "MisfThrMgm_out.h"
#endif

#ifdef _BUILD_IONMISF_
#include "IonMisf_out.h"
#endif

#ifdef _BUILD_IONKNOCKAIRCORR_
#include "IonKnockAirCorr_out.h"
#endif

#ifdef _BUILD_IONKNOCKEN_
#include "IonKnockEn_out.h"
#endif

#ifdef _BUILD_IONKNOCKFFT_
#include "IonKnockFFT_out.h"
#endif

#ifdef _BUILD_IONKNOCKINT_
#include "IonKnockInt_out.h"
#endif

#ifdef _BUILD_IONKNOCKPOWER_
#include "IonKnockPower_out.h"
#endif

#ifdef _BUILD_IONKNOCKSPIKEDET_
#include "IonKnockSpikeDet_out.h"
#endif

#ifdef _BUILD_IONKNOCKSTATE_
#include "IonKnockState_out.h"
#endif

#ifdef _BUILD_P2NOISEDETECT_
#include "P2NoiseDetect_out.h"
#endif

#ifdef _BUILD_MKNOCKDET_
#include "MKnockDet_out.h"
#endif
#ifdef _BUILD_SPARKPLUGTEST_
#include "SparkPlugTest_out.h"
#endif

#ifdef _BUILD_KNOCKCORRADP_
#include "KnockCorrAdp_out.h"
#endif

#ifdef _BUILD_KNOCKCORRMGM_
#include "KnockCorrMgm_out.h"
#endif

#ifdef _BUILD_KNOCKCORRNOM_
#include "KnockCorrNom_out.h"
#endif

#ifdef _BUILD_KNOCKCORRTOT_
#include "KnockCorrTot_out.h"
#endif

#ifdef _BUILD_RONDETECTCNT_
#include "rondetectcnt_out.h"
#endif

#ifdef _BUILD_RONDETECTCROSS_
#include "rondetectcross_out.h"
#endif

#ifdef _BUILD_RONDETECTEN_
#include "rondetecten_out.h"
#endif

#ifdef _BUILD_RONDETECTEST_
#include "rondetectest_out.h"
#endif

#ifdef _BUILD_RONDETECTMGM_

#include "rondetectfuel_out.h"

#include "rondetectmgm_out.h"
#endif

#ifdef _BUILD_RONDETECTSA_
#include "rondetectsa_out.h"
#endif

#ifdef _BUILD_TSPARKCTRLADAT_
#include "TSparkCtrlAdat_out.h"
#endif

#ifdef _BUILD_COMBAVGFFS_
#include "CombAvgFFS_out.h"
#endif

#ifdef _BUILD_COMBBAL_
#include "CombBal_out.h"
#endif

#ifdef _BUILD_COMBADP_
#include "CombAdp_out.h"
#endif

#ifdef _BUILD_COMBTOTCORR_
#include "CombTotCorr_out.h"
#endif

#include "cpumgm_out.h"
#include "stub.h"

#ifdef _BUILD_ACTIVE_DIAG_
#include "active_Diag_out.h"
#endif

#ifdef _BUILD_LIVENESSMGM_
#include "livenessmgm_out.h"
#endif

#ifdef _BUILD_BUCKDIAGMGM_
#include "buckdiagmgm_out.h"
#endif

#ifdef _BUILD_SAFETYMNGR_
#include "SafetyMngr_out.h"
#endif //_BUILD_SAFETYMNGR_

#ifdef _BUILD_STM_
#include "stm_out.h"
#endif

#ifdef _TEST_SPI_SBC_
//#include "WDT_out.h"
//#include "pwrmgm_out.h"
#include "TLE9278BQX_Com_out.h"
#include "TLE9278BQX_Cfg_out.h"
#endif
#include "./bios/isb/include/Crank_event.h"

#include "Stub.h"

#ifdef _TEST_SAFETYMNGR_
#include "SafetyMngr_test_out.h"
#endif

#ifdef _BUILD_GTM_
extern void Gtm_Eisb_Config(void);
#ifdef _TEST_GTM_
extern void Gtm_Eisb_StartCmdSim(void);
#endif
#endif

#ifdef _TEST_SPI_
extern void DSPI_Test(void);
#endif

#ifdef _TEST_CAN_
extern void CAN_Test(void);
extern void TTCAN_Test(void);
#endif


#ifdef _TEST_DIGIO_
// extern void DIGIO_Test(void);
#endif

#ifdef _TEST_RAM_ECC_
extern void Generate_noncorrectable_ECC_error(void);
uint32_T eccTestActivate = 0u;
#endif

#ifdef  _BUILD_RECOVERY_IVOR2_TEST_
extern void FuncRecoveryTest(void);
#endif

extern void app_data_c0_section_init(void);
extern void app_text_c0_section_init(void);

/* Prototypes */

void TaskPowerOn(void);
void TaskLive_Init(void);
void TaskLive_ResetCnt(void);
void CheckStackUsage(void); 
void CpuLoad(void);

void Func1ms(void);
void Func5ms(void);
void Func10ms(void);
void Func10msBody(void);
void Func20ms(void);
void Func50ms(void);
void Func100ms(void);

void FuncKeyOff(void); 
void FuncWaitForReset(void);
void FuncBackground(void);
void FuncSync(void);
void FuncNoSync(void);
void FuncAngle(void);
void FuncHTDC(void);
void FuncPreHTDC(void);
void FuncPreTDC(void);
void FuncTDC(void);
void FuncINJ_PRG(void);
void FuncEOA(uint8_T channel);

void Func_TaskSparkOn_0(void);
void Func_TaskSparkOn_1(void);
void Func_TaskSparkOn_2(void);
void Func_TaskSparkOn_3(void);
void Func_TaskSparkOn_4(void);
void Func_TaskSparkOn_5(void);
void Func_TaskSparkOn_6(void);
void Func_TaskSparkOn_7(void);
void Func_TaskSparkOff_0(void);
void Func_TaskSparkOff_1(void);
void Func_TaskSparkOff_2(void);
void Func_TaskSparkOff_3(void);
void Func_TaskSparkOff_4(void);
void Func_TaskSparkOff_5(void);
void Func_TaskSparkOff_6(void);
void Func_TaskSparkOff_7(void);
void Func_TaskSparkMon_1(void);
void Func_TaskSparkMon_2(void);
void Func_TaskEnBuck_1(void);
void Func_TaskEnBuck_2(void); 

// private functions
static void appManager(void);
// private functions used with KWP service $28 CommunicationControl
static void CanMgm_Enable_CanRecv10ms(void);
static void CanMgm_Enable_CanRecv50ms(void);


/*****************************************************************************/
/********************************* USERTASK **********************************/
/*****************************************************************************/
/*Defines per test*/

#ifdef _TEST_FLASH_
extern void FLASH_Test(void);
#endif

#ifdef _TEST_ADC_ 
#include "Adc_out.h"
static int16_T ret_adcEnable_sw_T0 = 0 ;
static int16_T ret_adcEnable_sw_T1 = 0 ;
static int16_T ret_adcInit_sw_T0 = 0 ;
static int16_T ret_adcInit_sw_T1 = 0 ;
static int16_T  ret_adcGet_sw_T0 = 0 ;
static int16_T  ret_adcGet_sw_T1 = 0 ;
static int16_T  err_ret_adcGet_sw_T0 = 0 ;
static int16_T  err_ret_adcGet_sw_T1 = 0 ;
static uint16_T  adcGet_sw_T0 = 0u ;
static uint16_T  adcGet_sw_T1 = 0u ;
#endif //_TEST_ADC_
#define _TASK_DEBUG_

volatile uint8_T SystemTrap;     // debug

uint8_T CntTaskNoSync;
/* MISRA rule 8.7, static variables usage */
static uint32_T    CntTaskBackground;
static uint8_T     CntTaskT1ms;        // Task T1ms
static uint8_T     CntTaskT5ms;        // Task T5ms
static uint8_T     CntTaskT10ms;        // Task T10ms
static uint8_T     CntTaskT20ms;        // Task T20ms
static uint8_T     CntTaskT50ms;        // Task T50ms
static uint32_T    EcuOnTime;
static uint8_T     CntTaskT100ms;        // Task T100ms
static uint8_T     CntTaskPowerOn;        // Task PowerOn
static uint8_T     CntTaskKeyOn;       // Task KeyOn
static uint8_T     CntTaskKeyOff;      // Task KeyOff
static uint32_T    TaskTimerBackground;
static uint32_T    TaskTimer5ms;
static uint32_T    TaskTimer10ms;
static uint32_T    TaskTimer100ms;
static uint32_T    TaskTimerOn0ms;
static uint32_T    TaskTimerOn1ms;
static uint32_T    TaskTimerOn2ms;
static uint32_T    TaskTimerOn3ms;
static uint32_T    TaskTimerOn4ms;
static uint32_T    TaskTimerOn5ms;
static uint32_T    TaskTimerOn6ms;
static uint32_T    TaskTimerOn7ms;
static uint32_T    TaskTimerOff0ms;
static uint32_T    TaskTimerOff1ms;
static uint32_T    TaskTimerOff2ms;
static uint32_T    TaskTimerOff3ms;
static uint32_T    TaskTimerOff4ms;
static uint32_T    TaskTimerOff5ms;
static uint32_T    TaskTimerOff6ms;
static uint32_T    TaskTimerOff7ms;
static uint32_T    TaskTimerMon1ms;
static uint32_T    TaskTimerMon2ms;
static uint32_T    TaskTimerBuck1ms;
static uint32_T    TaskTimerBuck2ms;
static uint32_T    TaskTimerBuck3ms;
static uint32_T    TaskTimerBuck4ms;
static uint32_T    TaskTimerEOAms;
static uint32_T    TaskTimerTDCms;
static uint32_T    TaskTimerHTDCms;
static uint32_T    TaskTimerPreTDCms;
static uint32_T    TaskTimerPreHTDCms;
static uint32_T    TaskTimerAngms;
static uint32_T    TaskTimerNoSyncms;
static uint8_T     CntTaskSync;        // Task Sync
static uint8_T     CntTaskAngle;       // Task Angle
static uint8_T     CntTaskTDC;
static uint8_T     CntTaskHTDC;
static uint8_T     CntTaskPreHTDC;
static uint8_T     CntTaskPreTDC;
static uint8_T     CntTaskEOA;

static uint8_T    CntTaskSparkOn0;
static uint8_T    CntTaskSparkOn1;
static uint8_T    CntTaskSparkOn2;
static uint8_T    CntTaskSparkOn3;
static uint8_T    CntTaskSparkOn4;
static uint8_T    CntTaskSparkOn5;
static uint8_T    CntTaskSparkOn6;
static uint8_T    CntTaskSparkOn7;
static uint8_T    CntTaskSparkOff0;
static uint8_T    CntTaskSparkOff1;
static uint8_T    CntTaskSparkOff2;
static uint8_T    CntTaskSparkOff3;
static uint8_T    CntTaskSparkOff4;
static uint8_T    CntTaskSparkOff5;
static uint8_T    CntTaskSparkOff6;
static uint8_T    CntTaskSparkOff7;
static uint8_T    CntTaskEnBuck1;
static uint8_T    CntTaskEnBuck2;
static uint8_T    CntTaskEnBuck3;
static uint8_T    CntTaskEnBuck4;
static uint8_T    CntTaskMon_1;
static uint8_T    CntTaskMon_2;
static uint32_T   EnECCTest;
static uint32_T   EnFlashTestITL;
static uint8_T    CntTaskSparkEv;

extern volatile uint8_T wdgspicanflg;
extern volatile uint8_T wdgspicanrx0;
extern volatile uint8_T wdgspicanrx1;

static uint8_T taskPowerOnCompleted = 0u;

/* Calcolo del tempo di permanenza nel loop */
static uint32_T CntMainLoop;



uint8_T do_diagcanmgm_init = 1u;
extern uint8_T RstRespFlag;
extern uint8_T DefaultFromBootRespFlag;
/// Diagnostic Reset Timeout for self reset
uint8_T DIAG_resetTimeout = 0u;

#ifdef DEBUG_RECOVERY
uint32_T Debug_RecoveryEcu_MEMU_ERR_PwrON = 0u;
#endif

/* Misura del carico CPU e utilizzo stack */
static uint32_T FastMemBase;
static uint64_T TimerCpuLoad;
static uint32_T MAXCPULOADTIME0;
static uint32_T MaxCntCpuLoad;
static uint32_T MaxCpuLoadCnt;
static uint32_T MinCntCpuLoad;
static uint16_T CpuLoadPerc;
static uint8_T  DISABLEINT;
static uint8_T  stDISABLEINT;
static uint16_T CpuTIMEINIT;
static uint16_T CntDisableCPUInit;
static uint32_T CntCpuLoad;
static uint32_T CntCpuLoadMin;
static uint32_T CntCpuLoadMax;
static uint32_T stackPeakUsage = 0u;
static uint32_T StoreInc;
//extern uint32_T __SP_INIT;
//extern uint32_T __SP_END;
//extern uint32_T __STACK_SIZE;
extern uint32_T __SP2_INIT;
extern uint32_T __SP2_END;
extern uint32_T __STACK2_SIZE;

void TaskPowerOn(void)
{   
    CntTaskPowerOn++;
    CntTaskT10ms        = 0u;    // Task T10ms
    CntTaskT20ms        = 0u;    // Task T20ms
    CntTaskT50ms        = 0u;    // Task T50ms
    CntTaskT100ms       = 0u;    // Task T100ms    
    CntTaskPowerOn      = 0u;    // Task PowerOn
    CntTaskKeyOn        = 0u;    // Task KeyOn
    CntTaskKeyOff       = 0u;    // Task KeyOff

    SystemTrap          = 0u;
    
    EnECCTest           = 0u;
    EnFlashTestITL      = 0u;

#ifdef _BUILD_VSRAMMGM_    
    /** DEVE ESSERE CHIAMATA PRIMA DI OGNI MODULO CHE USA LA VSRAM!!!! **/
    VsramState = VSRAMMGM_Verify();
    if(VsramState != NO_ERROR) 
    {
      VSRAMMGM_Init(0u);
      VSRAMMGM_Update();
    }
#endif

#ifdef _BUILD_TASK_
    TASK_Init();
#endif

    Stub_Init();


#ifdef _BUILD_DIAGMGM_
    DiagMgm_Init();
#endif

#ifdef _BUILD_RECMGM_
    RecMgm_Initialize(); 
#endif 

#ifdef _BUILD_DIGIN_
    DigIn_Initialize();
#endif 

#ifdef _BUILD_ANALOGIN_ 
    AnalogIn_Init();
#endif

#ifdef _BUILD_PWRMGM_
    PwrMgm_PowerOn();
#endif

#ifdef _BUILD_WDT_SBC_
    WDT_Init();
#endif

#ifdef _BUILD_TLE9278BQX_COM_
    TLE9278BQX_Com_Init();
    SPI_Enable(SPI_CH_E);
#endif

#ifdef _BUILD_TEMPECUMGM_
    TempECUMgm_PowerOn();
#endif

#ifdef _BUILD_TEMPMGM_
    TempMgm_Init();
#endif

/* APPL Initialization */

#ifdef _BUILD_COILTARGET_
    CoilTarget_Init();
#endif

#ifdef _BUILD_TSPARKCTRLADAT_
    TSparkCtrlAdat_Init();
#endif

#ifdef _BUILD_COILTIMPATTERN_
    CoilTimPattern_PowerOn();
#endif

#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_Init();
#endif

#ifdef _BUILD_COILANGPATTERN_
    /* TimeTrigIn(MSparkCmd), StPlasObj, DellTimeBase(CoilTarget), DegToutPulseIn(CoilTimPattern) */
    CoilAngPattern_PowerOn();
#endif

#ifdef _BUILD_IONCHARGECTRL_
    IonChargeCtrl_Init();
#endif

#ifdef _BUILD_IONACQ_
    IonAcq_Init();
#endif

#ifdef _BUILD_IONACQPAREVAL_
    IonAcqParEval_PowerOn();
#endif

#ifdef _BUILD_IONACQBUFMGM_
    IonAcqBufMgm_PowerOn();
#endif

#ifdef _BUILD_IONACQBUFREC_
    IonAcqBufRec_PowerOn();
#endif

#ifdef _BUILD_IONACQCIRCMGM_
    IonAcqCircMgm_PowerOn();
#endif

#ifdef  _BUILD_IONPHASEMGM_
    IonPhaseMgm_PowerOn();
#endif

#ifdef  _BUILD_IONINTMGM_
    IonIntMgm_PowerOn();
#endif

#ifdef  _BUILD_IONDWELLMGM_
    IonDwellMgm_PowerOn();
#endif

#ifdef _BUILD_BUCKDIAGMGM_
    BuckDiagMgm_Init();
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_Init();
#endif

#ifdef _BUILD_MISFTHRMGM_
    MisfThrMgm_PowerOn();
#endif

#ifdef _BUILD_IONMISF_
    IonMisf_PowerOn();
#endif

#ifdef _BUILD_IONKNOCKAIRCORR_
    IonKnockAirCorr_PowerOn();
#endif

#ifdef _BUILD_IONKNOCKEN_
    IonKnockEn_PowerOn();
#endif

#ifdef _BUILD_IONKNOCKSPIKEDET_
    IonKnockSpikeDet_PowerOn();
#endif

#ifdef _BUILD_IONKNOCKFFT_
    IonKnockFFT_PowerOn();
#endif

#ifdef _BUILD_IONKNOCKINT_
    IonKnockInt_PowerOn();
#endif

#ifdef _BUILD_IONKNOCKPOWER_
    IonKnockPower_PowerOn();
#endif

#ifdef _BUILD_IONKNOCKSTATE_
    IonKnockState_PowerOn();
#endif

#ifdef _BUILD_P2NOISEDETECT_
    P2NoiseDetect_PowerOn();
#endif

#ifdef _BUILD_MKNOCKDET_
    MKnockDet_PowerOn();
#endif

#ifdef _BUILD_SPARKPLUGTEST_
    SparkPlugTest_PowerOn();
#endif

#ifdef _BUILD_KNOCKCORRMGM_
    KnockCorrMgm_PowerOn();
#endif

#ifdef _BUILD_KNOCKCORRNOM_
    KnockCorrNom_PowerOn();
#endif

#ifdef _BUILD_KNOCKCORRADP_
    KnockCorrAdp_PowerOn();
#endif

#ifdef _BUILD_KNOCKCORRTOT_
    KnockCorrTot_PowerOn();
#endif



#ifdef _BUILD_RONDETECTCROSS_
	RonDetectCross_PowerOn();
#endif

#ifdef _BUILD_RONDETECTEN_
	RonDetectEn_PowerOn();
#endif

#ifdef _BUILD_RONDETECTMGM_
	RonDetectMgm_PowerOn();
#endif

#ifdef _BUILD_RONDETECTCNT_
	RonDetectCnt_PowerOn();
#endif

#ifdef _BUILD_RONDETECTEST_
	RonDetectEst_PowerOn();
#endif
#ifdef _BUILD_RONDETECTFUEL_
	RonDetectFuel_PowerOn();
#endif
#ifdef _BUILD_RONDETECTSA_
	RonDetectSA_PowerOn();
#endif

#ifdef _BUILD_LIVENESSMGM_
    LivenessMgm_PowerOn();
#endif

#ifdef _BUILD_COMBAVGFFS_
    CombAvgFFS_PowerOn();
#endif

#ifdef _BUILD_COMBBAL_
    CombBal_PowerOn();
#endif

#ifdef _BUILD_COMBADP_
    CombAdp_PowerOn();
#endif

#ifdef _BUILD_COMBTOTCORR_
    CombTotCorr_PowerOn();
#endif

#ifdef _BUILD_RELAYMGM_
    RelayMgm_Initialize();
#endif


#ifdef _BUILD_CCP_
    ccp_init();
#if (CCP_USE_ENGCAN == CCP_USE_MCAN)
    CAN_EnableReceive(CCP_CAN, CCP_CAN_RXMB, CAN_STD); //MC, da abilitare in seguito
#elif (CCP_USE_ENGCAN == CCP_USE_TTCAN)
    TTCAN_EnableReceive(CCP_CAN, CCP_CAN_RXMB, TTCAN_STD); //MC, da abilitare in seguito
#else
#error CAN engine not defined
#endif
#endif

#ifdef _BUILD_TPE_
    tpeInit();
    CAN_EnableReceive(TPE_CAN, TPE_CAN_RXBUF, CAN_XTD); 
    CAN_EnableReceive(TPE_CAN,TPE_CAN_RXBROADCASTBUF, CAN_XTD);
#endif

#ifdef _BUILD_DIAGCANMGM_
    if(do_diagcanmgm_init == 1u)
    {
        DIAGCANMGM_PowerOn();
        do_diagcanmgm_init = 0u;
    }
#endif
#ifdef _BUILD_ACTIVE_DIAG_
    Init_ActiveDiag();
#endif  /* _BUILD_ACTIVE_DIAG_ */

  
#ifdef _BUILD_CANMGM_
    CanMgm_Initialize();
#endif

#ifdef _BUILD_LOADMGM_
    LoadMgm_Init();
#endif

#ifdef _BUILD_WDT_
    TaskLive_Init();
#endif

#ifdef _BUILD_PIOTEST_
    PIOTEST_Init();
#endif

#ifdef _BUILD_SYNCMGM_  
    SYNCMGM_Init();
#endif

#ifdef _BUILD_TIMING_
    TimeStampsInit();
#endif

#ifdef _BUILD_PORT_
    PORT_Pre_Init();
#endif

#ifdef _BUILD_WDT_SBC_
    WDT_Bkg();
#endif

#ifdef _BUILD_ADC_
    ADC_SARInit();
#endif

#ifdef _BUILD_PORT_
    PORT_Init();
#endif

#ifdef _BUILD_EEMGM_
    EEMGM_SetFault();
#endif

#ifdef _BUILD_SAFETYMNGR_
    SafetyMngr_Init();
#endif // _BUILD_SAFETYMNGR_

    taskPowerOnCompleted = 1u;
}

void TaskPowerOff(void) 
{
#ifdef _BUILD_UTILS_
    /* Last function must be ECU shutdown !!! */
    Delay_ms(200u);
#endif
    /* Try to Software Reset, ERROR No correct PowerOff */
    SYS_SwRST();
}

void FuncTaskKeyOff(void) 
{
    FuncKeyOff();
    TerminateTask();
}

void FuncKeyOff(void) 
{
    CntTaskKeyOff++;
#ifdef _OSEK_
// RS: removed as in EISB2 Micro microcontroller does not have to be switched off
//    ShutdownHook((StatusType)EvPwrShutDown);
#else
    SYS_SwRST();
#endif
}


#pragma ghs nowarning 837
__asm  inline _trap(void)
{
    trap
}
#pragma ghs endnowarning
#pragma ghs endnowarning /* warning #837-D: omission of explicit type is nonstandard ("int" assumed) */

void FuncTaskSync(void)
{
    FuncSync();
    TerminateTask();
}

void FuncSync(void)
{
    CntTaskSync++;
}

void FuncTaskNoSync(void)
{
    FuncNoSync();
    TerminateTask();
}

void FuncNoSync(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldNoSyncms;
    uint64_T starttimerNoSyncms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldNoSyncms);
    }
    else { /* MISRA */ }
#endif

    CntTaskNoSync++;
    
    Gtm_Eisb_NoSync();
    
#ifdef _BUILD_IONACQ_
    IonAcq_NoSync();
#endif

#ifdef _BUILD_IONACQPAREVAL_
    IonAcqParEval_PowerOn();
#endif

#ifdef _BUILD_IONACQBUFMGM_
    IonAcqBufMgm_PowerOn();
#endif

#ifdef  _BUILD_IONPHASEMGM_
    IonPhaseMgm_PowerOn();
#endif

#ifdef  _BUILD_IONINTMGM_
    IonIntMgm_PowerOn();
#endif

#ifdef  _BUILD_IONDWELLMGM_
    IonDwellMgm_PowerOn();
#endif

#ifdef _BUILD_IONACQCIRCMGM_
    IonAcqCircMgm_PowerOn();
#endif
#ifdef _BUILD_MISFTHRMGM_
    MisfThrMgm_NoSync();
#endif
#ifdef _BUILD_IONMISF_
    IonMisf_NoSync();
#endif
#ifdef _BUILD_KNOCKCORRMGM_
    KnockCorrMgm_NoSync();
#endif
#ifdef _BUILD_KNOCKCORRNOM_
    KnockCorrNom_NoSync();
#endif
#ifdef _BUILD_KNOCKCORRADP_
    KnockCorrAdp_NoSync();
#endif
#ifdef _BUILD_KNOCKCORRTOT_
    KnockCorrTot_NoSync();
#endif
#ifdef _BUILD_COMBAVGFFS_
    CombAvgFFS_NoSync();
#endif

#ifdef _BUILD_COMBBAL_
    CombBal_NoSync();
#endif

#ifdef _BUILD_COMBADP_
    CombAdp_NoSync();
#endif

#ifdef _BUILD_COMBTOTCORR_
    CombTotCorr_NoSync();
#endif

#ifdef _BUILD_SYNCMGM_
    /* Reset SyncMgm variables */
    SYNCMGM_ResetVars();
#endif

#ifdef _BUILD_IGNINCMD_
    /* Reset IgnInCmd variables */
    IgnInCmd_NoSync();
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerNoSyncms);
        starttimerNoSyncms = (starttimerNoSyncms - starttimer_oldNoSyncms);
        TIMING_TicksToMicroSeconds(starttimerNoSyncms, &starttimerNoSyncms);
        if ((uint32_T)(starttimerNoSyncms) > TaskTimerNoSyncms)
        {
            TaskTimerNoSyncms = (uint32_T)(starttimerNoSyncms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskAngle(void)
{
    FuncAngle();
    TerminateTask();
}

void FuncAngle(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldAngms;
    uint64_T starttimerAngms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldAngms);
    }
    else { /* MISRA */ }
#endif

    CntTaskAngle++;

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerAngms);
        starttimerAngms = (starttimerAngms - starttimer_oldAngms);
        TIMING_TicksToMicroSeconds(starttimerAngms, &starttimerAngms);
        if ((uint32_T)(starttimerAngms) > TaskTimerAngms)
        {
            TaskTimerAngms = (uint32_T)(starttimerAngms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskTDC(void)
{
    FuncTDC();
    TerminateTask();
}    // end taskTDC()

void FuncTDC(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldTDCms;
    uint64_T starttimerTDCms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldTDCms);
    }
    else { /* MISRA */ }
#endif

    CntTaskTDC++;
    Gtm_Eisb_TDC();
#ifdef _BUILD_PWRMGM_
    PwrMgm_Tdc();
#endif

#ifdef _BUILD_LOADMGM_
    LoadMgm_TDC();
#endif
    
#ifdef _BUILD_IONACQ_
    IonAcq_TDC(); 
#endif

#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_TDC();
#endif

#ifdef _BUILD_IONMISF_
    IonMisf_TDC();
#ifdef _BUILD_CANMGM_
    CanMgmOut_TDC();
#endif
#endif

#ifdef _BUILD_CANMGM_
    CanMgm_DebounceTDC(AbsTdc);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerTDCms);
        starttimerTDCms = (starttimerTDCms - starttimer_oldTDCms);
        TIMING_TicksToMicroSeconds(starttimerTDCms, &starttimerTDCms);
        if ((uint32_T)(starttimerTDCms) > TaskTimerTDCms)
        {
            TaskTimerTDCms = (uint32_T)(starttimerTDCms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskHTDC(void)
{
    FuncHTDC();
    TerminateTask();    
}

void FuncHTDC(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldHTDCms;
    uint64_T starttimerHTDCms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldHTDCms);
    }
    else { /* MISRA */ }
#endif

    CntTaskHTDC++;

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerHTDCms);
        starttimerHTDCms = (starttimerHTDCms - starttimer_oldHTDCms);
        TIMING_TicksToMicroSeconds(starttimerHTDCms, &starttimerHTDCms);
        if ((uint32_T)(starttimerHTDCms) > TaskTimerHTDCms)
        {
            TaskTimerHTDCms = (uint32_T)(starttimerHTDCms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskPreHTDC(void)
{
    FuncPreHTDC();
    TerminateTask();
}

void FuncPreHTDC(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldPreHTDCms;
    uint64_T starttimerPreHTDCms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldPreHTDCms);
    }
    else { /* MISRA */ }
#endif

    CntTaskPreHTDC++;

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerPreHTDCms);
        starttimerPreHTDCms = (starttimerPreHTDCms - starttimer_oldPreHTDCms);
        TIMING_TicksToMicroSeconds(starttimerPreHTDCms, &starttimerPreHTDCms);
        if ((uint32_T)(starttimerPreHTDCms) > TaskTimerPreHTDCms)
        {
            TaskTimerPreHTDCms = (uint32_T)(starttimerPreHTDCms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskPreTDC(void)
{
    FuncPreTDC();
    TerminateTask();    
}

void FuncPreTDC(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldPreTDCms;
    uint64_T starttimerPreTDCms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldPreTDCms);
    }
    else { /* MISRA */ }
#endif

    CntTaskPreTDC++;

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerPreTDCms);
        starttimerPreTDCms = (starttimerPreTDCms - starttimer_oldPreTDCms);
        TIMING_TicksToMicroSeconds(starttimerPreTDCms, &starttimerPreTDCms);
        if ((uint32_T)(starttimerPreTDCms) > TaskTimerPreTDCms)
        {
            TaskTimerPreTDCms = (uint32_T)(starttimerPreTDCms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}


void FuncTaskINJ_PRG(void)
{
    FuncINJ_PRG();
    TerminateTask();    
}

void FuncINJ_PRG(void)
{
#if 0
    CntTaskINJ_PRG++;

#ifdef _TASK_DEBUG_
    TIMING_GetAbsTimer(&TaskTimerINJ_PRG);
#endif

#ifdef _BUILD_INJCMD_
    InjCmd_InjPrg();
#endif

#ifdef _TASK_DEBUG_
    TIMING_GetTaskTime(&TaskTimeINJ_PRG, TaskTimerINJ_PRG);
#endif
#endif
}

void FuncTaskWaitForReset(void)
{
    FuncWaitForReset();
    TerminateTask();
}

void FuncWaitForReset(void)
{
#ifdef _BUILD_DIAGCANMGM_   
    extern uint32_T comm_protocol;

    comm_protocol = tpeGetCommStatus();
    VSRAMMGM_Update();
#endif
#ifdef _BUILD_EEMGM_
    /* AM - MV4 strategy, valid until WDT strategy will be defined!!! */
    EEMGM_SetEventID(EE_AFTER_UDS_SW_DWLOAD);
#endif

    SYS_SwRST();

}

void FuncTaskSparkOn_0(void)
{
    Func_TaskSparkOn_0();
    TerminateTask();
}

void Func_TaskSparkOn_0(void)
{
    const uint8_T cyl = 0u;
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOn0ms;
    uint64_T starttimerOn0ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOn0ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOn0++;
    MSparkCmd_SparkOn(cyl);
    BuckDiagMgm_RunCheck(cyl & 0x01u);

#ifdef _BUILD_SYNCMGM_
    /* Rpm Diagnosis */
    SYNCMGM_RpmDiagnosis();
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOn0ms);
        starttimerOn0ms = (starttimerOn0ms - starttimer_oldOn0ms);
        TIMING_TicksToMicroSeconds(starttimerOn0ms, &starttimerOn0ms);
        if ((uint32_T)(starttimerOn0ms) > TaskTimerOn0ms)
        {
            TaskTimerOn0ms = (uint32_T)(starttimerOn0ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOn_1(void)
{
    Func_TaskSparkOn_1();
    TerminateTask();
}

void Func_TaskSparkOn_1(void)
{
    const uint8_T cyl = 1u;
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOn1ms;
    uint64_T starttimerOn1ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOn1ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOn1++;
    MSparkCmd_SparkOn(cyl);
    BuckDiagMgm_RunCheck(cyl & 0x01u);

#ifdef _BUILD_SYNCMGM_
    /* Rpm Diagnosis */
    SYNCMGM_RpmDiagnosis();
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOn1ms);
        starttimerOn1ms = (starttimerOn1ms - starttimer_oldOn1ms);
        TIMING_TicksToMicroSeconds(starttimerOn1ms, &starttimerOn1ms);
        if ((uint32_T)(starttimerOn1ms) > TaskTimerOn1ms)
        {
            TaskTimerOn1ms = (uint32_T)(starttimerOn1ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOn_2(void)
{
    Func_TaskSparkOn_2();
    TerminateTask();
}

void Func_TaskSparkOn_2(void)
{
    const uint8_T cyl = 2u;
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOn2ms;
    uint64_T starttimerOn2ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOn2ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOn2++;
    MSparkCmd_SparkOn(cyl);
    BuckDiagMgm_RunCheck(cyl & 0x01u);

#ifdef _BUILD_SYNCMGM_
    /* Rpm Diagnosis */
    SYNCMGM_RpmDiagnosis();
#endif


#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOn2ms);
        starttimerOn2ms = (starttimerOn2ms - starttimer_oldOn2ms);
        TIMING_TicksToMicroSeconds(starttimerOn2ms, &starttimerOn2ms);
        if ((uint32_T)(starttimerOn2ms) > TaskTimerOn2ms)
        {
            TaskTimerOn2ms = (uint32_T)(starttimerOn2ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOn_3(void)
{
    Func_TaskSparkOn_3();
    TerminateTask();
}

void Func_TaskSparkOn_3(void)
{
    const uint8_T cyl = 3u;
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOn3ms;
    uint64_T starttimerOn3ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOn3ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOn3++;
    MSparkCmd_SparkOn(cyl);
    BuckDiagMgm_RunCheck(cyl & 0x01u);

#ifdef _BUILD_SYNCMGM_
    /* Rpm Diagnosis */
    SYNCMGM_RpmDiagnosis();
#endif


#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOn3ms);
        starttimerOn3ms = (starttimerOn3ms - starttimer_oldOn3ms);
        TIMING_TicksToMicroSeconds(starttimerOn3ms, &starttimerOn3ms);
        if ((uint32_T)(starttimerOn3ms) > TaskTimerOn3ms)
        {
            TaskTimerOn3ms = (uint32_T)(starttimerOn3ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOn_4(void)
{
    Func_TaskSparkOn_4();
    TerminateTask();
}

void Func_TaskSparkOn_4(void)
{
    const uint8_T cyl = 4u;
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOn4ms;
    uint64_T starttimerOn4ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOn4ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOn4++;
    MSparkCmd_SparkOn(cyl);
    BuckDiagMgm_RunCheck(cyl & 0x01u);

#ifdef _BUILD_SYNCMGM_
    /* Rpm Diagnosis */
    SYNCMGM_RpmDiagnosis();
#endif


#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOn4ms);
        starttimerOn4ms = (starttimerOn4ms - starttimer_oldOn4ms);
        TIMING_TicksToMicroSeconds(starttimerOn4ms, &starttimerOn4ms);
        if ((uint32_T)(starttimerOn4ms) > TaskTimerOn4ms)
        {
            TaskTimerOn4ms = (uint32_T)(starttimerOn4ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOn_5(void)
{
    Func_TaskSparkOn_5();
    TerminateTask();
}

void Func_TaskSparkOn_5(void)
{
    const uint8_T cyl = 5u;
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOn5ms;
    uint64_T starttimerOn5ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOn5ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOn5++;
    MSparkCmd_SparkOn(cyl);
    BuckDiagMgm_RunCheck(cyl & 0x01u);

#ifdef _BUILD_SYNCMGM_
    /* Rpm Diagnosis */
    SYNCMGM_RpmDiagnosis();
#endif


#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOn5ms);
        starttimerOn5ms = (starttimerOn5ms - starttimer_oldOn5ms);
        TIMING_TicksToMicroSeconds(starttimerOn5ms, &starttimerOn5ms);
        if ((uint32_T)(starttimerOn5ms) > TaskTimerOn5ms)
        {
            TaskTimerOn5ms = (uint32_T)(starttimerOn5ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOn_6(void)
{
    Func_TaskSparkOn_6();
    TerminateTask();
}

void Func_TaskSparkOn_6(void)
{
    const uint8_T cyl = 6u;
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOn6ms;
    uint64_T starttimerOn6ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOn6ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOn6++;
    MSparkCmd_SparkOn(cyl);
    BuckDiagMgm_RunCheck(cyl & 0x01u);

#ifdef _BUILD_SYNCMGM_
    /* Rpm Diagnosis */
    SYNCMGM_RpmDiagnosis();
#endif


#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOn6ms);
        starttimerOn6ms = (starttimerOn6ms - starttimer_oldOn6ms);
        TIMING_TicksToMicroSeconds(starttimerOn6ms, &starttimerOn6ms);
        if ((uint32_T)(starttimerOn6ms) > TaskTimerOn6ms)
        {
            TaskTimerOn6ms = (uint32_T)(starttimerOn6ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOn_7(void)
{
    Func_TaskSparkOn_7();
    TerminateTask();
}

void Func_TaskSparkOn_7(void)
{
    const uint8_T cyl = 7u;
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOn7ms;
    uint64_T starttimerOn7ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOn7ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOn7++;
    MSparkCmd_SparkOn(cyl);
    BuckDiagMgm_RunCheck(cyl & 0x01u);

#ifdef _BUILD_SYNCMGM_
    /* Rpm Diagnosis */
    SYNCMGM_RpmDiagnosis();
#endif


#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOn7ms);
        starttimerOn7ms = (starttimerOn7ms - starttimer_oldOn7ms);
        TIMING_TicksToMicroSeconds(starttimerOn7ms, &starttimerOn7ms);
        if ((uint32_T)(starttimerOn7ms) > TaskTimerOn7ms)
        {
            TaskTimerOn7ms = (uint32_T)(starttimerOn7ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkMon_1(void)
{
    Func_TaskSparkMon_1();
    TerminateTask();
}

void Func_TaskSparkMon_1(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldMon1ms;
    uint64_T starttimerMon1ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldMon1ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskMon_1++;

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerMon1ms);
        starttimerMon1ms = (starttimerMon1ms - starttimer_oldMon1ms);
        TIMING_TicksToMicroSeconds(starttimerMon1ms, &starttimerMon1ms);
        if ((uint32_T)(starttimerMon1ms) > TaskTimerMon1ms)
        {
            TaskTimerMon1ms = (uint32_T)(starttimerMon1ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkMon_2(void)
{
    Func_TaskSparkMon_2();
    TerminateTask();
}

void Func_TaskSparkMon_2(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldMon2ms;
    uint64_T starttimerMon2ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldMon2ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskMon_2++;

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerMon2ms);
        starttimerMon2ms = (starttimerMon2ms - starttimer_oldMon2ms);
        TIMING_TicksToMicroSeconds(starttimerMon2ms, &starttimerMon2ms);
        if ((uint32_T)(starttimerMon2ms) > TaskTimerMon2ms)
        {
            TaskTimerMon2ms = (uint32_T)(starttimerMon2ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOff_0(void)
{
    Func_TaskSparkOff_0();
    TerminateTask();
}

void Func_TaskSparkOff_0(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOff0ms;
    uint64_T starttimerOff0ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOff0ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOff0++;
#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_SparkOff(0u);
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_SparkOff(0u);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOff0ms);
        starttimerOff0ms = (starttimerOff0ms - starttimer_oldOff0ms);
        TIMING_TicksToMicroSeconds(starttimerOff0ms, &starttimerOff0ms);
        if ((uint32_T)(starttimerOff0ms) > TaskTimerOff0ms)
        {
            TaskTimerOff0ms = (uint32_T)(starttimerOff0ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOff_1(void)
{
    Func_TaskSparkOff_1();
    TerminateTask();
}

void Func_TaskSparkOff_1(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOff1ms;
    uint64_T starttimerOff1ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOff1ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOff1++;
#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_SparkOff(1u);
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_SparkOff(1u);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOff1ms);
        starttimerOff1ms = (starttimerOff1ms - starttimer_oldOff1ms);
        TIMING_TicksToMicroSeconds(starttimerOff1ms, &starttimerOff1ms);
        if ((uint32_T)(starttimerOff1ms) > TaskTimerOff1ms)
        {
            TaskTimerOff1ms = (uint32_T)(starttimerOff1ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOff_2(void)
{
    Func_TaskSparkOff_2();
    TerminateTask();
}

void Func_TaskSparkOff_2(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOff2ms;
    uint64_T starttimerOff2ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOff2ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOff2++;
#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_SparkOff(2u);
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_SparkOff(2u);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOff2ms);
        starttimerOff2ms = (starttimerOff2ms - starttimer_oldOff2ms);
        TIMING_TicksToMicroSeconds(starttimerOff2ms, &starttimerOff2ms);
        if ((uint32_T)(starttimerOff2ms) > TaskTimerOff2ms)
        {
            TaskTimerOff2ms = (uint32_T)(starttimerOff2ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOff_3(void)
{
    Func_TaskSparkOff_3();
    TerminateTask();
}

void Func_TaskSparkOff_3(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOff3ms;
    uint64_T starttimerOff3ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOff3ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOff3++;
#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_SparkOff(3u);
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_SparkOff(3u);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOff3ms);
        starttimerOff3ms = (starttimerOff3ms - starttimer_oldOff3ms);
        TIMING_TicksToMicroSeconds(starttimerOff3ms, &starttimerOff3ms);
        if ((uint32_T)(starttimerOff3ms) > TaskTimerOff3ms)
        {
            TaskTimerOff3ms = (uint32_T)(starttimerOff3ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOff_4(void)
{
    Func_TaskSparkOff_4();
    TerminateTask();
}

void Func_TaskSparkOff_4(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOff4ms;
    uint64_T starttimerOff4ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOff4ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOff4++;
#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_SparkOff(4u);
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_SparkOff(4u);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOff4ms);
        starttimerOff4ms = (starttimerOff4ms - starttimer_oldOff4ms);
        TIMING_TicksToMicroSeconds(starttimerOff4ms, &starttimerOff4ms);
        if ((uint32_T)(starttimerOff4ms) > TaskTimerOff4ms)
        {
            TaskTimerOff4ms = (uint32_T)(starttimerOff4ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOff_5(void)
{
    Func_TaskSparkOff_5();
    TerminateTask();
}

void Func_TaskSparkOff_5(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOff5ms;
    uint64_T starttimerOff5ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOff5ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOff5++;
#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_SparkOff(5u);
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_SparkOff(5u);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOff5ms);
        starttimerOff5ms = (starttimerOff5ms - starttimer_oldOff5ms);
        TIMING_TicksToMicroSeconds(starttimerOff5ms, &starttimerOff5ms);
        if ((uint32_T)(starttimerOff5ms) > TaskTimerOff5ms)
        {
            TaskTimerOff5ms = (uint32_T)(starttimerOff5ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOff_6(void)
{
    Func_TaskSparkOff_6();
    TerminateTask();
}

void Func_TaskSparkOff_6(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOff6ms;
    uint64_T starttimerOff6ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOff6ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOff6++;
#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_SparkOff(6u);
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_SparkOff(6u);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOff6ms);
        starttimerOff6ms = (starttimerOff6ms - starttimer_oldOff6ms);
        TIMING_TicksToMicroSeconds(starttimerOff6ms, &starttimerOff6ms);
        if ((uint32_T)(starttimerOff6ms) > TaskTimerOff6ms)
        {
            TaskTimerOff6ms = (uint32_T)(starttimerOff6ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkOff_7(void)
{
    Func_TaskSparkOff_7();
    TerminateTask();
}

void Func_TaskSparkOff_7(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldOff7ms;
    uint64_T starttimerOff7ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldOff7ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskSparkOff7++;
#ifdef _BUILD_MSPARKCMD_
    MSparkCmd_SparkOff(7u);
#endif

#ifdef _BUILD_IGNINCMD_
    IgnInCmd_SparkOff(7u);
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerOff7ms);
        starttimerOff7ms = (starttimerOff7ms - starttimer_oldOff7ms);
        TIMING_TicksToMicroSeconds(starttimerOff7ms, &starttimerOff7ms);
        if ((uint32_T)(starttimerOff7ms) > TaskTimerOff7ms)
        {
            TaskTimerOff7ms = (uint32_T)(starttimerOff7ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskEnBuck_1(void)
{
    Func_TaskEnBuck_1();
    TerminateTask();
}

void Func_TaskEnBuck_1(void)
{
    uint8_T tmpBuckEnState;
    uint8_T tmpMosPhaseState;
    uint8_T tmpBuckCylReq;
    int16_T errtmp;
    uint8_T stdiag;

#ifdef _TEST_TIMING_
    uint64_T starttimer_oldBuck1ms;
    uint64_T starttimerBuck1ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldBuck1ms);
    }
    else { /* MISRA */ }
#endif

#ifdef _BUILD_BUCKDIAGMGM_
    /* Buck diagnosis */
    BuckDiagMgm_Batt();
#endif
#ifdef _BUILD_IGN_
    CntTaskEnBuck1++;
    CntVCapInOn[CylPlaMOS_idx]++;
#ifdef _BUILD_IONCHARGECTRL_
    IonChargeCtrl_T5ms();   // Update Obj, this call has been moved here because VChargeObjOff is updated at EOA
    /* While FlgSyncPhased = 0 the VCap acq can not run therefore the VCharge signal is reset on the Obj */
    if(FlgSyncPhased == 0u)
    {
        AnalogIn_VCapAcq(0u, 1u);
    }
    IonChargeCtrl_Tdc();
#endif
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerBuck1ms);
        starttimerBuck1ms = (starttimerBuck1ms - starttimer_oldBuck1ms);
        TIMING_TicksToMicroSeconds(starttimerBuck1ms, &starttimerBuck1ms);
        if ((uint32_T)(starttimerBuck1ms) > TaskTimerBuck1ms)
        {
            TaskTimerBuck1ms = (uint32_T)(starttimerBuck1ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskEnBuck_2(void)
{
    Func_TaskEnBuck_2();
    TerminateTask();
}

void Func_TaskEnBuck_2(void)
{
    uint8_T tmpBuckEnState;
    uint8_T tmpMosPhaseState;
    uint8_T tmpBuckCylReq;
    int16_T errtmp;
    uint8_T stdiag;

#ifdef _TEST_TIMING_
    uint64_T starttimer_oldBuck2ms;
    uint64_T starttimerBuck2ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldBuck2ms);
    }
    else { /* MISRA */ }
#endif

#ifdef _BUILD_BUCKDIAGMGM_
    /* Buck diagnosis */
    BuckDiagMgm_Batt();
#endif
#ifdef _BUILD_IGN_
    CntTaskEnBuck2++;
    
    CntVCapInOn[CylPlaMOS_idx]++;
#ifdef _BUILD_IONCHARGECTRL_
    IonChargeCtrl_T5ms();   // Update Obj, this call has been moved here because VChargeObjOff is updated at EOA
    /* While FlgSyncPhased = 0 the VCap acq can not run therefore the VCharge signal is reset on the Obj */
    if(FlgSyncPhased == 0u)
    {
        AnalogIn_VCapAcq(0u, 1u);
    }
    IonChargeCtrl_Tdc();
#endif
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerBuck2ms);
        starttimerBuck2ms = (starttimerBuck2ms - starttimer_oldBuck2ms);
        TIMING_TicksToMicroSeconds(starttimerBuck2ms, &starttimerBuck2ms);
        if ((uint32_T)(starttimerBuck2ms) > TaskTimerBuck2ms)
        {
            TaskTimerBuck2ms = (uint32_T)(starttimerBuck2ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskEOA_A(void)
{
    FuncEOA(ION_CHANNEL_A);
    TerminateTask();
}

void FuncTaskEOA_B(void)
{
    FuncEOA(ION_CHANNEL_B);
    TerminateTask();
}

void FuncTaskEOA_C(void)
{
    FuncEOA(ION_CHANNEL_C);
    TerminateTask();
}

void FuncTaskEOA_D(void)
{
    FuncEOA(ION_CHANNEL_D);
    TerminateTask();
}

void FuncEOA(uint8_T channel)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_oldEOAms;
    uint64_T starttimerEOAms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldEOAms);
    }
    else { /* MISRA */ }
#endif

    CntTaskEOA++;

    /* VCharge acq */
    AnalogIn_VCapAcq(channel, 0u);
    
#ifdef  _BUILD_IONACQ_
    IonAcq_LatchEOA(channel);
#endif
#ifdef  _BUILD_IONACQBUFMGM_
    IonAcqBufMgm_EOA();
#endif

#ifdef _BUILD_TSPARKCTRLADAT_
    TSparkCtrlAdat_EOA_PreCalc();
#endif
#ifdef  _BUILD_IONPHASEMGM_
    IonPhaseMgm_EOA();
#endif

#ifdef  _BUILD_IONINTMGM_
    IonIntMgm_EOA();
#endif

#ifdef  _BUILD_IONDWELLMGM_
    IonDwellMgm_EOA();
#endif

#ifdef _BUILD_IONKNOCKAIRCORR_
    IonKnockAirCorr_EOA();
#endif

#ifdef _BUILD_IONKNOCKEN_
    IonKnockEn_EOA();
#endif

#ifdef _BUILD_IONKNOCKSPIKEDET_
    IonKnockSpikeDet_EOA();
#endif

#ifdef _BUILD_IONKNOCKFFT_
    IonKnockFFT_EOA();
#endif

#ifdef _BUILD_IONKNOCKINT_
    IonKnockInt_EOA();
#endif

#ifdef _BUILD_IONKNOCKPOWER_
    IonKnockPower_EOA();
#endif

#ifdef _BUILD_P2NOISEDETECT_
    P2NoiseDetect_EOA();
#endif

#ifdef _BUILD_COMBAVGFFS_
    CombAvgFFS_EOA();
#endif

#ifdef _BUILD_COMBBAL_
    CombBal_EOA();
#endif

#ifdef _BUILD_COMBADP_
    CombAdp_EOA();
#endif

#ifdef _BUILD_COMBTOTCORR_
    CombTotCorr_EOA();
#endif

#ifdef _BUILD_MKNOCKDET_
    MKnockDet_EOA();
#endif

#ifdef _BUILD_SPARKPLUGTEST_
    SparkPlugTest_EOA();
#endif

#ifdef _BUILD_IONKNOCKSTATE_
    IonKnockState_EOA();
#endif

#ifdef _BUILD_RONDETECTEN_
	RonDetectEn_EOA();
#endif
	
#ifdef _BUILD_RONDETECTMGM_
	RonDetectMgm_EOA();
#endif
		
#ifdef _BUILD_RONDETECTEST_
	RonDetectEst_EOA();
#endif
	
#ifdef _BUILD_RONDETECTSA_
	RonDetectSA_EOA();
#endif

#ifdef _BUILD_KNOCKCORRMGM_
    KnockCorrMgm_EOA();
#endif
#ifdef _BUILD_KNOCKCORRNOM_
    KnockCorrNom_EOA();
#endif
#ifdef _BUILD_KNOCKCORRADP_
    KnockCorrAdp_EOA();
#endif
#ifdef _BUILD_KNOCKCORRTOT_
    KnockCorrTot_EOA();
#endif

#ifdef _BUILD_IONACQCIRCMGM_
    IonAcqCircMgm_EOA();
#endif

#ifdef _BUILD_TSPARKCTRLADAT_
    TSparkCtrlAdat_EOA();
#endif

#ifdef _BUILD_DIAGMGM_
    DiagMgm_EOA();
#endif

#ifdef _BUILD_MISFTHRMGM_
    MisfThrMgm_EOA();
#endif

#ifdef _BUILD_IONMISF_
    IonMisf_EOA();
#endif

#ifdef _BUILD_IONACQBUFREC_
    IonAcqBufRec_EOA();
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerEOAms);
        starttimerEOAms = (starttimerEOAms - starttimer_oldEOAms);
        TIMING_TicksToMicroSeconds(starttimerEOAms, &starttimerEOAms);
        if ((uint32_T)(starttimerEOAms) > TaskTimerEOAms)
        {
            TaskTimerEOAms = (uint32_T)(starttimerEOAms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncBackgroundTask(void)
{
    FuncBackground();
    TerminateTask();
}

void FuncBackground(void)
{

#ifdef _BUILD_DIAGCANMGM_
    if ((RstRespFlag == DIAG_ON) && (do_diagcanmgm_init == 0u))
    {
        /* Reset Flag in order to avoi continuus response */
        RstRespFlag = DIAG_OFF;
        // Set RstFlashed flag in order send the info to ScanTool
        CanMgm_SetClearRstFlashed((uint8_T)TRUE);

        DiagResponseAfterReset();

    }

    if ((DefaultFromBootRespFlag == DIAG_ON) && (do_diagcanmgm_init == 0u))
    {
        /* Reset Flag in order to avoid continous response */
        DefaultFromBootRespFlag = DIAG_OFF;

        DiagResponseAfterJump2Appl();
    }
#endif

#ifdef _TEST_TIMING_
    uint64_T starttimer_oldBkgms;
    uint64_T starttimerBkgms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_oldBkgms);
    }
    else { /* MISRA */ }
#endif
   
    CntTaskBackground++;
    
    if ((SystemTrap == 1u) 
#ifdef _BUILD_CPUMGM_
        || (SYSTEMTRAP0 == 0xFAFEu)
#endif
    )
    {
        SystemTrap = 0u;
#ifdef _BUILD_WDT_SBC_
#ifdef _BUILD_WDT_EVB_
        SYS_SwRST();
#else
        PwrMgm_Reset(); // Protected reset but VSRAM clear
#endif
#else
        SYS_SwRST();
#endif
    }
    else if (SystemTrap == 2u)
    {
        SystemTrap = 0u;
        _trap();
    }
    else if (SystemTrap == 3u)
    {
        SystemTrap = 0u;
        SYS_SwRST();
    }
    else { /* MISRA */ }

#ifdef _BUILD_FLASHMGM_
    FLASHMGM_FlashTest();
#endif
    
#ifdef _BUILD_CCP_
#ifdef  _OSEK_
    GetResource((ResourceType)RES_CCP);
#endif
    ccpSendCallBack();
#ifdef  _OSEK_
    ReleaseResource((ResourceType)RES_CCP);  
#endif
#endif

#ifdef _BUILD_WDT_SBC_
    WDT_Bkg();
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimerBkgms);
        starttimerBkgms = (starttimerBkgms - starttimer_oldBkgms);
        TIMING_TicksToMicroSeconds(starttimerBkgms, &starttimerBkgms);
        if ((uint32_T)(starttimerBkgms) > TaskTimerBackground)
        {
            TaskTimerBackground = (uint32_T)(starttimerBkgms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTaskSparkEv(void)
{
    CntTaskSparkEv++;

    /* Diag GTM */
    MSparkCmd_SparkEv();
    TerminateTask();
}

void FuncTask1ms(void)
{
    Func1ms();
    TerminateTask();
}   // end FuncTask1ms()

uint8_T Debug_ReadGpio94 = 0u;
void Func1ms(void)
{
    CntTaskT1ms++; 
    if ((taskPowerOnCompleted != 0u) && (DISABLEINT == 0u))
    {
#ifdef _BUILD_STM_    
#if 0
		if((CntTaskT5ms == 1u)&&(STM_EnableStatus == 0u))
		{
			STM_Enable(); //For S.M.       
		}		
#endif
#endif 	
#ifdef _TEST_GTM_
        Gtm_Eisb_StartCmdSim();
#endif
#ifdef _BUILD_TPE_
#ifdef TPE_TIME_BASE_MS
#if (TPE_TIME_BASE_MS == 1u)
        tpeTickTimer();
#endif // (TPE_TIME_BASE_MS == 1u)
#endif // TPE_TIME_BASE_MS
#endif // _BUILD_TPE_

    }
}

void FuncTask5ms(void)
{
    Func5ms();
    TerminateTask();
}   // end FuncTask5ms()

extern vuint32_t *cpu_debug_pmos_0_var_addr;
extern uint32_T cpu_debug_pmos_0_var;
extern vuint32_t *cpu_debug_pmos_1_var_addr;
extern uint32_T cpu_debug_pmos_1_var;
extern uint32_T mcs2_base;

void Func5ms(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_old5ms;
    uint64_T starttimer5ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old5ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskT5ms++;

    if ((taskPowerOnCompleted != 0u) && (DISABLEINT == 0u))
    {

#ifdef _BUILD_CANMGM_
        CanMgm_CanRecv5ms();
#endif

#ifdef _BUILD_CANMGM_
        /// Update BrokenFuseSx and BrokenFuseDx
        CanMgm_CalcBrokenFuse();
        CanMgmOut_T5ms();
#endif

//#ifdef _BUILD_SYNCMGM_
        SYNCMGM_T5ms();
//#endif   

#ifdef _BUILD_LOADMGM_
        LoadMgm_T5ms();
#endif

#ifdef _BUILD_IONACQCIRCMGM_
        IonAcqCircMgm_5ms();
#endif

#ifdef _BUILD_IONACQBUFREC_
        IonAcqBufRec_5ms();
#endif

#ifdef _BUILD_IONCHARGECTRL_
//        IONChargeCtrl_T5ms();
#endif
		
#ifdef _BUILD_RONDETECTCROSS_
		RonDetectCross_5ms();
#endif
							
#ifdef _BUILD_RONDETECTEST_
		RonDetectEst_5ms();
#endif
		
#ifdef _BUILD_WDT_SBC_
        /* call periodic check if operations are pending due to WDT mode change */
        WDT_PendingOperationsCheck();
#endif

#ifdef _BUILD_CCP_
#ifdef  _OSEK_
        /* CCP 5 ms monitoring */
        GetResource((ResourceType)RES_CCP);
#endif
        CCP_ActivityMonitoring();
        CCP_PeriodicMonitorTask5ms(); 
#ifdef  _OSEK_
        ReleaseResource((ResourceType)RES_CCP);
#endif
#endif

#ifdef _BUILD_TPE_
#ifdef TPE_TIME_BASE_MS
#if (TPE_TIME_BASE_MS == 5u)
        tpeTickTimer();
#endif // (TPE_TIME_BASE_MS == 5u)
#endif // TPE_TIME_BASE_MS
#endif // _BUILD_TPE_

#ifdef _BUILD_ACTIVE_DIAG_
        ActiveDiagMgm_T5ms();
#endif

#ifdef _BUILD_LIVENESSMGM_
        AnalogIn_IDE_FS_Liveness_Acq();
        LivenessMgm_T5ms();
#endif

#ifdef _BUILD_BUCKDIAGMGM_
        BuckDiagMgm_5ms();
#endif

        TDNTimeout_mng();
    }
    
#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer5ms);
        starttimer5ms = (starttimer5ms - starttimer_old5ms);
        TIMING_TicksToMicroSeconds(starttimer5ms, &starttimer5ms);
        if ((uint32_T)(starttimer5ms) > TaskTimer5ms)
        {
            TaskTimer5ms = (uint32_T)(starttimer5ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

void FuncTask10ms(void)     
{
    Func10ms();  
    TerminateTask();
}

void Func10ms(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_old10ms;
    uint64_T starttimer10ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old10ms);
    }
    else { /* MISRA */ }
#endif

    if(CntTaskT10ms == 250u)
    {
        //reset counter to handle overflow
        CntTaskT10ms = 1u;
    }
    else
    {
        CntTaskT10ms++;
    }
    
    Func10msBody();
    
    if ((CntTaskT10ms % 2u)== 0u)
    {
        Func20ms();
    }
    else { /* MISRA */ }
    
    if ((CntTaskT10ms % 5u) == 0u)
    {
        Func50ms();
    }
    else { /* MISRA */ }
    
#ifdef _BUILD_CCP_
#ifdef  _OSEK_
    // CCP 10 ms monitoring
    GetResource((ResourceType)RES_CCP);
#endif
    CCP_PeriodicMonitorTask10ms();
#ifdef  _OSEK_
    ReleaseResource((ResourceType)RES_CCP);
#endif
#endif

#ifdef _BUILD_TIMING_
    TIMING_UpdateTimeStampsT10ms();
#endif

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer10ms);
        starttimer10ms = (starttimer10ms - starttimer_old10ms);
        TIMING_TicksToMicroSeconds(starttimer10ms, &starttimer10ms);
        if ((uint32_T)(starttimer10ms) > TaskTimer10ms)
        {
            TaskTimer10ms = (uint32_T)(starttimer10ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}   // end Task10ms()

void Func10msBody(void)
{
    static boolean_T can10ms_disRX = TRUE;
   
#ifdef _TEST_DIGIO_
    // DIGIO_Test();
#endif

    if ((taskPowerOnCompleted != 0u) && (DISABLEINT == 0u))
    {
#ifdef _BUILD_DIAGCANMGM_
        if (DIAG_resetTimeout >= 2u)
        {
            DIAG_resetTimeout++;
        }
        if (DIAG_resetTimeout == 13u)
        {
#ifdef _BUILD_WDT_SBC_
#ifdef _BUILD_WDT_EVB_
            SYS_SwRST();
#else
            WDT_ExtSyncISRReset();
#endif
#else
            SYS_SwRST();
#endif
        }

        DIAGCANMGM_DiagApl();
#endif
#ifdef _BUILD_DIGIN_
        DigIn_T10ms();
#endif  

#ifdef _BUILD_ANALOGIN_
        AnalogIn_T10ms();
#endif

#ifdef _BUILD_CANMGM_
        if (can10ms_disRX == TRUE) //Reception has been previusly disabled
        {
            CanMgm_Enable_CanRecv10ms();
            can10ms_disRX = FALSE; //to avoid continously enabling
        }
        else
        {            
            // DO NOTHING - RX has been already enabled
        }
        CanMgm_CanRecv10ms();

        CanMgm_CntNoDiag();
#endif

#ifdef _BUILD_WDT_SBC_
        WDT_T10ms();
#endif

#ifdef _BUILD_RELAYMGM_
        RelayMgm_T10ms();
#endif

#ifdef _BUILD_TEMPECUMGM_
        TempECUMgm_10ms();
#endif

#ifdef _BUILD_COILTARGET_
        CoilTarget_T10ms();
#endif

#ifdef _BUILD_PWRMGM_
        PwrMgm_10ms();      /* To be called after CoilTarget_T10ms */
#endif

#ifdef _BUILD_COILANGPATTERN_
        CoilTimPattern_10ms();
#endif

#ifdef _BUILD_TSPARKCTRLADAT_
        TSparkCtrlAdat_T10ms();
#endif

#ifdef _BUILD_IONKNOCKAIRCORR_
        IonKnockAirCorr_10ms();
#endif

#ifdef _BUILD_IONKNOCKEN_
        IonKnockEn_10ms();
#endif

#ifdef _BUILD_IONKNOCKINT_
        IonKnockInt_10ms();
#endif

#ifdef _BUILD_P2NOISEDETECT_
        P2NoiseDetect_10ms();
#endif

#ifdef _BUILD_KNOCKCORRNOM_
        KnockCorrNom_10ms();
#endif

#ifdef _BUILD_KNOCKCORRADP_
        KnockCorrAdp_10ms();
#endif

#ifdef _BUILD_KNOCKCORRTOT_
        KnockCorrTot_10ms();
#endif

#ifdef _BUILD_CANMGM_
        CanMgmOut_T10ms();
#endif
    
#ifdef _BUILD_DIAGMGM_
        DiagMgm_T10ms();
#endif

#ifdef _BUILD_RECMGM_
        RecMgm_T10ms(); 
#endif  

#ifdef _TEST_ADC_
        ret_adcGet_sw_T0 =  ADC_GetSampleResSoftTrig (21 , &adcGet_sw_T0 , 12);
        if(ret_adcGet_sw_T0 != 0u)
        {
            err_ret_adcGet_sw_T0++;
        }

        ret_adcGet_sw_T1 =  ADC_GetSampleResSoftTrig (22 , &adcGet_sw_T1 , 12);
        if(ret_adcGet_sw_T1 != 0u)
        {
            err_ret_adcGet_sw_T1++;
        }
#endif

#ifdef _BUILD_CAN_
#ifdef _TEST_CAN_
        CAN_Test();
#ifdef SPC574K2_EVB_CAN
        TTCAN_Test();
#endif
#endif
#endif  

#ifdef _BUILD_SAFETYMNGR_
        SafetyMngr_Periodic10ms();
#endif // _BUILD_SAFETYMNGR_


    }
}

void Func20ms(void)
{
    CntTaskT20ms++;
    
    if ((taskPowerOnCompleted != 0u) && (DISABLEINT == 0u))
    {
#ifdef _BUILD_CANMGM_
        CanMgm_CanRecv20ms();
#endif

#ifdef _BUILD_CANMGM_
        CanMgmOut_T20ms();
#endif
    }
}

void Func50ms(void)
{
    static boolean_T can50ms_disRX = TRUE;

    CntTaskT50ms++;

    if ((taskPowerOnCompleted != 0u) && (DISABLEINT == 0u))
    {
#ifdef _BUILD_CANMGM_
        if (can50ms_disRX == TRUE) //Reception has been previusly disabled
        {
            CanMgm_Enable_CanRecv50ms();
            can50ms_disRX = FALSE; //to avoid continously enabling
        }
        else
        {            
            // DO NOTHING - RX has been already enabled
        }

        CanMgm_CanRecv50ms();

        CanMgmOut_T50ms();
#endif
    }

}

void FuncTask100ms(void)
{
    Func100ms();
    TerminateTask();
}   // end Task100ms()


void Func100ms(void)
{
#ifdef _TEST_TIMING_
    uint64_T starttimer_old100ms;
    uint64_T starttimer100ms;
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer_old100ms);
    }
    else { /* MISRA */ }
#endif

    CntTaskT100ms++;

    if ((taskPowerOnCompleted != 0u) && (DISABLEINT == 0u))
    {
#ifdef _BUILD_CANMGM_
        CanMgm_CanRecv100ms();
#endif

#ifdef _BUILD_TEMPMGM_
        TempMgm_T100ms();
#endif

#ifdef _BUILD_RONDETECTEST_
	    RonDetectEst_100ms();
#endif
#ifdef _BUILD_RONDETECTMGM_
	    RonDetectFuel_100ms();
	    RonDetectMgm_100ms();
#endif
	   	   


#ifdef _BUILD_DIGIN_
        DigIn_Update_EngType();
#endif


#ifdef _BUILD_CANMGM_
    if (Rpm == 0u)
    {
        CanMgm_DebounceTDC(CntTaskT100ms & 0x01u);
    }
#endif

#ifdef TEST_VRS_RECONFIG
        if(FlgVRSReconfig == 1u)
        {
            GQS_ReConfig();
            FlgVRSReconfig = 0u; /* to avoid continuous re-config */
        }
#endif
    
#ifdef _BUILD_CANMGM_
        //CanMgmOut_T100ms();
#endif

#ifdef _BUILD_RECOVERY_
#ifdef  _BUILD_RECOVERY_IVOR2_TEST_
        FuncRecoveryTest();
#endif

#ifdef _TEST_SAFETYMNGR_

#ifdef _BUILD_FLASHCHECK_SM_TEST_
        SafetyMngr_Test_FlashSBE((uint32_T)&__BACKUP_START);
#endif /*_BUILD_FLASHCHECK_SM_TEST_*/


#ifdef _BUILD_RAMCHECK_SM_TEST_
        SafetyMngr_Test_RamSBE((uint32_T)&__END_OF_RAM);
#endif /*_BUILD_RAMCHECK_SM_TEST_*/

#endif /*_TEST_SAFETYMNGR_*/


#ifdef _BUILD_RECOVERY_IVOR2_TEST_
#if 0
        if (ENECCTEST != 0u)
        {
            if (EnECCTest == 0xAEu) //0xAE=174.
            {
                EnECCTest = 0u;
                //Generate_noncorrectable_ECC_error();
                DEBUG_Ivor2Recovery_Ram = 1u;
                FuncRecoveryTest();
                DEBUG_Ivor2Recovery_Ram = 0u;
            }
            else { /* MISRA */ }
        }
        else { /* MISRA */ }
#endif
#endif

#ifdef _BUILD_RECOVERY_IVOR2_TEST_
#if 0
        if (ENFLASHTEST != 0u)
        {
            if (EnFlashTestITL == 0xAEu) //0xAE=174.
            {
                EnFlashTestITL = 0u;
                #if 0
                DEBUG_Ivor2Recovery_Appl_BLK0 = 1u;
                DEBUG_Ivor2Recovery_Appl_BLK1 = 1u;
                DEBUG_Ivor2Recovery_Appl_BLK2 = 1u;
                DEBUG_Ivor2Recovery_Appl_BLK3 = 1u;
                DEBUG_Ivor2Recovery_Appl_BLK4 = 1u;
                DEBUG_Ivor2Recovery_Appl_BLK5 = 1u;
                DEBUG_Ivor2Recovery_Appl_BLK6 = 1u;
                DEBUG_Ivor2Recovery_Appl_BLK7 = 1u;
                DEBUG_Ivor2Recovery_Appl_BLK8 = 1u;
                DEBUG_Ivor2Recovery_Calib = 1u;
                DEBUG_Ivor2Recovery_Boot_Blk0 = 1u;
                DEBUG_Ivor2Recovery_Boot_Blk1 = 1u;
                DEBUG_Ivor2Recovery_Boot_Blk2 = 1u;
                DEBUG_Ivor2Recovery_Backup = 1u;
                DEBUG_Ivor2Recovery_EE_Page0_Blk0 = 1u;
                DEBUG_Ivor2Recovery_EE_Page0_Blk1 = 1u;
                DEBUG_Ivor2Recovery_EE_Page1_Blk0 = 1u;
                DEBUG_Ivor2Recovery_EE_Page1_Blk1 = 1u;
                DEBUG_Ivor2Recovery_EE_Page0_Blk1_Page1_Blk0 = 1u;
                #endif
                #if 1
                DEBUG_Ivor2Recovery_EE_Page0_Blk0_Page1_Blk1 = 1u;
                #endif
                #if 0
                DEBUG_Ivor2Recovery_EE_TrigIvor2 = 1u;
                #endif
                FuncRecoveryTest();
                #if 0
                DEBUG_Ivor2Recovery_Appl_BLK0 = 0u;
                DEBUG_Ivor2Recovery_Appl_BLK1 = 0u;
                DEBUG_Ivor2Recovery_Appl_BLK2 = 0u;
                DEBUG_Ivor2Recovery_Appl_BLK3 = 0u;
                DEBUG_Ivor2Recovery_Appl_BLK4 = 0u;
                DEBUG_Ivor2Recovery_Appl_BLK5 = 0u;
                DEBUG_Ivor2Recovery_Appl_BLK6 = 0u;
                DEBUG_Ivor2Recovery_Appl_BLK7 = 0u;
                DEBUG_Ivor2Recovery_Appl_BLK8 = 0u;
                DEBUG_Ivor2Recovery_Calib = 0u;
                DEBUG_Ivor2Recovery_Boot_Blk0 = 0u;
                DEBUG_Ivor2Recovery_Boot_Blk1 = 0u;
                DEBUG_Ivor2Recovery_Boot_Blk2 = 0u;
                DEBUG_Ivor2Recovery_Backup = 0u;
                DEBUG_Ivor2Recovery_EE_Page0_Blk0 = 0u;
                DEBUG_Ivor2Recovery_EE_Page0_Blk1 = 0u;
                DEBUG_Ivor2Recovery_EE_Page1_Blk0 = 0u;
                DEBUG_Ivor2Recovery_EE_Page1_Blk1 = 0u;
                DEBUG_Ivor2Recovery_EE_Page0_Blk1_Page1_Blk0 = 0u;
                #endif
                #if 1
                DEBUG_Ivor2Recovery_EE_Page0_Blk0_Page1_Blk1 = 0u;
                #endif
                #if 0
                DEBUG_Ivor2Recovery_EE_TrigIvor2 = 1u;
                #endif
            }
            else { /* MISRA */ }
        }
        else { /* MISRA */ }
#endif
#endif

#endif

#ifdef _TEST_FLASH_
       FLASH_Test();
#endif

#ifdef _BUILD_ANALOGIN_
#if (BOARD_TYPE == BOARD_EISB8F_A)

        AnalogIn_T100ms();
#endif /* BOARD_TYPE */
#endif

#ifdef _BUILD_FLASHMGM_
        FLASHMGM_FlashTestStart();
#endif

#ifdef _BUILD_TIMING_
        WorkingTime_T100ms();
#endif

#ifdef _BUILD_CCP_
#ifdef  _OSEK_
        /* CCP 100 ms monitoring */
        GetResource((ResourceType)RES_CCP);
#endif
        CCP_PeriodicMonitorTask100ms();
#ifdef  _OSEK_
        ReleaseResource((ResourceType)RES_CCP);
#endif
#endif
    }

#ifdef _TEST_TIMING_
    if (ENTESTTIMING != 0u)
    {
        TIMING_GetAbsTimer(&starttimer100ms);
        starttimer100ms = (starttimer100ms - starttimer_old100ms);
        TIMING_TicksToMicroSeconds(starttimer100ms, &starttimer100ms);
        if ((uint32_T)(starttimer100ms) > TaskTimer100ms)
        {
            TaskTimer100ms = (uint32_T)(starttimer100ms);
        }
        else
        {
            /* None */
        }
    }
    else { /* MISRA */ }
#endif
}

#ifdef _BUILD_WDT_

#define TEST_CNT      100u            // [counter]

#define T5MS_PERIOD   5000u    // [us]
#define T10MS_PERIOD  10000u   // [us]
#define T100MS_PERIOD 100000u  // [us]

#define TASK_ERR_MAX  MAX_uint16_T      // [couner] MAX_uint16_T

uint8_T LiveTestCnt;
uint8_T LiveTestResult;
uint8_T StSync_old;
uint16_T TaskError;

void TaskLive_Init(void)
{    
  LiveTestCnt     = 0;
  LiveTestResult  = 1;
  TaskError       = 0;
}

void TaskLive_ResetCnt(void)
{    
  CntTaskT4ms       = 0u;    // Task T5ms
  CntTaskT10ms        = 0u;    // Task T10ms
  CntTaskT100ms        = 0u;    // Task T100ms
  //CntTaskEOA        = 0;    // Task EOA
}

#endif

void CpuLoad_Init(void);

void CpuLoad_Init(void)
{
#if 0  //MC
    CpuLoadPerc = 0u;
    MinCntCpuLoad = MAX_uint16_T;
    CntCpuLoadMin = MAX_uint32_T;
    CntCpuLoadMax = MIN_uint32_T;    
    CntCpuLoad = 0u;
    TIMING_GetAbsTimer(&TimerCpuLoad);
    MAXCPULOADTIME0 = 200000u;
    MaxCpuLoadCnt = MAXCPULOADCNT0;
    DISABLEINT = 0u;
    stDISABLEINT = 0u;
    CpuTIMEINIT = 20u;
#endif
}

void CpuLoad(void) 
{
#if 0  //MC
    uint32_T tmpcpuloadper = 0u;
    static uint64_T timer_l = 0u;    
    static uint64_T dtimer = 0u;
    static uint8_T flgTrigCpuLoadCnt = 0u;
    
    if ((DISABLEINT != 0u) && (stDISABLEINT == 0u)) 
    {
        //DisableAllInterrupts();
        stDISABLEINT = 1u;
        CntDisableCPUInit = 0u;
        CntCpuLoadMin = MAX_uint32_T;
        CntCpuLoadMax = MIN_uint32_T; 
        CntCpuLoad = 0u;
        TIMING_GetAbsTimer(&TimerCpuLoad);
    }
    else if ((DISABLEINT == 0u) && (stDISABLEINT != 0u)) 
    {
        stDISABLEINT = 0u;   
        MaxCpuLoadCnt = ((CntCpuLoadMax + CntCpuLoadMin) >> 1);
        CntCpuLoad = 0u;
        TIMING_GetAbsTimer(&TimerCpuLoad);
        //EnableAllInterrupts();
    }
    else /* MISRA 14.10 */
    {
        /* Non fare niente. */
    }
    
    TIMING_GetAbsTimer(&timer_l);
    TIMING_TicksToMicroSeconds((timer_l - TimerCpuLoad), &dtimer);
    if (dtimer >= MAXCPULOADTIME0) 
    {
        if (stDISABLEINT == 0u)
        {
            MaxCntCpuLoad = (uint32_T)((dtimer * MaxCpuLoadCnt) / MAXCPULOADTIME0);

            if (MaxCntCpuLoad > CntCpuLoad)
            {   
                tmpcpuloadper = ((MaxCntCpuLoad - CntCpuLoad) * 1600u) / MaxCntCpuLoad;
                CpuLoadPerc = (uint16_T)(tmpcpuloadper);
                EEMaxCpuLoadPerc = max(EEMaxCpuLoadPerc, CpuLoadPerc);
                if ((CpuLoadPerc > THRCPULOAD) && (flgTrigCpuLoadCnt == 0u))
                {
                    flgTrigCpuLoadCnt = 1u;
                    EECntCpuLoadPerc++;
                }
                else { /* MISRA */ }
            }    
            else
            {    
                CpuLoadPerc = 0u;
            }
            /* Eseguo il test dello stack in questo punto per non disturbare il calcolo di CpuLoad */
            CheckStackUsage();
            
            MinCntCpuLoad = min (MinCntCpuLoad, CntCpuLoad);
            
            TIMING_GetAbsTimer(&timer_l);
        }
        else
        {
            if (CntCpuLoad < CntCpuLoadMin)
            {    
                CntCpuLoadMin = CntCpuLoad;
            }    
            else if (CntCpuLoad > CntCpuLoadMax)
            {    
                CntCpuLoadMax = CntCpuLoad;
            }    
            else
            {
                 /* MISRA 14.10 */
            }
        }
        CntCpuLoad = 0u;
        CntDisableCPUInit++;
        TimerCpuLoad = timer_l;
    } 
    else 
    {
        if (CntDisableCPUInit >= CpuTIMEINIT)
        {
            DISABLEINT = 0u;
            CntDisableCPUInit = 0u;
        }
        else
        {
            /* Non fare niente. */    
        }
        CntCpuLoad++;
    }
#endif
}

void CheckStackUsage(void) //MC, initially adapted for core2: Z4
{
    uint32_T *stackStart = &__SP2_INIT;
    uint32_T *stackEnd = &__SP2_END;
    uint32_T *stackChecker = stackEnd;

    while (stackChecker != stackStart) 
    {
        if (*stackChecker != 0u)
        {    
            break;
        }
#pragma ghs startnomisra // 17.4
        stackChecker++;
#pragma ghs endnomisra
    }
    stackPeakUsage = (uint32_T)stackStart - (uint32_T)(stackChecker);
    StoreInc = ((100u * (stackPeakUsage << 4)) / 4096u);
}

uint8_T Debug_SysReset_c0 = 0u;
uint8_T Debug_SetIrqTest = 0u;
uint8_T Debug_SetIRQ_to_C2 = 0u;
uint8_T Debug_SetIRQ0_c0 = 0u;
#ifdef _TEST_VSRAMMGM_
volatile uint8_T testVsramMgm = 0u;
#endif
#ifdef _TEST_EEMGM_
volatile uint8_T testEEmgm = 0u;
#endif

#ifdef  _TEST_CACHE_
uint8_T testCache = 0u;

/*   IcacheInjErrEnable_c0 */
/*
Set Instruction Cache Error Injection Enable = 1, 
A double-bit error will be injected into each doubleword written into the cache
by inverting the two uppermost parity check bits (p_chk[0:1])
*/
static void IcacheInjErrEnable_c0(void) 
{

   __asm (" mfspr r5, 1011");
   __asm (" e_or2is  r5, 0x8000@h");
   __asm (" e_or2i r5, 0x8000@l");
   __asm (" se_isync ");
   __asm (" msync ");
   __asm (" mtspr 1011, r5  ");
}
#endif

void BackgroundActivities(void)
{        
    uint8_T bkt;
    bkt = (uint8_T) (BackgroundTaskID);
    ActivateTask(bkt);
}

int32_T main (void) // Core0(z4)
{

    /* StartUp e200z4 */
    app_data_c0_section_init();
    app_text_c0_section_init();

    /* StartUp OS */
#ifdef _OSEK_  //MC, Moved from Core2(z2) to Core0(z4)
    StartOS((AppModeType)OSAPPMODE); 
#endif

    appManager();

    TaskPowerOn();

    //CpuLoad_Init();

    //stackPeakUsage = 0;
    
    PIT_Enable(PIT0, PIT0_CH0);  //Enable OS TimeBase counter interrupt
    STM_Enable();                //For STM counter for Safety Mechanism  

    Gtm_Eisb_Config();

    EnableAllInterrupts();

    for(;;)
    {
        CntMainLoop++;
    
#ifdef _BUILD_PIT_
#ifdef _TEST_PIT_
        if (PIT_ConfigStatus == 1u)
        {
            if (Debug_Pit_Disable == 1u)
            {
                PIT_Disable(PIT0, PIT0_CH0 );
                Debug_Pit_Disable = 0u;
            }
        }
#endif
#endif

#ifdef TASK_SETIRQ_TEST
        if (Debug_SetIrqTest == 1u)
        { 
            Task_SetIrq_Test(226u); //226 =PIT0_CH0

            Debug_SetIrqTest = 0u;
        }
#endif

#ifdef TASK_SWSETCLRINT_TEST
        if (Debug_SetIRQ0_c0 == 1u)
        {
            SetIRQ_c0();
            Debug_SetIRQ0_c0 = 0u;
            
        }

        if (Debug_SetIRQ_to_C2 == 1u)
        {
            SetIRQ_to_c2();
            Debug_SetIRQ_to_C2 = 0u;
            
        }

        if (Debug_SysReset_c0 == 1u)
        {
           SYS_SwRST();
           Debug_SysReset_c0 = 0u;
           CntMainLoop = 0u;
        }
#endif

#ifdef _TEST_VSRAMMGM_
        if (testVsramMgm == 1u)
        {
            testVsramMgm = 0u;
            VSRAMMGM_Test();
        }
#endif

#ifdef _TEST_EEMGM_
        if(testEEmgm == 1u)
        {
            testEEmgm = 0u;
            EEMGM_test();
            SYS_SwRST();
        }
#endif

#ifdef  _TEST_CACHE_
        if(testCache == 1u)
        {
          /* Error Injection */
          IcacheInjErrEnable_c0();
          testCache = 2u;
        }
        /* Cache re-configuration, disable the error injection*/
        if(testCache == 3u)
        {
          cfg_CACHE();
          testCache = 0u;
        }
#endif

        /* Calcolo CpuLoad 
        CpuLoad();*/

        /* Task background */
        BackgroundActivities();
    }
}

static inline void Memu_clear(void)
{
    //Register affected by POR reset
    MEMU.ERR_FLAG.R = 0xFFFFFFFFu;
    MEMU.SYS_RAM_UNCERR_STS.B.VLD = 0u;
}

#ifdef _BUILD_SAFETYMNGR_FLASHCHECK_
/******************************************************************************
**   Function    : FlashSBCEenable
**
**   Description:
**    This function enables Single Bit Correction notification for flash.
**
**   Parameters :
**
**
**   Returns:
**    NO_ERROR                     - No error
**    C55_ERROR_CONFIG             - C55 configuration error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: This function is for test only.
**
**   EA GUID:
******************************************************************************/
static int16_T FlashSBCEenable(void)
{

    int16_T retVal = (int16_T)C55_ERROR_CONFIG;

    if( ((FLASH.MCR.R) & ((uint32_T)C55_MCR_ERS | (uint32_T)C55_MCR_PGM)) == 0U) /*no program/erase request*/
    {
        FLASH.UT0.R = (uint32_T)C55_USER_TEST_ENABLE_PASSWORD; /* Enable UTE */
        FLASH.UT0.R |= (C55_UT0_SBCE);               /* Enable SBCE */
        FLASH.UT0.R &= ~(C55_UT0_UTE);               /* Disable UTE */
    }
    if( ((FLASH.UT0.R) & (C55_UT0_SBCE | C55_UT0_UTE)) == C55_UT0_SBCE )
    {
        retVal = NO_ERROR;
    }
    return retVal;
}

#endif /* _BUILD_SAFETYMNGR_FLASHCHECK_ */

static void appManager(void)
{
int16_T errtmp = 0;

#ifdef _BUILD_RECOVERY_
    IVOR1_UserFunction_c0 = &ECC_IVOR_faultmgm; //to be duplicated;
#else /*_BUILD_RECOVERY_*/
    IVOR1_UserFunction_c0 = &IVOR_Common_ISR;//to be duplicated;
#endif

    IVOR_Common_ManagerUserFunction_c0 = &IVOR_Common_ISR;//to be duplicated;

#ifdef DEBUG_RECOVERY
    Debug_RecoveryEcu_MEMU_ERR_PwrON = MEMU.ERR_FLAG.R; 
#endif
    Memu_clear();
    
#ifdef  _BUILD_FLASH_
    FLASH_ConfigStatus = 0u;
    errtmp = FLASH_Config();
    if (errtmp < 0)
    {
#ifdef  _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,FLASH_IDX);
#endif
    }
#ifdef _BUILD_SAFETYMNGR_FLASHCHECK_
    FlashSBCEenable();
#endif /* _BUILD_SAFETYMNGR_FLASHCHECK_ */

#endif /* _BUILD_FLASH_ */

#ifdef _BUILD_EEMGM_
    EEMGM_PowerOn();
#endif

#ifdef _BUILD_DIAGCANMGM_
    UDS_Init();
#endif


}

#ifdef _BUILD_CANMGM_
static void CanMgm_Enable_CanRecv10ms(void)
{
#if(CAN_TYPE == CAN_P15)
    /* Reception shall be enabled  */
    CAN_EnableReceive(POWER_TRAIN_CAN,ECM_EISB_1_BUF);
#elif ((CAN_TYPE == CAN_F173)||(CAN_TYPE == CAN_F171))
    /* Reception shall be enabled  */
//    CAN_EnableReceive(POWER_TRAIN_CAN,MOTION_DX_BUF);
    /* Reception shall be enabled  */
//    CAN_EnableReceive(VEHICLE_CAN,NFR_ASR1_BUF);
#else
#warning no valid POWERTRAIN CAN_TYPE DEFINED
#endif
}

static void CanMgm_Enable_CanRecv50ms(void)
{
#if(CAN_TYPE == CAN_P15)
    /* Reception shall be enabled  */
    CAN_EnableReceive(POWER_TRAIN_CAN, ECM_EISB_2_BUF);
#elif ((CAN_TYPE == CAN_F173)||(CAN_TYPE == CAN_F171))
#else
#warning no valid POWERTRAIN CAN_TYPE DEFINED
#endif

}
#endif //_BUILD_CANMGM_


