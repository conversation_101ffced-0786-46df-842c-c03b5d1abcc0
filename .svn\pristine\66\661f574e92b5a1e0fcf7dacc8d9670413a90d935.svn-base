/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MCAN
**  Filename        :  Mcan_out.h
**  Created on      :  09-giu-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
/*****************************************************************************
**
**                        MCAN Description
**
**  MCAN Sw module provides driver and interface to manage MCAN peripheral on chip.
**
******************************************************************************/

#ifndef _MCAN_OUT_H_
#define _MCAN_OUT_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* MCAN Engine */
#define MCAN_ENG_A       0u
#define MCAN_ENG_B       1u

/* Last Message Buffer availables on MCAN Engine A */
#define NUM_BUFFER_MCAN_A        64u
/* Last Message Buffer availables on MCAN Engine B */
#define NUM_BUFFER_MCAN_B        64u

/* Maximum data length code for message buffer structure */
#define MAX_DLC_DIM         8u

/* Error Code - The chosen baudrate is not configurable */
#define BIT_RATE_ERROR          0x02u
/* Error Code - The disabling operation is unsuccessful */
#define NOK_MODULE_DISABLE      0x03u
/* Error Code - The enabling operation is unsuccessful */
#define NOK_MODULE_ENABLE       0x04u
/* Error Code - MCAN engine entered "Bus Off" state */
#define CAN_BUSOFF              ((int16_T)0x05)
/* Error Code - Error active bus state */
#define CAN_ERR_ACTIVE          0x09u
/* Error Code - Error passive bus state */
#define CAN_ERR_PASSIVE         0x0Au
/* Error Code - MCAN is transmitting a message */
#define CAN_TX_BUSY             0x06u
/* Error Code - MCAN is receiving a message */
#define CAN_RX_BUSY             0x07u
/* Error Code - Message buffer or associated  message buffer structure is empty */
#define CAN_RX_BUFFER_EMPTY     ((int16_T)0x04)
/* Error Code - Message buffer or associated  message buffer structure is overrun */
#define CAN_RX_BUFFER_OVERRUN   ((int16_T)0x06)
/* Error Code - Message buffer or associated  message buffer structure has message counter error */
#define CAN_RX_CNT_ERR          ((int16_T)0x0E)
/* Error Code - Message buffer or associated  message buffer structure has message crc error */
#define CAN_RX_CRC_ERR          ((int16_T)0x0F)

/* Message buffer structure is full */
#define BUFFER_FULL             0x1u
/* Message buffer structure is overrun */
#define BUFFER_OVERRUN          0x2u

/* CAN Buffer type */
#define CAN_STD                 0u
#define CAN_XTD                 1u

/* Return values */
#define NO_ERROR                      0
#define CAN_ALREADY_DISABLED          1
#define CAN_ALREADY_ENABLED           2
#define MCAN_TIMEOUT_ERROR            4

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* Typedef struct for CAN message used by CAN_ReceiveMsg(copies data directly from Message Buffer) */
typedef struct
{
    uint16_T ID;
    uint8_T  DATA[8];
    uint8_T  DLC;
} CANMsg_T;

/* Typedef struct for  CAN message used by CAN_RxData(copies data from the structure associated with Message Buffer) */
typedef struct CANBuff_T
{
    uint8_T b[MAX_DLC_DIM];
    uint8_T ide;
    uint32_T id;
    uint8_T dlc;
} CANBuffer_T;

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint16_T CANConfigStatus;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : CAN_EX_Tx_Rx_Config
**
**   Description:
**    Assign pointers to Tx/Rx exception functions.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] void* txFuncEx : pointer to Tx exception function
**    [in] void* rxFuncEx : pointer to Rx exception function
**
**   Returns:
**    void
**
******************************************************************************/
extern void CAN_EX_Tx_Rx_Config (uint8_T channel, void (*txFuncEx)(void), void (*rxFuncEx)(void));

/******************************************************************************
**   Function    : CAN_Config
**
**   Description:
**    Configuration of on-chip MCAN peripherals and buffers.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR                      - MCAN peripherals correctly configured
**    PERIPHERAL_ALREADY_CONFIGURED - MCAN peripherals already configured
**
******************************************************************************/
extern int16_T CAN_Config (void);

/******************************************************************************
**   Function    : CAN_ResetBufferTx
**
**   Description:
**    Reset the selected Tx MB of MCAN.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T nBuf : buffer
**
**   Returns:
**    NO_ERROR - Reset of the selected MB of MCAN performed correctly
**
******************************************************************************/
extern int16_T CAN_ResetBufferTx (uint8_T channel, uint8_T nBuf);

/******************************************************************************
**   Function    : CAN_ResetBufferRx
**
**   Description:
**    Reset the selected Rx MB of MCAN.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T nBufType : number of the Rx Buffer related to its type
**    [in] uint8_T type : type of the buffer (standard or extended)
**
**   Returns:
**    NO_ERROR - Reset of the selected MB of MCAN performed correctly
**
******************************************************************************/
extern int16_T CAN_ResetBufferRx (uint8_T channel, uint8_T nBufType, uint8_T type);

/******************************************************************************
**   Function    : CAN_TxData
**
**   Description:
**    Performs the data transmission.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T nBuf : Number of the Tx Buffer
**    [in] uint8_T* pData : Pointer to data to be sent
**
**   Returns:
**    NO_ERROR     - Data transmission performed correctly
**    CAN_MSG_WAIT - Tx Buffer unavailable
**
******************************************************************************/
extern int16_T CAN_TxData (uint8_T channel, uint8_T nBuf, uint8_T* pData);

/******************************************************************************
**   Function    : CAN_TxDataOptimized
**
**   Description:
**    Performs the transmission of exactly N-bytes indicated by dlc parameter.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T nBuf : Number of the Tx Buffer
**    [in] uint8_T* pData : Pointer to data to be sent
**    [in] uint8_T dlc : DLC field
**
**   Returns:
**    NO_ERROR     - Data transmission performed correctly
**    CAN_MSG_WAIT - Tx Buffer unavailable
**
******************************************************************************/
extern int16_T CAN_TxDataOptimized (uint8_T channel, uint8_T nBuf, uint8_T* pData, uint8_T dlc);

/******************************************************************************
**   Function    : CAN_EnableReceive
**
**   Description:
**    Activates reception of the Rx Message Buffer in MCAN channel.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T nBufType : number of the Rx Buffer related to its type
**    [in] uint8_T type : type of the buffer (standard or extended)
**
**   Returns:
**    NO_ERROR   - Reception enabled correctly
**    CAN_BUSOFF - MCAN channel is in Bus Off state
**
******************************************************************************/
extern int16_T CAN_EnableReceive (uint8_T channel, uint8_T nBufType, uint8_T type);

/******************************************************************************
**   Function    : CAN_PowerTrain_EnableReceive
**
**   Description:
**    Activates reception of the Rx Message Buffers used for Powertrain CAN 
**    in MCAN channel.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Selected Rx Buffers enabled correctly
**
******************************************************************************/
#ifdef POWER_TRAIN_CAN
extern int16_T CAN_PowerTrain_EnableReceive (void);
#endif

/******************************************************************************
**   Function    : CAN_Vehicle_EnableReceive
**
**   Description:
**    Activates reception of the Rx Message Buffers used for Vehicle CAN in 
**    MCAN channel.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Selected Rx Buffers enabled correctly
**
******************************************************************************/
#ifdef VEHICLE_CAN
extern int16_T CAN_Vehicle_EnableReceive (void);
#endif

/******************************************************************************
**   Function    : CAN_RxData
**
**   Description:
**    Copies data previously received in a memory location pointed by ptr.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T rxBuf : Rx Buffer
**    [in] struct CANBuff_T ** ptr : pointer to message data structure
**
**   Returns:
**    NO_ERROR                - Data correctly copied in a memory location
**    CAN_BUSOFF              - MCAN channel is in Bus Off state
**    CAN_RX_BUFFER_EMPTY     - CAN RX buffer is empty
**    CAN_RX_BUFFER_OVERRUN   - CAN RX buffer is in overrun
**
******************************************************************************/
extern int16_T CAN_RxData (uint8_T channel, uint8_T rxBuf, struct CANBuff_T ** ptr);

/******************************************************************************
**   Function    : CAN_ReceiveMsg
**
**   Description:
**    Polls message buffer to verify reception and stores receive data in memory
**    location pointed by ptr.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T rxBuf : Rx Buffer
**    [out] CANMsg_T * ptr : pointer to data structure where message is stored
**
**   Returns:
**    NO_ERROR            - Data receveid correctly
**    CAN_RX_BUFFER_EMPTY - Rx Buffer empty
**
******************************************************************************/
extern int16_T CAN_ReceiveMsg (uint8_T channel, uint8_T rxBuf, CANMsg_T * ptr);

/******************************************************************************
**   Function    : CAN_ErrIntDisable
**
**   Description:
**    Disables the error interrupt.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR           - Functionality correctly disabled
**    MCAN_TIMEOUT_ERROR - Timeout error
**
******************************************************************************/
extern int16_T CAN_ErrIntDisable (uint8_T channel);

/******************************************************************************
**   Function    : CAN_ErrIntEnable
**
**   Description:
**    Enables the error interrupt.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR           - Functionality correctly enabled
**    MCAN_TIMEOUT_ERROR - Timeout error
**
******************************************************************************/
extern int16_T CAN_ErrIntEnable (uint8_T channel);

/******************************************************************************
**   Function    : CAN_BusOffIntDisable
**
**   Description:
**    Disables the Bus Off interrupt.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR - Functionality correctly disabled
**
******************************************************************************/
extern int16_T CAN_BusOffIntDisable (uint8_T channel);

/******************************************************************************
**   Function    : CAN_Disable
**
**   Description:
**    Disables MCAN channel.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR             - MCAN channel correctly disabled
**    CAN_ALREADY_DISABLED - MCAN channel already disabled
**
******************************************************************************/
extern int16_T CAN_Disable (uint8_T channel);

/******************************************************************************
**   Function    : CAN_Enable
**
**   Description:
**    Enables MCAN channel.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR            - MCAN channel correctly enabled
**    CAN_ALREADY_ENABLED - MCAN channel already enabled
**    MCAN_TIMEOUT_ERROR  - Timeout error
**
******************************************************************************/
extern int16_T CAN_Enable (uint8_T channel);

/******************************************************************************
**   Function    : CAN_DisableReceive
**
**   Description:
**    Deactivates reception of a Message Buffer in MCAN channel.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T nBufType : number of the Rx Buffer related to its type
**    [in] uint8_T type : type of the buffer (standard or extended)
**
**   Returns:
**    NO_ERROR   - Reception disabled correctly
**    CAN_BUSOFF - MCAN channel is in Bus Off state
**
******************************************************************************/
extern int16_T CAN_DisableReceive (uint8_T channel, uint8_T nBufType, uint8_T type);

/******************************************************************************
**   Function    : CAN_BusOffRecEnable
**
**   Description:
**    Enables the automatic recovering from bus off state occurs according to 
**    the CAN Specification 2.0B.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR           - Functionality correctly enabled
**    MCAN_TIMEOUT_ERROR - Timeout error
**
******************************************************************************/
extern int16_T CAN_BusOffRecEnable (uint8_T channel);

/******************************************************************************
**   Function    : CAN_BusOffRecDisable
**
**   Description:
**    Disables the automatic recovering from bus off state occurs according to 
**    the CAN Specification 2.0B.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR           - Functionality correctly disabled
**    MCAN_TIMEOUT_ERROR - Timeout error
**
******************************************************************************/
extern int16_T CAN_BusOffRecDisable (uint8_T channel);

/******************************************************************************
**   Function    : CAN_BusOffIntEnable
**
**   Description:
**    Enables the Bus Off interrupt.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR - Functionality correctly enabled
**
******************************************************************************/
extern int16_T CAN_BusOffIntEnable (uint8_T channel);

/******************************************************************************
**   Function    : CAN_GetTxStatus
**
**   Description:
**    Gets the status of the MCAN Tx channel.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR    - MCAN channel is not busy
**    CAN_TX_BUSY - MCAN channel is busy
**
******************************************************************************/
extern int16_T CAN_GetTxStatus (uint8_T channel);

/******************************************************************************
**   Function    : CAN_GetStatus
**
**   Description:
**    Gets the status of the MCAN channel.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR        - MCAN channel is in Normal state
**    CAN_BUSOFF      - MCAN channel is in Bus Off state
**    CAN_ERR_PASSIVE - MCAN channel is in Error Passive state
**    CAN_ERR_ACTIVE  - MCAN channel is in Error Active state
**
******************************************************************************/
extern int16_T CAN_GetStatus (uint8_T channel);

/******************************************************************************
**   Function    : CAN_BusOffRecovery
**
**   Description:
**    Performs the Bus Off recovery.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR           - Reception enabled correctly
**    MCAN_TIMEOUT_ERROR - Timeout error
**
******************************************************************************/
extern int16_T CAN_BusOffRecovery (uint8_T channel);

/******************************************************************************
**   Function    : CAN_GetTxBufferStatus
**
**   Description:
**    Gets the status of the Tx Buffer.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T nBuf : Number of the Tx Buffer
**
**   Returns:
**    NO_ERROR    - Tx Buffer is in idle
**    CAN_TX_BUSY - Tx Buffer busy
**
******************************************************************************/
extern int16_T CAN_GetTxBufferStatus (uint8_T channel, uint8_T nBuf);

/******************************************************************************
**   Function    : CAN_EngineEnableReceive
**
**   Description:
**    This function enables the reception of all the CAN messages.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**
**   Returns:
**    NO_ERROR   - MCAN channel is in Normal state
**    CAN_BUSOFF - MCAN channel is in Bus Off state
**
******************************************************************************/
extern int16_T CAN_EngineEnableReceive (uint8_T channel);

/******************************************************************************
**   Function    : CAN_EngineMaskedEnableReceive
**
**   Description:
**    This function enable the reception of specific CAN messages.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T type : type of the message buffer (standard or extended)
**    [in] uint32_T maskRxEnableLow : Enable mask for the first 32 Rx Buffers (0-31)
**    [in] uint32_T maskRxEnableHigh : Enable mask for the second 32 Rx Buffers (32-63)
**
**   Returns:
**    NO_ERROR   - MCAN channel is in Normal state
**    CAN_BUSOFF - MCAN channel is in Bus Off state
**
******************************************************************************/
extern int16_T CAN_EngineMaskedEnableReceive (uint8_T channel, uint8_T type, uint32_T maskRxEnableLow, uint32_T maskRxEnableHigh);

/******************************************************************************
**   Function    : CAN_RxData_ISR
**
**   Description:
**    This function reads a specific CAN messages; it's called by the ISR 
**    handler of the selected MCAN channel.
**
**   Parameters :
**    [in] uint8_T channel : MCAN channel
**    [in] uint8_T rxBuf : Rx Message Buffers
**
**   Returns:
**    NO_ERROR - Reception of a message correctly performed
**
******************************************************************************/
extern int16_T CAN_RxData_ISR (uint8_T channel, uint8_T rxBuf);

/******************************************************************************
**   Function    : CAN_InitRAM
**
**   Description:
**    Initialize the Shared RAM.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CAN_InitRAM (void);

/******************************************************************************
**   Function    : CAN_CHA_L0
**
**   Description:
**    ISR handler for interrupt line 0 on MCAN_1.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CAN_CHA_L0 (void);

/******************************************************************************
**   Function    : CAN_CHA_L1
**
**   Description:
**    ISR handler for interrupt line 1 on MCAN_1.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CAN_CHA_L1 (void);

/******************************************************************************
**   Function    : CAN_CHB_L0
**
**   Description:
**    ISR handler for interrupt line 0 on MCAN_2.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CAN_CHB_L0(void);

/******************************************************************************
**   Function    : CAN_CHB_L1
**
**   Description:
**    ISR handler for interrupt line 1 on MCAN_2.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CAN_CHB_L1 (void);

#endif //_MCAN_OUT_H_

/****************************************************************************
 ****************************************************************************/
