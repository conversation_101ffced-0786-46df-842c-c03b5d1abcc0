/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_AM_11_SAFEMGM/tree/BIOS_S#$  */
/* $Revision:: 205844                                                                                         $  */
/* $Date:: 2022-02-03 15:22:40 +0100 (gio, 03 feb 2022)                                                       $  */
/* $Author:: MocciA                                                                                           $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_INT_out.h
**  Created on      :  03-Feb-2022 15:39:00
**  Original author :  Mocci A
******************************************************************************/
/*****************************************************************************
**
**                        SafetyMngr_INTC Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef SAFETYMNGR_INTC_OUT_H
#define SAFETYMNGR_INTC_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RTWtypes.h"

/* add here include files */

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_INTC_CheckCtx
**
**   Description:
 **    This function implements ISR_CHECK_PRIORITY and ISR_CHECK_CORE mechanism of 
**       "AN4446 - SPC574K72xx safety manual".
**
**   Parameters :
**    [in] uint16_T isrVectPos : ISR position inside the Vector table.
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_INTC_CheckCtx(uint16_T isrVectPos);

/******************************************************************************
**   Function    : SafetyMngr_INTC_CheckPeriodic
**
**   Description:
**    checks if IRQ flags in peripherals are all cleared
**
**   Parameters :
**    [inout] uint32_T * regs : Array that contains the values of the register
**    that has to be checked.
**    [inout] uint32_T * regMasks : Array with mask values that has to be used
**    for register check.
**    [in] uint8_T regNum :  be used for register check.
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_INTC_CheckPeriodic(uint32_T * regs, uint32_T * regMasks, uint8_T regNum);

#endif // SAFETYMNGR_INTC_OUT_H

/****************************************************************************
 ****************************************************************************/

