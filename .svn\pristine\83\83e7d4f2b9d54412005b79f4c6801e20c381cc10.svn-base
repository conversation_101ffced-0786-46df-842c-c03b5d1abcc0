/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef EEMGM_OUT_H
#define EEMGM_OUT_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "ee_out.h"

/*!
\defgroup PublicDefines Public Defines
\brief Defines with global scope

\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* EE possible tasks */
/// Dummy value used to perform no operations on EE IDs
#define EE_NO_EVENT                 (0u)
/// Before a normal powerlatch
#define EE_END_OF_POWERLATCH        (1u)
/// Before a normal powerlatch with engine run
#define EE_END_OF_POWERLATCH_RUN    (2u)
/// Before reset after an IVOR
#define EE_BEFORE_SW_RESET_IVOR     (3u)
/// Before reset after a UDS SW download
#define EE_AFTER_UDS_SW_DWLOAD      (4u)
/// After adaptive parameters invalidating request by KWP2000
#define EE_INVALIDATE_ADPT_PARAMS   (5u)
/// After diagnosis invalidating request by KWP2000
#define EE_INVALIDATE_DIAG          (6u)
/// After ECU data writing request by KWP2000
#define EE_WRITE_ECU_DATA           (7u)
/// After ECU supplier data writing request by KWP2000
#define EE_WRITE_SUPPL_DATA         (8u)
/// ECU Self WDT
#define EE_SELF_WDT                 (9u)
/// Number of possible tasks
#define EE_POSSIBLE_ACTIONS         (9u) // Remember not to count EE_NO_EVENT


/*!\egroup*/

/*!
\defgroup PublicTypedef Public Typedefs
\brief Types definitions with global scopes
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
///This is a public typedef 


/*!\egroup*/

/*!
\defgroup PublicInline Public Inline Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC INLINE FUNCTIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   Inline function name
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this inline function 
*/
/**************************************************************************/


/*!\egroup*/


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_T      EE_TaskToExec;
extern uint8_T      FlgFirstPowerOn;
extern uint8_T      EE_RecAttempts;
extern const char_T EE_Id_Version[EE_ID_TOT_NUMBER][sizeof(uint32_T)];
extern uint32_T     EEPhysErrCnt;
extern uint32_T     EELogicErrCnt;
extern uint8_T      FlgResetAdaptCylBal;


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
int16_T EEMGM_EETaskCmd(void);
void EEMGM_SetEventID(uint8_T EE_EventID);
void EEMGM_PowerOn(void);
void EEMGM_T100ms(void);
void EEMGM_SetFault(void);
#ifdef _TEST_EEMGM_
void EEMGM_test(void);
#endif //_TEST_EEMGM_


#endif // TEMPLATE_H

/****************************************************************************
 ****************************************************************************/


