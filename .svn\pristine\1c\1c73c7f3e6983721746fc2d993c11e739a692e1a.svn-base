/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmIn
**  Filename        :  CanMgmIn_F17x.h
**  Created on      :  26-nov-2020 09:37:00
**  Original author :  SantoroR
******************************************************************************/

#ifndef CANMGMIN_F17X_H
#define CANMGMIN_F17X_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Canmgmin_out.h"
#include "Canmgm_out.h"
#include "Mcan_out.h"
#include "mathlib.h"
#include "AnalogIn_out.h"
#include "DigIn_out.h"
#include "Diagmgm_out.h"
#include "utils.h"
#include "Diagcanmgm_out.h"
#include "SyncMgm_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define     CAN_ERROR_CNT_THR   5u   // = 5*Period message
#define     TIME_E_DATE_100_2_1000MS    10u

#define     MAX_LOAD    (510u*128u)   // = 510% con risoluzione 1/128
#define     CHECK_ANY_ABSENT_THR   5u   // = 5*10ms 50ms non messages

#define     VALID_VD        0u
#define     NOT_VALID_VD    1u

#define     BKRPMTIPIN_dim  5u /* Number of element used in vector. */
#define     BKTOILPOIL_dim  5u
#define     BKRPMPOIL_dim   12u

#define     EOILT_OFFSET    -48
#define     EOILP_FACTOR    5u

#define     KEYSTS_STOP     0x01u
#define     KEYSTS_PARK     0x02u
#define     KEYSTS_ON       0x04u
#define     KEYSTS_CRANK_ON 0x0Cu

///ENCRCALIVETEST values
#define     CAN_ALIVE_TEST_MASK 0x01u
#define     CAN_CRC_TEST_MASK 0x02u

#define BRAKE_FE3_ERR_60MS      3u      /// 3 x 20ms = 60ms
#define MOT1_ERR_30MS           3u      /// 3 x 10ms = 30ms
#define STATUS_B_CANX_ERR_300MS 3u      /// 3 x 100ms =300ms
#define STATUS_C_ERR_30MS       3u      /// 3 x 10ms  =30ms
#define ASR2_ERR_60MS           3u      /// 3 x 20ms = 60ms
#define NCR_INFO_ERR_30MS       3u      /// 3 x 10ms = 30ms
#define EPCX_STAT_ERR_30MS      3u      /// 3 x 10ms = 30ms
#define TIME_E_DATE_ERR_3000MS  3u      /// 3 x 1000ms = 3000ms

/// NCM masks
#define DCM_BRAKE_F3_ABS_MASK           0x00000001U
#define DCM_BRAKE_F3_OVR_MASK           0x00000002U
#define DCM_MOT1_ABS_MASK               0x00000004U
#define DCM_MOT1_OVR_MASK               0x00000008U
#define DCM_STATUS_B_CAN_ABS_MASK       0x00000010U
#define DCM_STATUS_B_CAN_OVR_MASK       0x00000020U
#define DCM_STATUS_B_CAN2_ABS_MASK      0x00000040U
#define DCM_STATUS_B_CAN2_OVR_MASK      0x00000080U
#define DCM_STATUS_C_NCM_ABS_MASK       0x00000100U
#define DCM_STATUS_C_NCM_OVR_MASK       0x00000200U
/// NCR masks
#define NCR_ASR2_ABS_MASK               0x00000400U
#define NCR_ASR2_OVR_MASK               0x00000800U
#define NCR_NCR_INFO_ABS_MASK           0x00001000U
#define NCR_NCR_INFO_OVR_MASK           0x00002000U
/// EPCS Node masks
#define EPCS_EPCS_STAT_ABS_MASK         0x00004000U
#define EPCS_EPCS_STAT_OVR_MASK         0x00008000U
/// EPCM Node masks
#define EPCM_EPCM_STAT_ABS_MASK         0x00010000U
#define EPCM_EPCM_STAT_OVR_MASK         0x00020000U
/// NQS Node masks
#define NQS_TIME_E_DATE_ABS_MASK        0x00040000U
#define NQS_TIME_E_DATE_OVR_MASK        0x00080000U

#define MASK_NODE_ABSENT  (DCM_BRAKE_F3_ABS_MASK | DCM_MOT1_ABS_MASK | DCM_STATUS_B_CAN_ABS_MASK | \
                           DCM_STATUS_B_CAN2_ABS_MASK | DCM_STATUS_C_NCM_ABS_MASK | NCR_ASR2_ABS_MASK | \
                           NCR_NCR_INFO_ABS_MASK | EPCS_EPCS_STAT_ABS_MASK | EPCM_EPCM_STAT_ABS_MASK | \
                           NQS_TIME_E_DATE_ABS_MASK)

#define MASK_NODE_OVERRUN (DCM_BRAKE_F3_OVR_MASK | DCM_MOT1_OVR_MASK | DCM_STATUS_B_CAN_OVR_MASK | \
                           DCM_STATUS_B_CAN2_OVR_MASK | DCM_STATUS_C_NCM_OVR_MASK | NCR_ASR2_OVR_MASK | \
                           NCR_NCR_INFO_OVR_MASK | EPCS_EPCS_STAT_OVR_MASK | EPCM_EPCM_STAT_OVR_MASK | \
                           NQS_TIME_E_DATE_OVR_MASK)

#define MASK_NODE_DCM  (DCM_BRAKE_F3_ABS_MASK | DCM_BRAKE_F3_OVR_MASK | DCM_MOT1_ABS_MASK | DCM_MOT1_OVR_MASK |\
                        DCM_STATUS_B_CAN_ABS_MASK | DCM_STATUS_B_CAN_OVR_MASK | DCM_STATUS_B_CAN2_ABS_MASK |\
                        DCM_STATUS_B_CAN2_OVR_MASK | DCM_STATUS_C_NCM_ABS_MASK | DCM_STATUS_C_NCM_OVR_MASK)


#define MASK_NODE_NCR  (NCR_ASR2_ABS_MASK | NCR_ASR2_OVR_MASK | NCR_NCR_INFO_ABS_MASK | NCR_NCR_INFO_OVR_MASK)

#define MASK_NODE_NQS  (NQS_TIME_E_DATE_ABS_MASK | NQS_TIME_E_DATE_OVR_MASK)


#define MASK_NODE_EPCS  (EPCS_EPCS_STAT_ABS_MASK | EPCS_EPCS_STAT_OVR_MASK)

#define MASK_NODE_EPCM  (EPCM_EPCM_STAT_ABS_MASK | EPCM_EPCM_STAT_OVR_MASK)

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
#pragma ghs startnomisra

typedef struct NCM_INFO_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  RpmCAN0   :4;
            uint8_T  NCM_InfoMsgCnt0   :4;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  RpmCAN1   :8;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Load2   :8;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TAirCAN3   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TWatCAN4   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EngStSCAN5   :2;
            uint8_T  DesCutOff5   :1;
            uint8_T  EraseFaults5   :1;
            uint8_T  NotEraseFaults5   :1;
            uint8_T  Ignition_CutOff5   :1;
            uint8_T  EngineAtLimiter5   :1;
            uint8_T  FlgNoTrqCtrSACAN5   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :3;
            uint8_T  NCM_LivenessFault6   :1;
            uint8_T  VDRpmCAN6   :1;
            uint8_T  VDTWaterCAN6   :1;
            uint8_T  VDLoadCAN6   :1;
            uint8_T  VDTAirCAN6   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  NCM_InfoCRC7   :8;
        } B;
    } Byte7;

} NCM_INFO_T;

typedef struct STATUS_B_CAN2_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CapoteSts0   :3;
            uint8_T  FuelButtonSts0   :1;
            uint8_T  LowBeamSts0   :1;
            uint8_T  FuelFlapSts0   :1;
            uint8_T  EngineOilTemperatureValidData0   :1;
            uint8_T  EngineOilPressureValidData0   :1;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EngineOilTemperature1   :8;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EngineOilPressure2   :6;
            uint8_T Dummy2_1   :2;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy3_0   :2;
            uint8_T  ElectricSteeringLamp_FailSts3   :2;
            uint8_T  TotalOdometer3   :4;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TotalOdometer4   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TotalOdometer5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  AVHDriverDoorSts6   :1;
            uint8_T  LongParkingSetting6   :2;
            uint8_T  NightDaySts6   :1;
            uint8_T  InternalLightLevel6   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  InternalLightSts7   :1;
            uint8_T  RechargeFlapSts7   :1;
            uint8_T  FanFailSts7   :3;
            uint8_T  EngineBonnetSts_LH7   :1;
            uint8_T  TrunkFailSts7   :1;
            uint8_T  Alcohol_Interlock_Inhibit7   :1;
        } B;
    } Byte7;

} STATUS_B_CAN2_T;

typedef struct ASR2_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VRD_RHValidData0   :1;
            uint8_T  RH_WheelDirectionSts0   :2;
            uint8_T  VRD_RH0   :5;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VRD_RH1   :8;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VRD_LHValidData2   :1;
            uint8_T  LH_WheelDirectionSts2   :2;
            uint8_T  VRD_LH2   :5;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VRD_LH3   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VRD_RVValidData4   :1;
            uint8_T  RV_WheelDirectionSts4   :2;
            uint8_T  VRD_RV4   :5;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VRD_RV5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VRD_LVValidData6   :1;
            uint8_T  LV_WheelDirectionSts6   :2;
            uint8_T  VRD_LV6   :5;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VRD_LV7   :8;
        } B;
    } Byte7;

} ASR2_T;

typedef struct BRAKE_FE3_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EPBSts0   :3;
            uint8_T  FunctionSts0   :1;
            uint8_T  Function2Sts0   :1;
            uint8_T  SlopeValidData0   :1;
            uint8_T  EPBMalfunction0   :1;
            uint8_T  Slope0   :1;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Slope1   :6;
            uint8_T  EPB_ButtonSts1   :2;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy2_0   :1;
            uint8_T  VehicleSpeedVSOSig2   :7;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VehicleSpeedVSOSig3   :6;
            uint8_T  VehicleSpeedVSOSigFailSts3   :1;
            uint8_T Dummy3_2   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ESCOFF_Mode4   :1;
            uint8_T  NFR_Speed_FA4   :7;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  NFR_Speed_FA5   :6;
            uint8_T  NFR_Speed_FA_FailSts5   :1;
            uint8_T  ABSFailPresent5   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  NFRCruiseEnabler6   :1;
            uint8_T  NFRAutoStopEnableSts6   :1;
            uint8_T  NFRAutoStopStaySts6   :1;
            uint8_T  NFRAutoStopFailSts6   :1;
            uint8_T  MsgCounter6   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Checksum7   :8;
        } B;
    } Byte7;

} BRAKE_FE3_T;

typedef struct STATUS_B_CAN_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ABSLamp_FailSts0   :2;
            uint8_T  EBDLamp_FailSts0   :2;
            uint8_T  TC_ASRLamp_FailSts0   :2;
            uint8_T  VDCLamp_FailSts0   :2;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  PowerModeSts1   :2;
            uint8_T  SimpleCapoteSts1   :1;
            uint8_T  AutoParkButtonSts1   :1;
            uint8_T  LifterButtonSts1   :1;
            uint8_T  ReverseGearBCSts1   :1;
            uint8_T  TrunkSts1   :1;
            uint8_T  LifterButtonValidData1   :1;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KeySts2   :4;
            uint8_T  DriverDoorSts2   :1;
            uint8_T  BackWindowSts2   :2;
            uint8_T  TrunkReleaseReqCntrl2   :1;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ExternalTemperature3   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ExternalTemperatureFailSts4   :1;
            uint8_T  RemoteDoorUnlock4   :1;
            uint8_T  EngineBonnetSts4   :1;
            uint8_T  LowNourriceLevel4   :1;
            uint8_T  CompressorACReqSts4   :1;
            uint8_T  IncreaseIdleSpeedReq4   :1;
            uint8_T  RawNourriceLevel4   :1;
            uint8_T  FuelLevelFailSts4   :1;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FuelLevelRawValue5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  NBC_OBD_Err_Cancel_Req6   :1;
            uint8_T  NTPCalibrationReqSts6   :1;
            uint8_T  SnowChainMode6   :1;
            uint8_T  TheftAlarmStatus6   :3;
            uint8_T  FOBSearchRequest6   :2;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BCANAutoStopEnableSts7   :1;
            uint8_T  BCANAutoStopStaySts7   :1;
            uint8_T  BCANAutoStopFailSts7   :4;
            uint8_T  PsngDoorSts7   :1;
            uint8_T  NBCStopStartButtonSts7   :1;
        } B;
    } Byte7;

} STATUS_B_CAN_T;

typedef struct NCR_INFO_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  GearEngagedForADAS0   :4;
            uint8_T Dummy0_1   :4;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy1_0   :1;
            uint8_T  WarmingUpCycle1   :1;
            uint8_T  DrivingCycle1   :1;
            uint8_T  GearBoxAutomaticMode1   :1;
            uint8_T  ActiveGearRatioValidData1   :1;
            uint8_T  NCRACCFailSts1   :1;
            uint8_T  ActiveGearRatio1   :2;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ActiveGearRatio2   :8;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FutureSyncGear3   :4;
            uint8_T  GearEngaged3   :4;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ShiftInProgress4   :1;
            uint8_T  AMTIndex4   :7;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  RaceStart5   :1;
            uint8_T  EngineSlaveMode5   :1;
            uint8_T  DCTState5   :6;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TorquePhaseTime6   :4;
            uint8_T  MessageCounterP_NCR_INFO6   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ChecksumP_NCR_INFO7   :8;
        } B;
    } Byte7;

} NCR_INFO_T;

typedef struct MOT1_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  GasPedalPositionValidData0   :1;
            uint8_T  VehDriverReqTrqValidData0   :1;
            uint8_T  GPFInfo_NCM_ValidData0   :1;
            uint8_T  GPFMasterRun0   :1;
            uint8_T  EngineSpeedValidData0   :1;
            uint8_T  GPFMasterWrite0   :1;
            uint8_T Dummy0_6   :1;
            uint8_T  VehDriverReqTrq0   :1;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VehDriverReqTrq1   :8;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EngineSpeed2   :8;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EngineSpeed3   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  GasPedalPosition4   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R;
        struct
        {
            uint8_T  GPFInfo_NCM_Lsb5   :5;
            uint8_T  GPFInfo_NCM_Cnt5   :2;
            uint8_T  GPFInfo_NCM_MuxId5   :1;
        } Mux0;
        struct
        {
            uint8_T  GPFInfo_NCM_Msb5   :5;
            uint8_T  GPFInfo_NCM_Cnt5   :2;
            uint8_T  GPFInfo_NCM_MuxId5   :1;
        } Mux1;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :4;
            uint8_T  MessageCounterP_MOT16   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ChecksumP_MOT17   :8;
        } B;
    } Byte7;

} MOT1_T;

typedef struct STATUS_C_NCM2_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Checksum0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  VehicleNotInUse1   :2;
            uint8_T  OverBoost1   :5;
            uint8_T  EngineOilTemperature1   :1;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EngineOilTemperature2   :7;
            uint8_T  EngineOilTemperatureValidData2   :1;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CruiseControlSts3   :1;
            uint8_T  BatteryVoltageLevel3   :7;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BatteryVoltageLevelFailSts4   :1;
            uint8_T  MessageCounter4   :4;
            uint8_T  RechargeSts4   :1;
            uint8_T  CruiseControlLampSts4   :2;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StopStartSts5   :2;
            uint8_T  EngineSts5   :2;
            uint8_T  StopStartVisualizationCode5   :4;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EngineBonnetSts26   :1;
            uint8_T  EDriveSts6   :2;
            uint8_T  NCMMildHybSysWarningForDisplay6   :4;
            uint8_T  ColdStartTime6   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TargetSpeed7   :8;
        } B;
    } Byte7;

} STATUS_C_NCM2_T;

typedef struct MCU_EPC_CNTRL_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Mot_SpeedReqEPC0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Mot_SpeedReqEPC1   :8;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Mot_SwitchEPC2   :2;
            uint8_T  Mot_MsgCnt2   :4;
            uint8_T Dummy2_2   :2;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy3_0   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :8;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Mot_Crc7   :8;
        } B;
    } Byte7;

} MCU_EPC_CNTRL_T;

typedef struct TIME_E_DATE_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Hour10   :4;
            uint8_T  Hour20   :4;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Minute11   :4;
            uint8_T  Minute21   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Day12   :4;
            uint8_T  Day22   :4;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Month13   :4;
            uint8_T  Month23   :4;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Year14   :4;
            uint8_T  Year24   :4;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Year35   :4;
            uint8_T  Year45   :4;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :8;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy7_0   :8;
        } B;
    } Byte7;

} TIME_E_DATE_T;

typedef struct MOTION_SX_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Load_SX0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TAirCAN1   :8;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TWatCAN2   :8;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  RpmCAN3   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Ignition_CutOff_Dx4   :1;
            uint8_T  EngineAtLimiter4   :1;
            uint8_T  EngStSCAN4   :2;
            uint8_T  RpmCAN4   :4;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Load_DX5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Ignition_CutOff_Sx6     :1;
            uint8_T Dummy6_0   :7;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  NotEraseFaults7   :1;
            uint8_T  EraseFaults7   :1;
            uint8_T  DesCutOff7   :1;
            uint8_T  VDRpmCAN7   :1;
            uint8_T  VDTWaterCAN7   :1;
            uint8_T  FlgNoTrqCtrSACAN7   :1;
            uint8_T  VDLoadCAN7   :1;
            uint8_T  VDTAirCAN7   :1;
        } B;
    } Byte7;
} MOTION_SX_T;

#pragma ghs endnomisra

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST uint16_T TNOCANDIAGAFTKEYON;
extern CALQUAL CALQUAL_POST uint16_T CANVBATTHRMIN;
extern CALQUAL CALQUAL_POST uint8_T  THRCNTRPMCANBLOCKED;
extern CALQUAL CALQUAL_POST uint8_T TIMDIAGRPMDIS;
extern CALQUAL CALQUAL_POST uint8_T FUELLEVELPWRONTOUT;
extern CALQUAL CALQUAL_POST uint8_T FORCETOIL;
extern CALQUAL CALQUAL_POST int16_T FORCEDTOIL;
extern CALQUAL CALQUAL_POST uint8_T TOILDEFAULTVAL;
extern CALQUAL CALQUAL_POST uint8_T POILDEFAULTVAL;
extern CALQUAL CALQUAL_POST uint16_T KFILTPOIL;
extern CALQUAL CALQUAL_POST uint8_T POILCANSEL;
extern CALQUAL CALQUAL_POST int16_T BKTOILPOIL[BKTOILPOIL_dim];
extern CALQUAL CALQUAL_POST uint16_T BKRPMPOIL[BKRPMPOIL_dim];
extern CALQUAL CALQUAL_POST uint16_T TBPOIL[BKTOILPOIL_dim*BKRPMPOIL_dim];
extern CALQUAL CALQUAL_POST int16_T TAIRREC;
extern CALQUAL CALQUAL_POST int16_T TWATERREC;
extern CALQUAL CALQUAL_POST uint16_T LOADREC;
extern CALQUAL CALQUAL_POST uint8_T ENGATEWAY;
extern CALQUAL CALQUAL_POST int16_T THDELTALOADCAN;
extern CALQUAL CALQUAL_POST uint8_T THDEBIGNITIONCUTOFF;
extern CALQUAL CALQUAL_POST uint8_T THDEBPTFAULTKEYSIG;
extern CALQUAL CALQUAL_POST uint8_T LOADCANVALASREC;
extern CALQUAL CALQUAL_POST uint8_T TAIRCANVALASREC;
extern CALQUAL CALQUAL_POST uint8_T TWATERCANVALASREC;
extern CALQUAL CALQUAL_POST uint32_T DIAGVEHCANMASK;
extern CALQUAL CALQUAL_POST uint8_T ENCRCALIVETEST;
extern CALQUAL CALQUAL_POST uint8_T NEWLOADSCALING;
extern CALQUAL CALQUAL_POST uint8_T CANTYPE;
extern CALQUAL CALQUAL_POST uint8_T ENOLDGPFINFO;

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static void CanMgm_ResetAllPri(void);
static void CanMgm_ResetAllVeh(void);
static int16_T CanMgm_CanRecv_MotIonDx(void);
static void CanMgm_BRAKE_FE3_Init(void);
static void CanMgm_ASR2_Init(void);
static void CanMgm_NCR_INFO_Init(void);
static void CanMgm_MOT1_Init(void);
static void CanMgm_STATUS_B_CAN_Init(void);
static void CanMgm_STATUS_B_CAN2_Init(void);
static void CanMgm_STATUS_C_Init(void);
static void CanMgm_TIME_E_DATE_Init(void);
static int16_T CanMgm_CanRecv_NcmInfo(void);
static int16_T CanMgm_CanRecv_BRAKE_FE3(void);
static int16_T CanMgm_CanRecv_ASR2(void);
static int16_T CanMgm_CanRecv_NCR_INFO(void);
static int16_T CanMgm_CanRecv_MOT1(void);
static int16_T CanMgm_CanRecv_STATUS_B_CAN(void);
static int16_T CanMgm_CanRecv_STATUS_B_CAN2(void);
static int16_T CanMgm_CanRecv_STATUS_C(void);
static int16_T CanMgm_CanRecv_TimeEDate(void);
static int16_T CanMgm_CanRecv_EpcxCntrl(int16_T *txRetVal);
static int16_T CanMgm_CanRecv_EpcmStat(int16_T *txRetVal);
static int16_T CanMgm_CanRecv_EpcsStat(int16_T *txRetVal);
static void CanMgm_VehSpeed_Calc(void);
static void CanMgm_CalcPOil_Calc(void);
static void CanMgm_VehSpeed_Calc(void);
static void CanMgm_NcmInfo_Reset(void);
static uint8_T CanMgm_TestVehCAN(uint8_T ptFaultDiagCanVeh);
static void CanMgm_DiagCanVehReset(uint8_T stDiag);
static void CanMgm_GPFInfoUpdate(struct CANBuff_T *ptrDataBuf);
/* To Be removed when only final configuration shall be active */
static int16_T CanMgm_CanRecv_MotIonSx(void);
static void CanMgm_UpdateDisDiagRpm(void);

#endif  // CANMGMIN_F17X_H

/****************************************************************************
 ****************************************************************************/


