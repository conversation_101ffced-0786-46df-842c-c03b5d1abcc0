/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tim.h
 * @brief   SPC5xx GTM TIM header file.
 *
 * @addtogroup TIM
 * @{
 */

#ifndef _GTM_TIM_H_
#define _GTM_TIM_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"

/*lint -e621*/

/**
 * @name    TIM definitions
 * @{
 */


/** Number of channels */
#define SPC5_GTM_TIM_CHANNELS                           8U

/** TIM channel 0 identifier */
#define TIM_CHANNEL0                                    0U
/** TIM channel 1 identifier */
#define TIM_CHANNEL1                                    1U
/** TIM channel 2 identifier */
#define TIM_CHANNEL2                                    2U
/** TIM channel 3 identifier */
#define TIM_CHANNEL3                                    3U
/** TIM channel 4 identifier */
#define TIM_CHANNEL4                                    4U
/** TIM channel 5 identifier */
#define TIM_CHANNEL5                                    5U
/** TIM channel 6 identifier */
#define TIM_CHANNEL6                                    6U
/** TIM channel 7 identifier */
#define TIM_CHANNEL7                                    7U

/**TIM UNIT TIM0 */
#define TIM_0                                           0U
/**TIM UNIT TIM1 */
#define TIM_1                                           1U
/**TIM UNIT TIM2 */
#define TIM_2                                           2U
/**TIM UNIT TIM3 */
#define TIM_3                                           3U
/**TIM UNIT TIM4 */
#define TIM_4                                           4U
/**TIM UNIT TIM5 */
#define TIM_5                                           5U

/** TIM IRQ Notify New Value interrupt */
#define SPC5_GTM_TIM_IRQ_STATUS_NEW_VALUE               0x01UL
/** TIM IRQ Notify Edge Counter interrupt */
#define SPC5_GTM_TIM_IRQ_STATUS_EDGE_COUNTER            0x02UL
/** TIM IRQ Notify SMU Counter interrupt */
#define SPC5_GTM_TIM_IRQ_STATUS_SMU_COUNTER             0x04UL
/** TIM IRQ Notify GPRS Data Overflow interrupt */
#define SPC5_GTM_TIM_IRQ_STATUS_GPRS_DATA_OVERFLOW      0x08UL
/** TIM IRQ Notify Timeout interrupt */
#define SPC5_GTM_TIM_IRQ_STATUS_TIMEOUT                 0x10UL
/** TIM IRQ Notify Glitch interrupt */
#define SPC5_GTM_TIM_IRQ_STATUS_GLITCH                  0x20UL

/** TIM IRQ Enable New Value interrupt */
#define SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE               0x01UL
/** TIM IRQ Enable Edge Counter interrupt */
#define SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER            0x02UL
/** TIM IRQ Enable SMU Counter interrupt */
#define SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER             0x04UL
/** TIM IRQ Enable GPRS Data Overflow interrupt */
#define SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW      0x08UL
/** TIM IRQ Enable Timeout interrupt */
#define SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT                 0x10UL
/** TIM IRQ Enable Glitch interrupt */
#define SPC5_GTM_TIM_IRQ_ENABLE_GLITCH                  0x20UL

/** TIM IRQ Force New Value interrupt */
#define SPC5_GTM_TIM_IRQ_FORCE_INT_NEW_VALUE            0x01UL
/** TIM IRQ Force Edge Counter interrupt */
#define SPC5_GTM_TIM_IRQ_FORCE_INT_EDGE_COUNTER         0x02UL
/** TIM IRQ Force SMU Counter interrupt */
#define SPC5_GTM_TIM_IRQ_FORCE_INT_SMU_COUNTER          0x04UL
/** TIM IRQ Force GPRS Data Overflow interrupt */
#define SPC5_GTM_TIM_IRQ_FORCE_INT_GPRS_DATA_OVERFLOW   0x08UL
/** TIM IRQ Force Timeout interrupt */
#define SPC5_GTM_TIM_IRQ_FORCE_INT_TIMEOUT              0x10UL
/** TIM IRQ Force Glitch interrupt */
#define SPC5_GTM_TIM_IRQ_FORCE_INT_GLITCH               0x20UL

/** TIM IRQ Mode Level */
#define SPC5_GTM_TIM_IRQ_MODE_LEVEL                     0U
/** TIM IRQ Mode Pulse */
#define SPC5_GTM_TIM_IRQ_MODE_PULSE                     1U
/** TIM IRQ Mode Pulse-Notify */
#define SPC5_GTM_TIM_IRQ_MODE_PULSE_NOTIFY              2U
/** TIM IRQ Mode Single-Pulse Mode */
#define SPC5_GTM_TIM_IRQ_MODE_SINGLE_PULSE              3U

/** TIM IRQ notify interrupt to ICM as Normal */
#define SPC5_GTM_TIM_INT_MODE_NORMAL                    1U
/** TIM IRQ notify interrupt to ICM as Error */
#define SPC5_GTM_TIM_INT_MODE_ERROR                     2U
/** TIM IRQ notify interrupt to ICM as Normal and Error */
#define SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR          3U


#define SW_RST_BASE                                     0x0UL
#define IN_SRC_CH_BASE                                  0x4UL
#define IN_SRC_CH_MODE_OFFSET                           0x2UL

/* TIM Configuration Register CTRL bit field */
#define CH_ENABLE                                       0UL
#define CH_MODES                                        1UL
#define CH_OSM                                          4UL
#define CH_ARU                                          5UL
#define CH_CICTRL                                       6UL
#define CH_TBU_SEL                                      7UL
#define CH_GPR0                                         8UL
#define CH_GPR1                                         10UL
#define CH_CNTS                                         12UL
#define CH_DSL                                          13UL
#define CH_ISL                                          14UL
#define CH_ECNT_RES                                     15UL
#define CH_FLT                                          16UL
#define CH_FLT_CNT_FREQ                                 17UL
#define CH_EXT_CAP                                      19UL
#define CH_FLT_M_RE                                     20UL
#define CH_FLT_CTR_RE                                   21UL
#define CH_FLT_CTR_FE                                   23UL
#define CH_FLT_M_FE                                     22UL
#define CH_CLK_SEL                                      24UL
#define CH_FR_ECNT_OVL                                  27UL
#define CH_EGPR0                                        28UL
#define CH_EGPR1                                        29UL
#define CH_TOCTRL                                       30UL

/** TIM Input selection for GPR0 and GPR1 register*/
#define SPC5_GTM_TIM_GPR_SEL_TBU_TS0                    0UL
#define SPC5_GTM_TIM_GPR_SEL_TBU_TS1                    1UL
#define SPC5_GTM_TIM_GPR_SEL_TBU_TS2                    2UL
#define SPC5_GTM_TIM_GPR_SEL_CNTS                       3UL
#define SPC5_GTM_TIM_GPR_SEL_CNT                        3UL

/** TIM CHANNEL CLOCK SOURCE CMU_CLK0 */
#define SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0                0UL
/** TIM CHANNEL CLOCK SOURCE CMU_CLK1 */
#define SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK1                1UL
/** TIM CHANNEL CLOCK SOURCE CMU_CLK2 */
#define SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK2                2UL
/** TIM CHANNEL CLOCK SOURCE CMU_CLK3 */
#define SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK3                3UL
/** TIM CHANNEL CLOCK SOURCE CMU_CLK4 */
#define SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK4                4UL
/** TIM CHANNEL CLOCK SOURCE CMU_CLK5 */
#define SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK5                5UL
/** TIM CHANNEL CLOCK SOURCE CMU_CLK6 */
#define SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK6                6UL
/** TIM CHANNEL CLOCK SOURCE CMU_CLK7 */
#define SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7                7UL


/** TIM CHANNEL MODE PWM */
#define SPC5_GTM_TIM_CH_MODE_TPWM                       0UL
/** TIM CHANNEL Pulse Integration Mode */
#define SPC5_GTM_TIM_CH_MODE_TPIM                       1UL
/** TIM CHANNEL Input Event Mode */
#define SPC5_GTM_TIM_CH_MODE_TIEM                       2UL
/** TIM CHANNEL Input Prescaler Mode */
#define SPC5_GTM_TIM_CH_MODE_TIPM                       3UL
/** TIM CHANNEL Bit Compression Mode */
#define SPC5_GTM_TIM_CH_MODE_TBCM                       4UL
/** TIM CHANNEL Gated Period Sampling Mode */
#define SPC5_GTM_TIM_CH_MODE_TGPS                       5UL

/** TIM CHANNEL FILTER CONTROL DISABLED */
#define SPC5_GTM_TIM_CH_FLTCTRL_DISABLED                0UL
/** TIM CHANNEL FILTER COUNT FREQUENCY FOR CMU_CLK0 */
#define SPC5_GTM_TIM_CH_FILTER_CNT_CMU_CLK0             0UL
/** TIM CHANNEL FILTER COUNT FREQUENCY FOR CMU_CLK1 */
#define SPC5_GTM_TIM_CH_FILTER_CNT_CMU_CLK1             1UL
/** TIM CHANNEL FILTER COUNT FREQUENCY FOR CMU_CLK6 */
#define SPC5_GTM_TIM_CH_FILTER_CNT_CMU_CLK6             2UL
/** TIM CHANNEL FILTER COUNT FREQUENCY FOR CMU_CLK7 */
#define SPC5_GTM_TIM_CH_FILTER_CNT_CMU_CLK7             3UL

/** TIM CHANNEL TIMEOUT CONTROL DISABLED */
#define SPC5_GTM_TIM_CH_TOCTRL_DISABLED                 0UL
/** TIM CHANNEL TIMEOUT CONTROL ENABLED FOR RISISNG EDGE ONLY */
#define SPC5_GTM_TIM_CH_TOCTRL_RISING_EDGE_ONLY         1UL
/** TIM CHANNEL TIMEOUT CONTROL ENABLED FOR FALLING EDGE ONLY */
#define SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY        2UL
/** TIM CHANNEL TIMEOUT CONTROL ENABLED FOR BOTH EDGE */
#define SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH             3UL

/** TIM CHANNEL SELECTED SOURCE FOR TRIGGERING EXT_CAPTURE FUNCTIONALITY */
#define SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ             0UL
/** TIM CHANNEL SELECTED SOURCE FOR TRIGGERING EXT_CAPTURE FUNCTIONALITY */
#define SPC5_GTM_TIM_CH_SRC_TRG_AUX_IN                  1UL
/** TIM CHANNEL SELECTED SOURCE FOR TRIGGERING EXT_CAPTURE FUNCTIONALITY */
#define SPC5_GTM_TIM_CH_SRC_TRG_CNTOFL_IRQ              2UL
/** TIM CHANNEL SELECTED SOURCE FOR TRIGGERING EXT_CAPTURE FUNCTIONALITY */
#define SPC5_GTM_TIM_CH_SRC_TRG_TIM_IN                  3UL
/** TIM CHANNEL SELECTED SOURCE FOR TRIGGERING EXT_CAPTURE FUNCTIONALITY */
#define SPC5_GTM_TIM_CH_SRC_TRG_ECNTOFL_IRQ             4UL
/** TIM CHANNEL SELECTED SOURCE FOR TRIGGERING EXT_CAPTURE FUNCTIONALITY */
#define SPC5_GTM_TIM_CH_SRC_TRG_TODET_IRQ               5UL
/** TIM CHANNEL SELECTED SOURCE FOR TRIGGERING EXT_CAPTURE FUNCTIONALITY */
#define SPC5_GTM_TIM_CH_SRC_TRG_GLITCHDET_IRQ           6UL
/** TIM CHANNEL SELECTED SOURCE FOR TRIGGERING EXT_CAPTURE FUNCTIONALITY */
#define SPC5_GTM_TIM_CH_SRC_TRG_GPROFL_IRQ              7UL

/** TIM INPUT SOURCE SELECTION (CHANGE STATE TO 0) */
#define SPC5_GTM_TIM_IN_SRC_CHANGE_TO_0                 0x1U
/** TIM INPUT SOURCE SELECTION (CHANGE STATE TO 1) */
#define SPC5_GTM_TIM_IN_SRC_CHANGE_TO_1                 0x2U

/** @} */
/*===========================================================================*/
/* Driver data structures and types.                                         */
/*===========================================================================*/

/**
 * @brief Type of a structure representing a (GTM-IP) TIM driver.
 */
typedef struct GTM_TIMDriver GTM_TIMDriver;

/**
 * @brief Type of a structure representing a (GTM-IP) TIM TIM_Channel_Cfg.
 */
typedef struct TIM_Channel_Cfg TIM_Channel_Cfg;

/**
 * @brief   (GTM-IP) TIM notification callback type.
 *
 * @param[in] timd      pointer to the @p MSCDriver object triggering the callback
 * @param[in] channel   channel triggering the callback
 */
typedef void (*gtm_tim_callback_t)(GTM_TIMDriver *timd, uint8_t channel);

/**
 * @brief Type of a structure representing a (GTM-IP) TIM channel callbacks.
 */
typedef struct GTM_TIM_Channel_Callbacks GTM_TIM_Channel_Callbacks;

/**
 * @brief   Structure representing a TIM Channel callbacks
 */
struct GTM_TIM_Channel_Callbacks {
  /**
   * @brief New value channel callback function.
   */
	gtm_tim_callback_t new_value;

  /**
   * @brief GPRs data overflow channel callback function.
   */
	gtm_tim_callback_t gprs_data_overflow;

  /**
   * @brief Timeout channel callback function.
   */
	gtm_tim_callback_t timeout;

  /**
   * @brief SMU counter channel callback function.
   */
	gtm_tim_callback_t smu_counter;

  /**
   * @brief Edge counter channel callback function.
   */
	gtm_tim_callback_t edge_counter;

  /**
   * @brief Glitch channel callback function.
   */
	gtm_tim_callback_t glitch;
};

/**
 * @brief Type of a structure representing a (GTM-IP) TIM channel register block.
 */
struct TIM_Channel_Cfg {
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] control register.
	 */
	uint32_t ctrl_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] extended control register.
	 */
	uint32_t ectrl_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] filter parameter register.
	 */
	uint32_t flt_re_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] filter parameter register.
	 */
	uint32_t flt_fe_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] general purpose register.
	 */
	uint32_t gpr0_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] general purpose register.
	 */
	uint32_t gpr1_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] SMU counter register.
	 */
	uint32_t cnt_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] SMU edge register.
	 */
	uint32_t ecnt_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel[x] SMU shadow register.
	 */
	uint32_t cnts_reg;
};

/**
 * @brief   Structure representing a (GTM) TIM driver.
 */
struct GTM_TIMDriver {

  /**
   * @brief Pointer to the (GTM) TIM registers block.
   */
	volatile GTM_TIM_TAG *tim;
	/**
	 * @brief Pointer to the (GTM) TIM software reset register.
	 */
	uint32_t sw_reset_reg;
	/**
	 * @brief Pointer to the (GTM) TIM AUX IN source selection register.
	 */
	uint32_t aux_in_src_reg;
	/**
	 * @brief Pointer to the (GTM) TIM Channel register block.
	 */
	TIM_Channel_Cfg channel[SPC5_GTM_TIM_CHANNELS];

	/**
	 * @brief Interrupts callbacks.
	 */
	GTM_TIM_Channel_Callbacks **callbacks;

	/**
	 * @brief Pointer for application private data.
	 */
	void *priv;
};


/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_TIM0 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TIMDriver TIMD1;
#endif

#if (SPC5_GTM_USE_TIM1 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TIMDriver TIMD2;
#endif

#if (SPC5_GTM_USE_TIM2 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TIMDriver TIMD3;
#endif

#if (SPC5_GTM_USE_TIM3 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TIMDriver TIMD4;
#endif

#if (SPC5_GTM_USE_TIM4 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TIMDriver TIMD5;
#endif

#if (SPC5_GTM_USE_TIM5 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TIMDriver TIMD6;
#endif

#ifdef __cplusplus
extern "C" {
#endif

void gtm_timInit(void);
extern void gtm_timStart(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timStop(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timSetCfg(GTM_TIMDriver *timd, uint8_t channel, TIM_Channel_Cfg channel_cfg);
extern TIM_Channel_Cfg gtm_timGetCfg(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timSetSWreset(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timSetAUX_IN(GTM_TIMDriver *timd, uint8_t channel, uint32_t mode, uint32_t val);
extern void gtm_timSetTimeoutMode(GTM_TIMDriver *timd, uint8_t channel, uint8_t mode);
extern uint8_t gtm_timGetTimeoutMode(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timSetTimeoutValue(GTM_TIMDriver *timd, uint8_t channel, uint8_t tov);
extern uint8_t gtm_timGetTimeoutValue(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timSetTimeoutClock(GTM_TIMDriver *timd, uint8_t channel, uint8_t tcs);
extern uint8_t gtm_timGetTimeoutClock(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timSetIRQMode(GTM_TIMDriver *timd, uint8_t channel, uint8_t mode);
extern void gtm_timEnableInt(GTM_TIMDriver *timd, uint8_t channel, uint32_t int_num);
extern void gtm_timNotifyInt(GTM_TIMDriver *timd, uint8_t channel, uint8_t int_num);
extern void gtm_timAckInt(GTM_TIMDriver *timd, uint8_t channel, uint32_t int_num);
extern void gtm_timDisableInt(GTM_TIMDriver *timd, uint8_t channel, uint32_t int_num);
extern uint32_t gtm_timGetIntStatus(GTM_TIMDriver *timd, uint8_t channel);
extern uint32_t gtm_timGetIntEnabled(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timEnableFilter(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timDisableFilter(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timSetFilterClockFreq(GTM_TIMDriver *timd, uint8_t channel, uint8_t flt_cnt_frq);
extern uint8_t gtm_timGetFilterClockFreq(GTM_TIMDriver *timd, uint8_t channel);
extern void gtm_timSetFilter(GTM_TIMDriver *timd, uint8_t channel, uint8_t edge, uint8_t mode, uint32_t time);
extern uint32_t gtm_timGetFilter(GTM_TIMDriver *timd, uint8_t channel);
#ifdef __cplusplus
}
#endif
/*lint +e621*/
#endif /* _GTM_TIM_H_ */
/** @} */
