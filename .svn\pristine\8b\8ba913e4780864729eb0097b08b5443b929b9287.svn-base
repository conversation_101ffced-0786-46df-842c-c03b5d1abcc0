/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Timing
**  Filename        :  timing.c
**  Created on      :  18-giu-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef _BUILD_TIMING_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Timing.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
uint8_T Minutes;
uint16_T Hours;
uint32_T seconds = 0u;
uint32_T SecondsRunTime = 0u;
uint32_T SecondsFirstRunTime = 0u;
uint32_T ECUTimeStamps = 0u;
uint16_T ECUTimeStampsFromKeyOn = 0u;


/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
#define UNMILLESIMO (2199023256u) /* scaling 2^41 */

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : TIMING_Config
**
**   Description:
**    
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T TIMING_Config (void) 
{
    return NO_ERROR;
}

/******************************************************************************
**   Function    : TIMING_Init
**
**   Description:
**    Initialize the absolute timer.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Timing functionality correctly initialized.  
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: 
**
**   EA GUID:
******************************************************************************/
int16_T TIMING_Init(void) 
{
    int16_T retCode = NO_ERROR;

    PIT_1.MCR.R = 0x00000002u;   // Clock for PIT timers is disabled.
    PIT_Enable(PIT1, PIT1_CH1);  //Enable Pit1 Ch1 
    PIT_SetChainMode();          //Set PIT1 chain mode
    PIT_Enable(PIT1, PIT1_CH0);  //Enable Pit1 Ch0
    PIT_1.MCR.R = 0x00000001u;   // Clock for PIT timers is enabled and timers are stopped in debug mode

    return retCode;
}

/******************************************************************************
**   Function    : TIMING_GetTaskTime
**
**   Description:
**    This function provide the task time. 
**
**   Parameters :
**    [in] uint16_T *task_time :
**    [in] uint64_T task_timer : 
** 
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void TIMING_GetTaskTime(uint16_T *task_time, uint64_T task_timer) 
{
    uint64_T timer_l;

    TIMING_GetAbsTimer(&timer_l);
    TIMING_TicksToMicroSeconds(timer_l - task_timer, &timer_l);
    *task_time = (uint16_T)timer_l;    
}

/******************************************************************************
**   Function    : TIMING_SetTimeout
**
**   Description:
**    
**
**   Parameters :
**    [in] uint32_T us_timeout : 
**    [in] timeoutHandler_t* handler : 
**
**   Returns:
**    NO_ERROR - Timeout correctly set.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T TIMING_SetTimeout(uint32_T us_timeout, timeoutHandler_t *handler)
{
    TIMING_GetAbsTimer(handler);
    *handler = *handler + ((timeoutHandler_t)us_timeout * PER_CLK);
    return NO_ERROR;
}

/******************************************************************************
**   Function    : TIMING_GetTimeoutStatus
**
**   Description:
**    Get the timeout status. 
**
**   Parameters :
**    [in] timeoutHandler_t handler : 
**    [out] uint8_T* status_l : 
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T TIMING_GetTimeoutStatus(timeoutHandler_t handler, uint8_T *status_l)
{
    uint64_T timer;
    TIMING_GetAbsTimer(&timer);
    if(handler > timer)
    {
        *status_l = TIMEOUT_PENDING;
    }
    else
    {
        *status_l = TIMEOUT_EXPIRED;
    }
    return NO_ERROR;
}

/******************************************************************************
**   Function    : TIMING_GetAbsTimer
**
**   Description:
**    Get the absolute timer. 
**
**   Parameters :
**    [in] uint64_T* abstime : Pointer to variable where the absolute time is stored.
**                            
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void TIMING_GetAbsTimer(uint64_T *abstime)
{
    volatile uint32_T timer_u;
    volatile uint32_T timer_l;

    timer_u = (0xFFFFFFFFu - PIT_1.LTMR64H.R);  // risoluzione (1/PER_CLK) * 1000 [ns]
    timer_l = (0xFFFFFFFFu - PIT_1.LTMR64L.R);  // risoluzione (1/PER_CLK) * 1000 [ns]

    *abstime = ((uint64_T)timer_l) | (((uint64_T)timer_u) << 32u);
}

/******************************************************************************
**   Function    : TIMING_TicksToSeconds
**
**   Description:
**    Convert ticks to seconds. 
**
**   Parameters :
**    [in] uint64_T abstime : Absolute time (in ticks). 
**    [in] uint64_T * sTime : Pointer to variable where the converted time is stored.
**
**   Returns:
**    NO_ERROR - Ticks correctly converted.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T TIMING_TicksToSeconds(uint64_T abstime, uint64_T *sTime) 
{
    uint32_T timer_u;
    uint32_T timer_l;
    uint64_T tmp;

    timer_l = (uint32_T)(abstime & 0x00000000FFFFFFFFu);
    timer_u = (uint32_T)((abstime >> 32) & 0x00000000FFFFFFFFu);

    tmp = (UNMILLESIMO * (uint64_T)timer_l);
    *sTime = tmp >> 41;
    tmp = (UNMILLESIMO * (uint64_T)timer_u);
    *sTime += tmp >> 9;

    timer_l = (uint32_T)(0x00000000ffffffffu & *sTime);
    timer_u = (uint32_T)((*sTime >> 32)& 0x00000000FFFFFFFFu);

    tmp = (UNMILLESIMO * (uint64_T)timer_l);
    *sTime = tmp >> 41;
    tmp = (UNMILLESIMO * (uint64_T)timer_u);
    *sTime += tmp >> 9;

    timer_l = (uint32_T)(0x00000000ffffffffu & *sTime);
    timer_u = (uint32_T)((*sTime >> 32)& 0x00000000FFFFFFFFu);

    tmp = (ONE_OVER_FSYS * (uint64_T)timer_l);
    *sTime = tmp >> 38;
    tmp = (ONE_OVER_FSYS * (uint64_T)timer_u);
    *sTime += tmp >> 6;
    
    return NO_ERROR;
}

/******************************************************************************
**   Function    : TIMING_TicksToMilliSeconds
**
**   Description:
**    description of operation: SwcName_PublicFunction1
**
**   Parameters :
**    [in] uint64_T abstime : Absolute time (in ticks). 
**    [in] uint64_T * msTime : Pointer to variable where the converted time is stored.
**
**   Returns:
**    NO_ERROR - Ticks correctly converted.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T TIMING_TicksToMilliSeconds(uint64_T abstime, uint64_T *msTime)
{
    uint32_T timer_u;
    uint32_T timer_l;
    uint64_T tmp;

    timer_l = (uint32_T)(abstime & 0x00000000FFFFFFFFu);
    timer_u = (uint32_T)((abstime >> 32) & 0x00000000FFFFFFFFu);

    tmp = (UNMILLESIMO * (uint64_T)timer_l);
    *msTime = tmp >> 41;
    tmp = (UNMILLESIMO * (uint64_T)timer_u);
    *msTime += tmp >> 9;

    timer_l = (uint32_T)(0x00000000ffffffffu & *msTime);
    timer_u = (uint32_T)((*msTime >> 32)& 0x00000000FFFFFFFFu);

    tmp = (ONE_OVER_FSYS * (uint64_T)timer_l);
    *msTime = tmp >> 38;
    tmp = (ONE_OVER_FSYS * (uint64_T)timer_u);
    *msTime += tmp >> 6;
    
    return NO_ERROR;
}

/******************************************************************************
**   Function    : TIMING_TicksToMicroSeconds
**
**   Description:
**    description of operation: SwcName_PublicFunction1
**
**   Parameters :
**    [in] uint64_T abstime : Absolute time (in ticks). 
**    [in] uint64_T * usTime : Pointer to variable where the converted time is stored.
**
**   Returns:
**    NO_ERROR - Ticks correctly converted.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T TIMING_TicksToMicroSeconds(uint64_T abstime, uint64_T *usTime) 
{
    uint32_T timer_u;
    uint32_T timer_l;
    uint64_T tmp;

    timer_l = (uint32_T)(abstime & 0x00000000FFFFFFFFu);
    timer_u = (uint32_T)((abstime >> 32) & 0x00000000FFFFFFFFu);

    tmp = (ONE_OVER_FSYS * (uint64_T)timer_l);
    *usTime = tmp >> 38;
    tmp = (ONE_OVER_FSYS * (uint64_T)timer_u);
    *usTime += tmp >> 6;
    
    return NO_ERROR;
}

/******************************************************************************
**   Function    : WorkingTime_T100ms
**
**   Description:
**    Engine operating Minutes. 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**    Increments SecondsWorkTime every minute the board is on.
**
**   EA GUID:
******************************************************************************/
void WorkingTime_T100ms(void)
{
    static uint8_T temp100Ms = 0u;
    static uint8_T firstengrun = 0u;

#ifdef _BUILD_SYNCMGM_
    temp100Ms++;
    if (temp100Ms >= 10u)
    {
        SecondsWorkTime++;    // EEPROM
        seconds++;
        temp100Ms = 0u;

        if (EndStartFlg != 0u)
        {
            SecondsRunTime++;
            firstengrun = 1u;
        }
        else
        {
            SecondsRunTime = 0u;
        }

        if (firstengrun != 0u)
        {
            SecondsFirstRunTime++;
        }
        else
        {
            SecondsFirstRunTime = 0u;
        }

        /* Runtime Time Stamps management */
        if (((SecondsWorkTime%60u) == 0u) && (SecondsWorkTime != 0u))
        {
            ECUTimeStamps++;
        }
        else
        {
            /* MISRA 14.10 */
        }

        //if (((SecondsRunTime%15u) == 0u) && (SecondsRunTime != 0u))
        if (((seconds%15u) == 0u) && (seconds != 0u))
        {
            ECUTimeStampsFromKeyOn++;
        }
        else
        {
            /* MISRA 14.10 */
        }
    }
#endif
}

/******************************************************************************
**   Function    : TimeStampsInit
**
**   Description:
**    Time Stamps initialization at each power on.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**    Update runtime variables with eeprom storage value (ECUTimeStamps) or
**    init value at the begining of the driving cycle (ECUTimeStampsFromKeyOn).
**
**   EA GUID:
******************************************************************************/
void TimeStampsInit(void)
{
    ECUTimeStamps = ECUTimeStampsEE; // At each Key ON, the ECU must initialize the content of RDI $1008 with the corresponding value in EEprom RDI $2008
    ECUTimeStampsFromKeyOn = 0u;   // at each Key ON it is initialized at 0
}

/******************************************************************************
**   Function    : TIMING_UpdateTimeStampsT10ms
**
**   Description:
**    Update eeprom variables (ECUTimeStampsEE, ECUTimeStampsFromKeyOnEE) 
**    with runtime values (ECUTimeStamps, ECUTimeStampsFromKeyOn).
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void TIMING_UpdateTimeStampsT10ms(void)
{
#ifdef _BUILD_PWRMGM_
    /* EEPROM Time Stamps management */
    if (StEcu == ECU_ON)
    {
        ECUTimeStampsEE = ECUTimeStamps;
        ECUTimeStampsFromKeyOnEE = ECUTimeStampsFromKeyOn;
    }
    else
    {
        /* MISRA 14.10 */
    }
#endif
}

/******************************************************************************
**   Function    : TIMING_SetDelay
**
**   Description:
**    Set delay in micro seconds
**
**   Parameters :
**    microSeconds to wait
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void TIMING_SetDelay(uint16_T microSeconds)
{
    uint64_T abstime;
    uint64_T usTimeStart;
    uint64_T usTimeEnd;

    TIMING_GetAbsTimer(&abstime);
    TIMING_TicksToMicroSeconds(abstime, &usTimeStart);
    usTimeEnd = usTimeStart;
    while ((usTimeEnd - usTimeStart) < microSeconds) // us
    {
        TIMING_GetAbsTimer(&abstime);
        TIMING_TicksToMicroSeconds(abstime, &usTimeEnd);
    }
}

#else

void TIMING_GetAbsTimer(uint64_T *abstime)
{
}

int16_T TIMING_TicksToMicroSeconds(uint64_T abstime, uint64_T *usTime) 
{
    return 0;
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/

#endif //_BUILD_TIMING_

/****************************************************************************
 ****************************************************************************/


