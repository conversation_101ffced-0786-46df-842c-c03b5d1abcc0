/*******************************************************************************************************************/
/* $HeadURL::                                                                                                   $  */
/* $Revision::                                                                                                  $  */
/* $Date::                                                                                                      $  */
/* $Author::                                                                                                    $  */
/*******************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  COMMON
**  Filename        :  GTM_HostInterface.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  SantoroR
******************************************************************************/

#ifndef _GTM_HOST_INTERFACE_H_
#define _GTM_HOST_INTERFACE_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
#ifdef _BUILD_GTM_

/******************************************************************************
**   Function    : GTM_Tim0_Channel0_IntHandler
**
**   Description:
**    GTM Tim0 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim0_Channel0_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim0_Channel1_IntHandler
**
**   Description:
**    GTM Tim0 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim0_Channel1_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim0_Channel1_IntHandler
**
**   Description:
**    GTM Tim0 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim0_Channel2_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim1_Channel0_IntHandler
**
**   Description:
**    GTM Tim1 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim1_Channel0_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim1_Channel1_IntHandler
**
**   Description:
**    GTM Tim1 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim1_Channel1_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim1_Channel2_IntHandler
**
**   Description:
**    GTM Tim1 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim1_Channel2_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim1_Channel3_IntHandler
**
**   Description:
**    GTM Tim1 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim1_Channel3_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim1_Channel4_IntHandler
**
**   Description:
**    GTM Tim1 Channel4 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim1_Channel4_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim1_Channel5_IntHandler
**
**   Description:
**    GTM Tim1 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim1_Channel5_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim1_Channel6_IntHandler
**
**   Description:
**    GTM Tim1 Channel6 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim1_Channel6_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim1_Channel7_IntHandler
**
**   Description:
**    GTM Tim1 Channel7 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim1_Channel7_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim2_Channel0_IntHandler
**
**   Description:
**    GTM Tim2 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim2_Channel0_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim2_Channel1_IntHandler
**
**   Description:
**    GTM Tim2 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim2_Channel1_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim2_Channel2_IntHandler
**
**   Description:
**    GTM Tim2 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim2_Channel2_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim2_Channel3_IntHandler
**
**   Description:
**    GTM Tim2 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim2_Channel3_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim2_Channel4_IntHandler
**
**   Description:
**    GTM Tim2 Channel4 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim2_Channel4_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim2_Channel5_IntHandler
**
**   Description:
**    GTM Tim2 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim2_Channel5_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim2_Channel6_IntHandler
**
**   Description:
**    GTM Tim2 Channel6 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim2_Channel6_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Tim2_Channel7_IntHandler
**
**   Description:
**    GTM Tim2 Channel7 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Tim2_Channel7_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs0_Channel0_IntHandler
**
**   Description:
**    GTM Mcs0 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs0_Channel0_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs0_Channel1_IntHandler
**
**   Description:
**    GTM Mcs0 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs0_Channel1_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs0_Channel2_IntHandler
**
**   Description:
**    GTM Mcs0 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs0_Channel2_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs0_Channel3_IntHandler
**
**   Description:
**    GTM Mcs0 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs0_Channel3_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs0_Channel5_IntHandler
**
**   Description:
**    GTM Mcs0 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs0_Channel5_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs0_Channel6_IntHandler
**
**   Description:
**    GTM Mcs0 Channel6 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs0_Channel6_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs0_Channel7_IntHandler
**
**   Description:
**    GTM Mcs0 Channel7 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs0_Channel7_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs1_Channel0_IntHandler
**
**   Description:
**    GTM Mcs1 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs1_Channel0_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs1_Channel1_IntHandler
**
**   Description:
**    GTM Mcs1 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs1_Channel1_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs1_Channel2_IntHandler
**
**   Description:
**    GTM Mcs1 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs1_Channel2_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs1_Channel3_IntHandler
**
**   Description:
**    GTM Mcs1 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs1_Channel3_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs1_Channel5_IntHandler
**
**   Description:
**    GTM Mcs1 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs1_Channel5_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs1_Channel6_IntHandler
**
**   Description:
**    GTM Mcs1 Channel6 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs1_Channel6_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs1_Channel7_IntHandler
**
**   Description:
**    GTM Mcs1 Channel7 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs1_Channel7_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs2_Channel0_IntHandler
**
**   Description:
**    GTM Mcs2 Channel0 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs2_Channel0_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs2_Channel1_IntHandler
**
**   Description:
**    GTM Mcs2 Channel1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs2_Channel1_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs2_Channel2_IntHandler
**
**   Description:
**    GTM Mcs2 Channel2 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs2_Channel2_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs2_Channel3_IntHandler
**
**   Description:
**    GTM Mcs2 Channel3 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs2_Channel3_IntHandler(void);
/******************************************************************************
**   Function    : GTM_Mcs2_Channel4_IntHandler
**
**   Description:
**    GTM Mcs2 Channel4 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs2_Channel4_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Mcs2_Channel5_IntHandler
**
**   Description:
**    GTM Mcs2 Channel5 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Mcs2_Channel5_IntHandler(void);

/******************************************************************************
**   Function    : GTM_Atom0_Ch0_Ch1_IntHandler
**
**   Description:
**    GTM Atom0 Ch0 - Ch1 exception handler.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void GTM_Atom0_Ch0_Ch1_IntHandler(void);

/******************************************************************************
**   Function    : DMA_CH0_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[0].
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void DMA_CH0_ISR(void);    //IPRI_B0

/******************************************************************************
**   Function    : DMA_CH1_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[1].
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void DMA_CH1_ISR(void);    //ISEC_B0

/******************************************************************************
**   Function    : DMA_CH2_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[2].
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void DMA_CH2_ISR(void);    //ION_26

/******************************************************************************
**   Function    : DMA_CH3_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[3].
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void DMA_CH3_ISR(void);    //ION_04

/******************************************************************************
**   Function    : DMA_CH16_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[16].
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void DMA_CH16_ISR(void);  //IPRI_B1

/******************************************************************************
**   Function    : DMA_CH17_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[17].
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void DMA_CH17_ISR(void);  //ISEC_B1

/******************************************************************************
**   Function    : DMA_CH24_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[24].
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void DMA_CH24_ISR(void);  //ION_15

/******************************************************************************
**   Function    : DMA_CH37_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[37].
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void DMA_CH25_ISR(void);  //ION_37

/******************************************************************************
**   Function    : GTM_DPLL_GL1I_IntHandler
**
**   Description:
**    ISR handler for GTM DPLL GL1I.
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void GTM_DPLL_GL1I_IntHandler(void);

/******************************************************************************
**   Function    : GTM_DPLL_LL1I_IntHandler
**
**   Description:
**    ISR handler for GTM DPLL LL1I.
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
extern void GTM_DPLL_LL1I_IntHandler(void);

//#endif //_BUILD_SYS_
#endif // _BUILD_GTM_

#endif /* _GTM_HOST_INTERFACE_H_ */

/****************************************************************************
 ****************************************************************************/

