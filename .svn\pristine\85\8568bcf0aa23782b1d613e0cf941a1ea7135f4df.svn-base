/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MCAN
**  Filename        :  mcan_test.c
**  Created on      :  09-giu-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef  _BUILD_CAN_
#ifdef  _TEST_CAN_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "mcan.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
#define BUFFER_RX_POLLING     0
#define STANDARD_SIZE       8U

// #define TEST_MCAN1
#ifndef DISCOVERY_K2_CUT2_4
#define TEST_MCAN2
#define TEST_UDS
#endif

#ifdef TEST_MCAN1
uint8_T VectA0[STANDARD_SIZE] = {0x1u, 0x2u, 0x3u, 0x4u, 0x5u, 0x6u, 0x7u, 0x0u};
uint8_T VectA1[STANDARD_SIZE] = {0x8u, 0x9u, 0xAu, 0xBu, 0xCu, 0xDu, 0xEu, 0x0u};
uint8_T VectA2[STANDARD_SIZE] = {0xFu, 0xAAu, 0xBBu, 0xCCu, 0xDDu, 0xEEu, 0xFFu, 0x0u};
uint8_T VectA3[STANDARD_SIZE] = {0x10u, 0x11u, 0x12u, 0x13u, 0x14u, 0x15u, 0x16u, 0x0u};
uint8_T VectA4[STANDARD_SIZE] = {0x17u, 0x18u, 0x19u, 0x20u, 0x21u, 0x22u, 0x23u, 0x0u};
struct CAN_buff* buffA0_p;
struct CAN_buff* buffA1_p;
struct CAN_buff* buffA2_p;
struct CAN_buff* buffA3_p;
struct CAN_buff* buffA4_p;
static void CAN_MCAN1_Tests(void);
#endif

#ifdef TEST_MCAN2
#define CAN_RX_MASK_LOW ((uint32_T) ( (1u << 0u) | (1u << 1u) )) //Rx Buffer 0 - 1
#define CAN_RX_MASK_HIGH ((uint32_T) ( 0u))
#define CAN_RX_MASK_LOW_EXT ((uint32_T) ( (1u << 0u) ) )//Rx Buffer 0
#define CAN_RX_MASK_HIGH_EXT ((uint32_T) ( 0u) )
uint8_T VectB0[STANDARD_SIZE] = {0x1u, 0x2u, 0x3u, 0x4u, 0x5u, 0x6u, 0x7u, 0x0u};
uint8_T VectB1[STANDARD_SIZE] = {0x8u, 0x9u, 0xAu, 0xBu, 0xCu, 0xDu, 0xEu, 0x0u};
uint8_T VectB2[STANDARD_SIZE] = {0xFu, 0xAAu, 0xBBu, 0xCCu, 0xDDu, 0xEEu, 0xFFu, 0x0u};
uint8_T VectB3[STANDARD_SIZE] = {0x10u, 0x11u, 0x12u, 0x13u, 0x14u, 0x15u, 0x16u, 0x0u};
uint8_T VectB4[STANDARD_SIZE] = {0x17u, 0x18u, 0x19u, 0x20u, 0x21u, 0x22u, 0x23u, 0x0u};
struct CAN_buff* buffB0_p;
struct CAN_buff* buffB1_p;
struct CAN_buff* buffB2_p;
struct CAN_buff* buffB12_p;
struct CAN_buff* buffB63_p;
t_canMSG RxFrame;
static void CAN_MCAN2_Tests(void);
#endif

uint32_T cnt_tmp = 0U;
static uint8_T Flg_can_test_TX = 0u;
static uint8_T Flg_can_test = 0U;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : CAN_Test
**
**   Description:
**    Tests selected MCAN Channels by define.
**
******************************************************************************/
void CAN_Test(void) {

    if (Flg_can_test == 0U) {
        /* MCAN Config and Enable */
        CAN_Config();
        
#pragma ghs startnomisra
#ifdef  TEST_MCAN1
        CAN_EngineEnableReceive(MCAN_ENG_A); //MCAN_1
        CAN_Enable(MCAN_ENG_A); //MCAN_1
#endif

#ifdef  TEST_MCAN2   
        // CAN_EnableReceive(MCAN_ENG_B, 0U, CAN_STD);  
        // CAN_EnableReceive(MCAN_ENG_B, 0U, CAN_XTD);  
        // CAN_EnableReceive(MCAN_ENG_B, 1U, CAN_STD);  

        CAN_EngineEnableReceive(MCAN_ENG_B); //MCAN_2
        // CAN_EngineMaskedEnableReceive(MCAN_ENG_B, CAN_STD, CAN_RX_MASK_LOW, CAN_RX_MASK_HIGH); //MCAN_2
        // CAN_EngineMaskedEnableReceive(MCAN_ENG_B, CAN_XTD, CAN_RX_MASK_LOW_EXT, CAN_RX_MASK_HIGH_EXT); //MCAN_2
        // CAN_DisableReceive(MCAN_ENG_B, 0U, CAN_XTD);
        // CAN_ResetBufferRx(MCAN_ENG_B, 0U, CAN_STD);

        CAN_Enable(MCAN_ENG_B); //MCAN_2
#endif

#pragma ghs endnomisra

        Flg_can_test = 1U;
        Flg_can_test_TX = 1u;

    }

    if (Flg_can_test_TX == 1u)
    {
#ifdef  TEST_MCAN1
        CAN_MCAN1_Tests();
#endif

#ifdef  TEST_MCAN2
        CAN_MCAN2_Tests();
#endif
    }
}

/******************************************************************************
**   Function    : CAN_MCAN1_Tests
**
**   Description:
**    Tests for MCAN1.
**
******************************************************************************/
#ifdef TEST_MCAN1
static void CAN_MCAN1_Tests(void) {

    /* First message on MCAN_1 */
    VectA0[7]++;

    //	CAN_TxData(MCAN_ENG_B, 0, &VectB0[0]);
    //CAN_TxDataOptimized(MCAN_ENG_A, 0U, &VectA0[0], 8U);
    CAN_TxData(MCAN_ENG_A, 0U, &VectA0[0]);
    /* Second message on MCAN_1 */
    VectA1[7]++;
    CAN_TxData(MCAN_ENG_A, 1U, &VectA1[0]);
    /* Third message on MCAN_1 */
    VectA2[7]++;
    /* Fourth message on MCAN_1 */
    VectA3[7]++;
    CAN_TxData(MCAN_ENG_A, 3U, &VectA3[0]);
    /* Fifth message on MCAN_1 */
    VectA4[7]++;
    CAN_TxData(MCAN_ENG_A, 4U, &VectA4[0]);

    
    /* Reading task with RxData */
    CAN_RxData(MCAN_ENG_A, 0U, &buffA0_p);
    /* Reading task with RxData */
    CAN_RxData(MCAN_ENG_A, 1U, &buffA1_p);    
    /* Reading task with RxData */
    CAN_RxData(MCAN_ENG_A, 2U, &buffA2_p);      
    /* Reading task with RxData */
    CAN_RxData(MCAN_ENG_A, 3U, &buffA3_p);        
    /* Reading task with RxData */
    CAN_RxData(MCAN_ENG_A, 4U, &buffA4_p);

}
#endif

/******************************************************************************
**   Function    : CAN_MCAN2_Tests
**
**   Description:
**    Tests for MCAN2.
**
******************************************************************************/
#ifdef TEST_MCAN2
static void CAN_MCAN2_Tests(void) {

#ifndef TEST_UDS
    /* First message on MCAN_2 */
    VectB0[7]++;

    //CAN_TxData(MCAN_ENG_B, 0, &VectB0[0]);
    CAN_TxDataOptimized(MCAN_ENG_B, 0U, &VectB0[0],STANDARD_SIZE);

    for (cnt_tmp = 0U; cnt_tmp < 10000U; cnt_tmp++) {

    }

    /* Second message on MCAN_2 */
    VectB1[7]++;

    CAN_TxData(MCAN_ENG_B, 1U, &VectB1[0]);

    for (cnt_tmp = 0U; cnt_tmp < 10000U; cnt_tmp++) {

    }

    /* Third message on MCAN_2 */
    VectB2[7]++;

    CAN_TxData(MCAN_ENG_B, 2U, &VectB2[0]);

    for (cnt_tmp = 0U; cnt_tmp < 10000U; cnt_tmp++) {

    }

    /* Fourth message on MCAN_2 */
    VectB3[7]++;

    CAN_TxData(MCAN_ENG_B, 3U, &VectB3[0]);

    for (cnt_tmp = 0U; cnt_tmp < 10000U; cnt_tmp++) {

    }

    /* Fifth message on MCAN_2 */
    VectB4[7]++;

    CAN_TxData(MCAN_ENG_B, 4U, &VectB4[0]);

    for (cnt_tmp = 0U; cnt_tmp < 10000U; cnt_tmp++) {

    }

#if (MCAN_2_RX_INT_LINE != CAN_RX_INT_DISABLE)
    /* Reading task with RxData */
    CAN_RxData(MCAN_ENG_B, 0U, &buffB0_p);
    CAN_RxData(MCAN_ENG_B, 1U, &buffB1_p);
    CAN_RxData(MCAN_ENG_B, 2U, &buffB2_p);
    CAN_RxData(MCAN_ENG_B, 12U, &buffB12_p);
    CAN_RxData(MCAN_ENG_B, 63U, &buffB63_p);
#endif

#if (MCAN_2_RX_INT_LINE == CAN_RX_INT_DISABLE)
    while (CAN_ReceiveMsg(MCAN_ENG_B, 1U, &RxFrame) != 0) {

    }
#endif

#else /* TEST_UDS */

    VectB0[7]++;
    CAN_TxDataOptimized(MCAN_ENG_B, 0U, &VectB0[0],CAN_CHB_BUF0_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }

    VectB1[7]++;
    CAN_TxDataOptimized(MCAN_ENG_B, 1U, &VectB1[0],CAN_CHB_BUF1_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }
       
    VectB2[7]++;
    CAN_TxDataOptimized(MCAN_ENG_B, 2U, &VectB2[0],CAN_CHB_BUF2_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }
    
    VectB3[7]++;
    CAN_TxDataOptimized(MCAN_ENG_B, 3U, &VectB3[0],CAN_CHB_BUF3_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }
    
    VectB4[7]++;
    CAN_TxDataOptimized(MCAN_ENG_B, 4U, &VectB4[0],CAN_CHB_BUF4_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }
    
    CAN_TxDataOptimized(MCAN_ENG_B, 5U, &VectB0[0],CAN_CHB_BUF5_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }
    
    CAN_TxDataOptimized(MCAN_ENG_B, 6U, &VectB1[0],CAN_CHB_BUF6_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }
        
    CAN_TxDataOptimized(MCAN_ENG_B, 7U, &VectB2[0],CAN_CHB_BUF7_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }
    
    CAN_TxDataOptimized(MCAN_ENG_B, 8U, &VectB3[0],CAN_CHB_BUF8_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }
    
    CAN_TxDataOptimized(MCAN_ENG_B, 9U, &VectB4[0],CAN_CHB_BUF9_TX_DLC);
    for (cnt_tmp = 0U; cnt_tmp < 1000U; cnt_tmp++) {

    }

#endif /* TEST_UDS */

}
#endif

#endif /* __TEST_CAN_ */
#endif /* _BUILD_CAN_ */

