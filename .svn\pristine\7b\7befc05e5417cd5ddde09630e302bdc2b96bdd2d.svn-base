/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_out.h
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Mocci A
******************************************************************************/
/*****************************************************************************
**
**                        SafetyMngr Description
**
**  This SWC's purpose is to implements all required actions and checks for 
**  ASIL-B maturation level.
******************************************************************************/

#ifndef SAFETYMNGR_OUT_H
#define SAFETYMNGR_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RTWtypes.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint8_T FlgSMError;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_Init
**
**   Description:
**    Inizialize the safety manager mechanism
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_Init(void);

/******************************************************************************
**   Function    : SafetyMngr_Periodic10ms
**
**   Description:
**    Safety Manager periodic task
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_Periodic10ms(void);

/******************************************************************************
**   Function    : SafetyMngr_ArrayIntegritySelfCheck
**
**   Description:
**    SafetyMngr interface to perform the array integrity self check safety
**    mechanism
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_ArrayIntegritySelfCheck(void);

/******************************************************************************
**   Function    : SafetyMngr_DMARequestCheck
**
**   Description:
**    SafetyMngr interface to perform the DMA Request check safety mechanism
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_DMARequestCheck(void);

/******************************************************************************
**   Function    : SafetyMngr_EnableResetGeneration
**
**   Description:
**    This unit enables the reset in case of error.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_EnableResetGeneration(void);

/******************************************************************************
**   Function    : SafetyMngr_EnableSafetyInterrupts
**
**   Description:
**    This unit enable the Safety Interrupts
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_EnableSafetyInterrupts(void);

/******************************************************************************
**   Function    : SafetyMngr_EnterSafeMode
**
**   Description:
**    This unit enable the Safety Interrupts
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_EnterSafeMode(void);

/******************************************************************************
**   Function    : SafetyMngr_FCCUInit
**
**   Description:
**    SafetyMngr interface to perform the Fccu init configuration
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_FCCUInit(void);

/******************************************************************************
**   Function    : SafetyMngr_FlashIntegrityCheckExpectedValues
**
**   Description:
**    SafetyMngr interface to perform the Flash Integrity check safety mechanism
**
**   Parameters :
**    [out] uint32_T* expectedValues : 
**    [in] uint8_T number : 
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_FlashIntegrityCheckExpectedValues(uint32_T * expectedValues, uint8_T number);

/******************************************************************************
**   Function    : SafetyMngr_INTCCheckCtx
**
**   Description:
**    SafetyMngr interface to perform the Interrupt controller check safety
**    mechanism in runtime
**
**   Parameters :
**    [in] uint8_T coreId : coreId Id of the core configured for the interrupt.
**    [in] uint8_T prio : Priority configured for the interrupt.
**    [in] uint32_T irqStsReg : Value contained in interrupt pending register.
**    [in] uint32_T maskReg : Mask that has to be applied for the check
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_INTCCheckCtx(uint8_T coreId, uint8_T prio, uint32_T irqStsReg, uint32_T maskReg);

/******************************************************************************
**   Function    : SafetyMngr_INTCCheckInit
**
**   Description:
**    SafetyMngr interface to perform the Interrupt controller check safety
**    mechanism 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_INTCCheckInit(void);

/******************************************************************************
**   Function    : SafetyMngr_MCMETransitionCheck
**
**   Description:
**    SafetyMngr interface to perform the MC_ME Transition check safety mechanism
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_MCMETransitionCheck(void);

/******************************************************************************
**   Function    : SafetyMngr_MCRGMCheckInit
**
**   Description:
**    SafetyMngr interface to perform the MC_RGM Transition check safety
**    mechanism
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_MCRGMCheckInit(void);

/******************************************************************************
**   Function    : SafetyMngr_MCRGMReadResetReason
**
**   Description:
**    SafetyMngr interface to perform the MC_RGM Read Reset Reason check safety
**    mechanism
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_MCRGMReadResetReason(void);

/******************************************************************************
**   Function    : SafetyMngr_REGPROTSafetyInit
**
**   Description:
**    SafetyMngr interface to perform the REG_PROT init configuration
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_REGPROTSafetyInit(void);

/******************************************************************************
**   Function    : SafetyMngr_REGPROTSafetyReset
**
**   Description:
**    SafetyMngr interface to perform the REG_PROT reset configuration
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_REGPROTSafetyReset(void);

/******************************************************************************
**   Function    : SafetyMngr_SIUL2Config
**
**   Description:
**    SafetyMngr interface to perform the SIUL2 Peripheral configuration
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_SIUL2Config(void);

/******************************************************************************
**   Function    : SafetyMngr_SMPUInit
**
**   Description:
**    SafetyMngr interface to perform the SMPU module configuration
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_SMPUInit(void);

/******************************************************************************
**   Function    : SafetyMngr_SetPINsTestMode
**
**   Description:
**    SafetyMngr interface to set the PINs in Test Mode
**
**   Parameters :
**    [in] boolean_T TestMode : 
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_SetPINsTestMode(boolean_T TestMode);

/******************************************************************************
**   Function    : SafetyMngr_SysClock_CheckInit
**
**   Description:
**    SafetyMngr interface to perform the Sys clock safety mechanism check init
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_SysClock_CheckInit(void);


#endif // SAFETYMNGR_OUT_H
/****************************************************************************
 ****************************************************************************/

