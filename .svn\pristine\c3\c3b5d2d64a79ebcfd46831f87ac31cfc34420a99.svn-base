/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  Checksum.c
**  Created on      :  22-giu-2020 10:00:00
**  Original author :  CarboniM
**  Integration history: 
**  -  Standard software driver for C55 Flash from "STM - SPC57xKLS-FLASH Driver for C55: Package version 0.2.0"
**          
*******************************************************************************************************************/
#ifdef _BUILD_FLASH_
#pragma ghs startnomisra

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
//nclude    "rtwtypes.h"
//nclude    "ssd_types.h"
#include    "ssd_c55.h"

#ifdef USE_FLASHCHKSUM_ST_DRIVER

#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1u) 
extern unsigned short CheckSum_C[] = 
#else
extern const unsigned short CheckSum_C[] = 
#endif
{
#if (TARGET_TYPE == SPC574K2_CUT24) || (TARGET_TYPE == SPC574K2)     /* Total Size = # half words */

      0x0080, 0x1821, 0x06F0, 0xD3F1, 0xD501, 0x0233, 0x0244,
      0x0153, 0x0164, 0x0175, 0x480F, 0x2C07, 0x0C74, 0xE206,
      0x5185, 0x0000, 0xC135, 0xC645, 0xE810, 0x7D87, 0x1B78, 
      0x70E0, 0xC803, 0xE603, 0x481F, 0xE825, 0x2A03, 0xE623,
      0x5585, 0x0000, 0xD135, 0xD645, 0x4807, 0xD074, 0x7C6A, 
      0x1470, 0x188A, 0xA878, 0xE403, 0x7140, 0x0078, 0x7120,
      0x0000, 0xE80A, 0xC064, 0x50EC, 0x0000, 0x0476, 0xD064, 
      0x198C, 0x8004, 0x1929, 0x8001, 0x7C09, 0x5040, 0xE4F5,
      0x5585, 0x0000, 0x7D47, 0x1070, 0x0737, 0xD175, 0x30EB, 
      0x0048, 0x2A07, 0xE608, 0x7FE3, 0xFB78, 0x1800, 0xD000,
      0x0002, 0x1800, 0xD000, 0x01F7, 0x0173, 0xC3F1, 0xC501,
      0x20F1, 0x0090, 0x0004, 0x3038, 0x3030, 0x3846, 0x4646
#endif   
}; /* Total Size = 84 half words */


#else  //ELDOR Chksum for tag checks

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * BDMEnable - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static inline void BDMEnable(void);


/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
static uint32_T returnCodeLocal;     


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * CheckSum - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint32_T CheckSum ( PSSD_CONFIG pSSDConfig,
                    uint32_T dest,
                    uint32_T size,
                    uint32_T *pSum,
                    void (*CallBack)(void))
{

    uint32_T destIndex;                 /* destination address index */
    uint32_T shadowRowEnd;              /* shadow row base + shadow size */
    uint32_T mainArrayEnd;              /* main array base + main array size */
    uint32_T temp;                      /* dest + size, or CallBack step */

    returnCodeLocal = C55_OK; 


    /* Check alignments */
    if ( ((dest | size) % C55_WORD_SIZE) != 0 )
    {
        returnCodeLocal = C55_ERROR_ALIGNMENT;

        if (pSSDConfig->BDMEnable)
            BDMEnable();
        return returnCodeLocal;

    }

    temp = dest + size;

    if  ((((dest >= FIRST_CODE_FLASH_ADDRESS) && (dest < LAST_CODE_FLASH_ADDRESS) && (temp <= LAST_CODE_FLASH_ADDRESS )) || 
          ((dest >= FIRST_DATA_FLASH_ADDRESS) && (dest  < LAST_DATA_FLASH_ADDRESS) && (temp <= LAST_DATA_FLASH_ADDRESS))) == 0u)
    {
       
        returnCodeLocal = C90FL_ERROR_RANGE;
        if (pSSDConfig->BDMEnable)
            BDMEnable();

        return returnCodeLocal;
    }
  
   *pSum = 0;
    temp = 0;

    /* word by word checksum */
    for (destIndex = 0; destIndex < (size / C55_WORD_SIZE); destIndex++)
    {
        /**********************/
        uint32_T swappedWord = 0;

        swappedWord |=  *(uint8_T *)(dest  );
        swappedWord |= (*(uint8_T *)(dest+1))<<8;
        swappedWord |= (*(uint8_T *)(dest+2))<<16;
        swappedWord |= (*(uint8_T *)(dest+3))<<24;

        *pSum += swappedWord;
        /**********************/
        /*        *pSum += *(uint32_T *)dest;          */

        dest += C55_WORD_SIZE;

        /* CallBack */
        if( (CallBack != NULL_CALLBACK) && (destIndex == temp) )
        {
            CallBack();
            temp += CALLBACK_CS;
        }
    }    

    return returnCodeLocal;
}


/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/

/*--------------------------------------------------------------------------*
 * BDMEnable - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/

static __asm  inline void BDMEnable(void)
{
    //lis r5, returnCodeLocal@h     # Load upper address of BIUAPR into R5
    e_lis r5, returnCodeLocal@h     # Load upper address of BIUAPR into R5
    //ori r5, r5, returnCodeLocal@l # Load lower address of BIUAPR into R5
    e_or2i  r5, returnCodeLocal@l
    mr r3,r5
    //sc                  /* generate system call interrupt */
    se_sc                  /* generate system call interrupt */
}
#endif

#pragma ghs endnomisra


#endif /* _BUILD_FLASH_ */
