#ifdef _BUILD_DIAGCANMGM_

#include "../include/diagcanmgm_Ferrari.h"

ECUcodeStrucTagID2    ECUcodeID2 =
{
    {"50543254   "},                                //0xF187    Vehicle Manufacturer spare part number
    {"52088219   "},                                //0xF188    Vehicle Manufacturer ECU Software Number
    {"          "},                                 //0xF18A    Vehicle Manufacturer ECU Software Calibration Number
    {"          "},                                 //0xF18B    Vehicle Manufacturer ECU Software Application Number
    {"                 "},                          //0xF190    Vehicle Identification Number
    {"52088218   "},                                //0xF191    Vehicle Manufacturer ECU Hardware Number
    {"      "},                                     //0xF1A4    Sincom and Factory
    {0x00u,0x58u,0x50u,0x28u,0x16u}                 //0xF1A5    ISO Code
};

ECUcodeStrucSupplier  ECUcodeSupData =
{
    {0u,0u,0u,0u,0u,0u,0u,0u,0u,0u,0u,0u,0u,0u,0u}, // UDS 0xF18C     ECU Serial Number
    ELDOR_ECU_HW_NUM,                               // UDS 0xF192     System Supplier ECU Hardware Number
    ELDOR_ECU_HW_VER,                               // UDS 0xF193     System Supplier ECU Hardware Version
    "NO APPLICAT",                                  // UDS 0xF194     System Supplier ECU Software Number
    {0xffu, 0xffu},                                 // UDS 0xF195     System Supplier ECU Software Version
    {"      "}                                      // UDS 0xF196     Homologation Code/Approval Number
};

ECUcodeEldor_T ECUcodeEldor =
{
    {"BOOT       "},                                // Eldor ECU SW Number, 11 ASCII UDS 0xF1F0
};


#endif // _BUILD_DIAGCANMGM_

