/* syncmgm  F173 header file */

#define MAX_INDEX               11
#define INDEX_GAP0            1
#define INDEX_GAP1              6

#define USE_RPM_CAN             0   // = 1 (per abilitare la lettura dei giri da CAN)

//#define  USE_ANGLE_EX
#define CAM_TYPE                (CAM_PIO_SENS)    // =0 sensore assente =1 solo livello (DIGIO) =2 livello + fronti (PIO)

#define INDEX_CAM0              0
#define INDEX_CAM1              5

#define N_CAM_EDGE              1
#define ENABLE_RESYNC_ON_EX_SYNC    0
#define ENABLE_RESYNC_ON_EV_CAMTEST 0
#define MAX_TOOTH_DELETED           2


/* COSTANTI PER CALCOLO AbsHTdc, AbsPreTdc, AbsPreHTdc */
#define TDC2PREHTDC 0
#define TDC2PRETDC 1
#define TDC2HTDC 2

// Struttura contenente tutti gli eventi di interrupt 
typedef struct
{
  uint8_T   ToothNumber;  // Numero di dente sul quale deve essere generata l'eccezione
  uint8_T   AbsTdcIndex;  // Absolute Tdc index table
  uint16_T  EventType;      // Event table type
  uint16_T  EventAngle;   // Event table type
} EVENT_STRUCT;

extern const uint32_T TDC_ANGLE[N_CYL_MAX * N_BANKS_MAX];
extern const uint32_T TDC_TOOTH[N_BANKS_MAX][N_CYL_MAX];