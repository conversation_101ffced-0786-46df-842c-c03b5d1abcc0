/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  SwcName
**  Filename        :  SwcName.h
**  Created on      :  16-giu-2020 14:04:00
**  Original author :  CarboniM
******************************************************************************/

#ifndef _DSPI_CFG_H_
#define _DSPI_CFG_H_

#pragma ghs startnomisra

#ifdef _BUILD_DMA_
//#define _SPI_USE_DMA_ MC, tba for K2
#endif

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define SPI_NUM_OF_CS 6u

#define TX_BUFFER_SIZE 100u
#define RX_BUFFER_SIZE (TX_BUFFER_SIZE)

#define TX_FIFO_SIZE        4u
#define RX_FIFO_SIZE        4u

#define SPI_TXRX_BUFFER_SIZE 70u
#if ((SPI_TXRX_BUFFER_SIZE < TX_BUFFER_SIZE) && (SPI_TXRX_BUFFER_SIZE < RX_BUFFER_SIZE))
/* Dimension Ok */
#else
#error Dimension out of range
#endif

#define SPI_TIMEOUT 20000u /*time in  usec, max time to wait for tx/rx */

/* CS */
#define IDX_PCS0 0u
#define IDX_PCS1 1u
#define IDX_PCS2 2u
#define IDX_PCS3 3u
#define IDX_PCS4 4u
#define IDX_PCS5 5u
#define IDX_PCS6 4u
#define IDX_PCS7 5u

/* SPI baudrate configuration based on clock frequency */
#if (DSPI_CLK==128u)
#define DBR_VAL_8M        0u
#define DBR_VAL_4M        0u
#define DBR_VAL_2M        0u
#define DBR_VAL_1M        0u
#define DBR_VAL_500K      0u
#define DBR_VAL_250K      0u
#define DBR_VAL_125K      0u
#define DBR_VAL_62500     0u
#define DBR_VAL_31250     0u

#define SPI_BRP_8M        BRP_SCALER_2
#define SPI_BRP_4M        BRP_SCALER_2
#define SPI_BRP_2M        BRP_SCALER_2
#define SPI_BRP_1M        BRP_SCALER_2
#define SPI_BRP_500K      BRP_SCALER_2
#define SPI_BRP_250K      BRP_SCALER_2
#define SPI_BRP_125K      BRP_SCALER_2
#define SPI_BRP_62500     BRP_SCALER_2
#define SPI_BRP_31250     BRP_SCALER_2

#define SPI_BR_8M         BR_SCALER_8 
#define SPI_BR_4M         BR_SCALER_16 
#define SPI_BR_2M         BR_SCALER_32 
#define SPI_BR_1M         BR_SCALER_64 
#define SPI_BR_500K       BR_SCALER_128 
#define SPI_BR_250K       BR_SCALER_256
#define SPI_BR_125K       BR_SCALER_512
#define SPI_BR_62500      BR_SCALER_1024
#define SPI_BR_31250      BR_SCALER_2048

#elif (DSPI_CLK==80u)
#define DBR_VAL_8M        0u
#define DBR_VAL_4M        0u
#define DBR_VAL_2M        0u
#define DBR_VAL_1M        0u
#define DBR_VAL_714K      0u
#define DBR_VAL_500K      0u
#define DBR_VAL_250K      0u
#define DBR_VAL_208K      0u
#define DBR_VAL_178K      0u
#define DBR_VAL_125K      0u
#define DBR_VAL_62500     0u
#define DBR_VAL_31250     0u

#define SPI_BRP_8M        (BRP_SCALER_5)
#define SPI_BRP_4M        (BRP_SCALER_5)
#define SPI_BRP_2M        (BRP_SCALER_5)
#define SPI_BRP_1M        (BRP_SCALER_5)
#define SPI_BRP_714K      (BRP_SCALER_7)
#define SPI_BRP_500K      (BRP_SCALER_5)
#define SPI_BRP_250K      (BRP_SCALER_5)
#define SPI_BRP_208K      (BRP_SCALER_3)
#define SPI_BRP_178K      (BRP_SCALER_7)
#define SPI_BRP_125K      (BRP_SCALER_5)
#define SPI_BRP_62500     (BRP_SCALER_5)
#define SPI_BRP_31250     (BRP_SCALER_5)

#define SPI_BR_8M         (BR_SCALER_2) 
#define SPI_BR_4M         (BR_SCALER_4) 
#define SPI_BR_2M         (BR_SCALER_8) 
#define SPI_BR_1M         (BR_SCALER_16)
#define SPI_BR_714K       (BR_SCALER_16)
#define SPI_BR_500K       (BR_SCALER_32)
#define SPI_BR_250K       (BR_SCALER_64)
#define SPI_BR_208K       (BR_SCALER_128)
#define SPI_BR_178K       (BR_SCALER_64)
#define SPI_BR_125K       (BR_SCALER_128)
#define SPI_BR_62500      (BR_SCALER_256)
#define SPI_BR_31250      (BR_SCALER_512)

#elif (DSPI_CLK==60)
#define DBR_VAL_6M        0u
#define DBR_VAL_4M        1u
#define DBR_VAL_2M        0u
#define DBR_VAL_1250K     0u
#define DBR_VAL_750K      0u
#define DBR_VAL_187K      0u

#define SPI_BRP_6M        (BRP_SCALER_5)
#define SPI_BRP_4M        (BRP_SCALER_5)
#define SPI_BRP_2M        (BRP_SCALER_5)
#define SPI_BRP_1250K     (BRP_SCALER_3)
#define SPI_BRP_750K      (BRP_SCALER_5) 
#define SPI_BRP_187K      (BRP_SCALER_5)

#define SPI_BR_6M         (BR_SCALER_2) 
#define SPI_BR_4M         (BR_SCALER_6) 
#define SPI_BR_2M         (BR_SCALER_6)
#define SPI_BR_1250K      (BR_SCALER_16) 
#define SPI_BR_750K       (BR_SCALER_16) 
#define SPI_BR_187K       (BR_SCALER_64)

#elif (DSPI_CLK==40)
#define SPI_BRP_10M        (BRP_SCALER_2)
#define SPI_BRP_5M         (BRP_SCALER_2)
#define SPI_BRP_4M         (BRP_SCALER_5)
#define SPI_BRP_2500K      (BRP_SCALER_2)
#define SPI_BRP_2M         (BRP_SCALER_5)
#define SPI_BRP_1250K      (BRP_SCALER_2)
#define SPI_BRP_1M         (BRP_SCALER_5)
#define SPI_BRP_800K       (BRP_SCALER_3)
#define SPI_BRP_500K       (BRP_SCALER_5)

#define SPI_BR_10M         (BR_SCALER_2)
#define SPI_BR_5M          (BR_SCALER_4)
#define SPI_BR_4M          (BR_SCALER_2)
#define SPI_BR_2500K       (BR_SCALER_2)
#define SPI_BR_2M          (BR_SCALER_4)
#define SPI_BR_1250K       (BR_SCALER_16)
#define SPI_BR_1M          (BR_SCALER_8)
#define SPI_BR_800K        (BR_SCALER_16)
#define SPI_BR_500K        (BR_SCALER_16)

#endif

/* SPI PAD CFG*/
#define SPI_PDA_GPIO 0u
#define SPI_PDA_PRI1 1u
#define SPI_PDA_PRI2 3u
#define SPI_PDA_ATL1 2u
#define SPI_PDA_ATL2 4u

/****************************************************************************/
/*                        Peripheral CONFIGURATION                          */
/*      The User should change only the following parameters for the        */
/*      correct peripheral configuration                                    */
/****************************************************************************/                                  

/* SPI Channel Configuration */
#define SPI_CH_A_EN 0u
#define SPI_CH_B_EN 0u   
#define SPI_CH_C_EN 0u
#define SPI_CH_D_EN 0u
#define SPI_CH_E_EN 1u

#define SPI_CH_A_EXTERNAL 0u //Enable External Pins for the SPI CHannel A
#define SPI_CH_B_EXTERNAL 0u //Enable External Pins for the SPI CHannel B
#define SPI_CH_C_EXTERNAL 0u //Enable External Pins for the SPI CHannel C
#define SPI_CH_D_EXTERNAL 0u //Enable External Pins for the SPI CHannel D
#define SPI_CH_E_EXTERNAL 0u //Enable External Pins for the SPI CHannel E

#define SPI_CH_A_MODE       (SPI_SLAVE)
#define SPI_CH_A_IRQ_MASK   0x0u // EOQ IRQ
#define SPI_CH_A_CNTS_SCK   0u  /*Continuous Clock */
#define SPI_CH_A_PULL_MODE  (DISABLE_PULL)
#define SPI_CH_A_DRVSTREN   (DRVSTR_10PF)       
#define SPI_CH_A_PCS_SRC    (MED_SLEWRATE)      /* A CHANNEL SLEW RATE CONTROL */       

#define SPI_CH_B_MODE       (SPI_SLAVE)
#define SPI_CH_B_IRQ_MASK   0x2u   // EOQ IRQ
#define SPI_CH_B_CNTS_SCK   0u    /*Continuous Clock */
#define SPI_CH_B_PULL_MODE  (DISABLE_PULL)
#define SPI_CH_B_DRVSTREN   (DRVSTR_10PF)       
#define SPI_CH_B_PCS_SRC    (MED_SLEWRATE)      /* B CHANNEL SLEW RATE CONTROL */

#define SPI_CH_C_MODE       (SPI_MASTER)
#define SPI_CH_C_IRQ_MASK   0x2u // EOQ IRQ
#define SPI_CH_C_CNTS_SCK   0u    /*Continuous Clock */
#define SPI_CH_C_PULL_MODE  (DISABLE_PULL)    
#define SPI_CH_C_DRVSTREN   (DRVSTR_10PF)       
#define SPI_CH_C_PCS_SRC    (MED_SLEWRATE)      /* C CHANNEL SLEW RATE CONTROL */

#define SPI_CH_D_MODE       (SPI_MASTER)
#define SPI_CH_D_IRQ_MASK   0x2u // EOQ IRQ
#define SPI_CH_D_CNTS_SCK   0u    /*Continuous Clock */
#define SPI_CH_D_PULL_MODE  (DISABLE_PULL)   
#define SPI_CH_D_DRVSTREN   (DRVSTR_10PF)       
#define SPI_CH_D_PCS_SRC    (MED_SLEWRATE)      /* D CHANNEL SLEW RATE CONTROL */

#define SPI_CH_E_MODE       (SPI_MASTER)
#define SPI_CH_E_IRQ_MASK   0x2u   // EOQ IRQ
#define SPI_CH_E_CNTS_SCK   0u    /*Continuous Clock */
#define SPI_CH_E_PULL_MODE  (DISABLE_PULL)
#define SPI_CH_E_DRVSTREN   (DRVSTR_10PF)
#define SPI_CH_E_PCS_SRC    (MED_SLEWRATE)      /* E CHANNEL SLEW RATE CONTROL */

#define SPI_INTERNAL_CONNECTION 0x00000000 /* No internal Connection */


/* SPI PINOUT CONFIGURATION - SIU PINS */

#define PCSA0_ENABLE  1u             /* (1) Enable the SPI PCSA0 Chip Select Attenzione Conflitto con CAN B*/
#define PCSA0_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSA0 Chip Select funtion pointer */
#define PCSA1_ENABLE  0u             /* (1) Enable the SPI PCSA1 Chip Select */
#define PCSA1_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSA1 Chip Select funtion pointer */
#define PCSA2_ENABLE  0u             /* (1) Enable the SPI PCSA2 Chip Select */
#define PCSA2_FUNC_ENABLE  0u   /* (1) Enable the SPI PCSA2 Chip Select funtion pointer */
#define PCSA3_ENABLE  0u             /* (1) Enable the SPI PCSA3 Chip Select */
#define PCSA3_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSA3 Chip Select funtion pointer */
#define PCSA4_ENABLE  0u             /* (1) Enable the SPI PCSA4 Chip Select */
#define PCSA4_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSA4 Chip Select funtion pointer  */
#define PCSA5_ENABLE  0u             /* (1) Enable the SPI PCSA5 Chip Select */
#define PCSA5_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSA5 Chip Select funtion pointer  */
#define PCSA6_ENABLE  0u             /* (1) Enable the SPI PCSA4 Chip Select */
#define PCSA6_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSA4 Chip Select funtion pointer  */
#define PCSA7_ENABLE  0u             /* (1) Enable the SPI PCSA5 Chip Select */
#define PCSA7_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSA5 Chip Select funtion pointer  */

#define PCSB0_ENABLE  0u             /* (1) Enable the SPI PCSB0 Chip Select */
#define PCSB0_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSB0 Chip Select funtion pointer */
#define PCSB1_ENABLE  0u             /* (1) Enable the SPI PCSB1 Chip Select */
#define PCSB1_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSB1 Chip Select funtion pointer */
#define PCSB2_ENABLE  0u             /* (1) Enable the SPI PCSB2 Chip Select */
#define PCSB2_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSB2 Chip Select funtion pointer */
#define PCSB3_ENABLE  0u             /* (1) Enable the SPI PCSB3 Chip Select */
#define PCSB3_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSB3 Chip Select funtion pointer */
#define PCSB4_ENABLE  0u             /* (1) Enable the SPI PCSB4 Chip Select */
#define PCSB4_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSB4 Chip Select funtion pointer */
#define PCSB5_ENABLE  0u             /* (1) Enable the SPI PCSB5 Chip Select */ 
#define PCSB5_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSB5 Chip Select funtion pointer */
#define PCSB6_ENABLE  0u             /* (1) Enable the SPI PCSB4 Chip Select */
#define PCSB6_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSB4 Chip Select funtion pointer */
#define PCSB7_ENABLE  0u             /* (1) Enable the SPI PCSB5 Chip Select */
#define PCSB7_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSB5 Chip Select funtion pointer */

#define PCSC0_ENABLE  0u             /* (1) Enable the SPI PCSC0 Chip Select */
#define PCSC0_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSC0 Chip Select funtion pointer */
#define PCSC1_ENABLE  0u             /* (1) Enable the SPI PCSC1 Chip Select */
#define PCSC1_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSC1 Chip Select funtion pointer */
#define PCSC2_ENABLE  0u             /* (1) Enable the SPI PCSC2 Chip Select */ 
#define PCSC2_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSC2 Chip Select funtion pointer */
#define PCSC3_ENABLE  0u             /* (1) Enable the SPI PCSC3 Chip Select */ 
#define PCSC3_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSC3 Chip Select funtion pointer */
#define PCSC4_ENABLE  0u             /* (1) Enable the SPI PCSC4 Chip Select */ 
#define PCSC4_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSC4 Chip Select funtion pointer */
#define PCSC5_ENABLE  0u             /* (1) Enable the SPI PCSC5 Chip Select */
#define PCSC5_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSC5 Chip Select funtion pointer */

#define PCSD0_ENABLE  0u             /* (1) Enable the SPI PCSD0 Chip Select */
#define PCSD0_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD0 Chip Select funtion pointer */
#define PCSD1_ENABLE  0u            /* (1) Enable the SPI PCSD1 Chip Select */
#define PCSD1_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD1 Chip Select funtion pointer */
#define PCSD2_ENABLE  0u             /* (1) Enable the SPI PCSD2 Chip Select */
#define PCSD2_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD2 Chip Select funtion pointer */
#define PCSD3_ENABLE  0u             /* (1) Enable the SPI PCSD3 Chip Select */
#define PCSD3_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD3 Chip Select funtion pointer */
#define PCSD4_ENABLE  0u             /* (1) Enable the SPI PCSD4 Chip Select */
#define PCSD4_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD4 Chip Select funtion pointer */
#define PCSD5_ENABLE  0u             /* (1) Enable the SPI PCSD5 Chip Select */
#define PCSD5_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD5 Chip Select funtion pointer */
#define PCSD6_ENABLE  0u             /* (1) Enable the SPI PCSD4 Chip Select */
#define PCSD6_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD4 Chip Select funtion pointer */
#define PCSD7_ENABLE  0u             /* (1) Enable the SPI PCSD5 Chip Select */
#define PCSD7_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD5 Chip Select funtion pointer */

#define PCSE0_ENABLE  1u             /* (1) Enable the SPI PCSD0 Chip Select */
#define PCSE0_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD0 Chip Select funtion pointer */
#define PCSE1_ENABLE  0u            /* (1) Enable the SPI PCSD1 Chip Select */
#define PCSE1_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD1 Chip Select funtion pointer */
#define PCSE2_ENABLE  0u             /* (1) Enable the SPI PCSD2 Chip Select */
#define PCSE2_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD2 Chip Select funtion pointer */
#define PCSE3_ENABLE  0u             /* (1) Enable the SPI PCSD3 Chip Select */
#define PCSE3_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD3 Chip Select funtion pointer */
#define PCSE4_ENABLE  0u             /* (1) Enable the SPI PCSD4 Chip Select */
#define PCSE4_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD4 Chip Select funtion pointer */
#define PCSE5_ENABLE  0u             /* (1) Enable the SPI PCSD5 Chip Select */
#define PCSE5_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD5 Chip Select funtion pointer */
#define PCSE6_ENABLE  0u             /* (1) Enable the SPI PCSD4 Chip Select */
#define PCSE6_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD4 Chip Select funtion pointer */
#define PCSE7_ENABLE  0u             /* (1) Enable the SPI PCSD5 Chip Select */
#define PCSE7_FUNC_ENABLE  0u  /* (1) Enable the SPI PCSD5 Chip Select funtion pointer */


#define PCSA0_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSA1_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSA2_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSA3_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSA4_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSA5_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSA6_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSA7_ACT_STATE     (PCS_ACTIVE_LOW)

#define PCSB0_ACT_STATE     (PCS_ACTIVE_LOW)            
#define PCSB1_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSB2_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSB3_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSB4_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSB5_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSB6_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSB7_ACT_STATE     (PCS_ACTIVE_LOW)

#define PCSC0_ACT_STATE     (PCS_ACTIVE_LOW)              
#define PCSC1_ACT_STATE     (PCS_ACTIVE_LOW)                           
#define PCSC2_ACT_STATE     (PCS_ACTIVE_HIGH)             
#define PCSC3_ACT_STATE     (PCS_ACTIVE_HIGH)          
#define PCSC4_ACT_STATE     (PCS_ACTIVE_HIGH)          
#define PCSC5_ACT_STATE     (PCS_ACTIVE_HIGH)
#define PCSC6_ACT_STATE     (PCS_ACTIVE_HIGH)
#define PCSC7_ACT_STATE     (PCS_ACTIVE_HIGH)

#define PCSD0_ACT_STATE     (PCS_ACTIVE_HIGH)              
#define PCSD1_ACT_STATE     (PCS_ACTIVE_HIGH)              
#define PCSD2_ACT_STATE     (PCS_ACTIVE_HIGH)              
#define PCSD3_ACT_STATE     (PCS_ACTIVE_HIGH)              
#define PCSD4_ACT_STATE     (PCS_ACTIVE_HIGH)              
#define PCSD5_ACT_STATE     (PCS_ACTIVE_HIGH)
#define PCSD6_ACT_STATE     (PCS_ACTIVE_HIGH)
#define PCSD7_ACT_STATE     (PCS_ACTIVE_HIGH)

#define PCSE0_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSE1_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSE2_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSE3_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSE4_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSE5_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSE6_ACT_STATE     (PCS_ACTIVE_LOW)
#define PCSE7_ACT_STATE     (PCS_ACTIVE_LOW)


#define PCSA0_CONT_EN     0U   /*Continuous PCS Enable/Disable */          
#define PCSA1_CONT_EN     0U   /*Continuous PCS Enable/Disable */       
#define PCSA2_CONT_EN     0U   /*Continuous PCS Enable/Disable */                    
#define PCSA3_CONT_EN     0U   /*Continuous PCS Enable/Disable */                    
#define PCSA4_CONT_EN     0U   /*Continuous PCS Enable/Disable */                       
#define PCSA5_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSA6_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSA7_CONT_EN     0U   /*Continuous PCS Enable/Disable */

#define PCSB0_CONT_EN     0U   /*Continuous PCS Enable/Disable */          
#define PCSB1_CONT_EN     0U   /*Continuous PCS Enable/Disable */                      
#define PCSB2_CONT_EN     0U   /*Continuous PCS Enable/Disable */                      
#define PCSB3_CONT_EN     0U   /*Continuous PCS Enable/Disable */                                    
#define PCSB4_CONT_EN     0U   /*Continuous PCS Enable/Disable */         
#define PCSB5_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSB6_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSB7_CONT_EN     0U   /*Continuous PCS Enable/Disable */

#define PCSC0_CONT_EN     0U   /*Continuous PCS Enable/Disable */          
#define PCSC1_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSC2_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSC3_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSC4_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSC5_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSC6_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSC7_CONT_EN     0U   /*Continuous PCS Enable/Disable */

#define PCSD0_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSD1_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSD2_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSD3_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSD4_CONT_EN     0U   /*Continuous PCS Enable/Disable */           
#define PCSD5_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSD6_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSD7_CONT_EN     0U   /*Continuous PCS Enable/Disable */

#define PCSE0_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSE1_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSE2_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSE3_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSE4_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSE5_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSE6_CONT_EN     0U   /*Continuous PCS Enable/Disable */
#define PCSE7_CONT_EN     0U   /*Continuous PCS Enable/Disable */


#if PCSA0_ENABLE
/* CS0 */
#define      SPI_CH_A0_DBR          (DBR_DISABLED)
#define      SPI_CH_A0_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */
#define      SPI_CH_A0_BR           (SPI_BR_500K)
#define      SPI_CH_A0_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_A0_CPOL         (SPI_CPOL_LOW)
#define      SPI_CH_A0_CPHA         (SPI_CPHA_HIGH)  
#define      SPI_CH_A0_CSSCK        (CTAR_SCALER_4)   /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/
#define      SPI_CH_A0_DT           (CTAR_SCALER_2)   /* Delay After Transfer SCALER */
#define      SPI_CH_A0_ASC          (CTAR_SCALER_2)   /* After  SCK delay     SCALER */
#define      SPI_CH_A0_PCSSCK       (CTAR_PRESCALER_7)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_A0_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_A0_PASC         (CTAR_PRESCALER_3)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_A0_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_A0_TIMEOUT      100u //(SPI_CH_A0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSA1_ENABLE
/* CS1 */
#define      SPI_CH_A1_DBR          (DBR_VAL_500K)
#define      SPI_CH_A1_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_A1_BR           (SPI_BR_500K)
#define      SPI_CH_A1_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_A1_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_A1_CPHA         (SPI_CPHA_HIGH)  
#define      SPI_CH_A1_CSSCK        (CTAR_SCALER_128)   /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                               
#define      SPI_CH_A1_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_A1_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_A1_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_A1_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_A1_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_A1_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_A1_TIMEOUT      100u //(SPI_CH_A0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSA2_ENABLE
/* CS2 */
#define      SPI_CH_A2_DBR          (DBR_VAL_500K)
#define      SPI_CH_A2_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_A2_BR           (SPI_BR_500K)
#define      SPI_CH_A2_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_A2_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_A2_CPHA         (SPI_CPHA_HIGH)  
#define      SPI_CH_A2_CSSCK        (CTAR_SCALER_128)   /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                               
#define      SPI_CH_A2_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_A2_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_A2_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_A2_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_A2_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_A2_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_A2_TIMEOUT      100u //(SPI_CH_A0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSA3_ENABLE 
/* CS3 */
#define      SPI_CH_A3_DBR          (DBR_VAL_500K)
#define      SPI_CH_A3_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_A3_BR           (SPI_BR_500K)
#define      SPI_CH_A3_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_A3_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_A3_CPHA         (SPI_CPHA_HIGH)  
#define      SPI_CH_A3_CSSCK        (CTAR_SCALER_128)   /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                               
#define      SPI_CH_A3_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_A3_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_A3_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_A3_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_A3_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_A3_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_A3_TIMEOUT      100u //(SPI_CH_A0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSA4_ENABLE 
/* CS4 */
#define      SPI_CH_A4_DBR          (DBR_VAL_500K)
#define      SPI_CH_A4_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_A4_BR           (SPI_BR_500K)
#define      SPI_CH_A4_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_A4_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_A4_CPHA         (SPI_CPHA_HIGH)  
#define      SPI_CH_A4_CSSCK        (CTAR_SCALER_128)   /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                               
#define      SPI_CH_A4_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_A4_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_A4_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_A4_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_A4_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_A4_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_A4_TIMEOUT      100u //(SPI_CH_A0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSA5_ENABLE 
/* CS5 */
#define      SPI_CH_A5_DBR          (DBR_VAL_500K)
#define      SPI_CH_A5_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_A5_BR           (SPI_BR_500K)
#define      SPI_CH_A5_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_A5_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_A5_CPHA         (SPI_CPHA_HIGH)  
#define      SPI_CH_A5_CSSCK        (CTAR_SCALER_128)   /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                               
#define      SPI_CH_A5_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_A5_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_A5_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_A5_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_A5_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_A5_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_A5_TIMEOUT      100u //(SPI_CH_A0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif


#if PCSB0_ENABLE
/* CS0 */
#define      SPI_CH_B0_DBR          (DBR_DISABLED)
#define      SPI_CH_B0_BRP          (SPI_BRP_1M)                   /*Channel Baud Rate */
#define      SPI_CH_B0_BR           (SPI_BR_1M)                    /*Channel Baud Rate */
#define      SPI_CH_B0_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_B0_CPOL         (SPI_CPOL_LOW)                 /* clock active high */
#define      SPI_CH_B0_CPHA         (SPI_CPHA_HIGH) 
#define      SPI_CH_B0_CSSCK        (CTAR_SCALER_4)    /* PCS to SCK delay     SCALER */
#define      SPI_CH_B0_DT           (CTAR_SCALER_2)   /* Delay After Transfer SCALER */
#define      SPI_CH_B0_ASC          (CTAR_SCALER_2)    /* After  SCK delay     SCALER */
#define      SPI_CH_B0_PCSSCK       (CTAR_PRESCALER_7)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_B0_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_B0_PASC         (CTAR_PRESCALER_3)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_B0_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_B0_TIMEOUT      100u //(SPI_CH_B0_FMSZ + 1)*13/10 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif


#if PCSB1_ENABLE
/* CS1 */
#define      SPI_CH_B1_DBR          (DBR_VAL_1M)
#define      SPI_CH_B1_BRP          (SPI_BRP_1M)                   /*Channel Baud Rate */
#define      SPI_CH_B1_BR           (SPI_BR_1M)                    /*Channel Baud Rate */
#define      SPI_CH_B1_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_B1_CPOL         (SPI_CPOL_HIGH)                 /* clock active high */
#define      SPI_CH_B1_CPHA         (SPI_CPHA_HIGH) 
#define      SPI_CH_B1_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                              
#define      SPI_CH_B1_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */
#define      SPI_CH_B1_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_B1_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_B1_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_B1_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_B1_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_B1_TIMEOUT      100u //(SPI_CH_B0_FMSZ + 1)*13/10 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSB2_ENABLE
/* CS2 */
#define      SPI_CH_B2_DBR          (DBR_VAL_1M)
#define      SPI_CH_B2_BRP          (SPI_BRP_1M)                   /*Channel Baud Rate */
#define      SPI_CH_B2_BR           (SPI_BR_1M)                    /*Channel Baud Rate */
#define      SPI_CH_B2_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_B2_CPOL         (SPI_CPOL_HIGH)                 /* clock active high */
#define      SPI_CH_B2_CPHA         (SPI_CPHA_HIGH) 
#define      SPI_CH_B2_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                              
#define      SPI_CH_B2_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */
#define      SPI_CH_B2_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_B2_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_B2_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_B2_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_B2_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_B2_TIMEOUT      100u //(SPI_CH_B0_FMSZ + 1)*13/10 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSB3_ENABLE
/* CS3 */
#define      SPI_CH_B3_DBR          (DBR_VAL_1M)
#define      SPI_CH_B3_BRP          (SPI_BRP_1M)                   /*Channel Baud Rate */
#define      SPI_CH_B3_BR           (SPI_BR_1M)                    /*Channel Baud Rate */
#define      SPI_CH_B3_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_B3_CPOL         (SPI_CPOL_HIGH)                 /* clock active high */
#define      SPI_CH_B3_CPHA         (SPI_CPHA_HIGH) 
#define      SPI_CH_B3_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                              
#define      SPI_CH_B3_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */
#define      SPI_CH_B3_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_B3_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_B3_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_B3_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_B3_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_B3_TIMEOUT      100u //(SPI_CH_B0_FMSZ + 1)*13/10 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSB4_ENABLE 
/* CS4 */
#define      SPI_CH_B4_DBR          (DBR_VAL_1M)
#define      SPI_CH_B4_BRP          (SPI_BRP_1M)                   /*Channel Baud Rate */
#define      SPI_CH_B4_BR           (SPI_BR_1M)                    /*Channel Baud Rate */
#define      SPI_CH_B4_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_B4_CPOL         (SPI_CPOL_HIGH)                 /* clock active high */
#define      SPI_CH_B4_CPHA         (SPI_CPHA_HIGH) 
#define      SPI_CH_B4_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                              
#define      SPI_CH_B4_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */
#define      SPI_CH_B4_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_B4_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_B4_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_B4_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_B4_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_B4_TIMEOUT      100u //(SPI_CH_B0_FMSZ + 1)*13/10 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSB5_ENABLE
/* CS5 */
#define      SPI_CH_B5_DBR          (DBR_VAL_1M)
#define      SPI_CH_B5_BRP          (SPI_BRP_1M)                   /*Channel Baud Rate */
#define      SPI_CH_B5_BR           (SPI_BR_1M)                    /*Channel Baud Rate */
#define      SPI_CH_B5_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_B5_CPOL         (SPI_CPOL_HIGH)                 /* clock active high */
#define      SPI_CH_B5_CPHA         (SPI_CPHA_HIGH) 
#define      SPI_CH_B5_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                              
#define      SPI_CH_B5_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */
#define      SPI_CH_B5_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_B5_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_B5_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_B5_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_B5_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_B5_TIMEOUT      100u //(SPI_CH_B0_FMSZ + 1)*13/10 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif


#if PCSC0_ENABLE
/* CS0 */
#define      SPI_CH_C0_DBR          (DBR_VAL_2M)
#define      SPI_CH_C0_BRP          (SPI_BRP_2M)                  /*Channel Baud Rate */ 
#define      SPI_CH_C0_BR           (SPI_BR_2M)                    /*Channel Baud Rate */ 
#define      SPI_CH_C0_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_C0_CPOL         (SPI_CPOL_HIGH)                /* clock active high */
#define      SPI_CH_C0_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_C0_CSSCK        (CTAR_SCALER_32)    /* PCS to SCK delay     SCALER */                               
#define      SPI_CH_C0_DT           (CTAR_SCALER_32)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_C0_ASC          (CTAR_SCALER_32)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_C0_PCSSCK       (CTAR_PRESCALER_1) /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_C0_PDT          (CTAR_PRESCALER_1) /* Delay After Transfer PRESCALER */ 
#define      SPI_CH_C0_PASC         (CTAR_PRESCALER_1) /* After  SCK delay     PRESCALER */ 
#define      SPI_CH_C0_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_C0_TIMEOUT      50u //(SPI_CH_C0_FMSZ + 1)*13/20 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif


#if PCSC1_ENABLE
/* CS1 */
#define      SPI_CH_C1_DBR          (DBR_VAL_2M)
#define      SPI_CH_C1_BRP          (SPI_BRP_2M)                  /*Channel Baud Rate */ 
#define      SPI_CH_C1_BR           (SPI_BR_2M)                    /*Channel Baud Rate */ 
#define      SPI_CH_C1_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_C1_CPOL         (SPI_CPOL_HIGH)                /* clock active high */
#define      SPI_CH_C1_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_C1_CSSCK        (CTAR_SCALER_32)    /* PCS to SCK delay     SCALER */                               
#define      SPI_CH_C1_DT           (CTAR_SCALER_32)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_C1_ASC          (CTAR_SCALER_32)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_C1_PCSSCK       (CTAR_PRESCALER_1) /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_C1_PDT          (CTAR_PRESCALER_1) /* Delay After Transfer PRESCALER */ 
#define      SPI_CH_C1_PASC         (CTAR_PRESCALER_1) /* After  SCK delay     PRESCALER */ 
#define      SPI_CH_C1_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_C1_TIMEOUT      50u //(SPI_CH_C0_FMSZ + 1)*13/20 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSC2_ENABLE
/* CS2 */
#define      SPI_CH_C2_DBR          (DBR_VAL_1M)
#define      SPI_CH_C2_BRP          (SPI_BRP_1M)                  /*Channel Baud Rate */ 
#define      SPI_CH_C2_BR           (SPI_BR_1M)                    /*Channel Baud Rate */ 
#define      SPI_CH_C2_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_C2_CPOL         (SPI_CPOL_HIGH)                /* clock active high */
#define      SPI_CH_C2_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_C2_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                               
#define      SPI_CH_C2_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_C2_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_C2_PCSSCK       (CTAR_PRESCALER_1) /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_C2_PDT          (CTAR_PRESCALER_1) /* Delay After Transfer PRESCALER */ 
#define      SPI_CH_C2_PASC         (CTAR_PRESCALER_1) /* After  SCK delay     PRESCALER */ 
#define      SPI_CH_C2_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_C2_TIMEOUT      100u //(SPI_CH_C0_FMSZ + 1)*13/20 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSC3_ENABLE
/* CS3 */
#define      SPI_CH_C3_DBR          (DBR_VAL_1M)
#define      SPI_CH_C3_BRP          (SPI_BRP_1M)                  /*Channel Baud Rate */ 
#define      SPI_CH_C3_BR           (SPI_BR_1M)                    /*Channel Baud Rate */ 
#define      SPI_CH_C3_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_C3_CPOL         (SPI_CPOL_HIGH)                /* clock active high */
#define      SPI_CH_C3_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_C3_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                               
#define      SPI_CH_C3_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_C3_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_C3_PCSSCK       (CTAR_PRESCALER_1) /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_C3_PDT          (CTAR_PRESCALER_1) /* Delay After Transfer PRESCALER */ 
#define      SPI_CH_C3_PASC         (CTAR_PRESCALER_1) /* After  SCK delay     PRESCALER */ 
#define      SPI_CH_C3_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_C3_TIMEOUT      100u //(SPI_CH_C0_FMSZ + 1)*13/20 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSC4_ENABLE
/* CS4 */
#define      SPI_CH_C4_DBR          (DBR_VAL_1M)
#define      SPI_CH_C4_BRP          (SPI_BRP_1M)                  /*Channel Baud Rate */ 
#define      SPI_CH_C4_BR           (SPI_BR_1M)                    /*Channel Baud Rate */ 
#define      SPI_CH_C4_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_C4_CPOL         (SPI_CPOL_HIGH)                /* clock active high */
#define      SPI_CH_C4_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_C4_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                               
#define      SPI_CH_C4_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_C4_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_C4_PCSSCK       (CTAR_PRESCALER_1) /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_C4_PDT          (CTAR_PRESCALER_1) /* Delay After Transfer PRESCALER */ 
#define      SPI_CH_C4_PASC         (CTAR_PRESCALER_1) /* After  SCK delay     PRESCALER */ 
#define      SPI_CH_C4_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_C4_TIMEOUT      100u //(SPI_CH_C0_FMSZ + 1)*13/20 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSC5_ENABLE
/* CS5 */
#define      SPI_CH_C5_DBR          (DBR_VAL_1M)
#define      SPI_CH_C5_BRP          (SPI_BRP_1M)                  /*Channel Baud Rate */ 
#define      SPI_CH_C5_BR           (SPI_BR_1M)                    /*Channel Baud Rate */ 
#define      SPI_CH_C5_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_C5_CPOL         (SPI_CPOL_HIGH)                /* clock active high */
#define      SPI_CH_C5_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_C5_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER */                               
#define      SPI_CH_C5_DT           (CTAR_SCALER_128)   /* Delay After Transfer SCALER */ 
#define      SPI_CH_C5_ASC          (CTAR_SCALER_128)   /* After  SCK delay     SCALER */ 
#define      SPI_CH_C5_PCSSCK       (CTAR_PRESCALER_1) /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_C5_PDT          (CTAR_PRESCALER_1) /* Delay After Transfer PRESCALER */ 
#define      SPI_CH_C5_PASC         (CTAR_PRESCALER_1) /* After  SCK delay     PRESCALER */ 
#define      SPI_CH_C5_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_C5_TIMEOUT      100u //(SPI_CH_C0_FMSZ + 1)*13/20 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif


#if PCSD0_ENABLE
/* CS0 */
#define      SPI_CH_D0_DBR          (DBR_VAL_500K)
#define      SPI_CH_D0_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_D0_BR           (SPI_BR_500K)
#define      SPI_CH_D0_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_D0_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_D0_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_D0_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D0_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */  
#define      SPI_CH_D0_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */  
#define      SPI_CH_D0_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_D0_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_D0_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_D0_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_D0_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSD1_ENABLE
/* CS1 */
#define      SPI_CH_D1_DBR          (DBR_VAL_500K)
#define      SPI_CH_D1_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_D1_BR           (SPI_BR_500K)
#define      SPI_CH_D1_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_D1_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_D1_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_D1_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D1_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */  
#define      SPI_CH_D1_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */  
#define      SPI_CH_D1_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_D1_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_D1_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_D1_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_D1_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSD2_ENABLE
/* CS2 */
#define      SPI_CH_D2_DBR          (DBR_VAL_500K)
#define      SPI_CH_D2_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_D2_BR           (SPI_BR_500K)
#define      SPI_CH_D2_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_D2_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_D2_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_D2_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D2_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */  
#define      SPI_CH_D2_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */  
#define      SPI_CH_D2_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_D2_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_D2_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_D2_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_D2_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSD3_ENABLE
/* CS3 */
#define      SPI_CH_D3_DBR          (DBR_VAL_500K)
#define      SPI_CH_D3_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_D3_BR           (SPI_BR_500K)
#define      SPI_CH_D3_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_D3_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_D3_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_D3_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D3_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */  
#define      SPI_CH_D3_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */  
#define      SPI_CH_D3_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_D3_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_D3_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_D3_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_D3_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSD4_ENABLE
/* CS4 */
#define      SPI_CH_D4_DBR          (DBR_VAL_500K)
#define      SPI_CH_D4_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_D4_BR           (SPI_BR_500K)
#define      SPI_CH_D4_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_D4_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_D4_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_D4_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D4_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */  
#define      SPI_CH_D4_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */  
#define      SPI_CH_D4_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_D4_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_D4_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_D4_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_D4_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSD5_ENABLE
/* CS5 */
#define      SPI_CH_D5_DBR          (DBR_VAL_500K)
#define      SPI_CH_D5_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */ 
#define      SPI_CH_D5_BR           (SPI_BR_500K)
#define      SPI_CH_D5_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_D5_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_D5_CPHA         (SPI_CPHA_LOW)  
#define      SPI_CH_D5_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/                          
#define      SPI_CH_D5_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */  
#define      SPI_CH_D5_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */  
#define      SPI_CH_D5_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_D5_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_D5_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_D5_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_D5_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSE0_ENABLE
/* CS0 */
#define      SPI_CH_E0_DBR          (DBR_DISABLED)
#define      SPI_CH_E0_BRP          (SPI_BRP_1M)                   /*Channel Baud Rate */
#define      SPI_CH_E0_BR           (SPI_BR_1M)
#define      SPI_CH_E0_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_E0_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_E0_CPHA         (SPI_CPHA_HIGH)
#define      SPI_CH_E0_CSSCK        (CTAR_SCALER_16)   /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/
#define      SPI_CH_E0_DT           (CTAR_SCALER_64)   /* Delay After Transfer SCALER */
#define      SPI_CH_E0_ASC          (CTAR_SCALER_16)   /* After  SCK delay     SCALER */
#define      SPI_CH_E0_PCSSCK       (CTAR_PRESCALER_5)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_E0_PDT          (CTAR_PRESCALER_3)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_E0_PASC         (CTAR_PRESCALER_5)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_E0_LSBFE        (SPI_LSB_FIRST)
#define      SPI_CH_E0_TIMEOUT      100u //(SPI_CH_A0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSE1_ENABLE
/* CS1 */
#define      SPI_CH_E1_DBR          (DBR_VAL_500K)
#define      SPI_CH_E1_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */
#define      SPI_CH_E1_BR           (SPI_BR_500K)
#define      SPI_CH_E1_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_E1_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_E1_CPHA         (SPI_CPHA_LOW)
#define      SPI_CH_E1_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/
#define      SPI_CH_E1_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */
#define      SPI_CH_E1_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_E1_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_E1_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_E1_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_E1_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_E1_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSE2_ENABLE
/* CS2 */
#define      SPI_CH_E2_DBR          (DBR_VAL_500K)
#define      SPI_CH_E2_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */
#define      SPI_CH_E2_BR           (SPI_BR_500K)
#define      SPI_CH_E2_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_E2_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_E2_CPHA         (SPI_CPHA_LOW)
#define      SPI_CH_E2_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/
#define      SPI_CH_E2_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */
#define      SPI_CH_E2_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_E2_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_E2_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_E2_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_E2_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_E2_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSE3_ENABLE
/* CS3 */
#define      SPI_CH_E3_DBR          (DBR_VAL_500K)
#define      SPI_CH_E3_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */
#define      SPI_CH_E3_BR           (SPI_BR_500K)
#define      SPI_CH_E3_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_E3_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_E3_CPHA         (SPI_CPHA_LOW)
#define      SPI_CH_E3_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/
#define      SPI_CH_E3_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */
#define      SPI_CH_E3_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_E3_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_E3_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_E3_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_E3_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_E3_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSE4_ENABLE
/* CS4 */
#define      SPI_CH_E4_DBR          (DBR_VAL_500K)
#define      SPI_CH_E4_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */
#define      SPI_CH_E4_BR           (SPI_BR_500K)
#define      SPI_CH_E4_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_E4_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_E4_CPHA         (SPI_CPHA_LOW)
#define      SPI_CH_E4_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/
#define      SPI_CH_E4_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */
#define      SPI_CH_E4_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_E4_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_E4_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_E4_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_E4_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_E4_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if PCSE5_ENABLE
/* CS5 */
#define      SPI_CH_E5_DBR          (DBR_VAL_500K)
#define      SPI_CH_E5_BRP          (SPI_BRP_500K)                   /*Channel Baud Rate */
#define      SPI_CH_E5_BR           (SPI_BR_500K)
#define      SPI_CH_E5_FMSZ         (FRAMESIZE_16)
#define      SPI_CH_E5_CPOL         (SPI_CPOL_HIGH)
#define      SPI_CH_E5_CPHA         (SPI_CPHA_LOW)
#define      SPI_CH_E5_CSSCK        (CTAR_SCALER_128)    /* PCS to SCK delay     SCALER  Note: in this way the delay is: 1/FCLK*(2^(SPI_CTAR_CSSCK + 1)); es: delay = 1/80*(2^(4+1))*1e-6 s = 400 ns;*/
#define      SPI_CH_E5_DT           (CTAR_SCALER_128)    /* Delay After Transfer SCALER */
#define      SPI_CH_E5_ASC          (CTAR_SCALER_128)    /* After  SCK delay     SCALER */
#define      SPI_CH_E5_PCSSCK       (CTAR_PRESCALER_1)  /* PCS to SCK delay     PRESCALER */
#define      SPI_CH_E5_PDT          (CTAR_PRESCALER_1)  /* Delay After Transfer PRESCALER */
#define      SPI_CH_E5_PASC         (CTAR_PRESCALER_1)  /* After  SCK delay     PRESCALER */
#define      SPI_CH_E5_LSBFE        (SPI_MSB_FIRST)
#define      SPI_CH_E5_TIMEOUT      100u //(SPI_CH_D0_FMSZ + 1)*13/5 /* FrameSize * (10^6 + TOLERANCE%)/SPI_BaudRate = us/frame */
#endif

#if SPI_CH_A_EN
#define SPI_A_CS_NUM    6u
#define SPI_A_FUNC_INT  0u  // set to 1 to handle direct function call
#define SPI_A_FUNC_EXC  0u  //set to 1 to handle task execution
#endif
#if SPI_CH_B_EN
#define SPI_B_CS_NUM    6u
#define SPI_B_FUNC_INT  0u  // set to 1 to handle direct function call
#define SPI_B_FUNC_EXC  0u  //set to 1 to handle task execution
#endif
#if SPI_CH_C_EN
#define SPI_C_CS_NUM    6u
#define SPI_C_FUNC_INT  1u  // set to 1 to handle direct function call
#define SPI_C_FUNC_EXC  1u  //set to 1 to handle task execution
#endif
#if SPI_CH_D_EN
#define SPI_D_CS_NUM    6u
#define SPI_D_FUNC_INT  0u  // set to 1 to handle direct function call
#define SPI_D_FUNC_EXC  0u  //set to 1 to handle task execution
#endif
#if SPI_CH_E_EN
#define SPI_E_CS_NUM    6u
#define SPI_E_FUNC_INT  0u  // set to 1 to handle direct function call
#define SPI_E_FUNC_EXC  0u  //set to 1 to handle task execution
#endif

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

#pragma ghs endnomisra

#endif /* _DSPI_CFG_H_ */

/****************************************************************************
 ****************************************************************************/

