/****************************************************************************
*
* Copyright © 2018-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tom_cfg.c
 * @brief   (GTM-IP) TOM Driver configuration code.
 *
 * @addtogroup TOM
 * @{
 */
#include "gtm_cfg.h"

#if (SPC5_GTM_USE_TOM == TRUE) || defined(__DOXYGEN__)

#include "gtm_tom_cfg.h"

/*===========================================================================*/
/* Driver local definitions.                                                 */
/*===========================================================================*/

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/* ---- TOM 0 Callbacks        ---- */
GTM_TOM_Channel_Callbacks *gtm_tom0_callbacks[SPC5_GTM_TOM_CHANNELS] = {
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL,
	NULL
};
/* ---- ---------------------- ---- */

/* ---- TOM 1 Callbacks        ---- */
/* ---- TOM 2 Callbacks        ---- */
/* ---- TOM 3 Callbacks        ---- */
/*===========================================================================*/
/* Driver local types.                                                       */
/*===========================================================================*/

/*===========================================================================*/
/* Driver local variables.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Driver local functions.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/

#endif /* SPC5_GTM_USE_ATOM */

/** @} */
