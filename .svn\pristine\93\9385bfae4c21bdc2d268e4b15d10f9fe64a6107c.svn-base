/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  ADC
**  Filename        :  Adc_events.c
**  Created on      :  09-giu-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef _BUILD_ADC_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "typedefs.h"
#include "Adc.h"
#include "sys.h"
#include "Digio_out.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

uint16_T cntErrorSarISR = 0U;

/* SAR channels */
extern uint16_T SAR0_CHANNELS[SAR0_ALL_CHANNELS];
extern uint16_T SAR2_CHANNELS[SAR2_ALL_CHANNELS];
extern uint16_T SAR4_CHANNELS[SAR4_ALL_CHANNELS];
extern uint16_T SAR6_CHANNELS[SAR6_ALL_CHANNELS];
extern uint16_T SARSV_CHANNELS[SARSV_ALL_CHANNELS];

uint16_T dataSd0RVal[SD0_FIFO_FULL_THRESHOLD+1U];
uint16_T dataSd3RVal[SD3_FIFO_FULL_THRESHOLD+1U];

#ifdef SD_TEST_CALCULATED_VALUE
float_T voltValueSd0;
float_T voltValueSd3;
#endif /* SD_TEST_CALCULATED_VALUE */

#ifdef SAR_TEST_CALCULATED_VALUE
float_T calculatedValueSar0[SAR0_NUM_CHANNELS];
float_T calculatedValueSar2[SAR2_NUM_CHANNELS];
float_T calculatedValueSar4[SAR4_NUM_CHANNELS];
float_T calculatedValueSar6[SAR6_NUM_CHANNELS];
float_T calculatedValueSarSV[SARSV_NUM_CHANNELS];
#endif /* SAR_TEST_CALCULATED_VALUE */
/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : SD0_ISR
**
**   Description:
**    ISR handler for SD0.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SD0_ISR(void) {

    uint8_T SD0_AcqIndex = 0u;

    /* check fifo is not empty */
    if (SDADC_0.SFR.B.DFEF == 0U)
    {

        /* callback*/

        /* stop conversion by powering down ADC module */
        SDADC_0.MCR.B.EN = 0U;

        for(SD0_AcqIndex = 0u; SD0_AcqIndex <= SD0_FIFO_FULL_THRESHOLD; SD0_AcqIndex++)
        {
            /* read converted data */
            dataSd0RVal[SD0_AcqIndex] = (uint16_T)(SDADC_0.CDR.R);
        }

        /* Flush FIFO */
        SDADC_0.FCR.B.FRST = 1U;

        /* Reset Data FIFO overrun flag*/
        SDADC_0.SFR.B.DFORF = 1U;

        /*Reset Data FIFO Full Flag*/
        SDADC_0.SFR.B.DFFF = 1U;

#ifdef SD_TEST_CALCULATED_VALUE
         /* apply calibration */
         voltValueSd0 = SDADC_TestApplyCalib(SD0, dataSd0RVal[SD0_FIFO_FULL_THRESHOLD]);
#endif /* SD_TEST_CALCULATED_VALUE */

    }
}

/******************************************************************************
**   Function    : SD3_ISR
**
**   Description:
**    ISR handler for SD3.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SD3_ISR(void) {

    uint8_T SD3_AcqIndex = 0u;

    /* check fifo is not empty */
    if (SDADC_3.SFR.B.DFEF == 0U)
    {

        /* callback*/

        /* stop conversion by powering down ADC module */
        SDADC_3.MCR.B.EN = 0U;

        for(SD3_AcqIndex = 0u; SD3_AcqIndex <= SD3_FIFO_FULL_THRESHOLD; SD3_AcqIndex++)
        {
            /* read converted data */
            dataSd3RVal[SD3_AcqIndex] = (uint16_T)(SDADC_3.CDR.R);
        }

        /* Flush FIFO */
        SDADC_3.FCR.B.FRST = 1U;

        /* Reset Data FIFO overrun flag*/
        SDADC_3.SFR.B.DFORF = 1U;

        /*Reset Data FIFO Full Flag*/
        SDADC_3.SFR.B.DFFF = 1U;

#ifdef SD_TEST_CALCULATED_VALUE
         /* apply calibration */
         voltValueSd3 = SDADC_TestApplyCalib(SD3, dataSd3RVal[SD3_FIFO_FULL_THRESHOLD]);
#endif /* SD_TEST_CALCULATED_VALUE */

    }
}

/******************************************************************************
**   Function    : SAR0_ISR
**
**   Description:
**    ISR handler for SARADC_0.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SAR0_ISR(void) 
{
    uint16_T data[SAR0_NUM_CHANNELS];
    uint16_T anCh;
    uint8_T i; 

    for (i = 0U; i < SAR0_NUM_CHANNELS; i++) 
    {
            anCh = SAR0_CHANNELS[i];
    
            if (anCh < SAR_INT_CHANNEL_NMB)
            {
                if (SARADC_0.ICDR[anCh].B.VALID == 1U)
                {
                    data[i] = (uint16_T)SARADC_0.ICDR[anCh].B.CDATA;

#ifdef SAR_TEST_CALCULATED_VALUE
                    calculatedValueSar0[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif 
                } 
                else 
                {
                    cntErrorSarISR++;
                }
            }
            else
            {
                if (SARADC_0.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.VALID == 1U)
                {
                    data[i] = (uint16_T)SARADC_0.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.CDATA;
#ifdef SAR_TEST_CALCULATED_VALUE
                    calculatedValueSar0[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
                } 
                else 
                {
                    cntErrorSarISR++;
            }
        }
    }

    /* Clear interrupt flags */
    SARADC_0.ISR.R = 0xFU;
}
/******************************************************************************
**   Function    : SAR2_ISR
**
**   Description:
**    ISR handler for SARADC_2.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SAR2_ISR(void) 
{
    uint16_T data[SAR2_NUM_CHANNELS];
    uint16_T anCh;
    uint8_T i; 

    for (i = 0U; i < SAR2_NUM_CHANNELS; i++) 
    {
        anCh = SAR2_CHANNELS[i];

        if (anCh < SAR_INT_CHANNEL_NMB)
        {
            if (SARADC_2.ICDR[anCh].B.VALID == 1U)
            {
                data[i] = (uint16_T)SARADC_2.ICDR[anCh].B.CDATA;
#ifdef SAR_TEST_CALCULATED_VALUE
                calculatedValueSar2[i]= ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
            } 
            else 
            {
                cntErrorSarISR++;
            }
        }
        else
        {
            if (SARADC_2.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.VALID == 1U)
            {
                data[i] = (uint16_T)SARADC_2.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.CDATA;
#ifdef SAR_TEST_CALCULATED_VALUE
                calculatedValueSar2[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
            } 
            else 
            {
                cntErrorSarISR++;
            }
        }
    }

    /* Clear interrupt flags */
    SARADC_2.ISR.R = 0xFU;
}

/******************************************************************************
**   Function    : SAR4_ISR
**
**   Description:
**    ISR handler for SARADC_4.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SAR4_ISR(void) 
{
    uint16_T data[SAR4_NUM_CHANNELS];
    uint16_T anCh;
    uint8_T i; 

    for (i = 0U; i < SAR4_NUM_CHANNELS; i++) 
    {
        anCh = SAR4_CHANNELS[i];

        if (anCh < SAR_INT_CHANNEL_NMB)
        {
            if (SARADC_4.ICDR[anCh].B.VALID == 1U)
            {
                data[i] = (uint16_T)SARADC_4.ICDR[anCh].B.CDATA;
#ifdef SAR_TEST_CALCULATED_VALUE
                calculatedValueSar4[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
            } 
            else 
            {
                cntErrorSarISR++;
            }
        }
        else
        {
            if (SARADC_4.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.VALID == 1U)
            {
                data[i] = (uint16_T)SARADC_4.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.CDATA;
#ifdef SAR_TEST_CALCULATED_VALUE
                calculatedValueSar4[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
            } 
            else 
            {
                cntErrorSarISR++;
            }
        }
    }

    /* Clear interrupt flags */
    SARADC_4.ISR.R = 0xFU;
}

/******************************************************************************
**   Function    : SAR6_ISR
**
**   Description:
**    ISR handler for SARADC_6.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SAR6_ISR(void) 
{
    uint16_T data[SAR6_NUM_CHANNELS];
    uint16_T anCh;
    uint8_T i; 

    for (i = 0U; i < SAR6_NUM_CHANNELS; i++) 
    {
        anCh = SAR6_CHANNELS[i];

        if (anCh < SAR_INT_CHANNEL_NMB)
        {
            if (SARADC_6.ICDR[anCh].B.VALID == 1U)
            {
                data[i] = (uint16_T)SARADC_6.ICDR[anCh].B.CDATA;

#ifdef SAR_TEST_CALCULATED_VALUE
                calculatedValueSar6[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
            } 
            else 
            {
                cntErrorSarISR++;
            }
        }
        else
        {
            if (SARADC_6.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.VALID == 1U)
            {
                data[i] = (uint16_T)SARADC_6.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.CDATA;

#ifdef SAR_TEST_CALCULATED_VALUE
                calculatedValueSar6[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
            } 
            else 
            {
                cntErrorSarISR++;
            }
        }
    }

    /* Clear interrupt flags */
    SARADC_6.ISR.R = 0xFU;
}

/******************************************************************************
**   Function    : SARSV_ISR
**
**   Description:
**    ISR handler for SAR Supervisor.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void SARSV_ISR(void) 
{
    uint16_T data[SARSV_NUM_CHANNELS];
    uint16_T anCh;
    uint8_T i; 

    for (i = 0U; i < SARSV_NUM_CHANNELS; i++) 
    {
        anCh = SARSV_CHANNELS[i];

        if (anCh < SAR_INT_CHANNEL_NMB)
        {
            if (SARADC_B.ICDR[anCh].B.VALID == 1U)
            {
                data[i] = (uint16_T)SARADC_B.ICDR[anCh].B.CDATA;

#ifdef SAR_TEST_CALCULATED_VALUE
                calculatedValueSarSV[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
            } 
            else 
            {
                cntErrorSarISR++;
            }
        }
        else
        {
            if (SARADC_B.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.VALID == 1U)
            {
                data[i] = (uint16_T)SARADC_B.TCDR[anCh - SAR_INT_CHANNEL_NMB].B.CDATA;

#ifdef SAR_TEST_CALCULATED_VALUE
                calculatedValueSarSV[i] = ((float_T)data[i] / (float_T)4096U)*(float_T)(SARADC_VREFP - SARADC_VREFN);
#endif
            } 
            else 
            {
                cntErrorSarISR++;
            }
        }
    }

    /* Clear interrupt flags */
    SARADC_B.ISR.R = 0xFU;
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/

#endif /* _BUILD_ADC_ */

/****************************************************************************
 ****************************************************************************/
