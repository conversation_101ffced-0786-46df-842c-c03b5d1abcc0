===================
== EE SIZE [hex] ==
===================
ID0           0x414
ID1           0x05c
ID2           0x09c
ID3           0x2c8
ID4           0x011
ID5           0x011
ID6           0x0a8
ID7           0x036
ID8           0x09f
ID9           0x12b8
ID10          0x000
ID11          0x000
=======================================
==== ID0 Vars & Relative pos [hex] ====
=======================================
EEDummyID0_00                     0x000
EEDummyID0_01                     0x004
EEDummyID0_02                     0x008
EEDummyID0_03                     0x00c
CntSyncError                      0x010
LastRpm                           0x012
LastLastSyncError                 0x014
LastNTeethDeleted                 0x018
TbKnockAdEE                       0x01c
CntKnockCohEE                     0x3dc
VtIPriCorrAdEE                    0x3e4
VtSparkPlugFactorEE               0x3f4
VtTSparkNomOffAdEE                0x404
=======================================
==== ID1 Vars & Relative pos [hex] ====
=======================================
EEDummyID1_00                     0x000
EEDummyID1_01                     0x004
EEDummyID1_02                     0x008
EEDummyID1_03                     0x00c
ECUcodeID1                        0x010
ProgrammingStatusEE               0x058
=======================================
==== ID2 Vars & Relative pos [hex] ====
=======================================
EEDummyID2_00                     0x000
EEDummyID2_01                     0x004
EEDummyID2_02                     0x008
EEDummyID2_03                     0x00c
ECUcodeID2                        0x010
ECUcodeSupData                    0x061
ECUcodeEldor                      0x08f
FlashCrcEE                        0x09a
=======================================
==== ID3 Vars & Relative pos [hex] ====
=======================================
EEDummyID3_00                     0x000
EEDummyID3_01                     0x004
EEPhysErrCnt                      0x008
EELogicErrCnt                     0x00c
StDiag                            0x010
DiagCntEE                         0x070
DTCStatusEE                       0x130
FailedOCCntEE                     0x190
DtcSymptomEE                      0x1f0
confirmedDTC_oldEE                0x250
EventCounterEE                    0x2b0
AbsCntWUC                         0x2bc
EE_BiosErr                        0x2c0
EESACmdInLevErrNoMax              0x2c4
EESACmdInLevErrSum                0x2c6
=======================================
==== ID4 Vars & Relative pos [hex] ====
=======================================
EEDummyID4_00                     0x000
EEDummyID4_01                     0x004
EEDummyID4_02                     0x008
EEDummyID4_03                     0x00c
EEEvPwrSelfWDT                    0x010
=======================================
==== ID5 Vars & Relative pos [hex] ====
=======================================
EEDummyID5_00                     0x000
EEDummyID5_01                     0x004
EEDummyID5_02                     0x008
EEDummyID5_03                     0x00c
FlgFirstPowerOn                   0x010
=======================================
==== ID6 Vars & Relative pos [hex] ====
=======================================
EEDummyID6_00                     0x000
EEDummyID6_01                     0x004
EEDummyID6_02                     0x008
EEDummyID6_03                     0x00c
EE_IvorCnt_c0                     0x010
EE_IvorIndex_c0                   0x030
EE_SRR0_Value_c0                  0x034
EE_SRR1_Value_c0                  0x038
EE_CSRR0_Value_c0                 0x03c
EE_CSRR1_Value_c0                 0x040
EE_SPR_ESRValue_c0                0x044
EE_SPR_DEARValue_c0               0x048
EE_SPR_MCSRValue_c0               0x04c
EE_SPR_MCARValue_c0               0x050
EE_MCSRR0_Value_c0                0x054
EE_MCSRR1_Value_c0                0x058
EE_IvorCnt_c2                     0x05c
EE_IvorIndex_c2                   0x07c
EE_SRR0_Value_c2                  0x080
EE_SRR1_Value_c2                  0x084
EE_CSRR0_Value_c2                 0x088
EE_CSRR1_Value_c2                 0x08c
EE_SPR_ESRValue_c2                0x090
EE_SPR_DEARValue_c2               0x094
EE_SPR_MCSRValue_c2               0x098
EE_SPR_MCARValue_c2               0x09c
EE_MCSRR0_Value_c2                0x0a0
EE_MCSRR1_Value_c2                0x0a4
=======================================
==== ID7 Vars & Relative pos [hex] ====
=======================================
EEDummyID7_00                     0x000
EEDummyID7_01                     0x004
EEDummyID7_02                     0x008
EEDummyID7_03                     0x00c
EEHTempECU                        0x010
EETempECUMax1                     0x012
EETempECUMax2                     0x014
EETempECUMax3                     0x016
EETimHTempECU                     0x018
EETimWTempECU                     0x01c
EEWTempECU                        0x020
EECntSbcOT                        0x022
EESbcOT                           0x023
EECntSbcSC                        0x024
EESbcSC                           0x025
EECntSbcCAN                       0x026
EESbcCAN                          0x027
EECntSbcUV                        0x028
EESbcUV                           0x029
EngTypeStored                     0x02a
EEDebug00                         0x02c
EEDebug01                         0x030
EECntSBCResend                    0x034
=======================================
==== ID8 Vars & Relative pos [hex] ====
=======================================
EEDummyID8_00                     0x000
EEDummyID8_01                     0x004
EEDummyID8_02                     0x008
EEDummyID8_03                     0x00c
FlgRonInheritEE                   0x010
FuelLevelEE                       0x011
FlgRonStoredEE                    0x012
OdomRonStartEE                    0x014
OdomRonStopEE                     0x018
SecRonStartEE                     0x01c
SecRonStopEE                      0x020
CntRonSuspEE                      0x024
CntRonSuspRunEE                   0x026
IDRonSuspRunEE                    0x028
RonLevelEE                        0x029
VtMKDwellIntEE                    0x02a
VtMKIntIonEE                      0x034
VtMKIonAbsTdcEE                   0x03e
VtMKKnockIntEE                    0x044
VtMKLoadEE                        0x058
VtMKRpmEE                         0x062
VtMKTotOdometerCANEE              0x06c
SparkPlugFaultCntEE               0x080
InfoGpfEE                         0x090
InfoGpfValidDataEE                0x092
SecondsWorkTime                   0x094
ECUTimeStampsEE                   0x098
ECUTimeStampsFromKeyOnEE          0x09c
EnRonDetectEE                     0x09e
=======================================
==== ID9 Vars & Relative pos [hex] ====
=======================================
EEDummyID9_00                     0x000
EEDummyID9_01                     0x004
EEDummyID9_02                     0x008
EEDummyID9_03                     0x00c
DTCSnapshotEE_1                   0x010
DTCSnapshotEE_2                   0x940
DTCExtendedEE                     0x1270
=======================================
==== ID10 Vars & Relative pos [hex] ===
=======================================
=======================================
==== ID11 Vars & Relative pos [hex] ===
=======================================
=======================================
