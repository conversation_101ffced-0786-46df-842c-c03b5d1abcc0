Path relativo dello script: \StrategyCreator

Possibili Comandi:
0)Crea Strategy
1)Import device description
2)Import memory immage
3)Import calibration data
4)Export memory immage
5)Export calibration data
6)execute script

Struttura import:
1)CAL_RAM
2)APP
3)CAL_ROM
4)GLOBAL
5)CAL_ROM_OFFLINE


Struttura export:
1)APP
2)CAL_RAM
3)CAL_ROM
4)CAL_ROM_OFFLINE
5)GLOBAL



Definizione Operazioni

%%% 1 - Crea Satrtegy %%%
Comando N°: 0
file: -
Region: - 

%%% 2 - Importa A2L %%%
Comando N°: 1
file: \GHS\bin\I1TB1D\I1TB1D.a2l
Region: - 

%%% 3 - Importa Binario Applicativo %%%
Comando N°: 2
file: \GHS\bin\I1TB1D\I1TB1D-appl.bin
Region: 6 

%%% 4 - Importa Binario calibrazione %%%
Comando N°: 2
file: \GHS\bin\I1TB1D\I1TB1D-calib.bin
Region: 6 

%%% 5 - Importa SRecord %%%
Comando N°: 2
file: \GHS\bin\I1TB1D\I1TB1D-appl.s37
Region: - 

%%% 6 - Importa Calibrazione %%%
Comando N°: 3
file: \CCP_Tool\Vision\EldorECU\cal4creastrategy.cal
Region: - 

%%% 7 - Esporta Calibrom offline %%%
Comando N°: 4
file: \GHS\bin\I1TB1D\ROM.bin
Region: 4

%%% 8 - Importa cal ram %%%
Comando N°: 2
file: \GHS\bin\I1TB1D\ROM.bin
Region: 1

%%% 9 - Esporta Calram %%%
Comando N°: 4
file: \CCP_Tool\Vision\EldorECU\calib_strategy_export\I1TB1D_CALRAM.bin
Region: 2

%%% 10 - esecuzione script %%%
Comando N°: 6
file: \KitCreate.bat
Region: \KWP2000\Dianalyzer\CAN\I1TB1D_EVB\I1TB1D_EVB.bin

%%% 11 - Importa Binario calibrazioni %%%
Comando N°: 2
file: \CCP_Tool\Vision\EldorECU\calibram.bin
Region: 3

Lista comandi:
Strategia calibrata Offline: 1,2,5,7,8,6,9,10,11
Strategia non calibrata: 1,2,5
