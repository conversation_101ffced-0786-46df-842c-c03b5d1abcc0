/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  IgnInCmd
**  Filename        :  IgnInCmd.c
**  Created on      :  31-mar-2021 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifdef _BUILD_IGNINCMD_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "ignincmd.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
int16_T  SAoutCyl[N_CYL_MAX];
/* Trigger in duration */
uint32_T EffDwellTrigTime[N_CYL_MAX];
/* Primary current peak */
//uint16_T VtILeadPeak[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
uint32_T CylPlaAbsOn_idx;
int16_T  SAout;
uint8_T FlgDSAoutDis;
int16_T  DSAoutCyl[N_CYL_MAX];

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
static uint32_T CylEdgeCbCnt[N_CYL_MAX][2];
static uint32_T AbsTimeRisEdge[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
static uint32_T AbsTimeFallEdge[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};

//static uint32_T DeltaTimePri[N_CYL_MAX];
static uint32_T VtDwellAngleOff[N_CYL_MAX];
static uint8_T CntDSAoutDis = 0u;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : IgnInCmd_Init
**
**   Description:
**    Reset the values of the module variables. The global variables (outputs) 
**    and the static variables (states) are reset to the default values by this function.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IgnInCmd_Init(void)
{
    uint8_T i;
    for(i = 0u; i< N_CYL_MAX; i++)
    {
        EffDwellTrigTime[i] = 0u;
        AbsTimeRisEdge[i] = 0u;
        AbsTimeFallEdge[i] = 0u;
        CylEdgeCbCnt[i][0] = 0u;
        CylEdgeCbCnt[i][1] = 0u;
        SAoutCyl[i] = 0;
        VtDwellAngleOff[i] = 0u;

#if 0
        
        VtILeadPeak[i] = 0u;
        DeltaTimePri[i] = 0u;
        
#endif
    }
    
    FlgDSAoutDis = 0u;
    CylPlaAbsOn_idx = 0u;
}

/******************************************************************************
**   Function    : IgnInCmd_RiseEdge
**
**   Description:
**    Rising edge Trigger In event
**
**   Parameters :
**    cyl:      cylinder index
**    riseEdgeTime: absolute rise time
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IgnInCmd_RiseEdge(uint8_T cyl, uint32_T riseEdgeTime)
{
    uint8_T adChannelIdx;

    CylEdgeCbCnt[cyl][1]++;
    
    /* time rising edge */
    AbsTimeRisEdge[cyl] = riseEdgeTime;
    if(AbsTimeRisEdge[cyl] > AbsTimeFallEdge[cyl])
    {
        EffDwellTrigTime[cyl] = AbsTimeRisEdge[cyl] - AbsTimeFallEdge[cyl];
    }
    else
    {
        EffDwellTrigTime[cyl] = AbsTimeRisEdge[cyl] + (MAX_uint24_T - AbsTimeFallEdge[cyl]);
    }
    MSparkCmd_BKEnLatch(cyl);
}

/******************************************************************************
**   Function    : IgnInCmd_FallingEdge
**
**   Description:
**    - Increments Trigger OFF edge counter
**    - Manage CntIGNInTot
**    - Call CoilTarget Closed Loop
**    - Call PlaCtrl
**    - Manage DIAG_COIL
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IgnInCmd_FallingEdge(uint8_T cyl, uint32_T fallEdgeTime)
{
    uint8_T buckIdx;

    CylEdgeCbCnt[cyl][0]++;
    
    /* time falling edge */
    AbsTimeFallEdge[cyl] = fallEdgeTime;
    CylPlaAbsOn_idx = cyl;
}

/******************************************************************************
**   Function    : IgnInCmd_SACalc
**
**   Description:
**    Rising edge Trigger In event
**
**   Parameters :
**    cyl:      cylinder index
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IgnInCmd_SparkOff(uint8_T cyl)
{
    int32_T  sa_tmp;
    int16_T thr_DSAoutCyl;
    uint16_T reset_counter;
    
    IgnInCmd_PhaseCheck(cyl, IgnAngle[cyl]);

    /* CONDIZIONE SU DSAoutCyl */
    /* Calcolo soglia per DSAoutCyl */
    LookUp_S8_U16(&(thr_DSAoutCyl), &(VTDSAOUTMAX[0]), Rpm, &(BKRPMDSA[0]), (BKRPMDSA_dim-1u));
    thr_DSAoutCyl = thr_DSAoutCyl/32; // scaled 2^-4

    /* Calcolo valore reset per contatore */
    LookUp_U8_U16(&(reset_counter), &(VTNCYCLEDIS[0]), Rpm, &(BKRPMDSA[0]), (BKRPMDSA_dim-1u));
    reset_counter = reset_counter>>8; // scaled 2^-4
    
    /* Spark advance calculation */
    if((FlgSyncPhased == 1u) && (CrankPhaseRqst == CRANK_PHASE_SYNC_EXECUTED))
    {
        VtDwellAngleOff[cyl] = IgnAngle[cyl];
        sa_tmp = ((int32_T)TDC_ANGLE[(uint8_T)(cyl<<1) + FlgBankSel] - (int32_T)IgnAngle[cyl]);
        sa_tmp = (sa_tmp*4)/25;
        /* Calculate SA correctly when TDC_ANGLE and crankangle are in different
           flywheel cycles */
        if (sa_tmp < SA_MIN)
        {
            //In caso di anticipo troppo negativo
            SAout = (int16_T)(sa_tmp + (int16_T)CYCLE_ANGLE);
        } 
        else if  (sa_tmp > (int16_T)SA_MAX)
        {
            //In caso di anticipo troppo positivo
            SAout = (int16_T)(sa_tmp - (int16_T)CYCLE_ANGLE);
        }
        else
        {
            SAout = (int16_T)sa_tmp;
        }

        DSAoutCyl[cyl] = SAout - SAoutCyl[cyl];

        /* Check SA calculation */
        if((SAout > SA_MIN) && (SAout < (int16_T)SA_MAX))
        {
            SAoutCyl[cyl] = (int16_T)SAout;
        }
        else
        {
            /* Reset SA value for visualization */
            SAoutCyl[cyl] = 0;

            /* Set DIAG_SYNC */
            SYNCMGM_DiagSync_SA();

            /* Restart DPLL */
            TDN_reinit_DPLL();
        }
        
        /* CALCOLO ABILITAZIONE */
        if (FlgDSAoutDis == 0u)
        {
            CntDSAoutDis = 0u;
            
            /* TEST SU DSAoutCyl */
            for (uint8_T i = 0u; i < N_CYLINDER; i++)
            {
                if (((FlgDCTShifting != 0u) || (abs(DSAoutCyl[i]) > thr_DSAoutCyl)) && (FlgDSAoutDis == 0u))
                {
                    FlgDSAoutDis = 1u;
                    CntDSAoutDis = (uint8_T)reset_counter;
                }
                else
                {
                    /* DO NOTHING */
                }
            }
        }
        else
        {
            CntDSAoutDis--;
            
            /* Reset contatore in caso di nuovo sfondamento soglia */
            for (uint8_T i = 0u; i < N_CYLINDER; i++)
            {
                if ((FlgDCTShifting != 0u) || (abs(DSAoutCyl[i]) > thr_DSAoutCyl))
                {
                    CntDSAoutDis = (uint8_T)reset_counter;
                }
                else
                {
                    /* DO NOTHING */
                }
            }
            
            if (CntDSAoutDis == 0u)
            {
                FlgDSAoutDis = 0u;
            }
            else
            {
                /* DO NOTHING */
            }
        }
    }
    else
    {
        SAoutCyl[cyl] = 0;
    }

    SAout = SAoutCyl[cyl];
}

/******************************************************************************
**   Function    : IgnInCmd_NoSync
**
**   Description:
**    - Reset IgnInCmd selected variables at NoSync
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IgnInCmd_NoSync(void)
{
    uint8_T i;
    
    FlgDSAoutDis = 0u;
    CntDSAoutDis = 0u;
    
    for (i = 0u; i < N_CYLINDER; i++){
        DSAoutCyl[i] = 0;
    }
}

/******************************************************************************
**   Function    : IgnInCmd_PhaseCheck
**
**   Description:
**    - Check if the system is phased
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void IgnInCmd_PhaseCheck(uint8_T cyl, uint32_T currentAngle)
{
    int16_T tdcBef, tdcAft;

    tdcBef = (int16_T)TDC_TOOTH[FlgBankSel][cyl];
    tdcBef = tdcBef - N_TOOTH_TOL_BEF;
    if(tdcBef < 0)
    {
        tdcBef = tdcBef + (int16_T)(N_TEETH * N_REV_PER_CYCLE);
    }

    tdcAft = (int16_T)TDC_TOOTH[FlgBankSel][cyl];
    tdcAft = tdcAft + N_TOOTH_TOL_AFT;
    if(tdcAft >= (int16_T)(N_TEETH * N_REV_PER_CYCLE))
    {
        tdcAft = tdcAft - (int16_T)(N_TEETH * N_REV_PER_CYCLE);
    }

    CrankPhaseCheck((uint32_T)tdcBef, (uint32_T)tdcAft, currentAngle);

}

#endif // _BUILD_MSPARKCMD_

