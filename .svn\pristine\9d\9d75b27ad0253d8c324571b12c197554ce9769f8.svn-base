import array
import os
import sys
import traceback
import tempfile
import unittest
import binascii
import intelhex
import shutil
from io import String<PERSON>
from subprocess import call
from intelhex import IntelHex
import binhex


def getConfData(conf):
    """getConfData read configuration data
        @return input_hex                   Name of hex file
        @return input_A2l                   Name of input_a2l file
        @return calib_add_flash             Address of calib region in flash
        @return calib_size_flash            Size of calib section in flash
        @return out_file_name               Output file name

        @param  fin     configuration file name
    """
    try:
        ConfFile = open(conf, "r")
    except:
        print ("Configuration file" + conf + " is missed")
        print ("Creating a template one")
        print ("Please update it with your parameters")

        ConfFile = open(conf, "w")
        ConfFile.writelines("/*Hex file with calibration updated exported from CCP tool*/" + "\n")
        ConfFile.writelines("--Project hex file name: example-appl_merged.hex" + "\n")
        ConfFile.writelines("/*A2L file the Hex file refers to*/" + "\n")
        ConfFile.writelines("--Project A2L file name: example.a2l" + "\n")
        ConfFile.writelines("/*Address of calibration region in flash, get this data from linker file*/" + "\n")
        ConfFile.writelines("--Address of calibration region in flash: 0x18000" + "\n")
        ConfFile.writelines("/*Size of calibration region in flash, get this data from linker file*/" + "\n")
        ConfFile.writelines("--Size of calibration region in flash: 0x8000" + "\n")
        ConfFile.writelines("/*Name of the calibrated output file*/" + "\n")
        ConfFile.writelines("--Output file name: example-calibrated.hex" + "\n")
        ConfFile.close
        sys.exit("")

    for line in ConfFile:
        if '--Project hex file name' in line:
            input_hex = os.getcwd() +"/"+ (line.split(':')[1]).strip()

        if '--Project A2L file name' in line:
            input_A2l= os.getcwd() +"/"+ (line.split(':')[1]).strip()

        if '--Address of calibration region' in line:
            calib_add_flash = int((line.split(':')[1]).strip(),0)

        if '--Size of calibration region' in line:
            calib_size_flash = int((line.split(':')[1]).strip(),0)

        if '--Output file name' in line:
            out_file_name = os.getcwd() +"/"+ (line.split(':')[1]).strip()

    ConfFile.close()
    return (input_hex, input_A2l, calib_add_flash, calib_size_flash, out_file_name)
#end getConfData

def readinput_a2l(fin):
    """readinput_a2l get memory sections addresses from input_a2l file
        @return prog_start_add
        @return prog_lenght
        @return calib_start_add
        @return calib_lenght

        @param  fin     input input_a2l file
    """
    try:
        input_a2lFile = open(fin, "r")
    except:
        print ('File' + fin + 'does not exist')

    prog_start_add = []
    prog_lenght = []
    calib_start_add = []
    calib_lenght = []

    for line in input_a2lFile:
        #Get application starting address in flash
        if 'Application' in line:
            next(input_a2lFile)
            next(input_a2lFile)
            next(input_a2lFile)
            address = next(input_a2lFile)

            try:
                prog_start_add.append(int('0x'+ address.split('0x')[1],0))
                prog_lenght.append(int('0x'+ address.split('0x')[2],0))
            except:
                print ('Cannot convert program addresses')

        #Get calibration starting address

        if 'Calibration' in line:
            next(input_a2lFile)
            next(input_a2lFile)
            next(input_a2lFile)
            address = next(input_a2lFile)

            try:
                calib_start_add.append(int('0x'+ address.split('0x')[1],0))
                calib_lenght.append(int('0x'+ address.split('0x')[2],0))
            except:
                print ('Cannot convert calibration addresses')

    input_a2lFile.close()
    return (prog_start_add, prog_lenght, calib_start_add, calib_lenght)
#end readinput_a2l



def hex2bin(fin, fout, start=None, end=None, size=None, pad=0xFF):
    """Hex-to-Bin convertor engine.
    @return     0   if all OK

    @param  fin     input hex file (filename or file-like object)
    @param  fout    output bin file (filename or file-like object)
    @param  start   start of address range (optional)
    @param  end     end of address range (optional)
    @param  size    size of resulting file (in bytes) (optional)
    @param  pad     padding byte (optional)
    """
    h = IntelHex(fin)
    if not h.readfile():
        print ("Bad input_hex file")
        return 1

    # start, end, size
    if size != None and size != 0:
        if end == None:
            if start == None:
                start = h.minaddr()
                print (start)
            end = start + size - 1
            print (end)
        else:
            if (end+1) >= size:
                start = end + 1 - size
            else:
                start = 0

    try:
        h.tobinfile(fout, start, end, pad)
    except IOError:
        print ("Could not write to file: %s" % fout)
        return 1

    return 0
#/def hex2bin

def main():
    try:
        print("-------------------------------------------------------------------------------\n")
        print("|                         Create a calibrated Hex file                        |\n")
        print("|                                                                             |\n")
        print("-------------------------------------------------------------------------------\n")

        conf_file = os.getcwd() + '/strategy_calib.cfg'
        bin_ram = os.getcwd() + '/bin/calibram'
        bin_rom = os.getcwd() + '/bin/calibrom'
        bin_app = os.getcwd() + '/bin/appl'
        bin_cal = os.getcwd() + '/bin/applram'
        hex_app = os.getcwd() + '/bin/hex_app'
        hex_cal = os.getcwd() + '/bin/hex_cal'
        hex_ram_cal = os.getcwd() + '/bin/hex_ram_cal'

        print
        print ("*************************************************")
        print ("Load configuration data from file")
        print

        try:
            (input_hex, input_a2l, calib_add_flash, calib_size_flash, out_file_name) = getConfData(conf_file);
        except:
            print ("Configuration file is corrupted")

        print ("Project hex file name: " + input_hex )
        print ("Project A2L file name: " + input_a2l )
        print ("Address of calibration region: " + hex(calib_add_flash))

        print ("Size of calibration region: " + hex(calib_size_flash))
        print ("Output file name: " + out_file_name)

        print ("*************************************************")
        print ("Get data and calibration addresses from input_a2l file")
        (prog_start_add, prog_lenght, calib_start_add, calib_lenght) = readinput_a2l(input_a2l)

        #now we need a bin file whose size is the size of calibration in flash and that contains all calibration bin file one after another
        #But adding all calibrations together does not make the size of the calibration section in flash, the difference must be padded with 0xFF
        calib_file = open(bin_ram + ".bin",'wb')
        calib_size = 0

        #convert all calibration sections taken from HEX file into single bin files
        for i in range(len(calib_start_add)):
            print ("Calibration starting address " + hex(calib_start_add[i]))
            print ("Calibration lenght " + hex(calib_lenght[i]))
            hex2bin(input_hex, bin_cal + "_" + str(i) + ".bin", start=calib_start_add[i], end=None, size=calib_lenght[i], pad=0xFF)
            #add the new created file to the general calibration file
            calib_file.write(open((bin_cal + "_" + str(i) + ".bin"),"rb").read())
            calib_size = calib_size + calib_lenght[i]

        #now pad the calib file with 0xFF
        calib_file.write(bytes.fromhex('FF')*(calib_size_flash - calib_size))
        calib_file.close()

        #convert all application sections taken from HEX file into single bin files (usually there is only one)
        for i in range(len(prog_start_add)):
            print ("Application starting address " + hex(prog_start_add[i]))
            print ("Application lenght " + hex(prog_lenght[i]))
            hex2bin(input_hex, bin_app + "_" + str(i) + ".bin", start=prog_start_add[i], end=None, size=prog_lenght[i], pad=0xFF)
        print


        print
        print ("*************************************************")
        print ("Update Bin files with new checksum")
        print
        #calculate the checksum of the calibrated region using the external program CalibBuilder
        shutil.copyfile(bin_ram + ".bin",bin_rom + ".bin")
        command = "./bin/calibBuilder \"" + bin_rom + ".bin\" " + str(calib_size_flash)
        print ("Executing: " + command)
        call(command)

        print
        print ("*************************************************")
        print ("Convert bin files to hex")
        print

        #time to assemble the new hex file.
        #First the calibration region
        command = "./bin/bin2hex /q /t /O" + hex(calib_add_flash) + " /l" + hex(calib_size_flash) + " \"" + bin_rom + ".bin\" \"" + hex_cal + ".hex\""
        print ("Executing: " + command)
        call(command)

        #next all application regions
        for i in range(len(prog_start_add)):
            command = "./bin/bin2hex /q /t /O" + hex(prog_start_add[i]) + " \"" + bin_app + "_" + str(i) + ".bin" + "\" \"" + hex_app + "_" + str(i) + ".hex"+ "\""
            print ("Executing: " + command)
            call(command)

        #last but not least all calibration regions
        for i in range(len(calib_start_add)):
            command = "./bin/bin2hex /q /O" + hex(calib_start_add[i]) + " /l" + hex(calib_lenght[i]) + " \"" + bin_cal + "_" + str(i) + ".bin" + "\" \"" + hex_ram_cal + "_" + str(i) + ".hex"+ "\""
            print ("Executing: " + command)
            call(command)


        print ("*************************************************")
        print ("Merge hex files")
        print

        #now merge all hex files
        destination = open(out_file_name,'wb')
        shutil.copyfileobj(open(hex_cal + ".hex",'rb'), destination)
        for i in range(len(prog_start_add)):
            shutil.copyfileobj(open(hex_app + "_" + str(i) + ".hex",'rb'), destination)
        for i in range(len(calib_start_add)):
            shutil.copyfileobj(open(hex_ram_cal + "_" + str(i) + ".hex",'rb'), destination)

        destination.close()

        print ("*************************************************")
        print ("DONE !!!!")

        #remove some support files that are now meaningless
        os.remove(bin_rom + ".bin")
        os.remove(bin_ram + ".bin")
        os.remove (hex_cal + ".hex")

        for i in range(len(prog_start_add)):
            os.remove (bin_app + "_" + str(i) + ".bin")
            os.remove (hex_app + "_" + str(i) + ".hex")

        for i in range(len(calib_start_add)):
            os.remove (bin_cal + "_" + str(i) + ".bin")
            os.remove (hex_ram_cal + "_" + str(i) + ".hex")

        return 0

    except Exception:
        traceback.print_exc()
        sys.stderr.write('ERROR!!!')
        return 1

if __name__ == '__main__':
    sys.exit(main())
























