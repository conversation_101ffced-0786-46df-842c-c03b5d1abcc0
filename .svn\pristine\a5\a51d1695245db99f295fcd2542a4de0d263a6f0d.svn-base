// T32TPU20001 Thu Jan 03 08:25:51 2019

 B::
 
 TOOLBAR ON
 STATUSBAR ON
 FramePOS 3.75,2.1429,,,Maximized
 WinPAGE.RESet
 
 WinPAGE.Create P000
 WinCLEAR
   
 WinPOS 0.375 26.643 100. 7. 0. 0. W001
 
 VAR.ADDWATCH %H %E %SL  Debug_PIT0_Core2_H
 VAR.ADDWATCH %H %E %SL  Debug_PIT0_Core2_L
 VAR.ADDWATCH %H %E %SL  cnt_Routine8
 VAR.ADDWATCH %H %E %SL  Debug_SetIRQ8_c2
 VAR.ADDWATCH %H %E %SL  cnt_Routine31
 VAR.ADDWATCH %H %E %SL  Debug_SetIRQ_to_C0
 VAR.ADDWATCH %H %E %SL  Debug_PIT0_Core2_cnt
 VAR.ADDWATCH %H %E %SL  Debug_SysReset_c2
 VAR.ADDWATCH %H %E %SL  CntMain2Loop
 VAR.ADDWATCH %H %E %SL  FLASHTEST_DOERASE
 VAR.ADDWATCH %H %E %SL  FLASHTEST_DOBLANKCHECK
 VAR.ADDWATCH %H %E %SL  FLASHTEST_DOPROGRAM
 VAR.ADDWATCH %H %E %SL  FLASHTEST_STARTADDRESS
 VAR.ADDWATCH %H %E %SL  FLASHTEST_SIZE
 VAR.ADDWATCH %H %E %SL  FLASHTEST_RETURNCODE
 VAR.ADDWATCH %H %E %SL  FLASHTEST_RETURNCODE_BLANCKCHECK
 VAR.ADDWATCH %H %E %SL  FLASHTEST_CB 
 VAR.ADDWATCH %H %E %SL  Prova_ID0
 VAR.ADDWATCH %H %E %SL  EE_IDtable_bl1 
 VAR.ADDWATCH %H %E %SL  EE_IDtable_bl0
 VAR.ADDWATCH %H %E %SL  testEEmgm 
 VAR.ADDWATCH %H %E %SL  next_eventdebug
 VAR.ADDWATCH %H %E %SL  MainCore2debug
 VAR.ADDWATCH %H %E %SL  LossLock
 VAR.ADDWATCH %H %E %SL  CrankPhased
 VAR.ADDWATCH %H %E %SL  CrankPhaseRqst
 VAR.ADDWATCH %H %E %SL  eventNoLock
 VAR.ADDWATCH %H %E %SL  eventObj
 VAR.ADDWATCH %H %E %SL  debug_N_losscnt
 VAR.ADDWATCH %H %E %SL  Last_event_tbu
 VAR.ADDWATCH %H %E %SL  Last_event
 VAR.ADDWATCH %H %E %SL  CurrentAngle
 VAR.ADDWATCH %H %E %SL  Tbu
 VAR.ADDWATCH %H %E %SL  AngleMin
 VAR.ADDWATCH %H %E %SL  AngleMax
 VAR.ADDWATCH %H %E %SL  NTimInterrupt
 VAR.ADDWATCH %H %E %SL  diffavg
 VAR.ADDWATCH %H %E %SL  SparkDurationCylSel
 VAR.ADDWATCH %H %E %SL  SparkAngleDuration
 VAR.ADDWATCH %H %E %SL  SparkLength
 VAR.ADDWATCH %H %E %SL  TrailEdge
 VAR.ADDWATCH %H %E %SL  LeadEdge
 VAR.ADDWATCH %H %E %SL  VtDwellAngleOff
 VAR.ADDWATCH %H %E %SL  RpmCalc
 VAR.ADDWATCH %H %E %SL  ToothAbsTime
 WinPAGE.select P000
 
 ENDDO
