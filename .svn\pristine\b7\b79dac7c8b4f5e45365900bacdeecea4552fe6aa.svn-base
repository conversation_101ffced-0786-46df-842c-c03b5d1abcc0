/*****************************************************************************************************************/
/* $HeadURL::                                                                                                                                    $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Startup
**  Filename        :  app_checkVersion.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include <string.h>
#include "rtwtypes.h"
#include "sys.h"



/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
#define CALIB_APPLIINFO_LABEL_POS (0u)     // blockVersionLabel position in tag
#define CALIB_APPLIINFO_LABEL_LEN (11u)
#define CALIB_APPLIINFO_CHECKSUM_POS (12u) // blockChecksum position in tag
#define CALIB_APPLIINFO_CHECKSUM_LEN (4u)

// external definitions

extern uint32_T __APP_START;
extern uint32_T __APP_SIZE;
extern uint32_T __CALIB_ROM_START;
extern uint32_T __CALIB_ROM_SIZE;


extern const char_T calib_buildCode[16];

const char_T app_buildCode[sizeof(uint32_T)] =
{
    "IBOX"
};


int16_T app_CheckVersion(void);
int16_T calib_CheckVersion(void);





/******************************************************************************
**   Function    : app_CheckVersion
**
**   Description:
**    This method performs a minimal application integrity check.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR   - no error
**    -1         - error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T app_CheckVersion(void)
{
    BlockDescription* InfoPtr;
    int16_T error;

    error = -1;
    InfoPtr = (BlockDescription *) (((uint32_T)(&__CALIB_ROM_START)+(uint32_T)(&__CALIB_ROM_SIZE))-sizeof(BlockDescription));
    if ((InfoPtr->blockStatusOK == (uint32_T)NO_ERROR) &&
        (CHECK_VALID_REGION(InfoPtr) == NO_ERROR))
    {
        error = NO_ERROR;
    }

    return error;
}



/******************************************************************************
**   Function    : calib_CheckVersion
**
**   Description:
**    This method performs a minimal calibration integrity check.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR   - no error
**    -1         - error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T calib_CheckVersion(void)           //!!PD  Da spostare nel boot
{    
    BlockDescription *appliTag;
    uint32_T i;
    uint8_T blockChecksum_temp[CALIB_APPLIINFO_CHECKSUM_LEN]; // 4 bytes
    int16_T retValue = NO_ERROR;

    appliTag = (BlockDescription *) (((uint32_T)(&__APP_START)+(uint32_T)(&__APP_SIZE))-sizeof(BlockDescription));

    for (i = 0u; (i < CALIB_APPLIINFO_LABEL_LEN) && (retValue == NO_ERROR); i++)
    {
        if (appliTag->blockVersionLabel[i] != (uint8_T)calib_buildCode[i])
        {
            retValue = -1;
        }
    }
    if (retValue == NO_ERROR)
    {
        
        /* Copying blk cksm in a local buffer to check validity */
        for (i = 0u; i<CALIB_APPLIINFO_CHECKSUM_LEN; i++)
        {
            blockChecksum_temp[3u-i] = (uint8_T)((appliTag->blockChecksum) >> (i*8u))&0xffu;
        }
        /* Validating cksm */
        for (i = 0u; (i < CALIB_APPLIINFO_CHECKSUM_LEN) && (retValue == NO_ERROR); i++)
        {
            if (blockChecksum_temp[i] != (uint8_T)calib_buildCode[CALIB_APPLIINFO_CHECKSUM_POS + (i - 1u)])
            {
                retValue = -1;
            }
        }
    }
    return retValue;
}

/****************************************************************************
 ****************************************************************************/
