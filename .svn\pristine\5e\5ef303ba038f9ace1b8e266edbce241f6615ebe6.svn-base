/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  Setlock.c
**  Created on      :  22-giu-2020 10:00:00
**  Original author :  CarboniM
**  Integration history: 
**  -  Standard software driver for C55 Flash from "STM - SPC57xKLS-FLASH Driver for C55: Package version 0.2.0"
**          
*******************************************************************************************************************/

#ifdef  _BUILD_FLASH_

#pragma ghs startnomisra

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include    "ssd_c55.h"


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* C90LC Driver v1.0.1 */
static const unsigned short SetLock_C[] = 
{
#if (TARGET_TYPE == SPC574K2_CUT24) || (TARGET_TYPE == SPC574K2)     /* Total Size = 169 half words */

    0x0080, 0x1821, 0x06F0, 0xD3F1, 0xD501, 0x0146, 0x480F, 
    0x30E3, 0x0040, 0x2A07, 0xE605, 0x7180, 0x0010, 0x4944,
    0xE804, 0x7180, 0x0028, 0x4AC4, 0x0167, 0x2227, 0xE005, 
    0x2207, 0xE409, 0xE61B, 0xE82A, 0x2237, 0xE437, 0xE64C,
    0x2247, 0xE661, 0xE865, 0x0344, 0x7180, 0x0010, 0xC363, 
    0xC473, 0x0476, 0xC573, 0x0476, 0xC273, 0x0467, 0x18E7,
    0xB020, 0x18C7, 0x84FF, 0x2C07, 0x4067, 0x7CE6, 0x8070, 
    0xE854, 0x0344, 0x7180, 0x0000, 0xC763, 0xC873, 0x0476,
    0xC973, 0x0476, 0xC673, 0x0467, 0x18C7, 0xB020, 0x2C07, 
    0x7CE6, 0x3430, 0xE844, 0x7180, 0x0000, 0xCB63, 0xCC73,
    0x0476, 0xCD73, 0x0476, 0xCA73, 0x0467, 0x18C7, 0xB020, 
    0x2C07, 0x7CE6, 0x3430, 0xE835, 0x30E3, 0x0040, 0x2A07,
    0xE603, 0x4984, 0xE803, 0x637F, 0xE83B, 0x7180, 0x0000, 
    0xCE73, 0x23F7, 0xE403, 0x2C06, 0xE826, 0xCE73, 0x18C7,
    0xB020, 0x2C07, 0x7CE6, 0x3430, 0xE81F, 0x30E3, 0x0040, 
    0x2A07, 0xE603, 0x49C4, 0xE803, 0x637F, 0xE825, 0x7180,
    0x0000, 0xCE73, 0x23F7, 0xE109, 0xCE73, 0x25F7, 0x18C7, 
    0xB020, 0x2C07, 0x7CE6, 0x3430, 0xE80A, 0x4806, 0xE808,
    0x0344, 0x7180, 0x001F, 0x6206, 0xE803, 0x638F, 0xE80F, 
    0xC073, 0x0474, 0x7CCB, 0x30F8, 0xC074, 0x7CE7, 0x5838,
    0xD074, 0x7CA7, 0x6030, 0x4676, 0xC074, 0x4467, 0xD074, 
    0x30E3, 0x0048, 0x2A07, 0xE608, 0x7FE3, 0xFB78, 0x1800,
    0xD000, 0x0002, 0x1800, 0xD000, 0x01F7, 0x0173, 0xC3F1, 
    0xC501, 0x20F1, 0x0090, 0x0004, 0x3038, 0x3030, 0x3746, 
    0x4646

#endif
};

extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned long *functionBuffer, uint32_T functionSize);

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * SetLock - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/  

uint32_T SetLock ( PSSD_CONFIG pSSDConfig,
        uint8_T blkLockIndicator,
        uint32_T blkLockState
)

{
#pragma ghs nowarning 171
    FlashFunctionLoader( (unsigned long*)SetLock_C, sizeof(SetLock_C)/2);
    return ((PSETLOCK)FlashFunctionPointer)(pSSDConfig, blkLockIndicator,
            blkLockState);
#pragma ghs endnowarning /* warning #171-D: invalid type conversion */
}
#pragma ghs endnomisra


#endif /* _BUILD_FLASH_ */

