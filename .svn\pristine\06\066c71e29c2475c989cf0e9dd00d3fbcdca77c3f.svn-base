/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonAcqParEval.h
 **  Date:          02-Dec-2021
 **
 **  Model Version: 1.1701
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonAcqParEval_h_
#define RTW_HEADER_IonAcqParEval_h_
#ifndef IonAcqParEval_COMMON_INCLUDES_
# define IonAcqParEval_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonAcqParEval_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: ELD_EXPORT_DEFINE */
#define ION_DT_MIN                     8U                        /* Referenced by:
                                                                  * '<S2>/Constant7'
                                                                  * '<S7>/Constant4'
                                                                  */

/* Minimum allowed value for sampling time. */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonAcqParEval_initialize(void);

/* Exported entry point function */
extern void IonAcqParEval_PowerOn(void);

/* Exported entry point function */
extern void IonAcqParEval_PrepareNewAcq(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T IonDTPrg[4];            /* '<S1>/Merge' */

/* Ion Acquisition step time for each channel */
extern uint16_T IonDThetaInvPrg[4];    /* '<S1>/Merge7' */

/* Inverse of ion Acquisition step angle for each channel */
extern uint16_T IonDThetaPrg[4];       /* '<S1>/Merge1' */

/* Ion Acquisition step angle for each channel */
extern uint8_T IonWindow[4];           /* '<S1>/Merge6' */

/* IonWindow */
extern uint16_T NSampleMaxPrg[4];      /* '<S1>/Merge3' */

/* Ion Acquisition Max Sample Counter for each channel */
extern uint16_T NSampleStartPrg[4];    /* '<S1>/Merge2' */

/* Ion Acquisition Start Sample Counter for each channel */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S8>/Constant' : Unused code path elimination
 * Block '<S8>/Data Type Duplicate' : Unused code path elimination
 * Block '<S8>/Data Type Propagation' : Unused code path elimination
 * Block '<S9>/Constant' : Unused code path elimination
 * Block '<S9>/Data Type Duplicate' : Unused code path elimination
 * Block '<S9>/Data Type Propagation' : Unused code path elimination
 * Block '<S10>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Data Type Propagation' : Unused code path elimination
 * Block '<S6>/SET_LSB_M10' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S10>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S10>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S10>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S10>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S17>/Assign_correct_lsb' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonAcqParEval'
 * '<S1>'   : 'IonAcqParEval/Merge'
 * '<S2>'   : 'IonAcqParEval/PowerOn'
 * '<S3>'   : 'IonAcqParEval/PrepareNewAcq'
 * '<S4>'   : 'IonAcqParEval/PrepareNewAcq/Acquisition_Step'
 * '<S5>'   : 'IonAcqParEval/PrepareNewAcq/Ion_Window'
 * '<S6>'   : 'IonAcqParEval/PrepareNewAcq/Acquisition_Step/AcquisitionAngle'
 * '<S7>'   : 'IonAcqParEval/PrepareNewAcq/Acquisition_Step/AcquisitionTime'
 * '<S8>'   : 'IonAcqParEval/PrepareNewAcq/Acquisition_Step/AcquisitionTime/LookUp_IR_U1'
 * '<S9>'   : 'IonAcqParEval/PrepareNewAcq/Acquisition_Step/AcquisitionTime/LookUp_IR_U8'
 * '<S10>'  : 'IonAcqParEval/PrepareNewAcq/Acquisition_Step/AcquisitionTime/PreLookUpIdSearch_U16'
 * '<S11>'  : 'IonAcqParEval/PrepareNewAcq/Ion_Window/Ion_Window'
 * '<S12>'  : 'IonAcqParEval/PrepareNewAcq/Ion_Window/MegaKnock_Window'
 * '<S13>'  : 'IonAcqParEval/PrepareNewAcq/Ion_Window/Start_Sample_Counter'
 * '<S14>'  : 'IonAcqParEval/PrepareNewAcq/Ion_Window/Ion_Window/Ion_Window'
 * '<S15>'  : 'IonAcqParEval/PrepareNewAcq/Ion_Window/Ion_Window/Max_Sample_Counter'
 * '<S16>'  : 'IonAcqParEval/PrepareNewAcq/Ion_Window/Ion_Window/Ion_Window/SaturationDynamic'
 * '<S17>'  : 'IonAcqParEval/PrepareNewAcq/Ion_Window/Ion_Window/Max_Sample_Counter/calculateSampleMax'
 * '<S18>'  : 'IonAcqParEval/PrepareNewAcq/Ion_Window/Ion_Window/Max_Sample_Counter/calculateSampleMax/Subsystem'
 */

/*-
 * Requirements for '<Root>': IonAcqParEval
 *
 * Inherited requirements for '<Root>/PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_1659: Software shall initialize each signal used for Ion Signal acquisition algorithm at ECU power on. (ECU_SW_Requirements#3896)

 */
#endif                                 /* RTW_HEADER_IonAcqParEval_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/