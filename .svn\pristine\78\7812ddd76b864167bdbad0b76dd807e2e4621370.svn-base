/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonKnockState.c
 **  File Creation Date: 22-Apr-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonKnockState
 **  Model Description:  This model defines the state of knock detection strategy within one of the following states (NO_KNOCK, ACTIVE_KNOCK, HEAVY_KNOCK, WAIT_HEAVY_KNOCK),
   IonKnockState is triggered at End Of Acquisition event and aggregates data collected from IonKnockInt and MKnockDet models.

 **  Model Version:      1.1381
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Thu Apr 22 09:49:53 2021
 **
 **  Last Saved Modification:  RoccaG - Thu Apr 22 09:45:36 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonKnockState_out.h"
#include "IonKnockState_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKRPMHKNOCKGAIN_dim            8U                        /* Referenced by: '<S6>/Constant2' */

/* BKRPMHKNOCKGAIN breakpoint dimension. */
#define ID_VER_IONKNOCKSTATE_DEF       11381U                    /* Referenced by: '<S2>/Constant12' */

/* Model Version. */
#define MAX_KNOCK_PERC_FILT            1000U                     /* Referenced by:
                                                                  * '<S9>/Constant2'
                                                                  * '<S11>/Constant2'
                                                                  */

/* Maximum value for percent step of knock counter. */
#define MAX_STEP_COUNT                 126U                      /* Referenced by: '<S11>/Constant3' */

/* Maximum step value for knock counter. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONKNOCKSTATE_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Calibration memory section */
/*Start of local calbration section*/

#pragma ghs section rodata=".calib"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMHKNOCKGAIN[9] = { 1000U, 2000U,
  3000U, 3500U, 4000U, 4500U, 5000U, 6000U, 8000U } ;

/* Rpm breakpoint vector for heavy knock threshold. */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T HKNOCKCYCLES = 3;

/* Number of cycles for knocking control freeze after heavy knock */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T KNOCKCNTTHR = 1;

/* Threshold on the knock counter to set the knock state */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T KNOCKCSTEPDOWN = -1;

/* Step to reduce the knock counter when knocking is not active */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T KNOCKPERCFILT = 0U;

/* Knocking percentage used to increment KnockCnt */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T MHKNOCKCYCLES = 5;

/* Number of cycles for knocking control freeze after mega knock */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTHKNOCKGAIN[9] = { 21U, 21U, 21U, 21U,
  21U, 21U, 21U, 21U, 21U } ;

/* Gain for heavy knock intensity declaration */
#pragma ghs section rodata=default

/*End of calibration section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T FlgHeavyKnock[8];              /* '<S3>/Merge1' */

/* Acquisition circuit switching flag for heavy-knock detection */
enum_KnockState KnockState[8];         /* '<S3>/Merge' */

/* Knock state */

/*Static test point definition*/
/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT int8_T HKnockCnt[8];

/* Heavy knocking cycles counter */
STATIC_TEST_POINT uint32_T HKnockIntThr;

/* HKnockIntThr */
STATIC_TEST_POINT uint32_T IdVer_IonKnockState;

/* Model Version */
STATIC_TEST_POINT int8_T KnockCnt[8];

/* Knocking cycles counter */
STATIC_TEST_POINT int8_T KnockCntCyl;

/* Knock cycles counter for last computed cylinder */
STATIC_TEST_POINT int8_T KnockCntMax[8];

/* Knocking cycles counter threshold */
STATIC_TEST_POINT int8_T KnockCntStep[8];

/* Knocking cycles counter step */

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void IonKnockState_EOA(void)
{
  /* local block i/o variables */
  uint16_T rtb_LookUp_U8_U16;
  int8_T rtb_Merge;
  int8_T rtb_Merge1;
  int16_T rtb_Add;
  uint16_T u0;
  boolean_T guard1 = false;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockState_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  This block performs is the runnable for EOA event
   */
  /* If: '<S9>/If' incorporates:
   *  Constant: '<S9>/Constant'
   *  Constant: '<S9>/Constant2'
   *  Logic: '<S9>/Logical Operator'
   *  RelationalOperator: '<S9>/Relational Operator'
   *  RelationalOperator: '<S9>/Relational Operator1'
   */
  if ((((int32_T)KNOCKPERCFILT) == 0) || (KNOCKPERCFILT > ((uint16_T)
        MAX_KNOCK_PERC_FILT))) {
    /* Outputs for IfAction SubSystem: '<S5>/StrategyDisabled_Or_WrongCalibration' incorporates:
     *  ActionPort: '<S10>/Action Port'
     *
     * Block description for '<S5>/StrategyDisabled_Or_WrongCalibration':
     *  This block define knock counter step and knock counter upper threshold as:
     *  -knock counter step = 1.
     *  -knock counter threshold = KNOCKCNTTHR
     *
     * Block requirements for '<S5>/StrategyDisabled_Or_WrongCalibration':
     *  1. EISB_FCA6CYL_SW_REQ_1202: Every time that calibration  KNOCKPERCFILT is equal to 0 or is gre... (ECU_SW_Requirements#1211)
     */
    /* SignalConversion generated from: '<S10>/knockCntStep' incorporates:
     *  Constant: '<S10>/Constant'
     */
    rtb_Merge = 1;

    /* SignalConversion generated from: '<S10>/knockCntMax' incorporates:
     *  Constant: '<S10>/Constant1'
     */
    rtb_Merge1 = KNOCKCNTTHR;

    /* End of Outputs for SubSystem: '<S5>/StrategyDisabled_Or_WrongCalibration' */
  } else {
    /* Outputs for IfAction SubSystem: '<S5>/StrategyOn' incorporates:
     *  ActionPort: '<S11>/Action Port'
     *
     * Block description for '<S5>/StrategyOn':
     *  This block define knock counter step and knock counter upper threshold as:
     *  -knock counter step = 100/KNOCKPERCFILT.
     *  -knock counter threshold = KnockCntStep +1
     *
     * Block requirements for '<S5>/StrategyOn':
     *  1. EISB_FCA6CYL_SW_REQ_1201: Every time that the calibration KNOCKPERCFILT is greater than 0 an... (ECU_SW_Requirements#1210)
     */
    /* Product: '<S11>/Divide' incorporates:
     *  Constant: '<S11>/Constant'
     *  Constant: '<S11>/Constant2'
     */
    u0 = (uint16_T)(((uint32_T)((uint16_T)MAX_KNOCK_PERC_FILT)) / ((uint32_T)
      KNOCKPERCFILT));

    /* MinMax: '<S11>/MinMax' incorporates:
     *  Constant: '<S11>/Constant3'
     */
    if (u0 >= ((uint16_T)MAX_STEP_COUNT)) {
      u0 = ((uint16_T)MAX_STEP_COUNT);
    }

    /* Sum: '<S11>/Add' incorporates:
     *  Constant: '<S11>/Constant1'
     *  DataTypeConversion: '<S11>/Data Type Conversion'
     *  MinMax: '<S11>/MinMax'
     */
    rtb_Merge1 = (int8_T)(((int8_T)u0) + 1);

    /* SignalConversion generated from: '<S11>/knockCntStep' incorporates:
     *  DataTypeConversion: '<S11>/Data Type Conversion'
     *  MinMax: '<S11>/MinMax'
     */
    rtb_Merge = (int8_T)u0;

    /* End of Outputs for SubSystem: '<S5>/StrategyOn' */
  }

  /* End of If: '<S9>/If' */

  /* If: '<S5>/If' incorporates:
   *  Inport: '<Root>/DeltaKnockNPow'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/IonKnockEnabled'
   *  MultiPortSwitch: '<S14>/Index Vector'
   *  RelationalOperator: '<S14>/Relational Operator'
   *  Switch: '<S14>/Switch'
   */
  if (((int32_T)IonKnockEnabled) == 0) {
    /* Outputs for IfAction SubSystem: '<S5>/calc_Counter1' incorporates:
     *  ActionPort: '<S15>/Action Port'
     *
     * Block description for '<S5>/calc_Counter1':
     *  All output (for each cylinder) is resetted when Ion Knock strategy is
     *  disabled.
     *
     * Block requirements for '<S5>/calc_Counter1':
     *  1. EISB_FCA6CYL_SW_REQ_1651: Software shall set to 0 each output produced for Knock State funct... (ECU_SW_Requirements#3115)
     */
    /* SignalConversion generated from: '<S15>/KnockCnt' */
    memset((&(KnockCnt[0])), 0, (sizeof(int8_T)) << 3U);

    /* SignalConversion generated from: '<S15>/KnockCntMax' */
    memset((&(KnockCntMax[0])), 0, (sizeof(int8_T)) << 3U);

    /* SignalConversion generated from: '<S15>/KnockCntStep' */
    memset((&(KnockCntStep[0])), 0, (sizeof(int8_T)) << 3U);

    /* SignalConversion generated from: '<S15>/KnockCntCyl' incorporates:
     *  Constant: '<S15>/Constant'
     */
    KnockCntCyl = 0;

    /* SignalConversion generated from: '<S15>/KnockCntMaxCyl' incorporates:
     *  Constant: '<S15>/Constant1'
     */
    rtb_Merge1 = 0;

    /* End of Outputs for SubSystem: '<S5>/calc_Counter1' */
  } else {
    /* Outputs for IfAction SubSystem: '<S5>/calc_Counter' incorporates:
     *  ActionPort: '<S14>/Action Port'
     *
     * Block description for '<S5>/calc_Counter':
     *  This block counts consecutive knock events for each cylinder.
     *  When no knock is detected then the knock counter is decreased by KNOCKSTEPDOWN steps until the counter reachs 0.
     *
     * Block requirements for '<S5>/calc_Counter':
     *  1. EISB_FCA6CYL_SW_REQ_1190: Every time that knock detection strategy is enabled and DeltaKnock... (ECU_SW_Requirements#1212)
     *  2. EISB_FCA6CYL_SW_REQ_1193: Every time that knock detection strategy is enabled and DeltaKnock... (ECU_SW_Requirements#1213)
     */
    if (DeltaKnockNPow[(IonAbsTdcEOA)] <= 0) {
      /* Switch: '<S14>/Switch' incorporates:
       *  Constant: '<S14>/Constant1'
       *
       * Block requirements for '<S14>/Constant1':
       *  1. EISB_FCA6CYL_SW_REQ_1193: Every time that knock detection strategy is enabled and DeltaKnock... (ECU_SW_Requirements#1213)
       */
      rtb_Merge = KNOCKCSTEPDOWN;
    }

    /* Sum: '<S14>/Add' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  MultiPortSwitch: '<S14>/Index Vector1'
     *  SignalConversion generated from: '<S1>/KnockCnt_old'
     *  Switch: '<S14>/Switch'
     *
     * Block requirements for '<S14>/Add':
     *  1. EISB_FCA6CYL_SW_REQ_1190: Every time that knock detection strategy is enabled and DeltaKnock... (ECU_SW_Requirements#1212)
     */
    rtb_Add = (int16_T)((int32_T)(((int32_T)KnockCnt[(IonAbsTdcEOA)]) +
      ((int32_T)rtb_Merge)));

    /* Switch: '<S16>/Switch2' incorporates:
     *  RelationalOperator: '<S16>/LowerRelop1'
     *  RelationalOperator: '<S16>/UpperRelop'
     *  Switch: '<S16>/Switch'
     */
    if (rtb_Add > ((int16_T)rtb_Merge1)) {
      rtb_Add = (int16_T)rtb_Merge1;
    } else {
      if (rtb_Add < 0) {
        /* Switch: '<S16>/Switch' incorporates:
         *  Constant: '<S14>/Constant2'
         */
        rtb_Add = 0;
      }
    }

    /* End of Switch: '<S16>/Switch2' */

    /* Assignment: '<S14>/Assignment' incorporates:
     *  DataTypeConversion: '<S14>/Data Type Conversion1'
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    KnockCnt[(IonAbsTdcEOA)] = (int8_T)rtb_Add;

    /* SignalConversion generated from: '<S14>/KnockCntCyl' incorporates:
     *  DataTypeConversion: '<S14>/Data Type Conversion1'
     */
    KnockCntCyl = (int8_T)rtb_Add;

    /* Assignment: '<S14>/Assignment1' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Switch: '<S14>/Switch'
     */
    KnockCntStep[(IonAbsTdcEOA)] = rtb_Merge;

    /* Assignment: '<S14>/Assignment2' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    KnockCntMax[(IonAbsTdcEOA)] = rtb_Merge1;

    /* End of Outputs for SubSystem: '<S5>/calc_Counter' */
  }

  /* End of If: '<S5>/If' */

  /* SignalConversion generated from: '<S1>/HKnockIntThr' incorporates:
   *  Chart: '<S4>/KnockStateMgm'
   *
   * Block description for '<S4>/KnockStateMgm':
   *  This chart calculates the knock state as following:
   *  -NO_KNOCK.
   *  -ACTIVE_KNOCK, when the knock counter is greater than knock counter threshold.
   *  -HEAVY_KNOCK, when mega knock has been detected or knock intensity is greater than a threshold higher than the standard knock intensity threshold.
   *  -WAIT_HEAVY_KNOCK, is the state assumed for a tunable number of comubustions after HEAVY KNOCK detection.
   */
  /* Gateway: EOA/CreaeKnockState/KnockStateMgm */
  /* During: EOA/CreaeKnockState/KnockStateMgm */
  /* This chart calculates the knock state as following:
     -NO_KNOCK.
     -ACTIVE_KNOCK, when the knock counter is greater than knock counter threshold.
     -HEAVY_KNOCK, when mega knock has been detected or knock intensity is greater than a threshold higher than the standard knock intensity threshold.
     -WAIT_HEAVY_KNOCK, is the state assumed for a tunable number of comubustions after HEAVY KNOCK detection. */
  /* Entry Internal: EOA/CreaeKnockState/KnockStateMgm */
  /* Transition: '<S7>:2' */
  HKnockIntThr = 0U;

  /* Chart: '<S4>/KnockStateMgm' incorporates:
   *  Constant: '<S4>/Constant'
   *  Constant: '<S4>/Constant1'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/IonKnockEnabled'
   *  Inport: '<Root>/MegaKnock'
   *  SignalConversion generated from: '<S1>/FlgHeavyKnock'
   *  SignalConversion generated from: '<S1>/KnockState'
   *  SignalConversion generated from: '<S1>/HKnockCnt_old'
   *
   * Block description for '<S4>/KnockStateMgm':
   *  This chart calculates the knock state as following:
   *  -NO_KNOCK.
   *  -ACTIVE_KNOCK, when the knock counter is greater than knock counter threshold.
   *  -HEAVY_KNOCK, when mega knock has been detected or knock intensity is greater than a threshold higher than the standard knock intensity threshold.
   *  -WAIT_HEAVY_KNOCK, is the state assumed for a tunable number of comubustions after HEAVY KNOCK detection.
   */
  /*  Assign old value to output variables to improve code generation  */
  /*  Ion Knock detection strategy is enabled  */
  if (((int32_T)IonKnockEnabled) != 0) {
    /* Transition: '<S7>:4' */
    /* Transition: '<S7>:9' */
    /*  Heavy knock detection is already active  */
    if (((HKNOCKCYCLES > 0) || (MHKNOCKCYCLES > 0)) && (HKnockCnt[(IonAbsTdcEOA)]
         > 0)) {
      /* Transition: '<S7>:11':
       *  1. EISB_FCA6CYL_SW_REQ_1204: Every time that heavy knock counter (i.e. HKnockCnt) is greater th... (ECU_SW_Requirements#1218)
       */
      /* Transition: '<S7>:15':
       *  1. EISB_FCA6CYL_SW_REQ_1198: Every time that (and condition):
         - knock detection strategy is ena... (ECU_SW_Requirements#1217)
       */
      HKnockCnt[(IonAbsTdcEOA)] = (int8_T)(HKnockCnt[(IonAbsTdcEOA)] - 1);
      KnockState[(IonAbsTdcEOA)] = WAIT_HEAVY_KNOCK;
      FlgHeavyKnock[(IonAbsTdcEOA)] = 1U;
    } else {
      /* Transition: '<S7>:24' */
      /* Mega knock detection  */
      if ((MHKNOCKCYCLES > 0) && (((1 << ((uint64_T)IonAbsTdcEOA)) & ((int32_T)
             MegaKnock)) != 0)) {
        /* Transition: '<S7>:27' */
        /* Transition: '<S7>:32':
         *  1. EISB_FCA6CYL_SW_REQ_1194: Every time that (and condition):
           - knock detection strategy is ena... (ECU_SW_Requirements#1214)
         */
        HKnockCnt[(IonAbsTdcEOA)] = MHKNOCKCYCLES;
        KnockState[(IonAbsTdcEOA)] = HEAVY_KNOCK;
        FlgHeavyKnock[(IonAbsTdcEOA)] = 1U;
      } else {
        /* Transition: '<S7>:35' */
        /* Heavy knock detection  */
        guard1 = false;
        if (HKNOCKCYCLES > 0) {
          /* Outputs for Function Call SubSystem: '<S4>/Heavy_Knock_Threshold'
           *
           * Block description for '<S4>/Heavy_Knock_Threshold':
           *  This block calculates the heavy knock intensity threshold, applying a
           *  gain to the standard knock intensity threshold.
           */
          /* S-Function (LookUp_U8_U16): '<S8>/LookUp_U8_U16' incorporates:
           *  Constant: '<S6>/Constant'
           *  Constant: '<S6>/Constant1'
           *  Constant: '<S6>/Constant2'
           */
          /* Transition: '<S7>:39' */
          /* Transition: '<S7>:41' */
          /* Event: '<S7>:42' */
          LookUp_U8_U16( &rtb_LookUp_U8_U16, &VTHKNOCKGAIN[0], Rpm,
                        &BKRPMHKNOCKGAIN[0], ((uint8_T)BKRPMHKNOCKGAIN_dim));

          /* Product: '<S6>/Product' incorporates:
           *  DataTypeConversion: '<S6>/Data Type Conversion'
           *  Inport: '<Root>/ThrIntKnock'
           *
           * Block requirements for '<S6>/Product':
           *  1. EISB_FCA6CYL_SW_REQ_1195: Software shall calculate a heavy knock threshold (i.e. HKnockIntTh... (ECU_SW_Requirements#1215)
           */
          HKnockIntThr = (uint32_T)((uint64_T)((((uint64_T)((uint16_T)
            (((uint32_T)rtb_LookUp_U8_U16) >> ((uint64_T)2)))) * ((uint64_T)
            ThrIntKnock)) >> ((uint64_T)10)));

          /* End of Outputs for SubSystem: '<S4>/Heavy_Knock_Threshold' */
          /* Transition: '<S7>:44' */
          if (KnockInt[(IonAbsTdcEOA)] > HKnockIntThr) {
            /* Transition: '<S7>:53' */
            /* Transition: '<S7>:56':
             *  1. EISB_FCA6CYL_SW_REQ_1197: Every time that (and condition):
               - knock detection strategy is ena... (ECU_SW_Requirements#1216)
             */
            HKnockCnt[(IonAbsTdcEOA)] = HKNOCKCYCLES;
            KnockState[(IonAbsTdcEOA)] = HEAVY_KNOCK;
            FlgHeavyKnock[(IonAbsTdcEOA)] = 1U;
          } else {
            /* Transition: '<S7>:84' */
            guard1 = true;
          }
        } else {
          /* Transition: '<S7>:48' */
          guard1 = true;
        }

        if (guard1) {
          /* Transition: '<S7>:51' */
          /* Standard knock detection  */
          if (KnockCntCyl >= rtb_Merge1) {
            /* Transition: '<S7>:70' */
            /* Transition: '<S7>:74':
             *  1. EISB_FCA6CYL_SW_REQ_1199: Every time that (and condition):
               - knock detection strategy is ena... (ECU_SW_Requirements#1219)
             */
            KnockState[(IonAbsTdcEOA)] = ACTIVE_KNOCK;

            /* Transition: '<S7>:78' */
          } else {
            /* Transition: '<S7>:77':
             *  1. EISB_FCA6CYL_SW_REQ_1200: Every time that knock detection is disabled or, nor heavy knock is... (ECU_SW_Requirements#1220)
             */
            KnockState[(IonAbsTdcEOA)] = NO_KNOCK;
          }

          /* Transition: '<S7>:80' */
          HKnockCnt[(IonAbsTdcEOA)] = 0;
          FlgHeavyKnock[(IonAbsTdcEOA)] = 0U;

          /* Transition: '<S7>:81' */
        }

        /* Transition: '<S7>:57' */
      }

      /* Transition: '<S7>:33' */
    }

    /* Transition: '<S7>:21' */
  } else {
    /* Transition: '<S7>:20':
     *  1. EISB_FCA6CYL_SW_REQ_1651: Software shall set to 0 each output produced for Knock State funct... (ECU_SW_Requirements#3115)
     */
    HKnockCnt[(IonAbsTdcEOA)] = 0;
    KnockState[(IonAbsTdcEOA)] = NO_KNOCK;
    FlgHeavyKnock[(IonAbsTdcEOA)] = 0U;
  }

  /* Transition: '<S7>:91' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockState_EOA' */
}

/* Model step function */
void IonKnockState_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockState_PowerOn' incorporates:
   *  SubSystem: '<Root>/Init'
   *
   * Block description for '<Root>/Init':
   *  This block performs signal initialization at power on event.
   *
   * Block requirements for '<Root>/Init':
   *  1. EISB_FCA6CYL_SW_REQ_1650: Software shall set to 0 each output produced for Knock State functionality at ECU power on. (ECU_SW_Requirements#3114)
   */
  /* DataTypeConversion: '<S2>/Conversion' */
  memset((&(KnockState[0])), 0, (sizeof(enum_KnockState)) << 3U);

  /* SignalConversion generated from: '<S2>/FlgHeavyKnock' */
  memset((&(FlgHeavyKnock[0])), 0, (sizeof(uint8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/HKnockCnt' */
  memset((&(HKnockCnt[0])), 0, (sizeof(int8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/KnockCnt' */
  memset((&(KnockCnt[0])), 0, (sizeof(int8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/KnockCntMax' */
  memset((&(KnockCntMax[0])), 0, (sizeof(int8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/KnockCntStep' */
  memset((&(KnockCntStep[0])), 0, (sizeof(int8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/HKnockIntThr' incorporates:
   *  Constant: '<S2>/Constant2'
   */
  HKnockIntThr = 0U;

  /* SignalConversion generated from: '<S2>/KnockCntCyl' incorporates:
   *  Constant: '<S2>/Constant3'
   */
  KnockCntCyl = 0;

  /* Constant: '<S2>/Constant12' */
  IdVer_IonKnockState = ID_VER_IONKNOCKSTATE_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockState_PowerOn' */
}

/* Model initialize function */
void IonKnockState_initialize(void)
{
  {
    int32_T i;
    for (i = 0; i < 8; i++) {
      /* SystemInitialize for Merge: '<S3>/Merge' */
      KnockState[(i)] = NO_KNOCK;
    }
  }
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T KnockState[N_CYL_MAX];
uint8_T FlgHeavyKnock[N_CYL_MAX];
void IonKnockState_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    KnockState[idx] = 0u;
    FlgHeavyKnock[idx] = 0u;
  }
}

void IonKnockState_PowerOn(void)
{
  IonKnockState_Stub();
}

void IonKnockState_EOA(void)
{
  IonKnockState_Stub();
}

#endif                                 /* _BUILD_IONKNOCKSTATE_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_win_target                                                       *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/