/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB8C/Appl/trunk/tree/BIOS/PIT/Pit.c                 $  */
/* $Revision:: 185736                                                                                         $  */
/* $Date:: 2021-10-08 10:05:18 +0200 (ven, 08 ott 2021)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Pit
**  Filename        :  Pit.c
**  Created on      :  12-mag-2020 14:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef _BUILD_PIT_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Pit.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
uint32_T PIT_1ms_ISR_cnt = 0u;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
static uint16_T Pit0ch1_ISRCount=0u;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/

/******************************************************************************
**   Function    : PIT0_Ch0_ISR
**
**   Description:
**    PIT timer interrupt routine for PIT0 channel 0
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT0_CH0_ISR (void)
{
#if (PIT0_FUNCINT_0 == 1u)
    ActivateTask(PIT0_UserISR[PIT0_CH0]);
#endif

#ifdef _TEST_PIT
    Debug_PIT0_Core2_cnt++;
#endif
    PIT_0.TIMER[PIT0_CHANNEL0].TFLG.R = 1u;
}

/******************************************************************************
**   Function    : PIT0_CH1_ISR
**
**   Description:
**    PIT timer interrupt routine for PIT0 channel 1
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT0_CH1_ISR (void)
{
#if (PIT0_CH1_ENABLE == 1u)
#if (PIT0_FUNCINT_1 == 1u)
    ActivateTask(PIT0_UserISR[PIT0_CH1]);
#endif

    PIT_0.TIMER[PIT0_CHANNEL1].TFLG.R = 1u;
#endif

    Pit0ch1_ISRCount++;
}

/******************************************************************************
**   Function    : PIT0_CH2_ISR
**
**   Description:
**    PIT timer interrupt routine for PIT0 channel 2
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT0_CH2_ISR (void)
{
#if (PIT0_CH2_ENABLE == 1u)
#if (PIT0_FUNCINT_2 == 1u)
    ActivateTask(PIT0_UserISR[PIT0_CH2]);
#endif

    Debug_PIT0_Ch2_cnt++;

    PIT_0.TIMER[PIT0_CHANNEL2].TFLG.R = 1u;
#endif
}

/******************************************************************************
**   Function    : PIT0_CH3_ISR
**
**   Description:
**    PIT timer interrupt routine for PIT0 channel 3
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT0_CH3_ISR (void)
{
#if (PIT0_CH3_ENABLE == 1u)
#if (PIT0_FUNCINT_3 == 1u)
    ActivateTask(PIT0_UserISR[PIT0_CH3]);
#endif

    PIT_0.TIMER[PIT0_CHANNEL3].TFLG.R = 1u;
#endif
}

/******************************************************************************
**   Function    : PIT0_CH4_ISR
**
**   Description:
**    PIT timer interrupt routine for PIT0 channel 4
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT0_CH4_ISR (void)
{
#if (PIT0_CH4_ENABLE == 1u)
#if (PIT0_FUNCINT_4 == 1u)
    ActivateTask(PIT0_UserISR[PIT0_CH4]);
#endif

    PIT_0.TIMER[PIT0_CHANNEL4].TFLG.R = 1u;
#endif
}

/******************************************************************************
**   Function    : PIT0_CH5_ISR
**
**   Description:
**    PIT timer interrupt routine for PIT0 channel 5
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT0_CH5_ISR (void)
{
#if (PIT0_CH5_ENABLE == 1u)
#if (PIT0_FUNCINT_5 == 1u)
    ActivateTask(PIT0_UserISR[PIT0_CH5]);
#endif

    PIT_0.TIMER[PIT0_CHANNEL5].TFLG.R = 1u;
#endif
}

/******************************************************************************
**   Function    : PIT1_CH0_ISR
**
**   Description:
**    PIT timer interrupt routine for PIT1 channel 0
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT1_CH0_ISR (void)
{
#ifdef _BUILD_SAFETYMNGR_INTC_
    SafetyMngr_INTC_CheckCtx(PIT_INT0_PIT1_ISR_POS); // Interrupt no. 240
#endif

#if (PIT1_CH0_ENABLE == 1u)
#if (PIT1_FUNCINT_0 == 1u)
    /* Add call here */
#endif
#ifdef _TEST_PIT_   
    Debug_PIT1_Ch0_Core0_cnt++;
#endif

    PIT_1.TIMER[PIT1_CHANNEL0].TFLG.R = 1u;
#endif
}

/******************************************************************************
**   Function    : PIT1_CH1_ISR
**
**   Description:
**    PIT timer interrupt routine for PIT1 channel 1
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT1_CH1_ISR (void)
{
#if (PIT1_CH1_ENABLE == 1u)
#if (PIT1_FUNCINT_1 == 1u)
    /* Add call here */
#endif

    PIT_1.TIMER[PIT1_CHANNEL1].TFLG.R = 1u;
#endif
}

/******************************************************************************
**   Function    : PIT_1msTimebase_ISR
**
**   Description:
**    PIT timer interrupt used as base time for OS.
**  
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void PIT_1msTimebase_ISR(void)
{
#ifdef _OSEK_ 
    uint8_T  almIndex; /*index of alarm*/
    uint8_T  cntIndex; /*index of counter*/
    static uint8_T  timeRes[OSNUMCTRS]; /*starting value for counter bases*/
#endif

#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(PIT_INT0_PIT0_ISR_POS); // Interrupt no. 226

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (PIT_0.TIMER[PIT0_CHANNEL_OS_TIMEBASE].TFLG.R != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    /* clear interrupt flag */
    PIT_0.TIMER[PIT0_CHANNEL_OS_TIMEBASE].TFLG.R = 1u;

    /* Increment ISr counter by one */
    PIT_1ms_ISR_cnt ++;


#ifdef _OSEK_
    /*incrementing OsCounters  software counters*/
    for (cntIndex = 0u; cntIndex < OSNUMCTRS; cntIndex++)
    {
        if (timeRes[cntIndex] == (OsCounters[cntIndex].info.ticksperbase-1u))
        {
            (OsCounters[cntIndex].value) += TIMING_RESOLUTION;
            timeRes[cntIndex] = 0u;
        }
        else
        {
            timeRes[cntIndex] += 1u;
        }
    }


    /* matching counters with alarm expiring time*/
    for (almIndex = 0u; almIndex < OSNUMALMS; almIndex++)
    {
        for (cntIndex = 0u; cntIndex < OSNUMCTRS; cntIndex++)
        {
            if (OsAlmTable[almIndex].cntrId == OsCounters[cntIndex].CntrID)
            {
                if (OsCounters[cntIndex].value >= OsAlmTable[almIndex].delta)
                {
                    if (OsAlmTable[almIndex].cycle != 0u)
                    {
                        DisableAllInterrupts();
                        OsAlmTable[almIndex].delta += OsAlmTable[almIndex].cycle;
                        EnableAllInterrupts();
                    }
                    /*if matches, activate corresponding task*/
                    (*(OsAlmTable[almIndex].action))((void*)OsAlmTable[almIndex].TaskId);
                }
            }
        }
    }
#endif

}

 #endif //_BUILD_PIT_

/****************************************************************************
 ****************************************************************************/

