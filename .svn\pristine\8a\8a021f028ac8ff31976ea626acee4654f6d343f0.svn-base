/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_ADC_out.h
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Mocci A
******************************************************************************/
/*****************************************************************************
**
**                     SafetyMngr_ADC Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/


#ifndef SAFETYMNGR_ADC_OUT_H
#define SAFETYMNGR_ADC_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_ADC_BiasCheck
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_ADC_BiasCheck(void);

/******************************************************************************
**   Function    : SafetyMngr_ADC_Init
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_ADC_Init(void);

/******************************************************************************
**   Function    : SafetyMngr_ADC_SM_MCU_4_39
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_ADC_SM_MCU_4_39(void);

#endif // SAFETYMNGR_ADC_OUT_H

/****************************************************************************
 ****************************************************************************/
