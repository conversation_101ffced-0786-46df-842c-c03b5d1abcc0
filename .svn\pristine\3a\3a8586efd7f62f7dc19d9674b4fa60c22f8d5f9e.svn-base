/****************************************************************************
*
* Copyright © 2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
*
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_brc.h
 * @brief   SPC5xx GTM BRC header file.
 *
 * @addtogroup BRC
 * @{
 */

#ifndef _GTM_BRC_H_
#define _GTM_BRC_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"
#include "gtm_aru.h"

/*lint -e621*/
/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/
/**
 * @name    BRC definitions
 * @{
 */
 
/** Number of BRC IP */
#define SPC5_GTM_BRC_NUM                            1U
 /** Number of Input channels */
 #define SPC5_GTM_BRC_CHANNELS                     12U
 
/** Number of Output channels */
 #define SPC5_GTM_BRC_CHANNELS_DEST                22U
 
/** BRC SRC channel 0 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL0                 0U
/** BRC SRC channel 1 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL1                 1U
/** BRC SRC channel 2 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL2                 2U
/** BRC SRC channel 3 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL3                 3U
/** BRC SRC channel 4 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL4                 4U
/** BRC SRC channel 5 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL5                 5U
/** BRC SRC channel 6 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL6                 6U
/** BRC SRC channel 7 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL7                 7U
/** BRC SRC channel 8 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL8                 8U
/** BRC SRC channel 9 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL9                 9U
/** BRC SRC channel 10 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL10               10U
/** BRC SRC channel 11 identifier */
#define SPC5_GTM_BRC_INPUT_CHANNEL11               11U

/** BRC DEST0 channel identifier */
#define SPC5_GTM_BRC_EN_DEST0                       0U                           
/** BRC DEST1 channel identifier */
#define SPC5_GTM_BRC_EN_DEST1                       1U                           
/** BRC DEST2 channel identifier */
#define SPC5_GTM_BRC_EN_DEST2                       2U                           
/** BRC DEST3 channel identifier */
#define SPC5_GTM_BRC_EN_DEST3                       3U                           
/** BRC DEST4 channel identifier */
#define SPC5_GTM_BRC_EN_DEST4                       4U                           
/** BRC DEST5 channel identifier */
#define SPC5_GTM_BRC_EN_DEST5                       5U                           
/** BRC DEST6 channel identifier */
#define SPC5_GTM_BRC_EN_DEST6                       6U                           
/** BRC DEST7 channel identifier */
#define SPC5_GTM_BRC_EN_DEST7                       7U                           
/** BRC DEST8 channel identifier */
#define SPC5_GTM_BRC_EN_DEST8                       8U                           
/** BRC DEST9 channel identifier */
#define SPC5_GTM_BRC_EN_DEST9                       9U                           
/** BRC DEST10 channel identifier */
#define SPC5_GTM_BRC_EN_DEST10                     10U                           
/** BRC DEST11 channel identifier */
#define SPC5_GTM_BRC_EN_DEST11                     11U                           
/** BRC DEST12 channel identifier */
#define SPC5_GTM_BRC_EN_DEST12                     12U                           
/** BRC DEST13 channel identifier */
#define SPC5_GTM_BRC_EN_DEST13                     13U                           
/** BRC DEST14 channel identifier */
#define SPC5_GTM_BRC_EN_DEST14                     14U                           
/** BRC DEST15 channel identifier */
#define SPC5_GTM_BRC_EN_DEST15                     15U                           
/** BRC DEST16 channel identifier */
#define SPC5_GTM_BRC_EN_DEST16                     16U                           
/** BRC DEST17 channel identifier */
#define SPC5_GTM_BRC_EN_DEST17                     17U                           
/** BRC DEST18 channel identifier */
#define SPC5_GTM_BRC_EN_DEST18                     18U                           
/** BRC DEST19 channel identifier */
#define SPC5_GTM_BRC_EN_DEST19                     19U                           
/** BRC DEST20 channel identifier */
#define SPC5_GTM_BRC_EN_DEST20                     20U                           
/** BRC DEST21 channel identifier */
#define SPC5_GTM_BRC_EN_DEST21                     21U                           



/** BRC Source Consistency Mode */
#define SPC5_GTM_BRC_MODE_DCM                       0U
/** BRC Source Maximum Throughput Mode */
#define SPC5_GTM_BRC_MODE_MTM                       1U
 
 
/** BRC IRQ Mode Level */
#define SPC5_GTM_BRC_IRQ_MODE_LEVEL                 0x0U
/** BRC IRQ Mode Pulse */
#define SPC5_GTM_BRC_IRQ_MODE_PULSE                 0x1U
/** BRC IRQ Mode Pulse-Notify */
#define SPC5_GTM_BRC_IRQ_MODE_PULSE_NOTIFY          0x2U
/** BRC IRQ Mode Single-Pulse Mode */
#define SPC5_GTM_BRC_IRQ_MODE_SINGLE_PULSE          0x3U

/*
 * IRQ notify to ICM as Normal, Error or Both
 */
#define SPC5_GTM_BRC_INT_MODE_NORMAL                0x01U
#define SPC5_GTM_BRC_INT_MODE_ERROR                 0x02U
#define SPC5_GTM_BRC_INT_MODE_NORMAL_AND_ERROR      0x03U

/** BRC IRQ BRC Interrupt Notified */
#define SPC5_GTM_BRC_IRQ_STATUS_BRC                 1UL
#define SPC5_GTM_BRC_IRQ_ENABLE_BRC                 1U

 /** @} */
 
/*===========================================================================*/
/* Driver data structures and types.                                         */
/*===========================================================================*/

/**
 * @brief Type of a structure representing a (GTM-IP) BRC driver.
 */
typedef struct GTM_BRCDriver GTM_BRCDriver;

/**
 * @brief   (GTM-IP) BRC notification callback type.
 *
 * @param[in] brcd      pointer to the @p BRCDriver object triggering the callback
 * @param[in] channel   channel triggering the callback
 */
typedef void (*gtm_brc_callback_t)(GTM_BRCDriver *brcd, uint8_t channel);

/**
 * @brief   (GTM-IP) BRC notification callback type.
 *
 * @param[in] brcd      pointer to the @p BRCDriver object triggering the callback
 */
typedef void (*gtm_brc_cb_t)(GTM_BRCDriver *brcd);

/**
 * @brief Type of a structure representing a (GTM-IP) BRC channel context.
 */
typedef struct GTM_BRC_Channel_Callbacks GTM_BRC_Channel_Callbacks;

/**
 * @brief Type of a structure representing a (GTM-IP) BRC context.
 */
typedef struct GTM_BRC_Callbacks GTM_BRC_Callbacks;


/**
 * @brief   Structure representing an BRC Channel callbacks
 */
struct GTM_BRC_Channel_Callbacks {
	/**
	 * @brief Data inconsistency for a source channel when in MTM mode.
	 */
	gtm_brc_callback_t data_inconsistent;
};

/**
 * @brief   Structure representing an BRC callbacks
 */
struct GTM_BRC_Callbacks {
	/**
	 * @brief Data inconsistency for a source channel when in MTM mode.
	 */
	gtm_brc_cb_t plausibility;
};

/**
 * @brief   Structure representing a (GTM) BRC driver.
 */
struct GTM_BRCDriver {

  /**
   * @brief Pointer to the (GTM) BRC registers block.
   */
	volatile GTM_BRC_TAG *brc;

  /**
   *  @brief Interrupts brc callbacks.
   */
   GTM_BRC_Callbacks **brc_callbacks;

  /**
   * @brief Interrupts channel callbacks.
   */
	GTM_BRC_Channel_Callbacks **callbacks;


  /**
   * @brief Pointer for application private data.
   */
    void *priv;
};
 
 

 
  
/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_BRC == TRUE) && !defined(__DOXYGEN__)
extern GTM_BRCDriver BRCD1;
#endif

#ifdef __cplusplus
extern "C" {
#endif

extern void gtm_brcInit(void);
extern void gtm_brcStart(GTM_BRCDriver *brc);
extern void gtm_brcStop(GTM_BRCDriver *brc);
extern void gtm_brcSetSource(GTM_BRCDriver *brc, uint32_t channel, uint32_t address);
extern void gtm_brcSetMode(GTM_BRCDriver *brc, uint32_t channel, uint8_t mode);
extern uint32_t gtm_brcGetSource(GTM_BRCDriver *brc, uint32_t channel);
extern uint8_t gtm_brcGetMode(GTM_BRCDriver *brc, uint32_t channel);
extern void gtm_brcSetOutput(GTM_BRCDriver *brc, uint32_t dest, uint32_t source, uint8_t value);
extern uint32_t gtm_brcGetOutput(GTM_BRCDriver *brc, uint32_t dest);

extern uint32_t gtm_brcGetIntStatus(GTM_BRCDriver *brcd, uint8_t channel);
extern uint32_t gtm_brcGetIntEnabled(GTM_BRCDriver *brcd, uint8_t channel);
extern void gtm_brcAckInt(GTM_BRCDriver *brcd, uint8_t channel, uint8_t int_num);
extern void gtm_brcSetIRQMode(GTM_BRCDriver *brcd, uint8_t mode);
extern void gtm_brcEnableInt(GTM_BRCDriver *brcd, uint8_t channel, uint8_t int_num);
extern void gtm_brcDisableInt(GTM_BRCDriver *brcd, uint8_t channel, uint8_t int_num);
extern void gtm_brcNotifyInt(GTM_BRCDriver *brcd, uint8_t channel, uint8_t int_num);
extern uint8_t gtm_brcGetPlausibilityIntStatus(GTM_BRCDriver *brcd);
extern uint8_t gtm_brcGetPlausibilityIntEnabled(GTM_BRCDriver *brcd);
extern void gtm_brcAckPlausibilityInt(GTM_BRCDriver *brcd, uint8_t int_num);
extern void gtm_brcEnablePlausibilityInt(GTM_BRCDriver *brcd);
extern void gtm_brcDisablePlausibilityInt(GTM_BRCDriver *brcd);
#ifdef __cplusplus
}
#endif

/*lint +e621*/
#endif /* _GTM_BRC_H_ */
/** @} */
 
