/*****************************************************************************************************************/
/* $HeadURL:: https://172.26.1.29/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_RS_35_PI_0204/tree/DD/CANMGMO#$  */
/* $Revision:: 139769                                                                                         $  */
/* $Date:: 2020-12-02 09:37:47 +0100 (mer, 02 dic 2020)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmOut
**  Filename        :  CanMgmOut_BR_calib.c
**  Created on      :  07-jul-2023 09:37:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_CANMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "CanMgmOut_BR.h"

#pragma ghs section rodata=".calib"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
// OFFSET SAKNOCK SU CAN
CALQUAL CALQUAL_POST int16_T    OFFSAKNOCKCAN = 360;  // 22.5 * 16

//Force the correction for knocking on the spark advance (=1) [flag]
CALQUAL CALQUAL_POST int16_T VTCANSAKNOCK[N_CYL_MAX] = 
{
    1440, 1440,  1440,  1440,  1440,  1440,  1440,  1440
};

//Enable Add SARON
CALQUAL CALQUAL_POST uint8_T ENADDSARON = 0u;

/* Force StMisf period */
CALQUAL CALQUAL_POST uint16_T VTSTMISFFORCEPERIOD[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
/* Force StMisf length */
CALQUAL CALQUAL_POST uint16_T VTSTMISFFORCEDLENGTH[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
/* Force StMisf error */
CALQUAL CALQUAL_POST uint8_T VTSTMISFFORCEDTYPE[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};

// Maximum delta Spark time
CALQUAL CALQUAL_POST uint32_T MAXDELTASPARKTIME = 50u;

CALQUAL CALQUAL_POST uint8_T FORCEIGNFAULT[N_CYL_MAX] = 
{
    0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u
};

CALQUAL CALQUAL_POST uint8_T FORCEDIGNFAULT[N_CYL_MAX] = 
{
    0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u
};

CALQUAL CALQUAL_POST uint8_T ENGPFINFO = 1u;

CALQUAL CALQUAL_POST uint8_T ION2SCANTOOLDEBCLEAR = 100u;
CALQUAL CALQUAL_POST uint8_T ION2SCANTOOLDEBSET = 0u;

CALQUAL CALQUAL_POST uint8_T FLASHORDTCERASEDEB = 5u;

CALQUAL CALQUAL_POST uint8_T ENKNOCKANALYSIS = 0u;

CALQUAL CALQUAL_POST uint8_T FORCEINJCORR = 0u;

CALQUAL CALQUAL_POST uint8_T VTFORCEINJCORR[N_CYL_MAX] = {128u, 128u, 128u, 128u, 128u, 128u, 128u, 128u};

CALQUAL CALQUAL_POST uint8_T INJINVCANEN = 1u;

#endif
/****************************************************************************
 ****************************************************************************/

