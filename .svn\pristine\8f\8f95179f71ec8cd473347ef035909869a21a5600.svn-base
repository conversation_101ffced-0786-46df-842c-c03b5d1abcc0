/*****************************************************************************************************************/
/* $HeadURL::                                                                                                              $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MSparkCmd
**  Filename        :  MSparkCmd_calib.c
**  Created on      :  30-mar-2021 12:01:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_MSPARKCMD_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "msparkcmd.h"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
#pragma ghs section rodata=".calib"
CALQUAL CALQUAL_POST uint8_T GNMINEFFTTM = (uint8_T)(0.7f * 128.0f);

CALQUAL CALQUAL_POST uint8_T GNMAXEFFTTM = (uint8_T)(1.3f * 128.0f);

CALQUAL CALQUAL_POST uint16_T THRILPOLDIAG = 655u; //(2.0006911999999990000*327.6755)

CALQUAL CALQUAL_POST uint8_T ENIONPHASEMISF = 0u;

CALQUAL CALQUAL_POST uint16_T THRILPSPDIAG = 3276u; //(10.0006911999999990000*327.6755)

CALQUAL CALQUAL_POST uint16_T THRINTSTARTFOUND = 50u;

CALQUAL CALQUAL_POST int16_T THRPEAKISEC = (int16_T)((2750.0f * 32768.0f) / 5000.0f); /* 2750mV */

CALQUAL CALQUAL_POST uint8_T EPWSMISFEN = 0u;

CALQUAL CALQUAL_POST int16_T TIMFIXEDTRIGIN = 2500;

CALQUAL CALQUAL_POST uint8_T THRCNTSPEVNT = (N_CYLINDER);

//CmdIn level in off state
CALQUAL CALQUAL_POST uint8_T CMDINLEVELOFF = 1u;

// CmdIn level Error counter SACmdInLevErrNo threshold
CALQUAL CALQUAL_POST uint8_T SACMDINLEVERRNOTHR = 1u;

// CmdIn level Error Test enable flag
CALQUAL CALQUAL_POST uint8_T SACMDINLEVERRTESTEN = 0u;

///History buffer of CmdIn level Error (circular bitmask buffer)
CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF0 = 0u;

///History buffer of CmdIn level Error (circular bitmask buffer)
CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF1 = 0u;

///History buffer of CmdIn level Error (circular bitmask buffer)
CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF2 = 0u;

///History buffer of CmdIn level Error (circular bitmask buffer)
CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF3 = 0u;

///History buffer of CmdIn level Error (circular bitmask buffer)
CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF4 = 0u;

///History buffer of CmdIn level Error (circular bitmask buffer)
CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF5 = 0u;

///History buffer of CmdIn level Error (circular bitmask buffer)
CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF6 = 0u;

///History buffer of CmdIn level Error (circular bitmask buffer)
CALQUAL CALQUAL_POST uint16_T SACMDINERRCIRCULARBUF7 = 0u;

///Number of cyles to be observed 
CALQUAL CALQUAL_POST uint8_T SACMDINLEVERRCYLES = 2u;

#endif // _BUILD_MSPARKCMD_
/****************************************************************************
 ****************************************************************************/
 
