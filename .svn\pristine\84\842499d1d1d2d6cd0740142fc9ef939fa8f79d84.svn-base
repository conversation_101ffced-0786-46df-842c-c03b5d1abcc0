/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           RonDetectCross.c
 **  File Creation Date: 29-Mar-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         RonDetectCross
 **  Model Description:
 **  Model Version:      1.955
 **  Model Author:       MarottaR - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: MarottaR - Tue Mar 29 10:17:51 2022
 **
 **  Last Saved Modification:  MarottaR - Tue Mar 29 10:16:39 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "RonDetectCross_out.h"
#include "RonDetectCross_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<S4>/Cross_Check' */
#define RonDetectCros_IN_RD_CC_DISABLED ((uint8_T)0U)
#define RonDetectCross_IN_NO_RE_FUEL   ((uint8_T)1U)
#define RonDetectCross_IN_RE_FUEL      ((uint8_T)2U)

/* Named constants for Chart: '<Root>/RonDetectCross_Scheduler' */
#define Ro_event_RonDetectCross_PowerOn (0)
#define RonDet_event_RonDetectCross_5ms (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_RONDETECTCROSS_DEF      1955U                     /* Referenced by: '<Root>/RonDetectCross_Scheduler' */

/* ID model version define */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_RONDETECTCROSS_

/* Add a check for each important define */
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint8_T RonDetectCross_is_Cross_Check;/* '<S4>/Cross_Check' */

/* State for cross check */
static uint16_T cnt;                   /* '<S4>/Cross_Check' */

/* Counter for cross check */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENRONCCHECK = 0U;/* Referenced by: '<S4>/ENRONCCHECK' */

/* Enable RON cross-check strategy */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXCNTINHERIT = 5100U;/* Referenced by: '<S4>/MAXCNTINHERIT' */

/* Timeout to inherit RonLevel from other ECU. Set the number of samples considering that the trigger time is 5ms. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXCNTINHERITWAIT = 40000U;
                                   /* Referenced by: '<S4>/MAXCNTINHERITWAIT' */

/* Max value to wait for ron level inherit. Set the number of samples considering that the trigger time is 5ms. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T MINCNTRONRUNWAIT = 3U;
                                    /* Referenced by: '<S4>/MINCNTRONRUNWAIT' */

/* Min CntRonRun value to wait for ron level inherit */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T RonLevelCross;                 /* '<S2>/MRonLevelCross' */
enum_StReFuel StReFuel; 
/* RON level stored in EE */
enum_StRonCCheck StRonCCheck;          /* '<S2>/MStRonCCheck' */

/* RonDetect cross-check state */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_RonDetectCross;/* '<Root>/RonDetectCross_Scheduler' */

/* ID model version */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void Ron_chartstep_c3_RonDetectCross(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/RonDetectCross_Scheduler' */
static void Ron_chartstep_c3_RonDetectCross(const int32_T *sfEvent)
{
  boolean_T guard1 = false;
  boolean_T guard2 = false;
  boolean_T guard3 = false;

  /* Chart: '<Root>/RonDetectCross_Scheduler'
   *
   * Block description for '<Root>/RonDetectCross_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  /* Chart: '<Root>/RonDetectCross_Scheduler'
   *
   * Block description for '<Root>/RonDetectCross_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  /* During: RonDetectCross_Scheduler */
  /* Entry Internal: RonDetectCross_Scheduler */
  /* Transition: '<S3>:2' */
  if ((*sfEvent) == ((int32_T)Ro_event_RonDetectCross_PowerOn)) {
    /* Outputs for Function Call SubSystem: '<Root>/Init_fcn'
     *
     * Block description for '<Root>/Init_fcn':
     *  This block performs outputs initialization.
     */
    /* SignalConversion generated from: '<S1>/RonLevelCross' incorporates:
     *  Constant: '<S1>/Constant'
     */
    /* Transition: '<S3>:5' */
    /* Transition: '<S3>:3'
     * Requirements for Transition: '<S3>:3':
     *  1. EISB_FCA6CYL_SW_REQ_2730: At PowerOn event software shall set RonLevelCross, StRonCCheck and FlgRonInheritEE equal to 0. (ECU_SW_Requirements#12268)
     */
    /* Event: '<S3>:19' */
    RonLevelCross = 0U;

    /* SignalConversion generated from: '<S1>/StRonCCheck' incorporates:
     *  Constant: '<S1>/Constant1'
     */
    StRonCCheck = RD_CC_DISABLED;

    /* End of Outputs for SubSystem: '<Root>/Init_fcn' */
    IdVer_RonDetectCross = ID_VER_RONDETECTCROSS_DEF;

    /* Transition: '<S3>:10' */
  } else {
    /* Outputs for Function Call SubSystem: '<Root>/T5ms_fcn'
     *
     * Block description for '<Root>/T5ms_fcn':
     *  This block performs the cross check in case of two ECUs.
     */
    /* SignalConversion generated from: '<S4>/RonLevelCross' incorporates:
     *  Chart: '<S4>/Cross_Check'
     *  Inport: '<Root>/RonLevelEE'
     *
     * Block description for '<S4>/Cross_Check':
     *  This block implements the cross check in case of two ECUs.
     */
    /* Transition: '<S3>:8' */
    /* RonDetectCross_5ms */
    /* Transition: '<S3>:6' */
    /* Event: '<S3>:18' */
    /* Gateway: T5ms_fcn/Cross_Check */
    /* During: T5ms_fcn/Cross_Check */
    /* Entry Internal: T5ms_fcn/Cross_Check */
    /* Transition: '<S5>:2' */
    RonLevelCross = RonLevelEE;

    /* Chart: '<S4>/Cross_Check' incorporates:
     *  Constant: '<S4>/ENRONCCHECK'
     *  Constant: '<S4>/MAXCNTINHERITWAIT'
     *  Constant: '<S4>/MINCNTRONRUNWAIT'
     *  Inport: '<Root>/CntRonRun'
     *  Inport: '<Root>/EnRonDetect'
     *  Inport: '<Root>/EnRonDetectEE'
     *  Inport: '<Root>/FlgRonStoredEE'
     *  Inport: '<Root>/FlgRonStoredIn'
     *  Inport: '<Root>/RonLevelEE'
     *  Inport: '<Root>/RonLevelIn'
     *  SignalConversion generated from: '<S4>/StRonCCheck_old'
     *
     * Block description for '<S4>/Cross_Check':
     *  This block implements the cross check in case of two ECUs.
     */
    if ((((int32_T)EnRonDetect) == 0) || (((int32_T)EnRonDetectEE) == 0)) {
      /* Transition: '<S5>:5' */
      /* Transition: '<S5>:21'
       * Requirements for Transition: '<S5>:21':
       *  1. EISB_FCA6CYL_SW_REQ_2731: Software shall set a signal (i.e. StRonCCheck) according to the st... (ECU_SW_Requirements#12269)
       */
      RonDetectCross_is_Cross_Check = RonDetectCros_IN_RD_CC_DISABLED;
      StRonCCheck = RD_CC_DISABLED;

      /* Transition: '<S5>:23' */
      /* Transition: '<S5>:166' */
      /* Transition: '<S5>:167' */
      /* Transition: '<S5>:77' */
      /* Transition: '<S5>:78' */
      /* Transition: '<S5>:79' */
      /* Transition: '<S5>:81' */
      /* Transition: '<S5>:99' */
      /* Transition: '<S5>:109' */
      /* Transition: '<S5>:126' */
      /* Transition: '<S5>:128' */
      /* Transition: '<S5>:133' */
      /* Transition: '<S5>:135' */
      /* Transition: '<S5>:172' */
      /* Transition: '<S5>:197' */
      /* Transition: '<S5>:204' */
      /* Transition: '<S5>:227' */
      /* Transition: '<S5>:230' */
      /* Transition: '<S5>:231' */
      /* Transition: '<S5>:233' */
      /* Transition: '<S5>:247' */
      /* Transition: '<S5>:254' */
      /* Transition: '<S5>:255' */
      /* Transition: '<S5>:256' */
    } else {
      /* Transition: '<S5>:145' */
      guard1 = false;
      guard2 = false;
      guard3 = false;
      if (((uint32_T)RonDetectCross_is_Cross_Check) ==
          RonDetectCross_IN_NO_RE_FUEL) {
        /* Transition: '<S5>:147' */
        if (((((int32_T)ENRONCCHECK) == 0) || (((int32_T)RonLevelIn) ==
              ((int32_T)0xFF))) || (((int32_T)FlgRonStoredIn) == ((int32_T)0xFF)))
        {
          /* Transition: '<S5>:151' */
          /* Transition: '<S5>:155'
           * Requirements for Transition: '<S5>:155':
           *  1. EISB_FCA6CYL_SW_REQ_2733: In NO_RE_FUEL mode if the calibration ENRONCCHECK is equal to 0 or... (ECU_SW_Requirements#12273)
           */
          RonDetectCross_is_Cross_Check = RonDetectCros_IN_RD_CC_DISABLED;
          StRonCCheck = RD_CC_DISABLED;

          /* Transition: '<S5>:166' */
          /* Transition: '<S5>:167' */
          /* Transition: '<S5>:77' */
          /* Transition: '<S5>:78' */
          /* Transition: '<S5>:79' */
          /* Transition: '<S5>:81' */
          /* Transition: '<S5>:99' */
          /* Transition: '<S5>:109' */
          /* Transition: '<S5>:126' */
          /* Transition: '<S5>:128' */
          /* Transition: '<S5>:133' */
          /* Transition: '<S5>:135' */
          /* Transition: '<S5>:172' */
          /* Transition: '<S5>:197' */
          /* Transition: '<S5>:204' */
          /* Transition: '<S5>:227' */
          /* Transition: '<S5>:230' */
          /* Transition: '<S5>:231' */
          /* Transition: '<S5>:233' */
          /* Transition: '<S5>:247' */
          /* Transition: '<S5>:254' */
          /* Transition: '<S5>:255' */
          /* Transition: '<S5>:256' */
        } else {
          /* Transition: '<S5>:153' */
          if (((uint32_T)StReFuel) == RD_FUEL_INC) {
            /* Transition: '<S5>:158' */
            /* Transition: '<S5>:162'
             * Requirements for Transition: '<S5>:162':
             *  1. EISB_FCA6CYL_SW_REQ_2734: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12274)
             */
            FlgRonInheritEE = 0U;
            cnt = 0U;
            RonDetectCross_is_Cross_Check = RonDetectCross_IN_RE_FUEL;
            StRonCCheck = RD_CC_SEARCH;

            /* Transition: '<S5>:167' */
            /* Transition: '<S5>:77' */
            /* Transition: '<S5>:78' */
            /* Transition: '<S5>:79' */
            /* Transition: '<S5>:81' */
            /* Transition: '<S5>:99' */
            /* Transition: '<S5>:109' */
            /* Transition: '<S5>:126' */
            /* Transition: '<S5>:128' */
            /* Transition: '<S5>:133' */
            /* Transition: '<S5>:135' */
            /* Transition: '<S5>:172' */
            /* Transition: '<S5>:197' */
            /* Transition: '<S5>:204' */
            /* Transition: '<S5>:227' */
            /* Transition: '<S5>:230' */
            /* Transition: '<S5>:231' */
            /* Transition: '<S5>:233' */
            /* Transition: '<S5>:247' */
            /* Transition: '<S5>:254' */
            /* Transition: '<S5>:255' */
            /* Transition: '<S5>:256' */
          } else {
            /* Transition: '<S5>:160' */
            if (((uint32_T)StRonCCheck) == RD_CC_NOTSTORED) {
              /* Transition: '<S5>:40' */
              if (((int32_T)FlgRonInheritEE) != 0) {
                /* Transition: '<S5>:44' */
                /* Transition: '<S5>:49' */
                guard2 = true;
              } else {
                /* Transition: '<S5>:46' */
                if (cnt >= MAXCNTINHERIT) {
                  /* Transition: '<S5>:53' */
                  if ((((int32_T)FlgRonStoredIn) != 0) && (RonLevelIn >
                       RonLevelEE)) {
                    /* Transition: '<S5>:58' */
                    if (CntRonRun < MINCNTRONRUNWAIT) {
                      /* Transition: '<S5>:62'
                       * Requirements for Transition: '<S5>:62':
                       *  1. EISB_FCA6CYL_SW_REQ_2736: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12276)
                       */
                      guard2 = true;
                    } else {
                      /* Transition: '<S5>:64' */
                      cnt = 0U;

                      /* Transition: '<S5>:76'
                       * Requirements for Transition: '<S5>:76':
                       *  1. EISB_FCA6CYL_SW_REQ_2737: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12277)
                       */
                      StRonCCheck = RD_CC_WAIT_STOREDIN;

                      /* Transition: '<S5>:79' */
                      /* Transition: '<S5>:81' */
                      /* Transition: '<S5>:99' */
                      /* Transition: '<S5>:109' */
                      /* Transition: '<S5>:126' */
                      /* Transition: '<S5>:128' */
                      /* Transition: '<S5>:133' */
                      /* Transition: '<S5>:135' */
                      /* Transition: '<S5>:172' */
                      /* Transition: '<S5>:197' */
                      /* Transition: '<S5>:204' */
                      /* Transition: '<S5>:227' */
                      /* Transition: '<S5>:230' */
                      /* Transition: '<S5>:231' */
                      /* Transition: '<S5>:233' */
                      /* Transition: '<S5>:247' */
                      /* Transition: '<S5>:254' */
                      /* Transition: '<S5>:255' */
                      /* Transition: '<S5>:256' */
                    }
                  } else {
                    /* Transition: '<S5>:60' */
                    guard1 = true;
                  }
                } else {
                  /* Transition: '<S5>:55'
                   * Requirements for Transition: '<S5>:55':
                   *  1. EISB_FCA6CYL_SW_REQ_2739: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12279)
                   */
                  cnt = (uint16_T)((int32_T)(((int32_T)cnt) + 1));
                  guard1 = true;
                }
              }
            } else {
              /* Transition: '<S5>:42' */
              switch (StRonCCheck) {
               case RD_CC_STORED:
                /* Transition: '<S5>:87' */
                if ((((int32_T)FlgRonStoredIn) != 0) && (RonLevelIn > RonLevelEE))
                {
                  /* Transition: '<S5>:91' */
                  /* Transition: '<S5>:95'
                   * Requirements for Transition: '<S5>:95':
                   *  1. EISB_FCA6CYL_SW_REQ_2740: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12280)
                   */
                  StRonCCheck = RD_CC_STOREDIN;
                  FlgRonInheritEE = 1U;

                  /* SignalConversion generated from: '<S4>/RonLevelCross' */
                  RonLevelCross = RonLevelIn;
                } else {
                  /* Transition: '<S5>:93' */
                  /* Transition: '<S5>:96' */
                }

                /* Transition: '<S5>:97' */
                /* Transition: '<S5>:99' */
                /* Transition: '<S5>:109' */
                /* Transition: '<S5>:126' */
                /* Transition: '<S5>:128' */
                /* Transition: '<S5>:133' */
                /* Transition: '<S5>:135' */
                /* Transition: '<S5>:172' */
                /* Transition: '<S5>:197' */
                /* Transition: '<S5>:204' */
                /* Transition: '<S5>:227' */
                /* Transition: '<S5>:230' */
                /* Transition: '<S5>:231' */
                /* Transition: '<S5>:233' */
                /* Transition: '<S5>:247' */
                /* Transition: '<S5>:254' */
                /* Transition: '<S5>:255' */
                /* Transition: '<S5>:256' */
                break;

               case RD_CC_STOREDIN:
                /* SignalConversion generated from: '<S4>/RonLevelCross' incorporates:
                 *  Inport: '<Root>/RonLevelIn'
                 */
                /* Transition: '<S5>:89' */
                /* Transition: '<S5>:103' */
                /* Transition: '<S5>:107'
                 * Requirements for Transition: '<S5>:107':
                 *  1. EISB_FCA6CYL_SW_REQ_2741: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12281)
                 */
                RonLevelCross = RonLevelIn;

                /* Transition: '<S5>:109' */
                /* Transition: '<S5>:126' */
                /* Transition: '<S5>:128' */
                /* Transition: '<S5>:133' */
                /* Transition: '<S5>:135' */
                /* Transition: '<S5>:172' */
                /* Transition: '<S5>:197' */
                /* Transition: '<S5>:204' */
                /* Transition: '<S5>:227' */
                /* Transition: '<S5>:230' */
                /* Transition: '<S5>:231' */
                /* Transition: '<S5>:233' */
                /* Transition: '<S5>:247' */
                /* Transition: '<S5>:254' */
                /* Transition: '<S5>:255' */
                /* Transition: '<S5>:256' */
                break;

               default:
                /* Transition: '<S5>:105' */
                /* Transition: '<S5>:111' */
                /* StRonCCheck = RD_CC_WAIT_STOREDIN */
                if (((int32_T)FlgRonStoredEE) != 0) {
                  /* Transition: '<S5>:113' */
                  /* Transition: '<S5>:130'
                   * Requirements for Transition: '<S5>:130':
                   *  1. EISB_FCA6CYL_SW_REQ_2742: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12282)
                   */
                  StRonCCheck = RD_CC_STORED;

                  /* Transition: '<S5>:135' */
                  /* Transition: '<S5>:172' */
                  /* Transition: '<S5>:197' */
                  /* Transition: '<S5>:204' */
                  /* Transition: '<S5>:227' */
                  /* Transition: '<S5>:230' */
                  /* Transition: '<S5>:231' */
                  /* Transition: '<S5>:233' */
                  /* Transition: '<S5>:247' */
                  /* Transition: '<S5>:254' */
                  /* Transition: '<S5>:255' */
                  /* Transition: '<S5>:256' */
                } else {
                  /* Transition: '<S5>:115' */
                  cnt = (uint16_T)((int32_T)(((int32_T)cnt) + 1));
                  if (cnt >= MAXCNTINHERITWAIT) {
                    /* Transition: '<S5>:117' */
                    cnt = MAXCNTINHERITWAIT;
                    if ((((int32_T)FlgRonStoredIn) != 0) && (RonLevelIn >
                         RonLevelEE)) {
                      /* Transition: '<S5>:121' */
                      /* Transition: '<S5>:132'
                       * Requirements for Transition: '<S5>:132':
                       *  1. EISB_FCA6CYL_SW_REQ_2743: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12283)
                       */
                      StRonCCheck = RD_CC_STOREDIN;
                      FlgRonInheritEE = 1U;

                      /* SignalConversion generated from: '<S4>/RonLevelCross' */
                      RonLevelCross = RonLevelIn;

                      /* Transition: '<S5>:133' */
                      /* Transition: '<S5>:135' */
                      /* Transition: '<S5>:172' */
                      /* Transition: '<S5>:197' */
                      /* Transition: '<S5>:204' */
                      /* Transition: '<S5>:227' */
                      /* Transition: '<S5>:230' */
                      /* Transition: '<S5>:231' */
                      /* Transition: '<S5>:233' */
                      /* Transition: '<S5>:247' */
                      /* Transition: '<S5>:254' */
                      /* Transition: '<S5>:255' */
                      /* Transition: '<S5>:256' */
                    } else {
                      /* Transition: '<S5>:125' */
                      /* Transition: '<S5>:128' */
                      /* Transition: '<S5>:133' */
                      /* Transition: '<S5>:135' */
                      /* Transition: '<S5>:172' */
                      /* Transition: '<S5>:197' */
                      /* Transition: '<S5>:204' */
                      /* Transition: '<S5>:227' */
                      /* Transition: '<S5>:230' */
                      /* Transition: '<S5>:231' */
                      /* Transition: '<S5>:233' */
                      /* Transition: '<S5>:247' */
                      /* Transition: '<S5>:254' */
                      /* Transition: '<S5>:255' */
                      /* Transition: '<S5>:256' */
                    }
                  } else {
                    /* Transition: '<S5>:119' */
                    /* Transition: '<S5>:126' */
                    /* Transition: '<S5>:128' */
                    /* Transition: '<S5>:133' */
                    /* Transition: '<S5>:135' */
                    /* Transition: '<S5>:172' */
                    /* Transition: '<S5>:197' */
                    /* Transition: '<S5>:204' */
                    /* Transition: '<S5>:227' */
                    /* Transition: '<S5>:230' */
                    /* Transition: '<S5>:231' */
                    /* Transition: '<S5>:233' */
                    /* Transition: '<S5>:247' */
                    /* Transition: '<S5>:254' */
                    /* Transition: '<S5>:255' */
                    /* Transition: '<S5>:256' */
                  }
                }
                break;
              }
            }
          }
        }
      } else {
        /* Transition: '<S5>:169' */
        if (((uint32_T)RonDetectCross_is_Cross_Check) ==
            RonDetectCros_IN_RD_CC_DISABLED) {
          /* Transition: '<S5>:25' */
          if (((((int32_T)ENRONCCHECK) != 0) && (((int32_T)RonLevelIn) !=
                ((int32_T)0xFF))) && (((int32_T)FlgRonStoredIn) != ((int32_T)
                0xFF))) {
            /* Transition: '<S5>:29' */
            cnt = 0U;

            /* Transition: '<S5>:33'
             * Requirements for Transition: '<S5>:33':
             *  1. EISB_FCA6CYL_SW_REQ_2732: In DISABLED mode if the calibration ENRONCCHECK is not equal to 0 ... (ECU_SW_Requirements#12271)
             */
            RonDetectCross_is_Cross_Check = RonDetectCross_IN_NO_RE_FUEL;
            StRonCCheck = RD_CC_NOTSTORED;
          } else {
            /* Transition: '<S5>:31' */
            /* Transition: '<S5>:34' */
          }

          /* Transition: '<S5>:35' */
          /* Transition: '<S5>:172' */
          /* Transition: '<S5>:197' */
          /* Transition: '<S5>:204' */
          /* Transition: '<S5>:227' */
          /* Transition: '<S5>:230' */
          /* Transition: '<S5>:231' */
          /* Transition: '<S5>:233' */
          /* Transition: '<S5>:247' */
          /* Transition: '<S5>:254' */
          /* Transition: '<S5>:255' */
          /* Transition: '<S5>:256' */
        } else {
          /* Transition: '<S5>:170' */
          /* Transition: '<S5>:174' */
          /* RonDetectCross_is_Cross_Check = IN_RE_FUEL */
          if (((((int32_T)ENRONCCHECK) == 0) || (((int32_T)RonLevelIn) ==
                ((int32_T)0xFF))) || (((int32_T)FlgRonStoredIn) == ((int32_T)
                0xFF))) {
            /* Transition: '<S5>:176' */
            /* Transition: '<S5>:180'
             * Requirements for Transition: '<S5>:180':
             *  1. EISB_FCA6CYL_SW_REQ_2750: In RE_FUEL mode if the calibration ENRONCCHECK is equal to 0 or th... (ECU_SW_Requirements#12285)
             */
            RonDetectCross_is_Cross_Check = RonDetectCros_IN_RD_CC_DISABLED;
            StRonCCheck = RD_CC_DISABLED;

            /* Transition: '<S5>:197' */
            /* Transition: '<S5>:204' */
            /* Transition: '<S5>:227' */
            /* Transition: '<S5>:230' */
            /* Transition: '<S5>:231' */
            /* Transition: '<S5>:233' */
            /* Transition: '<S5>:247' */
            /* Transition: '<S5>:254' */
            /* Transition: '<S5>:255' */
            /* Transition: '<S5>:256' */
          } else {
            /* Transition: '<S5>:178' */
            if (((uint32_T)StRonCCheck) == RD_CC_FOUND) {
              /* Transition: '<S5>:185' */
              if ((((int32_T)FlgRonStoredIn) != 0) && (RonLevelIn > RonLevelEE))
              {
                /* Transition: '<S5>:189' */
                /* Transition: '<S5>:193'
                 * Requirements for Transition: '<S5>:193':
                 *  1. EISB_FCA6CYL_SW_REQ_2751: In RE_FUEL mode if the calibration ENRONCCHECK is not equal to 0 a... (ECU_SW_Requirements#12286)
                 */
                StRonCCheck = RD_CC_FOUNDIN;
                FlgRonInheritEE = 1U;

                /* SignalConversion generated from: '<S4>/RonLevelCross' */
                RonLevelCross = RonLevelIn;
              } else {
                /* Transition: '<S5>:191' */
                /* Transition: '<S5>:194' */
              }

              /* Transition: '<S5>:196' */
              /* Transition: '<S5>:204' */
              /* Transition: '<S5>:227' */
              /* Transition: '<S5>:230' */
              /* Transition: '<S5>:231' */
              /* Transition: '<S5>:233' */
              /* Transition: '<S5>:247' */
              /* Transition: '<S5>:254' */
              /* Transition: '<S5>:255' */
              /* Transition: '<S5>:256' */
            } else {
              /* Transition: '<S5>:187' */
              if (((uint32_T)StRonCCheck) == RD_CC_FOUNDIN) {
                /* SignalConversion generated from: '<S4>/RonLevelCross' incorporates:
                 *  Inport: '<Root>/RonLevelIn'
                 */
                /* Transition: '<S5>:199' */
                /* Transition: '<S5>:203'
                 * Requirements for Transition: '<S5>:203':
                 *  1. EISB_FCA6CYL_SW_REQ_2752: In RE_FUEL mode if the calibration ENRONCCHECK is not equal to 0 a... (ECU_SW_Requirements#12287)
                 */
                RonLevelCross = RonLevelIn;

                /* Transition: '<S5>:227' */
                /* Transition: '<S5>:230' */
                /* Transition: '<S5>:231' */
                /* Transition: '<S5>:233' */
                /* Transition: '<S5>:247' */
                /* Transition: '<S5>:254' */
                /* Transition: '<S5>:255' */
                /* Transition: '<S5>:256' */
              } else {
                /* Transition: '<S5>:201' */
                if (((uint32_T)StRonCCheck) == RD_CC_SEARCH) {
                  /* Transition: '<S5>:206' */
                  if (cnt >= MAXCNTINHERIT) {
                    /* Transition: '<S5>:210' */
                    if ((((int32_T)FlgRonStoredIn) != 0) && (RonLevelIn >
                         RonLevelEE)) {
                      /* Transition: '<S5>:214' */
                      if (CntRonRun < MINCNTRONRUNWAIT) {
                        /* Transition: '<S5>:218' */
                        /* Transition: '<S5>:222'
                         * Requirements for Transition: '<S5>:222':
                         *  1. EISB_FCA6CYL_SW_REQ_2753: In RE_FUEL mode if the calibration ENRONCCHECK is not equal to 0 a... (ECU_SW_Requirements#12288)
                         */
                        StRonCCheck = RD_CC_FOUNDIN;
                        FlgRonInheritEE = 1U;

                        /* SignalConversion generated from: '<S4>/RonLevelCross' incorporates:
                         *  Inport: '<Root>/RonLevelIn'
                         */
                        RonLevelCross = RonLevelIn;

                        /* Transition: '<S5>:247' */
                        /* Transition: '<S5>:254' */
                        /* Transition: '<S5>:255' */
                        /* Transition: '<S5>:256' */
                      } else {
                        /* Transition: '<S5>:220' */
                        cnt = 0U;

                        /* Transition: '<S5>:234'
                         * Requirements for Transition: '<S5>:234':
                         *  1. EISB_FCA6CYL_SW_REQ_2754: In RE_FUEL mode if the calibration ENRONCCHECK is not equal to 0 a... (ECU_SW_Requirements#12289)
                         */
                        StRonCCheck = RD_CC_WAIT_FOUNDIN;

                        /* Transition: '<S5>:233' */
                        /* Transition: '<S5>:247' */
                        /* Transition: '<S5>:254' */
                        /* Transition: '<S5>:255' */
                        /* Transition: '<S5>:256' */
                      }
                    } else {
                      /* Transition: '<S5>:216' */
                      guard3 = true;
                    }
                  } else {
                    /* Transition: '<S5>:212'
                     * Requirements for Transition: '<S5>:212':
                     *  1. EISB_FCA6CYL_SW_REQ_2756: In RE_FUEL mode if the calibration ENRONCCHECK is not equal to 0 a... (ECU_SW_Requirements#12291)
                     */
                    cnt = (uint16_T)((int32_T)(((int32_T)cnt) + 1));
                    guard3 = true;
                  }
                } else {
                  /* Transition: '<S5>:208' */
                  /* Transition: '<S5>:236' */
                  /* StRonCCheck = RD_CC_WAIT_FOUNDIN */
                  if (((int32_T)FlgRonStoredEE) != 0) {
                    /* Transition: '<S5>:238' */
                    /* Transition: '<S5>:242'
                     * Requirements for Transition: '<S5>:242':
                     *  1. EISB_FCA6CYL_SW_REQ_2757: In RE_FUEL mode if the calibration ENRONCCHECK is not equal to 0 a... (ECU_SW_Requirements#12292)
                     */
                    StRonCCheck = RD_CC_FOUND;
                  } else {
                    /* Transition: '<S5>:240' */
                    cnt = (uint16_T)((int32_T)(((int32_T)cnt) + 1));
                    if (cnt >= MAXCNTINHERITWAIT) {
                      /* Transition: '<S5>:244' */
                      cnt = MAXCNTINHERITWAIT;
                      if ((((int32_T)FlgRonStoredIn) != 0) && (RonLevelIn >
                           RonLevelEE)) {
                        /* Transition: '<S5>:249' */
                        /* Transition: '<S5>:253'
                         * Requirements for Transition: '<S5>:253':
                         *  1. EISB_FCA6CYL_SW_REQ_2758: In RE_FUEL mode if the calibration ENRONCCHECK is not equal to 0 a... (ECU_SW_Requirements#12293)
                         */
                        StRonCCheck = RD_CC_FOUNDIN;
                        FlgRonInheritEE = 1U;

                        /* SignalConversion generated from: '<S4>/RonLevelCross' */
                        RonLevelCross = RonLevelIn;

                        /* Transition: '<S5>:256' */
                      } else {
                        /* Transition: '<S5>:251' */
                        /* Transition: '<S5>:255' */
                        /* Transition: '<S5>:256' */
                      }
                    } else {
                      /* Transition: '<S5>:246' */
                      /* Transition: '<S5>:254' */
                      /* Transition: '<S5>:255' */
                      /* Transition: '<S5>:256' */
                    }
                  }
                }
              }
            }
          }
        }
      }

      if (guard3) {
        if (((int32_T)FlgRonStoredEE) != 0) {
          /* Transition: '<S5>:224' */
          /* Transition: '<S5>:229'
           * Requirements for Transition: '<S5>:229':
           *  1. EISB_FCA6CYL_SW_REQ_2755: In RE_FUEL mode if the calibration ENRONCCHECK is not equal to 0 a... (ECU_SW_Requirements#12290)
           */
          StRonCCheck = RD_CC_FOUND;

          /* Transition: '<S5>:231' */
          /* Transition: '<S5>:233' */
          /* Transition: '<S5>:247' */
          /* Transition: '<S5>:254' */
          /* Transition: '<S5>:255' */
          /* Transition: '<S5>:256' */
        } else {
          /* Transition: '<S5>:226' */
          /* Transition: '<S5>:230' */
          /* Transition: '<S5>:231' */
          /* Transition: '<S5>:233' */
          /* Transition: '<S5>:247' */
          /* Transition: '<S5>:254' */
          /* Transition: '<S5>:255' */
          /* Transition: '<S5>:256' */
        }
      }

      if (guard2) {
        /* Transition: '<S5>:74'
         * Requirements for Transition: '<S5>:74':
         *  1. EISB_FCA6CYL_SW_REQ_2735: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12275)
         */
        StRonCCheck = RD_CC_STOREDIN;
        FlgRonInheritEE = 1U;

        /* SignalConversion generated from: '<S4>/RonLevelCross' incorporates:
         *  Inport: '<Root>/RonLevelIn'
         */
        RonLevelCross = RonLevelIn;

        /* Transition: '<S5>:81' */
        /* Transition: '<S5>:99' */
        /* Transition: '<S5>:109' */
        /* Transition: '<S5>:126' */
        /* Transition: '<S5>:128' */
        /* Transition: '<S5>:133' */
        /* Transition: '<S5>:135' */
        /* Transition: '<S5>:172' */
        /* Transition: '<S5>:197' */
        /* Transition: '<S5>:204' */
        /* Transition: '<S5>:227' */
        /* Transition: '<S5>:230' */
        /* Transition: '<S5>:231' */
        /* Transition: '<S5>:233' */
        /* Transition: '<S5>:247' */
        /* Transition: '<S5>:254' */
        /* Transition: '<S5>:255' */
        /* Transition: '<S5>:256' */
      }

      if (guard1) {
        if (((int32_T)FlgRonStoredEE) != 0) {
          /* Transition: '<S5>:67' */
          /* Transition: '<S5>:71'
           * Requirements for Transition: '<S5>:71':
           *  1. EISB_FCA6CYL_SW_REQ_2738: In NO_RE_FUEL mode if the calibration ENRONCCHECK is not equal to ... (ECU_SW_Requirements#12278)
           */
          StRonCCheck = RD_CC_STORED;

          /* Transition: '<S5>:78' */
          /* Transition: '<S5>:79' */
          /* Transition: '<S5>:81' */
          /* Transition: '<S5>:99' */
          /* Transition: '<S5>:109' */
          /* Transition: '<S5>:126' */
          /* Transition: '<S5>:128' */
          /* Transition: '<S5>:133' */
          /* Transition: '<S5>:135' */
          /* Transition: '<S5>:172' */
          /* Transition: '<S5>:197' */
          /* Transition: '<S5>:204' */
          /* Transition: '<S5>:227' */
          /* Transition: '<S5>:230' */
          /* Transition: '<S5>:231' */
          /* Transition: '<S5>:233' */
          /* Transition: '<S5>:247' */
          /* Transition: '<S5>:254' */
          /* Transition: '<S5>:255' */
          /* Transition: '<S5>:256' */
        } else {
          /* Transition: '<S5>:69' */
          /* Transition: '<S5>:77' */
          /* Transition: '<S5>:78' */
          /* Transition: '<S5>:79' */
          /* Transition: '<S5>:81' */
          /* Transition: '<S5>:99' */
          /* Transition: '<S5>:109' */
          /* Transition: '<S5>:126' */
          /* Transition: '<S5>:128' */
          /* Transition: '<S5>:133' */
          /* Transition: '<S5>:135' */
          /* Transition: '<S5>:172' */
          /* Transition: '<S5>:197' */
          /* Transition: '<S5>:204' */
          /* Transition: '<S5>:227' */
          /* Transition: '<S5>:230' */
          /* Transition: '<S5>:231' */
          /* Transition: '<S5>:233' */
          /* Transition: '<S5>:247' */
          /* Transition: '<S5>:254' */
          /* Transition: '<S5>:255' */
          /* Transition: '<S5>:256' */
        }
      }
    }

    /* End of Outputs for SubSystem: '<Root>/T5ms_fcn' */
    /* Transition: '<S5>:258' */
  }

  /* End of Chart: '<Root>/RonDetectCross_Scheduler' */
  /* Transition: '<S3>:13' */
}

/*
 * Output and update for function-call system: '<Root>/RonDetectCross_Scheduler'
 * Block description for: '<Root>/RonDetectCross_Scheduler'
 *   This block is the scheduler for the models functions.
 */
void RonDet_RonDetectCross_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[2];
  int32_T i;

  /* Chart: '<Root>/RonDetectCross_Scheduler' incorporates:
   *  TriggerPort: '<S3>/input events'
   *
   * Block description for '<Root>/RonDetectCross_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  for (i = 0; i < 2; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: RonDetectCross_Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S3>:14' */
    i = (int32_T)Ro_event_RonDetectCross_PowerOn;
    Ron_chartstep_c3_RonDetectCross(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S3>:15' */
    i = (int32_T)RonDet_event_RonDetectCross_5ms;
    Ron_chartstep_c3_RonDetectCross(&i);
  }
}

/* Model step function */
void RonDetectCross_5ms(void)
{
  /* Chart: '<Root>/RonDetectCross_Scheduler'
   *
   * Block description for '<Root>/RonDetectCross_Scheduler':
   *  This block is the scheduler for the models functions.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectCross_5ms' */

  /* Chart: '<Root>/RonDetectCross_Scheduler'
   *
   * Block description for '<Root>/RonDetectCross_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  RonDet_RonDetectCross_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectCross_5ms' */
}

/* Model step function */
void RonDetectCross_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectCross_PowerOn' incorporates:
   *  Chart: '<Root>/RonDetectCross_Scheduler'
   *
   * Block description for '<Root>/RonDetectCross_Scheduler':
   *  This block is the scheduler for the models functions.
   */

  /* Chart: '<Root>/RonDetectCross_Scheduler'
   *
   * Block description for '<Root>/RonDetectCross_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  RonDet_RonDetectCross_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectCross_PowerOn' */
}

/* Model initialize function */
void RonDetectCross_initialize(void)
{
  /* SystemInitialize for Merge: '<S2>/MStRonCCheck' */
  StRonCCheck = RD_CC_DISABLED;
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T RonLevelCross;
uint8_T StRonCCheck;
uint8_T FlgRonInheritEE;
void RonDetectCross_Stub(void)
{
  RonLevelCross = RonLevelEE;
  StRonCCheck = 0u;
  FlgRonInheritEE = 0u;
}

void RonDetectCross_PowerOn(void)
{
  RonDetectCross_Stub();
}

void RonDetectCross_5ms(void)
{
  RonDetectCross_Stub();
}

#endif                                 /* _BUILD_RONDETECTCROSS_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/