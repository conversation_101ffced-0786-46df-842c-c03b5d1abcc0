/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Spec/Application/KnockCorrNom/trunk/KnockCorrNom_codegen/K#$  */
/* $Revision:: 210102                                                                                         $  */
/* $Date:: 2022-02-25 14:09:30 +0100 (ven, 25 feb 2022)                                                       $  */
/* $Author:: MarottaR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      KnockCorrNom.h
 **  Date:          25-Feb-2022
 **
 **  Model Version: 1.1096
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_KnockCorrNom_h_
#define RTW_HEADER_KnockCorrNom_h_
#include <string.h>
#ifndef KnockCorrNom_COMMON_INCLUDES_
# define KnockCorrNom_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* KnockCorrNom_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  Code generation will declare the memory for
 * these signals and export their symbols.
 *
 */
extern boolean_T FlgSakPos;            /* '<S7>/Logical Operator' */

/* Model entry point functions */
extern void KnockCorrNom_initialize(void);

/* Exported entry point function */
extern void KnockCorrNom_10ms(void);

/* Exported entry point function */
extern void KnockCorrNom_EOA(void);

/* Exported entry point function */
extern void KnockCorrNom_NoSync(void);

/* Exported entry point function */
extern void KnockCorrNom_PowerOn(void);

/* Exported data declaration */

/*Exported calibration memory section */
/*Init of exported calibrations section*/

/* Declaration for custom storage class: ELD_EXPORT_CALIBRATION */
extern CALQUAL CALQUAL_POST uint8_T THRCNTKNOCKCOH;/* Referenced by:
                                                    * '<S6>/THRCNTKNOCKCOH'
                                                    * '<S14>/THRCNTKNOCKCOH'
                                                    */

/* CntKnockCohEE threshold */

/*End of exported calibrations section*/


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_EE_INTERFACE */
extern uint8_T CntKnockCohEE[8];       /* '<S4>/Merge5' */

/* Knocking coherence diagnosis pre-filter */

/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern int16_T DeltaKCorrCyl;          /* '<S4>/Merge' */

/* Delta knock correction for protection */
extern uint8_T FlgCntKnockCohInc[8];   /* '<S4>/Merge4' */

/* Flag to indicate CntKnockCohEE increment during trip */
extern uint8_T FlgKCohInc;             /* '<S4>/Merge1' */

/* Flag to indicate NCylKnockCoh above threshold */
extern uint8_T FlgKCohIncLev1[8];      /* '<S4>/Merge3' */

/* Flag to indicate knocking coherence active and below threshold */
extern uint8_T KCohDiagCnt[8];         /* '<S4>/Merge2' */

/* Internal counter for knocking coherence */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S12>/Constant' : Unused code path elimination
 * Block '<S12>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Data Type Propagation' : Unused code path elimination
 * Block '<S20>/Data Type Propagation' : Unused code path elimination
 * Block '<S18>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S20>/Reshape' : Reshape block reduction
 * Block '<S18>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S18>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S18>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S18>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S24>/Data Type Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'KnockCorrNom'
 * '<S1>'   : 'KnockCorrNom/EOA_fcn'
 * '<S2>'   : 'KnockCorrNom/Init_fcn'
 * '<S3>'   : 'KnockCorrNom/KnockCorrNom_Scheduler'
 * '<S4>'   : 'KnockCorrNom/Merge'
 * '<S5>'   : 'KnockCorrNom/NoSync_fcn'
 * '<S6>'   : 'KnockCorrNom/Recovery_fcn'
 * '<S7>'   : 'KnockCorrNom/EOA_fcn/Correction_Inc_Calculation'
 * '<S8>'   : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation'
 * '<S9>'   : 'KnockCorrNom/EOA_fcn/Rpmknock5_Calculation'
 * '<S10>'  : 'KnockCorrNom/EOA_fcn/Correction_Inc_Calculation/LookUp_VTKCORRINCDELAYN'
 * '<S11>'  : 'KnockCorrNom/EOA_fcn/Correction_Inc_Calculation/Parameters_Choice'
 * '<S12>'  : 'KnockCorrNom/EOA_fcn/Correction_Inc_Calculation/LookUp_VTKCORRINCDELAYN/LookUp_IR_U8'
 * '<S13>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation'
 * '<S14>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/Knock_Coh_PTfault_And_Recovery_Mgm'
 * '<S15>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/Timer_Enable_Inc'
 * '<S16>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation/KCorrDec_Mgm'
 * '<S17>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation/LookUp2D_TBKCORRDEC'
 * '<S18>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation/PreLookUpIdSearch_S16'
 * '<S19>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation/LookUp2D_TBKCORRDEC/ArrangeLSB'
 * '<S20>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/KCorrDec_Calculation/LookUp2D_TBKCORRDEC/Look2D_IR_S8'
 * '<S21>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/Knock_Coh_PTfault_And_Recovery_Mgm/Knock_Coh_PTfault'
 * '<S22>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/Timer_Enable_Inc/EnInc_Count_Management'
 * '<S23>'  : 'KnockCorrNom/EOA_fcn/Delta_Correction_Calculation/Timer_Enable_Inc/Increment Real World'
 * '<S24>'  : 'KnockCorrNom/EOA_fcn/Rpmknock5_Calculation/PreLookUpIdSearch_U16'
 * '<S25>'  : 'KnockCorrNom/Init_fcn/Reset_CntKnockCoh'
 * '<S26>'  : 'KnockCorrNom/NoSync_fcn/Reset_Counters'
 * '<S27>'  : 'KnockCorrNom/Recovery_fcn/KnockCorrNom_Scheduler'
 */

/*-
 * Requirements for '<Root>': KnockCorrNom
 */
#endif                                 /* RTW_HEADER_KnockCorrNom_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/