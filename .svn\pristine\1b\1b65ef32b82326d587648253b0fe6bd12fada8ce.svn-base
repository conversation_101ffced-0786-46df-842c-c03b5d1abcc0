/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  Getlock.c
**  Created on      :  22-giu-2020 10:00:00
**  Original author :  CarboniM
**  Integration history: 
**  -  Standard software driver for C55 Flash from "STM - SPC57xKLS-FLASH Driver for C55: Package version 0.2.0"
**          
*******************************************************************************************************************/

#ifdef  _BUILD_FLASH_

#pragma ghs startnomisra

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include    "ssd_c55.h"



/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
static const unsigned short GetLock_C[] = 
{
#if (TARGET_TYPE == SPC574K2_CUT24) || (TARGET_TYPE == SPC574K2)     /* Total Size = 162 half words */

    0x0080, 0x1821, 0x06F0, 0xD3F1, 0xD501, 0x0146, 0x480F, 
    0x30E3, 0x0040, 0x2A07, 0xE609, 0xC073, 0x20F7, 0x5187,
    0x0000, 0xC073, 0x2137, 0xC047, 0xE80A, 0xC073, 0x18E7, 
    0x8028, 0x5187, 0x0000, 0xC073, 0x18E7, 0x802C, 0xC047,
    0x0167, 0x2227, 0xE005, 0x2207, 0xE409, 0xE618, 0xE824, 
    0x2237, 0xE430, 0xE646, 0x2247, 0xE65C, 0xE85E, 0x4904,
    0xC363, 0xC473, 0x0476, 0xC573, 0x0476, 0xC273, 0x0467, 
    0x18E7, 0xB020, 0x18C7, 0x84FF, 0x2C07, 0x4067, 0x6D07,
    0xE850, 0x4804, 0xC763, 0xC873, 0x0476, 0xC973, 0x0476, 
    0xC673, 0x0467, 0x18C7, 0xB020, 0x2C07, 0x4067, 0xE843,
    0x0244, 0x4804, 0xCB63, 0xCC73, 0x0476, 0xCD73, 0x0476, 
    0xCA73, 0x0467, 0x18C7, 0xB020, 0x2C07, 0x4067, 0xE835,
    0x30E3, 0x0040, 0x2A07, 0xE606, 0xC073, 0x2177, 0x5187, 
    0x0000, 0xE803, 0x637F, 0xE82E, 0x4804, 0xCE73, 0x23F7,
    0xE403, 0x2C07, 0xE824, 0xCE73, 0x18C7, 0xB020, 0x2C07, 
    0x4067, 0xE81E, 0x30E3, 0x0040, 0x2A07, 0xE606, 0xC073,
    0x21B7, 0x5187, 0x0000, 0xE803, 0x637F, 0xE817, 0x4804, 
    0xCE73, 0x23F7, 0xE108, 0xCE73, 0x25F7, 0x18C7, 0xB020,
    0x2C07, 0x4067, 0xE808, 0x4807, 0xE806, 0x49F4, 0x6207, 
    0xE803, 0x638F, 0xE805, 0x7D87, 0x3838, 0x4047, 0xD075,
    0x30E3, 0x0048, 0x2A07, 0xE608, 0x7FE3, 0xFB78, 0x1800, 
    0xD000, 0x0002, 0x1800, 0xD000, 0x01F7, 0x0173, 0xC3F1,
    0xC501, 0x20F1, 0x0090, 0x0004, 0x3038, 0x3030, 0x3646, 
    0x4646

#endif
};

extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned long *functionBuffer, uint32_T functionSize);

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * GetLock - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/    
uint32_T GetLock ( PSSD_CONFIG pSSDConfig,
        uint8_T blkLockIndicator,
        uint32_T *blkLockState
)
{
#pragma ghs nowarning 171
    FlashFunctionLoader( (unsigned long*)GetLock_C, sizeof(GetLock_C)/2);
    return ((PGETLOCK)FlashFunctionPointer)(pSSDConfig, blkLockIndicator,
            blkLockState);
#pragma ghs endnowarning /* warning #171-D: invalid type conversion */
}


/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */ 
#pragma ghs endnomisra

#endif /* _BUILD_FLASH_ */

