/******************************************************************************
** COPYRIGHT
** Copyright (c) 2019 by Eldor Corporation S.P.A. , All rights reserved.
**
******************************************************************************/
/******************************************************************************
** SWC : SafetyMngr_test
** Filename : SafetyMngr_test_out.h
** Created on : 25-jan-2020 12:00:00
** Original author : ZandaE
******************************************************************************/
/*****************************************************************************
**
** RecoverySM_test Description
**
** This SWC is used to help in developing and testing Safety Mechanisms 
** implemented in the SafetyMngr module.
**
******************************************************************************/
#ifndef SAFETYMNGR_TEST_OUT_H
#define SAFETYMNGR_TEST_OUT_H
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_CommLib_out.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */
/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */
/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */
/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */
/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/

#ifdef _BUILD_FLASHCHECK_SM_TEST_
/******************************************************************************
** Function:   SafetyMngr_Test_FlashSBE
**
** Description:
** generates a single bit ECC error in the chosen memory region
** that will be flagged in the ECC module.
** The target address should not be already written, otherwise a MBE will be
** generated, instead of SBE.
**
** Parameters :
** [in] uint32_T targetAddr: target address to unwritten memory (0xFFFFFFFFU)
**
** Returns:
** void
**
** SW Requirements:
** NA
**
** Implementation Notes:
**
** EA GUID:
******************************************************************************/
extern void SafetyMngr_Test_FlashSBE(uint32_T targetAddr);
#endif /*_BUILD_FLASHCHECK_SM_TEST_*/

#ifdef _BUILD_RAMCHECK_SM_TEST_
/******************************************************************************
** Function:   SafetyMngr_Test_RamSBE
**
** Description:
** generates a single bit ECC error in the chosen memory region
** that will be flagged in the ECC module
**
** Parameters :
** void
**
** Returns:
** void
**
** SW Requirements:
** NA
**
** Implementation Notes:
**
** EA GUID:
******************************************************************************/
extern void SafetyMngr_Test_RamSBE(uint32_T targetAddr);
#endif /*_BUILD_RAMCHECK_SM_TEST_*/

#endif /* SAFETYMNGR_TEST_OUT_H */
/****************************************************************************
****************************************************************************/


