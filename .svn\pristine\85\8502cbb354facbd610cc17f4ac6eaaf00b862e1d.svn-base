/*  File    : fft_lib.h
 *  Author  : <PERSON><PERSON>
 *  Date    : 29/09/2005 12.25
 *  Revision: 1.0
 *	Note	: Aggiunta fft_real per calcolo FFT dati reali
 *
 *  Copyright 2005 Eldor Corporation
 */

#ifndef _FFT_LIB_H_
#define _FFT_LIB_H_

/** include files **/
#include "rtwtypes.h"
#include <string.h>

#define _MATLAB_FFT_PHASE_		/* define this const if you want to get the FFT phase like MATLAB */

/** definitions **/
#define SWAP(a,b) 	{(a)^=(b); (b)^=(a); (a)^=(b);}

/** default settings **/

/** external functions **/

/** external data **/

/** internal functions **/

/** public data **/

/* Imported (extern) block signals */

/** private data **/

/** public functions **/
void four1(int32_T *data, uint16_T nn, int16_T isign);
void fft_real(int32_T *data, uint16_T log2n); /*, int32_T *real_data, int32_T *imm_data);*/

#endif /* #define _FFT_LIB_H_ */
