/*****************************************************************************************************************/
/* $HeadURL:: https://172.26.1.29/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_RS_35_PI_0204/tree/DD/CANMGMO#$  */
/* $Revision:: 200525                                                                                         $  */
/* $Date:: 2021-12-23 12:56:33 +0100 (gio, 23 dic 2021)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmOut
**  Filename        :  CanMgmOut_BR.h
**  Created on      :  07-jul-2023 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifndef CANMGM_OUT_BR_H
#define CANMGM_OUT_BR_H
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Canmgmout_out.h"
#include "Canmgmin_out.h"
#include "Canmgm_out.h"
#include "Mcan_out.h"
#include "KnockCorrTot_out.h"
#include "pwrmgm_out.h"
#include "msparkcmd_out.h"
#include "recmgm_out.h"
#include "P2NoiseDetect_out.h"
#include "Ionacq_out.h"
#include "Diagmgm_out.h"
#include "AnalogIn_out.h"
#include "WDT_wrapper_out.h"
#include "FlashMgm_out.h"
#include "ron_detect.h"
#include "MisfThrMgm_out.h"
#include "MKnockDet_out.h"
#include "TempECUMgm_out.h"
#include "ionmisf_out.h"
#include "SyncMgm_out.h"
#include "mathlib.h"
#include "Sys.h"
#include "Utils_out.h"
#include "Dtc.h"
#include "DiagCanmgm_out.h"
#include "ccp.h"
#include "CombTotCorr_out.h"
#include "App_tag.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define BIT_0 0x01u
#define BIT_1 0x02u
#define BIT_2 0x04u
#define BIT_2_0 0x05u
#define BIT_3 0x08u
#define BIT_4 0x10u
#define BIT_0_1_2 0x07u
#define BIT_1_2 0x06u

#define NEUTRAL_CORR                   ((uint16_T) 32768U)

#define     CNT_10  10u

#define SA_KNOCK_SAT        372     //22.5+0.75� * 16 (2^-4)
#define SA_KNOCK_SAT_CAN    31      // SA_KNOCK_SAT / 16 / CAN_GAIN --> SA_KNOCK_SAT / 16 / 0.75
#define SA_KNOCK_CONV_CAN   12      // 16 * 0.75

#define TEMP_STS_NORMAL  0u
#define TEMP_STS_WARNING 1u
#define TEMP_STS_HAZARD  2u
#define TEMP_STS_FAULT   3u


/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
#pragma ghs startnomisra

typedef struct EISB_ECM_1_M_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum_1_M0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy1_1    :1;
            uint8_T  RON1   :3;
            uint8_T  CANMsgCnt_EISB_1_M1   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MKnockDet_02   :1;
            uint8_T  KnockCorr02   :5;
            uint8_T  Misf_flag_02   :2;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockCorr23   :5;
            uint8_T  Misf_flag_23   :2;
            uint8_T  MKnock_Fault_03   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockCorr54   :4;
            uint8_T  Misf_flag_54   :2;
            uint8_T  MKnock_Fault_24   :1;
            uint8_T  MKnockDet_24   :1;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockCorr75   :3;
            uint8_T  Misf_flag_75   :2;
            uint8_T  MKnock_Fault_55   :1;
            uint8_T  MKnockDet_55   :1;
            uint8_T  KnockCorr55   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :2;
            uint8_T  RONLvlSt6  :2;
            uint8_T  MKnock_Fault_76   :1;
            uint8_T  MKnockDet_76   :1;
            uint8_T  KnockCorr76   :2;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  LastMisfAbsTDC7   :3;
            uint8_T  FirstMisfAbsTDC7   :3;
            uint8_T  MisfCyclesCnt7   :2;
        } B;
    } Byte7;

} EISB_ECM_1_M_T;

typedef struct EISB_ECM_1_S_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum_1_S0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy1_0   :1;
            uint8_T  RON_TS1   :3;
            uint8_T  CANMsgCnt_EISB_1_S1   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MKnockDet_12   :1;
            uint8_T  KnockCorr12   :5;
            uint8_T  Misf_flag_12   :2;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockCorr33   :3;
            uint8_T  MKnock_Fault_33   :1;
            uint8_T  MKnockDet_33   :1;
            uint8_T  Misf_flag_33   :2;
            uint8_T  MKnock_Fault_13   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockCorr44   :4;
            uint8_T  Misf_flag_44   :2;
            uint8_T  KnockCorr34   :2;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockCorr65   :3;
            uint8_T  Misf_flag_65   :2;
            uint8_T  MKnock_Fault_45   :1;
            uint8_T  MKnockDet_45   :1;
            uint8_T  KnockCorr45   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :2;
            uint8_T  RONLvlSt_TS6  :2;
            uint8_T  MKnock_Fault_66   :1;
            uint8_T  MKnockDet_66   :1;
            uint8_T  KnockCorr66   :2;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  LastMisfAbsTDC7   :3;
            uint8_T  FirstMisfAbsTDC7   :3;
            uint8_T  MisfCyclesCnt7   :2;
        } B;
    } Byte7;

} EISB_ECM_1_S_T;

typedef struct EISB_ECM_2_M_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum_2_M0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy1_0   :4;
            uint8_T  CANMsgCnt_EISB_2_M1   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault52   :2;
            uint8_T  IgnFault22   :3;
            uint8_T  IgnFault02   :3;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault2_TS3   :1;
            uint8_T  IgnFault0_TS3   :3;
            uint8_T  IgnFault73   :3;
            uint8_T  IgnFault53   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault7_TS4   :3;
            uint8_T  IgnFault5_TS4   :3;
            uint8_T  IgnFault2_TS4   :2;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  InjCorr_Cyl25   :2;
            uint8_T  InjCorr_Cyl05   :6;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  InjCorr_Cyl56   :4;
            uint8_T  InjCorr_Cyl26   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  InjCorr_Cyl77   :6;
            uint8_T  InjCorr_Cyl57   :2;
        } B;
    } Byte7;

} EISB_ECM_2_M_T;

typedef struct EISB_ECM_2_S_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum_2_S0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy1_0   :4;
            uint8_T  CANMsgCnt_EISB_2_S1   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault42   :2;
            uint8_T  IgnFault32   :3;
            uint8_T  IgnFault12   :3;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault3_TS3   :1;
            uint8_T  IgnFault1_TS3   :3;
            uint8_T  IgnFault63   :3;
            uint8_T  IgnFault43   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault6_TS4   :3;
            uint8_T  IgnFault4_TS4   :3;
            uint8_T  IgnFault3_TS4   :2;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  InjCorr_Cyl35   :2;
            uint8_T  InjCorr_Cyl15   :6;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  InjCorr_Cyl46   :4;
            uint8_T  InjCorr_Cyl36   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  InjCorr_Cyl67   :6;
            uint8_T  InjCorr_Cyl47   :2;
        } B;
    } Byte7;

} EISB_ECM_2_S_T;

typedef struct EISB_ECM_3_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum_5   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Diag_T_WATER     :2;
            uint8_T  Diag_T_AIR       :2;
            uint8_T  CANMsgCnt_EISB_5 :4;
        } Mux0;
        struct
        {
            uint8_T  Diag_CPU         :2;
            uint8_T  Diag_CAN_NODE_1  :2;
            uint8_T  CANMsgCnt_EISB_5 :4;
        } Mux1;
        struct
        {
            uint8_T  Diag_SEC_2_6     :2;
            uint8_T  Diag_SEC_1_5     :2;
            uint8_T  CANMsgCnt_EISB_5 :4;
        } Mux2;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Diag_VBATTERY    :2;
            uint8_T  Diag_CAMLEVEL    :2;
            uint8_T  Diag_RPM         :2;
            uint8_T  Diag_LOAD        :2;
        } Mux0;
        struct
        {
            uint8_T  Diag_SPARK_EV_A  :2;
            uint8_T  Diag_TEMP_ECU_3  :2;
            uint8_T  Diag_SYNC        :2;
            uint8_T  Diag_RAM         :2;
        } Mux1;
        struct
        {
            uint8_T  Diag_VCAP_2_6   :2;
            uint8_T  Diag_VCAP_1_5   :2;
            uint8_T  Diag_SEC_4_8    :2;
            uint8_T  Diag_SEC_3_7    :2;
        } Mux2;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Diag_ION_0       :2;
            uint8_T  Diag_ION_CH_B    :2;
            uint8_T  Diag_ION_CH_A    :2;
            uint8_T  Diag_ADC         :2;
        } Mux0;
        struct
        {
            uint8_T  Diag_TRIGGER_0   :2;
            uint8_T  Diag_BUCK_B      :2;
            uint8_T  Diag_BUCK_A      :2;
            uint8_T  Diag_SPARK_EV_B  :2;
        } Mux1;
        struct
        {
            uint8_T  Diag_VCOIL_A_MON :2;
            uint8_T  Diag_CAN_NODE_OVER_RUN   :2;
            uint8_T  Diag_VCAP_4_8    :2;
            uint8_T  Diag_VCAP_3_7    :2;
        } Mux2;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Diag_ION_4        :2;
            uint8_T  Diag_ION_3        :2;
            uint8_T  Diag_ION_2        :2;
            uint8_T  Diag_ION_1        :2;
        } Mux0;
        struct
        {
            uint8_T  Diag_TRIGGER_4    :2;
            uint8_T  Diag_TRIGGER_3    :2;
            uint8_T  Diag_TRIGGER_2    :2;
            uint8_T  Diag_TRIGGER_1    :2;
        } Mux1;
        struct
        {
            uint8_T  Diag_KEY_SIGNAL   :2;
            uint8_T  Diag_BUCK_CURR_B  :2;
            uint8_T  Diag_BUCK_CURR_A  :2;
            uint8_T  Diag_VCOIL_B_MON  :2;
        } Mux2;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Diag_VBAT_CIRCUIT :2;
            uint8_T  Diag_ION_7        :2;
            uint8_T  Diag_ION_6        :2;
            uint8_T  Diag_ION_5        :2;
        } Mux0;
        struct
        {
            uint8_T  Diag_SPARK_0      :2;
            uint8_T  Diag_TRIGGER_7    :2;
            uint8_T  Diag_TRIGGER_6    :2;
            uint8_T  Diag_TRIGGER_5    :2;
        } Mux1;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Diag_LIVENESS     :2;
            uint8_T  Diag_PRI_B        :2;
            uint8_T  Diag_PRI_A        :2;
            uint8_T  Diag_WDT          :2;
        } Mux0;
        struct
        {
            uint8_T  Diag_SPARK_4      :2;
            uint8_T  Diag_SPARK_3      :2;
            uint8_T  Diag_SPARK_2      :2;
            uint8_T  Diag_SPARK_1      :2;
        } Mux1;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_ECM_5_Mux    :2;
            uint8_T  Diag_PRIVATE_CAN  :2;
            uint8_T  Diag_TEMP_ECU_2   :2;
            uint8_T  Diag_TEMP_ECU_1   :2;
        } Mux0;
        struct
        {
            uint8_T  EISB_ECM_5_Mux    :2;
            uint8_T  Diag_SPARK_7      :2;
            uint8_T  Diag_SPARK_6      :2;
            uint8_T  Diag_SPARK_5      :2;
        } Mux1;
        struct
        {
            uint8_T  EISB_ECM_5_Mux    :2;
            uint8_T  Dummy7            :6;
        } Mux2;
    } Byte7;

} EISB_ECM_3_T;

typedef struct EISB_ECM_4_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum_6   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_CALID_CVN_MUX   :2;
            uint8_T Dummy1_1   :2;
            uint8_T  CANMsgCnt_EISB_6   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_CALID_Index_01   :8;
        } Mux0;
        struct
        {
            uint8_T  EISB_CALID_Index_07   :8;
        } Mux1;
        struct
        {
            uint8_T  EISB_CALID_Index_13   :8;
        } Mux2;
        struct
        {
            uint8_T  EISB_CVN_Byte_1        :8;
        } Mux3;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_CALID_Index_02   :8;
        } Mux0;
        struct
        {
            uint8_T  EISB_CALID_Index_08   :8;
        } Mux1;
        struct
        {
            uint8_T  EISB_CALID_Index_14   :8;
        } Mux2;
        struct
        {
            uint8_T  EISB_CVN_Byte_2        :8;
        } Mux3;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_CALID_Index_03   :8;
        } Mux0;
        struct
        {
            uint8_T  EISB_CALID_Index_09   :8;
        } Mux1;
        struct
        {
            uint8_T  EISB_CALID_Index_15   :8;
        } Mux2;
        struct
        {
            uint8_T  EISB_CVN_Byte_3        :8;
        } Mux3;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_CALID_Index_04   :8;
        } Mux0;
        struct
        {
            uint8_T  EISB_CALID_Index_10   :8;
        } Mux1;
        struct
        {
            uint8_T  EISB_CALID_Index_16   :8;
        } Mux2;
        struct
        {
            uint8_T  EISB_CVN_Byte_4        :8;
        } Mux3;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_CALID_Index_05   :8;
        } Mux0;
        struct
        {
            uint8_T  EISB_CALID_Index_11   :8;
        } Mux1;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_CALID_Index_06   :8;
        } Mux0;
        struct
        {
            uint8_T  EISB_CALID_Index_12   :8;
        } Mux1;
    } Byte7;

} EISB_ECM_4_T;

typedef enum{
    EISB_ECM3_MUX0 = 0,
    EISB_ECM3_MUX1 = 1,
    EISB_ECM3_MUX2 = 2
}EisbEcm3Mux_T;
    
typedef enum{
    EISB_ECM4_MUX0 = 0,
    EISB_ECM4_MUX1 = 1,
    EISB_ECM4_MUX2 = 2,
    EISB_ECM4_MUX3 = 3
}EisbEcm4Mux_T;

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST int16_T  OFFSAKNOCKCAN;
extern CALQUAL CALQUAL_POST int16_T  VTCANSAKNOCK[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint8_T  ENADDSARON;
extern CALQUAL CALQUAL_POST uint16_T VTSTMISFFORCEPERIOD[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint16_T VTSTMISFFORCEDLENGTH[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint8_T  VTSTMISFFORCEDTYPE[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint32_T MAXDELTASPARKTIME;
extern CALQUAL CALQUAL_POST uint8_T FORCEIGNFAULT[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint8_T FORCEDIGNFAULT[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint8_T CALVER[4];
extern CALQUAL CALQUAL_POST uint8_T ENGPFINFO;
extern CALQUAL CALQUAL_POST uint8_T ION2SCANTOOLDEBCLEAR;
extern CALQUAL CALQUAL_POST uint8_T ION2SCANTOOLDEBSET;
extern CALQUAL CALQUAL_POST uint8_T FLASHORDTCERASEDEB;
extern CALQUAL CALQUAL_POST uint8_T ENKNOCKANALYSIS;
extern CALQUAL CALQUAL_POST uint8_T ENOLDGPFINFO;
extern CALQUAL CALQUAL_POST uint8_T FORCEINJCORR;
extern CALQUAL CALQUAL_POST uint8_T VTFORCEINJCORR[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint8_T INJINVCANEN;
/*****************************************************************************
** IMPORTED EE Variables
******************************************************************************/
extern uint16_T FlashCrcEE;

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static uint8_T CanMgm_UpdateStMisfInfo(uint8_T stMisf, uint8_T tdc);
static int16_T CanMgm_Send_EISB_ECM_1_M(uint8_T canMsgBuff);
static int16_T CanMgm_Send_EISB_ECM_1_S(uint8_T canMsgBuff);
static int16_T CanMgm_Send_EISB_ECM_2_M(uint8_T canMsgBuff);
static int16_T CanMgm_Send_EISB_ECM_2_S(uint8_T canMsgBuff);
static int16_T CanMgm_Send_EISB_ECM_3(uint8_T canMsgBuff, EisbEcm3Mux_T mux);
static int16_T CanMgm_Send_EISB_ECM_4(uint8_T canMsgBuff ,EisbEcm4Mux_T mux);
static void CanMgm_UpdateMisfAbsTdc(void);

#endif

/****************************************************************************
 ****************************************************************************/


