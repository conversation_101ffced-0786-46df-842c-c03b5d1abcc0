/*****************************************************************************************************************/
/* $HeadURL::                                                                                                                                  $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
#ifdef _BUILD_VSRAMMGM_
/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "vsrammgm.h"
#include "Vsram_shared_content.h"
#include "string.h"
//#include "MC33904U3_out.h"
#include "eemgm_out.h"
#include "diagcanmgm.h"
#include "sys.h"

/*!
\defgroup PublicVariables Public Variables 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/// Communication protocol (0 -> NO_COMM, 1 -> KWP/TPE communication is active, 3 -> CCP communication is active) 
uint32_T comm_protocol;
/// CCP Comand Return Message 
uint8_T ccpCrmTmp[CCP_CRM_LEN];
/// CCP Session Status (SS_RUN, SS_DAQ, SS_CONNECTED, SS_DISCONNECTED, SS_STORE,SS_RESUME)
uint8_T ccpSessionStatusTmp;
/// Boot Flashing Request, it is set if a boot upgrade has been received from the tester
boolean_T boot_flashing_rqst;
/// Return Code from FLASH_Erase of the Backup Area in order to upgrade Boot memory region
int16_T ret_erase_backup_boot;

/*!\egroup*/

/*!
\defgroup PublicFunctions Public Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   VSRAMMGM_LoadFromSharedMemory
//
//   Description:   This method loads shared data from VSRAM 
/*! \brief This method loads shared data from VSRAM
*/
//
//  Parameters: void
//  Returns: void
/*! 
*/
//  Notes:        
/*!
No notes
*/
/**************************************************************************/
void VSRAMMGM_LoadFromSharedMemory(void)
{
    comm_protocol = *((uint32_T*)(vsram_shared_memory+COMM_PROTOCOL_POS));
    memcpy(ccpCrmTmp, (vsram_shared_memory+CCP_CRM_POS), CCP_CRM_LEN);
    
    ccpSessionStatusTmp = *((uint8_T*)(vsram_shared_memory+CCP_SESSIONSTATUS_POS));
    boot_flashing_rqst = *((boolean_T*)(vsram_shared_memory+CCP_BOOTFLASHINGRQST_POS));
    ret_erase_backup_boot = *((int16_T*)(vsram_shared_memory+CCP_RETERASEBACKUPBOOT_POS));

#ifdef _BUILD_DIAGCANMGM_
    DIAGsession = *((uint32_T*)(vsram_shared_memory+DIAGSESSION_POS));
    DownloadStruct.CF = *((uint32_T*)(vsram_shared_memory+DOWNLOADSTRUCT_POS));
#endif /* _BUILD_DIAGCANMGM_ */
}


/***************************************************************************/
//   Function    :   VSRAMMGM_StoreToSharedMemory
//
//   Description:  This method stores shared data in VSRAM  
/*! \brief This method stores shared data in VSRAM
*/
//
//  Parameters: void
//  Returns: void
/*! 
*/
//  Notes:        
/*!
No notes
*/
/**************************************************************************/
void VSRAMMGM_StoreToSharedMemory(void)
{
    *((uint32_T*)(vsram_shared_memory+COMM_PROTOCOL_POS)) = comm_protocol;
    
    memcpy((vsram_shared_memory+CCP_CRM_POS), ccpCrmTmp, CCP_CRM_LEN);
    
    *((uint8_T*)(vsram_shared_memory+CCP_SESSIONSTATUS_POS)) = ccpSessionStatusTmp;
    *((boolean_T*)(vsram_shared_memory+CCP_BOOTFLASHINGRQST_POS)) = boot_flashing_rqst;
    *((int16_T*)(vsram_shared_memory+CCP_RETERASEBACKUPBOOT_POS)) = ret_erase_backup_boot;
    
#ifdef _BUILD_DIAGCANMGM_
    *((uint32_T*)(vsram_shared_memory+DIAGSESSION_POS)) = DIAGsession;
    *((uint32_T*)(vsram_shared_memory+DOWNLOADSTRUCT_POS)) = DownloadStruct.CF;
#endif /* _BUILD_DIAGCANMGM_ */

}

#ifdef _TEST_VSRAMMGM_
/***************************************************************************/
//   Function    :   VSRAMMGM_Test
//
//   Description:   This method method sets VSRAM variables values and invoke a SW reset
//                  actual checks shall be performed using T32 &/or calibration tool
//
//  Parameters: void
//  Returns: void
/*! 
*/
//  Notes:        
/*!
No notes
*/
/**************************************************************************/
void VSRAMMGM_Test(void)
{
uint8_T i = 0u;
int16_T res;

    for (i = 0u; i < CCP_CRM_LEN; i++)
    {
        ccpCrmTmp[i] = 0x5A;
    }
    ccpSessionStatusTmp = 0x5A;
    boot_flashing_rqst = 0x5A;
    ret_erase_backup_boot = -257;

    res = VSRAMMGM_Update();
    if (res == NO_ERROR)
    {
        SYS_SwRST();
    }
}
#endif //_TEST_VSRAMMGM_

/*!\egroup*/

#endif //_BUILD_VSRAMMGM_
