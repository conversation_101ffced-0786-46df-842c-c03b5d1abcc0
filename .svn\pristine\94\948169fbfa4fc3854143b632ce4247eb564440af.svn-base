/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef _IVOR_C0_H_
#define _IVOR_C0_H_


extern  uint8_T     IvorIndex_c0;
extern  uint32_T    SRR0_Value_c0;
extern  uint32_T    SRR1_Value_c0;
extern  uint32_T    CSRR0_Value_c0;
extern  uint32_T    CSRR1_Value_c0;
extern  uint32_T    SPR_ESRValue_c0;
extern  uint32_T    SPR_DEARValue_c0;
extern  uint32_T    SPR_MCSRValue_c0;
extern  uint32_T    SPR_MCARValue_c0;
extern  uint32_T    MCSRR0_Value_c0;
extern  uint32_T    MCSRR1_Value_c0;
extern  uint16_T    IVOR_ConfigStatus_c0;

extern  void (*IVOR_Common_ManagerUserFunction_c0) (void);
extern  void (*IVOR1_UserFunction_c0) (void);

/*
** =========================================================================
**     Method      :  IVOR_Config_c0
**
**     Description : This method intializes IVPR address base register
** =========================================================================
*/
int16_T IVOR_Config_c0(void);

/*
** =========================================================================
**     Method      :  InitIVPR
**
**     Description : This method intializes the IVPR register
** =========================================================================
*/
static void InitIVPR_c0(void);


/*===========================================================================*/
/**
**    \par Method
**    SYS_GetESRExceptionType 
**
**    \par Description :
**     Discriminate exceptions that can generate the same interrupt type.
**	   Note: only IVOR2,3,5,6,13,32,33,34 change ESR register
**         
**    \param uint8_T IvorType
**    \param uint32_T* ExcepType
**    \return error code
** ===================================================================
*/
int16_T SYS_GetESRExceptionType_c0(uint8_T IvorType,uint32_T* ExcepType);


/*===========================================================================*/
/**
**    \par Method
**    SYS_GetMCSRExceptionType 
**
**    \par Description :
**     diffentiate among machine check conditions; also indicates whether the
**	   source of a machine check condition is recoverable.
**	   Note: MCSR register is IVOR2 dedicated
**         
**    \param uint32_T* ExcepType
**    \return error code
** ===================================================================
*/
int16_T SYS_GetMCSRExceptionType_c0(uint32_T* ExcepType, uint32_T* Mav, uint32_T* Maph);


#endif /* _IVOR_H_ */
