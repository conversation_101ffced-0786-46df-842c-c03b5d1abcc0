/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/*
 * File: RonDetectEst_eep.c
 *
 * Code generated for Simulink model 'RonDetectEst'.
 *
 * Model version                  : 1.1107
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri May 20 08:34:23 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor->Custom Processor
 * Emulation hardware selection:
 *    Differs from embedded hardware (Intel->x86-64 (Windows64))
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. ROM efficiency
 *    5. RAM efficiency
 *    6. Traceability
 *    7. Debugging
 *    8. Polyspace
 * Validation result: Passed (31), Warning (1), Error (0)
 */
#include "rtwtypes.h"

/* Exported data definition */

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_EE_INTERFACE */
uint16_T CntRonSuspEE = ((uint16_T)0U);/* '<S9>/Merge28' */

/* Number of ron level change after detection */
uint16_T CntRonSuspRunEE = ((uint16_T)0U);/* '<S9>/Merge26' */

/* Number of background test executed after ron level detection */
uint8_T IDRonSuspRunEE = ((uint8_T)0U);/* '<S9>/Merge27' */

/* VtDRonLevelSuspRun index */
uint8_T RonLevelEE = ((uint8_T)0U);    /* '<S9>/Merge25' */

/* RON level stored in EE */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */