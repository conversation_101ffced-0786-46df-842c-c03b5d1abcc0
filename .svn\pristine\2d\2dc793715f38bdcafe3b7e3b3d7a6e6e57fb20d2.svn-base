/*****************************************************************************************************************/
/* $HeadURL::                                                                                                                          $   */
/* $ Description:                                                                                                                                                                                    */
/* $Revision::        $                                                                                                                                                                            */
/* $Date::                                                $                                                                                                                       */
/* $Author::                         $                                                                                                                                                            */
/*****************************************************************************************************************/

#ifndef _CPU_MGM_OUT_
#define _CPU_MGM_OUT_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
 #include "rtwtypes.h"
 #include "task.h"

/*!
\defgroup PublicDefines Public Defines
\brief Defines with global scope
 
\sgroup
*/
/*-----------------------------------*
* PUBLIC DEFINES
*-----------------------------------*/
/* Reset types */
#define POWERONRESET            0x00u
#define EXTERNALRESET           0x01u
#define LOSSOFLOCKRESET         0x02u
#define LOSSOFCLOCKRESET        0x03u
#define WATCHDOGRESET           0x04u
#define CHECKSTOPRESET          0x05u
#define SWSYSRESET              0x06u

/*!\egroup*/

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_T ResetType;

/*-----------------------------------*
 * PUBLIC CALIB DECLARATIONS
 *-----------------------------------*/
extern CALQUAL CALQUAL_POST uint32_T MAXCPULOADCNT0;
extern CALQUAL CALQUAL_POST uint16_T THRCPULOAD;
extern CALQUAL CALQUAL_POST uint32_T SYSTEMTRAP0;

///EE
extern uint16_T EEMaxCpuLoadPerc;
extern uint8_T  EEOsTaskListMaxElem[SOFT_SET_INTERRUPT_NUM];
extern uint16_T EECntCpuLoadPerc;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
void CpuMgm_Initialize(void);
void FuncEON(uint8_T *ptFautRam);

#endif

