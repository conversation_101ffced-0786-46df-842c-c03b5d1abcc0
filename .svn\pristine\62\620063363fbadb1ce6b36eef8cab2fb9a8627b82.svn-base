/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      RonDetectEn.h
 **  Date:          09-Feb-2022
 **
 **  Model Version: 1.1016
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_RonDetectEn_h_
#define RTW_HEADER_RonDetectEn_h_
#ifndef RonDetectEn_COMMON_INCLUDES_
# define RonDetectEn_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* RonDetectEn_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  uint16_T Memory1_PreviousInput;      /* '<S24>/Memory1' */
  uint16_T Memory_PreviousInput;       /* '<S24>/Memory' */
  uint16_T Memory1_PreviousInput_b;    /* '<S17>/Memory1' */
  uint16_T Memory_PreviousInput_c;     /* '<S17>/Memory' */
} DW_RonDetectEn_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_RonDetectEn_T RonDetectEn_DW;

/* Model entry point functions */
extern void RonDetectEn_initialize(void);

/* Exported entry point function */
extern void RonDetectEn_EOA(void);

/* Exported entry point function */
extern void RonDetectEn_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T EnRonDetect;            /* '<S3>/MEnRonDetect' */

/* Enabling condition for ron strategy by calibration */
extern uint8_T FlgSteadyStateRon;      /* '<S3>/MFlgSteadyStateRon' */

/* Both Rpm and Load are stable for ron detection */
extern uint16_T IDZoneRonLoad;         /* '<S3>/MIDZoneRonLoad' */

/* Load zone index for ron detection */
extern uint16_T IDZoneRonRpm;          /* '<S3>/MIDZoneRonRpm' */

/* Rpm zone index for ron detection */
extern uint8_T OdomEnableRon;          /* '<S3>/MOdomEnableRon' */

/* Ron enabling condition according to odometer value */
extern uint16_T RtZoneRonLoad;         /* '<S3>/MRtZoneRonLoad' */

/* Load zone ratio for ron detection */
extern uint16_T RtZoneRonRpm;          /* '<S3>/MRtZoneRonRpm' */

/* Rpm zone ratio for ron detection */
extern int16_T SAKnockLevel;           /* '<S3>/MSAKnockLevel' */

/* SA knock correction value to enable ron detection */
extern uint8_T TempEnableRon;          /* '<S3>/MTempEnableRon' */

/* Temperature enabling condition for RON estimation */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S18>/Constant' : Unused code path elimination
 * Block '<S18>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Data Type Propagation' : Unused code path elimination
 * Block '<S19>/Data Type Duplicate' : Unused code path elimination
 * Block '<S20>/Data Type Duplicate' : Unused code path elimination
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/Data Type Propagation' : Unused code path elimination
 * Block '<S28>/Data Type Duplicate' : Unused code path elimination
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S18>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S18>/Reshape' : Reshape block reduction
 * Block '<S19>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S19>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S19>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S19>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S20>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S20>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S20>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S20>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S25>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S27>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S27>/Reshape' : Reshape block reduction
 * Block '<S28>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S28>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'RonDetectEn'
 * '<S1>'   : 'RonDetectEn/EOA_fcn'
 * '<S2>'   : 'RonDetectEn/Init_fcn'
 * '<S3>'   : 'RonDetectEn/Merge'
 * '<S4>'   : 'RonDetectEn/RonDetectEn_Scheduler'
 * '<S5>'   : 'RonDetectEn/EOA_fcn/EnableRon_Calib'
 * '<S6>'   : 'RonDetectEn/EOA_fcn/Engine_WP_Stab'
 * '<S7>'   : 'RonDetectEn/EOA_fcn/TempEnableRon_Calculation'
 * '<S8>'   : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/FlagSteady_Calculation'
 * '<S9>'   : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/LoadSteady_Calculation'
 * '<S10>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/RpmSteady_Calculation'
 * '<S11>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/Rpm_Zone_Calculation'
 * '<S12>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/SAKnockLevel_Calculation'
 * '<S13>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/TdcStab_Calculation'
 * '<S14>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/LoadSteady_Calculation/MinLoadRon_Calculation'
 * '<S15>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/LoadSteady_Calculation/Reset_Condition'
 * '<S16>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/LoadSteady_Calculation/SteadyLoad_Threshold'
 * '<S17>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/LoadSteady_Calculation/Steady_State_Detect'
 * '<S18>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/LoadSteady_Calculation/MinLoadRon_Calculation/Look2D_IR_U8_minload'
 * '<S19>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/LoadSteady_Calculation/MinLoadRon_Calculation/PreLookUpIdSearch_S16'
 * '<S20>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/LoadSteady_Calculation/MinLoadRon_Calculation/PreLookUpIdSearch_U16_rpm1'
 * '<S21>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/RpmSteady_Calculation/Reset_Condition'
 * '<S22>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/RpmSteady_Calculation/RpmPerc_Calc'
 * '<S23>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/RpmSteady_Calculation/SteadyRpm_Threshold'
 * '<S24>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/RpmSteady_Calculation/Steady_State_Detect'
 * '<S25>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/Rpm_Zone_Calculation/PreLookUpIdSearch_U16_rpm'
 * '<S26>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/SAKnockLevel_Calculation/ArrangeLSB'
 * '<S27>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/SAKnockLevel_Calculation/Look2D_IR_S8_saklevel'
 * '<S28>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/SAKnockLevel_Calculation/PreLookUpIdSearch_U16_load'
 * '<S29>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/TdcStab_Calculation/LookUp_IR_U16'
 * '<S30>'  : 'RonDetectEn/EOA_fcn/Engine_WP_Stab/TdcStab_Calculation/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S31>'  : 'RonDetectEn/EOA_fcn/TempEnableRon_Calculation/TempEnableRon_Logics'
 */

/*-
 * Requirements for '<Root>': RonDetectEn
 */
#endif                                 /* RTW_HEADER_RonDetectEn_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/