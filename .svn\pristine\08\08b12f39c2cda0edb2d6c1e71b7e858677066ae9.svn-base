/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_cmu.h
 * @brief   SPC5xx GTM CMU header file.
 *
 * @addtogroup CMU
 * @{
 */
#ifndef _GTM_CMU_H_
#define _GTM_CMU_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"

/*lint -e621*/

/**
 * @name    CMU definitions
 * @{
 */
/* General MASK for all GTM CMU configuration register [8..31] valid bit */
#define SPC5_GTM_CMU_CTRL_REG_MASK               0x00FFFFFFUL
#define SPC5_GTM_CMU_CTRL_REG_EN_FXCLK_MASK      0x00C00000UL
#define SPC5_GTM_CMU_CTRL_REG_EN_CLK_MASK        0x0000FFFFUL
#define SPC5_GTM_CMU_CTRL_REG_EN_ECLK_MASK       0x003F0000UL


/** FXU Clock Source CMU_GCLK_EN */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_GCLK_EN     0U
/** FXU Clock Source CMU_CLK0 */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK0        1U
/** FXU Clock Source CMU_CLK1 */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK1        2U
/** FXU Clock Source CMU_CLK2 */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK2        3U
/** FXU Clock Source CMU_CLK3 */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK3        4U
/** FXU Clock Source CMU_CLK4 */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK4        5U
/** FXU Clock Source CMU_CLK5 */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK5        6U
/** FXU Clock Source CMU_CLK6 */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK6        7U
/** FXU Clock Source CMU_CLK7 */
#define  SPC5_GTM_CMU_FXU_SOURCE_CMU_CLK7        8U

/* Register CMU_CLK_EN bit */
#define SPC5_GTM_CMU_EN_CLK0                     0U
#define SPC5_GTM_CMU_EN_CLK1                     2U
#define SPC5_GTM_CMU_EN_CLK2                     4U
#define SPC5_GTM_CMU_EN_CLK3                     6U
#define SPC5_GTM_CMU_EN_CLK4                     8U
#define SPC5_GTM_CMU_EN_CLK5                     10U
#define SPC5_GTM_CMU_EN_CLK6                     12U
#define SPC5_GTM_CMU_EN_CLK7                     14U

#define SPC5_GTM_CMU_EN_ECLK0                    16U
#define SPC5_GTM_CMU_EN_ECLK1                    18U
#define SPC5_GTM_CMU_EN_ECLK2                    20U

#define SPC5_GTM_CMU_EN_FXCLK                    22U

#define SPC5_GTM_CMU_CLK0_ENABLED                (CLK_ENABLE << SPC5_GTM_CMU_EN_CLK0)
#define SPC5_GTM_CMU_CLK1_ENABLED                (CLK_ENABLE << SPC5_GTM_CMU_EN_CLK1)
#define SPC5_GTM_CMU_CLK2_ENABLED                (CLK_ENABLE << SPC5_GTM_CMU_EN_CLK2)
#define SPC5_GTM_CMU_CLK3_ENABLED                (CLK_ENABLE << SPC5_GTM_CMU_EN_CLK3)
#define SPC5_GTM_CMU_CLK4_ENABLED                (CLK_ENABLE << SPC5_GTM_CMU_EN_CLK4)
#define SPC5_GTM_CMU_CLK5_ENABLED                (CLK_ENABLE << SPC5_GTM_CMU_EN_CLK5)
#define SPC5_GTM_CMU_CLK6_ENABLED                (CLK_ENABLE << SPC5_GTM_CMU_EN_CLK6)
#define SPC5_GTM_CMU_CLK7_ENABLED                (CLK_ENABLE << SPC5_GTM_CMU_EN_CLK7)

#define SPC5_GTM_CMU_ECLK0_ENABLED               (CLK_ENABLE << SPC5_GTM_CMU_EN_ECLK0)
#define SPC5_GTM_CMU_ECLK1_ENABLED               (CLK_ENABLE << SPC5_GTM_CMU_EN_ECLK1)
#define SPC5_GTM_CMU_ECLK2_ENABLED               (CLK_ENABLE << SPC5_GTM_CMU_EN_ECLK2)

#define SPC5_GTM_CMU_FXCLK_ENABLED               (CLK_ENABLE << SPC5_GTM_CMU_EN_FXCLK)

#define SPC5_GTM_CMU_CFGU_CLKx_SEL_BIT           (1UL << 24)

/** GTM CMU Global Clock */
#define SPC5_GMT_CMU_GLOBAL_CLK                  0UL
/** GTM CMU CFGU Sub Unit */
#define SPC5_GTM_CMU_CFGU_CLK                    1UL
/** GTM CMU FXU Sub Unit */
#define SPC5_GTM_CMU_FXU_CLK                     2UL
/** GTM CMU EGU Sub Unit */
#define SPC5_GTM_CMU_EGU_CLK                     3UL

/** GTM CMU CLOCK SOURCE CLK0 */
#define SPC5_GTM_CMU_CLK0                        0U
/** GTM CMU CLOCK SOURCE CLK1 */
#define SPC5_GTM_CMU_CLK1                        1U
/** GTM CMU CLOCK SOURCE CLK2 */
#define SPC5_GTM_CMU_CLK2                        2U
/** GTM CMU CLOCK SOURCE CLK3 */
#define SPC5_GTM_CMU_CLK3                        3U
/** GTM CMU CLOCK SOURCE CLK4 */
#define SPC5_GTM_CMU_CLK4                        4U
/** GTM CMU CLOCK SOURCE CLK5 */
#define SPC5_GTM_CMU_CLK5                        5U
/** GTM CMU CLOCK SOURCE CLK6 */
#define SPC5_GTM_CMU_CLK6                        6U
/** GTM CMU CLOCK SOURCE CLK7 */
#define SPC5_GTM_CMU_CLK7                        7U

/** GTM CMU FIXED CLOCK FXCLK0 */
#define SPC5_GTM_CMU_FXCLK0                      0U
/** GTM CMU FIXED CLOCK FXCLK1 */
#define SPC5_GTM_CMU_FXCLK1                      1U
/** GTM CMU FIXED CLOCK FXCLK2 */
#define SPC5_GTM_CMU_FXCLK2                      2U
/** GTM CMU FIXED CLOCK FXCLK3 */
#define SPC5_GTM_CMU_FXCLK3                      3U
/** GTM CMU FIXED CLOCK FXCLK4 */
#define SPC5_GTM_CMU_FXCLK4                      4U

/** GTM CMU EXTERNAL CLOCK EXCLK0 */
#define SPC5_GTM_CMU_EXCLK0                      0U
/** GTM CMU EXTERNAL CLOCK EXCLK1 */
#define SPC5_GTM_CMU_EXCLK1                      1U
/** GTM CMU EXTERNAL CLOCK EXCLK2 */
#define SPC5_GTM_CMU_EXCLK2                      2U

/**
 * @brief Type of a structure representing a (GTM-IP) ATOM driver.
 */
typedef struct GTM_CMUDriver GTM_CMUDriver;

/**
 * @brief   Structure representing a GTM CMU CLK configuration
 */
typedef struct {
	/**
	 * @brief   Clock source enabled  information.
	 */
	uint32_t en_clk;
	/**
	 * @brief   Clock source enabled  information.
	 */
	uint32_t cmu_clk;

}CMUCLKConfig;

/**
 * @brief   Structure representing a GTM CMU CFGU configuration
 */
typedef struct {
	/**
	 * @brief   Clock source for CMU_CLK6.
	 */
	uint32_t clk6_sel;
	/**
	 * @brief   Clock source for CMU_CLK7.
	 */
	uint32_t clk7_sel;
	/**
	 * @brief   Clock count. Defines count value  fot the clock divider of clock source
	 */
	CMUCLKConfig cmuclk[8];
}CFGUConfig;

/**
 * @brief   Structure representing a GTM CMU FXU configuration
 */
typedef struct {
	/**
	 * @brief   Fixed clock subunit enabled.
	 */
	uint32_t en_fxclk;
	/**
	 * @brief   Fixed clock subunit clock source.
	 */
	uint32_t source_fxclk;
	/**
	 * @brief   Fixed clock subunit frequency.
	 */
	uint32_t cmu_fxclk[5];
}FXUConfig;

/**
 * @brief   Structure representing a GTM CMU EGU configuration
 */
typedef struct {
	/**
	 * @brief   External clock subunit enabled.
	 */
	uint32_t en_eclk;
	/**
	 * @brief   External clock subunit frequency.
	 */
	uint32_t cmu_eclk;
}EGUConfig;

/**
 * @brief   Structure representing a GTM CMU configuration
 */
typedef struct  {
	/**
	 * @brief   CFGU subunit pointer.
	 */
	CFGUConfig *cfgu_cfg;
	/**
	 * @brief   FXU subunit pointer.
	 */
	FXUConfig   *fxu_cfg;
	/**
	 * @brief   EGU subunit pointer.
	 */
	EGUConfig   egu_cfg[3];
}CMUConfig;

/**
 * @brief   Structure representing a (GTM) ATOM driver.
 */
struct GTM_CMUDriver {
	/**
	* @brief Pointer to the (GTM CMU) registers block.
	*/
	volatile GTM_CMU_TAG *cmu;
	/**
	* @brief cmu global clock divider signal.
	*/
	uint32_t cmu_gclk_en;
	/**
	* @brief pointer to cmu configuration settings.
	*/
	const CMUConfig *cmu_config;
	/**
	* @brief cmu enable clock source.
	*/
	uint32_t cmu_clk_en;
	/**
	* @brief Pointer for application private data.
	*/
	void *priv;
};
/** @}*/

extern GTM_CMUDriver CMUD1;

#ifdef __cplusplus
extern "C" {
#endif
void gtm_cmuInit(void);
extern void gtm_cmuStart(GTM_CMUDriver *cmud);
extern void gtm_cmuStop(GTM_CMUDriver *cmud);
extern uint32_t gtm_cmuGetClock(GTM_CMUDriver *cmud, uint32_t clock_source, uint8_t clock_index);
#ifdef __cplusplus
}
#endif

/*lint +e621*/
#endif /* _GTM_CMU_H_ */
/** @} */

