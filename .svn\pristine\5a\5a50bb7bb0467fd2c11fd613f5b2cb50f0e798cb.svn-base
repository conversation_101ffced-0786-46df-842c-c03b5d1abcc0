/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           CombTotCorr.c
 **  File Creation Date: 14-Sep-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         CombTotCorr
 **  Model Description:  The aim of this model is to calculate the final injection correction for each cylinder according to cylinder balancing strategy
 **  Model Version:      1.951
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Tue Sep 14 15:36:34 2021
 **
 **  Last Saved Modification:  RoccaG - Tue Sep 14 15:35:48 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "CombTotCorr_out.h"
#include "CombTotCorr_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_COMBTOTCORR_DEF         1951U                     /* Referenced by: '<S2>/Constant7' */

/* Model Version. */
#define MAX_INJ_CORR                   65535U                    /* Referenced by: '<S4>/Constant' */

/* Minimum allowed injection correction for cylinder balancing */
#define MAX_INJ_CORR_OFF               32767U                    /* Referenced by: '<S6>/ONE3' */

/* Offset for maximum allowed value for InjCorrCyl signal (calculated according calibration MAXADDCYLBALAD) */
#define MIN_INJ_CORR                   1U                        /* Referenced by: '<S6>/Constant' */

/* Maxximum allowed injection correction for cylinder balancing */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_COMBTOTCORR_

/* Add a check for each important define */
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Calibration memory section */
/*Start of local calbration section*/

#pragma ghs section rodata=".calib"

/* Definition for custom storage class: ELD_EXPORT_CALIBRATION */
CALQUAL CALQUAL_POST uint16_T MAXADDCYLBALAD = 328U;/* Referenced by:
                                                     * '<S6>/MAXADDCYLBALAD2'
                                                     * '<S6>/MAXADDCYLBALAD3'
                                                     */

/* CylBalAd gain correction saturation */
CALQUAL CALQUAL_POST uint8_T VTBANKSEL[8] = { 0U, 1U, 0U, 1U, 0U, 1U, 0U, 1U } ;
                                      /* Referenced by: '<S4>/FORCEDCYLCORR1' */

/* Select cylinders-banks association: 0 for bank 0, 1 for bank 1 */
#pragma ghs section rodata=default

/*End of calibration section*/

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T FORCECYLCORR = 0U;/* Referenced by: '<S4>/FORCECYLCORR' */

/* Enable forced cylinder correction */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T FORCEDCYLCORR[8] = { 32768U, 32768U,
  32768U, 32768U, 32768U, 32768U, 32768U, 32768U } ;/* Referenced by: '<S4>/FORCEDCYLCORR' */

/* Forced cylinder correction value */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T InjCorrCylTot;                /* '<S3>/Merge4' */

/* Final cylinder balancing correction (InjCorrCyl x InjCorrCylAd) */
uint16_T VtInjCorrCyl[8];              /* '<S3>/Merge2' */

/* Cylinder balancing after adaptive correction */
uint16_T VtInjCorrCylTot[8];           /* '<S3>/Merge' */

/* Final cylinder balancing correction (InjCorrCyl x InjCorrCylAd) */
int32_T VtPiFFS[8];                    /* '<S3>/Merge3' */

/* PI for cylinder balancing after adaptive correction */
uint8_T VtStAdaption[2];               /* '<S3>/Merge1' */

/* Activaction flag of adaptive control on injection correction */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_CombTotCorr;/* '<S2>/Constant7' */

/* Model Version */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Output and update for function-call system: '<Root>/Reset' */
void CombTotCorr_Reset(void)
{
  int32_T i;
  for (i = 0; i < 8; i++) {
    /* SignalConversion generated from: '<S2>/VtInjCorrCylTot' */
    VtInjCorrCylTot[(i)] = 32768U;

    /* SignalConversion generated from: '<S2>/VtInjCorrCyl' */
    VtInjCorrCyl[(i)] = 32768U;

    /* SignalConversion generated from: '<S2>/VtPiFFS' */
    VtPiFFS[(i)] = 0;
  }

  /* SignalConversion generated from: '<S2>/VtStAdaption' */
  for (i = 0; i < 2; i++) {
    VtStAdaption[(i)] = 0U;
  }

  /* End of SignalConversion generated from: '<S2>/VtStAdaption' */

  /* SignalConversion generated from: '<S2>/InjCorrCylTot' incorporates:
   *  Constant: '<S2>/Constant'
   */
  InjCorrCylTot = 32768U;

  /* Constant: '<S2>/Constant7' */
  IdVer_CombTotCorr = ID_VER_COMBTOTCORR_DEF;
}

/* Model step function */
void CombTotCorr_EOA(void)
{
  uint32_T rtb_Product2;
  int32_T rtb_MinMax2;
  uint32_T rtb_MinMax2_0;
  uint16_T tmp;

  /* RootInportFunctionCallGenerator generated from: '<Root>/CombTotCorr_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   */
  /* Outputs for Atomic SubSystem: '<S1>/CylinderBalance_FinalCorrection' */
  /* Assignment: '<S4>/Assignment1' incorporates:
   *  Constant: '<S4>/FORCEDCYLCORR1'
   *  DataTypeConversion: '<S4>/Conversion1'
   *  DataTypeConversion: '<S4>/Conversion3'
   *  DataTypeConversion: '<S4>/Conversion4'
   *  Inport: '<Root>/CylBalEn'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Logic: '<S4>/LogicalOperator'
   *  Selector: '<S4>/Selector_Outold1'
   *  Selector: '<S4>/Selector_Outold3'
   *  SignalConversion generated from: '<S1>/VtStAdaption_old'
   */
  VtStAdaption[(VTBANKSEL[(IonAbsTdcEOA)])] = (uint8_T)(((((int32_T)
    VtStAdaption[(VTBANKSEL[(IonAbsTdcEOA)])]) != 0) || (((int32_T)CylBalEn) !=
    0)) ? 1 : 0);

  /* Switch: '<S4>/Switch1' incorporates:
   *  Constant: '<S4>/FORCECYLCORR'
   *  Constant: '<S4>/FORCEDCYLCORR'
   *  DataTypeConversion: '<S4>/Conversion2'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Selector: '<S4>/Selector_Outold2'
   */
  if (((int32_T)FORCECYLCORR) != 0) {
    InjCorrCylTot = FORCEDCYLCORR[(IonAbsTdcEOA)];
  } else {
    /* Product: '<S4>/Product2' incorporates:
     *  Inport: '<Root>/InjCorrCyl'
     *  Inport: '<Root>/InjCorrCylAd'
     */
    rtb_Product2 = ((((uint32_T)InjCorrCyl) * ((uint32_T)InjCorrCylAd)) >>
                    ((uint32_T)15));

    /* MinMax: '<S4>/MinMax' incorporates:
     *  Constant: '<S4>/Constant'
     *  DataTypeConversion: '<S4>/Conversion'
     */
    if (rtb_Product2 < MAX_INJ_CORR) {
      InjCorrCylTot = (uint16_T)rtb_Product2;
    } else {
      InjCorrCylTot = (uint16_T)MAX_INJ_CORR;
    }

    /* End of MinMax: '<S4>/MinMax' */
  }

  /* End of Switch: '<S4>/Switch1' */

  /* Assignment: '<S4>/Assignment3' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  VtInjCorrCylTot[(IonAbsTdcEOA)] = InjCorrCylTot;

  /* End of Outputs for SubSystem: '<S1>/CylinderBalance_FinalCorrection' */

  /* If: '<S5>/If' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/VtCylBalTrigAdat'
   *  Selector: '<S5>/Selector_Outold2'
   */
  if (((int32_T)VtCylBalTrigAdat[(IonAbsTdcEOA)]) != 0) {
    /* Outputs for IfAction SubSystem: '<S5>/AdaptiveCorrection' incorporates:
     *  ActionPort: '<S6>/ActionPort'
     */
    /* MinMax: '<S6>/MinMax' incorporates:
     *  Constant: '<S6>/Constant'
     *  Inport: '<Root>/InjCorrCylAdNorm'
     */
    if (InjCorrCylAdNorm > ((uint16_T)MIN_INJ_CORR)) {
      tmp = InjCorrCylAdNorm;
    } else {
      tmp = ((uint16_T)MIN_INJ_CORR);
    }

    /* End of MinMax: '<S6>/MinMax' */

    /* Product: '<S6>/Product3' incorporates:
     *  Inport: '<Root>/InjCorrCyl'
     *  Inport: '<Root>/InjCorrCylAd'
     *  Product: '<S6>/Product2'
     */
    rtb_Product2 = (((((uint32_T)InjCorrCylAd) * ((uint32_T)InjCorrCyl)) >>
                     ((uint32_T)15)) << ((uint32_T)15)) / ((uint32_T)tmp);

    /* Sum: '<S6>/Sum4' incorporates:
     *  Constant: '<S6>/MAXADDCYLBALAD2'
     *  Constant: '<S6>/ONE3'
     */
    rtb_MinMax2 = (int32_T)((uint32_T)(((uint32_T)MAXADDCYLBALAD) + ((uint32_T)
      ((uint16_T)MAX_INJ_CORR_OFF))));

    /* MinMax: '<S6>/MinMax1' */
    if (((uint32_T)rtb_MinMax2) < rtb_Product2) {
      rtb_MinMax2_0 = (uint32_T)rtb_MinMax2;
    } else {
      rtb_MinMax2_0 = rtb_Product2;
    }

    /* Sum: '<S6>/Sum3' incorporates:
     *  Constant: '<S6>/MAXADDCYLBALAD3'
     *  Constant: '<S6>/ONE4'
     */
    rtb_Product2 = 32768U - ((uint32_T)MAXADDCYLBALAD);

    /* MinMax: '<S6>/MinMax2' incorporates:
     *  MinMax: '<S6>/MinMax1'
     */
    if (rtb_MinMax2_0 > rtb_Product2) {
      rtb_MinMax2 = (int32_T)rtb_MinMax2_0;
    } else {
      rtb_MinMax2 = (int32_T)rtb_Product2;
    }

    /* Assignment: '<S5>/Assignment1' incorporates:
     *  Constant: '<S6>/ONE1'
     *  DataTypeConversion: '<S6>/Conversion'
     *  MinMax: '<S6>/MinMax2'
     *  Sum: '<S6>/Sum2'
     */
    VtPiFFS[(IonAbsTdcEOA)] = 32768 - ((int32_T)((uint16_T)rtb_MinMax2));

    /* Assignment: '<S5>/Assignment3' incorporates:
     *  DataTypeConversion: '<S6>/Conversion'
     *  MinMax: '<S6>/MinMax2'
     *  SignalConversion generated from: '<S6>/InjCorrCyl_i'
     */
    VtInjCorrCyl[(IonAbsTdcEOA)] = (uint16_T)rtb_MinMax2;

    /* End of Outputs for SubSystem: '<S5>/AdaptiveCorrection' */
  } else {
    /* Outputs for IfAction SubSystem: '<S5>/KeepCorrection' incorporates:
     *  ActionPort: '<S7>/ActionPort'
     */
    /* Assignment: '<S5>/Assignment1' incorporates:
     *  Inport: '<Root>/VtPiFFSBal'
     *  Selector: '<S7>/Selector_Outold1'
     */
    VtPiFFS[(IonAbsTdcEOA)] = VtPiFFSBal[(IonAbsTdcEOA)];

    /* Assignment: '<S5>/Assignment3' incorporates:
     *  Inport: '<Root>/VtInjCorrCylBal'
     *  Selector: '<S7>/Selector_Outold2'
     */
    VtInjCorrCyl[(IonAbsTdcEOA)] = VtInjCorrCylBal[(IonAbsTdcEOA)];

    /* End of Outputs for SubSystem: '<S5>/KeepCorrection' */
  }

  /* End of If: '<S5>/If' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombTotCorr_EOA' */
}

/* Model step function */
void CombTotCorr_NoSync(void)
{
  /* Outputs for Function Call SubSystem: '<Root>/Reset' */
  /* RootInportFunctionCallGenerator generated from: '<Root>/CombTotCorr_NoSync' */
  CombTotCorr_Reset();

  /* End of Outputs for SubSystem: '<Root>/Reset' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombTotCorr_NoSync' */
}

/* Model step function */
void CombTotCorr_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/CombTotCorr_PowerOn' incorporates:
   *  SubSystem: '<Root>/Reset'
   */
  CombTotCorr_Reset();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombTotCorr_PowerOn' */
}

/* Model initialize function */
void CombTotCorr_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint16_T VtInjCorrCylTot[N_CYL_MAX];
uint8_T VtStAdaption[2];
uint16_T VtInjCorrCyl[N_CYL_MAX];
int32_T VtPiFFS[N_CYL_MAX];
void CombTotCorr_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    VtInjCorrCylTot[idx] = 32768u;     /* 1 lsb 2^-15 */
    VtStAdaption[idx] = 0u;
    VtInjCorrCyl[idx] = 32768u;        /* 1 lsb 2^-15 */
    VtPiFFS[idx] = 0;
  }
}

void CombTotCorr_PowerOn(void)
{
  CombTotCorr_Stub();
}

void CombTotCorr_NoSync(void)
{
  CombTotCorr_Stub();
}

void CombTotCorr_EOA(void)
{
  CombTotCorr_Stub();
}

#endif                                 /* _BUILD_COMBTOTCORR_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_coverage                                                          *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/