/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  gtm_eisb_Interface
**  Filename        :  gtm_eisb_Interface.c
**  Created on      :  25-nov-2020 12:00:00
**  Original author :  Carboni M.
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "gtm_eisb_interface.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
ISBDriver EISB;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define BUCK_REF_PH0    0u
#define BUCK_REF_PH1    1u
#define BUCK_REF_PH2    2u
#define BUCK_REF_PH3    3u
#define BUCK_REF_PH4    4u
#define BUCK_REF_PH5    5u
#define BUCK_REF_PH6    6u
#define BUCK_REF_PH7    7u


/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : IGN_HGISet_CmdOutDelay
**
**   Description:
**    Sets the delay for the command out opening.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T CmdOutDelay : delay of the command out activation from the buck enable
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_CmdOutDelay(uint8_T cyl, uint32_T CmdOutDelay)
{
    EISB.config->cylinder_config[cyl]->output_delay = CmdOutDelay;
}

/******************************************************************************/
/******************************************************************************
**   Function    : IGN_HGISet_BuckEnDelay
**
**   Description:
**    Sets the delay for the Buck enable opening.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T BuckEnDelay : delay for Buck enable
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_BuckEnDelay(uint8_T cyl, uint32_T BuckEnDelay)
{
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH0]->duration= BuckEnDelay;
}

/******************************************************************************
**   Function    : IGN_HGISet_BuckRefChargePh
**
**   Description:
**    Sets the duties and the durations of the pwm for the Buck charge phases.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T dutyPh1 : duty for Phase1
**    [in] uint32_T dutyPh2 : duty for Phase2
**    [in] uint32_T dutyPh3 : duty for Phase3
**    [in] uint32_T dutyPh4 : duty for Phase4
**    [in] uint32_T durationPh1 : duration for Phase1
**    [in] uint32_T durationPh2 : duration for Phase2
**    [in] uint32_T durationPh3 : duration for Phase3
**    [in] uint32_T durationPh4 : duration for Phase4
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_BuckRefChargePh(uint8_T cyl, uint32_T dutyPh1, uint32_T durationPh1,
                                            uint32_T dutyPh2, uint32_T durationPh2,
                                            uint32_T dutyPh3, uint32_T durationPh3,
                                            uint32_T dutyPh4, uint32_T durationPh4)
{
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH1]->duty     = dutyPh1;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH1]->duration = durationPh1;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH2]->duty     = dutyPh2;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH2]->duration = durationPh2;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH3]->duty     = dutyPh3;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH3]->duration = durationPh3;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH4]->duty     = dutyPh4;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH4]->duration = durationPh4;
}

/******************************************************************************
**   Function    : IGN_HGISet_BuckRefDisChargePh
**
**   Description:
**    Sets the duties and the durations of the pwm for the Buck discharge phases.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T dutyPh5 : duty for Phase5
**    [in] uint32_T dutyPh6 : duty for Phase6
**    [in] uint32_T dutyPh7 : duty for Phase7
**    [in] uint32_T durationPh5 : duration for Phase5
**    [in] uint32_T durationPh6 : duration for Phase6
**    [in] uint32_T durationPh7 : duration for Phase7
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_BuckRefDisChargePh(uint8_T cyl, uint32_T dutyPh5, uint32_T durationPh5,uint32_T dutyPh6, uint32_T durationPh6,
                                              uint32_T dutyPh7, uint32_T durationPh7)
{
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH5]->duty     = dutyPh5;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH5]->duration = durationPh5;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH6]->duty     = dutyPh6;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH6]->duration = durationPh6;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH7]->duty     = dutyPh7;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH7]->duration = durationPh7;
}

/******************************************************************************
**   Function    : IGN_HGISet_BuckRefPeriod
**
**   Description:
**    Sets the period of the pwm for the Buck.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T period : period value
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_BuckRefPeriod(uint8_T cyl, uint32_T period)
{
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH1]->period = period;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH2]->period = period;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH3]->period = period;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH4]->period = period;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH5]->period = period;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH6]->period = period;
    EISB.config->cylinder_config[cyl]->coil_prog[BUCK_REF_PH7]->period = period;
}

/******************************************************************************
**   Function    : IGN_HGISet_MosTime
**
**   Description:
**    Sets the MOS duration during the charging phase.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T pmosActiveDuration : duration of active pmos
**    [in] uint32_T nmosDelay : delay of the nmos
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_MosTime(uint8_T cyl, uint32_T pmosActiveDuration, uint32_T nmosDelay)
{
    EISB.config->cylinder_config[cyl]->pmos_duration = pmosActiveDuration;
    EISB.config->cylinder_config[cyl]->nmos_duration = nmosDelay;
}

/******************************************************************************
**   Function    : IGN_HGISet_EpwsStatus
**
**   Description:
**    Enables EPWS function for specified cylinder.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T enableEPWS : enabling for the EPWS funcion
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_EpwsStatus(uint8_T cyl, uint32_T status)
{
    EISB.config->cylinder_config[cyl]->epws = status;
}

/******************************************************************************
**   Function    : IGN_HGISet_EpwsPhase
**
**   Description:
**    Set EPWS phase parameters.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint8_T phase : EPWS phase to be configured
**    [in] uint32_T pulseNmb : Number of pulses for the configured phase
**    [in] uint32_T period : Period of the phase
**    [in] uint32_T duty : Duty cycle of the phase
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_EpwsPhase(uint8_T cyl, uint8_T phase, uint32_T pulseNmb, uint32_T period, uint32_T duty)
{
    EISB.config->cylinder_config[cyl]->epws_data[phase]->num_pulse = pulseNmb;
    EISB.config->cylinder_config[cyl]->epws_data[phase]->epws_period = period;
    EISB.config->cylinder_config[cyl]->epws_data[phase]->epws_duty = duty;
}


/******************************************************************************
**   Function    : IGN_HGISet_EpwsTimeOut
**
**   Description:
**    Set EPWS Timeout.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T timeOut : Timeout to be configured
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_EpwsTimeOut(uint8_T cyl, uint32_T timeOut)
{
    EISB.config->cylinder_config[cyl]->epws_timeout = timeOut;
}


/******************************************************************************
**   Function    : IGN_HGISet_EpwsLastPulseDur
**
**   Description:
**    Set EPWS last pulse duration.
**
**   Parameters :
**    [in] uint8_T cyl : cylinder to be configured
**    [in] uint32_T duration : Duration of the last pulse to be configured
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IGN_HGISet_EpwsLastPulseDur(uint8_T cyl, uint32_T duration)
{
    EISB.config->cylinder_config[cyl]->epws_last_pulse_Ton = duration;
}

 
/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/


/****************************************************************************
 ****************************************************************************/

