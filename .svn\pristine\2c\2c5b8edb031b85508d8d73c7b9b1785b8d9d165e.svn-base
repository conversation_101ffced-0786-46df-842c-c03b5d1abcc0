/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                              */
/* $Revision::        $                                                                                                        */
/* $Date::                                                $                                                                    */
/* $Author::                 $                                                                                         */
/*******************************************************************************************************************************/

#ifndef _COM_BOARD_CFG_H_
#define _COM_BOARD_CFG_H_

/* CAN */
/*********PIN ID ****** ***PCR NUMBER****/
#ifndef SPC574K2_EVB_CAN

/* MCAN1 */
#define TXD_MCAN1       12u //PA[12]
#define RXD_MCAN1       13u //PA[13]
#define TXD_MCAN1_SSS (SSS_1)
#define RXD_MCAN1_SSS (SSS_1) 

/* MCAN2 */
#ifndef DISCOVERY_K2_CUT2_3
#define TXD_MCAN2        2u //PA[2]
#define RXD_MCAN2        1u //PA[1]
#define TXD_MCAN2_SSS (SSS_1)
#define RXD_MCAN2_SSS (SSS_4)
#else
#define TXD_MCAN2       12u //PA[12]
#define RXD_MCAN2       13u //PA[13]
#define TXD_MCAN2_SSS (SSS_3)
#define RXD_MCAN2_SSS (SSS_1)
#endif

/* TTCAN */
#define TXD_TTCAN       69u //PE[5]
#define RXD_TTCAN      110u //PG[14]
#define TXD_TTCAN_SSS (SSS_1)
#define RXD_TTCAN_SSS (SSS_2)

#else /* SPC574K2_EVB_CAN */

/* MCAN1 */
#define TXD_MCAN1      10u //PA[10]
#define RXD_MCAN1      11u //PA[11]   --> Shared with IP_IgnCmd_5
#define TXD_MCAN1_SSS (SSS_1)
#define RXD_MCAN1_SSS (SSS_2)

/* MCAN2 */
#define TXD_MCAN2      41u //PC[9]    --> Shared with IP_IgnCmd_3
#define RXD_MCAN2      40u //PC[8]    --> Shared with IP_IgnCmd_4
#define TXD_MCAN2_SSS (SSS_1)
#define RXD_MCAN2_SSS (SSS_3)

/* TTCAN */
#define TXD_TTCAN      69u //PE[5]
#define RXD_TTCAN     110u //PG[14]
#define TXD_TTCAN_SSS (SSS_1)
#define RXD_TTCAN_SSS (SSS_2)

#endif


/* DSPI */
/*********PIN ID ****** ***PCR NUMBER****/
/* DSPI 5 */
#define SPIE_SPSCK  24u // PB[8]
#define SPIE_MISO    3u // PA[3]
#define SPIE_MOSI   27u // PB[11]
#define SPIE_CS0    25u // PB[9] 

#endif

