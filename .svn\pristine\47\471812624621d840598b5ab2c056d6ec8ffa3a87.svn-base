/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Spec/Application/KnockCorrTot/trunk/KnockCorrTot_codegen/K#$  */
/* $Revision:: 209711                                                                                         $  */
/* $Date:: 2022-02-24 12:13:35 +0100 (gio, 24 feb 2022)                                                       $  */
/* $Author:: MarottaR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           KnockCorrTot.c
 **  File Creation Date: 24-Feb-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         KnockCorrTot
 **  Model Description:  The aim of this model is to calculate the final spark advance knock correction for each cylinder.
 **  Model Version:      1.1015
 **  Model Author:       MarottaR - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: MarottaR - Thu Feb 24 11:21:19 2022
 **
 **  Last Saved Modification:  MarottaR - Thu Feb 24 11:20:36 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "KnockCorrTot_out.h"
#include "KnockCorrTot_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/KnockCorrTot_Scheduler' */
#define Knoc_event_KnockCorrTot_PowerOn (0)
#define KnockCo_event_KnockCorrTot_10ms (3)
#define KnockCor_event_KnockCorrTot_EOA (2)
#define Knock_event_KnockCorrTot_NoSync (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKLOADKNOCK3_dim               2U                        /* Referenced by: '<S33>/BKLOADKNOCK4_dim' */

/* Length of breakpoint BKLOADKNOCK3 */
#define BKRPMKNOCK8_dim                7U                        /* Referenced by: '<S33>/BKRPMKNOCK4_dim' */

/* Length of breakpoint BKRPMKNOCK8 */
#define ID_VER_KNOCKCORRTOT_DEF        11015U                    /* Referenced by: '<Root>/KnockCorrTot_Scheduler' */

/* Model Version. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_KNOCKCORRTOT_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint8_T RonLevelUsed_old;       /* '<S4>/Unit Delay' */

/* Old RON level used for SARon calculation */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADKNOCK3[3] = { 5120U, 8960U,
  14080U } ;                           /* Referenced by: '<S33>/BKLOADKNOCK4' */

/* Breakpoints of load for knock learning */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMKNOCK8[8] = { 1000U, 1500U, 2000U,
  3000U, 4000U, 5000U, 6000U, 7000U } ;/* Referenced by: '<S33>/BKRPMKNOCK4' */

/* Breakpoints of engine speed for knock control */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T KCSTEP = 10;/* Referenced by:
                                                      * '<S6>/SlewInd_Mgm'
                                                      * '<S6>/SlewTot_Mgm'
                                                      */

/* Delta on minimum adaptive knock correction */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T KNOCKRECRLDIFF = 16;
                                     /* Referenced by: '<S36>/KNOCKRECRLDIFF' */

/* Rate limiter input-output difference threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T KNOCKRECRLMAX = 16;
                                      /* Referenced by: '<S35>/KNOCKRECRLMAX' */

/* KnockRec positive rate limiter */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T KNOCKRECRLMIN = -96;
                                      /* Referenced by: '<S35>/KNOCKRECRLMIN' */

/* KnockRec negative rate limiter */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBKCORRMAX[60] = { -60, -60, -60, -60,
  -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60,
  -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60,
  -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60, -60,
  -60, -60, -60, -60, -60, -60, -60, -60 } ;/* Referenced by: '<S12>/TBKCORRMAX' */

/* Maximum total knock correction */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBKNOCKREC[24] = { -64, -64, -64, -64,
  -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64,
  -64, -64, -64, -64 } ;               /* Referenced by: '<S33>/TBKNOCKREC' */

/* Table of steps to reduce the SA in case of knock recovery */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T VTDKCORRMIN[5] = { 0, 0, 0, 0, 0 } ;/* Referenced by: '<S13>/VTDKCORRMIN' */

/* SAKnockMin offset for ron level */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTFORCESAK[8] = { 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U } ;            /* Referenced by: '<Root>/KnockCorrTot_Scheduler' */

/* Force the correction for knocking on the spark advance (=1) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTGAINSAKREC[5] = { 128U, 128U, 128U,
  128U, 128U } ;                       /* Referenced by: '<S33>/VTGAINSAKREC' */

/* Recovery correction gain for ron level */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTKCORRMIN[12] = { -96, -96, -96, -96,
  -96, -96, -96, -96, -96, -96, -96, -96 } ;/* Referenced by: '<S13>/VTKCORRMIN' */

/* Minimum total knock correction */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T VTKNOCKRECNOLOAD[8] = { -68, -68, -68,
  -68, -68, -68, -68, -68 } ;      /* Referenced by: '<S33>/VTKNOCKRECNOLOAD' */

/* SA reduction used while DIAG_LOAD is in FAULT. This is used instead of TBKNOCKREC */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTSAKNOCKFORCED[8] = { 0, 0, 0, 0, 0, 0,
  0, 0 } ;                            /* Referenced by: '<S2>/ForceKnock_Mgm' */

/* Forced knock correction */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T FlgSAKIndInc[8];               /* '<S7>/Merge4' */

/* The individual knock corr on SA is positive (=1) */
uint8_T FlgSAKnockInc[8];              /* '<S7>/Merge' */

/* SAKnock is greater than 0 (=1) */
int16_T SAKCorrInd[8];                 /* '<S7>/Merge5' */

/* Individual cylinder knock correction */
int16_T SAKnock[8];                    /* '<S7>/Merge2' */

/* Knock correction on Spark Advance */
int16_T SAKnockCyl;                    /* '<S7>/Merge1' */

/* Knock correction on Spark Advance */
int16_T SAKnockMin;                    /* '<S7>/Merge3' */

/* Minimum value of SAKnock */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint8_T FlgDisKnockRL[8];/* '<S36>/Data Type Conversion3' */

/* Flag to indicate KnockRec rate-limiter disable */
STATIC_TEST_POINT uint8_T FlgSAKnockSat[8];/* '<S14>/Data Type Conversion1' */

/* The knock correction on spark advance is saturated (=1) */
STATIC_TEST_POINT uint32_T IdVer_KnockCorrTot;/* '<Root>/KnockCorrTot_Scheduler' */

/* Model Version */
STATIC_TEST_POINT int16_T KnockRec;    /* '<S42>/Conversion2' */

/* Knock correction in recovery modality */
STATIC_TEST_POINT int16_T KnockRecRL[8];/* '<S7>/Merge11' */

/* KnockRec after rate limiter */
STATIC_TEST_POINT int16_T SAKCorrIndMax[8];/* '<S7>/Merge8' */

/* Maximum value for individual correction */
STATIC_TEST_POINT int16_T SAKnockMax;  /* '<S12>/Switch' */

/* Maximum value of SAKnock */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void Knock_chartstep_c3_KnockCorrTot(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/KnockCorrTot_Scheduler' */
static void Knock_chartstep_c3_KnockCorrTot(const int32_T *sfEvent)
{
  /* local block i/o variables */
  int16_T rtb_Look2D_S8_U16_U16;
  int16_T rtb_LookUp_S8_U16;
  int16_T rtb_RateLimiter_S16;
  int16_T rtb_LookUp_IR_S16;
  int16_T rtb_Look2D_IR_S8;
  uint8_T i;
  int32_T s46_iter;
  boolean_T rtb_FlgDisKnockRLCyl;
  int16_T rtb_Switch2;
  int16_T rtb_Add1;
  int16_T rtb_MinMax2;
  int16_T rtb_Switch1;
  int16_T rtb_Sum1_m;
  uint8_T IndCylRec;
  int16_T tmp;

  /* Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  /* Chart: '<Root>/KnockCorrTot_Scheduler' incorporates:
   *  Inport: '<Root>/FlgSyncPhased'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/KnockCorrMode'
   *  Inport: '<Root>/VtRec'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  /* During: KnockCorrTot_Scheduler */
  /* Entry Internal: KnockCorrTot_Scheduler */
  /* Transition: '<S3>:2' */
  switch (*sfEvent) {
   case Knoc_event_KnockCorrTot_PowerOn:
    /* Transition: '<S3>:4' */
    /* Transition: '<S3>:22' */
    IdVer_KnockCorrTot = ID_VER_KNOCKCORRTOT_DEF;

    /* Outputs for Function Call SubSystem: '<Root>/Reset_fcn'
     *
     * Block description for '<Root>/Reset_fcn':
     *  This block performs outputs initialization.
     */
    /* Outputs for Iterator SubSystem: '<S5>/Reset_VectorsKnock' incorporates:
     *  ForIterator: '<S46>/For Iterator'
     *
     * Block description for '<S5>/Reset_VectorsKnock':
     *  This block performs outputs initialization.
     */
    /* Transition: '<S3>:28'
     * Requirements for Transition: '<S3>:28':
     *  1. EISB_FCA6CYL_SW_REQ_1879: Software shall set to 0 each software variable implemented in knoc... (ECU_SW_Requirements#7025)
     */
    /* Event: '<S3>:26' */
    for (s46_iter = 0; s46_iter < 8; s46_iter++) {
      /* Switch: '<S46>/Switch3' incorporates:
       *  Constant: '<S46>/Constant7'
       *  MultiPortSwitch: '<S46>/Index Vector3'
       *  RelationalOperator: '<S46>/Relational Operator3'
       *  SignalConversion generated from: '<S5>/FlgSAKIndInc_old'
       */
      if (((int32_T)FlgSAKIndInc[(s46_iter)]) != 0) {
        /* SignalConversion generated from: '<S5>/FlgSAKIndInc' incorporates:
         *  Constant: '<S46>/Constant2'
         */
        FlgSAKIndInc[(s46_iter)] = 0U;
      }

      /* Switch: '<S46>/Switch2' incorporates:
       *  Constant: '<S46>/Constant1'
       *  MultiPortSwitch: '<S46>/Index Vector2'
       *  RelationalOperator: '<S46>/Relational Operator2'
       *  SignalConversion generated from: '<S5>/FlgSAKnockInc_old'
       */
      if (((int32_T)FlgSAKnockInc[(s46_iter)]) != 0) {
        /* SignalConversion generated from: '<S5>/FlgSAKnockInc' incorporates:
         *  Constant: '<S46>/Constant3'
         */
        FlgSAKnockInc[(s46_iter)] = 0U;
      }

      /* Switch: '<S46>/Switch1' incorporates:
       *  MultiPortSwitch: '<S46>/Index Vector1'
       *  RelationalOperator: '<S46>/Relational Operator1'
       *  SignalConversion generated from: '<S5>/SAKCorrInd_old'
       */
      if (SAKCorrInd[(s46_iter)] != 0) {
        /* SignalConversion generated from: '<S5>/SAKCorrInd' incorporates:
         *  Constant: '<S46>/Constant6'
         */
        SAKCorrInd[(s46_iter)] = 0;
      }

      /* Switch: '<S46>/Switch' incorporates:
       *  MultiPortSwitch: '<S46>/Index Vector'
       *  RelationalOperator: '<S46>/Relational Operator'
       *  SignalConversion generated from: '<S5>/SAKnock_old'
       */
      if (SAKnock[(s46_iter)] != 0) {
        /* SignalConversion generated from: '<S5>/SAKnock' incorporates:
         *  Constant: '<S46>/Constant4'
         */
        SAKnock[(s46_iter)] = 0;
      }
    }

    /* End of Outputs for SubSystem: '<S5>/Reset_VectorsKnock' */

    /* Chart: '<S5>/Reset_Variables' incorporates:
     *  SignalConversion generated from: '<S5>/KnockRecRL'
     *  SignalConversion generated from: '<S5>/SAKCorrIndMax'
     *
     * Block description for '<S5>/Reset_Variables':
     *  This block performs outputs initialization.
     */
    /* Gateway: Reset_fcn/Reset_Variables */
    /* During: Reset_fcn/Reset_Variables */
    /* Entry Internal: Reset_fcn/Reset_Variables */
    /* Transition: '<S45>:2' */
    for (i = 0U; i < N_CYLINDER; i = (uint8_T)((int32_T)(((int32_T)i) + 1))) {
      /* Transition: '<S45>:4' */
      /* Transition: '<S45>:32' */
      KnockRecRL[(i)] = 0;
      SAKCorrIndMax[(i)] = 0;

      /* Transition: '<S45>:33' */
    }

    /* SignalConversion generated from: '<S5>/SAKnockCyl' incorporates:
     *  Chart: '<S5>/Reset_Variables'
     *
     * Block description for '<S5>/Reset_Variables':
     *  This block performs outputs initialization.
     */
    /* Transition: '<S45>:30' */
    SAKnockCyl = 0;

    /* SignalConversion generated from: '<S5>/SAKnockMin' incorporates:
     *  Chart: '<S5>/Reset_Variables'
     *
     * Block description for '<S5>/Reset_Variables':
     *  This block performs outputs initialization.
     */
    SAKnockMin = -480;

    /* End of Outputs for SubSystem: '<Root>/Reset_fcn' */
    /* Transition: '<S3>:56' */
    /* Transition: '<S3>:93' */
    /* Transition: '<S3>:55' */
    break;

   case Knock_event_KnockCorrTot_NoSync:
    /* Outputs for Function Call SubSystem: '<Root>/Reset_fcn'
     *
     * Block description for '<Root>/Reset_fcn':
     *  This block performs outputs initialization.
     */
    /* Outputs for Iterator SubSystem: '<S5>/Reset_VectorsKnock' incorporates:
     *  ForIterator: '<S46>/For Iterator'
     *
     * Block description for '<S5>/Reset_VectorsKnock':
     *  This block performs outputs initialization.
     */
    /* Transition: '<S3>:6' */
    /* Transition: '<S3>:8' */
    /* Transition: '<S3>:28'
     * Requirements for Transition: '<S3>:28':
     *  1. EISB_FCA6CYL_SW_REQ_1879: Software shall set to 0 each software variable implemented in knoc... (ECU_SW_Requirements#7025)
     */
    /* Event: '<S3>:26' */
    for (s46_iter = 0; s46_iter < 8; s46_iter++) {
      /* Switch: '<S46>/Switch3' incorporates:
       *  Constant: '<S46>/Constant7'
       *  MultiPortSwitch: '<S46>/Index Vector3'
       *  RelationalOperator: '<S46>/Relational Operator3'
       *  SignalConversion generated from: '<S5>/FlgSAKIndInc_old'
       */
      if (((int32_T)FlgSAKIndInc[(s46_iter)]) != 0) {
        /* SignalConversion generated from: '<S5>/FlgSAKIndInc' incorporates:
         *  Constant: '<S46>/Constant2'
         */
        FlgSAKIndInc[(s46_iter)] = 0U;
      }

      /* Switch: '<S46>/Switch2' incorporates:
       *  Constant: '<S46>/Constant1'
       *  MultiPortSwitch: '<S46>/Index Vector2'
       *  RelationalOperator: '<S46>/Relational Operator2'
       *  SignalConversion generated from: '<S5>/FlgSAKnockInc_old'
       */
      if (((int32_T)FlgSAKnockInc[(s46_iter)]) != 0) {
        /* SignalConversion generated from: '<S5>/FlgSAKnockInc' incorporates:
         *  Constant: '<S46>/Constant3'
         */
        FlgSAKnockInc[(s46_iter)] = 0U;
      }

      /* Switch: '<S46>/Switch1' incorporates:
       *  MultiPortSwitch: '<S46>/Index Vector1'
       *  RelationalOperator: '<S46>/Relational Operator1'
       *  SignalConversion generated from: '<S5>/SAKCorrInd_old'
       */
      if (SAKCorrInd[(s46_iter)] != 0) {
        /* SignalConversion generated from: '<S5>/SAKCorrInd' incorporates:
         *  Constant: '<S46>/Constant6'
         */
        SAKCorrInd[(s46_iter)] = 0;
      }

      /* Switch: '<S46>/Switch' incorporates:
       *  MultiPortSwitch: '<S46>/Index Vector'
       *  RelationalOperator: '<S46>/Relational Operator'
       *  SignalConversion generated from: '<S5>/SAKnock_old'
       */
      if (SAKnock[(s46_iter)] != 0) {
        /* SignalConversion generated from: '<S5>/SAKnock' incorporates:
         *  Constant: '<S46>/Constant4'
         */
        SAKnock[(s46_iter)] = 0;
      }
    }

    /* End of Outputs for SubSystem: '<S5>/Reset_VectorsKnock' */

    /* Chart: '<S5>/Reset_Variables' incorporates:
     *  SignalConversion generated from: '<S5>/KnockRecRL'
     *  SignalConversion generated from: '<S5>/SAKCorrIndMax'
     *
     * Block description for '<S5>/Reset_Variables':
     *  This block performs outputs initialization.
     */
    /* Gateway: Reset_fcn/Reset_Variables */
    /* During: Reset_fcn/Reset_Variables */
    /* Entry Internal: Reset_fcn/Reset_Variables */
    /* Transition: '<S45>:2' */
    for (i = 0U; i < N_CYLINDER; i = (uint8_T)((int32_T)(((int32_T)i) + 1))) {
      /* Transition: '<S45>:4' */
      /* Transition: '<S45>:32' */
      KnockRecRL[(i)] = 0;
      SAKCorrIndMax[(i)] = 0;

      /* Transition: '<S45>:33' */
    }

    /* SignalConversion generated from: '<S5>/SAKnockCyl' incorporates:
     *  Chart: '<S5>/Reset_Variables'
     *
     * Block description for '<S5>/Reset_Variables':
     *  This block performs outputs initialization.
     */
    /* Transition: '<S45>:30' */
    SAKnockCyl = 0;

    /* SignalConversion generated from: '<S5>/SAKnockMin' incorporates:
     *  Chart: '<S5>/Reset_Variables'
     *
     * Block description for '<S5>/Reset_Variables':
     *  This block performs outputs initialization.
     */
    SAKnockMin = -480;

    /* End of Outputs for SubSystem: '<Root>/Reset_fcn' */
    /* Transition: '<S3>:56' */
    /* Transition: '<S3>:93' */
    /* Transition: '<S3>:55' */
    break;

   case KnockCor_event_KnockCorrTot_EOA:
    /* Transition: '<S3>:10' */
    /* Transition: '<S3>:12' */
    /* Test force knock */
    if (((int32_T)VTFORCESAK[(IonAbsTdcEOA)]) == 1) {
      /* Outputs for Function Call SubSystem: '<Root>/ForceKnock_fcn'
       *
       * Block description for '<Root>/ForceKnock_fcn':
       *  This block forces outputs in case of test.
       */
      /* Chart: '<S2>/ForceKnock_Mgm' incorporates:
       *  SignalConversion generated from: '<S2>/SAKnock'
       *
       * Block description for '<S2>/ForceKnock_Mgm':
       *  This block forces outputs in case of test.
       */
      /* Transition: '<S3>:81' */
      /* Transition: '<S3>:84' */
      /* Event: '<S3>:86' */
      /* Gateway: ForceKnock_fcn/ForceKnock_Mgm */
      /* During: ForceKnock_fcn/ForceKnock_Mgm */
      /* Entry Internal: ForceKnock_fcn/ForceKnock_Mgm */
      /* Transition: '<S29>:64' */
      /* Transition: '<S29>:56'
       * Requirements for Transition: '<S29>:56':
       *  1. EISB_FCA6CYL_SW_REQ_1876: If the tunable parameter VTFORCESAK for cylinder X is equal to 1,i... (ECU_SW_Requirements#7043)
       */
      SAKnock[(IonAbsTdcEOA)] = VTSAKNOCKFORCED[(IonAbsTdcEOA)];

      /* SignalConversion generated from: '<S2>/SAKnockCyl' incorporates:
       *  Chart: '<S2>/ForceKnock_Mgm'
       *
       * Block description for '<S2>/ForceKnock_Mgm':
       *  This block forces outputs in case of test.
       */
      SAKnockCyl = VTSAKNOCKFORCED[(IonAbsTdcEOA)];

      /* End of Outputs for SubSystem: '<Root>/ForceKnock_fcn' */
      /* Transition: '<S3>:85' */
      /* Transition: '<S3>:35' */
      /* Transition: '<S3>:76' */
      /* Transition: '<S3>:74' */
    } else {
      /* Transition: '<S3>:82' */
      /* Case ev_EOA and Recovery enabled */
      switch (KnockCorrMode) {
       case KNOCK_CORR_REC:
        /* Outputs for Function Call SubSystem: '<Root>/Recovery_fcn'
         *
         * Block description for '<Root>/Recovery_fcn':
         *  In this block are implemented the recovery actions.
         */
        /* Switch: '<S33>/Switch2' incorporates:
         *  Constant: '<S33>/Constant'
         *  Constant: '<S33>/REC_NO_LOAD '
         *  Inport: '<Root>/VtRec'
         *  RelationalOperator: '<S33>/Relational Operator'
         *  Selector: '<S33>/Selector1'
         */
        /* Transition: '<S3>:31' */
        /* Transition: '<S3>:33' */
        /* Event: '<S3>:34' */
        if (((int32_T)VtRec[(REC_NO_LOAD)]) == 0) {
          /* S-Function (Look2D_S8_U16_U16): '<S43>/Look2D_S8_U16_U16' incorporates:
           *  Constant: '<S33>/BKLOADKNOCK4'
           *  Constant: '<S33>/BKLOADKNOCK4_dim'
           *  Constant: '<S33>/BKRPMKNOCK4'
           *  Constant: '<S33>/BKRPMKNOCK4_dim'
           *  Constant: '<S33>/TBKNOCKREC'
           */
          Look2D_S8_U16_U16( &rtb_Look2D_S8_U16_U16, &TBKNOCKREC[0], Load,
                            &BKLOADKNOCK3[0], ((uint8_T)BKLOADKNOCK3_dim), RpmF,
                            &BKRPMKNOCK8[0], ((uint8_T)BKRPMKNOCK8_dim));
          rtb_Switch2 = rtb_Look2D_S8_U16_U16;
        } else {
          /* S-Function (LookUp_S8_U16): '<S44>/LookUp_S8_U16' incorporates:
           *  Constant: '<S33>/BKRPMKNOCK4'
           *  Constant: '<S33>/BKRPMKNOCK4_dim'
           *  Constant: '<S33>/VTKNOCKRECNOLOAD'
           */
          LookUp_S8_U16( &rtb_LookUp_S8_U16, &VTKNOCKRECNOLOAD[0], RpmF,
                        &BKRPMKNOCK8[0], ((uint8_T)BKRPMKNOCK8_dim));
          rtb_Switch2 = rtb_LookUp_S8_U16;
        }

        /* DataTypeConversion: '<S42>/Conversion2' incorporates:
         *  Constant: '<S33>/VTGAINSAKREC'
         *  Inport: '<Root>/RonLevelUsed'
         *  Product: '<S33>/Divide3'
         *  Product: '<S41>/Divide'
         *  Product: '<S42>/Divide'
         *  Selector: '<S33>/Selector'
         *
         * Block requirements for '<S33>/Divide3':
         *  1. EISB_FCA6CYL_SW_REQ_1874: The signal KnockRec shall be calculated starting from a 2-D lookup... (ECU_SW_Requirements#7049)
         */
        KnockRec = (int16_T)((((((int32_T)rtb_Switch2) + 15360) / 8) * ((int32_T)
          VTGAINSAKREC[(RonLevelUsed)])) / 128);

        /* Selector: '<S35>/Selector2' incorporates:
         *  SignalConversion generated from: '<S4>/KnockRecRL_old'
         */
        rtb_Switch2 = KnockRecRL[(IonAbsTdcEOA)];

        /* Sum: '<S36>/Add1' */
        rtb_Add1 = (int16_T)(KnockRec - rtb_Switch2);

        /* Abs: '<S36>/Abs' */
        if (rtb_Add1 < 0) {
          rtb_Add1 = (int16_T)(-rtb_Add1);
        }

        /* Logic: '<S36>/Logical Operator1' incorporates:
         *  Constant: '<S36>/Constant'
         *  Constant: '<S36>/KNOCKRECRLDIFF'
         *  RelationalOperator: '<S36>/Relational Operator'
         *  RelationalOperator: '<S36>/Relational Operator1'
         *  Selector: '<S36>/Selector1'
         *  SignalConversion generated from: '<S4>/FlgDisKnockRL_old'
         *
         * Block requirements for '<S36>/Logical Operator1':
         *  1. EISB_FCA6CYL_SW_REQ_1875: The flag FlgDisKnockRL for cylinder X shall be equal to1 if:
           -the ... (ECU_SW_Requirements#7050)
         */
        rtb_FlgDisKnockRLCyl = ((rtb_Add1 <= KNOCKRECRLDIFF) || (((int32_T)
          FlgDisKnockRL[(IonAbsTdcEOA)]) != 0));

        /* Switch: '<S36>/Switch' */
        if (rtb_FlgDisKnockRLCyl) {
          rtb_Switch2 = KnockRec;
        } else {
          /* S-Function (RateLimiter_S16): '<S38>/RateLimiter_S16' incorporates:
           *  Constant: '<S35>/KNOCKRECRLMAX'
           *  Constant: '<S35>/KNOCKRECRLMIN'
           */
          RateLimiter_S16( &rtb_RateLimiter_S16, KnockRec, rtb_Switch2,
                          KNOCKRECRLMIN, KNOCKRECRLMAX);
          rtb_Switch2 = rtb_RateLimiter_S16;
        }

        /* Chart: '<S36>/FlgDisKnock_Mgm' incorporates:
         *  DataTypeConversion: '<S36>/Data Type Conversion3'
         *  Inport: '<Root>/RonLevelUsed'
         *  RelationalOperator: '<S36>/Relational Operator2'
         *  UnitDelay: '<S4>/Unit Delay'
         *
         * Block description for '<S36>/FlgDisKnock_Mgm':
         *  In this block is assigned to the correspond output the signal
         *  previously calculated
         */
        /* Gateway: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRLCyl_Calc/FlgDisKnock_Mgm */
        /* During: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRLCyl_Calc/FlgDisKnock_Mgm */
        /* Entry Internal: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRLCyl_Calc/FlgDisKnock_Mgm */
        /* Transition: '<S39>:50' */
        if (RonLevelUsed != RonLevelUsed_old) {
          /* Transition: '<S39>:52' */
          for (i = 0U; i < N_CYLINDER; i = (uint8_T)((int32_T)(((int32_T)i) + 1)))
          {
            /* Transition: '<S39>:56' */
            /* Transition: '<S39>:60' */
            FlgDisKnockRL[(i)] = 0U;

            /* Transition: '<S39>:61' */
          }

          /* Transition: '<S39>:58' */
          /* Transition: '<S39>:62' */
        } else {
          /* Transition: '<S39>:54' */
          FlgDisKnockRL[(IonAbsTdcEOA)] = (uint8_T)(rtb_FlgDisKnockRLCyl ?
            ((uint8_T)1) : ((uint8_T)0));
        }

        /* Chart: '<S35>/KnockRecRL_Mgm' incorporates:
         *  SignalConversion generated from: '<S4>/KnockRecRL'
         *
         * Block description for '<S35>/KnockRecRL_Mgm':
         *  In this block is assigned to the correspond output the signal
         *  previously calculated
         */
        /* Transition: '<S39>:64' */
        /* Gateway: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRL_Mgm */
        /* During: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRL_Mgm */
        /* Entry Internal: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRL_Mgm */
        /* Transition: '<S37>:68' */
        /* Transition: '<S37>:64' */
        KnockRecRL[(IonAbsTdcEOA)] = rtb_Switch2;

        /* DataTypeConversion: '<S34>/Conversion2' incorporates:
         *  Inport: '<Root>/KnockRecGain'
         *  Product: '<S32>/Divide2'
         *  Product: '<S34>/Divide'
         *
         * Block requirements for '<S32>/Divide2':
         *  1. EISB_FCA6CYL_SW_REQ_1287: The software shall use a recovery value for the Individual cylinde... (ECU_SW_Requirements#2135)
         */
        rtb_Switch2 = (int16_T)((((int32_T)rtb_Switch2) * ((int32_T)KnockRecGain))
          / 4096);

        /* Chart: '<S4>/Recovery_Mgm' incorporates:
         *  Inport: '<Root>/SAKnockCorrAd'
         *  SignalConversion generated from: '<S4>/FlgSAKIndInc'
         *  SignalConversion generated from: '<S4>/FlgSAKnockInc'
         *  SignalConversion generated from: '<S4>/SAKCorrInd'
         *  SignalConversion generated from: '<S4>/SAKnock'
         *
         * Block description for '<S4>/Recovery_Mgm':
         *  In this block are assigned to the correspond outputs the following signals previously calculated, in case of recovery activeted:
         *  - SAKnockCyl
         *  - SAKnock
         *  - SAKCorInd
         */
        /* Gateway: Recovery_fcn/Recovery_Mgm */
        /* During: Recovery_fcn/Recovery_Mgm */
        /* Entry Internal: Recovery_fcn/Recovery_Mgm */
        /* Transition: '<S31>:49' */
        /* Transition: '<S31>:2'
         * Requirements for Transition: '<S31>:2':
         *  1. EISB_FCA6CYL_SW_REQ_1301: The software shall use a recovery value for Knock correction on Sp... (ECU_SW_Requirements#2143)
         */
        SAKCorrInd[(IonAbsTdcEOA)] = rtb_Switch2;
        SAKnockCyl = (int16_T)(rtb_Switch2 + SAKnockCorrAd[(IonAbsTdcEOA)]);
        SAKnock[(IonAbsTdcEOA)] = SAKnockCyl;
        FlgSAKIndInc[(IonAbsTdcEOA)] = 0U;
        FlgSAKnockInc[(IonAbsTdcEOA)] = 0U;

        /* Update for UnitDelay: '<S4>/Unit Delay' incorporates:
         *  Inport: '<Root>/RonLevelUsed'
         */
        RonLevelUsed_old = RonLevelUsed;

        /* End of Outputs for SubSystem: '<Root>/Recovery_fcn' */
        /* Transition: '<S3>:35' */
        /* Transition: '<S3>:76' */
        /* Transition: '<S3>:74' */
        break;

       case KNOCK_CORR_SLEW:
        /* Outputs for Function Call SubSystem: '<Root>/Slew_fcn'
         *
         * Block description for '<Root>/Slew_fcn':
         *  In this block are implemented actions in case of slew modality.
         */
        /* Chart: '<S6>/SlewInd_Mgm' incorporates:
         *  SignalConversion generated from: '<S6>/KnockRecRL'
         *  SignalConversion generated from: '<S6>/SAKCorrInd_old'
         *
         * Block description for '<S6>/SlewInd_Mgm':
         *  In this block is estimated the knock correction on spark advance for
         *  individual cylinder in case of slew modality
         */
        /* Transition: '<S3>:65' */
        /* Case ev_EOA and Slew modality */
        /* Transition: '<S3>:67' */
        /* Transition: '<S3>:77' */
        /* Event: '<S3>:78' */
        /* Gateway: Slew_fcn/SlewInd_Mgm */
        /* During: Slew_fcn/SlewInd_Mgm */
        /* Entry Internal: Slew_fcn/SlewInd_Mgm */
        /* Transition: '<S47>:2' */
        if (SAKCorrInd[(IonAbsTdcEOA)] > 0) {
          /* Transition: '<S47>:4' */
          /* Transition: '<S47>:32'
           * Requirements for Transition: '<S47>:32':
           *  1. EISB_FCA6CYL_SW_REQ_1266: In "SLEW" modality, if the Individual cylinder knock correction (i... (ECU_SW_Requirements#2088)
           */
          rtb_Switch2 = (int16_T)(SAKCorrInd[(IonAbsTdcEOA)] - KCSTEP);
          rtb_Switch2 = (int16_T)((rtb_Switch2 > 0) ? ((int32_T)rtb_Switch2) : 0);
          SAKCorrInd[(IonAbsTdcEOA)] = rtb_Switch2;
          KnockRecRL[(IonAbsTdcEOA)] = (int16_T)(rtb_Switch2 * 4);

          /* Transition: '<S47>:51' */
          /* Transition: '<S47>:50' */
        } else {
          /* Transition: '<S47>:30' */
          if (SAKCorrInd[(IonAbsTdcEOA)] < 0) {
            /* Transition: '<S47>:45' */
            /* Transition: '<S47>:49' */
            rtb_Switch2 = (int16_T)(SAKCorrInd[(IonAbsTdcEOA)] + KCSTEP);
            rtb_Switch2 = (int16_T)((rtb_Switch2 < 0) ? ((int32_T)rtb_Switch2) :
              0);
            SAKCorrInd[(IonAbsTdcEOA)] = rtb_Switch2;
            KnockRecRL[(IonAbsTdcEOA)] = (int16_T)(rtb_Switch2 * 4);

            /* Transition: '<S47>:50' */
          } else {
            /* Transition: '<S47>:47' */
          }
        }

        /* End of Chart: '<S6>/SlewInd_Mgm' */

        /* Chart: '<S6>/SlewTot_Mgm' incorporates:
         *  SignalConversion generated from: '<S6>/SAKnock_old'
         *
         * Block description for '<S6>/SlewTot_Mgm':
         *  In this block is estimated the total knock correction on spark advance
         *  in case of slew modality
         */
        /* Transition: '<S47>:53' */
        /* Gateway: Slew_fcn/SlewTot_Mgm */
        /* During: Slew_fcn/SlewTot_Mgm */
        /* Entry Internal: Slew_fcn/SlewTot_Mgm */
        /* Transition: '<S48>:49' */
        if (SAKnock[(IonAbsTdcEOA)] > 0) {
          /* Transition: '<S48>:53' */
          /* Transition: '<S48>:56'
           * Requirements for Transition: '<S48>:56':
           *  1. EISB_FCA6CYL_SW_REQ_1268: In "SLEW" modality, if the Knock correction on Spark Advance (i.e.... (ECU_SW_Requirements#2089)
           */
          rtb_Switch2 = (int16_T)(SAKnock[(IonAbsTdcEOA)] - KCSTEP);
          SAKnockCyl = (int16_T)((rtb_Switch2 > 0) ? ((int32_T)rtb_Switch2) : 0);
          SAKnock[(IonAbsTdcEOA)] = SAKnockCyl;

          /* Transition: '<S48>:45' */
          /* Transition: '<S48>:57' */
        } else {
          /* Transition: '<S48>:55' */
          if (SAKnock[(IonAbsTdcEOA)] < 0) {
            /* Transition: '<S48>:48' */
            /* Transition: '<S48>:61' */
            rtb_Switch2 = (int16_T)(SAKnock[(IonAbsTdcEOA)] + KCSTEP);
            SAKnockCyl = (int16_T)((rtb_Switch2 < 0) ? ((int32_T)rtb_Switch2) :
              0);
            SAKnock[(IonAbsTdcEOA)] = SAKnockCyl;

            /* Transition: '<S48>:57' */
          } else {
            /* Transition: '<S48>:58' */
            SAKnockCyl = SAKnock[(IonAbsTdcEOA)];
          }
        }

        /* End of Chart: '<S6>/SlewTot_Mgm' */
        /* End of Outputs for SubSystem: '<Root>/Slew_fcn' */
        /* Transition: '<S48>:60' */
        /* Transition: '<S3>:76' */
        /* Transition: '<S3>:74' */
        break;

       case KNOCK_CORR_OFF:
        /* Outputs for Function Call SubSystem: '<Root>/Reset_fcn'
         *
         * Block description for '<Root>/Reset_fcn':
         *  This block performs outputs initialization.
         */
        /* Outputs for Iterator SubSystem: '<S5>/Reset_VectorsKnock' incorporates:
         *  ForIterator: '<S46>/For Iterator'
         *
         * Block description for '<S5>/Reset_VectorsKnock':
         *  This block performs outputs initialization.
         */
        /* Transition: '<S3>:69' */
        /* Case ev_EOA and strategy disabled */
        /* Transition: '<S3>:71' */
        /* Transition: '<S3>:73'
         * Requirements for Transition: '<S3>:73':
         *  1. EISB_FCA6CYL_SW_REQ_1264: When the knock control is disabled, the Individual cylinder knock ... (ECU_SW_Requirements#2086)
         *  2. EISB_FCA6CYL_SW_REQ_1265: When the knock control is disabled, the Knock correction on Spark ... (ECU_SW_Requirements#2087)
         */
        /* Event: '<S3>:26' */
        for (s46_iter = 0; s46_iter < 8; s46_iter++) {
          /* Switch: '<S46>/Switch3' incorporates:
           *  Constant: '<S46>/Constant7'
           *  MultiPortSwitch: '<S46>/Index Vector3'
           *  RelationalOperator: '<S46>/Relational Operator3'
           *  SignalConversion generated from: '<S5>/FlgSAKIndInc_old'
           */
          if (((int32_T)FlgSAKIndInc[(s46_iter)]) != 0) {
            /* SignalConversion generated from: '<S5>/FlgSAKIndInc' incorporates:
             *  Constant: '<S46>/Constant2'
             */
            FlgSAKIndInc[(s46_iter)] = 0U;
          }

          /* Switch: '<S46>/Switch2' incorporates:
           *  Constant: '<S46>/Constant1'
           *  MultiPortSwitch: '<S46>/Index Vector2'
           *  RelationalOperator: '<S46>/Relational Operator2'
           *  SignalConversion generated from: '<S5>/FlgSAKnockInc_old'
           */
          if (((int32_T)FlgSAKnockInc[(s46_iter)]) != 0) {
            /* SignalConversion generated from: '<S5>/FlgSAKnockInc' incorporates:
             *  Constant: '<S46>/Constant3'
             */
            FlgSAKnockInc[(s46_iter)] = 0U;
          }

          /* Switch: '<S46>/Switch1' incorporates:
           *  MultiPortSwitch: '<S46>/Index Vector1'
           *  RelationalOperator: '<S46>/Relational Operator1'
           *  SignalConversion generated from: '<S5>/SAKCorrInd_old'
           */
          if (SAKCorrInd[(s46_iter)] != 0) {
            /* SignalConversion generated from: '<S5>/SAKCorrInd' incorporates:
             *  Constant: '<S46>/Constant6'
             */
            SAKCorrInd[(s46_iter)] = 0;
          }

          /* Switch: '<S46>/Switch' incorporates:
           *  MultiPortSwitch: '<S46>/Index Vector'
           *  RelationalOperator: '<S46>/Relational Operator'
           *  SignalConversion generated from: '<S5>/SAKnock_old'
           */
          if (SAKnock[(s46_iter)] != 0) {
            /* SignalConversion generated from: '<S5>/SAKnock' incorporates:
             *  Constant: '<S46>/Constant4'
             */
            SAKnock[(s46_iter)] = 0;
          }
        }

        /* End of Outputs for SubSystem: '<S5>/Reset_VectorsKnock' */

        /* Chart: '<S5>/Reset_Variables' incorporates:
         *  SignalConversion generated from: '<S5>/KnockRecRL'
         *  SignalConversion generated from: '<S5>/SAKCorrIndMax'
         *
         * Block description for '<S5>/Reset_Variables':
         *  This block performs outputs initialization.
         */
        /* Gateway: Reset_fcn/Reset_Variables */
        /* During: Reset_fcn/Reset_Variables */
        /* Entry Internal: Reset_fcn/Reset_Variables */
        /* Transition: '<S45>:2' */
        for (i = 0U; i < N_CYLINDER; i = (uint8_T)((int32_T)(((int32_T)i) + 1)))
        {
          /* Transition: '<S45>:4' */
          /* Transition: '<S45>:32' */
          KnockRecRL[(i)] = 0;
          SAKCorrIndMax[(i)] = 0;

          /* Transition: '<S45>:33' */
        }

        /* SignalConversion generated from: '<S5>/SAKnockCyl' incorporates:
         *  Chart: '<S5>/Reset_Variables'
         *
         * Block description for '<S5>/Reset_Variables':
         *  This block performs outputs initialization.
         */
        /* Transition: '<S45>:30' */
        SAKnockCyl = 0;

        /* SignalConversion generated from: '<S5>/SAKnockMin' incorporates:
         *  Chart: '<S5>/Reset_Variables'
         *
         * Block description for '<S5>/Reset_Variables':
         *  This block performs outputs initialization.
         */
        SAKnockMin = -480;

        /* End of Outputs for SubSystem: '<Root>/Reset_fcn' */
        /* Transition: '<S3>:74' */
        break;

       default:
        /* Outputs for Function Call SubSystem: '<Root>/EOA_fcn'
         *
         * Block description for '<Root>/EOA_fcn':
         *  This block performs EOA functionalities.
         */
        /* Switch: '<S12>/Switch' incorporates:
         *  Constant: '<S12>/Constant'
         *  DataTypeConversion: '<S12>/Data Type Conversion'
         *  Inport: '<Root>/FlgNoTrqCtrSA'
         *  Product: '<S21>/Divide'
         *
         * Block requirements for '<S12>/Switch':
         *  1. EISB_FCA6CYL_SW_REQ_1256: The Maximum value of SAKnock (i.e. signal SAKnockMax) shall be cal... (ECU_SW_Requirements#2125)
         *  2. EISB_FCA6CYL_SW_REQ_1257: The Maximum value of SAKnock (i.e. signal SAKnockMax) shall be equ... (ECU_SW_Requirements#2126)
         */
        /* Transition: '<S3>:24' */
        /* Event: '<S3>:25' */
        if (((int32_T)FlgNoTrqCtrSA) != 0) {
          /* S-Function (Look2D_IR_S8): '<S22>/Look2D_IR_S8' incorporates:
           *  Constant: '<S12>/TBKCORRMAX'
           *  Constant: '<S20>/BKLOADADKNOCK_dim1'
           *  Constant: '<S20>/BKRPMKNOCK12_dim1'
           */
          Look2D_IR_S8( &rtb_Look2D_IR_S8, &TBKCORRMAX[0], IDZoneKnockLoad,
                       RtZoneKnockLoad, BKLOADADKNOCK_dim, IDZoneKnockRpm,
                       RtZoneKnockRpm, BKRPMKNOCK12_dim);
          SAKnockMax = (int16_T)((rtb_Look2D_IR_S8 + 15360) / 32);
        } else {
          SAKnockMax = 0;
        }

        /* End of Switch: '<S12>/Switch' */

        /* MinMax: '<S9>/MinMax1' incorporates:
         *  Constant: '<S9>/Constant1'
         *  Inport: '<Root>/SAKnockCorrAd'
         *  Selector: '<S8>/Selector'
         */
        if (0 < SAKnockCorrAd[(IonAbsTdcEOA)]) {
          tmp = 0;
        } else {
          tmp = SAKnockCorrAd[(IonAbsTdcEOA)];
        }

        /* End of MinMax: '<S9>/MinMax1' */

        /* Sum: '<S9>/Sum'
         *
         * Block requirements for '<S9>/Sum':
         *  1. EISB_FCA6CYL_SW_REQ_1857: The Individual cylinder knock correction (i.e. signal SAKCorrInd) ... (ECU_SW_Requirements#7033)
         */
        rtb_Switch2 = (int16_T)(SAKnockMax - tmp);

        /* Sum: '<S9>/Sum1' incorporates:
         *  Inport: '<Root>/DeltaKCorrCyl'
         *  Selector: '<S9>/Selector1'
         *  SignalConversion generated from: '<S1>/SAKCorrInd_old'
         */
        rtb_Add1 = (int16_T)(SAKCorrInd[(IonAbsTdcEOA)] + DeltaKCorrCyl);

        /* Switch: '<S9>/Switch' incorporates:
         *  DataTypeConversion: '<S9>/Data Type Conversion'
         *  Inport: '<Root>/DeltaSAKnockCorrAd'
         *  Inport: '<Root>/TrigKnockAdat'
         *  MinMax: '<S9>/MinMax2'
         *  Selector: '<S9>/Selector2'
         *  Selector: '<S9>/Selector3'
         *  Selector: '<S9>/Selector4'
         *  SignalConversion generated from: '<S1>/SAKCorrIndMax_old'
         *  Sum: '<S9>/Sum2'
         *
         * Block requirements for '<S9>/Switch':
         *  1. EISB_FCA6CYL_SW_REQ_1822: If the signal TrigKnockAdat is equal to FALSE the software shall c... (ECU_SW_Requirements#6977)
         *
         * Block requirements for '<S9>/Sum2':
         *  1. EISB_FCA6CYL_SW_REQ_1856: If the signal TrigKnockAdat is equal to TRUE the software shall ca... (ECU_SW_Requirements#7032)
         */
        if (((int32_T)TrigKnockAdat[(IonAbsTdcEOA)]) != 0) {
          /* MinMax: '<S9>/MinMax2' incorporates:
           *  Selector: '<S9>/Selector2'
           *  SignalConversion generated from: '<S1>/SAKCorrIndMax_old'
           */
          if (rtb_Add1 >= SAKCorrIndMax[(IonAbsTdcEOA)]) {
            rtb_Add1 = SAKCorrIndMax[(IonAbsTdcEOA)];
          }

          rtb_Add1 -= DeltaSAKnockCorrAd[(IonAbsTdcEOA)];
        } else {
          if (rtb_Add1 >= SAKCorrIndMax[(IonAbsTdcEOA)]) {
            rtb_Add1 = SAKCorrIndMax[(IonAbsTdcEOA)];
          }
        }

        /* End of Switch: '<S9>/Switch' */

        /* MinMax: '<S15>/MinMax2' */
        if (rtb_Switch2 < rtb_Add1) {
          rtb_MinMax2 = rtb_Switch2;
        } else {
          rtb_MinMax2 = rtb_Add1;
        }

        /* End of MinMax: '<S15>/MinMax2' */

        /* S-Function (LookUp_IR_S16): '<S24>/LookUp_IR_S16' incorporates:
         *  Constant: '<S13>/VTKCORRMIN'
         *  Constant: '<S23>/BKRPMKNOCK12_dim'
         */
        LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTKCORRMIN[0], IDZoneKnockRpm,
                      RtZoneKnockRpm, BKRPMKNOCK12_dim);

        /* Sum: '<S13>/Sum2' incorporates:
         *  Constant: '<S13>/VTDKCORRMIN'
         *  Inport: '<Root>/RonLevelUsed'
         *  Selector: '<S13>/Selector3'
         *
         * Block requirements for '<S13>/Sum2':
         *  1. EISB_FCA6CYL_SW_REQ_1259: The Minimum value of SAKnock (i.e. signal SAKnockMin) shall be cal... (ECU_SW_Requirements#2128)
         */
        SAKnockMin = (int16_T)((((int16_T)VTDKCORRMIN[(RonLevelUsed)]) * 4) +
          rtb_LookUp_IR_S16);

        /* Switch: '<S9>/Switch1' incorporates:
         *  Constant: '<S9>/Constant2'
         *  Inport: '<Root>/VtP2NoiseDetFlg'
         *  Inport: '<Root>/VtP2NoiseDetRec'
         *  RelationalOperator: '<S9>/Relational Operator'
         *  Selector: '<S8>/Selector1'
         *  Selector: '<S8>/Selector2'
         *
         * Block requirements for '<S9>/Switch1':
         *  1. EISB_FCA6CYL_SW_REQ_1858: The Individual cylinder knock correction (i.e. signal SAKCorrInd) ... (ECU_SW_Requirements#7034)
         */
        if (((int32_T)VtP2NoiseDetFlg[(IonAbsTdcEOA)]) == 0) {
          rtb_Switch1 = SAKnockMin;
        } else {
          rtb_Switch1 = VtP2NoiseDetRec[(IonAbsTdcEOA)];
        }

        /* End of Switch: '<S9>/Switch1' */

        /* MinMax: '<S15>/MinMax1' */
        if (rtb_MinMax2 > rtb_Switch1) {
          rtb_Switch1 = rtb_MinMax2;
        }

        /* End of MinMax: '<S15>/MinMax1' */

        /* MinMax: '<S11>/MinMax' incorporates:
         *  Constant: '<S11>/Constant'
         *  Inport: '<Root>/SAKnockCorrAd'
         *  Selector: '<S8>/Selector'
         */
        if (0 > SAKnockCorrAd[(IonAbsTdcEOA)]) {
          tmp = 0;
        } else {
          tmp = SAKnockCorrAd[(IonAbsTdcEOA)];
        }

        /* End of MinMax: '<S11>/MinMax' */

        /* Sum: '<S11>/Sum' */
        rtb_MinMax2 = (int16_T)(SAKnockMax + tmp);

        /* Sum: '<S11>/Sum1' incorporates:
         *  Inport: '<Root>/SAKnockCorrAd'
         *  Inport: '<Root>/SATAir'
         *  Inport: '<Root>/SATipIn'
         *  Selector: '<S8>/Selector'
         *
         * Block requirements for '<S11>/Sum1':
         *  1. EISB_FCA6CYL_SW_REQ_1253: The software shall estimate the Knock correction on Spark Advance ... (ECU_SW_Requirements#2123)
         */
        rtb_Sum1_m = (int16_T)(((SATipIn + SAKnockCorrAd[(IonAbsTdcEOA)]) +
          SATAir) + rtb_Switch1);

        /* MinMax: '<S19>/MinMax2'
         *
         * Block requirements for '<S19>/MinMax2':
         *  1. EISB_FCA6CYL_SW_REQ_1258: The Knock correction on Spark Advance (i.e. signal SAKnock ) shall... (ECU_SW_Requirements#2127)
         */
        if (rtb_MinMax2 >= rtb_Sum1_m) {
          rtb_MinMax2 = rtb_Sum1_m;
        }

        /* End of MinMax: '<S19>/MinMax2' */

        /* Switch: '<S11>/Switch' incorporates:
         *  DataTypeConversion: '<S11>/Data Type Conversion'
         *  Inport: '<Root>/SAKnockCorrAd'
         *  Inport: '<Root>/VtP2NoiseDetFlg'
         *  Inport: '<Root>/VtP2NoiseDetRec'
         *  MinMax: '<S11>/MinMax1'
         *  Selector: '<S8>/Selector'
         *  Selector: '<S8>/Selector1'
         *  Selector: '<S8>/Selector2'
         *  Sum: '<S11>/Sum3'
         *
         * Block requirements for '<S11>/Switch':
         *  1. EISB_FCA6CYL_SW_REQ_1274: The Knock correction minimum value shall be equal to the engine no... (ECU_SW_Requirements#2129)
         */
        if (((int32_T)VtP2NoiseDetFlg[(IonAbsTdcEOA)]) != 0) {
          SAKnockCyl = VtP2NoiseDetRec[(IonAbsTdcEOA)];
        } else {
          if (0 < SAKnockCorrAd[(IonAbsTdcEOA)]) {
            /* MinMax: '<S11>/MinMax1' incorporates:
             *  Constant: '<S11>/Constant1'
             */
            tmp = 0;
          } else {
            /* MinMax: '<S11>/MinMax1' incorporates:
             *  Inport: '<Root>/SAKnockCorrAd'
             *  Selector: '<S8>/Selector'
             */
            tmp = SAKnockCorrAd[(IonAbsTdcEOA)];
          }

          SAKnockCyl = (int16_T)(SAKnockMin + tmp);
        }

        /* End of Switch: '<S11>/Switch' */

        /* MinMax: '<S19>/MinMax1'
         *
         * Block requirements for '<S19>/MinMax1':
         *  1. EISB_FCA6CYL_SW_REQ_1254: The Knock correction on Spark Advance (i.e. signal SAKnock ) shall... (ECU_SW_Requirements#2124)
         */
        if (rtb_MinMax2 > SAKnockCyl) {
          SAKnockCyl = rtb_MinMax2;
        }

        /* End of MinMax: '<S19>/MinMax1' */

        /* Chart: '<S10>/FlgSAKIndInc_Calculation' incorporates:
         *  RelationalOperator: '<S10>/Relational Operator'
         *  SignalConversion generated from: '<S1>/FlgSAKIndInc'
         *
         * Block description for '<S10>/FlgSAKIndInc_Calculation':
         *  In this block is assigned to the correspond output the signal
         *  previously calculated
         *
         * Block requirements for '<S10>/Relational Operator':
         *  1. EISB_FCA6CYL_SW_REQ_1859: If the Individual cylinder knock correction (i.e. signal SAKCorrIn... (ECU_SW_Requirements#7035)
         */
        /* Gateway: EOA_fcn/SAKCorrIndSignals_Calculation/FlgSAKIndInc_Calculation */
        /* During: EOA_fcn/SAKCorrIndSignals_Calculation/FlgSAKIndInc_Calculation */
        /* Entry Internal: EOA_fcn/SAKCorrIndSignals_Calculation/FlgSAKIndInc_Calculation */
        /* Transition: '<S16>:27' */
        /* Transition: '<S16>:2' */
        FlgSAKIndInc[(IonAbsTdcEOA)] = (uint8_T)((rtb_Switch1 >= 0) ? 1 : 0);

        /* Chart: '<S14>/FlgSAKnockInc_Calculation' incorporates:
         *  RelationalOperator: '<S14>/Relational Operator'
         *  SignalConversion generated from: '<S1>/FlgSAKnockInc'
         *
         * Block description for '<S14>/FlgSAKnockInc_Calculation':
         *  In this block is assigned to the correspond output the signal
         *  previously calculated
         *
         * Block requirements for '<S14>/Relational Operator':
         *  1. EISB_FCA6CYL_SW_REQ_1861: If the Individual cylinder knock correction on spark advance (i.e.... (ECU_SW_Requirements#7042)
         */
        /* Gateway: EOA_fcn/SAKnockSignals_Calculation/FlgSAKnockInc_Calculation */
        /* During: EOA_fcn/SAKnockSignals_Calculation/FlgSAKnockInc_Calculation */
        /* Entry Internal: EOA_fcn/SAKnockSignals_Calculation/FlgSAKnockInc_Calculation */
        /* Transition: '<S26>:29' */
        /* Transition: '<S26>:26' */
        FlgSAKnockInc[(IonAbsTdcEOA)] = (uint8_T)((SAKnockCyl >= 0) ? 1 : 0);

        /* Chart: '<S10>/SAKCorrInd_Calculation' incorporates:
         *  SignalConversion generated from: '<S1>/KnockRecRL'
         *  SignalConversion generated from: '<S1>/SAKCorrInd'
         *
         * Block description for '<S10>/SAKCorrInd_Calculation':
         *  In this block are assigned to the correspond outputs the signals
         *  previously calculated
         */
        /* Gateway: EOA_fcn/SAKCorrIndSignals_Calculation/SAKCorrInd_Calculation */
        /* During: EOA_fcn/SAKCorrIndSignals_Calculation/SAKCorrInd_Calculation */
        /* Entry Internal: EOA_fcn/SAKCorrIndSignals_Calculation/SAKCorrInd_Calculation */
        /* Transition: '<S18>:47' */
        /* Transition: '<S18>:29' */
        SAKCorrInd[(IonAbsTdcEOA)] = rtb_Switch1;
        KnockRecRL[(IonAbsTdcEOA)] = (int16_T)(rtb_Switch1 * 4);

        /* Chart: '<S10>/SAKCorrIndMax_Calculation' incorporates:
         *  SignalConversion generated from: '<S1>/SAKCorrIndMax'
         *
         * Block description for '<S10>/SAKCorrIndMax_Calculation':
         *  In this block is assigned to the correspond output the signal
         *  previously calculated
         */
        /* Gateway: EOA_fcn/SAKCorrIndSignals_Calculation/SAKCorrIndMax_Calculation */
        /* During: EOA_fcn/SAKCorrIndSignals_Calculation/SAKCorrIndMax_Calculation */
        /* Entry Internal: EOA_fcn/SAKCorrIndSignals_Calculation/SAKCorrIndMax_Calculation */
        /* Transition: '<S17>:29' */
        /* Transition: '<S17>:26' */
        SAKCorrIndMax[(IonAbsTdcEOA)] = rtb_Switch2;

        /* Chart: '<S14>/SAKnock_Calculation' incorporates:
         *  Inport: '<Root>/FlgActTipIn'
         *  Inport: '<Root>/SATipIn'
         *  SignalConversion generated from: '<S1>/SAKnock'
         *
         * Block description for '<S14>/SAKnock_Calculation':
         *  In this block is assigned to the correspond output the signal
         *  previously calculated
         */
        /* Gateway: EOA_fcn/SAKnockSignals_Calculation/SAKnock_Calculation */
        /* During: EOA_fcn/SAKnockSignals_Calculation/SAKnock_Calculation */
        /* Entry Internal: EOA_fcn/SAKnockSignals_Calculation/SAKnock_Calculation */
        /* Transition: '<S28>:46' */
        /* Transition: '<S28>:29' */
        SAKnock[(IonAbsTdcEOA)] = SAKnockCyl;
        if (((int32_T)FlgActTipIn) != 0) {
          /* Transition: '<S28>:32' */
          IndCylRec = 1U;
          i = (uint8_T)((int32_T)(((int32_T)IonAbsTdcEOA) + 1));
          while (IndCylRec < N_CYLINDER) {
            /* Transition: '<S28>:33' */
            if (i >= N_CYLINDER) {
              /* Transition: '<S28>:34' */
              i = 0U;
            } else {
              /* Transition: '<S28>:36' */
            }

            /* Transition: '<S28>:35' */
            SAKnock[(i)] = SATipIn;
            IndCylRec = (uint8_T)((int32_T)(((int32_T)IndCylRec) + 1));
            i = (uint8_T)((int32_T)(((int32_T)i) + 1));
          }

          /* Transition: '<S28>:40' */
        } else {
          /* Transition: '<S28>:31' */
        }

        /* End of Chart: '<S14>/SAKnock_Calculation' */

        /* Chart: '<S14>/FlgSAKnockSat_Calculation' incorporates:
         *  DataTypeConversion: '<S14>/Data Type Conversion1'
         *  Logic: '<S14>/Logical Operator'
         *  RelationalOperator: '<S11>/Relational Operator'
         *  RelationalOperator: '<S9>/Relational Operator1'
         *
         * Block description for '<S14>/FlgSAKnockSat_Calculation':
         *  In this block is assigned to the correspond output the signal
         *  previously calculated
         *
         * Block requirements for '<S14>/Logical Operator':
         *  1. EISB_FCA6CYL_SW_REQ_1860: If the signal SAKnock not saturated and the saturated one are not ... (ECU_SW_Requirements#7041)
         */
        /* Gateway: EOA_fcn/SAKnockSignals_Calculation/FlgSAKnockSat_Calculation */
        /* During: EOA_fcn/SAKnockSignals_Calculation/FlgSAKnockSat_Calculation */
        /* Entry Internal: EOA_fcn/SAKnockSignals_Calculation/FlgSAKnockSat_Calculation */
        /* Transition: '<S27>:27' */
        /* Transition: '<S27>:2' */
        FlgSAKnockSat[(IonAbsTdcEOA)] = (uint8_T)(((rtb_Sum1_m != SAKnockCyl) ||
          (rtb_Switch1 != rtb_Add1)) ? 1 : 0);

        /* End of Outputs for SubSystem: '<Root>/EOA_fcn' */
        break;
      }
    }

    /* Transition: '<S3>:37' */
    /* Transition: '<S3>:93' */
    /* Transition: '<S3>:55' */
    break;

   default:
    /* Transition: '<S3>:14' */
    /* KnockCorrTot_10ms */
    /* Test force knock */
    if (((int32_T)VTFORCESAK[(IonAbsTdcEOA)]) == 1) {
      /* Outputs for Function Call SubSystem: '<Root>/ForceKnock_fcn'
       *
       * Block description for '<Root>/ForceKnock_fcn':
       *  This block forces outputs in case of test.
       */
      /* Chart: '<S2>/ForceKnock_Mgm' incorporates:
       *  SignalConversion generated from: '<S2>/SAKnock'
       *
       * Block description for '<S2>/ForceKnock_Mgm':
       *  This block forces outputs in case of test.
       */
      /* Transition: '<S3>:91' */
      /* Transition: '<S3>:88' */
      /* Event: '<S3>:86' */
      /* Gateway: ForceKnock_fcn/ForceKnock_Mgm */
      /* During: ForceKnock_fcn/ForceKnock_Mgm */
      /* Entry Internal: ForceKnock_fcn/ForceKnock_Mgm */
      /* Transition: '<S29>:64' */
      /* Transition: '<S29>:56'
       * Requirements for Transition: '<S29>:56':
       *  1. EISB_FCA6CYL_SW_REQ_1876: If the tunable parameter VTFORCESAK for cylinder X is equal to 1,i... (ECU_SW_Requirements#7043)
       */
      SAKnock[(IonAbsTdcEOA)] = VTSAKNOCKFORCED[(IonAbsTdcEOA)];

      /* SignalConversion generated from: '<S2>/SAKnockCyl' incorporates:
       *  Chart: '<S2>/ForceKnock_Mgm'
       *
       * Block description for '<S2>/ForceKnock_Mgm':
       *  This block forces outputs in case of test.
       */
      SAKnockCyl = VTSAKNOCKFORCED[(IonAbsTdcEOA)];

      /* End of Outputs for SubSystem: '<Root>/ForceKnock_fcn' */
      /* Transition: '<S3>:55' */
    } else {
      /* Transition: '<S3>:92' */
      IndCylRec = 0U;
      while ((IndCylRec < N_CYLINDER) && (((int32_T)FlgSyncPhased) == 0)) {
        /* Transition: '<S3>:39' */
        /* Transition: '<S3>:44' */
        if (((int32_T)VtRec[REC_KNOCKCORR_OFF_0 + IndCylRec]) == 1) {
          /* Outputs for Function Call SubSystem: '<Root>/Recovery_fcn'
           *
           * Block description for '<Root>/Recovery_fcn':
           *  In this block are implemented the recovery actions.
           */
          /* Switch: '<S33>/Switch2' incorporates:
           *  Constant: '<S33>/Constant'
           *  Constant: '<S33>/REC_NO_LOAD '
           *  RelationalOperator: '<S33>/Relational Operator'
           *  Selector: '<S33>/Selector1'
           */
          /* Transition: '<S3>:46' */
          /* Transition: '<S3>:50' */
          /* Event: '<S3>:34' */
          if (((int32_T)VtRec[(REC_NO_LOAD)]) == 0) {
            /* S-Function (Look2D_S8_U16_U16): '<S43>/Look2D_S8_U16_U16' incorporates:
             *  Constant: '<S33>/BKLOADKNOCK4'
             *  Constant: '<S33>/BKLOADKNOCK4_dim'
             *  Constant: '<S33>/BKRPMKNOCK4'
             *  Constant: '<S33>/BKRPMKNOCK4_dim'
             *  Constant: '<S33>/TBKNOCKREC'
             */
            Look2D_S8_U16_U16( &rtb_Look2D_S8_U16_U16, &TBKNOCKREC[0], Load,
                              &BKLOADKNOCK3[0], ((uint8_T)BKLOADKNOCK3_dim),
                              RpmF, &BKRPMKNOCK8[0], ((uint8_T)BKRPMKNOCK8_dim));
            rtb_Switch2 = rtb_Look2D_S8_U16_U16;
          } else {
            /* S-Function (LookUp_S8_U16): '<S44>/LookUp_S8_U16' incorporates:
             *  Constant: '<S33>/BKRPMKNOCK4'
             *  Constant: '<S33>/BKRPMKNOCK4_dim'
             *  Constant: '<S33>/VTKNOCKRECNOLOAD'
             */
            LookUp_S8_U16( &rtb_LookUp_S8_U16, &VTKNOCKRECNOLOAD[0], RpmF,
                          &BKRPMKNOCK8[0], ((uint8_T)BKRPMKNOCK8_dim));
            rtb_Switch2 = rtb_LookUp_S8_U16;
          }

          /* DataTypeConversion: '<S42>/Conversion2' incorporates:
           *  Constant: '<S33>/VTGAINSAKREC'
           *  Inport: '<Root>/RonLevelUsed'
           *  Product: '<S33>/Divide3'
           *  Product: '<S41>/Divide'
           *  Product: '<S42>/Divide'
           *  Selector: '<S33>/Selector'
           *
           * Block requirements for '<S33>/Divide3':
           *  1. EISB_FCA6CYL_SW_REQ_1874: The signal KnockRec shall be calculated starting from a 2-D lookup... (ECU_SW_Requirements#7049)
           */
          KnockRec = (int16_T)((((((int32_T)rtb_Switch2) + 15360) / 8) *
                                ((int32_T)VTGAINSAKREC[(RonLevelUsed)])) / 128);

          /* Selector: '<S35>/Selector2' incorporates:
           *  SignalConversion generated from: '<S4>/KnockRecRL_old'
           */
          rtb_Switch2 = KnockRecRL[(IndCylRec)];

          /* Sum: '<S36>/Add1' */
          rtb_Add1 = (int16_T)(KnockRec - rtb_Switch2);

          /* Abs: '<S36>/Abs' */
          if (rtb_Add1 < 0) {
            rtb_Add1 = (int16_T)(-rtb_Add1);
          }

          /* Logic: '<S36>/Logical Operator1' incorporates:
           *  Constant: '<S36>/Constant'
           *  Constant: '<S36>/KNOCKRECRLDIFF'
           *  RelationalOperator: '<S36>/Relational Operator'
           *  RelationalOperator: '<S36>/Relational Operator1'
           *  Selector: '<S36>/Selector1'
           *  SignalConversion generated from: '<S4>/FlgDisKnockRL_old'
           *
           * Block requirements for '<S36>/Logical Operator1':
           *  1. EISB_FCA6CYL_SW_REQ_1875: The flag FlgDisKnockRL for cylinder X shall be equal to1 if:
             -the ... (ECU_SW_Requirements#7050)
           */
          rtb_FlgDisKnockRLCyl = ((rtb_Add1 <= KNOCKRECRLDIFF) || (((int32_T)
            FlgDisKnockRL[(IndCylRec)]) != 0));

          /* Switch: '<S36>/Switch' */
          if (rtb_FlgDisKnockRLCyl) {
            rtb_Switch2 = KnockRec;
          } else {
            /* S-Function (RateLimiter_S16): '<S38>/RateLimiter_S16' incorporates:
             *  Constant: '<S35>/KNOCKRECRLMAX'
             *  Constant: '<S35>/KNOCKRECRLMIN'
             */
            RateLimiter_S16( &rtb_RateLimiter_S16, KnockRec, rtb_Switch2,
                            KNOCKRECRLMIN, KNOCKRECRLMAX);
            rtb_Switch2 = rtb_RateLimiter_S16;
          }

          /* Chart: '<S36>/FlgDisKnock_Mgm' incorporates:
           *  DataTypeConversion: '<S36>/Data Type Conversion3'
           *  Inport: '<Root>/RonLevelUsed'
           *  RelationalOperator: '<S36>/Relational Operator2'
           *  UnitDelay: '<S4>/Unit Delay'
           *
           * Block description for '<S36>/FlgDisKnock_Mgm':
           *  In this block is assigned to the correspond output the signal
           *  previously calculated
           */
          /* Gateway: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRLCyl_Calc/FlgDisKnock_Mgm */
          /* During: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRLCyl_Calc/FlgDisKnock_Mgm */
          /* Entry Internal: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRLCyl_Calc/FlgDisKnock_Mgm */
          /* Transition: '<S39>:50' */
          if (RonLevelUsed != RonLevelUsed_old) {
            /* Transition: '<S39>:52' */
            for (i = 0U; i < N_CYLINDER; i = (uint8_T)((int32_T)(((int32_T)i) +
                   1))) {
              /* Transition: '<S39>:56' */
              /* Transition: '<S39>:60' */
              FlgDisKnockRL[(i)] = 0U;

              /* Transition: '<S39>:61' */
            }

            /* Transition: '<S39>:58' */
            /* Transition: '<S39>:62' */
          } else {
            /* Transition: '<S39>:54' */
            FlgDisKnockRL[(IndCylRec)] = (uint8_T)(rtb_FlgDisKnockRLCyl ?
              ((uint8_T)1) : ((uint8_T)0));
          }

          /* Chart: '<S35>/KnockRecRL_Mgm' incorporates:
           *  SignalConversion generated from: '<S4>/KnockRecRL'
           *
           * Block description for '<S35>/KnockRecRL_Mgm':
           *  In this block is assigned to the correspond output the signal
           *  previously calculated
           */
          /* Transition: '<S39>:64' */
          /* Gateway: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRL_Mgm */
          /* During: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRL_Mgm */
          /* Entry Internal: Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRL_Mgm */
          /* Transition: '<S37>:68' */
          /* Transition: '<S37>:64' */
          KnockRecRL[(IndCylRec)] = rtb_Switch2;

          /* DataTypeConversion: '<S34>/Conversion2' incorporates:
           *  Inport: '<Root>/KnockRecGain'
           *  Product: '<S32>/Divide2'
           *  Product: '<S34>/Divide'
           *
           * Block requirements for '<S32>/Divide2':
           *  1. EISB_FCA6CYL_SW_REQ_1287: The software shall use a recovery value for the Individual cylinde... (ECU_SW_Requirements#2135)
           */
          rtb_Switch2 = (int16_T)((((int32_T)rtb_Switch2) * ((int32_T)
            KnockRecGain)) / 4096);

          /* Chart: '<S4>/Recovery_Mgm' incorporates:
           *  Inport: '<Root>/SAKnockCorrAd'
           *  SignalConversion generated from: '<S4>/FlgSAKIndInc'
           *  SignalConversion generated from: '<S4>/FlgSAKnockInc'
           *  SignalConversion generated from: '<S4>/SAKCorrInd'
           *  SignalConversion generated from: '<S4>/SAKnock'
           *
           * Block description for '<S4>/Recovery_Mgm':
           *  In this block are assigned to the correspond outputs the following signals previously calculated, in case of recovery activeted:
           *  - SAKnockCyl
           *  - SAKnock
           *  - SAKCorInd
           */
          /* Gateway: Recovery_fcn/Recovery_Mgm */
          /* During: Recovery_fcn/Recovery_Mgm */
          /* Entry Internal: Recovery_fcn/Recovery_Mgm */
          /* Transition: '<S31>:49' */
          /* Transition: '<S31>:2'
           * Requirements for Transition: '<S31>:2':
           *  1. EISB_FCA6CYL_SW_REQ_1301: The software shall use a recovery value for Knock correction on Sp... (ECU_SW_Requirements#2143)
           */
          SAKCorrInd[(IndCylRec)] = rtb_Switch2;
          SAKnockCyl = (int16_T)(rtb_Switch2 + SAKnockCorrAd[(IndCylRec)]);
          SAKnock[(IndCylRec)] = SAKnockCyl;
          FlgSAKIndInc[(IndCylRec)] = 0U;
          FlgSAKnockInc[(IndCylRec)] = 0U;

          /* Update for UnitDelay: '<S4>/Unit Delay' incorporates:
           *  Inport: '<Root>/RonLevelUsed'
           */
          RonLevelUsed_old = RonLevelUsed;

          /* End of Outputs for SubSystem: '<Root>/Recovery_fcn' */
          /* Transition: '<S3>:51' */
        } else {
          /* Transition: '<S3>:48' */
        }

        /* Transition: '<S3>:53' */
        IndCylRec = (uint8_T)((int32_T)(((int32_T)IndCylRec) + 1));

        /* Transition: '<S3>:54' */
      }

      /* Transition: '<S3>:42' */
    }
    break;
  }

  /* End of Chart: '<Root>/KnockCorrTot_Scheduler' */
  /* Transition: '<S3>:58' */
}

/*
 * Output and update for function-call system: '<Root>/KnockCorrTot_Scheduler'
 * Block description for: '<Root>/KnockCorrTot_Scheduler'
 *   Model scheduler. This stateflow manage events of PowerOn NoSync, EOA and
 *   10ms.
 */
void KnockCor_KnockCorrTot_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[4];
  int32_T i;

  /* Chart: '<Root>/KnockCorrTot_Scheduler' incorporates:
   *  TriggerPort: '<S3>/input events'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  for (i = 0; i < 4; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: KnockCorrTot_Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S3>:15' */
    i = (int32_T)Knoc_event_KnockCorrTot_PowerOn;
    Knock_chartstep_c3_KnockCorrTot(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S3>:18' */
    i = (int32_T)Knock_event_KnockCorrTot_NoSync;
    Knock_chartstep_c3_KnockCorrTot(&i);
  }

  if (rtb_inputevents[2U] == 2) {
    /* Event: '<S3>:19' */
    i = (int32_T)KnockCor_event_KnockCorrTot_EOA;
    Knock_chartstep_c3_KnockCorrTot(&i);
  }

  if (rtb_inputevents[3U] == 2) {
    /* Event: '<S3>:20' */
    i = (int32_T)KnockCo_event_KnockCorrTot_10ms;
    Knock_chartstep_c3_KnockCorrTot(&i);
  }
}

/* Model step function */
void KnockCorrTot_10ms(void)
{
  /* Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrTot_10ms' */

  /* Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  KnockCor_KnockCorrTot_Scheduler(3);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrTot_10ms' */
}

/* Model step function */
void KnockCorrTot_EOA(void)
{
  /* Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrTot_EOA' */

  /* Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  KnockCor_KnockCorrTot_Scheduler(2);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrTot_EOA' */
}

/* Model step function */
void KnockCorrTot_NoSync(void)
{
  /* Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrTot_NoSync' */

  /* Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  KnockCor_KnockCorrTot_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrTot_NoSync' */
}

/* Model step function */
void KnockCorrTot_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrTot_PowerOn' incorporates:
   *  Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */

  /* Chart: '<Root>/KnockCorrTot_Scheduler'
   *
   * Block description for '<Root>/KnockCorrTot_Scheduler':
   *  Model scheduler. This stateflow manage events of PowerOn NoSync, EOA
   *  and 10ms.
   */
  KnockCor_KnockCorrTot_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrTot_PowerOn' */
}

/* Model initialize function */
void KnockCorrTot_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

int16_T SAKnockCyl;
int16_T SAKnockMin;
int16_T SAKnockMax;
int16_T KnockRec;
uint8_T FlgSAKnockInc[N_CYL_MAX];
int16_T SAKnock[N_CYL_MAX];
uint8_T FlgSAKIndInc[N_CYL_MAX];
int16_T SAKCorrInd[N_CYL_MAX];
uint8_T FlgSAKnockSat[N_CYL_MAX];
int16_T SAKCorrIndMax[N_CYL_MAX];
uint8_T FlgDisKnockRL[N_CYL_MAX];
int16_T KnockRecRL[N_CYL_MAX];
void KnockCorrTot_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    FlgSAKnockInc[idx] = 0u;
    SAKnock[idx] = 0;
    FlgSAKIndInc[idx] = 0u;
    SAKCorrInd[idx] = 0u;
    FlgSAKnockSat[idx] = 0u;
    SAKCorrIndMax[idx] = 0u;
    FlgDisKnockRL[idx] = 0u;
    KnockRecRL[idx] = 0u;
  }

  SAKnockCyl = 0;
  SAKnockMin = 0;
  SAKnockMax = 0;
  KnockRec = 0;
}

void KnockCorrTot_PowerOn(void)
{
  KnockCorrTot_Stub();
}

void KnockCorrTot_NoSync(void)
{
  KnockCorrTot_Stub();
}

void KnockCorrTot_EOA(void)
{
  KnockCorrTot_Stub();
}

void KnockCorrTot_10ms(void)
{
  KnockCorrTot_Stub();
}

#endif                                 /* _BUILD_KNOCKCORRTOT_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/