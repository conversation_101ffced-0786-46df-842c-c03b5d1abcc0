/****************************************************************************
 *
 * Copyright © 2020 STMicroelectronics - All Rights Reserved
 *
 * License terms: STMicroelectronics Proprietary in accordance with licensing
 * terms SLA0089 at www.st.com
 *
 * THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
 * INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
 *
 * EVALUATION ONLY - NOT FOR USE IN PRODUCTION
 *****************************************************************************/

#include <typedefs.h>
#include <gtm.h>
#include <crank_defs.h>
#include <crank_event.h>
#include "Timing_out.h"
#include "OS_api.h"
#include "TasksDefs.h"
#include "syncmgm_out.h"
#include "ignincmd_out.h"
#include "msparkcmd_out.h"
#include "SyncMgmCfg_out.h"

/*
 * This is the Reference Position, a specific tooth of the crank wheel
 * used as reference to generate injection and ignition events.
 *
 * By default it set to 0, that is the second tooth after the whole.
 * It can be changed by changing the following define.
 *
 * It should be in the range [-NOMINAL_TEETH, +NOMINAL_TEETH]
 *
 * For example, setting to 5, the reference position will be 7 teeth after the missing tooth/teeth.
 *
 * It is also possible to use angles instead of teeth to have fractional teeth.
 *
 */
#define REFERENCE_POSITION_OFFSET_IN_TEETH  (3U)
#define REFERENCE_POSITION_SCALING          (SUBINC_PER_FULL_SCALE + (REFERENCE_POSITION_OFFSET_IN_TEETH * TOOTH_MULTIPLIER))
#define K_RPM_720                           (1000000u * 60u * N_CYCLE)
#define K_RPM_120                           (K_RPM_720 / 6U)
#define K_RPM_90                            (K_RPM_720 / 8U)
#define MIN_RPM_CALC                        (200u)
#define MAX_ENGINE_PERIOD_720               (K_RPM_720 / MIN_RPM_CALC)
#define MAX_ENGINE_PERIOD_120               (K_RPM_120 / MIN_RPM_CALC)
#define MAX_ENGINE_PERIOD_90                (K_RPM_90 / MIN_RPM_CALC)
#define REFERENCE_TEETH_NOLOCK              (6u)
#define TDN_TIMEOUT                         (100u)
#define SYNC_TOOTH_INDEX                    (3UL)

/* Time stamps are 24 bits */
#define ROUND_24BIT(x)      (uint32_T)(((uint32_T)(x)) & 0x00FFFFFFUL)

typedef enum {
    CRANK_LOCKED = 0U,
    CRANK_NO_LOCKED = 1U
} CRANK_STATUS;

typedef enum{
    CRANK_PHASE_CHECK_NOT_EXECUTED = 0U,
    CRANK_PHASE_CHECK_EXECUTED = 1U
} CRANK_PHASE_CHECK_T;

static vuint32_t Reference_Position = 0u;
static uint32_T gap_found = 0u;
uint32_T rep_pos_angle = 0u;
static uint32_T previous_time_stamp = 0u;
static uint32_T new_time_stamp = 0u;
static uint32_T previous_diff = 0u;
static uint32_T diff = 0u;
static uint8_T CntGetLock = 0u;
static uint8_T CntLossLock = 0u;
static uint8_T CntToothEvent = 0u;
static uint32_T ToothAbsTime = 0u;
static uint32_T ToothAbsTimeOld = 0u;
static uint32_T ToothAbsTimeOld_Rec[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
static uint32_T EnginePeriod720 = 0u;
static uint32_T EnginePeriod = 0u;
static uint32_T EnginePeriod_Rec[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
uint16_T RpmCalc = 0u;
uint32_T AngleMin = 0u;
uint32_T AngleMax = 0u;
uint32_T CurrentAngleGlb = 0u;
void *NextEventGlb = NULL;
uint32_T ValueGlb = 0u;
uint16_T CntTdcCrk;
uint16_T CntAbsTdcCrk;
uint32_T TdcAngle;


static void * Last_event = NULL;
static uint32_T Tbu = 0u;
static uint32_T Last_event_tbu = 0;
static uint32_T CurrentAngle = 0u;
uint8_T FlagExecute = 0u;
uint32_T NTimInterrupt = 0u;
uint32_T NTimInterruptDiscart = 0u;
uint32_T TDNTimer = 0u;
uint8_T TDNTimeoutEnable = 0u;
static CRANK_STATUS LossLock = CRANK_NO_LOCKED;
static CRANK_PHASE_CHECK_T CrankPhased = CRANK_PHASE_CHECK_NOT_EXECUTED;
CRANK_PHASE_SYNC_STATUS CrankPhaseRqst = CRANK_PHASE_SYNC_NOT_EXECUTED;

void isb_crank_init_DPLL(void);
void crank_tdc_event(uint32_T ticks);
void *CrankSyncRequest(void *event, uint32_T AngleShift, uint32_T CurAngle);
static void Crank_CntTdcCrk(void);
static void reference_timer_remodulation(void);
static void StopTDNTimeout(void );
static void StartTDNTimeout(uint32_T counter);
static void ReInitTDNStatus(void);
static void CalcRPM(uint8_T cyl, uint8_T argc);

/*
 * DPLL get lock interrupt.
 *
 * This interrupt is fired each time the DPLL locks on profile
 * that is there is a match between the input signal waveform and
 * the provided user profile.
 *
 */
void dpll_sub_inc1_get_lock(GTM_DPLLDriver * dplld, uint32_T int_num) {
    (void) dplld;
    (void) int_num;

    CntGetLock++;
    LossLock = CRANK_LOCKED;
    Reference_Position = ROUND_24BIT((gtm_tbuGetTimeBaseChannel( &TBUD1, TBU_CHANNEL1) + (TOOTH_MULTIPLIER * (NOMINAL_TEETH - MISSING_TEETH + REFERENCE_POSITION_OFFSET_IN_TEETH))));

    uint32_T tmp;

    /* Prepare TDC event */
    ATOMD1.priv = setEventOnAngle(SUBINC_PER_FULL_SCALE, EVENT_ACTION_PERIODIC, NULL);
    if (ATOMD1.priv == NULL) {
        ATOMD1.priv = getEventRoot();
    }

    setEventOnTooth(TDC_TOOTH[0][0], EVENT_ACTION_PERIODIC, crank_tdc_event);
    setEventOnTooth(TDC_TOOTH[0][1], EVENT_ACTION_PERIODIC, crank_tdc_event);
    setEventOnTooth(TDC_TOOTH[0][2], EVENT_ACTION_PERIODIC, crank_tdc_event);
    setEventOnTooth(TDC_TOOTH[0][3], EVENT_ACTION_PERIODIC, crank_tdc_event);
    setEventOnTooth(TDC_TOOTH[0][4], EVENT_ACTION_PERIODIC, crank_tdc_event);
    setEventOnTooth(TDC_TOOTH[0][5], EVENT_ACTION_PERIODIC, crank_tdc_event);
#if (N_CYLINDER == 8)
    setEventOnTooth(TDC_TOOTH[0][6], EVENT_ACTION_PERIODIC, crank_tdc_event);
    setEventOnTooth(TDC_TOOTH[0][7], EVENT_ACTION_PERIODIC, crank_tdc_event);
#endif


    gtm_atomStop( &ATOMD1, ATOM_CHANNEL1);
    gtm_atomAckInt(&ATOMD1, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_STATUS_CCU1);

    tmp = ROUND_24BIT(Reference_Position - (TOOTH_MULTIPLIER * 1U));
    gtm_atomSetCompare0( &ATOMD1, ATOM_CHANNEL1, tmp);
    tmp = ROUND_24BIT(Reference_Position - (TOOTH_MULTIPLIER * 0U));
    gtm_atomSetCompare1( &ATOMD1, ATOM_CHANNEL1, tmp);

    gtm_atomStart( &ATOMD1, ATOM_CHANNEL1);

//    StopTDNTimeout();

    /* Set StSync */
    SYNCMGM_SetStSync();
}

/*
 * DPLL loss lock interrupt.
 *
 * This interrupt is fired each time the DPLL looses the lock on profile
 * that is there is a mismatch between the input signal waveform and
 * the provided user profile.
 *
 */
void dpll_sub_inc1_loss_lock(GTM_DPLLDriver * dplld, uint32_T int_num) {
    (void) int_num;

    /* Reset vars */
    ToothAbsTime = 0u;
    ToothAbsTimeOld = 0u;
    EnginePeriod720 = 0u;
    RpmCalc = 0u;

    gtm_tbuStopChannel( &TBUD1, TBU_CHANNEL1);

    /* Stop ATOM for Reference Position (active/passive TDC) event */
    gtm_atomAckInt( &ATOMD1, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_STATUS_CCU1);
    gtm_atomStop( &ATOMD1, ATOM_CHANNEL1);

    gtm_dpllDisable(dplld);
    ReInitTDNStatus();

    CrankPhased = CRANK_PHASE_CHECK_NOT_EXECUTED;
    CrankPhaseRqst = CRANK_PHASE_SYNC_NOT_EXECUTED;
    CntLossLock++;
    /* Check DIAG_SYNC */
    SYNCMGM_DiagSync_LL();
    
    isb_crank_init_DPLL();
    
    /* Got the gap, enable TIM interrupts */
    gtm_timEnableInt( &TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
    StopTDNTimeout();

    ActivateTask(TaskNoSyncID);
}

/*
 * =================================================================
 * -------------------- TOOTH EVENT CALLBACK -----------------------
 * =================================================================
 */
void crank_tdc_event(uint32_T ticks) {
    (void) ticks;

    CntToothEvent++;
    
#ifndef _BUILD_SYNCMGM_
    if(CntAbsTdc<MAX_uint32_T)
    {
      CntAbsTdc++;
    }
#endif
    CalcRPM(NULL, 0u);

    Crank_CntTdcCrk();
    
    /* Manage FlgAbsTdc flags */
    SYNCMGM_FlgAbsTdc();
    
    ActivateTask(TaskTDCID);
}

/*
 * ATOM0_1 interrupt routine called each time
 * the reference point is detected.
 *
 * Reference point could be set to active/passive TDC.
 *
 */
void crank_ref_pos_atom0_1_cb(GTM_ATOMDriver *atomd, uint8_T channel) {
    uint32_T tmp;
    void * event;
    void * next_event;
    uint32_T value;
    EventCallback_t callback;

    event = atomd -> priv;
    if (LossLock == CRANK_LOCKED) {
        StartTDNTimeout(TDN_TIMEOUT);
        next_event = getEventNext(event);
        atomd -> priv = next_event;

        Last_event = event;
        Last_event_tbu = gtm_tbuGetTimeBaseChannel( &TBUD1, TBU_CHANNEL1);

        TdcAngle = getEventAngle(event);

        if (TdcAngle == SUBINC_PER_FULL_SCALE) {
            value = getEventAngle(next_event);

            /* Update reference position */
            Reference_Position = ROUND_24BIT(SUBINC_PER_FULL_SCALE + Reference_Position);
            rep_pos_angle = gtm_tbuGetTimeBaseChannel( &TBUD1, TBU_CHANNEL1);
        } else {
            value = getEventAngle(next_event) - TdcAngle;
        }

        SYNCMGM_AbsTdcMgm();
        
        if(CrankPhaseRqst == CRANK_PHASE_SYNC_ONGOING)
        {
            CrankPhaseRqst = CRANK_PHASE_SYNC_EXECUTED;
        }
        
        if (CrankPhaseRqst == CRANK_PHASE_SYNC_REQUEST) {
            next_event = CrankSyncRequest(Last_event, 36000, getEventAngle(event));
            CurrentAngle = getEventAngle(event);
            NextEventGlb = next_event;
            
            if (next_event != Last_event) {
                if (getEventAngle(next_event) <= CurrentAngle) {
                    value = (SUBINC_PER_FULL_SCALE - CurrentAngle) + getEventAngle(next_event);
                } else {
                    value = getEventAngle(next_event) - CurrentAngle;
                }
            } else {
                value = SUBINC_PER_FULL_SCALE;
            }

            Last_event = next_event;
            Last_event_tbu = gtm_tbuGetTimeBaseChannel( &TBUD1, TBU_CHANNEL1);

            ValueGlb = value;
            ATOMD1.priv = next_event;
            CrankPhaseRqst = CRANK_PHASE_SYNC_ONGOING;
        }

        /* Read SR register to re-activate comparison */
        (void) gtm_atomGetShadowReg0(atomd, channel);
        (void) gtm_atomGetShadowReg1(atomd, channel);

        /* Read and set Compare register 0 for next comparison */
        tmp = gtm_atomGetCompare0(atomd, channel);
        tmp = ROUND_24BIT(value + tmp);
        gtm_atomSetCompare0(atomd, channel, tmp);

        /* Read and set Compare register 1 for next comparison */
        tmp = gtm_atomGetCompare1(atomd, channel);
        tmp = ROUND_24BIT(value + tmp);
        gtm_atomSetCompare1(atomd, channel, tmp);

        /* Call the associated callback (if any) */
        callback = getEventCallback(event);
        if (callback != NULL) {
            callback(getEventAngle(event));
        }

        /* Remove the event if one shot */
        if (getEventAction(event) == EVENT_ACTION_ONE_SHOT) {
            resetEvent(event);
        }
    } else {

        event = atomd -> priv;
        //Delta ANglee
        value = getEventAngle(event);

        /* Read SR register to re-activate comparison */
        (void) gtm_atomGetShadowReg0(atomd, channel);
        (void) gtm_atomGetShadowReg1(atomd, channel);

        /* Read and set Compare register 0 for next comparison */
        tmp = gtm_atomGetCompare0(atomd, channel);
        tmp = ROUND_24BIT(value + tmp);
        gtm_atomSetCompare0(atomd, channel, tmp);

        /* Read and set Compare register 1 for next comparison */
        tmp = gtm_atomGetCompare1(atomd, channel);
        tmp = ROUND_24BIT(value + tmp);
        gtm_atomSetCompare1(atomd, channel, tmp);

        /* Call the associated callback (if any) */
        callback = getEventCallback(event);
        if (callback != NULL) {
            callback(getEventAngle(event));
        }
    }
}

void tdn_new_value_cb(GTM_TIMDriver * timd, uint8_T channel) {
    (void) timd;
    (void) channel;

    uint32_T time_stamp;
    NTimInterruptDiscart++;
    if (NTimInterruptDiscart > 10)
    {
        time_stamp = timd->tim->GTM_CH_REG(0,GPR0).R;

        previous_time_stamp = new_time_stamp;
        previous_diff = diff;

        new_time_stamp = time_stamp & 0x00FFFFFFUL;

        if (new_time_stamp > previous_time_stamp) {
            diff = new_time_stamp - previous_time_stamp;
        } else {
            diff = (0x00FFFFFFUL - previous_time_stamp) + new_time_stamp + 1UL;
        }

        NTimInterrupt++;

        if (previous_diff >= ((2UL * diff))) {
            if (gap_found == 0) {

                NTimInterrupt = 0;
                gap_found = 1;
            } else {

                if (NTimInterrupt == 58)
                {
                    /* Clear all DPLL interrupts */
                    gtm_dpllAckInt( &DPLLD1, 0xFFFFFFFFUL);

                    /* Synchronization tooth index depends on compiler optimization level
                     * and if the program is going to be executed from flash or from RAM:
                     *
                     *               +-------+-----+
                     *               | Flash | RAM |
                     * +-------------+-------+-----+
                     * | -O2 (speed) |   3   |  2  |
                     * | -O0 (none)  |   3   |  3  |
                     * +-------------+-------+-----+
                     *
                     */

                    StartTDNTimeout(TDN_TIMEOUT);

                    gtm_dpllTriggerRAM2C( &DPLLD1, SYNC_TOOTH_INDEX);
                    gtm_dpllEnableSubInc1( &DPLLD1);
                    /* Got the gap, disable TIM interrupts */
                    gtm_timDisableInt(timd, channel, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);

                    gtm_tbuStopChannel( &TBUD1, TBU_CHANNEL1);
                    /* Synchronize and start the TBU counter for subinc 1 */
                    /* Second falling edge after the gap = 0 */
                    gtm_tbuSetRunningModeChannel_12( &TBUD1, TBU_CHANNEL1, SPC5_GTM_TBU_MODE_FORWARD_BACKWARD);
                    gtm_tbuSetTimeBaseChannel( &TBUD1, TBU_CHANNEL1, ROUND_24BIT(N_TEETH * TOOTH_MULTIPLIER));
                    gtm_tbuStartChannel( &TBUD1, TBU_CHANNEL1);
                    /* Update Reference Position */
                    Reference_Position = ROUND_24BIT(((gtm_tbuGetTimeBaseChannel( &TBUD1, TBU_CHANNEL1) + (TOOTH_MULTIPLIER * (NOMINAL_TEETH - SYNC_TOOTH_INDEX - MISSING_TEETH + REFERENCE_POSITION_OFFSET_IN_TEETH + LossLock)))));

                    uint32_T tmp;

                    /* Prepare TDC event */
                    ATOMD1.priv = setEventNoLock((TOOTH_MULTIPLIER * REFERENCE_TEETH_NOLOCK), NULL);
                    //ATOMD1.priv = setEventOnAngle(SUBINC_PER_FULL_SCALE, EVENT_ACTION_PERIODIC, NULL);
                    if (ATOMD1.priv == NULL) {
                        ATOMD1.priv = getEventRoot();
                    }
                    Reference_Position = ROUND_24BIT(((gtm_tbuGetTimeBaseChannel( &TBUD1, TBU_CHANNEL1) + (TOOTH_MULTIPLIER * (REFERENCE_TEETH_NOLOCK - MISSING_TEETH)))));

                    /* Schedule event for falling edge tooth 1 to falling edge tooth2 */
                    /* Make sure the rising edge is always after the falling edge of tooth 2 */
                    (void) gtm_atomGetShadowReg0( &ATOMD1, ATOM_CHANNEL1);
                    (void) gtm_atomGetShadowReg1( &ATOMD1, ATOM_CHANNEL1);

                    tmp = ROUND_24BIT(Reference_Position - (TOOTH_MULTIPLIER * 1U));
                    gtm_atomSetCompare0( &ATOMD1, ATOM_CHANNEL1, tmp);
                    tmp = ROUND_24BIT(Reference_Position - (TOOTH_MULTIPLIER * 0U));
                    gtm_atomSetCompare1( &ATOMD1, ATOM_CHANNEL1, tmp);

                    gtm_atomStart(&ATOMD1, ATOM_CHANNEL1);
                    gap_found = 0;
                    NTimInterruptDiscart = 0;
                }
                else {
                    NTimInterrupt = 0;
                }
            }
        }
    }
    else
    {
        time_stamp = timd->tim->GTM_CH_REG(0,GPR0).R;
        new_time_stamp = time_stamp & 0x00FFFFFFUL;
    }

}

void isb_crank_init_DPLL(void) {
    uint32_T i;
    uint32_T * p;

    /* Initialize RAM regions */
    gtm_dpllInitRAM( &DPLLD1);

    /* RAM2 offset to 128 Kb for each section (2a, 2b, 2c, 2d) */
    gtm_dpllSetRAM2Offset( &DPLLD1, SPC5_GTM_DPLL_RAM2_OFFSET_128);

    /* Fill-in the reference profile */
    p = gtm_dpllRAMBaseAddress( &DPLLD1, SC5_GTM_DPLL_RAM_REGION_2C);

    #define NOMINAL_INC 0x10000U

    /* First gap */
    p[0] = (uint32_T)(NOMINAL_INC * (MISSING_TEETH + 1U));
    /* Add TINT0 tooth interrupt just after the first gap */
    p[1] = (uint32_T) NOMINAL_INC;
    for (i = 2; i < NOMINAL_TEETH_FULL_SCALE; i++) {
        p[i] = NOMINAL_INC;
    }
    /* Second gap */
    p[NOMINAL_TEETH - MISSING_TEETH] = (uint32_T)(NOMINAL_INC * (MISSING_TEETH + 1U));

    /* Time Out Value of Active TRIGGER Slope (for missing TRIGGER generation) */
    gtm_dpllSetMemoryRegister( &DPLLD1, SPC5_GTM_DPLL_TRIGGER_TIME_OUT_VALUE_REG, 0x0800);

    gtm_dpllSetMultiplier( &DPLLD1, TOOTH_MULTIPLIER);
    gtm_dpllSetTriggerNumber( &DPLLD1, NOMINAL_TEETH);
    gtm_dpllTriggerSyncNum( &DPLLD1, MISSING_TEETH);
    gtm_dpllEnableTrigger( &DPLLD1);

    gtm_dpllTriggerSlope( &DPLLD1, SPC5_GTM_DPLL_SLOPE_HIGH);
    gtm_dpllTriggerPlausibility( &DPLLD1, SPC5_GTM_DPLL_TIME_RELATED_PLAUSIBILITY);

    /* Now enable the DPLL */
    gtm_dpllEnable( &DPLLD1);

    /* Reset SW vars to manage the crank phase check */
    CrankPhased = CRANK_PHASE_CHECK_NOT_EXECUTED;
    CrankPhaseRqst = CRANK_PHASE_SYNC_NOT_EXECUTED;
}

void *CrankSyncRequest(void *event, uint32_T AngleShift, uint32_T CurAngle)
{
    uint32_T TbuCount;
    TbuCount = gtm_tbuGetTimeBaseChannel(&TBUD1, TBU_CHANNEL1);
    TbuCount += AngleShift;
    TbuCount  = ROUND_24BIT(TbuCount);
    //Update the value
    gtm_tbuStopChannel(&TBUD1, TBU_CHANNEL1);
    gtm_tbuSetTimeBaseChannel(&TBUD1, TBU_CHANNEL1,TbuCount);
    gtm_tbuStartChannel(&TBUD1, TBU_CHANNEL1);
    //CurrentAngle = getEventAngle(event);

    return GetNextElemList( event,AngleShift,CurAngle );
}

void CrankPhaseCheck(uint32_T ToothMin, uint32_T ToothMax, uint32_T currentAngle) {
    //uint32_T AngleMin,AngleMax,value;
    uint32_T value;
    void *next_event;
    uint32_T tmp;

    if ((LossLock == CRANK_LOCKED) && (Last_event != NULL) && (CrankPhased == CRANK_PHASE_CHECK_NOT_EXECUTED) && (CrankPhaseRqst != CRANK_PHASE_SYNC_REQUEST) && (CrankPhaseRqst != CRANK_PHASE_SYNC_ONGOING)) {
        AngleMin = ToothMin * TOOTH_MULTIPLIER;
        AngleMax = ToothMax * TOOTH_MULTIPLIER;

        CurrentAngleGlb = currentAngle;
        if ((((CurrentAngleGlb < AngleMin) && (CurrentAngleGlb > AngleMax)) && (AngleMax < AngleMin)) ||
            (((CurrentAngleGlb > AngleMax) || (CurrentAngleGlb < AngleMin)) && (AngleMax > AngleMin))) {

            CrankPhaseRqst = CRANK_PHASE_SYNC_REQUEST;
        } else {
            CrankPhaseRqst = CRANK_PHASE_SYNC_EXECUTED;
            CrankPhased = CRANK_PHASE_CHECK_EXECUTED;

            /* Set flgSyncPhased */
            SYNCMGM_SetFlgSyncPhased();
        }
    }
}

uint32_T GetCurrentAngle(void) {
    uint32_T difftbu;
    uint32_T CurrentAngleObt;
    uint32_T localLast_event_tbu;
    void * localLast_event = NULL;

    localLast_event_tbu = Last_event_tbu;
    localLast_event = Last_event;
    
    Tbu = gtm_tbuGetTimeBaseChannel( &TBUD1, TBU_CHANNEL1);

    if (Tbu < localLast_event_tbu) {
        difftbu = (Tbu + (0xFFFFFF - localLast_event_tbu));
    } else {
        difftbu = (Tbu - localLast_event_tbu);
    }

    if (localLast_event != NULL) {
        CurrentAngleObt = getEventAngle(localLast_event) + difftbu;
        if (CurrentAngleObt > SUBINC_PER_FULL_SCALE) {
            CurrentAngleObt -= SUBINC_PER_FULL_SCALE;
        }
    } else {
        CurrentAngleObt = 0u;
    }
    
    return CurrentAngleObt;
}


void TDN_reinit_DPLL(void)
{
    gtm_tbuStopChannel(&TBUD1, TBU_CHANNEL1);
    
    /* Stop ATOM for Reference Position (active/passive TDC) event */
    gtm_atomAckInt(&ATOMD1, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_STATUS_CCU1);
    gtm_atomStop(&ATOMD1, ATOM_CHANNEL1);
    
    gtm_dpllDisable(&DPLLD1);
    ReInitTDNStatus();
    isb_crank_init_DPLL();
    
    /* Got the gap, enable TIM interrupts */
    gtm_timEnableInt(&TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
    StopTDNTimeout();
}

void TDNTimeout_mng(void)
{
    if (TDNTimer > 0u){
        TDNTimer--;
    }
    
    if ( (TDNTimeoutEnable == 1) && (TDNTimer == 0) )
    {
        TDN_reinit_DPLL();
    }
}

static void CalcRPM(uint8_T cyl, uint8_T argc) {

    uint64_T maxEnginePeriod;
    uint64_T k_rpm;
    uint8_T cylinder;
    uint64_T tooth_abs_time;
    uint64_T tooth_us_time;
    uint16_T rpmCalc;

    TIMING_GetAbsTimer( &tooth_abs_time);
    TIMING_TicksToMicroSeconds(tooth_abs_time, &tooth_us_time);

    ToothAbsTime = (uint32_T)(tooth_us_time & 0x00000000FFFFFFFFu);

    if (argc == 0u)
    {
#if (ENGINE_TYPE == FE_173_8C)||(ENGINE_TYPE == BR_16C)
        maxEnginePeriod = MAX_ENGINE_PERIOD_90;
        k_rpm = K_RPM_90;
#elif (ENGINE_TYPE == FE_171_6C)
        maxEnginePeriod = MAX_ENGINE_PERIOD_120;
        k_rpm = K_RPM_120;
#else
#error Wrong engine type
#endif
        if (ToothAbsTime < ToothAbsTimeOld) {
            EnginePeriod = (MAX_uint32_T - ToothAbsTime + ToothAbsTimeOld);
        } else {
            EnginePeriod = ToothAbsTime - ToothAbsTimeOld;
        }

        ToothAbsTimeOld = ToothAbsTime;
    }
    else if (argc == 1u)
    {
        cylinder = (uint8_T)cyl;
        maxEnginePeriod = MAX_ENGINE_PERIOD_720;
        k_rpm = K_RPM_720;

        if (ToothAbsTime < ToothAbsTimeOld_Rec[cylinder]) {
            EnginePeriod = (MAX_uint32_T - ToothAbsTime + ToothAbsTimeOld_Rec[cylinder]);
        } else {
            EnginePeriod = ToothAbsTime - ToothAbsTimeOld_Rec[cylinder];
        }

        ToothAbsTimeOld_Rec[cylinder] = ToothAbsTime;
    }
    else
    {
        /* Error*/
        return;
    }

    if ((EnginePeriod <= maxEnginePeriod) && (EnginePeriod != 0u)) {
        rpmCalc = k_rpm / EnginePeriod;
        RpmCalc = rpmCalc;
        
    } else {
        RpmCalc = 0u;
    }
}

static void Crank_CntTdcCrk(void)
{
    uint32_T tmpCntAbsTdc;

    if ((EndStartFlg == 0) || (CntAbsTdc == 1))
    {
        CntTdcCrk = 0;
        CntAbsTdcCrk = (uint16_T)((CntAbsTdcCrk < MAX_uint16_T) ? CntAbsTdc : MAX_uint16_T);
    }
    else
    {
        tmpCntAbsTdc = CntAbsTdc - CntAbsTdcCrk;
        CntTdcCrk = (uint16_T)((tmpCntAbsTdc<MAX_uint16_T)?tmpCntAbsTdc:MAX_uint16_T);
    }
}


static void reference_timer_remodulation(void) {
    uint32_T tmp;

    (void) gtm_atomGetShadowReg0( &ATOMD1, ATOM_CHANNEL1);
    (void) gtm_atomGetShadowReg1( &ATOMD1, ATOM_CHANNEL1);
    Reference_Position = ROUND_24BIT((gtm_tbuGetTimeBaseChannel( & TBUD1, TBU_CHANNEL1) + (TOOTH_MULTIPLIER * (NOMINAL_TEETH - MISSING_TEETH + REFERENCE_POSITION_OFFSET_IN_TEETH))));
    tmp = ROUND_24BIT((Reference_Position - (TOOTH_MULTIPLIER * 1U)));
    gtm_atomSetCompare0( &ATOMD1, ATOM_CHANNEL1, tmp);
    tmp = ROUND_24BIT((Reference_Position - (TOOTH_MULTIPLIER * 0U)));
    gtm_atomSetCompare1( &ATOMD1, ATOM_CHANNEL1, tmp);
}

static void StopTDNTimeout(void)
{
    TDNTimeoutEnable = 0;
}

static void StartTDNTimeout(uint32_T counter)
{
    TDNTimer = counter;
    TDNTimeoutEnable = 1;
}

static void ReInitTDNStatus(void)
{
    NTimInterruptDiscart = 0u;
    previous_time_stamp = 0u;
    new_time_stamp = 0u;
    previous_diff = 0u;
    diff = 0u;
    LossLock = CRANK_NO_LOCKED;
    Last_event = NULL;
}
