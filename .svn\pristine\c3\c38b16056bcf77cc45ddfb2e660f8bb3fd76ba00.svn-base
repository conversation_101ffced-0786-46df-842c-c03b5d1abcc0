#-------------------------------------------------------------------------------
# Name:        modulo1
# Purpose:
#
# Author:      SalimbeniT
#
# Created:     05/08/2013
# Copyright:   (c) SalimbeniT 2013
# Licence:     <your licence>
#-------------------------------------------------------------------------------
from ctypes import *
import time
import math
import os
def main():
    pass

if __name__ == '__main__':
    main()
Configurazione=[]


s=input("\nRecorder File Name:") #input del file contenente i dati della prova
in_file = open("RecorderExport\\"+s+".txt","r") #apertura in lettura dei file contenente la registrazione
RecorderName=s

config= open("AnalogInTestConfig.txt","r") #apertura in lettura dei file di configurazione


StartFound=0
StopFound=0

s=input("\nTest Start Time:") #input dell'istante di inizio della prova
if s!="": #nel caso l'utente specifichi l'istante lo start sara quello specificato
    Start=float(s)
    StartFound=1

s=input("\nTest Stop Time:") #input dell'istante di fine della prova
if s!="": #nel caso l'utente specifichi l'istante lo stop sara quello specificato
    Stop=float(s)
    StopFound=1




for i in range (1,9): #8 e' il numero massimo di DAC utilizzabili
    config.seek(0)
    for line in config:
        if ("DAC"+str(i)) in line:  #parsing del file di configurazione
            line=next(config)
            NomeSegnale=(line.split(':')[1]).strip()
            if NomeSegnale!="":  #se non viene espresso nella configurazione il nome del segnale abbinato al dac esso viene ignorato
                line=next(config)
                nome_raw=(line.split(':')[1]).strip()
                line=next(config)
                Gain=(line.split(':')[1]).strip()
                line=next(config)
                Offset=(line.split(':')[1]).strip()
                line=next(config)
                HLimit=(line.split(':')[1]).strip()
                line=next(config)
                LLimit=(line.split(':')[1]).strip()
                line=next(config)
                Err_Const=(line.split(':')[1]).strip()
                line=next(config)
                Gn_Err=(line.split(':')[1]).strip()
                line=next(config)
                Signal_Err_Th=(line.split(':')[1]).strip()


                Configurazione.append(("dac"+str(i)+"data",NomeSegnale,Gain,Offset,HLimit,LLimit,Err_Const,Gn_Err,Signal_Err_Th,nome_raw)) #Configurazione e' una lista di tuple contenete le informazioni estratte dalla configurazione per ogni segnale


Data=[]
in_file.seek(0)
Flag=0
for line in in_file:

    if ("TimeStamp") in line:
        line=line[1:-2]
        Intestazione=line.split('"\t"') #tupla contenete le stringhe contenenti il nome delle colonne del file di acquisizione
        line=next(in_file)
        line=next(in_file)
        Flag=1
    if Flag==1:
        Data.append((line[:-1].split('\t')))  #Data e' una lista di tuple contenete i dati parsati dal file di acquisizione

#NB: la tupla di Data ha la stessa dimensione della variabile Intestazione

for riga in Data:
    if (riga[1]!="0")&(riga[2]!="0")&(riga[3]!="0")&(riga[4]!="0")&(StartFound==0)&(StopFound==0): #se non e stato specificato un istante di start della prova si cerca la prima riga diversa da zero e si setta lo start
        Start=int(float(riga[0])+1)
        StartFound=1
    if (riga[1]=="0")&(riga[2]=="0")&(riga[3]=="0")&(riga[4]=="0")&(StartFound==1)&(StopFound==0): #se non e stato specificato un istante di stop della prova si cerca la prima riga uguale a zero dopo l'istante di start e si setta lo stop
        Stop=int(float(riga[0]))
        StopFound=1


""" INIZIO TEST"""
dev_list=[]
deviazione=[]
in_file.seek(0)
Fail=0
Failsegnale=[0]*len(Configurazione)
outfile=open("AnalogInTestReport.txt","w")
#
for k in range (0,len(Data)-1): #per ogni riga contenuta in Data
    for j in range (0,len(Configurazione)): #per ogni segnale sotto test cio per ogni
        Diaghigh=0
        Diaglow=0
        ordine=j
        if Stop>=float(Data[k][0])>=Start: #inizia a prendere in considerazione i dati successivi all'istante di start e precedenti l'istante di stop del test deciso dall'utente.
            dac_data=float(Data[k][Intestazione.index(Configurazione[j][0])])  #estrazione da ogni riga di Data dei valori necessari al test
            signal_data=float(Data[k][Intestazione.index(Configurazione[j][1])])
            gain=float(Data[k][Intestazione.index(Configurazione[j][2])])
            offset=float(Data[k][Intestazione.index(Configurazione[j][3])])
            hlimit=float(Data[k][Intestazione.index(Configurazione[j][4])])
            llimit=float(Data[k][Intestazione.index(Configurazione[j][5])])
            ErrConst=float(Configurazione[j][6])
            GnErr=float(Configurazione[j][7])
            SignalErrTh=float(Configurazione[j][8])
            rowvalue=float(Data[k][Intestazione.index(Configurazione[j][9])])

            SignalErr=abs(signal_data)*GnErr  #calcolo dell'errore massimo nella zona lineare della caratteristica Segnale/Errore

            ref= dac_data/1000*gain+offset    #calcolo del valore corretto del segnale a partire dalla tensione in uscita dal DAC

            if dac_data>=hlimit:              # calcolo dei limiti superiore e inferiore a cui limitare i segnali calcolati dai dati del DAC
                ref=hlimit/1000*gain+offset

            if dac_data<=llimit:
                ref=llimit/1000*gain+offset

            Delta=abs(ref-signal_data)        #errore istantaneo
            if abs(signal_data)<=SignalErrTh: #Zona costante della caratteristica Segnale/Errore
                if Delta<=ErrConst:
                    test=0
                else:
                    test=1
                err=ErrConst


            else:
                if Delta<=SignalErr:  #Zona lineare della caratteristica Segnale/Errore
                    test=0
                else:
                    test=1

                err=SignalErr


            deviazione.append(abs(dac_data-rowvalue)) # lista contenente gli scarti tra il segnale di input alla centralina e la tensione letta sull'adc

##            if 105<deviazione[-1]<107:
##                outfile.write("ECCOLO\n")
##                outfile.write("TIME = %s \n%s = %s \nCorrect Value: %s \nError: %s --> Admissible Error %s \n\n"% (Data[k][0],Configurazione[j][1],signal_data,ref,Delta,err))
##                outfile.write("DAC_DATA:%s\n\n"%dac_data)
##                outfile.write("rowvalue:%s\n\n"%rowvalue)

            if (test==1)&(signal_data!=0)&(dac_data!=0):
                outfile.write("*********ERRORE***********\n")
                outfile.write("TIME = %s \n%s = %s \nCorrect Value: %s \nError: %s --> Admissible Error %s \n\n"% (Data[k][0],Configurazione[j][1],signal_data,ref,Delta,err))
                outfile.write("Tensione in Ingresso reale:%s mV\n"%dac_data)
                outfile.write("Tensione in Ingresso letta:%s mV\n"%rowvalue)
                dev=dac_data-rowvalue
                outfile.write("Errore lettura tensione:%s mV\n\n"%dev)
                Fail=1
                Failsegnale[j]=1

outfile.write("\nRecorder File Name: %s.txt"%RecorderName)
outfile.write("\n\n-------------- TEST RESULT --------------\n\n")
for i in range (0,len(Configurazione)):
    if Failsegnale[i]==0:
        outfile.write("Test PASSED for %s                \n\n" %(Configurazione[i][1]))
    else:
        outfile.write("Test FAILED for %s <---- !!! \n\n" %(Configurazione[i][1]))


somma=0
for i in deviazione:   #viene fatto un calcolo della media dell'errore commesso sulla lettura della tensione sull'adc
    somma=somma+i

media=somma/(len(deviazione))

if Fail==0:
    outfile.write("*******************************************\n")
    outfile.write("*              TEST PASSED                *\n")
    outfile.write("*******************************************\n")
    outfile.write("Test Completed Successfully for all Signals")

else:
    outfile.write("*******************************************\n")
    outfile.write("*              TEST FAILED                *\n")
    outfile.write("*******************************************\n")

outfile.write("\n\nErrore medio nella lettura della tensione: %s mV"%int(media))

print("\n\nStart Time used=%s"%Start)
print("Stop Time used=%s"%Stop)

print("ANALOG TEST COMPLETED\n\n")


outfile.close()
in_file.close()
config.close()

total_file=open("Total_Report.txt",'a')
total_file.write(RecorderName + " : " + str(int(media))+" mV"+"\n")
total_file.close()


path=str(os.path.abspath(__file__)[0:-27])

s=input("Open Report File? [y/n]:")
if s=="y":
    osCommandString = "notepad.exe " + path + "AnalogInTestReport.txt"
    os.system(osCommandString)


