/**
 ******************************************************************************
 **  Filename:      CombCtrl.h
 **  Date:          16-Oct-2018
 **
 **  Model Version: 1.1908
 ******************************************************************************
 **/

#ifndef RTW_HEADER_CombCtrl_h_
#define RTW_HEADER_CombCtrl_h_
#ifndef CombCtrl_COMMON_INCLUDES_
# define CombCtrl_COMMON_INCLUDES_
#include "rtwtypes.h"
#include "zero_crossing_types.h"
#include "mathlib.h"
#endif                                 /* CombCtrl_COMMON_INCLUDES_ */

#include "CombCtrl_types.h"

/* Includes for objects with custom storage classes. */
#include "ETPU_EngineDefs.h"
#include "comb_ctrl.h"

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: Define */
#define BKETACSICYLBALAD_dim           5U
#define BKLOADCYLBAL_dim               4U
#define BKRPMCYLBAL_dim                11U
#define BKRPMENINJCORR_dim             7U
#define BKRPMLAMFIL_dim                7U
#define D1LAM_POLY_P1                  -4615
#define D1LAM_POLY_P2                  18496
#define D1LAM_POLY_P3                  -8393632
#define D2LAM_POLY_P1                  22069
#define D2LAM_POLY_P2                  -16295
#define D2LAM_POLY_P3                  4148744
#define NO_MISF                        0U
#define RPM_2_PERC                     85U

/* Block signals and states (auto storage) for system '<Root>' */
typedef struct {
  int64_T i0;
  int32_T Assignment1[8];              /* '<S18>/Assignment1' */
  int32_T i0_m;
  uint32_T Switch_m[8];                /* '<S75>/Switch' */
  uint32_T Switch1_g[8];               /* '<S75>/Switch1' */
  uint32_T Assignment3[8];             /* '<S61>/Assignment3' */
  uint32_T Assignment4[8];             /* '<S61>/Assignment4' */
  uint32_T Switch2;                    /* '<S76>/Switch2' */
  uint32_T Switch3;                    /* '<S76>/Switch3' */
  uint32_T Product1;                   /* '<S78>/Product1' */
  uint32_T Product1_j;                 /* '<S77>/Product1' */
  uint32_T Switch1;                    /* '<S76>/Switch1' */
  uint32_T Switch;                     /* '<S76>/Switch' */
  struct {
    uint_T is_c3_CombCtrl:2;           /* '<S17>/Median' */
    uint_T is_c20_CombCtrl:2;          /* '<S15>/RecAdaptEnable_Flag' */
    uint_T is_active_c3_CombCtrl:1;    /* '<S17>/Median' */
    uint_T is_active_c20_CombCtrl:1;   /* '<S15>/RecAdaptEnable_Flag' */
  } bitsForTID0;

  int16_T Assignment2_g[8];            /* '<S18>/Assignment2' */
  int16_T Memory_PreviousInput_l;      /* '<S60>/Memory' */
  uint16_T Buffer[56];                 /* '<S17>/Median' */
  uint16_T VettSort[7];                /* '<S17>/Median' */
  uint16_T Switch_l[8];                /* '<S11>/Switch' */
  uint16_T Assignment3_i[8];           /* '<S24>/Assignment3' */
  uint16_T median;                     /* '<S17>/Median' */
  uint16_T i_out;                      /* '<S63>/Chart' */
  uint16_T PreLookUpIdSearch_U16_o2;   /* '<S55>/PreLookUpIdSearch_U16' */
  uint16_T PreLookUpIdSearch_U16_o2_k; /* '<S54>/PreLookUpIdSearch_U16' */
  uint16_T Memory1_PreviousInput;      /* '<S73>/Memory1' */
  uint16_T Memory_PreviousInput;       /* '<S73>/Memory' */
  uint16_T Memory1_PreviousInput_e;    /* '<S72>/Memory1' */
  uint16_T Memory_PreviousInput_c;     /* '<S72>/Memory' */
  uint16_T Memory1_PreviousInput_l;    /* '<S56>/Memory1' */
  uint16_T Memory_PreviousInput_o;     /* '<S56>/Memory' */
  uint16_T Memory1_PreviousInput_g;    /* '<S57>/Memory1' */
  uint16_T Memory_PreviousInput_a;     /* '<S57>/Memory' */
  uint16_T Memory_PreviousInput_b;     /* '<S75>/Memory' */
  uint8_T Index[8];                    /* '<S17>/Median' */
  uint8_T Assignment3_k[8];            /* '<S59>/Assignment3' */
  uint8_T UnitDelay_DSTATE;            /* '<S67>/Unit Delay' */
  uint8_T UnitDelay_DSTATE_g;          /* '<S66>/Unit Delay' */
  uint8_T Memory_PreviousInput_d;      /* '<S82>/Memory' */
  uint8_T Memory_PreviousInput_m;      /* '<S23>/Memory' */
  uint8_T Memory1_PreviousInput_i;     /* '<S23>/Memory1' */
  boolean_T bv0[2];
  boolean_T Mgm_Ad_Table_MODE;         /* '<S15>/Mgm_Ad_Table' */
} D_Work_CombCtrl;

/* Zero-crossing (trigger) state */
typedef struct {
  ZCSigState TriggeredSubsystem2_Trig_ZCE;/* '<S1>/Triggered Subsystem2' */
  ZCSigState TriggeredSubsystem1_Trig_ZCE[2];/* '<S1>/Triggered Subsystem1' */
} PrevZCSigStates_CombCtrl;

/* Invariant block signals (auto storage) */
typedef struct {
  const uint16_T GatewayIn3;           /* '<S12>/Gateway In3' */
  const uint16_T GatewayIn2;           /* '<S12>/Gateway In2' */
  const int16_T GatewayIn1;            /* '<S12>/Gateway In1' */
  const uint16_T GatewayIn5;           /* '<S12>/Gateway In5' */
} ConstBlockIO_CombCtrl;

/* External inputs (root inport signals with auto storage) */
typedef struct {
  uint8_T ev_PowerOn;                  /* '<Root>/ev_PowerOn' */
  uint8_T ev_EOA;                      /* '<Root>/ev_EOA' */
  uint8_T ev_NoSync;                   /* '<Root>/ev_NoSync' */
} ExternalInputs_CombCtrl;

/* Block signals and states (auto storage) */
extern D_Work_CombCtrl CombCtrl_DWork;

/* External inputs (root inport signals with auto storage) */
extern ExternalInputs_CombCtrl CombCtrl_U;
extern const ConstBlockIO_CombCtrl CombCtrl_ConstB;/* constant block i/o */

/*
 * Exported Global Signals
 *
 * Note: Exported global signals are block signals with an exported global
 * storage class designation.  Code generation will declare the memory for
 * these signals and export their symbols.
 *
 */
extern int16_T eta_cylbal;             /* '<S38>/Calc_indices_ad' */
extern int16_T csi_cylbal;             /* '<S38>/Calc_indices_ad' */
extern uint8_T sstab_load_lam;         /* '<S72>/SteadyStateDetect' */
extern uint8_T ofsrCylBal;             /* '<S38>/Product' */
extern uint8_T indr_cylbal;            /* '<S38>/Calc_indices_ad' */
extern uint8_T indc_cylbal;            /* '<S38>/Calc_indices_ad' */
extern uint8_T TriggerCylBalAdat;      /* '<S31>/Switch' */
extern uint8_T EndCylBalLearnCyl;      /* '<S33>/Relational Operator' */

/* Model entry point functions */
extern void CombCtrl_initialize(void);
extern void CombCtrl_step(void);

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Note that this particular code originates from a subsystem build,
 * and has its own system numbers different from the parent model.
 * Refer to the system hierarchy for this subsystem below, and use the
 * MATLAB hilite_system command to trace the generated code back
 * to the parent model.  For example,
 *
 * hilite_system('CombCtrl_gen/CombCtrl')    - opens subsystem CombCtrl_gen/CombCtrl
 * hilite_system('CombCtrl_gen/CombCtrl/Kp') - opens and selects block Kp
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CombCtrl_gen'
 * '<S1>'   : 'CombCtrl_gen/CombCtrl'
 * '<S11>'  : 'CombCtrl_gen/CombCtrl/EOA'
 * '<S12>'  : 'CombCtrl_gen/CombCtrl/Init'
 * '<S13>'  : 'CombCtrl_gen/CombCtrl/Triggered Subsystem1'
 * '<S14>'  : 'CombCtrl_gen/CombCtrl/Triggered Subsystem2'
 * '<S15>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction'
 * '<S16>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction1'
 * '<S17>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc'
 * '<S18>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal'
 * '<S19>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Calc_Ad_Corr'
 * '<S20>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Compare To Zero'
 * '<S21>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table'
 * '<S22>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/RecAdaptEnable_Flag'
 * '<S23>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Zones_Learn'
 * '<S24>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Calc_Ad_Corr/Manage_Learning'
 * '<S25>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Calc_Ad_Corr/Reset_AdaptiveValues'
 * '<S26>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Calc_Ad_Corr/Manage_Learning/Decrement Real World'
 * '<S27>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Calc_Ad_Corr/Manage_Learning/Increment Real World'
 * '<S28>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Calc_Ad_Corr/Manage_Learning/Look2D_IR_U16'
 * '<S29>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Calc_Ad_Corr/Manage_Learning/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S30>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State'
 * '<S31>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/TriggerToLearn'
 * '<S32>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table'
 * '<S33>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning'
 * '<S34>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Reset_Learning'
 * '<S35>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Manage_Learning/Increment Real World'
 * '<S36>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Reset_Learning/Reset_Variables'
 * '<S37>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Calc_Ad_Corr'
 * '<S38>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd'
 * '<S39>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Calc_Ad_Corr/Decrement Real World'
 * '<S40>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Calc_Ad_Corr/Increment Real World'
 * '<S41>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Calc_Ad_Corr/Look2D_IR_U16'
 * '<S42>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Calc_Ad_Corr/Saturation Dynamic'
 * '<S43>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/Calc_Ad_Corr/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S44>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Calc_indices_ad'
 * '<S45>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Increment Real World'
 * '<S46>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd'
 * '<S47>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/Assign_TbInjCorrCylAd'
 * '<S48>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/LookUp 2-D TBGNAD'
 * '<S49>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/Saturation Dynamic'
 * '<S50>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/Assign_TbInjCorrCylAd/Assign_TbInjCorrCylAd'
 * '<S51>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/LookUp 2-D TBGNAD/Look2D_U16_U16_U16'
 * '<S52>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/LookUp 2-D TBGNAD/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 * '<S53>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Zones_Learn/LookUp_IR_U16'
 * '<S54>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Zones_Learn/PreLookUpIdSearch_U1'
 * '<S55>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Zones_Learn/PreLookUpIdSearch_U16'
 * '<S56>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Zones_Learn/Signal_Stability1'
 * '<S57>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Zones_Learn/Signal_Stability2'
 * '<S58>'  : 'CombCtrl_gen/CombCtrl/EOA/Adaptive_Correction/Zones_Learn/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S59>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/EnFilter'
 * '<S60>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc'
 * '<S61>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/Filtering'
 * '<S62>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/Median'
 * '<S63>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/FilterMgm'
 * '<S64>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/FilterParameters'
 * '<S65>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/RateLimiter_S16'
 * '<S66>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/stabLoad_calc'
 * '<S67>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/stabRpm_calc'
 * '<S68>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/FilterMgm/Chart'
 * '<S69>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/FilterMgm/PreLookUpIdSearch_U1'
 * '<S70>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/FilterMgm/PreLookUpIdSearch_U16'
 * '<S71>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S72>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/stabLoad_calc/Steady_State_Detect'
 * '<S73>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/stabRpm_calc/Steady_State_Detect'
 * '<S74>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/Filtering/Filter'
 * '<S75>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/Filtering/FilterState_Reset'
 * '<S76>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/Filtering/Filter/Butterworth_2order_LOWPASS'
 * '<S77>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/Filtering/Filter/ByPassValues'
 * '<S78>'  : 'CombCtrl_gen/CombCtrl/EOA/AvgFFS_Calc/Filtering/Filter/InitialCondition'
 * '<S79>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/FFSIntErr_Calc'
 * '<S80>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator'
 * '<S81>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/FFSIntErr_Calc/Media'
 * '<S82>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/CylBalEn_Calc'
 * '<S83>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/Dead Zone Dynamic'
 * '<S84>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/LookUp_S16_U16'
 * '<S85>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/Media'
 * '<S86>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/RateLimiter_S32'
 * '<S87>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/Saturation Dynamic'
 * '<S88>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/CylBalEn_Calc/Compare To Constant2'
 * '<S89>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/CylBalEn_Calc/Compare To Zero'
 * '<S90>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/CylBalEn_Calc/LookUp_U16_U16'
 * '<S91>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/CylBalEn_Calc/LookUp_U16_U16/Data Type Conversion Inherited3'
 * '<S92>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/LookUp_S16_U16/Data Type Conversion Inherited3'
 * '<S93>'  : 'CombCtrl_gen/CombCtrl/EOA/CylBal/PI_Regulator/RateLimiter_S32/Data Type Conversion Inherited1'
 * '<S94>'  : 'CombCtrl_gen/CombCtrl/Init/Lam_Vector_Reset'
 */

/*-
 * Requirements for '<Root>': CombCtrl
 */
#endif                                 /* RTW_HEADER_CombCtrl_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.3 (R2017b)24-Jul-2017                                             *
 * Simulink 9.0 (R2017b)24-Jul-2017                                           *
 * Simulink Coder 8.13 (R2017b)24-Jul-2017                                    *
 * Embedded Coder 6.13 (R2017b)24-Jul-2017                                    *
 * Stateflow 9.0 (R2017b)24-Jul-2017                                          *
 * Fixed-Point Designer 6.0 (R2017b)24-Jul-2017                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
