/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Cfg.h
 **  Date:          21-Jun-2023
 **
 **  Model Version: 1.334
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Cfg_h_
#define RTW_HEADER_TLE9278BQX_Cfg_h_
#ifndef TLE9278BQX_Cfg_COMMON_INCLUDES_
# define TLE9278BQX_Cfg_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TLE9278BQX_Cfg_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  uint32_T CCCodeBlock;                /* '<S7>/C//C++ Code Block' */
  uint32_T CCCodeBlock_b;              /* '<S6>/C//C++ Code Block' */
  uint16_T SBCGlobalStatusReg_m[25];   /* '<S7>/C//C++ Code Block1' */
  uint16_T SBCGlobalStatusReg_c[25];   /* '<S6>/C//C++ Code Block1' */
} DW_TLE9278BQX_Cfg_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_TLE9278BQX_Cfg_T TLE9278BQX_Cfg_DW;

/* Model entry point functions */
extern void TLE9278BQX_Cfg_initialize(void);

/* Exported entry point function */
extern void TLE9278BQX_Cfg_Bkg(void);

/* Exported entry point function */
extern void TLE9278BQX_Cfg_Init(void);

/* Exported entry point function */
extern void TLE9278BQX_Cfg_Reset(void);

/* Exported entry point function */
extern void TLE9278BQX_Cfg_Set(void);

/* Exported data declaration */

/* Declaration for custom storage class: ELD_EXPORT_CONST */
extern const uint16_T SBC_BYPASS_EE_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_BYPASS_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_CLEAR_WDT_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_INIT_READ[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_INIT_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_NORMAL_READ[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_NORMAL_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_PRE_INIT_READ[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_REG[25];     /* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_REG_MASK[25];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint8_T SBC_REG_SIZE;     /* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_STOP_READ[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_STOP_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_TO_NORMAL_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_TO_RESET_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_TO_SLEEP_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */
extern const uint16_T SBC_TO_STOP_WRITE[30];/* Referenced by: '<S5>/Init_Cfg' */

/* data */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint32_T KeyReqMsgOnD;          /* '<S3>/Merge' */

/* Debug trigger messages start action */
extern uint16_T SBCDataRxBuffer[30];   /* '<S5>/ZERO2' */

/* SPI Rx data message */
extern uint16_T SBCGlobalStatusReg[25];/* '<S5>/ZERO1' */

/* Status data message */


/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TLE9278BQX_Cfg'
 * '<S1>'   : 'TLE9278BQX_Cfg/Model Info'
 * '<S2>'   : 'TLE9278BQX_Cfg/TLE9278BQX_Cfg'
 * '<S3>'   : 'TLE9278BQX_Cfg/TLE9278BQX_Cfg/Merger'
 * '<S4>'   : 'TLE9278BQX_Cfg/TLE9278BQX_Cfg/fc_Bkg'
 * '<S5>'   : 'TLE9278BQX_Cfg/TLE9278BQX_Cfg/fc_Init'
 * '<S6>'   : 'TLE9278BQX_Cfg/TLE9278BQX_Cfg/fc_Reset'
 * '<S7>'   : 'TLE9278BQX_Cfg/TLE9278BQX_Cfg/fc_Set'
 * '<S8>'   : 'TLE9278BQX_Cfg/TLE9278BQX_Cfg/fc_Init/Init_Cfg'
 */

/*-
 * Requirements for '<Root>': TLE9278BQX_Cfg
 */
#endif                                 /* RTW_HEADER_TLE9278BQX_Cfg_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/