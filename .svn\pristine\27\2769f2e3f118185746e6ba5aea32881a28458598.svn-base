#ifndef _UDS_EISB_FE_
#define _UDS_EISB_FE_

/* UDS Services enable masks */
#define UDS_SERVICE_START_SESSION_EN                  0x00000001u
#define UDS_SERVICE_ECU_RESET_EN                      0x00000002u
#define UDS_SERVICE_CLEAR_DIAGNOSTIC_INFORMATION_EN   0x00000004u
#define UDS_SERVICE_READ_DTC_INFO_EN                  0x00000008u
#define UDS_SERVICE_READ_DATA_BY_ID_EN                0x00000010u
#define UDS_SERVICE_READ_MEM_BY_ADDR_EN               0x00000020u
#define UDS_SERVICE_SECURITY_ACCESS_EN                0x00000040u
#define UDS_SERVICE_COMM_CONTROL_EN                   0x00000080u
#define UDS_SERVICE_WRITE_DATA_BY_ID_EN               0x00000100u
#define UDS_SERVICE_IOCONTROL_EN                      0x00000200u
#define UDS_SERVICE_ROUTINE_CONTROL_EN                0x00000400u
#define UDS_SERVICE_REQUEST_DOWNLOAD_EN               0x00000800u
#define UDS_SERVICE_TRANSFER_DATA_EN                  0x00001000u
#define UDS_SERVICE_REQUEST_TRANSFER_EXIT_EN          0x00002000u
#define UDS_SERVICE_WRITE_MEM_BY_ADDR_EN              0x00004000u
#define UDS_SERVICE_TESTER_PRESENT_EN                 0x00008000u
#define UDS_SERVICE_CTRL_DTC_SETTING_EN               0x00010000u

/* UDS Session services enable mask */
#define DEFAULT_ENABLED_SERVICES ( UDS_SERVICE_START_SESSION_EN \
                                   |UDS_SERVICE_ECU_RESET_EN \
                                   |UDS_SERVICE_CLEAR_DIAGNOSTIC_INFORMATION_EN \
                                   |UDS_SERVICE_READ_DTC_INFO_EN \
                                   |UDS_SERVICE_READ_DATA_BY_ID_EN \
                                   |UDS_SERVICE_ROUTINE_CONTROL_EN \
                                   |UDS_SERVICE_WRITE_DATA_BY_ID_EN \
                                   |UDS_SERVICE_TESTER_PRESENT_EN )

#define ECUPROGR_ENABLED_SERVICES ( UDS_SERVICE_START_SESSION_EN) // with the new bootloader scheme only DiagnosticSessionControl is actually supported, and followed by a reset

#define EXTENDED_ENABLED_SERVICES ( UDS_SERVICE_START_SESSION_EN \
                                   |UDS_SERVICE_ECU_RESET_EN \
                                   |UDS_SERVICE_CLEAR_DIAGNOSTIC_INFORMATION_EN \
                                   |UDS_SERVICE_READ_DTC_INFO_EN \
                                   |UDS_SERVICE_READ_DATA_BY_ID_EN \
                                   |UDS_SERVICE_READ_MEM_BY_ADDR_EN \
                                   |UDS_SERVICE_SECURITY_ACCESS_EN \
                                   |UDS_SERVICE_COMM_CONTROL_EN \
                                   |UDS_SERVICE_ROUTINE_CONTROL_EN \
                                   |UDS_SERVICE_CTRL_DTC_SETTING_EN \
                                   |UDS_SERVICE_WRITE_DATA_BY_ID_EN \
                                   |UDS_SERVICE_IOCONTROL_EN \
                                   |UDS_SERVICE_TESTER_PRESENT_EN )

#define MANUFACTURE_EOL_ENABLED_SERVICES ( UDS_SERVICE_START_SESSION_EN \
                                   |UDS_SERVICE_ECU_RESET_EN \
                                   |UDS_SERVICE_CLEAR_DIAGNOSTIC_INFORMATION_EN \
                                   |UDS_SERVICE_READ_DTC_INFO_EN \
                                   |UDS_SERVICE_READ_DATA_BY_ID_EN \
                                   |UDS_SERVICE_READ_MEM_BY_ADDR_EN \
                                   |UDS_SERVICE_SECURITY_ACCESS_EN \
                                   |UDS_SERVICE_ROUTINE_CONTROL_EN \
                                   |UDS_SERVICE_WRITE_DATA_BY_ID_EN \
                                   |UDS_SERVICE_IOCONTROL_EN \
                                   |UDS_SERVICE_TESTER_PRESENT_EN )

#define SUPPLIER_ENABLED_SERVICES  ( UDS_SERVICE_START_SESSION_EN \
                                   |UDS_SERVICE_ECU_RESET_EN \
                                   |UDS_SERVICE_CLEAR_DIAGNOSTIC_INFORMATION_EN \
                                   |UDS_SERVICE_READ_DTC_INFO_EN \
                                   |UDS_SERVICE_READ_DATA_BY_ID_EN \
                                   |UDS_SERVICE_READ_MEM_BY_ADDR_EN \
                                   |UDS_SERVICE_SECURITY_ACCESS_EN \
                                   |UDS_SERVICE_COMM_CONTROL_EN \
                                   |UDS_SERVICE_ROUTINE_CONTROL_EN \
                                   |UDS_SERVICE_CTRL_DTC_SETTING_EN \
                                   |UDS_SERVICE_WRITE_DATA_BY_ID_EN \
                                   |UDS_SERVICE_IOCONTROL_EN \
                                   |UDS_SERVICE_TESTER_PRESENT_EN )

#define MAX_DIDS_READABLE   3u
//#define SUPPORT_FLASH_OPERATION  //Available only in boot, inhibited in application
//#define USE_HEX_FILE                 // Keep undef if BIN file is used for software upadates
#define USE_BIN_FILE                // Keep undef if HEX file is used for software upadates

#if defined (USE_HEX_FILE)
#define LAST_ADDRESS_OFFSET 1u      // for eg. with KTM procedures (crc +hex files)
#elif defined (USE_BIN_FILE)
#define LAST_ADDRESS_OFFSET 0u      // for eg. with Fiat-FCA procedures (idx+prm+bin files)
#else
#error flash end address offset not defined!!!
#endif

#define USE_PROGRAMMING_STATUS      // if defined, it allows error storing in EE2 during reflashing procedures (for example in FIAT-FCA download procedure)
//#define USE_PREVIOUS_SEED           // if defined, it allows seed reuse in case of security failure until the locked is unlocked, otherwise a nes seed is sent every time
#define USE_SECURITY_LEVEL_CHECK    // if defined, each diagnostic session can unlock only one security level per time
//#define USE_FLASHUPDATE_CNT        // if defined, after each reprogramming that is carried out successfully, FlashRewritingCntEE is increased the value by one
#define USE_NRC78_SERVICE_2E       // if defined, an NRC $28 is returned as soon as a $2e request has been received. Actual writing status (error/success) will be returned later


#define S3SERVER_TIMEOUT                           (502u) // 502 times 10msec task ~ 5 sec + 20 msec of tollerance according to CDD specification

#define SEC_TIMEOUT_CUSTOMER                       (1000u)// 1000 times 10msec task ~ 10 sec timeout according as required by the customer


#endif // _UDS_EISB_FE_

