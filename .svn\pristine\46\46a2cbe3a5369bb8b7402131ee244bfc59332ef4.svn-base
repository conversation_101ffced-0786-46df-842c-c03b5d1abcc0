/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_ADC.c
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Mocci A
******************************************************************************/

#ifdef _BUILD_SAFETYMNGR_ADC_

#ifndef _BUILD_SAFETYMNGR_
#error ADC Safety Module enabled without _BUILD_SAFETYMNGR_ macro enabled
#endif /*  _BUILD_DIAGCANMGM_ */
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_ADC.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_ADC_BiasCheck
**
**   Description:
**    SM_MCU_3_62 and SM_MCU_4_95 coverage
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SafetyMngr_ADC_BiasCheck(void)
{
    /* code construction start */
#if 0
#if ( STD_ON == SAFE_ADC_BIASCHECK  )
    uint16 l_SARBIAS_0 = 0x0U;
    uint16 l_SARBIAS_1 = 0x0U;
    uint16 l_SARBIAS_2 = 0x0U;
    uint16 l_SARBIAS_3 = 0x0U;

    l_SARBIAS_0 = (uint16_t)SAR_ADC_12BIT_B0.TCDR[28].R;
    l_SARBIAS_1 = (uint16_t)SAR_ADC_12BIT_B0.TCDR[29].R;
    l_SARBIAS_2 = (uint16_t)SAR_ADC_12BIT_B0.TCDR[30].R;
    l_SARBIAS_3 = (uint16_t)SAR_ADC_12BIT_B0.TCDR[31].R;

    if( (l_SARBIAS_0 < 300) || (l_SARBIAS_0 > 400))
    {
        SafetyMngr_ReportError(SAFE_ERR_ADC_SAR_BIAS0_OUT_OF_RANGE, FALSE);
    }

    if( (l_SARBIAS_1 < 1000) || (l_SARBIAS_1 > 1300))
    {
        SafetyMngr_ReportError(SAFE_ERR_ADC_SAR_BIAS1_OUT_OF_RANGE, FALSE);
    }

    if( (l_SARBIAS_2 < 2000) || (l_SARBIAS_2 > 2500))
    {
        SafetyMngr_ReportError(SAFE_ERR_ADC_SAR_BIAS2_OUT_OF_RANGE, FALSE);
    }

    if( (l_SARBIAS_3 < 3300) || (l_SARBIAS_3 > 4000))
    {
        SafetyMngr_ReportError(SAFE_ERR_ADC_SAR_BIAS3_OUT_OF_RANGE, FALSE);
    }
#endif
#endif
    /* code construction end */
}

/******************************************************************************
**   Function    : SafetyMngr_ADC_Init
**
**   Description:
**    Unit in draft
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SafetyMngr_ADC_Init(void)
{
    /* code construction start */
#if 0
    /* Do Nothing Design status in Draft */
    /* check if something is needed for this unit */
#endif
    /* code construction end */
}

/******************************************************************************
**   Function    : SafetyMngr_ADC_SM_MCU_4_39
**
**   Description:
**    Unit in draft
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SafetyMngr_ADC_SM_MCU_4_39(void)
{
    /* code construction start */
#if 0
    /* Do Nothing Design status in Draft */
    /**
    * requirement:   SM_MCU_4_39  Software shall compare the value sampled by the two ADCs and decide on their
    *
    */
#endif
    /* code construction end */
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/* None */

#endif // _BUILD_SAFETYMNGR_ADC_
/****************************************************************************
 ****************************************************************************/

