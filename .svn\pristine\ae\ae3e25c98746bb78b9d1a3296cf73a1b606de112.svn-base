#**************************************************************************/
#* FILE NAME: mpc5500_asmcfg.s              COPYRIGHT (c) Freescale 2004  */
#*                                                All Rights Reserved     */
#* DESCRIPTION:                                                           */
#* This file contains functions for the MPC5500 assembly configuration.   */
#=========================================================================*/
#*                                                                        */
#* REV      AUTHOR       DATE       DESCRIPTION OF CHANGE                 */
#* ---   -----------   ----------   ---------------------                 */
#* 0.1   G. <PERSON>    26/Mar/04    Initial version                       */ 
#* 0.2   G<PERSON>    29/Apr/04    Made compiler names unique for        */
#*                                    assembly configuration.             */
#*                                  Single designation for rcw values.    */
#* 0.3   G. <PERSON>    13/May/04    Changed definition of FMPLL_SYNCR     */
#*                                    register settings.                  */
#* 0.4   G. Jackson    15/May/04    Removed msync and isync from tlbwe    */
#*                                    commands.                           */
#* 0.5   G. Jackson    25/May/04    Changed __OPCOUNT to __SRAM_LOAD_SIZE */
#*                                  Changed __SRAM_OPCODE to __SRAM_LOAD  */
#*                                  Changed cfg_OPCODE to cfg_SRAM_LOAD   */
#*                                  Changed OPCNT_OFFSET to IP_ADVANCE    */
#* 0.6   G. Jackson    12/Jun/04    Changed TLB entries to work with      */
#*                                  MPC5554 Rev. 0.3 and later for the    */
#*                                  BAM, PBRIDGE_B, and Internal FLASH.   */
#* 0.7   G. Jackson    30/Jun/04    Added entries for RCHW (RCHW_VAL)     */
#* 0.8   G. Jackson    05/Aug/04    Added cfg_PNTRS for R13 and R2        */
#* 0.9   G. Jackson    18/Aug/04    Added cfg_ROMCPY for .data and .sdata */
#* 0.91  G. Jackson    20/Sep/04    cfg_ROMCPY changed to load by bytes.  */
#* 0.92  G. Jackson    11/Oct/04    L1CSR0 checks added for complete      */
#*                                    cache operation.                    */
#* 1.0   G. Jackson    12/Oct/04    Green Hills now does not require      */
#*                                    quotation marks around the section  */
#*                                  Added syntax to generate symbols for  */
#*                                    debug.                              */
#**************************************************************************/

    
   // #include "asm_ghs_abstraction.h", MC used explicit vle instructions

    .equ __GRNHS__,  1  // Designation for the Green Hills compiler
    .equ __PEGNU__,  0  // Designation for the P&E Micro Gnu compiler
    .equ __DIABCC__, 0  // Designation for the Wind River compiler
    .equ __CWWRKS__, 0  // Designation for the Metrowerks CodeWarrior compiler
    
    .include "../../tree/COMMON/CONFIG/ASM/mpc5500_usrdefs.inc"


    .globl cfg_MMU
    .globl cfg_CACHE      
    .globl disable_CACHE 
 
#################################################
#       This is the start of the .init section.

    .if __PEGNU__
    .section ".init","ax" # The "ax" is required to generate "non-text" code
    .endif

    .if __GRNHS__
    .section .init,"axv"     # The "axv" generates symbols for debug
    .vle
    .endif

    .if __DIABCC__
    .section .init,c      # The "c" generates symbols for debug
    .endif
    
     .if __CWWRKS__
    .section .init,text   # The "text" generates symbols for debug
    .endif

#*********************************************************************/
#*************************************************************************/
#                        MMU Functions                                   */
#*************************************************************************/

#*****************************************************************************/
# FUNCTION     : cfg_MMU                                                     */
# PURPOSE      : This function modifies the MMU TLB (translation lookaside   */
#                 buffer) table by writing to the appropriate MAS registers. */
# INPUT NOTES  : Requires SPRs defined and a data table for the TLB entries  */
#                mmu_tlb0 through mmu_tlb11, mmu_tlb15 from                  */
#                mpc5500_usrdefs.inc.                                        */
# RETURN NOTES : None                                                        */
# WARNING      : Registers used: R3,R5. Commands "msync" and "isync" are not */
#                required around the tlbwe since we are at configuration and */
#                 other background operations cannot be active.              */
#*****************************************************************************/

cfg_MMU:

#***************************************************/
#     setup MMU                                    */
#***************************************************/

# Configure the TLB0 PBRIDGE_B size to 1M.    
#default configuration

    e_lis   r3, mmu_tlb0@h     # base address of MAS Constants
    e_or2i r3,mmu_tlb0@l
    e_lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    e_lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    e_lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    e_lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe                    # Write the entry to the TLB 

# Configure the TLB1 Flash (1) size to 2M

    e_lis   r3, mmu_tlb1@h     # base address of MAS Constants
    e_or2i r3,mmu_tlb1@l 
    e_lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    e_lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    e_lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    e_lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    msync                    # synchronize for running out of Flash
    tlbwe                    # Write the entry to the TLB 
    se_isync                    # synchronize for running out of Flash 


# Configure the TLB2 EBI size to 16MB.  EBI is not present in MPC5642A, but is present in MPC5644A
#default configuration

    e_lis   r3, mmu_tlb2@h     # base address of MAS Constants
    e_or2i r3, mmu_tlb2@l
    e_lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    e_lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    e_lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    e_lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe                    # Write the entry to the TLB 

# Configure the TLB3 SRAM size to 128KB. 

    e_lis   r3, mmu_tlb3@h     # base address of MAS Constants
    e_or2i r3, mmu_tlb3@l
    e_lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    e_lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    e_lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    e_lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe                    # Write the entry to the TLB 

# Configure the TLB4 PBRIDGE_B size to 1M. 
#default configuration
    
    e_lis   r3, mmu_tlb4@h     # base address of MAS Constants
    e_or2i r3, mmu_tlb4@l
    e_lwz   r5,0(r3)           # Get MAS0 value
    mtspr mas0,r5            # mtspr MAS0,r5
    e_lwzu  r5,4(r3)           # Get MAS1 value
    mtspr mas1,r5            # mtspr MAS1,r5
    e_lwzu  r5,4(r3)           # Get MAS2 value
    mtspr mas2,r5            # mtspr MAS2,r5
    e_lwzu  r5,4(r3)           # Get MAS3 value
    mtspr mas3,r5            # mtspr MAS3,r5
    tlbwe  

    se_blr
# End of cfg_MMU 

##*************************************************************************/

#************************************************************************/
# FUNCTION     : cfg_CACHE                                             
# PURPOSE      : This function initializes the CACHE by invalidating and 
#                  then enabling the cache.                             
# INPUT NOTES  : CACHE_CLEAR, CACHE_SETTINGS, L1CSR0                     
# RETURN NOTES : None                                                    
# WARNING      : Registers used: R5,R8,R9,R10                           

cfg_CACHE:

    wrteei 0

# To activate cache invalidate operation,
# place a "1" in the CINV bit location.  (L1CSR1[30])
#  This operation takes 134 cycles to complete
    e_lis   r5, CACHE_CLEAR@h        # Load upper L1CSR1 (0x0) into R5
    e_or2i r5, CACHE_CLEAR@l    # Load lower L1CSR1 (CINV bit) into R5
    se_isync                          # Required before changing the CE bit to
    msync    
    mtspr 1011,r5                  # Move R5 to the L1CSR1 (SPR 1010)register.  
#                                  # This should start the CACHE invalidate
#                                  #  operation.
# Make sure that CINV is not active/finished
label_CINV_check:                  # Make sure that CINV is not active/finished
# The CINV mask bit will be compared to L1CSR1[30] CINV bit
    e_lis   r8, 0x0000               # Load upper CINV mask (0x0000) into R8
    e_or2i   r8, 0x0002           # Load lower L1CSR1[30] CINV mask bit into R8
CHECK_CINV:
    mfspr r9, 1011               # Move the L1CSR1 register value to R9.
# "AND" the two registers together to check for active CINV bit
    and.  r10,r8, r9
#                                  # The "." after "and" activates the condition register
    e_bne CHECK_CINV                 # Branch if not zero. CINV still=1.

# Enable cache
    mfspr r5, 1011               # Retrieve the L1CSR0 register value
    //oris  r5, r5, CACHE_SETTINGS@h # OR in CWM and DPB in upper L1CSR1[11:12]
    e_or2is  r5, CACHE_SETTINGS@h # OR in CWM and DPB in upper L1CSR1[11:12], MC tbv
    e_or2i r5, CACHE_SETTINGS@l # OR in cache enable bit L1CSR1[31]
    se_isync                          # Required before changing the CE bit to
    msync                          #  prevent disable/enable cache during access
    mtspr 1011, r5               # Return value to L1CSR1 to enable the cache

    wrteei 1

    se_blr   
# End of cfg_CACHE

#************************************************************************/
# FUNCTION     : disable_CACHE                                             
# PURPOSE      : This function disables the CACHE by invalidating and 
#                  then disabling the cache.                             
# INPUT NOTES  : CACHE_CLEAR, CACHE_SETTINGS, L1CSR0                     
# RETURN NOTES : None                                                    
# WARNING      : Registers used: R5,R8,R9,R10                           

disable_CACHE:

wrteei 0

# To activate cache invalidate operation,
# place a "1" in the CINV bit location.  (L1CSR0[30])
#  This operation takes 134 cycles to complete
    e_lis   r5, CACHE_CLEAR@h        # Load upper L1CSR1 (0x0) into R5
    e_or2i   r5, CACHE_CLEAR@l    # Load lower L1CSR1 (CINV bit) into R5  
    se_isync                          # Required before changing the CE bit to
    msync    
    mtspr 1011,r5                # Move R5 to the L1CSR1(SPR 1011)register.  
#                                  # This should start the CACHE invalidate
#                                  #  operation.
# Make sure that CINV is not active/finished
label_CINV_check2:                  # Make sure that CINV is not active/finished
# The CINV mask bit will be compared to L1CSR0[30] CINV bit
    e_lis   r8, 0x0000               # Load upper CINV mask (0x0000) into R8
    e_or2i r8, 0x0002           # Load lower L1CSR1[30] CINV mask bit into R8
CHECK_CINV2:
    mfspr r9, 1011               # Move the L1CSR1 register value to R9.
# "AND" the two registers together to check for active CINV bit
    and.  r10,r8, r9
#                                  # The "." after "and" activates the condition register
    e_bne CHECK_CINV2                 # Branch if not zero. CINV still=1.

#DisableCache
    e_lis   r5, 0
    se_isync                          # Required before changing the CE bit to
    msync    
    mtspr 1011,r5                 # Move R5 to the L1CSR1(SPR 1011)register.     

    wrteei 0

    se_blr   
# End of cfg_CACHE



##*************************************************************************/
# FUNCTION     : MMU DATA Tables                                          */
# PURPOSE      : This defines the MMU data tables for the TLB entries     */
#                which are set in the file mpc5500_asmcfg.s               */
# INPUT NOTES  : Requires that the TLB settings be in MPC5500_defs.inc    */
# RETURN NOTES : mmu_tlb0 [TLB0_MAS[0:3] through mmu_tlb11 [TLB0_MAS[0:3] */
# WARNING      : Registers used: none. Section is: .rodata                */
##*************************************************************************/


# Solinas : 07/04/2005 Section declaration for MMU TLBs :
    
    .if __PEGNU__
    .section ".rodata" 
    .endif

    .if __CWWRKS__ | __DIABCC__ | __GRNHS__
    .section .rodata
    .endif
        
    
#*************************************************************************/
#* DESCRIPTION:                                                          */  
#* This table contains definitions for the MPC564xA MMU TLB entries.      */
#* The bit definitions used in the TLB defines are located below.        */  
#* The second half of the file is the TLB setup code in mpc5500_asmcfg.s */
#*************************************************************************/ 

#*** TLB DEFINES ***/

#** TLB entry 0 - PBRIDGE_B set to 1M **
mmu_tlb0:
# TLB1_MAS0
    .long ( TLB_SELECT | TLB_ENTRY0 )
# TLB1_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_1M )
# TLB1_MAS2
    .long ( PBRIDGEB_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_GUARDED | PAGE_BIG_ENDIAN )
# TLB1_MAS3
    .long ( PBRIDGEB_BASE_ADDR | READWRITEEXECUTE )

#** TLB entry 1 - Internal FLASH set to 2MB **
mmu_tlb1:
# TLB1_MAS0
    .long ( TLB_SELECT | TLB_ENTRY1 )
# TLB1_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_2M )
# TLB1_MAS2
    .long ( FLASH_BASE_ADDR | CACHE_WRITE_BACK | CACHE_ACTIVE | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
# TLB1_MAS3
    .long ( FLASH_BASE_ADDR | READWRITEEXECUTE )

#** TLB entry 2 - External Bus Interface set to 16MB **   EBI is not present in MPC5642A, but is present in MPC5644A
mmu_tlb2:
# TLB2_MAS0
    .long ( TLB_SELECT | TLB_ENTRY2 )
# TLB2_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_16M )
# TLB2_MAS2
    .long ( EXTBUSINT_BASE_ADDR | CACHE_WRITE_BACK | CACHE_ACTIVE | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
# TLB2_MAS3
    .long ( EXTBUSINT_PHY_ADDR | READWRITEEXECUTE )


#** TLB entry 3 - Internal SRAM 128K **
mmu_tlb3:
# TLB2_MAS0
    .long ( TLB_SELECT | TLB_ENTRY3 )
# TLB2_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_128K )
# TLB2_MAS2
    .long ( SRAM_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_NOT_GUARDED | PAGE_BIG_ENDIAN )
# TLB2_MAS3
    .long ( SRAM_BASE_ADDR | READWRITEEXECUTE )

#** TLB entry 4 - PBRIDGE_A set to 1MB **
mmu_tlb4:
# TLB3_MAS0
    .long ( TLB_SELECT | TLB_ENTRY4)
# TLB3_MAS1
    .long ( TLB_ENTRY_VALID | ENTRY_PROTECTED | GLOBAL_MATCH | TS_IS_COMPARE | TSIZ_1M )
# TLB3_MAS2
    .long ( PBRIDGEA_BASE_ADDR | CACHE_WRITE_BACK | CACHE_INHIBIT | MEM_NO_COHERENCE | PAGE_GUARDED | PAGE_BIG_ENDIAN )
# TLB3_MAS3
    .long ( PBRIDGEA_BASE_ADDR | READWRITEEXECUTE )


