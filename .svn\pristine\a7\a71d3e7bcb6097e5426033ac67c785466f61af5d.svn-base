/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
#ifndef _STM_CFG_H_
#define _STM_CFG_H_

/*********************************************************************/
/*  STM Control Register- some possible configurations               */
/*********************************************************************/
/// Freeze enable bit
#define STM_CR_FRZ_EN 0x00000002u
/// Timer counter enable Bit
#define STM_CR_TEN_EN 0x00000001u

/*********************************************************************/
/*  STM Channel Control Register- some possible configurations       */
/*********************************************************************/
#define STM_CCR_CEN_EN 0x00000001u

/*********************************************************************/
/*  STM Channel Interrupt Register- some possible configurations     */
/*********************************************************************/
#define STM_CIR_CIF_EN 0x00000001u

/*********************************************************************/
/*  STM Channel Compare Register- some possible configurations       */
/*********************************************************************/
#define STM_MAX_TIMEOUT    ((uint32_T)(0xFFFFFFFFU))

#define STM_500US_TIMEOUT  ((uint32_T)(PBRIDGE_CLK * 500u) - 1u)
#define STM_100MS_TIMEOUT  ((uint32_T)(PBRIDGE_CLK * 100000u) - 1u)
#define STM_10MS_TIMEOUT   ((uint32_T)(PBRIDGE_CLK * 10000u) - 1u)
#define STM_5MS_TIMEOUT    ((uint32_T)(PBRIDGE_CLK * 5000u) - 1u)
#define STM_1MS_TIMEOUT    ((uint32_T)(PBRIDGE_CLK * 1000u) - 1u)

/*********************************************************************/
/*  NUMBER OF STM MODULES IN K2 MICROCONTROLLER                      */
/*********************************************************************/
#define STM_NUM 1u // only one in the RM, two engines in the uC memory map header file!!!

/*********************************************************************/
/*  NUMBER OF STM CHANNELS PER ENGINE IN K2 MICROCONTROLLER          */
/*********************************************************************/
#define STM_NUM_OF_CHANNELS   4u  // STM_0 number of channels

/*********************************************************************/
/*  STM ENABLING AND CONFIGURATION                                    */
/*********************************************************************/
#define STM_ENABLE    1u

#if (STM_ENABLE == 1u)
/* STM CHANNELS ENABLING */
#define STM_CH0_ENABLE 1u
#define STM_CH1_ENABLE 0u
#define STM_CH2_ENABLE 0u
#define STM_CH3_ENABLE 0u

/* STM CHANNELS CONFIGURATIONS */
#if(STM_CH0_ENABLE == 1u)
#define STM_CHANNEL0                  0u                  // assigned channel
#define STM_TIMEOUT0_INIT             (STM_500US_TIMEOUT)  // assigned timeout
#define STM_TIMEOUT0                  (STM_10MS_TIMEOUT)  // assigned timeout
#define STM_FUNCINT0                  0u                  // enable event task activation
#endif // (STM_CH0_ENABLE == 1u)

#if(STM_CH1_ENABLE == 1u)
#define STM_CHANNEL1                  1u                  // assigned channel
#define STM_TIMEOUT1                  (0u)                // assigned timeout
#define STM_FUNCINT1                  0u                  // enable event task activation
#endif // (STM_CH1_ENABLE == 1u)

#if(STM_CH2_ENABLE == 1u)
#define STM_CHANNEL2                  2u                  // assigned channel
#define STM_TIMEOUT2                  (0u)                // assigned timeout
#define STM_FUNCINT2                  0u                  // enable event task activation
#endif // (STM_CH2_ENABLE == 1u)

#if(STM_CH3_ENABLE == 1u)
#define STM_CHANNEL3                  3u                  // assigned channel
#define STM_TIMEOUT3                  (0u)                // assigned timeout
#define STM_FUNCINT3                  0u                  // enable event task activation
#endif // (STM_CH3_ENABLE == 1u)

#endif //STM_ENABLE


#endif //_STM_CFG_H_
/****************************************************************************
 ****************************************************************************/

