/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/*
 * File: RonDetectCross_eep.c
 *
 * Code generated for Simulink model 'RonDetectCross'.
 *
 * Model version                  : 1.953
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Wed Feb 16 17:17:21 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor->Custom Processor
 * Emulation hardware selection:
 *    Differs from embedded hardware (Intel->x86-64 (Windows64))
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. ROM efficiency
 *    5. RAM efficiency
 *    6. Traceability
 *    7. Debugging
 *    8. Polyspace
 * Validation result: Passed (31), Warning (1), Error (0)
 */
#include "rtwtypes.h"
 
/* Enumerated types definition */
typedef uint8_T enum_StRonCCheck;
#define RD_CC_DISABLED                 ((enum_StRonCCheck)0U)    /* Default value */
#define RD_CC_NOTSTORED                ((enum_StRonCCheck)1U)
#define RD_CC_STORED                   ((enum_StRonCCheck)2U)
#define RD_CC_STOREDIN                 ((enum_StRonCCheck)3U)
#define RD_CC_SEARCH                   ((enum_StRonCCheck)4U)
#define RD_CC_FOUND                    ((enum_StRonCCheck)5U)
#define RD_CC_FOUNDIN                  ((enum_StRonCCheck)6U)
#define RD_CC_WAIT_STOREDIN            ((enum_StRonCCheck)7U)
#define RD_CC_WAIT_FOUNDIN             ((enum_StRonCCheck)8U)
 

/* Exported data definition */

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_EE_INTERFACE */
uint8_T FlgRonInheritEE = ((uint8_T)0U);/* '<S2>/MFlgRonInheritEE' */

/* Ron level inherited from other engine bank - stored value */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */