
#print "Script to modify Kit<PERSON>reate_merge_E-SHOCK.bat is starting\n";

# check parameters or die
die "ERROR of eldor_calib_end.pl script - Usage: eldor_calib_end.pl -infiles [map_file_name.map] [KitCreate_file to modify] -key [END_OF_ELDOR_CALIB_HEX\END_OF_ELDOR_CALIB_HEX_EVB]\n" if $#ARGV !=4;

use Cwd;

#my $hex_offsetF0 = hex $offsetF0;

$dir = getcwd();
opendir (DIR, $dir) or die "Can not open directory $dir";


$mapFile = $ARGV[1];
open (FILE,  $mapFile) or die "Can't open map file: $mapFile!";  


$KitCreate = $ARGV[2];
open (FILE_F0,  $KitCreate) or die "Can't open KitCreate file: $KitCreate!"; 

open (OFILE, ">KitCreate-backup.bat") or die "Can't open file for backup file: $! ";

# binmode  (FILE);
# binmode  (FILE_F0);
# binmode  (OFILE);

# ----------------------> Parsing map file <----------------------
my $rom_calib_pattern     = " .ROM.calib" ;
my $rom_eshock_calib_pattern = " .ROM.skyhook_calib" ;
while (my $line = <FILE>) { 
    
    @fields = split (/\s+/, $line);
    
    if ($line =~ $rom_calib_pattern){
        $rom_calib_address =   @fields[2];
        $rom_calib_size    =   @fields[3];
        print "Found $rom_calib_pattern,  $rom_calib_address , $rom_calib_size \n ";
#last;
    }
        if ($line =~ $rom_eshock_calib_pattern){
        $rom_calib_address2 =   @fields[2];
#$rom_calib_size    =   @fields[3];
        print "Found $rom_eshock_calib_pattern,  $rom_calib_address2 \n ";
        last;
    }
}

$rom_calib_size = hex($rom_calib_address2) - hex($rom_calib_address);
$rom_calib_size = $rom_calib_size + 16;
print "Calculated e-SHOCK rom offset in dec format: $rom_calib_size \n ";
$rom_calib_size = sprintf("%X", $rom_calib_size);
print "Calculated e-SHOCK rom offset in hex format: $rom_calib_size \n ";
# ----------------------> Parsing KitCreate file to modify<----------------------
my $stringTochange = $ARGV[4] . "=";
my $substitutionOk = 0;
while (my $line = <FILE_F0>) { 
    
    @fields = split (/\s+/, $line);
    
    if ($line =~ $stringTochange && !$substitutionOk){
        $old_rom_calib_size    =   @fields[2];
        print "Found old_rom_calib_size with value: $old_rom_calib_size \n ";

        # compose new string to write in modified file
        $firstWord = @fields[1];
        my $newString = sprintf("set %s 0x%s \n", $firstWord, $rom_calib_size);
        print OFILE $newString;
        $substitutionOk = 1;
    }
    else {
        print OFILE $line;
    }
}

# close files 
close (FILE);
close (FILE_F0);

print "$KitCreate was correctly modified \n";