/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  CMUCheckSM_MCU_r_xx_out.h
**  Created on      :  07-Feb-2022 15:39:00
**  Original author :  Mocci A
******************************************************************************/
/*****************************************************************************
**
**                        CMUCheckSM_MCU_r_xx Description
**
** Initial check and runtime checks for the CMU (Clock Monitoring Unit)
** based on the requirements document:
** "SPC58xNx_SM_Rev4-Analysis_ST_20201021_cetitec.xlsx"
**
** CMU_Check covers the requirements:
**            - SM_MCU_3_44
**            - SM_MCU_3_45
**            - SM_MCU_3_46
**            - SM_MCU_3_47
**            - SM_MCU_3_48
**            - SM_MCU_3_50
**
** The safety relevant CMUs are:
**
**           Clock module | Monitored clock
** ---------------------- | ---------------
** CMU_0_PLL0_XOSC_IRCOSC | (PLL0:PHI, XOSC)
** CMU_1_CORE_XBAR_BD     | (CHKR_CLK, CORE_CLK, XBAR_CLK,BD_CLK)
** CMU_2_HPBM             | (HPBM_CLK)
** CMU_3_PBRIDGE          | (PBRIDGE_CLK)
** CMU_6_SARADC           | (SARADC_CLK)
** CMU_11_FBRIDGE         | (FBRIDGE_CLK)
******************************************************************************/


#ifndef CMU_CHECK_SM_MCU_R_XX_OUT_H
#define CMU_CHECK_SM_MCU_R_XX_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RTWtypes.h"

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint32_T g_CMUFccuStatus;
extern uint32_T g_CMUProcessor;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_44
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CMUCheck_SM_MCU_3_44(void);

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_45
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CMUCheck_SM_MCU_3_45(void);

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_46
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CMUCheck_SM_MCU_3_46(void);

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_47
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CMUCheck_SM_MCU_3_47(void);

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_48
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void CMUCheck_SM_MCU_3_48(void);


#endif // CMU_CHECK_SM_MCU_R_XX_OUT_H

/****************************************************************************
 ****************************************************************************/

