/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      CombBal.h
 **  Date:          14-Sep-2021
 **
 **  Model Version: 1.1006
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_CombBal_h_
#define RTW_HEADER_CombBal_h_
#ifndef CombBal_COMMON_INCLUDES_
# define CombBal_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* CombBal_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void CombBal_initialize(void);

/* Exported entry point function */
extern void CombBal_EOA(void);

/* Exported entry point function */
extern void CombBal_NoSync(void);

/* Exported entry point function */
extern void CombBal_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T CylBalEn;               /* '<S3>/Merge' */

/* Cylinder in balance enable */
extern uint16_T InjCorrCyl;            /* '<S3>/Merge1' */

/* Cylinder FFS Correction */
extern uint16_T VtInjCorrCylBal[8];    /* '<S3>/Merge2' */

/* Cylinder balancing nominal correction */
extern int32_T VtPiFFSBal[8];          /* '<S3>/Merge4' */

/* PI output for nominal correction on cylinder balancing */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S11>/Data Type Duplicate' : Unused code path elimination
 * Block '<S10>/Data Type Duplicate' : Unused code path elimination
 * Block '<S15>/Data Type Duplicate' : Unused code path elimination
 * Block '<S13>/Data Type Duplicate' : Unused code path elimination
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Data Type Duplicate' : Unused code path elimination
 * Block '<S10>/Conversion' : Eliminate redundant data type conversion
 * Block '<S10>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S10>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S10>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CombBal'
 * '<S1>'   : 'CombBal/EOA'
 * '<S2>'   : 'CombBal/Reset'
 * '<S3>'   : 'CombBal/Subsystem'
 * '<S4>'   : 'CombBal/EOA/BankManagement'
 * '<S5>'   : 'CombBal/EOA/ErrorPolarization'
 * '<S6>'   : 'CombBal/EOA/PI_Regulator'
 * '<S7>'   : 'CombBal/EOA/PI_Regulator/EnablingCondition'
 * '<S8>'   : 'CombBal/EOA/PI_Regulator/Regulator'
 * '<S9>'   : 'CombBal/EOA/PI_Regulator/Saturation'
 * '<S10>'  : 'CombBal/EOA/PI_Regulator/EnablingCondition/LookUp_U16_U16'
 * '<S11>'  : 'CombBal/EOA/PI_Regulator/EnablingCondition/LookUp_U16_U16/Data Type Conversion Inherited3'
 * '<S12>'  : 'CombBal/EOA/PI_Regulator/Regulator/ErrorSignMgm'
 * '<S13>'  : 'CombBal/EOA/PI_Regulator/Regulator/LookUp_S16_U16'
 * '<S14>'  : 'CombBal/EOA/PI_Regulator/Regulator/RescalSignedIntRightShift'
 * '<S15>'  : 'CombBal/EOA/PI_Regulator/Regulator/LookUp_S16_U16/Data Type Conversion Inherited3'
 * '<S16>'  : 'CombBal/EOA/PI_Regulator/Saturation/RateLimiter_S32'
 * '<S17>'  : 'CombBal/EOA/PI_Regulator/Saturation/RateLimiter_S32/Data Type Conversion Inherited1'
 */

/*-
 * Requirements for '<Root>': CombBal
 */
#endif                                 /* RTW_HEADER_CombBal_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/