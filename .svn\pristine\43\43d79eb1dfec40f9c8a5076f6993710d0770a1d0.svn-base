/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           TLE9278BQX_Cfg.c
 **  File Creation Date: 21-Jun-2023
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TLE9278BQX_Cfg
 **  Model Description:
 **  Model Version:      1.334
 **  Model Author:       SalimbeniT - Tue Jan 20 16:01:59 2015
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: LanaL - Wed Jun 21 09:17:01 2023
 **
 **  Last Saved Modification:  LanaL - Tue Jun 20 08:19:05 2023
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "TLE9278BQX_Cfg_out.h"
#include "TLE9278BQX_Cfg_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_TLE9278BQX_CFG_DEF      1334U                     /* Referenced by: '<S5>/ID_VER_TLE9278BQX_Cfg_DEF' */

/* ID version */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_TLE9278BQX_CFG_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_EXPORT_CONST */
const uint16_T SBC_BYPASS_EE_WRITE[30] = { 3U, 11138U, 5763U, 158U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U } ;                       /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_BYPASS_WRITE[30] = { 2U, 11138U, 158U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U } ;                               /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_CLEAR_WDT_WRITE[30] = { 2U, 4739U, 3U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U } ;                               /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_INIT_READ[30] = { 24U, 192U, 193U, 194U, 67U, 196U, 198U, 72U,
  201U, 202U, 203U, 204U, 216U, 126U, 1U, 14U, 2U, 4U, 6U, 7U, 8U, 10U, 11U, 12U,
  30U, 0U, 0U, 0U, 0U, 0U } ;          /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_INIT_WRITE[30] = { 23U, 1153U, 42382U, 2946U, 388U, 390U,
  135U, 136U, 394U, 139U, 1164U, 158U, 195U, 1U, 14U, 2U, 4U, 6U, 7U, 8U, 10U,
  11U, 12U, 30U, 0U, 0U, 0U, 0U, 0U, 0U } ;/* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_NORMAL_READ[30] = { 23U, 1U, 2U, 4U, 6U, 7U, 8U, 10U, 11U,
  12U, 14U, 30U, 192U, 193U, 194U, 195U, 196U, 198U, 72U, 201U, 202U, 203U, 204U,
  216U, 0U, 0U, 0U, 0U, 0U, 0U } ;     /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_NORMAL_WRITE[30] = { 11U, 1153U, 42382U, 2946U, 388U, 390U,
  135U, 136U, 394U, 139U, 1164U, 158U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U } ;   /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_PRE_INIT_READ[30] = { 1U, 30U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
} ;                                    /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_REG[25] = { 1U, 2U, 3U, 4U, 6U, 7U, 8U, 10U, 11U, 12U, 14U,
  30U, 64U, 65U, 66U, 67U, 68U, 70U, 72U, 73U, 74U, 75U, 76U, 88U, 126U } ;/* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_REG_MASK[25] = { 768U, 26880U, 16128U, 768U, 28928U, 256U,
  768U, 56064U, 6912U, 0U, 61184U, 65280U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U } ;                   /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint8_T SBC_REG_SIZE = 25U;      /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_STOP_READ[30] = { 22U, 1U, 2U, 4U, 6U, 7U, 8U, 10U, 11U, 12U,
  14U, 192U, 193U, 194U, 195U, 196U, 198U, 72U, 201U, 202U, 203U, 204U, 216U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U } ;           /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_STOP_WRITE[30] = { 2U, 2946U, 2U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U
} ;                                    /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_TO_NORMAL_WRITE[30] = { 11U, 1153U, 126U, 388U, 394U, 139U,
  4739U, 1U, 4U, 10U, 11U, 3U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U } ;           /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_TO_RESET_WRITE[30] = { 3U, 158U, 50305U, 1U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U } ;                           /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_TO_SLEEP_WRITE[30] = { 7U, 158U, 132U, 394U, 139U, 195U,
  17537U, 1U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U } ;               /* Referenced by: '<S5>/Init_Cfg' */

/* data */
const uint16_T SBC_TO_STOP_WRITE[30] = { 9U, 1414U, 132U, 394U, 139U, 21123U, 4U,
  10U, 11U, 33921U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U } ;               /* Referenced by: '<S5>/Init_Cfg' */

/* data */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T KEYREQMSGOND = 0U;/* Referenced by: '<S4>/KEYREQMSGOND' */

/* Enable messages on demand */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint32_T KeyReqMsgOnD;                 /* '<S3>/Merge' */

/* Debug trigger messages start action */
uint16_T SBCDataRxBuffer[30];          /* '<S5>/ZERO2' */

/* SPI Rx data message */
uint16_T SBCGlobalStatusReg[25];       /* '<S5>/ZERO1' */

/* Status data message */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_TLE9278BQX_Cfg;/* '<S5>/ID_VER_TLE9278BQX_Cfg_DEF' */

/* ID Version */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_TLE9278BQX_Cfg_T TLE9278BQX_Cfg_DW;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<S2>/fc_Bkg'
 * Block description for: '<S2>/fc_Bkg'
 *   10ms Subsystem
 */
void TLE9278BQX_Cfg_fc_Bkg(void)
{
  /* Constant: '<S4>/KEYREQMSGOND' */
  KeyReqMsgOnD = KEYREQMSGOND;

  /* user code (Output function Trailer for TID1) */

  /* System '<S2>/fc_Bkg' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Start for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Cfg_fc_Init_Start(void)
{
  int32_T i;

  /* Start for Constant: '<S5>/ZERO2' */
  for (i = 0; i < 30; i++) {
    SBCDataRxBuffer[(i)] = MAX_uint16_T;
  }

  /* End of Start for Constant: '<S5>/ZERO2' */

  /* Start for Constant: '<S5>/ZERO1' */
  for (i = 0; i < 25; i++) {
    SBCGlobalStatusReg[(i)] = 127U;
  }

  /* End of Start for Constant: '<S5>/ZERO1' */

  /* Start for Constant: '<S5>/ID_VER_TLE9278BQX_Cfg_DEF' */
  IdVer_TLE9278BQX_Cfg = ID_VER_TLE9278BQX_CFG_DEF;
}

/*
 * Output and update for function-call system: '<S2>/fc_Init'
 * Block description for: '<S2>/fc_Init'
 *   Init Subsystem
 */
void TLE9278BQX_Cfg_fc_Init(void)
{
  int32_T i;

  {
    /* user code (Output function Header for TID2) */

    /* System '<S2>/fc_Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    TLE9278BQX_Cfg_initialize();

    /* Constant: '<S5>/ZERO2' */
    for (i = 0; i < 30; i++) {
      SBCDataRxBuffer[(i)] = MAX_uint16_T;
    }

    /* End of Constant: '<S5>/ZERO2' */

    /* Constant: '<S5>/ZERO1' */
    for (i = 0; i < 25; i++) {
      SBCGlobalStatusReg[(i)] = 127U;
    }

    /* End of Constant: '<S5>/ZERO1' */

    /* Constant: '<S5>/ID_VER_TLE9278BQX_Cfg_DEF' */
    IdVer_TLE9278BQX_Cfg = ID_VER_TLE9278BQX_CFG_DEF;

    /* Constant: '<S5>/ZERO' */
    KeyReqMsgOnD = 0U;

    /* user code (Output function Trailer for TID2) */

    /* System '<S2>/fc_Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/*
 * Output and update for function-call system: '<S2>/fc_Reset'
 * Block description for: '<S2>/fc_Reset'
 *   Reset Subsystem
 *   SBCGlobalStatusReg is flagged to view
 */
void TLE9278BQX_Cfg_fc_Reset(void)
{
  /* S-Function (Cfg_Return_Addr_U16): '<S6>/C//C++ Code Block' */
  Cfg_Return_Addr_U16_Outputs_wrapper((&(SBCGlobalStatusReg[0])),
    &TLE9278BQX_Cfg_DW.CCCodeBlock_b);

  /* S-Function (Cfg_SkipVal): '<S6>/C//C++ Code Block1' */
  Cfg_SkipVal_Outputs_wrapper((&(SBCIndexReset)),
    &TLE9278BQX_Cfg_DW.CCCodeBlock_b, &TLE9278BQX_Cfg_DW.SBCGlobalStatusReg_c[0]);

  /* user code (Output function Trailer for TID3) */

  /* System '<S2>/fc_Reset' */

  /* PILOTAGGIO USCITE - 10ms */
}

/*
 * Output and update for function-call system: '<S2>/fc_Set'
 * Block description for: '<S2>/fc_Set'
 *   Reset Subsystem
 *   SBCGlobalStatusReg is write
 */
void TLE9278BQX_Cfg_fc_Set(void)
{
  /* S-Function (Cfg_Return_Addr_U16): '<S7>/C//C++ Code Block' */
  Cfg_Return_Addr_U16_Outputs_wrapper((&(SBCGlobalStatusReg[0])),
    &TLE9278BQX_Cfg_DW.CCCodeBlock);

  /* S-Function (Cfg_UpdateVal): '<S7>/C//C++ Code Block1' */
  Cfg_UpdateVal_Outputs_wrapper((&(SBCIndexSet)), &TLE9278BQX_Cfg_DW.CCCodeBlock,
    (&(SBCValSet)), &TLE9278BQX_Cfg_DW.SBCGlobalStatusReg_m[0]);

  /* user code (Output function Trailer for TID4) */

  /* System '<S2>/fc_Set' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Model step function */
void TLE9278BQX_Cfg_Bkg(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' incorporates:
   *  SubSystem: '<S2>/fc_Bkg'
   *
   * Block description for '<S2>/fc_Bkg':
   *  10ms Subsystem
   */
  TLE9278BQX_Cfg_fc_Bkg();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' */
}

/* Model step function */
void TLE9278BQX_Cfg_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   *
   * Block description for '<S2>/fc_Init':
   *  Init Subsystem
   */
  TLE9278BQX_Cfg_fc_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void TLE9278BQX_Cfg_Reset(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_Reset' incorporates:
   *  SubSystem: '<S2>/fc_Reset'
   *
   * Block description for '<S2>/fc_Reset':
   *  Reset Subsystem
   *  SBCGlobalStatusReg is flagged to view
   */
  TLE9278BQX_Cfg_fc_Reset();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_Reset' */
}

/* Model step function */
void TLE9278BQX_Cfg_Set(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_Set' incorporates:
   *  SubSystem: '<S2>/fc_Set'
   *
   * Block description for '<S2>/fc_Set':
   *  Reset Subsystem
   *  SBCGlobalStatusReg is write
   */
  TLE9278BQX_Cfg_fc_Set();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_Set' */
}

/* Model initialize function */
void TLE9278BQX_Cfg_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   *
   * Block description for '<S2>/fc_Init':
   *  Init Subsystem
   */
  TLE9278BQX_Cfg_fc_Init_Start();

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_TLE9278BQX_CFG_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/