/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgm
**  Filename        :  CanMgm.c
**  Created on      :  30-nov-2020 09:37:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_CANMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "CanMgm.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/// Counter to delay CAN diagnosis after key on
uint16_T CntNoDiagAfterKeyOn = 1000u;
uint16_T VehSpeed = 0u;
uint8_T  IgnitionCutOffDx = 0u;
uint8_T  IgnitionCutOffSx = 0u;
uint8_T  ActivePhaseSearch = 0u;

uint8_T BrokenFuseSX = 0u;
uint8_T BrokenFuseDX = 0u;

/// Enable state  of CAN messages sending
uint8_T StCanSendEn = 0u;

/// Reset due to reflashing DTC have been cleared
uint8_T RstFlashed = 0u;

/* Stub */
uint8_T  ASRActive = 0u;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : CanMgm_RunCanDiag
**
**   Description:
**    Manages CAN errors counters
**    For each error type a single counter is used.
**    The counter for a specific error is incremented when the 'error' value is 
**    equal to the corresponding error.
**    The counter is reset to 0 when the 'error' value is not equal to the 
**    corresponding error.
**    All counters are saturated to avoid overflow.
**
**   Parameters :
**    int16_T error: error type
**    uint8_T *buffEmpty: pointer to buffer empty error counter
**    uint8_T *busOff: pointer to bus off error counter
**    uint8_T *buffOverRun: pointer to buffer overrun error counter
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_RunCanDiag(int16_T error, uint8_T *buffEmpty, uint8_T *busOff, uint8_T *buffOverRun)
{
    if(error == CAN_RX_BUFFER_EMPTY)
    {
        if(*buffEmpty < 0xFFu)
        {
            *buffEmpty = *buffEmpty + 1u;
        }
    }
    else if(error == CAN_BUSOFF)
    {
        if(*busOff < 0xFFu)
        {
            *busOff = *busOff + 1u;
        }
    }
    else if(error == CAN_RX_BUFFER_OVERRUN)
    {
        if(*buffOverRun < 0xFFu)
        {
            *buffOverRun = *buffOverRun + 1u;
        }
    }
    else
    {
        *buffEmpty = 0u;
        *busOff = 0u;
        *buffOverRun = 0u;
    }
}

/******************************************************************************
**   Function    : CanMgm_RunCanDiagAdv
**
**   Description:
**    Manages CAN errors counters
**    For each error type a single counter is used.
**    The counter for a specific error is incremented when the 'error' value is 
**    equal to the corresponding error.
**    The counter is reset to 0 when the 'error' value is not equal to the 
**    corresponding error.
**    All counters are saturated to avoid overflow.
**
**   Parameters :
**    int16_T error: error type
**    uint8_T *buffEmpty: pointer to buffer empty error counter
**    uint8_T *busOff: pointer to bus off error counter
**    uint8_T *buffOverRun: pointer to buffer overrun error counter
**    uint8_T *crcError: pointer to crc error counter
**    uint8_T *cntError: pointer to cnt error counter
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_RunCanDiagAdv(int16_T error, uint8_T *buffEmpty, uint8_T *busOff, uint8_T *buffOverRun, uint8_T * crcError, uint8_T * cntError)
{
    switch(error)
    {
        case CAN_RX_BUFFER_EMPTY:
            // Buffer empty error increase counter
            if(*buffEmpty < 0xFFu)
            {
                *buffEmpty = *buffEmpty + 1u;
            }
            // Counters to be reset in this case
            // Bus OFF error increase counter
            *busOff = 0u;
            
            // Counters to be decreased
            // Buffer Over Run
            if(*buffOverRun> 0u)
            {
                *buffOverRun = *buffOverRun - 1u;
            }
            // Cnt Error
            if(*cntError> 0u)
            {
                *cntError = *cntError - 1u;
            }
            // CRC Error
            if(*crcError> 0u)
            {
                *crcError = *crcError - 1u;
            }
        break;

        case CAN_BUSOFF:
            // Bus OFF error increase counter
            if(*busOff < 0xFFu)
            {
                *busOff = *busOff + 1u;
            }
            // Counters to be reset in this case
            *buffEmpty = 0u;
            *buffOverRun = 0u;
            *crcError = 0u;
            *cntError = 0u;
        break;

        case CAN_RX_BUFFER_OVERRUN:
            // Buffer empty error increase counter
            if(*buffOverRun< 0xFFu)
            {
                *buffOverRun = *buffOverRun + 1u;
            }
            // Counters to be reset in this case
            // Bus OFF error increase counter
            *busOff = 0u;

            // Counters to be decreased
            // Buffer Empty
            if(*buffEmpty> 0u)
            {
                *buffEmpty= *buffEmpty - 1u;
            }
            // Cnt Error
            if(*cntError> 0u)
            {
                *cntError = *cntError - 1u;
            }
            // CRC Error
            if(*crcError> 0u)
            {
                *crcError = *crcError - 1u;
            }
        break;

        case CAN_RX_CRC_ERR:
            // Crc error increase counter
            if(*crcError< 0xFFu)
            {
                *crcError = *crcError+ 1u;
            }
            // Counters to be reset in this case
            // Bus OFF error increase counter
            *busOff = 0u;

            // Counters to be decreased
            // Buffer Empty
            if(*buffEmpty> 0u)
            {
                *buffEmpty= *buffEmpty - 1u;
            }
            // Cnt Error
            if(*cntError> 0u)
            {
                *cntError = *cntError - 1u;
            }
            // Buffer Over Run
            if(*buffOverRun> 0u)
            {
                *buffOverRun = *buffOverRun - 1u;
            }
        break;

        case CAN_RX_CNT_ERR:
            // Cnt error increase counter
            if(*cntError < 0xFFu)
            {
                *cntError = *cntError+ 1u;
            }
            // Counters to be reset in this case
            // Bus OFF error increase counter
            *busOff = 0u;

            // Counters to be decreased
            // Buffer Empty
            if(*buffEmpty> 0u)
            {
                *buffEmpty= *buffEmpty - 1u;
            }
            // Crc Error
            if(*crcError> 0u)
            {
                *crcError = *crcError - 1u;
            }
            // Buffer Over Run
            if(*buffOverRun> 0u)
            {
                *buffOverRun = *buffOverRun - 1u;
            }
        break;

        default:
            // Counters to be reset in this case
            // Bus OFF error increase counter
            *busOff = 0u;
            
            // Counters to be decreased
            // Buffer Empty
            if(*buffEmpty> 0u)
            {
                *buffEmpty= *buffEmpty - 1u;
            }
            // Cnt Error
            if(*cntError> 0u)
            {
                *cntError = *cntError - 1u;
            }
            // Crc Error
            if(*crcError> 0u)
            {
                *crcError = *crcError - 1u;
            }
            // Buffer Over Run
            if(*buffOverRun> 0u)
            {
                *buffOverRun = *buffOverRun - 1u;
            }
        break;
    }
}

/******************************************************************************
**   Function    : CANMGM_Var_Diag
**
**   Description:
**    Manages CAN Valid data
**    Manages signal Diagnosis, Recovery value 
**
**   Parameters :
**    int32_T rawvar: raw input value
**    int32_T oldvar: old output value
**    uint8_T useCanValAsRec: Flag signaling if the recovery value should be the input value
**    int32_T recvar: calibration recovery value
**    int32_T *outvar: output value pointer
**    uint8_T valid: valid data signal
**    uint8_T diagline: diagnostic line number
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CANMGM_Var_Diag(int32_T rawvar, int32_T oldvar, uint8_T useCanValAsRec, int32_T recvar, int32_T *outvar, uint8_T valid, uint8_T diagline)
{
    typPtFault local_fault;
    uint8_T local_diag;

    /* Valid data verification */
    if (valid == 0u)
    {
        if (FlgBankSel == BANKDX)
        {
            local_fault = INVALID_SERIAL_DATA;
        }
        else
        {
            local_fault = INVALID_SERIAL_DATA;
        }
        /* Keep old value */
        *outvar = oldvar;
    }
    else
    {
        local_fault = NO_PT_FAULT;
        /* Update out value */
        *outvar = rawvar;
    }

    /* DIAGNOSIS */
#ifdef _BUILD_DIAGMGM_
    DiagMgm_SetDiagState(diagline, local_fault, &local_diag);
#endif
    if(StDiag[diagline] == FAULT)
    {
        /* Overwrite out value */
        if(useCanValAsRec == TRUE)
        {
            *outvar = rawvar;
        }
        else
        {
            *outvar = recvar;
        }
    }
    else
    {
        /* DO NOTHING */
    }
}

/******************************************************************************
**   Function    : CANMGM_VarDxSx_Diag
**
**   Description:
**    Manages CAN Valid data and delta beween bank signals
**    Manages signal Diagnosis, Recovery value 
**
**   Parameters :
**    int32_T rawvardx: raw input value for dx bank
**    int32_T rawvarsx: raw input value for sx bank
**    int32_T oldvar: old output value
**    uint8_T useCanValAsRec: Flag signaling if the recovery value should be the input value
**    int32_T recvar: calibration recovery value
**    int32_T *outvar: output value pointer
**    uint8_T validdx: valid data signal for dx bank
**    uint8_T validsx: valid data signal for sx bank
**    int32_T deltavar: Max delta between rawdx/sx values
**    uint8_T diagline: diagnostic line number
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CANMGM_VarDxSx_Diag(int32_T rawvardx, int32_T rawvarsx, int32_T oldvar, uint8_T useCanValAsRec, int32_T recvar, int32_T *outvar, uint8_T validdx, uint8_T validsx, int32_T deltavar, uint8_T diagline)
{
    typPtFault local_fault;
    uint8_T local_diag;
    int32_T raw_delta;
    uint8_T flg_apply_rec;

    flg_apply_rec = 0u;
    
    /* Valid data verification */
    if((validdx == 0u) && (validsx != 0u))
    {
        /* Fault value DX */
        local_fault = INVALID_SERIAL_DATA;
        *outvar = rawvarsx;
    }
    else if((validdx != 0u) && (validsx == 0u))
    {
        /* Fault value SX */
        local_fault = INVALID_SERIAL_DATA;
        *outvar = rawvardx;
    }
    else if((validdx == 0u) && (validsx == 0u))
    {
        /* Fault both values DX/SX */
        local_fault = INVALID_SERIAL_DATA;
        *outvar = oldvar;
        flg_apply_rec = 1u;
    }
    else
    {
        /* Valid data OK                 */
        /* Functional value verification */
        raw_delta = rawvardx - rawvarsx;
        raw_delta = abs(raw_delta);

        if(raw_delta > deltavar)
        {
            /* Values too different */
            local_fault = SIGNAL_COMPARE_FAILURE;
            *outvar = (rawvardx + rawvarsx)/2;
        }
        else
        {
            /* OK */
            local_fault = NO_PT_FAULT;
            /* Update out value */
            *outvar = (rawvardx + rawvarsx)/2;
        }
    }

    /* DIAGNOSIS */
#ifdef _BUILD_DIAGMGM_
    DiagMgm_SetDiagState(diagline, local_fault, &local_diag);
#endif

    if((StDiag[diagline] == FAULT) && (flg_apply_rec == 1u))
    {
        /* Overwrite out value */
        if(useCanValAsRec == TRUE)
        {
            *outvar = (rawvardx + rawvarsx)/2;
        }
        else
        {
            *outvar = recvar;
        }
    }
    else
    {
        /* DO NOTHING */
    }
}

/******************************************************************************
**   Function    : CANMGM_RpmCANBlocked
**
**   Description:
**    Manages blocked Rpm signal received on CAN
**
**   Parameters :
**    uint16_T rpm: raw input rpm signal
**    uint16_T rpmCAN: raw input rpm signal from CAN
**    uint16_T rpmCANold_in: old rpmCAN value
**    uint16_T *rpmCANold_out: rpmOldCAN output pointer
**    uint8_T cnt_in: input value counter 
**    uint8_T *cnt_out: output counter value pointer
**    uint8_T thrcnt: Blocked Rpm counter threshold
**    uint8_T *flagblocked: output flag for blocked Rpm
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CANMGM_RpmCANBlocked(uint16_T rpm, uint16_T rpmCAN, uint16_T rpmCANold_in, uint16_T *rpmCANold_out, uint8_T cnt_in, uint8_T *cnt_out, uint8_T thrcnt, uint8_T *flagblocked)
{
    if ((rpmCAN == rpmCANold_in) && (rpm > 0u))
    {
        *cnt_out = cnt_in + 1u;
        if (*cnt_out >= thrcnt)
        {
            *flagblocked= 1u;
            *cnt_out = thrcnt;
        }
        else
        {
            /* DO NOTHING */
        }
    }
    else
    {
        if (cnt_in > 0u)
        {
            *cnt_out = cnt_in - 1u;
            if (*cnt_out == 0u)
            {
                *flagblocked = 0u;
                *cnt_out = 0u;
            }
            else
            {
                /* DO NOTHING */
            }
        }
    }
    *rpmCANold_out = rpmCAN;
}

/******************************************************************************
**   Function    : CanMgm_CntNoDiag
**
**   Description:
**    Decrement or set No diagnosis counter used after keyOn
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CntNoDiag(void)
{
    if (StIgn != IGN_ON)
    {
        CntNoDiagAfterKeyOn = TNOCANDIAGAFTKEYON;
    }
    else if(CntNoDiagAfterKeyOn > 0u)
    {
        CntNoDiagAfterKeyOn--;
    }
    else
    {
        /* DO NOTHING */
    }
}

/******************************************************************************
**   Function    : CanMgm_CalcCvn
**
**   Description:
**    Calculates CVN from crc16 signal 
**
**   Parameters :
**    uint32_T *cvnstr: pointer to output CVN string
**    uint16_T crc16: crc16 input value
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CalcCvn(uint32_T *cvnstr, uint16_T crc16)
{
    *cvnstr = (uint32_T)crc16;
}

/******************************************************************************
**   Function    : CanMgm_DebounceTDC
**
**   Description:
**    Debounce Ingnition cutoff signal received from CAN
**
**   Parameters :
**    uint8_T ionAbsTdc
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_DebounceTDC(uint8_T ionAbsTdc)
{
static uint8_T debOldIgnitionCutOffDx = 0u;
static uint8_T debCntIgnitionCutOffDxCAN = 0u;
static uint8_T debOldIgnitionCutOffSx = 0u;
static uint8_T debCntIgnitionCutOffSxCAN = 0u;
static uint8_T debOldActivePhaseSearch = 0u;
static uint8_T debCntActivePhaseSearch = 0u;

    if((ionAbsTdc & 0x01u) == 0u)
    {
        DigDebounceTwoWay(&IgnitionCutOffDx, &debOldIgnitionCutOffDx, IgnitionCutOffDxCAN, &debCntIgnitionCutOffDxCAN, THDEBIGNITIONCUTOFF, 1u);
    }
    else
    {
        DigDebounceTwoWay(&IgnitionCutOffSx, &debOldIgnitionCutOffSx, IgnitionCutOffSxCAN, &debCntIgnitionCutOffSxCAN, THDEBIGNITIONCUTOFF, 1u);
    }
    
    DigDebounceTwoWay(&ActivePhaseSearch, &debOldActivePhaseSearch, ActivePhaseSearchCAN, &debCntActivePhaseSearch, THDEBACTIVEPHASESEARCH, 1u);
}


/******************************************************************************
**   Function    : CanMgm_CalcBrokenFuse
**
**   Description:
**    Calculates Broken fuse flags
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CalcBrokenFuse(void)
{
    BrokenFuseSX = (uint8_T)(StDiag[DIAG_BUCK_B] == FAULT);

    BrokenFuseDX = (uint8_T)(StDiag[DIAG_BUCK_A] == FAULT);
}

/******************************************************************************
**   Function    : CanMgm_CanState
**
**   Description:
**    Calculates StCanSendEn
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CanState(void)
{
static uint16_T cntWaitState = 0u;

    switch(StCanSendEn)
    {
        case CANSEND_STOP:
#ifdef _BUILD_DIAGMGM_
            if ((StEcu == ECU_ON) && (VtRec[REC_CAN_LIVENESS_OFF] == 0u))
            {
                if(cntWaitState >= CANSENDWAITSTART)
                {
                    StCanSendEn = CANSEND_START;
                    cntWaitState = 0u;
                }
                cntWaitState++;
            }
            else
            {
                cntWaitState = 0u;
            }
#else
            if(cntWaitState >= CANSENDWAITSTART)
            {
                StCanSendEn = CANSEND_START;
                cntWaitState = 0u;
            }
            cntWaitState++;
#endif
        break;

        case CANSEND_WAITSTOP:
#ifdef _BUILD_DIAGMGM_
            if(VtRec[REC_CAN_LIVENESS_OFF] == 1u)
            {
                StCanSendEn = CANSEND_STOP;
            }
            else if ((StEcu != ECU_ON) || (VtRec[REC_CAN_LIVENESS_OFF] == 1u))
            {
                if(cntWaitState >= CANSENDWAITSTOP)
                {
                    StCanSendEn = CANSEND_STOP;
                    cntWaitState = 0u;
                }
                cntWaitState++;
            }
            else
#endif
            {
                StCanSendEn = CANSEND_START;
                cntWaitState = 0u;
            }
        break;
        
        case CANSEND_START:
#ifdef _BUILD_DIAGMGM_
            if(VtRec[REC_CAN_LIVENESS_OFF] == 1u)
            {
                StCanSendEn = CANSEND_STOP;
            }
            else if (StEcu != ECU_ON)
            {
                StCanSendEn = CANSEND_WAITSTOP;
                if(cntWaitState >= CANSENDWAITSTOP)
                {
                    StCanSendEn = CANSEND_STOP;
                    cntWaitState = 0u;
                }
            }
#endif
        break;
        
        default:
            StCanSendEn = CANSEND_STOP;
        break;

    }
}

/******************************************************************************
**   Function    : CanMgm_SetClearRstFlashed
**
**   Description:
**    Calculates RstFlashed
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_SetClearRstFlashed(uint8_T value)
{
    RstFlashed = value;
}
/***************************************************************************/
//   Function    :   CalcCvn
//
//   Description:    
/*! \brief Calculate CVN
*/
//
//  Parameters and Returns:
/*! 
\param void
\returns int16_T error code
*/
//  Notes:        
/*!
This function performs the following macro operations:
- Cleans the message data structure before filling in the requested bits
- Packs the variables to be sent into the message structure
- Invokes the BIOS function to send the message and record the error code
*/
/**************************************************************************/
void CalcCvn(uint32_T *cvnstr, uint16_T crc16)
{
    *cvnstr = (uint32_T)crc16;
}

/*==================================================================================================
                                       PRIVATE FUNCTIONS
==================================================================================================*/

#else

uint8_T BrokenFuseSX = 0u;
uint8_T BrokenFuseDX = 0u;

#endif // _BUILD_CANMGM_


