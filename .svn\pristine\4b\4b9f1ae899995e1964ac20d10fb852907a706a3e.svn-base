#************************************************************************
#* FILE NAME: mpc5500_usrdefs.inc            COPYRIGHT (c) Freescale 2004 
#*                                                All Rights Reserved     
#* DESCRIPTION:                                                           
#* This file contains user definitions for the MPC5500 assembly functions.
#* The user will only need to make changes to this file for the assembly
#*  portion of this code.
#* 
#*========================================================================
#* ORIGINAL AUTHOR: G. Jackson           
#* REV      AUTHOR        DATE       DESCRIPTION OF CHANGE                
#* ---   -----------   -----------   ---------------------                  
#* 0.1   <PERSON><PERSON>     12/Apr/04    Initial version        
#* 0.2   G. Jackson     15/Apr/04    Added compiler designations
#* 0.3   G. Jackson     13/May/04    Added runtime variables
#* 0.4   G. <PERSON>     06/Jun/04    Added EXT_BOOT config option
#* 0.5   G. Jackson     30/Jun/04    Added RCHW variables
#* 1.0   G. Jackson     07/Oct/04    Internal and external RAM set to 
#*                                    CACHE_INHIBIT (TLBs 3 & 11)
#************************************************************************
    .include "../tree/COMMON/CONFIG/ASM/mpc5500_defs.inc"
    .include "asm_ghs_macros.inc"

#*************** Runtime Variables *****************
#  These runtime variables are used in __start.s
# main(), assembly cfg_* calls, and cfg_mpc5500_ccd() need to  **
# have far absolute addressing if flashing to ROM with         **
# distant addresses on the MPC5500.                            **
# The .equ statements below should be set to "1" to be valid   **
#  and set to "0" to be inactive.                              **


  	.equ SRAM_TEST,        1  # Used to enable SRAM test
  	.equ VSRAM_EXIST,      1  # Used to manage the VSRAM memory ram if configured

    .equ FAR_ADDRESS,      0  # Used for a FAR_ADDRESS call
    .equ FAR_ADDRESS_MAIN, 1  # Used for a FAR_ADDRESS call to main
    .equ SIM_VER,          0  # Used with the Code Warrior simulator
    .equ VLE_ENABLE,       1  # Enable the VLE instruction set (Pictus has only VLE mode)
    
    .equ FLSH_RUN,         1  # Set to (1) for code in Flash
#                             #     to (0) for code in SRAM

    .equ EXT_BOOT,         0  # Set to (1) for External boot. 
#                             #  BAM sets up external bus and CS[0]
# Reset Configuration Half Word Variables **
    .equ RCHW_WTE, WDOG_DISABLE # Watchdog control at reset
    .equ RCHW_PS0, CS0_32BIT    # CS0 data port size at reset
    .equ BOOT_ID,  MPC5500_ID   # Valid boot ID for MPC5500 devices

#*************************************************************
#******** Special Initialization Option Constants  ***********
# The "I_" prefixed variables are initialization defines      
#  Set the value to one ("1") to enable the option.
#  Or, set the value to zero ("0") to disable the option.

    .equ I_LOCEN,    1 # Set loss of clock enable function
    .equ I_BCKUPCLK, 1 # Enable backup clock on loss of clock

# Mutually exclusive pair (one set to "0", one set to "1"):
    .equ I_LOSSCRST, 0 # Enable reset on loss of clock 
    .equ I_LOCINT,   1 # Enable interrupt on loss of clock
# Mutually exclusive pair (one set to "0", one set to "1"):
    .equ I_LOSSLRST, 0 # Enable reset on loss of lock  
    .equ I_LOLINT,   1 # Enable interrupt on loss of lock 


# To match MMU entry size:
    .equ I_SRAM_SIZE,   SIZE_64K   #   64 KB RAM Size
    .equ I_XSRAM_SIZE,  SIZE_512K  #  512 KB External RAM Size
    .equ I_XSRAM_SPACE, SIZE_4M    #    4 MB External RAM Space

#*************************************************************
#      User Defined Options
#  These values should be modified based on user requirements
#*************************************************************
# Cache definitions used by cfg_CACHE and cfg_STACK:
#  Copy back mode (CWM=1) and Push buffer disabled (DPB=1) is
#   required by errata #32 and #34 to allow MMU control of cache.
#   These errata may go away in the future (see current errata)
    #.equ CACHE_CLEAR,(CLFC_NO_OP | CINV_INV_OP | CE_DISABLE) ANDORRA
    #.equ CACHE_SETTINGS, (CHECKERR_ENABLE | CHECKERR_EDC | CHECKERR_AUTCOR | CORG_32S_4W | CE_ENABLE)  ANDORRA
# K2
    .equ CACHE_CLEAR,    (ICLOINV_INV_OP | ICINV_INV_OP | ICE_DISABLE)
    .equ CACHE_SETTINGS, (ICHECKERR_ENABLE | ICHECKERR_AUTCOR | ICE_ENABLE)   
#*******************************************************************
# Flash definitions used by cfg_FLASH:
#     Internal Flash: FLASH_BIUCR (0xC3F8_801C)    
# ap: mod
#    .equ FLASH_SETTINGS, (EBI_PREFTCH_ON | APC_3 | WWSC_1 | RWSC_2 | IPFEN_ANY | PFLIM_2 | BFEN_EN)
    .equ FLASH_SETTINGS, (EBI_PREFTCH_ON | APC_2 | WWSC_1 | RWSC_2 | IPFEN_ANY | PFLIM_2 | BFEN_EN)
#     External Flash: CS0 OR settings used by cfg_FLASH:
#     The next line is commented out as an example of optimizing
#      external Flash boot times.
#    .equ CS0_OR_OPTIONS, (AMASK_8M | OR0SCY_2 | OR0BSCY_0)
    
#*******************************************************************
# FMPLL definitions used by cfg_FMPLL
#  Set the internal clock to 32 MHz with MFD=16, and RFD=4.
#  Setting 1 is intended to only change the MFD bit with no change to the RFD bit.
    .equ FSYS_60,         0
    .equ FSYS_80,         1
    .equ FSYS_128,        0
    
    #.if FSYS_128
    #.equ FMPLL_SYNCR_SETTING1, (MFD_16 | RFD_4 | LOCEN_EN)  # MFD=16, RFD=4 for  32MHz  #OK???
    #.endif

    .if FSYS_80
    .equ FMPLL_SYNCR_SETTING1, (MFD_8 | PREDIV_3 | RFD_1 | LOCEN_EN)  # for 32 MHz   #OK!!!
    .endif
    
    .if FSYS_60
    .equ FMPLL_SYNCR_SETTING1, (MFD_10 | RFD_4 | LOCEN_EN)  # MFD=10, RFD=4 for 30 MHz #OK!!!
    .endif

#  removed by Akhela Start
#  Set the internal clock to 128 MHz with MFD=16, and RFD=1.
#  This sequence sets the RFD to divide-by-1 in the FMPLL_SYNCR Register.
#  Setting 2 is intended to only change the RFD bit with no change to the MFD bit.
#    .equ FMPLL_SYNCR_SETTING2, (MFD_16 | RFD_1 | LOCEN_EN)  # MFD=16, RFD=1 for 128MHz
#  removed By Akhela End

#  Set the internal clock to 80 MHz with MFD=10, and RFD=1.
#  This sequence sets the RFD to divide-by-1 in the FMPLL_SYNCR Register.
#           Fref * (MFD + 4)
#  Fsys = ---------------------
#         (PREDIV + 1)* 2^(RFD)

    #.if FSYS_128
    #.equ FMPLL_SYNCR_SETTING2, (MFD_16 | RFD_1 | LOCEN_EN)  # MFD=16, RFD=1 for 128MHz   #OK???
    #.endif

    .if FSYS_80
    .equ FMPLL_SYNCR_SETTING2, (MFD_20 | PREDIV_3 | RFD_1 | LOCEN_EN)  # MFD_20=16, PREDIV_3=2, RFD_1=0 for 80 MHz #OK!!!
    .endif

    .if FSYS_60
    .equ FMPLL_SYNCR_SETTING2, (MFD_10 | RFD_2 | LOCEN_EN)  # MFD_10=6, RFD_2=1 for 60 MHz  #OK!!!
    .endif
    
#*******************************************************************
# SIU definitions used by cfg_FMPLL
# The SIU definition below will generate a reset of the device when used.
#  A system reset or an external reset will result depending on settings.
    .equ SIU_SRCR_SYSRST, (SSR_SYSRST | SER_NORST | CRE_NO)    

#*******************************************************************
# ERRLOGREG (Error Log Register) address definition
    .equ ERRLOGREG, ERRLOGAD_ETPUPRAM_HI # Assembler token address
    
#*********************************************************************





