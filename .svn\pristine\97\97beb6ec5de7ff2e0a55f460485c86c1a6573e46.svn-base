/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB6C/Appl/branches/EISB6C_AP_02_FLASH/tree/BIOS/FLA#$  */
/* $Revision:: 119904                                                                                         $  */
/* $Date:: 2020-07-17 16:20:15 +0200 (ven, 17 lug 2020)                                                       $  */
/* $Author:: CarboniM                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  flashAsynchApi_Cbk.c
**  Created on      :  04-mar-2021 11:00:00
**  Original author :  CarboniM
**  Integration history: 
**  
**          
*******************************************************************************************************************/

#ifdef  _BUILD_FLASH_

#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1u)
#pragma ghs section text=FLASH_TEXTRAM_SECTION
#else
#error Flash driver: text RAM section undefined
#endif

#pragma ghs startnomisra
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Flash.h"
#include "sys.h"
#include "Dspi_out.h"

#ifdef FLASHERASECBK
uint32_T FlashEraseCallback_cnt = 0u;
uint8_T FlagDspiBusy = 0u;
#endif
#ifdef FLASHPRGCBK
uint32_T FlashProgramCallback_cnt500ms = 0u;
#endif
#ifdef FLASHPRGVERCBK
uint32_T FlashProgramVerifyCallback_cnt500ms = 0u;
#endif


#ifdef FLASHERASECBK
void FlashEraseCallback(uint8_T callback_module){

uint32_T address_pointer;
SpiError_T ret_SpiTransmit;
uint16_T WdtTxBuffer = 38531u;  // WDT: period = 1sec, mode = TimeTriggered and parity bit

    //WDT Configuration
    if(FlashEraseCallback_cnt == 0u)
    {
        if (SPI_GetChannelStatus(SPI_CH_E) == SPI_STOPPED)
        {
            ret_SpiTransmit =  SPI_Transmit(SPI_CH_E, PCS_0, &WdtTxBuffer, 1u);
        }
        else
        {
            FlagDspiBusy = 1u;
        }

    }
  
    if ((FlagDspiBusy == 1) && (FlashEraseCallback_cnt == 4000u ))  //5ms
    {
        ret_SpiTransmit =  SPI_Transmit(SPI_CH_E, PCS_0, &WdtTxBuffer, 1u);
        FlagDspiBusy = 0u;
    }

    FlashEraseCallback_cnt++;

    if (FlashEraseCallback_cnt == 240000u) //240000 => circa 300ms
    {
        FlashEraseCallback_cnt = 0u;
    }


    switch (callback_module){
#ifdef FLASH_ERASE_DIAG_CBK        
        case FLASH_ERASE_DIAG_CBK:        
        break;
#endif
        default:
        break;

    }
}
#endif

#ifdef FLASHPRGCBK
void FlashProgramCallback(void){

    //Add your code here
}
#endif

#ifdef FLASHPRGVERCBK
void FlashProgramVerifyCallback(void){

    //Add your code here
}
#endif

#ifdef FLASHBLKCHKCBK
void FlashBlankCheckCallback(void){

    //Add your code here
}
#endif

#ifdef FLASHCHKSUMCBK
void FlashChecksumCallback(void){

    //Add your code here
}
#endif


#endif /*  _BUILD_FLASH_ */

