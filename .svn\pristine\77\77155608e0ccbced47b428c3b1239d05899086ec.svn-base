/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonMisf.c
 **  File Creation Date: 07-Nov-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonMisf
 **  Model Description:  The target of this module is to detect the misfire by Ion strategy
 **  Model Version:      1.1137
 **  Model Author:       Raffaele Marotta - Thu Jul 01 14:50:43 2004
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: MarottaR - Mon Nov 07 16:31:31 2022
 **
 **  Last Saved Modification:  MarottaR - Mon Nov 07 16:18:44 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonMisf_out.h"
#include "IonMisf_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/IonMisf_sched' */
#define IonMisf_event_IonMisf_EOA      (3)
#define IonMisf_event_IonMisf_NoSync   (2)
#define IonMisf_event_IonMisf_PowerOn  (0)
#define IonMisf_event_IonMisf_TDC      (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define FLGMISFSET_ONE                 1U                        /* Referenced by: '<S4>/MisfireTDC_Detection' */

/* Value 1 to set bit of FlgMisfDetected */
#define ID_VER_IONMISF_DEF             11137U                    /* Referenced by: '<Root>/IonMisf_sched' */

/* ID model version define */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONMISF_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENIONMISF = 1;/* Referenced by: '<Root>/ENIONMISF' */

/* Enable IonMisf (=1) */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENKCOHINCMISF1 = 1;
                                      /* Referenced by: '<S1>/ENKCOHINCMISF1' */

/* Enable level 1 action in case of knocking coherence */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENKCOHINCMISF2 = 1;
                                      /* Referenced by: '<S1>/ENKCOHINCMISF2' */

/* Enable level 2 action in case of knocking coherence */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENKCOHINCMISF3 = 1;
                                      /* Referenced by: '<S1>/ENKCOHINCMISF3' */

/* Enable level 3 action in case of knocking coherence */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENSTMISFTHINT = 0;/* Referenced by: '<S1>/ENSTMISFTHINT' */

/* StMisf detection signal selector: 0 -> IntIon, 1 -> ThInt */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T MISFRESET = 0;/* Referenced by:
                                                          * '<S1>/MISFRESET'
                                                          * '<S4>/MISFRESET'
                                                          */

/* Misfire counters reset flag */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T FlgMisfDetected;               /* '<S3>/Merge1' */

/* Misfire bit mask */
enum_StMisf StMisf[8];                 /* '<S3>/Merge' */

/* Cylinder Misfire Status Vector */
enum_StMisf StMisfEOA[8];              /* '<S3>/Merge2' */

/* Cylinder Misfire Status Vector calculated at EOA event */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T CntBadComb; /* '<S1>/MisfireEOA_Detection' */

/* Bad combustion overall counter */
STATIC_TEST_POINT uint16_T CntMisfire; /* '<S4>/MisfireTDC_Detection' */

/* Misfire overall counter */
STATIC_TEST_POINT uint16_T CntNoComb;  /* '<S1>/MisfireEOA_Detection' */

/* No combustion overall counter */
STATIC_TEST_POINT uint16_T CntParComb; /* '<S1>/MisfireEOA_Detection' */

/* Partial combustion overall counter */
STATIC_TEST_POINT uint32_T IdVer_IonMisf;/* '<Root>/IonMisf_sched' */

/* ID model version */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_IonMisf_T IonMisf_DW;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void IonMisf_chartstep_c2_IonMisf(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<Root>/TDC_fcn'
 * Block description for: '<Root>/TDC_fcn'
 *   Function to detect misfire on MisfAbsTdc cylinder.
 *   The result of this analysis is stored in the status array StMisf[MisfAbsTdc].
 */
void IonMisf_TDC_fcn(void)
{
  uint8_T flgmisf_mask;
  enum_StMisf rtb_IndexVector1;

  /* MultiPortSwitch: '<S4>/Index Vector1' incorporates:
   *  Inport: '<Root>/MisfAbsTdc'
   *  SignalConversion generated from: '<S4>/StMisfEOA_old'
   */
  rtb_IndexVector1 = StMisfEOA[(MisfAbsTdc)];

  /* Chart: '<S4>/MisfireTDC_Detection' incorporates:
   *  Constant: '<S4>/MISFRESET'
   *  Inport: '<Root>/MisfAbsTdc'
   *  Inport: '<Root>/PtFaultTrigger'
   *  Inport: '<Root>/VtRec'
   *
   * Block requirements for '<S4>/MISFRESET':
   *  1. EISB_FCA6CYL_SW_REQ_1763: The software shall reset the counters CntMisFire, CntNoComb, CntPa... (ECU_SW_Requirements#5740)
   */
  /* Gateway: TDC_fcn/MisfireTDC_Detection */
  /* During: TDC_fcn/MisfireTDC_Detection */
  /* This stateflow calculates the StMisf status for each cylinder considering the result of the Ion signal, if it's plausible and no errors, recoveries or functionalities (like multispark), which can effect the misfire detection, are active. */
  /* Entry Internal: TDC_fcn/MisfireTDC_Detection */
  /* Transition: '<S6>:4' */
  flgmisf_mask = (uint8_T)(((uint8_T)FLGMISFSET_ONE) << ((uint32_T)MisfAbsTdc));
  if ((((uint32_T)PtFaultTrigger) != NO_PT_FAULT) && (((int32_T)VtRec
        [(MisfAbsTdc & ((uint8_T)FLGMISFSET_ONE)) + REC_NO_MISSPARK_A]) == 0)) {
    /* Assignment: '<S4>/Assignment2' */
    /* Transition: '<S6>:11'
     * Requirements for Transition: '<S6>:11':
     *  1. EISB_FCA6CYL_SW_REQ_1448: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#3213)
     *  2. EISB_FCA6CYL_SW_REQ_1760: The software shall set the bit related to the cylinder X of the bi... (ECU_SW_Requirements#5737)
     */
    /* Punctual error on coil bank, but recovery not still active */
    StMisf[(MisfAbsTdc)] = MIS_FIRE;
    CntMisfire = (uint16_T)((int32_T)(((int32_T)CntMisfire) + 1));
    IonMisf_DW.FlgMisfDetected_f |= flgmisf_mask;

    /* Transition: '<S6>:702' */
    /* Transition: '<S6>:773' */
  } else {
    /* Transition: '<S6>:15'
     * Requirements for Transition: '<S6>:15':
     *  1. EISB_FCA6CYL_SW_REQ_1760: The software shall set the bit related to the cylinder X of the bi... (ECU_SW_Requirements#5737)
     */
    IonMisf_DW.FlgMisfDetected_f = (uint8_T)(IonMisf_DW.FlgMisfDetected_f &
      (~flgmisf_mask));
    if (((uint32_T)rtb_IndexVector1) == EOA_NOT_EXE) {
      /* Assignment: '<S4>/Assignment2' */
      /* Transition: '<S6>:779' */
      StMisf[(MisfAbsTdc)] = NO_MISF;
    } else {
      /* Assignment: '<S4>/Assignment2' */
      /* Transition: '<S6>:774' */
      StMisf[(MisfAbsTdc)] = rtb_IndexVector1;
    }
  }

  if (MISFRESET) {
    /* Transition: '<S6>:770' */
    CntMisfire = 0U;
  } else {
    /* Transition: '<S6>:769' */
  }

  /* Assignment: '<S4>/Assignment1' incorporates:
   *  Chart: '<S4>/MisfireTDC_Detection'
   *  Inport: '<Root>/MisfAbsTdc'
   */
  /* Transition: '<S6>:776' */
  StMisfEOA[(MisfAbsTdc)] = EOA_NOT_EXE;

  /* SignalConversion generated from: '<S4>/FlgMisfDetected' */
  FlgMisfDetected = IonMisf_DW.FlgMisfDetected_f;
}

/* Function for Chart: '<Root>/IonMisf_sched' */
static void IonMisf_chartstep_c2_IonMisf(const int32_T *sfEvent)
{
  uint16_T IntStMisf;
  int32_T i;

  /* Chart: '<Root>/IonMisf_sched' */
  /* Chart: '<Root>/IonMisf_sched' incorporates:
   *  Constant: '<Root>/ENIONMISF'
   *
   * Block requirements for '<Root>/ENIONMISF':
   *  1. EISB_FCA6CYL_SW_REQ_1762: The software shall enable the calculation of  StMisf and FlgMisfDe... (ECU_SW_Requirements#5739)
   */
  /* During: IonMisf_sched */
  /* This stateflow contents the module scheduler.
     Functions is initialized at the PowerOn or after a lost of synchronization and it's exceuted on the angular event, when it's enabled: ENIONMISF set at TRUE. */
  /* Entry Internal: IonMisf_sched */
  /* Transition: '<S2>:1' */
  if ((((*sfEvent) == ((int32_T)IonMisf_event_IonMisf_PowerOn)) || ((*sfEvent) ==
        ((int32_T)IonMisf_event_IonMisf_NoSync))) || (!ENIONMISF)) {
    /* Transition: '<S2>:2'
     * Requirements for Transition: '<S2>:2':
     *  1. EISB_FCA6CYL_SW_REQ_1761: The software shall reset the bitmask signal FlgMisfDetected and se... (ECU_SW_Requirements#5738)
     */
    /* Transition: '<S2>:19'
     * Requirements for Transition: '<S2>:19':
     *  1. EISB_FCA6CYL_SW_REQ_1762: The software shall enable the calculation of  StMisf and FlgMisfDe... (ECU_SW_Requirements#5739)
     */
    /* Transition: '<S2>:27'
     * Requirements for Transition: '<S2>:27':
     *  1. EISB_FCA6CYL_SW_REQ_1762: The software shall enable the calculation of  StMisf and FlgMisfDe... (ECU_SW_Requirements#5739)
     *  2. EISB_FCA6CYL_SW_REQ_1761: The software shall reset the bitmask signal FlgMisfDetected and se... (ECU_SW_Requirements#5738)
     */
    FlgMisfDetected = 0U;
    for (i = 0; i < 8; i++) {
      StMisf[(i)] = 0U;
      StMisfEOA[(i)] = 0U;
    }

    IdVer_IonMisf = ID_VER_IONMISF_DEF;

    /* Transition: '<S2>:45' */
  } else {
    /* Transition: '<S2>:33' */
    /* Transition: '<S2>:44' */
    if ((*sfEvent) == ((int32_T)IonMisf_event_IonMisf_TDC)) {
      /* Outputs for Function Call SubSystem: '<Root>/TDC_fcn'
       *
       * Block description for '<Root>/TDC_fcn':
       *  Function to detect misfire on MisfAbsTdc cylinder.
       *  The result of this analysis is stored in the status array StMisf[MisfAbsTdc].
       */
      /* Transition: '<S2>:15' */
      /* Transition: '<S2>:18'
       * Requirements for Transition: '<S2>:18':
       *  1. EISB_FCA6CYL_SW_REQ_1762: The software shall enable the calculation of  StMisf and FlgMisfDe... (ECU_SW_Requirements#5739)
       */
      /* Event: '<S2>:26' */
      IonMisf_TDC_fcn();

      /* End of Outputs for SubSystem: '<Root>/TDC_fcn' */
      /* Transition: '<S2>:45' */
    } else {
      /* Outputs for Function Call SubSystem: '<Root>/EOA_fcn' */
      /* Chart: '<S1>/MisfireEOA_Detection' incorporates:
       *  Constant: '<S1>/ENKCOHINCMISF1'
       *  Constant: '<S1>/ENKCOHINCMISF2'
       *  Constant: '<S1>/ENKCOHINCMISF3'
       *  Constant: '<S1>/ENSTMISFTHINT'
       *  Constant: '<S1>/MISFRESET'
       *  Inport: '<Root>/BadCombThr'
       *  Inport: '<Root>/FlgCntKnockCohInc'
       *  Inport: '<Root>/FlgKCohInc'
       *  Inport: '<Root>/FlgKCohIncLev1'
       *  Inport: '<Root>/IntIon'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Inport: '<Root>/NMSparkPrg'
       *  Inport: '<Root>/NoCombThr'
       *  Inport: '<Root>/ParCombThr'
       *  Inport: '<Root>/PtFaultChannel'
       *  Inport: '<Root>/StPhase'
       *  Inport: '<Root>/ThInt'
       *  Inport: '<Root>/VtRec'
       *
       * Block requirements for '<S1>/ENKCOHINCMISF1':
       *  1. EISB_FCA6CYL_SW_REQ_1765: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#5734)
       *
       * Block requirements for '<S1>/ENKCOHINCMISF2':
       *  1. EISB_FCA6CYL_SW_REQ_1765: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#5734)
       *
       * Block requirements for '<S1>/ENKCOHINCMISF3':
       *  1. EISB_FCA6CYL_SW_REQ_1765: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#5734)
       *
       * Block requirements for '<S1>/ENSTMISFTHINT':
       *  1. EISB_FCA6CYL_SW_REQ_1764: The software shall set the source of the Ion signal to be used for... (ECU_SW_Requirements#5730)
       *
       * Block requirements for '<S1>/MISFRESET':
       *  1. EISB_FCA6CYL_SW_REQ_1763: The software shall reset the counters CntMisFire, CntNoComb, CntPa... (ECU_SW_Requirements#5740)
       */
      /* Transition: '<S2>:35' */
      /* IonMisf_EOA */
      /* Transition: '<S2>:39' */
      /* Event: '<S2>:38' */
      /* Gateway: EOA_fcn/MisfireEOA_Detection */
      /* During: EOA_fcn/MisfireEOA_Detection */
      /* This stateflow calculates the StMisf status for each cylinder considering the result of the Ion signal, if it's plausible and no errors, recoveries or functionalities (like multispark), which can effect the misfire detection, are active. */
      /* Entry Internal: EOA_fcn/MisfireEOA_Detection */
      /* Transition: '<S5>:9' */
      if (((uint32_T)PtFaultChannel[(IonAbsTdcEOA)]) != NO_PT_FAULT) {
        /* Assignment: '<S1>/Assignment1' */
        /* Transition: '<S5>:770'
         * Requirements for Transition: '<S5>:770':
         *  1. EISB_FCA6CYL_SW_REQ_1455: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#3219)
         */
        /* Punctual error on ION signal acquisition */
        StMisfEOA[(IonAbsTdcEOA)] = NO_MISF;

        /* Transition: '<S5>:771' */
        /* Transition: '<S5>:704' */
        /* Transition: '<S5>:706' */
        /* Transition: '<S5>:708' */
        /* Transition: '<S5>:735' */
        /* Transition: '<S5>:736' */
      } else {
        /* Transition: '<S5>:773' */
        if ((((ENKCOHINCMISF1) && (FlgKCohIncLev1[(IonAbsTdcEOA)])) ||
             ((ENKCOHINCMISF2) && (FlgCntKnockCohInc[(IonAbsTdcEOA)]))) ||
            ((ENKCOHINCMISF3) && (FlgKCohInc))) {
          /* Assignment: '<S1>/Assignment1' */
          /* Transition: '<S5>:223'
           * Requirements for Transition: '<S5>:223':
           *  1. EISB_FCA6CYL_SW_REQ_1765: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#5734)
           */
          /* Check on Knocking control coherence level */
          StMisfEOA[(IonAbsTdcEOA)] = NO_MISF;

          /* Transition: '<S5>:704' */
          /* Transition: '<S5>:706' */
          /* Transition: '<S5>:708' */
          /* Transition: '<S5>:735' */
          /* Transition: '<S5>:736' */
        } else {
          /* Transition: '<S5>:222' */
          if (((uint32_T)StPhase[(IonAbsTdcEOA)]) < CH_START_FOUND) {
            /* Assignment: '<S1>/Assignment1' */
            /* Transition: '<S5>:13'
             * Requirements for Transition: '<S5>:13':
             *  1. EISB_FCA6CYL_SW_REQ_1449: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#3214)
             */
            /* ION signal not plausible */
            StMisfEOA[(IonAbsTdcEOA)] = BAD_ION;

            /* Transition: '<S5>:706' */
            /* Transition: '<S5>:708' */
            /* Transition: '<S5>:735' */
            /* Transition: '<S5>:736' */
          } else {
            /* Transition: '<S5>:361' */
            if (((int32_T)NMSparkPrg[(IonAbsTdcEOA)]) > 0) {
              /* Assignment: '<S1>/Assignment1' */
              /* Transition: '<S5>:479'
               * Requirements for Transition: '<S5>:479':
               *  1. EISB_FCA6CYL_SW_REQ_1456: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#3218)
               */
              /* Multispark enabled */
              StMisfEOA[(IonAbsTdcEOA)] = NO_MISF;

              /* Transition: '<S5>:708' */
              /* Transition: '<S5>:735' */
              /* Transition: '<S5>:736' */
            } else {
              /* Transition: '<S5>:489' */
              if (((int32_T)VtRec[REC_NO_MISFUEL_0 + IonAbsTdcEOA]) != 0) {
                /* Assignment: '<S1>/Assignment1' */
                /* Transition: '<S5>:542' */
                /* Misfuel recovery enabled */
                StMisfEOA[(IonAbsTdcEOA)] = NO_MISF;

                /* Transition: '<S5>:735' */
                /* Transition: '<S5>:736' */
              } else {
                /* Transition: '<S5>:543' */
                if (ENSTMISFTHINT) {
                  /* Transition: '<S5>:748'
                   * Requirements for Transition: '<S5>:748':
                   *  1. EISB_FCA6CYL_SW_REQ_1764: The software shall set the source of the Ion signal to be used for... (ECU_SW_Requirements#5730)
                   */
                  IntStMisf = ThInt[(IonAbsTdcEOA)];
                } else {
                  /* Transition: '<S5>:747'
                   * Requirements for Transition: '<S5>:747':
                   *  1. EISB_FCA6CYL_SW_REQ_1764: The software shall set the source of the Ion signal to be used for... (ECU_SW_Requirements#5730)
                   */
                  IntStMisf = IntIon[(IonAbsTdcEOA)];
                }

                /* Transition: '<S5>:356' */
                if (IntStMisf < NoCombThr) {
                  /* Assignment: '<S1>/Assignment1' */
                  /* Transition: '<S5>:2'
                   * Requirements for Transition: '<S5>:2':
                   *  1. EISB_FCA6CYL_SW_REQ_1451: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#3215)
                   */
                  StMisfEOA[(IonAbsTdcEOA)] = NO_COMB;
                  CntNoComb = (uint16_T)((int32_T)(((int32_T)CntNoComb) + 1));

                  /* Transition: '<S5>:718' */
                  /* Transition: '<S5>:720' */
                  /* Transition: '<S5>:722' */
                  /* Transition: '<S5>:728' */
                } else {
                  /* Transition: '<S5>:6' */
                  if (IntStMisf <= BadCombThr) {
                    /* Assignment: '<S1>/Assignment1' */
                    /* Transition: '<S5>:1'
                     * Requirements for Transition: '<S5>:1':
                     *  1. EISB_FCA6CYL_SW_REQ_1453: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#3216)
                     */
                    StMisfEOA[(IonAbsTdcEOA)] = BAD_COMB;
                    CntBadComb = (uint16_T)((int32_T)(((int32_T)CntBadComb) + 1));

                    /* Transition: '<S5>:720' */
                    /* Transition: '<S5>:722' */
                    /* Transition: '<S5>:728' */
                  } else {
                    /* Transition: '<S5>:8' */
                    if (IntStMisf <= ParCombThr) {
                      /* Assignment: '<S1>/Assignment1' */
                      /* Transition: '<S5>:3'
                       * Requirements for Transition: '<S5>:3':
                       *  1. EISB_FCA6CYL_SW_REQ_1454: The software shall set the signal StMisf for the related cylinder ... (ECU_SW_Requirements#3217)
                       */
                      StMisfEOA[(IonAbsTdcEOA)] = PAR_COMB;
                      CntParComb = (uint16_T)((int32_T)(((int32_T)CntParComb) +
                        1));

                      /* Transition: '<S5>:722' */
                      /* Transition: '<S5>:728' */
                    } else {
                      /* Assignment: '<S1>/Assignment1' */
                      /* Transition: '<S5>:7' */
                      StMisfEOA[(IonAbsTdcEOA)] = NO_MISF;
                    }
                  }
                }
              }
            }
          }
        }
      }

      if (MISFRESET) {
        /* Transition: '<S5>:729' */
        CntNoComb = 0U;
        CntBadComb = 0U;
        CntParComb = 0U;
      } else {
        /* Transition: '<S5>:731' */
      }

      /* End of Chart: '<S1>/MisfireEOA_Detection' */
      /* End of Outputs for SubSystem: '<Root>/EOA_fcn' */
      /* Transition: '<S5>:768' */
    }
  }

  /* End of Chart: '<Root>/IonMisf_sched' */
  /* Transition: '<S2>:47' */
}

/* System initialize for function-call system: '<Root>/IonMisf_sched' */
void IonMisf_IonMisf_sched_Init(void)
{
  int32_T i;
  FlgMisfDetected = 0U;
  for (i = 0; i < 8; i++) {
    StMisf[(i)] = NO_MISF;
    StMisfEOA[(i)] = NO_MISF;
  }
}

/* Output and update for function-call system: '<Root>/IonMisf_sched' */
void IonMisf_IonMisf_sched(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[4];
  int32_T i;

  /* Chart: '<Root>/IonMisf_sched' incorporates:
   *  TriggerPort: '<S2>/input events'
   */
  for (i = 0; i < 4; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: IonMisf_sched */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S2>:23' */
    i = (int32_T)IonMisf_event_IonMisf_PowerOn;
    IonMisf_chartstep_c2_IonMisf(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S2>:25' */
    i = (int32_T)IonMisf_event_IonMisf_TDC;
    IonMisf_chartstep_c2_IonMisf(&i);
  }

  if (rtb_inputevents[2U] == 2) {
    /* Event: '<S2>:24' */
    i = (int32_T)IonMisf_event_IonMisf_NoSync;
    IonMisf_chartstep_c2_IonMisf(&i);
  }

  if (rtb_inputevents[3U] == 2) {
    /* Event: '<S2>:31' */
    i = (int32_T)IonMisf_event_IonMisf_EOA;
    IonMisf_chartstep_c2_IonMisf(&i);
  }
}

/* Model step function */
void IonMisf_EOA(void)
{
  /* Chart: '<Root>/IonMisf_sched' */

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_EOA' */

  /* Chart: '<Root>/IonMisf_sched' */
  IonMisf_IonMisf_sched(3);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_EOA' */
}

/* Model step function */
void IonMisf_NoSync(void)
{
  /* Chart: '<Root>/IonMisf_sched' */

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_NoSync' */

  /* Chart: '<Root>/IonMisf_sched' */
  IonMisf_IonMisf_sched(2);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_NoSync' */
}

/* Model step function */
void IonMisf_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_PowerOn' incorporates:
   *  Chart: '<Root>/IonMisf_sched'
   */

  /* Chart: '<Root>/IonMisf_sched' */
  IonMisf_IonMisf_sched(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_PowerOn' */
}

/* Model step function */
void IonMisf_TDC(void)
{
  /* Chart: '<Root>/IonMisf_sched' */

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_TDC' */

  /* Chart: '<Root>/IonMisf_sched' */
  IonMisf_IonMisf_sched(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_TDC' */
}

/* Model initialize function */
void IonMisf_initialize(void)
{
  {
    int32_T i;

    /* SystemInitialize for Chart: '<Root>/IonMisf_sched' */
    /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_EOA' */
    /* SystemInitialize for Chart: '<Root>/IonMisf_sched' */
    IonMisf_IonMisf_sched_Init();

    /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/IonMisf_EOA' */
    for (i = 0; i < 8; i++) {
      /* SystemInitialize for Merge: '<S3>/Merge' */
      StMisf[(i)] = NO_MISF;

      /* SystemInitialize for Merge: '<S3>/Merge2' */
      StMisfEOA[(i)] = NO_MISF;
    }

    /* SystemInitialize for Merge: '<S3>/Merge1' */
    FlgMisfDetected = 0U;
  }
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else                                  /*_BUILD_IonMisf_*/

/*Functions definition*/
void IonMisf_NoSync(void)
{
}

void IonMisf_TDC(void)
{
}

void IonMisf_initialize(void)
{
}

/*Outputs*/
uint8_T FlgMisfDetected;
uint8_T StMisf[8];
void IonMisf_PowerOn(void)
{
  uint8_T stmisf_cnt_tmp;
  FlgMisfDetected = 0U;
  for (stmisf_cnt_tmp = 0; stmisf_cnt_tmp < 8; stmisf_cnt_tmp++) {
    StMisf[stmisf_cnt_tmp] = ((uint8_T)NO_MISF);
  }
}

#endif                                 /* _BUILD_IonMisf_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/