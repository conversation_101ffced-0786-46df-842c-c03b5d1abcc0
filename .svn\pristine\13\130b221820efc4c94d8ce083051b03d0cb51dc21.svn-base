/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tim_cfg.h
 * @brief   GTM TIM Driver configuration macros and structures.
 *
 * @addtogroup TIM
 * @{
 */

#ifndef _GTM_TIM_CFG_H_
#define _GTM_TIM_CFG_H_

#include "gtm_tim.h"

#define TIMX_CHY_FLT_RE 400u
#define TIMX_CHY_FLT_FE 400u
/*lint -e621*/

/* ---- TIM 0 Settings ---- */

#define SPC5_GTM_TIM0_SW_RESET (0UL | (ENABLE_FIELD << (SW_RST_BASE + 0UL)) | (ENABLE_FIELD << (SW_RST_BASE + 1UL)) | (ENABLE_FIELD << (SW_RST_BASE + 2UL)))
#define SPC5_GTM_TIM0_IN_SRC  (0UL)

#define SPC5_GTM_TIM0_CH0_ENABLE  TRUE

#define SPC5_GTM_TIM0_CH0_CNT 0UL
#define SPC5_GTM_TIM0_CH0_GPR1 0UL
#define SPC5_GTM_TIM0_CH0_CNTS 0UL
#define SPC5_GTM_TIM0_CH0_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM0_CH0_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM0_CH0_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM0_CH0_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM0_CH0_FLT_RE         400UL
#define SPC5_GTM_TIM0_CH0_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM0_CH0_FLT_CTR_FE     4000UL
#define SPC5_GTM_TIM0_CH0_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM0_CH0_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0
#define SPC5_GTM_TIM0_CH0_TOV 0UL

/* SPC5_GTM_TIM0_CH0_CTRL Control Register */
#define SPC5_GTM_TIM0_CH0_CTRL (0UL | (SPC5_GTM_TIM_GPR_SEL_TBU_TS0 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS0 << CH_GPR1) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_DISABLED << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT) | (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM0_CH1_ENABLE        (TRUE)
#define SPC5_GTM_TIM0_CH1_CTRL          (0UL | (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS1 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_FILTER_CNT_CMU_CLK0 << CH_FLT_CNT_FREQ) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_DISABLED << CH_TOCTRL))
#define SPC5_GTM_TIM0_CH1_FLT_RE        (TIMX_CHY_FLT_RE)
#define SPC5_GTM_TIM0_CH1_FLT_FE        (TIMX_CHY_FLT_FE)
#define SPC5_GTM_TIM0_CH1_CNT           (0)
#define SPC5_GTM_TIM0_CH1_GPR1          (0)
#define SPC5_GTM_TIM0_CH1_CNTS          (0)
#define SPC5_GTM_TIM0_CH1_ECTRL         (SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ)

/* Timeout detection */
#define SPC5_GTM_TIM0_CH1_TCS           (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0)
#define SPC5_GTM_TIM0_CH1_TOV           (0UL)

#define SPC5_GTM_TIM0_CH2_ENABLE        (TRUE)
#define SPC5_GTM_TIM0_CH2_CTRL          (0UL | (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS1 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_FILTER_CNT_CMU_CLK0 << CH_FLT_CNT_FREQ) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_DISABLED << CH_TOCTRL))
#define SPC5_GTM_TIM0_CH2_FLT_RE        (TIMX_CHY_FLT_RE)
#define SPC5_GTM_TIM0_CH2_FLT_FE        (TIMX_CHY_FLT_FE)
#define SPC5_GTM_TIM0_CH2_CNT           (0)
#define SPC5_GTM_TIM0_CH2_GPR1          (0)
#define SPC5_GTM_TIM0_CH2_CNTS          (0)
#define SPC5_GTM_TIM0_CH2_ECTRL         (SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ)

/* Timeout detection */
#define SPC5_GTM_TIM0_CH2_TCS           (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0)
#define SPC5_GTM_TIM0_CH2_TOV           (0UL)

#define SPC5_GTM_TIM0_CH3_ENABLE         FALSE
#define SPC5_GTM_TIM0_CH3_CTRL           0UL
#define SPC5_GTM_TIM0_CH3_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM0_CH3_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM0_CH3_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM0_CH3_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM0_CH3_FLT_RE         0UL
#define SPC5_GTM_TIM0_CH3_FLT_FE         0UL
#define SPC5_GTM_TIM0_CH3_CNT            0UL
#define SPC5_GTM_TIM0_CH3_GPR1           0UL
#define SPC5_GTM_TIM0_CH3_CNTS           0UL
#define SPC5_GTM_TIM0_CH3_TCS            0UL
#define SPC5_GTM_TIM0_CH3_TOV            0UL
#define SPC5_GTM_TIM0_CH3_ECTRL          0UL

#define SPC5_GTM_TIM0_CH4_ENABLE         FALSE
#define SPC5_GTM_TIM0_CH4_CTRL           0UL
#define SPC5_GTM_TIM0_CH4_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM0_CH4_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM0_CH4_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM0_CH4_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM0_CH4_FLT_RE         0UL
#define SPC5_GTM_TIM0_CH4_FLT_FE         0UL
#define SPC5_GTM_TIM0_CH4_CNT            0UL
#define SPC5_GTM_TIM0_CH4_GPR1           0UL
#define SPC5_GTM_TIM0_CH4_CNTS           0UL
#define SPC5_GTM_TIM0_CH4_TCS            0UL
#define SPC5_GTM_TIM0_CH4_TOV            0UL
#define SPC5_GTM_TIM0_CH4_ECTRL          0UL

#define SPC5_GTM_TIM0_CH5_ENABLE         FALSE
#define SPC5_GTM_TIM0_CH5_CTRL           0UL
#define SPC5_GTM_TIM0_CH5_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM0_CH5_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM0_CH5_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM0_CH5_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM0_CH5_FLT_RE         0UL
#define SPC5_GTM_TIM0_CH5_FLT_FE         0UL
#define SPC5_GTM_TIM0_CH5_CNT            0UL
#define SPC5_GTM_TIM0_CH5_GPR1           0UL
#define SPC5_GTM_TIM0_CH5_CNTS           0UL
#define SPC5_GTM_TIM0_CH5_TCS            0UL
#define SPC5_GTM_TIM0_CH5_TOV            0UL
#define SPC5_GTM_TIM0_CH5_ECTRL          0UL

#define SPC5_GTM_TIM0_CH6_ENABLE         FALSE
#define SPC5_GTM_TIM0_CH6_CTRL           0UL
#define SPC5_GTM_TIM0_CH6_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM0_CH6_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM0_CH6_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM0_CH6_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM0_CH6_FLT_RE         0UL
#define SPC5_GTM_TIM0_CH6_FLT_FE         0UL
#define SPC5_GTM_TIM0_CH6_CNT            0UL
#define SPC5_GTM_TIM0_CH6_GPR1           0UL
#define SPC5_GTM_TIM0_CH6_CNTS           0UL
#define SPC5_GTM_TIM0_CH6_TCS            0UL
#define SPC5_GTM_TIM0_CH6_TOV            0UL
#define SPC5_GTM_TIM0_CH6_ECTRL          0UL

#define SPC5_GTM_TIM0_CH7_ENABLE         FALSE
#define SPC5_GTM_TIM0_CH7_CTRL           0UL
#define SPC5_GTM_TIM0_CH7_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM0_CH7_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM0_CH7_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM0_CH7_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM0_CH7_FLT_RE         0UL
#define SPC5_GTM_TIM0_CH7_FLT_FE         0UL
#define SPC5_GTM_TIM0_CH7_CNT            0UL
#define SPC5_GTM_TIM0_CH7_GPR1           0UL
#define SPC5_GTM_TIM0_CH7_CNTS           0UL
#define SPC5_GTM_TIM0_CH7_TCS            0UL
#define SPC5_GTM_TIM0_CH7_TOV            0UL
#define SPC5_GTM_TIM0_CH7_ECTRL          0UL

/* ---- TIM 1 Settings ---- */

#define SPC5_GTM_TIM1_SW_RESET (0UL | (ENABLE_FIELD << (SW_RST_BASE + 0UL)) | (ENABLE_FIELD << (SW_RST_BASE + 1UL)) | (ENABLE_FIELD << (SW_RST_BASE + 2UL)) | (ENABLE_FIELD << (SW_RST_BASE + 3UL)) | (ENABLE_FIELD << (SW_RST_BASE + 4UL)) | (ENABLE_FIELD << (SW_RST_BASE + 5UL)) | (ENABLE_FIELD << (SW_RST_BASE + 6UL)) | (ENABLE_FIELD << (SW_RST_BASE + 7UL)))
#define SPC5_GTM_TIM1_IN_SRC  (0UL)

#define SPC5_GTM_TIM1_CH0_ENABLE  TRUE

#define SPC5_GTM_TIM1_CH0_CNT 0UL
#define SPC5_GTM_TIM1_CH0_GPR1 0UL
#define SPC5_GTM_TIM1_CH0_CNTS 0UL
#define SPC5_GTM_TIM1_CH0_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM1_CH0_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM1_CH0_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM1_CH0_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM1_CH0_FLT_RE         400UL
#define SPC5_GTM_TIM1_CH0_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM1_CH0_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM1_CH0_FLT_FE         400UL

/* Timeout detection */
#define SPC5_GTM_TIM1_CH0_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM1_CH0_TOV 255UL

/* SPC5_GTM_TIM1_CH0_CTRL Control Register */
#define SPC5_GTM_TIM1_CH0_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT) | (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM1_CH1_ENABLE  TRUE

#define SPC5_GTM_TIM1_CH1_CNT 0UL
#define SPC5_GTM_TIM1_CH1_GPR1 0UL
#define SPC5_GTM_TIM1_CH1_CNTS 0UL
#define SPC5_GTM_TIM1_CH1_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM1_CH1_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM1_CH1_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM1_CH1_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM1_CH1_FLT_RE         400UL
#define SPC5_GTM_TIM1_CH1_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM1_CH1_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM1_CH1_FLT_FE         400UL

/* Timeout detection */
#define SPC5_GTM_TIM1_CH1_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM1_CH1_TOV 255UL

/* SPC5_GTM_TIM1_CH1_CTRL Control Register */
#define SPC5_GTM_TIM1_CH1_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT)| (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM1_CH2_ENABLE  TRUE

#define SPC5_GTM_TIM1_CH2_CNT 0UL
#define SPC5_GTM_TIM1_CH2_GPR1 0UL
#define SPC5_GTM_TIM1_CH2_CNTS 0UL
#define SPC5_GTM_TIM1_CH2_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM1_CH2_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM1_CH2_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM1_CH2_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM1_CH2_FLT_RE         400UL
#define SPC5_GTM_TIM1_CH2_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM1_CH2_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM1_CH2_FLT_FE         400UL

/* Timeout detection */
#define SPC5_GTM_TIM1_CH2_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM1_CH2_TOV 255UL

/* SPC5_GTM_TIM1_CH2_CTRL Control Register */
#define SPC5_GTM_TIM1_CH2_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT)| (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM1_CH3_ENABLE  TRUE

#define SPC5_GTM_TIM1_CH3_CNT 0UL
#define SPC5_GTM_TIM1_CH3_GPR1 0UL
#define SPC5_GTM_TIM1_CH3_CNTS 0UL
#define SPC5_GTM_TIM1_CH3_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM1_CH3_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM1_CH3_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM1_CH3_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM1_CH3_FLT_RE         400UL
#define SPC5_GTM_TIM1_CH3_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM1_CH3_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM1_CH3_FLT_FE         400UL

/* Timeout detection */
#define SPC5_GTM_TIM1_CH3_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM1_CH3_TOV 255UL

/* SPC5_GTM_TIM1_CH3_CTRL Control Register */
#define SPC5_GTM_TIM1_CH3_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT)| (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM1_CH4_ENABLE  TRUE

#define SPC5_GTM_TIM1_CH4_CNT 0UL
#define SPC5_GTM_TIM1_CH4_GPR1 0UL
#define SPC5_GTM_TIM1_CH4_CNTS 0UL
#define SPC5_GTM_TIM1_CH4_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM1_CH4_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM1_CH4_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM1_CH4_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM1_CH4_FLT_RE         400UL
#define SPC5_GTM_TIM1_CH4_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM1_CH4_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM1_CH4_FLT_FE         400UL

/* Timeout detection */
#define SPC5_GTM_TIM1_CH4_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM1_CH4_TOV 255UL

/* SPC5_GTM_TIM1_CH4_CTRL Control Register */
#define SPC5_GTM_TIM1_CH4_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT) | (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM1_CH5_ENABLE  TRUE

#define SPC5_GTM_TIM1_CH5_CNT 0UL
#define SPC5_GTM_TIM1_CH5_GPR1 0UL
#define SPC5_GTM_TIM1_CH5_CNTS 0UL
#define SPC5_GTM_TIM1_CH5_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM1_CH5_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM1_CH5_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM1_CH5_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM1_CH5_FLT_RE         400UL
#define SPC5_GTM_TIM1_CH5_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM1_CH5_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM1_CH5_FLT_FE         400UL

/* Timeout detection */
#define SPC5_GTM_TIM1_CH5_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM1_CH5_TOV 255UL

/* SPC5_GTM_TIM1_CH5_CTRL Control Register */
#define SPC5_GTM_TIM1_CH5_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT) | (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM1_CH6_ENABLE  TRUE

#define SPC5_GTM_TIM1_CH6_CNT 0UL
#define SPC5_GTM_TIM1_CH6_GPR1 0UL
#define SPC5_GTM_TIM1_CH6_CNTS 0UL
#define SPC5_GTM_TIM1_CH6_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM1_CH6_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM1_CH6_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM1_CH6_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM1_CH6_FLT_RE         400UL
#define SPC5_GTM_TIM1_CH6_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM1_CH6_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM1_CH6_FLT_FE         400UL

/* Timeout detection */
#define SPC5_GTM_TIM1_CH6_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM1_CH6_TOV 255UL

/* SPC5_GTM_TIM1_CH6_CTRL Control Register */
#define SPC5_GTM_TIM1_CH6_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT) | (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM1_CH7_ENABLE  TRUE

#define SPC5_GTM_TIM1_CH7_CNT 0UL
#define SPC5_GTM_TIM1_CH7_GPR1 0UL
#define SPC5_GTM_TIM1_CH7_CNTS 0UL
#define SPC5_GTM_TIM1_CH7_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM1_CH7_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM1_CH7_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM1_CH7_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM1_CH7_FLT_RE         400UL
#define SPC5_GTM_TIM1_CH7_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM1_CH7_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM1_CH7_FLT_FE         400UL

/* Timeout detection */
#define SPC5_GTM_TIM1_CH7_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM1_CH7_TOV 255UL

/* SPC5_GTM_TIM1_CH7_CTRL Control Register */
#define SPC5_GTM_TIM1_CH7_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT) | (ENABLE_FIELD << CH_FLT) | (ENABLE_FIELD << CH_FLT_M_RE) | (ENABLE_FIELD << CH_FLT_M_FE))

/*------------------------------------------------------------------------------*/

/* ---- TIM 2 Settings ---- */

#define SPC5_GTM_TIM2_SW_RESET (0UL | (ENABLE_FIELD << (SW_RST_BASE + 0UL)) | (ENABLE_FIELD << (SW_RST_BASE + 1UL)) | (ENABLE_FIELD << (SW_RST_BASE + 2UL)) | (ENABLE_FIELD << (SW_RST_BASE + 3UL)) | (ENABLE_FIELD << (SW_RST_BASE + 4UL)) | (ENABLE_FIELD << (SW_RST_BASE + 5UL)) | (ENABLE_FIELD << (SW_RST_BASE + 6UL)) | (ENABLE_FIELD << (SW_RST_BASE + 7UL)))
#define SPC5_GTM_TIM2_IN_SRC  (0UL)

#define SPC5_GTM_TIM2_CH0_ENABLE  TRUE

#define SPC5_GTM_TIM2_CH0_CNT 0UL
#define SPC5_GTM_TIM2_CH0_GPR1 0UL
#define SPC5_GTM_TIM2_CH0_CNTS 0UL
#define SPC5_GTM_TIM2_CH0_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM2_CH0_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM2_CH0_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM2_CH0_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM2_CH0_FLT_RE         0UL
#define SPC5_GTM_TIM2_CH0_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM2_CH0_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM2_CH0_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM2_CH0_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM2_CH0_TOV 255UL

/* SPC5_GTM_TIM2_CH0_CTRL Control Register */
#define SPC5_GTM_TIM2_CH0_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_DSL) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM2_CH1_ENABLE  TRUE

#define SPC5_GTM_TIM2_CH1_CNT 0UL
#define SPC5_GTM_TIM2_CH1_GPR1 0UL
#define SPC5_GTM_TIM2_CH1_CNTS 0UL
#define SPC5_GTM_TIM2_CH1_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM2_CH1_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM2_CH1_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM2_CH1_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM2_CH1_FLT_RE         0UL
#define SPC5_GTM_TIM2_CH1_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM2_CH1_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM2_CH1_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM2_CH1_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM2_CH1_TOV 255UL

/* SPC5_GTM_TIM2_CH1_CTRL Control Register */
#define SPC5_GTM_TIM2_CH1_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_DSL) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM2_CH2_ENABLE  TRUE

#define SPC5_GTM_TIM2_CH2_CNT 0UL
#define SPC5_GTM_TIM2_CH2_GPR1 0UL
#define SPC5_GTM_TIM2_CH2_CNTS 0UL
#define SPC5_GTM_TIM2_CH2_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM2_CH2_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM2_CH2_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM2_CH2_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM2_CH2_FLT_RE         0UL
#define SPC5_GTM_TIM2_CH2_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM2_CH2_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM2_CH2_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM2_CH2_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM2_CH2_TOV 255UL

/* SPC5_GTM_TIM2_CH2_CTRL Control Register */
#define SPC5_GTM_TIM2_CH2_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_DSL) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM2_CH3_ENABLE  TRUE

#define SPC5_GTM_TIM2_CH3_CNT 0UL
#define SPC5_GTM_TIM2_CH3_GPR1 0UL
#define SPC5_GTM_TIM2_CH3_CNTS 0UL
#define SPC5_GTM_TIM2_CH3_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM2_CH3_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM2_CH3_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM2_CH3_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM2_CH3_FLT_RE         0UL
#define SPC5_GTM_TIM2_CH3_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM2_CH3_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM2_CH3_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM2_CH3_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM2_CH3_TOV 255UL

/* SPC5_GTM_TIM2_CH3_CTRL Control Register */
#define SPC5_GTM_TIM2_CH3_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_DSL) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM2_CH4_ENABLE  TRUE

#define SPC5_GTM_TIM2_CH4_CNT 0UL
#define SPC5_GTM_TIM2_CH4_GPR1 0UL
#define SPC5_GTM_TIM2_CH4_CNTS 0UL
#define SPC5_GTM_TIM2_CH4_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM2_CH4_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM2_CH4_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM2_CH4_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM2_CH4_FLT_RE         0UL
#define SPC5_GTM_TIM2_CH4_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM2_CH4_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM2_CH4_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM2_CH4_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM2_CH4_TOV 255UL

/* SPC5_GTM_TIM2_CH4_CTRL Control Register */
#define SPC5_GTM_TIM2_CH4_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_DSL) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM2_CH5_ENABLE  TRUE

#define SPC5_GTM_TIM2_CH5_CNT 0UL
#define SPC5_GTM_TIM2_CH5_GPR1 0UL
#define SPC5_GTM_TIM2_CH5_CNTS 0UL
#define SPC5_GTM_TIM2_CH5_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM2_CH5_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM2_CH5_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM2_CH5_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM2_CH5_FLT_RE         0UL
#define SPC5_GTM_TIM2_CH5_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM2_CH5_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM2_CH5_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM2_CH5_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM2_CH5_TOV 255UL

/* SPC5_GTM_TIM2_CH5_CTRL Control Register */
#define SPC5_GTM_TIM2_CH5_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_DSL) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM2_CH6_ENABLE  TRUE

#define SPC5_GTM_TIM2_CH6_CNT 0UL
#define SPC5_GTM_TIM2_CH6_GPR1 0UL
#define SPC5_GTM_TIM2_CH6_CNTS 0UL
#define SPC5_GTM_TIM2_CH6_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM2_CH6_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM2_CH6_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM2_CH6_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM2_CH6_FLT_RE         0UL
#define SPC5_GTM_TIM2_CH6_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM2_CH6_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM2_CH6_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM2_CH6_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM2_CH6_TOV 255UL

/* SPC5_GTM_TIM2_CH6_CTRL Control Register */
#define SPC5_GTM_TIM2_CH6_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_DSL) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT))

/*------------------------------------------------------------------------------*/

#define SPC5_GTM_TIM2_CH7_ENABLE  TRUE

#define SPC5_GTM_TIM2_CH7_CNT 0UL
#define SPC5_GTM_TIM2_CH7_GPR1 0UL
#define SPC5_GTM_TIM2_CH7_CNTS 0UL
#define SPC5_GTM_TIM2_CH7_ECTRL SPC5_GTM_TIM_CH_SRC_TRG_NEW_VAL_IRQ

/* Filter functionality */
#define SPC5_GTM_TIM2_CH7_FLT_CNT_FRQ    0UL
#define SPC5_GTM_TIM2_CH7_FLT_MODE_RE    0UL
#define SPC5_GTM_TIM2_CH7_FLT_CTR_RE     0UL
#define SPC5_GTM_TIM2_CH7_FLT_RE         0UL
#define SPC5_GTM_TIM2_CH7_FLT_MODE_FE    0UL
#define SPC5_GTM_TIM2_CH7_FLT_CTR_FE     0UL
#define SPC5_GTM_TIM2_CH7_FLT_FE         0UL

/* Timeout detection */
#define SPC5_GTM_TIM2_CH7_TCS SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
#define SPC5_GTM_TIM2_CH7_TOV 255UL

/* SPC5_GTM_TIM2_CH7_CTRL Control Register */
#define SPC5_GTM_TIM2_CH7_CTRL (0UL | (ENABLE_FIELD << CH_ARU) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR0) | (SPC5_GTM_TIM_GPR_SEL_TBU_TS2 << CH_GPR1) | (ENABLE_FIELD << CH_DSL) | (ENABLE_FIELD << CH_ISL) | (ENABLE_FIELD << CH_ECNT_RES) | (SPC5_GTM_TIM_CH_MODE_TIEM << CH_MODES) | (SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0 << CH_CLK_SEL) | (SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH << CH_TOCTRL) | (SPC5_GTM_TIM_CH_FLTCTRL_DISABLED << CH_FLT))

/*------------------------------------------------------------------------------*/

/* ---- TIM 3 Settings ---- */

/* ---- TIM 4 Settings ---- */

/* ---- TIM 5 Settings ---- */





/* ---- TIM0 Settings ---- */

/* ---- TIM0 Channel 0 Settings ---- */
#define SPC5_GTM_TIM0_USE_CHANNEL0                             TRUE
#define SPC5_GTM_TIM0_CHANNEL0_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM0_CHANNEL0_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM0_CHANNEL0_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM0_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL0_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM0_CHANNEL0_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL0_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM0_CHANNEL0_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL0_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM0_CHANNEL0_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL0_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM0_CHANNEL0_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM0 Channel 1 Settings ---- */
#define SPC5_GTM_TIM0_USE_CHANNEL1                             TRUE
#define SPC5_GTM_TIM0_CHANNEL1_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM0_CHANNEL1_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM0_CHANNEL1_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM0_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL1_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM0_CHANNEL1_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL1_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM0_CHANNEL1_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL1_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM0_CHANNEL1_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL1_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM0_CHANNEL1_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM0 Channel 2 Settings ---- */
#define SPC5_GTM_TIM0_USE_CHANNEL2                             TRUE
#define SPC5_GTM_TIM0_CHANNEL2_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM0_CHANNEL2_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM0_CHANNEL2_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM0_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL2_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM0_CHANNEL2_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL2_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM0_CHANNEL2_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL2_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM0_CHANNEL2_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL2_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM0_CHANNEL2_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM0 Channel 3 Settings ---- */
#define SPC5_GTM_TIM0_USE_CHANNEL3                             FALSE
#define SPC5_GTM_TIM0_CHANNEL3_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM0_CHANNEL3_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM0_CHANNEL3_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM0_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL3_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM0_CHANNEL3_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL3_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM0_CHANNEL3_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL3_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM0_CHANNEL3_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL3_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM0_CHANNEL3_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM0 Channel 4 Settings ---- */
#define SPC5_GTM_TIM0_USE_CHANNEL4                             FALSE
#define SPC5_GTM_TIM0_CHANNEL4_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM0_CHANNEL4_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM0_CHANNEL4_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM0_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL4_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM0_CHANNEL4_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL4_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM0_CHANNEL4_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL4_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM0_CHANNEL4_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL4_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM0_CHANNEL4_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM0 Channel 5 Settings ---- */
#define SPC5_GTM_TIM0_USE_CHANNEL5                             FALSE
#define SPC5_GTM_TIM0_CHANNEL5_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM0_CHANNEL5_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM0_CHANNEL5_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM0_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL5_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM0_CHANNEL5_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL5_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM0_CHANNEL5_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL5_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM0_CHANNEL5_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL5_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM0_CHANNEL5_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM0 Channel 6 Settings ---- */
#define SPC5_GTM_TIM0_USE_CHANNEL6                             FALSE
#define SPC5_GTM_TIM0_CHANNEL6_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM0_CHANNEL6_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM0_CHANNEL6_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM0_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL6_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM0_CHANNEL6_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL6_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM0_CHANNEL6_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL6_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM0_CHANNEL6_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL6_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM0_CHANNEL6_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM0 Channel 7 Settings ---- */
#define SPC5_GTM_TIM0_USE_CHANNEL7                             FALSE
#define SPC5_GTM_TIM0_CHANNEL7_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM0_CHANNEL7_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM0_CHANNEL7_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM0_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL7_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM0_CHANNEL7_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL7_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM0_CHANNEL7_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL7_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM0_CHANNEL7_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM0_CHANNEL7_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM0_CHANNEL7_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM1 Settings ---- */

/* ---- TIM1 Channel 0 Settings ---- */
#define SPC5_GTM_TIM1_USE_CHANNEL0                             TRUE
#define SPC5_GTM_TIM1_CHANNEL0_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM1_CHANNEL0_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM1_CHANNEL0_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM1_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL0_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM1_CHANNEL0_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL0_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM1_CHANNEL0_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL0_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM1_CHANNEL0_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL0_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM1_CHANNEL0_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM1 Channel 1 Settings ---- */
#define SPC5_GTM_TIM1_USE_CHANNEL1                             TRUE
#define SPC5_GTM_TIM1_CHANNEL1_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM1_CHANNEL1_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM1_CHANNEL1_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM1_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL1_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM1_CHANNEL1_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL1_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM1_CHANNEL1_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL1_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM1_CHANNEL1_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL1_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM1_CHANNEL1_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM1 Channel 2 Settings ---- */
#define SPC5_GTM_TIM1_USE_CHANNEL2                             TRUE
#define SPC5_GTM_TIM1_CHANNEL2_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM1_CHANNEL2_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM1_CHANNEL2_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM1_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL2_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM1_CHANNEL2_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL2_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM1_CHANNEL2_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL2_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM1_CHANNEL2_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL2_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM1_CHANNEL2_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM1 Channel 3 Settings ---- */
#define SPC5_GTM_TIM1_USE_CHANNEL3                             TRUE
#define SPC5_GTM_TIM1_CHANNEL3_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM1_CHANNEL3_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM1_CHANNEL3_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM1_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL3_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM1_CHANNEL3_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL3_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM1_CHANNEL3_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL3_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM1_CHANNEL3_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL3_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM1_CHANNEL3_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM1 Channel 4 Settings ---- */
#define SPC5_GTM_TIM1_USE_CHANNEL4                             TRUE
#define SPC5_GTM_TIM1_CHANNEL4_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM1_CHANNEL4_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM1_CHANNEL4_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM1_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL4_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM1_CHANNEL4_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL4_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM1_CHANNEL4_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL4_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM1_CHANNEL4_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL4_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM1_CHANNEL4_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM1 Channel 5 Settings ---- */
#define SPC5_GTM_TIM1_USE_CHANNEL5                             TRUE
#define SPC5_GTM_TIM1_CHANNEL5_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM1_CHANNEL5_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM1_CHANNEL5_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM1_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL5_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM1_CHANNEL5_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL5_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM1_CHANNEL5_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL5_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM1_CHANNEL5_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL5_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM1_CHANNEL5_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM1 Channel 6 Settings ---- */
#define SPC5_GTM_TIM1_USE_CHANNEL6                             TRUE
#define SPC5_GTM_TIM1_CHANNEL6_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM1_CHANNEL6_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM1_CHANNEL6_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM1_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL6_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM1_CHANNEL6_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL6_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM1_CHANNEL6_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL6_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM1_CHANNEL6_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL6_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM1_CHANNEL6_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM1 Channel 7 Settings ---- */
#define SPC5_GTM_TIM1_USE_CHANNEL7                             TRUE
#define SPC5_GTM_TIM1_CHANNEL7_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM1_CHANNEL7_INT_NEW_VALUE_ENABLED           TRUE
#define SPC5_GTM_TIM1_CHANNEL7_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM1_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL7_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM1_CHANNEL7_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL7_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM1_CHANNEL7_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL7_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM1_CHANNEL7_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM1_CHANNEL7_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM1_CHANNEL7_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM2 Settings ---- */

/* ---- TIM2 Channel 0 Settings ---- */
#define SPC5_GTM_TIM2_USE_CHANNEL0                             TRUE
#define SPC5_GTM_TIM2_CHANNEL0_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM2_CHANNEL0_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM2_CHANNEL0_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM2_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL0_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM2_CHANNEL0_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL0_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM2_CHANNEL0_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL0_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM2_CHANNEL0_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL0_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM2_CHANNEL0_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM2 Channel 1 Settings ---- */
#define SPC5_GTM_TIM2_USE_CHANNEL1                             TRUE
#define SPC5_GTM_TIM2_CHANNEL1_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM2_CHANNEL1_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM2_CHANNEL1_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM2_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL1_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM2_CHANNEL1_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL1_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM2_CHANNEL1_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL1_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM2_CHANNEL1_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL1_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM2_CHANNEL1_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM2 Channel 2 Settings ---- */
#define SPC5_GTM_TIM2_USE_CHANNEL2                             TRUE
#define SPC5_GTM_TIM2_CHANNEL2_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM2_CHANNEL2_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM2_CHANNEL2_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM2_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL2_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM2_CHANNEL2_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL2_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM2_CHANNEL2_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL2_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM2_CHANNEL2_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL2_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM2_CHANNEL2_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM2 Channel 3 Settings ---- */
#define SPC5_GTM_TIM2_USE_CHANNEL3                             TRUE
#define SPC5_GTM_TIM2_CHANNEL3_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM2_CHANNEL3_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM2_CHANNEL3_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM2_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL3_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM2_CHANNEL3_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL3_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM2_CHANNEL3_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL3_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM2_CHANNEL3_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL3_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM2_CHANNEL3_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM2 Channel 4 Settings ---- */
#define SPC5_GTM_TIM2_USE_CHANNEL4                             TRUE
#define SPC5_GTM_TIM2_CHANNEL4_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM2_CHANNEL4_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM2_CHANNEL4_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM2_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL4_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM2_CHANNEL4_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL4_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM2_CHANNEL4_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL4_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM2_CHANNEL4_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL4_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM2_CHANNEL4_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM2 Channel 5 Settings ---- */
#define SPC5_GTM_TIM2_USE_CHANNEL5                             TRUE
#define SPC5_GTM_TIM2_CHANNEL5_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM2_CHANNEL5_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM2_CHANNEL5_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM2_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL5_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM2_CHANNEL5_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL5_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM2_CHANNEL5_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL5_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM2_CHANNEL5_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL5_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM2_CHANNEL5_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM2 Channel 6 Settings ---- */
#define SPC5_GTM_TIM2_USE_CHANNEL6                             TRUE
#define SPC5_GTM_TIM2_CHANNEL6_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM2_CHANNEL6_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM2_CHANNEL6_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM2_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL6_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM2_CHANNEL6_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL6_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM2_CHANNEL6_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL6_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM2_CHANNEL6_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL6_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM2_CHANNEL6_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM2 Channel 7 Settings ---- */
#define SPC5_GTM_TIM2_USE_CHANNEL7                             TRUE
#define SPC5_GTM_TIM2_CHANNEL7_IRQ_MODE                        SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM2_CHANNEL7_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM2_CHANNEL7_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM2_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL7_INT_TIMEOUT_ENABLED             TRUE
#define SPC5_GTM_TIM2_CHANNEL7_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL7_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM2_CHANNEL7_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL7_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM2_CHANNEL7_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM2_CHANNEL7_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM2_CHANNEL7_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM3 Settings ---- */

/* ---- TIM3 Channel 0 Settings ---- */
#define SPC5_GTM_TIM3_USE_CHANNEL0                             FALSE
#define SPC5_GTM_TIM3_CHANNEL0_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM3_CHANNEL0_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM3_CHANNEL0_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM3_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL0_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM3_CHANNEL0_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL0_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM3_CHANNEL0_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL0_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM3_CHANNEL0_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL0_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM3_CHANNEL0_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM3 Channel 1 Settings ---- */
#define SPC5_GTM_TIM3_USE_CHANNEL1                             FALSE
#define SPC5_GTM_TIM3_CHANNEL1_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM3_CHANNEL1_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM3_CHANNEL1_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM3_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL1_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM3_CHANNEL1_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL1_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM3_CHANNEL1_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL1_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM3_CHANNEL1_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL1_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM3_CHANNEL1_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM3 Channel 2 Settings ---- */
#define SPC5_GTM_TIM3_USE_CHANNEL2                             FALSE
#define SPC5_GTM_TIM3_CHANNEL2_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM3_CHANNEL2_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM3_CHANNEL2_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM3_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL2_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM3_CHANNEL2_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL2_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM3_CHANNEL2_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL2_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM3_CHANNEL2_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL2_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM3_CHANNEL2_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM3 Channel 3 Settings ---- */
#define SPC5_GTM_TIM3_USE_CHANNEL3                             FALSE
#define SPC5_GTM_TIM3_CHANNEL3_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM3_CHANNEL3_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM3_CHANNEL3_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM3_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL3_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM3_CHANNEL3_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL3_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM3_CHANNEL3_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL3_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM3_CHANNEL3_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL3_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM3_CHANNEL3_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM3 Channel 4 Settings ---- */
#define SPC5_GTM_TIM3_USE_CHANNEL4                             FALSE
#define SPC5_GTM_TIM3_CHANNEL4_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM3_CHANNEL4_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM3_CHANNEL4_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM3_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL4_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM3_CHANNEL4_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL4_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM3_CHANNEL4_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL4_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM3_CHANNEL4_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL4_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM3_CHANNEL4_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM3 Channel 5 Settings ---- */
#define SPC5_GTM_TIM3_USE_CHANNEL5                             FALSE
#define SPC5_GTM_TIM3_CHANNEL5_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM3_CHANNEL5_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM3_CHANNEL5_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM3_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL5_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM3_CHANNEL5_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL5_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM3_CHANNEL5_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL5_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM3_CHANNEL5_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL5_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM3_CHANNEL5_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM3 Channel 6 Settings ---- */
#define SPC5_GTM_TIM3_USE_CHANNEL6                             FALSE
#define SPC5_GTM_TIM3_CHANNEL6_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM3_CHANNEL6_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM3_CHANNEL6_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM3_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL6_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM3_CHANNEL6_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL6_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM3_CHANNEL6_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL6_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM3_CHANNEL6_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL6_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM3_CHANNEL6_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM3 Channel 7 Settings ---- */
#define SPC5_GTM_TIM3_USE_CHANNEL7                             FALSE
#define SPC5_GTM_TIM3_CHANNEL7_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM3_CHANNEL7_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM3_CHANNEL7_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM3_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL7_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM3_CHANNEL7_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL7_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM3_CHANNEL7_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL7_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM3_CHANNEL7_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM3_CHANNEL7_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM3_CHANNEL7_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM4 Settings ---- */

/* ---- TIM4 Channel 0 Settings ---- */
#define SPC5_GTM_TIM4_USE_CHANNEL0                             FALSE
#define SPC5_GTM_TIM4_CHANNEL0_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM4_CHANNEL0_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM4_CHANNEL0_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM4_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL0_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM4_CHANNEL0_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL0_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM4_CHANNEL0_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL0_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM4_CHANNEL0_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL0_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM4_CHANNEL0_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM4 Channel 1 Settings ---- */
#define SPC5_GTM_TIM4_USE_CHANNEL1                             FALSE
#define SPC5_GTM_TIM4_CHANNEL1_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM4_CHANNEL1_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM4_CHANNEL1_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM4_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL1_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM4_CHANNEL1_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL1_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM4_CHANNEL1_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL1_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM4_CHANNEL1_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL1_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM4_CHANNEL1_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM4 Channel 2 Settings ---- */
#define SPC5_GTM_TIM4_USE_CHANNEL2                             FALSE
#define SPC5_GTM_TIM4_CHANNEL2_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM4_CHANNEL2_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM4_CHANNEL2_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM4_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL2_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM4_CHANNEL2_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL2_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM4_CHANNEL2_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL2_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM4_CHANNEL2_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL2_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM4_CHANNEL2_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM4 Channel 3 Settings ---- */
#define SPC5_GTM_TIM4_USE_CHANNEL3                             FALSE
#define SPC5_GTM_TIM4_CHANNEL3_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM4_CHANNEL3_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM4_CHANNEL3_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM4_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL3_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM4_CHANNEL3_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL3_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM4_CHANNEL3_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL3_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM4_CHANNEL3_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL3_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM4_CHANNEL3_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM4 Channel 4 Settings ---- */
#define SPC5_GTM_TIM4_USE_CHANNEL4                             FALSE
#define SPC5_GTM_TIM4_CHANNEL4_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM4_CHANNEL4_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM4_CHANNEL4_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM4_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL4_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM4_CHANNEL4_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL4_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM4_CHANNEL4_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL4_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM4_CHANNEL4_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL4_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM4_CHANNEL4_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM4 Channel 5 Settings ---- */
#define SPC5_GTM_TIM4_USE_CHANNEL5                             FALSE
#define SPC5_GTM_TIM4_CHANNEL5_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM4_CHANNEL5_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM4_CHANNEL5_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM4_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL5_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM4_CHANNEL5_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL5_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM4_CHANNEL5_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL5_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM4_CHANNEL5_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL5_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM4_CHANNEL5_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM4 Channel 6 Settings ---- */
#define SPC5_GTM_TIM4_USE_CHANNEL6                             FALSE
#define SPC5_GTM_TIM4_CHANNEL6_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM4_CHANNEL6_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM4_CHANNEL6_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM4_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL6_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM4_CHANNEL6_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL6_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM4_CHANNEL6_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL6_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM4_CHANNEL6_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL6_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM4_CHANNEL6_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM4 Channel 7 Settings ---- */
#define SPC5_GTM_TIM4_USE_CHANNEL7                             FALSE
#define SPC5_GTM_TIM4_CHANNEL7_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM4_CHANNEL7_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM4_CHANNEL7_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM4_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL7_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM4_CHANNEL7_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL7_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM4_CHANNEL7_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL7_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM4_CHANNEL7_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM4_CHANNEL7_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM4_CHANNEL7_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM5 Settings ---- */

/* ---- TIM5 Channel 0 Settings ---- */
#define SPC5_GTM_TIM5_USE_CHANNEL0                             FALSE
#define SPC5_GTM_TIM5_CHANNEL0_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM5_CHANNEL0_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM5_CHANNEL0_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM5_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL0_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM5_CHANNEL0_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL0_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM5_CHANNEL0_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL0_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM5_CHANNEL0_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL0_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM5_CHANNEL0_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM5 Channel 1 Settings ---- */
#define SPC5_GTM_TIM5_USE_CHANNEL1                             FALSE
#define SPC5_GTM_TIM5_CHANNEL1_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM5_CHANNEL1_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM5_CHANNEL1_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM5_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL1_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM5_CHANNEL1_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL1_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM5_CHANNEL1_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL1_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM5_CHANNEL1_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL1_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM5_CHANNEL1_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM5 Channel 2 Settings ---- */
#define SPC5_GTM_TIM5_USE_CHANNEL2                             FALSE
#define SPC5_GTM_TIM5_CHANNEL2_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM5_CHANNEL2_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM5_CHANNEL2_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM5_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL2_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM5_CHANNEL2_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL2_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM5_CHANNEL2_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL2_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM5_CHANNEL2_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL2_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM5_CHANNEL2_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM5 Channel 3 Settings ---- */
#define SPC5_GTM_TIM5_USE_CHANNEL3                             FALSE
#define SPC5_GTM_TIM5_CHANNEL3_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM5_CHANNEL3_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM5_CHANNEL3_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM5_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL3_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM5_CHANNEL3_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL3_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM5_CHANNEL3_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL3_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM5_CHANNEL3_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL3_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM5_CHANNEL3_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM5 Channel 4 Settings ---- */
#define SPC5_GTM_TIM5_USE_CHANNEL4                             FALSE
#define SPC5_GTM_TIM5_CHANNEL4_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM5_CHANNEL4_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM5_CHANNEL4_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM5_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL4_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM5_CHANNEL4_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL4_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM5_CHANNEL4_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL4_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM5_CHANNEL4_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL4_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM5_CHANNEL4_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM5 Channel 5 Settings ---- */
#define SPC5_GTM_TIM5_USE_CHANNEL5                             FALSE
#define SPC5_GTM_TIM5_CHANNEL5_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM5_CHANNEL5_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM5_CHANNEL5_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM5_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL5_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM5_CHANNEL5_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL5_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM5_CHANNEL5_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL5_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM5_CHANNEL5_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL5_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM5_CHANNEL5_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM5 Channel 6 Settings ---- */
#define SPC5_GTM_TIM5_USE_CHANNEL6                             FALSE
#define SPC5_GTM_TIM5_CHANNEL6_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM5_CHANNEL6_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM5_CHANNEL6_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM5_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL6_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM5_CHANNEL6_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL6_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM5_CHANNEL6_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL6_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM5_CHANNEL6_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL6_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM5_CHANNEL6_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */
/* ---- TIM5 Channel 7 Settings ---- */
#define SPC5_GTM_TIM5_USE_CHANNEL7                             FALSE
#define SPC5_GTM_TIM5_CHANNEL7_IRQ_MODE                        SPC5_GTM_TIM_IRQ_MODE_LEVEL
#define SPC5_GTM_TIM5_CHANNEL7_INT_NEW_VALUE_ENABLED           FALSE
#define SPC5_GTM_TIM5_CHANNEL7_INT_NEW_VALUE_MODE              SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED  FALSE
#define SPC5_GTM_TIM5_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE     SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL7_INT_TIMEOUT_ENABLED             FALSE
#define SPC5_GTM_TIM5_CHANNEL7_INT_TIMEOUT_MODE                SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL7_INT_SMU_COUNTER_ENABLED         FALSE
#define SPC5_GTM_TIM5_CHANNEL7_INT_SMU_COUNTER_MODE            SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL7_INT_EDGE_COUNTER_ENABLED        FALSE
#define SPC5_GTM_TIM5_CHANNEL7_INT_EDGE_COUNTER_MODE           SPC5_GTM_TIM_INT_MODE_NORMAL
#define SPC5_GTM_TIM5_CHANNEL7_INT_GLITCH_ENABLED              FALSE
#define SPC5_GTM_TIM5_CHANNEL7_INT_GLITCH_MODE                 SPC5_GTM_TIM_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- TIM0 Interrupts callbacks ---- */
extern GTM_TIM_Channel_Callbacks *gtm_tim0_callbacks[SPC5_GTM_TIM_CHANNELS];
extern GTM_TIM_Channel_Callbacks gtm_tim0_channel0_callbacks;
void tdn_new_value_cb(GTM_TIMDriver *timd, uint8_T channel);
void spark_cb_ch0(GTM_TIMDriver *timd, uint8_T channel);
void spark_cb_ch1(GTM_TIMDriver *timd, uint8_T channel);
extern GTM_TIM_Channel_Callbacks gtm_tim0_channel1_callbacks;
extern GTM_TIM_Channel_Callbacks gtm_tim0_channel2_callbacks;
extern GTM_TIM_Channel_Callbacks gtm_tim0_channel3_callbacks;
extern GTM_TIM_Channel_Callbacks gtm_tim0_channel4_callbacks;
extern GTM_TIM_Channel_Callbacks gtm_tim0_channel5_callbacks;
extern GTM_TIM_Channel_Callbacks gtm_tim0_channel6_callbacks;
extern GTM_TIM_Channel_Callbacks gtm_tim0_channel7_callbacks;
/* ---- TIM1 Interrupts callbacks ---- */
extern GTM_TIM_Channel_Callbacks *gtm_tim1_callbacks[SPC5_GTM_TIM_CHANNELS];
extern GTM_TIM_Channel_Callbacks gtm_tim1_channel0_callbacks;
void cyl0_edge_cb(GTM_TIMDriver *timd, uint8_t channel);
void cyl0_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim1_channel1_callbacks;
void cyl1_edge_cb(GTM_TIMDriver *timd, uint8_t channel);
void cyl1_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim1_channel2_callbacks;
void cyl2_edge_cb(GTM_TIMDriver *timd, uint8_t channel);
void cyl2_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim1_channel3_callbacks;
void cyl3_edge_cb(GTM_TIMDriver *timd, uint8_t channel);
void cyl3_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim1_channel4_callbacks;
void cyl4_edge_cb(GTM_TIMDriver *timd, uint8_t channel);
void cyl4_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim1_channel5_callbacks;
void cyl5_edge_cb(GTM_TIMDriver *timd, uint8_t channel);
void cyl5_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim1_channel6_callbacks;
void cyl6_edge_cb(GTM_TIMDriver *timd, uint8_t channel);
void cyl6_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim1_channel7_callbacks;
void cyl7_edge_cb(GTM_TIMDriver *timd, uint8_t channel);
void cyl7_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
/* ---- TIM2 Interrupts callbacks ---- */
extern GTM_TIM_Channel_Callbacks *gtm_tim2_callbacks[SPC5_GTM_TIM_CHANNELS];
extern GTM_TIM_Channel_Callbacks gtm_tim2_channel0_callbacks;
void cyl0_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim2_channel1_callbacks;
void cyl1_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim2_channel2_callbacks;
void cyl2_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim2_channel3_callbacks;
void cyl3_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim2_channel4_callbacks;
void cyl4_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim2_channel5_callbacks;
void cyl5_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim2_channel6_callbacks;
void cyl6_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);
extern GTM_TIM_Channel_Callbacks gtm_tim2_channel7_callbacks;
void cyl7_epws_timeout_cb(GTM_TIMDriver *timd, uint8_t channel);

/*lint +e621*/
#endif /* _GTM_TIM_CFG_H_ */
/** @} */
