;/****************************************************************************
;*
;* Copyright © 2019-2020 STMicroelectronics - All Rights Reserved
;*
;* License terms: STMicroelectronics Proprietary in accordance with licensing
;* terms SLA0089 at www.st.com
;* 
;* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
;* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
;*
;* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
;*****************************************************************************/

; 1) prepare assembler for MCS memory
; -------------------------------------
.include "mcs24_2.inc"

; 2) define some constants
; -------------------------
.define MCS_CHx_DISABLE_MASK $FFFFE

.register PMOS_REG       R0
.register NMOS_REG       R1
 
;
; MCS Channel_0 defines
;
.define MCS2_PMOS0_4_PORT_IDX              $0
.define MCS2_NMOS0_4_PORT_IDX              $1
.define MCS2_PMOS1_5_PORT_IDX              $2
.define MCS2_NMOS1_5_PORT_IDX              $3
.define MCS2_PMOS2_6_PORT_IDX              $4
.define MCS2_NMOS2_6_PORT_IDX              $5
.define MCS2_PMOS3_7_PORT_IDX              $6
.define MCS2_NMOS3_7_PORT_IDX              $7

.define MCS2_IPRI0_PORT_IDX               $8   ;ATOM0_4
.define MCS2_ISEC0_PORT_IDX               $8   ; Same IPRI0 ATOM (ATOM0_4)
.define MCS2_IPRI1_PORT_IDX               $9   ;ATOM0_6
.define MCS2_ISEC1_PORT_IDX               $9   ; Same IPRI1 ATOM (ATOM0_6)
    
.define MCS2_xMOS_CYL0_ADDR                BRC_WRADDR0
.define MCS2_xMOS_CYL2_ADDR                BRC_WRADDR2
.define MCS2_xMOS_CYL4_ADDR                BRC_WRADDR4
.define MCS2_xMOS_CYL6_ADDR                BRC_WRADDR6

.define MCS2_xMOS_CYL1_ADDR                BRC_WRADDR1
.define MCS2_xMOS_CYL3_ADDR                BRC_WRADDR3
.define MCS2_xMOS_CYL5_ADDR                BRC_WRADDR5
.define MCS2_xMOS_CYL7_ADDR                BRC_WRADDR7

.define MCS2_IPRI0_ADDR                    BRC_WRADDR8
.define MCS2_IPRI1_ADDR                    BRC_WRADDR9

.define NMOS0_START_ADDR                   MCS2_WRADDR10
.define NMOS1_START_ADDR                   MCS2_WRADDR11
.define NMOS2_START_ADDR                   MCS2_WRADDR12
.define NMOS3_START_ADDR                   MCS2_WRADDR13
.define NMOS4_START_ADDR                   MCS2_WRADDR14
.define NMOS5_START_ADDR                   MCS2_WRADDR15
.define NMOS6_START_ADDR                   MCS2_WRADDR16
.define NMOS7_START_ADDR                   MCS2_WRADDR17


.define MCS2_CH0_TRIGGER_BIT               $1       ;TRG.0
.define MCS2_CH1_TRIGGER_BIT               $2       ;TRG.1
.define MCS2_CH2_TRIGGER_BIT               $4       ;TRG.2
.define MCS2_CH3_TRIGGER_BIT               $8       ;TRG.3
.define MCS2_CH4_TRIGGER_BIT               $10      ;TRG.4
.define MCS2_CH5_TRIGGER_BIT               $20      ;TRG.5
.define MCS2_CH6_TRIGGER_BIT               $40      ;TRG.6
.define MCS2_CH7_TRIGGER_BIT               $80      ;TRG.7

.define IPRI0_ADC_RECON_REQ                $100     ; TRG.8
.define ISEC0_ADC_RECON_REQ                $200     ; TRG.9
.define IPRI1_ADC_RECON_REQ                $400     ; TR.10
.define ISEC1_ADC_RECON_REQ                $800     ; TR.11

.define CPU_REOPEN_PMOS0_4_TRG_BIT         $1000    ; TR.12
.define CPU_REOPEN_PMOS1_5_TRG_BIT         $2000    ; TR.13
.define CPU_REOPEN_PMOS2_6_TRG_BIT         $4000    ; TR.14
.define CPU_REOPEN_PMOS3_7_TRG_BIT         $8000    ; TR.15

.define IPRI0_ADC_PERIOD  10
.define ISEC0_ADC_PERIOD  10
.define IPRI1_ADC_PERIOD  10
.define ISEC1_ADC_PERIOD  10
.define ISEC_ADC_DURATION 1500

.define MOS0_4_OPEN                        1
.define MOS0_4_CLOSE                       0
.define MOS1_5_OPEN                        1
.define MOS1_5_CLOSE                       0
.define MOS2_6_OPEN                        1
.define MOS2_6_CLOSE                       0
.define MOS3_7_OPEN                        1
.define MOS3_7_CLOSE                       0

.define PMOS_REOPEN_DELAY 50
;=====================================================================
; STATUS FLAGS
;=====================================================================  
.define IPRI_FAULT                          0x999999

; 3) initialize reset vectors of MCS channels 0
; ----------------------------------------------------
.org 0x0
jmp tsk0_init
jmp tsk1_init
jmp tsk2_init
jmp tsk3_init
jmp tsk4_init
jmp tsk5_init
jmp tsk6_init
jmp tsk7_init

; 4) allocate and initialize memory variables
; -------------------------------------------



; 5) allocate stack frames (each task has 16 memory locations)
; ------------------------------------------------------------
; MCS channels stack memory allocation
;
tsk0_stack:
.org (tsk0_stack+0x40)
tsk1_stack:
.org (tsk1_stack+0x40)
tsk2_stack:
.org (tsk2_stack+0x40)
tsk3_stack:
.org (tsk3_stack+0x40)
tsk4_stack:
.org (tsk4_stack+0x40)
tsk5_stack:
.org (tsk5_stack+0x40)
tsk6_stack:
.org (tsk6_stack+0x40)
tsk7_stack:
.org (tsk7_stack+0x40)

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
; 6) program entry for MCS-channel 0
; 
; ----------------------------------
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;;;;;;;;;;;;;;
; xMOS0-4
;;;;;;;;;;;;;;;
tsk0_init:
    movl R0, 0
	movl R1, 0
	movl R2, 0
	movl R3, 0
	movl R4, 0
	movl R5, 0
	movl R6, 0
	movl R7, (tsk0_stack-4)

TASK0_START:
    movl  R0, MCS2_CH0_TRIGGER_BIT
    wurm  R0, STRG, MCS2_CH0_TRIGGER_BIT
    jbs STA CWT tsk0_init
    movl  CTRG, MCS2_CH0_TRIGGER_BIT                 ; Clear start trigger

MOS0_4:
    nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4
    jbs STA SAT MOS4                                  ; SAT = 1 new cyl4 request
    nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0
    jbs STA SAT MOS0                                  ; SAT = 1 new cyl0 request
    jmp MOS0_4

MOS0:
   atul R0, 0xFFFFFF
   jbc STA Z MOS0_WAIT_PARAM
   atul R1, 0xFFFFFF
   jbs STA Z MOS0_4
MOS0_WAIT_PARAM:  
   ard R3, R5, MCS2_xMOS_CYL0_ADDR   ; R3 = pmos_duration, R5 = nmos_delay
   jmp PMOS0_4

MOS4:
   atul R0, 0xFFFFFF
   jbc STA Z MOS4_WAIT_PARAM
   atul R1, 0xFFFFFF
   jbs STA Z MOS0_4
MOS4_WAIT_PARAM:
   ard R3, R5, MCS2_xMOS_CYL4_ADDR   ; R3 = pmos_duration, R5 = nmos_delay

PMOS0_4:
;CLOSE MOS signals
; Stop NMOS 
    movl ACB, MOS0_4_CLOSE                           
    awr R0, R1, MCS2_NMOS0_4_PORT_IDX
; Stop PMOS     
;    movl ACB, MOS0_4_CLOSE
;    awr R0, R1, MCS2_PMOS0_4_PORT_IDX

    movl STA, 0x000003   ; raise interrupt ADC ION setup

    movl ACB, MOS0_4_OPEN                             ; Start PMOS
    awr R0, R1, MCS2_PMOS0_4_PORT_IDX                 ; PORT 0 of MCS2 ATOM2_0
    mov PMOS_REG, TBU_TS2

    add PMOS_REG, R3
    jbs STA CY PMOS0_4_LOOP_WA
    jmp PMOS0_4_LOOP
PMOS0_4_LOOP_WA:
    nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0
    jbs STA SAT PMOS0_4_STOP_TRIGGER
    nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4
    jbs STA SAT PMOS0_4_STOP_TRIGGER
    atu PMOS_REG, TBU_TS2 
    jbs STA CY PMOS0_4_LOOP_WA
PMOS0_4_LOOP:
    nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0
    jbs STA SAT PMOS0_4_STOP_TRIGGER
    nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4
    jbs STA SAT PMOS0_4_STOP_TRIGGER
    atu TBU_TS2, PMOS_REG
    jbc STA CY PMOS0_4_STOP_DELAY
    jmp PMOS0_4_LOOP

PMOS0_4_STOP_TRIGGER:
    movl ACB, MOS0_4_CLOSE                            ; Stop PMOS
    awr R0, R1, MCS2_PMOS0_4_PORT_IDX                 ; PORT 0 of MCS2 ATOM2_0
    jmp NMOS0_4_ENABLE

PMOS0_4_STOP_DELAY:    
    movl ACB, MOS0_4_CLOSE                            ; Stop PMOS
    awr R0, R1, MCS2_PMOS0_4_PORT_IDX                 ; PORT 0 of MCS2 ATOM2_0
  
    mov NMOS_REG, TBU_TS2
    add NMOS_REG, R5                                  ;NMOS_Delay
    jbs STA CY NMOS0_4_WAIT_EVENT_WA
    jmp NMOS0_4_WAIT_EVENT
NMOS0_4_WAIT_EVENT_WA:
    nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0
    jbs STA SAT NMOS0_4_ENABLE
    nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4
    jbs STA SAT NMOS0_4_ENABLE
    atu NMOS_REG, TBU_TS2
    jbs STA CY NMOS0_4_WAIT_EVENT_WA
NMOS0_4_WAIT_EVENT:                                   ;NMOS Enable with the first event between NMOS_delay or Command-Out close trigger
    nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0
    jbs STA SAT NMOS0_4_ENABLE
    nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4
    jbs STA SAT NMOS0_4_ENABLE
    atu TBU_TS2, NMOS_REG
    jbs STA CY NMOS0_4_WAIT_EVENT
    
NMOS0_4_ENABLE:
    movl ACB, MOS0_4_OPEN                             ; Start NMOS 
    awr R0, R1, MCS2_NMOS0_4_PORT_IDX                 ; PORT 1 of MCS2 ATOM2_1
            
    
PMOS0_4_WAIT_TRG_REOPEN:
    btl STRG, CPU_REOPEN_PMOS0_4_TRG_BIT                ; (A & B) if TRUE Z=0; if FALSE Z=1 
    jbs STA Z PMOS0_4_WAIT_TRG_REOPEN                   ; loop until the TRG bit is not set
    ; delay before opening
    movl R0, PMOS_REOPEN_DELAY
    add R0, TBU_TS2
    jbs STA CY PMOS0_4_REOPEN_DELAY_WA
    jmp PMOS0_4_REOPEN_DELAY

PMOS0_4_REOPEN_DELAY_WA:
    atu R0, tbu_ts2
    jbc STA CY PMOS0_4_REOPEN_DELAY_WA

PMOS0_4_REOPEN_DELAY:
    atu TBU_TS2, R0
    jbc STA CY PMOS0_4_REOPEN
    jmp PMOS0_4_REOPEN_DELAY

PMOS0_4_REOPEN:
    movl CTRG, CPU_REOPEN_PMOS0_4_TRG_BIT               ; clean trigger bit
    movl ACB, MOS0_4_OPEN
    awr R0, R1, MCS2_PMOS0_4_PORT_IDX
     
    jmp MOS0_4
       
TASK0_DONE:
; Restart again
    ;movl STA, 0x000003   ; raise interrupt
    jmp   TASK0_START

; Should not get here
    andl STA MCS_CHx_DISABLE_MASK                     ; Disable MCS Channel

;;;;;;;;;;;;;;;
; xMOS1-5
;;;;;;;;;;;;;;;
tsk1_init:
    movl R0, 0
	movl R1, 0
	movl R2, 0
	movl R3, 0
	movl R4, 0
	movl R5, 0
	movl R6, 0
	movl R7, (tsk1_stack-4)

TASK1_START:
    movl  R0, MCS2_CH1_TRIGGER_BIT
    wurm  R0, STRG, MCS2_CH1_TRIGGER_BIT
    jbs STA CWT tsk1_init
    movl  CTRG, MCS2_CH1_TRIGGER_BIT                  ; Clear start trigger

MOS1_5:
    nard R0, R1, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1
    jbs STA SAT MOS1                                  ; SAT = 1 new cyl2 request
    nard R0, R1, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5
    jbs STA SAT MOS5                                  ; SAT = 1 new cyl5 request
    jmp MOS1_5

MOS1:
   atul R0, 0xFFFFFF
   jbc STA Z MOS1_WAIT_PARAM
   atul R1, 0xFFFFFF
   jbs STA Z MOS1_5
MOS1_WAIT_PARAM: 
   ard R3, R5, MCS2_xMOS_CYL1_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration
   jmp PMOS1_5

MOS5:
   atul R0, 0xFFFFFF
   jbc STA Z MOS5_WAIT_PARAM
   atul R1, 0xFFFFFF
   jbs STA Z MOS1_5
MOS5_WAIT_PARAM:
   ard R3, R5, MCS2_xMOS_CYL5_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration

PMOS1_5:
;CLOSE MOS signals
; Stop NMOS 
    movl ACB, MOS1_5_CLOSE                           
    awr R0, R1, MCS2_NMOS1_5_PORT_IDX
; Stop PMOS     
;    movl ACB, MOS1_5_CLOSE
;    awr R0, R1, MCS2_PMOS1_5_PORT_IDX
    
    movl STA, 0x000003                                ; raise interrupt adc setup

    movl ACB, MOS1_5_OPEN                             ; Start PMOS
    awr R0, R1, MCS2_PMOS1_5_PORT_IDX                 ; PORT 2 of MCS2 ATOM2_2
 
    mov PMOS_REG, TBU_TS2
    add PMOS_REG, R3
    jbs STA CY PMOS1_5_LOOP_WA
    jmp PMOS1_5_LOOP
PMOS1_5_LOOP_WA:
    nard R2, R1, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1
    jbs STA SAT PMOS1_5_STOP_TRIGGER
    nard R2, R1, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5
    jbs STA SAT PMOS1_5_STOP_TRIGGER
    atu PMOS_REG, TBU_TS2 
    jbs STA CY PMOS1_5_LOOP_WA
 PMOS1_5_LOOP:
    nard R2, R1, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1
    jbs STA SAT PMOS1_5_STOP_TRIGGER
    nard R2, R1, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5
    jbs STA SAT PMOS1_5_STOP_TRIGGER
    atu TBU_TS2, PMOS_REG
    jbc STA CY PMOS1_5_STOP_DELAY
    
    jmp PMOS1_5_LOOP

PMOS1_5_STOP_TRIGGER:
    movl ACB, MOS1_5_CLOSE                            ; Stop PMOS
    awr R0, R1, MCS2_PMOS1_5_PORT_IDX                 ; PORT 2 of MCS2 ATOM2_2
    jmp NMOS1_5_ENABLE

PMOS1_5_STOP_DELAY:
    movl ACB, MOS1_5_CLOSE                            ; Stop PMOS
    awr R0, R1, MCS2_PMOS1_5_PORT_IDX                 ; PORT 2 of MCS2 ATOM2_2

    mov NMOS_REG, TBU_TS2
    add NMOS_REG, R5                                  ;NMOS_Delay
    jbs STA CY NMOS1_5_WAIT_EVENT_WA
    jmp NMOS1_5_WAIT_EVENT
NMOS1_5_WAIT_EVENT_WA:
    nard R0, R2, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1
    jbs STA SAT NMOS1_5_ENABLE
    nard R0, R2, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5
    jbs STA SAT NMOS1_5_ENABLE
    atu NMOS_REG, TBU_TS2 
    jbs STA CY NMOS1_5_WAIT_EVENT_WA    
NMOS1_5_WAIT_EVENT:
    nard R0, R2, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1
    jbs STA SAT NMOS1_5_ENABLE
    nard R0, R2, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5
    jbs STA SAT NMOS1_5_ENABLE
    atu TBU_TS2, NMOS_REG
    jbc STA CY NMOS1_5_ENABLE
    jmp NMOS1_5_WAIT_EVENT
    
NMOS1_5_ENABLE:
    movl ACB, MOS1_5_OPEN                             ; Start NMOS
    awr R0, R1, MCS2_NMOS1_5_PORT_IDX                 ; PORT 3 of MCS2 ATOM2_3

    ;movl CTRG, CPU_REOPEN_PMOS1_5_TRG_BIT              ; clean trigger bit
PMOS1_5_WAIT_TRG_REOPEN:
    btl STRG, CPU_REOPEN_PMOS1_5_TRG_BIT                ; (A & B) if TRUE Z=0; if FALSE Z=1 
    jbs STA Z PMOS1_5_WAIT_TRG_REOPEN                   ; loop until the TRG bit is not set
    ; delay before opening
    movl R0, PMOS_REOPEN_DELAY
    add R0, TBU_TS2
    jbs STA CY PMOS1_5_REOPEN_DELAY_WA
    jmp PMOS1_5_REOPEN_DELAY

PMOS1_5_REOPEN_DELAY_WA:
    atu R0, tbu_ts2
    jbc STA CY PMOS1_5_REOPEN_DELAY_WA

PMOS1_5_REOPEN_DELAY:
    atu TBU_TS2, R0
    jbc STA CY PMOS1_5_REOPEN
    jmp PMOS1_5_REOPEN_DELAY

PMOS1_5_REOPEN:
    movl CTRG, CPU_REOPEN_PMOS1_5_TRG_BIT               ; clean trigger bit
    movl ACB, MOS1_5_OPEN
    awr R0, R1, MCS2_PMOS1_5_PORT_IDX
       
    jmp MOS1_5
       
TASK1_DONE:
; Restart again
    ;movl STA, 0x000003   ; raise interrupt
    jmp   TASK1_START

; Should not get here
    andl STA MCS_CHx_DISABLE_MASK                     ; Disable MCS Channel
    
;;;;;;;;;;;;;;;
; xMOS2-6
;;;;;;;;;;;;;;;
tsk2_init:
    movl R0, 0
	movl R1, 0
	movl R2, 0
	movl R3, 0
	movl R4, 0
	movl R5, 0
	movl R6, 0
	movl R7, (tsk2_stack-4)

TASK2_START:
    movl  R0, MCS2_CH2_TRIGGER_BIT
    wurm  R0, STRG, MCS2_CH2_TRIGGER_BIT
    jbs STA CWT tsk2_init
    movl  CTRG, MCS2_CH2_TRIGGER_BIT                 ; Clear start trigger

MOS2_6:
    nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT MOS2                                  ; SAT = 1 new cyl1 request
    nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST4
    jbs STA SAT MOS6                                  ; SAT = 1 new cyl4 request
    jmp MOS2_6

MOS2:
   atul R0, 0xFFFFFF
   jbc STA Z MOS2_WAIT_PARAM
   atul R1, 0xFFFFFF
   jbs STA Z MOS2_6
MOS2_WAIT_PARAM:
   ard R3, R5, MCS2_xMOS_CYL2_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration
   jmp PMOS2_6

MOS6:
   atul R0, 0xFFFFFF
   jbc STA Z MOS6_WAIT_PARAM
   atul R1, 0xFFFFFF
   jbs STA Z MOS2_6
MOS6_WAIT_PARAM:
   ard R3, R5, MCS2_xMOS_CYL6_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration

PMOS2_6:
;CLOSE MOS signals
; Stop NMOS 
    movl ACB, MOS2_6_CLOSE                           
    awr R0, R1, MCS2_NMOS2_6_PORT_IDX
; Stop PMOS     
;    movl ACB, MOS2_6_CLOSE
;    awr R0, R1, MCS2_PMOS2_6_PORT_IDX
    
    movl STA, 0x000003                                ; raise interrupt - ADC ION setup


    movl ACB, MOS2_6_OPEN                             ; Start PMOS
    awr R0, R1, MCS2_PMOS2_6_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4
    mov PMOS_REG, TBU_TS2
    add PMOS_REG, R3
    jbs STA CY PMOS2_6_LOOP_WA
    jmp PMOS2_6_LOOP

PMOS2_6_LOOP_WA:    
    nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT PMOS2_6_STOP_TRIGGER
    nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST6
    jbs STA SAT PMOS2_6_STOP_TRIGGER
    ATU PMOS_REG, TBU_TS2
    jbs STA CY PMOS2_6_LOOP_WA                     ; loop until (current) TBU_TS2 < R1
PMOS2_6_LOOP:
    nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT PMOS2_6_STOP_TRIGGER
    nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST6
    jbs STA SAT PMOS2_6_STOP_TRIGGER
    ATU TBU_TS2, PMOS_REG
    jbc STA CY PMOS2_6_STOP_DELAY                     ; loop until (current) TBU_TS2 < R1
    jmp PMOS2_6_LOOP

PMOS2_6_STOP_TRIGGER:    
    movl ACB, MOS2_6_CLOSE                            ; Stop PMOS
    awr R0, R1, MCS2_PMOS2_6_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4
    jmp NMOS2_6_ENABLE
    
PMOS2_6_STOP_DELAY:
    movl ACB, MOS2_6_CLOSE                            ; Stop PMOS
    awr R0, R1, MCS2_PMOS2_6_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4    

    mov NMOS_REG, TBU_TS2
    add NMOS_REG, R5                                  ;NMOS_Delay
    jbs STA CY NMOS2_6_WAIT_EVENT_WA
    jmp NMOS2_6_WAIT_EVENT
NMOS2_6_WAIT_EVENT_WA:
    nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT NMOS2_6_ENABLE
    nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST6
    jbs STA SAT NMOS2_6_ENABLE
    atu NMOS_REG, TBU_TS2 
    jbs STA CY NMOS2_6_WAIT_EVENT_WA
NMOS2_6_WAIT_EVENT:
    nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT NMOS2_6_ENABLE
    nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST6
    jbs STA SAT NMOS2_6_ENABLE
    atu TBU_TS2, NMOS_REG
    jbc STA CY NMOS2_6_ENABLE
    jmp NMOS2_6_WAIT_EVENT    

NMOS2_6_ENABLE:
    movl ACB, MOS2_6_OPEN                             ; Start NMOS
    awr R0, R1, MCS2_NMOS2_6_PORT_IDX                 ; PORT 5 of MCS2 ATOM1_4

PMOS2_6_WAIT_TRG_REOPEN:
    btl STRG, CPU_REOPEN_PMOS2_6_TRG_BIT                ; (A & B) if TRUE Z=0; if FALSE Z=1 
    jbs STA Z PMOS2_6_WAIT_TRG_REOPEN                   ; loop until the TRG bit is not set
    ; delay before opening
    movl R0, PMOS_REOPEN_DELAY
    add R0, TBU_TS2
    jbs STA CY PMOS2_6_REOPEN_DELAY_WA
    jmp PMOS2_6_REOPEN_DELAY

PMOS2_6_REOPEN_DELAY_WA:
    atu R0, tbu_ts2
    jbc STA CY PMOS2_6_REOPEN_DELAY_WA

PMOS2_6_REOPEN_DELAY:
    atu TBU_TS2, R0
    jbc STA CY PMOS2_6_REOPEN
    jmp PMOS2_6_REOPEN_DELAY

PMOS2_6_REOPEN:
    movl CTRG, CPU_REOPEN_PMOS2_6_TRG_BIT                ; clean trigger bit
    movl ACB, MOS2_6_OPEN
    awr R0, R1, MCS2_PMOS2_6_PORT_IDX
        
    jmp MOS2_6
       
TASK2_DONE:
; Restart again
    ;movl STA, 0x000003   ; raise interrupt
    jmp   TASK2_START

; Should not get here
    andl STA MCS_CHx_DISABLE_MASK ; disable MCS

;;;;;;;;;;;;;;;
; xMOS3-7
;;;;;;;;;;;;;;;
tsk3_init:
    movl R0, 0
	movl R1, 0
	movl R2, 0
	movl R3, 0
	movl R4, 0
	movl R5, 0
	movl R6, 0
	movl R7, (tsk3_stack-4)

TASK3_START:
    movl  R0, MCS2_CH3_TRIGGER_BIT
    wurm  R0, STRG, MCS2_CH3_TRIGGER_BIT
    jbs STA CWT tsk3_init
    movl  CTRG, MCS2_CH3_TRIGGER_BIT                 ; Clear start trigger

MOS3_7:
    nard R0, R1, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST3
    jbs STA SAT MOS3                                  ; SAT = 1 new cyl3 request
    nard R0, R1, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST7
    jbs STA SAT MOS7                                  ; SAT = 1 new cyl7 request
    jmp MOS3_7

MOS3:
   atul R0, 0xFFFFFF
   jbc STA Z MOS3_WAIT_PARAM
   atul R1, 0xFFFFFF
   jbs STA Z MOS3_7
MOS3_WAIT_PARAM:
   ard R3, R5, MCS2_xMOS_CYL3_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration
   jmp PMOS3_7

MOS7:
   atul R0, 0xFFFFFF
   jbc STA Z MOS7_WAIT_PARAM
   atul R1, 0xFFFFFF
   jbs STA Z MOS3_7
MOS7_WAIT_PARAM:
   ard R3, R5, MCS2_xMOS_CYL7_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration

PMOS3_7:
;CLOSE MOS signals
; Stop NMOS 
    movl ACB, MOS3_7_CLOSE                           
    awr R0, R1, MCS2_NMOS3_7_PORT_IDX
; Stop PMOS     
;    movl ACB, MOS3_7_CLOSE
;    awr R0, R1, MCS2_PMOS3_7_PORT_IDX
    
    movl STA, 0x000003                                ; raise interrupt - ADC ION setup

    movl ACB, MOS3_7_OPEN                             ; Start PMOS
    awr R0, R1, MCS2_PMOS3_7_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4
    mov PMOS_REG, TBU_TS2
    add PMOS_REG, R3
    jbs STA CY PMOS3_7_LOOP_WA
    jmp PMOS3_7_LOOP
PMOS3_7_LOOP_WA:
    nard R2, R1, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT PMOS3_7_STOP_TRIGGER
    nard R2, R1, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST6
    jbs STA SAT PMOS3_7_STOP_TRIGGER
    ATU PMOS_REG, TBU_TS2 
    jbs STA CY PMOS3_7_LOOP_WA                        ; loop until (current) TBU_TS2 < R1
PMOS3_7_LOOP:
    nard R2, R1, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT PMOS3_7_STOP_TRIGGER
    nard R2, R1, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST6
    jbs STA SAT PMOS3_7_STOP_TRIGGER
    ATU TBU_TS2, PMOS_REG
    jbc STA CY PMOS3_7_STOP_DELAY                     ; loop until (current) TBU_TS2 < R1
    
    jmp PMOS3_7_LOOP

PMOS3_7_STOP_TRIGGER:    
    movl ACB, MOS3_7_CLOSE                            ; Stop PMOS
    awr R0, R1, MCS2_PMOS3_7_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4
    jmp NMOS3_7_ENABLE
    
PMOS3_7_STOP_DELAY:
    movl ACB, MOS3_7_CLOSE                            ; Stop PMOS
    awr R0, R1, MCS2_PMOS3_7_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4
    
    mov NMOS_REG, TBU_TS2
    add NMOS_REG, R5                                  ;NMOS_Delay
    jbs STA CY NMOS3_7_WAIT_EVENT_WA
    jmp NMOS3_7_WAIT_EVENT
NMOS3_7_WAIT_EVENT_WA:
    nard R0, R2, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT NMOS3_7_ENABLE
    nard R0, R2, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST6
    jbs STA SAT NMOS3_7_ENABLE
    atu NMOS_REG, TBU_TS2 
    jbs STA CY NMOS3_7_WAIT_EVENT_WA
NMOS3_7_WAIT_EVENT:
    nard R0, R2, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST2
    jbs STA SAT NMOS3_7_ENABLE
    nard R0, R2, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST6
    jbs STA SAT NMOS3_7_ENABLE
    atu TBU_TS2, NMOS_REG
    jbc STA CY NMOS3_7_ENABLE
    
    jmp NMOS3_7_WAIT_EVENT    

NMOS3_7_ENABLE:
    movl ACB, MOS3_7_OPEN                             ; Start NMOS
    awr R0, R1, MCS2_NMOS3_7_PORT_IDX                 ; PORT 5 of MCS2 ATOM1_4

   ;Update from UseCase #33, TRG.13 sent by CPU (BUCK_EN close ISR) to Re-open the PMOS, closure triggered by CPU at the end of ION sampling 
   ;movl CTRG, CPU_REOPEN_PMOS3_7_TRG_BIT                ; clean trigger bit
   
PMOS3_7_WAIT_TRG_REOPEN:
    btl STRG, CPU_REOPEN_PMOS3_7_TRG_BIT                ; (A & B) if TRUE Z=0; if FALSE Z=1 
    jbs STA Z PMOS3_7_WAIT_TRG_REOPEN                   ; loop until the TRG bit is not set
    ; delay before opening
    movl R0, PMOS_REOPEN_DELAY
    add R0, TBU_TS2
    jbs STA CY PMOS3_7_REOPEN_DELAY_WA
    jmp PMOS3_7_REOPEN_DELAY

PMOS3_7_REOPEN_DELAY_WA:
    atu R0, tbu_ts2
    jbc STA CY PMOS3_7_REOPEN_DELAY_WA

PMOS3_7_REOPEN_DELAY:
    atu TBU_TS2, R0
    jbc STA CY PMOS3_7_REOPEN
    jmp PMOS3_7_REOPEN_DELAY

PMOS3_7_REOPEN:
    movl CTRG, CPU_REOPEN_PMOS3_7_TRG_BIT                ; clean trigger bit
    movl ACB, MOS3_7_OPEN
    awr R0, R1, MCS2_PMOS3_7_PORT_IDX
        
    jmp MOS3_7
       
TASK3_DONE:
; Restart again
    ;movl STA, 0x000003   ; raise interrupt
    jmp   TASK3_START

; Should not get here
    andl STA MCS_CHx_DISABLE_MASK ; disable MCS

;========================================================================================
;--------------------------- PRIMARY - SECONDARY CURRENT BANK0 --------------------------
;========================================================================================
tsk4_init:
    movl R0, 0
	movl R1, 0
	movl R2, 0
	movl R3, 0
	movl R4, 0
	movl R5, 0
	movl R6, 0
	movl R7, (tsk4_stack-4)

TASK4_START:
    movl  R0, MCS2_CH4_TRIGGER_BIT
    wurm  R0, STRG, MCS2_CH4_TRIGGER_BIT
    jbs STA CWT tsk4_init
    movl  CTRG, MCS2_CH4_TRIGGER_BIT      ; Clear start trigger

IPRI0_WAIT_EVENT:
    nard R0, R1, MCS2_IPRI0_ADDR
    jbs STA SAT IPRI0_START
    jmp IPRI0_WAIT_EVENT

IPRI0_START:
    movl STRG, IPRI0_ADC_RECON_REQ        ; set request (TRG.8) bit to IPRI ADC reconfiguration
    xorl STA 2                            ; raise interrupt

IPRI0_ADC_RECON_WAIT:
    btl STRG, IPRI0_ADC_RECON_REQ         ; wait TRG.8 clean from CPU (ADC ready)
    jbc STA Z IPRI0_ADC_RECON_WAIT
    
    ;start atom for adc trigger
    movl ACB 0x14
    movl R2, 1
    movl R3, 1
    awr R2, R3, MCS2_IPRI0_PORT_IDX       ; Fake Start ATOM0_4 ADC Trigger
    
    movl ACB 0x14
    movl R2, IPRI0_ADC_PERIOD             ; R2 = PWM-ADC period
    mov R3, R2
    shr R3, 1                             ; R3 = PWM-ADC duty 
    awr R2, R3, MCS2_IPRI0_PORT_IDX       ; Start ATOM0_4 ADC Trigger
    
IPRI0_WAIT_STOP:
   nard R0, R1, MCS2_IPRI0_ADDR
   jbs STA SAT IPRI0_STOP
   jmp IPRI0_WAIT_STOP

IPRI0_STOP:
    movl ACB 0x14
    movl R3, 0
    awr R3, R3, MCS2_IPRI0_PORT_IDX      ; Disable ATOM0_4 ADC Trigger
    
    atul R0, IPRI_FAULT  ; Arriving a FAULT from Cylinder
    jbs STA Z IPRI0_LOOP

ISEC0_START:
    movl STRG, ISEC0_ADC_RECON_REQ         ; set request (TRG.9) bit to ISEC ADC reconfiguration
    xorl STA 2                            ; raise interrupt for SARADC re-configuration Channel

ISEC0_ADC_RECON_WAIT:
    btl STRG, ISEC0_ADC_RECON_REQ          ; wait TRG.9 clean from CPU (ADC ready)
    jbc STA Z ISEC0_ADC_RECON_WAIT
    
    movl ACB 0x14
    movl R2, ISEC0_ADC_PERIOD              ; R2 = PWM-ADC period
    mov R3, R2
    shr R3, 1                              ; R3 = PWM-ADC duty 
    awr R2, R3, MCS2_ISEC0_PORT_IDX        ; Start ATOM0_4 ADC Trigger

    mov R4, TBU_TS2
    addl R4, ISEC_ADC_DURATION
    jbs STA CY  SPARK_B0_LOOP_WA
    jmp SPARK_B0_LOOP
SPARK_B0_LOOP_WA:    
    atu R4, TBU_TS2
    jbs STA CY SPARK_B0_LOOP_WA
SPARK_B0_LOOP:
    atu TBU_TS2, R4
    jbc STA CY ISEC_DISABLE     ; TBU_TS2 >= R4
    jmp SPARK_B0_LOOP
 ISEC_DISABLE:   
    movl ACB 0x14
    movl R3, 0
    awr R3, R3, MCS2_ISEC0_PORT_IDX     ; Disable ATOM0_4 ADC Trigger
IPRI0_LOOP:
   jmp IPRI0_WAIT_EVENT

TASK4_DONE:
; Restart again
    ;movl STA, 0x000003   ; raise interrupt
    jmp   TASK4_START

; Should not get here
    andl STA MCS_CHx_DISABLE_MASK ; disable MCS

;========================================================================================
;--------------------------- PRIMARY - SECONDARY CURRENT BANK1 --------------------------
;========================================================================================
tsk5_init:
    movl R0, 0
	movl R1, 0
	movl R2, 0
	movl R3, 0
	movl R4, 0
	movl R5, 0
	movl R6, 0
	movl R7, (tsk5_stack-4)

TASK5_START:
    movl  R0, MCS2_CH5_TRIGGER_BIT
    wurm  R0, STRG, MCS2_CH5_TRIGGER_BIT
    jbs STA CWT tsk5_init
    movl  CTRG, MCS2_CH5_TRIGGER_BIT                 ; Clear start trigger

IPRI1_WAIT_EVENT:
    nard R0, R1, MCS2_IPRI1_ADDR
    jbs STA SAT IPRI1_START
    jmp IPRI1_WAIT_EVENT

IPRI1_START:
    movl STRG, IPRI1_ADC_RECON_REQ        ; set request (TRG.10) bit to IPRI ADC reconfiguration
    xorl STA 2                            ; raise interrupt

IPRI1_ADC_RECON_WAIT:
    btl STRG, IPRI1_ADC_RECON_REQ         ; wait TRG.10 clean from CPU (ADC ready)
    jbc STA Z IPRI1_ADC_RECON_WAIT
    
    
    ;start atom for adc trigger
    movl ACB 0x14
    movl R2, 1
    movl R3, 1
    awr R2, R3, MCS2_IPRI1_PORT_IDX     ; Fake Start ATOM0_6 ADC Trigger
    
    movl ACB 0x14
    movl R2, IPRI1_ADC_PERIOD           ; R2 = PWM-ADC period
    mov R3, R2
    shr R3, 1                             ; R3 = PWM-ADC duty 
    awr R2, R3, MCS2_IPRI1_PORT_IDX     ; Start ATOM0_6 ADC Trigger
    
IPRI1_WAIT_STOP:
    nard R0, R1, MCS2_IPRI1_ADDR
    jbs STA SAT IPRI1_STOP
    jmp IPRI1_WAIT_STOP
 
 IPRI1_STOP:
    atul R0, 0xFF
    jbc STA Z IPRI1_ERROR
    atul R1, 0xFF
    jbc STA Z IPRI1_ERROR
    movl ACB 0x14
    movl R3, 0
    awr R3, R3, MCS2_IPRI1_PORT_IDX     ; Disable ATOM0_6 ADC Trigger

ISEC1_START:
    movl STRG, ISEC1_ADC_RECON_REQ      ; set request (TRG.11) bit to IPRI ADC reconfiguration
    xorl STA 2                          ; raise interrupt for SARADC re-configuration Channel

ISEC1_ADC_RECON_WAIT:
    btl STRG, ISEC1_ADC_RECON_REQ       ; wait TRG.11 clean from CPU (ADC ready)
    jbc STA Z ISEC1_ADC_RECON_WAIT
        
    movl ACB 0x14
    movl R2, ISEC1_ADC_PERIOD           ; R2 = PWM-ADC period
    mov R3, R2
    shr R3, 1                           ; R3 = PWM-ADC duty 
    awr R2, R3, MCS2_ISEC1_PORT_IDX     ; Start ATOM0_6 ADC Trigger

    movl R4, ISEC_ADC_DURATION
    add R4, TBU_TS2
    jbs STA CY SPARK_B1_LOOP_WA
    jmp SPARK_B1_LOOP
SPARK_B1_LOOP_WA:
    atu R4, TBU_TS2
    jbs STA CY SPARK_B1_LOOP_WA       
SPARK_B1_LOOP:
    atu TBU_TS2, R4
    jbc STA CY ISEC1_STOP  ; TBU_TS2 >= R4
    jmp SPARK_B1_LOOP

 ISEC1_STOP:
    movl ACB 0x14
    movl R3, 0
    awr R3, R3, MCS2_ISEC1_PORT_IDX     ; Disable ATOM0_6 ADC Trigger
    
    jmp IPRI1_WAIT_EVENT
    
IPRI1_ERROR:
   jmp  IPRI1_START
     
TASK5_DONE:
; Restart again
    ;movl STA, 0x000003   ; raise interrupt
    jmp   TASK5_START
    andl STA MCS_CHx_DISABLE_MASK ; disable MCS

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;Unused MCS2 Threads
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
tsk6_init:
    andl STA MCS_CHx_DISABLE_MASK ; disable MCS

tsk7_init:
    andl STA MCS_CHx_DISABLE_MASK ; disable MCS    