/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

#include "gtm.h"

/**
 * @file    gtm_tbu.c
 * @brief   SPC5xx GTM TBU low level driver code.
 *
 * @addtogroup TBU
 * @{
 */
 
#if (SPC5_GTM_USE_TBU == TRUE) || defined(__DOXYGEN__)

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/**
 * @brief   ATOM0 driver identifier.
 */
#if (SPC5_GTM_USE_TBU0 == TRUE) || defined(__DOXYGEN__)
GTM_TBUDriver TBUD1;
#endif

/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/


/**
 * @brief   Low level GTM TBU driver initialization.
 *
 * @init
 */
void gtm_tbuInit(void) {

#if (SPC5_GTM_USE_TBU0 == TRUE)

	/* Set Register access pointer */
	TBUD1.tbu = &(GTM_TBU);

#if(SPC5_GTM_TBU0_USE_CHANNEL0 == TRUE)
	TBUD1.tbu->CH0_CTRL.B.CH_CLK_SRC = SPC5_GTM_TBU0_CHANNEL0_CLOCK_SOURCE;
	TBUD1.tbu->CH0_BASE.R = SPC5_GTM_TBU0_CHANNEL0_TIME_BASE;

#if (SPC5_GTM_TBU0_CHANNEL0_RESOLUTION == SPC5_GTM_TBU0_CH0_LOW_RES)
	TBUD1.tbu->CH0_CTRL.B.LOW_RES = 0x0;
#else
	TBUD1.tbu->CH0_CTRL.B.LOW_RES = 0x1;
#endif
#endif /* SPC5_GTM_TBU0_USE_CHANNEL0 */


#if(SPC5_GTM_TBU0_USE_CHANNEL1 == TRUE)
	TBUD1.tbu->CH1_CTRL.B.CH_CLK_SRC = SPC5_GTM_TBU0_CHANNEL1_CLOCK_SOURCE;
	TBUD1.tbu->CH1_BASE.R = SPC5_GTM_TBU0_CHANNEL1_TIME_BASE;

#if (SPC5_GTM_TBU0_CHANNEL1_MODE == SPC5_GTM_TBU_MODE_FREE_RUNNING)
	TBUD1.tbu->CH1_CTRL.B.CH_MODE = 0x0;
#elif (SPC5_GTM_TBU0_CHANNEL1_MODE == SPC5_GTM_TBU_MODE_FORWARD_BACKWARD)
	TBUD1.tbu->CH1_CTRL.B.CH_MODE = 0x1;
#endif
#endif /* SPC5_GTM_TBU0_USE_CHANNEL1 */


#if(SPC5_GTM_TBU0_USE_CHANNEL2 == TRUE)
	TBUD1.tbu->CH2_CTRL.B.CH_CLK_SRC = SPC5_GTM_TBU0_CHANNEL2_CLOCK_SOURCE;
	TBUD1.tbu->CH2_BASE.R = SPC5_GTM_TBU0_CHANNEL2_TIME_BASE;

#if (SPC5_GTM_TBU0_CHANNEL2_MODE == SPC5_GTM_TBU_MODE_FREE_RUNNING)
	TBUD1.tbu->CH2_CTRL.B.CH_MODE = 0x0;
#elif (SPC5_GTM_TBU0_CHANNEL2_MODE == SPC5_GTM_TBU_MODE_FORWARD_BACKWARD)
	TBUD1.tbu->CH2_CTRL.B.CH_MODE = 0x1;
#endif

#endif /* SPC5_GTM_TBU0_USE_CHANNEL2 */

#endif /* SPC5_GTM_USE_TBU0 */
}

/**
 * @brief   Start TBU configured channels
 *
 * @param[in] tbud      GTM TBU driver pointer
 *
 * @api
 */
void gtm_tbuStart(GTM_TBUDriver *tbud) {
	uint32_t value;

	value = 0;

#if(SPC5_GTM_TBU0_USE_CHANNEL0 == TRUE)
	value |= 0x02UL;
#endif

#if(SPC5_GTM_TBU0_USE_CHANNEL1 == TRUE)
	value |= 0x08UL;
#endif

#if(SPC5_GTM_TBU0_USE_CHANNEL2 == TRUE)
	value |= 0x20UL;
#endif

	/* Enable TBU module and channels */
	tbud->tbu->CHEN.R = value;
}

/**
 * @brief   Stop TBU all enabled channels
 *
 * @param[in] tbud      GTM TBU driver pointer
 *
 * @api
 */
void gtm_tbuStop(GTM_TBUDriver *tbud) {
	/* Disable all TBU channels */
	tbud->tbu->CHEN.R = 0x15;
}

/**
 * @brief   Start TBU specified channel
 *
 * @param[in] tbud      GTM TBU driver pointer
 *
 * @param[in] channel  GTM TBU channel number
 *
 * @api
 */
void gtm_tbuStartChannel(GTM_TBUDriver *tbud, uint8_t channel) {
	/* Enable TBU specific channel */
	tbud->tbu->CHEN.R = (0x02UL << (channel << 1UL));
}

/**
 * @brief   Stop TBU specified channel
 *
 * @param[in] tbud      GTM TBU driver pointer
 *
 * @param[in] channel  GTM TBU channel number
 *
 * @api
 */
void gtm_tbuStopChannel(GTM_TBUDriver *tbud, uint8_t channel) {
	/* Disable TBU specific channel */
	tbud->tbu->CHEN.R = (0x01UL << (channel << 1UL));
}

/**
 * @brief   Set TBU clock channel
 *
 * @param[in] tbud          GTM TBU driver pointer
 *
 * @param[in] channel       GTM TBU channel number
 *
 * @param[in] clock_source  GTM TBU clock source
 *
 * @api
 */
void gtm_tbuSetClockChannel(GTM_TBUDriver *tbud, uint8_t channel, uint8_t clock_source) {
	switch(channel) {
	case TBU_CHANNEL0:
		tbud->tbu->CH0_CTRL.B.CH_CLK_SRC = clock_source;
		break;
	case TBU_CHANNEL1:
		tbud->tbu->CH1_CTRL.B.CH_CLK_SRC = clock_source;
		break;
	case TBU_CHANNEL2:
		tbud->tbu->CH2_CTRL.B.CH_CLK_SRC = clock_source;
		break;
	default:
		/* MISRA check */
		break;
	}
}


/**
 * @brief   Get TBU clock channel
 *
 * @param[in] tbud          GTM TBU driver pointer
 *
 * @param[in] channel       GTM TBU channel number
 *
 * @return Clock
 *
 * @api
 */
uint32_t gtm_tbuGetClockChannel(GTM_TBUDriver *tbud, uint8_t channel) {
	uint32_t clock_source;

	switch(channel) {
	case TBU_CHANNEL0:
		clock_source = tbud->tbu->CH0_CTRL.B.CH_CLK_SRC;
		break;
	case TBU_CHANNEL1:
		clock_source = tbud->tbu->CH1_CTRL.B.CH_CLK_SRC;
		break;
	case TBU_CHANNEL2:
		clock_source = tbud->tbu->CH2_CTRL.B.CH_CLK_SRC;
		break;
	default:
		clock_source = 0;
		break;
	}
	return clock_source;
}

/**
 * @brief   Set TBU Time Base channel
 *
 * @param[in] tbud          GTM TBU driver pointer
 *
 * @param[in] channel       GTM TBU channel number
 *
 * @param[in] time_base     GTM TBU time base
 *
 * @api
 */
void gtm_tbuSetTimeBaseChannel(GTM_TBUDriver *tbud, uint8_t channel, uint32_t time_base) {
	switch(channel) {
	case TBU_CHANNEL0:
		tbud->tbu->CH0_BASE.R = time_base;
		break;
	case TBU_CHANNEL1:
		tbud->tbu->CH1_BASE.R = time_base;
		break;
	case TBU_CHANNEL2:
		tbud->tbu->CH2_BASE.R = time_base;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Get TBU Time Base for the channel
 *
 * @param[in] tbud          GTM TBU driver pointer
 *
 * @param[in] channel       GTM TBU channel number
 *
 * @return Time Base
 *
 * @api
 */
uint32_t gtm_tbuGetTimeBaseChannel(GTM_TBUDriver *tbud, uint8_t channel) {
	uint32_t time_base;

	switch(channel) {
	case TBU_CHANNEL0:
		time_base = tbud->tbu->CH0_BASE.R;
		break;
	case TBU_CHANNEL1:
		time_base = tbud->tbu->CH1_BASE.R;
		break;
	case TBU_CHANNEL2:
		time_base = tbud->tbu->CH2_BASE.R;
		break;
	default:
		time_base = 0;
		break;
	}
	return time_base;
}

/**
 * @brief   Set TBU Low Res for channel0
 *
 * @param[in] tbud          GTM TBU driver pointer
 *
 * @param[in] low_res      GTM TBU Low Res setting
 *
 * @api
 */
void gtm_tbuSetLowResChannel_0(GTM_TBUDriver *tbud, uint32_t low_res) {
	if (low_res == TRUE) {
		tbud->tbu->CH0_CTRL.B.LOW_RES = 0;
	} else {
		tbud->tbu->CH0_CTRL.B.LOW_RES = 1;
	}
}

/**
 * @brief   Get TBU Low Res for the channel0
 *
 * @param[in] tbud          GTM TBU driver pointer
 *
 * @return Low Res
 *
 * @api
 */
uint32_t gtm_tbuGetLowResChannel_0(GTM_TBUDriver *tbud) {
	uint32_t low_res;

	if (tbud->tbu->CH0_CTRL.B.LOW_RES == 0U) {
		low_res =1;
	} else {
		low_res = 0;
	}

	return low_res;
}

/**
 * @brief   Set TBU Running Mode for channel1 and channel2
 *
 * @param[in] tbud          GTM TBU driver pointer
 *
 * @param[in] channel       GTM TBU channel number
 *
 * @param[in] running_mode  GTM TBU running mode
 *
 * @sa
 * SPC5_GTM_TBU_MODE_FREE_RUNNING, SPC5_GTM_TBU_MODE_FORWARD_BACKWARD
 *
 * @api
 */
void gtm_tbuSetRunningModeChannel_12(GTM_TBUDriver *tbud, uint8_t channel, uint8_t running_mode) {
	if (channel == 1U) {
		tbud->tbu->CH1_CTRL.B.CH_MODE = running_mode;
	} else if (channel == 2U) {
		tbud->tbu->CH2_CTRL.B.CH_MODE = running_mode;
	} else {
		/* MISRA check */
	}
}

/**
 * @brief   Get TBU Running Mode for channel1 and channel2
 *
 * @param[in] tbud          GTM TBU driver pointer
 *
 * @param[in] channel       GTM TBU channel number
 *
 * @return running_mode
 *
 * @api
 */
uint8_t gtm_tbuGetRunningModeChannel_12(GTM_TBUDriver *tbud, uint8_t channel) {
	if (channel == 1U) {
		if (tbud->tbu->CH1_CTRL.B.CH_MODE == 0U) {
			return SPC5_GTM_TBU_MODE_FREE_RUNNING;
		} else {
			return SPC5_GTM_TBU_MODE_FORWARD_BACKWARD;
		}
	} else if (channel == 2U) {
		if (tbud->tbu->CH2_CTRL.B.CH_MODE == 0U) {
			return SPC5_GTM_TBU_MODE_FREE_RUNNING;
		} else {
			return SPC5_GTM_TBU_MODE_FORWARD_BACKWARD;
		}
	} else {
		/* MISRA check */
	}
	return 0;
}

#endif /* SPC5_GTM_USE_TBU */
/** @} */

