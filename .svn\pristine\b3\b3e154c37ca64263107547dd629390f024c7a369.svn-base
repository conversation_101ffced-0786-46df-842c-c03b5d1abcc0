/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Tpe
**  Filename        :  Tpe.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  MocciA
******************************************************************************/
#ifndef __TPE_H__
#define __TPE_H__

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "tpe_out.h"
#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
#include "MCAN_out.h"
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
#include "TTCAN_out.h"
#else
#error TPE peripheral not defined
#endif
#include "Utils_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/**
---------------------------------------------------------------
Transfer Protocol Data Unit (TPDU) with normal addressing mode
-----------------------+------+------+------+------+------+------+------+------+
                (Byte) |  #1  |  #2  |  #3  |  #4  |  #5  |  #6  |  #7  |  #8  |
-----------------------+------+------+------+------+------+------+------+------+
Single Frame (SF)      | TPCI | DATA | DATA | DATA | DATA | DATA | DATA | DATA |
-----------------------+------+------+------+------+------+------+------+------+
First Frame (FF)       | TPCI |  DL  | DATA | DATA | DATA | DATA | DATA | DATA |
-----------------------+------+------+------+------+------+------+------+------+
Consecutive Frame (CF) | TPCI | DATA | DATA | DATA | DATA | DATA | DATA | DATA |
-----------------------+------+------+------+------+------+------+------+------+
Flow Control (FC)      | TPCI |  BS  |  ST  |
-----------------------+------+------+------+------+------+------+------+------+

--------------------------------------------------------------
Transfer Protocol Data Unit (TPDU) with extended addressing mode
-----------------------+------+------+------+------+------+------+------+------+
                (Byte) |  #1  |  #2  |  #3  |  #4  |  #5  |  #6  |  #7  |  #8  |
-----------------------+------+------+------+------+------+------+------+------+
Single Frame (SF)      |  TA  | TPCI | DATA | DATA | DATA | DATA | DATA | DATA |
-----------------------+------+------+------+------+------+------+------+------+
First Frame (FF)       |  TA  | TPCI |  DL  | DATA | DATA | DATA | DATA | DATA |
-----------------------+------+------+------+------+------+------+------+------+
Consecutive Frame (CF) |  TA  | TPCI | DATA | DATA | DATA | DATA | DATA | DATA |
-----------------------+------+------+------+------+------+------+------+------+
Flow Control (FC)      |  TA  | TPCI |  BS  |  ST  |
-----------------------+------+------+------+------+------+------+------+------+

--------------------------------------------------------------
Transfer Protocol Control Information (TPCI)
-----------------------+----+----+----+----+----+----+----+----+
                 (Bit) | #7 | #6 | #5 | #4 | #3 | #2 | #1 | #0 |
-----------------------+----+----+----+----+----+----+----+----+
Single Frame (SF)      | 0  | 0  | 0  | 0  |        DL         | Data Length
-----------------------+----+----+----+----+----+----+----+----+
First Frame (FF)       | 0  | 0  | 0  | 1  |        XDL        | Extended Data Length
-----------------------+----+----+----+----+----+----+----+----+
Consecutive Frame (CF) | 0  | 0  | 1  | 0  |        SN         | Sequence number
-----------------------+----+----+----+----+----+----+----+----+
Flow Control (FC)      | 0  | 0  | 1  | 1  |        FCS        | Flow Control Status 0 -> CLEAR_TO_SEND, 1 -> WAIT, 2 -> OVERFLOW
-----------------------+----+----+----+----+----+----+----+----+
**/

/// Single Frame high nibbles identifier
#define TPE_TPDU_SF_ID                0x00U
/// First Frame high nibbles identifier
#define TPE_TPDU_FF_ID                0x10U
/// Consecutive Frame high nibbles identifier
#define TPE_TPDU_TPCI_CF              0x20U
/// Flow Control Frame high nibbles identifier
#define TPE_TPDU_TPCI_FC              0x30U

#ifdef ISO_TP_MIXED_MODE
/// Target Address Byte position inside TPDU
#define TPE_TPDU_TA_POS               (0U)
/// Protocol Control Information Byte position
#define TPE_TPDU_TPCI_POS             (1U)
/// Single Frame first data byte position
#define TPE_TPDU_SF_DATA_POS          (2U)
/// First Frame first data byte position
#define TPE_TPDU_FF_DATA_POS          (3U)
/// Consecutive Frame first data byte position
#define TPE_TPDU_CF_DATA_POS          (2U)
/// Flow Control Frame first data byte position
#define TPE_TPDU_FC_DATA_POS          (4U)
/// First Frame Data length byte position
#define TPE_TPDU_FF_DL_POS            (2U)
/// Block Size byte position inside the FC frame
#define TPE_TPDU_FC_BS_POS            (2U)
/// Separation Time byte position inside the FC frame
#define TPE_TPDU_FC_ST_POS            (3U)
#else
/// Transport Protocol Control Information Byte position
#define TPE_TPDU_TPCI_POS             (0U)
/// Single Frame first data byte position
#define TPE_TPDU_SF_DATA_POS          (1U)
/// First Frame first data byte position
#define TPE_TPDU_FF_DATA_POS          (2U)
/// Consecutive Frame first data byte position
#define TPE_TPDU_CF_DATA_POS          (1U)
/// Flow Control Frame first data byte position
#define TPE_TPDU_FC_DATA_POS          (3U)
/// First Frame Data length byte position
#define TPE_TPDU_FF_DL_POS            (1U)
/// Block Size byte position inside the FC frame
#define TPE_TPDU_FC_BS_POS            (1U)
/// Separation Time byte position inside the FC frame
#define TPE_TPDU_FC_ST_POS            (2U)
#endif

/// Single Frame TPCI bitmask
#define TPE_TPDU_TPCI_SF_MASK         0xF8U
/// Other Frames: TPCI Mask
#define TPE_TPDU_TPCI_NO_SF_MASK      0xF0U
/// Flow Control Frame FlowStatus bitmask
#define TPE_TPDU_FC_STATUS_MASK       0x0FU
/// Single Frame data len bitmask
#define TPE_TPDU_SF_DL_MASK           0x07U
/// First Frame extended data len bitmask
#define TPE_TPDU_FF_XDL_MASK          0x0FU
/// Consecutive Frame segment number bitmask
#define TPE_TPDU_CF_SN_MASK           0x0FU

/// Flow Control Clear To Send Status Bit value
#define TPE_TPDU_FC_CTS               0x00U
/// Flow Control Wait Status Bit value
#define TPE_TPDU_FC_WAIT              0x01U
/// Flow Control Overflow/Abort Status Bit value
#define TPE_TPDU_FC_OF                0x02U
/// Flow Control Block Size min value
#define TPE_TPDU_FC_BS_MIN            (TPE_BS)
/// Flow Control Block Size max value
#define TPE_TPDU_FC_BS_MAX            0xFFU
/// Flow Control Separation Time min value
#define TPE_TPDU_FC_ST_MIN            (TPE_ST_MIN)
/// Flow Control Separation Time max value
#define TPE_TPDU_FC_ST_MAX            0xFFU

#ifdef ISO_TP_MIXED_MODE
/// Data Bytes = TPE_CANBUFF_SIZE - 1(Target Address) - 1(TPCI)
#define TPE_DATABUFF_SIZE             (6U)
#else
/// Data Bytes = TPE_CANBUFF_SIZE - 1(TPCI)
#define TPE_DATABUFF_SIZE             (7U)
#endif

/// Received data valid flag
#define DIAGNOSE_REQUEST              (0x04U)
/// Received Flow Control valid flag
#define DIAGNOSE_REQUEST_FC           (0x02U)
/// Max CAN Tx attempts on BUSY error code
#define TPE_CAN_TX_MAX_TRIAL          (3U)

/// Connection timeout calculated from FF sent by the TPE and the FC recevied from external tool
#define TPE_CN_COUNTER                (N_Bs_TIMEOUT)
/// timeout on intermediate frames flow control  (flowcontrol)
#define TPE_FC_COUNTER                (N_As_TIMEOUT + N_Bs_TIMEOUT)
/// N_Cr Time until reception of the next Consecutive Frame N_PDU
#define TPE_CR_COUNTER                (N_Cr_TIMEOUT)

/// Minimum message length for FC message ($30$<BS>$<ST> at least)
#define TPE_FC_PDU_LEN                (3U)

#ifdef TPE_TIME_BASE_MS
#if (TPE_TIME_BASE_MS == 1u)
/// Separation time with 1msec granularity
#define STMIN_DISCRETE_STEP           (1u)
#elif (TPE_TIME_BASE_MS == 5u)
/// Separation time with 5msec granularity
#define STMIN_DISCRETE_STEP           (5u)
#else
#warning Timing resolution not defined for TPE state machine
#endif
#endif
/// Separation time MAX with 5msec granularity
#define STMIN_MAX                     (125u)
/// Separation Time Requested Error
#define STMIN_ERROR                   (255u)

/// All Ok!!!
#define TPE_SUCCESS                   0x00u
/// Error while tx/rx PDUs
#define TPE_NOTSUCCESS                0x01u
/// Timeout expired 
#define TPE_ERROR_TIMEOUT             0x02u
/// Pending Transmission
#define TPE_PENDING                   0x03u

/// Transport Protocol layer inactive
#define TPE_OFF                       0x0u
/// Transport Protocol layer active
#define TPE_ON                        0x1u


/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/// Transport Layer generic error
typedef enum
{
    TPE_OK                         =  0u, // The sun is shinig and world is so wonderful in covid-19 time
    TPE_SERVER_CONNECTION_TIMEOUT  =  1u, // No response from the Client in time
    TPE_FC_TIMEOUT                 =  2u, // FC from the Client not received in time
    TPE_CF_TIMEOUT                 =  3u, // Last CF the Client not received in time
    TPE_MSG_DLC_MISMATCH           =  4u, // L_PDU lenght received from the Link Layer (CAN message DLC) shorter or equal to the T_PDU length field, eg Client sends a SF with a CAN DLC <= SF_DL
    TPE_FF_DL_NOT_VALID            =  5u, // FF_DL less than 8 according to Table 8 of ISO 15765-2:2011
    TPE_CHECK_DLC_FAILED           =  6u, // If Client sends a request with a CAN DLC less than 8 
    TPE_FUNC_FF_NOT_COMPLIANT      =  7u, // If Client sends a funtional FF, not compliant with table 22 of ISO 15765-2:2011
    TPE_FUNC_CF_NOT_COMPLIANT      =  8u, // If Client sends a funtional CF, not compliant with table 22 of ISO 15765-2:2011
    TPE_FUNC_FC_NOT_COMPLIANT      =  9u, // If Client sends a funtional FC, not compliant with table 22 of ISO 15765-2:2011
    TPE_FC_INVALID_LENGTH          = 10u, // If Client sends a FC with a CAN DLC less than 3, according to table 12 of ISO 15765-2:2011
    TPE_FC_ST_NOT_VALID            = 11u, // If Client sends a FC with a STmin not valid, according to table 15 of ISO 15765-2:2011
    TPE_FC_FS_NOT_VALID            = 12u, // If Client sends a FC with a FS not valid, according to table 13 of ISO 15765-2:2011
    TPE_CLIENT_BUFFER_OVFLW        = 13u, // If Client sends a FC with a FS Overflow, something goes wrong at Client side
    TPE_CF_SN_NOT_VALID            = 14u, // If Client sends a CF with a SN not valid, according to table 10/11 of ISO 15765-2:2011
    TPE_RX_BUFFER_OVERFLOW         = 15u, // If Client sends a FF with a FF_DL value higher than server reception capabilities MAX_TP_MSG_SIZE
} tpeError_T;

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static void tpeReInit(void);
static void tpeRxMngr(uint8_T *tpdu);
static uint8_T tpeTxMngr(void);
static void  tpeRxDataException(void);
static void  tpeTxDataException(void);
static uint8_T tpeSTMinCheck(uint8_T stmin_req);
static uint8_T tpeSendDataOnLL(uint8_T* data, uint8_T data_length);
static uint8_T tpeReceiveDataFromLL(uint8_T* data);
static void tpeSetReqType(tpeReqType_T req);
#endif //__TPE_H__

