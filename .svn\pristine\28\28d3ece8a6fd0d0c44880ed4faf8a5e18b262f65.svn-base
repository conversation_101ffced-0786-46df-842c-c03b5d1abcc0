/****************************************************************************
*
* Copyright © 2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_brc.c
 * @brief   SPC5xx GTM BRC driver code.
 *
 * @addtogroup BRC
 * @{
 */

#include "gtm.h"
//#include <irq.h> MC

/*lint -e621*/

#if (SPC5_GTM_USE_BRC == TRUE) || defined(__DOXYGEN__)

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/**
 * @brief   BRC driver identifier.
 */
#if (SPC5_GTM_USE_BRC == TRUE) || defined(__DOXYGEN__)
GTM_BRCDriver BRCD1;
#endif

/*===========================================================================*/
/* Driver local variables and types.                                         */
/*===========================================================================*/
#if !defined (__DOXYGEN__)
#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL0 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL0_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL1 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL1_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL2 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL2_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL3 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL3_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL4 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL4_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL5 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL5_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL6 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL6_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL7 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL7_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL7_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL8 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL8_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL8_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL8_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL9 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL9_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL9_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL9_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL10 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL10_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL10_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL10_INT    FALSE
#endif

#if ((SPC5_GTM_BRC_USE_INPUT_CHANNEL11 == TRUE) && \
	(SPC5_GTM_BRC_CHANNEL11_INT_BRC_ENABLED == TRUE) )

#define SPC5_GTM_BRC_CHANNEL11_INT    TRUE
#else
#define SPC5_GTM_BRC_CHANNEL11_INT    FALSE
#endif
#endif /* (__DOXYGEN__) */

/*===========================================================================*/
/* Driver local functions.                                                   */
/*===========================================================================*/
void spc5_gtm_brc_interrupt_channel_handler(GTM_BRCDriver *brcd, uint8_t channel);
void spc5_gtm_brc_interrupt_handler(GTM_BRCDriver *brcd);

/**
 * @brief   Low level interrupt handler.
 *
 * @param[in] brcd       GTM BRC driver pointer
 *
 * @param[in] channel    BRC channel
 *
 * @sa
 * BRC_CHANNEL0, BRC_CHANNEL1, BRC_CHANNEL2, BRC_CHANNEL3, BRC_CHANNEL4, BRC_CHANNEL5, BRC_CHANNEL6, BRC_CHANNEL7, BRC_CHANNEL8, BRC_CHANNEL9, BRC_CHANNEL10, BRC_CHANNEL11
 *
 */
void spc5_gtm_brc_interrupt_channel_handler(GTM_BRCDriver *brcd, uint8_t channel) {

	uint32_t status;
	uint32_t enabled;
	GTM_BRC_Channel_Callbacks *callback;

	/* Read interrupt status */
	status = gtm_brcGetIntStatus(brcd, (uint8_t)channel);

	/* Mask disabled interrupts */
	enabled = gtm_brcGetIntEnabled(brcd, (uint8_t)channel) & status;
	callback = brcd->callbacks[channel];

	if (callback != NULL) {

		if ((enabled & SPC5_GTM_BRC_IRQ_STATUS_BRC) != 0UL) {
			if (brcd->callbacks[channel]->data_inconsistent != NULL) {
				brcd->callbacks[channel]->data_inconsistent(brcd, channel);
			}
		}
	}

	/* Acknowledge the interrupts */
	gtm_brcAckInt(brcd, (uint8_t)channel, (uint8_t)status);
}

/**
 * @brief   Low level interrupt handler.
 *
 * @param[in] brcd       GTM BRC driver pointer
 *
 */
void spc5_gtm_brc_interrupt_handler(GTM_BRCDriver *brcd) {

	uint32_t status;
	uint32_t enabled;
	GTM_BRC_Callbacks *callback;

	/* Read interrupt status */
	status = gtm_brcGetPlausibilityIntStatus(brcd);

	/* Mask disabled interrupts */
	enabled = gtm_brcGetPlausibilityIntEnabled(brcd) & status;
	callback = brcd->brc_callbacks[0];

	if (callback != NULL) {

		if ((enabled & SPC5_GTM_BRC_IRQ_STATUS_BRC) != 0UL) {
			if (brcd->brc_callbacks[0]->plausibility != NULL) {
				brcd->brc_callbacks[0]->plausibility(brcd);
			}
		}
	}

	/* Acknowledge the interrupts */
	gtm_brcAckPlausibilityInt(brcd, (uint8_t)status);
}

/*===========================================================================*/
/* Driver interrupt handlers.                                                */
/*===========================================================================*/
#if (SPC5_GTM_BRC_IRQ_INT_ENABLE == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC interrupt hander.
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_handler(&BRCD1);

	IRQ_EPILOGUE();
}

#endif

#if (SPC5_GTM_BRC_CHANNEL0_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 0 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL0
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL0);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL1_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 1 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL1);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL2_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 2 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL2
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL2);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL3_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 3 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL3);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL4_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 4 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL4
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL4);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL5_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 5 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL5);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL6_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 6 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL6
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL6);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL7_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 7 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL7);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL8_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 8 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL8
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL8);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL9_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 9 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL9
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL9);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL10_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 10 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL10
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL10);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_BRC_CHANNEL11_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   BRC Channel 11 interrupt hander.
 *
 * @sa
 * BRC_CHANNEL11
 *
 */
IRQ_HANDLER(SPC5_BRC_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_brc_interrupt_channel_handler(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL11);

	IRQ_EPILOGUE();
}
#endif

/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/

/**
 * @brief   Low level GTM BRC driver initialization.
 *
 * @init
 */ 
void gtm_brcInit(void) {

#if (SPC5_GTM_USE_BRC == TRUE)

	BRCD1.brc = &(GTM_BRC);
	
	/* Interrupts callbacks */
	BRCD1.callbacks = (GTM_BRC_Channel_Callbacks **)gtm_brc_callbacks;
	
	BRCD1.brc_callbacks = (GTM_BRC_Callbacks **)gtm_brc_callback;
	
	BRCD1.priv = NULL;

	/*BRC Soft Reset */
	BRCD1.brc->RST.R =  SPC5_GTM_BRC_SOFTWARE_RESET;

#if (SPC5_GTM_BRC_IRQ_INT_ENABLE == TRUE)
	gtm_brcSetIRQMode(&BRCD1, SPC5_GTM_BRC_IRQ_MODE);
	INTC_PSR(SPC5_BRC_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_BRC_INT_PRIORITY);

	gtm_brcEnablePlausibilityInt(&BRCD1);
#endif

#if (SPC5_GTM_BRC_CHANNEL0_INT_BRC_ENABLED == TRUE)
#if (SPC5_GTM_BRC_CHANNEL0_INT_BRC_MODE == SPC5_GTM_BRC_INT_MODE_NORMAL)
	gtm_brcEnableInt(&BRCD1, SPC5_GTM_BRC_INPUT_CHANNEL0, SPC5_GTM_BRC_IRQ_ENABLE_BRC);
#elif (SPC5_GTM_BRC_CHANNEL0_INT_BRC_MODE == SPC5_GTM_BRC_INT_MODE_ERROR)
#elif (SPC5_GTM_BRC_CHANNEL0_INT_BRC_MODE == SPC5_GTM_BRC_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL0 == TRUE)
	BRCD1.brc->SRC[0].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC0_ADDR;
	BRCD1.brc->SRC[0].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC0_MODE ;
	BRCD1.brc->SRC[0].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC0_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL1 == TRUE)
	BRCD1.brc->SRC[1].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC1_ADDR;
	BRCD1.brc->SRC[1].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC1_MODE;
	BRCD1.brc->SRC[1].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC1_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL2 == TRUE)
	BRCD1.brc->SRC[2].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC2_ADDR;
	BRCD1.brc->SRC[2].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC2_MODE;
	BRCD1.brc->SRC[2].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC2_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL3 == TRUE)
	BRCD1.brc->SRC[3].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC3_ADDR;
	BRCD1.brc->SRC[3].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC3_MODE;
	BRCD1.brc->SRC[3].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC3_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL4 == TRUE)
	BRCD1.brc->SRC[4].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC4_ADDR;
	BRCD1.brc->SRC[4].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC4_MODE;
	BRCD1.brc->SRC[4].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC4_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL5 == TRUE)
	BRCD1.brc->SRC[5].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC5_ADDR;
	BRCD1.brc->SRC[5].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC5_MODE;
	BRCD1.brc->SRC[5].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC5_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL6 == TRUE)
	BRCD1.brc->SRC[6].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC6_ADDR;
	BRCD1.brc->SRC[6].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC6_MODE;
	BRCD1.brc->SRC[6].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC6_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL7 == TRUE)
	BRCD1.brc->SRC[7].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC7_ADDR;
	BRCD1.brc->SRC[7].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC7_MODE;
	BRCD1.brc->SRC[7].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC7_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL8 == TRUE)
	BRCD1.brc->SRC[8].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC8_ADDR;
	BRCD1.brc->SRC[8].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC8_MODE;
	BRCD1.brc->SRC[8].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC8_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL9 == TRUE)
	BRCD1.brc->SRC[9].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC9_ADDR;
	BRCD1.brc->SRC[9].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC9_MODE;
	BRCD1.brc->SRC[9].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC9_TRASH;
#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL10 == TRUE)
	BRCD1.brc->SRC[10].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC10_ADDR;
	BRCD1.brc->SRC[10].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC10_MODE;
	BRCD1.brc->SRC[10].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC10_TRASH;

#endif

#if (SPC5_GTM_BRC_USE_INPUT_CHANNEL11 == TRUE)
	BRCD1.brc->SRC[11].ADDR.B.ADDR = (uint16_t)SPC5_GTM_BRC_SRC11_ADDR;
	BRCD1.brc->SRC[11].ADDR.B.BRC_MODE = SPC5_GTM_BRC_SRC11_MODE;
	BRCD1.brc->SRC[11].DEST.B.EN_TRASHBIN = SPC5_GTM_BRC_SRC11_TRASH;

#endif
#endif
}

/**
 * @brief   Start BRC channel
 *
 * @param[in] brc       GTM BRC driver pointer
 *
 * @api
 */
void gtm_brcStart(GTM_BRCDriver *brc) {

#if (SPC5_GTM_USE_BRC == TRUE)
#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL0 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST0].DEST.B.EN_DEST0 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL1 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST1].DEST.B.EN_DEST1 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL2 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST2].DEST.B.EN_DEST2 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL3 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST3].DEST.B.EN_DEST3 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL4 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST4].DEST.B.EN_DEST4 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL5 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST5].DEST.B.EN_DEST5 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL6 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST6].DEST.B.EN_DEST6 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL7 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST7].DEST.B.EN_DEST7 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL8 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST8].DEST.B.EN_DEST8 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL9 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST9].DEST.B.EN_DEST9 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL10 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST10].DEST.B.EN_DEST10 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL11 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST11].DEST.B.EN_DEST11 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL12 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST12].DEST.B.EN_DEST12 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL13 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST13].DEST.B.EN_DEST13 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL14 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST14].DEST.B.EN_DEST14 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL15 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST15].DEST.B.EN_DEST15 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL16 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST16].DEST.B.EN_DEST16 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL17 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST17].DEST.B.EN_DEST17 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL18 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST18].DEST.B.EN_DEST18 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL19 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST19].DEST.B.EN_DEST19 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL20 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST20].DEST.B.EN_DEST20 = 1U;
#endif

#if (SPC5_GTM_BRC_USE_OUTPUT_CHANNEL21 == TRUE)
	brc->brc->SRC[SPC5_GTM_BRC_SRC_FOR_DEST21].DEST.B.EN_DEST21 = 1U;
#endif

#endif

}

/**
 * @brief   Stop BRC channel
 *
 * @param[in] brc       GTM BRC driver pointer
 *
 * @api
 */
void gtm_brcStop(GTM_BRCDriver *brc) {
	uint32_t i;

	for (i = 0; i < SPC5_GTM_BRC_CHANNELS; i++) {
	   brc->brc->SRC[i].DEST.R &= 0x00400000UL;
	}
}

/**
 * @brief   Set BRC channel source
 *
 * @param[in] brc       GTM BRC driver pointer
 *
 * @param[in] channel   GTM BRC source channel
 *
 * @param[in] address   GTM BRC source address
 *
 * @api
 */
void gtm_brcSetSource(GTM_BRCDriver *brc, uint32_t channel, uint32_t address) {
	brc->brc->SRC[channel].ADDR.B.ADDR = (uint16_t)address;
}

/**
 * @brief   Set BRC channel mode
 *
 * @param[in] brc       GTM BRC driver pointer
 *
 * @param[in] channel   GTM BRC source channel
 *
 * @param[in] mode      GTM BRC source channel mode
 *
 * @api
 */
void gtm_brcSetMode(GTM_BRCDriver *brc, uint32_t channel, uint8_t mode) {
	brc->brc->SRC[channel].ADDR.B.BRC_MODE = mode;
}

/**
 * @brief   Return BRC Channel source 
 *
 * @param[in] brc        GTM BRC driver pointer
 *
 * @param[in] channel     GTM BRC channel number
 *
 * @return source address
*
 * @api
 */
uint32_t gtm_brcGetSource(GTM_BRCDriver *brc, uint32_t channel) {
	return brc->brc->SRC[channel].ADDR.B.ADDR;
}

/**
 * @brief   Return BRC Channel mode 
 *
 * @param[in] brc        GTM BRC driver pointer
 *
 * @param[in] channel     GTM BRC channel number
 *
 * @return source mode
*
 * @api
 */
uint8_t gtm_brcGetMode(GTM_BRCDriver *brc, uint32_t channel) {
	return brc->brc->SRC[channel].ADDR.B.BRC_MODE;
}

/**
 * @brief   Set BRC channel mode
 *
 * @param[in] brc       GTM BRC driver pointer
 *
 * @param[in] dest     GTM BRC destination channel
 *
 * @param[in] source   GTM BRC source channel
 *
 * @param[in] value     GTM BRC destination value settings 
 *
 * @api
 */
void gtm_brcSetOutput(GTM_BRCDriver *brc, uint32_t dest, uint32_t source, uint8_t value) {
	brc->brc->SRC[source].DEST.R = ((uint32_t)value << dest);
}

/**
 * @brief   Return BRC Channel destination mapping 
 *
 * @param[in] brc      GTM BRC driver pointer
 *
 * @param[in] dest     GTM BRC destination channel number
 *
 * @return source channel
*
 * @api
 */
uint32_t gtm_brcGetOutput(GTM_BRCDriver *brc, uint32_t dest) {
	uint32_t i;
	uint32_t output = 0;

	for (i = 0; i < SPC5_GTM_BRC_CHANNELS; i++) {
		if (((brc->brc->SRC[i].DEST.R & (1UL << dest)) >> dest) == 1U) {
			output = i;
			break;
		}
	}
	return output;
}

/**
 * @brief   Return BRC Channel interrupt status
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @param[in] channel     GTM BRC channel number
 *
 * @return Interrupt status
 *
 * @sa
 * BRC_CHANNEL0, BRC_CHANNEL1, BRC_CHANNEL2, BRC_CHANNEL3, BRC_CHANNEL4, BRC_CHANNEL5, BRC_CHANNEL6, BRC_CHANNEL7,
 * <br>
 * BRC_CHANNEL8, BRC_CHANNEL9, BRC_CHANNEL10, BRC_CHANNEL11
 *
 * @api
 */
uint32_t gtm_brcGetIntStatus(GTM_BRCDriver *brcd, uint8_t channel) {
	uint32_t value;

	if ( (brcd->brc->IRQ_NOTIFY.R & (1UL << (channel + 1U))) != 0U ) {
		value = 1;
	} else {
		value = 0;
	}
	return value;
}

/**
 * @brief   Return BRC Channel interrupt enabled
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @param[in] channel     GTM BRC channel number
 *
 * @return Interrupt enabled
 *
 * @sa
 * BRC_CHANNEL0, BRC_CHANNEL1, BRC_CHANNEL2, BRC_CHANNEL3, BRC_CHANNEL4, BRC_CHANNEL5, BRC_CHANNEL6, BRC_CHANNEL7,
 * <br>
 * BRC_CHANNEL8, BRC_CHANNEL9, BRC_CHANNEL10, BRC_CHANNEL11
 *
 * @api
 */
uint32_t gtm_brcGetIntEnabled(GTM_BRCDriver *brcd, uint8_t channel) {
	uint32_t value;

	if ( (brcd->brc->IRQ_EN.R & (1UL << (channel + 1U))) != 0U ) {
		value = 1;
	} else {
		value = 0;
	}
	return value;
}

/**
 * @brief   Acknowledge BRC Channel interrupt
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @param[in] channel     GTM BRC channel number
 *
 * @param[in] int_num   GTM BRC interrupt to acknowledge
 *
 * @sa
 * BRC_CHANNEL0, BRC_CHANNEL1, BRC_CHANNEL2, BRC_CHANNEL3, BRC_CHANNEL4, BRC_CHANNEL5, BRC_CHANNEL6, BRC_CHANNEL7,
 * <br>
 * BRC_CHANNEL8, BRC_CHANNEL9, BRC_CHANNEL10, BRC_CHANNEL11
 *
 * @api
 */
void gtm_brcAckInt(GTM_BRCDriver *brcd, uint8_t channel, uint8_t int_num) {

	brcd->brc->IRQ_NOTIFY.R |= ((uint32_t)int_num << (channel + 1U));
}

/**
 * @brief   Set BRC Channel IRQ mode (Level, Pulse, Notify, Single)
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @param[in] mode        GTM BRC channel IRQ mode
 *
 * @sa
 * SPC5_GTM_BRC_IRQ_MODE_LEVEL, SPC5_GTM_BRC_IRQ_MODE_PULSE,
 * SPC5_GTM_BRC_IRQ_MODE_PULSE_NOTIFY, SPC5_GTM_BRC_IRQ_MODE_SINGLE_PULSE
 *
 * @api
 */
void gtm_brcSetIRQMode(GTM_BRCDriver *brcd, uint8_t mode) {
	brcd->brc->IRQ_MODE.B.IRQ_MODE = mode;
}

/**
 * @brief   Enable BRC Channel interrupt
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @param[in] channel     GTM BRC channel number
 *
 * @param[in] int_num   GTM BRC interrupt to enable
 *
 * @sa
 * BRC_CHANNEL0, BRC_CHANNEL1, BRC_CHANNEL2, BRC_CHANNEL3, BRC_CHANNEL4, BRC_CHANNEL5, BRC_CHANNEL6, BRC_CHANNEL7,
 * <br>
 * BRC_CHANNEL8, BRC_CHANNEL9, BRC_CHANNEL10, BRC_CHANNEL11

 *
 * @api
 */
void gtm_brcEnableInt(GTM_BRCDriver *brcd, uint8_t channel, uint8_t int_num) {
	brcd->brc->IRQ_EN.R |= ((uint32_t)int_num << (channel + 1U));
}

/**
 * @brief   Disable BRC Channel interrupt
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @param[in] channel     GTM BRC channel number
 *
 * @param[in] int_num   GTM BRC interrupt to enable
 *
 * @sa
 * BRC_CHANNEL0, BRC_CHANNEL1, BRC_CHANNEL2, BRC_CHANNEL3, BRC_CHANNEL4, BRC_CHANNEL5, BRC_CHANNEL6, BRC_CHANNEL7,
 * <br>
 * BRC_CHANNEL8, BRC_CHANNEL9, BRC_CHANNEL10, BRC_CHANNEL11

 *
 * @api
 */
void gtm_brcDisableInt(GTM_BRCDriver *brcd, uint8_t channel, uint8_t int_num) {
	brcd->brc->IRQ_EN.R &= ~((uint32_t)int_num << (channel + 1U));
}

/**
 * @brief   Software notification of BRC Channel interrupt
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @param[in] channel     GTM BRC channel number
 *
 * @param[in] int_num   GTM BRC interrupt to enable
 *
 * @sa
 * BRC_CHANNEL0, BRC_CHANNEL1, BRC_CHANNEL2, BRC_CHANNEL3, BRC_CHANNEL4, BRC_CHANNEL5, BRC_CHANNEL6, BRC_CHANNEL7,
 * <br>
 * BRC_CHANNEL8, BRC_CHANNEL9, BRC_CHANNEL10, BRC_CHANNEL11

 *
 * @api
 */
void gtm_brcNotifyInt(GTM_BRCDriver *brcd, uint8_t channel, uint8_t int_num) {
	brcd->brc->IRQ_FORCINT.R  |= ((uint32_t)int_num << (channel + 1U));
	brcd->brc->IRQ_FORCINT.B.TRG_DEST_ERR = 1U;
}

/**
 * @brief   Return BRC interrupt status
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @return Interrupt status
 *
 * @api
 */
uint8_t gtm_brcGetPlausibilityIntStatus(GTM_BRCDriver *brcd) {
	return brcd->brc->IRQ_NOTIFY.B.DEST_ERR;
}


/**
 * @brief   Enable BRC Channel interrupt
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @return Interrupt enabled
 *
 * @api
 */
uint8_t gtm_brcGetPlausibilityIntEnabled(GTM_BRCDriver *brcd) {
	return brcd->brc->IRQ_EN.B.GTM_BRC_IRQ_EN_DEST_ERR_IRQ_EN_BIT;
}


/**
 * @brief   Acknowledge BRC Channel interrupt
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @param[in] int_num   GTM BRC interrupt to acknowledge
 *
 *
 * @api
 */
void gtm_brcAckPlausibilityInt(GTM_BRCDriver *brcd, uint8_t int_num) {

	brcd->brc->IRQ_NOTIFY.B.DEST_ERR = int_num;

}

/**
 * @brief   Enable BRC Channel interrupt
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @api
 */
void gtm_brcEnablePlausibilityInt(GTM_BRCDriver *brcd) {
	brcd->brc->IRQ_EN.B.GTM_BRC_IRQ_EN_DEST_ERR_IRQ_EN_BIT = 1U;
}

/**
 * @brief   Disable BRC Channel interrupt
 *
 * @param[in] brcd        GTM BRC driver pointer
 *
 * @api
 */
void gtm_brcDisablePlausibilityInt(GTM_BRCDriver *brcd) {
	brcd->brc->IRQ_EN.B.GTM_BRC_IRQ_EN_DEST_ERR_IRQ_EN_BIT = 0U;
}

/*lint +e621*/
#endif /* SPC5_GTM_USE_BRC */

/** @} */
