              ASM-MCS log file 
              ================ 

 tool name           : ASM-MCS
 tool version        : 0.9
 tool vendor         : Copyright (C) 2011-2016 by <PERSON>, Germany
 target architecture : mcs24-1
 source file         : source\mcs2.mcs
 assembled size      : 2496 bytes

 1) Symbol table content after assembling procedure
 ==================================================

     Type          | Name                           | Value 
     --------------+--------------------------------+----------
     Variable      |                  ATOM2_WRADDR0 | 0x12F
                   |                  ATOM3_WRADDR6 | 0x13D
                   |                   MCS2_WRADDR5 | 0xAC
                   |                   TIM4_WRADDR2 | 0x23
                   |                   F2A0_WRADDR2 | 0x53
                   |                  MCS5_WRADDR11 | 0xFA
                   |                  ATOM6_WRADDR3 | 0x152
                   |                  ATOM9_WRADDR6 | 0x16D
                   |                   MCS0_WRADDR1 | 0x78
                   |                   TIM1_WRADDR3 | 0xC
                   |                  MCS0_WRADDR21 | 0x8C
                   |                   F2A1_WRADDR2 | 0x5B
                   |                   MCS4_WRADDR5 | 0xDC
                   |            ISEC1_ADC_RECON_REQ | 0x800
                   |                    BRC_WRADDR1 | 0x62
                   |                  MCS3_WRADDR18 | 0xD1
                   |                   MCS3_WRADDR5 | 0xC4
                   |                           ACB4 | 0x4
                   |                             EN | 0x0
                   |                   MCS4_WRADDR0 | 0xD7
                   |                   DPLL_WRADDR7 | 0x186
                   |           MCS2_CH0_TRIGGER_BIT | 0x1
                   |                        SAT_MSK | 0x400
                   |                         CY_MSK | 0x10
                   |                  ATOM4_WRADDR5 | 0x144
                   |                  ATOM7_WRADDR7 | 0x15E
                   |                  MCS0_WRADDR15 | 0x86
                   |                   TIM5_WRADDR5 | 0x2E
                   |                   TIM6_WRADDR6 | 0x37
                   |                  ARU_FULL_ADDR | 0x1FF
                   |                   F2A0_WRADDR5 | 0x56
                   |                  MCS5_WRADDR18 | 0x101
                   |                  MCS6_WRADDR14 | 0x115
                   |                  ATOM7_WRADDR0 | 0x157
                   |                   F2A1_WRADDR6 | 0x5F
                   |                   MCS4_WRADDR9 | 0xE0
                   |                    BRC_WRADDR7 | 0x68
                   |                  MCS3_WRADDR16 | 0xCF
                   |                   MCS3_WRADDR7 | 0xC6
                   |                    BRC_WRADDR9 | 0x6A
                   |                  MCS3_WRADDR10 | 0xC9
                   |                  MCS1_WRADDR19 | 0xA2
                   |                  ATOM1_WRADDR3 | 0x12A
                   |                  ATOM5_WRADDR6 | 0x14D
                   |                   MCS2_WRADDR9 | 0xB0
                   |                  MCS2_WRADDR16 | 0xB7
                   |           MCS2_CH2_TRIGGER_BIT | 0x4
                   |                  MCS4_WRADDR16 | 0xE7
                   |                   MCS6_WRADDR0 | 0x107
                   |                  MCS4_WRADDR20 | 0xEB
                   |                  DPLL_WRADDR12 | 0x18B
                   |                  ATOM0_WRADDR4 | 0x123
                   |                  MCS1_WRADDR17 | 0xA0
                   |                  ATOM8_WRADDR0 | 0x15F
                   |                  ATOM0_WRADDR0 | 0x11F
                   |                   TIM3_WRADDR4 | 0x1D
                   |                  ATOM0_WRADDR7 | 0x126
                   |                  MCS1_WRADDR16 | 0x9F
                   |                  ATOM8_WRADDR3 | 0x162
                   |                  ATOM0_WRADDR3 | 0x122
                   |                       ACB1_MSK | 0x2
                   |                   MCS4_WRADDR2 | 0xD9
                   |                         EN_MSK | 0x1
                   |                   MCS5_WRADDR7 | 0xF6
                   |                   MCS6_WRADDR4 | 0x10B
                   |                  ATOM3_WRADDR2 | 0x139
                   |                   TIM0_WRADDR2 | 0x3
                   |                  ATOM0_WRADDR6 | 0x125
                   |                  ATOM8_WRADDR2 | 0x161
                   |                  ATOM0_WRADDR2 | 0x121
                   |                             CY | 0x4
                   |                  ATOM2_WRADDR1 | 0x130
                   |                  ATOM3_WRADDR1 | 0x138
                   |                    MOS1_5_OPEN | 0x1
                   |                  ATOM0_WRADDR1 | 0x120
                   |                   F2A0_WRADDR3 | 0x54
                   |                  MCS5_WRADDR16 | 0xFF
                   |                  MCS1_WRADDR21 | 0xA4
                   |                   MCS1_WRADDR6 | 0x95
                   |                  ATOM2_WRADDR4 | 0x133
                   |                   MCS2_WRADDR7 | 0xAE
                   |                  MCS5_WRADDR21 | 0x104
                   |                   TIM4_WRADDR4 | 0x25
                   |                   MCS5_WRADDR1 | 0xF0
                   |                  MCS2_WRADDR12 | 0xB3
                   |                  MCS3_WRADDR11 | 0xCA
                   |                  MCS1_WRADDR18 | 0xA1
                   |                        ERR_MSK | 0x4
                   |                  ATOM9_WRADDR3 | 0x16A
                   |                   MCS0_WRADDR4 | 0x7B
                   |                   TIM3_WRADDR1 | 0x1A
                   |                   MCS5_WRADDR0 | 0xEF
                   |                   TIM0_WRADDR1 | 0x2
                   |                  ATOM1_WRADDR7 | 0x12E
                   |                  ATOM5_WRADDR2 | 0x149
                   |                            IRQ | 0x1
                   |                  MCS2_WRADDR15 | 0xB6
                   |                            ERR | 0x2
                   |                            MCA | 0x3
                   |                              Z | 0x5
                   |                              V | 0x6
                   |                              N | 0x7
                   |                            CAT | 0x8
                   |                  ATOM6_WRADDR4 | 0x153
                   |                  ATOM9_WRADDR5 | 0x16C
                   |                   MCS0_WRADDR2 | 0x79
                   |                   TIM1_WRADDR0 | 0x9
                   |                            CWT | 0x9
                   |                            SAT | 0xA
                   |                        IRQ_MSK | 0x2
                   |                        MCA_MSK | 0x8
                   |                   MCS1_WRADDR5 | 0x94
                   |                          Z_MSK | 0x20
                   |                          V_MSK | 0x40
                   |                          N_MSK | 0x80
                   |                        CAT_MSK | 0x100
                   |                        CWT_MSK | 0x200
                   |                  MCS2_WRADDR13 | 0xB4
                   |                           ACB0 | 0x0
                   |                           ACB1 | 0x1
                   |                   TIM2_WRADDR5 | 0x16
                   |                           ACB2 | 0x2
                   |                   TIM2_WRADDR4 | 0x15
                   |                           ACB3 | 0x3
                   |                   TIM2_WRADDR7 | 0x18
                   |                       ACB0_MSK | 0x1
                   |                  MCS5_WRADDR19 | 0x102
                   |                  MCS6_WRADDR13 | 0x114
                   |                       ACB2_MSK | 0x4
                   |                       ACB3_MSK | 0x8
                   |                       ACB4_MSK | 0x10
                   |                   MCS1_WRADDR0 | 0x8F
                   |                     ARU_ACCESS | 0x0
                   |                   TIM0_WRADDR0 | 0x1
                   |                  ATOM2_WRADDR3 | 0x132
                   |                  ATOM3_WRADDR3 | 0x13A
                   |                   TIM0_WRADDR3 | 0x4
                   |                   TIM0_WRADDR4 | 0x5
                   |                   TIM0_WRADDR5 | 0x6
                   |                   TIM0_WRADDR6 | 0x7
                   |                   TIM0_WRADDR7 | 0x8
                   |                  ATOM6_WRADDR5 | 0x154
                   |                  ATOM9_WRADDR4 | 0x16B
                   |                   MCS0_WRADDR3 | 0x7A
                   |                   TIM1_WRADDR1 | 0xA
                   |                  ATOM6_WRADDR2 | 0x151
                   |                  ATOM9_WRADDR7 | 0x16E
                   |                   MCS0_WRADDR0 | 0x77
                   |                   TIM1_WRADDR2 | 0xB
                   |                   TIM1_WRADDR4 | 0xD
                   |                   TIM1_WRADDR5 | 0xE
                   |                  ATOM6_WRADDR6 | 0x155
                   |                   TIM1_WRADDR6 | 0xF
                   |                  ATOM6_WRADDR7 | 0x156
                   |                   TIM1_WRADDR7 | 0x10
                   |                   TIM2_WRADDR0 | 0x11
                   |                   TIM2_WRADDR1 | 0x12
                   |                   TIM2_WRADDR2 | 0x13
                   |                   TIM2_WRADDR3 | 0x14
                   |                   TIM2_WRADDR6 | 0x17
                   |                   TIM3_WRADDR0 | 0x19
                   |                  ATOM9_WRADDR2 | 0x169
                   |                   MCS0_WRADDR5 | 0x7C
                   |                   TIM3_WRADDR2 | 0x1B
                   |                  ATOM6_WRADDR0 | 0x14F
                   |                  ATOM9_WRADDR1 | 0x168
                   |                   MCS0_WRADDR6 | 0x7D
                   |                   TIM3_WRADDR3 | 0x1C
                   |                   TIM3_WRADDR5 | 0x1E
                   |                   TIM3_WRADDR6 | 0x1F
                   |                   TIM3_WRADDR7 | 0x20
                   |                   TIM4_WRADDR0 | 0x21
                   |                   TIM4_WRADDR1 | 0x22
                   |                   MCS2_WRADDR4 | 0xAB
                   |                  MCS5_WRADDR20 | 0x103
                   |                   TIM4_WRADDR3 | 0x24
                   |                   MCS2_WRADDR6 | 0xAD
                   |                  MCS5_WRADDR22 | 0x105
                   |                   TIM4_WRADDR5 | 0x26
                   |                   MCS2_WRADDR1 | 0xA8
                   |                  MCS5_WRADDR23 | 0x106
                   |                   TIM4_WRADDR6 | 0x27
                   |                   MCS2_WRADDR0 | 0xA7
                   |                   TIM4_WRADDR7 | 0x28
                   |                  ATOM4_WRADDR0 | 0x13F
                   |                  MCS0_WRADDR16 | 0x87
                   |                   TIM5_WRADDR0 | 0x29
                   |                  ATOM4_WRADDR1 | 0x140
                   |                  MCS0_WRADDR19 | 0x8A
                   |                   TIM5_WRADDR1 | 0x2A
                   |                  ATOM4_WRADDR2 | 0x141
                   |                  ATOM7_WRADDR6 | 0x15D
                   |                  MCS0_WRADDR14 | 0x85
                   |                   TIM5_WRADDR2 | 0x2B
                   |                  ATOM4_WRADDR3 | 0x142
                   |                  MCS0_WRADDR17 | 0x88
                   |                   TIM5_WRADDR3 | 0x2C
                   |                  ATOM4_WRADDR4 | 0x143
                   |                  ATOM7_WRADDR4 | 0x15B
                   |                  MCS0_WRADDR12 | 0x83
                   |                   TIM5_WRADDR4 | 0x2D
                   |                  ATOM4_WRADDR6 | 0x145
                   |                  ATOM7_WRADDR2 | 0x159
                   |                   F2A1_WRADDR4 | 0x5D
                   |                  MCS0_WRADDR10 | 0x81
                   |                   TIM5_WRADDR6 | 0x2F
                   |                  ATOM4_WRADDR7 | 0x146
                   |                  ATOM7_WRADDR5 | 0x15C
                   |                  MCS0_WRADDR13 | 0x84
                   |                   TIM5_WRADDR7 | 0x30
                   |                   TIM6_WRADDR0 | 0x31
                   |                   TIM6_WRADDR1 | 0x32
                   |                   TIM6_WRADDR2 | 0x33
                   |                   TIM6_WRADDR3 | 0x34
                   |                   TIM6_WRADDR4 | 0x35
                   |                   TIM6_WRADDR5 | 0x36
                   |                   TIM6_WRADDR7 | 0x38
                   |                   F2A0_WRADDR0 | 0x51
                   |                  MCS5_WRADDR17 | 0x100
                   |                  MCS6_WRADDR19 | 0x11A
                   |                   F2A0_WRADDR1 | 0x52
                   |                  MCS5_WRADDR14 | 0xFD
                   |            MCS2_XMOS_CYL4_ADDR | 0x65
                   |                  MCS6_WRADDR18 | 0x119
                   |                   F2A0_WRADDR4 | 0x55
                   |                  MCS6_WRADDR15 | 0x116
                   |                   F2A0_WRADDR6 | 0x57
                   |                  MCS5_WRADDR15 | 0xFE
                   |                  MCS6_WRADDR17 | 0x118
                   |                   F2A0_WRADDR7 | 0x58
                   |                  MCS6_WRADDR16 | 0x117
                   |                   F2A1_WRADDR0 | 0x59
                   |                   F2A1_WRADDR1 | 0x5A
                   |                   F2A1_WRADDR3 | 0x5C
                   |                  ATOM7_WRADDR3 | 0x15A
                   |                   F2A1_WRADDR5 | 0x5E
                   |                  MCS0_WRADDR11 | 0x82
                   |                  ATOM7_WRADDR1 | 0x158
                   |                   F2A1_WRADDR7 | 0x60
                   |                    BRC_WRADDR0 | 0x61
                   |                  MCS3_WRADDR17 | 0xD0
                   |                   MCS3_WRADDR6 | 0xC5
                   |                    BRC_WRADDR2 | 0x63
                   |                  MCS3_WRADDR19 | 0xD2
                   |                   MCS3_WRADDR4 | 0xC3
                   |                    BRC_WRADDR3 | 0x64
                   |                    BRC_WRADDR4 | 0x65
                   |                  MCS3_WRADDR13 | 0xCC
                   |                   MCS3_WRADDR2 | 0xC1
                   |                    BRC_WRADDR5 | 0x66
                   |               ISEC0_ADC_PERIOD | 0xA
                   |                   MCS3_WRADDR1 | 0xC0
                   |                  MCS3_WRADDR14 | 0xCD
                   |                    BRC_WRADDR6 | 0x67
                   |                   MCS3_WRADDR0 | 0xBF
                   |                  MCS3_WRADDR15 | 0xCE
                   |                    BRC_WRADDR8 | 0x69
                   |                   BRC_WRADDR10 | 0x6B
                   |                   BRC_WRADDR11 | 0x6C
                   |                   BRC_WRADDR12 | 0x6D
                   |                   BRC_WRADDR13 | 0x6E
                   |                   BRC_WRADDR14 | 0x6F
                   |                   BRC_WRADDR15 | 0x70
                   |                   BRC_WRADDR16 | 0x71
                   |                   MCS2_WRADDR8 | 0xAF
                   |                   BRC_WRADDR17 | 0x72
                   |                 ATOM10_WRADDR0 | 0x16F
                   |                   BRC_WRADDR18 | 0x73
                   |                 ATOM10_WRADDR1 | 0x170
                   |                   BRC_WRADDR19 | 0x74
                   |                   BRC_WRADDR20 | 0x75
                   |                   BRC_WRADDR21 | 0x76
                   |                  ATOM6_WRADDR1 | 0x150
                   |                  ATOM9_WRADDR0 | 0x167
                   |                   MCS0_WRADDR7 | 0x7E
                   |                   MCS0_WRADDR8 | 0x7F
                   |                   MCS0_WRADDR9 | 0x80
                   |                  MCS0_WRADDR18 | 0x89
                   |                  MCS0_WRADDR20 | 0x8B
                   |                  MCS0_WRADDR22 | 0x8D
                   |                  MCS0_WRADDR23 | 0x8E
                   |                   MCS1_WRADDR1 | 0x90
                   |                  MCS1_WRADDR20 | 0xA3
                   |                   MCS1_WRADDR2 | 0x91
                   |                   MCS1_WRADDR3 | 0x92
                   |                  MCS1_WRADDR23 | 0xA6
                   |                   MCS1_WRADDR4 | 0x93
                   |                  MCS1_WRADDR22 | 0xA5
                   |                   MCS1_WRADDR7 | 0x96
                   |                   MCS1_WRADDR8 | 0x97
                   |                   MCS1_WRADDR9 | 0x98
                   |                  ATOM8_WRADDR5 | 0x164
                   |                  MCS1_WRADDR10 | 0x99
                   |                  MCS1_WRADDR11 | 0x9A
                   |                  ATOM8_WRADDR7 | 0x166
                   |                  MCS1_WRADDR12 | 0x9B
                   |                  ATOM8_WRADDR4 | 0x163
                   |                  MCS1_WRADDR13 | 0x9C
                   |                  ATOM0_WRADDR5 | 0x124
                   |                  MCS1_WRADDR14 | 0x9D
                   |                  ATOM8_WRADDR1 | 0x160
                   |                  ATOM8_WRADDR6 | 0x165
                   |                  MCS1_WRADDR15 | 0x9E
                   |                   MCS2_WRADDR2 | 0xA9
                   |                   MCS2_WRADDR3 | 0xAA
                   |                  MCS2_WRADDR10 | 0xB1
                   |                  MCS2_WRADDR11 | 0xB2
                   |                  MCS2_WRADDR14 | 0xB5
                   |                  MCS2_WRADDR17 | 0xB8
                   |                  MCS2_WRADDR18 | 0xB9
                   |                  MCS2_WRADDR19 | 0xBA
                   |                  MCS2_WRADDR20 | 0xBB
                   |                   MCS5_WRADDR5 | 0xF4
                   |                  MCS4_WRADDR18 | 0xE9
                   |                   MCS6_WRADDR6 | 0x10D
                   |                  MCS2_WRADDR21 | 0xBC
                   |                   MCS5_WRADDR4 | 0xF3
                   |                   MCS6_WRADDR7 | 0x10E
                   |                  MCS2_WRADDR22 | 0xBD
                   |                   MCS5_WRADDR3 | 0xF2
                   |                   MCS6_WRADDR8 | 0x10F
                   |                  MCS2_WRADDR23 | 0xBE
                   |                   MCS5_WRADDR2 | 0xF1
                   |                  MCS4_WRADDR19 | 0xEA
                   |                   MCS6_WRADDR9 | 0x110
                   |                  MCS3_WRADDR12 | 0xCB
                   |                   MCS3_WRADDR3 | 0xC2
                   |                   MCS3_WRADDR8 | 0xC7
                   |                   MCS3_WRADDR9 | 0xC8
                   |                  MCS3_WRADDR20 | 0xD3
                   |                  MCS3_WRADDR21 | 0xD4
                   |                  MCS3_WRADDR22 | 0xD5
                   |                  MCS3_WRADDR23 | 0xD6
                   |                   MCS4_WRADDR1 | 0xD8
                   |                   MCS4_WRADDR3 | 0xDA
                   |                   MCS4_WRADDR4 | 0xDB
                   |                   MCS4_WRADDR6 | 0xDD
                   |                   MCS4_WRADDR7 | 0xDE
                   |                   MCS4_WRADDR8 | 0xDF
                   |                  MCS4_WRADDR10 | 0xE1
                   |                  MCS4_WRADDR11 | 0xE2
                   |                   MCS6_WRADDR1 | 0x108
                   |                  MCS4_WRADDR12 | 0xE3
                   |                  MCS5_WRADDR13 | 0xFC
                   |                  MCS4_WRADDR13 | 0xE4
                   |                  MCS5_WRADDR12 | 0xFB
                   |                  MCS4_WRADDR14 | 0xE5
                   |                   MCS5_WRADDR9 | 0xF8
                   |                   MCS6_WRADDR2 | 0x109
                   |                  MCS4_WRADDR15 | 0xE6
                   |                   MCS5_WRADDR6 | 0xF5
                   |                   MCS6_WRADDR5 | 0x10C
                   |                  MCS4_WRADDR17 | 0xE8
                   |                   MCS5_WRADDR8 | 0xF7
                   |                   MCS6_WRADDR3 | 0x10A
                   |                  MCS4_WRADDR21 | 0xEC
                   |                  MCS4_WRADDR22 | 0xED
                   |                  MCS4_WRADDR23 | 0xEE
                   |                  MCS5_WRADDR10 | 0xF9
                   |                  MCS6_WRADDR10 | 0x111
                   |                  MCS6_WRADDR11 | 0x112
                   |                  MCS6_WRADDR12 | 0x113
                   |                  MCS6_WRADDR20 | 0x11B
                   |                  MCS6_WRADDR21 | 0x11C
                   |                  MCS6_WRADDR22 | 0x11D
                   |                  MCS6_WRADDR23 | 0x11E
                   |                  ATOM1_WRADDR0 | 0x127
                   |                  ATOM5_WRADDR3 | 0x14A
                   |                  ATOM1_WRADDR1 | 0x128
                   |                  ATOM5_WRADDR4 | 0x14B
                   |                  ATOM1_WRADDR2 | 0x129
                   |                  ATOM5_WRADDR5 | 0x14C
                   |                  ATOM1_WRADDR4 | 0x12B
                   |                  ATOM1_WRADDR5 | 0x12C
                   |                  ATOM5_WRADDR0 | 0x147
                   |                  ATOM1_WRADDR6 | 0x12D
                   |                  ATOM5_WRADDR1 | 0x148
                   |                  ATOM2_WRADDR2 | 0x131
                   |                  ATOM3_WRADDR0 | 0x137
                   |                  ATOM2_WRADDR5 | 0x134
                   |                  ATOM3_WRADDR5 | 0x13C
                   |                  ATOM2_WRADDR6 | 0x135
                   |                  ATOM3_WRADDR4 | 0x13B
                   |                  ATOM2_WRADDR7 | 0x136
                   |                  ATOM3_WRADDR7 | 0x13E
                   |                  ATOM5_WRADDR7 | 0x14E
                   |                 ATOM10_WRADDR2 | 0x171
                   |                 ATOM10_WRADDR3 | 0x172
                   |                 ATOM10_WRADDR4 | 0x173
                   |                 ATOM10_WRADDR5 | 0x174
                   |                 ATOM10_WRADDR6 | 0x175
                   |                 ATOM10_WRADDR7 | 0x176
                   |                 ATOM11_WRADDR0 | 0x177
                   |                 ATOM11_WRADDR1 | 0x178
                   |                 ATOM11_WRADDR2 | 0x179
                   |                 ATOM11_WRADDR3 | 0x17A
                   |                 ATOM11_WRADDR4 | 0x17B
                   |                 ATOM11_WRADDR5 | 0x17C
                   |                 ATOM11_WRADDR6 | 0x17D
                   |                 ATOM11_WRADDR7 | 0x17E
                   |                   DPLL_WRADDR0 | 0x17F
                   |                   DPLL_WRADDR1 | 0x180
                   |                   DPLL_WRADDR2 | 0x181
                   |                   DPLL_WRADDR3 | 0x182
                   |                   DPLL_WRADDR4 | 0x183
                   |                   DPLL_WRADDR5 | 0x184
                   |                   DPLL_WRADDR6 | 0x185
                   |                   DPLL_WRADDR8 | 0x187
                   |                   DPLL_WRADDR9 | 0x188
                   |                  DPLL_WRADDR10 | 0x189
                   |                  DPLL_WRADDR11 | 0x18A
                   |                  DPLL_WRADDR13 | 0x18C
                   |                  DPLL_WRADDR14 | 0x18D
                   |                  DPLL_WRADDR15 | 0x18E
                   |                  DPLL_WRADDR16 | 0x18F
                   |                  DPLL_WRADDR17 | 0x190
                   |                  DPLL_WRADDR18 | 0x191
                   |                  DPLL_WRADDR19 | 0x192
                   |                  DPLL_WRADDR20 | 0x193
                   |                  DPLL_WRADDR21 | 0x194
                   |                  DPLL_WRADDR22 | 0x195
                   |                  DPLL_WRADDR23 | 0x196
                   |                  DPLL_WRADDR24 | 0x197
                   |                  DPLL_WRADDR25 | 0x198
                   |            MCS2_XMOS_CYL2_ADDR | 0x63
                   |                  DPLL_WRADDR26 | 0x199
                   |                  DPLL_WRADDR27 | 0x19A
                   |                  DPLL_WRADDR28 | 0x19B
                   |                  DPLL_WRADDR29 | 0x19C
                   |                  DPLL_WRADDR30 | 0x19D
                   |                  DPLL_WRADDR31 | 0x19E
                   |                 ARU_EMPTY_ADDR | 0x1FE
                   |           MCS_CHX_DISABLE_MASK | 0xFFFFE
                   |          MCS2_PMOS0_4_PORT_IDX | 0x0
                   |          MCS2_NMOS0_4_PORT_IDX | 0x1
                   |          MCS2_PMOS1_5_PORT_IDX | 0x2
                   |          MCS2_NMOS1_5_PORT_IDX | 0x3
                   |          MCS2_PMOS2_6_PORT_IDX | 0x4
                   |          MCS2_NMOS2_6_PORT_IDX | 0x5
                   |          MCS2_PMOS3_7_PORT_IDX | 0x6
                   |          MCS2_NMOS3_7_PORT_IDX | 0x7
                   |            MCS2_IPRI0_PORT_IDX | 0x8
                   |            MCS2_ISEC0_PORT_IDX | 0x8
                   |            MCS2_IPRI1_PORT_IDX | 0x9
                   |            MCS2_ISEC1_PORT_IDX | 0x9
                   |            MCS2_XMOS_CYL0_ADDR | 0x61
                   |            MCS2_XMOS_CYL6_ADDR | 0x67
                   |            MCS2_XMOS_CYL1_ADDR | 0x62
                   |            MCS2_XMOS_CYL3_ADDR | 0x64
                   |            MCS2_XMOS_CYL5_ADDR | 0x66
                   |            MCS2_XMOS_CYL7_ADDR | 0x68
                   |                MCS2_IPRI0_ADDR | 0x69
                   |                MCS2_IPRI1_ADDR | 0x6A
                   |               NMOS0_START_ADDR | 0xB1
                   |               NMOS1_START_ADDR | 0xB2
                   |               NMOS2_START_ADDR | 0xB3
                   |               NMOS3_START_ADDR | 0xB4
                   |               NMOS4_START_ADDR | 0xB5
                   |               NMOS5_START_ADDR | 0xB6
                   |               NMOS6_START_ADDR | 0xB7
                   |               NMOS7_START_ADDR | 0xB8
                   |           MCS2_CH1_TRIGGER_BIT | 0x2
                   |           MCS2_CH3_TRIGGER_BIT | 0x8
                   |           MCS2_CH4_TRIGGER_BIT | 0x10
                   |           MCS2_CH5_TRIGGER_BIT | 0x20
                   |           MCS2_CH6_TRIGGER_BIT | 0x40
                   |           MCS2_CH7_TRIGGER_BIT | 0x80
                   |            IPRI0_ADC_RECON_REQ | 0x100
                   |            ISEC0_ADC_RECON_REQ | 0x200
                   |            IPRI1_ADC_RECON_REQ | 0x400
                   |     CPU_REOPEN_PMOS0_4_TRG_BIT | 0x1000
                   |     CPU_REOPEN_PMOS1_5_TRG_BIT | 0x2000
                   |     CPU_REOPEN_PMOS2_6_TRG_BIT | 0x4000
                   |     CPU_REOPEN_PMOS3_7_TRG_BIT | 0x8000
                   |               IPRI0_ADC_PERIOD | 0xA
                   |               IPRI1_ADC_PERIOD | 0xA
                   |               ISEC1_ADC_PERIOD | 0xA
                   |              ISEC_ADC_DURATION | 0x5DC
                   |                    MOS0_4_OPEN | 0x1
                   |                   MOS0_4_CLOSE | 0x0
                   |                   MOS1_5_CLOSE | 0x0
                   |                    MOS2_6_OPEN | 0x1
                   |                   MOS2_6_CLOSE | 0x0
                   |                    MOS3_7_OPEN | 0x1
                   |                   MOS3_7_CLOSE | 0x0
                   |              PMOS_REOPEN_DELAY | 0x32
                   |                     IPRI_FAULT | 0x999999
     --------------+--------------------------------+----------
     Label         |             PMOS0_4_STOP_DELAY | 0x2F4
                   |                PMOS0_4_LOOP_WA | 0x2B4
                   |                  SPARK_B0_LOOP | 0x898
                   |                   PMOS3_7_LOOP | 0x70C
                   |                     TSK2_STACK | 0xA0
                   |                     TSK7_STACK | 0x1E0
                   |                MOS5_WAIT_PARAM | 0x3F4
                   |                 NMOS0_4_ENABLE | 0x33C
                   |                     TSK0_STACK | 0x20
                   |           ISEC0_ADC_RECON_WAIT | 0x864
                   |        PMOS0_4_WAIT_TRG_REOPEN | 0x344
                   |             PMOS3_7_STOP_DELAY | 0x734
                   |                      TSK3_INIT | 0x660
                   |                     TSK6_STACK | 0x1A0
                   |                   ISEC_DISABLE | 0x8A4
                   |                     TSK3_STACK | 0xE0
                   |                 PMOS0_4_REOPEN | 0x370
                   |                    IPRI1_START | 0x8F8
                   |                MOS7_WAIT_PARAM | 0x6CC
                   |                      TSK0_INIT | 0x220
                   |                     TSK1_STACK | 0x60
                   |                     IPRI0_LOOP | 0x8B0
                   |                     TASK5_DONE | 0x9B0
                   |           PMOS2_6_STOP_TRIGGER | 0x5BC
                   |                         MOS1_5 | 0x3B8
                   |                  SPARK_B1_LOOP | 0x990
                   |                         MOS0_4 | 0x250
                   |        PMOS2_6_REOPEN_DELAY_WA | 0x634
                   |                 PMOS3_7_REOPEN | 0x7B4
                   |                     TSK5_STACK | 0x160
                   |                        PMOS0_4 | 0x290
                   |                 PMOS1_5_REOPEN | 0x4DC
                   |                     TASK0_DONE | 0x380
                   |                    ISEC0_START | 0x85C
                   |          NMOS0_4_WAIT_EVENT_WA | 0x30C
                   |                         MOS2_6 | 0x524
                   |                    TASK1_START | 0x3A8
                   |                MOS3_WAIT_PARAM | 0x6B4
                   |                     TSK4_STACK | 0x120
                   |                MOS1_WAIT_PARAM | 0x3DC
                   |        PMOS0_4_REOPEN_DELAY_WA | 0x35C
                   |                    TASK0_START | 0x240
                   |                           MOS0 | 0x264
                   |                        PMOS3_7 | 0x6D0
                   |                MOS0_WAIT_PARAM | 0x274
                   |                           MOS4 | 0x27C
                   |                MOS4_WAIT_PARAM | 0x28C
                   |                   PMOS0_4_LOOP | 0x2CC
                   |           PMOS0_4_STOP_TRIGGER | 0x2E8
                   |             NMOS0_4_WAIT_EVENT | 0x324
                   |           PMOS0_4_REOPEN_DELAY | 0x364
                   |                    IPRI0_START | 0x808
                   |                      TSK1_INIT | 0x388
                   |                           MOS1 | 0x3CC
                   |                           MOS5 | 0x3E4
                   |                        PMOS1_5 | 0x3F8
                   |                PMOS1_5_LOOP_WA | 0x41C
                   |                   PMOS1_5_LOOP | 0x434
                   |           PMOS1_5_STOP_TRIGGER | 0x450
                   |             PMOS1_5_STOP_DELAY | 0x45C
                   |          NMOS1_5_WAIT_EVENT_WA | 0x474
                   |                    TASK2_START | 0x514
                   |                MOS2_WAIT_PARAM | 0x548
                   |             NMOS1_5_WAIT_EVENT | 0x48C
                   |                 NMOS1_5_ENABLE | 0x4A8
                   |        PMOS1_5_WAIT_TRG_REOPEN | 0x4B0
                   |        PMOS1_5_REOPEN_DELAY_WA | 0x4C8
                   |           PMOS1_5_REOPEN_DELAY | 0x4D0
                   |                     TASK1_DONE | 0x4EC
                   |                      TSK2_INIT | 0x4F4
                   |                           MOS2 | 0x538
                   |                           MOS6 | 0x550
                   |                MOS6_WAIT_PARAM | 0x560
                   |                        PMOS2_6 | 0x564
                   |                PMOS2_6_LOOP_WA | 0x588
                   |                    TASK4_START | 0x7EC
                   |                   PMOS2_6_LOOP | 0x5A0
                   |             PMOS2_6_STOP_DELAY | 0x5C8
                   |           PMOS3_7_REOPEN_DELAY | 0x7A8
                   |          NMOS2_6_WAIT_EVENT_WA | 0x5E0
                   |             NMOS2_6_WAIT_EVENT | 0x5F8
                   |                 NMOS2_6_ENABLE | 0x614
                   |        PMOS2_6_WAIT_TRG_REOPEN | 0x61C
                   |           PMOS2_6_REOPEN_DELAY | 0x63C
                   |                 PMOS2_6_REOPEN | 0x648
                   |                     TASK2_DONE | 0x658
                   |                    TASK3_START | 0x680
                   |           ISEC1_ADC_RECON_WAIT | 0x95C
                   |                         MOS3_7 | 0x690
                   |                           MOS3 | 0x6A4
                   |                           MOS7 | 0x6BC
                   |               SPARK_B0_LOOP_WA | 0x890
                   |                PMOS3_7_LOOP_WA | 0x6F4
                   |           PMOS3_7_STOP_TRIGGER | 0x728
                   |          NMOS3_7_WAIT_EVENT_WA | 0x74C
                   |             NMOS3_7_WAIT_EVENT | 0x764
                   |                 NMOS3_7_ENABLE | 0x780
                   |        PMOS3_7_WAIT_TRG_REOPEN | 0x788
                   |        PMOS3_7_REOPEN_DELAY_WA | 0x7A0
                   |                     TASK3_DONE | 0x7C4
                   |                      TSK4_INIT | 0x7CC
                   |               IPRI0_WAIT_EVENT | 0x7FC
                   |           IPRI0_ADC_RECON_WAIT | 0x810
                   |                    ISEC1_START | 0x954
                   |                IPRI0_WAIT_STOP | 0x83C
                   |                     IPRI0_STOP | 0x848
                   |                     TASK4_DONE | 0x8B4
                   |                      TSK5_INIT | 0x8BC
                   |                    TASK5_START | 0x8DC
                   |               IPRI1_WAIT_EVENT | 0x8EC
                   |           IPRI1_ADC_RECON_WAIT | 0x900
                   |                IPRI1_WAIT_STOP | 0x92C
                   |                     IPRI1_STOP | 0x938
                   |               SPARK_B1_LOOP_WA | 0x988
                   |                     ISEC1_STOP | 0x99C
                   |                    IPRI1_ERROR | 0x9AC
                   |                      TSK6_INIT | 0x9B8
                   |                      TSK7_INIT | 0x9BC
     --------------+--------------------------------+----------
     User Register |                       PMOS_REG | 0x0
                   |                       NMOS_REG | 0x1
     --------------+--------------------------------+----------
     Register      |                             R0 | 0x0
                   |                             R1 | 0x1
                   |                             R2 | 0x2
                   |                            STA | 0x8
                   |                             R3 | 0x3
                   |                            MHB | 0xF
                   |                             R4 | 0x4
                   |                             R5 | 0x5
                   |                             R6 | 0x6
                   |                             R7 | 0x7
                   |                            ACB | 0x9
                   |                           CTRG | 0xA
                   |                           STRG | 0xB
                   |                        TBU_TS0 | 0xC
                   |                        TBU_TS1 | 0xD
                   |                        TBU_TS2 | 0xE
                   |                           ZERO | 0xC
     --------------+--------------------------------+----------

 2) Assembling Results 
 ======================


 ------------------ BEGIN OF FILE: source\mcs2.mcs 

                           |     1: ;/**************************************************************************** 
                           |     2: ;* 
                           |     3: ;* Copyright © 2019-2020 STMicroelectronics - All Rights Reserved 
                           |     4: ;* 
                           |     5: ;* License terms: STMicroelectronics Proprietary in accordance with licensing 
                           |     6: ;* terms SLA0089 at www.st.com 
                           |     7: ;*  
                           |     8: ;* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,  
                           |     9: ;* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE. 
                           |    10: ;* 
                           |    11: ;* EVALUATION ONLY - NOT FOR USE IN PRODUCTION 
                           |    12: ;*****************************************************************************/ 
                           |    13:  
                           |    14: ; 1) prepare assembler for MCS memory 
                           |    15: ; ------------------------------------- 
                           |    16: .include "mcs24_2.inc" 

 ------------------ BEGIN OF FILE: mcs24_2.inc 

                           |     1: 
                           |     2: ;----------------------------------------------
                           |     3: ; Bit definitions for MCS architecture MCS24-1
                           |     4: ;----------------------------------------------
                           |     5: 
                           |     6: ; bit definitions for register STA 
                           |     7: .define EN             0
                           |     8: .define IRQ            1
                           |     9: .define ERR            2
                           |    10: .define MCA            3
                           |    11: .define CY             4
                           |    12: .define Z              5
                           |    13: .define V              6
                           |    14: .define N              7
                           |    15: .define CAT            8
                           |    16: .define CWT            9
                           |    17: .define SAT            10
                           |    18: 
                           |    19: ; bit mask definitions for register STA 
                           |    20: .define EN_MSK          2**EN 
                           |    21: .define IRQ_MSK         2**IRQ
                           |    22: .define ERR_MSK         2**ERR
                           |    23: .define MCA_MSK         2**MCA
                           |    24: .define CY_MSK          2**CY
                           |    25: .define Z_MSK           2**Z
                           |    26: .define V_MSK           2**V
                           |    27: .define N_MSK           2**N
                           |    28: .define CAT_MSK         2**CAT
                           |    29: .define CWT_MSK         2**CWT
                           |    30: .define SAT_MSK         2**SAT
                           |    31: 
                           |    32: ; bit definitions for register ACB 
                           |    33: .define ACB0           0
                           |    34: .define ACB1           1
                           |    35: .define ACB2           2
                           |    36: .define ACB3           3
                           |    37: .define ACB4           4
                           |    38: 
                           |    39: ; bit mask definitions for register ACB 
                           |    40: .define ACB0_MSK           2**ACB0
                           |    41: .define ACB1_MSK           2**ACB1
                           |    42: .define ACB2_MSK           2**ACB2
                           |    43: .define ACB3_MSK           2**ACB3
                           |    44: .define ACB4_MSK           2**ACB4
                           |    45: 
                           |    46: ; ARU write addresses 
                           |    47: .define  ARU_ACCESS $000
                           |    48: .define  TIM0_WRADDR0 $001
                           |    49: .define  TIM0_WRADDR1 $002
                           |    50: .define  TIM0_WRADDR2 $003
                           |    51: .define  TIM0_WRADDR3 $004
                           |    52: .define  TIM0_WRADDR4 $005
                           |    53: .define  TIM0_WRADDR5 $006
                           |    54: .define  TIM0_WRADDR6 $007
                           |    55: .define  TIM0_WRADDR7 $008
                           |    56: .define  TIM1_WRADDR0 $009
                           |    57: .define  TIM1_WRADDR1 $00A
                           |    58: .define  TIM1_WRADDR2 $00B
                           |    59: .define  TIM1_WRADDR3 $00C
                           |    60: .define  TIM1_WRADDR4 $00D
                           |    61: .define  TIM1_WRADDR5 $00E
                           |    62: .define  TIM1_WRADDR6 $00F
                           |    63: .define  TIM1_WRADDR7 $010
                           |    64: .define  TIM2_WRADDR0 $011
                           |    65: .define  TIM2_WRADDR1 $012
                           |    66: .define  TIM2_WRADDR2 $013
                           |    67: .define  TIM2_WRADDR3 $014
                           |    68: .define  TIM2_WRADDR4 $015
                           |    69: .define  TIM2_WRADDR5 $016
                           |    70: .define  TIM2_WRADDR6 $017
                           |    71: .define  TIM2_WRADDR7 $018
                           |    72: .define  TIM3_WRADDR0 $019
                           |    73: .define  TIM3_WRADDR1 $01A
                           |    74: .define  TIM3_WRADDR2 $01B
                           |    75: .define  TIM3_WRADDR3 $01C
                           |    76: .define  TIM3_WRADDR4 $01D
                           |    77: .define  TIM3_WRADDR5 $01E
                           |    78: .define  TIM3_WRADDR6 $01F
                           |    79: .define  TIM3_WRADDR7 $020
                           |    80: .define  TIM4_WRADDR0 $021
                           |    81: .define  TIM4_WRADDR1 $022
                           |    82: .define  TIM4_WRADDR2 $023
                           |    83: .define  TIM4_WRADDR3 $024
                           |    84: .define  TIM4_WRADDR4 $025
                           |    85: .define  TIM4_WRADDR5 $026
                           |    86: .define  TIM4_WRADDR6 $027
                           |    87: .define  TIM4_WRADDR7 $028
                           |    88: .define  TIM5_WRADDR0 $029
                           |    89: .define  TIM5_WRADDR1 $02A
                           |    90: .define  TIM5_WRADDR2 $02B
                           |    91: .define  TIM5_WRADDR3 $02C
                           |    92: .define  TIM5_WRADDR4 $02D
                           |    93: .define  TIM5_WRADDR5 $02E
                           |    94: .define  TIM5_WRADDR6 $02F
                           |    95: .define  TIM5_WRADDR7 $030
                           |    96: .define  TIM6_WRADDR0 $031
                           |    97: .define  TIM6_WRADDR1 $032
                           |    98: .define  TIM6_WRADDR2 $033
                           |    99: .define  TIM6_WRADDR3 $034
                           |   100: .define  TIM6_WRADDR4 $035
                           |   101: .define  TIM6_WRADDR5 $036
                           |   102: .define  TIM6_WRADDR6 $037
                           |   103: .define  TIM6_WRADDR7 $038
                           |   104: .define  F2A0_WRADDR0 $051
                           |   105: .define  F2A0_WRADDR1 $052
                           |   106: .define  F2A0_WRADDR2 $053
                           |   107: .define  F2A0_WRADDR3 $054
                           |   108: .define  F2A0_WRADDR4 $055
                           |   109: .define  F2A0_WRADDR5 $056
                           |   110: .define  F2A0_WRADDR6 $057
                           |   111: .define  F2A0_WRADDR7 $058
                           |   112: .define  F2A1_WRADDR0 $059
                           |   113: .define  F2A1_WRADDR1 $05A
                           |   114: .define  F2A1_WRADDR2 $05B
                           |   115: .define  F2A1_WRADDR3 $05C
                           |   116: .define  F2A1_WRADDR4 $05D
                           |   117: .define  F2A1_WRADDR5 $05E
                           |   118: .define  F2A1_WRADDR6 $05F
                           |   119: .define  F2A1_WRADDR7 $060
                           |   120: .define  BRC_WRADDR0 $061
                           |   121: .define  BRC_WRADDR1 $062
                           |   122: .define  BRC_WRADDR2 $063
                           |   123: .define  BRC_WRADDR3 $064
                           |   124: .define  BRC_WRADDR4 $065
                           |   125: .define  BRC_WRADDR5 $066
                           |   126: .define  BRC_WRADDR6 $067
                           |   127: .define  BRC_WRADDR7 $068
                           |   128: .define  BRC_WRADDR8 $069
                           |   129: .define  BRC_WRADDR9 $06A
                           |   130: .define  BRC_WRADDR10 $06B
                           |   131: .define  BRC_WRADDR11 $06C
                           |   132: .define  BRC_WRADDR12 $06D
                           |   133: .define  BRC_WRADDR13 $06E
                           |   134: .define  BRC_WRADDR14 $06F
                           |   135: .define  BRC_WRADDR15 $070
                           |   136: .define  BRC_WRADDR16 $071
                           |   137: .define  BRC_WRADDR17 $072
                           |   138: .define  BRC_WRADDR18 $073
                           |   139: .define  BRC_WRADDR19 $074
                           |   140: .define  BRC_WRADDR20 $075
                           |   141: .define  BRC_WRADDR21 $076
                           |   142: .define  MCS0_WRADDR0 $077
                           |   143: .define  MCS0_WRADDR1 $078
                           |   144: .define  MCS0_WRADDR2 $079
                           |   145: .define  MCS0_WRADDR3 $07A
                           |   146: .define  MCS0_WRADDR4 $07B
                           |   147: .define  MCS0_WRADDR5 $07C
                           |   148: .define  MCS0_WRADDR6 $07D
                           |   149: .define  MCS0_WRADDR7 $07E
                           |   150: .define  MCS0_WRADDR8 $07F
                           |   151: .define  MCS0_WRADDR9 $080
                           |   152: .define  MCS0_WRADDR10 $081
                           |   153: .define  MCS0_WRADDR11 $082
                           |   154: .define  MCS0_WRADDR12 $083
                           |   155: .define  MCS0_WRADDR13 $084
                           |   156: .define  MCS0_WRADDR14 $085
                           |   157: .define  MCS0_WRADDR15 $086
                           |   158: .define  MCS0_WRADDR16 $087
                           |   159: .define  MCS0_WRADDR17 $088
                           |   160: .define  MCS0_WRADDR18 $089
                           |   161: .define  MCS0_WRADDR19 $08A
                           |   162: .define  MCS0_WRADDR20 $08B
                           |   163: .define  MCS0_WRADDR21 $08C
                           |   164: .define  MCS0_WRADDR22 $08D
                           |   165: .define  MCS0_WRADDR23 $08E
                           |   166: .define  MCS1_WRADDR0 $08F
                           |   167: .define  MCS1_WRADDR1 $090
                           |   168: .define  MCS1_WRADDR2 $091
                           |   169: .define  MCS1_WRADDR3 $092
                           |   170: .define  MCS1_WRADDR4 $093
                           |   171: .define  MCS1_WRADDR5 $094
                           |   172: .define  MCS1_WRADDR6 $095
                           |   173: .define  MCS1_WRADDR7 $096
                           |   174: .define  MCS1_WRADDR8 $097
                           |   175: .define  MCS1_WRADDR9 $098
                           |   176: .define  MCS1_WRADDR10 $099
                           |   177: .define  MCS1_WRADDR11 $09A
                           |   178: .define  MCS1_WRADDR12 $09B
                           |   179: .define  MCS1_WRADDR13 $09C
                           |   180: .define  MCS1_WRADDR14 $09D
                           |   181: .define  MCS1_WRADDR15 $09E
                           |   182: .define  MCS1_WRADDR16 $09F
                           |   183: .define  MCS1_WRADDR17 $0A0
                           |   184: .define  MCS1_WRADDR18 $0A1
                           |   185: .define  MCS1_WRADDR19 $0A2
                           |   186: .define  MCS1_WRADDR20 $0A3
                           |   187: .define  MCS1_WRADDR21 $0A4
                           |   188: .define  MCS1_WRADDR22 $0A5
                           |   189: .define  MCS1_WRADDR23 $0A6
                           |   190: .define  MCS2_WRADDR0 $0A7
                           |   191: .define  MCS2_WRADDR1 $0A8
                           |   192: .define  MCS2_WRADDR2 $0A9
                           |   193: .define  MCS2_WRADDR3 $0AA
                           |   194: .define  MCS2_WRADDR4 $0AB
                           |   195: .define  MCS2_WRADDR5 $0AC
                           |   196: .define  MCS2_WRADDR6 $0AD
                           |   197: .define  MCS2_WRADDR7 $0AE
                           |   198: .define  MCS2_WRADDR8 $0AF
                           |   199: .define  MCS2_WRADDR9 $0B0
                           |   200: .define  MCS2_WRADDR10 $0B1
                           |   201: .define  MCS2_WRADDR11 $0B2
                           |   202: .define  MCS2_WRADDR12 $0B3
                           |   203: .define  MCS2_WRADDR13 $0B4
                           |   204: .define  MCS2_WRADDR14 $0B5
                           |   205: .define  MCS2_WRADDR15 $0B6
                           |   206: .define  MCS2_WRADDR16 $0B7
                           |   207: .define  MCS2_WRADDR17 $0B8
                           |   208: .define  MCS2_WRADDR18 $0B9
                           |   209: .define  MCS2_WRADDR19 $0BA
                           |   210: .define  MCS2_WRADDR20 $0BB
                           |   211: .define  MCS2_WRADDR21 $0BC
                           |   212: .define  MCS2_WRADDR22 $0BD
                           |   213: .define  MCS2_WRADDR23 $0BE
                           |   214: .define  MCS3_WRADDR0 $0BF
                           |   215: .define  MCS3_WRADDR1 $0C0
                           |   216: .define  MCS3_WRADDR2 $0C1
                           |   217: .define  MCS3_WRADDR3 $0C2
                           |   218: .define  MCS3_WRADDR4 $0C3
                           |   219: .define  MCS3_WRADDR5 $0C4
                           |   220: .define  MCS3_WRADDR6 $0C5
                           |   221: .define  MCS3_WRADDR7 $0C6
                           |   222: .define  MCS3_WRADDR8 $0C7
                           |   223: .define  MCS3_WRADDR9 $0C8
                           |   224: .define  MCS3_WRADDR10 $0C9
                           |   225: .define  MCS3_WRADDR11 $0CA
                           |   226: .define  MCS3_WRADDR12 $0CB
                           |   227: .define  MCS3_WRADDR13 $0CC
                           |   228: .define  MCS3_WRADDR14 $0CD
                           |   229: .define  MCS3_WRADDR15 $0CE
                           |   230: .define  MCS3_WRADDR16 $0CF
                           |   231: .define  MCS3_WRADDR17 $0D0
                           |   232: .define  MCS3_WRADDR18 $0D1
                           |   233: .define  MCS3_WRADDR19 $0D2
                           |   234: .define  MCS3_WRADDR20 $0D3
                           |   235: .define  MCS3_WRADDR21 $0D4
                           |   236: .define  MCS3_WRADDR22 $0D5
                           |   237: .define  MCS3_WRADDR23 $0D6
                           |   238: .define  MCS4_WRADDR0 $0D7
                           |   239: .define  MCS4_WRADDR1 $0D8
                           |   240: .define  MCS4_WRADDR2 $0D9
                           |   241: .define  MCS4_WRADDR3 $0DA
                           |   242: .define  MCS4_WRADDR4 $0DB
                           |   243: .define  MCS4_WRADDR5 $0DC
                           |   244: .define  MCS4_WRADDR6 $0DD
                           |   245: .define  MCS4_WRADDR7 $0DE
                           |   246: .define  MCS4_WRADDR8 $0DF
                           |   247: .define  MCS4_WRADDR9 $0E0
                           |   248: .define  MCS4_WRADDR10 $0E1
                           |   249: .define  MCS4_WRADDR11 $0E2
                           |   250: .define  MCS4_WRADDR12 $0E3
                           |   251: .define  MCS4_WRADDR13 $0E4
                           |   252: .define  MCS4_WRADDR14 $0E5
                           |   253: .define  MCS4_WRADDR15 $0E6
                           |   254: .define  MCS4_WRADDR16 $0E7
                           |   255: .define  MCS4_WRADDR17 $0E8
                           |   256: .define  MCS4_WRADDR18 $0E9
                           |   257: .define  MCS4_WRADDR19 $0EA
                           |   258: .define  MCS4_WRADDR20 $0EB
                           |   259: .define  MCS4_WRADDR21 $0EC
                           |   260: .define  MCS4_WRADDR22 $0ED
                           |   261: .define  MCS4_WRADDR23 $0EE
                           |   262: .define  MCS5_WRADDR0 $0EF
                           |   263: .define  MCS5_WRADDR1 $0F0
                           |   264: .define  MCS5_WRADDR2 $0F1
                           |   265: .define  MCS5_WRADDR3 $0F2
                           |   266: .define  MCS5_WRADDR4 $0F3
                           |   267: .define  MCS5_WRADDR5 $0F4
                           |   268: .define  MCS5_WRADDR6 $0F5
                           |   269: .define  MCS5_WRADDR7 $0F6
                           |   270: .define  MCS5_WRADDR8 $0F7
                           |   271: .define  MCS5_WRADDR9 $0F8
                           |   272: .define  MCS5_WRADDR10 $0F9
                           |   273: .define  MCS5_WRADDR11 $0FA
                           |   274: .define  MCS5_WRADDR12 $0FB
                           |   275: .define  MCS5_WRADDR13 $0FC
                           |   276: .define  MCS5_WRADDR14 $0FD
                           |   277: .define  MCS5_WRADDR15 $0FE
                           |   278: .define  MCS5_WRADDR16 $0FF
                           |   279: .define  MCS5_WRADDR17 $100
                           |   280: .define  MCS5_WRADDR18 $101
                           |   281: .define  MCS5_WRADDR19 $102
                           |   282: .define  MCS5_WRADDR20 $103
                           |   283: .define  MCS5_WRADDR21 $104
                           |   284: .define  MCS5_WRADDR22 $105
                           |   285: .define  MCS5_WRADDR23 $106
                           |   286: .define  MCS6_WRADDR0 $107
                           |   287: .define  MCS6_WRADDR1 $108
                           |   288: .define  MCS6_WRADDR2 $109
                           |   289: .define  MCS6_WRADDR3 $10A
                           |   290: .define  MCS6_WRADDR4 $10B
                           |   291: .define  MCS6_WRADDR5 $10C
                           |   292: .define  MCS6_WRADDR6 $10D
                           |   293: .define  MCS6_WRADDR7 $10E
                           |   294: .define  MCS6_WRADDR8 $10F
                           |   295: .define  MCS6_WRADDR9 $110
                           |   296: .define  MCS6_WRADDR10 $111
                           |   297: .define  MCS6_WRADDR11 $112
                           |   298: .define  MCS6_WRADDR12 $113
                           |   299: .define  MCS6_WRADDR13 $114
                           |   300: .define  MCS6_WRADDR14 $115
                           |   301: .define  MCS6_WRADDR15 $116
                           |   302: .define  MCS6_WRADDR16 $117
                           |   303: .define  MCS6_WRADDR17 $118
                           |   304: .define  MCS6_WRADDR18 $119
                           |   305: .define  MCS6_WRADDR19 $11A
                           |   306: .define  MCS6_WRADDR20 $11B
                           |   307: .define  MCS6_WRADDR21 $11C
                           |   308: .define  MCS6_WRADDR22 $11D
                           |   309: .define  MCS6_WRADDR23 $11E
                           |   310: .define  ATOM0_WRADDR0 $11F
                           |   311: .define  ATOM0_WRADDR1 $120
                           |   312: .define  ATOM0_WRADDR2 $121
                           |   313: .define  ATOM0_WRADDR3 $122
                           |   314: .define  ATOM0_WRADDR4 $123
                           |   315: .define  ATOM0_WRADDR5 $124
                           |   316: .define  ATOM0_WRADDR6 $125
                           |   317: .define  ATOM0_WRADDR7 $126
                           |   318: .define  ATOM1_WRADDR0 $127
                           |   319: .define  ATOM1_WRADDR1 $128
                           |   320: .define  ATOM1_WRADDR2 $129
                           |   321: .define  ATOM1_WRADDR3 $12A
                           |   322: .define  ATOM1_WRADDR4 $12B
                           |   323: .define  ATOM1_WRADDR5 $12C
                           |   324: .define  ATOM1_WRADDR6 $12D
                           |   325: .define  ATOM1_WRADDR7 $12E
                           |   326: .define  ATOM2_WRADDR0 $12F
                           |   327: .define  ATOM2_WRADDR1 $130
                           |   328: .define  ATOM2_WRADDR2 $131
                           |   329: .define  ATOM2_WRADDR3 $132
                           |   330: .define  ATOM2_WRADDR4 $133
                           |   331: .define  ATOM2_WRADDR5 $134
                           |   332: .define  ATOM2_WRADDR6 $135
                           |   333: .define  ATOM2_WRADDR7 $136
                           |   334: .define  ATOM3_WRADDR0 $137
                           |   335: .define  ATOM3_WRADDR1 $138
                           |   336: .define  ATOM3_WRADDR2 $139
                           |   337: .define  ATOM3_WRADDR3 $13A
                           |   338: .define  ATOM3_WRADDR4 $13B
                           |   339: .define  ATOM3_WRADDR5 $13C
                           |   340: .define  ATOM3_WRADDR6 $13D
                           |   341: .define  ATOM3_WRADDR7 $13E
                           |   342: .define  ATOM4_WRADDR0 $13F
                           |   343: .define  ATOM4_WRADDR1 $140
                           |   344: .define  ATOM4_WRADDR2 $141
                           |   345: .define  ATOM4_WRADDR3 $142
                           |   346: .define  ATOM4_WRADDR4 $143
                           |   347: .define  ATOM4_WRADDR5 $144
                           |   348: .define  ATOM4_WRADDR6 $145
                           |   349: .define  ATOM4_WRADDR7 $146
                           |   350: .define  ATOM5_WRADDR0 $147
                           |   351: .define  ATOM5_WRADDR1 $148
                           |   352: .define  ATOM5_WRADDR2 $149
                           |   353: .define  ATOM5_WRADDR3 $14A
                           |   354: .define  ATOM5_WRADDR4 $14B
                           |   355: .define  ATOM5_WRADDR5 $14C
                           |   356: .define  ATOM5_WRADDR6 $14D
                           |   357: .define  ATOM5_WRADDR7 $14E
                           |   358: .define  ATOM6_WRADDR0 $14F
                           |   359: .define  ATOM6_WRADDR1 $150
                           |   360: .define  ATOM6_WRADDR2 $151
                           |   361: .define  ATOM6_WRADDR3 $152
                           |   362: .define  ATOM6_WRADDR4 $153
                           |   363: .define  ATOM6_WRADDR5 $154
                           |   364: .define  ATOM6_WRADDR6 $155
                           |   365: .define  ATOM6_WRADDR7 $156
                           |   366: .define  ATOM7_WRADDR0 $157
                           |   367: .define  ATOM7_WRADDR1 $158
                           |   368: .define  ATOM7_WRADDR2 $159
                           |   369: .define  ATOM7_WRADDR3 $15A
                           |   370: .define  ATOM7_WRADDR4 $15B
                           |   371: .define  ATOM7_WRADDR5 $15C
                           |   372: .define  ATOM7_WRADDR6 $15D
                           |   373: .define  ATOM7_WRADDR7 $15E
                           |   374: .define  ATOM8_WRADDR0 $15F
                           |   375: .define  ATOM8_WRADDR1 $160
                           |   376: .define  ATOM8_WRADDR2 $161
                           |   377: .define  ATOM8_WRADDR3 $162
                           |   378: .define  ATOM8_WRADDR4 $163
                           |   379: .define  ATOM8_WRADDR5 $164
                           |   380: .define  ATOM8_WRADDR6 $165
                           |   381: .define  ATOM8_WRADDR7 $166
                           |   382: .define  ATOM9_WRADDR0 $167
                           |   383: .define  ATOM9_WRADDR1 $168
                           |   384: .define  ATOM9_WRADDR2 $169
                           |   385: .define  ATOM9_WRADDR3 $16A
                           |   386: .define  ATOM9_WRADDR4 $16B
                           |   387: .define  ATOM9_WRADDR5 $16C
                           |   388: .define  ATOM9_WRADDR6 $16D
                           |   389: .define  ATOM9_WRADDR7 $16E
                           |   390: .define  ATOM10_WRADDR0 $16F
                           |   391: .define  ATOM10_WRADDR1 $170
                           |   392: .define  ATOM10_WRADDR2 $171
                           |   393: .define  ATOM10_WRADDR3 $172
                           |   394: .define  ATOM10_WRADDR4 $173
                           |   395: .define  ATOM10_WRADDR5 $174
                           |   396: .define  ATOM10_WRADDR6 $175
                           |   397: .define  ATOM10_WRADDR7 $176
                           |   398: .define  ATOM11_WRADDR0 $177
                           |   399: .define  ATOM11_WRADDR1 $178
                           |   400: .define  ATOM11_WRADDR2 $179
                           |   401: .define  ATOM11_WRADDR3 $17A
                           |   402: .define  ATOM11_WRADDR4 $17B
                           |   403: .define  ATOM11_WRADDR5 $17C
                           |   404: .define  ATOM11_WRADDR6 $17D
                           |   405: .define  ATOM11_WRADDR7 $17E
                           |   406: .define  DPLL_WRADDR0 $17F
                           |   407: .define  DPLL_WRADDR1 $180
                           |   408: .define  DPLL_WRADDR2 $181
                           |   409: .define  DPLL_WRADDR3 $182
                           |   410: .define  DPLL_WRADDR4 $183
                           |   411: .define  DPLL_WRADDR5 $184
                           |   412: .define  DPLL_WRADDR6 $185
                           |   413: .define  DPLL_WRADDR7 $186
                           |   414: .define  DPLL_WRADDR8 $187
                           |   415: .define  DPLL_WRADDR9 $188
                           |   416: .define  DPLL_WRADDR10 $189
                           |   417: .define  DPLL_WRADDR11 $18A
                           |   418: .define  DPLL_WRADDR12 $18B
                           |   419: .define  DPLL_WRADDR13 $18C
                           |   420: .define  DPLL_WRADDR14 $18D
                           |   421: .define  DPLL_WRADDR15 $18E
                           |   422: .define  DPLL_WRADDR16 $18F
                           |   423: .define  DPLL_WRADDR17 $190
                           |   424: .define  DPLL_WRADDR18 $191
                           |   425: .define  DPLL_WRADDR19 $192
                           |   426: .define  DPLL_WRADDR20 $193
                           |   427: .define  DPLL_WRADDR21 $194
                           |   428: .define  DPLL_WRADDR22 $195
                           |   429: .define  DPLL_WRADDR23 $196
                           |   430: .define  DPLL_WRADDR24 $197
                           |   431: .define  DPLL_WRADDR25 $198
                           |   432: .define  DPLL_WRADDR26 $199
                           |   433: .define  DPLL_WRADDR27 $19A
                           |   434: .define  DPLL_WRADDR28 $19B
                           |   435: .define  DPLL_WRADDR29 $19C
                           |   436: .define  DPLL_WRADDR30 $19D
                           |   437: .define  DPLL_WRADDR31 $19E
                           |   438: .define  ARU_EMPTY_ADDR $1FE
                           |   439: .define  ARU_FULL_ADDR $1FF
                           |   440: 
                           |   441:  
                           |   442:  

 ------------------ END OF FILE: mcs24_2.inc 

                           |    17:  
                           |    18: ; 2) define some constants 
                           |    19: ; ------------------------- 
                           |    20: .define MCS_CHx_DISABLE_MASK $FFFFE 
                           |    21:  
                           |    22: .register PMOS_REG       R0 
                           |    23: .register NMOS_REG       R1 
                           |    24:   
                           |    25: ; 
                           |    26: ; MCS Channel_0 defines 
                           |    27: ; 
                           |    28: .define MCS2_PMOS0_4_PORT_IDX              $0 
                           |    29: .define MCS2_NMOS0_4_PORT_IDX              $1 
                           |    30: .define MCS2_PMOS1_5_PORT_IDX              $2 
                           |    31: .define MCS2_NMOS1_5_PORT_IDX              $3 
                           |    32: .define MCS2_PMOS2_6_PORT_IDX              $4 
                           |    33: .define MCS2_NMOS2_6_PORT_IDX              $5 
                           |    34: .define MCS2_PMOS3_7_PORT_IDX              $6 
                           |    35: .define MCS2_NMOS3_7_PORT_IDX              $7 
                           |    36:  
                           |    37: .define MCS2_IPRI0_PORT_IDX               $8   ;ATOM0_4 
                           |    38: .define MCS2_ISEC0_PORT_IDX               $8   ; Same IPRI0 ATOM (ATOM0_4) 
                           |    39: .define MCS2_IPRI1_PORT_IDX               $9   ;ATOM0_6 
                           |    40: .define MCS2_ISEC1_PORT_IDX               $9   ; Same IPRI1 ATOM (ATOM0_6) 
                           |    41:      
                           |    42: .define MCS2_xMOS_CYL0_ADDR                BRC_WRADDR0 
                           |    43: .define MCS2_xMOS_CYL2_ADDR                BRC_WRADDR2 
                           |    44: .define MCS2_xMOS_CYL4_ADDR                BRC_WRADDR4 
                           |    45: .define MCS2_xMOS_CYL6_ADDR                BRC_WRADDR6 
                           |    46:  
                           |    47: .define MCS2_xMOS_CYL1_ADDR                BRC_WRADDR1 
                           |    48: .define MCS2_xMOS_CYL3_ADDR                BRC_WRADDR3 
                           |    49: .define MCS2_xMOS_CYL5_ADDR                BRC_WRADDR5 
                           |    50: .define MCS2_xMOS_CYL7_ADDR                BRC_WRADDR7 
                           |    51:  
                           |    52: .define MCS2_IPRI0_ADDR                    BRC_WRADDR8 
                           |    53: .define MCS2_IPRI1_ADDR                    BRC_WRADDR9 
                           |    54:  
                           |    55: .define NMOS0_START_ADDR                   MCS2_WRADDR10 
                           |    56: .define NMOS1_START_ADDR                   MCS2_WRADDR11 
                           |    57: .define NMOS2_START_ADDR                   MCS2_WRADDR12 
                           |    58: .define NMOS3_START_ADDR                   MCS2_WRADDR13 
                           |    59: .define NMOS4_START_ADDR                   MCS2_WRADDR14 
                           |    60: .define NMOS5_START_ADDR                   MCS2_WRADDR15 
                           |    61: .define NMOS6_START_ADDR                   MCS2_WRADDR16 
                           |    62: .define NMOS7_START_ADDR                   MCS2_WRADDR17 
                           |    63:  
                           |    64:  
                           |    65: .define MCS2_CH0_TRIGGER_BIT               $1       ;TRG.0 
                           |    66: .define MCS2_CH1_TRIGGER_BIT               $2       ;TRG.1 
                           |    67: .define MCS2_CH2_TRIGGER_BIT               $4       ;TRG.2 
                           |    68: .define MCS2_CH3_TRIGGER_BIT               $8       ;TRG.3 
                           |    69: .define MCS2_CH4_TRIGGER_BIT               $10      ;TRG.4 
                           |    70: .define MCS2_CH5_TRIGGER_BIT               $20      ;TRG.5 
                           |    71: .define MCS2_CH6_TRIGGER_BIT               $40      ;TRG.6 
                           |    72: .define MCS2_CH7_TRIGGER_BIT               $80      ;TRG.7 
                           |    73:  
                           |    74: .define IPRI0_ADC_RECON_REQ                $100     ; TRG.8 
                           |    75: .define ISEC0_ADC_RECON_REQ                $200     ; TRG.9 
                           |    76: .define IPRI1_ADC_RECON_REQ                $400     ; TR.10 
                           |    77: .define ISEC1_ADC_RECON_REQ                $800     ; TR.11 
                           |    78:  
                           |    79: .define CPU_REOPEN_PMOS0_4_TRG_BIT         $1000    ; TR.12 
                           |    80: .define CPU_REOPEN_PMOS1_5_TRG_BIT         $2000    ; TR.13 
                           |    81: .define CPU_REOPEN_PMOS2_6_TRG_BIT         $4000    ; TR.14 
                           |    82: .define CPU_REOPEN_PMOS3_7_TRG_BIT         $8000    ; TR.15 
                           |    83:  
                           |    84: .define IPRI0_ADC_PERIOD  10 
                           |    85: .define ISEC0_ADC_PERIOD  10 
                           |    86: .define IPRI1_ADC_PERIOD  10 
                           |    87: .define ISEC1_ADC_PERIOD  10 
                           |    88: .define ISEC_ADC_DURATION 1500 
                           |    89:  
                           |    90: .define MOS0_4_OPEN                        1 
                           |    91: .define MOS0_4_CLOSE                       0 
                           |    92: .define MOS1_5_OPEN                        1 
                           |    93: .define MOS1_5_CLOSE                       0 
                           |    94: .define MOS2_6_OPEN                        1 
                           |    95: .define MOS2_6_CLOSE                       0 
                           |    96: .define MOS3_7_OPEN                        1 
                           |    97: .define MOS3_7_CLOSE                       0 
                           |    98:  
                           |    99: .define PMOS_REOPEN_DELAY 50 
                           |   100: ;===================================================================== 
                           |   101: ; STATUS FLAGS 
                           |   102: ;=====================================================================   
                           |   103: .define IPRI_FAULT                          0x999999 
                           |   104:  
                           |   105: ; 3) initialize reset vectors of MCS channels 0 
                           |   106: ; ---------------------------------------------------- 
                           |   107: .org 0x0 
 [0x00000000] = 0xE0000220 |   108: jmp tsk0_init 
 [0x00000004] = 0xE0000388 |   109: jmp tsk1_init 
 [0x00000008] = 0xE00004F4 |   110: jmp tsk2_init 
 [0x0000000C] = 0xE0000660 |   111: jmp tsk3_init 
 [0x00000010] = 0xE00007CC |   112: jmp tsk4_init 
 [0x00000014] = 0xE00008BC |   113: jmp tsk5_init 
 [0x00000018] = 0xE00009B8 |   114: jmp tsk6_init 
 [0x0000001C] = 0xE00009BC |   115: jmp tsk7_init 
                           |   116:  
                           |   117: ; 4) allocate and initialize memory variables 
                           |   118: ; ------------------------------------------- 
                           |   119:  
                           |   120:  
                           |   121:  
                           |   122: ; 5) allocate stack frames (each task has 16 memory locations) 
                           |   123: ; ------------------------------------------------------------ 
                           |   124: ; MCS channels stack memory allocation 
                           |   125: ; 
                           |   126: tsk0_stack: 
                           |   127: .org (tsk0_stack+0x40) 
                           |   128: tsk1_stack: 
                           |   129: .org (tsk1_stack+0x40) 
                           |   130: tsk2_stack: 
                           |   131: .org (tsk2_stack+0x40) 
                           |   132: tsk3_stack: 
                           |   133: .org (tsk3_stack+0x40) 
                           |   134: tsk4_stack: 
                           |   135: .org (tsk4_stack+0x40) 
                           |   136: tsk5_stack: 
                           |   137: .org (tsk5_stack+0x40) 
                           |   138: tsk6_stack: 
                           |   139: .org (tsk6_stack+0x40) 
                           |   140: tsk7_stack: 
                           |   141: .org (tsk7_stack+0x40) 
                           |   142:  
                           |   143: ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; 
                           |   144: ; 6) program entry for MCS-channel 0 
                           |   145: ;  
                           |   146: ; ---------------------------------- 
                           |   147: ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; 
                           |   148: ;;;;;;;;;;;;;;; 
                           |   149: ; xMOS0-4 
                           |   150: ;;;;;;;;;;;;;;; 
                           |   151: tsk0_init: 
 [0x00000220] = 0x10000000 |   152:     movl R0, 0 
 [0x00000224] = 0x11000000 |   153: 	movl R1, 0 
 [0x00000228] = 0x12000000 |   154: 	movl R2, 0 
 [0x0000022C] = 0x13000000 |   155: 	movl R3, 0 
 [0x00000230] = 0x14000000 |   156: 	movl R4, 0 
 [0x00000234] = 0x15000000 |   157: 	movl R5, 0 
 [0x00000238] = 0x16000000 |   158: 	movl R6, 0 
 [0x0000023C] = 0x1700001C |   159: 	movl R7, (tsk0_stack-4) 
                           |   160:  
                           |   161: TASK0_START: 
 [0x00000240] = 0x10000001 |   162:     movl  R0, MCS2_CH0_TRIGGER_BIT 
 [0x00000244] = 0xF0B00001 |   163:     wurm  R0, STRG, MCS2_CH0_TRIGGER_BIT 
 [0x00000248] = 0xE8910220 |   164:     jbs STA CWT tsk0_init 
 [0x0000024C] = 0x1A000001 |   165:     movl  CTRG, MCS2_CH0_TRIGGER_BIT                 ; Clear start trigger 
                           |   166:  
                           |   167: MOS0_4: 
 [0x00000250] = 0xB0120065 |   168:     nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4 
 [0x00000254] = 0xE8A1027C |   169:     jbs STA SAT MOS4                                  ; SAT = 1 new cyl4 request 
 [0x00000258] = 0xB0120061 |   170:     nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0 
 [0x0000025C] = 0xE8A10264 |   171:     jbs STA SAT MOS0                                  ; SAT = 1 new cyl0 request 
 [0x00000260] = 0xE0000250 |   172:     jmp MOS0_4 
                           |   173:  
                           |   174: MOS0: 
 [0x00000264] = 0x70FFFFFF |   175:    atul R0, 0xFFFFFF 
 [0x00000268] = 0xE8520274 |   176:    jbc STA Z MOS0_WAIT_PARAM 
 [0x0000026C] = 0x71FFFFFF |   177:    atul R1, 0xFFFFFF 
 [0x00000270] = 0xE8510250 |   178:    jbs STA Z MOS0_4 
                           |   179: MOS0_WAIT_PARAM:   
 [0x00000274] = 0xB3500061 |   180:    ard R3, R5, MCS2_xMOS_CYL0_ADDR   ; R3 = pmos_duration, R5 = nmos_delay 
 [0x00000278] = 0xE0000290 |   181:    jmp PMOS0_4 
                           |   182:  
                           |   183: MOS4: 
 [0x0000027C] = 0x70FFFFFF |   184:    atul R0, 0xFFFFFF 
 [0x00000280] = 0xE852028C |   185:    jbc STA Z MOS4_WAIT_PARAM 
 [0x00000284] = 0x71FFFFFF |   186:    atul R1, 0xFFFFFF 
 [0x00000288] = 0xE8510250 |   187:    jbs STA Z MOS0_4 
                           |   188: MOS4_WAIT_PARAM: 
 [0x0000028C] = 0xB3500065 |   189:    ard R3, R5, MCS2_xMOS_CYL4_ADDR   ; R3 = pmos_duration, R5 = nmos_delay 
                           |   190:  
                           |   191: PMOS0_4: 
                           |   192: ;CLOSE MOS signals 
                           |   193: ; Stop NMOS  
 [0x00000290] = 0x19000000 |   194:     movl ACB, MOS0_4_CLOSE                            
 [0x00000294] = 0xB0110001 |   195:     awr R0, R1, MCS2_NMOS0_4_PORT_IDX 
                           |   196: ; Stop PMOS      
                           |   197: ;    movl ACB, MOS0_4_CLOSE 
                           |   198: ;    awr R0, R1, MCS2_PMOS0_4_PORT_IDX 
                           |   199:  
 [0x00000298] = 0x18000003 |   200:     movl STA, 0x000003   ; raise interrupt ADC ION setup 
                           |   201:  
 [0x0000029C] = 0x19000001 |   202:     movl ACB, MOS0_4_OPEN                             ; Start PMOS 
 [0x000002A0] = 0xB0110000 |   203:     awr R0, R1, MCS2_PMOS0_4_PORT_IDX                 ; PORT 0 of MCS2 ATOM2_0 
 [0x000002A4] = 0xA0E00000 |   204:     mov PMOS_REG, TBU_TS2 
                           |   205:  
 [0x000002A8] = 0xC0300000 |   206:     add PMOS_REG, R3 
 [0x000002AC] = 0xE84102B4 |   207:     jbs STA CY PMOS0_4_LOOP_WA 
 [0x000002B0] = 0xE00002CC |   208:     jmp PMOS0_4_LOOP 
                           |   209: PMOS0_4_LOOP_WA: 
 [0x000002B4] = 0xB0120061 |   210:     nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0 
 [0x000002B8] = 0xE8A102E8 |   211:     jbs STA SAT PMOS0_4_STOP_TRIGGER 
 [0x000002BC] = 0xB0120065 |   212:     nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4 
 [0x000002C0] = 0xE8A102E8 |   213:     jbs STA SAT PMOS0_4_STOP_TRIGGER 
 [0x000002C4] = 0xD0E00000 |   214:     atu PMOS_REG, TBU_TS2  
 [0x000002C8] = 0xE84102B4 |   215:     jbs STA CY PMOS0_4_LOOP_WA 
                           |   216: PMOS0_4_LOOP: 
 [0x000002CC] = 0xB0120061 |   217:     nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0 
 [0x000002D0] = 0xE8A102E8 |   218:     jbs STA SAT PMOS0_4_STOP_TRIGGER 
 [0x000002D4] = 0xB0120065 |   219:     nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4 
 [0x000002D8] = 0xE8A102E8 |   220:     jbs STA SAT PMOS0_4_STOP_TRIGGER 
 [0x000002DC] = 0xDE000000 |   221:     atu TBU_TS2, PMOS_REG 
 [0x000002E0] = 0xE84202F4 |   222:     jbc STA CY PMOS0_4_STOP_DELAY 
 [0x000002E4] = 0xE00002CC |   223:     jmp PMOS0_4_LOOP 
                           |   224:  
                           |   225: PMOS0_4_STOP_TRIGGER: 
 [0x000002E8] = 0x19000000 |   226:     movl ACB, MOS0_4_CLOSE                            ; Stop PMOS 
 [0x000002EC] = 0xB0110000 |   227:     awr R0, R1, MCS2_PMOS0_4_PORT_IDX                 ; PORT 0 of MCS2 ATOM2_0 
 [0x000002F0] = 0xE000033C |   228:     jmp NMOS0_4_ENABLE 
                           |   229:  
                           |   230: PMOS0_4_STOP_DELAY:     
 [0x000002F4] = 0x19000000 |   231:     movl ACB, MOS0_4_CLOSE                            ; Stop PMOS 
 [0x000002F8] = 0xB0110000 |   232:     awr R0, R1, MCS2_PMOS0_4_PORT_IDX                 ; PORT 0 of MCS2 ATOM2_0 
                           |   233:    
 [0x000002FC] = 0xA1E00000 |   234:     mov NMOS_REG, TBU_TS2 
 [0x00000300] = 0xC1500000 |   235:     add NMOS_REG, R5                                  ;NMOS_Delay 
 [0x00000304] = 0xE841030C |   236:     jbs STA CY NMOS0_4_WAIT_EVENT_WA 
 [0x00000308] = 0xE0000324 |   237:     jmp NMOS0_4_WAIT_EVENT 
                           |   238: NMOS0_4_WAIT_EVENT_WA: 
 [0x0000030C] = 0xB0120061 |   239:     nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0 
 [0x00000310] = 0xE8A1033C |   240:     jbs STA SAT NMOS0_4_ENABLE 
 [0x00000314] = 0xB0120065 |   241:     nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4 
 [0x00000318] = 0xE8A1033C |   242:     jbs STA SAT NMOS0_4_ENABLE 
 [0x0000031C] = 0xD1E00000 |   243:     atu NMOS_REG, TBU_TS2 
 [0x00000320] = 0xE841030C |   244:     jbs STA CY NMOS0_4_WAIT_EVENT_WA 
                           |   245: NMOS0_4_WAIT_EVENT:                                   ;NMOS Enable with the first event between NMOS_delay or Command-Out close trigger 
 [0x00000324] = 0xB0120061 |   246:     nard R0, R1, MCS2_xMOS_CYL0_ADDR                  ; wait trigger from BRC_DEST0 
 [0x00000328] = 0xE8A1033C |   247:     jbs STA SAT NMOS0_4_ENABLE 
 [0x0000032C] = 0xB0120065 |   248:     nard R0, R1, MCS2_xMOS_CYL4_ADDR                  ; wait trigger from BRC_DEST4 
 [0x00000330] = 0xE8A1033C |   249:     jbs STA SAT NMOS0_4_ENABLE 
 [0x00000334] = 0xDE100000 |   250:     atu TBU_TS2, NMOS_REG 
 [0x00000338] = 0xE8410324 |   251:     jbs STA CY NMOS0_4_WAIT_EVENT 
                           |   252:      
                           |   253: NMOS0_4_ENABLE: 
 [0x0000033C] = 0x19000001 |   254:     movl ACB, MOS0_4_OPEN                             ; Start NMOS  
 [0x00000340] = 0xB0110001 |   255:     awr R0, R1, MCS2_NMOS0_4_PORT_IDX                 ; PORT 1 of MCS2 ATOM2_1 
                           |   256:              
                           |   257:      
                           |   258: PMOS0_4_WAIT_TRG_REOPEN: 
 [0x00000344] = 0x9B001000 |   259:     btl STRG, CPU_REOPEN_PMOS0_4_TRG_BIT                ; (A & B) if TRUE Z=0; if FALSE Z=1  
 [0x00000348] = 0xE8510344 |   260:     jbs STA Z PMOS0_4_WAIT_TRG_REOPEN                   ; loop until the TRG bit is not set 
                           |   261:     ; delay before opening 
 [0x0000034C] = 0x10000032 |   262:     movl R0, PMOS_REOPEN_DELAY 
 [0x00000350] = 0xC0E00000 |   263:     add R0, TBU_TS2 
 [0x00000354] = 0xE841035C |   264:     jbs STA CY PMOS0_4_REOPEN_DELAY_WA 
 [0x00000358] = 0xE0000364 |   265:     jmp PMOS0_4_REOPEN_DELAY 
                           |   266:  
                           |   267: PMOS0_4_REOPEN_DELAY_WA: 
 [0x0000035C] = 0xD0E00000 |   268:     atu R0, tbu_ts2 
 [0x00000360] = 0xE842035C |   269:     jbc STA CY PMOS0_4_REOPEN_DELAY_WA 
                           |   270:  
                           |   271: PMOS0_4_REOPEN_DELAY: 
 [0x00000364] = 0xDE000000 |   272:     atu TBU_TS2, R0 
 [0x00000368] = 0xE8420370 |   273:     jbc STA CY PMOS0_4_REOPEN 
 [0x0000036C] = 0xE0000364 |   274:     jmp PMOS0_4_REOPEN_DELAY 
                           |   275:  
                           |   276: PMOS0_4_REOPEN: 
 [0x00000370] = 0x1A001000 |   277:     movl CTRG, CPU_REOPEN_PMOS0_4_TRG_BIT               ; clean trigger bit 
 [0x00000374] = 0x19000001 |   278:     movl ACB, MOS0_4_OPEN 
 [0x00000378] = 0xB0110000 |   279:     awr R0, R1, MCS2_PMOS0_4_PORT_IDX 
                           |   280:       
 [0x0000037C] = 0xE0000250 |   281:     jmp MOS0_4 
                           |   282:         
                           |   283: TASK0_DONE: 
                           |   284: ; Restart again 
                           |   285:     ;movl STA, 0x000003   ; raise interrupt 
 [0x00000380] = 0xE0000240 |   286:     jmp   TASK0_START 
                           |   287:  
                           |   288: ; Should not get here 
 [0x00000384] = 0x480FFFFE |   289:     andl STA MCS_CHx_DISABLE_MASK                     ; Disable MCS Channel 
                           |   290:  
                           |   291: ;;;;;;;;;;;;;;; 
                           |   292: ; xMOS1-5 
                           |   293: ;;;;;;;;;;;;;;; 
                           |   294: tsk1_init: 
 [0x00000388] = 0x10000000 |   295:     movl R0, 0 
 [0x0000038C] = 0x11000000 |   296: 	movl R1, 0 
 [0x00000390] = 0x12000000 |   297: 	movl R2, 0 
 [0x00000394] = 0x13000000 |   298: 	movl R3, 0 
 [0x00000398] = 0x14000000 |   299: 	movl R4, 0 
 [0x0000039C] = 0x15000000 |   300: 	movl R5, 0 
 [0x000003A0] = 0x16000000 |   301: 	movl R6, 0 
 [0x000003A4] = 0x1700005C |   302: 	movl R7, (tsk1_stack-4) 
                           |   303:  
                           |   304: TASK1_START: 
 [0x000003A8] = 0x10000002 |   305:     movl  R0, MCS2_CH1_TRIGGER_BIT 
 [0x000003AC] = 0xF0B00002 |   306:     wurm  R0, STRG, MCS2_CH1_TRIGGER_BIT 
 [0x000003B0] = 0xE8910388 |   307:     jbs STA CWT tsk1_init 
 [0x000003B4] = 0x1A000002 |   308:     movl  CTRG, MCS2_CH1_TRIGGER_BIT                  ; Clear start trigger 
                           |   309:  
                           |   310: MOS1_5: 
 [0x000003B8] = 0xB0120062 |   311:     nard R0, R1, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1 
 [0x000003BC] = 0xE8A103CC |   312:     jbs STA SAT MOS1                                  ; SAT = 1 new cyl2 request 
 [0x000003C0] = 0xB0120066 |   313:     nard R0, R1, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5 
 [0x000003C4] = 0xE8A103E4 |   314:     jbs STA SAT MOS5                                  ; SAT = 1 new cyl5 request 
 [0x000003C8] = 0xE00003B8 |   315:     jmp MOS1_5 
                           |   316:  
                           |   317: MOS1: 
 [0x000003CC] = 0x70FFFFFF |   318:    atul R0, 0xFFFFFF 
 [0x000003D0] = 0xE85203DC |   319:    jbc STA Z MOS1_WAIT_PARAM 
 [0x000003D4] = 0x71FFFFFF |   320:    atul R1, 0xFFFFFF 
 [0x000003D8] = 0xE85103B8 |   321:    jbs STA Z MOS1_5 
                           |   322: MOS1_WAIT_PARAM:  
 [0x000003DC] = 0xB3500062 |   323:    ard R3, R5, MCS2_xMOS_CYL1_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration 
 [0x000003E0] = 0xE00003F8 |   324:    jmp PMOS1_5 
                           |   325:  
                           |   326: MOS5: 
 [0x000003E4] = 0x70FFFFFF |   327:    atul R0, 0xFFFFFF 
 [0x000003E8] = 0xE85203F4 |   328:    jbc STA Z MOS5_WAIT_PARAM 
 [0x000003EC] = 0x71FFFFFF |   329:    atul R1, 0xFFFFFF 
 [0x000003F0] = 0xE85103B8 |   330:    jbs STA Z MOS1_5 
                           |   331: MOS5_WAIT_PARAM: 
 [0x000003F4] = 0xB3500066 |   332:    ard R3, R5, MCS2_xMOS_CYL5_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration 
                           |   333:  
                           |   334: PMOS1_5: 
                           |   335: ;CLOSE MOS signals 
                           |   336: ; Stop NMOS  
 [0x000003F8] = 0x19000000 |   337:     movl ACB, MOS1_5_CLOSE                            
 [0x000003FC] = 0xB0110003 |   338:     awr R0, R1, MCS2_NMOS1_5_PORT_IDX 
                           |   339: ; Stop PMOS      
                           |   340: ;    movl ACB, MOS1_5_CLOSE 
                           |   341: ;    awr R0, R1, MCS2_PMOS1_5_PORT_IDX 
                           |   342:      
 [0x00000400] = 0x18000003 |   343:     movl STA, 0x000003                                ; raise interrupt adc setup 
                           |   344:  
 [0x00000404] = 0x19000001 |   345:     movl ACB, MOS1_5_OPEN                             ; Start PMOS 
 [0x00000408] = 0xB0110002 |   346:     awr R0, R1, MCS2_PMOS1_5_PORT_IDX                 ; PORT 2 of MCS2 ATOM2_2 
                           |   347:   
 [0x0000040C] = 0xA0E00000 |   348:     mov PMOS_REG, TBU_TS2 
 [0x00000410] = 0xC0300000 |   349:     add PMOS_REG, R3 
 [0x00000414] = 0xE841041C |   350:     jbs STA CY PMOS1_5_LOOP_WA 
 [0x00000418] = 0xE0000434 |   351:     jmp PMOS1_5_LOOP 
                           |   352: PMOS1_5_LOOP_WA: 
 [0x0000041C] = 0xB2120062 |   353:     nard R2, R1, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1 
 [0x00000420] = 0xE8A10450 |   354:     jbs STA SAT PMOS1_5_STOP_TRIGGER 
 [0x00000424] = 0xB2120066 |   355:     nard R2, R1, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5 
 [0x00000428] = 0xE8A10450 |   356:     jbs STA SAT PMOS1_5_STOP_TRIGGER 
 [0x0000042C] = 0xD0E00000 |   357:     atu PMOS_REG, TBU_TS2  
 [0x00000430] = 0xE841041C |   358:     jbs STA CY PMOS1_5_LOOP_WA 
                           |   359:  PMOS1_5_LOOP: 
 [0x00000434] = 0xB2120062 |   360:     nard R2, R1, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1 
 [0x00000438] = 0xE8A10450 |   361:     jbs STA SAT PMOS1_5_STOP_TRIGGER 
 [0x0000043C] = 0xB2120066 |   362:     nard R2, R1, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5 
 [0x00000440] = 0xE8A10450 |   363:     jbs STA SAT PMOS1_5_STOP_TRIGGER 
 [0x00000444] = 0xDE000000 |   364:     atu TBU_TS2, PMOS_REG 
 [0x00000448] = 0xE842045C |   365:     jbc STA CY PMOS1_5_STOP_DELAY 
                           |   366:      
 [0x0000044C] = 0xE0000434 |   367:     jmp PMOS1_5_LOOP 
                           |   368:  
                           |   369: PMOS1_5_STOP_TRIGGER: 
 [0x00000450] = 0x19000000 |   370:     movl ACB, MOS1_5_CLOSE                            ; Stop PMOS 
 [0x00000454] = 0xB0110002 |   371:     awr R0, R1, MCS2_PMOS1_5_PORT_IDX                 ; PORT 2 of MCS2 ATOM2_2 
 [0x00000458] = 0xE00004A8 |   372:     jmp NMOS1_5_ENABLE 
                           |   373:  
                           |   374: PMOS1_5_STOP_DELAY: 
 [0x0000045C] = 0x19000000 |   375:     movl ACB, MOS1_5_CLOSE                            ; Stop PMOS 
 [0x00000460] = 0xB0110002 |   376:     awr R0, R1, MCS2_PMOS1_5_PORT_IDX                 ; PORT 2 of MCS2 ATOM2_2 
                           |   377:  
 [0x00000464] = 0xA1E00000 |   378:     mov NMOS_REG, TBU_TS2 
 [0x00000468] = 0xC1500000 |   379:     add NMOS_REG, R5                                  ;NMOS_Delay 
 [0x0000046C] = 0xE8410474 |   380:     jbs STA CY NMOS1_5_WAIT_EVENT_WA 
 [0x00000470] = 0xE000048C |   381:     jmp NMOS1_5_WAIT_EVENT 
                           |   382: NMOS1_5_WAIT_EVENT_WA: 
 [0x00000474] = 0xB0220062 |   383:     nard R0, R2, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1 
 [0x00000478] = 0xE8A104A8 |   384:     jbs STA SAT NMOS1_5_ENABLE 
 [0x0000047C] = 0xB0220066 |   385:     nard R0, R2, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5 
 [0x00000480] = 0xE8A104A8 |   386:     jbs STA SAT NMOS1_5_ENABLE 
 [0x00000484] = 0xD1E00000 |   387:     atu NMOS_REG, TBU_TS2  
 [0x00000488] = 0xE8410474 |   388:     jbs STA CY NMOS1_5_WAIT_EVENT_WA     
                           |   389: NMOS1_5_WAIT_EVENT: 
 [0x0000048C] = 0xB0220062 |   390:     nard R0, R2, MCS2_xMOS_CYL1_ADDR                  ; wait trigger from BRC_DEST1 
 [0x00000490] = 0xE8A104A8 |   391:     jbs STA SAT NMOS1_5_ENABLE 
 [0x00000494] = 0xB0220066 |   392:     nard R0, R2, MCS2_xMOS_CYL5_ADDR                  ; wait trigger from BRC_DEST5 
 [0x00000498] = 0xE8A104A8 |   393:     jbs STA SAT NMOS1_5_ENABLE 
 [0x0000049C] = 0xDE100000 |   394:     atu TBU_TS2, NMOS_REG 
 [0x000004A0] = 0xE84204A8 |   395:     jbc STA CY NMOS1_5_ENABLE 
 [0x000004A4] = 0xE000048C |   396:     jmp NMOS1_5_WAIT_EVENT 
                           |   397:      
                           |   398: NMOS1_5_ENABLE: 
 [0x000004A8] = 0x19000001 |   399:     movl ACB, MOS1_5_OPEN                             ; Start NMOS 
 [0x000004AC] = 0xB0110003 |   400:     awr R0, R1, MCS2_NMOS1_5_PORT_IDX                 ; PORT 3 of MCS2 ATOM2_3 
                           |   401:  
                           |   402:     ;movl CTRG, CPU_REOPEN_PMOS1_5_TRG_BIT              ; clean trigger bit 
                           |   403: PMOS1_5_WAIT_TRG_REOPEN: 
 [0x000004B0] = 0x9B002000 |   404:     btl STRG, CPU_REOPEN_PMOS1_5_TRG_BIT                ; (A & B) if TRUE Z=0; if FALSE Z=1  
 [0x000004B4] = 0xE85104B0 |   405:     jbs STA Z PMOS1_5_WAIT_TRG_REOPEN                   ; loop until the TRG bit is not set 
                           |   406:     ; delay before opening 
 [0x000004B8] = 0x10000032 |   407:     movl R0, PMOS_REOPEN_DELAY 
 [0x000004BC] = 0xC0E00000 |   408:     add R0, TBU_TS2 
 [0x000004C0] = 0xE84104C8 |   409:     jbs STA CY PMOS1_5_REOPEN_DELAY_WA 
 [0x000004C4] = 0xE00004D0 |   410:     jmp PMOS1_5_REOPEN_DELAY 
                           |   411:  
                           |   412: PMOS1_5_REOPEN_DELAY_WA: 
 [0x000004C8] = 0xD0E00000 |   413:     atu R0, tbu_ts2 
 [0x000004CC] = 0xE84204C8 |   414:     jbc STA CY PMOS1_5_REOPEN_DELAY_WA 
                           |   415:  
                           |   416: PMOS1_5_REOPEN_DELAY: 
 [0x000004D0] = 0xDE000000 |   417:     atu TBU_TS2, R0 
 [0x000004D4] = 0xE84204DC |   418:     jbc STA CY PMOS1_5_REOPEN 
 [0x000004D8] = 0xE00004D0 |   419:     jmp PMOS1_5_REOPEN_DELAY 
                           |   420:  
                           |   421: PMOS1_5_REOPEN: 
 [0x000004DC] = 0x1A002000 |   422:     movl CTRG, CPU_REOPEN_PMOS1_5_TRG_BIT               ; clean trigger bit 
 [0x000004E0] = 0x19000001 |   423:     movl ACB, MOS1_5_OPEN 
 [0x000004E4] = 0xB0110002 |   424:     awr R0, R1, MCS2_PMOS1_5_PORT_IDX 
                           |   425:         
 [0x000004E8] = 0xE00003B8 |   426:     jmp MOS1_5 
                           |   427:         
                           |   428: TASK1_DONE: 
                           |   429: ; Restart again 
                           |   430:     ;movl STA, 0x000003   ; raise interrupt 
 [0x000004EC] = 0xE00003A8 |   431:     jmp   TASK1_START 
                           |   432:  
                           |   433: ; Should not get here 
 [0x000004F0] = 0x480FFFFE |   434:     andl STA MCS_CHx_DISABLE_MASK                     ; Disable MCS Channel 
                           |   435:      
                           |   436: ;;;;;;;;;;;;;;; 
                           |   437: ; xMOS2-6 
                           |   438: ;;;;;;;;;;;;;;; 
                           |   439: tsk2_init: 
 [0x000004F4] = 0x10000000 |   440:     movl R0, 0 
 [0x000004F8] = 0x11000000 |   441: 	movl R1, 0 
 [0x000004FC] = 0x12000000 |   442: 	movl R2, 0 
 [0x00000500] = 0x13000000 |   443: 	movl R3, 0 
 [0x00000504] = 0x14000000 |   444: 	movl R4, 0 
 [0x00000508] = 0x15000000 |   445: 	movl R5, 0 
 [0x0000050C] = 0x16000000 |   446: 	movl R6, 0 
 [0x00000510] = 0x1700009C |   447: 	movl R7, (tsk2_stack-4) 
                           |   448:  
                           |   449: TASK2_START: 
 [0x00000514] = 0x10000004 |   450:     movl  R0, MCS2_CH2_TRIGGER_BIT 
 [0x00000518] = 0xF0B00004 |   451:     wurm  R0, STRG, MCS2_CH2_TRIGGER_BIT 
 [0x0000051C] = 0xE89104F4 |   452:     jbs STA CWT tsk2_init 
 [0x00000520] = 0x1A000004 |   453:     movl  CTRG, MCS2_CH2_TRIGGER_BIT                 ; Clear start trigger 
                           |   454:  
                           |   455: MOS2_6: 
 [0x00000524] = 0xB0120063 |   456:     nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2 
 [0x00000528] = 0xE8A10538 |   457:     jbs STA SAT MOS2                                  ; SAT = 1 new cyl1 request 
 [0x0000052C] = 0xB0120067 |   458:     nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST4 
 [0x00000530] = 0xE8A10550 |   459:     jbs STA SAT MOS6                                  ; SAT = 1 new cyl4 request 
 [0x00000534] = 0xE0000524 |   460:     jmp MOS2_6 
                           |   461:  
                           |   462: MOS2: 
 [0x00000538] = 0x70FFFFFF |   463:    atul R0, 0xFFFFFF 
 [0x0000053C] = 0xE8520548 |   464:    jbc STA Z MOS2_WAIT_PARAM 
 [0x00000540] = 0x71FFFFFF |   465:    atul R1, 0xFFFFFF 
 [0x00000544] = 0xE8510524 |   466:    jbs STA Z MOS2_6 
                           |   467: MOS2_WAIT_PARAM: 
 [0x00000548] = 0xB3500063 |   468:    ard R3, R5, MCS2_xMOS_CYL2_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration 
 [0x0000054C] = 0xE0000564 |   469:    jmp PMOS2_6 
                           |   470:  
                           |   471: MOS6: 
 [0x00000550] = 0x70FFFFFF |   472:    atul R0, 0xFFFFFF 
 [0x00000554] = 0xE8520560 |   473:    jbc STA Z MOS6_WAIT_PARAM 
 [0x00000558] = 0x71FFFFFF |   474:    atul R1, 0xFFFFFF 
 [0x0000055C] = 0xE8510524 |   475:    jbs STA Z MOS2_6 
                           |   476: MOS6_WAIT_PARAM: 
 [0x00000560] = 0xB3500067 |   477:    ard R3, R5, MCS2_xMOS_CYL6_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration 
                           |   478:  
                           |   479: PMOS2_6: 
                           |   480: ;CLOSE MOS signals 
                           |   481: ; Stop NMOS  
 [0x00000564] = 0x19000000 |   482:     movl ACB, MOS2_6_CLOSE                            
 [0x00000568] = 0xB0110005 |   483:     awr R0, R1, MCS2_NMOS2_6_PORT_IDX 
                           |   484: ; Stop PMOS      
                           |   485: ;    movl ACB, MOS2_6_CLOSE 
                           |   486: ;    awr R0, R1, MCS2_PMOS2_6_PORT_IDX 
                           |   487:      
 [0x0000056C] = 0x18000003 |   488:     movl STA, 0x000003                                ; raise interrupt - ADC ION setup 
                           |   489:  
                           |   490:  
 [0x00000570] = 0x19000001 |   491:     movl ACB, MOS2_6_OPEN                             ; Start PMOS 
 [0x00000574] = 0xB0110004 |   492:     awr R0, R1, MCS2_PMOS2_6_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4 
 [0x00000578] = 0xA0E00000 |   493:     mov PMOS_REG, TBU_TS2 
 [0x0000057C] = 0xC0300000 |   494:     add PMOS_REG, R3 
 [0x00000580] = 0xE8410588 |   495:     jbs STA CY PMOS2_6_LOOP_WA 
 [0x00000584] = 0xE00005A0 |   496:     jmp PMOS2_6_LOOP 
                           |   497:  
                           |   498: PMOS2_6_LOOP_WA:     
 [0x00000588] = 0xB0120063 |   499:     nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2 
 [0x0000058C] = 0xE8A105BC |   500:     jbs STA SAT PMOS2_6_STOP_TRIGGER 
 [0x00000590] = 0xB0120067 |   501:     nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST6 
 [0x00000594] = 0xE8A105BC |   502:     jbs STA SAT PMOS2_6_STOP_TRIGGER 
 [0x00000598] = 0xD0E00000 |   503:     ATU PMOS_REG, TBU_TS2 
 [0x0000059C] = 0xE8410588 |   504:     jbs STA CY PMOS2_6_LOOP_WA                     ; loop until (current) TBU_TS2 < R1 
                           |   505: PMOS2_6_LOOP: 
 [0x000005A0] = 0xB0120063 |   506:     nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2 
 [0x000005A4] = 0xE8A105BC |   507:     jbs STA SAT PMOS2_6_STOP_TRIGGER 
 [0x000005A8] = 0xB0120067 |   508:     nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST6 
 [0x000005AC] = 0xE8A105BC |   509:     jbs STA SAT PMOS2_6_STOP_TRIGGER 
 [0x000005B0] = 0xDE000000 |   510:     ATU TBU_TS2, PMOS_REG 
 [0x000005B4] = 0xE84205C8 |   511:     jbc STA CY PMOS2_6_STOP_DELAY                     ; loop until (current) TBU_TS2 < R1 
 [0x000005B8] = 0xE00005A0 |   512:     jmp PMOS2_6_LOOP 
                           |   513:  
                           |   514: PMOS2_6_STOP_TRIGGER:     
 [0x000005BC] = 0x19000000 |   515:     movl ACB, MOS2_6_CLOSE                            ; Stop PMOS 
 [0x000005C0] = 0xB0110004 |   516:     awr R0, R1, MCS2_PMOS2_6_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4 
 [0x000005C4] = 0xE0000614 |   517:     jmp NMOS2_6_ENABLE 
                           |   518:      
                           |   519: PMOS2_6_STOP_DELAY: 
 [0x000005C8] = 0x19000000 |   520:     movl ACB, MOS2_6_CLOSE                            ; Stop PMOS 
 [0x000005CC] = 0xB0110004 |   521:     awr R0, R1, MCS2_PMOS2_6_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4     
                           |   522:  
 [0x000005D0] = 0xA1E00000 |   523:     mov NMOS_REG, TBU_TS2 
 [0x000005D4] = 0xC1500000 |   524:     add NMOS_REG, R5                                  ;NMOS_Delay 
 [0x000005D8] = 0xE84105E0 |   525:     jbs STA CY NMOS2_6_WAIT_EVENT_WA 
 [0x000005DC] = 0xE00005F8 |   526:     jmp NMOS2_6_WAIT_EVENT 
                           |   527: NMOS2_6_WAIT_EVENT_WA: 
 [0x000005E0] = 0xB0120063 |   528:     nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2 
 [0x000005E4] = 0xE8A10614 |   529:     jbs STA SAT NMOS2_6_ENABLE 
 [0x000005E8] = 0xB0120067 |   530:     nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST6 
 [0x000005EC] = 0xE8A10614 |   531:     jbs STA SAT NMOS2_6_ENABLE 
 [0x000005F0] = 0xD1E00000 |   532:     atu NMOS_REG, TBU_TS2  
 [0x000005F4] = 0xE84105E0 |   533:     jbs STA CY NMOS2_6_WAIT_EVENT_WA 
                           |   534: NMOS2_6_WAIT_EVENT: 
 [0x000005F8] = 0xB0120063 |   535:     nard R0, R1, MCS2_xMOS_CYL2_ADDR                  ; wait trigger from BRC_DEST2 
 [0x000005FC] = 0xE8A10614 |   536:     jbs STA SAT NMOS2_6_ENABLE 
 [0x00000600] = 0xB0120067 |   537:     nard R0, R1, MCS2_xMOS_CYL6_ADDR                  ; wait trigger from BRC_DEST6 
 [0x00000604] = 0xE8A10614 |   538:     jbs STA SAT NMOS2_6_ENABLE 
 [0x00000608] = 0xDE100000 |   539:     atu TBU_TS2, NMOS_REG 
 [0x0000060C] = 0xE8420614 |   540:     jbc STA CY NMOS2_6_ENABLE 
 [0x00000610] = 0xE00005F8 |   541:     jmp NMOS2_6_WAIT_EVENT     
                           |   542:  
                           |   543: NMOS2_6_ENABLE: 
 [0x00000614] = 0x19000001 |   544:     movl ACB, MOS2_6_OPEN                             ; Start NMOS 
 [0x00000618] = 0xB0110005 |   545:     awr R0, R1, MCS2_NMOS2_6_PORT_IDX                 ; PORT 5 of MCS2 ATOM1_4 
                           |   546:  
                           |   547: PMOS2_6_WAIT_TRG_REOPEN: 
 [0x0000061C] = 0x9B004000 |   548:     btl STRG, CPU_REOPEN_PMOS2_6_TRG_BIT                ; (A & B) if TRUE Z=0; if FALSE Z=1  
 [0x00000620] = 0xE851061C |   549:     jbs STA Z PMOS2_6_WAIT_TRG_REOPEN                   ; loop until the TRG bit is not set 
                           |   550:     ; delay before opening 
 [0x00000624] = 0x10000032 |   551:     movl R0, PMOS_REOPEN_DELAY 
 [0x00000628] = 0xC0E00000 |   552:     add R0, TBU_TS2 
 [0x0000062C] = 0xE8410634 |   553:     jbs STA CY PMOS2_6_REOPEN_DELAY_WA 
 [0x00000630] = 0xE000063C |   554:     jmp PMOS2_6_REOPEN_DELAY 
                           |   555:  
                           |   556: PMOS2_6_REOPEN_DELAY_WA: 
 [0x00000634] = 0xD0E00000 |   557:     atu R0, tbu_ts2 
 [0x00000638] = 0xE8420634 |   558:     jbc STA CY PMOS2_6_REOPEN_DELAY_WA 
                           |   559:  
                           |   560: PMOS2_6_REOPEN_DELAY: 
 [0x0000063C] = 0xDE000000 |   561:     atu TBU_TS2, R0 
 [0x00000640] = 0xE8420648 |   562:     jbc STA CY PMOS2_6_REOPEN 
 [0x00000644] = 0xE000063C |   563:     jmp PMOS2_6_REOPEN_DELAY 
                           |   564:  
                           |   565: PMOS2_6_REOPEN: 
 [0x00000648] = 0x1A004000 |   566:     movl CTRG, CPU_REOPEN_PMOS2_6_TRG_BIT                ; clean trigger bit 
 [0x0000064C] = 0x19000001 |   567:     movl ACB, MOS2_6_OPEN 
 [0x00000650] = 0xB0110004 |   568:     awr R0, R1, MCS2_PMOS2_6_PORT_IDX 
                           |   569:          
 [0x00000654] = 0xE0000524 |   570:     jmp MOS2_6 
                           |   571:         
                           |   572: TASK2_DONE: 
                           |   573: ; Restart again 
                           |   574:     ;movl STA, 0x000003   ; raise interrupt 
 [0x00000658] = 0xE0000514 |   575:     jmp   TASK2_START 
                           |   576:  
                           |   577: ; Should not get here 
 [0x0000065C] = 0x480FFFFE |   578:     andl STA MCS_CHx_DISABLE_MASK ; disable MCS 
                           |   579:  
                           |   580: ;;;;;;;;;;;;;;; 
                           |   581: ; xMOS3-7 
                           |   582: ;;;;;;;;;;;;;;; 
                           |   583: tsk3_init: 
 [0x00000660] = 0x10000000 |   584:     movl R0, 0 
 [0x00000664] = 0x11000000 |   585: 	movl R1, 0 
 [0x00000668] = 0x12000000 |   586: 	movl R2, 0 
 [0x0000066C] = 0x13000000 |   587: 	movl R3, 0 
 [0x00000670] = 0x14000000 |   588: 	movl R4, 0 
 [0x00000674] = 0x15000000 |   589: 	movl R5, 0 
 [0x00000678] = 0x16000000 |   590: 	movl R6, 0 
 [0x0000067C] = 0x170000DC |   591: 	movl R7, (tsk3_stack-4) 
                           |   592:  
                           |   593: TASK3_START: 
 [0x00000680] = 0x10000008 |   594:     movl  R0, MCS2_CH3_TRIGGER_BIT 
 [0x00000684] = 0xF0B00008 |   595:     wurm  R0, STRG, MCS2_CH3_TRIGGER_BIT 
 [0x00000688] = 0xE8910660 |   596:     jbs STA CWT tsk3_init 
 [0x0000068C] = 0x1A000008 |   597:     movl  CTRG, MCS2_CH3_TRIGGER_BIT                 ; Clear start trigger 
                           |   598:  
                           |   599: MOS3_7: 
 [0x00000690] = 0xB0120064 |   600:     nard R0, R1, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST3 
 [0x00000694] = 0xE8A106A4 |   601:     jbs STA SAT MOS3                                  ; SAT = 1 new cyl3 request 
 [0x00000698] = 0xB0120068 |   602:     nard R0, R1, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST7 
 [0x0000069C] = 0xE8A106BC |   603:     jbs STA SAT MOS7                                  ; SAT = 1 new cyl7 request 
 [0x000006A0] = 0xE0000690 |   604:     jmp MOS3_7 
                           |   605:  
                           |   606: MOS3: 
 [0x000006A4] = 0x70FFFFFF |   607:    atul R0, 0xFFFFFF 
 [0x000006A8] = 0xE85206B4 |   608:    jbc STA Z MOS3_WAIT_PARAM 
 [0x000006AC] = 0x71FFFFFF |   609:    atul R1, 0xFFFFFF 
 [0x000006B0] = 0xE8510690 |   610:    jbs STA Z MOS3_7 
                           |   611: MOS3_WAIT_PARAM: 
 [0x000006B4] = 0xB3500064 |   612:    ard R3, R5, MCS2_xMOS_CYL3_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration 
 [0x000006B8] = 0xE00006D0 |   613:    jmp PMOS3_7 
                           |   614:  
                           |   615: MOS7: 
 [0x000006BC] = 0x70FFFFFF |   616:    atul R0, 0xFFFFFF 
 [0x000006C0] = 0xE85206CC |   617:    jbc STA Z MOS7_WAIT_PARAM 
 [0x000006C4] = 0x71FFFFFF |   618:    atul R1, 0xFFFFFF 
 [0x000006C8] = 0xE8510690 |   619:    jbs STA Z MOS3_7 
                           |   620: MOS7_WAIT_PARAM: 
 [0x000006CC] = 0xB3500068 |   621:    ard R3, R5, MCS2_xMOS_CYL7_ADDR                    ; R3 = pmos_duration, R5 = nmos_duration 
                           |   622:  
                           |   623: PMOS3_7: 
                           |   624: ;CLOSE MOS signals 
                           |   625: ; Stop NMOS  
 [0x000006D0] = 0x19000000 |   626:     movl ACB, MOS3_7_CLOSE                            
 [0x000006D4] = 0xB0110007 |   627:     awr R0, R1, MCS2_NMOS3_7_PORT_IDX 
                           |   628: ; Stop PMOS      
                           |   629: ;    movl ACB, MOS3_7_CLOSE 
                           |   630: ;    awr R0, R1, MCS2_PMOS3_7_PORT_IDX 
                           |   631:      
 [0x000006D8] = 0x18000003 |   632:     movl STA, 0x000003                                ; raise interrupt - ADC ION setup 
                           |   633:  
 [0x000006DC] = 0x19000001 |   634:     movl ACB, MOS3_7_OPEN                             ; Start PMOS 
 [0x000006E0] = 0xB0110006 |   635:     awr R0, R1, MCS2_PMOS3_7_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4 
 [0x000006E4] = 0xA0E00000 |   636:     mov PMOS_REG, TBU_TS2 
 [0x000006E8] = 0xC0300000 |   637:     add PMOS_REG, R3 
 [0x000006EC] = 0xE84106F4 |   638:     jbs STA CY PMOS3_7_LOOP_WA 
 [0x000006F0] = 0xE000070C |   639:     jmp PMOS3_7_LOOP 
                           |   640: PMOS3_7_LOOP_WA: 
 [0x000006F4] = 0xB2120064 |   641:     nard R2, R1, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST2 
 [0x000006F8] = 0xE8A10728 |   642:     jbs STA SAT PMOS3_7_STOP_TRIGGER 
 [0x000006FC] = 0xB2120068 |   643:     nard R2, R1, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST6 
 [0x00000700] = 0xE8A10728 |   644:     jbs STA SAT PMOS3_7_STOP_TRIGGER 
 [0x00000704] = 0xD0E00000 |   645:     ATU PMOS_REG, TBU_TS2  
 [0x00000708] = 0xE84106F4 |   646:     jbs STA CY PMOS3_7_LOOP_WA                        ; loop until (current) TBU_TS2 < R1 
                           |   647: PMOS3_7_LOOP: 
 [0x0000070C] = 0xB2120064 |   648:     nard R2, R1, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST2 
 [0x00000710] = 0xE8A10728 |   649:     jbs STA SAT PMOS3_7_STOP_TRIGGER 
 [0x00000714] = 0xB2120068 |   650:     nard R2, R1, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST6 
 [0x00000718] = 0xE8A10728 |   651:     jbs STA SAT PMOS3_7_STOP_TRIGGER 
 [0x0000071C] = 0xDE000000 |   652:     ATU TBU_TS2, PMOS_REG 
 [0x00000720] = 0xE8420734 |   653:     jbc STA CY PMOS3_7_STOP_DELAY                     ; loop until (current) TBU_TS2 < R1 
                           |   654:      
 [0x00000724] = 0xE000070C |   655:     jmp PMOS3_7_LOOP 
                           |   656:  
                           |   657: PMOS3_7_STOP_TRIGGER:     
 [0x00000728] = 0x19000000 |   658:     movl ACB, MOS3_7_CLOSE                            ; Stop PMOS 
 [0x0000072C] = 0xB0110006 |   659:     awr R0, R1, MCS2_PMOS3_7_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4 
 [0x00000730] = 0xE0000780 |   660:     jmp NMOS3_7_ENABLE 
                           |   661:      
                           |   662: PMOS3_7_STOP_DELAY: 
 [0x00000734] = 0x19000000 |   663:     movl ACB, MOS3_7_CLOSE                            ; Stop PMOS 
 [0x00000738] = 0xB0110006 |   664:     awr R0, R1, MCS2_PMOS3_7_PORT_IDX                 ; PORT 4 of MCS2 ATOM2_4 
                           |   665:      
 [0x0000073C] = 0xA1E00000 |   666:     mov NMOS_REG, TBU_TS2 
 [0x00000740] = 0xC1500000 |   667:     add NMOS_REG, R5                                  ;NMOS_Delay 
 [0x00000744] = 0xE841074C |   668:     jbs STA CY NMOS3_7_WAIT_EVENT_WA 
 [0x00000748] = 0xE0000764 |   669:     jmp NMOS3_7_WAIT_EVENT 
                           |   670: NMOS3_7_WAIT_EVENT_WA: 
 [0x0000074C] = 0xB0220064 |   671:     nard R0, R2, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST2 
 [0x00000750] = 0xE8A10780 |   672:     jbs STA SAT NMOS3_7_ENABLE 
 [0x00000754] = 0xB0220068 |   673:     nard R0, R2, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST6 
 [0x00000758] = 0xE8A10780 |   674:     jbs STA SAT NMOS3_7_ENABLE 
 [0x0000075C] = 0xD1E00000 |   675:     atu NMOS_REG, TBU_TS2  
 [0x00000760] = 0xE841074C |   676:     jbs STA CY NMOS3_7_WAIT_EVENT_WA 
                           |   677: NMOS3_7_WAIT_EVENT: 
 [0x00000764] = 0xB0220064 |   678:     nard R0, R2, MCS2_xMOS_CYL3_ADDR                  ; wait trigger from BRC_DEST2 
 [0x00000768] = 0xE8A10780 |   679:     jbs STA SAT NMOS3_7_ENABLE 
 [0x0000076C] = 0xB0220068 |   680:     nard R0, R2, MCS2_xMOS_CYL7_ADDR                  ; wait trigger from BRC_DEST6 
 [0x00000770] = 0xE8A10780 |   681:     jbs STA SAT NMOS3_7_ENABLE 
 [0x00000774] = 0xDE100000 |   682:     atu TBU_TS2, NMOS_REG 
 [0x00000778] = 0xE8420780 |   683:     jbc STA CY NMOS3_7_ENABLE 
                           |   684:      
 [0x0000077C] = 0xE0000764 |   685:     jmp NMOS3_7_WAIT_EVENT     
                           |   686:  
                           |   687: NMOS3_7_ENABLE: 
 [0x00000780] = 0x19000001 |   688:     movl ACB, MOS3_7_OPEN                             ; Start NMOS 
 [0x00000784] = 0xB0110007 |   689:     awr R0, R1, MCS2_NMOS3_7_PORT_IDX                 ; PORT 5 of MCS2 ATOM1_4 
                           |   690:  
                           |   691:    ;Update from UseCase #33, TRG.13 sent by CPU (BUCK_EN close ISR) to Re-open the PMOS, closure triggered by CPU at the end of ION sampling  
                           |   692:    ;movl CTRG, CPU_REOPEN_PMOS3_7_TRG_BIT                ; clean trigger bit 
                           |   693:     
                           |   694: PMOS3_7_WAIT_TRG_REOPEN: 
 [0x00000788] = 0x9B008000 |   695:     btl STRG, CPU_REOPEN_PMOS3_7_TRG_BIT                ; (A & B) if TRUE Z=0; if FALSE Z=1  
 [0x0000078C] = 0xE8510788 |   696:     jbs STA Z PMOS3_7_WAIT_TRG_REOPEN                   ; loop until the TRG bit is not set 
                           |   697:     ; delay before opening 
 [0x00000790] = 0x10000032 |   698:     movl R0, PMOS_REOPEN_DELAY 
 [0x00000794] = 0xC0E00000 |   699:     add R0, TBU_TS2 
 [0x00000798] = 0xE84107A0 |   700:     jbs STA CY PMOS3_7_REOPEN_DELAY_WA 
 [0x0000079C] = 0xE00007A8 |   701:     jmp PMOS3_7_REOPEN_DELAY 
                           |   702:  
                           |   703: PMOS3_7_REOPEN_DELAY_WA: 
 [0x000007A0] = 0xD0E00000 |   704:     atu R0, tbu_ts2 
 [0x000007A4] = 0xE84207A0 |   705:     jbc STA CY PMOS3_7_REOPEN_DELAY_WA 
                           |   706:  
                           |   707: PMOS3_7_REOPEN_DELAY: 
 [0x000007A8] = 0xDE000000 |   708:     atu TBU_TS2, R0 
 [0x000007AC] = 0xE84207B4 |   709:     jbc STA CY PMOS3_7_REOPEN 
 [0x000007B0] = 0xE00007A8 |   710:     jmp PMOS3_7_REOPEN_DELAY 
                           |   711:  
                           |   712: PMOS3_7_REOPEN: 
 [0x000007B4] = 0x1A008000 |   713:     movl CTRG, CPU_REOPEN_PMOS3_7_TRG_BIT                ; clean trigger bit 
 [0x000007B8] = 0x19000001 |   714:     movl ACB, MOS3_7_OPEN 
 [0x000007BC] = 0xB0110006 |   715:     awr R0, R1, MCS2_PMOS3_7_PORT_IDX 
                           |   716:          
 [0x000007C0] = 0xE0000690 |   717:     jmp MOS3_7 
                           |   718:         
                           |   719: TASK3_DONE: 
                           |   720: ; Restart again 
                           |   721:     ;movl STA, 0x000003   ; raise interrupt 
 [0x000007C4] = 0xE0000680 |   722:     jmp   TASK3_START 
                           |   723:  
                           |   724: ; Should not get here 
 [0x000007C8] = 0x480FFFFE |   725:     andl STA MCS_CHx_DISABLE_MASK ; disable MCS 
                           |   726:  
                           |   727: ;======================================================================================== 
                           |   728: ;--------------------------- PRIMARY - SECONDARY CURRENT BANK0 -------------------------- 
                           |   729: ;======================================================================================== 
                           |   730: tsk4_init: 
 [0x000007CC] = 0x10000000 |   731:     movl R0, 0 
 [0x000007D0] = 0x11000000 |   732: 	movl R1, 0 
 [0x000007D4] = 0x12000000 |   733: 	movl R2, 0 
 [0x000007D8] = 0x13000000 |   734: 	movl R3, 0 
 [0x000007DC] = 0x14000000 |   735: 	movl R4, 0 
 [0x000007E0] = 0x15000000 |   736: 	movl R5, 0 
 [0x000007E4] = 0x16000000 |   737: 	movl R6, 0 
 [0x000007E8] = 0x1700011C |   738: 	movl R7, (tsk4_stack-4) 
                           |   739:  
                           |   740: TASK4_START: 
 [0x000007EC] = 0x10000010 |   741:     movl  R0, MCS2_CH4_TRIGGER_BIT 
 [0x000007F0] = 0xF0B00010 |   742:     wurm  R0, STRG, MCS2_CH4_TRIGGER_BIT 
 [0x000007F4] = 0xE89107CC |   743:     jbs STA CWT tsk4_init 
 [0x000007F8] = 0x1A000010 |   744:     movl  CTRG, MCS2_CH4_TRIGGER_BIT      ; Clear start trigger 
                           |   745:  
                           |   746: IPRI0_WAIT_EVENT: 
 [0x000007FC] = 0xB0120069 |   747:     nard R0, R1, MCS2_IPRI0_ADDR 
 [0x00000800] = 0xE8A10808 |   748:     jbs STA SAT IPRI0_START 
 [0x00000804] = 0xE00007FC |   749:     jmp IPRI0_WAIT_EVENT 
                           |   750:  
                           |   751: IPRI0_START: 
 [0x00000808] = 0x1B000100 |   752:     movl STRG, IPRI0_ADC_RECON_REQ        ; set request (TRG.8) bit to IPRI ADC reconfiguration 
 [0x0000080C] = 0x68000002 |   753:     xorl STA 2                            ; raise interrupt 
                           |   754:  
                           |   755: IPRI0_ADC_RECON_WAIT: 
 [0x00000810] = 0x9B000100 |   756:     btl STRG, IPRI0_ADC_RECON_REQ         ; wait TRG.8 clean from CPU (ADC ready) 
 [0x00000814] = 0xE8520810 |   757:     jbc STA Z IPRI0_ADC_RECON_WAIT 
                           |   758:      
                           |   759:     ;start atom for adc trigger 
 [0x00000818] = 0x19000014 |   760:     movl ACB 0x14 
 [0x0000081C] = 0x12000001 |   761:     movl R2, 1 
 [0x00000820] = 0x13000001 |   762:     movl R3, 1 
 [0x00000824] = 0xB2310008 |   763:     awr R2, R3, MCS2_IPRI0_PORT_IDX       ; Fake Start ATOM0_4 ADC Trigger 
                           |   764:      
 [0x00000828] = 0x19000014 |   765:     movl ACB 0x14 
 [0x0000082C] = 0x1200000A |   766:     movl R2, IPRI0_ADC_PERIOD             ; R2 = PWM-ADC period 
 [0x00000830] = 0xA3200000 |   767:     mov R3, R2 
 [0x00000834] = 0xC3060001 |   768:     shr R3, 1                             ; R3 = PWM-ADC duty  
 [0x00000838] = 0xB2310008 |   769:     awr R2, R3, MCS2_IPRI0_PORT_IDX       ; Start ATOM0_4 ADC Trigger 
                           |   770:      
                           |   771: IPRI0_WAIT_STOP: 
 [0x0000083C] = 0xB0120069 |   772:    nard R0, R1, MCS2_IPRI0_ADDR 
 [0x00000840] = 0xE8A10848 |   773:    jbs STA SAT IPRI0_STOP 
 [0x00000844] = 0xE000083C |   774:    jmp IPRI0_WAIT_STOP 
                           |   775:  
                           |   776: IPRI0_STOP: 
 [0x00000848] = 0x19000014 |   777:     movl ACB 0x14 
 [0x0000084C] = 0x13000000 |   778:     movl R3, 0 
 [0x00000850] = 0xB3310008 |   779:     awr R3, R3, MCS2_IPRI0_PORT_IDX      ; Disable ATOM0_4 ADC Trigger 
                           |   780:      
 [0x00000854] = 0x70999999 |   781:     atul R0, IPRI_FAULT  ; Arriving a FAULT from Cylinder 
 [0x00000858] = 0xE85108B0 |   782:     jbs STA Z IPRI0_LOOP 
                           |   783:  
                           |   784: ISEC0_START: 
 [0x0000085C] = 0x1B000200 |   785:     movl STRG, ISEC0_ADC_RECON_REQ         ; set request (TRG.9) bit to ISEC ADC reconfiguration 
 [0x00000860] = 0x68000002 |   786:     xorl STA 2                            ; raise interrupt for SARADC re-configuration Channel 
                           |   787:  
                           |   788: ISEC0_ADC_RECON_WAIT: 
 [0x00000864] = 0x9B000200 |   789:     btl STRG, ISEC0_ADC_RECON_REQ          ; wait TRG.9 clean from CPU (ADC ready) 
 [0x00000868] = 0xE8520864 |   790:     jbc STA Z ISEC0_ADC_RECON_WAIT 
                           |   791:      
 [0x0000086C] = 0x19000014 |   792:     movl ACB 0x14 
 [0x00000870] = 0x1200000A |   793:     movl R2, ISEC0_ADC_PERIOD              ; R2 = PWM-ADC period 
 [0x00000874] = 0xA3200000 |   794:     mov R3, R2 
 [0x00000878] = 0xC3060001 |   795:     shr R3, 1                              ; R3 = PWM-ADC duty  
 [0x0000087C] = 0xB2310008 |   796:     awr R2, R3, MCS2_ISEC0_PORT_IDX        ; Start ATOM0_4 ADC Trigger 
                           |   797:  
 [0x00000880] = 0xA4E00000 |   798:     mov R4, TBU_TS2 
 [0x00000884] = 0x240005DC |   799:     addl R4, ISEC_ADC_DURATION 
 [0x00000888] = 0xE8410890 |   800:     jbs STA CY  SPARK_B0_LOOP_WA 
 [0x0000088C] = 0xE0000898 |   801:     jmp SPARK_B0_LOOP 
                           |   802: SPARK_B0_LOOP_WA:     
 [0x00000890] = 0xD4E00000 |   803:     atu R4, TBU_TS2 
 [0x00000894] = 0xE8410890 |   804:     jbs STA CY SPARK_B0_LOOP_WA 
                           |   805: SPARK_B0_LOOP: 
 [0x00000898] = 0xDE400000 |   806:     atu TBU_TS2, R4 
 [0x0000089C] = 0xE84208A4 |   807:     jbc STA CY ISEC_DISABLE     ; TBU_TS2 >= R4 
 [0x000008A0] = 0xE0000898 |   808:     jmp SPARK_B0_LOOP 
                           |   809:  ISEC_DISABLE:    
 [0x000008A4] = 0x19000014 |   810:     movl ACB 0x14 
 [0x000008A8] = 0x13000000 |   811:     movl R3, 0 
 [0x000008AC] = 0xB3310008 |   812:     awr R3, R3, MCS2_ISEC0_PORT_IDX     ; Disable ATOM0_4 ADC Trigger 
                           |   813: IPRI0_LOOP: 
 [0x000008B0] = 0xE00007FC |   814:    jmp IPRI0_WAIT_EVENT 
                           |   815:  
                           |   816: TASK4_DONE: 
                           |   817: ; Restart again 
                           |   818:     ;movl STA, 0x000003   ; raise interrupt 
 [0x000008B4] = 0xE00007EC |   819:     jmp   TASK4_START 
                           |   820:  
                           |   821: ; Should not get here 
 [0x000008B8] = 0x480FFFFE |   822:     andl STA MCS_CHx_DISABLE_MASK ; disable MCS 
                           |   823:  
                           |   824: ;======================================================================================== 
                           |   825: ;--------------------------- PRIMARY - SECONDARY CURRENT BANK1 -------------------------- 
                           |   826: ;======================================================================================== 
                           |   827: tsk5_init: 
 [0x000008BC] = 0x10000000 |   828:     movl R0, 0 
 [0x000008C0] = 0x11000000 |   829: 	movl R1, 0 
 [0x000008C4] = 0x12000000 |   830: 	movl R2, 0 
 [0x000008C8] = 0x13000000 |   831: 	movl R3, 0 
 [0x000008CC] = 0x14000000 |   832: 	movl R4, 0 
 [0x000008D0] = 0x15000000 |   833: 	movl R5, 0 
 [0x000008D4] = 0x16000000 |   834: 	movl R6, 0 
 [0x000008D8] = 0x1700015C |   835: 	movl R7, (tsk5_stack-4) 
                           |   836:  
                           |   837: TASK5_START: 
 [0x000008DC] = 0x10000020 |   838:     movl  R0, MCS2_CH5_TRIGGER_BIT 
 [0x000008E0] = 0xF0B00020 |   839:     wurm  R0, STRG, MCS2_CH5_TRIGGER_BIT 
 [0x000008E4] = 0xE89108BC |   840:     jbs STA CWT tsk5_init 
 [0x000008E8] = 0x1A000020 |   841:     movl  CTRG, MCS2_CH5_TRIGGER_BIT                 ; Clear start trigger 
                           |   842:  
                           |   843: IPRI1_WAIT_EVENT: 
 [0x000008EC] = 0xB012006A |   844:     nard R0, R1, MCS2_IPRI1_ADDR 
 [0x000008F0] = 0xE8A108F8 |   845:     jbs STA SAT IPRI1_START 
 [0x000008F4] = 0xE00008EC |   846:     jmp IPRI1_WAIT_EVENT 
                           |   847:  
                           |   848: IPRI1_START: 
 [0x000008F8] = 0x1B000400 |   849:     movl STRG, IPRI1_ADC_RECON_REQ        ; set request (TRG.10) bit to IPRI ADC reconfiguration 
 [0x000008FC] = 0x68000002 |   850:     xorl STA 2                            ; raise interrupt 
                           |   851:  
                           |   852: IPRI1_ADC_RECON_WAIT: 
 [0x00000900] = 0x9B000400 |   853:     btl STRG, IPRI1_ADC_RECON_REQ         ; wait TRG.10 clean from CPU (ADC ready) 
 [0x00000904] = 0xE8520900 |   854:     jbc STA Z IPRI1_ADC_RECON_WAIT 
                           |   855:      
                           |   856:      
                           |   857:     ;start atom for adc trigger 
 [0x00000908] = 0x19000014 |   858:     movl ACB 0x14 
 [0x0000090C] = 0x12000001 |   859:     movl R2, 1 
 [0x00000910] = 0x13000001 |   860:     movl R3, 1 
 [0x00000914] = 0xB2310009 |   861:     awr R2, R3, MCS2_IPRI1_PORT_IDX     ; Fake Start ATOM0_6 ADC Trigger 
                           |   862:      
 [0x00000918] = 0x19000014 |   863:     movl ACB 0x14 
 [0x0000091C] = 0x1200000A |   864:     movl R2, IPRI1_ADC_PERIOD           ; R2 = PWM-ADC period 
 [0x00000920] = 0xA3200000 |   865:     mov R3, R2 
 [0x00000924] = 0xC3060001 |   866:     shr R3, 1                             ; R3 = PWM-ADC duty  
 [0x00000928] = 0xB2310009 |   867:     awr R2, R3, MCS2_IPRI1_PORT_IDX     ; Start ATOM0_6 ADC Trigger 
                           |   868:      
                           |   869: IPRI1_WAIT_STOP: 
 [0x0000092C] = 0xB012006A |   870:     nard R0, R1, MCS2_IPRI1_ADDR 
 [0x00000930] = 0xE8A10938 |   871:     jbs STA SAT IPRI1_STOP 
 [0x00000934] = 0xE000092C |   872:     jmp IPRI1_WAIT_STOP 
                           |   873:   
                           |   874:  IPRI1_STOP: 
 [0x00000938] = 0x700000FF |   875:     atul R0, 0xFF 
 [0x0000093C] = 0xE85209AC |   876:     jbc STA Z IPRI1_ERROR 
 [0x00000940] = 0x710000FF |   877:     atul R1, 0xFF 
 [0x00000944] = 0xE85209AC |   878:     jbc STA Z IPRI1_ERROR 
 [0x00000948] = 0x19000014 |   879:     movl ACB 0x14 
 [0x0000094C] = 0x13000000 |   880:     movl R3, 0 
 [0x00000950] = 0xB3310009 |   881:     awr R3, R3, MCS2_IPRI1_PORT_IDX     ; Disable ATOM0_6 ADC Trigger 
                           |   882:  
                           |   883: ISEC1_START: 
 [0x00000954] = 0x1B000800 |   884:     movl STRG, ISEC1_ADC_RECON_REQ      ; set request (TRG.11) bit to IPRI ADC reconfiguration 
 [0x00000958] = 0x68000002 |   885:     xorl STA 2                          ; raise interrupt for SARADC re-configuration Channel 
                           |   886:  
                           |   887: ISEC1_ADC_RECON_WAIT: 
 [0x0000095C] = 0x9B000800 |   888:     btl STRG, ISEC1_ADC_RECON_REQ       ; wait TRG.11 clean from CPU (ADC ready) 
 [0x00000960] = 0xE852095C |   889:     jbc STA Z ISEC1_ADC_RECON_WAIT 
                           |   890:          
 [0x00000964] = 0x19000014 |   891:     movl ACB 0x14 
 [0x00000968] = 0x1200000A |   892:     movl R2, ISEC1_ADC_PERIOD           ; R2 = PWM-ADC period 
 [0x0000096C] = 0xA3200000 |   893:     mov R3, R2 
 [0x00000970] = 0xC3060001 |   894:     shr R3, 1                           ; R3 = PWM-ADC duty  
 [0x00000974] = 0xB2310009 |   895:     awr R2, R3, MCS2_ISEC1_PORT_IDX     ; Start ATOM0_6 ADC Trigger 
                           |   896:  
 [0x00000978] = 0x140005DC |   897:     movl R4, ISEC_ADC_DURATION 
 [0x0000097C] = 0xC4E00000 |   898:     add R4, TBU_TS2 
 [0x00000980] = 0xE8410988 |   899:     jbs STA CY SPARK_B1_LOOP_WA 
 [0x00000984] = 0xE0000990 |   900:     jmp SPARK_B1_LOOP 
                           |   901: SPARK_B1_LOOP_WA: 
 [0x00000988] = 0xD4E00000 |   902:     atu R4, TBU_TS2 
 [0x0000098C] = 0xE8410988 |   903:     jbs STA CY SPARK_B1_LOOP_WA        
                           |   904: SPARK_B1_LOOP: 
 [0x00000990] = 0xDE400000 |   905:     atu TBU_TS2, R4 
 [0x00000994] = 0xE842099C |   906:     jbc STA CY ISEC1_STOP  ; TBU_TS2 >= R4 
 [0x00000998] = 0xE0000990 |   907:     jmp SPARK_B1_LOOP 
                           |   908:  
                           |   909:  ISEC1_STOP: 
 [0x0000099C] = 0x19000014 |   910:     movl ACB 0x14 
 [0x000009A0] = 0x13000000 |   911:     movl R3, 0 
 [0x000009A4] = 0xB3310009 |   912:     awr R3, R3, MCS2_ISEC1_PORT_IDX     ; Disable ATOM0_6 ADC Trigger 
                           |   913:      
 [0x000009A8] = 0xE00008EC |   914:     jmp IPRI1_WAIT_EVENT 
                           |   915:      
                           |   916: IPRI1_ERROR: 
 [0x000009AC] = 0xE00008F8 |   917:    jmp  IPRI1_START 
                           |   918:       
                           |   919: TASK5_DONE: 
                           |   920: ; Restart again 
                           |   921:     ;movl STA, 0x000003   ; raise interrupt 
 [0x000009B0] = 0xE00008DC |   922:     jmp   TASK5_START 
 [0x000009B4] = 0x480FFFFE |   923:     andl STA MCS_CHx_DISABLE_MASK ; disable MCS 
                           |   924:  
                           |   925: ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; 
                           |   926: ;Unused MCS2 Threads 
                           |   927: ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; 
                           |   928: tsk6_init: 
 [0x000009B8] = 0x480FFFFE |   929:     andl STA MCS_CHx_DISABLE_MASK ; disable MCS 
                           |   930:  
                           |   931: tsk7_init: 
 [0x000009BC] = 0x480FFFFE |   932:     andl STA MCS_CHx_DISABLE_MASK ; disable MCS    

 ------------------ END OF FILE: source\mcs2.mcs 

