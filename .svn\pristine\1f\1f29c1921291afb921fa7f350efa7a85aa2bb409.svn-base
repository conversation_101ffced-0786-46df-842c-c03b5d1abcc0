/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonAcqCircMgm.c
 **  File Creation Date: 23-Apr-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonAcqCircMgm
 **  Model Description:  This model selects the acquisition circuit for ion signal within a maximum number of N_CIR circuits, each of them apply a different resistance in order to increase or decrease ion signal voltage level.
   The subsystem is divided into two main tasks:
   -EOA perform standard management.
   -5ms decides if applying a manual forcing to circuit selection, according to VTFORCEIONSELECT calibration.


 **  Model Version:      1.1372
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Fri Apr 23 10:24:36 2021
 **
 **  Last Saved Modification:  RoccaG - Fri Apr 23 10:16:34 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonAcqCircMgm_out.h"
#include "IonAcqCircMgm_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/
#define IonAcqCircMgm_MINVCHARGE       (MIN_int16_T)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKACQLOAD_dim                  5U                        /* Referenced by:
                                                                  * '<S12>/Constant6'
                                                                  * '<S12>/Constant8'
                                                                  */

/* BKACQLOAD breakpoint dimension. */
#define BKACQRPM_dim                   5U                        /* Referenced by:
                                                                  * '<S12>/Constant3'
                                                                  * '<S12>/Constant4'
                                                                  */

/* BKACQRPM breakpoint dimension. */
#define DEC_GAIN                       2U                        /* Referenced by: '<S1>/CircuitSelection_Chart' */

/* Decrement gain for acquisition circuit. */
#define ID_VER_IONACQCIRCMGM_DEF       11372U                    /* Referenced by: '<S2>/Constant12' */

/* Model Version. */
#define INC_GAIN                       1U                        /* Referenced by: '<S1>/CircuitSelection_Chart' */

/* Increment gain for acquisition circuit. */
#define MAX_UINT16                     65535U                    /* Referenced by: '<S1>/CircuitSelection_Chart' */

/* Max value for uint16 type. */
#define NO_ACTION                      0U                        /* Referenced by: '<S1>/CircuitSelection_Chart' */

/* No action required for acquisition circuit gain. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONACQCIRCMGM_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinders!
#endif

#if (N_CIR_MAX != 8)
#error This code was generated with a different number of circuits!
#endif

#if (N_REC != 50)
#error This code was generated with a different number of recoveries!
#endif

#if (PMOS_MAX != 4)
#error This code was generated with a different number of P_MOS!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static int16_T VChargeObjOffPreRL_tmp; /* '<S1>/CircuitSelection_Chart' */

/* VchargeObj offset to be applied to VChargeObj in case of Gain saturation prior to RL */
static int16_T VChargeObjOff_tmp;      /* '<S1>/CircuitSelection_Chart' */

/* VchargeObj offset to be applied to VChargeObj in case of Gain saturation */
static uint8_T circuitIdx;             /* '<S8>/C Caller' */
static uint8_T cyl360;                 /* '<S8>/C Caller' */

/*Calibration memory section */
/*Start of local calbration section*/
#pragma ghs section rodata=".calib"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKACQLOAD[6] = { 6400U, 7040U, 8320U,
  9600U, 10880U, 12160U } ;

/* Breakpoints of Load for TBIONSELECT */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKACQLOADHYST[6] = { 6016U, 6656U,
  7936U, 9216U, 10496U, 11776U } ;

/* Breakpoints of Load for TBIONSELECT (hyst) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKACQRPM[6] = { 2000U, 3500U, 5500U,
  6500U, 7500U, 8500U } ;

/* Breakpoints of RpmF for TBIONSELECT */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKACQRPMHYST[6] = { 1900U, 3400U,
  5400U, 6400U, 7400U, 8400U } ;

/* Breakpoints of RpmF for TBIONSELECT (hyst) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENSELFCIRSEL = 0U;

/* Enable ion acquisition self circuit selection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENSELFCIRSELPLASMA = 0U;

/* Enable ion acquisition self circuit selection during multispark */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENVCHARGEOBJDEC = 1U;

/* Enable flag to activate VChargeObj decrement strategy */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T GAINCIRHITHR = 0U;

/* IonSelect high Gain thr (highest gain is 0) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T IONSELECTHISATTHR = 2U;

/* Levels of Threshold to confirm High saturation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T IONSELECTLOWSATTHR = 7U;

/* Levels of Threshold to confirm Low saturation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T IONSELECTTHR = 2U;

/* Max difference between TBIONSELECT and IonSelectCyl when increasing signal level in case of self circuit selection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T IONSELECTTHRDEC = 2U;

/* Max difference between TBIONSELECT and IonSelectCyl when decreasing signal level in case of self circuit selection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TBIONSELECT[36] = { 2U, 2U, 2U, 2U, 2U,
  2U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U, 3U,
  3U, 3U, 3U, 4U, 4U, 4U, 4U, 4U, 4U, 4U, 4U } ;

/* Ion input circuit selector table */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VCHARGEOBJDECDISNCYCLETHR = 200U;

/* THR of engine cycles in order to reset disable of VChargeObj Decrement startegy */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VCHARGEOBJOFFDEC = 2560;

/* Decrement on Offset of VChargeObj */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T VTFORCEIONSELECT[8] = { -1, -1, -1, -1,
  -1, -1, -1, -1 } ;

/* Force ion input circuit selector  */
#pragma ghs section rodata=default

/*End of calibration section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T IonSelect[8];                  /* '<S3>/Merge12' */

/* Ion input circuit selector */
uint8_T IonSelectCyl;                  /* '<S3>/Merge11' */

/* Ion input circuit selector */
int16_T VChargeObjOff[4];              /* '<S3>/Merge1' */

/* VchargeObj offset to be applied to VChargeObj in case of Gain saturation */

/*Static test point definition*/
/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint8_T CylToggle;

/* Cylinder toggle for circuit selection */
STATIC_TEST_POINT uint8_T DecreaseDone[8];

/* Ion acquisition circuit need level decrease */
STATIC_TEST_POINT uint16_T EngineCyclesDecDis[8];

/* Number of consecutive combustions with VChargeObjOff disabled for implausibility between cylinders on same circuit */
STATIC_TEST_POINT uint32_T IdVer_IonAcqCircMgm;

/* Model Version */
STATIC_TEST_POINT uint32_T IdxLoadIonSelect;

/* Load index with hystheresis */
STATIC_TEST_POINT uint32_T IdxRpmIonSelect;

/* Rpm index with hystheresis */
STATIC_TEST_POINT uint8_T IonSelectCylTab;

/* Ion input circuit selector */
STATIC_TEST_POINT int16_T VChargeObjOffPreRL[4];

/* VchargeObj offset to be applied to VChargeObj in case of Gain saturation prior to RL */
STATIC_TEST_POINT uint8_T VtVChargeObjDecDis[8];

/* VChargeObjOff disabled for implausibility between cylinders on same circuit */

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static uint8_T IonAcqCircMgm_CalcVChargeOff(uint8_T ionLevel, const uint8_T
  IonSelect_m[8], uint8_T VtVChargeObjDecDis_e[8], const int16_T
  VChargeObjOff_n[4], int16_T VChargeObjOffPreRL_p[4]);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<S5>/DecreaseLevel'
 * Block description for: '<S5>/DecreaseLevel'
 *   This block tries to select an acquisition circuit with a lower gain if
 *   possible.
 */
void IonAcqCircMgm_DecreaseLevel(uint8_T rtu_tmpIonSelect, uint8_T
  rtu_tmpIonSelecTab, uint8_T *rty_tmpIonSelectCyl)
{
  uint16_T u0;
  uint16_T u1;

  /* Sum: '<S7>/Add' incorporates:
   *  Constant: '<S7>/Constant8'
   */
  u0 = (uint16_T)(((uint32_T)rtu_tmpIonSelecTab) + ((uint32_T)IONSELECTTHRDEC));

  /* Sum: '<S7>/Add2' incorporates:
   *  Constant: '<S7>/Constant'
   *  Constant: '<S7>/Constant2'
   */
  u1 = (uint16_T)(((uint32_T)N_CIR) - 1U);

  /* MinMax: '<S7>/MinMax' */
  if (u0 < u1) {
    u1 = u0;
  }

  /* End of MinMax: '<S7>/MinMax' */

  /* Sum: '<S7>/Add1' incorporates:
   *  Constant: '<S7>/Constant1'
   */
  u0 = (uint16_T)(((uint32_T)rtu_tmpIonSelect) + 1U);

  /* MinMax: '<S7>/MinMax1'
   *
   * Block requirements for '<S7>/MinMax1':
   *  1. EISB_FCA6CYL_SW_REQ_1662: Software shall bound IonSelect signal by means of IonSelectTab acc... (ECU_SW_Requirements#3937)
   */
  if (u0 < u1) {
    /* DataTypeConversion: '<S7>/DataTypeConversion' */
    *rty_tmpIonSelectCyl = (uint8_T)u0;
  } else {
    /* DataTypeConversion: '<S7>/DataTypeConversion' */
    *rty_tmpIonSelectCyl = (uint8_T)u1;
  }

  /* End of MinMax: '<S7>/MinMax1' */
}

/*
 * Output and update for function-call system: '<S5>/IncreaseLevel'
 * Block description for: '<S5>/IncreaseLevel'
 *   This block tries to select an acquisition circuit with a greater gain if
 *   possible.
 */
void IonAcqCircMgm_IncreaseLevel(uint8_T rtu_tmpIonSelect, uint8_T
  rtu_tmpIonSelecTab, uint8_T *rty_tmpIonSelectCyl)
{
  int16_T u0;
  int16_T u0_0;

  /* Sum: '<S9>/Add' incorporates:
   *  Constant: '<S9>/Constant8'
   */
  u0 = (int16_T)((int32_T)(((int32_T)rtu_tmpIonSelecTab) - ((int32_T)
    IONSELECTTHR)));

  /* MinMax: '<S9>/MinMax' */
  if (u0 <= 0) {
    u0 = 0;
  }

  /* End of MinMax: '<S9>/MinMax' */

  /* MinMax: '<S9>/MinMax1' incorporates:
   *  Constant: '<S9>/Constant1'
   *  Sum: '<S9>/Add1'
   *
   * Block requirements for '<S9>/MinMax1':
   *  1. EISB_FCA6CYL_SW_REQ_1662: Software shall bound IonSelect signal by means of IonSelectTab acc... (ECU_SW_Requirements#3937)
   */
  u0_0 = (int16_T)(((int32_T)rtu_tmpIonSelect) - 1);
  if (u0_0 > u0) {
    /* DataTypeConversion: '<S9>/DataTypeConversion' */
    *rty_tmpIonSelectCyl = (uint8_T)u0_0;
  } else {
    /* DataTypeConversion: '<S9>/DataTypeConversion' */
    *rty_tmpIonSelectCyl = (uint8_T)u0;
  }

  /* End of MinMax: '<S9>/MinMax1' */
}

/*
 * Output and update for function-call system: '<S5>/KeepLevel'
 * Block description for: '<S5>/KeepLevel'
 *   This block tries to keep the old acquisition circuit respecting the limits
 *   imposed by the current engine state (engine speed and load).
 */
void IonAcqCircMgm_KeepLevel(uint8_T rtu_tmpIonSelect, uint8_T
  rtu_tmpIonSelecTab, uint8_T *rty_tmpIonSelectCyl)
{
  uint8_T u0;
  uint8_T u1;
  int16_T u0_0;

  /* Sum: '<S10>/Add1' incorporates:
   *  Constant: '<S10>/Constant1'
   */
  u0 = (uint8_T)(((uint32_T)rtu_tmpIonSelecTab) + ((uint32_T)IONSELECTTHRDEC));

  /* Sum: '<S10>/Add2' incorporates:
   *  Constant: '<S10>/Constant2'
   *  Constant: '<S10>/Constant3'
   */
  u1 = (uint8_T)(((uint32_T)N_CIR) - 1U);

  /* MinMax: '<S10>/MinMax2' */
  if (u0 < u1) {
    u1 = u0;
  }

  /* End of MinMax: '<S10>/MinMax2' */

  /* MinMax: '<S10>/MinMax3' */
  if (rtu_tmpIonSelect < u1) {
    u1 = rtu_tmpIonSelect;
  }

  /* End of MinMax: '<S10>/MinMax3' */

  /* Sum: '<S10>/Add' incorporates:
   *  Constant: '<S10>/Constant8'
   */
  u0_0 = (int16_T)((int32_T)(((int32_T)rtu_tmpIonSelecTab) - ((int32_T)
    IONSELECTTHR)));

  /* MinMax: '<S10>/MinMax' */
  if (u0_0 > 0) {
    /* DataTypeConversion: '<S10>/DataTypeConversion1' */
    u0 = (uint8_T)u0_0;
  } else {
    /* DataTypeConversion: '<S10>/DataTypeConversion1' */
    u0 = 0U;
  }

  /* End of MinMax: '<S10>/MinMax' */

  /* MinMax: '<S10>/MinMax4'
   *
   * Block requirements for '<S10>/MinMax4':
   *  1. EISB_FCA6CYL_SW_REQ_1662: Software shall bound IonSelect signal by means of IonSelectTab acc... (ECU_SW_Requirements#3937)
   */
  if (u1 > u0) {
    *rty_tmpIonSelectCyl = u1;
  } else {
    *rty_tmpIonSelectCyl = u0;
  }

  /* End of MinMax: '<S10>/MinMax4' */
}

/* Function for Chart: '<S1>/CircuitSelection_Chart' */
static uint8_T IonAcqCircMgm_CalcVChargeOff(uint8_T ionLevel, const uint8_T
  IonSelect_m[8], uint8_T VtVChargeObjDecDis_e[8], const int16_T
  VChargeObjOff_n[4], int16_T VChargeObjOffPreRL_p[4])
{
  uint8_T incDecGain;
  int16_T tmpOffset;

  /* Graphical Function 'CalcVChargeOff': '<S6>:202' */
  /* Transition: '<S6>:204' */
  incDecGain = ((uint8_T)NO_ACTION);

  /* Inport: '<Root>/VtRec' */
  if (((int32_T)VtRec[REC_VCHARGE_OFF_1 + circuitIdx]) == 0) {
    /* Transition: '<S6>:207' */
    /* Transition: '<S6>:210' */
    if (((uint32_T)ionLevel) == ION_LEVEL_HIGH) {
      /* Inport: '<Root>/StVPeakIon' incorporates:
       *  Constant: '<S1>/Constant8'
       */
      /* Transition: '<S6>:212' */
      /* Transition: '<S6>:214' */
      /*  In case of an High level has been detected and the lower circuit gain has been reached
         two actions can be taken depending on the status of the other circuit:  */
      if ((((uint32_T)StVPeakIon[(cyl360)]) == ION_LEVEL_LOW) &&
          (IonSelect_m[cyl360] <= IONSELECTHISATTHR)) {
        /* Inport: '<Root>/IonAbsTdcEOA' */
        /* Transition: '<S6>:216' */
        /* Transition: '<S6>:219'
         * Requirements for Transition: '<S6>:219':
         *  1. EISB_FCA6CYL_SW_REQ_1667: Every time that ion signal level is acknowledged high for the i_th... (ECU_SW_Requirements#3867)
         */
        /*    Considered that this is an unplausible condition the VChargeObjOff
           for this circuit shall be disabled  */
        VtVChargeObjDecDis_e[IonAbsTdcEOA] = 1U;
      } else {
        /* Constant: '<S1>/Constant6' */
        /* Transition: '<S6>:221'
         * Requirements for Transition: '<S6>:221':
         *  1. EISB_FCA6CYL_SW_REQ_1666: Every time that ion signal level is acknowledged high according to... (ECU_SW_Requirements#3866)
         */
        /*  Decrease offset  */
        VChargeObjOffPreRL_p[circuitIdx] = (int16_T)
          (((VChargeObjOff_n[circuitIdx] - VCHARGEOBJOFFDEC) > -32768) ?
           (VChargeObjOff_n[circuitIdx] - VCHARGEOBJOFFDEC) : ((int32_T)
            IonAcqCircMgm_MINVCHARGE));

        /* Transition: '<S6>:226' */
      }

      /* Transition: '<S6>:232' */
      /*  End first IF  ION_LEVEL_HIGH  */
      /* Transition: '<S6>:263' */
    } else {
      /* Inport: '<Root>/IonAbsTdcEOA' incorporates:
       *  Constant: '<S1>/Constant9'
       */
      /* Transition: '<S6>:228' */
      if ((IonSelect_m[IonAbsTdcEOA] <= GAINCIRHITHR) &&
          (VChargeObjOff_n[circuitIdx] < 0)) {
        /* Inport: '<Root>/StVPeakIon' incorporates:
         *  Constant: '<S1>/Constant7'
         *  Constant: '<S1>/Constant8'
         */
        /* Transition: '<S6>:230' */
        /* Transition: '<S6>:234' */
        if (((((uint32_T)StVPeakIon[(cyl360)]) == ION_LEVEL_HIGH) &&
             (IonSelect_m[cyl360] >= IONSELECTLOWSATTHR)) &&
            (IonSelect_m[IonAbsTdcEOA] > IONSELECTHISATTHR)) {
          /* Transition: '<S6>:236' */
          /* Transition: '<S6>:240' */
          incDecGain = ((uint8_T)INC_GAIN);

          /* Transition: '<S6>:244' */
        } else {
          /* Constant: '<S1>/Constant6' */
          /* Transition: '<S6>:242'
           * Requirements for Transition: '<S6>:242':
           *  1. EISB_FCA6CYL_SW_REQ_1668: Every time that ion signal level is acknowledged low according to ... (ECU_SW_Requirements#3868)
           */
          tmpOffset = (int16_T)(VChargeObjOff_n[circuitIdx] + VCHARGEOBJOFFDEC);
          VChargeObjOffPreRL_p[circuitIdx] = (int16_T)((tmpOffset < 0) ?
            ((int32_T)tmpOffset) : 0);
        }

        /* Transition: '<S6>:248' */
        /* Transition: '<S6>:249' */
      } else {
        /* Transition: '<S6>:246' */
        incDecGain = ((uint8_T)INC_GAIN);
      }

      /* Transition: '<S6>:251' */
      /*  End second IF ION_LEVEL_NOT_HIGH (LOW)  */
    }

    /* Transition: '<S6>:264' */
  } else {
    /* Transition: '<S6>:255' */
    /*  Recovery management  */
    if (((uint32_T)ionLevel) == ION_LEVEL_LOW) {
      /* Transition: '<S6>:254' */
      /* Transition: '<S6>:257' */
      incDecGain = ((uint8_T)INC_GAIN);

      /* Transition: '<S6>:260' */
    } else {
      /* Transition: '<S6>:259' */
      /*  ION_LEVEL_HIGH  */
      incDecGain = ((uint8_T)DEC_GAIN);
    }

    /* Transition: '<S6>:262' */
    /*  End third IF RECOVERY  */
  }

  /* End of Inport: '<Root>/VtRec' */
  /* Transition: '<S6>:266' */
  return incDecGain;
}

/* Model step function */
void IonAcqCircMgm_5ms(void)
{
  uint8_T ionIdx;
  uint8_T rtb_IonSelect_f[8];
  uint8_T CylToggle_c;
  uint8_T IonSelectCyl_d;
  int32_T u1;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonAcqCircMgm_5ms' incorporates:
   *  SubSystem: '<Root>/T5ms'
   *
   * Block description for '<Root>/T5ms':
   *  This subsystem force the acquisition circuit selection according to VTFORCEIONSELECT calibration, in case of engine stopped.
   *  No requirement has been linked to this subsystem because it is used only for debug purposes.
   */
  /* Chart: '<S4>/Task_5ms' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Inport: '<Root>/Rpm'
   *  SignalConversion generated from: '<S4>/CylToggle_old'
   *  SignalConversion generated from: '<S4>/IonSelectCyl_old'
   *  SignalConversion generated from: '<S4>/IonSelect_old'
   */
  /* Gateway: T5ms/Task_5ms */
  /* During: T5ms/Task_5ms */
  /* Entry Internal: T5ms/Task_5ms */
  /* Transition: '<S23>:10' */
  /*  Assign always a value to each output to improve code generation  */
  memcpy(&rtb_IonSelect_f[0], (&(IonSelect[0])), (sizeof(uint8_T)) << 3U);
  IonSelectCyl_d = IonSelectCyl;
  CylToggle_c = CylToggle;
  for (ionIdx = 0U; ((int32_T)ionIdx) < 2; ionIdx = (uint8_T)((int32_T)
        (((int32_T)ionIdx) + 1))) {
    /* Transition: '<S23>:48' */
    /* Transition: '<S23>:49' */
    if ((VTFORCEIONSELECT[(ionIdx)] >= 0) && (((int32_T)Rpm) == 0)) {
      /* Transition: '<S23>:12' */
      /* Transition: '<S23>:14' */
      u1 = ((int32_T)N_CIR) - 1;
      if (((int32_T)VTFORCEIONSELECT[(ionIdx)]) < u1) {
        IonSelectCyl_d = (uint8_T)VTFORCEIONSELECT[(ionIdx)];
      } else {
        IonSelectCyl_d = (uint8_T)u1;
      }

      rtb_IonSelect_f[CylToggle_c] = IonSelectCyl_d;

      /* Outputs for Function Call SubSystem: '<S4>/Calc'
       *
       * Block description for '<S4>/Calc':
       *  Call to low level function "IonAcqDD_MuxOutput" (i.e. Activation
       *  function of the switching commands of the ion acquisition circuit).
       */
      /* CCaller: '<S22>/IonAcqDD_MuxOutput' */
      /* Event: '<S23>:8' */
      IonAcqDD_MuxOutput(CylToggle_c, IonSelectCyl_d);

      /* End of Outputs for SubSystem: '<S4>/Calc' */
      CylToggle_c = (uint8_T)((int32_T)(((int32_T)CylToggle_c) + 1));
      if (CylToggle_c >= N_CYLINDER) {
        /* Transition: '<S23>:20' */
        /* Transition: '<S23>:53' */
        CylToggle_c = 0U;

        /* Transition: '<S23>:56' */
      } else {
        /* Transition: '<S23>:55' */
      }

      /* Transition: '<S23>:57' */
    } else {
      /* Transition: '<S23>:16' */
    }

    /* Transition: '<S23>:22' */
  }

  /* End of Chart: '<S4>/Task_5ms' */

  /* SignalConversion generated from: '<S4>/CylToggle' */
  /* Transition: '<S23>:50' */
  CylToggle = CylToggle_c;

  /* SignalConversion generated from: '<S4>/IonSelect' */
  memcpy((&(IonSelect[0])), &rtb_IonSelect_f[0], (sizeof(uint8_T)) << 3U);

  /* SignalConversion generated from: '<S4>/IonSelectCyl' */
  IonSelectCyl = IonSelectCyl_d;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqCircMgm_5ms' */
}

/* Model step function */
void IonAcqCircMgm_EOA(void)
{
  /* local block i/o variables */
  int16_T rtb_RateLimiter_S16;
  uint8_T incDecGain;
  uint8_T IonSelect_g[8];
  int32_T IdxRpmIonSelect_b;
  int32_T IdxLoadIonSelect_p;
  uint8_T VtVChargeObjDecDis_b[8];
  uint16_T EngineCyclesDecDis_n[8];
  int8_T DecreaseDone_o[8];
  uint32_T rtb_ELDOR_BINARYSEARCH_U16_Near;
  int16_T rtb_Product;
  int16_T VChargeObjOff_n[4];
  int32_T i;
  int16_T VChargeObjOffPreRL_p[4];

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonAcqCircMgm_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  The aim of this subsystem is to select the acquisition circuit for ion signal within a maximum number of N_CIR circuits, each of them applies a different resistance, so a different acquisition gain.
   *  Moreover the subsystem calculates a voltage offset to be applied to ion signal acquisition.
   *

   */
  /* SignalConversion generated from: '<S1>/IdxRpmIonSelect_old' */
  IdxRpmIonSelect_b = (int32_T)IdxRpmIonSelect;

  /* SignalConversion generated from: '<S1>/IdxLoadIonSelect_old' */
  IdxLoadIonSelect_p = (int32_T)IdxLoadIonSelect;
  for (i = 0; i < 8; i++) {
    /* SignalConversion generated from: '<S1>/IonSelect_old' */
    IonSelect_g[i] = IonSelect[(i)];

    /* SignalConversion generated from: '<S1>/VtVChargeObjDecDis_old' */
    VtVChargeObjDecDis_b[i] = VtVChargeObjDecDis[(i)];

    /* SignalConversion generated from: '<S1>/EngineCyclesDecDis_old' */
    EngineCyclesDecDis_n[i] = EngineCyclesDecDis[(i)];

    /* SignalConversion generated from: '<S1>/DecreaseDone_old' */
    DecreaseDone_o[i] = (int8_T)DecreaseDone[(i)];
  }

  /* SignalConversion generated from: '<S1>/VChargeObjOff_old' */
  /* Gateway: EOA/CircuitSelection_Chart */
  /* During: EOA/CircuitSelection_Chart */
  /* This chart oversees the selection of ion signal acquisition circuit .
     Circuit selection derives from:
     -voltage level of previous ion signals in correspondence with thermal peak.
     -heavy knock detection.

     First a threshold for circuit selection is calculated according to engine speed and engine load (IonSelectedCylTab) by means of a table.
     Then two different strategies can be applied, according to enabling calibrations and plasma strategy presence:

     Dinamic selection
     High level
     If heavy knock or an high level of voltage in corrispondence thermal peak have been detected, than software tries to select an acquisition circuit with a lower resistance, in order to reduce voltage value.
     It the lowerest circuit has been already selected, than software tries to reduce the voltage offset applied to ion signal.

     Low level
     If low level for thermal peak have been detected, than software tries to select an acquisition circuit with a greater resistance, so as to increase voltage value.
     It the circuit with the highest gain has been already selected, than software tries to increase the voltage offset applied to ion signal until it reaches its neutral value of 0V.

     Static selection
     No consideration is given to the voltage value at thermal peak, but acquisition circuit is selected only according to heavy knock detection and circuit selection threshold (IonSelectCylTab).

     Recovery strategy
     If a recovery on voltage offset calculation is active, than the offset is reduced until it reaches its neutral value of 0V.

     Offset disabled
     An implausibility is detected if an high level has been detected for the i_th cylinder (thermal peak voltage) but a lower level is instead detected for the other cylinder linked to the same acquisition circuit.
     In this case offset is disabled for a tunable number of combustions.

     Rate Limiter
     A rate limiter is applied to the voltage offset. */
  /* Entry Internal: EOA/CircuitSelection_Chart */
  /* Transition: '<S6>:55' */
  /* Transition: '<S6>:84' */
  /*  Assign a init value to each stateflow output to improve code generation */
  memcpy(&VChargeObjOff_n[0], (&(VChargeObjOff[0])), (sizeof(int16_T)) << 2U);

  /* SignalConversion generated from: '<S1>/VChargeObjPreRL_old' */
  memcpy(&VChargeObjOffPreRL_p[0], (&(VChargeObjOffPreRL[0])), (sizeof(int16_T))
         << 2U);

  /* Chart: '<S1>/CircuitSelection_Chart' incorporates:
   *  SubSystem: '<S5>/GetCircIdx'
   *
   * Block description for '<S1>/CircuitSelection_Chart':
   *  This chart oversees the selection of ion signal acquisition circuit .
   *  Circuit selection derives from:
   *  -voltage level of previous ion signals in correspondence with thermal peak.
   *  -heavy knock detection.
   *
   *  First a threshold for circuit selection is calculated according to engine speed and engine load (IonSelectedCylTab) by means of a table.
   *  Then two different strategies can be applied, according to enabling calibrations and plasma strategy presence:
   *
   *  Dinamic selection
   *  High level
   *  If heavy knock or an high level of voltage in corrispondence thermal peak have been detected, than software tries to select an acquisition circuit with a lower resistance, in order to reduce voltage value.
   *  It the lowerest circuit has been already selected, than software tries to reduce the voltage offset applied to ion signal.
   *
   *  Low level
   *  If low level for thermal peak have been detected, than software tries to select an acquisition circuit with a greater resistance, so as to increase voltage value.
   *  It the circuit with the highest gain has been already selected, than software tries to increase the voltage offset applied to ion signal until it reaches its neutral value of 0V.
   *
   *  Static selection
   *  No consideration is given to the voltage value at thermal peak, but acquisition circuit is selected only according to heavy knock detection and circuit selection threshold (IonSelectCylTab).
   *
   *  Recovery strategy
   *  If a recovery on voltage offset calculation is active, than the offset is reduced until it reaches its neutral value of 0V.
   *
   *  Offset disabled
   *  An implausibility is detected if an high level has been detected for the i_th cylinder (thermal peak voltage) but a lower level is instead detected for the other cylinder linked to the same acquisition circuit.
   *  In this case offset is disabled for a tunable number of combustions.
   *
   *  Rate Limiter
   *  A rate limiter is applied to the voltage offset.
   *
   * Block description for '<S5>/GetCircIdx':
   *  This block performs a static assignment of each cylinder to an acquisition channel defined by the signal "circuitIdx" (with a maximum number of channel equals to 4).
   *  Moreorer it defines for each cylinder the id of the other cylinder assigned to the same acquisition channel (signal cyl360).
   *  No requirement has been linked to this subsystem because it represents an utility function for following calculations.
   */
  /* CCaller: '<S8>/C Caller' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  /* Transition: '<S6>:86' */
  /*  calculate circuit id  */
  /* Event: '<S6>:2' */
  IonAcq_GetCirIdx(IonAbsTdcEOA, (&(cyl360)), (&(circuitIdx)));

  /* Inport: '<Root>/VtFlgVCrgObjSatMin' incorporates:
   *  Inport: '<Root>/VtFlgVCrgObjSatMax'
   */
  /* Transition: '<S6>:87' */
  /* Transition: '<S6>:56' */
  /*  Check saturation on ion charge capacitor and assign a saturation value to VCharge offset */
  if ((((int32_T)VtFlgVCrgObjSatMin[(circuitIdx)]) != 0) || (((int32_T)
        VtFlgVCrgObjSatMax[(circuitIdx)]) != 0)) {
    /* Inport: '<Root>/VCrgObjOffMinMax' */
    /* Transition: '<S6>:57' */
    /* Transition: '<S6>:59' */
    VChargeObjOff_n[circuitIdx] = VCrgObjOffMinMax[(circuitIdx)];

    /* Transition: '<S6>:61' */
  } else {
    /* Transition: '<S6>:58' */
  }

  /* End of Inport: '<Root>/VtFlgVCrgObjSatMin' */

  /* Inport: '<Root>/VtRec' */
  /* Transition: '<S6>:60' */
  /*  Check for recovery presence and reset gradually VCharge offset  */
  if (((int32_T)VtRec[REC_VCHARGE_OFF_1 + circuitIdx]) != 0) {
    /* Transition: '<S6>:45' */
    /* Transition: '<S6>:41'
     * Requirements for Transition: '<S6>:41':
     *  1. EISB_FCA6CYL_SW_REQ_1669: Every time that a recovery is active for the offset correction str... (ECU_SW_Requirements#3869)
     */
    VChargeObjOffPreRL_p[circuitIdx] = 0;

    /* Transition: '<S6>:42' */
  } else {
    /* Transition: '<S6>:43' */
    VChargeObjOffPreRL_p[circuitIdx] = VChargeObjOff_n[circuitIdx];
  }

  /* End of Inport: '<Root>/VtRec' */

  /* Constant: '<S1>/Constant2' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  /* Transition: '<S6>:46' */
  if (VTFORCEIONSELECT[(IonAbsTdcEOA)] < 0) {
    /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
     *
     * Block description for '<S5>/TbIndex':
     *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
     *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
     */
    /* DataTypeConversion: '<S16>/Conversion3' incorporates:
     *  Constant: '<S12>/Constant8'
     */
    /* Transition: '<S6>:101' */
    /* Transition: '<S6>:105' */
    /* Event: '<S6>:106' */
    rtb_ELDOR_BINARYSEARCH_U16_Near = (uint32_T)((uint8_T)BKACQLOAD_dim);

    /* S-Function (ELDOR_BINARYSEARCH_U16_Near_iL): '<S16>/ELDOR_BINARYSEARCH_U16_Near_iL' incorporates:
     *  Constant: '<S12>/Constant7'
     */
    BINARYSEARCH_U16_Near_iL( &rtb_ELDOR_BINARYSEARCH_U16_Near, Load,
      &BKACQLOADHYST[0], rtb_ELDOR_BINARYSEARCH_U16_Near);

    /* DataTypeConversion: '<S15>/Conversion3' incorporates:
     *  Constant: '<S12>/Constant6'
     */
    IdxLoadIonSelect = ((uint8_T)BKACQLOAD_dim);

    /* S-Function (ELDOR_BINARYSEARCH_U16_Near_iL): '<S15>/ELDOR_BINARYSEARCH_U16_Near_iL' incorporates:
     *  Constant: '<S12>/Constant5'
     */
    BINARYSEARCH_U16_Near_iL( (&(IdxLoadIonSelect)), Load, &BKACQLOAD[0],
      IdxLoadIonSelect);

    /* Switch: '<S13>/Switch' incorporates:
     *  Logic: '<S13>/LogicalOperator'
     *  Logic: '<S13>/LogicalOperator1'
     *  RelationalOperator: '<S13>/RelationaOperator'
     *  RelationalOperator: '<S13>/RelationaOperator1'
     *  RelationalOperator: '<S13>/RelationaOperator2'
     */
    if ((IdxLoadIonSelect != rtb_ELDOR_BINARYSEARCH_U16_Near) &&
        ((rtb_ELDOR_BINARYSEARCH_U16_Near == ((uint32_T)IdxLoadIonSelect_p)) ||
         (((uint32_T)IdxLoadIonSelect_p) == IdxLoadIonSelect))) {
      IdxLoadIonSelect = (uint32_T)IdxLoadIonSelect_p;
    }

    /* End of Switch: '<S13>/Switch' */

    /* DataTypeConversion: '<S18>/Conversion3' incorporates:
     *  Constant: '<S12>/Constant4'
     */
    rtb_ELDOR_BINARYSEARCH_U16_Near = (uint32_T)((uint8_T)BKACQRPM_dim);

    /* S-Function (ELDOR_BINARYSEARCH_U16_Near_iL): '<S18>/ELDOR_BINARYSEARCH_U16_Near_iL' incorporates:
     *  Constant: '<S12>/Constant1'
     */
    BINARYSEARCH_U16_Near_iL( &rtb_ELDOR_BINARYSEARCH_U16_Near, RpmF,
      &BKACQRPMHYST[0], rtb_ELDOR_BINARYSEARCH_U16_Near);

    /* DataTypeConversion: '<S17>/Conversion3' incorporates:
     *  Constant: '<S12>/Constant3'
     */
    IdxRpmIonSelect = ((uint8_T)BKACQRPM_dim);

    /* S-Function (ELDOR_BINARYSEARCH_U16_Near_iL): '<S17>/ELDOR_BINARYSEARCH_U16_Near_iL' incorporates:
     *  Constant: '<S12>/Constant2'
     */
    BINARYSEARCH_U16_Near_iL( (&(IdxRpmIonSelect)), RpmF, &BKACQRPM[0],
      IdxRpmIonSelect);

    /* Switch: '<S14>/Switch' incorporates:
     *  Logic: '<S14>/LogicalOperator'
     *  Logic: '<S14>/LogicalOperator1'
     *  RelationalOperator: '<S14>/RelationaOperator'
     *  RelationalOperator: '<S14>/RelationaOperator1'
     *  RelationalOperator: '<S14>/RelationaOperator2'
     */
    if ((IdxRpmIonSelect != rtb_ELDOR_BINARYSEARCH_U16_Near) &&
        ((rtb_ELDOR_BINARYSEARCH_U16_Near == ((uint32_T)IdxRpmIonSelect_b)) ||
         (((uint32_T)IdxRpmIonSelect_b) == IdxRpmIonSelect))) {
      IdxRpmIonSelect = (uint32_T)IdxRpmIonSelect_b;
    }

    /* End of Switch: '<S14>/Switch' */

    /* SignalConversion generated from: '<S1>/IonSelectCylTab' incorporates:
     *  Constant: '<S12>/Constant9'
     *  Selector: '<S12>/Selector'
     *
     * Block requirements for '<S12>/Constant9':
     *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
     */
    IonSelectCylTab = TBIONSELECT[(6 * ((int32_T)IdxLoadIonSelect)) + ((int32_T)
      IdxRpmIonSelect)];

    /* End of Outputs for SubSystem: '<S5>/TbIndex' */
    /* Transition: '<S6>:110' */
    /*  Resets VtVChargeObjDecDis after N cycles if active */
    if (((int32_T)VtVChargeObjDecDis_b[IonAbsTdcEOA]) != 0) {
      /* Constant: '<S1>/Constant1' */
      /* Transition: '<S6>:120' */
      /* Transition: '<S6>:124' */
      if (EngineCyclesDecDis_n[IonAbsTdcEOA] > VCHARGEOBJDECDISNCYCLETHR) {
        /* Transition: '<S6>:126'
         * Requirements for Transition: '<S6>:126':
         *  1. EISB_FCA6CYL_SW_REQ_1667: Every time that ion signal level is acknowledged high for the i_th... (ECU_SW_Requirements#3867)
         */
        /* Transition: '<S6>:135' */
        /*  Counter over thr reset flag  */
        VtVChargeObjDecDis_b[IonAbsTdcEOA] = 0U;

        /*  Reset counter  */
        EngineCyclesDecDis_n[IonAbsTdcEOA] = 0U;

        /* Transition: '<S6>:138' */
        /* Transition: '<S6>:139' */
      } else {
        /* Transition: '<S6>:128' */
        if (EngineCyclesDecDis_n[IonAbsTdcEOA] < ((uint16_T)MAX_UINT16)) {
          /* Transition: '<S6>:130' */
          /* Transition: '<S6>:137' */
          EngineCyclesDecDis_n[IonAbsTdcEOA] = (uint16_T)((int32_T)(((int32_T)
            EngineCyclesDecDis_n[IonAbsTdcEOA]) + 1));

          /* Transition: '<S6>:139' */
        } else {
          /* Transition: '<S6>:132' */
        }
      }

      /* End of Constant: '<S1>/Constant1' */
      /* Transition: '<S6>:142' */
      /* Transition: '<S6>:141' */
    } else {
      /* Transition: '<S6>:122' */
      EngineCyclesDecDis_n[IonAbsTdcEOA] = 0U;
    }

    /* Constant: '<S1>/Constant3' incorporates:
     *  Constant: '<S1>/Constant4'
     *  Inport: '<Root>/VtStPlasObjDBuff'
     */
    /* Transition: '<S6>:143' */
    if ((((int32_T)ENSELFCIRSEL) == 1) && ((VtStPlasObjDBuff[(IonAbsTdcEOA)] ==
          ION_ST_SEL) || ((((int32_T)ENSELFCIRSELPLASMA) == 1) &&
                          (VtStPlasObjDBuff[(IonAbsTdcEOA)] == PLAS_ION_ST_SEL))))
    {
      /* Inport: '<Root>/StVPeakIon' */
      /* Transition: '<S6>:154' */
      /* Transition: '<S6>:156' */
      /*  Dynamic selection of ion acquisition circuit  */
      switch (StVPeakIon[(IonAbsTdcEOA)]) {
       case ION_LEVEL_HIGH:
        /* Constant: '<S1>/Constant5' */
        /* Transition: '<S6>:165'
         * Requirements for Transition: '<S6>:165':
         *  1. EISB_FCA6CYL_SW_REQ_1661: Every time that ion signal level is high according to signal StVPe... (ECU_SW_Requirements#3933)
         */
        /* Transition: '<S6>:168' */
        if ((((int32_T)ENVCHARGEOBJDEC) != 0) && (((int32_T)
              VtVChargeObjDecDis_b[IonAbsTdcEOA]) == 0)) {
          /* Transition: '<S6>:170' */
          /* Transition: '<S6>:172' */
          if (IonSelect_g[IonAbsTdcEOA] < ((uint8_T)((int32_T)(((int32_T)N_CIR)
                 - 1)))) {
            /* Outputs for Function Call SubSystem: '<S5>/DecreaseLevel'
             *
             * Block description for '<S5>/DecreaseLevel':
             *  This block tries to select an acquisition circuit with a lower gain if
             *  possible.
             */
            /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
             *
             * Block description for '<S5>/TbIndex':
             *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
             *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
             */
            /* Selector: '<S12>/Selector' incorporates:
             *  Constant: '<S12>/Constant9'
             *
             * Block requirements for '<S12>/Constant9':
             *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
             */
            /* Transition: '<S6>:184' */
            /* Transition: '<S6>:186' */
            /*  Parameters assignment for FC decrease  */
            /*  decrease FC  */
            /* Event: '<S6>:190' */
            IonAcqCircMgm_DecreaseLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
              [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
              (&(IonSelectCyl)));

            /* End of Outputs for SubSystem: '<S5>/TbIndex' */
            /* End of Outputs for SubSystem: '<S5>/DecreaseLevel' */
            /*  output assignment for FC decrease */
            DecreaseDone_o[IonAbsTdcEOA] = 1;

            /* Transition: '<S6>:281' */
          } else {
            /* Transition: '<S6>:197' */
            incDecGain = IonAcqCircMgm_CalcVChargeOff(ION_LEVEL_HIGH,
              IonSelect_g, VtVChargeObjDecDis_b, VChargeObjOff_n,
              VChargeObjOffPreRL_p);

            /* Check if decrease has to be done due to Recovery  */
            if (incDecGain == ((uint8_T)DEC_GAIN)) {
              /* Outputs for Function Call SubSystem: '<S5>/DecreaseLevel'
               *
               * Block description for '<S5>/DecreaseLevel':
               *  This block tries to select an acquisition circuit with a lower gain if
               *  possible.
               */
              /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
               *
               * Block description for '<S5>/TbIndex':
               *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
               *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
               */
              /* Selector: '<S12>/Selector' incorporates:
               *  Constant: '<S12>/Constant9'
               *
               * Block requirements for '<S12>/Constant9':
               *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
               */
              /* Transition: '<S6>:270' */
              /* Transition: '<S6>:272' */
              /*  Parameters assignment for FC decrease  */
              /*  decrease FC  */
              /* Event: '<S6>:190' */
              IonAcqCircMgm_DecreaseLevel(IonSelect_g[IonAbsTdcEOA],
                TBIONSELECT[((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)
                IdxLoadIonSelect))], (&(IonSelectCyl)));

              /* End of Outputs for SubSystem: '<S5>/TbIndex' */
              /* End of Outputs for SubSystem: '<S5>/DecreaseLevel' */
              /*  output assignment for FC decrease */
              DecreaseDone_o[IonAbsTdcEOA] = 1;

              /* Transition: '<S6>:277' */
            } else {
              /* Outputs for Function Call SubSystem: '<S5>/KeepLevel'
               *
               * Block description for '<S5>/KeepLevel':
               *  This block tries to keep the old acquisition circuit respecting the
               *  limits imposed by the current engine state (engine speed and load).
               */
              /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
               *
               * Block description for '<S5>/TbIndex':
               *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
               *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
               */
              /* Selector: '<S12>/Selector' incorporates:
               *  Constant: '<S12>/Constant9'
               *
               * Block requirements for '<S12>/Constant9':
               *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
               */
              /* Transition: '<S6>:275' */
              /*  Parameters assignment for FC keep  */
              /*  increase FC  */
              /* Event: '<S6>:276' */
              IonAcqCircMgm_KeepLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
                [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
                (&(IonSelectCyl)));

              /* End of Outputs for SubSystem: '<S5>/TbIndex' */
              /* End of Outputs for SubSystem: '<S5>/KeepLevel' */
              /* output assignment for FC keep */
            }

            /* Transition: '<S6>:279' */
          }

          /* Transition: '<S6>:283' */
          /* Transition: '<S6>:284' */
        } else {
          /* Outputs for Function Call SubSystem: '<S5>/DecreaseLevel'
           *
           * Block description for '<S5>/DecreaseLevel':
           *  This block tries to select an acquisition circuit with a lower gain if
           *  possible.
           */
          /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
           *
           * Block description for '<S5>/TbIndex':
           *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
           *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
           */
          /* Selector: '<S12>/Selector' incorporates:
           *  Constant: '<S12>/Constant9'
           *
           * Block requirements for '<S12>/Constant9':
           *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
           */
          /* Transition: '<S6>:195' */
          /*  Parameters assignment for FC decrease  */
          /*  decrease FC  */
          /* Event: '<S6>:190' */
          IonAcqCircMgm_DecreaseLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
            [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
            (&(IonSelectCyl)));

          /* End of Outputs for SubSystem: '<S5>/TbIndex' */
          /* End of Outputs for SubSystem: '<S5>/DecreaseLevel' */
          /*  output assignment for FC decrease */
          DecreaseDone_o[IonAbsTdcEOA] = 1;
        }

        /* Transition: '<S6>:285' */
        /* Transition: '<S6>:438' */
        /* Transition: '<S6>:439' */
        /* Transition: '<S6>:440' */
        /* Transition: '<S6>:452' */
        break;

       case ION_LEVEL_LOW:
        /* Constant: '<S1>/Constant5' */
        /* Transition: '<S6>:290' */
        /* Transition: '<S6>:292'
         * Requirements for Transition: '<S6>:292':
         *  1. EISB_FCA6CYL_SW_REQ_1663: Every time that ion signal level is low according to signal StVPea... (ECU_SW_Requirements#3934)
         */
        /* Transition: '<S6>:323' */
        if ((((int32_T)ENVCHARGEOBJDEC) != 0) && (((int32_T)
              VtVChargeObjDecDis_b[IonAbsTdcEOA]) == 0)) {
          /* Constant: '<S1>/Constant9' */
          /* Transition: '<S6>:339' */
          /* Transition: '<S6>:341' */
          if (IonSelect_g[IonAbsTdcEOA] > GAINCIRHITHR) {
            /* Outputs for Function Call SubSystem: '<S5>/IncreaseLevel'
             *
             * Block description for '<S5>/IncreaseLevel':
             *  This block tries to select an acquisition circuit with a greater gain
             *  if possible.
             */
            /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
             *
             * Block description for '<S5>/TbIndex':
             *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
             *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
             */
            /* Selector: '<S12>/Selector' incorporates:
             *  Constant: '<S12>/Constant9'
             *
             * Block requirements for '<S12>/Constant9':
             *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
             */
            /* Transition: '<S6>:342' */
            /* Transition: '<S6>:344' */
            /*  Parameters assignment for FC increase  */
            /*  increase FC  */
            /* Event: '<S6>:273' */
            IonAcqCircMgm_IncreaseLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
              [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
              (&(IonSelectCyl)));

            /* End of Outputs for SubSystem: '<S5>/TbIndex' */
            /* End of Outputs for SubSystem: '<S5>/IncreaseLevel' */
            /*  output assignment for FC increase */
            DecreaseDone_o[IonAbsTdcEOA] = 0;

            /* Transition: '<S6>:350' */
          } else {
            /* Transition: '<S6>:343' */
            incDecGain = IonAcqCircMgm_CalcVChargeOff(ION_LEVEL_LOW, IonSelect_g,
              VtVChargeObjDecDis_b, VChargeObjOff_n, VChargeObjOffPreRL_p);

            /* Check if increase has to be done due to Recovery  */
            if (incDecGain == ((uint8_T)INC_GAIN)) {
              /* Outputs for Function Call SubSystem: '<S5>/IncreaseLevel'
               *
               * Block description for '<S5>/IncreaseLevel':
               *  This block tries to select an acquisition circuit with a greater gain
               *  if possible.
               */
              /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
               *
               * Block description for '<S5>/TbIndex':
               *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
               *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
               */
              /* Selector: '<S12>/Selector' incorporates:
               *  Constant: '<S12>/Constant9'
               *
               * Block requirements for '<S12>/Constant9':
               *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
               */
              /* Transition: '<S6>:345' */
              /* Transition: '<S6>:347' */
              /*  Parameters assignment for FC increase  */
              /*  increase FC  */
              /* Event: '<S6>:273' */
              IonAcqCircMgm_IncreaseLevel(IonSelect_g[IonAbsTdcEOA],
                TBIONSELECT[((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)
                IdxLoadIonSelect))], (&(IonSelectCyl)));

              /* End of Outputs for SubSystem: '<S5>/TbIndex' */
              /* End of Outputs for SubSystem: '<S5>/IncreaseLevel' */
              /*  output assignment for FC increase */
              DecreaseDone_o[IonAbsTdcEOA] = 0;

              /* Transition: '<S6>:348' */
            } else {
              /* Outputs for Function Call SubSystem: '<S5>/KeepLevel'
               *
               * Block description for '<S5>/KeepLevel':
               *  This block tries to keep the old acquisition circuit respecting the
               *  limits imposed by the current engine state (engine speed and load).
               */
              /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
               *
               * Block description for '<S5>/TbIndex':
               *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
               *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
               */
              /* Selector: '<S12>/Selector' incorporates:
               *  Constant: '<S12>/Constant9'
               *
               * Block requirements for '<S12>/Constant9':
               *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
               */
              /* Transition: '<S6>:346' */
              /*  Parameters assignment for FC keep  */
              /*  increase FC  */
              /* Event: '<S6>:276' */
              IonAcqCircMgm_KeepLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
                [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
                (&(IonSelectCyl)));

              /* End of Outputs for SubSystem: '<S5>/TbIndex' */
              /* End of Outputs for SubSystem: '<S5>/KeepLevel' */
              /*  output assignment for FC keep */
            }

            /* Transition: '<S6>:349' */
          }

          /* End of Constant: '<S1>/Constant9' */
          /* Transition: '<S6>:351' */
          /* Transition: '<S6>:352' */
        } else {
          /* Outputs for Function Call SubSystem: '<S5>/IncreaseLevel'
           *
           * Block description for '<S5>/IncreaseLevel':
           *  This block tries to select an acquisition circuit with a greater gain
           *  if possible.
           */
          /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
           *
           * Block description for '<S5>/TbIndex':
           *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
           *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
           */
          /* Selector: '<S12>/Selector' incorporates:
           *  Constant: '<S12>/Constant9'
           *
           * Block requirements for '<S12>/Constant9':
           *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
           */
          /* Transition: '<S6>:340' */
          /*  Parameters assignment for FC increase  */
          /*  increase FC  */
          /* Event: '<S6>:273' */
          IonAcqCircMgm_IncreaseLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
            [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
            (&(IonSelectCyl)));

          /* End of Outputs for SubSystem: '<S5>/TbIndex' */
          /* End of Outputs for SubSystem: '<S5>/IncreaseLevel' */
          /*  output assignment for FC increase */
          DecreaseDone_o[IonAbsTdcEOA] = 0;
        }

        /* Transition: '<S6>:354' */
        /* Transition: '<S6>:439' */
        /* Transition: '<S6>:440' */
        /* Transition: '<S6>:452' */
        break;

       default:
        /* Inport: '<Root>/FlgHeavyKnock' */
        /* Transition: '<S6>:388' */
        if ((((int32_T)FlgHeavyKnock[(IonAbsTdcEOA)]) != 0) &&
            (DecreaseDone_o[IonAbsTdcEOA] == 0)) {
          /* Constant: '<S1>/Constant5' */
          /* Transition: '<S6>:390'
           * Requirements for Transition: '<S6>:390':
           *  1. EISB_FCA6CYL_SW_REQ_1664: Every time that an heavy knock has been detected (see FlgHeavyKnoc... (ECU_SW_Requirements#3936)
           */
          /* Transition: '<S6>:391' */
          if ((((int32_T)ENVCHARGEOBJDEC) != 0) && (((int32_T)
                VtVChargeObjDecDis_b[IonAbsTdcEOA]) == 0)) {
            /* Transition: '<S6>:369' */
            /* Transition: '<S6>:371' */
            if (IonSelect_g[IonAbsTdcEOA] < ((uint8_T)((int32_T)(((int32_T)N_CIR)
                   - 1)))) {
              /* Outputs for Function Call SubSystem: '<S5>/DecreaseLevel'
               *
               * Block description for '<S5>/DecreaseLevel':
               *  This block tries to select an acquisition circuit with a lower gain if
               *  possible.
               */
              /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
               *
               * Block description for '<S5>/TbIndex':
               *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
               *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
               */
              /* Selector: '<S12>/Selector' incorporates:
               *  Constant: '<S12>/Constant9'
               *
               * Block requirements for '<S12>/Constant9':
               *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
               */
              /* Transition: '<S6>:372' */
              /* Transition: '<S6>:374' */
              /*  Parameters assignment for FC decrease  */
              /*  decrease FC  */
              /* Event: '<S6>:190' */
              IonAcqCircMgm_DecreaseLevel(IonSelect_g[IonAbsTdcEOA],
                TBIONSELECT[((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)
                IdxLoadIonSelect))], (&(IonSelectCyl)));

              /* End of Outputs for SubSystem: '<S5>/TbIndex' */
              /* End of Outputs for SubSystem: '<S5>/DecreaseLevel' */
              /*  output assignment for FC decrease */
              DecreaseDone_o[IonAbsTdcEOA] = 1;

              /* Transition: '<S6>:380' */
            } else {
              /* Transition: '<S6>:373' */
              incDecGain = IonAcqCircMgm_CalcVChargeOff(ION_LEVEL_HIGH,
                IonSelect_g, VtVChargeObjDecDis_b, VChargeObjOff_n,
                VChargeObjOffPreRL_p);

              /* Check if decrease has to be done due to Recovery  */
              if (incDecGain == ((uint8_T)DEC_GAIN)) {
                /* Outputs for Function Call SubSystem: '<S5>/DecreaseLevel'
                 *
                 * Block description for '<S5>/DecreaseLevel':
                 *  This block tries to select an acquisition circuit with a lower gain if
                 *  possible.
                 */
                /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
                 *
                 * Block description for '<S5>/TbIndex':
                 *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
                 *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
                 */
                /* Selector: '<S12>/Selector' incorporates:
                 *  Constant: '<S12>/Constant9'
                 *
                 * Block requirements for '<S12>/Constant9':
                 *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
                 */
                /* Transition: '<S6>:375' */
                /* Transition: '<S6>:377' */
                /*  Parameters assignment for FC decrease  */
                /*  decrease FC  */
                /* Event: '<S6>:190' */
                IonAcqCircMgm_DecreaseLevel(IonSelect_g[IonAbsTdcEOA],
                  TBIONSELECT[((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)
                  IdxLoadIonSelect))], (&(IonSelectCyl)));

                /* End of Outputs for SubSystem: '<S5>/TbIndex' */
                /* End of Outputs for SubSystem: '<S5>/DecreaseLevel' */
                /*  output assignment for FC decrease */
                DecreaseDone_o[IonAbsTdcEOA] = 1;

                /* Transition: '<S6>:378' */
              } else {
                /* Outputs for Function Call SubSystem: '<S5>/KeepLevel'
                 *
                 * Block description for '<S5>/KeepLevel':
                 *  This block tries to keep the old acquisition circuit respecting the
                 *  limits imposed by the current engine state (engine speed and load).
                 */
                /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
                 *
                 * Block description for '<S5>/TbIndex':
                 *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
                 *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
                 */
                /* Selector: '<S12>/Selector' incorporates:
                 *  Constant: '<S12>/Constant9'
                 *
                 * Block requirements for '<S12>/Constant9':
                 *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
                 */
                /* Transition: '<S6>:376' */
                /*  Parameters assignment for FC keep  */
                /*  increase FC  */
                /* Event: '<S6>:276' */
                IonAcqCircMgm_KeepLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
                  [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
                  (&(IonSelectCyl)));

                /* End of Outputs for SubSystem: '<S5>/TbIndex' */
                /* End of Outputs for SubSystem: '<S5>/KeepLevel' */
                /*  output assignment for FC keep */
              }

              /* Transition: '<S6>:379' */
            }

            /* Transition: '<S6>:381' */
            /* Transition: '<S6>:382' */
          } else {
            /* Outputs for Function Call SubSystem: '<S5>/DecreaseLevel'
             *
             * Block description for '<S5>/DecreaseLevel':
             *  This block tries to select an acquisition circuit with a lower gain if
             *  possible.
             */
            /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
             *
             * Block description for '<S5>/TbIndex':
             *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
             *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
             */
            /* Selector: '<S12>/Selector' incorporates:
             *  Constant: '<S12>/Constant9'
             *
             * Block requirements for '<S12>/Constant9':
             *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
             */
            /* Transition: '<S6>:370' */
            /*  Parameters assignment for FCdecrease  */
            /*  decrease FC  */
            /* Event: '<S6>:190' */
            IonAcqCircMgm_DecreaseLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
              [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
              (&(IonSelectCyl)));

            /* End of Outputs for SubSystem: '<S5>/TbIndex' */
            /* End of Outputs for SubSystem: '<S5>/DecreaseLevel' */
            /*  output assignment for FC decrease */
            DecreaseDone_o[IonAbsTdcEOA] = 1;
          }

          /* Transition: '<S6>:384' */
          /* Transition: '<S6>:440' */
          /* Transition: '<S6>:452' */
        } else {
          /* Transition: '<S6>:427'
           * Requirements for Transition: '<S6>:427':
           *  1. EISB_FCA6CYL_SW_REQ_1665: Every time that none of the strategies defined in requirements EIS... (ECU_SW_Requirements#3935)
           */
          if (((int32_T)FlgHeavyKnock[(IonAbsTdcEOA)]) == 0) {
            /* Outputs for Function Call SubSystem: '<S5>/KeepLevel'
             *
             * Block description for '<S5>/KeepLevel':
             *  This block tries to keep the old acquisition circuit respecting the
             *  limits imposed by the current engine state (engine speed and load).
             */
            /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
             *
             * Block description for '<S5>/TbIndex':
             *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
             *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
             */
            /* Selector: '<S12>/Selector' incorporates:
             *  Constant: '<S12>/Constant9'
             *
             * Block requirements for '<S12>/Constant9':
             *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
             */
            /* Transition: '<S6>:429' */
            /* Transition: '<S6>:432' */
            /* Transition: '<S6>:416' */
            /*  Parameters assignment for FC keep  */
            /*  increase FC  */
            /* Event: '<S6>:276' */
            IonAcqCircMgm_KeepLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
              [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
              (&(IonSelectCyl)));

            /* End of Outputs for SubSystem: '<S5>/TbIndex' */
            /* End of Outputs for SubSystem: '<S5>/KeepLevel' */
            /*  output assignment for FC keep */
            DecreaseDone_o[IonAbsTdcEOA] = 0;

            /* Transition: '<S6>:424' */
            /* Transition: '<S6>:452' */
          } else {
            /* Outputs for Function Call SubSystem: '<S5>/KeepLevel'
             *
             * Block description for '<S5>/KeepLevel':
             *  This block tries to keep the old acquisition circuit respecting the
             *  limits imposed by the current engine state (engine speed and load).
             */
            /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
             *
             * Block description for '<S5>/TbIndex':
             *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
             *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
             */
            /* Selector: '<S12>/Selector' incorporates:
             *  Constant: '<S12>/Constant9'
             *
             * Block requirements for '<S12>/Constant9':
             *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
             */
            /* Transition: '<S6>:448' */
            /* Transition: '<S6>:445' */
            /*  Parameters assignment for FC keep  */
            /*  increase FC  */
            /* Event: '<S6>:276' */
            IonAcqCircMgm_KeepLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
              [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
              (&(IonSelectCyl)));

            /* End of Outputs for SubSystem: '<S5>/TbIndex' */
            /* End of Outputs for SubSystem: '<S5>/KeepLevel' */
            /*  output assignment for FC keep */
            DecreaseDone_o[IonAbsTdcEOA] = 0;

            /* Transition: '<S6>:447' */
          }
        }
        break;
      }

      /* End of Inport: '<Root>/StVPeakIon' */
      /* Transition: '<S6>:453' */
      /* Transition: '<S6>:665' */
    } else {
      /* Inport: '<Root>/FlgHeavyKnock' */
      /* Transition: '<S6>:158' */
      /*  Static selection of ion acquisition circuit  */
      if ((((int32_T)FlgHeavyKnock[(IonAbsTdcEOA)]) != 0) &&
          (DecreaseDone_o[IonAbsTdcEOA] == 0)) {
        /* Outputs for Function Call SubSystem: '<S5>/DecreaseLevel'
         *
         * Block description for '<S5>/DecreaseLevel':
         *  This block tries to select an acquisition circuit with a lower gain if
         *  possible.
         */
        /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
         *
         * Block description for '<S5>/TbIndex':
         *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
         *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
         */
        /* Selector: '<S12>/Selector' incorporates:
         *  Constant: '<S12>/Constant9'
         *
         * Block requirements for '<S12>/Constant9':
         *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
         */
        /* Transition: '<S6>:480'
         * Requirements for Transition: '<S6>:480':
         *  1. EISB_FCA6CYL_SW_REQ_1664: Every time that an heavy knock has been detected (see FlgHeavyKnoc... (ECU_SW_Requirements#3936)
         */
        /* Transition: '<S6>:483' */
        /* Transition: '<S6>:590' */
        /*  Parameters assignment for FC decrease  */
        /*  decrease FC  */
        /* Event: '<S6>:190' */
        IonAcqCircMgm_DecreaseLevel(TBIONSELECT[((int32_T)IdxRpmIonSelect) + (6 *
          ((int32_T)IdxLoadIonSelect))], TBIONSELECT[((int32_T)IdxRpmIonSelect)
          + (6 * ((int32_T)IdxLoadIonSelect))], (&(IonSelectCyl)));

        /* End of Outputs for SubSystem: '<S5>/TbIndex' */
        /* End of Outputs for SubSystem: '<S5>/DecreaseLevel' */
        /*  output assignment for FC decrease */
        DecreaseDone_o[IonAbsTdcEOA] = 1;

        /* Transition: '<S6>:603' */
        /* Transition: '<S6>:500' */
        /* Transition: '<S6>:499' */
      } else {
        /* Transition: '<S6>:481'
         * Requirements for Transition: '<S6>:481':
         *  1. EISB_FCA6CYL_SW_REQ_1665: Every time that none of the strategies defined in requirements EIS... (ECU_SW_Requirements#3935)
         */
        if (((int32_T)FlgHeavyKnock[(IonAbsTdcEOA)]) == 0) {
          /* Outputs for Function Call SubSystem: '<S5>/KeepLevel'
           *
           * Block description for '<S5>/KeepLevel':
           *  This block tries to keep the old acquisition circuit respecting the
           *  limits imposed by the current engine state (engine speed and load).
           */
          /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
           *
           * Block description for '<S5>/TbIndex':
           *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
           *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
           */
          /* Selector: '<S12>/Selector' incorporates:
           *  Constant: '<S12>/Constant9'
           *
           * Block requirements for '<S12>/Constant9':
           *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
           */
          /* Transition: '<S6>:484' */
          /* Transition: '<S6>:488' */
          /* Transition: '<S6>:573' */
          /*  Parameters assignment for FC decrease  */
          /*  increase FC  */
          /* Event: '<S6>:276' */
          IonAcqCircMgm_KeepLevel(TBIONSELECT[((int32_T)IdxRpmIonSelect) + (6 *
            ((int32_T)IdxLoadIonSelect))], TBIONSELECT[((int32_T)IdxRpmIonSelect)
            + (6 * ((int32_T)IdxLoadIonSelect))], (&(IonSelectCyl)));

          /* End of Outputs for SubSystem: '<S5>/TbIndex' */
          /* End of Outputs for SubSystem: '<S5>/KeepLevel' */
          /*  output assignment for FC keep */
          DecreaseDone_o[IonAbsTdcEOA] = 0;

          /* Transition: '<S6>:574' */
          /* Transition: '<S6>:499' */
        } else {
          /* Outputs for Function Call SubSystem: '<S5>/KeepLevel'
           *
           * Block description for '<S5>/KeepLevel':
           *  This block tries to keep the old acquisition circuit respecting the
           *  limits imposed by the current engine state (engine speed and load).
           */
          /* Outputs for Function Call SubSystem: '<S5>/TbIndex'
           *
           * Block description for '<S5>/TbIndex':
           *  This subsystem calculates a threshold for circuit selection by means of the table TBIONSELECT. TBIONSELECT is function of engine speed and engine load.
           *  The circuit selection threshold is calculated taking into account a double hysteresis, both on engine speed and on engine load.
           */
          /* Selector: '<S12>/Selector' incorporates:
           *  Constant: '<S12>/Constant9'
           *
           * Block requirements for '<S12>/Constant9':
           *  1. EISB_FCA6CYL_SW_REQ_1660: Software shall define a normal value for gain selector of ion acqu... (ECU_SW_Requirements#3932)
           */
          /* Transition: '<S6>:486' */
          /* Transition: '<S6>:567' */
          /*  Parameters assignment for FC decrease  */
          /*  increase FC  */
          /* Event: '<S6>:276' */
          IonAcqCircMgm_KeepLevel(IonSelect_g[IonAbsTdcEOA], TBIONSELECT
            [((int32_T)IdxRpmIonSelect) + (6 * ((int32_T)IdxLoadIonSelect))],
            (&(IonSelectCyl)));

          /* End of Outputs for SubSystem: '<S5>/TbIndex' */
          /* End of Outputs for SubSystem: '<S5>/KeepLevel' */
          /*  output assignment for FC keep */
          DecreaseDone_o[IonAbsTdcEOA] = 0;

          /* Transition: '<S6>:568' */
        }
      }

      /* Transition: '<S6>:504' */
    }

    /* End of Constant: '<S1>/Constant3' */
    /* Transition: '<S6>:667' */
    IdxRpmIonSelect_b = ((int32_T)N_CIR) - 1;
    if (((int32_T)IonSelectCyl) >= IdxRpmIonSelect_b) {
      IonSelectCyl = (uint8_T)IdxRpmIonSelect_b;
    }

    /* Transition: '<S6>:668' */
  } else {
    /* Transition: '<S6>:103' */
    IdxRpmIonSelect_b = ((int32_T)N_CIR) - 1;
    if (((int32_T)VTFORCEIONSELECT[(IonAbsTdcEOA)]) < IdxRpmIonSelect_b) {
      IonSelectCyl = (uint8_T)VTFORCEIONSELECT[(IonAbsTdcEOA)];
    } else {
      IonSelectCyl = (uint8_T)IdxRpmIonSelect_b;
    }

    /* Outputs for Function Call SubSystem: '<S5>/RstIndex'
     *
     * Block requirements for '<S5>/RstIndex':
     *  1. EISB_FCA6CYL_SW_REQ_1658: Software shall initialize each signal used for Ion HW gain management function at ECU power on. (ECU_SW_Requirements#3930)
     */
    /* SignalConversion generated from: '<S11>/IdxRpmIonSelect' incorporates:
     *  Constant: '<S11>/Constant'
     */
    /* Event: '<S6>:107' */
    IdxRpmIonSelect = 0U;

    /* SignalConversion generated from: '<S11>/IdxLoadIonSelect' incorporates:
     *  Constant: '<S11>/Constant1'
     */
    IdxLoadIonSelect = 0U;

    /* SignalConversion generated from: '<S1>/IonSelectCylTab' incorporates:
     *  Constant: '<S11>/Constant2'
     *  SignalConversion generated from: '<S11>/IonSelectCylTab'
     */
    IonSelectCylTab = 0U;

    /* End of Outputs for SubSystem: '<S5>/RstIndex' */
  }

  /* End of Constant: '<S1>/Constant2' */
  /* Transition: '<S6>:670' */
  /* Transition: '<S6>:681' */
  /*  Apply rate limiter to VChargeObjOff output  */
  /* Simulink Function 'RateLimiter': '<S6>:675' */
  VChargeObjOffPreRL_tmp = VChargeObjOffPreRL_p[circuitIdx];
  VChargeObjOff_tmp = VChargeObjOff_n[circuitIdx];

  /* Chart: '<S1>/CircuitSelection_Chart' incorporates:
   *  SubSystem: '<S6>/VCharge_Offset_Out.RateLimiter'
   *
   * Block description for '<S1>/CircuitSelection_Chart':
   *  This chart oversees the selection of ion signal acquisition circuit .
   *  Circuit selection derives from:
   *  -voltage level of previous ion signals in correspondence with thermal peak.
   *  -heavy knock detection.
   *
   *  First a threshold for circuit selection is calculated according to engine speed and engine load (IonSelectedCylTab) by means of a table.
   *  Then two different strategies can be applied, according to enabling calibrations and plasma strategy presence:
   *
   *  Dinamic selection
   *  High level
   *  If heavy knock or an high level of voltage in corrispondence thermal peak have been detected, than software tries to select an acquisition circuit with a lower resistance, in order to reduce voltage value.
   *  It the lowerest circuit has been already selected, than software tries to reduce the voltage offset applied to ion signal.
   *
   *  Low level
   *  If low level for thermal peak have been detected, than software tries to select an acquisition circuit with a greater resistance, so as to increase voltage value.
   *  It the circuit with the highest gain has been already selected, than software tries to increase the voltage offset applied to ion signal until it reaches its neutral value of 0V.
   *
   *  Static selection
   *  No consideration is given to the voltage value at thermal peak, but acquisition circuit is selected only according to heavy knock detection and circuit selection threshold (IonSelectCylTab).
   *
   *  Recovery strategy
   *  If a recovery on voltage offset calculation is active, than the offset is reduced until it reaches its neutral value of 0V.
   *
   *  Offset disabled
   *  An implausibility is detected if an high level has been detected for the i_th cylinder (thermal peak voltage) but a lower level is instead detected for the other cylinder linked to the same acquisition circuit.
   *  In this case offset is disabled for a tunable number of combustions.
   *
   *  Rate Limiter
   *  A rate limiter is applied to the voltage offset.
   */
  /* Product: '<S19>/Product' incorporates:
   *  Constant: '<S19>/Constant1'
   */
  rtb_Product = (int16_T)(-VCHARGEOBJOFFDEC);

  /* S-Function (RateLimiter_S16): '<S20>/RateLimiter_S16' incorporates:
   *  Constant: '<S19>/Constant1'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16, VChargeObjOffPreRL_tmp,
                  VChargeObjOff_tmp, rtb_Product, VCHARGEOBJOFFDEC);

  /* DataTypeConversion: '<S21>/Conversion' */
  VChargeObjOff_n[circuitIdx] = rtb_RateLimiter_S16;

  /* Inport: '<Root>/IonAbsTdcEOA' */
  /* Transition: '<S6>:680' */
  IonSelect_g[IonAbsTdcEOA] = IonSelectCyl;
  for (i = 0; i < 8; i++) {
    /* SignalConversion generated from: '<S1>/DecreaseDone' */
    DecreaseDone[(i)] = (uint8_T)DecreaseDone_o[i];

    /* SignalConversion generated from: '<S1>/EngineCyclesDecDis' */
    EngineCyclesDecDis[(i)] = EngineCyclesDecDis_n[i];

    /* SignalConversion generated from: '<S1>/IonSelect' */
    IonSelect[(i)] = IonSelect_g[i];
  }

  /* SignalConversion generated from: '<S1>/VChargeObjOff' */
  memcpy((&(VChargeObjOff[0])), &VChargeObjOff_n[0], (sizeof(int16_T)) << 2U);

  /* SignalConversion generated from: '<S1>/VChargeObjOffPreRL' */
  memcpy((&(VChargeObjOffPreRL[0])), &VChargeObjOffPreRL_p[0], (sizeof(int16_T))
         << 2U);

  /* SignalConversion generated from: '<S1>/VtVChargeObjDecDis' */
  memcpy((&(VtVChargeObjDecDis[0])), &VtVChargeObjDecDis_b[0], (sizeof(uint8_T))
         << 3U);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqCircMgm_EOA' */
}

/* Model step function */
void IonAcqCircMgm_PowerOn(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonAcqCircMgm_PowerOn' incorporates:
   *  SubSystem: '<Root>/PowerOn'
   *
   * Block description for '<Root>/PowerOn':
   *  This block performs initialization for each model output.
   *
   * Block requirements for '<Root>/PowerOn':
   *  1. EISB_FCA6CYL_SW_REQ_1658: Software shall initialize each signal used for Ion HW gain management function at ECU power on. (ECU_SW_Requirements#3930)
   */
  for (i = 0; i < 8; i++) {
    /* MinMax: '<S2>/MinMax' incorporates:
     *  Constant: '<S2>/Constant1'
     *
     * Block requirements for '<S2>/MinMax':
     *  1. EISB_FCA6CYL_SW_REQ_397: Software shall set the digital output G1_X_X to a tunable value, d... (ECU_SW_Requirements#372)
     */
    if (VTFORCEIONSELECT[(i)] > 0) {
      /* DataTypeConversion: '<S2>/DataTypeConversion' */
      IonSelect[(i)] = (uint8_T)VTFORCEIONSELECT[(i)];
    } else {
      /* DataTypeConversion: '<S2>/DataTypeConversion' */
      IonSelect[(i)] = 0U;
    }

    /* End of MinMax: '<S2>/MinMax' */

    /* SignalConversion generated from: '<S2>/VtVChargeObjDecDis' */
    VtVChargeObjDecDis[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/EngineCyclesDecDis' */
    EngineCyclesDecDis[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/DecreaseDone' */
    DecreaseDone[(i)] = 0U;
  }

  /* MultiPortSwitch: '<S2>/IndexVector' incorporates:
   *  Constant: '<S2>/Constant2'
   */
  IonSelectCyl = IonSelect[0];

  /* SignalConversion generated from: '<S2>/VChargeObjOff' */
  memset((&(VChargeObjOff[0])), 0, (sizeof(int16_T)) << 2U);

  /* SignalConversion generated from: '<S2>/VChargeObjOffPreRL' */
  memset((&(VChargeObjOffPreRL[0])), 0, (sizeof(int16_T)) << 2U);

  /* SignalConversion generated from: '<S2>/IdxRpmIonSelect' incorporates:
   *  Constant: '<S2>/Constant4'
   */
  IdxRpmIonSelect = 0U;

  /* SignalConversion generated from: '<S2>/IdxLoadIonSelect' incorporates:
   *  Constant: '<S2>/Constant5'
   */
  IdxLoadIonSelect = 0U;

  /* SignalConversion generated from: '<S2>/IonSelectCylTab' incorporates:
   *  Constant: '<S2>/Constant6'
   */
  IonSelectCylTab = 0U;

  /* SignalConversion generated from: '<S2>/CylToggle' incorporates:
   *  Constant: '<S2>/Constant7'
   */
  CylToggle = 0U;

  /* Constant: '<S2>/Constant12' */
  IdVer_IonAcqCircMgm = ID_VER_IONACQCIRCMGM_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonAcqCircMgm_PowerOn' */
}

/* Model initialize function */
void IonAcqCircMgm_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T IonSelectCyl;
uint8_T IonSelect[N_CYL_MAX];
int16_T VChargeObjOff[PMOS_MAX];
void IonAcqCircMgm_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    IonSelect[idx] = 0u;
  }

  for (idx=0;idx<PMOS_MAX;idx++) {
    VChargeObjOff[idx] = 0;
  }

  IonSelectCyl = 0u;
}

void IonAcqCircMgm_PowerOn(void)
{
  IonAcqCircMgm_Stub();
}

void IonAcqCircMgm_EOA(void)
{
  IonAcqCircMgm_Stub();
}

void IonAcqCircMgm_5ms(void)
{
  IonAcqCircMgm_Stub();
}

#endif                                 /* _BUILD_IONACQCIRCMGM_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/