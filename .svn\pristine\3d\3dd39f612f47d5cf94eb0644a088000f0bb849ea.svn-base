;/****************************************************************************
;*
;* Copyright © 2019-2020 STMicroelectronics - All Rights Reserved
;*
;* License terms: STMicroelectronics Proprietary in accordance with licensing
;* terms SLA0089 at www.st.com
;*
;* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
;* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
;*
;* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
;*****************************************************************************/

;--------------------------------------------------------
;==================  PSM DATA for CYL 7  ================
;--------------------------------------------------------

; PSM_SIZE        46 data words
Data_cylinder7:
  cyl7_psm_size:              .var 0
  cyl7_cylinder_index:        .var 0
  cyl7_output_delay:          .var 0

  cyl7_epws:                  .var 0

cyl7_epws_data:
  cyl7_epws1_n_pulse:         .var 0
  cyl7_epws1_period:          .var 0
  cyl7_epws1_duty:            .var 0

  cyl7_epws2_n_pulse:         .var 0
  cyl7_epws2_period:          .var 0
  cyl7_epws2_duty:            .var 0

  cyl7_epws3_n_pulse:         .var 0
  cyl7_epws3_period:          .var 0
  cyl7_epws3_duty:            .var 0

  cyl7_epws4_n_pulse:         .var 0
  cyl7_epws4_period:          .var 0
  cyl7_epws4_duty:            .var 0

  cyl7_epws_last_pulse:       .var 0
  cyl7_epws_timeout:          .var 0

  cyl7_pmos_duration:         .var 0
  cyl7_nmos_duration:         .var 0

  cyl7_coil_loading_step:     .var 0
  cyl7_coil_unloading_step:   .var 0

  cyl7_d1_duration:           .var 0
  cyl7_d1_perdiod:            .var 0
  cyl7_d1_duty:               .var 0

cyl7_coil_settings:
  cyl7_d2_duration:           .var 0
  cyl7_d2_perdiod:            .var 0
  cyl7_d2_duty:               .var 0

  cyl7_d3_duration:           .var 0
  cyl7_d3_perdiod:            .var 0
  cyl7_d3_duty:               .var 0

  cyl7_d4_duration:           .var 0
  cyl7_d4_perdiod:            .var 0
  cyl7_d4_duty:               .var 0

  cyl7_d5_duration:           .var 0
  cyl7_d5_perdiod:            .var 0
  cyl7_d5_duty:               .var 0

  cyl7_d6_duration:           .var 0
  cyl7_d6_perdiod:            .var 0
  cyl7_d6_duty:               .var 0

  cyl7_d7_duration:           .var 0
  cyl7_d7_perdiod:            .var 0
  cyl7_d7_duty:               .var 0

  cyl7_d8_duration:           .var 0
  cyl7_d8_perdiod:            .var 0
  cyl7_d8_duty:               .var 0

  cyl7_end_conf_param:        .var 0xFFFFFF7
.define   CYL7_VAR    Data_cylinder7
