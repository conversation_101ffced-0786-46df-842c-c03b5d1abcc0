/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Digio
**  Filename        :  Digio.c
**  Created on      :  12-mag-2020 08:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef _BUILD_DIGIO_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Digio.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : Dio_InOut
**
**   Description:
**    Configures microcontroller pins like output or input
**
**   Parameters :
**    [in] uint8_T type : pin direction
**    [in] uint16_T pid : pin number
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
inline void Dio_InOut(uint8_T type, uint16_T pid)
{
    switch (type)
    {
        case (GPIO_OUTPUT):
            // configure pin pid as a GPIO output pin
            SIUL2.MSCR_IO[pid].B.SSS = 0u;
            SIUL2.MSCR_IO[pid].B.IBE = 0u;
            SIUL2.MSCR_IO[pid].B.ODC = 2u;
        break;
        case (GPIO_INPUT):
            // configure pin pid as a GPIO input pin 
            SIUL2.MSCR_IO[pid].B.SSS = 0u;
            SIUL2.MSCR_IO[pid].B.IBE = 1u;
        break;
        default:
        break;
    }
}

/******************************************************************************
**   Function    : Dio_ReadChannel
**
**   Description:
**    Reads the status of the selected pin
**
**   Parameters :
**    [in] uint16_T chId : pin number
**
**   Returns:
**    uint8_T level: status of the selected pin (0/1)
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
inline uint8_T  Dio_ReadChannel(uint16_T chId)
{
    uint8_T level;

    if (SIUL2.MSCR_IO[chId].B.ODC == 0u) //Output buffer disabled 
    {
        level = (uint8_T)(SIUL2.GPDI[chId].B.PDI);
    }
    else
    {
        level = (uint8_T)(SIUL2.GPDO[chId].B.PDO);
    }

    return level;

}

/******************************************************************************
**   Function    : Dio_WriteChannel
**
**   Description:
**    Set the selected pin at the level value
**
**   Parameters :
**    [in] uint16_T chId : pin number
**    [in] uint8_T level: requested level
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
inline void  Dio_WriteChannel(uint16_T chId, uint8_T level)
{
    SIUL2.GPDO[chId].R = level;
}

 
/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/

 #endif //_BUILD_SWCNAME_

/****************************************************************************
 ****************************************************************************/
