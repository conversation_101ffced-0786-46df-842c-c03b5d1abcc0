/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonIntMgm.h
 **  Date:          02-Dec-2021
 **
 **  Model Version: 1.1383
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonIntMgm_h_
#define RTW_HEADER_IonIntMgm_h_
#include <string.h>
#ifndef IonIntMgm_COMMON_INCLUDES_
# define IonIntMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonIntMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

 
/* Enumerated types definition */
typedef uint8_T enum_StVPeakIon;
#define ION_LEVEL_OK                   ((enum_StVPeakIon)0U)     /* Default value */
#define ION_LEVEL_HIGH                 ((enum_StVPeakIon)1U)
#define ION_LEVEL_LOW                  ((enum_StVPeakIon)2U)
 
/* Model entry point functions */
extern void IonIntMgm_initialize(void);

/* Exported entry point function */
extern void IonIntMgm_EOA(void);

/* Exported entry point function */
extern void IonIntMgm_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T ChPeakCyl;             /* '<S3>/Merge14' */

/* Ion Chemical phase peak */
extern uint16_T FFS[8];                /* '<S3>/Merge13' */

/* Ion Front flame speed */
extern uint16_T IntIon[8];             /* '<S3>/Merge6' */

/* Ion total integral */
extern uint8_T IonErrorStatus[8];      /* '<S3>/Merge2' */

/* Ion error status */
extern enum_StVPeakIon StVPeakIon[8];  /* '<S3>/Merge3' */

/* Dynamic circuit selection state */
extern uint16_T ThInt[8];              /* '<S3>/Merge23' */

/* Ion thermal phase integral */
extern uint16_T ThPeak[8];             /* '<S3>/Merge25' */

/* Ion thermal phase peak */
extern int16_T ThPeakAngle[8];         /* '<S3>/Merge26' */

/* Ion thermal phase peak angle from TDC */
extern uint32_T ThPeakCyl;             /* '<S3>/Merge21' */

/* Ion thermal phase peak */
extern uint32_T ThPeakHR[8];           /* '<S3>/Merge24' */

/* Ion thermal phase peak high res */
extern uint16_T ThPeakId;              /* '<S3>/Merge22' */

/* Ion thermal peak index */
extern uint16_T VPeakIon;              /* '<S3>/Merge4' */

/* Ion peak for circuit selection */
extern uint16_T VtIntIonOffset[8];     /* '<S3>/Merge' */

/* Offset value applied to IonBuffer for IntIon calculation */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S18>/Constant' : Unused code path elimination
 * Block '<S18>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Data Type Propagation' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Propagation' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Propagation' : Unused code path elimination
 * Block '<S13>/Conversion' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion1' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonIntMgm'
 * '<S1>'   : 'IonIntMgm/EOA'
 * '<S2>'   : 'IonIntMgm/PowerOn'
 * '<S3>'   : 'IonIntMgm/Subsystem'
 * '<S4>'   : 'IonIntMgm/EOA/Ch_Th_Integral'
 * '<S5>'   : 'IonIntMgm/EOA/Circuit_Selection'
 * '<S6>'   : 'IonIntMgm/EOA/ErrorStatus_Mgm'
 * '<S7>'   : 'IonIntMgm/EOA/Ion_Integral'
 * '<S8>'   : 'IonIntMgm/EOA/Ion_Offset'
 * '<S9>'   : 'IonIntMgm/EOA/Ch_Th_Integral/Chemical_Post_Elaboration'
 * '<S10>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Chemical_Thermal_Mgm'
 * '<S11>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Phase_Integral_Zone'
 * '<S12>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration'
 * '<S13>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Chemical_Post_Elaboration/Chemical_FrontFlame_Speed'
 * '<S14>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Chemical_Post_Elaboration/Chemical_Integral'
 * '<S15>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Chemical_Post_Elaboration/Chemical_Peak'
 * '<S16>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Chemical_Post_Elaboration/Chemical_FrontFlame_Speed/IfActionSubsystem'
 * '<S17>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Chemical_Post_Elaboration/Chemical_FrontFlame_Speed/IfActionSubsystem1'
 * '<S18>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Phase_Integral_Zone/LookUp_IR_U8'
 * '<S19>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration/Thermal_FrontFlame_Speed'
 * '<S20>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration/Thermal_Integral'
 * '<S21>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration/Thermal_Peak'
 * '<S22>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration/Thermal_Peak_Angle'
 * '<S23>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration/Thermal_FrontFlame_Speed/IfActionSubsystem'
 * '<S24>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration/Thermal_FrontFlame_Speed/IfActionSubsystem1'
 * '<S25>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration/Thermal_Peak_Angle/ArrangeOutputLSB'
 * '<S26>'  : 'IonIntMgm/EOA/Ch_Th_Integral/Thermal_Post_Elaboration/Thermal_Peak_Angle/SaturationDynamic'
 * '<S27>'  : 'IonIntMgm/EOA/Circuit_Selection/Circuit_Selection'
 * '<S28>'  : 'IonIntMgm/EOA/Ion_Integral/Band_Integral'
 * '<S29>'  : 'IonIntMgm/EOA/Ion_Integral/Front_Flame_Speed'
 * '<S30>'  : 'IonIntMgm/EOA/Ion_Integral/Ion_Integral'
 * '<S31>'  : 'IonIntMgm/EOA/Ion_Integral/Ion_Integral/FOF_Reset_S16_FXP'
 * '<S32>'  : 'IonIntMgm/EOA/Ion_Integral/Ion_Integral/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S33>'  : 'IonIntMgm/EOA/Ion_Offset/Integral_Offset'
 */

/*-
 * Requirements for '<Root>': IonIntMgm
 *
 * Inherited requirements for '<Root>/PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_1628: Software shall initialize each output produced for the evaluation ... (ECU_SW_Requirements#2791)

 */
#endif                                 /* RTW_HEADER_IonIntMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/