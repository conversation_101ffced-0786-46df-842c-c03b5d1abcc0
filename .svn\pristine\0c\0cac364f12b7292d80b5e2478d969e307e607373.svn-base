/******************************************************************************************************************************/
/* $HeadURL::                                                                                                             $   */
/* $ Description:                                                                                                             */
/* $Revision::        $                                                                                                       */
/* $Date::                                                $                                                                   */
/* $Author::                         $                                                                                        */
/******************************************************************************************************************************/

#ifndef _CCP_CFG_H_
#define _CCP_CFG_H_
/* Precompiler defines for ccp module */

#pragma	ghs startnomisra

#ifndef GLOBAL_CCPPAR
#define GLOBAL_CCPPAR extern
#endif


/*----------------------------------------------------------------------------*/
/* Platform independant types */


#define CCP_MOTOROLA
#define CCP_BYTE    uint8_T          //unsigned char
#define CCP_WORD    uint16_T         //unsigned short
#define CCP_DWORD   uint32_T         //unsigned long
#define CCP_BYTEPTR uint8_T*         //unsigned char*
#define CCP_ROM
#define CCP_RAM
#define CCP_DAQBYTEPTR CCP_BYTEPTR
#define CCP_MTABYTEPTR CCP_BYTEPTR


/*----------------------------------------------------------------------------*/
/* Disable/Enable Interrupts */

/* Has to be defined if ccpSendCallBack may interrupt ccpDaq */
//#define CCP_DISABLE_INTERRUPT
//#define CCP_ENABLE_INTERRUPT

/*----------------------------------------------------------------------------*/

#define CCP_STATION_ID "CCPtest"
#define CCP_STATION_ADDR 0x3900U

#define CCP_USE_TTCAN 1u
#define CCP_USE_MCAN  2u

#define CCP_USE_ENGCAN CCP_USE_TTCAN

//#define ID_CCP_DTO 0x200  PAY ATTENTION!! This symbols are defined into... 
//#define ID_CCP_CRO 0x201  ...CAN.cfg module

#define CCP_DAQ
#ifdef CCP_DAQ
#define CCP_SEND_QUEUE
#endif

#define CCP_MAX_ODT 25U                 
#define CCP_MAX_DAQ 3U

#ifdef CCP_SEND_QUEUE
/* Checks */
#ifndef CCP_SEND_QUEUE_SIZE
  #define CCP_QUEUE_SIZE_MULTIPLIER     2u
  #define CCP_SEND_QUEUE_SIZE           (CCP_MAX_ODT*CCP_MAX_DAQ*CCP_QUEUE_SIZE_MULTIPLIER)
#endif

#ifdef CCP_SEND_SINGLE
  #error Do not use CCP_SEND_SINGLE together with CCP_SEND_QUEUE
#endif

#if CCP_MAX_ODT > CCP_SEND_QUEUE_SIZE
  #error CCP_SEND_QUEUE_SIZE is too small
#endif
#endif

#define CCP_ODT_ENTRY_SIZE

#define CCP_SET_SESSION_STATUS

#define CCP_CHECKSUM
#define CCP_CHECKSUM_TYPE CCP_DWORD

#define CCP_CALPAGE
#ifndef _BUILD_CCP_PROD_
#define CCP_PROGRAM
#else
#undef CCP_PROGRAM
#endif

#define CCPCNTNOPTXDELAY 5u

#pragma	ghs endnomisra

#endif // _CCP_CFG_H_
