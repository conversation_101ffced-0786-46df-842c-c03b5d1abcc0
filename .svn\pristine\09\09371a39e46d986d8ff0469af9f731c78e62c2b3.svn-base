/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonDwellMgm.c
 **  File Creation Date: 16-May-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonDwellMgm
 **  Model Description:  This model calculates the integral of acquired ion signal during dwell-time and it is triggered at End Of Acquisition event.
 **  Model Version:      1.1128
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Mon May 16 17:15:02 2022
 **
 **  Last Saved Modification:  RoccaG - Mon May 16 17:00:39 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonDwellMgm_out.h"
#include "IonDwellMgm_private.h"
#include "asr_s32.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKRPMION_dim                   11U                       /* Referenced by: '<S8>/Constant1' */

/* BKRPMION breakpoint dimension. */
#define D1_HP_P1                       36260U                    /* Referenced by: '<S24>/Constant20' */

/* Butterworth filter coefficient. */
#define D1_HP_P2                       -32764                    /* Referenced by: '<S24>/Constant21' */

/* Butterworth filter coefficient. */
#define D1_LP_P1                       -24471                    /* Referenced by: '<S25>/Constant3' */

/* Butterworth filter coefficient. */
#define D1_LP_P2                       33345U                    /* Referenced by: '<S25>/Constant1' */

/* Butterworth filter coefficient. */
#define D1_LP_P3                       -31763                    /* Referenced by: '<S25>/Constant2' */

/* Butterworth filter coefficient. */
#define D2_HP_P1                       35801U                    /* Referenced by: '<S24>/Constant22' */

/* Butterworth filter coefficient. */
#define D2_HP_P2                       -18093                    /* Referenced by: '<S24>/Constant15' */

/* Butterworth filter coefficient. */
#define D2_HP_P3                       65526U                    /* Referenced by: '<S24>/Constant16' */

/* Butterworth filter coefficient. */
#define D2_LP_P1                       44108U                    /* Referenced by: '<S25>/Constant10' */

/* Butterworth filter coefficient. */
#define D2_LP_P2                       -22001                    /* Referenced by: '<S25>/Constant8' */

/* Butterworth filter coefficient. */
#define D2_LP_P3                       54980U                    /* Referenced by: '<S25>/Constant9' */

/* Butterworth filter coefficient. */
#define FREQ_SCALING                   15625U                    /* Referenced by:
                                                                  * '<S24>/Constant9'
                                                                  * '<S25>/Constant17'
                                                                  */

/* Conversion factor to normalize cut-off frequencies */
#define ID_VER_IONDWELLMGM_DEF         11128U                    /* Referenced by: '<S2>/Constant12' */

/* Model Version. */
#define LAST_SAMPLE_ID                 799U                      /* Referenced by: '<S4>/Dwell_Mgm' */

/* Maximum index on samples buffer (according to zero based notation). */
#define MAXFCUTHIGHPASS                1000U                     /* Referenced by: '<S24>/Constant24' */

/* Maximum cut frequency value for high pass filter. */
#define MAXFCUTLOWPASS                 12500U                    /* Referenced by: '<S25>/Constant13' */

/* Maximum cut frequency value for low pass filter. */
#define MAX_BUFFFILT_SIZE              250U                      /* Referenced by: '<S4>/Dwell_Mgm' */

/* Max size for filter. */
#define MAX_DWELLINT_VALUE             1048576U                  /* Referenced by: '<S5>/Constant6' */

/* Maximum value for integral on dwell phase. */
#define MAX_IONBUFFER_VALUE            65520                     /* Referenced by:
                                                                  * '<S4>/Dwell_Mgm'
                                                                  * '<S12>/Constant3'
                                                                  * '<S13>/Constant2'
                                                                  */

/* Maximum allowed value for filtered buffer on ion signal */
#define MAX_UINT16                     65535U                    /* Referenced by: '<S5>/Dwell_Integral' */

/* Max value for type uint16. */
#define MAX_UINT8                      255U                      /* Referenced by: '<S4>/Dwell_Mgm' */

/* Max value for type uint8. */
#define MINFCUTHIGHPASS                200U                      /* Referenced by: '<S24>/Constant10' */

/* Minimum cut frequency value for high pass filter. */
#define MINFCUTLOWPASS                 11000U                    /* Referenced by: '<S25>/Constant11' */

/* Minimum cut frequency value for low pass filter. */
#define N0_HP_P1                       -17123                    /* Referenced by: '<S24>/Constant18' */

/* Butterworth filter coefficient. */
#define N0_HP_P2                       65449U                    /* Referenced by: '<S24>/Constant19' */

/* Butterworth filter coefficient. */
#define N0_LP_P1                       37990U                    /* Referenced by: '<S25>/Constant22' */

/* Butterworth filter coefficient. */
#define N0_LP_P2                       45378U                    /* Referenced by: '<S25>/Constant15' */

/* Butterworth filter coefficient. */
#define N0_LP_P3                       -26143                    /* Referenced by: '<S25>/Constant16' */

/* Butterworth filter coefficient. */
#define NORM_NOT_OFFSET                1U                        /* Referenced by: '<S5>/Dwell_Integral' */

/* Normalization mode. */
#define NORM_OFFSET                    3U                        /* Referenced by: '<S5>/Dwell_Integral' */

/* Normalization mode. */
#define NOT_NORM_NOT_OFFSET            0U                        /* Referenced by: '<S5>/Dwell_Integral' */

/* Normalization mode. */
#define NOT_NORM_OFFSET                2U                        /* Referenced by: '<S5>/Dwell_Integral' */

/* Normalization mode. */
#define TWO                            2U                        /* Referenced by:
                                                                  * '<S8>/Constant2'
                                                                  * '<S12>/Constant'
                                                                  * '<S13>/Constant'
                                                                  */

/* Two */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONDWELLMGM_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

#if (MAX_SAMPLE != 800)
#error This code was generated with different number of ion sample!
#endif

#if (ION_DT_MIN != 8)
#error This code was generated with different minimum value for ion sampling time!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BUTTERFILTMAXRPM = 10000U;/* Referenced by: '<S4>/Constant2' */

/* Max Rpm to run Butterworth filter (= 0 to disable filter) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T DWELLINTCALCTYPE = 3U;/* Referenced by: '<S5>/Constant5' */

/* DwellInt calc type 0: IonSignalAcc (not normalized) - 1: as 0 but normalized - 2: IntIonOffset applied to IonSignalAcc (not norm) - 3: as 2 but normalized */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T FCUTHPFILT = 250U;/* Referenced by: '<S24>/Constant23' */

/* Cutoff frequency for HighPass filter */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T FCUTLPFILT = 11500U;/* Referenced by: '<S25>/Constant12' */

/* Cutoff frequency for LowPass filter */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T IONBUFFERTESTFILT = 0U;/* Referenced by: '<S4>/Constant3' */

/* Force IONBUFFERTESTFILT */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T IONBUFFERTESTFILTDIM = 200U;/* Referenced by: '<S4>/Constant5' */

/* Test buffer DwellInt length */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXDSPARKTIMEDWELL = 120U;/* Referenced by: '<S4>/Constant' */

/* Max value for DeltaSparkTime to include it in DwellInt calc */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MINIONDWELL = 3277U;/* Referenced by: '<S8>/Constant3' */

/* Ion signal dead-band during dwell [mV] */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T MINIONDWELLOFFSETEN = 0U;/* Referenced by: '<S4>/Constant7' */

/* 1 use MINIONDELL as an OFFSET saturated to zero,0 use MINIONDWELL as a dead band */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T OLSECINTTHR = 3277U;/* Referenced by: '<S8>/Constant4' */

/* Threshold on VtOLSecInt to set flag FlgOLSpark and to detect Secondary winding open-load detection on switching off condition */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T OLSECINTTIME = 200U;/* Referenced by: '<S8>/Constant10' */

/* Time for the integral of ion signal in order to detect Secondary winding open-load detection on switching off condition */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T STARTIONDWELL = 350U;/* Referenced by: '<S4>/Constant1' */

/* Time after ign-on for DwellInt calculation start */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T VTIONBUFFERTESTFILT[200] = { 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U, 160U,
  160U, 160U, 160U, 160U } ;           /* Referenced by: '<S4>/Constant6' */

/* VTIONBUFFERTESTFILT */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T VTSTOPIONDWELL[12] = { 30, -30, -30, -16,
  -1, 13, 13, 13, 13, 13, 13, 13 } ;   /* Referenced by: '<S8>/Constant' */

/* Time before ign-off for DwellInt calculation stop (if negative, AFTER ign-off) */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T DwellInt[8];                  /* '<S3>/Merge1' */

/* Ion integral during main dwell-time */
uint16_T DwellIntCyl;                  /* '<S3>/Merge' */

/* DwellIntCyl */
uint8_T FlgOLSpark[8];                 /* '<S3>/Merge7' */

/* Secondary winding open-load detection on switching off condition */
uint32_T VtOLSecInt[8];                /* '<S3>/Merge6' */

/* Ion integral on first samples, used to detect Secondary winding open-load detection on switching off condition */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT int16_T D1HP;        /* '<S24>/Conversion3' */

/* D1 coefficient for HighPass Butterworth filter */
STATIC_TEST_POINT int16_T D1LP;        /* '<S25>/Conversion13' */

/* D1 coefficient for LowPass Butterworth filter */
STATIC_TEST_POINT int16_T D2HP;        /* '<S24>/Conversion11' */

/* D2 coefficient for HighPass Butterworth filter */
STATIC_TEST_POINT int16_T D2LP;        /* '<S25>/Conversion14' */

/* D2 coefficient for LowPass Butterworth filter */
STATIC_TEST_POINT uint16_T FreqHPNorm; /* '<S24>/Conversion1' */

/* Normalized CutOff frequency for HighPass filter */
STATIC_TEST_POINT uint16_T FreqLPNorm; /* '<S25>/Conversion3' */

/* Normalized CutOff frequency for LowPass filter */
STATIC_TEST_POINT uint32_T IdVer_IonDwellMgm;/* '<S2>/Constant12' */

/* Model Version */
STATIC_TEST_POINT uint16_T IonBufferFilt[250];/* '<S3>/Merge5' */

/* Ion current Buffer Filtered */
STATIC_TEST_POINT uint32_T IonSignalDwellAcc;/* '<S3>/Merge2' */

/* Sum of IonBuffer elements during coil charge */
STATIC_TEST_POINT int16_T N0HP;        /* '<S24>/Conversion2' */

/* N0 coefficient for HighPass Butterworth filter */
STATIC_TEST_POINT int16_T N0LP;        /* '<S25>/Conversion12' */

/* N0 coefficient for LowPass Butterworth filter */
STATIC_TEST_POINT uint8_T StartIonDwellCyl[8];/* '<S3>/Merge3' */

/* DwellInt calculation start sample */
STATIC_TEST_POINT uint8_T StopIonDwellCyl[8];/* '<S3>/Merge4' */

/* DwellInt calculation stop sample */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<S4>/StopIonDwell'
 * Block description for: '<S4>/StopIonDwell'
 *   In function of engine speed and time duration of ion acquisition, this block calculates the  stop index on ion signal buffer for coil charge phase.
 *   Moreover, it calculates also the number for samples used to calculate the ion integral in order to detect secondary winding open-load fault on switching-off of injection.
 */
void IonDwellMgm_StopIonDwell(uint16_T rtu_IonMgmBkRpmIonIndex, uint16_T
  rtu_IonMgmBkRpmIonRatio, uint16_T rtu_IonGainEOA, uint8_T rtu_IonDTEOA,
  int16_T *rty_tmpStopIonDwell, uint8_T *rty_olSecIntSamp, uint32_T
  *rty_OLSecIntThruA, uint32_T *rty_minIonDwelluA)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_IR_S8;
  uint32_T rtb_Conversion2_m;
  uint8_T u0;

  /* S-Function (LookUp_IR_S8): '<S26>/LookUp_IR_S8' incorporates:
   *  Constant: '<S8>/Constant'
   *  Constant: '<S8>/Constant1'
   */
  LookUp_IR_S8( &rtb_LookUp_IR_S8, &VTSTOPIONDWELL[0], rtu_IonMgmBkRpmIonIndex,
               rtu_IonMgmBkRpmIonRatio, ((uint8_T)BKRPMION_dim));

  /* Product: '<S8>/Divide' incorporates:
   *  DataTypeConversion: '<S8>/Conversion'
   *
   * Block requirements for '<S8>/Divide':
   *  1. EISB_FCA6CYL_SW_REQ_1596: Software shall estimate, cylinder by cylinder, the end index of dw... (ECU_SW_Requirements#2833)
   */
  *rty_tmpStopIonDwell = (int16_T)(asr_s32((int32_T)rtb_LookUp_IR_S8, 6U) /
    ((int32_T)rtu_IonDTEOA));

  /* DataTypeConversion: '<S8>/Conversion1' incorporates:
   *  Constant: '<S8>/Constant10'
   *  Product: '<S8>/Divide1'
   */
  u0 = (uint8_T)(((uint32_T)OLSECINTTIME) / ((uint32_T)rtu_IonDTEOA));

  /* MinMax: '<S8>/MinMax' incorporates:
   *  Constant: '<S8>/Constant2'
   *
   * Block requirements for '<S8>/MinMax':
   *  1. EISB_FCA6CYL_SW_REQ_2155: Software shall calculate the sum of first ion samples, defined by ... (ECU_SW_Requirements#9448)
   */
  if (u0 > ((uint8_T)TWO)) {
    *rty_olSecIntSamp = u0;
  } else {
    *rty_olSecIntSamp = ((uint8_T)TWO);
  }

  /* End of MinMax: '<S8>/MinMax' */

  /* DataTypeConversion: '<S8>/Conversion2' */
  rtb_Conversion2_m = ((((uint32_T)rtu_IonGainEOA) * 625U) >> ((uint32_T)7));

  /* DataTypeConversion: '<S8>/Conversion3' incorporates:
   *  Constant: '<S8>/Constant3'
   *  Product: '<S8>/Product'
   */
  *rty_minIonDwelluA = ((rtb_Conversion2_m * ((uint32_T)MINIONDWELL)) >>
                        ((uint32_T)12));

  /* DataTypeConversion: '<S8>/Conversion4' incorporates:
   *  Constant: '<S8>/Constant4'
   *  Product: '<S8>/Product1'
   */
  *rty_OLSecIntThruA = ((rtb_Conversion2_m * ((uint32_T)OLSECINTTHR)) >>
                        ((uint32_T)12));
}

/* Model step function */
void IonDwellMgm_EOA(void)
{
  uint16_T dwellBuffSize;
  uint8_T b_index;
  uint16_T locStartIonDwell;
  uint16_T locStopIonDwell;
  uint16_T index_16;
  int32_T rtb_oldIn1N0;
  int32_T rtb_oldOut1;
  int32_T rtb_oldIn1N0_k;
  int32_T rtb_oldOut1_a;
  int32_T rtb_Add3;
  int32_T rtb_Switch2;
  int32_T rtb_Divide_p;
  int32_T rtb_Divide_m;
  uint8_T MinMax;
  int16_T Divide;
  uint32_T Conversion4;
  uint32_T Conversion3;
  int32_T oldIn1N0_e;
  int32_T oldIn2N0_o;
  int32_T oldOut2_l;
  int32_T oldIn2N0;
  int32_T oldOut2;
  uint32_T u0;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonDwellMgm_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  This block implements EOA runnable. The aim of IonDwellMgm is to
   *  calculate ion integral during main dwell-time.
   */
  /* Gateway: EOA/Dwell_Integral/Dwell_Mgm */
  /* During: EOA/Dwell_Integral/Dwell_Mgm */
  /* This chart performs sum of ion signal buffer along coil charge phase.
     As first step it calculates coil charge phase (start and stop index on ion buffer signal).
     After it calculates the sum on the above mentioned interval.
     Before applying sum, the chart may call a Butterworth filter on ion signal if engine speed is lower than a tunable threshold (BUTTERFILTMAXRPM).
     The chart provides also a test modality for filter and integration, by means of a tunable array (VTIONBUFFERTESTFILT).
   */
  /* Entry Internal: EOA/Dwell_Integral/Dwell_Mgm */
  /* Transition: '<S7>:4' */
  /*  Default Values  */
  IonSignalDwellAcc = 0U;
  locStartIonDwell = 0U;
  locStopIonDwell = 0U;

  /* SignalConversion generated from: '<S1>/IonBufferFilt' */
  memset((&(IonBufferFilt[0])), 0, 250U * (sizeof(uint16_T)));

  /* Chart: '<S4>/Dwell_Mgm' incorporates:
   *  SubSystem: '<S4>/StopIonDwell'
   *
   * Block description for '<S4>/Dwell_Mgm':
   *  This chart performs sum of ion signal buffer along coil charge phase.
   *  As first step it calculates coil charge phase (start and stop index on ion buffer signal).
   *  After it calculates the sum on the above mentioned interval.
   *  Before applying sum, the chart may call a Butterworth filter on ion signal if engine speed is lower than a tunable threshold (BUTTERFILTMAXRPM).
   *  The chart provides also a test modality for filter and integration, by means of a tunable array (VTIONBUFFERTESTFILT).

   *
   * Block description for '<S4>/StopIonDwell':
   *  In function of engine speed and time duration of ion acquisition, this block calculates the  stop index on ion signal buffer for coil charge phase.
   *  Moreover, it calculates also the number for samples used to calculate the ion integral in order to detect secondary winding open-load fault on switching-off of injection.
   *
   * Block requirements for '<S4>/StopIonDwell':
   *  1. EISB_FCA6CYL_SW_REQ_1596: Software shall estimate, cylinder by cylinder, the end index of dw... (ECU_SW_Requirements#2833)
   */
  /* Event: '<S7>:8' */
  IonDwellMgm_StopIonDwell(IonMgmBkRpmIonIndex, IonMgmBkRpmIonRatio, IonGainEOA,
    IonDTEOA, &Divide, &MinMax, &Conversion4, &Conversion3);

  /* Transition: '<S7>:339' */
  /* Transition: '<S7>:346' */
  b_index = 0U;

  /* Inport: '<Root>/IonAbsTdcEOA' */
  VtOLSecInt[(IonAbsTdcEOA)] = 0U;
  while (b_index < MinMax) {
    /* Inport: '<Root>/IonAbsTdcEOA' incorporates:
     *  Inport: '<Root>/IonBuffer'
     */
    /* Transition: '<S7>:348' */
    /* Transition: '<S7>:350':
     *  1. EISB_FCA6CYL_SW_REQ_2155: Software shall calculate the sum of first ion samples, defined by ... (ECU_SW_Requirements#9448)
     */
    VtOLSecInt[(IonAbsTdcEOA)] = VtOLSecInt[(IonAbsTdcEOA)] + IonBuffer[(b_index)];

    /* Transition: '<S7>:351' */
    b_index = (uint8_T)((int32_T)(((int32_T)b_index) + 1));
  }

  /* Inport: '<Root>/IonAbsTdcEOA' */
  /* Transition: '<S7>:353' */
  if (VtOLSecInt[(IonAbsTdcEOA)] < Conversion4) {
    /* SignalConversion generated from: '<S1>/FlgOLSpark' */
    /* Transition: '<S7>:355':
     *  1. EISB_FCA6CYL_SW_REQ_2156: Software shall set the flag FlgOLSpark (secondary winding open loa... (ECU_SW_Requirements#9449)
     */
    /* Transition: '<S7>:357' */
    FlgOLSpark[(IonAbsTdcEOA)] = 1U;

    /* Transition: '<S7>:360' */
  } else {
    /* SignalConversion generated from: '<S1>/FlgOLSpark' */
    /* Transition: '<S7>:359' */
    FlgOLSpark[(IonAbsTdcEOA)] = 0U;
  }

  /* Inport: '<Root>/DwellSampCyl' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  /* Transition: '<S7>:361' */
  if (((int32_T)DwellSampCyl[(IonAbsTdcEOA)]) != 0) {
    /* Transition: '<S7>:7' */
    /* Transition: '<S7>:13' */
    if (((int32_T)DwellSampCyl[(IonAbsTdcEOA)]) > ((int32_T)Divide)) {
      /* Transition: '<S7>:25' */
      /* Transition: '<S7>:37':
       *  1. EISB_FCA6CYL_SW_REQ_1596: Software shall estimate, cylinder by cylinder, the end index of dw... (ECU_SW_Requirements#2833)
       */
      /*  DwellSampCyl is equal to EffDwellTime saturated at MAX_UINT_8 (255)
         so StopIOnDwell will be equal to EffDwellTime - tmpStopIonDwell  */
      locStopIonDwell = (uint16_T)((int32_T)(((int32_T)DwellSampCyl
        [(IonAbsTdcEOA)]) - ((int32_T)Divide)));

      /* Inport: '<Root>/DeltaSparkTime' incorporates:
       *  Constant: '<S4>/Constant'
       */
      if ((DeltaSparkTime[(IonAbsTdcEOA)] < ((uint32_T)MAXDSPARKTIMEDWELL)) &&
          (Divide >= 0)) {
        /* Inport: '<Root>/IonDTEOA' */
        /* Transition: '<S7>:39' */
        /* Transition: '<S7>:43' */
        /*  DeltaSparkTime is a stub setted always to 0 in production code
           so  StopIOnDwell will be equal to EffDwellTime - tmpStopIonDwell  */
        locStopIonDwell = (uint16_T)((DeltaSparkTime[(IonAbsTdcEOA)] /
          ((uint32_T)IonDTEOA)) + ((uint32_T)locStopIonDwell));

        /* Transition: '<S7>:45' */
      } else {
        /* Transition: '<S7>:46' */
      }

      /* End of Inport: '<Root>/DeltaSparkTime' */

      /* Constant: '<S4>/Constant1' incorporates:
       *  Inport: '<Root>/IonDTEOA'
       */
      /* Transition: '<S7>:49':
       *  1. EISB_FCA6CYL_SW_REQ_1595: Software shall estimate, cylinder by cylinder, the start index of ... (ECU_SW_Requirements#2832)
       */
      locStartIonDwell = (uint16_T)(((uint32_T)STARTIONDWELL) / ((uint32_T)
        IonDTEOA));
      if (locStopIonDwell >= ((uint16_T)LAST_SAMPLE_ID)) {
        locStopIonDwell = ((uint16_T)LAST_SAMPLE_ID);
      }

      if (locStopIonDwell <= locStartIonDwell) {
        /* Transition: '<S7>:62' */
        /* Transition: '<S7>:66' */
        locStopIonDwell = 0U;

        /* Transition: '<S7>:67' */
      } else {
        /* Transition: '<S7>:64' */
      }

      /* Transition: '<S7>:69' */
    } else {
      /* Transition: '<S7>:70' */
    }

    /* Transition: '<S7>:22' */
    if (((int32_T)locStopIonDwell) > 0) {
      /* Inport: '<Root>/Rpm' incorporates:
       *  Constant: '<S4>/Constant2'
       */
      /* Transition: '<S7>:77' */
      /* Transition: '<S7>:79' */
      if (Rpm <= BUTTERFILTMAXRPM) {
        /* Transition: '<S7>:81' */
        /* Transition: '<S7>:86' */
        dwellBuffSize = (uint16_T)(locStopIonDwell - locStartIonDwell);
        if (dwellBuffSize >= ((uint16_T)MAX_BUFFFILT_SIZE)) {
          dwellBuffSize = ((uint16_T)MAX_BUFFFILT_SIZE);
        }

        /* Outputs for Function Call SubSystem: '<S6>/BandPassInit'
         *
         * Block description for '<S6>/BandPassInit':
         *  The aim of this block is to calculate Butterworth filter coefficients. It shall called once before starting buffer filter.
         *  Filter coefficients depend on the duration of ion signal acquisition (and so to sampling rate).
         */
        /* MinMax: '<S24>/MinMax' incorporates:
         *  Constant: '<S24>/Constant23'
         *  Constant: '<S24>/Constant24'
         */
        /* Event: '<S7>:114' */
        if (FCUTHPFILT < ((uint16_T)MAXFCUTHIGHPASS)) {
          index_16 = FCUTHPFILT;
        } else {
          index_16 = ((uint16_T)MAXFCUTHIGHPASS);
        }

        /* End of MinMax: '<S24>/MinMax' */

        /* MinMax: '<S24>/MinMax1' incorporates:
         *  Constant: '<S24>/Constant10'
         */
        if (index_16 <= ((uint16_T)MINFCUTHIGHPASS)) {
          index_16 = ((uint16_T)MINFCUTHIGHPASS);
        }

        /* End of MinMax: '<S24>/MinMax1' */

        /* DataTypeConversion: '<S24>/Conversion1' incorporates:
         *  Constant: '<S24>/Constant9'
         *  DataTypeConversion: '<S24>/Conversion'
         *  Inport: '<Root>/IonDTEOA'
         *  Product: '<S24>/Divide'
         *  Product: '<S24>/Product'
         *
         * Block requirements for '<S24>/Divide':
         *  1. EISB_FCA6CYL_SW_REQ_1632: Software shall calculate the HIGH pass cut frequency used by Butte... (ECU_SW_Requirements#2837)
         */
        FreqHPNorm = (uint16_T)(((((uint32_T)index_16) * ((uint32_T)IonDTEOA)) <<
          ((uint32_T)10)) / ((uint32_T)((uint16_T)FREQ_SCALING)));

        /* DataTypeConversion: '<S24>/Conversion2' incorporates:
         *  Constant: '<S24>/Constant18'
         *  Constant: '<S24>/Constant19'
         *  Product: '<S24>/Product2'
         *  Sum: '<S24>/Add'
         */
        N0HP = (int16_T)asr_s32(asr_s32(((int32_T)FreqHPNorm) * ((int32_T)
          ((int16_T)N0_HP_P1)), 12U) + ((int32_T)((uint16_T)N0_HP_P2)), 3U);

        /* DataTypeConversion: '<S24>/Conversion3' incorporates:
         *  Constant: '<S24>/Constant20'
         *  Constant: '<S24>/Constant21'
         *  Product: '<S24>/Product3'
         *  Sum: '<S24>/Add1'
         */
        D1HP = (int16_T)asr_s32(((int32_T)((uint32_T)((((uint32_T)FreqHPNorm) *
          ((uint32_T)((uint16_T)D1_HP_P1))) >> ((uint32_T)14)))) + ((int32_T)
          ((int16_T)D1_HP_P2)), 1U);

        /* DataTypeConversion: '<S24>/Conversion11' incorporates:
         *  Constant: '<S24>/Constant15'
         *  Constant: '<S24>/Constant16'
         *  Constant: '<S24>/Constant22'
         *  Product: '<S24>/Product4'
         *  Product: '<S24>/Product5'
         *  Sum: '<S24>/Add2'
         *  Sum: '<S24>/Add3'
         */
        D2HP = (int16_T)asr_s32(asr_s32((((int32_T)((uint32_T)((((uint32_T)
          FreqHPNorm) * ((uint32_T)((uint16_T)D2_HP_P1))) >> ((uint32_T)15)))) +
          ((int32_T)((int16_T)D2_HP_P2))) * ((int32_T)FreqHPNorm), 11U) +
          ((int32_T)((uint16_T)D2_HP_P3)), 3U);

        /* SignalConversion generated from: '<S24>/Constant' incorporates:
         *  Constant: '<S24>/Constant'
         */
        oldIn1N0_e = 0;

        /* SignalConversion generated from: '<S24>/Constant1' incorporates:
         *  Constant: '<S24>/Constant1'
         */
        oldIn2N0_o = 0;

        /* SignalConversion generated from: '<S24>/Constant2' incorporates:
         *  Constant: '<S24>/Constant2'
         */
        rtb_Divide_p = 0;

        /* SignalConversion generated from: '<S24>/Constant3' incorporates:
         *  Constant: '<S24>/Constant3'
         */
        oldOut2_l = 0;

        /* SignalConversion generated from: '<S25>/Constant4' incorporates:
         *  Constant: '<S25>/Constant4'
         */
        rtb_Add3 = 0;

        /* SignalConversion generated from: '<S25>/Constant5' incorporates:
         *  Constant: '<S25>/Constant5'
         */
        oldIn2N0 = 0;

        /* SignalConversion generated from: '<S25>/Constant6' incorporates:
         *  Constant: '<S25>/Constant6'
         */
        rtb_Divide_m = 0;

        /* SignalConversion generated from: '<S25>/Constant7' incorporates:
         *  Constant: '<S25>/Constant7'
         */
        oldOut2 = 0;

        /* MinMax: '<S25>/MinMax2' incorporates:
         *  Constant: '<S25>/Constant12'
         *  Constant: '<S25>/Constant13'
         */
        if (FCUTLPFILT < ((uint16_T)MAXFCUTLOWPASS)) {
          index_16 = FCUTLPFILT;
        } else {
          index_16 = ((uint16_T)MAXFCUTLOWPASS);
        }

        /* End of MinMax: '<S25>/MinMax2' */

        /* MinMax: '<S25>/MinMax3' incorporates:
         *  Constant: '<S25>/Constant11'
         */
        if (index_16 <= ((uint16_T)MINFCUTLOWPASS)) {
          index_16 = ((uint16_T)MINFCUTLOWPASS);
        }

        /* End of MinMax: '<S25>/MinMax3' */

        /* DataTypeConversion: '<S25>/Conversion3' incorporates:
         *  Constant: '<S25>/Constant17'
         *  DataTypeConversion: '<S25>/Conversion2'
         *  Inport: '<Root>/IonDTEOA'
         *  Product: '<S25>/Divide1'
         *  Product: '<S25>/Product1'
         *
         * Block requirements for '<S25>/Divide1':
         *  1. EISB_FCA6CYL_SW_REQ_1631: Software shall calculate the low pass cut frequency used by Butter... (ECU_SW_Requirements#2836)
         */
        FreqLPNorm = (uint16_T)(((((uint32_T)IonDTEOA) * ((uint32_T)index_16)) <<
          ((uint32_T)10)) / ((uint32_T)((uint16_T)FREQ_SCALING)));

        /* DataTypeConversion: '<S25>/Conversion13' incorporates:
         *  Constant: '<S25>/Constant1'
         *  Constant: '<S25>/Constant2'
         *  Constant: '<S25>/Constant3'
         *  DataTypeConversion: '<S25>/Conversion15'
         *  Product: '<S25>/Product2'
         *  Product: '<S25>/Product3'
         *  Sum: '<S25>/Add1'
         *  Sum: '<S25>/Add4'
         */
        D1LP = (int16_T)asr_s32((asr_s32(asr_s32(asr_s32(((int32_T)FreqLPNorm) *
          ((int32_T)((int16_T)D1_LP_P1)), 18U) + ((int32_T)((uint16_T)D1_LP_P2)),
          1U) * ((int32_T)FreqLPNorm), 13U) * 64) + (((int32_T)((int16_T)
          D1_LP_P3)) * 64), 7U);

        /* DataTypeConversion: '<S25>/Conversion12' incorporates:
         *  Constant: '<S25>/Constant15'
         *  Constant: '<S25>/Constant16'
         *  Constant: '<S25>/Constant22'
         *  DataTypeConversion: '<S25>/Conversion'
         *  Product: '<S25>/Product4'
         *  Product: '<S25>/Product5'
         *  Sum: '<S25>/Add2'
         *  Sum: '<S25>/Add3'
         */
        N0LP = (int16_T)asr_s32(((int32_T)((uint32_T)((((uint32_T)asr_s32
          (((int32_T)((uint32_T)((((uint32_T)FreqLPNorm) * ((uint32_T)((uint16_T)
          N0_LP_P1))) >> ((uint32_T)14)))) + ((int32_T)((uint16_T)N0_LP_P2)), 2U))
          * ((uint32_T)FreqLPNorm)) >> ((uint32_T)10)))) + ((int32_T)((int16_T)
          N0_LP_P3)), 7U);

        /* DataTypeConversion: '<S25>/Conversion14' incorporates:
         *  Constant: '<S25>/Constant10'
         *  Constant: '<S25>/Constant8'
         *  Constant: '<S25>/Constant9'
         *  DataTypeConversion: '<S25>/Conversion17'
         *  Product: '<S25>/Product6'
         *  Product: '<S25>/Product7'
         *  Sum: '<S25>/Add5'
         *  Sum: '<S25>/Add6'
         */
        D2LP = (int16_T)asr_s32(asr_s32((((int32_T)((uint32_T)((((uint32_T)
          FreqLPNorm) * ((uint32_T)((uint16_T)D2_LP_P1))) >> ((uint32_T)16)))) +
          ((int32_T)((int16_T)D2_LP_P2))) * ((int32_T)FreqLPNorm), 12U) +
          ((int32_T)((uint16_T)D2_LP_P3)), 3U);

        /* End of Outputs for SubSystem: '<S6>/BandPassInit' */

        /* Constant: '<S4>/Constant3' */
        if (((int32_T)IONBUFFERTESTFILT) == 0) {
          /* Transition: '<S7>:95' */
          /* Transition: '<S7>:139' */
          /* Transition: '<S7>:100' */
          /*  Butterworth band-pass filter  */
          for (index_16 = 0U; index_16 < dwellBuffSize; index_16 = (uint16_T)
               ((int32_T)(((int32_T)index_16) + 1))) {
            /* Outputs for Function Call SubSystem: '<S6>/BandPassFilter'
             *
             * Block description for '<S6>/BandPassFilter':
             *  Pass band filter is implemented through 2 cascade filter, a high pass
             *  Butterworth filter and a low pass Butterworth filter.
             *
             * Block requirements for '<S6>/BandPassFilter':
             *  1. EISB_FCA6CYL_SW_REQ_1597: Software shall apply a band pass Butterworth filter to the ion sig... (ECU_SW_Requirements#2835)
             */
            /* SignalConversion generated from: '<S9>/filterMemoryLP_in' */
            /* Transition: '<S7>:101' */
            /* NEW_PATTERN */
            /* Transition: '<S7>:102':
             *  1. EISB_FCA6CYL_SW_REQ_1597: Software shall apply a band pass Butterworth filter to the ion sig... (ECU_SW_Requirements#2835)
             */
            /* Event: '<S7>:115' */
            rtb_oldIn1N0 = rtb_Add3;

            /* SignalConversion generated from: '<S9>/filterMemoryLP_in' */
            rtb_oldOut1 = rtb_Divide_m;

            /* SignalConversion generated from: '<S9>/filterMemoryHP_in' */
            rtb_oldIn1N0_k = oldIn1N0_e;

            /* SignalConversion generated from: '<S9>/filterMemoryHP_in' */
            rtb_oldOut1_a = rtb_Divide_p;

            /* End of Outputs for SubSystem: '<S6>/BandPassFilter' */

            /* Inport: '<Root>/IonBuffer' */
            u0 = IonBuffer[index_16 + locStartIonDwell];
            Conversion4 = (uint32_T)MAX_IONBUFFER_VALUE;
            if (u0 < Conversion4) {
              Conversion4 = u0;
            }

            /* Outputs for Function Call SubSystem: '<S6>/BandPassFilter'
             *
             * Block description for '<S6>/BandPassFilter':
             *  Pass band filter is implemented through 2 cascade filter, a high pass
             *  Butterworth filter and a low pass Butterworth filter.
             *
             * Block requirements for '<S6>/BandPassFilter':
             *  1. EISB_FCA6CYL_SW_REQ_1597: Software shall apply a band pass Butterworth filter to the ion sig... (ECU_SW_Requirements#2835)
             */
            /* Product: '<S17>/Divide' incorporates:
             *  Product: '<S12>/Product2'
             */
            rtb_Add3 = (((int32_T)((uint16_T)Conversion4)) * ((int32_T)N0HP)) /
              16;

            /* SignalConversion generated from: '<S9>/filterMemoryHP' */
            oldIn1N0_e = rtb_Add3;

            /* Product: '<S14>/Divide' incorporates:
             *  Constant: '<S12>/Constant'
             *  Product: '<S12>/Product'
             *  Product: '<S12>/Product1'
             *  Product: '<S12>/Product3'
             *  Product: '<S15>/Divide'
             *  Product: '<S16>/Divide'
             *  SignalConversion generated from: '<S9>/filterMemoryHP_in'
             *  Sum: '<S12>/Add'
             *  Sum: '<S12>/Add1'
             *  Sum: '<S12>/Add2'
             *  Sum: '<S12>/Add3'
             */
            rtb_Divide_p = ((((oldIn2N0_o + rtb_Add3) - ((rtb_Divide_p *
              ((int32_T)D1HP)) / 16)) - ((((int32_T)D2HP) * oldOut2_l) / 16)) -
                            (rtb_oldIn1N0_k * ((int32_T)((uint8_T)TWO)))) / 512;

            /* Switch: '<S18>/Switch2' incorporates:
             *  Constant: '<S12>/Constant3'
             *  RelationalOperator: '<S18>/LowerRelop1'
             *  RelationalOperator: '<S18>/UpperRelop'
             *  Switch: '<S18>/Switch'
             */
            if (rtb_Divide_p > MAX_IONBUFFER_VALUE) {
              rtb_Switch2 = MAX_IONBUFFER_VALUE;
            } else if (rtb_Divide_p < 0) {
              /* Switch: '<S18>/Switch' incorporates:
               *  Constant: '<S12>/Constant1'
               */
              rtb_Switch2 = 0;
            } else {
              rtb_Switch2 = rtb_Divide_p;
            }

            /* Product: '<S21>/Divide' incorporates:
             *  DataTypeConversion: '<S12>/Conversion1'
             *  Product: '<S13>/Product2'
             */
            rtb_Add3 = (rtb_Switch2 * ((int32_T)N0LP)) / 16;

            /* Product: '<S19>/Divide' incorporates:
             *  Constant: '<S13>/Constant'
             *  Product: '<S13>/Product'
             *  Product: '<S13>/Product1'
             *  Product: '<S13>/Product3'
             *  Product: '<S20>/Divide'
             *  Product: '<S22>/Divide'
             *  SignalConversion generated from: '<S9>/filterMemoryLP_in'
             *  Sum: '<S13>/Add'
             *  Sum: '<S13>/Add3'
             */
            rtb_Divide_m = (((((rtb_oldIn1N0 * ((int32_T)((uint8_T)TWO))) +
                               oldIn2N0) + rtb_Add3) - ((rtb_Divide_m *
              ((int32_T)D1LP)) / 16)) - ((((int32_T)D2LP) * oldOut2) / 16)) /
              512;

            /* Switch: '<S23>/Switch2' incorporates:
             *  Constant: '<S13>/Constant2'
             *  RelationalOperator: '<S23>/LowerRelop1'
             *  RelationalOperator: '<S23>/UpperRelop'
             *  Switch: '<S23>/Switch'
             */
            if (rtb_Divide_m > MAX_IONBUFFER_VALUE) {
              rtb_Switch2 = MAX_IONBUFFER_VALUE;
            } else if (rtb_Divide_m < 0) {
              /* Switch: '<S23>/Switch' incorporates:
               *  Constant: '<S13>/Constant1'
               */
              rtb_Switch2 = 0;
            } else {
              rtb_Switch2 = rtb_Divide_m;
            }

            /* SignalConversion generated from: '<S9>/filterMemoryLP' */
            oldIn2N0 = rtb_oldIn1N0;

            /* SignalConversion generated from: '<S9>/filterMemoryLP' */
            oldOut2 = rtb_oldOut1;

            /* SignalConversion generated from: '<S9>/filterMemoryHP' */
            oldIn2N0_o = rtb_oldIn1N0_k;

            /* SignalConversion generated from: '<S9>/filterMemoryHP' */
            oldOut2_l = rtb_oldOut1_a;

            /* SignalConversion generated from: '<S1>/IonBufferFilt' incorporates:
             *  DataTypeConversion: '<S13>/Conversion1'
             */
            IonBufferFilt[(index_16)] = (uint16_T)rtb_Switch2;

            /* DataTypeConversion: '<S13>/Conversion1' incorporates:
             *  Constant: '<S4>/Constant7'
             */
            if (((uint32_T)((uint16_T)rtb_Switch2)) > Conversion3) {
              /* Transition: '<S7>:119':
               *  1. EISB_FCA6CYL_SW_REQ_1598: Software shall calculate the signal IonSignalDwellAcc as the sum o... (ECU_SW_Requirements#2840)
               */
              /* Transition: '<S7>:124' */
              IonSignalDwellAcc = IonSignalDwellAcc + ((uint32_T)((uint16_T)
                rtb_Switch2));
              if (((int32_T)MINIONDWELLOFFSETEN) == 1) {
                /* Transition: '<S7>:128' */
                /* Transition: '<S7>:132' */
                IonSignalDwellAcc = IonSignalDwellAcc - Conversion3;

                /* Transition: '<S7>:133' */
              } else {
                /* Transition: '<S7>:130' */
              }

              /* Transition: '<S7>:135' */
            } else {
              /* Transition: '<S7>:136' */
            }

            /* End of Outputs for SubSystem: '<S6>/BandPassFilter' */
            /* Transition: '<S7>:105' */
            /* Transition: '<S7>:103' */
          }

          /* Transition: '<S7>:104' */
          /* Transition: '<S7>:242' */
        } else {
          /* Transition: '<S7>:169' */
          /* Transition: '<S7>:154' */
          /*  Butterworth band-pass filter test  */
          /* Constant: '<S4>/Constant5' */
          for (b_index = 0U; b_index < IONBUFFERTESTFILTDIM; b_index = (uint8_T)
               ((int32_T)(((int32_T)b_index) + 1))) {
            /* Outputs for Function Call SubSystem: '<S6>/BandPassFilter'
             *
             * Block description for '<S6>/BandPassFilter':
             *  Pass band filter is implemented through 2 cascade filter, a high pass
             *  Butterworth filter and a low pass Butterworth filter.
             *
             * Block requirements for '<S6>/BandPassFilter':
             *  1. EISB_FCA6CYL_SW_REQ_1597: Software shall apply a band pass Butterworth filter to the ion sig... (ECU_SW_Requirements#2835)
             */
            /* SignalConversion generated from: '<S9>/filterMemoryLP_in' */
            /* Transition: '<S7>:155' */
            /* NEW_PATTERN */
            /* Transition: '<S7>:159':
             *  1. EISB_FCA6CYL_SW_REQ_1599: Software shall apply a band pass Butterworth filter to the signal ... (ECU_SW_Requirements#2838)
             */
            /* Event: '<S7>:115' */
            rtb_oldIn1N0 = rtb_Add3;

            /* SignalConversion generated from: '<S9>/filterMemoryLP_in' */
            rtb_oldOut1 = rtb_Divide_m;

            /* SignalConversion generated from: '<S9>/filterMemoryHP_in' */
            rtb_oldIn1N0_k = oldIn1N0_e;

            /* SignalConversion generated from: '<S9>/filterMemoryHP_in' */
            rtb_oldOut1_a = rtb_Divide_p;

            /* End of Outputs for SubSystem: '<S6>/BandPassFilter' */
            Conversion4 = (uint32_T)MAX_IONBUFFER_VALUE;

            /* Constant: '<S4>/Constant6' */
            if (VTIONBUFFERTESTFILT[(b_index)] < Conversion4) {
              Conversion4 = VTIONBUFFERTESTFILT[(b_index)];
            }

            /* Outputs for Function Call SubSystem: '<S6>/BandPassFilter'
             *
             * Block description for '<S6>/BandPassFilter':
             *  Pass band filter is implemented through 2 cascade filter, a high pass
             *  Butterworth filter and a low pass Butterworth filter.
             *
             * Block requirements for '<S6>/BandPassFilter':
             *  1. EISB_FCA6CYL_SW_REQ_1597: Software shall apply a band pass Butterworth filter to the ion sig... (ECU_SW_Requirements#2835)
             */
            /* Product: '<S17>/Divide' incorporates:
             *  Product: '<S12>/Product2'
             */
            rtb_Add3 = (((int32_T)Conversion4) * ((int32_T)N0HP)) / 16;

            /* SignalConversion generated from: '<S9>/filterMemoryHP' */
            oldIn1N0_e = rtb_Add3;

            /* Product: '<S14>/Divide' incorporates:
             *  Constant: '<S12>/Constant'
             *  Product: '<S12>/Product'
             *  Product: '<S12>/Product1'
             *  Product: '<S12>/Product3'
             *  Product: '<S15>/Divide'
             *  Product: '<S16>/Divide'
             *  SignalConversion generated from: '<S9>/filterMemoryHP_in'
             *  Sum: '<S12>/Add'
             *  Sum: '<S12>/Add1'
             *  Sum: '<S12>/Add2'
             *  Sum: '<S12>/Add3'
             */
            rtb_Divide_p = ((((oldIn2N0_o + rtb_Add3) - ((rtb_Divide_p *
              ((int32_T)D1HP)) / 16)) - ((((int32_T)D2HP) * oldOut2_l) / 16)) -
                            (rtb_oldIn1N0_k * ((int32_T)((uint8_T)TWO)))) / 512;

            /* Switch: '<S18>/Switch2' incorporates:
             *  Constant: '<S12>/Constant3'
             *  RelationalOperator: '<S18>/LowerRelop1'
             *  RelationalOperator: '<S18>/UpperRelop'
             *  Switch: '<S18>/Switch'
             */
            if (rtb_Divide_p > MAX_IONBUFFER_VALUE) {
              rtb_Switch2 = MAX_IONBUFFER_VALUE;
            } else if (rtb_Divide_p < 0) {
              /* Switch: '<S18>/Switch' incorporates:
               *  Constant: '<S12>/Constant1'
               */
              rtb_Switch2 = 0;
            } else {
              rtb_Switch2 = rtb_Divide_p;
            }

            /* Product: '<S21>/Divide' incorporates:
             *  DataTypeConversion: '<S12>/Conversion1'
             *  Product: '<S13>/Product2'
             */
            rtb_Add3 = (rtb_Switch2 * ((int32_T)N0LP)) / 16;

            /* Product: '<S19>/Divide' incorporates:
             *  Constant: '<S13>/Constant'
             *  Product: '<S13>/Product'
             *  Product: '<S13>/Product1'
             *  Product: '<S13>/Product3'
             *  Product: '<S20>/Divide'
             *  Product: '<S22>/Divide'
             *  SignalConversion generated from: '<S9>/filterMemoryLP_in'
             *  Sum: '<S13>/Add'
             *  Sum: '<S13>/Add3'
             */
            rtb_Divide_m = (((((rtb_oldIn1N0 * ((int32_T)((uint8_T)TWO))) +
                               oldIn2N0) + rtb_Add3) - ((rtb_Divide_m *
              ((int32_T)D1LP)) / 16)) - ((((int32_T)D2LP) * oldOut2) / 16)) /
              512;

            /* Switch: '<S23>/Switch2' incorporates:
             *  Constant: '<S13>/Constant2'
             *  RelationalOperator: '<S23>/LowerRelop1'
             *  RelationalOperator: '<S23>/UpperRelop'
             *  Switch: '<S23>/Switch'
             */
            if (rtb_Divide_m > MAX_IONBUFFER_VALUE) {
              rtb_Switch2 = MAX_IONBUFFER_VALUE;
            } else if (rtb_Divide_m < 0) {
              /* Switch: '<S23>/Switch' incorporates:
               *  Constant: '<S13>/Constant1'
               */
              rtb_Switch2 = 0;
            } else {
              rtb_Switch2 = rtb_Divide_m;
            }

            /* SignalConversion generated from: '<S9>/filterMemoryLP' */
            oldIn2N0 = rtb_oldIn1N0;

            /* SignalConversion generated from: '<S9>/filterMemoryLP' */
            oldOut2 = rtb_oldOut1;

            /* SignalConversion generated from: '<S9>/filterMemoryHP' */
            oldIn2N0_o = rtb_oldIn1N0_k;

            /* SignalConversion generated from: '<S9>/filterMemoryHP' */
            oldOut2_l = rtb_oldOut1_a;

            /* SignalConversion generated from: '<S1>/IonBufferFilt' incorporates:
             *  DataTypeConversion: '<S13>/Conversion1'
             */
            IonBufferFilt[(b_index)] = (uint16_T)rtb_Switch2;

            /* DataTypeConversion: '<S13>/Conversion1' incorporates:
             *  Constant: '<S4>/Constant7'
             */
            if (((uint32_T)((uint16_T)rtb_Switch2)) > Conversion3) {
              /* Transition: '<S7>:160':
               *  1. EISB_FCA6CYL_SW_REQ_1598: Software shall calculate the signal IonSignalDwellAcc as the sum o... (ECU_SW_Requirements#2840)
               */
              /* Transition: '<S7>:162' */
              IonSignalDwellAcc = IonSignalDwellAcc + ((uint32_T)((uint16_T)
                rtb_Switch2));
              if (((int32_T)MINIONDWELLOFFSETEN) == 1) {
                /* Transition: '<S7>:163' */
                /* Transition: '<S7>:165' */
                IonSignalDwellAcc = IonSignalDwellAcc - Conversion3;

                /* Transition: '<S7>:167' */
              } else {
                /* Transition: '<S7>:164' */
              }

              /* Transition: '<S7>:166' */
            } else {
              /* Transition: '<S7>:161' */
            }

            /* End of Outputs for SubSystem: '<S6>/BandPassFilter' */
            /* Transition: '<S7>:168' */
            /* Transition: '<S7>:156' */
          }

          /* Transition: '<S7>:158' */
        }

        /* Transition: '<S7>:246' */
        /* Transition: '<S7>:249' */
      } else {
        /* Constant: '<S4>/Constant3' */
        /* Transition: '<S7>:178' */
        if (((int32_T)IONBUFFERTESTFILT) == 0) {
          /* Transition: '<S7>:177' */
          /* Transition: '<S7>:173' */
          /* Transition: '<S7>:192' */
          /*  IonBuffer integral with dead zone  */
          for (index_16 = locStartIonDwell; index_16 < locStopIonDwell; index_16
               = (uint16_T)((int32_T)(((int32_T)index_16) + 1))) {
            /* Inport: '<Root>/IonBuffer' */
            /* Transition: '<S7>:193' */
            /* NEW_PATTERN */
            /* Transition: '<S7>:197' */
            if (IonBuffer[(index_16)] > Conversion3) {
              /* Transition: '<S7>:198':
               *  1. EISB_FCA6CYL_SW_REQ_1601: Software shall calculate the signal IonSignalDwellAcc as the sum o... (ECU_SW_Requirements#2841)
               */
              /* Transition: '<S7>:200' */
              IonSignalDwellAcc = IonSignalDwellAcc + IonBuffer[(index_16)];

              /* Constant: '<S4>/Constant7' */
              if (((int32_T)MINIONDWELLOFFSETEN) == 1) {
                /* Transition: '<S7>:201' */
                /* Transition: '<S7>:203' */
                IonSignalDwellAcc = IonSignalDwellAcc - Conversion3;

                /* Transition: '<S7>:205' */
              } else {
                /* Transition: '<S7>:202' */
              }

              /* Transition: '<S7>:204' */
            } else {
              /* Transition: '<S7>:199' */
            }

            /* Transition: '<S7>:206' */
            /* Transition: '<S7>:194' */
          }

          /* Transition: '<S7>:196' */
          /* Transition: '<S7>:249' */
        } else {
          /* Transition: '<S7>:174' */
          /* Transition: '<S7>:222' */
          /*  IonBuffer integral test with dead zone  */
          /* Constant: '<S4>/Constant5' */
          for (b_index = 0U; b_index < IONBUFFERTESTFILTDIM; b_index = (uint8_T)
               ((int32_T)(((int32_T)b_index) + 1))) {
            /* Constant: '<S4>/Constant6' */
            /* Transition: '<S7>:223' */
            /* NEW_PATTERN */
            /* Transition: '<S7>:227' */
            if (VTIONBUFFERTESTFILT[(b_index)] > Conversion3) {
              /* Transition: '<S7>:228':
               *  1. EISB_FCA6CYL_SW_REQ_1602: Software shall calculate the signal IonSignalDwellAcc as the sum o... (ECU_SW_Requirements#2842)
               */
              /* Transition: '<S7>:230' */
              IonSignalDwellAcc = IonSignalDwellAcc + VTIONBUFFERTESTFILT
                [(b_index)];

              /* Constant: '<S4>/Constant7' */
              if (((int32_T)MINIONDWELLOFFSETEN) == 1) {
                /* Transition: '<S7>:231' */
                /* Transition: '<S7>:233' */
                IonSignalDwellAcc = IonSignalDwellAcc - Conversion3;

                /* Transition: '<S7>:235' */
              } else {
                /* Transition: '<S7>:232' */
              }

              /* Transition: '<S7>:234' */
            } else {
              /* Transition: '<S7>:229' */
            }

            /* Transition: '<S7>:236' */
            /* Transition: '<S7>:224' */
          }

          /* Transition: '<S7>:226' */
        }
      }

      /* End of Inport: '<Root>/Rpm' */
      /* Transition: '<S7>:251' */
    } else {
      /* Transition: '<S7>:252' */
    }

    /* Transition: '<S7>:253' */
    /* Transition: '<S7>:257' */
  } else {
    /* Transition: '<S7>:15' */
  }

  /* Inport: '<Root>/IonAbsTdcEOA' */
  /* Transition: '<S7>:32' */
  /*  Output assignment  */
  StartIonDwellCyl[(IonAbsTdcEOA)] = (uint8_T)locStartIonDwell;
  if (locStopIonDwell < ((uint16_T)((uint8_T)MAX_UINT8))) {
    /* Inport: '<Root>/IonAbsTdcEOA' */
    StopIonDwellCyl[(IonAbsTdcEOA)] = (uint8_T)locStopIonDwell;
  } else {
    /* Inport: '<Root>/IonAbsTdcEOA' */
    StopIonDwellCyl[(IonAbsTdcEOA)] = ((uint8_T)MAX_UINT8);
  }

  /* Chart: '<S5>/Dwell_Integral' incorporates:
   *  Constant: '<S5>/Constant5'
   *  Constant: '<S5>/Constant6'
   *  Inport: '<Root>/DwellSampCyl'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/VtIntIonOffset'
   *
   * Block description for '<S5>/Dwell_Integral':
   *  The aim of this chart is to obtain the ion integral during main dwell-time.
   *  To obtain the dwell ion integral (DwellInt), it applies a normalization to the sum of IonBuffer elements during coil charge (IonSignalDwellAcc) calculated by IonDwellMgm model.
   *  Four normalization mode are available:
   *  1. None normalization and none offset; IonSignalDwellAcc is assigned directly to DwellInt.
   *  2. Only normalization; IonSignalDwellAcc is divided for the number of elements summed to obtain it.
   *  3. Only offset; the offset calculated by Integral_Offset chart is subtracted from IonSignalDwellAcc.
   *  4. Offset and normalization; both normalization (2) and offset (3) are applied to IonSignalDwellAcc.
   */
  /* Gateway: EOA/Dwell_Normalization/Dwell_Integral */
  /* During: EOA/Dwell_Normalization/Dwell_Integral */
  /* The aim of this chart is to obtain the ion integral during main dwell-time.
     To obtain the dwell ion integral (DwellInt), it applies a normalization to the sum of IonBuffer elements during coil charge (IonSignalDwellAcc) calculated by IonDwellMgm model.
     Four normalization mode are available:
     1. None normalization and none offset; IonSignalDwellAcc is assigned directly to DwellInt.
     2. Only normalization; IonSignalDwellAcc is divided for the number of elements summed to obtain it.
     3. Only offset; the offset calculated by Integral_Offset chart is subtracted from IonSignalDwellAcc.
     4. Offset and normalization; both normalization (2) and offset (3) are applied to IonSignalDwellAcc. */
  /* Entry Internal: EOA/Dwell_Normalization/Dwell_Integral */
  /* Transition: '<S27>:21' */
  /*  Assign default values
     Local variables  */
  MinMax = 1U;
  Conversion3 = IonSignalDwellAcc;

  /* Output variables  */
  /*  Verify if it is possible to calculate normalization  */
  if ((((int32_T)DwellSampCyl[(IonAbsTdcEOA)]) != 0) && (StopIonDwellCyl
       [(IonAbsTdcEOA)] > StartIonDwellCyl[(IonAbsTdcEOA)])) {
    /* Transition: '<S27>:23' */
    /* Transition: '<S27>:27' */
    MinMax = (uint8_T)(StopIonDwellCyl[(IonAbsTdcEOA)] - StartIonDwellCyl
                       [(IonAbsTdcEOA)]);

    /* Transition: '<S27>:28' */
  } else {
    /* Transition: '<S27>:25' */
  }

  /* Transition: '<S27>:30' */
  /*  NORMALIZATION MODE (switch case)  */
  switch (DWELLINTCALCTYPE) {
   case ((uint8_T)NOT_NORM_NOT_OFFSET):
    /* Transition: '<S27>:31' */
    /* Transition: '<S27>:33':
     *  1. EISB_FCA6CYL_SW_REQ_1607: Software shall calculate the ion integral during charging phase (i... (ECU_SW_Requirements#2844)
     */
    /* Transition: '<S27>:34' */
    /* Transition: '<S27>:35' */
    /* Transition: '<S27>:40' */
    /* Transition: '<S27>:45' */
    /* Transition: '<S27>:50' */
    /* Transition: '<S27>:51' */
    break;

   case ((uint8_T)NORM_NOT_OFFSET):
    /* Transition: '<S27>:32' */
    /* Transition: '<S27>:36' */
    /* Transition: '<S27>:38':
     *  1. EISB_FCA6CYL_SW_REQ_1608: Software shall calculate the ion integral during charging phase (i... (ECU_SW_Requirements#2845)
     */
    Conversion3 = IonSignalDwellAcc / ((uint32_T)MinMax);

    /* Transition: '<S27>:39' */
    /* Transition: '<S27>:40' */
    /* Transition: '<S27>:45' */
    /* Transition: '<S27>:50' */
    /* Transition: '<S27>:51' */
    break;

   case ((uint8_T)NOT_NORM_OFFSET):
    /* Transition: '<S27>:37' */
    /* Transition: '<S27>:41':
     *  1. EISB_FCA6CYL_SW_REQ_1609: Software shall calculate the ion integral during charging phase (i... (ECU_SW_Requirements#2846)
     */
    /* Transition: '<S27>:43' */
    Conversion3 = ((uint32_T)VtIntIonOffset[(IonAbsTdcEOA)]) * ((uint32_T)MinMax);
    if (IonSignalDwellAcc > Conversion3) {
      /* Transition: '<S27>:74' */
      /* Transition: '<S27>:77' */
      Conversion3 = IonSignalDwellAcc - Conversion3;
    } else {
      /* Transition: '<S27>:78' */
      Conversion3 = 0U;

      /* Transition: '<S27>:76' */
    }

    /* Transition: '<S27>:44' */
    /* Transition: '<S27>:45' */
    /* Transition: '<S27>:50' */
    /* Transition: '<S27>:51' */
    break;

   case ((uint8_T)NORM_OFFSET):
    /* Transition: '<S27>:42' */
    /* Transition: '<S27>:46':
     *  1. EISB_FCA6CYL_SW_REQ_1610: Software shall calculate the ion integral during charging phase (i... (ECU_SW_Requirements#2847)
     */
    /* Transition: '<S27>:48' */
    Conversion3 = IonSignalDwellAcc / ((uint32_T)MinMax);
    if (Conversion3 > ((uint32_T)VtIntIonOffset[(IonAbsTdcEOA)])) {
      /* Transition: '<S27>:49' */
      /* Transition: '<S27>:87' */
      Conversion3 -= (uint32_T)VtIntIonOffset[(IonAbsTdcEOA)];
    } else {
      /* Transition: '<S27>:89' */
      Conversion3 = 0U;

      /* Transition: '<S27>:90' */
    }

    /* Transition: '<S27>:86' */
    /* Transition: '<S27>:50' */
    /* Transition: '<S27>:51' */
    break;

   default:
    /* Transition: '<S27>:47' */
    /*  Default mode
       ionsignalacc = IonSignalDwellAcc; */
    break;
  }

  /* Transition: '<S27>:52' */
  if (Conversion3 < MAX_DWELLINT_VALUE) {
    /* Transition: '<S27>:94' */
    /* Transition: '<S27>:98' */
    DwellInt[(IonAbsTdcEOA)] = (uint16_T)(Conversion3 >> ((uint32_T)4));

    /* Transition: '<S27>:99' */
  } else {
    /* Transition: '<S27>:96' */
    DwellInt[(IonAbsTdcEOA)] = (uint16_T)MAX_UINT16;
  }

  /* SignalConversion generated from: '<S1>/DwellIntCyl' incorporates:
   *  Chart: '<S5>/Dwell_Integral'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *
   * Block description for '<S5>/Dwell_Integral':
   *  The aim of this chart is to obtain the ion integral during main dwell-time.
   *  To obtain the dwell ion integral (DwellInt), it applies a normalization to the sum of IonBuffer elements during coil charge (IonSignalDwellAcc) calculated by IonDwellMgm model.
   *  Four normalization mode are available:
   *  1. None normalization and none offset; IonSignalDwellAcc is assigned directly to DwellInt.
   *  2. Only normalization; IonSignalDwellAcc is divided for the number of elements summed to obtain it.
   *  3. Only offset; the offset calculated by Integral_Offset chart is subtracted from IonSignalDwellAcc.
   *  4. Offset and normalization; both normalization (2) and offset (3) are applied to IonSignalDwellAcc.
   */
  /* Transition: '<S27>:101' */
  /*  Outputs assignment  */
  DwellIntCyl = DwellInt[(IonAbsTdcEOA)];

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonDwellMgm_EOA' */
}

/* Model step function */
void IonDwellMgm_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonDwellMgm_PowerOn' incorporates:
   *  SubSystem: '<Root>/PowerOn'
   *
   * Block description for '<Root>/PowerOn':
   *  This block implements PowerOn runnable. Its aim is to initialize all
   *  signals.
   *
   * Block requirements for '<Root>/PowerOn':
   *  1. EISB_FCA6CYL_SW_REQ_1738: Software shall initialize each output produced for Coil Charge Phase integration at ECU power on. (ECU_SW_Requirements#4072)
   */
  /* SignalConversion generated from: '<S2>/IonBufferFilt' */
  memset((&(IonBufferFilt[0])), 0, 250U * (sizeof(uint16_T)));

  /* SignalConversion generated from: '<S2>/StartIonDwellCyl' */
  memset((&(StartIonDwellCyl[0])), 0, (sizeof(uint8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/StopIonDwellCyl' */
  memset((&(StopIonDwellCyl[0])), 0, (sizeof(uint8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/VtOLSecInt' */
  memset((&(VtOLSecInt[0])), 0, (sizeof(uint32_T)) << 3U);

  /* SignalConversion generated from: '<S2>/DwellInt' */
  memset((&(DwellInt[0])), 0, (sizeof(uint16_T)) << 3U);

  /* SignalConversion generated from: '<S2>/FlgOLSpark' */
  memset((&(FlgOLSpark[0])), 0, (sizeof(uint8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/IonSignalDwellAcc' incorporates:
   *  Constant: '<S2>/Constant2'
   */
  IonSignalDwellAcc = 0U;

  /* SignalConversion generated from: '<S2>/DwellIntCyl' incorporates:
   *  Constant: '<S2>/Constant'
   */
  DwellIntCyl = 0U;

  /* Constant: '<S2>/Constant12' */
  IdVer_IonDwellMgm = ID_VER_IONDWELLMGM_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonDwellMgm_PowerOn' */
}

/* Model initialize function */
void IonDwellMgm_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint16_T DwellIntCyl;
uint8_T FlgOLSpark;
uint16_T DwellIntl[N_CYL_MAX];
uint32_T VtOLSecInt[N_CYL_MAX];
void IonDwellMgm_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    DwellInt[idx] = 0u;
    VtOLSecInt[idx] = 0u;
  }

  DwellIntCyl = 0u;
  FlgOLSpark = 0u;
}

void IonDwellMgm_PowerOn(void)
{
  IonDwellMgm_Stub();
}

void IonDwellMgm_EOA(void)
{
  IonDwellMgm_Stub();
}

#endif                                 /*_BUILD_IONDWELLMGM_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/