use strict;
use warnings;
#use autodie;

# check parameter
die "Usage: create.pl <inputfile.map> <pattern_to_locate>" if $#ARGV !=1;
  
sub main 
{ 

    #input file from console
    my $file = $ARGV[0]; #'I2F61D.map'
    
    #input pattern from console 
    my $pattern = $ARGV[1]; #'__APP_CODE_END';
    
    #try open file, throw exception if needed
    open(FH, $file) or die("File $file not found"); 
    
    #loop among the lines to find the pattern
    while(my $String = <FH>) 
    { 
        #if pattern matches
        if($String =~ $pattern) 
        { 
            #open handle for writing a file
            open WRITEHANDLE, '>prmFile1.txt' or die $!;
            
            #print header on batchfile
            print WRITEHANDLE ("__KWP_START_ADD 00ff0000 \n");
            print WRITEHANDLE ("__KWP_CODE_SIZE 00210000 \n");
            print WRITEHANDLE ("__KWP_APP_CODE_END ");
            
            #string appears as '                  000123456+offset __APP_CODE_END' -> remove blank chars from the line
            $String =~ s/^\s+//;
            
            #string appears as '000123456+offset __APP_CODE_END' -> get 1st 8(eight) byte of the string
            $String = substr $String, 0,8;
            my $app_code_end =  hex $String;
            #print "app_code_end = $app_code_end\n";
                        
            #0xFF0000 is the Application SW start Address in the new EISB ST K based
            my $calib_offset = 0xFF0000;
            #0x30000 is the Application SW start Address in the old EISB Freescale MPC5642A based
            #my $calib_offset = 0x30000;
            #print "calib_offset = $calib_offset\n";            
                       
            #subtract Application SW start Address value to __APP_CODE_END
            my $temp = $app_code_end - $calib_offset;
            #my $temp = $app_code_end; 
            #print "Last Valid Application Address: $temp\n";
            #print "Last Valid Application Address: ", sprintf('%08X', $temp), "\n";
            # print "Last Valid Application Address: %08X\n", $temp;
                       
            # my $temp4 = ($temp2);
            # print $temp4;
                                    
            #string appears as '000123456' -> append 8(eight) byte to the batchfile handler
            print WRITEHANDLE sprintf('%08X', $temp); 
            print WRITEHANDLE (" \n");
            print WRITEHANDLE ("TOTAL_SIZE 01200000 "); 
           
            
            # header file updated  
            close WRITEHANDLE;
        } 
    } 
    #Close the reading handler
    close(FH); 
} 
#call main function
main(); 