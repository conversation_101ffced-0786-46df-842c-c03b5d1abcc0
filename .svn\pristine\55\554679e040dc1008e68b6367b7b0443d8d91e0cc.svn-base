/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DSPI
**  Filename        :  Dspi_out.h
**  Created on      :  16-giu-2020 14:04:00
**  Original author :  CarboniM
******************************************************************************/
/*****************************************************************************
**
**                        DSPI Description
**
**  DSPI Sw module provides driver and interface to manage DSPI peripheral on chip.
**
******************************************************************************/

#ifndef _DSPI_OUT_H_
#define _DSPI_OUT_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "sys.h"
#include "OS_resources.h"
#include "rtwtypes.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
///SPI channel A
#define SPI_CH_A 0u
///SPI channel B
#define SPI_CH_B 1u
///SPI channel C
#define SPI_CH_C 2u
///SPI channel D
#define SPI_CH_D 3u
///SPI channel E
#define SPI_CH_E 4u

///SPI chip select 0
#define PCS_0            0u
///SPI chip select 1
#define PCS_1            1u
///SPI chip select 2
#define PCS_2            2u
///SPI chip select 3
#define PCS_3            3u
///SPI chip select 4
#define PCS_4            4u
///SPI chip select 5
#define PCS_5            5u
///SPI chip select 6
#define PCS_6            6u
///SPI chip select 7
#define PCS_7            7u


///SPI error code for wrong size message
#define SPI_WRONG_SIZE -9
///SPI status code: spi running
#define SPI_RUNNING    -10
///SPI status code: spi stopped
#define SPI_STOPPED    -11
///SPI status code: spi error
#define SPI_ERROR      -12
///SPI error code for happened timeout on RX counter
#define SPI_TIMEOUT_ERROR -13
///SPI resource status FREE
#define SPI_FREE 0u
///SPI resource status BUSY
#define SPI_BUSY 1u

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
///SPI type for error codes 
typedef int16_T SpiError_T;

///This is a public typedef for function pointer associated to CSx
typedef  void (*FuncSpi_T)(void);

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint16_T SPIConfigStatus;
extern uint8_T SPIChannelStatus[5];

///Function pointer to the Irq vector associated to the CSx, set for SPI_SLAVE
#if SPI_CH_A_EN
#if SPI_A_FUNC_INT
extern FuncSpi_T SPIAIrqVect[SPI_A_CS_NUM];
#endif
#endif
#if SPI_CH_B_EN
#if SPI_B_FUNC_INT
extern FuncSpi_T SPIBIrqVect[SPI_B_CS_NUM];
#endif
#endif
#if SPI_CH_C_EN
#if SPI_C_FUNC_INT
extern FuncSpi_T SPICIrqVect[SPI_C_CS_NUM];
#endif
#endif
#if SPI_CH_D_EN
#if SPI_D_FUNC_INT
extern FuncSpi_T SPIDIrqVect[SPI_D_CS_NUM];
#endif
#endif

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : SPI_Config
**
**   Description:
**    Function for configuration of SPI channels.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR                      - SPI channels correctly configured.
**    PERIPHERAL_ALREADY_CONFIGURED - SPI channels were already configured.
**
******************************************************************************/
extern SpiError_T SPI_Config(void);

/******************************************************************************
**   Function    : SPI_RxTxBuffer
**
**   Description:
**    This function configures and enables transmission and reception on selected
**    channel with DMA support.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint16_T* txBuffer : Pointer to transmission buffer
**    [in] uint8_T txSize : Size of buffer to be transmitted
**    [in] uint8_T ctas : Clock Transfer Attribute Select
**
**   Returns:
**    NO_ERROR                   - SPI channel correctly configured and enabled.
**    SPI_WRONG_SIZE             - txSize is greater than the size of configured buffer.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
******************************************************************************/
extern SpiError_T SPI_RxTxBuffer(uint8_T channel, uint16_T *txBuffer, uint8_T txSize, uint8_T ctas);

/******************************************************************************
**   Function    : SPI_GetRxData
**
**   Description:
**    This method  reads the data sent by the slave, previously copied in
**    memory by DMA.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [out] uint16_T rxBuffer[] : Pointer to reception buffer
**    [in] uint32_T rxSize : Size of buffer to be received
**
**   Returns:
**    NO_ERROR                   - SPI channel correctly configured and enabled.
**    SPI_WRONG_SIZE             - rxSize is greater than the size of configured buffer.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
******************************************************************************/
extern SpiError_T SPI_GetRxData(uint8_T channel, uint16_T rxBuffer[], uint32_T rxSize);

/******************************************************************************
**   Function    : SPI_Write
**
**   Description:
**    Configures and enables DMA channels.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint16_T* txBuffer : Pointer to transmission buffer
**    [in] uint8_T wordNumber : Size of buffer to be transmitted
**    [in] uint8_T nPCS : Peripheral Chip Select mask
**    [in] uint8_T ctas : Clock Transfer Attribute Select
**
**   Returns:
**    NO_ERROR                      - SPI channel correctly configured and enabled.
**    PERIPHERAL_ALREADY_CONFIGURED - Peripheral already configured
**
******************************************************************************/
extern int16_T SPI_Write(uint8_T channel, uint16_T *txBuffer, uint8_T wordNumber, uint8_T nPCS, uint8_T ctas);

/******************************************************************************
**   Function    : SPI_Disable
**
**   Description:
**    Disables SPI and DMA associated channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel to be disabled
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPI_Disable(uint8_T channel);

/******************************************************************************
**   Function    : SPI_TxRx
**
**   Description:
**    Transmission and reception of SPI messages.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T nPCS : Peripheral Chip Select mask
**    [in] const uint16_T* txBuffer : Pointer to the buffer to be transmitted
**    [in] uint16_T* rxBuffer : Pointer to the receive buffer
**    [in] uint8_T size : Size of the message
**
**   Returns:
**    NO_ERROR                       - SPI messages correctly transmitted and received.
**    SPI_WRONG_SIZE                 - size is greater than the size of configured buffer.
**    SPI_TIMEOUT_ERROR              - SPI timeout error.
**    PERIPHERAL_NOT_INITIALIZED     - SPI channel is not initialized.
**
******************************************************************************/
extern SpiError_T SPI_TxRx(uint8_T channel, uint8_T nPCS, const uint16_T *txBuffer, uint16_T *rxBuffer, uint8_T size);

/******************************************************************************
**   Function    : SPI_Enable
**
**   Description:
**    Enables SPI channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel to be enabled
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPI_Enable(uint8_T channel);

/******************************************************************************
**   Function    : SPI_GetChannelStatus
**
**   Description:
**    Gets and returns SPI channel status.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel to get status.
**
**   Returns:
**    SPI_STOPPED                - SPI channel is stopped.
**    SPI_RUNNING                - SPI channel is running.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
******************************************************************************/
extern SpiError_T SPI_GetChannelStatus(uint8_T channel);

/******************************************************************************
**   Function    : SPI_SetInterruptHandler
**
**   Description:
**    Sets interrupt handler for specific SPI channel (and chip select).
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T cs : Chip Select
**    [in] TaskType isrFunction : ID for Interrupt Service Routine
**
**   Returns:
**    NO_ERROR                       - Interrupt handler set correctly.
**    SPI_ERROR                      - Interrupt handler not set correctly.
**    PERIPHERAL_NOT_INITIALIZED     - SPI channel is not initialized
**
******************************************************************************/
extern int16_T SPI_SetInterruptHandler(uint8_T channel, uint8_T cs, TaskType isrFunction);

/******************************************************************************
**   Function    : SPI_Transmit
**
**   Description:
**    Transmission of SPI message without DMA support.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T nPCS : Peripheral Chip Select mask
**    [in] const uint16_T txBuffer[] : Pointer to the buffer to be transmitted
**    [in] const uint8_T txSize : Size of the buffer to be transmitted
**
**   Returns:
**    NO_ERROR                   - SPI message correctly transmitted.
**    SPI_WRONG_SIZE             - txSize is greater than the size of configured buffer.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
******************************************************************************/
extern SpiError_T SPI_Transmit(uint8_T channel, uint8_T nPCS, const uint16_T txBuffer[], const uint8_T txSize);

/******************************************************************************
**   Function    : SPIRes_Init
**
**   Description:
**    Initialization of SPI resource status.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPIRes_Init(void);

/******************************************************************************
**   Function    : SPIRes_SetFlag
**
**   Description:
**    Sets status of SPI channel as SPI_BUSY.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPIRes_SetFlag(uint8_T channel);

/******************************************************************************
**   Function    : ResetSPIResFlag
**
**   Description:
**    Sets status of SPI channel as SPI_FREE.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    void
**
******************************************************************************/
extern void ResetSPIResFlag(uint8_T channel);

/******************************************************************************
**   Function    : GetSPIResFlag
**
**   Description:
**    Returns actual status for SPI channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    SPI_FREE   - SPI free.
**    SPI_BUSY   - SPI busy.
**
******************************************************************************/
extern uint8_T GetSPIResFlag(uint8_T channel);

/******************************************************************************
**   Function    : SPIRes_GetFromChannel
**
**   Description:
**    Gets Resource ID from channel you need to protect from concurrent access.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [out] ResourceType* resource : Pointer to resource ID
**
**   Returns:
**    0  - No error
**    -1 - Error.
**
******************************************************************************/
extern int16_T SPIRes_GetFromChannel(uint8_T channel, ResourceType *resource);

/******************************************************************************
**   Function    : SPI_A_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel A.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPI_A_EOQ_ISR(void);

/******************************************************************************
**   Function    : SPI_B_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel B.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPI_B_EOQ_ISR(void);

/******************************************************************************
**   Function    : SPI_C_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel C.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPI_C_EOQ_ISR(void);

/******************************************************************************
**   Function    : SPI_D_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel D.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPI_D_EOQ_ISR(void);

/******************************************************************************
**   Function    : SPI_E_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel E.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SPI_E_EOQ_ISR(void);


#endif /* _DSPI_OUT_H_ */

/****************************************************************************
 ****************************************************************************/
