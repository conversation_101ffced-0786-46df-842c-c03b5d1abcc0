/************************************************************************************/
/* $HeadURL$   */
/* $ Description:                                                                   */
/* $Revision::        $                                                             */
/* $Date::                                                $                         */
/* $Author::                             $                                          */
/************************************************************************************/

#include "rtwtypes.h"
#include "diagmgm.h"

#ifdef _BUILD_DIAGMGM_

#pragma ghs section rodata=".calib"

/// Table to disable diagnosis index
CALQUAL CALQUAL_POST uint8_T TBDISDIAG[DIAG_NUMBER][TBDISDIAG_CULS] = 
{
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_T_AIR               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_T_WATER             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_LOAD                */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_RPM                 */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_CAMLEVEL            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_FLGBANKSEL          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VBATTERY            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_DUMMY               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ADC                 */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_FREE_9              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_FREE_10             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_0               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_1               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_2               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_3               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_4               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_5               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_6               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_7               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VBAT_CIRCUIT        */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_COIL_0              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_COIL_1              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_COIL_2              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_COIL_3              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_COIL_4              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_COIL_5              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_COIL_6              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_COIL_7              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ELDOR_SW            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_WDT                 */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_PRI_A               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_PRI_B               */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_LIVENESS            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_BANK_MISF           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TEMP_ECU_1          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TEMP_ECU_2          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_PRIVATE_CAN         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_CAN_NODE_1          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_CPU                 */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_EEPROM              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KNOCK_COH_0         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KNOCK_COH_1         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KNOCK_COH_2         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KNOCK_COH_3         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KNOCK_COH_4         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KNOCK_COH_5         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KNOCK_COH_6         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KNOCK_COH_7         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SYNC                */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TEMP_ECU_3          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SPARK_EV_A          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SPARK_EV_B          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_BUCK_A              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_BUCK_B              */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TRIGGER_0           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TRIGGER_1           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TRIGGER_2           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TRIGGER_3           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TRIGGER_4           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TRIGGER_5           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TRIGGER_6           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TRIGGER_7           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_OL_0             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_OL_1             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_OL_2             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_OL_3             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_OL_4             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_OL_5             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_OL_6             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_OL_7             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_CAN_NODE_OVER_RUN   */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VEHICLE_CAN         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_0_4             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_1_5             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_2_6             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_SEC_3_7             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VCAP_0_4            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VCAP_1_5            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VCAP_2_6            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VCAP_3_7            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_IGN                 */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VCOIL_A_MON         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_VCOIL_B_MON         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_FREE_83             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_FREE_84             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_BOARD_SEL           */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_FREE_86             */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_FREE_87         */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_KEY_SIGNAL          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_GTM                 */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_TLE9278BQX          */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_CH_A            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_CH_B            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_CH_C            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}, /*  DIAG_ION_CH_D            */
    {255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u, 255u}  /*  DIAG_FREE_95             */
};

///Number of WUC to reset the stored fault flag in NVM counter
CALQUAL CALQUAL_POST uint8_T THRDIAGWUC =  40u;
///Lower threshold on VBattery to set the flag FlgDisDiagVBat [V]
CALQUAL CALQUAL_POST uint16_T THINFVBATDDIAG = 144u;   //( 9.0000*16)
///Higher threshold on VBattery to set the flag FlgDisDiagVBat [V]
CALQUAL CALQUAL_POST uint16_T THSUPVBATDDIAG = 256u;   //(16.0000*16)
///Rpm threshold to enable the diagnosis
CALQUAL CALQUAL_POST uint16_T THRPMDIAG = 600u;
/// Diagnosis enable vector
CALQUAL CALQUAL_POST uint8_T VTDIAGENABLE[DIAG_NUMBER] = 
{
    DIAG_DISABLED, /*  DIAG_T_AIR               */
    DIAG_DISABLED, /*  DIAG_T_WATER             */
    DIAG_DISABLED, /*  DIAG_LOAD                */
    DIAG_DISABLED, /*  DIAG_RPM                 */
    DIAG_DISABLED, /*  DIAG_CAMLEVEL            */
    DIAG_DISABLED, /*  DIAG_FLGBANKSEL          */
    DIAG_DISABLED, /*  DIAG_VBATTERY            */
    DIAG_DISABLED, /*  DIAG_DUMMY               */
    DIAG_DISABLED, /*  DIAG_ADC                 */
    DIAG_DISABLED, /*  DIAG_FREE_9              */
    DIAG_DISABLED, /*  DIAG_FREE_10             */
    DIAG_DISABLED, /*  DIAG_ION_0               */
    DIAG_DISABLED, /*  DIAG_ION_1               */
    DIAG_DISABLED, /*  DIAG_ION_2               */
    DIAG_DISABLED, /*  DIAG_ION_3               */
    DIAG_DISABLED, /*  DIAG_ION_4               */
    DIAG_DISABLED, /*  DIAG_ION_5               */
    DIAG_DISABLED, /*  DIAG_ION_6               */
    DIAG_DISABLED, /*  DIAG_ION_7               */
    DIAG_DISABLED, /*  DIAG_VBAT_CIRCUIT        */
    DIAG_DISABLED, /*  DIAG_COIL_0              */
    DIAG_DISABLED, /*  DIAG_COIL_1              */
    DIAG_DISABLED, /*  DIAG_COIL_2              */
    DIAG_DISABLED, /*  DIAG_COIL_3              */
    DIAG_DISABLED, /*  DIAG_COIL_4              */
    DIAG_DISABLED, /*  DIAG_COIL_5              */
    DIAG_DISABLED, /*  DIAG_COIL_6              */
    DIAG_DISABLED, /*  DIAG_COIL_7              */
    DIAG_DISABLED, /*  DIAG_ELDOR_SW            */
    DIAG_DISABLED, /*  DIAG_WDT                 */
    DIAG_DISABLED, /*  DIAG_PRI_A               */
    DIAG_DISABLED, /*  DIAG_PRI_B               */
    DIAG_DISABLED, /*  DIAG_LIVENESS            */
    DIAG_DISABLED, /*  DIAG_BANK_MISF           */
    DIAG_DISABLED, /*  DIAG_TEMP_ECU_1          */
    DIAG_DISABLED, /*  DIAG_TEMP_ECU_2          */
    DIAG_DISABLED, /*  DIAG_PRIVATE_CAN         */
    DIAG_DISABLED, /*  DIAG_CAN_NODE_1          */
    DIAG_DISABLED, /*  DIAG_CPU                 */
    DIAG_DISABLED, /*  DIAG_EEPROM              */
    DIAG_DISABLED, /*  DIAG_KNOCK_COH_0         */
    DIAG_DISABLED, /*  DIAG_KNOCK_COH_1         */
    DIAG_DISABLED, /*  DIAG_KNOCK_COH_2         */
    DIAG_DISABLED, /*  DIAG_KNOCK_COH_3         */
    DIAG_DISABLED, /*  DIAG_KNOCK_COH_4         */
    DIAG_DISABLED, /*  DIAG_KNOCK_COH_5         */
    DIAG_DISABLED, /*  DIAG_KNOCK_COH_6         */
    DIAG_DISABLED, /*  DIAG_KNOCK_COH_7         */
    DIAG_DISABLED, /*  DIAG_SYNC                */
    DIAG_DISABLED, /*  DIAG_TEMP_ECU_3          */
    DIAG_DISABLED, /*  DIAG_SPARK_EV_A          */
    DIAG_DISABLED, /*  DIAG_SPARK_EV_B          */
    DIAG_DISABLED, /*  DIAG_BUCK_A              */
    DIAG_DISABLED, /*  DIAG_BUCK_B              */
    DIAG_DISABLED, /*  DIAG_TRIGGER_0           */
    DIAG_DISABLED, /*  DIAG_TRIGGER_1           */
    DIAG_DISABLED, /*  DIAG_TRIGGER_2           */
    DIAG_DISABLED, /*  DIAG_TRIGGER_3           */
    DIAG_DISABLED, /*  DIAG_TRIGGER_4           */
    DIAG_DISABLED, /*  DIAG_TRIGGER_5           */
    DIAG_DISABLED, /*  DIAG_TRIGGER_6           */
    DIAG_DISABLED, /*  DIAG_TRIGGER_7           */
    DIAG_DISABLED, /*  DIAG_SEC_OL_0             */
    DIAG_DISABLED, /*  DIAG_SEC_OL_1             */
    DIAG_DISABLED, /*  DIAG_SEC_OL_2             */
    DIAG_DISABLED, /*  DIAG_SEC_OL_3             */
    DIAG_DISABLED, /*  DIAG_SEC_OL_4             */
    DIAG_DISABLED, /*  DIAG_SEC_OL_5             */
    DIAG_DISABLED, /*  DIAG_SEC_OL_6             */
    DIAG_DISABLED, /*  DIAG_SEC_OL_7             */
    DIAG_DISABLED, /*  DIAG_CAN_NODE_OVER_RUN   */
    DIAG_DISABLED, /*  DIAG_VEHICLE_CAN         */
    DIAG_DISABLED, /*  DIAG_SEC_0_4             */
    DIAG_DISABLED, /*  DIAG_SEC_1_5             */
    DIAG_DISABLED, /*  DIAG_SEC_2_6             */
    DIAG_DISABLED, /*  DIAG_SEC_3_7             */
    DIAG_DISABLED, /*  DIAG_VCAP_0_4            */
    DIAG_DISABLED, /*  DIAG_VCAP_1_5            */
    DIAG_DISABLED, /*  DIAG_VCAP_2_6            */
    DIAG_DISABLED, /*  DIAG_VCAP_3_7            */
    DIAG_DISABLED, /*  DIAG_IGN                 */
    DIAG_DISABLED, /*  DIAG_FREE_81             */
    DIAG_DISABLED, /*  DIAG_FREE_82             */
    DIAG_DISABLED, /*  DIAG_FREE_83             */
    DIAG_DISABLED, /*  DIAG_FREE_84             */
    DIAG_DISABLED, /*  DIAG_FREE_85             */
    DIAG_DISABLED, /*  DIAG_FREE_86             */
    DIAG_DISABLED, /*  DIAG_FREE_87             */
    DIAG_DISABLED, /*  DIAG_KEY_SIGNAL          */
    DIAG_DISABLED, /*  DIAG_GTM                 */
    DIAG_DISABLED, /*  DIAG_TLE9278BQX          */
    DIAG_DISABLED, /*  DIAG_ION_CH_A            */
    DIAG_DISABLED, /*  DIAG_ION_CH_B            */
    DIAG_DISABLED, /*  DIAG_ION_CH_C            */
    DIAG_DISABLED, /*  DIAG_ION_CH_D            */
    DIAG_DISABLED  /*  DIAG_FREE_95             */
};
///Fault counter increment step counter
CALQUAL CALQUAL_POST uint8_T VTSTEPINCFAULT[DIAG_NUMBER] = 
{
    1u, /*  DIAG_T_AIR               */
    1u, /*  DIAG_T_WATER             */
    1u, /*  DIAG_LOAD                */
    1u, /*  DIAG_RPM                 */
    1u, /*  DIAG_CAMLEVEL            */
    1u, /*  DIAG_FLGBANKSEL          */
    1u, /*  DIAG_VBATTERY            */
    1u, /*  DIAG_DUMMY               */
    1u, /*  DIAG_ADC                 */
    1u, /*  DIAG_FREE_9              */
    1u, /*  DIAG_FREE_10             */
    1u, /*  DIAG_ION_0               */
    1u, /*  DIAG_ION_1               */
    1u, /*  DIAG_ION_2               */
    1u, /*  DIAG_ION_3               */
    1u, /*  DIAG_ION_4               */
    1u, /*  DIAG_ION_5               */
    1u, /*  DIAG_ION_6               */
    1u, /*  DIAG_ION_7               */
    1u, /*  DIAG_VBAT_CIRCUIT        */
    1u, /*  DIAG_COIL_0              */
    1u, /*  DIAG_COIL_1              */
    1u, /*  DIAG_COIL_2              */
    1u, /*  DIAG_COIL_3              */
    1u, /*  DIAG_COIL_4              */
    1u, /*  DIAG_COIL_5              */
    1u, /*  DIAG_COIL_6              */
    1u, /*  DIAG_COIL_7              */
    1u, /*  DIAG_ELDOR_SW            */
    1u, /*  DIAG_WDT                 */
    1u, /*  DIAG_PRI_A               */
    1u, /*  DIAG_PRI_B               */
    1u, /*  DIAG_LIVENESS            */
    1u, /*  DIAG_BANK_MISF           */
    1u, /*  DIAG_TEMP_ECU_1          */
    1u, /*  DIAG_TEMP_ECU_2          */
    1u, /*  DIAG_PRIVATE_CAN         */
    1u, /*  DIAG_CAN_NODE_1          */
    1u, /*  DIAG_CPU                 */
    1u, /*  DIAG_RAM                 */
    1u, /*  DIAG_KNOCK_COH_0         */
    1u, /*  DIAG_KNOCK_COH_1         */
    1u, /*  DIAG_KNOCK_COH_2         */
    1u, /*  DIAG_KNOCK_COH_3         */
    1u, /*  DIAG_KNOCK_COH_4         */
    1u, /*  DIAG_KNOCK_COH_5         */
    1u, /*  DIAG_KNOCK_COH_6         */
    1u, /*  DIAG_KNOCK_COH_7         */
    1u, /*  DIAG_SYNC                */
    1u, /*  DIAG_TEMP_ECU_3          */
    1u, /*  DIAG_SPARK_EV_A          */
    1u, /*  DIAG_SPARK_EV_B          */
    1u, /*  DIAG_BUCK_A              */
    1u, /*  DIAG_BUCK_B              */
    1u, /*  DIAG_TRIGGER_0           */
    1u, /*  DIAG_TRIGGER_1           */
    1u, /*  DIAG_TRIGGER_2           */
    1u, /*  DIAG_TRIGGER_3           */
    1u, /*  DIAG_TRIGGER_4           */
    1u, /*  DIAG_TRIGGER_5           */
    1u, /*  DIAG_TRIGGER_6           */
    1u, /*  DIAG_TRIGGER_7           */
    1u, /*  DIAG_SEC_OL_0             */
    1u, /*  DIAG_SEC_OL_1             */
    1u, /*  DIAG_SEC_OL_2             */
    1u, /*  DIAG_SEC_OL_3             */
    1u, /*  DIAG_SEC_OL_4             */
    1u, /*  DIAG_SEC_OL_5             */
    1u, /*  DIAG_SEC_OL_6             */
    1u, /*  DIAG_SEC_OL_7             */
    1u, /*  DIAG_CAN_NODE_OVER_RUN   */
    1u, /*  DIAG_VEHICLE_CAN         */
    1u, /*  DIAG_SEC_0_4             */
    1u, /*  DIAG_SEC_1_5             */
    1u, /*  DIAG_SEC_2_6             */
    1u, /*  DIAG_SEC_3_7             */
    1u, /*  DIAG_VCAP_0_4            */
    1u, /*  DIAG_VCAP_1_5            */
    1u, /*  DIAG_VCAP_2_6            */
    1u, /*  DIAG_VCAP_3_7            */
    1u, /*  DIAG_IGN                 */
    1u, /*  DIAG_FREE_81             */
    1u, /*  DIAG_FREE_82             */
    1u, /*  DIAG_FREE_83             */
    1u, /*  DIAG_FREE_84             */
    1u, /*  DIAG_FREE_85             */
    1u, /*  DIAG_FREE_86             */
    1u, /*  DIAG_FREE_87             */
    1u, /*  DIAG_KEY_SIGNAL          */
    1u, /*  DIAG_GTM                 */
    1u, /*  DIAG_TLE9278BQX          */
    1u, /*  DIAG_ION_CH_A            */
    1u, /*  DIAG_ION_CH_B            */
    1u, /*  DIAG_ION_CH_C            */
    1u, /*  DIAG_ION_CH_D            */
    1u  /*  DIAG_FREE_95             */
};

///Fault counter decrement step counter
CALQUAL CALQUAL_POST uint8_T VTSTEPDECFAULT[DIAG_NUMBER] = 
{
    1u, /*  DIAG_T_AIR               */
    1u, /*  DIAG_T_WATER             */
    1u, /*  DIAG_LOAD                */
    1u, /*  DIAG_RPM                 */
    1u, /*  DIAG_CAMLEVEL            */
    1u, /*  DIAG_FLGBANKSEL          */
    1u, /*  DIAG_VBATTERY            */
    1u, /*  DIAG_DUMMY               */
    1u, /*  DIAG_ADC                 */
    1u, /*  DIAG_ION_CH_A            */
    1u, /*  DIAG_ION_CH_B            */
    1u, /*  DIAG_ION_0               */
    1u, /*  DIAG_ION_1               */
    1u, /*  DIAG_ION_2               */
    1u, /*  DIAG_ION_3               */
    1u, /*  DIAG_ION_4               */
    1u, /*  DIAG_ION_5               */
    1u, /*  DIAG_ION_6               */
    1u, /*  DIAG_ION_7               */
    1u, /*  DIAG_VBAT_CIRCUIT        */
    1u, /*  DIAG_COIL_0              */
    1u, /*  DIAG_COIL_1              */
    1u, /*  DIAG_COIL_2              */
    1u, /*  DIAG_COIL_3              */
    1u, /*  DIAG_COIL_4              */
    1u, /*  DIAG_COIL_5              */
    1u, /*  DIAG_COIL_6              */
    1u, /*  DIAG_COIL_7              */
    1u, /*  DIAG_ELDOR_SW            */
    1u, /*  DIAG_WDT                 */
    1u, /*  DIAG_PRI_A               */
    1u, /*  DIAG_PRI_B               */
    1u, /*  DIAG_LIVENESS            */
    1u, /*  DIAG_BANK_MISF           */
    1u, /*  DIAG_TEMP_ECU_1          */
    1u, /*  DIAG_TEMP_ECU_2          */
    1u, /*  DIAG_PRIVATE_CAN         */
    1u, /*  DIAG_CAN_NODE_1          */
    1u, /*  DIAG_CPU                 */
    1u, /*  DIAG_RAM                 */
    1u, /*  DIAG_KNOCK_COH_0         */
    1u, /*  DIAG_KNOCK_COH_1         */
    1u, /*  DIAG_KNOCK_COH_2         */
    1u, /*  DIAG_KNOCK_COH_3         */
    1u, /*  DIAG_KNOCK_COH_4         */
    1u, /*  DIAG_KNOCK_COH_5         */
    1u, /*  DIAG_KNOCK_COH_6         */
    1u, /*  DIAG_KNOCK_COH_7         */
    1u, /*  DIAG_SYNC                */
    1u, /*  DIAG_TEMP_ECU_3          */
    1u, /*  DIAG_SPARK_EV_A          */
    1u, /*  DIAG_SPARK_EV_B          */
    1u, /*  DIAG_BUCK_A              */
    1u, /*  DIAG_BUCK_B              */
    1u, /*  DIAG_TRIGGER_0           */
    1u, /*  DIAG_TRIGGER_1           */
    1u, /*  DIAG_TRIGGER_2           */
    1u, /*  DIAG_TRIGGER_3           */
    1u, /*  DIAG_TRIGGER_4           */
    1u, /*  DIAG_TRIGGER_5           */
    1u, /*  DIAG_TRIGGER_6           */
    1u, /*  DIAG_TRIGGER_7           */
    1u, /*  DIAG_SEC_OL_0             */
    1u, /*  DIAG_SEC_OL_1             */
    1u, /*  DIAG_SEC_OL_2             */
    1u, /*  DIAG_SEC_OL_3             */
    1u, /*  DIAG_SEC_OL_4             */
    1u, /*  DIAG_SEC_OL_5             */
    1u, /*  DIAG_SEC_OL_6             */
    1u, /*  DIAG_SEC_OL_7             */
    1u, /*  DIAG_CAN_NODE_OVER_RUN   */
    1u, /*  DIAG_VEHICLE_CAN         */
    1u, /*  DIAG_SEC_0_4             */
    1u, /*  DIAG_SEC_1_5             */
    1u, /*  DIAG_SEC_2_6             */
    1u, /*  DIAG_SEC_3_7             */
    1u, /*  DIAG_VCAP_0_4            */
    1u, /*  DIAG_VCAP_1_5            */
    1u, /*  DIAG_VCAP_2_6            */
    1u, /*  DIAG_VCAP_3_7            */
    1u, /*  DIAG_IGN                 */
    1u, /*  DIAG_FREE_81             */
    1u, /*  DIAG_FREE_82             */
    1u, /*  DIAG_FREE_83             */
    1u, /*  DIAG_FREE_84             */
    1u, /*  DIAG_FREE_85             */
    1u, /*  DIAG_FREE_86             */
    1u, /*  DIAG_FREE_87             */
    1u, /*  DIAG_KEY_SIGNAL          */
    1u, /*  DIAG_GTM                 */
    1u, /*  DIAG_TLE9278BQX          */
    1u, /*  DIAG_ION_CH_A            */
    1u, /*  DIAG_ION_CH_B            */
    1u, /*  DIAG_ION_CH_C            */
    1u, /*  DIAG_ION_CH_D            */
    1u  /*  DIAG_FREE_95             */
};

///Faults confirmation thresholds vector counter
CALQUAL CALQUAL_POST int16_T VTTHRCONFFAULT[DIAG_NUMBER] = 
{
    127, /*  DIAG_T_AIR               */
    127, /*  DIAG_T_WATER             */
    127, /*  DIAG_LOAD                */
    127, /*  DIAG_RPM                 */
    127, /*  DIAG_CAMLEVEL            */
    127, /*  DIAG_FLGBANKSEL          */
    127, /*  DIAG_VBATTERY            */
    127, /*  DIAG_DUMMY               */
    127, /*  DIAG_ADC                 */
    127, /*  DIAG_ION_CH_A            */
    127, /*  DIAG_ION_CH_B            */
    127, /*  DIAG_ION_0               */
    127, /*  DIAG_ION_1               */
    127, /*  DIAG_ION_2               */
    127, /*  DIAG_ION_3               */
    127, /*  DIAG_ION_4               */
    127, /*  DIAG_ION_5               */
    127, /*  DIAG_ION_6               */
    127, /*  DIAG_ION_7               */
    127, /*  DIAG_VBAT_CIRCUIT        */
    127, /*  DIAG_COIL_0              */
    127, /*  DIAG_COIL_1              */
    127, /*  DIAG_COIL_2              */
    127, /*  DIAG_COIL_3              */
    127, /*  DIAG_COIL_4              */
    127, /*  DIAG_COIL_5              */
    127, /*  DIAG_COIL_6              */
    127, /*  DIAG_COIL_7              */
    127, /*  DIAG_ELDOR_SW            */
    127, /*  DIAG_WDT                 */
    127, /*  DIAG_PRI_A               */
    127, /*  DIAG_PRI_B               */
    127, /*  DIAG_LIVENESS            */
    127, /*  DIAG_BANK_MISF           */
    127, /*  DIAG_TEMP_ECU_1          */
    127, /*  DIAG_TEMP_ECU_2          */
    127, /*  DIAG_PRIVATE_CAN         */
    127, /*  DIAG_CAN_NODE_1          */
    127, /*  DIAG_CPU                 */
    127, /*  DIAG_RAM                 */
    127, /*  DIAG_KNOCK_COH_0         */
    127, /*  DIAG_KNOCK_COH_1         */
    127, /*  DIAG_KNOCK_COH_2         */
    127, /*  DIAG_KNOCK_COH_3         */
    127, /*  DIAG_KNOCK_COH_4         */
    127, /*  DIAG_KNOCK_COH_5         */
    127, /*  DIAG_KNOCK_COH_6         */
    127, /*  DIAG_KNOCK_COH_7         */
    127, /*  DIAG_SYNC                */
    127, /*  DIAG_TEMP_ECU_3          */
    127, /*  DIAG_SPARK_EV_A          */
    127, /*  DIAG_SPARK_EV_B          */
    127, /*  DIAG_BUCK_A              */
    127, /*  DIAG_BUCK_B              */
    127, /*  DIAG_TRIGGER_0           */
    127, /*  DIAG_TRIGGER_1           */
    127, /*  DIAG_TRIGGER_2           */
    127, /*  DIAG_TRIGGER_3           */
    127, /*  DIAG_TRIGGER_4           */
    127, /*  DIAG_TRIGGER_5           */
    127, /*  DIAG_TRIGGER_6           */
    127, /*  DIAG_TRIGGER_7           */
    127, /*  DIAG_SEC_OL_0             */
    127, /*  DIAG_SEC_OL_1             */
    127, /*  DIAG_SEC_OL_2             */
    127, /*  DIAG_SEC_OL_3             */
    127, /*  DIAG_SEC_OL_4             */
    127, /*  DIAG_SEC_OL_5             */
    127, /*  DIAG_SEC_OL_6             */
    127, /*  DIAG_SEC_OL_7             */
    127, /*  DIAG_CAN_NODE_OVER_RUN   */
    127, /*  DIAG_VEHICLE_CAN         */
    127, /*  DIAG_SEC_0_4             */
    127, /*  DIAG_SEC_1_5             */
    127, /*  DIAG_SEC_2_6             */
    127, /*  DIAG_SEC_3_7             */
    127, /*  DIAG_VCAP_0_4            */
    127, /*  DIAG_VCAP_1_5            */
    127, /*  DIAG_VCAP_2_6            */
    127, /*  DIAG_VCAP_3_7            */
    127, /*  DIAG_IGN                 */
    127, /*  DIAG_FREE_81             */
    127, /*  DIAG_FREE_82             */
    127, /*  DIAG_FREE_83             */
    127, /*  DIAG_FREE_84             */
    127, /*  DIAG_FREE_85             */
    127, /*  DIAG_FREE_86             */
    127, /*  DIAG_FREE_87             */
    127, /*  DIAG_KEY_SIGNAL          */
    127, /*  DIAG_GTM                 */
    127, /*  DIAG_TLE9278BQX          */
    127, /*  DIAG_ION_CH_A            */
    127, /*  DIAG_ION_CH_B            */
    127, /*  DIAG_ION_CH_C            */
    127, /*  DIAG_ION_CH_D            */
    127  /*  DIAG_FREE_95             */
};

CALQUAL CALQUAL_POST int8_T VTTHRCONFPASSED[DIAG_NUMBER] = 
{
    -128, /*  DIAG_T_AIR               */
    -128, /*  DIAG_T_WATER             */
    -128, /*  DIAG_LOAD                */
    -128, /*  DIAG_RPM                 */
    -128, /*  DIAG_CAMLEVEL            */
    -128, /*  DIAG_FLGBANKSEL          */
    -128, /*  DIAG_VBATTERY            */
    -128, /*  DIAG_DUMMY               */
    -128, /*  DIAG_ADC                 */
    -128, /*  DIAG_ION_CH_A            */
    -128, /*  DIAG_ION_CH_B            */
    -128, /*  DIAG_ION_0               */
    -128, /*  DIAG_ION_1               */
    -128, /*  DIAG_ION_2               */
    -128, /*  DIAG_ION_3               */
    -128, /*  DIAG_ION_4               */
    -128, /*  DIAG_ION_5               */
    -128, /*  DIAG_ION_6               */
    -128, /*  DIAG_ION_7               */
    -128, /*  DIAG_VBAT_CIRCUIT        */
    -128, /*  DIAG_COIL_0              */
    -128, /*  DIAG_COIL_1              */
    -128, /*  DIAG_COIL_2              */
    -128, /*  DIAG_COIL_3              */
    -128, /*  DIAG_COIL_4              */
    -128, /*  DIAG_COIL_5              */
    -128, /*  DIAG_COIL_6              */
    -128, /*  DIAG_COIL_7              */
    -128, /*  DIAG_ELDOR_SW            */
    -128, /*  DIAG_WDT                 */
    -128, /*  DIAG_PRI_A               */
    -128, /*  DIAG_PRI_B               */
    -128, /*  DIAG_LIVENESS            */
    -128, /*  DIAG_BANK_MISF           */
    -128, /*  DIAG_TEMP_ECU_1          */
    -128, /*  DIAG_TEMP_ECU_2          */
    -128, /*  DIAG_PRIVATE_CAN         */
    -128, /*  DIAG_CAN_NODE_1          */
    -128, /*  DIAG_CPU                 */
    -128, /*  DIAG_RAM                 */
    -128, /*  DIAG_KNOCK_COH_0         */
    -128, /*  DIAG_KNOCK_COH_1         */
    -128, /*  DIAG_KNOCK_COH_2         */
    -128, /*  DIAG_KNOCK_COH_3         */
    -128, /*  DIAG_KNOCK_COH_4         */
    -128, /*  DIAG_KNOCK_COH_5         */
    -128, /*  DIAG_KNOCK_COH_6         */
    -128, /*  DIAG_KNOCK_COH_7         */
    -128, /*  DIAG_SYNC                */
    -128, /*  DIAG_TEMP_ECU_3          */
    -128, /*  DIAG_SPARK_EV_A          */
    -128, /*  DIAG_SPARK_EV_B          */
    -128, /*  DIAG_BUCK_A              */
    -128, /*  DIAG_BUCK_B              */
    -128, /*  DIAG_TRIGGER_0           */
    -128, /*  DIAG_TRIGGER_1           */
    -128, /*  DIAG_TRIGGER_2           */
    -128, /*  DIAG_TRIGGER_3           */
    -128, /*  DIAG_TRIGGER_4           */
    -128, /*  DIAG_TRIGGER_5           */
    -128, /*  DIAG_TRIGGER_6           */
    -128, /*  DIAG_TRIGGER_7           */
    -128, /*  DIAG_SEC_OL_0             */
    -128, /*  DIAG_SEC_OL_1             */
    -128, /*  DIAG_SEC_OL_2             */
    -128, /*  DIAG_SEC_OL_3             */
    -128, /*  DIAG_SEC_OL_4             */
    -128, /*  DIAG_SEC_OL_5             */
    -128, /*  DIAG_SEC_OL_6             */
    -128, /*  DIAG_SEC_OL_7             */
    -128, /*  DIAG_CAN_NODE_OVER_RUN   */
    -128, /*  DIAG_VEHICLE_CAN         */
    -128, /*  DIAG_SEC_0_4             */
    -128, /*  DIAG_SEC_1_5             */
    -128, /*  DIAG_SEC_2_6             */
    -128, /*  DIAG_SEC_3_7             */
    -128, /*  DIAG_VCAP_0_4            */
    -128, /*  DIAG_VCAP_1_5            */
    -128, /*  DIAG_VCAP_2_6            */
    -128, /*  DIAG_VCAP_3_7            */
    -128, /*  DIAG_IGN                 */
    -128, /*  DIAG_FREE_81             */
    -128, /*  DIAG_FREE_82             */
    -128, /*  DIAG_FREE_83             */
    -128, /*  DIAG_FREE_84             */
    -128, /*  DIAG_FREE_85             */
    -128, /*  DIAG_FREE_86             */
    -128, /*  DIAG_FREE_87             */
    -128, /*  DIAG_KEY_SIGNAL          */
    -128, /*  DIAG_GTM                 */
    -128, /*  DIAG_TLE9278BQX          */
    -128, /*  DIAG_ION_CH_A            */
    -128, /*  DIAG_ION_CH_B            */
    -128, /*  DIAG_ION_CH_C            */
    -128, /*  DIAG_ION_CH_D            */
    -128  /*  DIAG_FREE_95             */
};

/// Force PtFault value, disabled if 255
CALQUAL CALQUAL_POST uint8_T VTFORCEPTFAULT[DIAG_NUMBER] = 
{
    255u, /*  DIAG_T_AIR               */
    255u, /*  DIAG_T_WATER             */
    255u, /*  DIAG_LOAD                */
    255u, /*  DIAG_RPM                 */
    255u, /*  DIAG_CAMLEVEL            */
    255u, /*  DIAG_FLGBANKSEL          */
    255u, /*  DIAG_VBATTERY            */
    255u, /*  DIAG_DUMMY               */
    255u, /*  DIAG_ADC                 */
    255u, /*  DIAG_ION_CH_A            */
    255u, /*  DIAG_ION_CH_B            */
    255u, /*  DIAG_ION_0               */
    255u, /*  DIAG_ION_1               */
    255u, /*  DIAG_ION_2               */
    255u, /*  DIAG_ION_3               */
    255u, /*  DIAG_ION_4               */
    255u, /*  DIAG_ION_5               */
    255u, /*  DIAG_ION_6               */
    255u, /*  DIAG_ION_7               */
    255u, /*  DIAG_VBAT_CIRCUIT        */
    255u, /*  DIAG_COIL_0              */
    255u, /*  DIAG_COIL_1              */
    255u, /*  DIAG_COIL_2              */
    255u, /*  DIAG_COIL_3              */
    255u, /*  DIAG_COIL_4              */
    255u, /*  DIAG_COIL_5              */
    255u, /*  DIAG_COIL_6              */
    255u, /*  DIAG_COIL_7              */
    255u, /*  DIAG_ELDOR_SW            */
    255u, /*  DIAG_WDT                 */
    255u, /*  DIAG_PRI_A               */
    255u, /*  DIAG_PRI_B               */
    255u, /*  DIAG_LIVENESS            */
    255u, /*  DIAG_BANK_MISF           */
    255u, /*  DIAG_TEMP_ECU_1          */
    255u, /*  DIAG_TEMP_ECU_2          */
    255u, /*  DIAG_PRIVATE_CAN         */
    255u, /*  DIAG_CAN_NODE_1          */
    255u, /*  DIAG_CPU                 */
    255u, /*  DIAG_RAM                 */
    255u, /*  DIAG_KNOCK_COH_0         */
    255u, /*  DIAG_KNOCK_COH_1         */
    255u, /*  DIAG_KNOCK_COH_2         */
    255u, /*  DIAG_KNOCK_COH_3         */
    255u, /*  DIAG_KNOCK_COH_4         */
    255u, /*  DIAG_KNOCK_COH_5         */
    255u, /*  DIAG_KNOCK_COH_6         */
    255u, /*  DIAG_KNOCK_COH_7         */
    255u, /*  DIAG_SYNC                */
    255u, /*  DIAG_TEMP_ECU_3          */
    255u, /*  DIAG_SPARK_EV_A          */
    255u, /*  DIAG_SPARK_EV_B          */
    255u, /*  DIAG_BUCK_A              */
    255u, /*  DIAG_BUCK_B              */
    255u, /*  DIAG_TRIGGER_0           */
    255u, /*  DIAG_TRIGGER_1           */
    255u, /*  DIAG_TRIGGER_2           */
    255u, /*  DIAG_TRIGGER_3           */
    255u, /*  DIAG_TRIGGER_4           */
    255u, /*  DIAG_TRIGGER_5           */
    255u, /*  DIAG_TRIGGER_6           */
    255u, /*  DIAG_TRIGGER_7           */
    255u, /*  DIAG_SEC_OL_0             */
    255u, /*  DIAG_SEC_OL_1             */
    255u, /*  DIAG_SEC_OL_2             */
    255u, /*  DIAG_SEC_OL_3             */
    255u, /*  DIAG_SEC_OL_4             */
    255u, /*  DIAG_SEC_OL_5             */
    255u, /*  DIAG_SEC_OL_6             */
    255u, /*  DIAG_SEC_OL_7             */
    255u, /*  DIAG_CAN_NODE_OVER_RUN   */
    255u, /*  DIAG_VEHICLE_CAN         */
    255u, /*  DIAG_SEC_0_4             */
    255u, /*  DIAG_SEC_1_5             */
    255u, /*  DIAG_SEC_2_6             */
    255u, /*  DIAG_SEC_3_7             */
    255u, /*  DIAG_VCAP_0_4            */
    255u, /*  DIAG_VCAP_1_5            */
    255u, /*  DIAG_VCAP_2_6            */
    255u, /*  DIAG_VCAP_3_7            */
    255u, /*  DIAG_IGN                 */
    255u, /*  DIAG_FREE_81             */
    255u, /*  DIAG_FREE_82             */
    255u, /*  DIAG_FREE_83             */
    255u, /*  DIAG_FREE_84             */
    255u, /*  DIAG_FREE_85             */
    255u, /*  DIAG_FREE_86             */
    255u, /*  DIAG_FREE_87             */
    255u, /*  DIAG_KEY_SIGNAL          */
    255u, /*  DIAG_GTM                 */
    255u, /*  DIAG_TLE9278BQX          */
    255u, /*  DIAG_ION_CH_A            */
    255u, /*  DIAG_ION_CH_B            */
    255u, /*  DIAG_ION_CH_C            */
    255u, /*  DIAG_ION_CH_D            */
    255u  /*  DIAG_FREE_95             */
};
#if (SAVE_ENV_DATA_EE== 1u)
/// DTC severity Mask
CALQUAL CALQUAL_POST uint8_T DTCSEVERITY[DIAG_NUMBER] =
{
    NSA, /*  DIAG_T_AIR               */
    NSA, /*  DIAG_T_WATER             */
    NSA, /*  DIAG_LOAD                */
    NSA, /*  DIAG_RPM                 */
    NSA, /*  DIAG_CAMLEVEL            */
    NSA, /*  DIAG_FLGBANKSEL          */
    NSA, /*  DIAG_VBATTERY            */
    NSA, /*  DIAG_DUMMY               */
    NSA, /*  DIAG_ADC                 */
    NSA, /*  DIAG_ION_CH_A            */
    NSA, /*  DIAG_ION_CH_B            */
    NSA, /*  DIAG_ION_0               */
    NSA, /*  DIAG_ION_1               */
    NSA, /*  DIAG_ION_2               */
    NSA, /*  DIAG_ION_3               */
    NSA, /*  DIAG_ION_4               */
    NSA, /*  DIAG_ION_5               */
    NSA, /*  DIAG_ION_6               */
    NSA, /*  DIAG_ION_7               */
    NSA, /*  DIAG_VBAT_CIRCUIT        */
    NSA, /*  DIAG_COIL_0              */
    NSA, /*  DIAG_COIL_1              */
    NSA, /*  DIAG_COIL_2              */
    NSA, /*  DIAG_COIL_3              */
    NSA, /*  DIAG_COIL_4              */
    NSA, /*  DIAG_COIL_5              */
    NSA, /*  DIAG_COIL_6              */
    NSA, /*  DIAG_COIL_7              */
    NSA, /*  DIAG_ELDOR_SW            */
    NSA, /*  DIAG_WDT                 */
    NSA, /*  DIAG_PRI_A               */
    NSA, /*  DIAG_PRI_B               */
    NSA, /*  DIAG_LIVENESS            */
    NSA, /*  DIAG_BANK_MISF           */
    NSA, /*  DIAG_TEMP_ECU_1          */
    NSA, /*  DIAG_TEMP_ECU_2          */
    NSA, /*  DIAG_PRIVATE_CAN         */
    NSA, /*  DIAG_CAN_NODE_1          */
    NSA, /*  DIAG_CPU                 */
    NSA, /*  DIAG_RAM                 */
    NSA, /*  DIAG_KNOCK_COH_0         */
    NSA, /*  DIAG_KNOCK_COH_1         */
    NSA, /*  DIAG_KNOCK_COH_2         */
    NSA, /*  DIAG_KNOCK_COH_3         */
    NSA, /*  DIAG_KNOCK_COH_4         */
    NSA, /*  DIAG_KNOCK_COH_5         */
    NSA, /*  DIAG_KNOCK_COH_6         */
    NSA, /*  DIAG_KNOCK_COH_7         */
    NSA, /*  DIAG_SYNC                */
    NSA, /*  DIAG_TEMP_ECU_3          */
    NSA, /*  DIAG_SPARK_EV_A          */
    NSA, /*  DIAG_SPARK_EV_B          */
    NSA, /*  DIAG_BUCK_A              */
    NSA, /*  DIAG_BUCK_B              */
    NSA, /*  DIAG_TRIGGER_0           */
    NSA, /*  DIAG_TRIGGER_1           */
    NSA, /*  DIAG_TRIGGER_2           */
    NSA, /*  DIAG_TRIGGER_3           */
    NSA, /*  DIAG_TRIGGER_4           */
    NSA, /*  DIAG_TRIGGER_5           */
    NSA, /*  DIAG_TRIGGER_6           */
    NSA, /*  DIAG_TRIGGER_7           */
    NSA, /*  DIAG_SEC_OL_0             */
    NSA, /*  DIAG_SEC_OL_1             */
    NSA, /*  DIAG_SEC_OL_2             */
    NSA, /*  DIAG_SEC_OL_3             */
    NSA, /*  DIAG_SEC_OL_4             */
    NSA, /*  DIAG_SEC_OL_5             */
    NSA, /*  DIAG_SEC_OL_6             */
    NSA, /*  DIAG_SEC_OL_7             */
    NSA, /*  DIAG_CAN_NODE_OVER_RUN   */
    NSA, /*  DIAG_VEHICLE_CAN         */
    NSA, /*  DIAG_SEC_0_4             */
    NSA, /*  DIAG_SEC_1_5             */
    NSA, /*  DIAG_SEC_2_6             */
    NSA, /*  DIAG_SEC_3_7             */
    NSA, /*  DIAG_VCAP_0_4            */
    NSA, /*  DIAG_VCAP_1_5            */
    NSA, /*  DIAG_VCAP_2_6            */
    NSA, /*  DIAG_VCAP_3_7            */
    NSA, /*  DIAG_IGN                 */
    NSA, /*  DIAG_FREE_81             */
    NSA, /*  DIAG_FREE_82             */
    NSA, /*  DIAG_FREE_83             */
    NSA, /*  DIAG_FREE_84             */
    NSA, /*  DIAG_FREE_85             */
    NSA, /*  DIAG_FREE_86             */
    NSA, /*  DIAG_FREE_87             */
    NSA, /*  DIAG_KEY_SIGNAL          */
    NSA, /*  DIAG_GTM                 */
    NSA, /*  DIAG_TLE9278BQX          */
    NSA, /*  DIAG_ION_CH_A            */
    NSA, /*  DIAG_ION_CH_B            */
    NSA, /*  DIAG_ION_CH_C            */
    NSA, /*  DIAG_ION_CH_D            */
    NSA  /*  DIAG_FREE_95             */
};
#endif
///VBattery MinThr to disable diag 
CALQUAL CALQUAL_POST uint16_T VBATTDISDIAGMINTHR = (uint16_T)(11u*16u);
//FailedOCCntEE threshold to set Confirmed DTC
CALQUAL CALQUAL_POST uint8_T CONFDTCTHR = 3u;
//FailedOCCntEE incremental step
CALQUAL CALQUAL_POST uint8_T CONFDTCINC = 3u;
/// Force PtFault value mode, 0 = @ normal execution task, 1 = @ 10Ms Task
CALQUAL CALQUAL_POST uint8_T FORCEPTFAULT10MS = 0u;
// Reset Diagnosis on transition 0 -> 1
CALQUAL CALQUAL_POST uint8_T FORCERESETDIAG = 0u; /* */
// Reset Diagnosis enable rpm THR
CALQUAL CALQUAL_POST uint16_T FORCERESETDIAGTHR = 800u; /* */

#endif // _BUILD_DIAGMGM_

