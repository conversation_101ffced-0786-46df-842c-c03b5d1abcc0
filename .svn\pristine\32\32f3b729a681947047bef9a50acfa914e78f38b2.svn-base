/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           CombCtrl.c
 **  File Creation Date: 16-Oct-2018
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         CombCtrl
 **  Model Description:
 **  Model Version:      1.1908
 **  Model Author:       <PERSON> - Wed Jul 01 14:50:43 2009
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: SantoroR - Tue Oct 16 15:23:04 2018
 **
 **  Last Saved Modification:   -
 **
 **
 *******************************************************************************
 **/

#include "CombCtrl.h"
#include "CombCtrl_private.h"

/*  Defines */

/* Named constants for Chart: '<S15>/RecAdaptEnable_Flag' */
#define CombCtrl_IN_ADAPT_DISABLE      ((uint8_T)1U)
#define CombCtrl_IN_ADAPT_ENABLE       ((uint8_T)2U)

/* Named constants for Chart: '<S17>/Median' */
#define CombCtrl_IN_BUF                ((uint8_T)1U)
#define CombCtrl_IN_RESET              ((uint8_T)2U)

/*  Data Types */

/* user code (top of source file) */
/* System '<Root>/CombCtrl' */
#ifdef _BUILD_COMBCTRL_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/**************************** GLOBAL DATA *************************************/
/*  Definitions */

/* Exported block signals */
int16_T eta_cylbal;                    /* '<S38>/Calc_indices_ad' */
int16_T csi_cylbal;                    /* '<S38>/Calc_indices_ad' */
uint8_T sstab_load_lam;                /* '<S72>/SteadyStateDetect' */
uint8_T ofsrCylBal;                    /* '<S38>/Product' */
uint8_T indr_cylbal;                   /* '<S38>/Calc_indices_ad' */
uint8_T indc_cylbal;                   /* '<S38>/Calc_indices_ad' */
uint8_T TriggerCylBalAdat;             /* '<S31>/Switch' */
uint8_T EndCylBalLearnCyl;             /* '<S33>/Relational Operator' */

/* Block signals and states (auto storage) */
D_Work_CombCtrl CombCtrl_DWork;

/* Previous zero-crossings (trigger) states */
PrevZCSigStates_CombCtrl CombCtrl_PrevZCSigState;

/* External inputs (root inport signals with auto storage) */
ExternalInputs_CombCtrl CombCtrl_U;

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint16_T AvgFFS[8];                    /* Average FFS */
uint16_T AvgFFSBank;                   /* Bank average FFS */
uint32_T Avg_HR_FFS[8];                /* Average Filtered FFS */
uint32_T Avg_HR_old_FFS[8];            /* Average Filtered FFS[n -1] */
uint8_T CylBalEn;                      /* Cylinder in balance enable */
uint16_T CylBalIndAvgCyl;              /* Mean Cylinder balancing Individual correction */
int16_T D1LamFil;                      /* D1 Filter Parameter */
int16_T D2LamFil;                      /* D2 Filter Parameter */
uint8_T EnFilButter;                   /* Enable Butterworth Filter */
uint16_T FFSMedian;                    /* Median FFS */
uint8_T FStabLoadCylBalAd;             /* Load stable flag for cylinder balancing */
uint8_T FStabLoadCylBalStateAd;        /* Stability state flag for cylinder balancing adaptative correction */
uint8_T FStabRpmCylBalAd;              /* RpmF stable flag for cylinder balancing */
uint8_T FStabRpmCylBalStateAd;         /* Stability state flag for cylinder balancing adaptative correction */
int16_T FfsErrThr;                     /* Cylinder in balance disable treshold */
uint8_T FiltLamEnable;                 /* Filter Lambda Enable */
uint8_T FiltLamFreeze;                 /* Filter Lambda Freeze */
uint8_T FiltParReset;                  /* Filter Parameter Reset */
uint8_T FlgRecAdCylBalEn;              /* Rec Enable flag for cylinder balancingcorrection  */
uint8_T FlgSteadyStateAd;              /* Steady state flag for cylinder balancing adaptative correction */
int16_T FreqNorm;                      /* Rate freq */
uint32_T InSum_FFS[8];                 /* Filter Input sum */
uint32_T In_x_N0_FFS[8];               /* N0 x FFS */
uint16_T LoadEnInjCorr;                /* Inj correction enable load threshold */
uint16_T N0LamFil;                     /* N0 Filter Parameter */
uint8_T StabLoadLamTr;                 /* Stab Load Lam */
uint8_T StabRpmLamTr;                  /* Stab Rpm Lam */
uint16_T VtCntCylBalLearn[8];          /* Cylinder balancing learning counter */
uint32_T VtCylBalIndSum[8];            /* Cylinder balancing accumulator */
uint8_T VtCylBalLearnState[8];         /* Cylinder balancing learning state 0 = Normal, 1 = Learning */
uint8_T VtCylBalTrigAdat[8];           /* Cylinder balancing trigger adaptativity */
uint8_T VtEndCylBalLearn[8];           /* Cylinder balancing adaptativity learning end */
int16_T VtFFSErr[8];                   /* FFS error after Deadzone filter */
int16_T VtFFSInErr[8];                 /* FFS error before Deadzone filter */
uint16_T VtInjCorrCyl[8];              /* Cylinder FFS Correction */
uint16_T VtInjCorrCylAd[8];            /* Cylinder FFS Correction Adaptative */
uint16_T VtInjCorrCylTot[8];           /* Cylinder FFS Correction */
uint8_T VtMisfCount[8];                /* Misfire counter */
int32_T VtPiFFS[8];                    /* Cyl Bal Pi */
uint8_T sstab_rpm_lam;                 /* Stab Lambda rpm state */

/*  Declarations  */

/***************************** FILE SCOPE DATA ********************************/

/*************************** FUNCTIONS ****************************************/

/* Output and update for function-call system: '<S1>/Init' */
void CombCtrl_Init(void)
{
  uint8_T idx;

  /* DataStoreWrite: '<S12>/Data Store Write1' incorporates:
   *  Constant: '<S12>/ONE1'
   */
  FiltLamEnable = 1U;

  /* DataStoreWrite: '<S12>/Data Store Write10' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  FlgRecAdCylBalEn = 0U;

  /* DataStoreWrite: '<S12>/Data Store Write11' */
  AvgFFSBank = CombCtrl_ConstB.GatewayIn3;

  /* DataStoreWrite: '<S12>/Data Store Write13' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  FStabRpmCylBalStateAd = 0U;

  /* DataStoreWrite: '<S12>/Data Store Write14' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  FStabLoadCylBalStateAd = 0U;

  /* DataStoreWrite: '<S12>/Data Store Write2' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  FiltLamFreeze = 0U;

  /* DataStoreWrite: '<S12>/Data Store Write3' */
  LoadEnInjCorr = CombCtrl_ConstB.GatewayIn5;

  /* DataStoreWrite: '<S12>/Data Store Write4' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  CylBalEn = 0U;

  /* DataStoreWrite: '<S12>/Data Store Write5' */
  FfsErrThr = CombCtrl_ConstB.GatewayIn1;

  /* DataStoreWrite: '<S12>/Data Store Write6' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  FStabLoadCylBalAd = 0U;

  /* DataStoreWrite: '<S12>/Data Store Write7' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  FStabRpmCylBalAd = 0U;

  /* DataStoreWrite: '<S12>/Data Store Write8' incorporates:
   *  Constant: '<S12>/ZERO1'
   */
  FlgSteadyStateAd = 0U;

  /* DataStoreWrite: '<S12>/Data Store Write9' */
  CylBalIndAvgCyl = CombCtrl_ConstB.GatewayIn2;

  /* Chart: '<S12>/Lam_Vector_Reset' */
  /* Gateway: CombCtrl/Init/Lam_Vector_Reset */
  /* During: CombCtrl/Init/Lam_Vector_Reset */
  /* Entry Internal: CombCtrl/Init/Lam_Vector_Reset */
  /* Transition: '<S94>:1' */
  for (idx = 0U; idx < ((uint8_T)N_CYL_MAX); idx++) {
    /* Transition: '<S94>:2' */
    AvgFFS[(idx)] = 0U;
    Avg_HR_FFS[(idx)] = 0U;
    Avg_HR_old_FFS[(idx)] = 0U;
    In_x_N0_FFS[(idx)] = 0U;
    InSum_FFS[(idx)] = 0U;
    VtInjCorrCyl[(idx)] = 32768U;
    VtInjCorrCylTot[(idx)] = 32768U;
    VtPiFFS[(idx)] = 0;
    VtInjCorrCylAd[(idx)] = 32768U;
    VtFFSErr[(idx)] = 0;
    VtFFSInErr[(idx)] = 0;
    VtEndCylBalLearn[(idx)] = 0U;
    VtCntCylBalLearn[(idx)] = 0U;
    VtCylBalLearnState[(idx)] = 0U;
    VtCylBalIndSum[(idx)] = 0U;
    VtCylBalTrigAdat[(idx)] = 0U;
    VtMisfCount[(idx)] = 0U;

    /* Transition: '<S94>:4' */
  }

  /* End of Chart: '<S12>/Lam_Vector_Reset' */
  /* Transition: '<S94>:3' */
}

/* Output and update for function-call system: '<S38>/Write_TbCylBalAd' */
void CombCtrl_Write_TbCylBalAd(uint16_T rtu_CylBalIndAvgCyl, uint8_T
  rtu_indr_cylbal, uint8_T rtu_indc_cylbal, int16_T rtu_eta_cylbal, int16_T
  rtu_csi_cylbal, uint16_T rtu_RtRpmCylBal, uint16_T rtu_RtLoadCylBal)
{
  uint16_T rtb_Sum5;
  int16_T rtb_Product3_k;
  int16_T rtb_Product4;
  uint16_T rtb_Product_l;
  int32_T tmp;

  /* S-Function (Look2D_U16_U16_U16): '<S51>/Look2D_U16_U16_U16' incorporates:
   *  Constant: '<S46>/BKETACSICYLBALAD'
   *  Constant: '<S46>/TBCYLBALGNAD'
   *  Constant: '<S48>/BKETACSICYLBALAD_dim'
   */
  Look2D_U16_U16_U16(&rtb_Sum5, (uint16_T*)&TBCYLBALGNAD[0], rtu_RtRpmCylBal,
                     (uint16_T*)&BKETACSICYLBALAD[0], ((uint8_T)
    BKETACSICYLBALAD_dim), rtu_RtLoadCylBal, (uint16_T*)&BKETACSICYLBALAD[0],
                     ((uint8_T)BKETACSICYLBALAD_dim));

  /* Product: '<S46>/Product2' incorporates:
   *  Product: '<S46>/Product1'
   */
  rtb_Product4 = (int16_T)((((int16_T)((rtu_eta_cylbal * rtu_csi_cylbal) >> 10))
    * rtb_Sum5) >> 11);

  /* DataTypeConversion: '<S46>/Data Type Conversion2' incorporates:
   *  Constant: '<S46>/MAXCYLBALADWEIGHT'
   */
  rtb_Product3_k = (int16_T)(((uint32_T)MAXCYLBALADWEIGHT) >> 1);

  /* MinMax: '<S46>/MinMax' */
  if (rtb_Product3_k < rtb_Product4) {
    rtb_Product4 = rtb_Product3_k;
  }

  /* End of MinMax: '<S46>/MinMax' */

  /* Product: '<S46>/Product4' incorporates:
   *  Constant: '<S46>/CYLBALGNAD'
   *  Constant: '<S46>/ONE2'
   *  Product: '<S46>/Product3'
   *  Sum: '<S46>/Sum3'
   */
  tmp = (((((rtu_CylBalIndAvgCyl - 32768) * CYLBALGNAD) >> 16) * rtb_Product4) >>
         3);
  if (tmp > 32767) {
    tmp = 32767;
  } else {
    if (tmp < -32768) {
      tmp = -32768;
    }
  }

  /* Product: '<S46>/Product' incorporates:
   *  Constant: '<S46>/ONE3'
   *  DataStoreRead: '<S46>/Data Store Read8'
   *  Product: '<S46>/Product4'
   *  Selector: '<S46>/Selector3'
   *  Sum: '<S46>/Sum2'
   */
  rtb_Product_l = (uint16_T)((((uint32_T)TbInjCorrCylAd[(96 * rtu_indc_cylbal) +
    rtu_indr_cylbal]) * (32768 + tmp)) >> 15);

  /* Sum: '<S46>/Sum5' incorporates:
   *  Constant: '<S46>/MAXADDCYLBALAD'
   *  Constant: '<S46>/ONE'
   */
  rtb_Sum5 = (uint16_T)(MAXADDCYLBALAD + 32768U);

  /* Switch: '<S49>/Switch2' incorporates:
   *  RelationalOperator: '<S49>/LowerRelop1'
   */
  if (!(rtb_Product_l > rtb_Sum5)) {
    /* Sum: '<S46>/Sum1' incorporates:
     *  Constant: '<S46>/MAXADDCYLBALAD'
     *  Constant: '<S46>/ONE1'
     */
    rtb_Sum5 = (uint16_T)(32768 - MAXADDCYLBALAD);

    /* Switch: '<S49>/Switch' incorporates:
     *  RelationalOperator: '<S49>/UpperRelop'
     */
    if (!(rtb_Product_l < rtb_Sum5)) {
      rtb_Sum5 = rtb_Product_l;
    }

    /* End of Switch: '<S49>/Switch' */
  }

  /* End of Switch: '<S49>/Switch2' */

  /* Chart: '<S47>/Assign_TbInjCorrCylAd' */
  /* Gateway: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/Assign_TbInjCorrCylAd/Assign_TbInjCorrCylAd */
  /* During: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/Assign_TbInjCorrCylAd/Assign_TbInjCorrCylAd */
  /* Entry Internal: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Write_TbCylBalAd/Assign_TbInjCorrCylAd/Assign_TbInjCorrCylAd */
  /* Transition: '<S50>:1' */
  TbInjCorrCylAd[rtu_indr_cylbal + (96 * rtu_indc_cylbal)] = rtb_Sum5;
}

/* System initialize for function-call system: '<S1>/EOA' */
void CombCtrl_EOA_Init(void)
{
  /* SystemInitialize for Atomic SubSystem: '<S17>/Filtering' */
  /* InitializeConditions for Memory: '<S75>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput_b = MAX_uint16_T;

  /* End of SystemInitialize for SubSystem: '<S17>/Filtering' */
}

/* Output and update for function-call system: '<S1>/EOA' */
void CombCtrl_EOA(void)
{
  /* local block i/o variables */
  int16_T rtb_LookUp_S16_U16;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_d;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_h;
  uint16_T rtb_SteadyStateDetect_o3;
  uint16_T rtb_SteadyStateDetect_o4;
  uint16_T rtb_SteadyStateDetect_o3_j;
  uint16_T rtb_SteadyStateDetect_o4_p;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_n;
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint16_T rtb_SigStab_o3_d;
  uint16_T rtb_SigStab_o4_e;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_b;
  uint16_T rtb_LookUp_U16_U16;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_Look2D_IR_U16;
  uint16_T rtb_Look2D_IR_U16_f;
  uint8_T rtb_SigStab_o2;
  uint8_T rtb_SigStab_o2_d;
  uint8_T idx1;
  uint8_T idx2;
  uint8_T idx_median;
  uint16_T temp;
  uint16_T rtb_Mul1;
  boolean_T rtb_FlgSteadyState;
  int16_T rtb_Add1_e;
  uint32_T rtb_inputnum0;
  uint32_T rtb_DataTypeConversion4;
  int32_T rtb_DataTypeConversion3;
  uint16_T rtb_Memory;
  int16_T rtb_Memory_a;
  uint16_T rtb_Memory1;
  int32_T rtb_RateLimiter_S32;
  int32_T i;
  int8_T tmp;
  boolean_T tmp_0;
  uint8_T idx2_tmp;
  uint16_T *rtb_DataStoreRead4_ie_0;
  int32_T exitg1;

  /* Switch: '<S11>/Switch' incorporates:
   *  Constant: '<S11>/COMBCTRLINSEL'
   *  Constant: '<S11>/INTION2FFS'
   *  Inport: '<Root>/FFS'
   *  Inport: '<Root>/IntIon'
   *  Product: '<S11>/Product'
   */
  for (i = 0; i < 8; i++) {
    if (COMBCTRLINSEL != 0) {
      CombCtrl_DWork.Switch_l[i] = (uint16_T)((((uint32_T)IntIon[(i)]) *
        INTION2FFS) >> 11);
    } else {
      CombCtrl_DWork.Switch_l[i] = FFS[(i)];
    }
  }

  /* End of Switch: '<S11>/Switch' */

  /* Chart: '<S17>/Median' incorporates:
   *  DataStoreRead: '<S17>/Data Store Read14'
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  /* Gateway: CombCtrl/EOA/AvgFFS_Calc/Median */
  /* During: CombCtrl/EOA/AvgFFS_Calc/Median */
  if (CombCtrl_DWork.bitsForTID0.is_active_c3_CombCtrl == 0U) {
    /* Entry: CombCtrl/EOA/AvgFFS_Calc/Median */
    CombCtrl_DWork.bitsForTID0.is_active_c3_CombCtrl = 1U;

    /* Entry Internal: CombCtrl/EOA/AvgFFS_Calc/Median */
    /* Transition: '<S62>:3' */
    for (idx1 = 0U; idx1 < LENBUFMED; idx1++) {
      /* Transition: '<S62>:8' */
      for (idx2 = 0U; idx2 < ((uint8_T)N_CYLINDER); idx2++) {
        /* Transition: '<S62>:9' */
        CombCtrl_DWork.Buffer[idx1 + (7 * idx2)] =
          CombCtrl_DWork.Switch_l[IonAbsTdcEOA];
        CombCtrl_DWork.Index[idx2] = 0U;
      }

      /* Transition: '<S62>:10' */
    }

    /* Transition: '<S62>:7' */
    CombCtrl_DWork.median = CombCtrl_DWork.Switch_l[IonAbsTdcEOA];
    CombCtrl_DWork.bitsForTID0.is_c3_CombCtrl = CombCtrl_IN_BUF;
  } else if (CombCtrl_DWork.bitsForTID0.is_c3_CombCtrl == CombCtrl_IN_BUF) {
    /* During 'BUF': '<S62>:1' */
    /* Transition: '<S62>:11' */
    if (!(FiltLamEnable != 0)) {
      /* Transition: '<S62>:12' */
      CombCtrl_DWork.bitsForTID0.is_c3_CombCtrl = CombCtrl_IN_RESET;
    } else {
      /* Transition: '<S62>:24' */
      if (FiltLamFreeze == 0) {
        /* Transition: '<S62>:4' */
        CombCtrl_DWork.Buffer[CombCtrl_DWork.Index[IonAbsTdcEOA] + (7 *
          IonAbsTdcEOA)] = CombCtrl_DWork.Switch_l[IonAbsTdcEOA];
      } else {
        /* Transition: '<S62>:23' */
      }

      if (CombCtrl_DWork.Index[IonAbsTdcEOA] < (LENBUFMED - 1)) {
        /* Transition: '<S62>:6' */
        CombCtrl_DWork.Index[IonAbsTdcEOA] = (uint8_T)
          (CombCtrl_DWork.Index[IonAbsTdcEOA] + 1);
      } else {
        /* Transition: '<S62>:5' */
        CombCtrl_DWork.Index[IonAbsTdcEOA] = 0U;
      }

      /* Transition: '<S62>:21' */
      for (idx1 = 0U; idx1 < LENBUFMED; idx1++) {
        /* Transition: '<S62>:22' */
        CombCtrl_DWork.VettSort[idx1] = CombCtrl_DWork.Buffer[(7 * IonAbsTdcEOA)
          + idx1];
      }

      /* Transition: '<S62>:14' */
      idx1 = 0U;
      idx_median = (uint8_T)(((uint32_T)LENBUFMED) >> 1);
      while (idx1 <= idx_median) {
        /* Transition: '<S62>:15' */
        idx2_tmp = (uint8_T)(idx1 + 1);
        idx2 = idx2_tmp;
        while (idx2 < LENBUFMED) {
          /* Transition: '<S62>:16' */
          if (CombCtrl_DWork.VettSort[idx2] < CombCtrl_DWork.VettSort[idx1]) {
            /* Transition: '<S62>:17' */
            temp = CombCtrl_DWork.VettSort[idx2];
            CombCtrl_DWork.VettSort[idx2] = CombCtrl_DWork.VettSort[idx1];
            CombCtrl_DWork.VettSort[idx1] = temp;
            idx2++;
          } else {
            /* Transition: '<S62>:18' */
            idx2++;
          }
        }

        /* Transition: '<S62>:19' */
        idx1 = idx2_tmp;
      }

      /* Transition: '<S62>:20' */
      CombCtrl_DWork.median = CombCtrl_DWork.VettSort[idx_median];
      CombCtrl_DWork.bitsForTID0.is_c3_CombCtrl = CombCtrl_IN_BUF;
    }
  } else {
    /* During 'RESET': '<S62>:2' */
    if (FiltLamEnable != 0) {
      /* Transition: '<S62>:13' */
      for (idx1 = 0U; idx1 < LENBUFMED; idx1++) {
        /* Transition: '<S62>:8' */
        for (idx2 = 0U; idx2 < ((uint8_T)N_CYLINDER); idx2++) {
          /* Transition: '<S62>:9' */
          CombCtrl_DWork.Buffer[idx1 + (7 * idx2)] =
            CombCtrl_DWork.Switch_l[IonAbsTdcEOA];
          CombCtrl_DWork.Index[idx2] = 0U;
        }

        /* Transition: '<S62>:10' */
      }

      /* Transition: '<S62>:7' */
      CombCtrl_DWork.median = CombCtrl_DWork.Switch_l[IonAbsTdcEOA];
      CombCtrl_DWork.bitsForTID0.is_c3_CombCtrl = CombCtrl_IN_BUF;
    }
  }

  /* End of Chart: '<S17>/Median' */

  /* Outputs for Atomic SubSystem: '<S17>/FiltPar_calc' */
  /* S-Function (PreLookUpIdSearch_U16): '<S70>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S63>/BKRPMLAMFIL'
   *  Constant: '<S63>/BKRPMLAMFIL_dim'
   *  Inport: '<Root>/Rpm'
   */
  PreLookUpIdSearch_U16(&rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, Rpm, (uint16_T*)
                        &BKRPMLAMFIL[0], ((uint8_T)BKRPMLAMFIL_dim));

  /* Sum: '<S63>/Add4' incorporates:
   *  Constant: '<S63>/RPMHYSTFN'
   *  Inport: '<Root>/Rpm'
   */
  temp = (uint16_T)(((uint32_T)Rpm) + RPMHYSTFN);

  /* S-Function (PreLookUpIdSearch_U16): '<S69>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S63>/BKRPMLAMFIL'
   *  Constant: '<S63>/BKRPMLAMFIL_dim'
   */
  PreLookUpIdSearch_U16(&rtb_PreLookUpIdSearch_U16_o1_d,
                        &rtb_PreLookUpIdSearch_U16_o2_h, temp, (uint16_T*)
                        &BKRPMLAMFIL[0], ((uint8_T)BKRPMLAMFIL_dim));

  /* Chart: '<S63>/Chart' */
  /* Gateway: CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/FilterMgm/Chart */
  /* During: CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/FilterMgm/Chart */
  /* Entry Internal: CombCtrl/EOA/AvgFFS_Calc/FiltPar_calc/FilterMgm/Chart */
  /* Transition: '<S68>:1' */
  if ((rtb_PreLookUpIdSearch_U16_o1 == rtb_PreLookUpIdSearch_U16_o1_d) ||
      ((rtb_PreLookUpIdSearch_U16_o1 != CombCtrl_DWork.i_out) &&
       (rtb_PreLookUpIdSearch_U16_o1_d != CombCtrl_DWork.i_out))) {
    /* Transition: '<S68>:2' */
    CombCtrl_DWork.i_out = rtb_PreLookUpIdSearch_U16_o1;
  } else {
    /* Transition: '<S68>:3' */
  }

  /* End of Chart: '<S63>/Chart' */

  /* Selector: '<S63>/Selector2' incorporates:
   *  Constant: '<S63>/VTENFILBUTTER'
   */
  EnFilButter = VTENFILBUTTER[CombCtrl_DWork.i_out];

  /* Product: '<S67>/Product' incorporates:
   *  Constant: '<S67>/RPM_2_PERC'
   *  Inport: '<Root>/Rpm'
   */
  temp = (uint16_T)((((uint8_T)RPM_2_PERC) == 0U) ? MAX_uint32_T : ((((uint32_T)
    Rpm) << 7) / ((uint8_T)RPM_2_PERC)));

  /* UnitDelay: '<S67>/Unit Delay' */
  idx1 = CombCtrl_DWork.UnitDelay_DSTATE;

  /* Memory: '<S73>/Memory1' */
  rtb_Memory = CombCtrl_DWork.Memory1_PreviousInput;

  /* Memory: '<S73>/Memory' */
  rtb_Mul1 = CombCtrl_DWork.Memory_PreviousInput;

  /* S-Function (SteadyStateDetect): '<S73>/SteadyStateDetect' incorporates:
   *  Constant: '<S67>/Constant'
   *  Constant: '<S67>/TDCSTABRPMLAMTR'
   *  Constant: '<S67>/THRSTABRPMLAMTR'
   */
  SteadyStateDetect( (&(StabRpmLamTr)), (&(sstab_rpm_lam)),
                    &rtb_SteadyStateDetect_o3, &rtb_SteadyStateDetect_o4,
                    (uint16_T)temp, (uint8_T)((uint8_T)0U), (uint16_T)
                    THRSTABRPMLAMTR, (uint16_T)TDCSTABRPMLAMTR, (uint8_T)idx1,
                    (uint16_T)rtb_Memory, (uint16_T)rtb_Mul1);

  /* UnitDelay: '<S66>/Unit Delay' */
  idx1 = CombCtrl_DWork.UnitDelay_DSTATE_g;

  /* Memory: '<S72>/Memory1' */
  rtb_Mul1 = CombCtrl_DWork.Memory1_PreviousInput_e;

  /* Memory: '<S72>/Memory' */
  rtb_Memory = CombCtrl_DWork.Memory_PreviousInput_c;

  /* S-Function (SteadyStateDetect): '<S72>/SteadyStateDetect' incorporates:
   *  Constant: '<S66>/Constant'
   *  Constant: '<S66>/TDCSTABLOADLAMTR'
   *  Constant: '<S66>/THRSTABLOADLAMTR'
   *  Inport: '<Root>/Load'
   */
  SteadyStateDetect( (&(StabLoadLamTr)), &sstab_load_lam,
                    &rtb_SteadyStateDetect_o3_j, &rtb_SteadyStateDetect_o4_p,
                    (uint16_T)Load, (uint8_T)((uint8_T)0U), (uint16_T)
                    THRSTABLOADLAMTR, (uint16_T)TDCSTABLOADLAMTR, (uint8_T)idx1,
                    (uint16_T)rtb_Mul1, (uint16_T)rtb_Memory);

  /* Logic: '<S60>/Logical Operator' */
  rtb_FlgSteadyState = ((StabRpmLamTr != 0) && (StabLoadLamTr != 0));

  /* Switch: '<S63>/Switch' incorporates:
   *  Constant: '<S63>/FREQNORMLAMTR'
   *  Constant: '<S63>/VTFREQNORMCUT'
   *  Selector: '<S63>/Selector'
   */
  if (rtb_FlgSteadyState) {
    rtb_Add1_e = VTFREQNORMCUT[CombCtrl_DWork.i_out];
  } else {
    rtb_Add1_e = FREQNORMLAMTR;
  }

  /* End of Switch: '<S63>/Switch' */

  /* Memory: '<S60>/Memory' */
  rtb_Memory_a = CombCtrl_DWork.Memory_PreviousInput_l;

  /* S-Function (RateLimiter_S16): '<S65>/RateLimiter_S16' incorporates:
   *  Constant: '<S60>/FREQRATEMAX'
   *  Constant: '<S60>/FREQRATEMIN'
   */
  RateLimiter_S16(&rtb_Add1_e, rtb_Add1_e, rtb_Memory_a, FREQRATEMIN,
                  FREQRATEMAX);

  /* DataTypeConversion: '<S71>/Conversion' */
  FreqNorm = rtb_Add1_e;

  /* Product: '<S64>/Mul1' */
  rtb_Mul1 = (uint16_T)((FreqNorm * FreqNorm) >> 14);

  /* DataTypeConversion: '<S64>/Data Type Conversion1' incorporates:
   *  Constant: '<S64>/D1LAM_POLY_P1'
   *  Constant: '<S64>/D1LAM_POLY_P2'
   *  Constant: '<S64>/D1LAM_POLY_P3'
   *  Product: '<S64>/Mul2'
   *  Product: '<S64>/Mul3'
   *  Sum: '<S64>/Add2'
   *  Sum: '<S64>/Add3'
   */
  D1LamFil = (int16_T)(((((rtb_Mul1 * ((int16_T)D1LAM_POLY_P1)) << 4) +
    (FreqNorm * ((int16_T)D1LAM_POLY_P2))) + D1LAM_POLY_P3) >> 8);

  /* DataTypeConversion: '<S64>/Data Type Conversion2' incorporates:
   *  Constant: '<S64>/D2LAM_POLY_P1'
   *  Constant: '<S64>/D2LAM_POLY_P2'
   *  Constant: '<S64>/D2LAM_POLY_P3'
   *  Product: '<S64>/Mul4'
   *  Product: '<S64>/Mul5'
   *  Sum: '<S64>/Add4'
   *  Sum: '<S64>/Add5'
   */
  D2LamFil = (int16_T)(((((rtb_Mul1 * ((int16_T)D2LAM_POLY_P1)) << 4) +
    (FreqNorm * ((int16_T)D2LAM_POLY_P2))) + D2LAM_POLY_P3) >> 8);

  /* Sum: '<S64>/Add1' incorporates:
   *  Constant: '<S64>/one'
   *  Sum: '<S64>/Add'
   */
  rtb_Add1_e = (int16_T)(((int16_T)(D1LamFil + D2LamFil)) + 16384);

  /* Product: '<S64>/Divide' */
  if (rtb_Add1_e <= 0) {
    N0LamFil = 0U;
  } else if (rtb_Add1_e > 4095) {
    N0LamFil = MAX_uint16_T;
  } else {
    N0LamFil = (uint16_T)(rtb_Add1_e << 4);
  }

  /* End of Product: '<S64>/Divide' */

  /* RelationalOperator: '<S60>/Relational Operator' */
  FiltParReset = (uint8_T)(FreqNorm != rtb_Memory_a);

  /* Update for UnitDelay: '<S67>/Unit Delay' */
  CombCtrl_DWork.UnitDelay_DSTATE = sstab_rpm_lam;

  /* Update for Memory: '<S73>/Memory1' */
  CombCtrl_DWork.Memory1_PreviousInput = rtb_SteadyStateDetect_o3;

  /* Update for Memory: '<S73>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput = rtb_SteadyStateDetect_o4;

  /* Update for UnitDelay: '<S66>/Unit Delay' */
  CombCtrl_DWork.UnitDelay_DSTATE_g = sstab_load_lam;

  /* Update for Memory: '<S72>/Memory1' */
  CombCtrl_DWork.Memory1_PreviousInput_e = rtb_SteadyStateDetect_o3_j;

  /* Update for Memory: '<S72>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput_c = rtb_SteadyStateDetect_o4_p;

  /* Update for Memory: '<S60>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput_l = FreqNorm;

  /* End of Outputs for SubSystem: '<S17>/FiltPar_calc' */

  /* Outputs for Atomic SubSystem: '<S17>/Filtering' */
  /* Product: '<S75>/Product5' incorporates:
   *  Memory: '<S75>/Memory'
   */
  rtb_DataTypeConversion4 = (CombCtrl_DWork.Memory_PreviousInput_b == 0U) ?
    MAX_uint32_T : ((((uint32_T)N0LamFil) << 8) /
                    CombCtrl_DWork.Memory_PreviousInput_b);
  if (rtb_DataTypeConversion4 > 65535U) {
    rtb_DataTypeConversion4 = 65535U;
  }

  temp = (uint16_T)rtb_DataTypeConversion4;

  /* End of Product: '<S75>/Product5' */
  for (i = 0; i < 8; i++) {
    /* Switch: '<S75>/Switch' incorporates:
     *  DataStoreRead: '<S17>/Data Store Read7'
     *  DataStoreRead: '<S17>/Data Store Read8'
     *  Product: '<S75>/Product1'
     *  Product: '<S75>/Product2'
     *  Switch: '<S75>/Switch1'
     */
    if (FiltParReset != 0) {
      /* Product: '<S75>/Product1' incorporates:
       *  DataStoreRead: '<S17>/Data Store Read7'
       *  DataTypeConversion: '<S75>/Data Type Conversion2'
       */
      rtb_DataTypeConversion4 = (((In_x_N0_FFS[(i)] >> 16) * temp) >> 4);
      if (rtb_DataTypeConversion4 > 65535U) {
        rtb_DataTypeConversion4 = 65535U;
      }

      CombCtrl_DWork.Switch_m[i] = (rtb_DataTypeConversion4 << 12);

      /* Product: '<S75>/Product2' incorporates:
       *  DataStoreRead: '<S17>/Data Store Read8'
       *  DataTypeConversion: '<S75>/Data Type Conversion1'
       *  Product: '<S75>/Product1'
       */
      rtb_DataTypeConversion4 = (((InSum_FFS[(i)] >> 16) * temp) >> 4);
      if (rtb_DataTypeConversion4 > 65535U) {
        rtb_DataTypeConversion4 = 65535U;
      }

      CombCtrl_DWork.Switch1_g[i] = (rtb_DataTypeConversion4 << 12);
    } else {
      CombCtrl_DWork.Switch_m[i] = In_x_N0_FFS[(i)];
      CombCtrl_DWork.Switch1_g[i] = InSum_FFS[(i)];
    }

    /* End of Switch: '<S75>/Switch' */
  }

  /* Outputs for Enabled SubSystem: '<S74>/Butterworth_2order_LOWPASS' incorporates:
   *  EnablePort: '<S76>/Enable'
   */
  /* DataStoreRead: '<S17>/Data Store Read2' */
  if (FiltLamEnable > 0) {
    /* Product: '<S76>/Product7' */
    rtb_inputnum0 = ((uint32_T)CombCtrl_DWork.median) * N0LamFil;

    /* Switch: '<S76>/Switch2' incorporates:
     *  DataStoreRead: '<S17>/Data Store Read3'
     *  DataStoreRead: '<S17>/Data Store Read6'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Product: '<S76>/Product7'
     *  Selector: '<S61>/Selector_InputSum'
     *  Selector: '<S61>/Selector_Input_x_N0'
     *  Selector: '<S61>/Selector_Outold1'
     *  Selector: '<S61>/Selector_Outold2'
     *  Sum: '<S76>/HeadSum1'
     *  Switch: '<S75>/Switch2'
     *  Switch: '<S76>/Switch'
     *  Switch: '<S76>/Switch1'
     *  Switch: '<S76>/Switch3'
     */
    if (FiltLamFreeze != 0) {
      CombCtrl_DWork.Switch2 = Avg_HR_FFS[(IonAbsTdcEOA)];
      CombCtrl_DWork.Switch1 = CombCtrl_DWork.Switch_m[IonAbsTdcEOA];
      CombCtrl_DWork.Switch = CombCtrl_DWork.Switch1_g[IonAbsTdcEOA];

      /* Selector: '<S61>/Selector_Outold2' incorporates:
       *  DataStoreRead: '<S17>/Data Store Read6'
       *  DataStoreRead: '<S17>/Data Store Read9'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Selector: '<S61>/Selector_InputSum'
       *  Selector: '<S61>/Selector_Input_x_N0'
       *  Selector: '<S61>/Selector_Outold1'
       *  Switch: '<S75>/Switch2'
       */
      if (FiltParReset != 0) {
        CombCtrl_DWork.Switch3 = Avg_HR_FFS[(IonAbsTdcEOA)];
      } else {
        CombCtrl_DWork.Switch3 = Avg_HR_old_FFS[(IonAbsTdcEOA)];
      }
    } else {
      if (FiltParReset != 0) {
        /* Selector: '<S61>/Selector_Outold2' incorporates:
         *  DataStoreRead: '<S17>/Data Store Read6'
         *  Inport: '<Root>/IonAbsTdcEOA'
         *  Switch: '<S75>/Switch2'
         */
        rtb_DataTypeConversion4 = Avg_HR_FFS[(IonAbsTdcEOA)];
      } else {
        /* Selector: '<S61>/Selector_Outold2' incorporates:
         *  DataStoreRead: '<S17>/Data Store Read9'
         *  Inport: '<Root>/IonAbsTdcEOA'
         *  Switch: '<S75>/Switch2'
         */
        rtb_DataTypeConversion4 = Avg_HR_old_FFS[(IonAbsTdcEOA)];
      }

      /* Sum: '<S76>/BodyRSum1' incorporates:
       *  DataStoreRead: '<S17>/Data Store Read6'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Product: '<S76>/Product1'
       *  Product: '<S76>/Product11'
       *  Selector: '<S61>/Selector_Outold1'
       */
      CombCtrl_DWork.i0 = (-((int64_T)((int32_T)((((int64_T)
        rtb_DataTypeConversion4) * D2LamFil) >> 16)))) - ((int32_T)((((int64_T)
        Avg_HR_FFS[(IonAbsTdcEOA)]) * D1LamFil) >> 16));
      if (CombCtrl_DWork.i0 > 2147483647LL) {
        CombCtrl_DWork.i0 = 2147483647LL;
      } else {
        if (CombCtrl_DWork.i0 < -2147483648LL) {
          CombCtrl_DWork.i0 = -2147483648LL;
        }
      }

      /* Sum: '<S76>/HeadSum1' incorporates:
       *  ArithShift: '<S76>/Gain_x_2_shifting'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Product: '<S76>/Product7'
       *  Selector: '<S61>/Selector_InputSum'
       *  Selector: '<S61>/Selector_Input_x_N0'
       *  Sum: '<S76>/BodyRSum1'
       *  Sum: '<S76>/HeadSum2'
       *  Sum: '<S76>/HeadSum3'
       */
      CombCtrl_DWork.i0 = (((CombCtrl_DWork.Switch_m[IonAbsTdcEOA] << 1) +
                            rtb_inputnum0) +
                           CombCtrl_DWork.Switch1_g[IonAbsTdcEOA]) + (((int64_T)
        ((int32_T)CombCtrl_DWork.i0)) << 6);
      if (CombCtrl_DWork.i0 < 0LL) {
        CombCtrl_DWork.i0 = 0LL;
      } else {
        if (CombCtrl_DWork.i0 > 4294967295LL) {
          CombCtrl_DWork.i0 = 4294967295LL;
        }
      }

      CombCtrl_DWork.Switch2 = (((uint32_T)CombCtrl_DWork.i0) >> 4);
      CombCtrl_DWork.Switch1 = rtb_inputnum0;
      CombCtrl_DWork.Switch = CombCtrl_DWork.Switch_m[IonAbsTdcEOA];
      CombCtrl_DWork.Switch3 = Avg_HR_FFS[(IonAbsTdcEOA)];
    }

    /* End of Switch: '<S76>/Switch2' */
  }

  /* End of Outputs for SubSystem: '<S74>/Butterworth_2order_LOWPASS' */

  /* Outputs for Enabled SubSystem: '<S74>/InitialCondition' incorporates:
   *  EnablePort: '<S78>/Enable'
   */
  /* Logic: '<S74>/Logical Operator' incorporates:
   *  DataStoreRead: '<S17>/Data Store Read2'
   */
  if (!(FiltLamEnable != 0)) {
    /* Product: '<S78>/Product1' */
    CombCtrl_DWork.Product1 = ((uint32_T)CombCtrl_DWork.median) * N0LamFil;
  }

  /* End of Logic: '<S74>/Logical Operator' */
  /* End of Outputs for SubSystem: '<S74>/InitialCondition' */

  /* Outputs for Enabled SubSystem: '<S74>/ByPassValues' incorporates:
   *  EnablePort: '<S77>/Enable'
   */
  /* Logic: '<S74>/Logical Operator1' */
  if (!(EnFilButter != 0)) {
    /* Product: '<S77>/Product1' */
    CombCtrl_DWork.Product1_j = ((uint32_T)CombCtrl_DWork.median) * N0LamFil;
  }

  /* End of Logic: '<S74>/Logical Operator1' */
  /* End of Outputs for SubSystem: '<S74>/ByPassValues' */

  /* Assignment: '<S61>/Assignment1' incorporates:
   *  DataStoreRead: '<S17>/Data Store Read7'
   */
  for (i = 0; i < 8; i++) {
    CombCtrl_DWork.Switch_m[i] = In_x_N0_FFS[(i)];
  }

  /* Switch: '<S74>/Switch5' */
  if (EnFilButter != 0) {
    /* Switch: '<S74>/Switch1' incorporates:
     *  DataStoreRead: '<S17>/Data Store Read2'
     */
    if (FiltLamEnable != 0) {
      /* Assignment: '<S61>/Assignment1' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Switch_m[IonAbsTdcEOA] = CombCtrl_DWork.Switch1;
    } else {
      /* Assignment: '<S61>/Assignment1' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Switch_m[IonAbsTdcEOA] = CombCtrl_DWork.Product1;
    }

    /* End of Switch: '<S74>/Switch1' */
  } else {
    /* Assignment: '<S61>/Assignment1' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    CombCtrl_DWork.Switch_m[IonAbsTdcEOA] = CombCtrl_DWork.Product1_j;
  }

  /* End of Switch: '<S74>/Switch5' */

  /* Assignment: '<S61>/Assignment2' incorporates:
   *  DataStoreRead: '<S17>/Data Store Read8'
   */
  for (i = 0; i < 8; i++) {
    CombCtrl_DWork.Switch1_g[i] = InSum_FFS[(i)];
  }

  /* Switch: '<S74>/Switch6' */
  if (EnFilButter != 0) {
    /* Switch: '<S74>/Switch2' incorporates:
     *  DataStoreRead: '<S17>/Data Store Read2'
     */
    if (FiltLamEnable != 0) {
      /* Assignment: '<S61>/Assignment2' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Switch1_g[IonAbsTdcEOA] = CombCtrl_DWork.Switch;
    } else {
      /* Assignment: '<S61>/Assignment2' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Switch1_g[IonAbsTdcEOA] = CombCtrl_DWork.Product1;
    }

    /* End of Switch: '<S74>/Switch2' */
  } else {
    /* Assignment: '<S61>/Assignment2' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    CombCtrl_DWork.Switch1_g[IonAbsTdcEOA] = CombCtrl_DWork.Product1_j;
  }

  /* End of Switch: '<S74>/Switch6' */

  /* DataTypeConversion: '<S74>/Data Type Conversion4' */
  rtb_DataTypeConversion4 = (((uint32_T)CombCtrl_DWork.median) << 16);

  /* DataTypeConversion: '<S74>/Data Type Conversion1' */
  rtb_inputnum0 = (((uint32_T)CombCtrl_DWork.median) << 16);

  /* Assignment: '<S61>/Assignment3' incorporates:
   *  DataStoreRead: '<S17>/Data Store Read6'
   */
  for (i = 0; i < 8; i++) {
    CombCtrl_DWork.Assignment3[i] = Avg_HR_FFS[(i)];
  }

  /* Switch: '<S74>/Switch7' incorporates:
   *  Switch: '<S74>/Switch8'
   */
  if (EnFilButter != 0) {
    /* Switch: '<S74>/Switch3' incorporates:
     *  DataStoreRead: '<S17>/Data Store Read2'
     *  Switch: '<S74>/Switch4'
     */
    if (FiltLamEnable != 0) {
      /* Assignment: '<S61>/Assignment3' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Assignment3[IonAbsTdcEOA] = CombCtrl_DWork.Switch2;
      rtb_inputnum0 = CombCtrl_DWork.Switch3;
    } else {
      /* Assignment: '<S61>/Assignment3' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Assignment3[IonAbsTdcEOA] = rtb_DataTypeConversion4;
      rtb_inputnum0 = rtb_DataTypeConversion4;
    }

    /* End of Switch: '<S74>/Switch3' */
  } else {
    /* Assignment: '<S61>/Assignment3' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    CombCtrl_DWork.Assignment3[IonAbsTdcEOA] = rtb_inputnum0;
  }

  /* End of Switch: '<S74>/Switch7' */

  /* Outputs for Atomic SubSystem: '<S17>/EnFilter' */
  for (i = 0; i < 8; i++) {
    /* Assignment: '<S61>/Assignment4' incorporates:
     *  DataStoreRead: '<S17>/Data Store Read9'
     */
    CombCtrl_DWork.Assignment4[i] = Avg_HR_old_FFS[(i)];

    /* DataStoreWrite: '<S17>/Data Store Write5' incorporates:
     *  Assignment: '<S61>/Assignment4'
     */
    Avg_HR_FFS[(i)] = CombCtrl_DWork.Assignment3[i];

    /* DataTypeConversion: '<S17>/Data Type Conversion14' incorporates:
     *  Assignment: '<S61>/Assignment4'
     */
    temp = (uint16_T)(CombCtrl_DWork.Assignment3[i] >> 16);

    /* DataStoreWrite: '<S17>/Data Store Write1' incorporates:
     *  Assignment: '<S61>/Assignment4'
     */
    AvgFFS[(i)] = temp;

    /* Assignment: '<S59>/Assignment3' incorporates:
     *  Assignment: '<S61>/Assignment4'
     *  DataStoreRead: '<S59>/Data Store Read3'
     */
    CombCtrl_DWork.Assignment3_k[i] = VtMisfCount[(i)];

    /* DataTypeConversion: '<S17>/Data Type Conversion14' incorporates:
     *  Assignment: '<S61>/Assignment4'
     */
    CombCtrl_DWork.Switch_l[i] = temp;
  }

  /* End of Outputs for SubSystem: '<S17>/EnFilter' */

  /* Assignment: '<S61>/Assignment4' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  CombCtrl_DWork.Assignment4[IonAbsTdcEOA] = rtb_inputnum0;

  /* Update for Memory: '<S75>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput_b = N0LamFil;

  /* End of Outputs for SubSystem: '<S17>/Filtering' */

  /* Outputs for Atomic SubSystem: '<S17>/EnFilter' */
  /* Switch: '<S59>/Switch' incorporates:
   *  Constant: '<S59>/NO_MISF'
   *  Constant: '<S59>/One'
   *  Constant: '<S59>/One1'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/StMisf'
   *  RelationalOperator: '<S59>/Relational Operator1'
   *  Selector: '<S59>/Selector_Outold1'
   */
  if (StMisf[(IonAbsTdcEOA)] != ((uint8_T)NO_MISF)) {
    tmp = 1;
  } else {
    tmp = -1;
  }

  /* End of Switch: '<S59>/Switch' */

  /* Sum: '<S59>/Sum' incorporates:
   *  DataStoreRead: '<S59>/Data Store Read3'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Selector: '<S59>/Selector_Outold3'
   */
  rtb_Add1_e = (int16_T)(VtMisfCount[(IonAbsTdcEOA)] + tmp);
  if (rtb_Add1_e < 0) {
    rtb_Add1_e = 0;
  } else {
    if (rtb_Add1_e > 255) {
      rtb_Add1_e = 255;
    }
  }

  idx1 = (uint8_T)rtb_Add1_e;

  /* End of Sum: '<S59>/Sum' */

  /* Assignment: '<S59>/Assignment3' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  CombCtrl_DWork.Assignment3_k[IonAbsTdcEOA] = idx1;

  /* DataStoreWrite: '<S59>/Data Store Write1' */
  for (i = 0; i < 8; i++) {
    VtMisfCount[(i)] = CombCtrl_DWork.Assignment3_k[i];
  }

  /* End of DataStoreWrite: '<S59>/Data Store Write1' */

  /* DataStoreWrite: '<S59>/Data Store Write5' incorporates:
   *  Constant: '<S59>/ENFILTLAMFREEZE'
   *  Constant: '<S59>/LENBUFMED'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/IonErrorStatus'
   *  Logic: '<S59>/Logical Operator'
   *  Logic: '<S59>/Logical Operator2'
   *  Product: '<S59>/Product'
   *  RelationalOperator: '<S59>/Relational Operator'
   *  S-Function (sfix_bitop): '<S59>/Bitwise Operator'
   *  Selector: '<S59>/Selector_Outold2'
   */
  FiltLamFreeze = (uint8_T)((((IonErrorStatus[(IonAbsTdcEOA)] & 3) != 0) ||
    (((int32_T)(((uint32_T)LENBUFMED) >> 1)) <= idx1)) && (ENFILTLAMFREEZE != 0));

  /* End of Outputs for SubSystem: '<S17>/EnFilter' */

  /* Outputs for Atomic SubSystem: '<S11>/CylBal' */
  /* Chart: '<S79>/Media' */
  /* Gateway: CombCtrl/EOA/CylBal/FFSIntErr_Calc/Media */
  /* During: CombCtrl/EOA/CylBal/FFSIntErr_Calc/Media */
  /* Entry Internal: CombCtrl/EOA/CylBal/FFSIntErr_Calc/Media */
  /* Transition: '<S81>:78' */
  /* Transition: '<S81>:3' */
  idx1 = 0U;
  rtb_DataTypeConversion4 = 0U;
  do {
    /* Transition: '<S81>:58' */
    rtb_DataTypeConversion4 += CombCtrl_DWork.Switch_l[idx1];
    idx1++;

    /* Transition: '<S81>:67' */
  } while (idx1 < ((uint8_T)N_CYLINDER));

  /* Transition: '<S81>:66' */
  temp = (uint16_T)((((uint8_T)N_CYLINDER) == 0U) ? MAX_uint32_T :
                    (rtb_DataTypeConversion4 / ((uint8_T)N_CYLINDER)));
  AvgFFSBank = temp;

  /* Switch: '<S79>/Switch' incorporates:
   *  Constant: '<S79>/FFSPEAKLAMBDA'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/LamObjCAN'
   *  RelationalOperator: '<S79>/Relational Operator1'
   *  Selector: '<S79>/Selector_Outold1'
   *  Sum: '<S79>/Add2'
   *  Sum: '<S79>/Add3'
   */
  if (LamObjCAN >= FFSPEAKLAMBDA) {
    rtb_Add1_e = (int16_T)(temp - CombCtrl_DWork.Switch_l[IonAbsTdcEOA]);
  } else {
    rtb_Add1_e = (int16_T)(CombCtrl_DWork.Switch_l[IonAbsTdcEOA] - temp);
  }

  /* End of Switch: '<S79>/Switch' */

  /* Outputs for Atomic SubSystem: '<S18>/PI_Regulator' */
  /* S-Function (LookUp_S16_U16): '<S84>/LookUp_S16_U16' incorporates:
   *  Constant: '<S80>/BKRPMLAMFIL'
   *  Constant: '<S80>/BKRPMLAMFIL_dim'
   *  Constant: '<S80>/VTFFSERRTHR'
   *  Inport: '<Root>/Rpm'
   */
  LookUp_S16_U16(&rtb_LookUp_S16_U16, (int16_T*)&VTFFSERRTHR[0], Rpm, (uint16_T*)
                 &BKRPMLAMFIL[0], ((uint8_T)BKRPMLAMFIL_dim));

  /* S-Function (LookUp_U16_U16): '<S90>/LookUp_U16_U16' incorporates:
   *  Constant: '<S82>/BKRPMENINJCORR'
   *  Constant: '<S82>/BKRPMENINJCORR_dim'
   *  Constant: '<S82>/VTLOADENINJCORR'
   *  Inport: '<Root>/Rpm'
   */
  LookUp_U16_U16(&rtb_LookUp_U16_U16, (uint16_T*)&VTLOADENINJCORR[0], Rpm,
                 (uint16_T*)&BKRPMENINJCORR[0], ((uint8_T)BKRPMENINJCORR_dim));

  /* Switch: '<S82>/Switch1' incorporates:
   *  Constant: '<S82>/LOADENINJCORRHYST'
   *  Constant: '<S82>/boolean3'
   *  Memory: '<S82>/Memory'
   */
  if (CombCtrl_DWork.Memory_PreviousInput_d != 0) {
    temp = 0U;
  } else {
    temp = LOADENINJCORRHYST;
  }

  /* End of Switch: '<S82>/Switch1' */

  /* Sum: '<S82>/Sum' */
  rtb_DataTypeConversion3 = rtb_LookUp_U16_U16 - temp;
  if (rtb_DataTypeConversion3 < 0) {
    rtb_DataTypeConversion3 = 0;
  }

  rtb_Memory = (uint16_T)rtb_DataTypeConversion3;

  /* End of Sum: '<S82>/Sum' */

  /* RelationalOperator: '<S82>/Relational Operator3' incorporates:
   *  Inport: '<Root>/Load'
   */
  idx1 = (uint8_T)(Load <= rtb_Memory);

  /* Switch: '<S82>/Switch2' incorporates:
   *  Constant: '<S82>/FORCEFFSOBJ'
   *  Constant: '<S82>/boolean1'
   *  Constant: '<S82>/boolean4'
   *  Constant: '<S88>/Constant'
   *  Inport: '<Root>/Rpm'
   *  RelationalOperator: '<S82>/Relational Operator2'
   *  RelationalOperator: '<S88>/Compare'
   */
  if (Rpm == 0) {
    tmp_0 = (FORCEFFSOBJ != 2);
  } else {
    tmp_0 = false;
  }

  /* End of Switch: '<S82>/Switch2' */

  /* Logic: '<S82>/Logical Operator1' incorporates:
   *  Constant: '<S82>/CYLBALENLAMOBJMAX'
   *  Constant: '<S82>/CYLBALENLAMOBJMIN'
   *  Constant: '<S82>/ENINJCORRCYL'
   *  Constant: '<S82>/boolean2'
   *  DataStoreRead: '<S82>/Data Store Read14'
   *  Inport: '<Root>/AFRCLActive'
   *  Inport: '<Root>/CylBalCLReq'
   *  Inport: '<Root>/KeySignal'
   *  Inport: '<Root>/LamObjCAN'
   *  Logic: '<S82>/Logical Operator2'
   *  RelationalOperator: '<S82>/Relational Operator4'
   *  RelationalOperator: '<S82>/Relational Operator5'
   *  RelationalOperator: '<S82>/Relational Operator7'
   */
  idx2 = (uint8_T)((((((((((CylBalCLReq != 0) && (!tmp_0)) && (ENINJCORRCYL != 0))
    && (AFRCLActive != 0)) && (!(FiltLamFreeze != 0))) && (LamObjCAN <=
    CYLBALENLAMOBJMAX)) && (LamObjCAN >= CYLBALENLAMOBJMIN)) && (KeySignal != 0))
                    && rtb_FlgSteadyState) && (idx1 != 0));

  /* DataStoreWrite: '<S82>/Data Store Write1' */
  CylBalEn = idx2;

  /* Switch: '<S83>/Switch' incorporates:
   *  RelationalOperator: '<S83>/u_GTE_up'
   */
  if (rtb_Add1_e >= rtb_LookUp_S16_U16) {
    rtb_Memory_a = rtb_LookUp_S16_U16;
  } else {
    /* Gain: '<S80>/Gain1' */
    rtb_Memory_a = (int16_T)(-rtb_LookUp_S16_U16);

    /* Switch: '<S83>/Switch1' incorporates:
     *  RelationalOperator: '<S83>/u_GT_lo'
     */
    if (rtb_Add1_e > rtb_Memory_a) {
      rtb_Memory_a = rtb_Add1_e;
    }

    /* End of Switch: '<S83>/Switch1' */
  }

  /* End of Switch: '<S83>/Switch' */

  /* Sum: '<S83>/Diff' */
  rtb_Memory_a = (int16_T)(rtb_Add1_e - rtb_Memory_a);

  /* Chart: '<S80>/Media' */
  /* Gateway: CombCtrl/EOA/CylBal/PI_Regulator/Media */
  /* During: CombCtrl/EOA/CylBal/PI_Regulator/Media */
  /* Entry Internal: CombCtrl/EOA/CylBal/PI_Regulator/Media */
  /* Transition: '<S85>:78' */
  if (rtb_Memory_a == 0) {
    /* Transition: '<S85>:8' */
    if (rtb_Add1_e >= 0) {
      /* Transition: '<S85>:86' */
      rtb_Memory_a = FFSERRDZ;
    } else {
      /* Transition: '<S85>:88' */
      rtb_Memory_a = (int16_T)(-FFSERRDZ);
    }
  } else {
    /* Transition: '<S85>:54' */
  }

  /* End of Chart: '<S80>/Media' */

  /* Switch: '<S80>/Switch1' incorporates:
   *  Constant: '<S80>/boolean2'
   */
  if (idx2 != 0) {
    /* Sum: '<S80>/Sum6' incorporates:
     *  Constant: '<S80>/PIFFSKI'
     *  Constant: '<S80>/PIFFSKP'
     */
    rtb_DataTypeConversion3 = (int32_T)((((uint32_T)PIFFSKI) + PIFFSKP) >> 1);
    if (((uint32_T)rtb_DataTypeConversion3) > 32767U) {
      rtb_DataTypeConversion3 = 32767;
    }

    /* Sum: '<S80>/Sum5' incorporates:
     *  Constant: '<S80>/PIFFSKP'
     *  DataStoreRead: '<S18>/Data Store Read1'
     *  DataStoreRead: '<S18>/Data Store Read6'
     *  DataTypeConversion: '<S80>/Data Type Conversion2'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Product: '<S80>/Product'
     *  Product: '<S80>/Product1'
     *  Selector: '<S18>/Selector_Outold4'
     *  Selector: '<S18>/Selector_Outold5'
     *  Sum: '<S80>/Sum4'
     *  Sum: '<S80>/Sum6'
     */
    i = (((rtb_Memory_a * rtb_DataTypeConversion3) >> 1) - ((((int32_T)
            (((uint32_T)PIFFSKP) >> 1)) * VtFFSErr[(IonAbsTdcEOA)]) >> 1)) +
      VtPiFFS[(IonAbsTdcEOA)];

    /* DataTypeConversion: '<S80>/Data Type Conversion3' incorporates:
     *  Constant: '<S80>/MAXVFFSOUT'
     */
    rtb_DataTypeConversion3 = (MAXVFFSOUT << 1);

    /* Switch: '<S87>/Switch2' incorporates:
     *  RelationalOperator: '<S87>/LowerRelop1'
     */
    if (!(i > rtb_DataTypeConversion3)) {
      /* Gain: '<S80>/Gain' incorporates:
       *  Constant: '<S80>/MAXVFFSOUT'
       */
      rtb_DataTypeConversion3 = -2 * MAXVFFSOUT;

      /* Switch: '<S87>/Switch' incorporates:
       *  RelationalOperator: '<S87>/UpperRelop'
       */
      if (!(i < rtb_DataTypeConversion3)) {
        rtb_DataTypeConversion3 = i;
      }

      /* End of Switch: '<S87>/Switch' */
    }

    /* End of Switch: '<S87>/Switch2' */
  } else {
    rtb_DataTypeConversion3 = 0;
  }

  /* End of Switch: '<S80>/Switch1' */

  /* Sum: '<S80>/Sum1' */
  i = 32768 - (rtb_DataTypeConversion3 >> 1);

  /* Switch: '<S80>/Switch4' incorporates:
   *  DataTypeConversion: '<S80>/Data Type Conversion4'
   *  DataTypeConversion: '<S80>/Data Type Conversion5'
   */
  if (idx2 != 0) {
    temp = (uint16_T)i;
  } else {
    /* DataTypeConversion: '<S80>/Data Type Conversion1' incorporates:
     *  DataStoreRead: '<S18>/Data Store Read8'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Selector: '<S18>/Selector_Outold6'
     */
    rtb_RateLimiter_S32 = VtInjCorrCyl[(IonAbsTdcEOA)];

    /* S-Function (RateLimiter_S32): '<S86>/RateLimiter_S32' incorporates:
     *  Constant: '<S80>/INJCORRCYLRLMAX'
     *  Constant: '<S80>/INJCORRCYLRLMIN'
     */
    RateLimiter_S32(&rtb_RateLimiter_S32, i, rtb_RateLimiter_S32,
                    INJCORRCYLRLMIN, INJCORRCYLRLMAX);
    temp = (uint16_T)rtb_RateLimiter_S32;
  }

  /* End of Switch: '<S80>/Switch4' */

  /* DataStoreWrite: '<S82>/Data Store Write2' */
  LoadEnInjCorr = rtb_Memory;

  /* DataStoreWrite: '<S80>/Data Store Write7' */
  FfsErrThr = rtb_LookUp_S16_U16;

  /* Update for Memory: '<S82>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput_d = idx1;

  /* End of Outputs for SubSystem: '<S18>/PI_Regulator' */

  /* Assignment: '<S18>/Assignment1' incorporates:
   *  DataStoreRead: '<S18>/Data Store Read4'
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  for (i = 0; i < 8; i++) {
    CombCtrl_DWork.Assignment1[i] = VtPiFFS[(i)];
  }

  CombCtrl_DWork.Assignment1[IonAbsTdcEOA] = rtb_DataTypeConversion3;

  /* End of Assignment: '<S18>/Assignment1' */
  for (i = 0; i < 8; i++) {
    /* DataStoreWrite: '<S18>/Data Store Write1' */
    VtPiFFS[(i)] = CombCtrl_DWork.Assignment1[i];

    /* Assignment: '<S18>/Assignment2' incorporates:
     *  DataStoreRead: '<S18>/Data Store Read2'
     *  DataStoreWrite: '<S18>/Data Store Write1'
     */
    CombCtrl_DWork.Assignment2_g[i] = VtFFSErr[(i)];
  }

  /* Assignment: '<S18>/Assignment2' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  CombCtrl_DWork.Assignment2_g[IonAbsTdcEOA] = rtb_Memory_a;
  for (i = 0; i < 8; i++) {
    /* DataStoreWrite: '<S18>/Data Store Write7' */
    VtFFSErr[(i)] = CombCtrl_DWork.Assignment2_g[i];

    /* Assignment: '<S18>/Assignment3' incorporates:
     *  DataStoreRead: '<S18>/Data Store Read3'
     *  DataStoreWrite: '<S18>/Data Store Write7'
     */
    CombCtrl_DWork.Switch_l[i] = VtInjCorrCyl[(i)];
  }

  /* Assignment: '<S18>/Assignment3' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  CombCtrl_DWork.Switch_l[IonAbsTdcEOA] = temp;
  for (i = 0; i < 8; i++) {
    /* DataStoreWrite: '<S18>/Data Store Write2' */
    VtInjCorrCyl[(i)] = CombCtrl_DWork.Switch_l[i];

    /* Assignment: '<S18>/Assignment4' incorporates:
     *  DataStoreRead: '<S18>/Data Store Read9'
     *  DataStoreWrite: '<S18>/Data Store Write2'
     */
    CombCtrl_DWork.Assignment2_g[i] = VtFFSInErr[(i)];
  }

  /* Assignment: '<S18>/Assignment4' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  CombCtrl_DWork.Assignment2_g[IonAbsTdcEOA] = rtb_Add1_e;
  for (i = 0; i < 8; i++) {
    /* DataStoreWrite: '<S18>/Data Store Write3' */
    VtFFSInErr[(i)] = CombCtrl_DWork.Assignment2_g[i];

    /* DataStoreWrite: '<S17>/Data Store Write6' incorporates:
     *  DataStoreWrite: '<S18>/Data Store Write3'
     */
    In_x_N0_FFS[(i)] = CombCtrl_DWork.Switch_m[i];

    /* DataStoreWrite: '<S17>/Data Store Write7' incorporates:
     *  DataStoreWrite: '<S18>/Data Store Write3'
     */
    InSum_FFS[(i)] = CombCtrl_DWork.Switch1_g[i];

    /* DataStoreWrite: '<S17>/Data Store Write8' incorporates:
     *  DataStoreWrite: '<S18>/Data Store Write3'
     */
    Avg_HR_old_FFS[(i)] = CombCtrl_DWork.Assignment4[i];
  }

  /* End of Outputs for SubSystem: '<S11>/CylBal' */

  /* Outputs for Atomic SubSystem: '<S11>/Adaptive_Correction' */
  /* Chart: '<S15>/RecAdaptEnable_Flag' incorporates:
   *  Inport: '<Root>/FlgResetAdaptCylBal'
   */
  /* Gateway: CombCtrl/EOA/Adaptive_Correction/RecAdaptEnable_Flag */
  /* During: CombCtrl/EOA/Adaptive_Correction/RecAdaptEnable_Flag */
  if (CombCtrl_DWork.bitsForTID0.is_active_c20_CombCtrl == 0U) {
    /* Entry: CombCtrl/EOA/Adaptive_Correction/RecAdaptEnable_Flag */
    CombCtrl_DWork.bitsForTID0.is_active_c20_CombCtrl = 1U;

    /* Entry Internal: CombCtrl/EOA/Adaptive_Correction/RecAdaptEnable_Flag */
    /* Transition: '<S22>:3' */
    CombCtrl_DWork.bitsForTID0.is_c20_CombCtrl = CombCtrl_IN_ADAPT_ENABLE;

    /* Entry 'ADAPT_ENABLE': '<S22>:1' */
    idx1 = 1U;
  } else if (CombCtrl_DWork.bitsForTID0.is_c20_CombCtrl ==
             CombCtrl_IN_ADAPT_DISABLE) {
    /* During 'ADAPT_DISABLE': '<S22>:6' */
    /* Transition: '<S22>:8' */
    idx1 = 0U;
    do {
      exitg1 = 0;
      if (CombCtrl_DWork.Switch_l[idx1] == 32768) {
        /* Transition: '<S22>:11' */
        idx1++;
        if (idx1 >= ((uint8_T)N_CYLINDER)) {
          /* Transition: '<S22>:13' */
          CombCtrl_DWork.bitsForTID0.is_c20_CombCtrl = CombCtrl_IN_ADAPT_ENABLE;

          /* Entry 'ADAPT_ENABLE': '<S22>:1' */
          idx1 = 1U;
          exitg1 = 1;
        } else {
          /* Transition: '<S22>:14' */
        }
      } else {
        /* Transition: '<S22>:16' */
        CombCtrl_DWork.bitsForTID0.is_c20_CombCtrl = CombCtrl_IN_ADAPT_DISABLE;

        /* Entry 'ADAPT_DISABLE': '<S22>:6' */
        idx1 = 0U;
        exitg1 = 1;
      }
    } while (exitg1 == 0);
  } else {
    idx1 = 1U;

    /* During 'ADAPT_ENABLE': '<S22>:1' */
    if (FlgResetAdaptCylBal != 0) {
      /* Transition: '<S22>:7' */
      CombCtrl_DWork.bitsForTID0.is_c20_CombCtrl = CombCtrl_IN_ADAPT_DISABLE;

      /* Entry 'ADAPT_DISABLE': '<S22>:6' */
      idx1 = 0U;
    }
  }

  /* End of Chart: '<S15>/RecAdaptEnable_Flag' */

  /* DataStoreWrite: '<S15>/Data Store Write7' */
  FlgRecAdCylBalEn = idx1;

  /* Outputs for Atomic SubSystem: '<S15>/Zones_Learn' */
  /* S-Function (PreLookUpIdSearch_U16): '<S55>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S23>/ENKNOCKAD1'
   *  Constant: '<S23>/ENKNOCKAD3'
   *  Inport: '<Root>/RpmF'
   */
  PreLookUpIdSearch_U16(&rtb_PreLookUpIdSearch_U16_o1_n,
                        &CombCtrl_DWork.PreLookUpIdSearch_U16_o2, RpmF,
                        (uint16_T*)&BKRPMCYLBAL[0], ((uint8_T)BKRPMCYLBAL_dim));

  /* S-Function (LookUp_IR_U16): '<S53>/LookUp_IR_U16' incorporates:
   *  Constant: '<S23>/ENKNOCKAD3'
   *  Constant: '<S23>/VTTDCSTABCYLBALAD'
   */
  LookUp_IR_U16(&rtb_LookUp_IR_U16, (uint16_T*)&VTTDCSTABCYLBALAD[0],
                rtb_PreLookUpIdSearch_U16_o1_n,
                CombCtrl_DWork.PreLookUpIdSearch_U16_o2, ((uint8_T)
    BKRPMCYLBAL_dim));

  /* Product: '<S23>/Product1' incorporates:
   *  Inport: '<Root>/RpmF'
   */
  rtb_Memory = (uint16_T)((((uint32_T)RpmF) << 5) / 25U);

  /* Memory: '<S23>/Memory' */
  idx_median = CombCtrl_DWork.Memory_PreviousInput_m;

  /* Memory: '<S56>/Memory1' */
  rtb_Mul1 = CombCtrl_DWork.Memory1_PreviousInput_l;

  /* Memory: '<S56>/Memory' */
  rtb_Memory1 = CombCtrl_DWork.Memory_PreviousInput_o;

  /* S-Function (SigStab): '<S56>/SigStab' incorporates:
   *  Constant: '<S23>/Constant'
   *  Constant: '<S23>/THRSTABRPMLAMTR'
   */
  SigStab(&idx_median, &rtb_SigStab_o2, &rtb_SigStab_o3, &rtb_SigStab_o4,
          rtb_Memory, ((uint8_T)0U), THRSTABRPMLAMTR, rtb_LookUp_IR_U16,
          idx_median, rtb_Mul1, rtb_Memory1);

  /* Memory: '<S23>/Memory1' */
  idx2 = CombCtrl_DWork.Memory1_PreviousInput_i;

  /* Memory: '<S57>/Memory1' */
  rtb_Memory1 = CombCtrl_DWork.Memory1_PreviousInput_g;

  /* Memory: '<S57>/Memory' */
  rtb_Mul1 = CombCtrl_DWork.Memory_PreviousInput_a;

  /* S-Function (SigStab): '<S57>/SigStab' incorporates:
   *  Constant: '<S23>/Constant1'
   *  Constant: '<S23>/THRSTABLOADLAMTR'
   *  Inport: '<Root>/Load'
   */
  SigStab(&idx2, &rtb_SigStab_o2_d, &rtb_SigStab_o3_d, &rtb_SigStab_o4_e, Load,
          ((uint8_T)0U), THRSTABLOADLAMTR, rtb_LookUp_IR_U16, idx2, rtb_Memory1,
          rtb_Mul1);

  /* S-Function (PreLookUpIdSearch_U16): '<S54>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S23>/ENKNOCKAD5'
   *  Constant: '<S23>/ENKNOCKAD6'
   *  Inport: '<Root>/Load'
   */
  PreLookUpIdSearch_U16(&rtb_PreLookUpIdSearch_U16_o1_b,
                        &CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k, Load,
                        (uint16_T*)&BKLOADCYLBAL[0], ((uint8_T)BKLOADCYLBAL_dim));

  /* DataStoreWrite: '<S23>/Data Store Write3' */
  FStabLoadCylBalAd = idx2;

  /* Logic: '<S23>/Logical Operator2' */
  idx2 = (uint8_T)((idx_median != 0) && (idx2 != 0));

  /* DataStoreWrite: '<S23>/Data Store Write4' */
  FlgSteadyStateAd = idx2;

  /* DataStoreWrite: '<S23>/Data Store Write5' */
  FStabLoadCylBalStateAd = rtb_SigStab_o2_d;

  /* DataStoreWrite: '<S23>/Data Store Write1' */
  FStabRpmCylBalStateAd = rtb_SigStab_o2;

  /* DataStoreWrite: '<S23>/Data Store Write2' */
  FStabRpmCylBalAd = idx_median;

  /* Update for Memory: '<S23>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput_m = rtb_SigStab_o2;

  /* Update for Memory: '<S56>/Memory1' */
  CombCtrl_DWork.Memory1_PreviousInput_l = rtb_SigStab_o3;

  /* Update for Memory: '<S56>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput_o = rtb_SigStab_o4;

  /* Update for Memory: '<S23>/Memory1' */
  CombCtrl_DWork.Memory1_PreviousInput_i = rtb_SigStab_o2_d;

  /* Update for Memory: '<S57>/Memory1' */
  CombCtrl_DWork.Memory1_PreviousInput_g = rtb_SigStab_o3_d;

  /* Update for Memory: '<S57>/Memory' */
  CombCtrl_DWork.Memory_PreviousInput_a = rtb_SigStab_o4_e;

  /* End of Outputs for SubSystem: '<S15>/Zones_Learn' */

  /* Outputs for Atomic SubSystem: '<S15>/Calc_Ad_Corr' */
  /* If: '<S19>/If' incorporates:
   *  Constant: '<S15>/ENCYLBALADCORR'
   */
  if (ENCYLBALADCORR == 0) {
    /* Outputs for IfAction SubSystem: '<S19>/Reset_AdaptiveValues' incorporates:
     *  ActionPort: '<S25>/Action Port'
     */
    /* Assignment: '<S25>/Assignment3' incorporates:
     *  Constant: '<S25>/ENKNOCKAD2'
     *  DataStoreRead: '<S25>/Data Store Read1'
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    for (i = 0; i < 8; i++) {
      CombCtrl_DWork.Assignment3_i[i] = VtInjCorrCylAd[(i)];
    }

    CombCtrl_DWork.Assignment3_i[IonAbsTdcEOA] = 32768U;

    /* End of Assignment: '<S25>/Assignment3' */

    /* DataStoreWrite: '<S25>/Data Store Write2' */
    for (i = 0; i < 8; i++) {
      VtInjCorrCylAd[(i)] = CombCtrl_DWork.Assignment3_i[i];
    }

    /* End of DataStoreWrite: '<S25>/Data Store Write2' */
    /* End of Outputs for SubSystem: '<S19>/Reset_AdaptiveValues' */
  } else {
    /* Outputs for IfAction SubSystem: '<S19>/Manage_Learning' incorporates:
     *  ActionPort: '<S24>/Action Port'
     */
    /* DataStoreRead: '<S24>/Data Store Read4' */
    rtb_DataStoreRead4_ie_0 = (&(TbInjCorrCylAd[0]));

    /* Sum: '<S27>/FixPt Sum1' incorporates:
     *  Constant: '<S24>/ENKNOCKAD3'
     *  Constant: '<S27>/FixPt Constant'
     */
    idx_median = (uint8_T)(((uint8_T)BKRPMCYLBAL_dim) + 1U);

    /* Sum: '<S24>/Add1' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Product: '<S24>/Product'
     */
    rtb_Memory = (uint16_T)(((uint32_T)((uint8_T)(((uint32_T)IonAbsTdcEOA) *
      idx_median))) + rtb_PreLookUpIdSearch_U16_o1_n);

    /* Sum: '<S26>/FixPt Sum1' incorporates:
     *  Constant: '<S24>/ENKNOCKAD2'
     *  Constant: '<S26>/FixPt Constant'
     *  Product: '<S24>/Product1'
     */
    idx_median = (uint8_T)(((uint8_T)(((uint32_T)idx_median) * ((uint8_T)
      N_CYL_MAX))) - 1);

    /* S-Function (Look2D_IR_U16): '<S28>/Look2D_IR_U16' incorporates:
     *  Constant: '<S24>/ENKNOCKAD6'
     */
    Look2D_IR_U16(&rtb_Look2D_IR_U16_f, (uint16_T*)&rtb_DataStoreRead4_ie_0[0],
                  rtb_Memory, CombCtrl_DWork.PreLookUpIdSearch_U16_o2,
                  idx_median, rtb_PreLookUpIdSearch_U16_o1_b,
                  CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k, ((uint8_T)
      BKLOADCYLBAL_dim));

    /* Assignment: '<S24>/Assignment3' incorporates:
     *  DataStoreRead: '<S24>/Data Store Read1'
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    for (i = 0; i < 8; i++) {
      CombCtrl_DWork.Assignment3_i[i] = VtInjCorrCylAd[(i)];
    }

    CombCtrl_DWork.Assignment3_i[IonAbsTdcEOA] = rtb_Look2D_IR_U16_f;

    /* End of Assignment: '<S24>/Assignment3' */

    /* DataStoreWrite: '<S24>/Data Store Write2' */
    for (i = 0; i < 8; i++) {
      VtInjCorrCylAd[(i)] = CombCtrl_DWork.Assignment3_i[i];
    }

    /* End of DataStoreWrite: '<S24>/Data Store Write2' */
    /* End of Outputs for SubSystem: '<S19>/Manage_Learning' */
  }

  /* End of If: '<S19>/If' */
  /* End of Outputs for SubSystem: '<S15>/Calc_Ad_Corr' */

  /* Outputs for Enabled SubSystem: '<S15>/Mgm_Ad_Table' incorporates:
   *  EnablePort: '<S21>/Enable (when disabled RESET states MIXED outputs)'
   */
  /* Logic: '<S15>/Logical Operator' incorporates:
   *  Constant: '<S15>/ENCYLBALAD'
   *  Constant: '<S15>/ENCYLBALADCORR'
   *  Constant: '<S15>/THWATCYLBALAD'
   *  Constant: '<S20>/Constant'
   *  Inport: '<Root>/FlgEOL'
   *  Inport: '<Root>/TWater'
   *  RelationalOperator: '<S15>/Relational Operator'
   *  RelationalOperator: '<S20>/Compare'
   */
  if (((((idx1 != 0) && (ENCYLBALAD != 0)) && (FlgEOL == 0)) && (TWater >
        THWATCYLBALAD)) && (ENCYLBALADCORR != 0)) {
    if (!CombCtrl_DWork.Mgm_Ad_Table_MODE) {
      CombCtrl_DWork.Mgm_Ad_Table_MODE = true;
    }

    /* Outputs for Atomic SubSystem: '<S21>/Ad_State' */
    /* If: '<S30>/If' */
    if (idx2 == 0) {
      /* Outputs for IfAction SubSystem: '<S30>/Reset_Learning' incorporates:
       *  ActionPort: '<S34>/Action Port'
       */
      /* Chart: '<S34>/Reset_Variables' */
      /* Gateway: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Reset_Learning/Reset_Variables */
      /* During: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Reset_Learning/Reset_Variables */
      /* Entry Internal: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Ad_State/Reset_Learning/Reset_Variables */
      /* Transition: '<S36>:1' */
      for (idx1 = 0U; idx1 < ((uint8_T)N_CYLINDER); idx1++) {
        /* Transition: '<S36>:2' */
        VtEndCylBalLearn[(idx1)] = 0U;
        VtCntCylBalLearn[(idx1)] = 0U;
        VtCylBalLearnState[(idx1)] = 0U;
      }

      /* End of Chart: '<S34>/Reset_Variables' */
      /* End of Outputs for SubSystem: '<S30>/Reset_Learning' */
      /* Transition: '<S36>:3' */
    } else {
      /* Outputs for IfAction SubSystem: '<S30>/Manage_Learning' incorporates:
       *  ActionPort: '<S33>/Action Port'
       */
      /* Switch: '<S33>/Switch1' incorporates:
       *  DataStoreRead: '<S33>/Data Store Read1'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Selector: '<S33>/Selector1'
       */
      idx1 = (uint8_T)(!(VtEndCylBalLearn[(IonAbsTdcEOA)] != 0));

      /* Switch: '<S33>/Switch' incorporates:
       *  Constant: '<S33>/Constant2'
       *  Constant: '<S35>/FixPt Constant'
       *  DataStoreRead: '<S33>/Data Store Read4'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Selector: '<S33>/Selector4'
       *  Sum: '<S35>/FixPt Sum1'
       */
      if (idx1 != 0) {
        rtb_Memory = (uint16_T)(VtCntCylBalLearn[(IonAbsTdcEOA)] + 1U);
      } else {
        rtb_Memory = 0U;
      }

      /* End of Switch: '<S33>/Switch' */

      /* Assignment: '<S33>/Assignment1' incorporates:
       *  DataStoreRead: '<S33>/Data Store Read3'
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      for (i = 0; i < 8; i++) {
        CombCtrl_DWork.Assignment3_i[i] = VtCntCylBalLearn[(i)];
      }

      CombCtrl_DWork.Assignment3_i[IonAbsTdcEOA] = rtb_Memory;

      /* End of Assignment: '<S33>/Assignment1' */
      for (i = 0; i < 8; i++) {
        /* DataStoreWrite: '<S33>/Data Store Write1' */
        VtCntCylBalLearn[(i)] = CombCtrl_DWork.Assignment3_i[i];

        /* Assignment: '<S33>/Assignment2' incorporates:
         *  DataStoreRead: '<S33>/Data Store Read5'
         *  DataStoreWrite: '<S33>/Data Store Write1'
         */
        CombCtrl_DWork.Assignment3_k[i] = VtCylBalLearnState[(i)];
      }

      /* Assignment: '<S33>/Assignment2' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Assignment3_k[IonAbsTdcEOA] = idx1;

      /* RelationalOperator: '<S33>/Relational Operator' incorporates:
       *  Constant: '<S33>/CYLBALLEARNDUR'
       */
      EndCylBalLearnCyl = (uint8_T)(rtb_Memory >= CYLBALLEARNDUR);
      for (i = 0; i < 8; i++) {
        /* DataStoreWrite: '<S33>/Data Store Write3' */
        VtCylBalLearnState[(i)] = CombCtrl_DWork.Assignment3_k[i];
        CombCtrl_DWork.Assignment3_k[i] = VtEndCylBalLearn[(i)];
      }

      /* Assignment: '<S33>/Assignment3' incorporates:
       *  DataStoreRead: '<S33>/Data Store Read2'
       *  DataStoreWrite: '<S33>/Data Store Write3'
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Assignment3_k[IonAbsTdcEOA] = EndCylBalLearnCyl;

      /* DataStoreWrite: '<S33>/Data Store Write2' */
      for (i = 0; i < 8; i++) {
        VtEndCylBalLearn[(i)] = CombCtrl_DWork.Assignment3_k[i];
      }

      /* End of DataStoreWrite: '<S33>/Data Store Write2' */
      /* End of Outputs for SubSystem: '<S30>/Manage_Learning' */
    }

    /* End of If: '<S30>/If' */
    /* End of Outputs for SubSystem: '<S21>/Ad_State' */

    /* Outputs for Atomic SubSystem: '<S21>/TriggerToLearn' */
    /* Switch: '<S31>/Switch1' incorporates:
     *  Constant: '<S31>/Constant4'
     *  DataStoreRead: '<S31>/Data Store Read3'
     *  DataStoreRead: '<S31>/Data Store Read5'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Selector: '<S31>/Selector1'
     *  Selector: '<S31>/Selector_Outold6'
     *  Sum: '<S31>/Sum1'
     */
    if (VtCylBalLearnState[(IonAbsTdcEOA)] != 0) {
      rtb_inputnum0 = VtCylBalIndSum[(IonAbsTdcEOA)] + temp;
    } else {
      rtb_inputnum0 = 0U;
    }

    /* End of Switch: '<S31>/Switch1' */

    /* Assignment: '<S31>/Assignment2' incorporates:
     *  DataStoreRead: '<S31>/Data Store Read1'
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    for (i = 0; i < 8; i++) {
      CombCtrl_DWork.Switch_m[i] = VtCylBalIndSum[(i)];
    }

    CombCtrl_DWork.Switch_m[IonAbsTdcEOA] = rtb_inputnum0;

    /* End of Assignment: '<S31>/Assignment2' */

    /* DataStoreWrite: '<S31>/Data Store Write3' */
    for (i = 0; i < 8; i++) {
      VtCylBalIndSum[(i)] = CombCtrl_DWork.Switch_m[i];
    }

    /* End of DataStoreWrite: '<S31>/Data Store Write3' */

    /* Product: '<S31>/Product' incorporates:
     *  Constant: '<S31>/CYLBALLEARNDUR'
     */
    rtb_Memory = (uint16_T)((CYLBALLEARNDUR == 0U) ? MAX_uint32_T :
      (rtb_inputnum0 / CYLBALLEARNDUR));

    /* Switch: '<S31>/Switch' incorporates:
     *  Constant: '<S31>/Constant1'
     *  Constant: '<S31>/THCYLBALCORRINDAD'
     *  DataStoreRead: '<S31>/Data Store Read2'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  RelationalOperator: '<S31>/Relational Operator1'
     *  Selector: '<S31>/Selector2'
     */
    if (VtEndCylBalLearn[(IonAbsTdcEOA)] != 0) {
      /* Sum: '<S31>/Sum2' incorporates:
       *  Constant: '<S31>/Constant2'
       */
      rtb_DataTypeConversion3 = 32768 - rtb_Memory;

      /* Abs: '<S31>/Abs' */
      if (rtb_DataTypeConversion3 < 0) {
        rtb_Mul1 = (uint16_T)(-rtb_DataTypeConversion3);
      } else {
        rtb_Mul1 = (uint16_T)rtb_DataTypeConversion3;
      }

      /* End of Abs: '<S31>/Abs' */
      TriggerCylBalAdat = (uint8_T)(rtb_Mul1 >= THCYLBALCORRINDAD);
    } else {
      TriggerCylBalAdat = 0U;
    }

    /* End of Switch: '<S31>/Switch' */

    /* Assignment: '<S31>/Assignment1' incorporates:
     *  DataStoreRead: '<S31>/Data Store Read4'
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    for (i = 0; i < 8; i++) {
      CombCtrl_DWork.Assignment3_k[i] = VtCylBalTrigAdat[(i)];
    }

    CombCtrl_DWork.Assignment3_k[IonAbsTdcEOA] = TriggerCylBalAdat;

    /* End of Assignment: '<S31>/Assignment1' */

    /* DataStoreWrite: '<S31>/Data Store Write2' */
    for (i = 0; i < 8; i++) {
      VtCylBalTrigAdat[(i)] = CombCtrl_DWork.Assignment3_k[i];
    }

    /* End of DataStoreWrite: '<S31>/Data Store Write2' */

    /* DataStoreWrite: '<S31>/Data Store Write1' */
    CylBalIndAvgCyl = rtb_Memory;

    /* End of Outputs for SubSystem: '<S21>/TriggerToLearn' */

    /* Outputs for Enabled SubSystem: '<S21>/Up_Ad_Table' incorporates:
     *  EnablePort: '<S32>/Enable (When disabled HOLD  states HOLD outputs)'
     */
    if (TriggerCylBalAdat > 0) {
      /* Outputs for Atomic SubSystem: '<S32>/InterpolateWriteTbCylBalAd' */
      /* Product: '<S38>/Product' incorporates:
       *  Constant: '<S38>/ENKNOCKAD3'
       *  Constant: '<S45>/FixPt Constant'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Sum: '<S45>/FixPt Sum1'
       */
      ofsrCylBal = (uint8_T)(((uint32_T)((uint8_T)(((uint8_T)BKRPMCYLBAL_dim) +
        1U))) * IonAbsTdcEOA);

      /* Gateway: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Calc_indices_ad */
      /* During: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Calc_indices_ad */
      /* Entry Internal: CombCtrl/EOA/Adaptive_Correction/Mgm_Ad_Table/Up_Ad_Table/InterpolateWriteTbCylBalAd/Calc_indices_ad */
      /* Transition: '<S44>:27' */
      /* Transition: '<S44>:1' */
      idx1 = 1U;
      rtb_DataTypeConversion3 = rtb_PreLookUpIdSearch_U16_o1_n + ofsrCylBal;
      indr_cylbal = (uint8_T)rtb_DataTypeConversion3;
      indc_cylbal = (uint8_T)rtb_PreLookUpIdSearch_U16_o1_b;
      eta_cylbal = (int16_T)((65536 - CombCtrl_DWork.PreLookUpIdSearch_U16_o2) >>
        6);
      csi_cylbal = (int16_T)((65536 - CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k)
        >> 6);

      /* Chart: '<S38>/Calc_indices_ad' incorporates:
       *  SubSystem: '<S38>/Write_TbCylBalAd'
       */
      /* Event: '<S44>:24' */
      CombCtrl_Write_TbCylBalAd(rtb_Memory, indr_cylbal, indc_cylbal, eta_cylbal,
        csi_cylbal, CombCtrl_DWork.PreLookUpIdSearch_U16_o2,
        CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k);
      if (rtb_PreLookUpIdSearch_U16_o1_b < ((uint8_T)BKLOADCYLBAL_dim)) {
        /* Transition: '<S44>:2' */
        idx1 = 2U;

        /*  indr_cylbal=  IdxRpmCylBal+ofsrCylBal; */
        indc_cylbal = (uint8_T)(rtb_PreLookUpIdSearch_U16_o1_b + 1);

        /*  eta_cylbal=   (1-RtRpmCylBal); */
        csi_cylbal = (int16_T)(((uint32_T)
          CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k) >> 6);

        /* Outputs for Function Call SubSystem: '<S38>/Write_TbCylBalAd' */

        /* Event: '<S44>:24' */
        CombCtrl_Write_TbCylBalAd(rtb_Memory, indr_cylbal, indc_cylbal,
          eta_cylbal, csi_cylbal, CombCtrl_DWork.PreLookUpIdSearch_U16_o2,
          CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k);

        /* End of Outputs for SubSystem: '<S38>/Write_TbCylBalAd' */
      } else {
        /* Transition: '<S44>:4' */
      }

      if (rtb_PreLookUpIdSearch_U16_o1_n < ((uint8_T)BKRPMCYLBAL_dim)) {
        /* Transition: '<S44>:3' */
        idx1++;
        indr_cylbal = (uint8_T)(rtb_DataTypeConversion3 + 1);
        indc_cylbal = (uint8_T)rtb_PreLookUpIdSearch_U16_o1_b;
        eta_cylbal = (int16_T)(((uint32_T)
          CombCtrl_DWork.PreLookUpIdSearch_U16_o2) >> 6);
        csi_cylbal = (int16_T)((65536 -
          CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k) >> 6);

        /* Outputs for Function Call SubSystem: '<S38>/Write_TbCylBalAd' */

        /* Event: '<S44>:24' */
        CombCtrl_Write_TbCylBalAd(rtb_Memory, indr_cylbal, indc_cylbal,
          eta_cylbal, csi_cylbal, CombCtrl_DWork.PreLookUpIdSearch_U16_o2,
          CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k);

        /* End of Outputs for SubSystem: '<S38>/Write_TbCylBalAd' */
        if (idx1 == 3) {
          /* Transition: '<S44>:5' */
          /* indr_cylbal=   IdxRpmCylBal+1+ofsrCylBal; */
          indc_cylbal = (uint8_T)(rtb_PreLookUpIdSearch_U16_o1_b + 1);

          /*  eta_cylbal=   RtRpmCylBal; */
          csi_cylbal = (int16_T)(((uint32_T)
            CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k) >> 6);

          /* Outputs for Function Call SubSystem: '<S38>/Write_TbCylBalAd' */

          /* Event: '<S44>:24' */
          CombCtrl_Write_TbCylBalAd(rtb_Memory, indr_cylbal, indc_cylbal,
            eta_cylbal, csi_cylbal, CombCtrl_DWork.PreLookUpIdSearch_U16_o2,
            CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k);

          /* End of Outputs for SubSystem: '<S38>/Write_TbCylBalAd' */
        } else {
          /* Transition: '<S44>:6' */
        }
      } else {
        /* Transition: '<S44>:7' */
      }

      /* End of Outputs for SubSystem: '<S32>/InterpolateWriteTbCylBalAd' */

      /* Outputs for Atomic SubSystem: '<S32>/Calc_Ad_Corr' */
      /* DataStoreRead: '<S37>/Data Store Read4' */
      rtb_DataStoreRead4_ie_0 = (&(TbInjCorrCylAd[0]));

      /* Sum: '<S37>/Sum5' incorporates:
       *  Constant: '<S37>/MAXADDCYLBALAD'
       *  Constant: '<S37>/ONE'
       */
      rtb_Memory = (uint16_T)(MAXADDCYLBALAD + 32768U);

      /* Sum: '<S40>/FixPt Sum1' incorporates:
       *  Constant: '<S37>/ENKNOCKAD3'
       *  Constant: '<S40>/FixPt Constant'
       */
      idx1 = (uint8_T)(((uint8_T)BKRPMCYLBAL_dim) + 1U);

      /* Sum: '<S37>/Add1' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Product: '<S37>/Product'
       */
      rtb_Mul1 = (uint16_T)(((uint32_T)((uint8_T)(((uint32_T)IonAbsTdcEOA) *
        idx1))) + rtb_PreLookUpIdSearch_U16_o1_n);

      /* Sum: '<S39>/FixPt Sum1' incorporates:
       *  Constant: '<S37>/ENKNOCKAD2'
       *  Constant: '<S39>/FixPt Constant'
       *  Product: '<S37>/Product1'
       */
      idx1 = (uint8_T)(((uint8_T)(((uint32_T)idx1) * ((uint8_T)N_CYL_MAX))) - 1);

      /* S-Function (Look2D_IR_U16): '<S41>/Look2D_IR_U16' incorporates:
       *  Constant: '<S37>/ENKNOCKAD6'
       */
      Look2D_IR_U16(&rtb_Look2D_IR_U16, (uint16_T*)&rtb_DataStoreRead4_ie_0[0],
                    rtb_Mul1, CombCtrl_DWork.PreLookUpIdSearch_U16_o2, idx1,
                    rtb_PreLookUpIdSearch_U16_o1_b,
                    CombCtrl_DWork.PreLookUpIdSearch_U16_o2_k, ((uint8_T)
        BKLOADCYLBAL_dim));

      /* Outputs for Atomic SubSystem: '<S15>/Calc_Ad_Corr' */
      /* Product: '<S37>/Product2' incorporates:
       *  DataStoreRead: '<S19>/Data Store Read1'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Selector: '<S19>/Selector_Outold6'
       */
      rtb_DataTypeConversion4 = ((((uint32_T)VtInjCorrCylAd[(IonAbsTdcEOA)]) *
        temp) >> 15);

      /* End of Outputs for SubSystem: '<S15>/Calc_Ad_Corr' */
      if (rtb_DataTypeConversion4 > 65535U) {
        rtb_DataTypeConversion4 = 65535U;
      }

      /* Product: '<S37>/Product3' incorporates:
       *  Product: '<S37>/Product2'
       */
      rtb_Mul1 = (uint16_T)((rtb_Look2D_IR_U16 == 0U) ? MAX_uint32_T :
                            ((rtb_DataTypeConversion4 << 15) / rtb_Look2D_IR_U16));

      /* Switch: '<S42>/Switch2' incorporates:
       *  RelationalOperator: '<S42>/LowerRelop1'
       */
      if (!(rtb_Mul1 > rtb_Memory)) {
        /* Sum: '<S37>/Sum1' incorporates:
         *  Constant: '<S37>/MAXADDCYLBALAD'
         *  Constant: '<S37>/ONE2'
         */
        rtb_Memory = (uint16_T)(32768 - MAXADDCYLBALAD);

        /* Switch: '<S42>/Switch' incorporates:
         *  RelationalOperator: '<S42>/UpperRelop'
         */
        if (!(rtb_Mul1 < rtb_Memory)) {
          rtb_Memory = rtb_Mul1;
        }

        /* End of Switch: '<S42>/Switch' */
      }

      /* End of Switch: '<S42>/Switch2' */

      /* Assignment: '<S37>/Assignment1' incorporates:
       *  Constant: '<S37>/ONE1'
       *  DataStoreRead: '<S37>/Data Store Read1'
       *  DataTypeConversion: '<S37>/Data Type Conversion4'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Sum: '<S37>/Sum2'
       */
      for (i = 0; i < 8; i++) {
        CombCtrl_DWork.Assignment1[i] = VtPiFFS[(i)];
      }

      CombCtrl_DWork.Assignment1[IonAbsTdcEOA] = 65536 - (rtb_Memory << 1);

      /* End of Assignment: '<S37>/Assignment1' */

      /* Assignment: '<S37>/Assignment3' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      CombCtrl_DWork.Switch_l[IonAbsTdcEOA] = rtb_Memory;
      for (i = 0; i < 8; i++) {
        /* DataStoreWrite: '<S37>/Data Store Write1' */
        VtPiFFS[(i)] = CombCtrl_DWork.Assignment1[i];

        /* DataStoreWrite: '<S37>/Data Store Write2' incorporates:
         *  DataStoreWrite: '<S37>/Data Store Write1'
         */
        VtInjCorrCyl[(i)] = CombCtrl_DWork.Switch_l[i];
      }

      /* End of Outputs for SubSystem: '<S32>/Calc_Ad_Corr' */
    }

    /* End of Outputs for SubSystem: '<S21>/Up_Ad_Table' */
  } else {
    if (CombCtrl_DWork.Mgm_Ad_Table_MODE) {
      CombCtrl_DWork.Mgm_Ad_Table_MODE = false;
    }
  }

  /* End of Logic: '<S15>/Logical Operator' */
  /* End of Outputs for SubSystem: '<S15>/Mgm_Ad_Table' */
  /* End of Outputs for SubSystem: '<S11>/Adaptive_Correction' */

  /* Outputs for Atomic SubSystem: '<S11>/Adaptive_Correction1' */
  /* Switch: '<S16>/Switch1' incorporates:
   *  Constant: '<S16>/FORCECYLCORR'
   *  Constant: '<S16>/FORCEDCYLCORR'
   *  DataStoreRead: '<S19>/Data Store Read1'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Product: '<S16>/Product2'
   *  Selector: '<S16>/Selector_Outold2'
   *  Selector: '<S19>/Selector_Outold6'
   */
  if (FORCECYLCORR != 0) {
    temp = FORCEDCYLCORR[IonAbsTdcEOA];
  } else {
    /* Outputs for Atomic SubSystem: '<S11>/Adaptive_Correction' */
    /* Outputs for Atomic SubSystem: '<S15>/Calc_Ad_Corr' */
    temp = (uint16_T)((((uint32_T)temp) * VtInjCorrCylAd[(IonAbsTdcEOA)]) >> 15);

    /* End of Outputs for SubSystem: '<S15>/Calc_Ad_Corr' */
    /* End of Outputs for SubSystem: '<S11>/Adaptive_Correction' */
  }

  /* End of Switch: '<S16>/Switch1' */

  /* Assignment: '<S16>/Assignment5' incorporates:
   *  DataStoreRead: '<S16>/Data Store Read7'
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  for (i = 0; i < 8; i++) {
    CombCtrl_DWork.Switch_l[i] = VtInjCorrCylTot[(i)];
  }

  CombCtrl_DWork.Switch_l[IonAbsTdcEOA] = temp;

  /* End of Assignment: '<S16>/Assignment5' */

  /* DataStoreWrite: '<S16>/Data Store Write4' */
  for (i = 0; i < 8; i++) {
    VtInjCorrCylTot[(i)] = CombCtrl_DWork.Switch_l[i];
  }

  /* End of DataStoreWrite: '<S16>/Data Store Write4' */
  /* End of Outputs for SubSystem: '<S11>/Adaptive_Correction1' */

  /* DataTypeConversion: '<S17>/Data Type Conversion6' */
  FFSMedian = CombCtrl_DWork.median;
}

/* Model step function */
void CombCtrl_step(void)
{
  boolean_T tmp;

  /* Outputs for Atomic SubSystem: '<Root>/CombCtrl' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem2' incorporates:
   *  TriggerPort: '<S14>/Trigger'
   */
  /* Inport: '<Root>/ev_EOA' */
  if ((CombCtrl_U.ev_EOA > 0) &&
      (CombCtrl_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE != POS_ZCSIG)) {
    /* S-Function (fcncallgen): '<S14>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/EOA'
     */
    CombCtrl_EOA();

    /* End of Outputs for S-Function (fcncallgen): '<S14>/Function-Call Generator' */
  }

  CombCtrl_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE = (ZCSigState)
    (CombCtrl_U.ev_EOA > 0);

  /* End of Inport: '<Root>/ev_EOA' */
  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem2' */

  /* Outputs for Triggered SubSystem: '<S1>/Triggered Subsystem1' incorporates:
   *  TriggerPort: '<S13>/Trigger'
   */
  /* Inport: '<Root>/ev_PowerOn' */
  CombCtrl_DWork.bv0[0] = (CombCtrl_U.ev_PowerOn > 0);

  /* Inport: '<Root>/ev_NoSync' */
  CombCtrl_DWork.bv0[1] = (CombCtrl_U.ev_NoSync > 0);
  tmp = false;
  for (CombCtrl_DWork.i0_m = 0; CombCtrl_DWork.i0_m < 2; CombCtrl_DWork.i0_m++)
  {
    tmp = (tmp || ((CombCtrl_DWork.bv0[CombCtrl_DWork.i0_m]) &&
                   (CombCtrl_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[CombCtrl_DWork.i0_m]
                    != POS_ZCSIG)));
  }

  if (tmp) {
    /* S-Function (fcncallgen): '<S13>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/Init'
     */
    CombCtrl_Init();

    /* End of Outputs for S-Function (fcncallgen): '<S13>/Function-Call Generator' */
  }

  /* Inport: '<Root>/ev_PowerOn' */
  CombCtrl_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[0] = (ZCSigState)
    (CombCtrl_U.ev_PowerOn > 0);

  /* Inport: '<Root>/ev_NoSync' */
  CombCtrl_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[1] = (ZCSigState)
    (CombCtrl_U.ev_NoSync > 0);

  /* End of Outputs for SubSystem: '<S1>/Triggered Subsystem1' */

  /* End of Outputs for SubSystem: '<Root>/CombCtrl' */
}

/* Model initialize function */
void CombCtrl_initialize(void)
{
  {
    int32_T i;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_1' */
    AvgFFSBank = 32U;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_10' */
    FStabLoadCylBalAd = 1U;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_11' */
    FlgSteadyStateAd = 1U;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_19' */
    CylBalIndAvgCyl = 32768U;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_22' */
    FStabRpmCylBalStateAd = 1U;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_24' */
    FStabLoadCylBalStateAd = 1U;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_30' */
    FlgRecAdCylBalEn = 1U;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_5' */
    FStabRpmCylBalAd = 1U;
    for (i = 0; i < 8; i++) {
      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_13' */
      VtInjCorrCylAd[(i)] = 32768U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_15' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtEndCylBalLearn[(i)] = 1U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_16' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtCntCylBalLearn[(i)] = 1U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_17' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtCylBalLearnState[(i)] = 1U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_18' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtCylBalIndSum[(i)] = 32768U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_2' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      Avg_HR_old_FFS[(i)] = 65536U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_20' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtCylBalTrigAdat[(i)] = 1U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_21' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtInjCorrCylTot[(i)] = 32768U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_25' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtMisfCount[(i)] = 1U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_26' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtFFSErr[(i)] = 32;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_27' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtPiFFS[(i)] = 65536;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_28' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtInjCorrCyl[(i)] = 32768U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_29' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      AvgFFS[(i)] = 32U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_3' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      In_x_N0_FFS[(i)] = 1048576U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_31' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      Avg_HR_FFS[(i)] = 65536U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_4' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      InSum_FFS[(i)] = 1048576U;

      /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_6' incorporates:
       *  DataStoreMemory: '<Root>/_DataStoreBlk_13'
       */
      VtFFSInErr[(i)] = 32;
    }

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_7' */
    FfsErrThr = 32;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_8' */
    LoadEnInjCorr = 128U;

    /* Start for DataStoreMemory: '<Root>/_DataStoreBlk_9' */
    CylBalEn = 1U;
    for (i = 0; i < 2; i++) {
      CombCtrl_PrevZCSigState.TriggeredSubsystem1_Trig_ZCE[i] = POS_ZCSIG;
    }

    CombCtrl_PrevZCSigState.TriggeredSubsystem2_Trig_ZCE = POS_ZCSIG;

    /* SystemInitialize for Atomic SubSystem: '<Root>/CombCtrl' */

    /* SystemInitialize for Triggered SubSystem: '<S1>/Triggered Subsystem2' */

    /* SystemInitialize for S-Function (fcncallgen): '<S14>/Function-Call Generator' incorporates:
     *  SubSystem: '<S1>/EOA'
     */
    CombCtrl_EOA_Init();

    /* End of SystemInitialize for S-Function (fcncallgen): '<S14>/Function-Call Generator' */

    /* End of SystemInitialize for SubSystem: '<S1>/Triggered Subsystem2' */

    /* End of SystemInitialize for SubSystem: '<Root>/CombCtrl' */
  }
}

/* user code (bottom of source file) */
/* System '<Root>/CombCtrl' */
void CombCtrl_NoSync(void)
{
  CombCtrl_Init();
}

#else

uint16_T InjCorrCyl[N_CYL_MAX];
void CombCtrl_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    InjCorrCyl[idx] = 32768;
  }
}

void CombCtrl_NoSync(void)
{
  CombCtrl_Stub();
}

void CombCtrl_Init(void)
{
  CombCtrl_Stub();
}

void CombCtrl_EOA(void)
{
  CombCtrl_Stub();
}

#endif                                 // _BUILD_COMBCTRL

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.3 (R2017b)24-Jul-2017                                             *
 * Simulink 9.0 (R2017b)24-Jul-2017                                           *
 * Simulink Coder 8.13 (R2017b)24-Jul-2017                                    *
 * Embedded Coder 6.13 (R2017b)24-Jul-2017                                    *
 * Stateflow 9.0 (R2017b)24-Jul-2017                                          *
 * Fixed-Point Designer 6.0 (R2017b)24-Jul-2017                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/
