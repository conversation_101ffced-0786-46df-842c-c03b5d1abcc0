/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonKnockPower.c
 **  File Creation Date: 21-Jul-2023
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonKnockPower
 **  Model Description:  IonKnockPower performs different tasks:
   -calculate the knock intensity.
   -calculate the maximum acceptable value for knock intensity (knock threshold).
   -calculate the difference between knock intensity and knock threshold.

   To do this, the module evaluates a nominal spark advance, then normalizes knock integral (calculated in IonKnockInt) according to the ration between thermal peak value and its nominal value,
   finally calculates the knock intensity and the maxim acceptable threshold for knock intensity.
 **  Model Version:      1.1403
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: DelloRussoA - Fri Jul 21 11:35:27 2023
 **
 **  Last Saved Modification:  DelloRussoA - Fri Jul 21 11:31:28 2023
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonKnockPower_out.h"
#include "IonKnockPower_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/Scheduler' */
#define IonKnoc_event_IonKnockPower_EOA (1)
#define Ion_event_IonKnockPower_PowerOn (0)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKETHPERCKNOCK_dim             4U                        /* Referenced by: '<S37>/Constant10' */

/* BKETHPERCKNOCK breakpoint dimension. */
#define BKIONSQRT_dim                  20U                       /* Referenced by:
                                                                  * '<S35>/Constant3'
                                                                  * '<S39>/Constant3'
                                                                  * '<S39>/Constant6'
                                                                  */

/*  BKIONSQRT brekpoint dimension */
#define BKLOADIONKNOCK_dim             4U                        /* Referenced by:
                                                                  * '<S37>/Constant2'
                                                                  * '<S54>/Constant2'
                                                                  * '<S30>/Constant1'
                                                                  */

/* BKLOADIONKNOCK breakpoint dimension. */
#define BKRPMIONKNOCK_dim              11U                       /* Referenced by:
                                                                  * '<S37>/Constant'
                                                                  * '<S37>/Constant9'
                                                                  * '<S54>/Constant1'
                                                                  * '<S30>/Constant'
                                                                  * '<S19>/Constant'
                                                                  * '<S19>/Constant1'
                                                                  */

/* BKRPMIONKNOCK breakpoint dimension. */
#define BKSAIONKNOCK_dim               7U                        /* Referenced by: '<S30>/Constant5' */

/* BKSAIONKNOCK breakpoint dimension. */
#define BKTAIRION_dim                  3U                        /* Referenced by: '<S37>/Constant7' */

/* BKTAIRION breakpoint dimension. */
#define BKTWATION_dim                  5U                        /* Referenced by: '<S37>/Constant5' */

/* BKTWATION breakpoint dimension. */
#define DIV_CONV_16384                 16384U                    /* Referenced by: '<S12>/Constant' */

/* Number for Convert to uA */
#define DTHPEAKGAIN_NEUTRAL            1024U                     /* Referenced by:
                                                                  * '<S4>/Constant17'
                                                                  * '<S15>/Constant1'
                                                                  * '<S15>/Constant4'
                                                                  */

/* Neutral value for DThPeakGain */
#define DTHPEAKGAIN_NEUTRAL_HR         16777216                  /* Referenced by: '<S15>/Constant3' */

/* Neutral value for DThPeakGain_HR */
#define ID_VER_IONKNOCKPOWER_DEF       11403U                    /* Referenced by: '<Root>/Scheduler' */

/* Model Version. */
#define MAX_INT_16                     32767                     /* Referenced by:
                                                                  * '<S38>/Constant'
                                                                  * '<S18>/Constant6'
                                                                  */

/* Maximum value for int16 type. */
#define MAX_INT_32                     2147483647                /* Referenced by: '<S46>/Constant' */

/* Maximum value for int32 type. */
#define MAX_UINT_32                    4294967295U               /* Referenced by:
                                                                  * '<S12>/Constant2'
                                                                  * '<S36>/Constant5'
                                                                  * '<S20>/Constant5'
                                                                  */

/* Maximum value for uint32 type. */
#define MIN_INT_16                     -32768                    /* Referenced by: '<S38>/Constant1' */

/* Minimum value for int16 type. */
#define NORM_OLD                       0U                        /* Referenced by: '<S8>/Constant1' */

/* Normalization type. */
#define PROD_CONV_15625                15625U                    /* Referenced by: '<S12>/Constant4' */

/* Number for Convert to uA */
#define PROD_CONV_25                   25U                       /* Referenced by: '<S12>/Constant1' */

/* Number for Convert to uA */
#define THESHOLD                       128U                      /* Referenced by: '<S20>/Constant1' */

/* Theshold  */
#define TWO_EXP_M30                    1073741824U               /* Referenced by: '<S27>/Constant' */

/* 2^-30 */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONKNOCKPOWER_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint32_T KnockPowNormTmp;       /* '<S11>/Merge7' */

/* Knocking power */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T BKIONSQRT[21] = { 0U, 410U, 819U,
  1229U, 1638U, 2048U, 2458U, 2867U, 3277U, 3686U, 4096U, 8192U, 12288U, 16384U,
  20480U, 40960U, 81920U, 204800U, 409600U, 1228800U, 4096000U } ;/* Referenced by:
                                                                   * '<S39>/Constant2'
                                                                   * '<S39>/Constant5'
                                                                   */

/* KnockInt Breakpoint for VTIONSQRT */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKSAIONKNOCK[8] = { -240, -160, -80, 0,
  80, 160, 240, 320 } ;                /* Referenced by: '<S30>/Constant4' */

/* Breakpoint vector f(SATotCyl) */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKTAIRION[4] = { 640, 960, 1280, 1600 }
;                                      /* Referenced by: '<S37>/Constant6' */

/* Breakpoint vector for inlet air temperature (IonKnock module) */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKTWATION[6] = { -480, 0, 640, 960,
  1280, 1600 } ;                       /* Referenced by: '<S37>/Constant4' */

/* Breakpoint vector for coolant temperature (IonKnock module) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T DTHPEAKRSTLOADTHR = 8960U;/* Referenced by: '<Root>/Constant1' */

/* Load threshold to reset DthPeak in case of IonKnockEnable == 0 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T DTHPEAKRSTRPMTHR = 5000U;/* Referenced by: '<Root>/Constant' */

/* Rpm threshold to reset DthPeak in case of IonKnockEnable == 0 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENTHPEAKNOM = 1U;/* Referenced by: '<S14>/Constant' */

/* Flag to enable KnockInt normalization based on TBTHPEAKNOM */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T IONNORMTYPE = 0U;/* Referenced by: '<S8>/Constant' */

/* Ion Normalization calc Type (0= normalization using SAKnock, 1= normalization using TBSANOM) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T KFKNOCKINT = 1024U;/* Referenced by: '<S46>/Constant2' */

/* Filtering coefficient for KnockIntF */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXDTHPEAKGAIN = 65535U;/* Referenced by:
                                                                   * '<S20>/Constant'
                                                                   * '<S28>/Constant'
                                                                   */

/* Max DThPeakGain */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MINDTHPEAKGAIN = 10U;/* Referenced by: '<S20>/Constant2' */

/* Min DThPeakGain */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T RPMTHRSQRT = 8200U;/* Referenced by:
                                                              * '<S35>/Constant'
                                                              * '<S39>/Constant'
                                                              */

/* Rpm threshold for VTIONSQRT */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TBCORRTHRINT[24] = { 64U, 64U, 64U, 64U,
  64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U, 64U,
  64U, 64U, 64U, 64U } ;               /* Referenced by: '<S37>/Constant3' */

/* Table of gain to calculate the normalised knock power threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBGNTHRKNOCK[60] = { 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
  1024U, 1024U, 1024U } ;              /* Referenced by: '<S37>/Constant8' */

/* Correction gain applied to threshold for EthPerc */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBSANOM[60] = { 30, 30, 30, 30, 30, 30,
  30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30,
  30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30,
  30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30 } ;/* Referenced by: '<S54>/Constant' */

/* Table of nominal SA used for normalization */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBTHPEAKNOM[60] = { 65535U, 65535U,
  65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U,
  65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U,
  65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U,
  65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U,
  65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U,
  65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U } ;/* Referenced by: '<S30>/Constant2' */

/* Table of the ion signal nom ThPeak */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBTHRINTKNOCK[60] = { 486U, 862U,
  1018U, 1176U, 2352U, 2780U, 3474U, 3752U, 4170U, 6144U, 2364U, 7578U, 498U,
  740U, 848U, 1038U, 1870U, 2294U, 2674U, 2814U, 2744U, 4096U, 2124U, 7578U,
  510U, 618U, 680U, 902U, 1390U, 1806U, 1876U, 1876U, 1320U, 2048U, 1890U, 5940U,
  510U, 496U, 510U, 1006U, 1286U, 1598U, 1772U, 1702U, 1216U, 1946U, 1716U,
  3788U, 510U, 496U, 510U, 1112U, 1182U, 1390U, 1668U, 1528U, 1112U, 1740U,
  1536U, 3788U } ;                     /* Referenced by: '<S37>/Constant1' */

/* Table of the normalised knock power threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T VTIONSQRT1[21] = { 0U, 1296U, 1832U,
  2244U, 2590U, 2896U, 3173U, 3427U, 3664U, 3886U, 4096U, 5793U, 7094U, 8192U,
  9159U, 12953U, 18318U, 28963U, 40960U, 70945U, 129527U } ;/* Referenced by:
                                                             * '<S35>/Constant1'
                                                             * '<S39>/Constant7'
                                                             */

/* KnockInt Sqrt Table */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T VTIONSQRT2[21] = { 0U, 1902U, 2395U,
  2742U, 3018U, 3251U, 3455U, 3637U, 3802U, 3955U, 4096U, 5161U, 5907U, 6502U,
  7004U, 8824U, 11118U, 15090U, 19012U, 27420U, 40960U } ;/* Referenced by:
                                                           * '<S35>/Constant2'
                                                           * '<S39>/Constant4'
                                                           */

/* KnockInt Sqrt Table */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTKFILTDTHPEAK[12] = { 16384U, 16384U,
  16384U, 16384U, 16384U, 16384U, 16384U, 16384U, 16384U, 16384U, 16384U, 16384U
} ;                                    /* Referenced by: '<S19>/Constant2' */

/* Vector of filter constants used to filter DThPeak */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTKNOCKCOEFCYL[8] = { 64U, 64U, 64U,
  64U, 64U, 64U, 64U, 64U } ;          /* Referenced by: '<S36>/Constant1' */

/* Vector for knock individual cylinder correction */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTMINDTHPEAKSAT[12] = { 100U, 100U,
  100U, 100U, 100U, 100U, 100U, 100U, 100U, 100U, 100U, 100U } ;/* Referenced by: '<S19>/Constant3' */

/* Vector of Minimum saturation of DThPeak input */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTTHPEAKNOMSAGAIN[8] = { 16384U,
  16384U, 16384U, 16384U, 16384U, 16384U, 16384U, 16384U } ;/* Referenced by: '<S30>/Constant3' */

/* Vector of gain f(SATotCyl) */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
int16_T DSAIon;                        /* '<S9>/Merge1' */

/* Delta between the implemented advance and the expected nominal advance */
int16_T DThPeakCyl;                    /* '<S11>/Merge1' */

/* ThPeakCyl divided for ThPeakNom */
int16_T DeltaKnockNPow[8];             /* '<S10>/Merge11' */

/* Estimated knock intensity minus the target  */
uint32_T KnockInt[8];                  /* '<S10>/Merge8' */

/* Estimated knock Intensity */
uint32_T KnockPowNorm;                 /* '<S10>/Merge1' */

/* Knocking power */
int16_T SATotCyl;                      /* '<S9>/Merge14' */

/* SA Tot Cyl = SARon + SAKnockCyl */
uint32_T ThrIntKnock;                  /* '<S10>/Merge9' */

/* Knock Intensity threshold */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T DThPeak[8]; /* '<S11>/Merge4' */

/* ThPeakCyl divided for ThPeakMax */
STATIC_TEST_POINT uint16_T DThPeakGain;/* '<S11>/Merge6' */

/* Gain applied to KnockInt_cyl */
STATIC_TEST_POINT int32_T DThPeak_HR[8];/* '<S11>/Merge5' */

/* ThPeakCyl divided for ThPeakMax high res */
STATIC_TEST_POINT int16_T DeltaKnockNPowCyl;/* '<S10>/Merge10' */

/* Estimated knock intensity minus the target  */
STATIC_TEST_POINT uint32_T IdVer_IonKnockPower;/* '<Root>/Scheduler' */

/* Model Version */
STATIC_TEST_POINT uint16_T KFiltDThPeak;/* '<S11>/Merge8' */

/* Filter constant used to filter DThPeak */
STATIC_TEST_POINT uint32_T KnockIntCyl;/* '<S10>/Merge7' */

/* Estimated knock Intensity */
STATIC_TEST_POINT int32_T KnockIntF[8];/* '<S10>/Merge12' */

/* Filtered value of KnockInt */
STATIC_TEST_POINT uint32_T KnockPowNormGain;/* '<S11>/Merge13' */

/* Knocking power multiplied by DThPeakGain */
STATIC_TEST_POINT uint16_T MinDThPeakSat;/* '<S11>/Merge9' */

/* Saturation of DThPeak input */
STATIC_TEST_POINT int16_T SANom;       /* '<S9>/Merge15' */

/* Output of TBSANOM */
STATIC_TEST_POINT uint16_T ThPeakNom;  /* '<S11>/Merge3' */

/* Output of TBTHPEAKNOM */
STATIC_TEST_POINT uint32_T ThPeakNomTot;/* '<S11>/Merge2' */

/* ThPeakNomTot = ThPeakNom * ThPeakNomSAGain */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void IonK_chartstep_c4_IonKnockPower(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/Scheduler' */
static void IonK_chartstep_c4_IonKnockPower(const int32_T *sfEvent)
{
  /* local block i/o variables */
  uint32_T rtb_LookUp_U32_U32;
  uint32_T rtb_LookUp_U32_U32_g;
  uint32_T rtb_sqrtU32;
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  uint16_T rtb_Look2D_U8_S16_S16;
  uint16_T rtb_Look2D_IR_U16;
  uint16_T rtb_Look2D_IR_U16_e;
  uint16_T rtb_Look2D_IR_U16_p;
  uint16_T rtb_LookUp_U16_S16;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_LookUp_IR_U16_l;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  int16_T rtb_Look2D_IR_S8;
  uint64_T rtb_DataTypeConversion;
  uint64_T rtb_DataTypeConversion3;
  uint32_T rtb_Divide_mo;
  int64_T rtb_Divide_a;
  int64_T rtb_DataTypeConversion_i;
  int16_T rtb_Switch1;
  int32_T i;

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules model runnables, according to the following events:
   *  -Power On task;
   *  -End of Acquisition task.
   *  The chart enable output evaluation only if knock detection strategy is enabled.
   */
  /* Chart: '<Root>/Scheduler' incorporates:
   *  Constant: '<Root>/Constant'
   *  Constant: '<Root>/Constant1'
   *  Inport: '<Root>/IonKnockEnabled'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/Rpm'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules model runnables, according to the following events:
   *  -Power On task;
   *  -End of Acquisition task.
   *  The chart enable output evaluation only if knock detection strategy is enabled.
   */
  /* During: Scheduler */
  /* This chart schedules model runnables, according to the following events:
     -Power On task.
     -End of Acquisition task.
     The chart enable output evaluation only if knock detection strategy is enabled. */
  /* Entry Internal: Scheduler */
  /* Transition: '<S2>:37' */
  if ((*sfEvent) == ((int32_T)Ion_event_IonKnockPower_PowerOn)) {
    /* Outputs for Function Call SubSystem: '<S1>/InitSATotCyl'
     *
     * Block description for '<S1>/InitSATotCyl':
     *  The aim of this block is to initialize the total spark advance
     *  correction used to calculate the nominal thermal peak value.
     */
    /* SignalConversion generated from: '<S6>/SATotCyl' incorporates:
     *  Constant: '<S6>/Constant14'
     */
    /* Transition: '<S2>:8' */
    /* Transition: '<S2>:19'
     * Requirements for Transition: '<S2>:19':
     *  1. EISB_FCA6CYL_SW_REQ_1645: Software shall set to 0 each output produced for Power Evaluation ... (ECU_SW_Requirements#3093)
     */
    /* Initialize Output */
    /* Event: '<S2>:4' */
    SATotCyl = 0;

    /* SignalConversion generated from: '<S6>/SANom' incorporates:
     *  Constant: '<S6>/Constant15'
     */
    SANom = 0;

    /* SignalConversion generated from: '<S6>/DSAIon' incorporates:
     *  Constant: '<S6>/Constant1'
     */
    DSAIon = 0;

    /* End of Outputs for SubSystem: '<S1>/InitSATotCyl' */

    /* Outputs for Function Call SubSystem: '<S1>/InitGain'
     *
     * Block description for '<S1>/InitGain':
     *  This block initializes outputs related to the evalutation of gain used
     *  to normalize knock power.
     */
    /* MinMax: '<S18>/MinMax1' incorporates:
     *  Constant: '<S4>/Constant1'
     *  SignalConversion generated from: '<S4>/DThPeakCyl'
     *
     * Block requirements for '<S18>/MinMax1':
     *  1. EISB_FCA6CYL_SW_REQ_1177: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1198)
     */
    /* Event: '<S2>:32' */
    DThPeakCyl = 0;

    /* SignalConversion generated from: '<S4>/ThPeakNomTot' incorporates:
     *  Constant: '<S4>/Constant2'
     */
    ThPeakNomTot = 0U;

    /* SignalConversion generated from: '<S4>/ThPeakNom' incorporates:
     *  Constant: '<S4>/Constant3'
     */
    ThPeakNom = 0U;

    /* MinMax: '<S20>/MinMax1' incorporates:
     *  Constant: '<S4>/Constant17'
     *  DataTypeConversion: '<S4>/Data Type Conversion'
     */
    DThPeakGain = ((uint16_T)DTHPEAKGAIN_NEUTRAL);

    /* SignalConversion generated from: '<S4>/KnockPowNormGain' incorporates:
     *  Constant: '<S4>/Constant13'
     */
    KnockPowNormGain = 0U;

    /* SignalConversion generated from: '<S4>/KnockPowNormTmp' incorporates:
     *  Constant: '<S4>/Constant'
     */
    KnockPowNormTmp = 0U;

    /* SignalConversion generated from: '<S4>/KFiltDThPeak' incorporates:
     *  Constant: '<S4>/Constant5'
     */
    KFiltDThPeak = 0U;

    /* SignalConversion generated from: '<S4>/MinDThPeakSat' incorporates:
     *  Constant: '<S4>/Constant7'
     */
    MinDThPeakSat = 0U;

    /* Outputs for Function Call SubSystem: '<S1>/InitKnockIntEOA'
     *
     * Block description for '<S1>/InitKnockIntEOA':
     *  This block performs reset of output signal when knock detection
     *  strategy is disabled.
     */
    /* Event: '<S2>:33' */
    for (i = 0; i < 8; i++) {
      /* SignalConversion generated from: '<S4>/DThPeak' */
      DThPeak[(i)] = 1024U;

      /* SignalConversion generated from: '<S4>/DThPeak_HR' */
      DThPeak_HR[(i)] = 16777216;

      /* SignalConversion generated from: '<S5>/KnockInt' */
      KnockInt[(i)] = 0U;

      /* SignalConversion generated from: '<S5>/DeltaKnockNPow' */
      DeltaKnockNPow[(i)] = 0;

      /* SignalConversion generated from: '<S5>/KnockIntF' */
      KnockIntF[(i)] = 0;
    }

    /* End of Outputs for SubSystem: '<S1>/InitGain' */

    /* SignalConversion generated from: '<S5>/KnockIntCyl' incorporates:
     *  Constant: '<S5>/Constant7'
     */
    KnockIntCyl = 0U;

    /* SignalConversion generated from: '<S5>/ThrIntKnock' incorporates:
     *  Constant: '<S5>/Constant9'
     */
    ThrIntKnock = 0U;

    /* SignalConversion generated from: '<S5>/DeltaKnockNPowCyl' incorporates:
     *  Constant: '<S5>/Constant10'
     */
    DeltaKnockNPowCyl = 0;

    /* SignalConversion generated from: '<S5>/KnockPowNorm' incorporates:
     *  Constant: '<S5>/Constant'
     */
    KnockPowNorm = 0U;

    /* End of Outputs for SubSystem: '<S1>/InitKnockIntEOA' */
    IdVer_IonKnockPower = ID_VER_IONKNOCKPOWER_DEF;
  } else {
    /* Outputs for Function Call SubSystem: '<S1>/SATotCylEOA'
     *
     * Block description for '<S1>/SATotCylEOA':
     *  The aim of this block is to calculate the total spark advance correction used to calculate the nominal thermal peak value.
     *  Two different strategies are available according to the value of IONNORMTYPE calibration.
     */
    /* Outputs for Atomic SubSystem: '<S8>/IfActionSubsystem2'
     *
     * Block description for '<S8>/IfActionSubsystem2':
     *  This block models total spark advance correction according to the following formula:
     *  SATotCyl = SAoutCyl - TBSANOM(rpm,load)
     *  where SAoutCyl is the last spard advance correction and TBSANOM is a correction offset.
     *
     * Block requirements for '<S8>/IfActionSubsystem2':
     *  1. EISB_FCA6CYL_SW_REQ_1173: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1193)
     */
    /* S-Function (Look2D_IR_S8): '<S56>/Look2D_IR_S8' incorporates:
     *  Constant: '<S54>/Constant'
     *  Constant: '<S54>/Constant1'
     *  Constant: '<S54>/Constant2'
     */
    /* Transition: '<S2>:10' */
    /* IonKnockPower_EOA  */
    /* Event: '<S2>:31' */
    Look2D_IR_S8( &rtb_Look2D_IR_S8, &TBSANOM[0], IonKnockRpmIndex,
                 IonKnockRpmRatio, ((uint8_T)BKRPMIONKNOCK_dim),
                 IonKnockLoadIndex, IonKnockLoadRatio, ((uint8_T)
      BKLOADIONKNOCK_dim));

    /* Product: '<S55>/Divide' */
    SANom = (int16_T)(rtb_Look2D_IR_S8 / 32);

    /* Sum: '<S54>/Add' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Inport: '<Root>/SAoutCyl'
     *  MultiPortSwitch: '<S54>/Index Vector1'
     */
    DSAIon = (int16_T)(SAoutCyl[(IonAbsTdcEOA)] - SANom);

    /* End of Outputs for SubSystem: '<S8>/IfActionSubsystem2' */

    /* Switch: '<S8>/Switch' incorporates:
     *  Constant: '<S8>/Constant'
     *  Constant: '<S8>/Constant1'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Inport: '<Root>/SAKnock'
     *  Inport: '<Root>/SARon'
     *  MultiPortSwitch: '<S53>/Index Vector1'
     *  RelationalOperator: '<S8>/Relational Operator'
     *  Sum: '<S53>/Add'
     */
    if (IONNORMTYPE == ((uint8_T)NORM_OLD)) {
      /* Outputs for Atomic SubSystem: '<S8>/IfActionSubsystem'
       *
       * Block description for '<S8>/IfActionSubsystem':
       *  This block evaluates the total spark advance correction according to the following formula:
       *  SATotCyl = SARon +SAKnock
       *  spark advance correction for ron level + spark advance correction for knock events.
       *
       * Block requirements for '<S8>/IfActionSubsystem':
       *  1. EISB_FCA6CYL_SW_REQ_1172: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1192)
       */
      SATotCyl = (int16_T)(SARon + SAKnock[(IonAbsTdcEOA)]);

      /* End of Outputs for SubSystem: '<S8>/IfActionSubsystem' */
    } else {
      SATotCyl = DSAIon;
    }

    /* End of Switch: '<S8>/Switch' */
    /* End of Outputs for SubSystem: '<S1>/SATotCylEOA' */
    if (((int32_T)IonKnockEnabled) != 0) {
      /* Outputs for Function Call SubSystem: '<S1>/GainEOA'
       *
       * Block description for '<S1>/GainEOA':
       *  This block nomalizes knock power through a normalization gain which is
       *  the ratio between thermal peak value and its nominal value.
       */
      /* DataTypeConversion: '<S12>/Data Type Conversion' incorporates:
       *  Constant: '<S12>/Constant2'
       */
      /* Transition: '<S2>:12' */
      /* Transition: '<S2>:15' */
      /*  Output evaluation  */
      /* Event: '<S2>:5' */
      rtb_DataTypeConversion = (uint64_T)MAX_UINT_32;

      /* DataTypeConversion: '<S12>/Data Type Conversion3' incorporates:
       *  Constant: '<S12>/Constant'
       *  Constant: '<S12>/Constant1'
       *  Constant: '<S12>/Constant4'
       *  DataTypeConversion: '<S12>/Data Type Conversion2'
       *  Inport: '<Root>/IonGainEOA'
       *  Inport: '<Root>/KnPowNormFft'
       *  Product: '<S12>/Divide'
       *  Product: '<S12>/Product2'
       *  Product: '<S12>/Product3'
       *  Product: '<S12>/mul2'
       *  Product: '<S12>/mult'
       *
       * Block requirements for '<S12>/mul2':
       *  1. EISB_FCA6CYL_SW_REQ_1171: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1191)
       */
      rtb_DataTypeConversion3 = ((((((((uint64_T)((uint32_T)(((uint32_T)
        IonGainEOA) * ((uint32_T)((uint16_T)PROD_CONV_15625))))) * ((uint64_T)
        KnPowNormFft)) / ((uint64_T)((uint16_T)DIV_CONV_16384))) >> ((uint64_T)
        12)) * ((uint64_T)IonGainEOA)) * ((uint64_T)((uint8_T)PROD_CONV_25))) >>
        ((uint64_T)12));

      /* MinMax: '<S12>/MinMax' */
      if (rtb_DataTypeConversion3 < rtb_DataTypeConversion) {
        /* DataTypeConversion: '<S12>/Data Type Conversion1' */
        KnockPowNormTmp = (uint32_T)rtb_DataTypeConversion3;
      } else {
        /* DataTypeConversion: '<S12>/Data Type Conversion1' */
        KnockPowNormTmp = (uint32_T)rtb_DataTypeConversion;
      }

      /* End of MinMax: '<S12>/MinMax' */

      /* If: '<S14>/If' incorporates:
       *  Constant: '<S14>/Constant'
       *  Constant: '<S14>/Constant2'
       *  RelationalOperator: '<S14>/Relational Operator'
       */
      if (((int32_T)ENTHPEAKNOM) == 0) {
        /* Outputs for IfAction SubSystem: '<S14>/Reset_ThPeakNomTot' incorporates:
         *  ActionPort: '<S29>/Action Port'
         *
         * Block description for '<S14>/Reset_ThPeakNomTot':
         *  Nominal value of thermal peak is resetted if ENTHPEAKNOM is 0.
         *
         * Block requirements for '<S14>/Reset_ThPeakNomTot':
         *  1. EISB_FCA6CYL_SW_REQ_1175: Software shall set to zero the value of nominal thermal peak  (i.e... (ECU_SW_Requirements#1195)
         */
        /* SignalConversion generated from: '<S29>/ThPeakNom' incorporates:
         *  Constant: '<S29>/Constant2'
         */
        ThPeakNom = 0U;

        /* SignalConversion generated from: '<S29>/ThPeakNomTot' incorporates:
         *  Constant: '<S29>/Constant1'
         */
        ThPeakNomTot = 0U;

        /* End of Outputs for SubSystem: '<S14>/Reset_ThPeakNomTot' */
      } else {
        /* Outputs for IfAction SubSystem: '<S14>/ThPeak_Model' incorporates:
         *  ActionPort: '<S30>/Action Port'
         *
         * Block description for '<S14>/ThPeak_Model':
         *  This block implements a model for nominal value of thermal peak.
         *  The model is build up by means of a table, function of engine speed and load, and a correction gain function of SATotCyl.
         *
         * Block requirements for '<S14>/ThPeak_Model':
         *  1. EISB_FCA6CYL_SW_REQ_1174: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1194)
         */
        /* S-Function (Look2D_IR_U16): '<S31>/Look2D_IR_U16' incorporates:
         *  Constant: '<S30>/Constant'
         *  Constant: '<S30>/Constant1'
         *  Constant: '<S30>/Constant2'
         */
        Look2D_IR_U16( &rtb_Look2D_IR_U16_p, &TBTHPEAKNOM[0], IonKnockRpmIndex,
                      IonKnockRpmRatio, ((uint8_T)BKRPMIONKNOCK_dim),
                      IonKnockLoadIndex, IonKnockLoadRatio, ((uint8_T)
          BKLOADIONKNOCK_dim));

        /* S-Function (LookUp_U16_S16): '<S32>/LookUp_U16_S16' incorporates:
         *  Constant: '<S30>/Constant3'
         *  Constant: '<S30>/Constant4'
         *  Constant: '<S30>/Constant5'
         */
        LookUp_U16_S16( &rtb_LookUp_U16_S16, &VTTHPEAKNOMSAGAIN[0], SATotCyl,
                       &BKSAIONKNOCK[0], ((uint8_T)BKSAIONKNOCK_dim));

        /* Product: '<S30>/Product' */
        ThPeakNomTot = ((((uint32_T)rtb_Look2D_IR_U16_p) * ((uint32_T)
          rtb_LookUp_U16_S16)) >> ((uint64_T)10));

        /* SignalConversion generated from: '<S30>/ThPeakNom' */
        ThPeakNom = rtb_Look2D_IR_U16_p;

        /* End of Outputs for SubSystem: '<S14>/ThPeak_Model' */
      }

      /* End of If: '<S14>/If' */

      /* If: '<S13>/If' incorporates:
       *  RelationalOperator: '<S13>/Relational Operator'
       */
      if (ThPeakNomTot <= 0U) {
        /* Outputs for IfAction SubSystem: '<S13>/IfActionSubsystem' incorporates:
         *  ActionPort: '<S15>/Action Port'
         *
         * Block description for '<S13>/IfActionSubsystem':
         *  No normalization is performed if nominal value of thermal peak is
         *  lower than zero (protection against bad calculation)
         *
         * Block requirements for '<S13>/IfActionSubsystem':
         *  1. EISB_FCA6CYL_SW_REQ_1185: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1201)
         */
        /* Assignment: '<S15>/Assignment1' incorporates:
         *  Constant: '<S15>/Constant3'
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        DThPeak_HR[(IonAbsTdcEOA)] = DTHPEAKGAIN_NEUTRAL_HR;

        /* MinMax: '<S18>/MinMax1' incorporates:
         *  Constant: '<S15>/Constant2'
         *  SignalConversion generated from: '<S15>/DThPeakCyl'
         *
         * Block requirements for '<S18>/MinMax1':
         *  1. EISB_FCA6CYL_SW_REQ_1177: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1198)
         */
        DThPeakCyl = 0;

        /* SignalConversion generated from: '<S15>/KFiltDThPeak' incorporates:
         *  Constant: '<S15>/Constant5'
         */
        KFiltDThPeak = 0U;

        /* SignalConversion generated from: '<S15>/MinDThPeakSat' incorporates:
         *  Constant: '<S15>/Constant6'
         */
        MinDThPeakSat = 0U;

        /* Assignment: '<S15>/Assignment2' incorporates:
         *  Constant: '<S15>/Constant1'
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        DThPeak[(IonAbsTdcEOA)] = ((uint16_T)DTHPEAKGAIN_NEUTRAL);

        /* MinMax: '<S20>/MinMax1' incorporates:
         *  Constant: '<S15>/Constant4'
         *  DataTypeConversion: '<S15>/Data Type Conversion1'
         */
        DThPeakGain = ((uint16_T)DTHPEAKGAIN_NEUTRAL);

        /* DataTypeConversion: '<S16>/Conversion' incorporates:
         *  SignalConversion: '<S15>/Copy'
         */
        KnockPowNormGain = KnockPowNormTmp;

        /* End of Outputs for SubSystem: '<S13>/IfActionSubsystem' */
      } else {
        /* Outputs for IfAction SubSystem: '<S13>/calc_ThPeakNomTot2' incorporates:
         *  ActionPort: '<S17>/Action Port'
         *
         * Block description for '<S13>/calc_ThPeakNomTot2':
         *  This block nomalizes knock power through a normalization gain which is
         *  the ratio between thermal peak value and its nominal value
         */
        /* S-Function (LookUp_IR_U16): '<S23>/LookUp_IR_U16' incorporates:
         *  Constant: '<S19>/Constant1'
         *  Constant: '<S19>/Constant3'
         *
         * Block requirements for '<S19>/Constant3':
         *  1. EISB_FCA6CYL_SW_REQ_1178: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1197)
         */
        LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTMINDTHPEAKSAT[0], IonKnockRpmIndex,
                      IonKnockRpmRatio, ((uint8_T)BKRPMIONKNOCK_dim));

        /* Product: '<S18>/Divide' incorporates:
         *  DataTypeConversion: '<S18>/IncrementPrecisionBeforeDivision'
         *  Inport: '<Root>/ThPeakCyl'
         *
         * Block requirements for '<S18>/Divide':
         *  1. EISB_FCA6CYL_SW_REQ_1176: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1196)
         */
        rtb_Divide_mo = (ThPeakCyl << ((uint64_T)10)) / ThPeakNomTot;

        /* MinMax: '<S18>/MinMax' incorporates:
         *  DataTypeConversion: '<S18>/DataTypeConversion2'
         */
        if (((uint32_T)rtb_LookUp_IR_U16) > rtb_Divide_mo) {
          i = (int32_T)rtb_LookUp_IR_U16;
        } else {
          i = (int32_T)rtb_Divide_mo;
        }

        /* End of MinMax: '<S18>/MinMax' */

        /* Switch: '<S18>/Switch1' incorporates:
         *  Constant: '<S18>/Constant6'
         *  DataTypeConversion: '<S18>/DataTypeConversion3'
         *  RelationalOperator: '<S18>/Relational Operator1'
         */
        if (((uint32_T)i) < ((uint32_T)((int16_T)MAX_INT_16))) {
          rtb_Switch1 = (int16_T)i;
        } else {
          rtb_Switch1 = ((int16_T)MAX_INT_16);
        }

        /* End of Switch: '<S18>/Switch1' */

        /* S-Function (LookUp_IR_U16): '<S24>/LookUp_IR_U16' incorporates:
         *  Constant: '<S19>/Constant'
         *  Constant: '<S19>/Constant2'
         *
         * Block requirements for '<S19>/Constant2':
         *  1. EISB_FCA6CYL_SW_REQ_1649: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#3101)
         */
        LookUp_IR_U16( &rtb_LookUp_IR_U16_l, &VTKFILTDTHPEAK[0],
                      IonKnockRpmIndex, IonKnockRpmRatio, ((uint8_T)
          BKRPMIONKNOCK_dim));

        /* MultiPortSwitch: '<S18>/Index Vector1' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         *  SignalConversion generated from: '<S3>/DThPeak_HR_old'
         */
        i = DThPeak_HR[(IonAbsTdcEOA)];

        /* S-Function (FOF_Reset_S16_FXP): '<S21>/FOF_Reset_S16_FXP' incorporates:
         *  Constant: '<S18>/Constant'
         */
        FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
                          rtb_Switch1, rtb_LookUp_IR_U16_l, rtb_Switch1,
                          ((uint8_T)0U), i);

        /* MinMax: '<S18>/MinMax1' incorporates:
         *  Constant: '<S18>/Constant1'
         *
         * Block requirements for '<S18>/MinMax1':
         *  1. EISB_FCA6CYL_SW_REQ_1177: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1198)
         */
        if (rtb_FOF_Reset_S16_FXP_o1 > 0) {
          DThPeakCyl = rtb_FOF_Reset_S16_FXP_o1;
        } else {
          DThPeakCyl = 0;
        }

        /* If: '<S20>/If' incorporates:
         *  Constant: '<S20>/Constant1'
         *  MinMax: '<S18>/MinMax1'
         *  RelationalOperator: '<S20>/Relational Operator'
         *
         * Block requirements for '<S18>/MinMax1':
         *  1. EISB_FCA6CYL_SW_REQ_1177: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1198)
         */
        if (((int32_T)DThPeakCyl) <= ((int32_T)((uint16_T)THESHOLD))) {
          /* Outputs for IfAction SubSystem: '<S20>/IfActionSubsystem1' incorporates:
           *  ActionPort: '<S28>/Action Port'
           *
           * Block description for '<S20>/IfActionSubsystem1':
           *  DThPeakGain takes the value from MAXDTHPEAKGAIN
           */
          /* MinMax: '<S20>/MinMax1' incorporates:
           *  Constant: '<S28>/Constant'
           *  SignalConversion generated from: '<S28>/DThPeakGain'
           */
          DThPeakGain = MAXDTHPEAKGAIN;

          /* End of Outputs for SubSystem: '<S20>/IfActionSubsystem1' */
        } else {
          /* Outputs for IfAction SubSystem: '<S20>/IfActionSubsystem' incorporates:
           *  ActionPort: '<S27>/Action Port'
           *
           * Block description for '<S20>/IfActionSubsystem':
           *  DThPeakGain is calculated from DThPeakCyl
           */
          /* MinMax: '<S20>/MinMax1' incorporates:
           *  Constant: '<S27>/Constant'
           *  DataTypeConversion: '<S27>/Data Type Conversion1'
           *  Product: '<S27>/Divide'
           *  Product: '<S27>/Divide1'
           */
          DThPeakGain = (uint16_T)((TWO_EXP_M30 / ((uint32_T)((uint16_T)
            DThPeakCyl))) / ((uint32_T)((uint16_T)DThPeakCyl)));

          /* End of Outputs for SubSystem: '<S20>/IfActionSubsystem' */
        }

        /* End of If: '<S20>/If' */

        /* MinMax: '<S20>/MinMax1' incorporates:
         *  Constant: '<S20>/Constant2'
         */
        if (DThPeakGain <= MINDTHPEAKGAIN) {
          DThPeakGain = MINDTHPEAKGAIN;
        }

        /* MinMax: '<S20>/MinMax' incorporates:
         *  Constant: '<S20>/Constant'
         *  MinMax: '<S20>/MinMax1'
         *
         * Block requirements for '<S20>/MinMax':
         *  1. EISB_FCA6CYL_SW_REQ_1179: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1199)
         */
        if (DThPeakGain >= MAXDTHPEAKGAIN) {
          DThPeakGain = MAXDTHPEAKGAIN;
        }

        /* SignalConversion generated from: '<S17>/KFiltDThPeak' */
        KFiltDThPeak = rtb_LookUp_IR_U16_l;

        /* SignalConversion generated from: '<S17>/MinDThPeakSat' */
        MinDThPeakSat = rtb_LookUp_IR_U16;

        /* Assignment: '<S18>/Assignment1' incorporates:
         *  DataTypeConversion: '<S18>/Data Type Conversion4'
         *  Inport: '<Root>/IonAbsTdcEOA'
         *  MinMax: '<S18>/MinMax1'
         *
         * Block requirements for '<S18>/MinMax1':
         *  1. EISB_FCA6CYL_SW_REQ_1177: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1198)
         */
        DThPeak[(IonAbsTdcEOA)] = (uint16_T)DThPeakCyl;

        /* Assignment: '<S18>/Assignment2' incorporates:
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        DThPeak_HR[(IonAbsTdcEOA)] = rtb_FOF_Reset_S16_FXP_o2;

        /* Product: '<S20>/Product' incorporates:
         *  MinMax: '<S20>/MinMax'
         *
         * Block requirements for '<S20>/Product':
         *  1. EISB_FCA6CYL_SW_REQ_1180: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1200)
         *
         * Block requirements for '<S20>/MinMax':
         *  1. EISB_FCA6CYL_SW_REQ_1179: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1199)
         */
        rtb_DataTypeConversion = ((((uint64_T)KnockPowNormTmp) * ((uint64_T)
          DThPeakGain)) >> ((uint64_T)10));

        /* DataTypeConversion: '<S20>/Data Type Conversion2' incorporates:
         *  Constant: '<S20>/Constant5'
         */
        rtb_DataTypeConversion3 = (uint64_T)MAX_UINT_32;

        /* MinMax: '<S20>/MinMax2' */
        if (rtb_DataTypeConversion < rtb_DataTypeConversion3) {
          /* DataTypeConversion: '<S16>/Conversion' incorporates:
           *  DataTypeConversion: '<S20>/Data Type Conversion'
           */
          KnockPowNormGain = (uint32_T)rtb_DataTypeConversion;
        } else {
          /* DataTypeConversion: '<S16>/Conversion' incorporates:
           *  DataTypeConversion: '<S20>/Data Type Conversion'
           */
          KnockPowNormGain = (uint32_T)rtb_DataTypeConversion3;
        }

        /* End of MinMax: '<S20>/MinMax2' */
        /* End of Outputs for SubSystem: '<S13>/calc_ThPeakNomTot2' */
      }

      /* End of If: '<S13>/If' */
      /* End of Outputs for SubSystem: '<S1>/GainEOA' */

      /* Outputs for Function Call SubSystem: '<S1>/KnockIntEOA'
       *
       * Block description for '<S1>/KnockIntEOA':
       *  The aim of this block is to:
       *  -calculate the knock intensity.
       *  -calculate the maximum acceptable value for knock intensity before detecting a knock (knock threshold).
       *  -calculate the difference between knock intensity and knock threshold.
       */
      /* SignalConversion: '<S35>/Copy' */
      /* Event: '<S2>:35' */
      KnockPowNorm = KnockPowNormTmp;

      /* MultiPortSwitch: '<S35>/Index Vector' incorporates:
       *  Constant: '<S35>/Constant'
       *  Constant: '<S35>/Constant1'
       *  Constant: '<S35>/Constant2'
       *  Constant: '<S35>/Constant3'
       *  Inport: '<Root>/Rpm'
       *  RelationalOperator: '<S35>/RelationalOperator'
       *  Switch: '<S35>/Switch'
       */
      if (Rpm < RPMTHRSQRT) {
        rtb_Divide_mo = VTIONSQRT1[(((uint8_T)BKIONSQRT_dim))];
      } else {
        rtb_Divide_mo = VTIONSQRT2[(((uint8_T)BKIONSQRT_dim))];
      }

      /* End of MultiPortSwitch: '<S35>/Index Vector' */

      /* If: '<S35>/If' incorporates:
       *  Constant: '<S39>/Constant'
       *  Inport: '<Root>/Rpm'
       *  RelationalOperator: '<S35>/Relational Operator1'
       *  RelationalOperator: '<S39>/RelationalOperator'
       *  Switch: '<S39>/Switch1'
       */
      if (rtb_Divide_mo == 0U) {
        /* Outputs for IfAction SubSystem: '<S35>/IfActionSubsystem1' incorporates:
         *  ActionPort: '<S40>/Action Port'
         *
         * Block description for '<S35>/IfActionSubsystem1':
         *  KnockIntCyl is calculated from KnockPowNormGain
         */
        /* S-Function (sqrtU32): '<S45>/sqrtU32' */
        rtb_sqrtU32 = sqrtU32( KnockPowNormGain);

        /* DataTypeConversion: '<S40>/Data Type Conversion' */
        KnockIntCyl = (rtb_sqrtU32 << ((uint64_T)6));

        /* End of Outputs for SubSystem: '<S35>/IfActionSubsystem1' */
      } else {
        /* Outputs for IfAction SubSystem: '<S35>/IfActionSubsystem' incorporates:
         *  ActionPort: '<S39>/Action Port'
         *
         * Block description for '<S35>/IfActionSubsystem':
         *  KnockIntCyl is calulated by differents table:
         *  Rpm<RPMTHRSQRT is used VTIONSQRT1 cal for points
         *  Rpm>= RPMTHRSQRT is used VTIONSQRT2 cal for points
         *
         * Block requirements for '<S35>/IfActionSubsystem':
         *  1. EISB_FCA6CYL_SW_REQ_1183: Every time that knock detection strategy is enabled and that engin... (ECU_SW_Requirements#1203)
         *  2. EISB_FCA6CYL_SW_REQ_1181: Every time that knock detection strategy is enabled and that engin... (ECU_SW_Requirements#1202)
         */
        if (Rpm < RPMTHRSQRT) {
          /* DataTypeConversion: '<S41>/Conversion3' incorporates:
           *  Constant: '<S39>/Constant6'
           *  Switch: '<S39>/Switch1'
           */
          rtb_Divide_mo = (uint32_T)((uint8_T)BKIONSQRT_dim);

          /* S-Function (LookUp_U32_U32): '<S41>/LookUp_U32_U32' incorporates:
           *  Constant: '<S39>/Constant5'
           *  Constant: '<S39>/Constant7'
           *  Switch: '<S39>/Switch1'
           *
           * Block requirements for '<S39>/Constant7':
           *  1. EISB_FCA6CYL_SW_REQ_1181: Every time that knock detection strategy is enabled and that engin... (ECU_SW_Requirements#1202)
           */
          LookUp_U32_U32( &rtb_LookUp_U32_U32, &VTIONSQRT1[0], KnockPowNormGain,
                         &BKIONSQRT[0], rtb_Divide_mo);

          /* Switch: '<S39>/Switch1' */
          KnockIntCyl = rtb_LookUp_U32_U32;
        } else {
          /* DataTypeConversion: '<S42>/Conversion3' incorporates:
           *  Constant: '<S39>/Constant3'
           *  Switch: '<S39>/Switch1'
           */
          rtb_Divide_mo = (uint32_T)((uint8_T)BKIONSQRT_dim);

          /* S-Function (LookUp_U32_U32): '<S42>/LookUp_U32_U32' incorporates:
           *  Constant: '<S39>/Constant2'
           *  Constant: '<S39>/Constant4'
           *  Switch: '<S39>/Switch1'
           *
           * Block requirements for '<S39>/Constant4':
           *  1. EISB_FCA6CYL_SW_REQ_1183: Every time that knock detection strategy is enabled and that engin... (ECU_SW_Requirements#1203)
           */
          LookUp_U32_U32( &rtb_LookUp_U32_U32_g, &VTIONSQRT2[0],
                         KnockPowNormGain, &BKIONSQRT[0], rtb_Divide_mo);

          /* Switch: '<S39>/Switch1' */
          KnockIntCyl = rtb_LookUp_U32_U32_g;
        }

        /* End of Outputs for SubSystem: '<S35>/IfActionSubsystem' */
      }

      /* End of If: '<S35>/If' */

      /* Product: '<S36>/Product' incorporates:
       *  Constant: '<S36>/Constant1'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  MultiPortSwitch: '<S36>/Index Vector'
       *
       * Block requirements for '<S36>/Product':
       *  1. EISB_FCA6CYL_SW_REQ_1184: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1204)
       */
      rtb_DataTypeConversion = ((((uint64_T)KnockIntCyl) * ((uint64_T)
        VTKNOCKCOEFCYL[(IonAbsTdcEOA)])) >> ((uint64_T)6));

      /* DataTypeConversion: '<S36>/DataTypeConversion2' incorporates:
       *  Constant: '<S36>/Constant5'
       */
      rtb_DataTypeConversion3 = (uint64_T)MAX_UINT_32;

      /* MinMax: '<S36>/MinMax' */
      if (rtb_DataTypeConversion < rtb_DataTypeConversion3) {
        rtb_DataTypeConversion3 = rtb_DataTypeConversion;
      }

      /* DataTypeConversion: '<S36>/DataTypeConversion' incorporates:
       *  MinMax: '<S36>/MinMax'
       */
      rtb_Divide_mo = (uint32_T)rtb_DataTypeConversion3;

      /* Assignment: '<S36>/Assignment2' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      KnockInt[(IonAbsTdcEOA)] = rtb_Divide_mo;

      /* Sum: '<S46>/Add' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  MultiPortSwitch: '<S46>/Index Vector1'
       *  SignalConversion generated from: '<S7>/KnockIntF_old'
       *  Sum: '<S46>/Add1'
       *
       * Block requirements for '<S46>/Add1':
       *  1. EISB_FCA6CYL_SW_REQ_1186: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1205)
       */
      rtb_Divide_a = (int64_T)KnockIntF[(IonAbsTdcEOA)];

      /* Product: '<S47>/Divide' incorporates:
       *  Constant: '<S46>/Constant2'
       *  DataTypeConversion: '<S46>/Data Type Conversion1'
       *  MinMax: '<S36>/MinMax'
       *  Product: '<S46>/Product1'
       *  Sum: '<S46>/Add'
       *  Sum: '<S46>/Add1'
       *
       * Block requirements for '<S46>/Add1':
       *  1. EISB_FCA6CYL_SW_REQ_1186: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1205)
       */
      rtb_Divide_a = (((((int64_T)((uint64_T)(rtb_DataTypeConversion3 <<
        ((uint64_T)10)))) - rtb_Divide_a) * ((int64_T)KFKNOCKINT)) +
                      (rtb_Divide_a * 1024LL)) / 1024LL;

      /* DataTypeConversion: '<S46>/Data Type Conversion' incorporates:
       *  Constant: '<S46>/Constant'
       */
      rtb_DataTypeConversion_i = (int64_T)MAX_INT_32;

      /* MinMax: '<S46>/MinMax' incorporates:
       *  DataTypeConversion: '<S47>/Conversion2'
       */
      if (rtb_Divide_a < rtb_DataTypeConversion_i) {
        rtb_DataTypeConversion_i = rtb_Divide_a;
      }

      /* MinMax: '<S46>/MinMax1' incorporates:
       *  MinMax: '<S46>/MinMax'
       */
      if (rtb_DataTypeConversion_i > 0LL) {
        /* Assignment: '<S46>/Assignment2' incorporates:
         *  DataTypeConversion: '<S46>/Conversion2'
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        KnockIntF[(IonAbsTdcEOA)] = (int32_T)rtb_DataTypeConversion_i;
      } else {
        /* Assignment: '<S46>/Assignment2' incorporates:
         *  Constant: '<S46>/Constant1'
         *  DataTypeConversion: '<S46>/Conversion2'
         *  Inport: '<Root>/IonAbsTdcEOA'
         */
        KnockIntF[(IonAbsTdcEOA)] = 0;
      }

      /* End of MinMax: '<S46>/MinMax1' */

      /* S-Function (Look2D_U8_S16_S16): '<S50>/Look2D_U8_S16_S16' incorporates:
       *  Constant: '<S37>/Constant3'
       *  Constant: '<S37>/Constant4'
       *  Constant: '<S37>/Constant5'
       *  Constant: '<S37>/Constant6'
       *  Constant: '<S37>/Constant7'
       */
      Look2D_U8_S16_S16( &rtb_Look2D_U8_S16_S16, &TBCORRTHRINT[0], TWater,
                        &BKTWATION[0], ((uint8_T)BKTWATION_dim), TAir,
                        &BKTAIRION[0], ((uint8_T)BKTAIRION_dim));

      /* S-Function (Look2D_IR_U16): '<S48>/Look2D_IR_U16' incorporates:
       *  Constant: '<S37>/Constant10'
       *  Constant: '<S37>/Constant8'
       *  Constant: '<S37>/Constant9'
       */
      Look2D_IR_U16( &rtb_Look2D_IR_U16, &TBGNTHRKNOCK[0], IonKnockRpmIndex,
                    IonKnockRpmRatio, ((uint8_T)BKRPMIONKNOCK_dim),
                    IonKnockEthPercIndex, IonKnockEthPercRatio, ((uint8_T)
        BKETHPERCKNOCK_dim));

      /* S-Function (Look2D_IR_U16): '<S49>/Look2D_IR_U16' incorporates:
       *  Constant: '<S37>/Constant'
       *  Constant: '<S37>/Constant1'
       *  Constant: '<S37>/Constant2'
       */
      Look2D_IR_U16( &rtb_Look2D_IR_U16_e, &TBTHRINTKNOCK[0], IonKnockRpmIndex,
                    IonKnockRpmRatio, ((uint8_T)BKRPMIONKNOCK_dim),
                    IonKnockLoadIndex, IonKnockLoadRatio, ((uint8_T)
        BKLOADIONKNOCK_dim));

      /* Product: '<S37>/Product1' incorporates:
       *  DataTypeConversion: '<S37>/Conversion'
       *  Product: '<S37>/Product'
       *
       * Block requirements for '<S37>/Product1':
       *  1. EISB_FCA6CYL_SW_REQ_1187: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1206)
       */
      ThrIntKnock = (uint32_T)((uint64_T)((((uint64_T)((uint32_T)(((uint32_T)
        ((uint16_T)(((uint32_T)rtb_Look2D_U8_S16_S16) >> ((uint64_T)4)))) *
        ((uint32_T)rtb_Look2D_IR_U16_e)))) * ((uint64_T)rtb_Look2D_IR_U16)) >>
        ((uint64_T)18)));

      /* Sum: '<S38>/Add' incorporates:
       *  DataTypeConversion: '<S38>/Data Type Conversion3'
       *  DataTypeConversion: '<S38>/Data Type Conversion4'
       *
       * Block requirements for '<S38>/Add':
       *  1. EISB_FCA6CYL_SW_REQ_1188: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1207)
       */
      i = ((int32_T)rtb_Divide_mo) - ((int32_T)ThrIntKnock);

      /* MinMax: '<S38>/MinMax' incorporates:
       *  Constant: '<S38>/Constant'
       *  DataTypeConversion: '<S38>/Data Type Conversion'
       */
      if (i >= ((int32_T)((int16_T)MAX_INT_16))) {
        i = (int32_T)((int16_T)MAX_INT_16);
      }

      /* MinMax: '<S38>/MinMax1' incorporates:
       *  Constant: '<S38>/Constant1'
       *  DataTypeConversion: '<S38>/Data Type Conversion1'
       *  MinMax: '<S38>/MinMax'
       */
      if (i <= ((int32_T)((int16_T)MIN_INT_16))) {
        i = (int32_T)((int16_T)MIN_INT_16);
      }

      /* SignalConversion generated from: '<S7>/DeltaKnockNPowCyl' incorporates:
       *  DataTypeConversion: '<S38>/Data Type Conversion2'
       *  MinMax: '<S38>/MinMax1'
       */
      DeltaKnockNPowCyl = (int16_T)i;

      /* Assignment: '<S38>/Assignment2' incorporates:
       *  DataTypeConversion: '<S38>/Data Type Conversion2'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  MinMax: '<S38>/MinMax1'
       */
      DeltaKnockNPow[(IonAbsTdcEOA)] = (int16_T)i;

      /* End of Outputs for SubSystem: '<S1>/KnockIntEOA' */
    } else {
      /* Transition: '<S2>:26' */
      if ((Rpm < DTHPEAKRSTRPMTHR) && (Load < DTHPEAKRSTLOADTHR)) {
        /* Outputs for Function Call SubSystem: '<S1>/InitGain'
         *
         * Block description for '<S1>/InitGain':
         *  This block initializes outputs related to the evalutation of gain used
         *  to normalize knock power.
         */
        /* MinMax: '<S18>/MinMax1' incorporates:
         *  Constant: '<S4>/Constant1'
         *  SignalConversion generated from: '<S4>/DThPeakCyl'
         *
         * Block requirements for '<S18>/MinMax1':
         *  1. EISB_FCA6CYL_SW_REQ_1177: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1198)
         */
        /* Transition: '<S2>:28' */
        /* Transition: '<S2>:30'
         * Requirements for Transition: '<S2>:30':
         *  1. EISB_FCA6CYL_SW_REQ_1750: Software shall set to 0 each output produced for Power Evaluation ... (ECU_SW_Requirements#4349)
         */
        /* Reset gain too */
        /* Event: '<S2>:32' */
        DThPeakCyl = 0;

        /* SignalConversion generated from: '<S4>/ThPeakNomTot' incorporates:
         *  Constant: '<S4>/Constant2'
         */
        ThPeakNomTot = 0U;

        /* SignalConversion generated from: '<S4>/ThPeakNom' incorporates:
         *  Constant: '<S4>/Constant3'
         */
        ThPeakNom = 0U;

        /* MinMax: '<S20>/MinMax1' incorporates:
         *  Constant: '<S4>/Constant17'
         *  DataTypeConversion: '<S4>/Data Type Conversion'
         */
        DThPeakGain = ((uint16_T)DTHPEAKGAIN_NEUTRAL);

        /* SignalConversion generated from: '<S4>/KnockPowNormGain' incorporates:
         *  Constant: '<S4>/Constant13'
         */
        KnockPowNormGain = 0U;

        /* SignalConversion generated from: '<S4>/KnockPowNormTmp' incorporates:
         *  Constant: '<S4>/Constant'
         */
        KnockPowNormTmp = 0U;

        /* SignalConversion generated from: '<S4>/KFiltDThPeak' incorporates:
         *  Constant: '<S4>/Constant5'
         */
        KFiltDThPeak = 0U;

        /* SignalConversion generated from: '<S4>/MinDThPeakSat' incorporates:
         *  Constant: '<S4>/Constant7'
         */
        MinDThPeakSat = 0U;

        /* Outputs for Function Call SubSystem: '<S1>/InitKnockIntEOA'
         *
         * Block description for '<S1>/InitKnockIntEOA':
         *  This block performs reset of output signal when knock detection
         *  strategy is disabled.
         */
        /* Event: '<S2>:33' */
        for (i = 0; i < 8; i++) {
          /* SignalConversion generated from: '<S4>/DThPeak' */
          DThPeak[(i)] = 1024U;

          /* SignalConversion generated from: '<S4>/DThPeak_HR' */
          DThPeak_HR[(i)] = 16777216;

          /* SignalConversion generated from: '<S5>/KnockInt' */
          KnockInt[(i)] = 0U;

          /* SignalConversion generated from: '<S5>/DeltaKnockNPow' */
          DeltaKnockNPow[(i)] = 0;

          /* SignalConversion generated from: '<S5>/KnockIntF' */
          KnockIntF[(i)] = 0;
        }

        /* End of Outputs for SubSystem: '<S1>/InitGain' */

        /* SignalConversion generated from: '<S5>/KnockIntCyl' incorporates:
         *  Constant: '<S5>/Constant7'
         */
        KnockIntCyl = 0U;

        /* SignalConversion generated from: '<S5>/ThrIntKnock' incorporates:
         *  Constant: '<S5>/Constant9'
         */
        ThrIntKnock = 0U;

        /* SignalConversion generated from: '<S5>/DeltaKnockNPowCyl' incorporates:
         *  Constant: '<S5>/Constant10'
         */
        DeltaKnockNPowCyl = 0;

        /* SignalConversion generated from: '<S5>/KnockPowNorm' incorporates:
         *  Constant: '<S5>/Constant'
         */
        KnockPowNorm = 0U;

        /* End of Outputs for SubSystem: '<S1>/InitKnockIntEOA' */
      } else {
        /* Outputs for Function Call SubSystem: '<S1>/InitKnockIntEOA'
         *
         * Block description for '<S1>/InitKnockIntEOA':
         *  This block performs reset of output signal when knock detection
         *  strategy is disabled.
         */
        /* SignalConversion generated from: '<S5>/KnockInt' */
        /* Transition: '<S2>:17'
         * Requirements for Transition: '<S2>:17':
         *  1. EISB_FCA6CYL_SW_REQ_1750: Software shall set to 0 each output produced for Power Evaluation ... (ECU_SW_Requirements#4349)
         */
        /*  Reset only knock intensity  */
        /* Event: '<S2>:33' */
        memset((&(KnockInt[0])), 0, (sizeof(uint32_T)) << 3U);

        /* SignalConversion generated from: '<S5>/DeltaKnockNPow' */
        memset((&(DeltaKnockNPow[0])), 0, (sizeof(int16_T)) << 3U);

        /* SignalConversion generated from: '<S5>/KnockIntF' */
        memset((&(KnockIntF[0])), 0, (sizeof(int32_T)) << 3U);

        /* SignalConversion generated from: '<S5>/KnockIntCyl' incorporates:
         *  Constant: '<S5>/Constant7'
         */
        KnockIntCyl = 0U;

        /* SignalConversion generated from: '<S5>/ThrIntKnock' incorporates:
         *  Constant: '<S5>/Constant9'
         */
        ThrIntKnock = 0U;

        /* SignalConversion generated from: '<S5>/DeltaKnockNPowCyl' incorporates:
         *  Constant: '<S5>/Constant10'
         */
        DeltaKnockNPowCyl = 0;

        /* SignalConversion generated from: '<S5>/KnockPowNorm' incorporates:
         *  Constant: '<S5>/Constant'
         */
        KnockPowNorm = 0U;

        /* End of Outputs for SubSystem: '<S1>/InitKnockIntEOA' */
      }
    }
  }

  /* End of Chart: '<Root>/Scheduler' */
}

/*
 * Output and update for function-call system: '<Root>/Scheduler'
 * Block description for: '<Root>/Scheduler'
 *   This chart schedules model runnables, according to the following events:
 *   -Power On task;
 *   -End of Acquisition task.
 *   The chart enable output evaluation only if knock detection strategy is enabled.
 */
void IonKnockPower_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[2];
  int32_T i;

  /* Chart: '<Root>/Scheduler' incorporates:
   *  TriggerPort: '<S2>/input events'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules model runnables, according to the following events:
   *  -Power On task;
   *  -End of Acquisition task.
   *  The chart enable output evaluation only if knock detection strategy is enabled.
   */
  for (i = 0; i < 2; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S2>:2' */
    i = (int32_T)Ion_event_IonKnockPower_PowerOn;
    IonK_chartstep_c4_IonKnockPower(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S2>:3' */
    i = (int32_T)IonKnoc_event_IonKnockPower_EOA;
    IonK_chartstep_c4_IonKnockPower(&i);
  }
}

/* Model step function */
void IonKnockPower_EOA(void)
{
  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules model runnables, according to the following events:
   *  -Power On task;
   *  -End of Acquisition task.
   *  The chart enable output evaluation only if knock detection strategy is enabled.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockPower_EOA' */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules model runnables, according to the following events:
   *  -Power On task;
   *  -End of Acquisition task.
   *  The chart enable output evaluation only if knock detection strategy is enabled.
   */
  IonKnockPower_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockPower_EOA' */
}

/* Model step function */
void IonKnockPower_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockPower_PowerOn' incorporates:
   *  Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules model runnables, according to the following events:
   *  -Power On task;
   *  -End of Acquisition task.
   *  The chart enable output evaluation only if knock detection strategy is enabled.
   */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules model runnables, according to the following events:
   *  -Power On task;
   *  -End of Acquisition task.
   *  The chart enable output evaluation only if knock detection strategy is enabled.
   */
  IonKnockPower_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockPower_PowerOn' */
}

/* Model initialize function */
void IonKnockPower_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint32_T KnockPowNorm;
int16_T SATotCyl;
int16_T DSAIon;
int16_T DThPeakCyl;
uint32_T ThrIntKnock;
uint32_T KnockInt[N_CYL_MAX];
int16_T DeltaKnockNPow[N_CYL_MAX];
int32_T KnockIntF[N_CYL_MAX];
void IonKnockPower_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    KnockInt[idx] = 0u;
    DeltaKnockNPow[idx] = 0;
    KnockIntF[idx] = 0u;
  }

  KnockPowNorm = 0u;
  SATotCyl = 0;
  DThPeakCyl = 0;
  ThrIntKnock = 0u;
  DSAIon = 0;
}

void IonKnockPower_PowerOn(void)
{
  IonKnockPower_Stub();
}

void IonKnockPower_EOA(void)
{
  IonKnockPower_Stub();
}

#endif                                 /*_BUILD_IONKNOCKPOWER_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/