/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      KnockCorrMgm.h
 **  Date:          28-May-2021
 **
 **  Model Version: 1.1048
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_KnockCorrMgm_h_
#define RTW_HEADER_KnockCorrMgm_h_
#include <string.h>
#ifndef KnockCorrMgm_COMMON_INCLUDES_
# define KnockCorrMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* KnockCorrMgm_COMMON_INCLUDES_ */

typedef uint8_T enum_KnockCorrMode;
#define KNOCK_CORR_OFF                 ((enum_KnockCorrMode)0U)  /* Default value */
#define KNOCK_CORR_NORMAL              ((enum_KnockCorrMode)1U)
#define KNOCK_CORR_SLEW                ((enum_KnockCorrMode)2U)
#define KNOCK_CORR_REC                 ((enum_KnockCorrMode)3U)

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: ELD_EXPORT_DEFINE */
#define BKLOADADKNOCK_dim              4U                        /* Referenced by: '<S7>/ENKNOCKAD4' */

/* Length for breakpoint BKLOADKNOCK (-1) */
#define BKRPMKNOCK12_dim               11U                       /* Referenced by: '<S7>/ENKNOCKAD3' */

/* Length for breakpoint BKRPMKNOCK12 (-1) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void KnockCorrMgm_initialize(void);

/* Exported entry point function */
extern void KnockCorrMgm_EOA(void);

/* Exported entry point function */
extern void KnockCorrMgm_NoSync(void);

/* Exported entry point function */
extern void KnockCorrMgm_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgActTipIn;            /* '<S2>/Merge' */

/* Flag used to enable spark advance correction in case of knock detection */
extern uint16_T IDZoneKnockLoad;       /* '<S2>/Merge3' */

/* Load zone index for knock learning */
extern uint16_T IDZoneKnockRpm;        /* '<S2>/Merge1' */

/* Rpm zone index for knock learning */
extern enum_KnockCorrMode KnockCorrMode;/* '<Root>/KnockCorrMgm_Scheduler' */

/* Knock correction modality */
extern uint16_T RtZoneKnockLoad;       /* '<S2>/Merge4' */

/* Load zone ratio for knock learning */
extern uint16_T RtZoneKnockRpm;        /* '<S2>/Merge2' */

/* Rpm zone ratio for knock learning */
extern int16_T SATipIn;                /* '<S2>/Merge5' */

/* TipIn correction on Spark Advance */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S6>/Data Type Duplicate' : Unused code path elimination
 * Block '<S10>/Data Type Duplicate' : Unused code path elimination
 * Block '<S11>/Data Type Duplicate' : Unused code path elimination
 * Block '<S15>/Data Type Duplicate' : Unused code path elimination
 * Block '<S15>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S15>/Data Type Propagation' : Unused code path elimination
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S17>/Data Type Propagation' : Unused code path elimination
 * Block '<S10>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S10>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S10>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S11>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S11>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S11>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S15>/Reshape' : Reshape block reduction
 * Block '<S17>/Conversion' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'KnockCorrMgm'
 * '<S1>'   : 'KnockCorrMgm/KnockCorrMgm_Scheduler'
 * '<S2>'   : 'KnockCorrMgm/Merge'
 * '<S3>'   : 'KnockCorrMgm/Reset'
 * '<S4>'   : 'KnockCorrMgm/TipIn'
 * '<S5>'   : 'KnockCorrMgm/KnockCorrMgm_Scheduler/RECOVERY_MODE.ResetDiagKnockCoh'
 * '<S6>'   : 'KnockCorrMgm/KnockCorrMgm_Scheduler/RECOVERY_MODE.ResetDiagKnockCoh/SetDiagState'
 * '<S7>'   : 'KnockCorrMgm/TipIn/EngineRange'
 * '<S8>'   : 'KnockCorrMgm/TipIn/Interpolation'
 * '<S9>'   : 'KnockCorrMgm/TipIn/TipIn_Chart'
 * '<S10>'  : 'KnockCorrMgm/TipIn/EngineRange/PreLookUpIdSearch_U1'
 * '<S11>'  : 'KnockCorrMgm/TipIn/EngineRange/PreLookUpIdSearch_U16'
 * '<S12>'  : 'KnockCorrMgm/TipIn/Interpolation/calc_SATipIn'
 * '<S13>'  : 'KnockCorrMgm/TipIn/Interpolation/calc_SaTipInStep'
 * '<S14>'  : 'KnockCorrMgm/TipIn/Interpolation/calc_SATipIn/ArrangeLookUpOutputLSB'
 * '<S15>'  : 'KnockCorrMgm/TipIn/Interpolation/calc_SATipIn/Look2D_S8_U16_U16'
 * '<S16>'  : 'KnockCorrMgm/TipIn/Interpolation/calc_SaTipInStep/ArrangeLookUpOutputLSB'
 * '<S17>'  : 'KnockCorrMgm/TipIn/Interpolation/calc_SaTipInStep/LookUp_S8_U16'
 */

/*-
 * Requirements for '<Root>': KnockCorrMgm
 *
 * Inherited requirements for '<Root>/Reset':
 *  1. EISB_FCA6CYL_SW_REQ_1805: Software shall set to 0 each software variable implemented in knoc... (ECU_SW_Requirements#6952)

 */
#endif                                 /* RTW_HEADER_KnockCorrMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/