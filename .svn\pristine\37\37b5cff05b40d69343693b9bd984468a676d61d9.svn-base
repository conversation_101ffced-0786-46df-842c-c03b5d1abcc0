/*****************************************************************************************************************/
/* $HeadURL::                                                                                                $   */
/* $Description:: Enviromnent Variable and Obd Emission Relevant variables conversion functions              $   */
/* $Revision::                                                                                               $   */
/* $Date::                                                                                                   $   */
/* $Author::                                                                                                 $   */
/*****************************************************************************************************************/
#ifndef _ENV_VAR_CONVERSION_OUT_H
#define _ENV_VAR_CONVERSION_OUT_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "RtwTypes.h"
#include "Mathlib.h"
#include "rli.h"

/*!
\defgroup Public Defines
\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#pragma ghs startnomisra
///
#define __KwpConv_Rpm(SwVar) (uint8_T)min(((SwVar + 25u)/50u), MAX_uint8_T) //OS = 25, K = 50
///
#define __KwpConv_Load(SwVar) (uint8_T)min(((SwVar + 128u)>>8u), MAX_uint8_T) //OS = 128, K = 256
///
#define __KwpConv_GasPos(SwVar) (uint8_T)min(((SwVar + 8u)>>4u), MAX_uint8_T) //OS = 8, K = 16
///
#define __KwpConv_TWater(SwVar) convTemp(SwVar)
///
#define __KwpConv_TAir(SwVar) __KwpConv_TWater(SwVar) //OS = 648, K = 16
///
#define __KwpConv_TempECU(SwVar) __KwpConv_TWater(SwVar) //OS = 648, K = 16
///
#define __KwpConv_TotOdometer(SwVar) (uint32_T)min(SwVar, MAX_uint24_T)
///
#define __KwpConv_VBattery(SwVar) (uint8_T)min(((SwVar + 1u)>>1u),MAX_uint8_T) //OS = 1, K = 2
///
#define __KwpConv_ILeadObj(SwVar) (uint8_T)min((((uint32_T)SwVar + 32u)>>6u), MAX_uint8_T) //OS = 32, K = 64
///
#define __KwpConv_VtILeadPeak(SwVar)  __KwpConv_ILeadObj(SwVar) //OS = 32, K = 64
///
#define __KwpConv_IPriCorrCyl(SwVar) __KwpConv_Load(SwVar) //OS = 128, K = 256
///
#define __KwpConv_VtIPriCorr(SwVar)  __KwpConv_IPriCorrCyl(SwVar) //OS = 128, K = 256
///
#define __KwpConv_SAout(SwVar) (uint8_T)min(((SwVar + 2048u)>>4u),MAX_uint8_T) //OS = 2048, K = 16
///
#define __KwpConv_VehSpeed(SwVar) (uint8_T)min((((uint32_T)SwVar + 16u)>>5u),MAX_uint8_T) //OS = 16, K = 32
///
#define __KwpConv_ResetType(SwVar) (uint8_T)min(SwVar,7u) // max value 0b111 ~ 0x7 ~ 7
///
#define __KwpConv_StPlasObj(SwVar) (uint8_T)min(SwVar,3u) // max value 0b11 ~ 0x3 ~ 3
///
#define __KwpConv_RONLevelEE(SwVar) (uint8_T)min(SwVar,3u) // max value 0b11 ~ 0x3 ~ 3
///
#define __KwpConv_GearPos(SwVar)(uint8_T)min(SwVar,15u) // max value 0b1111 ~ 0xF ~ 15
///
#define __KwpConv_VVFdbkLiveness(SwVar) (uint8_T)min(((SwVar + 8u)>>4u), MAX_uint8_T) //OS = 8, K = 16
///
#define __KwpConv_VBankSel(SwVar) (uint16_T) (SwVar) //OS = 0.5, K = 1
///
#define __KwpConv_EETempECUMax(SwVar) __KwpConv_TWater(SwVar) //OS = 640, K = 16
///
#define __KwpConv_VVTempECU(SwVar) __KwpConv_VVFdbkLiveness(SwVar)  //OS = 8, K = 16
///
#define __KwpConv_CntNoSyncNOsts(SwVar) (uint8_T)min((SwVar),MAX_uint8_T)
///
#define __KwpConv_NTeethDeleted(SwVar) __KwpConv_ResetType(SwVar)// max value 0b111 ~ 0x7 ~ 7
///
#define __KwpConv_StSync(SwVar) __KwpConv_StPlasObj(SwVar) // max value 0b11 ~ 0x3 ~ 3
///
#define __KwpConv_LastSyncError(SwVar) __KwpConv_StPlasObj(SwVar) // max value 0b11 ~ 0x3 ~ 3
///
#define __KwpConv_ThPeakCyl(SwVar) (uint16_T)min(((SwVar + 2u)>>2u), MAX_uint16_T) //OS = 2, K = 4
///
#define __KwpConv_ChPeakCyl(SwVar) __KwpConv_ThPeakCyl(SwVar) //OS = 8, K = 16
///
#define __KwpConv_DThPeakCyl(SwVar) __KwpConv_VehSpeed(SwVar) //OS = 16, K = 32
///
#define __KwpConv_DwellIntCyl(SwVar) (uint8_T)min(((SwVar + 4u)>>3u), MAX_uint8_T) // OS = 4, K = 8
///
#define __KwpConv_Start_ionCyl(SwVar) __KwpConv_VBattery(SwVar) //OS = 1, K = 2
///
#define __KwpConv_VCharge(SwVar) __KwpConv_VehSpeed(SwVar) //OS = 16, K = 32
///
#define __KwpConv_SparkLength(SwVar) (uint8_T)min(((SwVar + 5u)/10u), MAX_uint8_T) //OS = 5, K = 10
///
#define __KwpConv_VtTSparkFilt(SwVar) __KwpConv_SparkLength(SwVar) //OS = 5, K = 10
///
#define __KwpConv_VtIShotPeak(SwVar) __KwpConv_VBankSel(SwVar) // OS = 0.5, K = 1
///
#define __KwpConv_IntIon(SwVar) (uint16_T) (SwVar) //OS = 0.5, K = 1
///
#define __KwpConv_CntIGNTrgInOn(SwVar) (uint8_T) min(SwVar,MAX_uint8_T) // at least less significant bits of CntIGNTrgInOn
///
#define __KwpConv_EffDwellTime(SwVar) __KwpConv_SparkLength(SwVar) //OS = 5, K = 10
///
#define __KwpConv_VtIBuckCM(SwVar) __KwpConv_VVFdbkLiveness(SwVar) //OS = 8, K = 16
///
#define __KwpConv_VBuck(SwVar) __KwpConv_VVFdbkLiveness(SwVar) //OS = 8, K = 16
///
#define __KwpConv_KCohDiagCnt(SwVar) (uint8_T)(SwVar) //OS = 0.5, K = 1
///
#define __KwpConv_CntKnockCohEE(SwVar) (uint8_T) (SwVar) //OS = 0.5, K = 1
///
#define __KwpConv_KnockInt(SwVar) __KwpConv_Load(SwVar) //OS = 128, K = 256
///
#define __KwpConv_TSpark(SwVar) __KwpConv_VtTSparkFilt(SwVar)

#pragma ghs endnomisra

/*!\egroup*/


/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/

/*!\egroup*/



/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/




#endif //_ENV_VAR_CONVERSION_OUT_H

