/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonDwellMgm.h
 **  Date:          16-May-2022
 **
 **  Model Version: 1.1128
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonDwellMgm_h_
#define RTW_HEADER_IonDwellMgm_h_
#include <string.h>
#ifndef IonDwellMgm_COMMON_INCLUDES_
# define IonDwellMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonDwellMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonDwellMgm_initialize(void);

/* Exported entry point function */
extern void IonDwellMgm_EOA(void);

/* Exported entry point function */
extern void IonDwellMgm_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T DwellInt[8];           /* '<S3>/Merge1' */

/* Ion integral during main dwell-time */
extern uint16_T DwellIntCyl;           /* '<S3>/Merge' */

/* DwellIntCyl */
extern uint8_T FlgOLSpark[8];          /* '<S3>/Merge7' */

/* Secondary winding open-load detection on switching off condition */
extern uint32_T VtOLSecInt[8];         /* '<S3>/Merge6' */

/* Ion integral on first samples, used to detect Secondary winding open-load detection on switching off condition */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S18>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Data Type Propagation' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Propagation' : Unused code path elimination
 * Block '<S26>/Data Type Propagation' : Unused code path elimination
 * Block '<S12>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S17>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion10' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion10' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion11' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S26>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S26>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S5>/Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonDwellMgm'
 * '<S1>'   : 'IonDwellMgm/EOA'
 * '<S2>'   : 'IonDwellMgm/PowerOn'
 * '<S3>'   : 'IonDwellMgm/Subsystem'
 * '<S4>'   : 'IonDwellMgm/EOA/Dwell_Integral'
 * '<S5>'   : 'IonDwellMgm/EOA/Dwell_Normalization'
 * '<S6>'   : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter'
 * '<S7>'   : 'IonDwellMgm/EOA/Dwell_Integral/Dwell_Mgm'
 * '<S8>'   : 'IonDwellMgm/EOA/Dwell_Integral/StopIonDwell'
 * '<S9>'   : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter'
 * '<S10>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassInit'
 * '<S11>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/Subsystem'
 * '<S12>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterHP'
 * '<S13>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterLP'
 * '<S14>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterHP/RescalSignedIntRightShift'
 * '<S15>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterHP/RescalSignedIntRightShift1'
 * '<S16>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterHP/RescalSignedIntRightShift2'
 * '<S17>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterHP/RescalSignedIntRightShift3'
 * '<S18>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterHP/SaturationDynamic'
 * '<S19>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterLP/RescalSignedIntRightShift'
 * '<S20>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterLP/RescalSignedIntRightShift1'
 * '<S21>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterLP/RescalSignedIntRightShift2'
 * '<S22>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterLP/RescalSignedIntRightShift3'
 * '<S23>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassFilter/ButterworthFilterLP/SaturationDynamic'
 * '<S24>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassInit/HighPassFilter_Init'
 * '<S25>'  : 'IonDwellMgm/EOA/Dwell_Integral/BandPassFilter/BandPassInit/LowPassFilter_Init'
 * '<S26>'  : 'IonDwellMgm/EOA/Dwell_Integral/StopIonDwell/LookUp_IR_S8'
 * '<S27>'  : 'IonDwellMgm/EOA/Dwell_Normalization/Dwell_Integral'
 */

/*-
 * Requirements for '<Root>': IonDwellMgm
 *
 * Inherited requirements for '<Root>/PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_1738: Software shall initialize each output produced for Coil Charge Phase integration at ECU power on. (ECU_SW_Requirements#4072)
 *
 * Inherited requirements for '<S4>/StopIonDwell':
 *  1. EISB_FCA6CYL_SW_REQ_1596: Software shall estimate, cylinder by cylinder, the end index of dw... (ECU_SW_Requirements#2833)
 *
 * Inherited requirements for '<S6>/BandPassFilter':
 *  1. EISB_FCA6CYL_SW_REQ_1597: Software shall apply a band pass Butterworth filter to the ion sig... (ECU_SW_Requirements#2835)

 */
#endif                                 /* RTW_HEADER_IonDwellMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/