/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           CoilTarget.c
 **  File Creation Date: 08-Sep-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         CoilTarget
 **  Model Description:  The operation goal of this module is to calculate a dwell time target for each coil in order to have the primary current measured as close as possible on its target.
 **  Model Version:      1.339
 **  Model Author:       SaccoP - Thu Jul 30 09:02:50 2020
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: PanettaM - Wed Sep 08 14:49:31 2021
 **
 **  Last Saved Modification:  PanettaM - Wed Sep 08 14:28:15 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "CoilTarget_out.h"
#include "CoilTarget_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BIT0                           1U                        /* Referenced by: '<S45>/BIT0' */

/* Bit0 value */
#define BKILEADOBJDWT_dim              9U                        /* Referenced by:
                                                                  * '<S1>/BKILEADOBJDWT_dim'
                                                                  * '<S49>/BKILEADOBJDWT_dim'
                                                                  */

/* Last index of BKILEADOBJDWT */
#define BKKPDWELLDTRG_dim              3U                        /* Referenced by:
                                                                  * '<S19>/BKKPDWELLDTRG_dim'
                                                                  * '<S19>/BKKPDWELLDTRG_dim1'
                                                                  */

/* Last index of BKKPDWELLDTRG */
#define BKKPDWELLOBJ_dim               8U                        /* Referenced by:
                                                                  * '<S19>/BKKPDWELLOBJ_dim'
                                                                  * '<S19>/BKKPDWELLOBJ_dim1'
                                                                  */

/* Last index of BKKPDWELLOBJ */
#define BKLOADILEADOBJ_dim             8U                        /* Referenced by:
                                                                  * '<S1>/BKLOADILEADOBJ_dim'
                                                                  * '<S15>/BKLOADILEADOBJ_dim'
                                                                  * '<S49>/BKLOADILEADOBJ_dim'
                                                                  * '<S50>/BKLOADILEADOBJ_dim'
                                                                  */

/* Last index of BKLOADILEADOBJ */
#define BKLOADSTPLASOBJ_dim            8U                        /* Referenced by: '<S45>/BKLOADSTPLASOBJ_dim' */

/* Last index of BKLOADSTPLASOBJ */
#define BKRPMILEADOBJ_dim              6U                        /* Referenced by: '<S50>/BKRPMILEADOBJ_dim1' */

/* Last index of BKRPMILEADOBJ */
#define BKRPMSTPLASOBJ_dim             6U                        /* Referenced by: '<S45>/BKRPMSTPLASOBJ_dim' */

/* Last index of BKRPMSTPLASOBJ */
#define BKTDCILEADOBJ_dim              4U                        /* Referenced by:
                                                                  * '<S15>/BKTDCILEADOBJ_dim'
                                                                  * '<S15>/BKTDCILEADOBJ_dim1'
                                                                  */

/* Last index of BKTDCILEADOBJ */
#define BKTDCMSPARKEN_dim              4U                        /* Referenced by: '<S43>/BKTDCMSPARKEN_dim' */

/* Last index of bkp BKTDCMSPARKEN */
#define BKTWILEADOBJ_dim               8U                        /* Referenced by:
                                                                  * '<S15>/BKTWILEADOBJ_dim'
                                                                  * '<S15>/BKTWILEADOBJ_dim1'
                                                                  * '<S16>/BKTWILEADOBJ_dim'
                                                                  * '<S48>/BKTWILEADOBJ_dim'
                                                                  */

/* Last index of BKTWILEADOBJ */
#define BKTWMSPARKEN_dim               6U                        /* Referenced by: '<S43>/BKTWMSPARKEN_dim' */

/* Last index of BKTWMSPARKEN */
#define BKVBATILEADOBJ_dim             6U                        /* Referenced by: '<S16>/BKVBATILEADOBJ_dim' */

/* Last index of BKVBATILEADOBJ */
#define ID_VER_COILTARGET_DEF          1339U                     /* Referenced by: '<S1>/ID_VER_COILTARGET_DEF' */

/* Id model version define */
#define INITDWELLOBJ                   600                       /* Referenced by: '<S1>/INITDWELLOBJ' */

/* Init value for dwell target */
#define INITLOADILEADOBJ               2560U                     /* Referenced by: '<S1>/INITLOADILEADOBJ' */

/* Init Load value to calculate the ILeadObj init */
#define MAXDWELLOBJDEF                 5000U                     /* Referenced by: '<S23>/MAXDWELLOBJDEF' */

/* Maximum value of dwell time target */
#define MAXILEADDEF                    25088                     /* Referenced by: '<S48>/MAXILEADDEF' */

/* Maximum value for primary current target */
#define MAXLOADDPLAEN                  65280                     /* Referenced by: '<S45>/MAXLOADDPLAEN' */

/* Maximum value of load compensated for plasma enabling */
#define MAX_UWORD                      65535U                    /* Referenced by: '<S11>/MAX_UWORD' */

/* Maximum unsigned word integer value */
#define MINDWELLOBJDEF                 -5000                     /* Referenced by: '<S23>/MINDWELLOBJDEF' */

/* Minimum value of dwell time target */
#define ONE                            1U                        /* Referenced by:
                                                                  * '<S43>/find_element_table'
                                                                  * '<S43>/Constant'
                                                                  * '<S43>/Constant1'
                                                                  * '<S45>/find_element_table'
                                                                  */

/* Define for number 1 */
#define TIMEDELAYTIMCMDSTALL           1U                        /* Referenced by: '<S2>/TIMEDELAYTIMCMDSTALL' */

/* Delay for time to stall primary current PI control */
#define ZERO                           0U                        /* Referenced by:
                                                                  * '<S43>/Constant2'
                                                                  * '<S43>/Constant3'
                                                                  */

/* Define for value 0 */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_COILTARGET_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKILEADOBJDWT[10] = { 3584U, 4607U,
  5631U, 6656U, 7679U, 8703U, 9728U, 10751U, 11776U, 12800U } ;/* Referenced by:
                                                                * '<S1>/BKILEADOBJDWT'
                                                                * '<S49>/BKILEADOBJDWT'
                                                                */

/* Breakpoint for primary current of TBDWELLTIME */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKKPDWELLDTRG[4] = { -5121, -2560, 0,
  512 } ;                             /* Referenced by: '<S19>/BKKPDWELLDTRG' */

/* PI delta Error breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKKPDWELLOBJ[9] = { -4096, -2560, -514,
  0, 512, 1534, 2048, 2558, 5120 } ;   /* Referenced by: '<S19>/BKKPDWELLOBJ' */

/* PI Error breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADILEADOBJ[9] = { 0U, 1280U, 2560U,
  5120U, 6400U, 7680U, 8960U, 10240U, 12800U } ;/* Referenced by:
                                                 * '<S1>/BKLOADILEADOBJ'
                                                 * '<S15>/BKLOADILEADOBJ'
                                                 */

/* Load ILEAD breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADSTPLASOBJ[9] = { 0U, 1280U,
  2560U, 5120U, 6400U, 7680U, 8960U, 10240U, 12800U } ;
                                    /* Referenced by: '<S45>/BKLOADSTPLASOBJ' */

/* Load Plas breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMILEADOBJ[7] = { 600U, 1000U,
  2000U, 3000U, 5000U, 7000U, 9000U } ;
                                      /* Referenced by: '<S50>/BKRPMILEADOBJ' */

/* Rpm ILEAD breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMSTPLASOBJ[7] = { 600U, 1000U,
  2000U, 3000U, 5000U, 7000U, 9000U } ;
                                     /* Referenced by: '<S45>/BKRPMSTPLASOBJ' */

/* Rpm Plas breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKTDCILEADOBJ[5] = { 100U, 500U, 1000U,
  2000U, 15000U } ;                   /* Referenced by: '<S15>/BKTDCILEADOBJ' */

/* TDC counter breakpoint for DLoad cond calculation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKTDCMSPARKEN[5] = { 100U, 500U, 1000U,
  2000U, 15000U } ;                   /* Referenced by: '<S43>/BKTDCMSPARKEN' */

/* TDC counter breakpoint for Multispark enable cond calculation */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKTWILEADOBJ[9] = { -800, -400, 0, 400,
  800, 1200, 1440, 1600, 1920 } ;      /* Referenced by: '<S15>/BKTWILEADOBJ' */

/* Water temperature breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKTWMSPARKEN[7] = { -320, 0, 80, 320,
  960, 1120, 1280 } ;                  /* Referenced by: '<S43>/BKTWMSPARKEN' */

/* TWater breakpoint for Multispark enable cond calculation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKVBATILEADOBJ[7] = { 96U, 128U, 176U,
  208U, 256U, 288U, 384U } ;         /* Referenced by: '<S16>/BKVBATILEADOBJ' */

/* VBattery breakpoint */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CNTTDCIPRICL = 10U;/* Referenced by: '<S71>/CNTTDCIPRICL' */

/* Number Tdc after stall to enable primary current PI control */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENRSTCTCTRL = 1;/* Referenced by: '<S18>/ENRSTCTCTRL' */

/* Enable reset control in rating */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENTBSTPLASOBJB = 0;
                                     /* Referenced by: '<S45>/ENTBSTPLASOBJB' */

/* Enable the use of TBSTPLASOBJB */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENTSPARKCORR = 1;/* Referenced by: '<S48>/ENTSPARKCORR' */

/* Enable flag for TSpark correction */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T FOFLGNOMAP = -1;/* Referenced by: '<S37>/FOFLGNOMAP' */

/* Force FlgNoMap */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T HYSLOADSTPLASOBJ = 640U;
                                   /* Referenced by: '<S45>/HYSLOADSTPLASOBJ' */

/* Load hysterress */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T HYSRPMSTPLASOBJ = 200U;
                                    /* Referenced by: '<S45>/HYSRPMSTPLASOBJ' */

/* Rpm hysterress */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T ILEADOBJREC = 9728U;/* Referenced by:
                                                               * '<S1>/ILEADOBJREC'
                                                               * '<S50>/ILEADOBJREC'
                                                               */

/* Primary current recovery target */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T KFTRGDOWN = 11469U;/* Referenced by: '<S51>/KFTRGDOWN' */

/* First order filter gain for primary current target corrected when it's decreasing */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T KFTRGUP = 16384U;/* Referenced by: '<S51>/KFTRGUP' */

/* First order filter gain for primary current target corrected when it's increasing */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T LATCHIPRIPICTRL = 1;
                                    /* Referenced by: '<S12>/LATCHIPRIPICTRL' */

/* Latch PI Ctrl */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MAXRTBKKPDTRG = 2048;
                                      /* Referenced by: '<S18>/MAXRTBKKPDTRG' */

/* Maximum rate delta target */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXSATILEADOBJ = 20480U;
                                     /* Referenced by: '<S48>/MAXSATILEADOBJ' */

/* High saturation for primary current target corrected */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MINERRINTSATDWOBJ = -12800;/* Referenced by:
                                                                     * '<S19>/MINERRINTSATDWOBJ'
                                                                     * '<S28>/MINERRINTSATDWOBJ'
                                                                     */

/* Low integral saturation */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MINRTBKKPDTRG = -2048;
                                      /* Referenced by: '<S18>/MINRTBKKPDTRG' */

/* Minimum rate delta target */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MINSATILEADOBJ = 4352U;
                                     /* Referenced by: '<S48>/MINSATILEADOBJ' */

/* Low saturation for primary current target corrected */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SCDWELLOBJGAIN = 40U;/* Referenced by:
                                                                * '<S23>/SCDWELLOBJGAIN'
                                                                * '<S28>/SCDWELLOBJGAIN'
                                                                */

/* Gain to convert PI control output into a dwell time */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SCDWELLOBJOFF = 500U;/* Referenced by:
                                                                * '<S23>/SCDWELLOBJOFF'
                                                                * '<S28>/SCDWELLOBJOFF'
                                                                */

/* Offset to convert PI control output into a dwell time */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T SELCTABSIDX = 1;/* Referenced by: '<S11>/SELCTABSIDX' */

/* Select cylinder index to be used: 1 for CylPlaAbsOff_idx, 0 for CylPlaAbsOn_idx */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T SELCTVBATT = 1;/* Referenced by: '<S16>/SELCTVBATT' */

/* Select which battery voltage signal has to be used: 1 for VBattery, 0 for VBatteryF. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T SELERILEADOBJ0 = 0U;
                                     /* Referenced by: '<S21>/SELERILEADOBJ0' */

/* Array selector value to calculate ErrILeadObj0 and DILeadObjRt0 */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T SELILODTB = 1;/* Referenced by: '<S49>/SELILODTB' */

/* Use primary current target filtered (set 1) or not (set 0). */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T SELPIFFWCTRL = 0;/* Referenced by:
                                                             * '<S1>/SELPIFFWCTRL'
                                                             * '<S18>/SELPIFFWCTRL'
                                                             * '<S23>/SELPIFFWCTRL'
                                                             * '<S28>/SELPIFFWCTRL'
                                                             */

/* Enable feed forward path of primary winding current control */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T SELPLACTRLOL = 0;/* Referenced by: '<S10>/SELPLACTRLOL' */

/* Enable OL Dwell strategy */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TBDLOADPLAEN[45] = { 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 } ;/* Referenced by: '<S15>/TBDLOADPLAEN' */

/* Delta Load compensation to enable plasma */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBDWELLTIME[90] = { 570U, 640U, 780U,
  866U, 940U, 1030U, 1200U, 1355U, 1430U, 1535U, 570U, 640U, 780U, 866U, 940U,
  1030U, 1200U, 1355U, 1430U, 1535U, 570U, 640U, 780U, 866U, 940U, 1030U, 1140U,
  1295U, 1370U, 1475U, 570U, 640U, 780U, 866U, 940U, 1020U, 1140U, 1295U, 1370U,
  1475U, 518U, 576U, 707U, 783U, 852U, 926U, 1073U, 1231U, 1309U, 1406U, 490U,
  539U, 650U, 723U, 781U, 844U, 998U, 1163U, 1248U, 1328U, 490U, 539U, 650U,
  723U, 781U, 844U, 998U, 1163U, 1248U, 1328U, 490U, 539U, 650U, 723U, 781U,
  844U, 998U, 1163U, 1248U, 1328U, 490U, 539U, 650U, 723U, 781U, 844U, 998U,
  1163U, 1248U, 1328U } ;              /* Referenced by:
                                        * '<S1>/TBDWELLTIME'
                                        * '<S49>/TBDWELLTIME'
                                        */

/* Dwell time */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T TBENMSPARK[35] = { 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0 } ;                          /* Referenced by: '<S43>/TBENMSPARK' */

/* Plasma Enable flag Table */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBILEADOBJ[63] = { 5632U, 5632U, 5632U,
  6144U, 7168U, 7680U, 8704U, 5632U, 5632U, 5632U, 6144U, 7168U, 7680U, 8704U,
  5632U, 5632U, 5632U, 6144U, 6656U, 7168U, 8192U, 5632U, 5632U, 5632U, 6144U,
  6656U, 7168U, 8192U, 5632U, 5632U, 5632U, 6144U, 6656U, 7168U, 9216U, 6656U,
  6656U, 6656U, 6656U, 7168U, 7168U, 9216U, 7168U, 7168U, 7168U, 7680U, 7680U,
  7680U, 9728U, 8192U, 8192U, 8192U, 8192U, 8192U, 9216U, 9728U, 8704U, 8704U,
  8704U, 8704U, 8704U, 9728U, 9728U } ;/* Referenced by:
                                        * '<S1>/TBILEADOBJ'
                                        * '<S50>/TBILEADOBJ'
                                        */

/* Primary current target table */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBKIDWELLOBJ[36] = { 816U, 816U, 816U,
  816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U,
  816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U, 816U,
  816U, 816U, 816U, 816U, 816U, 816U, 816U } ;/* Referenced by: '<S19>/TBKIDWELLOBJ' */

/* Table for integral gain in PI control */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBKPDWELLOBJ[36] = { 80U, 80U, 80U,
  80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U,
  80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U, 80U,
  80U } ;                              /* Referenced by: '<S19>/TBKPDWELLOBJ' */

/* Table for proportional gain in PI control */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBMAXERRINTSATDWOBJ[63] = { 18944U,
  18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U,
  18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U,
  18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U,
  18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U,
  18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U,
  18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U, 18944U,
  18944U, 18944U } ;            /* Referenced by: '<S16>/TBMAXERRINTSATDWOBJ' */

/* Max saturation for integral part */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TBSTPLASOBJ[63] = { 2U, 2U, 2U, 2U, 2U,
  2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U,
  2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U,
  2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U } ;/* Referenced by:
                                                                      * '<S1>/TBSTPLASOBJ'
                                                                      * '<S45>/TBSTPLASOBJ'
                                                                      */

/* Selector of ignition mode for bank 1 if ENTBSTPLASOBJB = 1 or both banks if ENTBSTPLASOBJB = 0 (1: EPWS + ion, 2: single spark + ion) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TBSTPLASOBJB[63] = { 2U, 2U, 2U, 2U, 2U,
  2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U,
  2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U,
  2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U, 2U } ;/* Referenced by:
                                                                      * '<S1>/TBSTPLASOBJB'
                                                                      * '<S45>/TBSTPLASOBJB'
                                                                      */

/* Selector of ignition mode for bank 2 if ENTBSTPLASOBJB = 1 or ignored if ENTBSTPLASOBJB = 0 (1: EPWS + ion, 2: single spark + ion) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T TIMIPRISTALL = 100U;/* Referenced by: '<S71>/TIMIPRISTALL' */

/* Time to stall primary current PI control */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTFODWELLTIMEOBJ[8] = { -1, -1, -1, -1,
  -1, -1, -1, -1 } ;               /* Referenced by: '<S17>/VTFODWELLTIMEOBJ' */

/* Force dwell time target at the tunable value */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTTWILEADOBJGAIN[9] = { 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U } ;
                                   /* Referenced by: '<S48>/VTTWILEADOBJGAIN' */

/* Water temperature gain to correct basic primary current target  */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTTWILEADOBJOFF[9] = { 0, 0, 0, 0, 0, 0,
  0, 0, 0 } ;                       /* Referenced by: '<S48>/VTTWILEADOBJOFF' */

/* Water temperature offset to correct basic primary current target  */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T DwellTimeBase;                /* '<S4>/Merge' */

/* Basic dwell time related to the primary current target calculated */
int16_T DwellTimeObj[8];               /* '<S4>/Merge15' */

/* Dwell time target calculated by the PI control (if SELPIFFWCTRL is set, its value is used as an offset, that can be also negative) */
uint16_T ILeadObj;                     /* '<S4>/Merge3' */

/* Primary current target corrected and filtered */
uint16_T LoadDPlaEn;                   /* '<S15>/Data Type Conversion1' */

/* Load plasma enable compensated */
boolean_T SelPIFFWCtrl;                /* '<S1>/SELPIFFWCTRL' */

/* Enable feed forward path of primary winding current control  */
uint8_T StPlasObj;                     /* '<S4>/Merge1' */

/* Current ignition mode: 1=EPWS+ION; 2=single spark+ION. */
boolean_T VtSelPlaCtrlOL[8];           /* '<S4>/Merge14' */

/* Flag to Enable OL */
uint32_T VtTimCmdStall[8];             /* '<S4>/Merge16' */

/* Time to stall primary current PI control */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T CntAbsTrigIn;/* '<S4>/Merge8' */

/* Trigger Tdc counter */
STATIC_TEST_POINT int16_T DILeadObjRt0;/* '<S4>/Merge21' */

/* Delta Error */
STATIC_TEST_POINT boolean_T EnIgnModeCnd;/* '<S43>/Logical Operator1' */

/* Possibility to select a specific ignition mode enabled */
STATIC_TEST_POINT int16_T ErrILeadObj0;/* '<S4>/Merge12' */

/* Error for PI control associated at the cylinder SELERILEADOBJ0. */
STATIC_TEST_POINT boolean_T FlgNoMap;  /* '<S37>/Switch' */

/* Flag to calculate the ILeadBase with a recovery value */
STATIC_TEST_POINT uint16_T ILeadBase;  /* '<S4>/Merge13' */

/* Primary current basic target */
STATIC_TEST_POINT uint16_T ILeadObj0;  /* '<S4>/Merge20' */

/* Primary current target corrected */
STATIC_TEST_POINT uint32_T IdVer_CoilTarget;/* '<S1>/ID_VER_COILTARGET_DEF' */

/* Id model version */
STATIC_TEST_POINT int32_T InvPIErrDwellObj;/* '<S28>/Switch1' */

/* PI output value obtained inverting the Dwell time target */
STATIC_TEST_POINT boolean_T SelPlaCtrlOL_latched;/* '<S12>/Switch' */

/* Open loop condition for PI control after bypass condition on LATCHIPRIPICTRL */
STATIC_TEST_POINT uint8_T StPlasObjB_mem;/* '<S4>/Merge24' */

/* Output value of table TBSTPLASOBJB memorized */
STATIC_TEST_POINT uint8_T StPlasObj_mem;/* '<S4>/Merge23' */

/* Output value of table TBSTPLASOBJ memorized */
STATIC_TEST_POINT uint32_T TmpCntIGNInOff[8];/* '<S4>/Merge17' */

/* Counter Tdc */
STATIC_TEST_POINT int16_T VtDILeadObjRt[8];/* '<S4>/Merge19' */

/* Error target VtDILeadObjRt */
STATIC_TEST_POINT int16_T VtErrILeadObj[8];/* '<S4>/Merge10' */

/* Error for PI control */
STATIC_TEST_POINT int32_T VtErrPropDwellObj[8];/* '<S4>/Merge11' */

/* Proportional part of the PI control */
STATIC_TEST_POINT uint32_T VtILeadObjMem[8];/* '<S4>/Merge18' */

/* Primary current target corrected and filtered at high resolution */
STATIC_TEST_POINT uint16_T VtILeadObjRt[8];/* '<S4>/Merge5' */

/* Target input IleadObj rate limited */
STATIC_TEST_POINT uint16_T VtMaxErrIntSat[8];/* '<S4>/Merge2' */

/* Max Error PI Dwell Obj */
STATIC_TEST_POINT int32_T VtPIErrDwellObj[8];/* '<S4>/Merge9' */

/* Output of the PI control */
STATIC_TEST_POINT int32_T VtPIErrDwellObjDir[8];/* '<S4>/Merge7' */

/* Integral part of the PI control */
STATIC_TEST_POINT boolean_T VtResetMem[8];/* '<S18>/rate_limiter_and_ctrl_reset' */

/* Flag to Reset PI controller */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void CoilTarget_Init(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2_d;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_b;
  uint16_T rtb_Look2D_IR_U16_i;
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/CoilTarget_Init' incorporates:
   *  SubSystem: '<Root>/CoilTarget_Init_mgm'
   *
   * Block description for '<Root>/CoilTarget_Init_mgm':
   *  Variables initialization.
   */
  /* S-Function (PreLookUpIdSearch_U16): '<S6>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S1>/BKILEADOBJDWT'
   *  Constant: '<S1>/BKILEADOBJDWT_dim'
   *  Constant: '<S1>/ILEADOBJREC'
   */
  PreLookUpIdSearch_U16( &rtb_Look2D_IR_U16_i, &rtb_PreLookUpIdSearch_U16_o2_d,
                        ILEADOBJREC, &BKILEADOBJDWT[0], ((uint8_T)
    BKILEADOBJDWT_dim));

  /* S-Function (PreLookUpIdSearch_U16): '<S7>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S1>/BKLOADILEADOBJ'
   *  Constant: '<S1>/BKLOADILEADOBJ_dim'
   *  Constant: '<S1>/INITLOADILEADOBJ'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_b, (&(ILeadObj0)),
                        ((uint16_T)INITLOADILEADOBJ), &BKLOADILEADOBJ[0],
                        ((uint8_T)BKLOADILEADOBJ_dim));

  /* S-Function (Look2D_IR_U16): '<S5>/Look2D_IR_U16' incorporates:
   *  Constant: '<S1>/BKILEADOBJDWT_dim'
   *  Constant: '<S1>/BKLOADILEADOBJ_dim'
   *  Constant: '<S1>/TBDWELLTIME'
   *
   * Block requirements for '<S1>/TBDWELLTIME':
   *  1. EISB_FCA6CYL_SW_REQ_1803: The basic dwell time (DwellTimeBase) is initialized at the PowerOn... (ECU_SW_Requirements#6351)
   */
  Look2D_IR_U16( &rtb_Look2D_IR_U16_i, &TBDWELLTIME[0], rtb_Look2D_IR_U16_i,
                rtb_PreLookUpIdSearch_U16_o2_d, ((uint8_T)BKILEADOBJDWT_dim),
                rtb_PreLookUpIdSearch_U16_o1_b, ILeadObj0, ((uint8_T)
    BKLOADILEADOBJ_dim));

  /* SignalConversion generated from: '<S1>/DwellTimeBase' */
  DwellTimeBase = rtb_Look2D_IR_U16_i;

  /* DataTypeConversion: '<S1>/Data Type Conversion' incorporates:
   *  Constant: '<S1>/TBSTPLASOBJ'
   *  Constant: '<S1>/ZERO_INDEX'
   *  Constant: '<S1>/ZERO_INDEX1'
   *  Selector: '<S1>/Selector'
   *
   * Block requirements for '<S1>/TBSTPLASOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_1867: The software shall initialize at the PowerOn event the ignition mo... (ECU_SW_Requirements#7289)
   */
  StPlasObj = TBSTPLASOBJ[0];

  /* SignalConversion generated from: '<S1>/StPlasObj_mem' incorporates:
   *  Constant: '<S1>/TBSTPLASOBJ'
   *  Constant: '<S1>/ZERO_INDEX'
   *  Constant: '<S1>/ZERO_INDEX1'
   *  Selector: '<S1>/Selector'
   *
   * Block requirements for '<S1>/TBSTPLASOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_1867: The software shall initialize at the PowerOn event the ignition mo... (ECU_SW_Requirements#7289)
   */
  StPlasObj_mem = TBSTPLASOBJ[0];

  /* Selector: '<S1>/Selector2' incorporates:
   *  Constant: '<S1>/TBILEADOBJ'
   *  Constant: '<S1>/ZERO_INDEX4'
   *  Constant: '<S1>/ZERO_INDEX5'
   *
   * Block requirements for '<S1>/TBILEADOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_1801: The coil primary current variables (ILeadBase, ILeadObj, ILeadObj0... (ECU_SW_Requirements#6329)
   */
  ILeadObj0 = TBILEADOBJ[0];

  /* DataTypeConversion: '<S1>/Data Type Conversion1' */
  ILeadObj = (uint16_T)((((uint32_T)ILeadObj0) << ((uint32_T)4)) / 25U);

  /* SignalConversion generated from: '<S1>/ILeadBase' */
  ILeadBase = ILeadObj0;

  /* Selector: '<S1>/Selector1' incorporates:
   *  Constant: '<S1>/TBSTPLASOBJB'
   *  Constant: '<S1>/ZERO_INDEX6'
   *  Constant: '<S1>/ZERO_INDEX7'
   *
   * Block requirements for '<S1>/TBSTPLASOBJB':
   *  1. EISB_FCA6CYL_SW_REQ_1867: The software shall initialize at the PowerOn event the ignition mo... (ECU_SW_Requirements#7289)
   */
  StPlasObjB_mem = TBSTPLASOBJB[0];

  /* Constant: '<S1>/SELPIFFWCTRL' */
  SelPIFFWCtrl = SELPIFFWCTRL;

  /* Constant: '<S1>/ID_VER_COILTARGET_DEF' */
  IdVer_CoilTarget = ID_VER_COILTARGET_DEF;

  /* Constant: '<S1>/ZERO2' */
  CntAbsTrigIn = 0U;

  /* Constant: '<S1>/ZERO1' */
  ErrILeadObj0 = 0;
  for (i = 0; i < 8; i++) {
    /* Product: '<S1>/Divide' incorporates:
     *  Constant: '<S1>/INITDWELLOBJ'
     *
     * Block requirements for '<S1>/INITDWELLOBJ':
     *  1. EISB_FCA6CYL_SW_REQ_1804: The software shall initialize the dwell time target (DwellTimeObj)... (ECU_SW_Requirements#6366)
     */
    DwellTimeObj[(i)] = ((int16_T)INITDWELLOBJ);

    /* Constant: '<S1>/ZERO9' */
    VtMaxErrIntSat[(i)] = 0U;

    /* Constant: '<S1>/ZERO11' */
    VtILeadObjRt[(i)] = 0U;

    /* Constant: '<S1>/ZERO12' */
    VtPIErrDwellObjDir[(i)] = 0;

    /* Constant: '<S1>/ZERO4' */
    VtPIErrDwellObj[(i)] = 0;

    /* Constant: '<S1>/ZERO6' */
    VtErrILeadObj[(i)] = 0;

    /* Constant: '<S1>/ZERO5' */
    VtErrPropDwellObj[(i)] = 0;

    /* Constant: '<S1>/Ones1'
     *
     * Block requirements for '<S1>/Ones1':
     *  1. EISB_FCA6CYL_SW_REQ_1783: VtSelPlaCtrlOL is initialized at true value at the PowerOn event. (ECU_SW_Requirements#6341)
     */
    VtSelPlaCtrlOL[(i)] = true;

    /* Constant: '<S1>/ZERO8' */
    VtTimCmdStall[(i)] = 0U;

    /* Constant: '<S1>/ZERO7' */
    TmpCntIGNInOff[(i)] = 0U;

    /* Constant: '<S1>/ZERO13' */
    VtILeadObjMem[(i)] = 0U;

    /* Constant: '<S1>/ZERO14' */
    VtDILeadObjRt[(i)] = 0;
  }

  /* Constant: '<S1>/ZERO_INDEX2' */
  DILeadObjRt0 = 0;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CoilTarget_Init' */
}

/* Model step function */
void CoilTarget_T10ms(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/CoilTarget_T10ms' incorporates:
   *  SubSystem: '<Root>/CoilTarget_T10ms_mgm'
   *
   * Block description for '<Root>/CoilTarget_T10ms_mgm':
   *  Each 10ms event the timer VtTimCmdStall is increased by 10ms for all the cylinders.
   *  The timer of the current cylinder is then reset on its angular event, but it's used before to detect the time elapsed between two angular events on the same cylinder, as a condition for the stall detection to enable closed loop control.
   *
   * Block requirements for '<Root>/CoilTarget_T10ms_mgm':
   *  1. EISB_FCA6CYL_SW_REQ_1784: The software shall increase on 10ms event the timer VtTimCmdStall ... (ECU_SW_Requirements#6343)
   */
  /* Assignment: '<S2>/Assignment1' incorporates:
   *  Constant: '<S2>/TIMEDELAYTIMCMDSTALL'
   *  SignalConversion generated from: '<S2>/VtTimCmdStall_old'
   *  Sum: '<S2>/Add'
   */
  for (i = 0; i < 8; i++) {
    VtTimCmdStall[(i)] = VtTimCmdStall[(i)] + TIMEDELAYTIMCMDSTALL;
  }

  /* End of Assignment: '<S2>/Assignment1' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CoilTarget_T10ms' */
}

/* Model step function */
void CoilTarget_Tdc(void)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_IR_U16;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  uint16_T rtb_PreLookUpIdSearch_S16_o1_i;
  uint16_T rtb_PreLookUpIdSearch_S16_o2_g;
  uint16_T rtb_Look2D_IR_U16_g;
  uint16_T rtb_PreLookUpIdSearch_S16_o1_l;
  uint16_T rtb_PreLookUpIdSearch_S16_o2_c;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_i;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_p;
  uint16_T rtb_Look2D_IR_U16_gw;
  uint16_T rtb_Look2D_IR_U16_m;
  uint16_T rtb_Look2D_IR_U16_c;
  int16_T rtb_Look2D_IR_S16;
  uint8_T tmp_index;
  uint8_T offset_row;
  uint8_T offset_col;
  uint8_T rtb_CylPlaAbs_idx;
  uint16_T rtb_Switch_l;
  uint16_T rtb_DataTypeConversion3_l;
  uint16_T rtb_DataTypeConversion1_c;
  boolean_T rtb_Switch_o0;
  uint32_T rtb_Switch2;
  int32_T rtb_MAXDWELLOBJDEF;
  int32_T rtb_DataTypeConversion2;
  int32_T rtb_dwell_time_min_gain;
  uint32_T rtb_MinMax;
  uint16_T rtb_DataTypeConversion1_j;
  uint16_T rtb_ILeadObjRt;
  int32_T rtb_ErrPropDwellObj_n;
  int16_T rtb_Assignment1[8];
  int16_T rtb_Assignment2[8];
  int16_T rtb_DataTypeConversion2_g;
  int16_T rtb_DILeadObjRt;
  uint16_T rtb_CntAbsTrigIn_g;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_o;
  uint8_T idx_row;
  uint8_T idx_col;
  boolean_T guard1 = false;

  /* RootInportFunctionCallGenerator generated from: '<Root>/CoilTarget_Tdc' incorporates:
   *  SubSystem: '<Root>/CoilTarget_Tdc_mgm'
   *
   * Block description for '<Root>/CoilTarget_Tdc_mgm':
   *  Operations done on the angular event.
   */
  /* Switch: '<S11>/Switch' incorporates:
   *  Constant: '<S11>/SELCTABSIDX'
   *  Inport: '<Root>/CylPlaAbsOff_idx'
   *  Inport: '<Root>/CylPlaAbsOn_idx'
   *
   * Block requirements for '<S11>/SELCTABSIDX':
   *  1. EISB_FCA6CYL_SW_REQ_1785: At each angular event, the software shall work on the coil primary... (ECU_SW_Requirements#6344)
   */
  if (SELCTABSIDX) {
    rtb_CylPlaAbs_idx = CylPlaAbsOff_idx;
  } else {
    rtb_CylPlaAbs_idx = CylPlaAbsOn_idx;
  }

  /* End of Switch: '<S11>/Switch' */

  /* Switch: '<S71>/Switch2' incorporates:
   *  Constant: '<S71>/TIMIPRISTALL'
   *  Constant: '<S72>/DiagCoil'
   *  Constant: '<S72>/DiagPri'
   *  Constant: '<S72>/FAULT'
   *  Constant: '<S72>/FAULT1'
   *  Inport: '<Root>/CntIGNInOff'
   *  Inport: '<Root>/StDiag'
   *  Logic: '<S71>/Logical Operator'
   *  Logic: '<S72>/Logical Operator'
   *  RelationalOperator: '<S71>/Relational Operator1'
   *  RelationalOperator: '<S72>/Relational Operator'
   *  RelationalOperator: '<S72>/Relational Operator1'
   *  Selector: '<S71>/Selector1'
   *  Selector: '<S71>/Selector3'
   *  Selector: '<S71>/Selector4'
   *  Selector: '<S72>/Selector1'
   *  Selector: '<S72>/Selector2'
   *  Selector: '<S72>/Selector3'
   *  Selector: '<S72>/Selector5'
   *
   * Block requirements for '<S71>/TIMIPRISTALL':
   *  1. EISB_FCA6CYL_SW_REQ_1782: The software shall enable the closed loop control (VtSelPlaCtrlOL ... (ECU_SW_Requirements#6340)
   *
   * Block requirements for '<S72>/Logical Operator':
   *  1. EISB_FCA6CYL_SW_REQ_1782: The software shall enable the closed loop control (VtSelPlaCtrlOL ... (ECU_SW_Requirements#6340)
   */
  if (((((uint32_T)StDiag[(DiagPri[(rtb_CylPlaAbs_idx)])]) == FAULT) ||
       (((uint32_T)StDiag[(DiagCoil[(rtb_CylPlaAbs_idx)])]) == FAULT)) ||
      (VtTimCmdStall[(rtb_CylPlaAbs_idx)] > TIMIPRISTALL)) {
    rtb_Switch2 = CntIGNInOff[(rtb_CylPlaAbs_idx)];
  } else {
    rtb_Switch2 = TmpCntIGNInOff[(rtb_CylPlaAbs_idx)];
  }

  /* End of Switch: '<S71>/Switch2' */

  /* Switch: '<S10>/Switch' incorporates:
   *  Constant: '<S10>/SELPLACTRLOL'
   *  Constant: '<S71>/CNTTDCIPRICL'
   *  Inport: '<Root>/CntIGNInOff'
   *  RelationalOperator: '<S71>/Relational Operator3'
   *  Selector: '<S71>/Selector3'
   *  Sum: '<S71>/Add'
   *
   * Block requirements for '<S10>/SELPLACTRLOL':
   *  1. EISB_FCA6CYL_SW_REQ_1782: The software shall enable the closed loop control (VtSelPlaCtrlOL ... (ECU_SW_Requirements#6340)
   *
   * Block requirements for '<S71>/CNTTDCIPRICL':
   *  1. EISB_FCA6CYL_SW_REQ_1782: The software shall enable the closed loop control (VtSelPlaCtrlOL ... (ECU_SW_Requirements#6340)
   *
   * Block requirements for '<S71>/Relational Operator3':
   *  1. EISB_FCA6CYL_SW_REQ_1782: The software shall enable the closed loop control (VtSelPlaCtrlOL ... (ECU_SW_Requirements#6340)
   */
  if (SELPLACTRLOL) {
    rtb_Switch_o0 = SELPLACTRLOL;
  } else {
    rtb_Switch_o0 = (CntIGNInOff[(rtb_CylPlaAbs_idx)] <= (rtb_Switch2 +
      ((uint32_T)CNTTDCIPRICL)));
  }

  /* End of Switch: '<S10>/Switch' */

  /* Switch: '<S12>/Switch' incorporates:
   *  Constant: '<S12>/LATCHIPRIPICTRL'
   *
   * Block requirements for '<S12>/LATCHIPRIPICTRL':
   *  1. EISB_FCA6CYL_SW_REQ_1787: The signal VtSelPlaCtrlOL can be bypassed to force closed loop con... (ECU_SW_Requirements#6342)
   */
  SelPlaCtrlOL_latched = ((LATCHIPRIPICTRL) && rtb_Switch_o0);

  /* Switch: '<S16>/Switch' incorporates:
   *  Constant: '<S16>/SELCTVBATT'
   *  Inport: '<Root>/VBattery'
   *  Inport: '<Root>/VBatteryF'
   *
   * Block requirements for '<S16>/SELCTVBATT':
   *  1. EISB_FCA6CYL_SW_REQ_1779: The software shall calculate the high saturation of the PI control... (ECU_SW_Requirements#6363)
   */
  if (SELCTVBATT) {
    rtb_Switch_l = VBattery;
  } else {
    rtb_Switch_l = VBatteryF;
  }

  /* End of Switch: '<S16>/Switch' */

  /* S-Function (PreLookUpIdSearch_U16): '<S69>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S16>/BKVBATILEADOBJ'
   *  Constant: '<S16>/BKVBATILEADOBJ_dim'
   */
  PreLookUpIdSearch_U16( &rtb_Switch_l, &rtb_DataTypeConversion3_l, rtb_Switch_l,
                        &BKVBATILEADOBJ[0], ((uint8_T)BKVBATILEADOBJ_dim));

  /* S-Function (PreLookUpIdSearch_S16): '<S64>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S15>/BKTWILEADOBJ'
   *  Constant: '<S15>/BKTWILEADOBJ_dim'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1_l,
                        &rtb_PreLookUpIdSearch_S16_o2_c, TWater, &BKTWILEADOBJ[0],
                        ((uint8_T)BKTWILEADOBJ_dim));

  /* S-Function (Look2D_IR_U16): '<S68>/Look2D_IR_U16' incorporates:
   *  Constant: '<S16>/BKTWILEADOBJ_dim'
   *  Constant: '<S16>/BKVBATILEADOBJ_dim'
   *  Constant: '<S16>/TBMAXERRINTSATDWOBJ'
   *
   * Block requirements for '<S16>/TBMAXERRINTSATDWOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_1779: The software shall calculate the high saturation of the PI control... (ECU_SW_Requirements#6363)
   */
  Look2D_IR_U16( &rtb_Look2D_IR_U16, &TBMAXERRINTSATDWOBJ[0], rtb_Switch_l,
                rtb_DataTypeConversion3_l, ((uint8_T)BKVBATILEADOBJ_dim),
                rtb_PreLookUpIdSearch_S16_o1_l, rtb_PreLookUpIdSearch_S16_o2_c,
                ((uint8_T)BKTWILEADOBJ_dim));

  /* Switch: '<S28>/Switch1' incorporates:
   *  Constant: '<S28>/ZERO'
   *
   * Block requirements for '<S28>/Switch1':
   *  1. EISB_FCA6CYL_SW_REQ_1789: In case of open loop control is active (SelPlaCtrlOL_latched at tr... (ECU_SW_Requirements#6362)
   */
  if (SelPlaCtrlOL_latched) {
    /* Switch: '<S28>/Switch2' incorporates:
     *  Constant: '<S28>/SELPIFFWCTRL'
     *  Constant: '<S28>/ZERO1'
     *
     * Block requirements for '<S28>/SELPIFFWCTRL':
     *  1. EISB_FCA6CYL_SW_REQ_1789: In case of open loop control is active (SelPlaCtrlOL_latched at tr... (ECU_SW_Requirements#6362)
     */
    if (SELPIFFWCTRL) {
      InvPIErrDwellObj = 0;
    } else {
      /* DataTypeConversion: '<S28>/Data Type Conversion3' */
      InvPIErrDwellObj = (rtb_Look2D_IR_U16 << ((uint32_T)2));

      /* DataTypeConversion: '<S28>/Data Type Conversion2' incorporates:
       *  Constant: '<S28>/MINERRINTSATDWOBJ'
       *
       * Block requirements for '<S28>/MINERRINTSATDWOBJ':
       *  1. EISB_FCA6CYL_SW_REQ_1789: In case of open loop control is active (SelPlaCtrlOL_latched at tr... (ECU_SW_Requirements#6362)
       */
      rtb_DataTypeConversion2 = ((int32_T)MINERRINTSATDWOBJ) * 8;

      /* Product: '<S28>/Product' incorporates:
       *  Constant: '<S28>/SCDWELLOBJGAIN'
       *  Constant: '<S28>/SCDWELLOBJOFF'
       *  DataTypeConversion: '<S28>/Data Type Conversion1'
       *  Inport: '<Root>/VtDwellTime'
       *  Selector: '<S20>/Selector7'
       *  Sum: '<S28>/Add2'
       *
       * Block requirements for '<S28>/SCDWELLOBJGAIN':
       *  1. EISB_FCA6CYL_SW_REQ_1789: In case of open loop control is active (SelPlaCtrlOL_latched at tr... (ECU_SW_Requirements#6362)
       *
       * Block requirements for '<S28>/SCDWELLOBJOFF':
       *  1. EISB_FCA6CYL_SW_REQ_1789: In case of open loop control is active (SelPlaCtrlOL_latched at tr... (ECU_SW_Requirements#6362)
       */
      rtb_dwell_time_min_gain = (((int32_T)VtDwellTime[(rtb_CylPlaAbs_idx)]) -
        ((int32_T)SCDWELLOBJOFF)) * ((int32_T)SCDWELLOBJGAIN);

      /* MinMax: '<S28>/MinMax' */
      if (rtb_dwell_time_min_gain > rtb_DataTypeConversion2) {
        rtb_DataTypeConversion2 = rtb_dwell_time_min_gain;
      }

      /* MinMax: '<S28>/MinMax1' incorporates:
       *  MinMax: '<S28>/MinMax'
       */
      if (rtb_DataTypeConversion2 < InvPIErrDwellObj) {
        InvPIErrDwellObj = rtb_DataTypeConversion2;
      }

      /* End of MinMax: '<S28>/MinMax1' */
    }

    /* End of Switch: '<S28>/Switch2' */
  } else {
    InvPIErrDwellObj = 0;
  }

  /* End of Switch: '<S28>/Switch1' */

  /* Constant: '<S48>/MAXILEADDEF' */
  rtb_DataTypeConversion3_l = (uint16_T)((int16_T)MAXILEADDEF);

  /* Switch: '<S37>/Switch' incorporates:
   *  Constant: '<S37>/DIAG_CAN_NODE_1'
   *  Constant: '<S37>/DIAG_CAN_NODE_OVER_RUN'
   *  Constant: '<S37>/DIAG_PRIVATE_CAN'
   *  Constant: '<S37>/FAULT2'
   *  Constant: '<S37>/FOFLGNOMAP'
   *  Constant: '<S40>/Constant'
   *  Constant: '<S41>/Constant'
   *  Constant: '<S42>/Constant'
   *  Inport: '<Root>/Rpm'
   *  Inport: '<Root>/StDiag'
   *  Logic: '<S37>/Logical Operator1'
   *  RelationalOperator: '<S37>/Relational Operator2'
   *  RelationalOperator: '<S37>/Relational Operator3'
   *  RelationalOperator: '<S37>/Relational Operator4'
   *  RelationalOperator: '<S40>/Compare'
   *  RelationalOperator: '<S41>/Compare'
   *  RelationalOperator: '<S42>/Compare'
   *  Selector: '<S37>/Selector1'
   *  Selector: '<S37>/Selector2'
   *  Selector: '<S37>/Selector4'
   *
   * Block requirements for '<S37>/Switch':
   *  1. EISB_FCA6CYL_SW_REQ_1777: The software shall set the flag FlgNoMap, if there is not any acti... (ECU_SW_Requirements#6330)
   *
   * Block requirements for '<S37>/FOFLGNOMAP':
   *  1. EISB_FCA6CYL_SW_REQ_1777: The software shall set the flag FlgNoMap, if there is not any acti... (ECU_SW_Requirements#6330)
   *
   * Block requirements for '<S37>/Logical Operator1':
   *  1. EISB_FCA6CYL_SW_REQ_1777: The software shall set the flag FlgNoMap, if there is not any acti... (ECU_SW_Requirements#6330)
   */
  if (FOFLGNOMAP >= 0) {
    FlgNoMap = (FOFLGNOMAP > 0);
  } else {
    FlgNoMap = ((((((uint32_T)StDiag[(DIAG_CAN_NODE_OVER_RUN)]) == FAULT) ||
                  (((uint32_T)StDiag[(DIAG_PRIVATE_CAN)]) == FAULT)) ||
                 (((uint32_T)StDiag[(DIAG_CAN_NODE_1)]) == FAULT)) || (((int32_T)
      Rpm) == 0));
  }

  /* End of Switch: '<S37>/Switch' */

  /* S-Function (PreLookUpIdSearch_U16): '<S65>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S15>/BKLOADILEADOBJ'
   *  Constant: '<S15>/BKLOADILEADOBJ_dim'
   *
   * Block requirements for '<S15>/BKLOADILEADOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_1871: The software shall calculate the signal LoadDPlaEn, as output of t... (ECU_SW_Requirements#7292)
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, Load, &BKLOADILEADOBJ[0],
                        ((uint8_T)BKLOADILEADOBJ_dim));

  /* Switch: '<S50>/Switch' incorporates:
   *  Constant: '<S50>/ILEADOBJREC'
   *
   * Block requirements for '<S50>/ILEADOBJREC':
   *  1. EISB_FCA6CYL_SW_REQ_229: The software shall estimate the coil primary current peak basic ta... (ECU_SW_Requirements#291)
   */
  if (FlgNoMap) {
    rtb_Switch_l = ILEADOBJREC;
  } else {
    /* S-Function (PreLookUpIdSearch_U16): '<S61>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S50>/BKRPMILEADOBJ'
     *  Constant: '<S50>/BKRPMILEADOBJ_dim1'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_i,
                          &rtb_PreLookUpIdSearch_U16_o2_p, Rpm, &BKRPMILEADOBJ[0],
                          ((uint8_T)BKRPMILEADOBJ_dim));

    /* S-Function (Look2D_IR_U16): '<S60>/Look2D_IR_U16' incorporates:
     *  Constant: '<S50>/BKLOADILEADOBJ_dim'
     *  Constant: '<S50>/BKRPMILEADOBJ_dim1'
     *  Constant: '<S50>/TBILEADOBJ'
     *
     * Block requirements for '<S50>/TBILEADOBJ':
     *  1. EISB_FCA6CYL_SW_REQ_229: The software shall estimate the coil primary current peak basic ta... (ECU_SW_Requirements#291)
     */
    Look2D_IR_U16( &rtb_Look2D_IR_U16_g, &TBILEADOBJ[0],
                  rtb_PreLookUpIdSearch_U16_o1_i, rtb_PreLookUpIdSearch_U16_o2_p,
                  ((uint8_T)BKRPMILEADOBJ_dim), rtb_PreLookUpIdSearch_U16_o1,
                  rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKLOADILEADOBJ_dim));
    rtb_Switch_l = rtb_Look2D_IR_U16_g;
  }

  /* End of Switch: '<S50>/Switch' */

  /* S-Function (LookUp_IR_U16): '<S53>/LookUp_IR_U16' incorporates:
   *  Constant: '<S48>/BKTWILEADOBJ_dim'
   *  Constant: '<S48>/VTTWILEADOBJGAIN'
   *
   * Block requirements for '<S48>/VTTWILEADOBJGAIN':
   *  1. EISB_FCA6CYL_SW_REQ_233: The coil primary peak current basic target shall be corrected addi... (ECU_SW_Requirements#293)
   */
  LookUp_IR_U16( &rtb_DataTypeConversion1_c, &VTTWILEADOBJGAIN[0],
                rtb_PreLookUpIdSearch_S16_o1_l, rtb_PreLookUpIdSearch_S16_o2_c,
                ((uint8_T)BKTWILEADOBJ_dim));

  /* S-Function (LookUp_IR_S16): '<S52>/LookUp_IR_S16' incorporates:
   *  Constant: '<S48>/BKTWILEADOBJ_dim'
   *  Constant: '<S48>/VTTWILEADOBJOFF'
   *
   * Block requirements for '<S48>/VTTWILEADOBJOFF':
   *  1. EISB_FCA6CYL_SW_REQ_233: The coil primary peak current basic target shall be corrected addi... (ECU_SW_Requirements#293)
   */
  LookUp_IR_S16( &rtb_Look2D_IR_S16, &VTTWILEADOBJOFF[0],
                rtb_PreLookUpIdSearch_S16_o1_l, rtb_PreLookUpIdSearch_S16_o2_c,
                ((uint8_T)BKTWILEADOBJ_dim));

  /* Sum: '<S48>/Add1' incorporates:
   *  Product: '<S48>/Product1'
   *
   * Block requirements for '<S48>/Add1':
   *  1. EISB_FCA6CYL_SW_REQ_233: The coil primary peak current basic target shall be corrected addi... (ECU_SW_Requirements#293)
   */
  rtb_ErrPropDwellObj_n = ((int32_T)((uint32_T)((((uint32_T)rtb_Switch_l) *
    ((uint32_T)rtb_DataTypeConversion1_c)) >> ((uint32_T)10)))) + ((int32_T)
    rtb_Look2D_IR_S16);

  /* MinMax: '<S48>/MinMax3' incorporates:
   *  Constant: '<S48>/Constant2'
   */
  if (rtb_ErrPropDwellObj_n <= 0) {
    rtb_ErrPropDwellObj_n = 0;
  }

  /* Switch: '<S48>/Switch1' incorporates:
   *  MinMax: '<S48>/MinMax3'
   *  RelationalOperator: '<S48>/Relational Operator'
   */
  if (((int32_T)rtb_DataTypeConversion3_l) >= rtb_ErrPropDwellObj_n) {
    rtb_DataTypeConversion3_l = (uint16_T)rtb_ErrPropDwellObj_n;
  }

  /* End of Switch: '<S48>/Switch1' */

  /* Switch: '<S48>/Switch' incorporates:
   *  Constant: '<S48>/Constant'
   *  Constant: '<S48>/ENTSPARKCORR'
   *  Inport: '<Root>/IPriCorrCyl'
   *
   * Block requirements for '<S48>/Switch':
   *  1. EISB_FCA6CYL_SW_REQ_235: The coil primary peak current basic target shall be corrected usin... (ECU_SW_Requirements#295)
   *
   * Block requirements for '<S48>/ENTSPARKCORR':
   *  1. EISB_FCA6CYL_SW_REQ_235: The coil primary peak current basic target shall be corrected usin... (ECU_SW_Requirements#295)
   */
  if (ENTSPARKCORR) {
    rtb_DataTypeConversion1_c = IPriCorrCyl;
  } else {
    rtb_DataTypeConversion1_c = 32768U;
  }

  /* End of Switch: '<S48>/Switch' */

  /* Product: '<S48>/Product3'
   *
   * Block requirements for '<S48>/Product3':
   *  1. EISB_FCA6CYL_SW_REQ_235: The coil primary peak current basic target shall be corrected usin... (ECU_SW_Requirements#295)
   */
  rtb_MinMax = ((((uint32_T)rtb_DataTypeConversion3_l) * ((uint32_T)
    rtb_DataTypeConversion1_c)) >> ((uint32_T)15));

  /* MinMax: '<S48>/MinMax1' incorporates:
   *  Constant: '<S48>/MINSATILEADOBJ'
   *
   * Block requirements for '<S48>/MINSATILEADOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_235: The coil primary peak current basic target shall be corrected usin... (ECU_SW_Requirements#295)
   */
  if (((uint32_T)MINSATILEADOBJ) > rtb_MinMax) {
    rtb_MinMax = (uint32_T)MINSATILEADOBJ;
  }

  /* MinMax: '<S48>/MinMax' incorporates:
   *  Constant: '<S48>/MAXSATILEADOBJ'
   *  MinMax: '<S48>/MinMax1'
   *
   * Block requirements for '<S48>/MAXSATILEADOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_235: The coil primary peak current basic target shall be corrected usin... (ECU_SW_Requirements#295)
   */
  if (((uint32_T)MAXSATILEADOBJ) < rtb_MinMax) {
    /* DataTypeConversion: '<S48>/Data Type Conversion1' */
    rtb_DataTypeConversion1_c = MAXSATILEADOBJ;
  } else {
    /* DataTypeConversion: '<S48>/Data Type Conversion1' */
    rtb_DataTypeConversion1_c = (uint16_T)rtb_MinMax;
  }

  /* End of MinMax: '<S48>/MinMax' */

  /* Switch: '<S51>/Switch1' incorporates:
   *  DataTypeConversion: '<S51>/Data Type Conversion'
   *  DataTypeConversion: '<S51>/Data Type Conversion1'
   *  Product: '<S51>/Product'
   *  RelationalOperator: '<S51>/Relational Operator'
   *  Selector: '<S51>/Selector'
   *  Sum: '<S51>/Add'
   *  Sum: '<S51>/Add1'
   *  Switch: '<S51>/Switch'
   *
   * Block requirements for '<S51>/Switch1':
   *  1. EISB_FCA6CYL_SW_REQ_236: The coil primary peak current target (ILeadObj0) can be filtered u... (ECU_SW_Requirements#296)
   */
  if (rtb_Switch_o0) {
    rtb_MinMax = (((uint32_T)rtb_DataTypeConversion1_c) << ((uint32_T)14));
  } else {
    if (((int32_T)rtb_DataTypeConversion1_c) < ((int32_T)((uint32_T)((((uint32_T)
             ILeadObj) * 25U) >> ((uint32_T)4))))) {
      /* Switch: '<S51>/Switch' incorporates:
       *  Constant: '<S51>/KFTRGDOWN'
       *
       * Block requirements for '<S51>/KFTRGDOWN':
       *  1. EISB_FCA6CYL_SW_REQ_236: The coil primary peak current target (ILeadObj0) can be filtered u... (ECU_SW_Requirements#296)
       */
      rtb_DataTypeConversion3_l = KFTRGDOWN;
    } else {
      /* Switch: '<S51>/Switch' incorporates:
       *  Constant: '<S51>/KFTRGUP'
       *
       * Block requirements for '<S51>/KFTRGUP':
       *  1. EISB_FCA6CYL_SW_REQ_236: The coil primary peak current target (ILeadObj0) can be filtered u... (ECU_SW_Requirements#296)
       */
      rtb_DataTypeConversion3_l = KFTRGUP;
    }

    rtb_MinMax = (uint32_T)((int32_T)(((((int32_T)rtb_DataTypeConversion1_c) -
      ((int32_T)((uint32_T)(VtILeadObjMem[(rtb_CylPlaAbs_idx)] >> ((uint32_T)14)))))
      * ((int32_T)rtb_DataTypeConversion3_l)) + ((int32_T)VtILeadObjMem
      [(rtb_CylPlaAbs_idx)])));
  }

  /* End of Switch: '<S51>/Switch1' */

  /* DataTypeConversion: '<S51>/Data Type Conversion3' */
  rtb_DataTypeConversion3_l = (uint16_T)(rtb_MinMax / 25600U);

  /* DataTypeConversion: '<S18>/Data Type Conversion1' */
  rtb_DataTypeConversion1_j = (uint16_T)((((uint32_T)rtb_DataTypeConversion3_l) *
    25U) >> ((uint32_T)4));

  /* Chart: '<S18>/rate_limiter_and_ctrl_reset' incorporates:
   *  Constant: '<S18>/ENRSTCTCTRL'
   *  Constant: '<S18>/MAXRTBKKPDTRG'
   *  Constant: '<S18>/MINRTBKKPDTRG'
   *  Constant: '<S18>/SELPIFFWCTRL'
   *  Selector: '<S20>/Selector1'
   *
   * Block requirements for '<S18>/rate_limiter_and_ctrl_reset':
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   *
   * Block requirements for '<S18>/ENRSTCTCTRL':
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   *
   * Block requirements for '<S18>/MAXRTBKKPDTRG':
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   *
   * Block requirements for '<S18>/MINRTBKKPDTRG':
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   *
   * Block requirements for '<S18>/SELPIFFWCTRL':
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   */
  /* Gateway: CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_Error/rate_limiter_and_ctrl_reset
   * Requirements for Gateway: CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_Error/rate_limiter_and_ctrl_reset:
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   */
  /* During: CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_Error/rate_limiter_and_ctrl_reset
   * Requirements for During: CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_Error/rate_limiter_and_ctrl_reset:
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   */
  /* Calculation of the rate limited signal and reset of the control (if enabled) in case of the step is too high.  */
  /* Entry Internal: CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_Error/rate_limiter_and_ctrl_reset
   * Requirements for Entry Internal: CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_Error/rate_limiter_and_ctrl_reset:
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   */
  /* Transition: '<S27>:26' */
  VtResetMem[(rtb_CylPlaAbs_idx)] = false;
  if (SelPlaCtrlOL_latched) {
    /* Transition: '<S27>:52' */
    rtb_DILeadObjRt = 0;
    rtb_ILeadObjRt = rtb_DataTypeConversion1_j;

    /* Transition: '<S27>:60' */
  } else {
    /* Transition: '<S27>:49' */
    rtb_DILeadObjRt = (int16_T)((int32_T)(((int32_T)rtb_DataTypeConversion1_j) -
      ((int32_T)VtILeadObjRt[(rtb_CylPlaAbs_idx)])));
    guard1 = false;
    if (rtb_DILeadObjRt >= MAXRTBKKPDTRG) {
      /* Transition: '<S27>:35' */
      rtb_DILeadObjRt = MAXRTBKKPDTRG;

      /* Transition: '<S27>:53' */
      guard1 = true;
    } else {
      /* Transition: '<S27>:40' */
      if (rtb_DILeadObjRt <= MINRTBKKPDTRG) {
        /* Transition: '<S27>:42' */
        rtb_DILeadObjRt = MINRTBKKPDTRG;
        guard1 = true;
      } else {
        /* Transition: '<S27>:44' */
        rtb_ILeadObjRt = rtb_DataTypeConversion1_j;
      }
    }

    if (guard1) {
      /* Transition: '<S27>:59' */
      rtb_ILeadObjRt = (uint16_T)((int32_T)(((int32_T)VtILeadObjRt
        [(rtb_CylPlaAbs_idx)]) + ((int32_T)rtb_DILeadObjRt)));
      VtResetMem[(rtb_CylPlaAbs_idx)] = ((ENRSTCTCTRL) && (SELPIFFWCTRL));
    }
  }

  /* Selector: '<S20>/Selector5' incorporates:
   *  Inport: '<Root>/VtILeadPeak'
   */
  /* Transition: '<S27>:62' */
  rtb_PreLookUpIdSearch_U16_o2_o = VtILeadPeak[(rtb_CylPlaAbs_idx)];

  /* DataTypeConversion: '<S18>/Data Type Conversion3' */
  rtb_CntAbsTrigIn_g = (uint16_T)((((uint32_T)rtb_PreLookUpIdSearch_U16_o2_o) *
    25U) >> ((uint32_T)4));

  /* DataTypeConversion: '<S18>/Data Type Conversion2' incorporates:
   *  Sum: '<S18>/Add'
   *
   * Block requirements for '<S18>/Add':
   *  1. EISB_FCA6CYL_SW_REQ_254: The software shall estimate the peak current closed loop error (si... (ECU_SW_Requirements#311)
   */
  rtb_DataTypeConversion2_g = (int16_T)((int32_T)(((int32_T)
    rtb_DataTypeConversion1_j) - ((int32_T)rtb_CntAbsTrigIn_g)));

  /* S-Function (PreLookUpIdSearch_S16): '<S32>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S19>/BKKPDWELLOBJ'
   *  Constant: '<S19>/BKKPDWELLOBJ_dim1'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                        &rtb_PreLookUpIdSearch_S16_o2, rtb_DataTypeConversion2_g,
                        &BKKPDWELLOBJ[0], ((uint8_T)BKKPDWELLOBJ_dim));

  /* S-Function (PreLookUpIdSearch_S16): '<S31>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S19>/BKKPDWELLDTRG'
   *  Constant: '<S19>/BKKPDWELLDTRG_dim1'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1_i,
                        &rtb_PreLookUpIdSearch_S16_o2_g, rtb_DILeadObjRt,
                        &BKKPDWELLDTRG[0], ((uint8_T)BKKPDWELLDTRG_dim));

  /* Switch: '<S19>/Switch3' incorporates:
   *  Chart: '<S18>/rate_limiter_and_ctrl_reset'
   *  Constant: '<S19>/ZERO2'
   *  Product: '<S19>/Product1'
   *
   * Block requirements for '<S19>/Switch3':
   *  1. EISB_FCA6CYL_SW_REQ_1788: The PI controller can be reset in case of VtResetMem (see requirem... (ECU_SW_Requirements#6361)
   *
   * Block requirements for '<S18>/rate_limiter_and_ctrl_reset':
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   */
  if (VtResetMem[(rtb_CylPlaAbs_idx)]) {
    rtb_DataTypeConversion2 = 0;
  } else {
    /* S-Function (Look2D_IR_U16): '<S30>/Look2D_IR_U16' incorporates:
     *  Constant: '<S19>/BKKPDWELLDTRG_dim'
     *  Constant: '<S19>/BKKPDWELLOBJ_dim'
     *  Constant: '<S19>/TBKPDWELLOBJ'
     *
     * Block requirements for '<S19>/TBKPDWELLOBJ':
     *  1. EISB_FCA6CYL_SW_REQ_255: A PI controller shall be implemented using the estimated error (si... (ECU_SW_Requirements#312)
     */
    Look2D_IR_U16( &rtb_Look2D_IR_U16_m, &TBKPDWELLOBJ[0],
                  rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                  ((uint8_T)BKKPDWELLOBJ_dim), rtb_PreLookUpIdSearch_S16_o1_i,
                  rtb_PreLookUpIdSearch_S16_o2_g, ((uint8_T)BKKPDWELLDTRG_dim));
    rtb_DataTypeConversion2 = ((int32_T)rtb_Look2D_IR_U16_m) * ((int32_T)
      rtb_DataTypeConversion2_g);
  }

  /* End of Switch: '<S19>/Switch3' */

  /* Switch: '<S19>/Switch2' incorporates:
   *  Chart: '<S18>/rate_limiter_and_ctrl_reset'
   *  Constant: '<S19>/ZERO'
   *  Constant: '<S19>/ZERO1'
   *  Inport: '<Root>/VtILPGlitch'
   *  Logic: '<S19>/Logical Operator1'
   *  Product: '<S19>/Product3'
   *  RelationalOperator: '<S19>/Relational Operator'
   *  Selector: '<S20>/Selector6'
   *
   * Block requirements for '<S19>/Switch2':
   *  1. EISB_FCA6CYL_SW_REQ_1788: The PI controller can be reset in case of VtResetMem (see requirem... (ECU_SW_Requirements#6361)
   *
   * Block requirements for '<S18>/rate_limiter_and_ctrl_reset':
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   */
  if (((VtResetMem[(rtb_CylPlaAbs_idx)]) || (SelPlaCtrlOL_latched)) ||
      (((int32_T)VtILPGlitch[(rtb_CylPlaAbs_idx)]) > 0)) {
    rtb_dwell_time_min_gain = 0;
  } else {
    /* S-Function (Look2D_IR_U16): '<S29>/Look2D_IR_U16' incorporates:
     *  Constant: '<S19>/BKKPDWELLDTRG_dim'
     *  Constant: '<S19>/BKKPDWELLOBJ_dim'
     *  Constant: '<S19>/TBKIDWELLOBJ'
     *
     * Block requirements for '<S19>/TBKIDWELLOBJ':
     *  1. EISB_FCA6CYL_SW_REQ_255: A PI controller shall be implemented using the estimated error (si... (ECU_SW_Requirements#312)
     */
    Look2D_IR_U16( &rtb_Look2D_IR_U16_gw, &TBKIDWELLOBJ[0],
                  rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                  ((uint8_T)BKKPDWELLOBJ_dim), rtb_PreLookUpIdSearch_S16_o1_i,
                  rtb_PreLookUpIdSearch_S16_o2_g, ((uint8_T)BKKPDWELLDTRG_dim));
    rtb_dwell_time_min_gain = ((int32_T)rtb_DataTypeConversion2_g) * ((int32_T)
      rtb_Look2D_IR_U16_gw);
  }

  /* End of Switch: '<S19>/Switch2' */

  /* Switch: '<S19>/Switch' incorporates:
   *  Chart: '<S18>/rate_limiter_and_ctrl_reset'
   *  Logic: '<S19>/Logical Operator'
   *  Switch: '<S19>/Switch1'
   *
   * Block requirements for '<S18>/rate_limiter_and_ctrl_reset':
   *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
   *
   * Block requirements for '<S19>/Switch1':
   *  1. EISB_FCA6CYL_SW_REQ_1788: The PI controller can be reset in case of VtResetMem (see requirem... (ECU_SW_Requirements#6361)
   */
  if (SelPlaCtrlOL_latched) {
    rtb_ErrPropDwellObj_n = InvPIErrDwellObj;
  } else {
    if ((SelPlaCtrlOL_latched) || (VtResetMem[(rtb_CylPlaAbs_idx)])) {
      /* Switch: '<S19>/Switch1' incorporates:
       *  Constant: '<S19>/ZERO3'
       *
       * Block requirements for '<S19>/Switch1':
       *  1. EISB_FCA6CYL_SW_REQ_1788: The PI controller can be reset in case of VtResetMem (see requirem... (ECU_SW_Requirements#6361)
       */
      rtb_ErrPropDwellObj_n = 0;
    } else {
      /* Switch: '<S19>/Switch1' incorporates:
       *  Selector: '<S20>/Selector3'
       *  Selector: '<S20>/Selector4'
       *  Sum: '<S19>/Add2'
       *
       * Block requirements for '<S19>/Switch1':
       *  1. EISB_FCA6CYL_SW_REQ_1788: The PI controller can be reset in case of VtResetMem (see requirem... (ECU_SW_Requirements#6361)
       *
       * Block requirements for '<S19>/Add2':
       *  1. EISB_FCA6CYL_SW_REQ_255: A PI controller shall be implemented using the estimated error (si... (ECU_SW_Requirements#312)
       */
      rtb_ErrPropDwellObj_n = ((rtb_DataTypeConversion2 - VtErrPropDwellObj
        [(rtb_CylPlaAbs_idx)]) + rtb_dwell_time_min_gain) + (VtPIErrDwellObj
        [(rtb_CylPlaAbs_idx)] * 1024);
    }

    /* Switch: '<S34>/Switch1' incorporates:
     *  DataTypeConversion: '<S34>/reduce_scaling1'
     *  DataTypeConversion: '<S34>/reduce_scaling2'
     *  DataTypeConversion: '<S34>/reduce_scaling3'
     *  Product: '<S34>/Product'
     *  Product: '<S34>/Product2'
     *  RelationalOperator: '<S34>/Relational Operator'
     */
    if (rtb_ErrPropDwellObj_n < 0) {
      rtb_ErrPropDwellObj_n = -((int32_T)((uint32_T)(((uint32_T)((int32_T)
        (-rtb_ErrPropDwellObj_n))) >> ((uint32_T)10))));
    } else {
      rtb_ErrPropDwellObj_n = (int32_T)((uint32_T)(((uint32_T)
        rtb_ErrPropDwellObj_n) >> ((uint32_T)10)));
    }

    /* End of Switch: '<S34>/Switch1' */

    /* Switch: '<S33>/Switch2' incorporates:
     *  Constant: '<S19>/MINERRINTSATDWOBJ'
     *  RelationalOperator: '<S33>/LowerRelop1'
     *  RelationalOperator: '<S33>/UpperRelop'
     *  Switch: '<S33>/Switch'
     *
     * Block requirements for '<S19>/MINERRINTSATDWOBJ':
     *  1. EISB_FCA6CYL_SW_REQ_255: A PI controller shall be implemented using the estimated error (si... (ECU_SW_Requirements#312)
     */
    if (rtb_ErrPropDwellObj_n > ((int32_T)((uint32_T)(((uint32_T)
            rtb_Look2D_IR_U16) << ((uint32_T)2))))) {
      rtb_ErrPropDwellObj_n = (int32_T)((uint32_T)(((uint32_T)rtb_Look2D_IR_U16)
        << ((uint32_T)2)));
    } else {
      if (rtb_ErrPropDwellObj_n < (((int32_T)MINERRINTSATDWOBJ) * 8)) {
        /* Switch: '<S33>/Switch' incorporates:
         *  Constant: '<S19>/MINERRINTSATDWOBJ'
         *
         * Block requirements for '<S19>/MINERRINTSATDWOBJ':
         *  1. EISB_FCA6CYL_SW_REQ_255: A PI controller shall be implemented using the estimated error (si... (ECU_SW_Requirements#312)
         */
        rtb_ErrPropDwellObj_n = ((int32_T)MINERRINTSATDWOBJ) * 8;
      }
    }

    /* End of Switch: '<S33>/Switch2' */
  }

  /* End of Switch: '<S19>/Switch' */

  /* If: '<S17>/If' incorporates:
   *  RelationalOperator: '<S25>/Relational Operator'
   *  Switch: '<S25>/Switch4'
   */
  if (SelPlaCtrlOL_latched) {
    /* Outputs for IfAction SubSystem: '<S17>/OpenLoop_TDwell' incorporates:
     *  ActionPort: '<S24>/Action Port'
     *
     * Block description for '<S17>/OpenLoop_TDwell':
     *  Calculation of the dwell time in open loop.
     *
     * Block requirements for '<S17>/OpenLoop_TDwell':
     *  1. EISB_FCA6CYL_SW_REQ_1780: In open loop control the current calculated dwell time (VtDwellTim... (ECU_SW_Requirements#6350)
     */
    /* DataTypeConversion: '<S24>/Data Type Conversion' incorporates:
     *  Inport: '<Root>/VtDwellTime'
     *  Selector: '<S20>/Selector7'
     */
    rtb_Look2D_IR_S16 = (int16_T)VtDwellTime[(rtb_CylPlaAbs_idx)];

    /* End of Outputs for SubSystem: '<S17>/OpenLoop_TDwell' */
  } else {
    /* Outputs for IfAction SubSystem: '<S17>/InversionPI_TDwell' incorporates:
     *  ActionPort: '<S23>/Action Port'
     *
     * Block description for '<S17>/InversionPI_TDwell':
     *  Calculation of the dwell time in closed loop.
     *
     * Block requirements for '<S17>/InversionPI_TDwell':
     *  1. EISB_FCA6CYL_SW_REQ_1790: In closed loop the dwell time target (DwellTimeObj) is calculated ... (ECU_SW_Requirements#6364)
     */
    if (rtb_ErrPropDwellObj_n < 0) {
      /* Switch: '<S25>/Switch4' incorporates:
       *  Constant: '<S23>/SCDWELLOBJGAIN'
       *  Product: '<S25>/Divide'
       *  Product: '<S25>/Product'
       *  Product: '<S25>/Product2'
       */
      rtb_MAXDWELLOBJDEF = -((int32_T)((uint32_T)(((uint32_T)((int32_T)
        (-rtb_ErrPropDwellObj_n))) / ((uint32_T)SCDWELLOBJGAIN))));
    } else {
      /* Switch: '<S25>/Switch4' incorporates:
       *  Constant: '<S23>/SCDWELLOBJGAIN'
       *  DataTypeConversion: '<S25>/Data Type Conversion'
       *  Product: '<S25>/Divide1'
       */
      rtb_MAXDWELLOBJDEF = (int32_T)((uint32_T)(((uint32_T)rtb_ErrPropDwellObj_n)
        / ((uint32_T)SCDWELLOBJGAIN)));
    }

    /* Sum: '<S23>/Add2' incorporates:
     *  Constant: '<S23>/SCDWELLOBJOFF'
     *  DataTypeConversion: '<S23>/Data Type Conversion'
     */
    rtb_MAXDWELLOBJDEF += (int32_T)SCDWELLOBJOFF;

    /* Switch: '<S26>/Switch2' incorporates:
     *  Constant: '<S23>/MAXDWELLOBJDEF'
     *  Constant: '<S23>/MINDWELLOBJDEF'
     *  RelationalOperator: '<S26>/LowerRelop1'
     *  RelationalOperator: '<S26>/UpperRelop'
     *  Switch: '<S26>/Switch'
     */
    if (rtb_MAXDWELLOBJDEF > ((int32_T)((uint16_T)MAXDWELLOBJDEF))) {
      rtb_MAXDWELLOBJDEF = (int32_T)((uint16_T)MAXDWELLOBJDEF);
    } else {
      if (rtb_MAXDWELLOBJDEF < ((int32_T)((int16_T)MINDWELLOBJDEF))) {
        /* Switch: '<S26>/Switch' incorporates:
         *  Constant: '<S23>/MINDWELLOBJDEF'
         */
        rtb_MAXDWELLOBJDEF = (int32_T)((int16_T)MINDWELLOBJDEF);
      }
    }

    /* End of Switch: '<S26>/Switch2' */

    /* Switch: '<S23>/Switch1' incorporates:
     *  Constant: '<S23>/SELPIFFWCTRL'
     *  MinMax: '<S23>/MinMax'
     */
    if (SELPIFFWCTRL) {
      rtb_Look2D_IR_S16 = (int16_T)rtb_MAXDWELLOBJDEF;
    } else if (rtb_MAXDWELLOBJDEF > 0) {
      /* MinMax: '<S23>/MinMax' */
      rtb_Look2D_IR_S16 = (int16_T)rtb_MAXDWELLOBJDEF;
    } else {
      rtb_Look2D_IR_S16 = 0;
    }

    /* End of Switch: '<S23>/Switch1' */
    /* End of Outputs for SubSystem: '<S17>/InversionPI_TDwell' */
  }

  /* End of If: '<S17>/If' */

  /* Assignment: '<S21>/Assignment' */
  VtILeadObjRt[(rtb_CylPlaAbs_idx)] = rtb_ILeadObjRt;
  for (rtb_MAXDWELLOBJDEF = 0; rtb_MAXDWELLOBJDEF < 8; rtb_MAXDWELLOBJDEF++) {
    /* Assignment: '<S21>/Assignment1' */
    rtb_Assignment1[rtb_MAXDWELLOBJDEF] = VtDILeadObjRt[(rtb_MAXDWELLOBJDEF)];

    /* Assignment: '<S21>/Assignment2' */
    rtb_Assignment2[rtb_MAXDWELLOBJDEF] = VtErrILeadObj[(rtb_MAXDWELLOBJDEF)];
  }

  /* Assignment: '<S21>/Assignment1' */
  rtb_Assignment1[rtb_CylPlaAbs_idx] = rtb_DILeadObjRt;

  /* Assignment: '<S21>/Assignment2' */
  rtb_Assignment2[rtb_CylPlaAbs_idx] = rtb_DataTypeConversion2_g;

  /* Assignment: '<S21>/Assignment3' */
  VtErrPropDwellObj[(rtb_CylPlaAbs_idx)] = rtb_DataTypeConversion2;

  /* Assignment: '<S21>/Assignment5' */
  VtPIErrDwellObjDir[(rtb_CylPlaAbs_idx)] = rtb_dwell_time_min_gain;

  /* Assignment: '<S21>/Assignment7'
   *
   * Block requirements for '<S21>/Assignment7':
   *  1. EISB_FCA6CYL_SW_REQ_421: The software shall implement a closed loop control for each primary coil current. (ECU_SW_Requirements#306)
   */
  VtPIErrDwellObj[(rtb_CylPlaAbs_idx)] = rtb_ErrPropDwellObj_n;

  /* Switch: '<S17>/Switch2' incorporates:
   *  Constant: '<S17>/VTFODWELLTIMEOBJ'
   *  Constant: '<S22>/Constant'
   *  RelationalOperator: '<S22>/Compare'
   *  Selector: '<S17>/Selector4'
   *
   * Block requirements for '<S17>/VTFODWELLTIMEOBJ':
   *  1. EISB_FCA6CYL_SW_REQ_1791: The dwell time target (DwellTimeObj) can be also bypassed for test... (ECU_SW_Requirements#6365)
   */
  if (VTFODWELLTIMEOBJ[(rtb_CylPlaAbs_idx)] > 0) {
    /* Assignment: '<S21>/Assignment8' */
    DwellTimeObj[(rtb_CylPlaAbs_idx)] = VTFODWELLTIMEOBJ[(rtb_CylPlaAbs_idx)];
  } else {
    /* Assignment: '<S21>/Assignment8' */
    DwellTimeObj[(rtb_CylPlaAbs_idx)] = rtb_Look2D_IR_S16;
  }

  /* End of Switch: '<S17>/Switch2' */

  /* Selector: '<S21>/Selector' incorporates:
   *  Constant: '<S21>/SELERILEADOBJ0'
   *
   * Block requirements for '<S21>/SELERILEADOBJ0':
   *  1. EISB_FCA6CYL_SW_REQ_1792: The software shall give the possibility to select a cell of the ar... (ECU_SW_Requirements#6367)
   */
  ErrILeadObj0 = rtb_Assignment2[SELERILEADOBJ0];

  /* Selector: '<S21>/Selector2' incorporates:
   *  Constant: '<S21>/SELERILEADOBJ0'
   *
   * Block requirements for '<S21>/SELERILEADOBJ0':
   *  1. EISB_FCA6CYL_SW_REQ_1792: The software shall give the possibility to select a cell of the ar... (ECU_SW_Requirements#6367)
   */
  DILeadObjRt0 = rtb_Assignment1[SELERILEADOBJ0];

  /* Switch: '<S11>/Switch1' incorporates:
   *  Constant: '<S11>/ZERO'
   *  Inport: '<Root>/EngstsCAN'
   *  RelationalOperator: '<S11>/Relational Operator'
   *
   * Block requirements for '<S11>/Switch1':
   *  1. EISB_FCA6CYL_SW_REQ_1868: The software shall increase the counter CntAbsTrigIn each angular ... (ECU_SW_Requirements#7288)
   */
  if (((int32_T)EngstsCAN) > 0) {
    /* Switch: '<S11>/Switch2' incorporates:
     *  Constant: '<S11>/MAX_UWORD'
     *  Constant: '<S11>/ONE'
     *  RelationalOperator: '<S11>/Relational Operator1'
     *  Sum: '<S11>/Add'
     */
    if (CntAbsTrigIn < ((uint16_T)MAX_UWORD)) {
      rtb_CntAbsTrigIn_g = (uint16_T)(((uint32_T)CntAbsTrigIn) + 1U);
    } else {
      rtb_CntAbsTrigIn_g = CntAbsTrigIn;
    }

    /* End of Switch: '<S11>/Switch2' */
  } else {
    rtb_CntAbsTrigIn_g = 0U;
  }

  /* End of Switch: '<S11>/Switch1' */

  /* Chart: '<S43>/find_element_table' incorporates:
   *  Constant: '<S43>/BKTDCMSPARKEN'
   *  Constant: '<S43>/BKTDCMSPARKEN_dim'
   *  Constant: '<S43>/BKTWMSPARKEN'
   *  Constant: '<S43>/BKTWMSPARKEN_dim'
   *  Inport: '<Root>/TWater'
   *
   * Block requirements for '<S43>/BKTDCMSPARKEN':
   *  1. EISB_FCA6CYL_SW_REQ_1866: The software shall give the possibility to select an ignition mode... (ECU_SW_Requirements#7287)
   *
   * Block requirements for '<S43>/BKTWMSPARKEN':
   *  1. EISB_FCA6CYL_SW_REQ_1866: The software shall give the possibility to select an ignition mode... (ECU_SW_Requirements#7287)
   */
  /* Gateway: CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/EnableIgnitionModeMaps/find_element_table */
  /* During: CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/EnableIgnitionModeMaps/find_element_table */
  /* Find the indeces of the related breakpoints, where the correspondent values are greater than the associated inputs. */
  /* Entry Internal: CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/EnableIgnitionModeMaps/find_element_table */
  /* Transition: '<S46>:2' */
  idx_row = 1U;
  idx_col = 1U;
  while ((idx_row <= ((uint8_T)BKTDCMSPARKEN_dim)) && (rtb_CntAbsTrigIn_g >=
          BKTDCMSPARKEN[(idx_row)])) {
    /* Transition: '<S46>:4' */
    idx_row += ((uint8_T)ONE);

    /* Transition: '<S46>:31' */
  }

  /* Transition: '<S46>:10' */
  while ((idx_col <= ((uint8_T)BKTWMSPARKEN_dim)) && (TWater >= BKTWMSPARKEN
          [(idx_col)])) {
    /* Transition: '<S46>:14' */
    idx_col += ((uint8_T)ONE);

    /* Transition: '<S46>:20' */
  }

  /* End of Chart: '<S43>/find_element_table' */

  /* Logic: '<S43>/Logical Operator1' incorporates:
   *  Constant: '<S43>/Constant'
   *  Constant: '<S43>/Constant1'
   *  Constant: '<S43>/Constant2'
   *  Constant: '<S43>/Constant3'
   *  Constant: '<S43>/REC_PLASMA_OFF'
   *  Constant: '<S43>/TBENMSPARK'
   *  Inport: '<Root>/IonKnockEnabled'
   *  Inport: '<Root>/VtRec'
   *  Logic: '<S43>/Logical Operator2'
   *  RelationalOperator: '<S43>/Relational Operator1'
   *  RelationalOperator: '<S43>/Relational Operator2'
   *  Selector: '<S43>/Selector1'
   *  Selector: '<S43>/Selector2'
   *  Sum: '<S43>/Add'
   *  Sum: '<S43>/Add1'
   *
   * Block requirements for '<S43>/Logical Operator1':
   *  1. EISB_FCA6CYL_SW_REQ_1866: The software shall give the possibility to select an ignition mode... (ECU_SW_Requirements#7287)
   *
   * Block requirements for '<S43>/TBENMSPARK':
   *  1. EISB_FCA6CYL_SW_REQ_1866: The software shall give the possibility to select an ignition mode... (ECU_SW_Requirements#7287)
   */
  /* Transition: '<S46>:19' */
  EnIgnModeCnd = ((((!FlgNoMap) && (VtRec[(REC_PLASMA_OFF)] == ((uint8_T)ZERO)))
                   && (IonKnockEnabled == ((uint8_T)ZERO))) && (TBENMSPARK
    [(((int32_T)((uint8_T)(idx_col - ((uint8_T)ONE)))) * 5) + ((int32_T)
    ((uint8_T)(idx_row - ((uint8_T)ONE))))]));

  /* S-Function (PreLookUpIdSearch_U16): '<S66>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S15>/BKTDCILEADOBJ'
   *  Constant: '<S15>/BKTDCILEADOBJ_dim'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o2_o, &rtb_Look2D_IR_U16_c,
                        rtb_CntAbsTrigIn_g, &BKTDCILEADOBJ[0], ((uint8_T)
    BKTDCILEADOBJ_dim));

  /* S-Function (Look2D_IR_S16): '<S63>/Look2D_IR_S16' incorporates:
   *  Constant: '<S15>/BKTDCILEADOBJ_dim1'
   *  Constant: '<S15>/BKTWILEADOBJ_dim1'
   *  Constant: '<S15>/TBDLOADPLAEN'
   *
   * Block requirements for '<S15>/TBDLOADPLAEN':
   *  1. EISB_FCA6CYL_SW_REQ_1871: The software shall calculate the signal LoadDPlaEn, as output of t... (ECU_SW_Requirements#7292)
   */
  Look2D_IR_S16( &rtb_Look2D_IR_S16, &TBDLOADPLAEN[0],
                rtb_PreLookUpIdSearch_S16_o1_l, rtb_PreLookUpIdSearch_S16_o2_c,
                ((uint8_T)BKTWILEADOBJ_dim), rtb_PreLookUpIdSearch_U16_o2_o,
                rtb_Look2D_IR_U16_c, ((uint8_T)BKTDCILEADOBJ_dim));

  /* Sum: '<S15>/Add2' incorporates:
   *  Inport: '<Root>/Load'
   */
  rtb_DataTypeConversion2 = (((int32_T)rtb_Look2D_IR_S16) * 128) + ((int32_T)
    Load);

  /* MinMax: '<S15>/MinMax'
   *
   * Block requirements for '<S15>/MinMax':
   *  1. EISB_FCA6CYL_SW_REQ_1871: The software shall calculate the signal LoadDPlaEn, as output of t... (ECU_SW_Requirements#7292)
   */
  if (rtb_DataTypeConversion2 > 0) {
    /* DataTypeConversion: '<S15>/Data Type Conversion1' */
    LoadDPlaEn = (uint16_T)rtb_DataTypeConversion2;
  } else {
    /* DataTypeConversion: '<S15>/Data Type Conversion1' incorporates:
     *  Constant: '<S15>/ZERO'
     */
    LoadDPlaEn = 0U;
  }

  /* End of MinMax: '<S15>/MinMax' */

  /* If: '<S38>/If' incorporates:
   *  Constant: '<S44>/ION_ST_SEL'
   *  Constant: '<S44>/ION_ST_SEL1'
   *  Constant: '<S44>/ION_ST_SEL2'
   */
  if (EnIgnModeCnd) {
    /* Outputs for IfAction SubSystem: '<S38>/IgnitionModeMaps' incorporates:
     *  ActionPort: '<S45>/Action Port'
     *
     * Block description for '<S38>/IgnitionModeMaps':
     *  Calculation of the ignition mode by maps.
     *  Therefore, the ignition mode (StPlasObj) can be defined from a reference table having only two possible values (PLAS_ST_SEL and ION_ST_SEL), that is TBSTPLASOBJB, function of Rpm and LoadDPlaEn, if the current cylinder is odd and tunable parameter ENTBSTPLASOBJB is set, otherwise TBSTPLASOBJ, function of Rpm and LoadDPlaEn.
     *  Moreover, to avoid frequent jumps among different ignition modes, the change of this shall be done using hysteresis on the table axes: HYSRPMSTPLASOBJ for Rpm and HYSLOADSTPLASOBJ for LoadDPlaEn. Therefore, at each step the software shall calculate two values from the reference table: the first considering the current Rpm and LoadDPlaEn, the latter the current Rpm and LoadDPlaEn plus related hysteresis. Then StPlasObj shall change only if both values will be the same.
     *
     * Block requirements for '<S38>/IgnitionModeMaps':
     *  1. EISB_FCA6CYL_SW_REQ_1870: When the signal EnIgnModeCnd is true, the ignition mode (StPlasObj... (ECU_SW_Requirements#7291)
     */
    /* Sum: '<S45>/Add' incorporates:
     *  Constant: '<S45>/HYSRPMSTPLASOBJ'
     *  Inport: '<Root>/Rpm'
     */
    rtb_DataTypeConversion1_j = (uint16_T)(((uint32_T)Rpm) + ((uint32_T)
      HYSRPMSTPLASOBJ));

    /* Sum: '<S45>/Add1' incorporates:
     *  Constant: '<S45>/HYSLOADSTPLASOBJ'
     */
    rtb_DataTypeConversion2 = (int32_T)((uint32_T)(((uint32_T)LoadDPlaEn) +
      ((uint32_T)HYSLOADSTPLASOBJ)));

    /* MinMax: '<S45>/MinMax2' incorporates:
     *  Constant: '<S45>/MAXLOADDPLAEN'
     */
    if (MAXLOADDPLAEN < rtb_DataTypeConversion2) {
      /* DataTypeConversion: '<S45>/Data Type Conversion1' */
      rtb_ILeadObjRt = (uint16_T)MAXLOADDPLAEN;
    } else {
      /* DataTypeConversion: '<S45>/Data Type Conversion1' */
      rtb_ILeadObjRt = (uint16_T)rtb_DataTypeConversion2;
    }

    /* End of MinMax: '<S45>/MinMax2' */

    /* Chart: '<S45>/find_element_table' incorporates:
     *  Constant: '<S45>/BKLOADSTPLASOBJ'
     *  Constant: '<S45>/BKLOADSTPLASOBJ_dim'
     *  Constant: '<S45>/BKRPMSTPLASOBJ'
     *  Constant: '<S45>/BKRPMSTPLASOBJ_dim'
     *  Inport: '<Root>/Rpm'
     */
    /* Gateway: CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/IgnitionModeMaps/find_element_table */
    /* During: CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/IgnitionModeMaps/find_element_table */
    /* Find the indeces of the related breakpoints, where the correspondent values are greater than the associated inputs. */
    /* Entry Internal: CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/IgnitionModeMaps/find_element_table */
    /* Transition: '<S47>:2' */
    idx_row = ((uint8_T)ONE);
    idx_col = ((uint8_T)ONE);
    offset_row = 0U;
    offset_col = 0U;
    while ((idx_row <= ((uint8_T)BKRPMSTPLASOBJ_dim)) && (Rpm >= BKRPMSTPLASOBJ
            [(idx_row)])) {
      /* Transition: '<S47>:4' */
      idx_row += ((uint8_T)ONE);

      /* Transition: '<S47>:31' */
    }

    /* Transition: '<S47>:10' */
    tmp_index = idx_row;
    while ((tmp_index <= ((uint8_T)BKRPMSTPLASOBJ_dim)) &&
           (rtb_DataTypeConversion1_j >= BKRPMSTPLASOBJ[(tmp_index)])) {
      /* Transition: '<S47>:36' */
      offset_row += ((uint8_T)ONE);
      tmp_index = (uint8_T)(idx_row + offset_row);

      /* Transition: '<S47>:37' */
    }

    /* Transition: '<S47>:38' */
    idx_row -= ((uint8_T)ONE);
    while ((idx_col <= ((uint8_T)BKLOADSTPLASOBJ_dim)) && (LoadDPlaEn >=
            BKLOADSTPLASOBJ[(idx_col)])) {
      /* Transition: '<S47>:14' */
      idx_col += ((uint8_T)ONE);

      /* Transition: '<S47>:20' */
    }

    /* Transition: '<S47>:19' */
    tmp_index = idx_col;
    while ((tmp_index <= ((uint8_T)BKLOADSTPLASOBJ_dim)) && (rtb_ILeadObjRt >=
            BKLOADSTPLASOBJ[(tmp_index)])) {
      /* Transition: '<S47>:40' */
      offset_col += ((uint8_T)ONE);
      tmp_index = (uint8_T)(idx_col + offset_col);

      /* Transition: '<S47>:41' */
    }

    /* Transition: '<S47>:43' */
    idx_col -= ((uint8_T)ONE);

    /* Selector: '<S45>/Selector2' incorporates:
     *  Constant: '<S45>/TBSTPLASOBJ'
     *  Constant: '<S45>/TBSTPLASOBJB'
     *  Selector: '<S45>/Selector4'
     */
    rtb_DataTypeConversion2 = (7 * ((int32_T)idx_col)) + ((int32_T)idx_row);

    /* Selector: '<S45>/Selector1' incorporates:
     *  Chart: '<S45>/find_element_table'
     *  Constant: '<S45>/TBSTPLASOBJ'
     *  Constant: '<S45>/TBSTPLASOBJB'
     *  Selector: '<S45>/Selector3'
     */
    rtb_dwell_time_min_gain = (7 * ((int32_T)((uint8_T)(idx_col + offset_col))))
      + ((int32_T)((uint8_T)(idx_row + offset_row)));

    /* Switch: '<S45>/Switch2' incorporates:
     *  Constant: '<S45>/TBSTPLASOBJB'
     *  RelationalOperator: '<S45>/Relational Operator1'
     *  Selector: '<S45>/Selector1'
     *  Selector: '<S45>/Selector2'
     */
    if (TBSTPLASOBJB[(rtb_DataTypeConversion2)] == TBSTPLASOBJB
        [(rtb_dwell_time_min_gain)]) {
      StPlasObjB_mem = TBSTPLASOBJB[(rtb_DataTypeConversion2)];
    }

    /* End of Switch: '<S45>/Switch2' */

    /* Switch: '<S45>/Switch1' incorporates:
     *  Constant: '<S45>/TBSTPLASOBJ'
     *  RelationalOperator: '<S45>/Relational Operator2'
     *  Selector: '<S45>/Selector3'
     *  Selector: '<S45>/Selector4'
     */
    if (TBSTPLASOBJ[(rtb_DataTypeConversion2)] == TBSTPLASOBJ
        [(rtb_dwell_time_min_gain)]) {
      StPlasObj_mem = TBSTPLASOBJ[(rtb_DataTypeConversion2)];
    }

    /* End of Switch: '<S45>/Switch1' */

    /* Switch: '<S45>/Switch' incorporates:
     *  Constant: '<S45>/BIT0'
     *  Constant: '<S45>/ENTBSTPLASOBJB'
     *  Logic: '<S45>/Logical Operator2'
     *  RelationalOperator: '<S45>/Relational Operator3'
     *  S-Function (sfix_bitop): '<S45>/Bitwise Operator'
     */
    if (((rtb_CylPlaAbs_idx & ((uint8_T)BIT0)) >= ((uint8_T)BIT0)) &&
        (ENTBSTPLASOBJB)) {
      StPlasObj = StPlasObjB_mem;
    } else {
      StPlasObj = StPlasObj_mem;
    }

    /* End of Switch: '<S45>/Switch' */
    /* End of Outputs for SubSystem: '<S38>/IgnitionModeMaps' */
  } else {
    /* Outputs for IfAction SubSystem: '<S38>/IgnitionModeDefault' incorporates:
     *  ActionPort: '<S44>/Action Port'
     *
     * Block description for '<S38>/IgnitionModeDefault':
     *  The ignition mode (StPlasObj) is set at single spark with ION
     *  (ION_ST_SEL), in case condition to enable mode by maps is disabled.
     *
     * Block requirements for '<S38>/IgnitionModeDefault':
     *  1. EISB_FCA6CYL_SW_REQ_1869: When the signal EnIgnModeCnd is false, the ignition mode (StPlasOb... (ECU_SW_Requirements#7290)
     */
    StPlasObj = ION_ST_SEL;
    StPlasObjB_mem = ION_ST_SEL;
    StPlasObj_mem = ION_ST_SEL;

    /* End of Outputs for SubSystem: '<S38>/IgnitionModeDefault' */
  }

  /* End of If: '<S38>/If' */

  /* Outputs for Atomic SubSystem: '<S51>/PolyAssert' */
  /* CCaller: '<S54>/C Caller' incorporates:
   *  Inport: '<S54>/In1'
   *  RelationalOperator: '<S54>/Relational Operator1'
   */
#ifdef POLYSPACE_ENV
  AssertForPoly(rtb_MinMax <= 411041792U);
#endif

  /* Assignment: '<S51>/Assignment' incorporates:
   *  Inport: '<S54>/In1'
   */
  VtILeadObjMem[(rtb_CylPlaAbs_idx)] = rtb_MinMax;

  /* End of Outputs for SubSystem: '<S51>/PolyAssert' */

  /* Switch: '<S49>/Switch' incorporates:
   *  Constant: '<S49>/SELILODTB'
   *  DataTypeConversion: '<S49>/Data Type Conversion3'
   *
   * Block requirements for '<S49>/SELILODTB':
   *  1. EISB_FCA6CYL_SW_REQ_250: The basic dwell time calculation(DwellTimeBase) in open loop shall... (ECU_SW_Requirements#301)
   */
  if (SELILODTB) {
    rtb_Look2D_IR_U16_c = (uint16_T)((((uint32_T)rtb_DataTypeConversion3_l) *
      25U) >> ((uint32_T)4));
  } else {
    rtb_Look2D_IR_U16_c = rtb_DataTypeConversion1_c;
  }

  /* End of Switch: '<S49>/Switch' */

  /* S-Function (PreLookUpIdSearch_U16): '<S58>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S49>/BKILEADOBJDWT'
   *  Constant: '<S49>/BKILEADOBJDWT_dim'
   */
  PreLookUpIdSearch_U16( &rtb_Look2D_IR_U16_c, &rtb_PreLookUpIdSearch_U16_o2_o,
                        rtb_Look2D_IR_U16_c, &BKILEADOBJDWT[0], ((uint8_T)
    BKILEADOBJDWT_dim));

  /* S-Function (Look2D_IR_U16): '<S57>/Look2D_IR_U16' incorporates:
   *  Constant: '<S49>/BKILEADOBJDWT_dim'
   *  Constant: '<S49>/BKLOADILEADOBJ_dim'
   *  Constant: '<S49>/TBDWELLTIME'
   *
   * Block requirements for '<S49>/TBDWELLTIME':
   *  1. EISB_FCA6CYL_SW_REQ_250: The basic dwell time calculation(DwellTimeBase) in open loop shall... (ECU_SW_Requirements#301)
   */
  Look2D_IR_U16( &rtb_Look2D_IR_U16_c, &TBDWELLTIME[0], rtb_Look2D_IR_U16_c,
                rtb_PreLookUpIdSearch_U16_o2_o, ((uint8_T)BKILEADOBJDWT_dim),
                rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                ((uint8_T)BKLOADILEADOBJ_dim));

  /* Assignment: '<S16>/Assignment6' */
  VtMaxErrIntSat[(rtb_CylPlaAbs_idx)] = rtb_Look2D_IR_U16;

  /* SignalConversion generated from: '<S3>/CntAbsTrigIn' */
  CntAbsTrigIn = rtb_CntAbsTrigIn_g;

  /* SignalConversion generated from: '<S3>/DwellTimeBase' */
  DwellTimeBase = rtb_Look2D_IR_U16_c;

  /* SignalConversion generated from: '<S3>/ILeadBase' */
  ILeadBase = rtb_Switch_l;

  /* SignalConversion generated from: '<S3>/ILeadObj' */
  ILeadObj = rtb_DataTypeConversion3_l;

  /* SignalConversion generated from: '<S3>/ILeadObj0' */
  ILeadObj0 = rtb_DataTypeConversion1_c;
  for (rtb_MAXDWELLOBJDEF = 0; rtb_MAXDWELLOBJDEF < 8; rtb_MAXDWELLOBJDEF++) {
    /* SignalConversion generated from: '<S3>/VtDILeadObjRt' */
    VtDILeadObjRt[(rtb_MAXDWELLOBJDEF)] = rtb_Assignment1[rtb_MAXDWELLOBJDEF];

    /* SignalConversion generated from: '<S3>/VtErrILeadObj' */
    VtErrILeadObj[(rtb_MAXDWELLOBJDEF)] = rtb_Assignment2[rtb_MAXDWELLOBJDEF];
  }

  /* Assignment: '<S10>/Assignment4'
   *
   * Block requirements for '<S10>/Assignment4':
   *  1. EISB_FCA6CYL_SW_REQ_1782: The software shall enable the closed loop control (VtSelPlaCtrlOL ... (ECU_SW_Requirements#6340)
   */
  VtSelPlaCtrlOL[(rtb_CylPlaAbs_idx)] = rtb_Switch_o0;

  /* Assignment: '<S71>/Assignment1' */
  TmpCntIGNInOff[(rtb_CylPlaAbs_idx)] = rtb_Switch2;

  /* Assignment: '<S71>/Assignment2' incorporates:
   *  Constant: '<S71>/ZERO'
   *
   * Block requirements for '<S71>/ZERO':
   *  1. EISB_FCA6CYL_SW_REQ_1784: The software shall increase on 10ms event the timer VtTimCmdStall ... (ECU_SW_Requirements#6343)
   */
  VtTimCmdStall[(rtb_CylPlaAbs_idx)] = 0U;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CoilTarget_Tdc' */
}

/* Model initialize function */
void CoilTarget_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/CoilTarget_Init' incorporates:
   *  SubSystem: '<Root>/CoilTarget_Init_mgm'
   *
   * Block description for '<Root>/CoilTarget_Init_mgm':
   *  Variables initialization.
   */
  /* Start for Constant: '<S1>/SELPIFFWCTRL' */
  SelPIFFWCtrl = SELPIFFWCTRL;

  /* Start for Constant: '<S1>/ID_VER_COILTARGET_DEF' */
  IdVer_CoilTarget = ID_VER_COILTARGET_DEF;

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/CoilTarget_Init' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint16_T DwellTimeBase;
int16_T DwellTimeObj[8];
uint16_T ILeadObj;
uint16_T LoadDPlaEn;
boolean_T SelPIFFWCtrl;
uint8_T StPlasObj;
boolean_T VtSelPlaCtrlOL[8];
uint32_T VtTimCmdStall[8];
void CoilTarget_Init(void);
void CoilTarget_T10ms(void);
void CoilTarget_Tdc(void);
void CoilTarget_Init(void)
{
  uint8_T idx;
  DwellTimeBase= 0U;
  ILeadObj= 0;
  LoadDPlaEn= 0;
  SelPIFFWCtrl= 1;
  StPlasObj= 1;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    DwellTimeObj[idx]= 0;
    VtSelPlaCtrlOL[idx]= 1U;
    VtTimCmdStall[idx]= 0U
  } }

#endif                                 /*_BUILD_COILTARGET_*/

  /*======================== TOOL VERSION INFORMATION ==========================*
   * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
   * Simulink 10.0 (R2019b)18-Jul-2019                                          *
   * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
   * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
   * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
   * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
   *============================================================================*/

  /*======================= LICENSE IN USE INFORMATION =========================*
   * fixed_point_toolbox                                                        *
   * matlab                                                                     *
   * matlab_coder                                                               *
   * matlab_report_gen                                                          *
   * real-time_workshop                                                         *
   * rtw_embedded_coder                                                         *
   * simulink                                                                   *
   * simulink_report_gen                                                        *
   * simulink_requirements                                                      *
   * sl_verification_validation                                                 *
   * stateflow                                                                  *
   * text_analytics_toolbox                                                     *
   *============================================================================*/