/*
 * File: MKnockDet_eep.c
 *
 * Code generated for Simulink model 'MKnockDet'.
 *
 * Model version                  : 1.997
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Thu Apr 22 16:36:54 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor->Custom Processor
 * Emulation hardware selection:
 *    Differs from embedded hardware (Intel->x86-64 (Windows64))
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. ROM efficiency
 *    5. RAM efficiency
 *    6. Traceability
 *    7. Debugging
 *    8. Polyspace
 * Validation result: Passed (30), Warnings (2), Error (0)
 */

#include "rtwtypes.h"

/* Exported data definition */

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_EE_INTERFACE */
uint16_T VtMKDwellIntEE[5] = { ((uint16_T)0U), ((uint16_T)0U), ((uint16_T)0U),
  ((uint16_T)0U), ((uint16_T)0U) };    /* '<S1>/MegaKnock_Mgm' */

/* VtMKDwellIntEE */
uint16_T VtMKIntIonEE[5] = { ((uint16_T)0U), ((uint16_T)0U), ((uint16_T)0U),
  ((uint16_T)0U), ((uint16_T)0U) };    /* '<S1>/MegaKnock_Mgm' */

/* VtMKIntIonEE */
uint8_T VtMKIonAbsTdcEE[5] = { ((uint8_T)0U), ((uint8_T)0U), ((uint8_T)0U),
  ((uint8_T)0U), ((uint8_T)0U) };      /* '<S1>/MegaKnock_Mgm' */

/* VtMKIonAbsTdcEE */
uint32_T VtMKKnockIntEE[5] = { 0U, 0U, 0U, 0U, 0U };/* '<S1>/MegaKnock_Mgm' */

/* VtMKKnockIntEE */
uint16_T VtMKLoadEE[5] = { 0U, 0U, 0U, 0U, 0U };/* '<S1>/MegaKnock_Mgm' */

/* VtMKLoadEE */
uint16_T VtMKRpmEE[5] = { ((uint16_T)0U), ((uint16_T)0U), ((uint16_T)0U),
  ((uint16_T)0U), ((uint16_T)0U) };    /* '<S1>/MegaKnock_Mgm' */

/* VtMKRpmEE */
uint32_T VtMKTotOdometerCANEE[5] = { 0U, 0U, 0U, 0U, 0U };/* '<S1>/MegaKnock_Mgm' */

/* VtMKTotOdometerCANEE */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */