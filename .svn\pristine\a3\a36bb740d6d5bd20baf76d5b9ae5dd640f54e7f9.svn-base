/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/
/**
 * @file    gtm.h
 * @brief   SPC5xx GTM header file.
 *
 * @addtogroup GTM
 * @
 */
#ifndef _GTM_H_
#define _GTM_H_


#pragma ghs startnomisra



//#include <platform.h>  MC
#include "gtm_common.h"
#include "gtm_cfg.h"
#include "gtm_tbu_cfg.h"
#include "gtm_icm_cfg.h"
#include "gtm_aru_cfg.h"
#include "gtm_tom_cfg.h"
#include "gtm_mcs_cfg.h"
#include "gtm_psm_cfg.h"
#include "gtm_tim_cfg.h"
#include "gtm_map_cfg.h"
#include "gtm_atom_cfg.h"
#include "gtm_dpll_cfg.h"
#include "gtm_dtm_cfg.h"
#include "gtm_brc_cfg.h"

/**
 * @brief Type of a structure representing an GTM driver.
 */
typedef struct GTMDriver GTMDriver;

#if (SPC5_HAS_GTM_IP_101 == TRUE) || defined (__DOXYGEN__)
#include "SPC572L_GTM.h"
#include "SPC572L_GTM_IRQ.h"
#include "SPC572L_GTM_IP.h"
#endif

#if (SPC5_HAS_GTM_IP_122 == TRUE) || defined (__DOXYGEN__)
#include "SPC574K_GTM.h"
#include "SPC574K_GTM_IRQ.h"
#include "SPC574K_GTM_IP.h"
#endif

#if (SPC5_HAS_GTM_IP_343 == TRUE) || defined (__DOXYGEN__)
#include "SPC58NE_GTM.h"
#include "SPC58NE_GTM_IRQ.h"
#include "SPC58NE_GTM_IP.h"
#endif

#if (SPC5_HAS_GTM_IP_344 == TRUE) || defined (__DOXYGEN__)
#include "SPC58NN_GTM.h"
#include "SPC58NN_GTM_IRQ.h"
#include "SPC58NN_GTM_IP.h"
#endif

#include "gtm_cmu.h"
#include "gtm_tbu.h"
#include "gtm_icm.h"
#include "gtm_aru.h"
#include "gtm_tom.h"
#include "gtm_mcs.h"
#include "gtm_psm.h"
#include "gtm_tim.h"
#include "gtm_map.h"
#include "gtm_atom.h"
#include "gtm_dpll.h"
#include "gtm_dtm.h"
#include "gtm_brc.h"

extern uint32_T mcs2_base ;
extern vuint32_t *cpu_debug_pmos_0_var_addr;
extern uint32_T cpu_debug_pmos_0_var;
extern vuint32_t *cpu_debug_pmos_1_var_addr;
extern uint32_T cpu_debug_pmos_1_var;
/**
 * @name    Generic Macro
 * @{
 */

#define SPC5_GTM_IP_TIM_AUXIN_TOM  0x0UL
#define SPC5_GTM_IP_TIM_AUXIN_ATOM 0x1UL

#define SPC5_GTM_IP_SW_RFPROT_EN      0x0UL
#define SPC5_GTM_IP_SW_RFPROT_DIS     0x1UL


/** Get GTM sub-modules driver data private value */
#define gtm_GetPrivate(ptr) ({ \
  const typeof( ptr ) __ptr = (ptr); \
   __ptr -> priv; \
})

/** Set GTM sub-modules driver data private value */
#define gtm_SetPrivate(ptr, priv_data) ({ \
  const typeof( ptr ) __ptr = (ptr); \
   __ptr -> priv = ((typeof(__ptr -> priv)) (priv_data)); \
})

/** @} */

/**
 * @brief   Structure representing a GTM INT Priorities
 */
typedef struct {
	/**
     * @brief gtm interrupt priority for ARU module.
     */
	uint32_t aru;
	/**
     * @brief gtm interrupt priority for ATOM module.
     */
	uint32_t atom;
	/**
     * @brief gtm interrupt priority for DPLL module.
     */
	uint32_t dpll;
	/**
     * @brief gtm interrupt priority for TOM module.
     */
	uint32_t tom;
	/**
     * @brief gtm interrupt priority for TIM module.
     */
	uint32_t tim;
	/**
     * @brief gtm interrupt priority for PSM module.
     */
	uint32_t psm;
	/**
     * @brief gtm interrupt priority for BRC module.
     */
	uint32_t brc;
	/**
     * @brief gtm interrupt priority for MCS module.
     */
	uint32_t mcs;
}GTM_INT_PR;

/**
 * @brief Structure representing an GTM driver.
 */
struct GTMDriver {
	/**
	 * @brief Pointer to the (GTM) registers block.
	 */
	volatile GTM_TAG *gtm;
	/**
     * @brief gtm-ip version.
     */
	uint32_t ip;

	/**
     * @brief AUXIN TIM channel signal.
     */
	uint32_t gtm_auxin[SPC5_GTM_TIM_NUM];
	/**
     * @brief Interrupt priority for gtm module.
     */
	const GTM_INT_PR *gtm_int_pr;
};

/* Required by MISRA Check */
extern GTMDriver GTMD;

/**
 * @brief Generic Macro
 */
#define GTM_ERROR      0x1U


#ifdef __cplusplus
extern "C" {
#endif
void gtmInit(void);
extern void gtm_SetAuxIN(GTMDriver *gtmd, uint32_t tim, uint32_t channel, uint32_t value);
extern void gtm_SetRFPROT(GTMDriver *gtmd, uint32_t rf_prot);
extern uint32_t gtm_GetRFPROT(GTMDriver *gtmd);
#ifdef __cplusplus
}
#endif

#pragma ghs endnomisra  

 #endif /* _GTM_H_ */
/** @ */

