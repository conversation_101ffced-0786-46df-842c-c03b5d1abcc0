target_connection {
     {
        title = "Motorola MPC5554 Demo Board with Simulator for PowerPC (simppc)"
        type = "Simulator for PowerPC (simppc)"
        short_type = "simppc"
        args = " -ppc5554   "
        command = "simppc -ppc5554   "
        logfile = "simppc"
        mode = ""
        setup_script = ""
        sane = "no"
        log = "no"
        timestamp = "0"
    }
     {
        title = "Motorola MPC5554 Demo Board with Green Hills Debug Probe (mpserv)"
        type = "Green Hills Debug Probe (mpserv)"
        short_type = "mpserv"
        args = "  -usb"
        command = "mpserv  -usb"
        logfile = ""
        mode = "download"
        setup_script = "D:\\tests\\GHS\\2007_03_12\\GHS\\mpserv_standard.mbs"
        sane = "yes"
        log = "no"
        timestamp = "0"
    }
}
