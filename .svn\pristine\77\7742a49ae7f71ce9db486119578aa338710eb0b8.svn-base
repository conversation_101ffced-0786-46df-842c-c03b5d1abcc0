/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      CoilAngPattern.h
 **  Date:          15-Sep-2021
 **
 **  Model Version: 1.1101
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_CoilAngPattern_h_
#define RTW_HEADER_CoilAngPattern_h_
#ifndef CoilAngPattern_COMMON_INCLUDES_
# define CoilAngPattern_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* CoilAngPattern_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: ELD_EXPORT_DEFINE */
#define ION_ST_SEL                     2U                        /* Referenced by: '<S36>/ION_ST_SEL' */
#define PLAS_ION_ST_SEL                3U                        /* Referenced by: '<S36>/PLAS_ION_ST_SEL' */
#define PLAS_ST_SEL                    1U                        /* Referenced by: '<S36>/PLAS_ST_SEL' */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void CoilAngPattern_initialize(void);

/* Exported entry point function */
extern void CoilAngPattern_PowerOn(void);

/* Exported entry point function */
extern void CoilAngPattern_SparkOff(void);

/* Exported data declaration */

/*Calibration memory section */
/*Start of local calbration section*/
#pragma ghs section rodata=".calib"

/* Declaration for custom storage class: ELD_EXPORT_CALIBRATION */
extern CALQUAL CALQUAL_POST int16_T PLACTRLTIMSAT;/* Referenced by:
                                                   * '<S7>/PLACTRLTIMSAT'
                                                   * '<S9>/PLACTRLTIMSAT'
                                                   * '<S120>/PLACTRLTIMSAT'
                                                   * '<S121>/PLACTRLTIMSAT'
                                                   */

/* Global saturation */
#pragma ghs section rodata=default

/*End of calibration section*/


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T TbFallBkDutyOut[10];   /* '<S3>/Merge9' */

/* Buck duty */
extern uint16_T TbRiseBkDutyOut[32];   /* '<S3>/Merge5' */

/* Buck duty */
extern uint32_T TrigInTimeFilt;        /* '<S3>/Merge18' */

/* Trigger filtering time after spark */
extern uint32_T TrigInTimeFiltTout;    /* '<S3>/Merge19' */

/* Trigger filtering time after SSD */
extern uint16_T VtDelayBkDisc[8];      /* '<S3>/Merge7' */

/* Delay Buck phase discharge */
extern uint16_T VtDelayBkEn[8];        /* '<S3>/Merge1' */

/* Delay Start Buck Enable */
extern uint16_T VtDelayBkF1[8];        /* '<S3>/Merge2' */

/* Delay Buck phase 1 */
extern uint16_T VtDelayBkF2[8];        /* '<S3>/Merge3' */

/* Delay Buck phase 2 */
extern uint16_T VtDelayBkF3[8];        /* '<S3>/Merge4' */

/* Delay Buck phase 3 */
extern uint16_T VtDeltaBkEn[8];        /* '<S3>/Merge10' */

/* Delta Start Buck Enable */
extern uint16_T VtDwellTime[8];        /* '<S3>/Merge' */

/* Dwell time */
extern uint16_T VtNPlaPulseOL[8];      /* '<S3>/Merge16' */

/* Number pulse phase OL */
extern uint8_T VtStPlasObj[8];         /* '<S3>/Merge12' */

/* Select strategy cyl */
extern uint16_T VtTMKWindow[8];        /* '<S3>/Merge6' */

/* Time of Megaknock window */
extern uint16_T VtTOffPlaPulseOL[8];   /* '<S3>/Merge14' */

/* Ton pulse phase OL */
extern uint16_T VtTOnLastPlaPulseOL[8];/* '<S3>/Merge17' */

/* Ton last pulse phase OL */
extern uint16_T VtTOnPlaPulseOL[8];    /* '<S3>/Merge13' */

/* Ton pulse phase OL */
extern uint16_T VtTSlopeBk[8];         /* '<S3>/Merge8' */

/* Time Slope for phase */
extern uint16_T VtThrPalPulseOn[8];    /* '<S3>/Merge15' */

/* Threshold pulse phase OL */
extern uint16_T VtToPulseIn[8];        /* '<S3>/Merge11' */

/* Timeout input trigger */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S42>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate' : Unused code path elimination
 * Block '<S44>/Data Type Duplicate' : Unused code path elimination
 * Block '<S45>/Data Type Duplicate' : Unused code path elimination
 * Block '<S53>/Data Type Duplicate' : Unused code path elimination
 * Block '<S54>/Data Type Duplicate' : Unused code path elimination
 * Block '<S62>/Data Type Duplicate' : Unused code path elimination
 * Block '<S64>/Data Type Duplicate' : Unused code path elimination
 * Block '<S67>/Data Type Duplicate' : Unused code path elimination
 * Block '<S69>/Data Type Duplicate' : Unused code path elimination
 * Block '<S72>/Data Type Duplicate' : Unused code path elimination
 * Block '<S74>/Data Type Duplicate' : Unused code path elimination
 * Block '<S83>/Data Type Duplicate' : Unused code path elimination
 * Block '<S85>/Data Type Duplicate' : Unused code path elimination
 * Block '<S88>/Data Type Duplicate' : Unused code path elimination
 * Block '<S90>/Data Type Duplicate' : Unused code path elimination
 * Block '<S93>/Data Type Duplicate' : Unused code path elimination
 * Block '<S95>/Data Type Duplicate' : Unused code path elimination
 * Block '<S98>/Data Type Duplicate' : Unused code path elimination
 * Block '<S100>/Data Type Duplicate' : Unused code path elimination
 * Block '<S107>/Data Type Duplicate' : Unused code path elimination
 * Block '<S108>/Data Type Duplicate' : Unused code path elimination
 * Block '<S111>/Data Type Duplicate' : Unused code path elimination
 * Block '<S112>/Data Type Duplicate' : Unused code path elimination
 * Block '<S113>/Data Type Duplicate' : Unused code path elimination
 * Block '<S114>/Data Type Duplicate' : Unused code path elimination
 * Block '<S115>/Data Type Duplicate' : Unused code path elimination
 * Block '<S116>/Data Type Duplicate' : Unused code path elimination
 * Block '<S117>/Data Type Duplicate' : Unused code path elimination
 * Block '<S118>/Data Type Duplicate' : Unused code path elimination
 * Block '<S128>/Data Type Duplicate' : Unused code path elimination
 * Block '<S15>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S37>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S39>/Reshape' : Reshape block reduction
 * Block '<S40>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S40>/Reshape' : Reshape block reduction
 * Block '<S41>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S41>/Reshape' : Reshape block reduction
 * Block '<S38>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S45>/Conversion' : Eliminate redundant data type conversion
 * Block '<S38>/Reshape' : Reshape block reduction
 * Block '<S51>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S51>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S53>/Conversion' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S62>/Conversion' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S65>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S65>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S65>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S65>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S67>/Conversion' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S72>/Conversion' : Eliminate redundant data type conversion
 * Block '<S73>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S73>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S73>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S81>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S83>/Conversion' : Eliminate redundant data type conversion
 * Block '<S84>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S84>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S84>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S86>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S88>/Conversion' : Eliminate redundant data type conversion
 * Block '<S89>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S89>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S89>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S91>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S91>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S91>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S91>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S93>/Conversion' : Eliminate redundant data type conversion
 * Block '<S94>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S94>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S94>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S96>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S96>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S96>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S96>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S98>/Conversion' : Eliminate redundant data type conversion
 * Block '<S99>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S99>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S99>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S105>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S105>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S105>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S106>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S106>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S106>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S106>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S108>/Conversion' : Eliminate redundant data type conversion
 * Block '<S109>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S109>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S109>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S110>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S110>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S110>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S110>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S112>/Conversion' : Eliminate redundant data type conversion
 * Block '<S113>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S113>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S114>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S114>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S114>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S114>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S115>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S115>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S115>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S115>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S116>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S116>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S116>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S116>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S117>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S117>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S117>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S117>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S118>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S118>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S118>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S118>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S124>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S127>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S127>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S127>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S127>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S128>/Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CoilAngPattern'
 * '<S1>'   : 'CoilAngPattern/CoilAngPattern_Scheduler'
 * '<S2>'   : 'CoilAngPattern/Init_fcn'
 * '<S3>'   : 'CoilAngPattern/Merge'
 * '<S4>'   : 'CoilAngPattern/SparkOff_fcn'
 * '<S5>'   : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init'
 * '<S6>'   : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases'
 * '<S7>'   : 'CoilAngPattern/Init_fcn/Init_Calc_Times'
 * '<S8>'   : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Assign_Params'
 * '<S9>'   : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Saturate_Params'
 * '<S10>'  : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Assign_Params/DwellTime0_Clc'
 * '<S11>'  : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Assign_Params/For_Iterator_Subsystem'
 * '<S12>'  : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Assign_Params/DwellTime0_Clc/DwellTime0_Calc'
 * '<S13>'  : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Assign_Params/For_Iterator_Subsystem/Saturation'
 * '<S14>'  : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Saturate_Params/Sat_DelayBk'
 * '<S15>'  : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Saturate_Params/Sat_DwellTime'
 * '<S16>'  : 'CoilAngPattern/Init_fcn/Calc_Sat_Param_Init/Saturate_Params/Sat_PlaPulseOL'
 * '<S17>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK'
 * '<S18>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK/Init_TbRiseBkDutyOut_1'
 * '<S19>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK/Init_TbRiseBkDutyOut_2'
 * '<S20>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK/Init_TbRiseBkDutyOut_3'
 * '<S21>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK/Init_TbRiseBkDutyOut_4'
 * '<S22>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK/Init_TbRiseBkDutyOut_1/Assign_F1'
 * '<S23>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK/Init_TbRiseBkDutyOut_2/Assign_F2'
 * '<S24>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK/Init_TbRiseBkDutyOut_3/Assign_F3'
 * '<S25>'  : 'CoilAngPattern/Init_fcn/Init_Calc_DutyBk_Phases/TBRISEDUTYBK/Init_TbRiseBkDutyOut_4/Assign_F4'
 * '<S26>'  : 'CoilAngPattern/SparkOff_fcn/CylPlaAbsOff_idx_Sel'
 * '<S27>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc'
 * '<S28>'  : 'CoilAngPattern/SparkOff_fcn/VBattery_Set'
 * '<S29>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters'
 * '<S30>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Ratio'
 * '<S31>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param'
 * '<S32>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy'
 * '<S33>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param'
 * '<S34>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time'
 * '<S35>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL'
 * '<S36>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_VtStPlasObj'
 * '<S37>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Calc_TPla'
 * '<S38>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Look2D_IR_U16'
 * '<S39>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Calc_TPla/Look2D_IR_U1'
 * '<S40>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Calc_TPla/Look2D_IR_U2'
 * '<S41>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Calc_TPla/Look2D_IR_U3'
 * '<S42>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Calc_TPla/Look2D_IR_U1/Data Type Conversion Inherited1'
 * '<S43>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Calc_TPla/Look2D_IR_U2/Data Type Conversion Inherited1'
 * '<S44>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Calc_TPla/Look2D_IR_U3/Data Type Conversion Inherited1'
 * '<S45>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Assign_Strategy/Calc_PlaPulseOL/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S46>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck'
 * '<S47>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBkEn'
 * '<S48>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases'
 * '<S49>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases'
 * '<S50>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBkEn/VBatt_BE_Compensate'
 * '<S51>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBkEn/VBatt_BE_Compensate/LookUp_IR_S1'
 * '<S52>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBkEn/VBatt_BE_Compensate/LookUp_IR_S16'
 * '<S53>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBkEn/VBatt_BE_Compensate/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S54>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBkEn/VBatt_BE_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S55>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Fall_Phase'
 * '<S56>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase'
 * '<S57>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_1'
 * '<S58>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_2'
 * '<S59>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_3'
 * '<S60>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_1/LookUp_IR_U16_1'
 * '<S61>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_1/VBatt_BKF_Compensate'
 * '<S62>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_1/LookUp_IR_U16_1/Data Type Conversion Inherited3'
 * '<S63>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_1/VBatt_BKF_Compensate/LookUp_IR_S16'
 * '<S64>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_1/VBatt_BKF_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S65>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_2/LookUp_IR_U16_1'
 * '<S66>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_2/VBatt_BKF_Compensate'
 * '<S67>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_2/LookUp_IR_U16_1/Data Type Conversion Inherited3'
 * '<S68>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_2/VBatt_BKF_Compensate/LookUp_IR_S16'
 * '<S69>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_2/VBatt_BKF_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S70>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_3/LookUp_IR_U16'
 * '<S71>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_3/VBatt_BKF_Compensate'
 * '<S72>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_3/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S73>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_3/VBatt_BKF_Compensate/LookUp_IR_S16'
 * '<S74>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DelayBk_Phases/Calc_Rise_Phase/Calc_Delay_Phase_3/VBatt_BKF_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S75>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Fall_phase'
 * '<S76>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase'
 * '<S77>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_1'
 * '<S78>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_2'
 * '<S79>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_3'
 * '<S80>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_4'
 * '<S81>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_1/LookUp_IR_U16_1'
 * '<S82>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_1/VBatt_BKF_Compensate'
 * '<S83>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_1/LookUp_IR_U16_1/Data Type Conversion Inherited3'
 * '<S84>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_1/VBatt_BKF_Compensate/LookUp_IR_S16'
 * '<S85>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_1/VBatt_BKF_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S86>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_2/LookUp_IR_U16_1'
 * '<S87>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_2/VBatt_BKF_Compensate'
 * '<S88>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_2/LookUp_IR_U16_1/Data Type Conversion Inherited3'
 * '<S89>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_2/VBatt_BKF_Compensate/LookUp_IR_S16'
 * '<S90>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_2/VBatt_BKF_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S91>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_3/LookUp_IR_U16_1'
 * '<S92>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_3/VBatt_BKF_Compensate'
 * '<S93>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_3/LookUp_IR_U16_1/Data Type Conversion Inherited3'
 * '<S94>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_3/VBatt_BKF_Compensate/LookUp_IR_S16'
 * '<S95>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_3/VBatt_BKF_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S96>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_4/LookUp_IR_U16'
 * '<S97>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_4/VBatt_BKF_Compensate'
 * '<S98>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_4/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S99>'  : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_4/VBatt_BKF_Compensate/LookUp_IR_S1'
 * '<S100>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Buck_Param/Calc_DelayBuck/Calc_DutyBk_Phases/Calc_Pwm_Rise_phase/Calc_PWM_Phase_4/VBatt_BKF_Compensate/LookUp_IR_S1/Data Type Conversion Inherited3'
 * '<S101>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW'
 * '<S102>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/Select_Ctrl'
 * '<S103>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/Temp_Compensate'
 * '<S104>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/VBatt_Compensate'
 * '<S105>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/Temp_Compensate/LookUp_IR_S16'
 * '<S106>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/Temp_Compensate/LookUp_IR_S16_1'
 * '<S107>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/Temp_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S108>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/Temp_Compensate/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S109>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/VBatt_Compensate/LookUp_IR_S16'
 * '<S110>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/VBatt_Compensate/LookUp_IR_S16_1'
 * '<S111>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/VBatt_Compensate/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S112>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Parameters/Calc_Dwell_Time/FFW/VBatt_Compensate/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S113>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Ratio/PreLookUpIdSearch_S16_1'
 * '<S114>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Ratio/PreLookUpIdSearch_S16_2'
 * '<S115>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Ratio/PreLookUpIdSearch_U16'
 * '<S116>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Ratio/PreLookUpIdSearch_U16_1'
 * '<S117>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Ratio/PreLookUpIdSearch_U16_2'
 * '<S118>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Ratio/PreLookUpIdSearch_U16_3'
 * '<S119>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/Assign_Params'
 * '<S120>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/Saturate_Params'
 * '<S121>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/TrigInTime_Calc'
 * '<S122>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/Assign_Params/Saturation'
 * '<S123>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/Saturate_Params/Sat_DelayBk'
 * '<S124>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/Saturate_Params/Sat_DwellTime'
 * '<S125>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/Saturate_Params/Sat_PlaPulseOL'
 * '<S126>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/TrigInTime_Calc/Calc_Scale_Us'
 * '<S127>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/TrigInTime_Calc/Calc_Scale_Us/LookUp_IR_U16'
 * '<S128>' : 'CoilAngPattern/SparkOff_fcn/PlaCtrl_Calc/Calc_Sat_Param/TrigInTime_Calc/Calc_Scale_Us/LookUp_IR_U16/Data Type Conversion Inherited3'
 */

/*-
 * Requirements for '<Root>': CoilAngPattern
 */
#endif                                 /* RTW_HEADER_CoilAngPattern_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/