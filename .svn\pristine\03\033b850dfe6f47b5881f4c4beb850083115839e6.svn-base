#!gbuild
[Subproject]
	-I..\tree\DD\COMMON
	--misra_2004=-18.4	#too many proven in use modules with unions
DD\TEMPMGM\TempMgm_calib.c
DD\TEMPMGM\TempMgm.c
	--misra_2004=-10.1,-12.7,-13.7
DD\IONACQ\ionacq_calib.c
	--misra_2004=-8.12
DD\IONACQ\ionacq.c
	--misra_2004=-2.4,-8.12,-12.4
DD\MSPARKCMD\msparkcmd_calib.c
DD\MSPARKCMD\msparkcmd.c
	--misra_2004=-12.4
DD\IGNINCMD\ignincmd_calib.c
DD\IGNINCMD\ignincmd.c
DD\BUCKDIAGMGM\buckdiagmgm_calib.c
DD\BUCKDIAGMGM\buckdiagmgm.c
DD\TPE\src\tpe.c
	--misra_2004=-10.1,-10.5,-12.6,-16.9,-17.4
DD\DIAGCANMGM\src\DIAGCANMGM_calib.c
DD\DIAGCANMGM\src\Diagcanmgm.c
	--misra_2004=-10.1,-10.5,-12.6,-13.2
DD\DIAGCANMGM\src\Active_Diag.c
DD\DIAGCANMGM\src\Rli.c
	--misra_2004=-10.1,-10.4,-12.1,-12.5,-12.7,-16.9,-17.4
DD\DIGIN\DigIn.c
DD\DIGIN\DigIn_calib.c
DD\EEMGM\eemgm_calib.c
DD\EEMGM\eemgm.c
	--misra_2004=-12.7
DD\EEMGM\ee_ID0.c
	-Onone
DD\EEMGM\ee_ID1.c
	-Onone
DD\EEMGM\ee_ID2.c
	-Onone
DD\EEMGM\ee_ID3.c
	-no_discard_zero_initializers
	-Onone
DD\EEMGM\ee_ID8.c
	-Onone
DD\EEMGM\ee_ID7.c
	-Onone
DD\EEMGM\ee_ID6.c
	-no_discard_zero_initializers
	-Onone
DD\EEMGM\ee_ID5.c
	-Onone
DD\EEMGM\ee_ID4.c
	-Onone
DD\EEMGM\ee_ID9.c
	-Onone
DD\EEMGM\ee_ID10.c
DD\EEMGM\ee_ID11.c
DD\INTSRCMGM\intsrcmgm.c
DD\ANALOGIN\AnalogIn.c
	--misra_2004=-12.7,-17.4
DD\ANALOGIN\AnalogIn_calib.c
DD\CCP\ccptxdata.c
DD\CCP\ccp_can_interface.c
DD\CCP\ccp.c
DD\SPIMGM\SPIMGM.c
DD\VSRAMMGM\vsrammgm.c
DD\VSRAMMGM\Vsram_shared_IO.c
	--misra_2004=-10.1,-11.4,-17.4	                 #MISRA 17.4 violation with pointers
DD\VSRAMMGM\vsram_shared_content.c
DD\VSRAMMGM\vsram_content.c
DD\VSRAMMGM\vsram_checksum.c
DD\CANMGM\CanMgm.c
DD\CANMGM\CanMgm_calib.c
DD\TLE9278BQX\TLE9278BQX_Cfg\TLE9278BQX_Cfg.c
	--misra_2004=-10.1,-12.7,-19.1
DD\TLE9278BQX\TLE9278BQX_Cfg\Cfg_Return_Addr_U16_wrapper.c
	--misra_2004=-2.4,-11.4-11.5,-17.4
DD\TLE9278BQX\TLE9278BQX_Cfg\Cfg_SkipVal_wrapper.c
	--misra_2004=-2.4,-10.1,-11.4-11.5,-12.7,-17.4
DD\TLE9278BQX\TLE9278BQX_Cfg\Cfg_UpdateVal_wrapper.c
	--misra_2004=-2.4,-10.1,-11.4-11.5,-12.7,-17.4
DD\TLE9278BQX\TLE9278BQX_Com\TLE9278BQX_Com.c
	--misra_2004=-10.1,-10.6,-12.4,-12.6-12.7,-13.2,-13.7,-19.1
DD\TLE9278BQX\TLE9278BQX_Com\Ret_SBCData_Addr_wrapper.c
	--misra_2004=-2.4,-11.4-11.5,-17.4
DD\TLE9278BQX\TLE9278BQX_Com\TLE9278BQX_IvorEE.c
DD\TLE9278BQX\TLE9278BQX_Com\fc_EECntSBCResend_SetVal_wrapper.c
	--misra_2004=-2.4,-11.4-11.5,-17.4
DD\TLE9278BQX\TLE9278BQX_Com\EECntSBCResend_Addr_U16_wrapper.c
	--misra_2004=-2.4,-11.4-11.5,-17.4
DD\TLE9278BQX\TLE9278BQX_Diag\TLE9278BQX_Diag.c
	--misra_2004=-10.1,-10.5,-12.7,-19.1
DD\TLE9278BQX\TLE9278BQX_Diag\fc_Diag_SetVal_wrapper.c
	--misra_2004=-2.4,-8.1
DD\TLE9278BQX\TLE9278BQX_Diag\Diag_Return_Addr_U8_wrapper.c
	--misra_2004=-2.4,-8.1,-11.4-11.5,-17.4
DD\TLE9278BQX\TLE9278BQX_Get\TLE9278BQX_Get.c
	--misra_2004=-10.1,-12.7,-15.3,-19.1
DD\TLE9278BQX\TLE9278BQX_Mgm\TLE9278BQX_Mgm.c
	--misra_2004=-10.1,-10.5,-12.4,-12.6-12.7,-13.2,-19.1,-19.11
DD\TLE9278BQX\TLE9278BQX_Prs\TLE9278BQX_Prs.c
	--misra_2004=-10.1,-12.7,-19.1
DD\TLE9278BQX\TLE9278BQX_IOs\TLE9278BQX_IOs.c
	--misra_2004=-10.1,-12.7,-19.1
DD\WDT\WDT.c
	--misra_2004=-10.1,-12.4,-12.6-12.7,-13.2,-13.7,-19.1
DD\WDT\WDT_wrapper.c
DD\FLASHMGM\Flashmgm.c
DD\FLASHMGM\Flashmgm_calib.c
