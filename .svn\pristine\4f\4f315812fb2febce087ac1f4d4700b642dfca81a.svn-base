/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmOut
**  Filename        :  CanMgmOut_F17x.h
**  Created on      :  26-nov-2020 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifndef CANMGM_OUT_F17x_H
#define CANMGM_OUT_F17x_H
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Canmgmout_out.h"
#include "Canmgmin_out.h"
#include "Canmgm_out.h"
#include "Mcan_out.h"
#include "KnockCorrTot_out.h"
#include "pwrmgm_out.h"
#include "msparkcmd_out.h"
#include "recmgm_out.h"
#include "P2NoiseDetect_out.h"
#include "Ionacq_out.h"
#include "Diagmgm_out.h"
#include "AnalogIn_out.h"
#include "WDT_wrapper_out.h"
#include "FlashMgm_out.h"
#include "ron_detect.h"
#include "MisfThrMgm_out.h"
#include "MKnockDet_out.h"
#include "TempECUMgm_out.h"
#include "ionmisf_out.h"
#include "SyncMgm_out.h"
#include "mathlib.h"
#include "Sys.h"
#include "Utils_out.h"
#include "Dtc.h"
#include "DiagCanmgm_out.h"
#include "ccp.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define BIT_0 0x01u
#define BIT_1 0x02u
#define BIT_2 0x04u
#define BIT_3 0x08u
#define BIT_4 0x10u
#define BIT_0_1_2 0x07u
#define BIT_1_2 0x06u

#define NEUTRAL_CORR                   ((uint16_T) 32768U)

#define     CNT_ONE_SECOND  20u

#define TEMP_STS_NORMAL  0u
#define TEMP_STS_WARNING 1u
#define TEMP_STS_HAZARD  2u
#define TEMP_STS_FAULT   3u

#define THPEAKANGLE_CAN_CONV_OFFSET 2049
#define KNOCKINT_CAN_CONV_OFF 16u

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
#pragma ghs startnomisra

typedef struct IONMOT_DX_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Misf_flag_00   :2;
            uint8_T  KnockFault00   :1;
            uint8_T  KnockCorr00   :5;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Misf_flag_21   :2;
            uint8_T  KnockFault21   :1;
            uint8_T  KnockCorr21   :5;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Misf_flag_42   :2;
            uint8_T  KnockFault42   :1;
            uint8_T  KnockCorr42   :5;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Misf_flag_63   :2;
            uint8_T  KnockFault63   :1;
            uint8_T  KnockCorr63   :5;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy4_0   :2;
            uint8_T  LastMisfAbsTDC4   :3;
            uint8_T  FirstMisfAbsTdc4   :3;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BitFault_76   :1;
            uint8_T  BitFault_66   :1;
            uint8_T  LostSync6   :1;
            uint8_T  BitFault_TempEcu36   :1;
            uint8_T  CANMsgCnt6   :2;
            uint8_T  MisfCyclesCnt6   :2;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum7   :8;
        } B;
    } Byte7;

} IONMOT_DX_T;

typedef struct IONMOT_SX_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Misf_flag_10   :2;
            uint8_T  KnockFault10   :1;
            uint8_T  KnockCorr10   :5;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Misf_flag_31   :2;
            uint8_T  KnockFault31   :1;
            uint8_T  KnockCorr31   :5;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Misf_flag_52   :2;
            uint8_T  KnockFault52   :1;
            uint8_T  KnockCorr52   :5;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Misf_flag_73   :2;
            uint8_T  KnockFault73   :1;
            uint8_T  KnockCorr73   :5;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BitFault_76   :1;
            uint8_T  BitFault_66   :1;
            uint8_T  LostSync6   :1;
            uint8_T  BitFault_TempEcu36   :1;
            uint8_T  CANMsgCnt6   :2;
            uint8_T  MisfCyclesCnt6   :2;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum7   :8;
        } B;
    } Byte7;

} IONMOT_SX_T;

typedef struct IONMOT3_DX_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault20   :3;
            uint8_T  IgnFault00   :5;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault61   :1;
            uint8_T  IgnFault41   :5;
            uint8_T  IgnFault21   :2;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy2_0   :4;
            uint8_T  IgnFault62   :4;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MSstatusDX3   :2;
            uint8_T Dummy3_1   :6;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FuelDetDX4   :1;
            uint8_T  MSignnumDX4   :3;
            uint8_T  msgctrDX4   :4;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FuelCorrSADX5   :5;
            uint8_T  FuelQualDX5   :3;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MKDetImpsbl_DX6   :1;
            uint8_T Dummy6_1   :2;
            uint8_T  MegaKnock66   :1;
            uint8_T  MegaKnock46   :1;
            uint8_T  MegaKnock26   :1;
            uint8_T  MegaKnock06   :1;
            uint8_T  BrokenFuseDX6   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  checksumDX7   :8;
        } B;
    } Byte7;

} IONMOT3_DX_T;

typedef struct IONMOT3_SX_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault30   :3;
            uint8_T  IgnFault10   :5;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IgnFault71   :1;
            uint8_T  IgnFault51   :5;
            uint8_T  IgnFault31   :2;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy2_0   :4;
            uint8_T  IgnFault72   :4;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MSstatusSX3   :2;
            uint8_T Dummy3_1   :6;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FuelDetSX4   :1;
            uint8_T  MSignnumSX4   :3;
            uint8_T  msgctrSX4   :4;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FuelCorrSASX5   :5;
            uint8_T  FuelQualSX5   :3;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MKDetImpsbl_SX6   :1;
            uint8_T Dummy6_1   :2;
            uint8_T  MegaKnock76   :1;
            uint8_T  MegaKnock56   :1;
            uint8_T  MegaKnock36   :1;
            uint8_T  MegaKnock16   :1;
            uint8_T  BrokenFuseSX6   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  chacksumSX7   :8;
        } B;
    } Byte7;

} IONMOT3_SX_T;

typedef struct IONMOT2_DX_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FDuty0   :5;
            uint8_T  DiagStsAlt0   :3;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TimeOut1   :1;
            uint8_T  ComFaultAlt1   :1;
            uint8_T  ExcCurAlt1   :6;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  AltCode2   :5;
            uint8_T  SuppCode2   :3;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy3_0   :7;
            uint8_T  LinComSts3   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy5_0   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :8;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy7_0   :8;
        } B;
    } Byte7;

} IONMOT2_DX_T;

typedef struct ION_THPEAKANGLE_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ThPeakAngle0_0   :7;
            uint8_T  ThPeakAngleMux   :1;
        } Mux0;
        struct
        {
            uint8_T  ThPeakAngle4_0   :7;
            uint8_T  ThPeakAngleMux   :1;
        } Mux1;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ThPeakAngle1_1   :4;
            uint8_T  ThPeakAngle0_1   :4;
        } Mux0;
        struct
        {
            uint8_T  ThPeakAngle5_1   :4;
            uint8_T  ThPeakAngle4_1   :4;
        } Mux1;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ThPeakAngle2_2   :1;
            uint8_T  ThPeakAngle1_2   :7;
        } Mux0;
        struct
        {
            uint8_T  ThPeakAngle6_2   :1;
            uint8_T  ThPeakAngle5_2   :7;
        } Mux1;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ThPeakAngle2_3   :8;
        } Mux0;
        struct
        {
            uint8_T  ThPeakAngle6_3   :8;
        } Mux1;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ThPeakAngle3_4   :6;
            uint8_T  ThPeakAngle2_4   :2;
        } Mux0;
        struct
        {
            uint8_T  ThPeakAngle7_4   :6;
            uint8_T  ThPeakAngle6_4   :2;
        } Mux1;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Dummy_5       :3;
            uint8_T  ThPeakAngle3_5   :5;
        } Mux0;
        struct
        {
            uint8_T  Dummy_5       :3;
            uint8_T  ThPeakAngle7_5   :5;
        } Mux1;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Dummy_6   :8;
        } Mux0;
        struct
        {
            uint8_T  Dummy_6   :8;
        } Mux1;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ThPeakAngleMsgCnt_5   :3;
            uint8_T  Bummy_7   :5;
        } Mux0;
        struct
        {
            uint8_T  ThPeakAngleMsgCnt_5   :3;
            uint8_T  Bummy_7   :5;
        } Mux1;
    } Byte7;
} ION_THPEAKANGLE_T;

typedef struct ION_KNOCKINT_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt0_0   :7;
            uint8_T  KnockIntMux   :1;
        } Mux0;
        struct
        {
            uint8_T  KnockInt4_0   :7;
            uint8_T  KnockIntMux   :1;
        } Mux1;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt1_1   :4;
            uint8_T  KnockInt0_1   :4;
        } Mux0;
        struct
        {
            uint8_T  KnockInt5_1   :4;
            uint8_T  KnockInt4_1   :4;
        } Mux1;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt2_2   :1;
            uint8_T  KnockInt1_2   :7;
        } Mux0;
        struct
        {
            uint8_T  KnockInt6_2   :1;
            uint8_T  KnockInt5_2   :7;
        } Mux1;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt2_3   :8;
        } Mux0;
        struct
        {
            uint8_T  KnockInt6_3   :8;
        } Mux1;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt3_4   :6;
            uint8_T  KnockInt2_4   :2;
        } Mux0;
        struct
        {
            uint8_T  KnockInt7_4   :6;
            uint8_T  KnockInt6_4   :2;
        } Mux1;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Dummy_5       :3;
            uint8_T  KnockInt3_5   :5;
        } Mux0;
        struct
        {
            uint8_T  Dummy_5       :3;
            uint8_T  KnockInt7_5   :5;
        } Mux1;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Dummy_6   :8;
        } Mux0;
        struct
        {
            uint8_T  Dummy_6   :8;
        } Mux1;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockIntMsgCnt_5   :3;
            uint8_T  Bummy_7   :5;
        } Mux0;
        struct
        {
            uint8_T  KnockIntMsgCnt_5   :3;
            uint8_T  Bummy_7   :5;
        } Mux1;
    } Byte7;
} ION_KNOCKINT_T;

typedef struct ION_TO_SCANTOOL_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  APPL_ID0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CALID_11   :4;
            uint8_T  CALID_01   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CALID_32   :4;
            uint8_T  CALID_22   :4;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CALID_53   :4;
            uint8_T  CALID_43   :4;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CALID_74   :4;
            uint8_T  CALID_64   :4;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy5_0   :7;
            uint8_T Flash_Or_DTC_Erased_5   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Reserved_CVN_06   :8;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Reserved_CVN_17   :8;
        } B;
    } Byte7;

} ION_TO_SCANTOOL_T;

typedef struct ION_TO_SCANTOOL_FAULTS_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BitFault_RPM_Plausibility_0   :1;
            uint8_T  BilFault_Rpm_EL_CrankShaftSig_0   :1;
            uint8_T  BilFault_Rpm_EnginePhase_0   :1;
            uint8_T  BilFault_Rpm_FlyWheel_0   :1;
            uint8_T  BitFault_Phase_Over_Thr_0   :1;
            uint8_T  BitFault_Phase_Under_Thr_0   :1;
            uint8_T  BitFault_Phase_Plausibility_0   :1;
            uint8_T  BitFault_Private_CAN_0   :1;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BitFault_Liveness_SC2VBAT_1   :1;
            uint8_T  BitFault_Liveness_OC_1   :1;
            uint8_T  BitFault_Liveness_SC2GND_1   :1;
            uint8_T  BitFault_INT_CPU_1   :1;
            uint8_T  BitFault_KeySignal_1   :1;
            uint8_T  BitFault_CCAN_1   :1;
            uint8_T  BitFault_INT_VBAT_HIGH_1   :1;
            uint8_T  BitFault_INT_VBAT_LOW_1   :1;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BitFault_INT_IonShape_cyl_52   :1;
            uint8_T  BitFault_INT_IonShape_cyl_42   :1;
            uint8_T  BitFault_INT_IonShape_cyl_32   :1;
            uint8_T  BitFault_INT_IonShape_cyl_22   :1;
            uint8_T  BitFault_INT_IonShape_cyl_12   :1;
            uint8_T  BitFault_INT_IonShape_cyl_02   :1;
            uint8_T  BitFault_IonAcqCh_B_2   :1;
            uint8_T  BitFault_IonAcqCh_A_2   :1;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EISB_Temp_Status3   :2;
            uint8_T  GPFInfo_ION_ValidData3   :1;
            uint8_T  BitFault_IN_3   :1;
            uint8_T  BitFault_Bank_not_Plausible_3   :1;
            uint8_T  BitFault_INT_Spark_3   :1;
            uint8_T  BitFault_INT_IonShape_cyl_73   :1;
            uint8_T  BitFault_INT_IonShape_cyl_63   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R;
        struct
        {
            uint8_T  GPFInfo_ION_Lsb4   :5;
            uint8_T  GPFInfo_ION_Cnt4   :2;
            uint8_T  GPFInfo_ION_MuxId4   :1;
        } Mux0;
        struct
        {
            uint8_T  GPFInfo_ION_Msb4   :5;
            uint8_T  GPFInfo_ION_Cnt4   :2;
            uint8_T  GPFInfo_ION_MuxId4   :1;
        } Mux1;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BitFault_Spark_OC_55   :1;
            uint8_T  BitFault_Spark_OC_45   :1;
            uint8_T  BitFault_Spark_OC_35   :1;
            uint8_T  BitFault_Spark_OC_25   :1;
            uint8_T  BitFault_Spark_OC_15   :1;
            uint8_T  BitFault_Spark_OC_05   :1;
            uint8_T  BitFault_WDT_Safety_Failure5   :1;
            uint8_T  BitFault_WDT_Comp_Int_Failure5   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  BitFault_Spark_SC2GND_56   :1;
            uint8_T  BitFault_Spark_SC2GND_46   :1;
            uint8_T  BitFault_Spark_SC2GND_36   :1;
            uint8_T  BitFault_Spark_SC2GND_26   :1;
            uint8_T  BitFault_Spark_SC2GND_16   :1;
            uint8_T  BitFault_Spark_SC2GND_06   :1;
            uint8_T  BitFault_Spark_OC_76   :1;
            uint8_T  BitFault_Spark_OC_66   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy7_0   :1;
            uint8_T  BitFault_ETPU7   :1;
            uint8_T  BitFault_Secondary_SC2VCC_4_87   :1;
            uint8_T  BitFault_Secondary_SC2VCC_3_77   :1;
            uint8_T  BitFault_Secondary_SC2VCC_2_67   :1;
            uint8_T  BitFault_Secondary_SC2VCC_1_57   :1;
            uint8_T  BitFault_Spark_SC2GND_77   :1;
            uint8_T  BitFault_Spark_SC2GND_67   :1;
        } B;
    } Byte7; 
}ION_TO_SCANTOOL_FAULTS_T;

/* New ANGULAR frames */
typedef struct ION_TIME_STS_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy0_0   :1;
            uint8_T  FuelDetected0   :1;
            uint8_T  AdvIgnErr0   :1;
            uint8_T  LostSync0   :1;
            uint8_T  ION_TimeStsMsgCnt0   :4;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FuelQual1   :3;
            uint8_T  FuelCorrSA1   :5;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy2_0   :8;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy3_0   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ION_TimeStsCRC5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :8;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy7_0   :8;
        } B;
    } Byte7;
} ION_TIME_STS_T;

typedef struct ION_TDC_STS_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy0_0   :1;
            uint8_T  ION_TdcStsMsgCnt0   :4;
            uint8_T  ION_TdcStsMuxCyl    :3;
        } B;
    } Byte0;
    
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MKnockDis   :1;
            uint8_T  MegaKnock   :1;
            uint8_T  KnockFault  :1;
            uint8_T  KnockCorr   :5;
        } Cyl0;
        struct
        {
            uint8_T  MKnockDis   :1;
            uint8_T  MegaKnock   :1;
            uint8_T  KnockFault  :1;
            uint8_T  KnockCorr   :5;
        } Cyl1;
        struct
        {
            uint8_T  MKnockDis   :1;
            uint8_T  MegaKnock   :1;
            uint8_T  KnockFault  :1;
            uint8_T  KnockCorr   :5;
        } Cyl2;
        struct
        {
            uint8_T  MKnockDis   :1;
            uint8_T  MegaKnock   :1;
            uint8_T  KnockFault  :1;
            uint8_T  KnockCorr   :5;
        } Cyl3;
        struct
        {
            uint8_T  MKnockDis   :1;
            uint8_T  MegaKnock   :1;
            uint8_T  KnockFault  :1;
            uint8_T  KnockCorr   :5;
        } Cyl4;
        struct
        {
            uint8_T  MKnockDis   :1;
            uint8_T  MegaKnock   :1;
            uint8_T  KnockFault  :1;
            uint8_T  KnockCorr   :5;
        } Cyl5;
        struct
        {
            uint8_T  MKnockDis   :1;
            uint8_T  MegaKnock   :1;
            uint8_T  KnockFault  :1;
            uint8_T  KnockCorr   :5;
        } Cyl6;
        struct
        {
            uint8_T  MKnockDis   :1;
            uint8_T  MegaKnock   :1;
            uint8_T  KnockFault  :1;
            uint8_T  KnockCorr   :5;
        } Cyl7;
    } Byte1;
    
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  RetIgnErr   :1;
            uint8_T  BankOFF_A   :1;
            uint8_T  Misf_flag   :2;
            uint8_T  IgnFault_Checked   :1;
            uint8_T  IgnFault   :3;
        } Cyl0;
        struct
        {
            uint8_T  RetIgnErr   :1;
            uint8_T  BankOFF_A   :1;
            uint8_T  Misf_flag   :2;
            uint8_T  IgnFault_Checked   :1;
            uint8_T  IgnFault   :3;
        } Cyl1;
        struct
        {
            uint8_T  RetIgnErr   :1;
            uint8_T  BankOFF_A   :1;
            uint8_T  Misf_flag   :2;
            uint8_T  IgnFault_Checked   :1;
            uint8_T  IgnFault   :3;
        } Cyl2;
        struct
        {
            uint8_T  RetIgnErr   :1;
            uint8_T  BankOFF_A   :1;
            uint8_T  Misf_flag   :2;
            uint8_T  IgnFault_Checked   :1;
            uint8_T  IgnFault   :3;
        } Cyl3;
        struct
        {
            uint8_T  RetIgnErr   :1;
            uint8_T  BankOFF_A   :1;
            uint8_T  Misf_flag   :2;
            uint8_T  IgnFault_Checked   :1;
            uint8_T  IgnFault   :3;
        } Cyl4;
        struct
        {
            uint8_T  RetIgnErr   :1;
            uint8_T  BankOFF_A   :1;
            uint8_T  Misf_flag   :2;
            uint8_T  IgnFault_Checked   :1;
            uint8_T  IgnFault   :3;
        } Cyl5;
        struct
        {
            uint8_T  RetIgnErr   :1;
            uint8_T  BankOFF_A   :1;
            uint8_T  Misf_flag   :2;
            uint8_T  IgnFault_Checked   :1;
            uint8_T  IgnFault   :3;
        } Cyl6;
        struct
        {
            uint8_T  RetIgnErr   :1;
            uint8_T  BankOFF_A   :1;
            uint8_T  Misf_flag   :2;
            uint8_T  IgnFault_Checked   :1;
            uint8_T  IgnFault   :3;
        } Cyl7;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy3_0   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy4_0   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ION_TdcStsCRC5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :8;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy7_0   :8;
        } B;
    } Byte7;

} ION_TDC_STS_T;

typedef enum{
    ION_TDC_STS_CYL0 = 0,
    ION_TDC_STS_CYL1 = 1,
    ION_TDC_STS_CYL2 = 2,
    ION_TDC_STS_CYL3 = 3,
    ION_TDC_STS_CYL4 = 4,
    ION_TDC_STS_CYL5 = 5,
    ION_TDC_STS_CYL6 = 6,
    ION_TDC_STS_CYL7 = 7
}IonTdcStsMux_T;
/****************************************/

typedef enum{
    NO_PROJECT = 0,
    Giorgio_V6_952 = 1,
    F142M = 2,
    F149M = 3,
    F152VS = 4,
    F151M_V12 = 5,
    F151M_V8T =6,
    F164 = 7,
    F152M = 8,
    F150R = 9,
    F142M_CHP = 10,
    F142M_VS = 11,
    /* 12 - 19 reserved for EI3.x projects */
    F173 = 20,
    F173VS = 21,
    F8_22 = 22,
    F8_23 = 23,
    F8_24 = 24,
    F8_25 = 25,
    F8_26 = 26,
    F8_27 = 27,
    F8_28 = 28,
    F8_29 = 29,
    F171 = 30,
    F250 = 31,
    F6_32 = 32,
    F6_33 = 33,
    F6_34 = 34,
    F6_35 = 35,
    F6_36 = 36,
    F6_37 = 37,
    F6_38 = 38,
    F6_39 = 39
}ApplIdVal_t;

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST int16_T  OFFSAKNOCKCAN;
extern CALQUAL CALQUAL_POST int16_T  VTCANSAKNOCK[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint8_T  ENADDSARON;
extern CALQUAL CALQUAL_POST uint16_T VTSTMISFFORCEPERIOD[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint16_T VTSTMISFFORCEDLENGTH[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint8_T  VTSTMISFFORCEDTYPE[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint32_T MAXDELTASPARKTIME;
extern CALQUAL CALQUAL_POST uint8_T FORCEIGNFAULT[N_CYL_MAX];
extern CALQUAL CALQUAL_POST uint8_T FORCEDIGNFAULT[N_CYL_MAX];
extern CALQUAL CALQUAL_POST ApplIdVal_t APPLIDVAL;
extern CALQUAL CALQUAL_POST uint8_T CALVER[4];
extern CALQUAL CALQUAL_POST uint8_T ENGPFINFO;
extern CALQUAL CALQUAL_POST uint8_T ION2SCANTOOLDEBCLEAR;
extern CALQUAL CALQUAL_POST uint8_T ION2SCANTOOLDEBSET;
extern CALQUAL CALQUAL_POST uint8_T FLASHORDTCERASEDEB;
extern CALQUAL CALQUAL_POST uint8_T ENKNOCKANALYSIS;
extern CALQUAL CALQUAL_POST uint8_T CANTYPE;
extern CALQUAL CALQUAL_POST uint8_T ENOLDGPFINFO;

/*****************************************************************************
** IMPORTED EE Variables
******************************************************************************/
extern uint16_T FlashCrcEE;

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static uint8_T CanMgm_UpdateStMisfInfo(uint8_T stMisf, uint8_T tdc);
static int16_T CanMgm_Send_IonToScanTool(void);
static int16_T CanMgm_Send_IonToScanTool_FAULTS(void);
static void CanMgm_UpdateIon2ScanToolFaults(void);
static int16_T CanMgm_Send_IONTDCSTS(uint8_T cyl);
static int16_T CanMgm_Send_IONTIMESTS(void);
static int16_T CanMgm_Send_IonThPeakAng(uint8_T muxCyl);
static int16_T CanMgm_Send_IonKnockInt(uint8_T muxCyl);
static uint16_T CanMgm_ThPeakCanConv(int16_T thPeakAngle);
static uint16_T CanMgm_KnockIntCanConv(uint32_T knockInt);

/* To Be removed when only final configuration shall be active */
static int16_T CanMgm_Send_IONMOT_SX(uint8_T canMsgBuff);
static int16_T CanMgm_Send_IONMOT_DX(uint8_T canMsgBuff);
static int16_T CanMgm_Send_IONMOT_3_SX(uint8_T canMsgBuff);
static int16_T CanMgm_Send_IONMOT_3_DX(uint8_T canMsgBuff);
static void CanMgm_UpdateMisfAbsTdc(void);

#endif

/****************************************************************************
 ****************************************************************************/


