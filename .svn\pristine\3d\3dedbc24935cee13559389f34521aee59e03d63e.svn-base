/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonPhaseMgm.h
 **  Date:          26-Oct-2022
 **
 **  Model Version: 1.1317
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonPhaseMgm_h_
#define RTW_HEADER_IonPhaseMgm_h_
#include <string.h>
#ifndef IonPhaseMgm_COMMON_INCLUDES_
# define IonPhaseMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonPhaseMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

 
/* Enumerated types definition */
typedef uint8_T enum_StPhase;
#define START_SEARCH                   ((enum_StPhase)0U)        /* Default value */
#define EDGE_FOUND                     ((enum_StPhase)1U)
#define FIRST_PEAK_FOUND               ((enum_StPhase)2U)
#define CH_START_FOUND                 ((enum_StPhase)3U)
#define TH_START_FOUND                 ((enum_StPhase)4U)
 
/* Model entry point functions */
extern void IonPhaseMgm_initialize(void);

/* Exported entry point function */
extern void IonPhaseMgm_EOA(void);

/* Exported entry point function */
extern void IonPhaseMgm_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgNoRelChem;           /* '<S45>/Merge5' */

/* Chemical phase not detected */
extern uint16_T IonMgmBkRpmIonIndex;   /* '<S21>/Merge3' */

/* Index on BKRPMION breakpoints pre-lookup */
extern uint16_T IonMgmBkRpmIonRatio;   /* '<S21>/Merge4' */

/* Ratio on BKRPMION breakpoints pre-lookup */
extern uint32_T IonMgmRpmCHDelayIndex; /* '<S21>/Merge2' */

/* Left index on BKRPMCTF breakpoints for binary search */
extern enum_StPhase StPhase[8];        /* '<S45>/Merge7' */

/* Search phase flag. */
extern enum_StPhase StPhaseCyl;        /* '<S45>/Merge15' */

/* Search phase flag for cylinder */
extern uint16_T StartChIonCyl;         /* '<S3>/Merge' */

/* Ion Chemical phase start delta index */
extern uint16_T Start_Ch[8];           /* '<S45>/Merge1' */

/* Ion Chemical phase start index */
extern uint16_T Start_Th[8];           /* '<S45>/Merge6' */

/* Ion thermal phase start index */
extern uint16_T Start_ThCyl;           /* '<S45>/Merge4' */

/* Ion thermal phase start index */
extern uint16_T Start_ionCyl;          /* '<S45>/Merge3' */

/* Ion start index */
extern uint16_T TSpark[8];             /* '<S45>/Merge9' */

/* Estimated spark time */
extern uint8_T TSparkFreezeFlg;        /* '<S45>/Merge2' */

/* Flag to signal Tspark has not been updated */
extern uint16_T VtTSparkMedian[8];     /* '<S45>/Merge' */

/* Median of Estimated spark time */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S18>/Data Type Duplicate' : Unused code path elimination
 * Block '<S30>/Constant' : Unused code path elimination
 * Block '<S30>/Data Type Duplicate' : Unused code path elimination
 * Block '<S30>/Data Type Propagation' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S31>/Data Type Propagation' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S32>/Data Type Propagation' : Unused code path elimination
 * Block '<S33>/Data Type Duplicate' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S38>/Data Type Duplicate' : Unused code path elimination
 * Block '<S37>/Data Type Duplicate' : Unused code path elimination
 * Block '<S49>/Data Type Duplicate' : Unused code path elimination
 * Block '<S49>/Data Type Propagation' : Unused code path elimination
 * Block '<S54>/Data Type Duplicate' : Unused code path elimination
 * Block '<S28>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S30>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S30>/Reshape' : Reshape block reduction
 * Block '<S31>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S31>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S31>/Reshape' : Reshape block reduction
 * Block '<S32>/Conversion' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S32>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S33>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S33>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S33>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S33>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S35>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S35>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S35>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S35>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion' : Eliminate redundant data type conversion
 * Block '<S36>/Reshape' : Reshape block reduction
 * Block '<S37>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S37>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S37>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S37>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S54>/Conversion' : Eliminate redundant data type conversion
 * Block '<S53>/ConversionIn' : Eliminate redundant data type conversion
 * Block '<S53>/ConversionIn1' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonPhaseMgm'
 * '<S1>'   : 'IonPhaseMgm/IonPhaseMgm_FC'
 * '<S2>'   : 'IonPhaseMgm/IonPhaseMgm_Scheduler'
 * '<S3>'   : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis'
 * '<S4>'   : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation'
 * '<S5>'   : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Windows_Evaluation'
 * '<S6>'   : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search'
 * '<S7>'   : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/InitDiagnosis'
 * '<S8>'   : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/ResetDiagnosis'
 * '<S9>'   : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/SetDiagnosis'
 * '<S10>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/ResetDiagnosis/Reset'
 * '<S11>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/ResetDiagnosis/Reset/ResetOneDiag'
 * '<S12>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/SetDiagnosis/CH_START_FOUND_case'
 * '<S13>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/SetDiagnosis/EDGE_FOUND_case'
 * '<S14>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/SetDiagnosis/FIRST_PEAK_FOUND_case'
 * '<S15>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/SetDiagnosis/START_SEARCH_case'
 * '<S16>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/SetDiagnosis/SetDiagnosis'
 * '<S17>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/SetDiagnosis/TH_START_FOUND_case'
 * '<S18>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Diagnosis/SetDiagnosis/SetDiagnosis/SetDiagState'
 * '<S19>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/InitPhasesParam_PowerOn'
 * '<S20>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA'
 * '<S21>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/Subsystem1'
 * '<S22>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Angular_Normalization'
 * '<S23>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Chemical_Phase_Duration'
 * '<S24>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Common_PreLookUp'
 * '<S25>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Delay_Evaluation'
 * '<S26>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Angular_Normalization/RescalSignedIntRightShift'
 * '<S27>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Angular_Normalization/RescalSignedIntRightShift1'
 * '<S28>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Chemical_Phase_Duration/ArrangeLookUpOutputLSB'
 * '<S29>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Chemical_Phase_Duration/ArrangeOutputLSB'
 * '<S30>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Chemical_Phase_Duration/Look2D_IR_U8'
 * '<S31>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Chemical_Phase_Duration/Look2D_U8_U16_U16'
 * '<S32>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Chemical_Phase_Duration/LookUp_S8_S16'
 * '<S33>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Chemical_Phase_Duration/PreLookUpIdSearch_U16'
 * '<S34>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Common_PreLookUp/BINARY_SEARCH_U16'
 * '<S35>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Common_PreLookUp/PreLookUpIdSearch_U16'
 * '<S36>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Delay_Evaluation/Look2D_IR_U16'
 * '<S37>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Delay_Evaluation/PreLookUpIdSearch_U16'
 * '<S38>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Parameters_Evaluation/PhasesParam_EOA/Delay_Evaluation/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S39>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Windows_Evaluation/DMA_LatestIndex_EOA'
 * '<S40>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Windows_Evaluation/Init_LatestIndex_PowerOn'
 * '<S41>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Windows_Evaluation/STD_LatestIndex_EOA'
 * '<S42>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phase_Windows_Evaluation/Subsystem1'
 * '<S43>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/Init_Phases_PowerOn'
 * '<S44>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA'
 * '<S45>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/Subsystem1'
 * '<S46>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/PhasesSearch'
 * '<S47>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/TSparkEstimate'
 * '<S48>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/TSparkMedian'
 * '<S49>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/TSparkEstimate/SaturationDynamic'
 * '<S50>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/TSparkMedian/CalculateMedianLength'
 * '<S51>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/TSparkMedian/EvaluateMedian'
 * '<S52>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/TSparkMedian/PrepareLocalBuffer'
 * '<S53>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/TSparkMedian/EvaluateMedian/InsertionSort_U16'
 * '<S54>'  : 'IonPhaseMgm/IonPhaseMgm_FC/Phases_Search/PhasesSearch_EOA/TSparkMedian/EvaluateMedian/InsertionSort_U16/Conversion'
 */

/*-
 * Requirements for '<Root>': IonPhaseMgm
 *
 * Inherited requirements for '<S3>/InitDiagnosis':
 *  1. EISB_FCA6CYL_SW_REQ_1627: Software shall initialize each output produced for the strategy of... (ECU_SW_Requirements#2744)
 *
 * Inherited requirements for '<S3>/ResetDiagnosis':
 *  1. EISB_FCA6CYL_SW_REQ_1737: The test for diagnosis line DIAG_ION_X shall be resetted (i.e. dia... (ECU_SW_Requirements#4211)
 *
 * Inherited requirements for '<S4>/InitPhasesParam_PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_1627: Software shall initialize each output produced for the strategy of... (ECU_SW_Requirements#2744)
 *
 * Inherited requirements for '<S5>/Init_LatestIndex_PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_1627: Software shall initialize each output produced for the strategy of... (ECU_SW_Requirements#2744)
 *
 * Inherited requirements for '<S6>/Init_Phases_PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_1627: Software shall initialize each output produced for the strategy of... (ECU_SW_Requirements#2744)

 */
#endif                                 /* RTW_HEADER_IonPhaseMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/