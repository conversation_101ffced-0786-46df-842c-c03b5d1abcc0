/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef _TASKDEFS_H_
#define _TASKDEFS_H_

#pragma ghs startnomisra

#include "os_api.h"

/*  Task functions declaration */
DeclareTask(Task1ms);
DeclareTask(Task5ms);
DeclareTask(Task10ms);
DeclareTask(Task100ms);
DeclareTask(TaskKeyOff);
#if defined (_BUILD_SPI_) && defined (_BUILD_SPIMGM_)
DeclareTask(SPI_ExTxDoneChB_PCS0);
DeclareTask(SPI_ExTxDoneChB_PCS1);
DeclareTask(SPI_ExTxDoneChB_PCS2);
DeclareTask(SPI_ExTxDoneChB_PCS3);
DeclareTask(SPI_ExTxDoneChB_PCS4);
DeclareTask(SPI_ExTxDoneChB_PCS5);
DeclareTask(SPI_ExTxDoneChC_PCS0);
DeclareTask(SPI_ExTxDoneChC_PCS1);
DeclareTask(SPI_ExTxDoneChC_PCS2);
DeclareTask(SPI_ExTxDoneChC_PCS3);
DeclareTask(SPI_ExTxDoneChC_PCS4);
DeclareTask(SPI_ExTxDoneChC_PCS5);
#endif
DeclareTask(BackgroundTask);
/* CAN Tasks under protection with ifdef for Task identifiers */
DeclareTask(CAN_ExRxDoneChA);
DeclareTask(CAN_ExTxDoneChA);
DeclareTask(CAN_ExRxDoneChB);
DeclareTask(CAN_ExTxDoneChB);
DeclareTask(CAN_ExRxDoneChC);
DeclareTask(CAN_ExTxDoneChC);
#ifdef _BUILD_DIAGCANMGM_
DeclareTask(TaskWaitForReset);
#endif
DeclareTask(TaskSync);
DeclareTask(TaskNoSync);
DeclareTask(TaskAngle);
DeclareTask(TaskTDC);
DeclareTask(TaskHTDC);
DeclareTask(TaskPreTDC);
DeclareTask(TaskPreHTDC);
DeclareTask(TaskINJ_PRG);
DeclareTask(INJCMD_Ex_Cyl_0);
#ifdef _BUILD_IGN_
DeclareTask(TaskSparkOn_0);
DeclareTask(TaskSparkOff_0);
DeclareTask(TaskSparkOn_1);
DeclareTask(TaskSparkOff_1);
DeclareTask(TaskSparkOn_2);
DeclareTask(TaskSparkOff_2);
DeclareTask(TaskSparkOn_3);
DeclareTask(TaskSparkOff_3);
DeclareTask(TaskSparkOn_4);
DeclareTask(TaskSparkOff_4);
DeclareTask(TaskSparkOn_5);
DeclareTask(TaskSparkOff_5);
DeclareTask(TaskSparkOn_6);
DeclareTask(TaskSparkOff_6);
DeclareTask(TaskSparkOn_7);
DeclareTask(TaskSparkOff_7);
#if 0
DeclareTask(TaskSparkMon_1);
DeclareTask(TaskSparkMon_2);
#endif
DeclareTask(TaskEnBuck_1);
DeclareTask(TaskEnBuck_2);
#endif
#ifdef _BUILD_IONACQ_
DeclareTask(TaskEOA_A);
DeclareTask(TaskEOA_B);
DeclareTask(TaskEOA_C);
DeclareTask(TaskEOA_D);
#endif
#ifdef _BUILD_EXT_WDT_
DeclareTask(Task_RestoreExtWDTServiceGPIO);
#endif

#ifdef _BUILD_ACTIVE_DIAG_
DeclareTask(TaskIgnStop);
#endif

DeclareTask(TaskSparkEv);

/*  Task identifiers  */
enum TaskID {
    Task5msID,
    Task10msID,
    Task100msID,    
#pragma ghs startnomisra
#if((CAN_CHA_BUF0_RX_EXC!=0) || \
    (CAN_CHA_BUF1_RX_EXC!=0) || \
    CAN_CHA_BUF2_RX_EXC || \
    CAN_CHA_BUF3_RX_EXC || \
    CAN_CHA_BUF4_RX_EXC || \
    CAN_CHA_BUF5_RX_EXC || \
    CAN_CHA_BUF6_RX_EXC || \
    CAN_CHA_BUF7_RX_EXC || \
    CAN_CHA_BUF8_RX_EXC || \
    CAN_CHA_BUF9_RX_EXC || \
    CAN_CHA_BUF10_RX_EXC || \
    CAN_CHA_BUF11_RX_EXC || \
    CAN_CHA_BUF12_RX_EXC || \
    CAN_CHA_BUF13_RX_EXC || \
    CAN_CHA_BUF14_RX_EXC || \
    CAN_CHA_BUF15_RX_EXC || \
    CAN_CHA_BUF16_RX_EXC || \
    CAN_CHA_BUF17_RX_EXC || \
    CAN_CHA_BUF18_RX_EXC || \
    CAN_CHA_BUF19_RX_EXC || \
    CAN_CHA_BUF20_RX_EXC || \
    CAN_CHA_BUF21_RX_EXC || \
    CAN_CHA_BUF22_RX_EXC || \
    CAN_CHA_BUF23_RX_EXC || \
    CAN_CHA_BUF24_RX_EXC || \
    CAN_CHA_BUF25_RX_EXC || \
    CAN_CHA_BUF26_RX_EXC || \
    CAN_CHA_BUF27_RX_EXC || \
    CAN_CHA_BUF28_RX_EXC || \
    CAN_CHA_BUF29_RX_EXC || \
    CAN_CHA_BUF30_RX_EXC || \
    CAN_CHA_BUF31_RX_EXC || \
    CAN_CHA_BUF32_RX_EXC || \
    CAN_CHA_BUF33_RX_EXC || \
    CAN_CHA_BUF34_RX_EXC || \
    CAN_CHA_BUF35_RX_EXC || \
    CAN_CHA_BUF36_RX_EXC || \
    CAN_CHA_BUF37_RX_EXC || \
    CAN_CHA_BUF38_RX_EXC || \
    CAN_CHA_BUF39_RX_EXC || \
    CAN_CHA_BUF40_RX_EXC || \
    CAN_CHA_BUF41_RX_EXC || \
    CAN_CHA_BUF42_RX_EXC || \
    CAN_CHA_BUF43_RX_EXC || \
    CAN_CHA_BUF43_RX_EXC || \
    CAN_CHA_BUF44_RX_EXC || \
    CAN_CHA_BUF45_RX_EXC || \
    CAN_CHA_BUF46_RX_EXC || \
    CAN_CHA_BUF47_RX_EXC || \
    CAN_CHA_BUF48_RX_EXC || \
    CAN_CHA_BUF49_RX_EXC || \
    CAN_CHA_BUF50_RX_EXC || \
    CAN_CHA_BUF51_RX_EXC || \
    CAN_CHA_BUF52_RX_EXC || \
    CAN_CHA_BUF53_RX_EXC || \
    CAN_CHA_BUF54_RX_EXC || \
    CAN_CHA_BUF55_RX_EXC || \
    CAN_CHA_BUF56_RX_EXC || \
    CAN_CHA_BUF57_RX_EXC || \
    CAN_CHA_BUF58_RX_EXC || \
    CAN_CHA_BUF59_RX_EXC || \
    CAN_CHA_BUF60_RX_EXC || \
    CAN_CHA_BUF61_RX_EXC || \
    CAN_CHA_BUF62_RX_EXC || \
    CAN_CHA_BUF63_RX_EXC )
    CAN_ExRxDoneChAID,
    CAN_ExTxDoneChAID,
#endif
#if((CAN_CHB_BUF0_RX_EXC!=0) || \
    (CAN_CHB_BUF1_RX_EXC!=0) || \
    CAN_CHB_BUF2_RX_EXC || \
    CAN_CHB_BUF3_RX_EXC || \
    CAN_CHB_BUF4_RX_EXC || \
    CAN_CHB_BUF5_RX_EXC || \
    CAN_CHB_BUF6_RX_EXC || \
    CAN_CHB_BUF7_RX_EXC || \
    CAN_CHB_BUF8_RX_EXC || \
    CAN_CHB_BUF9_RX_EXC || \
    CAN_CHB_BUF10_RX_EXC || \
    CAN_CHB_BUF11_RX_EXC || \
    CAN_CHB_BUF12_RX_EXC || \
    CAN_CHB_BUF13_RX_EXC || \
    CAN_CHB_BUF14_RX_EXC || \
    CAN_CHB_BUF15_RX_EXC || \
    CAN_CHB_BUF16_RX_EXC || \
    CAN_CHB_BUF17_RX_EXC || \
    CAN_CHB_BUF18_RX_EXC || \
    CAN_CHB_BUF19_RX_EXC || \
    CAN_CHB_BUF20_RX_EXC || \
    CAN_CHB_BUF21_RX_EXC || \
    CAN_CHB_BUF22_RX_EXC || \
    CAN_CHB_BUF23_RX_EXC || \
    CAN_CHB_BUF24_RX_EXC || \
    CAN_CHB_BUF25_RX_EXC || \
    CAN_CHB_BUF26_RX_EXC || \
    CAN_CHB_BUF27_RX_EXC || \
    CAN_CHB_BUF28_RX_EXC || \
    CAN_CHB_BUF29_RX_EXC || \
    CAN_CHB_BUF30_RX_EXC || \
    CAN_CHB_BUF31_RX_EXC || \
    CAN_CHB_BUF32_RX_EXC || \
    CAN_CHB_BUF33_RX_EXC || \
    CAN_CHB_BUF34_RX_EXC || \
    CAN_CHB_BUF35_RX_EXC || \
    CAN_CHB_BUF36_RX_EXC || \
    CAN_CHB_BUF37_RX_EXC || \
    CAN_CHB_BUF38_RX_EXC || \
    CAN_CHB_BUF39_RX_EXC || \
    CAN_CHB_BUF40_RX_EXC || \
    CAN_CHB_BUF41_RX_EXC || \
    CAN_CHB_BUF42_RX_EXC || \
    CAN_CHB_BUF43_RX_EXC || \
    CAN_CHB_BUF43_RX_EXC || \
    CAN_CHB_BUF44_RX_EXC || \
    CAN_CHB_BUF45_RX_EXC || \
    CAN_CHB_BUF46_RX_EXC || \
    CAN_CHB_BUF47_RX_EXC || \
    CAN_CHB_BUF48_RX_EXC || \
    CAN_CHB_BUF49_RX_EXC || \
    CAN_CHB_BUF50_RX_EXC || \
    CAN_CHB_BUF51_RX_EXC || \
    CAN_CHB_BUF52_RX_EXC || \
    CAN_CHB_BUF53_RX_EXC || \
    CAN_CHB_BUF54_RX_EXC || \
    CAN_CHB_BUF55_RX_EXC || \
    CAN_CHB_BUF56_RX_EXC || \
    CAN_CHB_BUF57_RX_EXC || \
    CAN_CHB_BUF58_RX_EXC || \
    CAN_CHB_BUF59_RX_EXC || \
    CAN_CHB_BUF60_RX_EXC || \
    CAN_CHB_BUF61_RX_EXC || \
    CAN_CHB_BUF62_RX_EXC || \
    CAN_CHB_BUF63_RX_EXC )
    CAN_ExRxDoneChBID,
    CAN_ExTxDoneChBID,
#endif

#if(CAN_CHC_BUF0_RX_EXC || \
    CAN_CHC_BUF1_RX_EXC || \
    CAN_CHC_BUF2_RX_EXC || \
    CAN_CHC_BUF3_RX_EXC || \
    CAN_CHC_BUF4_RX_EXC || \
    CAN_CHC_BUF5_RX_EXC || \
    CAN_CHC_BUF6_RX_EXC || \
    CAN_CHC_BUF7_RX_EXC || \
    CAN_CHC_BUF8_RX_EXC || \
    CAN_CHC_BUF9_RX_EXC || \
    CAN_CHC_BUF10_RX_EXC || \
    CAN_CHC_BUF11_RX_EXC || \
    CAN_CHC_BUF12_RX_EXC || \
    CAN_CHC_BUF13_RX_EXC || \
    CAN_CHC_BUF14_RX_EXC || \
    CAN_CHC_BUF15_RX_EXC || \
    CAN_CHC_BUF16_RX_EXC || \
    CAN_CHC_BUF17_RX_EXC || \
    CAN_CHC_BUF18_RX_EXC || \
    CAN_CHC_BUF19_RX_EXC || \
    CAN_CHC_BUF20_RX_EXC || \
    CAN_CHC_BUF21_RX_EXC || \
    CAN_CHC_BUF22_RX_EXC || \
    CAN_CHC_BUF23_RX_EXC || \
    CAN_CHC_BUF24_RX_EXC || \
    CAN_CHC_BUF25_RX_EXC || \
    CAN_CHC_BUF26_RX_EXC || \
    CAN_CHC_BUF27_RX_EXC || \
    CAN_CHC_BUF28_RX_EXC || \
    CAN_CHC_BUF29_RX_EXC || \
    CAN_CHC_BUF30_RX_EXC || \
    CAN_CHC_BUF31_RX_EXC )
    CAN_ExRxDoneChCID,
    CAN_ExTxDoneChCID,
#endif
#pragma ghs endnomisra
    TaskKeyOffID,
#if defined (_BUILD_SPI_) && defined (_BUILD_SPIMGM_)
    SPI_ExTxDoneChB_PCS0ID,
    SPI_ExTxDoneChB_PCS1ID,
    SPI_ExTxDoneChB_PCS2ID,
    SPI_ExTxDoneChB_PCS3ID,
    SPI_ExTxDoneChB_PCS4ID,
    SPI_ExTxDoneChB_PCS5ID,
    SPI_ExTxDoneChC_PCS0ID,
    SPI_ExTxDoneChC_PCS1ID,
    SPI_ExTxDoneChC_PCS2ID,
    SPI_ExTxDoneChC_PCS3ID,
    SPI_ExTxDoneChC_PCS4ID,
    SPI_ExTxDoneChC_PCS5ID,
#endif
    BackgroundTaskID,
#ifdef _BUILD_DIAGCANMGM_
    TaskWaitForResetID,
#endif
    TaskSyncID,
    TaskNoSyncID,
    TaskAngleID,
    TaskTDCID,
    TaskHTDCID,
    TaskPreTDCID,
    TaskPreHTDCID,
    TaskINJ_PRGID,
#ifdef _BUILD_IGN_
    SparkTOnID0,
    SparkAOffID0,
    SparkTOnID1,
    SparkAOffID1,
    SparkTOnID2,
    SparkAOffID2,
    SparkTOnID3,
    SparkAOffID3,
    SparkTOnID4,
    SparkAOffID4,
    SparkTOnID5,
    SparkAOffID5,
    SparkTOnID6,
    SparkAOffID6,
    SparkTOnID7,
    SparkAOffID7,
#if 0
    SparkMonID1,
    SparkMonID2,
#endif
    SparkEnBuckID0,
    SparkEnBuckID1,
#endif
#ifdef _BUILD_IONACQ_
    TaskEOAID_A,
    TaskEOAID_B,
    TaskEOAID_C,
    TaskEOAID_D,
#endif
#ifdef _BUILD_EXT_WDT_
    WDT_PwmOff_ID,
#endif
#ifdef _BUILD_ACTIVE_DIAG_
    TaskIgnStop_ID,
#endif
    Task1msID,
    TaskSparkEvID,
    MAX_NUM_TASK
};

#pragma ghs endnomisra

#endif
