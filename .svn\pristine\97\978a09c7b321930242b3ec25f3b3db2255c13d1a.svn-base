/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  STM
**  Filename        :  STM_out.h
**  Created on      :  09-Feb-2022 11:00:00
**  Original author :  MocciA
******************************************************************************/
/*****************************************************************************
**
**                        STM Description
**
**   This SWC implements both module and channels configurations of the STM
**   (System Timer Module) configurations.
**   At the moment, only STM_0 engine is configurable
******************************************************************************/

#ifndef _STM_OUT_H_
#define _STM_OUT_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Rtwtypes.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/// STM channel 0 identifier
#define STM_CH0     0u
/// STM channel 1 identifier
#define STM_CH1     1u
/// STM channel 2 identifier
#define STM_CH2     2u
/// STM channel 3 identifier
#define STM_CH3     3u

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint16_T STM_ConfigStatus;
extern uint16_T STM_EnableStatus;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : STM_Config
**
**   Description:
**    Performs configuration of STM modules enabled
**    Called at startup.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - configuration correctly executed
**    PERIPHERAL_ALREADY_CONFIGURED - peripheral was already configured
**
******************************************************************************/
extern int16_T STM_Config(void);

/******************************************************************************
**   Function    : STM_Enable
**
**   Description:
**    Performs configuration of STM modules enabled
**    Called at startup.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - configuration correctly executed
**    PERIPHERAL_NOT_CONFIGURED - peripheral was not configured
**
******************************************************************************/
extern int16_T STM_Enable(void);

/***********************************************************************************/
/* Interrupt manager functions                                                     */
/***********************************************************************************/
/******************************************************************************
**   Function    : STM_CH0_Isr36
**
**   Description:
**    Interrupt request STM channel 0
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void STM_CH0_Isr36(void);

/******************************************************************************
**   Function    : STM_CH1_Isr37
**
**   Description:
**    Interrupt request STM channel 1
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void STM_CH1_Isr37(void);

/******************************************************************************
**   Function    : STM_CH2_Isr38
**
**   Description:
**    Interrupt request STM channel 2
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void STM_CH2_Isr38(void);

/******************************************************************************
**   Function    : STM_CH3_Isr39
**
**   Description:
**    Interrupt request STM channel 3
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void STM_CH3_Isr39(void);


#endif // _STM_OUT_H_

/****************************************************************************
 ****************************************************************************/

