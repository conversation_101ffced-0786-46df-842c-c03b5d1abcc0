@echo off

rem echo %CD%



set BIN_PATH=%CD%\Bin\%1

echo %BIN_PATH%

cd ..
set PRJ_PATH=%CD%
echo %PRJ_PATH%

set NAME=%1

set FLAG_TEST="0"

if not x%NAME:_test=%==x%NAME% set FLAG_TEST="1"

set FLAG_EVB="0"

if not x%NAME:_EVB=%==x%NAME% set FLAG_EVB="1"
rem echo %FLAG_EVB%

set NAME_FILE=%NAME%.elf
echo %NAME_FILE%

set CALIB_NAME_FILE=Calib_%NAME%
set UPDATE_CALIB_NAME_FILE=%CALIB_NAME_FILE%.bin
set ELF_FILE="%BIN_PATH%\%NAME_FILE%"


set ELF_FILE=%ELF_FILE:"=%
set ELF_FILE="%ELF_FILE%"

set MAP_FILE=%ELF_FILE:.elf=.MAP% 

set BIN_FILE=%ELF_FILE:.elf=.bin%

set APPL_BIN_FILE=%ELF_FILE:.elf=-appl.bin%
set UPDATE_APPL_NAME_FILE=%NAME%.bin
set CALIB_BIN_FILE=%ELF_FILE:.elf=-calib.bin%
set BOOT_BIN_FILE=%APPL_BIN_FILE:-appl=-boot%
set UNIQUE_BIN_FILE=%ELF_FILE:.elf=-unique.bin%
set APPL_FILE_TAG=%NAME%-appl_Tag.bin

set APPL_VALID_TAG_IN=%NAME%-appl.bin  
set APPL_VALID_TAG_OUT=%NAME%-appl_trusted.bin  

set MERGE_BIN_FILE=%ELF_FILE:.elf=-merge.bin%

set HEX_FILE=%ELF_FILE:.elf=.hex%

set VST_FILE=%ELF_FILE:.elf=.vst%

if exist %BIN_FILE% del %BIN_FILE% /Q

if exist %HEX_FILE% del %HEX_FILE% /Q

if exist %VST_FILE% del %VST_FILE% /Q

if exist %APPL_BIN_FILE% del %APPL_BIN_FILE% /Q
if exist %MERGE_BIN_FILE% del %MERGE_BIN_FILE% /Q
if exist %UNIQUE_BIN_FILE% del %UNIQUE_BIN_FILE% /Q
if exist %BIN_PATH%\out.map del %BIN_PATH%\out.map /Q

cd "%BIN_PATH%\.."

rem Import dei valori di configurazione
call Cfg_BatchRunnerPostLinker.bat

echo %RAM_SIZE_TOTAL%
echo %APP_END%
echo %APP_START%
echo %APP_START_HEX%
echo %APP_TAG_START%
echo %APP_TAG_CHKSUM_ADDR%
echo %CALIB_ROM_START%
echo %CALIB_ROM_START_HEX%
echo %CALIB_TAG_START%
echo %CALIB_TAG_CHKSUM_ADDR%
echo %GMEM_END_HEX%
echo %OUTPUT_EXTENSION%
echo %VMEMORY_USAGE%
echo %MERGE_APPL_AND_CALIB%
echo %COMPILER_IN_USE%
echo %UPDATE_CALIB_BUILDCODE%
echo %APP_TAG_APPVERS_ADDR%
echo %CALIB_ROM_APPCHKSUM%
echo %ENABLE_MISRA_ANALYSIS%
echo %APP_TAG_START_IN_APPLBINFILE%

rem VALUES TO CONFIGURE IN Cfg_BatchRunnerPostLinker.bat for new Batch file
REM set MERGE_APPL_AND_CALIB="NO"
REM set OUTPUT_EXTENSION="hex"
REM set COMPILER_IN_USE="1.7"

rem DEFAULT VALUES for non specified options in Cfg_BatchRunnerPostLinker.bat
if "%MERGE_APPL_AND_CALIB%"=="" (
set MERGE_APPL_AND_CALIB="NO"
echo Dafault value was used for MERGE_APPL_AND_CALIB=NO
)
if "%OUTPUT_EXTENSION%"=="" (
set OUTPUT_EXTENSION="hex"
echo Dafault value was used for OUTPUT_EXTENSION=hex
)
if "%COMPILER_IN_USE%"=="" (
set COMPILER_IN_USE="1.7"
echo Dafault value was used for COMPILER_IN_USE=1.7
) else (
echo COMPILER_IN_USE=%COMPILER_IN_USE%
)

rem .elf to .bin file conversion

IF %COMPILER_IN_USE%=="1.6" (
    rem "C:\ghs\multi516\gmemfile" %ELF_FILE% -fill1 0 .app_tag 0xFF -fill1 %APP_END_HEX% %GMEM_END_HEX% 0xFF -size %GMEM_END% -o %BIN_FILE%
    "C:\ghs\multi516\gmemfile" %ELF_FILE% -fill1 0 .app_tag 0xFF -size %APP_END% -o %BIN_FILE%
) ELSE (
    "C:\ghs\multi517\gmemfile" %ELF_FILE% -fill1 0 .app_tag 0xFF -size %APP_END% -o %BIN_FILE%
)


echo Updating memory regions checksum
rem APP
"%PRJ_PATH%\GHS\bin\checksum_calc" %BIN_FILE% %APP_START% %APP_TAG_START% %APP_TAG_CHKSUM_ADDR%

rem CALIB
IF %UPDATE_CALIB_BUILDCODE%=="1"  (
  "%PRJ_PATH%\GHS\bin\binmerge" -i %BIN_FILE% -s %APP_TAG_APPVERS_ADDR% -l 11 -f %BIN_FILE% -p %CALIB_ROM_START%
  "%PRJ_PATH%\GHS\bin\binmerge" -i %BIN_FILE% -s %APP_TAG_CHKSUM_ADDR%  -l  4 -f %BIN_FILE% -p %CALIB_ROM_APPCHKSUM%
)

IF %UPDATE_APPLTAGEPK%=="1"  (
rem Updating appl TAG calibration INCAAPPLID 
"%PRJ_PATH%\GHS\bin\AppTagEPK.exe" %BIN_FILE%  %APP_END%  %CALIB_ROM_START%
)

"%PRJ_PATH%\GHS\bin\checksum_calc" %BIN_FILE% %CALIB_ROM_START% %CALIB_TAG_START% %CALIB_TAG_CHKSUM_ADDR%

set /a APP_LENGTH = %APP_END% - %APP_START%

rem verifying that there is a merging condition
set active_merge = FALSE
IF %MERGE_APPL_AND_CALIB%=="YES" set active_merge=TRUE
IF %MERGE_APPL_AND_CALIB%=="YES-i" set active_merge=TRUE

rem Binaries cutting 
IF "%active_merge%"=="TRUE" (
    REM "%PRJ_PATH%\GHS\bin\acut" -e %APP_START% -l 147456 %BIN_FILE% %APPL_BIN_FILE%  used for 5601
    "%PRJ_PATH%\GHS\bin\acut" -e %APP_START% -l %APP_LENGTH% %BIN_FILE% %APPL_BIN_FILE% 
    "%PRJ_PATH%\GHS\bin\acut" -e %CALIB_ROM_START% -l %CALIB_ROM_LENGTH% %BIN_FILE% %CALIB_BIN_FILE%
) ELSE (
            "%PRJ_PATH%\GHS\bin\acut" -e %CALIB_ROM_START% %BIN_FILE% %APPL_BIN_FILE%
            )

IF %UPDATE_APPLTAGEPK%=="1"  (
rem Cutting appl tag    
"%PRJ_PATH%\GHS\bin\acut" -e %APP_TAG_START_IN_APPLBINFILE% %APPL_BIN_FILE% %APPL_FILE_TAG%
)

cd %BIN_PATH%



rem Validating TAG, if needed...
IF %VALIDATE_TAG%=="1"  (
echo VALIDATING application tag
"%PRJ_PATH%\GHS\bin\ValidateTag.exe" %APPL_BIN_FILE%  %APPL_VALID_TAG_OUT%
)

copy %APPL_VALID_TAG_OUT% appl.bin

rem Producing SRECORD files
IF "%active_merge%"=="TRUE" (
    copy %CALIB_BIN_FILE% calib.bin
    rem Merge two hex for cropping the unused addresses of the s32 file
    "%PRJ_PATH%\GHS\bin\bin2srec" q -a 4 -o %APP_START_HEX% appl.bin > %NAME%-appl2merge.%OUTPUT_EXTENSION%
    "%PRJ_PATH%\GHS\bin\bin2srec" q -a 4 -o %CALIB_ROM_START_HEX% calib.bin > %NAME%-calib2merge.%OUTPUT_EXTENSION%
    perl %PRJ_PATH%\GHS\bin\PostProcessSRecMot.pl  -vmemory %VMEMORY_USAGE% -mergeSrec %MERGE_APPL_AND_CALIB% -extension %OUTPUT_EXTENSION% -multiCalibSect %NUM_OF_ADDITIONAL_CALIB_SECT% %MULTI_CALIB_SECTION%
    
    rem Aggiorna il crc del file esadecimale di applicativo non mergiato (no CALIB RAM) in uscita dal Post Processing
    "%PRJ_PATH%\GHS\bin\s19crc.exe"  %PRJ_PATH%\GHS\bin\%NAME%\%NAME%-appl.%OUTPUT_EXTENSION%
) ELSE (
            "%PRJ_PATH%\GHS\bin\bin2srec" q -a 4 -o %CALIB_ROM_START_HEX% appl.bin > %NAME%-appl.%OUTPUT_EXTENSION%
            perl %PRJ_PATH%\GHS\bin\PostProcessSRecMot.pl  -vmemory %VMEMORY_USAGE% -mergeSrec NO -extension %OUTPUT_EXTENSION% -multiCalibSect %NUM_OF_ADDITIONAL_CALIB_SECT% %MULTI_CALIB_SECTION%
            )


rem Aggiorna il crc del file esadecimale "mergiato" dal post Process
"%PRJ_PATH%\GHS\bin\s19crc.exe"  %PRJ_PATH%\GHS\bin\%NAME%\*_merged.%OUTPUT_EXTENSION%

rem pause

rem Deleting temporary files
if exist appl.bin del appl.bin /Q
if exist calib.bin del calib.bin /Q
if exist %NAME%-appl2merge.%OUTPUT_EXTENSION% del %NAME%-appl2merge.%OUTPUT_EXTENSION% /Q
if exist %NAME%-calib2merge.%OUTPUT_EXTENSION% del %NAME%-calib2merge.%OUTPUT_EXTENSION% /Q
 
rem Unisce i vari file .a2l in uno singolo
IF %FLAG_TEST%=="1"  (
    perl "%PRJ_PATH%\GHS\bin\A2lUnion.pl" -format GENERAL_TEST -target %NAME% -vmemory %VMEMORY_USAGE%
    set A2L_LOG_FILE=a2logGeneral_test
) ELSE (
    perl "%PRJ_PATH%\GHS\bin\A2lUnion.pl" -format GENERAL      -target %NAME% -vmemory %VMEMORY_USAGE%
    set A2L_LOG_FILE=a2log
) 

IF %FLAG_EVB%=="1"  (
    set A2L_LOG_FILE=%A2L_LOG_FILE%_EVB
)

rem pause
echo %A2L_LOG_FILE%

echo Extract symbols information from ELF file
"%PRJ_PATH%\GHS\bin\elf" %ELF_FILE%

set EXTRA_MAP_FILE="%BIN_PATH%\out.map" 

rem Aggiorna gli indirizzi del file a2l totale
rem ..\PostLinking\INCAUpdate.exe     %BIN_PATH%\*.map     %PRJ_PATH%\AAU\%NAME%_inca.a2l      %BIN_PATH%\%NAME%_inca.a2l  %PRJ_PATH%\AAU\a2logInca.txt
rem ..\PostLinking\INCAUpdate.exe     %BIN_PATH%\*.map     %PRJ_PATH%\AAU\%NAME%_ati.a2l       %BIN_PATH%\%NAME%_ati.a2l   %PRJ_PATH%\AAU\a2logAti.txt

set NAME1=%NAME:~0,5%
set NAME2=%NAME1%D
echo %NAME2%
%PRJ_PATH%\GHS\bin\INCAUpdate.exe     %BIN_PATH%\out.map     %PRJ_PATH%\AAU\%NAME%_no_addr.a2l  %BIN_PATH%\%NAME2%.a2l       %BIN_PATH%\%NAME2%_%A2L_LOG_FILE%.txt -format GENERAL -vmemory %VMEMORY_USAGE% -compiler %COMPILER_IN_USE% -updateEPK %UPDATE_APPLTAGEPK% %PRJ_PATH%\GHS\bin\%APPL_FILE_TAG%
copy %BIN_PATH%\%NAME2%.a2l %BIN_PATH%\%NAME%.a2l
copy %BIN_PATH%\%NAME2%_%A2L_LOG_FILE%.txt %BIN_PATH%\%NAME%_%A2L_LOG_FILE%.txt

IF %NAME%==%NAME2% (
	echo Target D
	rem Deleting temporary files
	if exist %PRJ_PATH%\GHS\bin\%APPL_FILE_TAG% del %PRJ_PATH%\GHS\bin\%APPL_FILE_TAG% /Q
) ELSE (
	echo Target R
	rem Deleting temporary files
	if exist %PRJ_PATH%\GHS\bin\%APPL_FILE_TAG% del %PRJ_PATH%\GHS\bin\%APPL_FILE_TAG% /Q
	if exist %BIN_PATH%\%NAME2%.a2l del %BIN_PATH%\%NAME2%.a2l /Q
	if exist %BIN_PATH%\%NAME2%_%A2L_LOG_FILE%.txt del %BIN_PATH%\%NAME2%_%A2L_LOG_FILE%.txt /Q
)

rem Analizza il numero di errori MISRA e valuta le regressioni
IF %ENABLE_MISRA_ANALYSIS%=="1"  ( 
  copy %NAME%_ErrorsWarnings_tmp.txt %NAME%_ErrorsWarnings.txt
  del %NAME%_ErrorsWarnings_tmp.txt
 rem pause
  cd %PRJ_PATH%\GHS\bin\
  START perl MisraAnalysis.pl
)

rem Valuta l'utilizzo della memoria
cd %BIN_PATH%
echo Wait PROFILING Running....
"%PRJ_PATH%\GHS\bin\Profiling"  %NAME% %RAM_SIZE_TOTAL% -vmemory %VMEMORY_USAGE%  -appEndAdd  %APP_END_HEX% -compiler 1.7 -corememory %COREMEMORY% %DMEM0_Z4_TOTAL_SIZE_PARAMETER% %DMEM2_Z2_TOTAL_SIZE_PARAMETER% %IMEM0_Z4_TOTAL_SIZE_PARAMETER% %IMEM2_Z2_TOTAL_SIZE_PARAMETER%  rem A.M. manually set because COMPILER_IN_USE var is used in other point of the batch 
echo PROFILING CREATED see log_profiling.txt

rem Validating TAG, if needed...
rem IF %VALIDATE_TAG%=="1"  (
rem echo VALIDATING application tag
rem "%PRJ_PATH%\GHS\bin\ValidateTag.exe" %APPL_VALID_TAG_IN%  %APPL_VALID_TAG_OUT%
rem )
 
if %BUILD_KWP2_KIT%=="1"  (
echo UPDATE Diagnostic Kit FILES 

if not exist "%PRJ_PATH%\DiagKit\CAN\%NAME%" echo "Dir di progetto su DiagKit %PRJ_PATH%\DiagKit\CAN\%NAME% Mancante"

if exist "%PRJ_PATH%\DiagKit\CAN\%NAME%\%BIN_FILE%" del "%PRJ_PATH%\DiagKit\CAN\%NAME%\%BIN_FILE%"

if exist "%PRJ_PATH%\DiagKit\CAN\%NAME%" copy %APPL_BIN_FILE% "%PRJ_PATH%\DiagKit\CAN\%NAME%"

if exist "%PRJ_PATH%\DiagKit\CAN\%NAME%" copy %MAP_FILE%      "%PRJ_PATH%\DiagKit\CAN\%NAME%"

if exist "%PRJ_PATH%\DiagKit\CAN\%NAME%" cd "%PRJ_PATH%\DiagKit\CAN\%NAME%"

if exist "%PRJ_PATH%\DiagKit\CAN\%NAME%" call bincrcupdate.bat
)

rem pause