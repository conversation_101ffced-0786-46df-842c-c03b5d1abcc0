/*
 * Trial License - for use to evaluate programs for possible purchase as
 * an end-user only.
 *
 * File: div_nzp_ssu32_floor.h
 *
 * Code generated for Simulink model 'PlaCtrl'.
 *
 * Model version                  : 1.149
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Thu Sep 19 09:27:14 2019
 */

#ifndef SHARE_div_nzp_ssu32_floor
#define SHARE_div_nzp_ssu32_floor
#include "rtwtypes.h"

extern int32_T div_nzp_ssu32_floor(int32_T numerator, uint32_T denominator);

#endif

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
