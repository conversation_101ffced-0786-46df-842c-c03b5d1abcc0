/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB6C/Appl/branches/EISB6C_RS_12_MSPARKCMD/tree/COMM#$  */
/* $Revision:: 155176                                                                                         $  */
/* $Date:: 2021-04-01 11:44:25 +0200 (gio, 01 apr 2021)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MSparkCmd
**  Filename        :  MSparkCmd_out.h
**  Created on      :  31-mar-2021 12:01:00
**  Original author :  SantoroR
******************************************************************************/
/*****************************************************************************
**
**                        MSparkCmd Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef MSPARKCMD_OUT_H
#define MSPARKCMD_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"
#include "IgnHEInterface.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint8_T  NMSparkPrg[N_CYL_MAX];
extern uint8_T  VtThrMisfPlaSel[N_CYL_MAX];
extern uint32_T CntVCapInOn[PMOS_MAX];
extern uint32_T CylPlaMOS_idx;
extern uint32_T CylPlaMOSRes_idx;
extern uint32_T DeltaSparkTime[N_CYL_MAX];
extern uint8_T  VtStPlasObjDBuff[N_CYL_MAX];
extern uint8_T  EffNMSpark[N_CYL_MAX];
extern uint8_T  PtFaultTrigger;
extern uint8_T  FlgWrongIGNIn[N_CYL_MAX];
/* Ignition input command counter */
extern uint32_T CntIGNInOff[N_CYL_MAX];
extern uint32_T CntIGNTrgInOn[N_CYL_MAX];
extern uint16_T VtDelayBkEnEOA[N_CYL_MAX];
extern uint16_T VtDeltaBkEnEOA[N_CYL_MAX];
/* TSpark correction */
extern uint16_T IPriCorrCyl;
/* Cylinder indexes */
extern uint32_T CylPlaAbsOff_idx;
extern int16_T TimeFixedTrigIn;
extern const uint8_T DiagCoil[N_CYL_MAX];
extern const uint8_T DiagPri[N_CYL_MAX];

/* EEPROM */
///EE Max SA Err
extern uint8_T EESACmdInLevErrNoMax;
/* Counter of ETPU errors */
extern uint16_T EESACmdInLevErrSum;

/* STUB */
extern uint8_T  EffNIgnError[N_CYL_MAX];
extern uint8_T FlgDSAoutDis;
extern uint8_T SACmdInLevErrNo;
extern uint8_T  FlgWrongSpark[N_CYL_MAX];
extern uint8_T  VtILPGlitch[N_CYL_MAX];
extern uint8_T SACmdInLevCirBuff[N_CYL_MAX];
extern uint8_T Flg5PreIgnition;
/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
 /******************************************************************************
**   Function    : MSparkCmd_Init
**
**   Description:
**    Initialize Module Global and local Variables
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void MSparkCmd_Init(void);

/******************************************************************************
**   Function    : MSparkCmd_SparkOn
**
**   Description:
**    SparkOn event module funtion Call
**    This functions performs:
**     - Increments global variable CntIGNTrgInOn
**     - Sets global variable CntIGNInTot
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void MSparkCmd_SparkOn(uint8_T cyl);

/******************************************************************************
**   Function    : MSparkCmd_SparkOff
**
**   Description:
**    SparkOn event module funtion Call
**    This functions performs:
**     - Increments global variable CntIGNTrgInOn
**     - Sets global variable CntIGNInTot
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void MSparkCmd_SparkOff(uint8_T cyl);

/******************************************************************************
**   Function    : MSparkCmd_TDC
**
**   Description:
**    All the vectors are indexed using MisfAbsTdc
**    - Manage DIAG_TRIGGER_cyl
**    - Manage DIAG_SPARK_cyl
**    - Manage DIAG_PRI_A/B
**    - Manage FlgWrongIGNIn and PtFaultTrigger
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void MSparkCmd_TDC(void);

/******************************************************************************
**   Function    : MSparkCmd_ISecDma
**
**   Description:
**    - calculates EffNMSpark based on VtIShotPeak
**
**   Parameters :
**    cyl: cylinder index
**
**   Returns:
**    void
**
******************************************************************************/
void MSparkCmd_ISecDma(uint8_T cyl);

/******************************************************************************
**   Function    : MSparkCmd_PostIgnitionParameter
**
**   Description:
**    Calculates: * VtCmdInPinLev
**                * VtDelayBkEnEOA
**                * VtDeltaBkEnEOA
**                * VtDwellTimeEOA
**
**   Parameters :
**    cyl: cylinder index
**
**   Returns:
**    void
**
******************************************************************************/
void MSparkCmd_PostIgnitionParameter(uint8_T cyl);

/******************************************************************************
**   Function    : MSparkCmd_ThrMisfPlaSel
**
**   Description:
**    Calculates: * VtThrMisfPlaSel
**                * NMSparkPrg
**
**   Parameters :
**    cyl: cylinder index
**
**   Returns:
**    void
**
******************************************************************************/
void MSparkCmd_ThrMisfPlaSel(uint8_T cyl);

/******************************************************************************
**   Function    : MSparkCmd_SparkEv
**
**   Description:
**    Performs GTM diagnostic routine
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_SparkEv(void);

/******************************************************************************
**   Function    : MSparkCmd_BKEnLatch
**
**   Description:
**    Latch: * VtDelayBkEn
**           * VtDeltaBkEn
**
**   Parameters :
**    cyl: cylinder index
**
**   Returns:
**    void
**
******************************************************************************/
void MSparkCmd_BKEnLatch(uint8_T cyl);

#endif // MSPARKCMD_OUT_H

/****************************************************************************
 ****************************************************************************/
