/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      MisfThrMgm.h
 **  Date:          20-Jul-2023
 **
 **  Model Version: 1.597
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_MisfThrMgm_h_
#define RTW_HEADER_MisfThrMgm_h_
#ifndef MisfThrMgm_COMMON_INCLUDES_
# define MisfThrMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* MisfThrMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void MisfThrMgm_initialize(void);

/* Exported entry point function */
extern void MisfThrMgm_EOA(void);

/* Exported entry point function */
extern void MisfThrMgm_NoSync(void);

/* Exported entry point function */
extern void MisfThrMgm_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T BadCombThr;            /* '<S2>/Merge2' */

/* Bad Combustion threshold */
extern uint16_T NoCombThr;             /* '<S2>/Merge1' */

/* No combustion threshold */
extern uint16_T ParCombThr;            /* '<S2>/Merge3' */

/* Partial combustion threshold */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S9>/Data Type Duplicate' : Unused code path elimination
 * Block '<S9>/Data Type Propagation' : Unused code path elimination
 * Block '<S16>/Data Type Duplicate' : Unused code path elimination
 * Block '<S13>/Data Type Duplicate' : Unused code path elimination
 * Block '<S13>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S14>/Data Type Duplicate' : Unused code path elimination
 * Block '<S14>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S15>/Data Type Duplicate' : Unused code path elimination
 * Block '<S15>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S15>/Data Type Propagation' : Unused code path elimination
 * Block '<S9>/Conversion' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S8>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S8>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S8>/Data Type Conversion2' : Eliminate redundant data type conversion
 * Block '<S8>/Data Type Conversion3' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S16>/Conversion' : Eliminate redundant data type conversion
 * Block '<S13>/Reshape' : Reshape block reduction
 * Block '<S14>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S14>/Reshape' : Reshape block reduction
 * Block '<S15>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S15>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S15>/Reshape' : Reshape block reduction
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'MisfThrMgm'
 * '<S1>'   : 'MisfThrMgm/Init'
 * '<S2>'   : 'MisfThrMgm/MergeSignals'
 * '<S3>'   : 'MisfThrMgm/calc_EOA'
 * '<S4>'   : 'MisfThrMgm/calc_EOA/Calc_Comp'
 * '<S5>'   : 'MisfThrMgm/calc_EOA/InterpMisfThrTb'
 * '<S6>'   : 'MisfThrMgm/calc_EOA/ThrCalc'
 * '<S7>'   : 'MisfThrMgm/calc_EOA/Calc_Comp/Calc_RonComp'
 * '<S8>'   : 'MisfThrMgm/calc_EOA/Calc_Comp/enable_sparkadv_use'
 * '<S9>'   : 'MisfThrMgm/calc_EOA/Calc_Comp/Calc_RonComp/LookUp_U8_S16'
 * '<S10>'  : 'MisfThrMgm/calc_EOA/Calc_Comp/Calc_RonComp/Turn_On_delay'
 * '<S11>'  : 'MisfThrMgm/calc_EOA/Calc_Comp/enable_sparkadv_use/Compare To Zero'
 * '<S12>'  : 'MisfThrMgm/calc_EOA/InterpMisfThrTb/Compare To Zero'
 * '<S13>'  : 'MisfThrMgm/calc_EOA/InterpMisfThrTb/Look2D_U16_U16_U16_2'
 * '<S14>'  : 'MisfThrMgm/calc_EOA/InterpMisfThrTb/Look2D_U16_U16_U16_3'
 * '<S15>'  : 'MisfThrMgm/calc_EOA/InterpMisfThrTb/Look2D_U8_S16_S16'
 * '<S16>'  : 'MisfThrMgm/calc_EOA/InterpMisfThrTb/Look2D_U16_U16_U16_2/Data Type Conversion Inherited1'
 * '<S17>'  : 'MisfThrMgm/calc_EOA/InterpMisfThrTb/Look2D_U16_U16_U16_3/Data Type Conversion Inherited1'
 */

/*-
 * Requirements for '<Root>': MisfThrMgm
 *
 * Inherited requirements for '<Root>/Init':
 *  1. EISB_FCA6CYL_SW_REQ_1773: The software shall initialize the strategy each PowerOn and loss o... (ECU_SW_Requirements#5742)

 */
#endif                                 /* RTW_HEADER_MisfThrMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * aerospace_blockset                                                         *
 * aerospace_toolbox                                                          *
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/