/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
#ifndef _EE_SCU_H_
#define _EE_SCU_H_

#pragma ghs startnomisra    // 19.4 - Proven in use

#define EE_RECOVERY_ENABLE                       //Enable of EE Recovery
//#define EE_RECOVERY_MEMORY_CHECK_ENABLE     //Enable check of flag EE_flashErrorOnBlock0/1
#define EE_RECOVERY_CONTROL_RECORD               //Enable use of Control Record
#define EE_RECOVERY_DIAG                                   //Enable diagnostic check of recovery results
#undef EE_RECOVERY_EXTERNALIZE_FLUSH                //Externalize EE_Flush

#undef  EE_SERVE_WATCHDOG   //Enable of watchdog management

/* Left shift of event id for padding   */
#define  EE_ID_PAD_LSH  EE_ID_PAD_LSH_APPL

//#define _TEST_EEPROM_

#define EE_BLKDESCR_ALIGN 16  //16 for GHS 7.1.4, 32 for others used GHS

/* EEPROM IDs total number */
#define EE_ID_TOT_NUMBER (12u)

/* EEPROM IDs and version */

#define EE_ID0_USED
#ifdef EE_ID0_USED
#define EE_ID0_VERSION "VEE0"
#endif

#define EE_ID1_USED
#ifdef EE_ID1_USED
#define EE_ID1_VERSION "VEE0"
#endif


#define EE_ID2_USED
#ifdef EE_ID2_USED
#define EE_ID2_VERSION "VEE0"
#endif


#define EE_ID3_USED
#ifdef EE_ID3_USED
#define EE_ID3_VERSION "VEE0"
#endif


#define EE_ID4_USED
#ifdef EE_ID4_USED
#define EE_ID4_VERSION "VEE0"
#endif


#define EE_ID5_USED
#ifdef EE_ID5_USED
#define EE_ID5_VERSION "VEE0"
#endif


#define EE_ID6_USED
#ifdef EE_ID6_USED
#define EE_ID6_VERSION "VEE0"
#endif


#define EE_ID7_USED
#ifdef EE_ID7_USED
#define EE_ID7_VERSION "VEE0"
#endif


#define EE_ID8_USED
#ifdef EE_ID8_USED
#define EE_ID8_VERSION "VEE0"
#endif


#define EE_ID9_USED
#ifdef EE_ID9_USED
#define EE_ID9_VERSION "VEE0"
#endif


#if (EE_ID_TOT_NUMBER > 10)
#define EE_ID10_USED
#ifdef EE_ID10_USED
#define EE_ID10_VERSION "VEE0"
#endif
#endif


#if (EE_ID_TOT_NUMBER > 11)
#define EE_ID11_USED
#ifdef EE_ID11_USED
#define EE_ID11_VERSION "VEE0"
#endif
#endif

#pragma ghs endnomisra

#endif  //_EE_SCU_H_

