/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tag.h
 * @brief   SPC5xxx GTM_TAG.
 *
 * @addtogroup GTM
 * @{
 */

#ifndef _GTM_TAG_H_
#define _GTM_TAG_H_

#include "gtm_cfg.h"

#if 0 //MC
/**
 * @name    Common constants
 * @{
 */
#if !defined(FALSE) || defined(__DOXYGEN__)
#define FALSE                               0U
#endif

#if !defined(TRUE) || defined(__DOXYGEN__)
#define TRUE                                1U
#endif
/** @} */
#endif

#if (SPC5_HAS_GTM_IP_101 == TRUE || SPC5_HAS_GTM_IP_122 == TRUE) || defined (__DOXYGEN__)

#if (SPC5_HAS_GTM_IP_101 == TRUE) || defined (__DOXYGEN__)
#include "SPC572L_GTM.h"
#endif /* SPC5_HAS_GTM_IP_101 */
#if (SPC5_HAS_GTM_IP_122 == TRUE) || defined (__DOXYGEN__)
#include "SPC574K_GTM.h"
#endif /* SPC5_HAS_GTM_IP_122 */

/**
 * @brief   GTM module TAGs
 *
 * @{
 */
#define GTM_TAG                             struct GTM_tag
#define GTM_TBU_TAG                         struct GTM_TBU_tag
#define GTM_MON_TAG                         struct GTM_MON_tag
#define GTM_CMP_TAG                         struct GTM_CMP_tag
#define GTM_ARU_TAG                         struct GTM_ARU_tag
#define GTM_CMU_TAG                         struct GTM_CMU_tag
#define GTM_ICM_TAG                         struct GTM_ICM_tag
#define GTM_SPE_TAG                         struct GTM_SPE_tag
#define GTM_MAP_TAG                         struct GTM_MAP_tag
#define GTM_MCFG_TAG                        struct GTM_MCFG_tag
#define GTM_TIM_TAG                         struct GTM_TIM_tag
#define GTM_TOM_TAG                         struct GTM_TOM_tag
#define GTM_ATOM_TAG                        struct GTM_ATOM_tag
#define GTM_F2A_TAG                         struct GTM_F2A_tag
#define GTM_AFD_TAG                         struct GTM_AFD_tag
#define GTM_FIFO_TAG                        struct GTM_FIFO_tag
#define GTM_DPLL_TAG                        struct GTM_DPLL_tag
#define GTM_MCS_TAG                         struct GTM_MCS_tag
#define GTM_BRC_TAG                         struct GTM_BRC_tag
/** @} */

/**
 * @brief   GTM register macro
 *
 * @{
 */
/**
 * @brief   Gets the value of TIM AUX_IN_SRC registers.
 *
 * @param[in] tim       the number of TIM to which the AUX_IN_SRC register
 *                      is related to.
 * @return              the value of the TIM AUX_IN_SRC register.
 *
 * @notapi
 */
/*lint -e9023 */
#define GTM_GET_TIM_AUX_IN_SRC(tim)                                         \
  (uint32_t)(gtmd->gtm->TIM##tim##_AUX_IN_SRC.R)
/*lint +e9023 */

/**
 * @brief   Sets the value of TIM AUX_IN_SRC registers.
 *
 * @param[in] tim       the number of TIM to which the AUX_IN_SRC register
 *                      is related to.
 * @param[in] val       the value to set in the TIM AUX_IN_SRC register.
 *
 * @notapi
 */
/*lint -e9023 */
#define GTM_SET_TIM_AUX_IN_SRC(tim, val)                                    \
  (gtmd->gtm->TIM##tim##_AUX_IN_SRC.R = val)
/*lint +e9023 */

/**
 * @brief   Wrapper to point to the registers RST and IN_SRC of the TIM.
 *
 * @param[in] reg       register to point (RST, IN_SRC).
 *
 * @notapi
 */
#define GTM_TIM_GC_REG(reg)                                                 \
  reg

/**
 * @brief   Wrapper to point to the TGC registers of the TOM.
 *
 * @param[in] index     TGC register set to point.
 * @param[in] reg       TGC register to point.
 *
 * @notapi
 */
/*lint -e9023 */
#define GTM_TOM_TGC_REG(index, reg)                                         \
  TGC##index##_##reg
/*lint +e9023 */

/**
 * @brief   Wrapper to point to the AGC registers of the ATOM.
 *
 * @param[in] reg       register to point.
 *
 * @notapi
 */
#define GTM_ATOM_AGC_REG(reg)                                           \
  AGC_##reg

/**
 * @brief   Wrapper to point to the channel registers of TIM, TOM and ATOM.
 *
 * @param[in] channel   number of channel related to the channel registers to
 *                      point.
 * @param[in] reg       register to point.
 *
 * @notapi
 */
/*lint -e9023 */
#define GTM_CH_REG(channel, reg)                                            \
  CH##channel##_##reg
/*lint +e9023 */

/**
 * @brief   Sets the value of DPLL ID_PMTR registers.
 *
 * @param[in] index     ID_PMTR to be set.
 * @param[in] value     the value to set in the DPLL ID_PMTR register.
 *
 * @notapi
 */
#define GTM_SET_DPLL_ID_PMTR(index, value)                                  \
  (dplld->dpll->DPLL_ID_PMTR[index].R = value)

/**
 * @brief   Wrapper to point to the registers RST of the MCS.
 *
 * @notapi
 */
#define GTM_MCS_GC_CTRL_REG                                                 \
  CTRL

/**
 * @brief   Wrapper to point to the MCS Scheduling Mode bit within the MCS
 *          CTRL_STAT registers.
 *
 * @notapi
 */
#define GTM_MCS_GC_CTRL_SCD_MODE_BIT                                        \
  SCHED

/**
 * @brief   Wrapper to point to the registers RST of the MCS.
 *
 * @notapi
 */
#define GTM_MCS_GC_RST_REG                                                  \
  RST

/**
 * @brief   Wrapper to point to the registers CTRG, STRG and ERR of the MCS.
 *
 * @param[in] reg       register to point (CTRG, STRG, ERR).
 *
 * @notapi
 */
#define GTM_MCS_GC_REG(reg)                                                 \
  reg

/**
 * @brief   Wrapper to point to the PSM FIFO CHANNEL registers.
 *
 * @notapi
 */
#define GTM_PSM_FIFO_CH                                                     \
  CHANNEL

/**
 * @brief   Wrapper to point to the ECLK registers of the CMU.
 *
 * @param[in] index     ECLK register set to point.
 * @param[in] reg       ECLK register to point.
 *
 * @notapi
 */
#define GTM_CMU_ECLK_REG(index, reg)                                        \
  ECLK[index].reg

/**
 * @brief   Wrapper to point to the BRC DEST_ERR_IRQ bit within the BRC IRQ_EN
 *          register.
 *
 * @notapi
 */
#define GTM_BRC_IRQ_EN_DEST_ERR_IRQ_EN_BIT                                  \
  DEST_ERR_EN
/** @} */

#endif /* (SPC5_HAS_GTM_IP_101 || SPC5_HAS_GTM_IP_122) */

#if (SPC5_HAS_GTM_IP_343 == TRUE || SPC5_HAS_GTM_IP_344 == TRUE) || defined (__DOXYGEN__)

#if (SPC5_HAS_GTM_IP_343 == TRUE) || defined (__DOXYGEN__)
#include "SPC58NE_GTM.h"
#endif /* SPC5_HAS_GTM_IP_343 */
#if (SPC5_HAS_GTM_IP_344 == TRUE) || defined (__DOXYGEN__)
#include "SPC58NN_GTM.h"
#endif /* SPC5_HAS_GTM_IP_344 */

/**
 * @brief   GTM module pointers
 *
 * @{
 */
#define GTM_TBU                             (GTM.TBU)
#define GTM_MON                             (GTM.MON)
#define GTM_CMP                             (GTM.CMP)
#define GTM_ARU                             (GTM.ARU)
#define GTM_CMU                             (GTM.CMU)
#define GTM_ICM                             (GTM.ICM)
#define GTM_SPE_0                           (GTM.SPE[0])
#define GTM_SPE_1                           (GTM.SPE[1])
#define GTM_MAP                             (GTM.MAP)
#define GTM_MCFG                            (GTM.MCFG)
#define GTM_TIM_0                           (GTM.TIM[0])
#define GTM_TIM_1                           (GTM.TIM[1])
#define GTM_TIM_2                           (GTM.TIM[2])
#define GTM_TIM_3                           (GTM.TIM[3])
#define GTM_TIM_4                           (GTM.TIM[4])
#if (SPC5_HAS_GTM_IP_344 == TRUE) || defined (__DOXYGEN__)
#define GTM_TIM_5                           (GTM.TIM[5])
#endif /* SPC5_HAS_GTM_IP_344 */
#define GTM_TOM_0                           (GTM.TOM[0])
#define GTM_TOM_1                           (GTM.TOM[1])
#define GTM_TOM_2                           (GTM.TOM[2])
#if (SPC5_HAS_GTM_IP_343 == TRUE) || defined (__DOXYGEN__)
#define GTM_TOM_3                           (GTM.TOM[3])
#endif /* SPC5_HAS_GTM_IP_343 */
#define GTM_ATOM_0                          (GTM.ATOM[0])
#define GTM_ATOM_1                          (GTM.ATOM[1])
#define GTM_ATOM_2                          (GTM.ATOM[2])
#define GTM_ATOM_3                          (GTM.ATOM[3])
#define GTM_ATOM_4                          (GTM.ATOM[4])
#if (SPC5_HAS_GTM_IP_344 == TRUE) || defined (__DOXYGEN__)
#define GTM_ATOM_5                          (GTM.ATOM[5])
#endif /* SPC5_HAS_GTM_IP_344 */
#define GTM_F2A_0                           (GTM.PSM[0].F2A)
#define GTM_AFD_0                           (GTM.PSM[0].AFD)
#define GTM_FIFO_0                          (GTM.PSM[0].FIFO)
#define GTM_DPLL                            (GTM.DPLL)
#define GTM_MCS_0                           (GTM.MCS[0])
#define GTM_MCS_1                           (GTM.MCS[1])
#define GTM_MCS_2                           (GTM.MCS[2])
#define GTM_MCS_3                           (GTM.MCS[3])
#define GTM_MCS_4                           (GTM.MCS[4])
#if (SPC5_HAS_GTM_IP_343 == TRUE) || defined (__DOXYGEN__)
#define GTM_CDTM_0_DTM_0                    (GTM.DTM[0])
#define GTM_CDTM_0_DTM_1                    (GTM.DTM[1])
#define GTM_CDTM_0_DTM_2                    (GTM.DTM[2])
#define GTM_CDTM_0_DTM_3                    (GTM.DTM[3])
#define GTM_CDTM_0_DTM_4                    (GTM.DTM[24])
#define GTM_CDTM_0_DTM_5                    (GTM.DTM[25])
#define GTM_CDTM_1_DTM_0                    (GTM.DTM[4])
#define GTM_CDTM_1_DTM_1                    (GTM.DTM[5])
#define GTM_CDTM_1_DTM_2                    (GTM.DTM[6])
#define GTM_CDTM_1_DTM_3                    (GTM.DTM[7])
#define GTM_CDTM_1_DTM_4                    (GTM.DTM[26])
#define GTM_CDTM_1_DTM_5                    (GTM.DTM[27])
#define GTM_CDTM_2_DTM_0                    (GTM.DTM[8])
#define GTM_CDTM_2_DTM_1                    (GTM.DTM[9])
#define GTM_CDTM_2_DTM_2                    (GTM.DTM[10])
#define GTM_CDTM_2_DTM_3                    (GTM.DTM[11])
#define GTM_CDTM_2_DTM_4                    (GTM.DTM[28])
#define GTM_CDTM_2_DTM_5                    (GTM.DTM[29])
#define GTM_CDTM_3_DTM_0                    (GTM.DTM[12])
#define GTM_CDTM_3_DTM_1                    (GTM.DTM[13])
#define GTM_CDTM_3_DTM_2                    (GTM.DTM[14])
#define GTM_CDTM_3_DTM_3                    (GTM.DTM[15])
#define GTM_CDTM_3_DTM_4                    (GTM.DTM[30])
#define GTM_CDTM_3_DTM_5                    (GTM.DTM[31])
#define GTM_CDTM_4_DTM_0                    (GTM.DTM[16])
#define GTM_CDTM_4_DTM_1                    (GTM.DTM[17])
#define GTM_CDTM_4_DTM_2                    (GTM.DTM[18])
#define GTM_CDTM_4_DTM_3                    (GTM.DTM[19])
#define GTM_CDTM_4_DTM_4                    (GTM.DTM[32])
#define GTM_CDTM_4_DTM_5                    (GTM.DTM[33])
#endif /* SPC5_HAS_GTM_IP_343 */
#if (SPC5_HAS_GTM_IP_344 == TRUE) || defined (__DOXYGEN__)
#define GTM_CDTM_0_DTM_0                    (GTM.CDTM[0].DTM[0])
#define GTM_CDTM_0_DTM_1                    (GTM.CDTM[0].DTM[1])
#define GTM_CDTM_0_DTM_2                    (GTM.CDTM[0].DTM[2])
#define GTM_CDTM_0_DTM_3                    (GTM.CDTM[0].DTM[3])
#define GTM_CDTM_0_DTM_4                    (GTM.CDTM[0].DTM[4])
#define GTM_CDTM_0_DTM_5                    (GTM.CDTM[0].DTM[5])
#define GTM_CDTM_1_DTM_0                    (GTM.CDTM[1].DTM[0])
#define GTM_CDTM_1_DTM_1                    (GTM.CDTM[1].DTM[1])
#define GTM_CDTM_1_DTM_2                    (GTM.CDTM[1].DTM[2])
#define GTM_CDTM_1_DTM_3                    (GTM.CDTM[1].DTM[3])
#define GTM_CDTM_1_DTM_4                    (GTM.CDTM[1].DTM[4])
#define GTM_CDTM_1_DTM_5                    (GTM.CDTM[1].DTM[5])
#define GTM_CDTM_2_DTM_0                    (GTM.CDTM[2].DTM[0])
#define GTM_CDTM_2_DTM_1                    (GTM.CDTM[2].DTM[1])
#define GTM_CDTM_2_DTM_2                    (GTM.CDTM[2].DTM[2])
#define GTM_CDTM_2_DTM_3                    (GTM.CDTM[2].DTM[3])
#define GTM_CDTM_2_DTM_4                    (GTM.CDTM[2].DTM[4])
#define GTM_CDTM_2_DTM_5                    (GTM.CDTM[2].DTM[5])
#define GTM_CDTM_3_DTM_0                    (GTM.CDTM[3].DTM[0])
#define GTM_CDTM_3_DTM_1                    (GTM.CDTM[3].DTM[1])
#define GTM_CDTM_3_DTM_2                    (GTM.CDTM[3].DTM[2])
#define GTM_CDTM_3_DTM_3                    (GTM.CDTM[3].DTM[3])
#define GTM_CDTM_3_DTM_4                    (GTM.CDTM[3].DTM[4])
#define GTM_CDTM_3_DTM_5                    (GTM.CDTM[3].DTM[5])
#define GTM_CDTM_4_DTM_0                    (GTM.CDTM[4].DTM[0])
#define GTM_CDTM_4_DTM_1                    (GTM.CDTM[4].DTM[1])
#define GTM_CDTM_4_DTM_2                    (GTM.CDTM[4].DTM[2])
#define GTM_CDTM_4_DTM_3                    (GTM.CDTM[4].DTM[3])
#define GTM_CDTM_4_DTM_4                    (GTM.CDTM[4].DTM[4])
#define GTM_CDTM_4_DTM_5                    (GTM.CDTM[4].DTM[5])
#endif /* SPC5_HAS_GTM_IP_344 */
#define GTM_BRC                             (GTM.BRC)
/** @} */

/**
 * @brief   GTM module TAGs
 *
 * @{
 */
#define GTM_TAG                             GTM_bf_type
#define GTM_TBU_TAG                         GTM_TBU_bf_type
#define GTM_MON_TAG                         GTM_MON_bf_type
#define GTM_CMP_TAG                         GTM_CMP_bf_type
#define GTM_ARU_TAG                         GTM_ARU_bf_type
#define GTM_CMU_TAG                         GTM_CMU_bf_type
#define GTM_ICM_TAG                         GTM_ICM_bf_type
#define GTM_SPE_TAG                         GTM_SPE_bf_type
#define GTM_MAP_TAG                         GTM_MAP_bf_type
#define GTM_MCFG_TAG                        GTM_MCFG_bf_type
#define GTM_TIM_TAG                         GTM_TIM_bf_type
#define GTM_TOM_TAG                         GTM_TOM_bf_type
#define GTM_ATOM_TAG                        GTM_ATOM_bf_type
#define GTM_F2A_TAG                         GTM_PSM_F2A_bf_type
#define GTM_AFD_TAG                         GTM_PSM_AFD_bf_type
#define GTM_FIFO_TAG                        GTM_PSM_FIFO_bf_type
#define GTM_DPLL_TAG                        GTM_DPLL_bf_type
#define GTM_MCS_TAG                         GTM_MCS_bf_type
#define GTM_DTM_TAG                         GTM_CDTM_DTM_bf_type
#define GTM_BRC_TAG                         GTM_BRC_bf_type
/** @} */

/**
 * @brief   GTM register macro
 *
 * @{
 */
/**
 * @brief   Gets the value of TIM AUX_IN_SRC registers.
 *
 * @param[in] tim       the number of TIM to which the AUX_IN_SRC register
 *                      is related to.
 * @return              the value of the TIM AUX_IN_SRC register.
 *
 * @notapi
 */
#if (SPC5_HAS_GTM_IP_343 == TRUE) || defined (__DOXYGEN__)
#define GTM_GET_TIM_AUX_IN_SRC(tim)                                         \
  (uint32_t)(gtmd->gtm->TIM_AUX_IN_SRC[tim].R)
#endif /* SPC5_HAS_GTM_IP_343 */
#if (SPC5_HAS_GTM_IP_344 == TRUE) || defined (__DOXYGEN__)
#define GTM_GET_TIM_AUX_IN_SRC(tim)                                         \
  (uint32_t)(gtmd->gtm->CCM[tim].TIM_AUX_IN_SRC.R)
#endif /* SPC5_HAS_GTM_IP_344 */

/**
 * @brief   Sets the value of TIM AUX_IN_SRC registers.
 *
 * @param[in] tim       the number of TIM to which the AUX_IN_SRC register
 *                      is related to.
 * @param[in] val       the value to set in the TIM AUX_IN_SRC register.
 *
 * @notapi
 */
#if (SPC5_HAS_GTM_IP_343 == TRUE) || defined (__DOXYGEN__)
#define GTM_SET_TIM_AUX_IN_SRC(tim, val)                                    \
  (gtmd->gtm->TIM_AUX_IN_SRC[tim].R = val)
#endif /* SPC5_HAS_GTM_IP_343 */
#if (SPC5_HAS_GTM_IP_344 == TRUE) || defined (__DOXYGEN__)
#define GTM_SET_TIM_AUX_IN_SRC(tim, val)                                    \
  (gtmd->gtm->CCM[tim].TIM_AUX_IN_SRC.R = val)
#endif /* SPC5_HAS_GTM_IP_344 */

/**
 * @brief   Wrapper to point to the registers RST and IN_SRC of the TIM.
 *
 * @param[in] reg       register to point (RST, IN_SRC).
 *
 * @notapi
 */
#define GTM_TIM_GC_REG(reg)                                                 \
  GC.reg

/**
 * @brief   Wrapper to point to the TGC registers of the TOM.
 *
 * @param[in] index     TGC register set to point.
 * @param[in] reg       TGC register to point.
 *
 * @notapi
 */
#define GTM_TOM_TGC_REG(index, reg)                                           \
  TGC[index].reg

/**
 * @brief   Wrapper to point to the AGC registers of the ATOM.
 *
 * @param[in] reg       register to point.
 *
 * @notapi
 */
#define GTM_ATOM_AGC_REG(reg)                                           \
  AGC.reg

/**
 * @brief   Wrapper to point to the channel registers of TIM, TOM and ATOM.
 *
 * @param[in] channel   number of channel related to the channel registers to
 *                      point.
 * @param[in] reg       register to point.
 *
 * @notapi
 */
#define GTM_CH_REG(channel, reg)                                            \
  CH[channel].reg

/**
 * @brief   Sets the value of DPLL ID_PMTR registers.
 *
 * @param[in] index     ID_PMTR to be set.
 * @param[in] value     the value to set in the DPLL ID_PMTR register.
 *
 * @notapi
 */
#define GTM_SET_DPLL_ID_PMTR(index, value)                                  \
  (dplld->dpll->ID_PMTR[index].R = value)

/**
 * @brief   Wrapper to point to the registers RST of the MCS.
 *
 * @notapi
 */
#define GTM_MCS_GC_CTRL_REG                                                 \
  GC.CTRL_STAT

/**
 * @brief   Wrapper to point to the MCS Scheduling Mode bit within the MCS
 *          CTRL_STAT registers.
 *
 * @notapi
 */
#define GTM_MCS_GC_CTRL_SCD_MODE_BIT                                        \
  SCD_MODE

/**
 * @brief   Wrapper to point to the registers RST of the MCS.
 *
 * @notapi
 */
#define GTM_MCS_GC_RST_REG                                                  \
  GC.RESET

/**
 * @brief   Wrapper to point to the registers CTRG, STRG and ERR of the MCS.
 *
 * @param[in] reg       register to point (CTRG, STRG, ERR).
 *
 * @notapi
 */
#define GTM_MCS_GC_REG(reg)                                                 \
  GC.reg

/**
 * @brief   Wrapper to point to the PSM FIFO CHANNEL registers.
 *
 * @notapi
 */
#define GTM_PSM_FIFO_CH                                                     \
  CH

/**
 * @brief   Wrapper to point to the ECLK registers of the CMU.
 *
 * @param[in] index     ECLK register set to point.
 * @param[in] reg       ECLK register to point.
 *
 * @notapi
 */
/*lint -e9023 */
#define GTM_CMU_ECLK_REG(index, reg)                                         \
  ECLK_##index##_##reg
/*lint +e9023 */

/**
 * @brief   Wrapper to point to the BRC DEST_ERR_IRQ bit within the BRC IRQ_EN
 *          register.
 *
 * @notapi
 */
#define GTM_BRC_IRQ_EN_DEST_ERR_IRQ_EN_BIT                                  \
  DEST_ERR_IRQ_EN
/** @} */

#endif /* (SPC5_HAS_GTM_IP_343 || SPC5_HAS_GTM_IP_344) */

#endif /* _GTM_TAG_H_ */

/** @} */
