/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Prs.h
 **  Date:          03-Jul-2023
 **
 **  Model Version: 1.427
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Prs_h_
#define RTW_HEADER_TLE9278BQX_Prs_h_
#ifndef TLE9278BQX_Prs_COMMON_INCLUDES_
# define TLE9278BQX_Prs_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TLE9278BQX_Prs_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  uint16_T sbcGlobalCfgReg[25];        /* '<S4>/Parse_Frame' */
  uint16_T addrTag;                    /* '<S4>/Parse_Frame' */
  uint16_T Add;                        /* '<S8>/Add' */
  uint16_T Memory_PreviousInput;       /* '<S8>/Memory' */
  uint8_T flgSBCResend;                /* '<S4>/Parse_Frame' */
  uint8_T oldCntTrgSBCRes;             /* '<S4>/Parse_Frame' */
} DW_TLE9278BQX_Prs_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_TLE9278BQX_Prs_T TLE9278BQX_Prs_DW;

/* Model entry point functions */
extern void TLE9278BQX_Prs_initialize(void);

/* Exported entry point function */
extern void TLE9278BQX_Prs_Bkg(void);

/* Exported entry point function */
extern void TLE9278BQX_Prs_Init(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgSBCResend;           /* '<S3>/Merge1' */

/* SBC Status */
extern uint16_T SBCGlobalCfgReg[25];   /* '<S3>/Merge5' */

/* Config data message */
extern uint16_T SBCIndexSearch;        /* '<S13>/PreLookUpIdSearch_U16' */

/* SBC SBCIndex Search */
extern uint16_T SBCIndexSet;           /* '<S15>/SBCIndexSet' */

/* Index to write data */
extern uint16_T SBCStatusReg;          /* '<S3>/Merge8' */

/* SBC Status diagnosis */
extern uint16_T SBCValSet;             /* '<S15>/SBCValSet' */

/* Data to write */
extern uint8_T StSBCMode;              /* '<S3>/Merge2' */

/* M_S_CTRL: MODE */
extern uint16_T VtRecSBCMsg[25];       /* '<S4>/Parse_Frame' */

/* SBC Counter Rec */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S13>/Data Type Duplicate' : Unused code path elimination
 * Block '<S13>/Data Type Conversion1' : Unused code path elimination
 * Block '<S13>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S13>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S13>/Data Type Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TLE9278BQX_Prs'
 * '<S1>'   : 'TLE9278BQX_Prs/Model Info'
 * '<S2>'   : 'TLE9278BQX_Prs/TLE9278BQX_Prs'
 * '<S3>'   : 'TLE9278BQX_Prs/TLE9278BQX_Prs/Merger'
 * '<S4>'   : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg'
 * '<S5>'   : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Init'
 * '<S6>'   : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/Parse_Frame'
 * '<S7>'   : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_BinarySearchData'
 * '<S8>'   : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SBCFrameChanged'
 * '<S9>'   : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SBCFrameError'
 * '<S10>'  : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SBCGlobalStatusReg'
 * '<S11>'  : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SBCStatusError'
 * '<S12>'  : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SelMode'
 * '<S13>'  : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_BinarySearchData/PreLookUpIdSearch_U16_1'
 * '<S14>'  : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SBCGlobalStatusReg/body'
 * '<S15>'  : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SBCGlobalStatusReg/data'
 * '<S16>'  : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SBCGlobalStatusReg/fc_seq_call'
 * '<S17>'  : 'TLE9278BQX_Prs/TLE9278BQX_Prs/fc_Bkg/fc_SelMode/Calc_Mode'
 */

/*-
 * Requirements for '<Root>': TLE9278BQX_Prs
 */
#endif                                 /* RTW_HEADER_TLE9278BQX_Prs_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/