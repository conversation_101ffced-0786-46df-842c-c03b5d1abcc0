/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  RamCheckSM_MCU_r_xx.c
**  Created on      :  07-Feb-2022 15:39:00
**  Original author :  Mocci A
******************************************************************************/
#ifdef _BUILD_SAFETYMNGR_RAMCHECK_

#ifndef _BUILD_SAFETYMNGR_
#error RAM checks enabled without _BUILD_SAFETYMNGR_ macro enabled
#endif /*  _BUILD_SAFETYMNGR_ */

#if 0
#if ( SIZE_SRAM_0 != SIZE_SRAM_2 )
#error "Size of RAM block have to be equal"
#endif

#if ((SIZE_D_MEM_0 != SIZE_D_MEM_1) || (SIZE_D_MEM_0 != SIZE_D_MEM_2))
#error "Size of RAM block have to be equal"
#endif

#if ((SIZE_I_MEM_0 != SIZE_I_MEM_1) || (SIZE_I_MEM_0 != SIZE_I_MEM_2))
#error "Size of RAM block have to be equal"
#endif
#endif

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RamCheckSM_MCU_4_xx.h"


/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* None */
/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/* None */
/****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/

/******************************************************************************
**   Function    : RamCheck_SM_MCU_SRCEcheck
**
**   Description:
**    This function implements the mechanism described in chapter 3.3.8 page 40
**    of the k2 Safety Manual (AN4446 rev.1). Performing a pattern read on the 
**    RAM partition affected by Correctable Error
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void RamCheck_SM_MCU_SRCEcheck(void)
{
    const baseAddress_t addrTable[RAM_NUM_PARTITIONS] =
    {
#ifdef  SRAM_CHECK_ENABLE
       {SRAM_START, SRAM_END, SRAM_PATTERN_OFFSET_1, SRAM_PATTERN_OFFSET_2, SRAM_PATTERN_OFFSET_3, SRAM_PATTERN_OFFSET_4},
#endif
#ifdef  I_MEM_0_CHECK_ENABLE
       {I_MEM_0_START, I_MEM_0_END, I_MEM_0_PATTERN_OFFSET_1, I_MEM_0_PATTERN_OFFSET_2, I_MEM_0_PATTERN_OFFSET_3, I_MEM_0_PATTERN_OFFSET_4},
#endif
#ifdef  D_MEM_0_CHECK_ENABLE
       {D_MEM_0_START, D_MEM_0_END, D_MEM_0_PATTERN_OFFSET_1, D_MEM_0_PATTERN_OFFSET_2, D_MEM_0_PATTERN_OFFSET_3, D_MEM_0_PATTERN_OFFSET_4},
#endif
#ifdef  I_MEM_2_CHECK_ENABLE
       {I_MEM_2_START, I_MEM_2_END, I_MEM_2_PATTERN_OFFSET_1, I_MEM_2_PATTERN_OFFSET_2, I_MEM_2_PATTERN_OFFSET_3, I_MEM_2_PATTERN_OFFSET_4},
#endif
#ifdef  D_MEM_2_CHECK_ENABLE
       {D_MEM_2_START, D_MEM_2_END, D_MEM_2_PATTERN_OFFSET_1, D_MEM_2_PATTERN_OFFSET_2, D_MEM_2_PATTERN_OFFSET_3, D_MEM_2_PATTERN_OFFSET_4},
#endif
    };
    const uint32_T pattern[RAM_NUM_PATTERN] =
    {
        RAM_PATTERN_1,
        RAM_PATTERN_2,
        RAM_PATTERN_3,
        RAM_PATTERN_4
    };

    RamCheck_SM_MCU_SRCEcheck_sub(addrTable, pattern);

    uint32_T index;
    uint32_T errValue = ZERO;

    /* find the last entry in the table with the VLD '1'
       stop checking on first error, otherwise delete VLD bit.
     */
    for (index = ZERO; (errValue == ZERO) && (index < MEMU_SIZE_SRAM_REPORT_TABLE); index++)
    {
        if (MEMU.CHANNEL0[index].SYS_RAM_CERR_STS.B.VLD != ZERO)
        {
            errValue = Ram_PatternCheck(addrTable, pattern, index);
            MEMU.CHANNEL0[index].SYS_RAM_CERR_STS.R &= (~MEMU_SRAM_CERR_STSn_VLD_MASK);
        }
    }
    if (errValue != ZERO)
    {
        index--;    /* index correction due to "for loop" counts at the end */
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_SRAM_ErrorIndUncorrectableErrorCb, FALSE);
    }
}

#if 0
/******************************************************************************
**   Function    : RamCheck_SM_MCU_4_21
**
**   Description:
**    This function implements requirement SM_MCU_4_21
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void RamCheck_SM_MCU_4_21(void)
{
    /*#############################################################################
    REVIEW: Statement from ELDOR: Not applicable.
    */
}

/******************************************************************************
**   Function    : RamCheck_SM_MCU_4_22
**
**   Description:
**    This function implements requirement SM_MCU_4_22
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void RamCheck_SM_MCU_4_22(void)
{
    /** Here we use static variable to keep the values.
       The variables are not critical, as the values are always checked for
       compliance with the range used before use.
    */
    uint_fast32_t factor;
    uint_fast32_t ramValue;

    /* check if the offset is beyond the end */
    if (g_readOffsetSRAM >= OFFSET_RANGE_SRAM)
    {
        g_readOffsetSRAM -= OFFSET_RANGE_SRAM;
    }

    if (g_readOffsetI_MEM >= (OFFSET_RANGE_I_MEM))
    {
        g_readOffsetI_MEM -= (OFFSET_RANGE_I_MEM);
    }

    if (g_readOffsetD_MEM >= OFFSET_RANGE_D_MEM)
    {
        g_readOffsetD_MEM -= OFFSET_RANGE_D_MEM;
    }

/*#############################################################################
       ToDo REVIEW:
       Statement from ELDOR: This test shall running on Core_2.
    Assumption: access to I_MEM and D_MEM Core_0 but not on Core_1 is allowed.
    */
    /* 2 read accesses per area */
    for ( factor = 0; factor < SECTIONS; factor++)
    {
        //ramValue = RAMReadMem32(START_ADDR_SRAM_0, (factor * OFFSET_RANGE_SRAM) + g_readOffsetSRAM);
        //ramValue = RAMReadMem32(START_ADDR_SRAM_2, (factor * OFFSET_RANGE_SRAM) + g_readOffsetSRAM);

        /* Reading of I-MEM and D-MEM of CORE_0 */
/*
        ramValue = RAMReadMem32(START_ADDR_I_MEM_0, (factor * OFFSET_RANGE_I_MEM) + readOffsetI_MEM);
        ramValue = RAMReadMem32(START_ADDR_D_MEM_0, (factor * OFFSET_RANGE_D_MEM) + readOffsetD_MEM);
*/

        /* Reading of I-MEM and D-MEM of CORE_1 */
/*
        Core 1 is currently not in the scope of testing
        ramValue = RAMReadMem32(START_ADDR_I_MEM_1, (factor * OFFSET_RANGE_I_MEM) + readOffsetI_MEM);
        ramValue = RAMReadMem32(START_ADDR_D_MEM_1, (factor * OFFSET_RANGE_D_MEM) + readOffsetD_MEM);
*/

        /* Reading of I-MEM and D-MEM of CORE_2 */
        //ramValue = RAMReadMem32(START_ADDR_I_MEM_2, (factor * OFFSET_RANGE_I_MEM) + g_readOffsetI_MEM);
        //ramValue = RAMReadMem32(START_ADDR_D_MEM_2, (factor * OFFSET_RANGE_D_MEM) + g_readOffsetD_MEM);
    }

    /* set the offset for the next FTTI */
    g_readOffsetSRAM  += RUN_OFFSET;
    g_readOffsetI_MEM += RUN_OFFSET;
    g_readOffsetD_MEM += RUN_OFFSET;
}

/******************************************************************************
**   Function    : RamCheck_SM_MCU_4_23
**
**   Description:
**    This function implements requirement SM_MCU_4_23
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void RamCheck_SM_MCU_4_23(void)
{
/*#############################################################################
    REVIEW: Statement from ELDOR: This check will be covered by the test on MCU_4_22.
*/
}
#endif
/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/

/******************************************************************************
**   Function    : Ram_PatternCheck
**
**   Description:
**    This function implements a pattern based check on the corresponding base 
**    address is checked o detected an uncorrectable error occurred
**
**   Parameters :
**    const baseAddress_t addrTable[],     parameter address block description array
**    const uint32_T pattern[],            pattern array with 4 pattern
**    uint32_T index                       index to the address of the correctable error
**
**   Returns:
**    uint32_T patterMismatch (0 no uncorrectable error, otherwise the number of the pattern that fails)
**
**   SW Requirements:
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static uint32_T Ram_PatternCheck(const baseAddress_t addrTable[], const uint32_T pattern[], uint32_T index)
{
    uint32_T count;
    uint32_T regAddr;
    uint32_T cerrAddr;
    uint32_T patterMismatch = ZERO;

    /* retrieve the base address of the partition where the ECC correction occurs */
    cerrAddr = MEMU.CHANNEL0[index].SYS_RAM_CERR_ADDR.R;
    for (count = ZERO; (patterMismatch == ZERO) && (count < RAM_NUM_PARTITIONS); count++)
    {
        /* this check is carried out to ensure that there is no address with 0
           due to an incorrect definition */
        if ((addrTable[count].start != ZERO) && (addrTable[count].end != ZERO))
        {
            if ((cerrAddr >= addrTable[count].start) && (cerrAddr <= addrTable[count].end))
            {
                uint32_T checkNo = ZERO;
                // RAM partition found. Now check the pattern
                uint32_T patternAddr = addrTable[count].start + addrTable[count].patternOffset1;
                uint32_T patternData = RamReadMem32(patternAddr);
                if (patternData != pattern[checkNo])
                {
                    /* error */
                    patterMismatch |= (ERROR_FLAG << checkNo);
                }
                checkNo++;
                patternAddr = addrTable[count].start + addrTable[count].patternOffset2;
                patternData = RamReadMem32(patternAddr);
                if (patternData != pattern[checkNo])
                {
                    /* error */
                    patterMismatch |= (ERROR_FLAG << checkNo);
                }
                checkNo++;
                patternAddr = addrTable[count].start + addrTable[count].patternOffset3;
                patternData = RamReadMem32(patternAddr);
                if (patternData != pattern[checkNo])
                {
                    /* error */
                    patterMismatch |= (ERROR_FLAG << checkNo);
                }
                checkNo++;
                patternAddr = addrTable[count].start + addrTable[count].patternOffset4;
                patternData = RamReadMem32(patternAddr);
                if (patternData != pattern[checkNo])
                {
                    /* error */
                    patterMismatch |= (ERROR_FLAG << checkNo);
                }
            }
        }
    }
    return patterMismatch;
}

/******************************************************************************
**   Function    : RamCheck_SM_MCU_SRCEcheck_sub
**
**   Description:
**    This function is a intermediate function of RamCheck_SM_MCU_SRCEcheck to be able 
**    to do unit test on target
**
**   Parameters :
**    const baseAddress_t addrTable[],    parameter address block description array
**    const uint32_t pattern[])           pattern array with 4 pattern
**
**   Returns:
**   void
**
**   SW Requirements:
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void RamCheck_SM_MCU_SRCEcheck_sub(const baseAddress_t addrTable[], const uint32_T pattern[])
{
    uint32_T index;
    uint32_T errValue = ZERO;

    /* find the last entry in the table with the VLD '1'
       stop checking on first error, otherwise delete VLD bit.
     */
    for (index = ZERO; (errValue == ZERO) && (index < MEMU_SIZE_SRAM_REPORT_TABLE); index++)
    {
        if (MEMU.CHANNEL0[index].SYS_RAM_CERR_STS.B.VLD != ZERO)
        {
            errValue = Ram_PatternCheck(addrTable, pattern, index);
            MEMU.CHANNEL0[index].SYS_RAM_CERR_STS.R &= (~MEMU_SRAM_CERR_STSn_VLD_MASK);
        }
    }
    if (errValue != ZERO)
    {
        index--;    /* index correction due to "for loop" counts at the end */
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_SRAM_ErrorIndUncorrectableErrorCb, FALSE);
    }
}

#endif // _BUILD_SAFETYMNGR_RAMCHECK_
/****************************************************************************
 ****************************************************************************/

