/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _IGN_HEI_H_
#define _IGN_HEI_H_

/* include files */
#include "rtwtypes.h"
#include "OS_api.h"

/* Enable Debug mode. */
//#define IGN_MS_DEBUG /* WARNING: Dependent compiled, vanno riaggiornati gli indirizzi se definita. */

#define IGN_CHANNEL_INIT    1u
#define IGN_CHANNEL_ENABLE  2u
#define IGN_CHANNEL_DISABLE 3u
#define IGN_CHANNEL_ABORT   4u

#define IGN_ACT_ON  0u
#define IGN_ACT_OFF 1u

#define IGN_ST_RES    0u
#define IGN_ST_TINIT  1u
#define IGN_ST_AINIT  2u
#define IGN_ST_EN     3u
#define IGN_ST_MDIS   4u
#define IGN_ST_SPRS   5u
#define IGN_ST_OC     6u
#define IGN_ST_LNKON  7u
#define IGN_ST_LNKOFF 8u
#define IGN_ST_ENDSQ  9u
#define IGN_ST_TOUT   10u
#define IGN_ST_WTC    11u
#define IGN_ST_FLXERR 12u
#define IGN_ST_TERR   13u
#define IGN_ST_AERR   14u
#define IGN_ST_TBOUNCE  15u
#define IGN_ST_OL     16u
#define IGN_ST_MERR   17u
#define IGN_ST_CHERR  18u
#define IGN_ST_MTOUT  19u
#define IGN_ST_SPEAK  20u

#define MAX_SPARK_PHASE 4u

#define BUCK_NUMBER  2U
#define BUCK_MAX_PWM_PHASE  4U

#define PMOS_MAX        4U
#define PMOS_NUMBER     4U

#define ETPU_A_MON_1 (ETPU_IGNACQ_0)
#define ETPU_A_MON_2 (ETPU_IGNACQ_1)


#if (N_CYLINDER == 4u)
extern const uint8_T CylMSparkInTable[8];
extern const uint8_T CylMSparkOutTable[8];
extern const uint8_T CylMSparkPmosEnTable[8];
extern const uint8_T CylMSparkMonTable[8];
#elif (N_CYLINDER == 6u)
extern const uint8_T CylMSparkInTable[8];
extern const uint8_T CylMSparkOutTable[8];
extern const uint8_T CylMSparkPmosEnTable[8];
extern const uint8_T CylMSparkMonTable[8];
#elif (N_CYLINDER == 8u)
extern const uint8_T CylMSparkInTable[8];
extern const uint8_T CylMSparkOutTable[8];
extern const uint8_T CylMSparkPmosEnTable[8];
extern const uint8_T CylMSparkMonTable[8];
#else
#error "N_CYLINDER value not ok"
#endif

/*************************/
#define N_MS_SPARK_STORE 6u
#define V_MS_MAIN_SHOT   0u
#define V_MS_START_LEAD  1u
#define V_MS_START_SHOT  2u
#define V_MS_START_IKILL 3u
#define V_MS_SPARK_DIAG  4u
#define V_MS_SPARK_PEAK  5u
/*************************/

int16_T IGN_HEIConfig (uint8_T cylNumber, uint32_T saturation, TaskType evTOn, TaskType evAOff, TaskType evMon1, TaskType evMon2, TaskType evBuckEn, uint16_T periodTrig,
                           uint32_T leadMainChargeTh, uint32_T PmosMchargeTon, uint32_T PmosDischargeDelayOn, uint32_T HostMainDischargeTh, uint32_T HostPmosDelayVcap, 
                           uint32_T HostAcqDelaySecDischarge, uint32_T HostAcqBuckDischTrigger, uint32_T HostLastAcqBuckDischTrigger, uint32_T HostAcqDelaySecTimeout, uint32_T HostAcqDelayStart,
                           uint32_T AcqBuckOffDelay, uint8_T CylOperation, uint32_T SparkBuckEnDelay,
                           uint32_T PulseDelayStart, uint32_T NmosActiveDelay, uint32_T DelayBkPh2, uint32_T DelayBkPh3, uint32_T DelayBkDischPh,
                           uint32_T PlasmaNpulse, uint32_T PlasmaTon, uint32_T PlasmaToff, uint32_T PlasmaToutOn, uint32_T PlasmaLastPulseTon, uint32_T PlasmaThreshold, uint32_T iKillThreshold, uint8_T CylIkill,
                           uint8_T IsecUndThrNmb, uint8_T CylTrigDetection, uint32_T TrigInTimeFilt, uint32_T TrigInTimeFiltTout);

int16_T IGN_HEIEnable (uint8_T cylNumber);
void IGN_HEISet_SparkOff (uint8_T cyl, uint32_T DelayDWT, uint32_T DelayBkEn, uint32_T DelayTmkWin, uint32_T DelayBkF1, uint32_T TrigInTimeFilt);
void IGN_HEISet_BuckEn (uint8_T cyl,uint32_T DelayBkF2, uint32_T DelayBkF3, uint32_T ToPulseIn, uint32_T TDbMKW, uint32_T TrigInTimeFiltTout);
void IGN_HEISet_SparkOn (uint8_T cyl, uint32_T DelayBkDisc, uint32_T DelayBkDischPh, uint32_T ToSlope, uint32_T ToLastSlope, 
                                uint32_T TDbNPMOS, uint32_T TDbShot, uint32_T ToShotOut, uint32_T TPlaSample, uint32_T ThLeadDiag);
void IGN_HEISet_PlasmaOL (uint8_T cyl, uint32_T PlasmaNpulse, uint32_T PlasmaTon, uint32_T PlasmaToff, uint32_T PlasmaLastPulseTon, uint32_T PlasmaThreshold, uint32_T PlasmaToutOn);
void IGN_HEISet_PulseDelayStart(uint8_T cyl, uint32_T PulseDelayStart);
void IGN_HEISet_AcqTrigPeriod(uint32_T AcqTrigPeriod);
void IGN_HEISet_SparkBuckEnDelay(uint8_T cyl,uint32_T SparkBuckEnDelay);
void IGN_HEISet_AcqDelayStart(uint8_T cyl, uint32_T AcqDelayStart);
void IGN_HEISet_AcqBuckTrigger(uint8_T cyl, uint32_T AcqBuckTrigger);
void IGN_HEISet_AcqBuckTrigger1(uint8_T cyl, uint32_T AcqBuckTrigger1);
void IGN_HEISet_AcqBuckDischTrigger(uint32_T AcqBuckDischTrigger);
void IGN_HEISet_AcqBuckOffDelay(uint8_T cyl, uint32_T AcqBuckOffDelay);
void IGN_HEISet_AcqBuckOffDischTrig(uint8_T cyl, uint32_T AcqBuckOffDischTrig);
void IGN_HEISet_AcqDelaySecTimeout(uint32_T AcqDelaySecTimeout);
void IGN_HEISet_HostMainChargeDiagTh(uint32_T MainChargeDiagThreshold);
void IGN_HEISet_HostPmosMchargetTimeOn(uint8_T cyl,uint32_T PmosMchargetTimeOn);
void IGN_HEISet_HostPmosDischargeDelayOn(uint32_T PmosDischargeDelayOn);
void IGN_HEISet_HostMainDischargeTh(uint8_T ch, uint32_T MainDischargeTh);
void IGN_HEISet_IsecUndThrNmb(uint8_T ch, uint32_T IsecUndThrNmb);
void IGN_HEISet_PlasmaThreshold(uint8_T ch, uint32_T PlasmaThreshold);
void IGN_HEISet_IKillThreshold(uint8_T ch, uint32_T IkillThreshold);
//void IGN_HEISet_HostMainDischargeCapTh(uint32_T MainDischargeCapTh);
void IGN_HEISet_HostPmosDelayVcap(uint8_T ch, uint32_T HostPmosDelayVcap);
void IGN_HEISet_HostNmosActiveDelay(uint32_T HostNmosActiveDelay);
void IGN_HEISet_HostAcqDelaySecDischarge(uint32_T AcqDelaySecDischarge);
void IGN_HEISet_HostCylOperation(uint32_T position,uint32_T value);
void IGN_HEISet_HostCylkill(uint32_T position,uint32_T value);
void IGN_HEISet_HostCylTrigDetection(uint32_T position,uint32_T value);
int16_T IGN_HEIPmosDisable(uint8_T channel);
int16_T IGN_HEISparkAbort(uint8_T cyl);
int16_T IGN_HEICmdOutDisable(uint8_T channel);
void IGN_HEISet (uint8_T cylNumber, uint32_T saturation);
void IGN_HEIGetCurrOCDiagTime(uint8_T cylNumber, uint32_T *time);
void IGN_HEIGetCurrSparkTimeOn(uint8_T cylNumber, uint32_T *time);
void IGN_HEIGetCurrSparkOnMiss(uint8_T cylNumber, uint32_T *flag);
void IGN_HEIGetCurrSparkTimeOff(uint8_T cylNumber, uint32_T *time);
void IGN_HEIGetCurrSparkTrigTimeOn(uint8_T cylNumber, uint32_T *time);
void IGN_HEIGetCurrSparkTimeOutOff(uint8_T cylNumber, uint32_T *time);
void IGN_HEIGetCurrSparkIkillTimOn(uint8_T cylNumber, uint32_T *time);
void IGN_HEIGetCurrSparkAngleOn(uint8_T cylNumber, uint32_T *ang);
void IGN_HEIGetCurrSparkAngleOff(uint8_T cylNumber, uint32_T *ang);
void IGN_HEIGetCurrSparkAngleToutOff(uint8_T cylNumber, uint32_T *ang);
void IGN_HEIGetCurrSparkTrigAngleOn(uint8_T cylNumber, uint32_T *ang);
void IGN_HEIGetLeadSparkStore(uint8_T cylNumber, uint8_T phase, uint16_T *val);
void IGN_HEIGetShotSparkStore(uint8_T cylNumber, uint8_T phase, uint16_T *val);
void IGN_HEIGetLeadPeakStore(uint8_T cylNumber, uint16_T *val);
void IGN_HEIGetShotPeakStore(uint8_T cylNumber, uint16_T *val);
void IGN_HEIGetShotIntegral(uint8_T cylNumber, uint32_T *valInt, uint16_T *sampInt);
void IGN_HEIGetStatusIRQ(uint8_T idxNumber, uint8_T *statusMachine, uint8_T *cylNumber);
void IGN_HEIGetBuckRefChPhase(uint8_T idxNumber, uint8_T *counterMachineBuckRef);
void IGN_HEIGetBuckRefPhase(uint8_T idxNumber, uint8_T *counterMachineBuckRef);
void IGN_HEIGetAdcTrigPhaseByCyl(uint8_T idxNumber, uint8_T *counterMachineBuckRef);
void IGN_HEIGetBuckEnRequest(uint8_T idxNumber, uint8_T *counterMachineBuckEn);
void IGN_HEIGetMosPhase(uint8_T idxNumber, uint8_T *counterMachineBuckEn);
//void IGN_HEIGetAdcPriPeakAcq(uint8_T idxNumber, uint8_T *val);
void IGN_HEIGetAdcPriOC(uint8_T idxNumber, uint8_T *val);
void IGN_HEIGetBuckCylRequest(uint8_T idxNumber, uint8_T *val);
void IGN_HEIGetAdcTrigPhase(uint8_T idxNumber, uint32_T *val);
void IGN_HEIControl(uint8_T cylNumber, uint8_T *statusMachine, uint8_T *sequenceMachine, uint8_T *counterMachine,
                        uint32_T saturation, uint32_T *dWell, uint32_T *interSpark,
                        uint32_T *time, uint32_T *ang, uint8_T spEvent);


#endif

