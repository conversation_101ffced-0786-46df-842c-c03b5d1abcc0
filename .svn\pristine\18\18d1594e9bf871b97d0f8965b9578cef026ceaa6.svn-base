/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Utils
**  Filename        :  Utils.h
**  Created on      :  12-nov-2021 14:00:00
**  Original author :  CarboniM
******************************************************************************/
/*****************************************************************************
**
**                        Utils Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/

#ifndef _UTILS_H_
#define _UTILS_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

void    UTILS_nop(void);
void    Delay_ms(uint32_T ms);
void    UTILS_vectcat(uint8_T dest[], uint16_T dest_size,uint8_T offset,const volatile uint8_T src[], uint8_T size);
uint8_T UTILS_hexCharToInt(uint8_T c);
uint8_T UTILS_hexByteToInt(uint8_T hi, uint8_T lo);
void    DigDebounce(uint8_T *pOut, uint8_T *pOldval,uint8_T newval, uint8_T *pCntDeb, uint8_T nf);
void    DigDebounceTwoWay(uint8_T *pOut, uint8_T *pOldval,uint8_T newval, uint8_T *pCntDeb, uint8_T nf0, uint8_T nf1);
uint8_T UTILS_CountSetBits(uint32_T n);

#endif /* _UTILS_H_ */
/****************************************************************************
 ****************************************************************************/
 
