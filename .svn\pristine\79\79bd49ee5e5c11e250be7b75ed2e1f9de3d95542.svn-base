/*
 * File: mul_s32_s32_s32_sr18.c
 *
 * Code generated for Simulink model 'IONChargeCtrl'.
 *
 * Model version                  : 1.123
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Mon Feb 05 15:05:01 2018
 */

#include "rtwtypes.h"
#include "mul_wide_s32.h"
#include "mul_s32_s32_s32_sr18.h"

int32_T mul_s32_s32_s32_sr18(int32_T a, int32_T b)
{
  uint32_T u32_chi;
  uint32_T u32_clo;
  mul_wide_s32(a, b, &u32_chi, &u32_clo);
  u32_clo = (u32_chi << 14U) | (u32_clo >> 18U);
  return (int32_T)u32_clo;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
