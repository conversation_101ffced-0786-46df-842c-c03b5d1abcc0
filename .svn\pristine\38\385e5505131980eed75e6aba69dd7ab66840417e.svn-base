/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_Cache.h
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Mocci A
******************************************************************************/
#ifndef SAFETYMNGR_CACHE_H
#define SAFETYMNGR_CACHE_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_Cache_out.h"

/* add here include files */


/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static volatile uint32_T SafetyMngr_Cache_GetDataCacheReg(void);
static volatile uint32_T SafetyMngr_Cache_GetInstructionCacheReg(void);

#endif // SAFETYMNGR_CACHE_H
/****************************************************************************
 ****************************************************************************/


