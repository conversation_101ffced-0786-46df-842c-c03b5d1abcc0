/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonKnockFFT.h
 **  Date:          21-Apr-2021
 **
 **  Model Version: 1.1447
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonKnockFFT_h_
#define RTW_HEADER_IonKnockFFT_h_
#include <string.h>
#ifndef IonKnockFFT_COMMON_INCLUDES_
# define IonKnockFFT_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonKnockFFT_COMMON_INCLUDES_ */


/* Includes for objects with custom storage classes. */

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* Exported data define */

/* Definition for custom storage class: ELD_EXPORT_DEFINE */
#define FFT_SAMPLE                     128U                      /* Referenced by: '<S7>/DataBufferBuild' */

/* Number of samples used for FFT. */
#define FFT_SAMPLE_DIV2                64U                       /* Referenced by: '<S7>/DataBufferBuild' */

/* Number of samples used for FFT, divided 2. */

/* user code (top of header file) */
#include "fft_lib.h"

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonKnockFFT_initialize(void);

/* Exported entry point function */
extern void IonKnockFFT_EOA(void);

/* Exported entry point function */
extern void IonKnockFFT_PowerOn(void);

/* Exported data declaration */

/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern int32_T ImgData[64];            /* '<S5>/Merge1' */

/* ImgData contains imaginary terms of fft */
extern int32_T RealData[64];           /* '<S5>/Merge' */

/* RealData contains real terms of fft */

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S6>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S6>/Conversion5' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonKnockFFT'
 * '<S1>'   : 'IonKnockFFT/IonKnockFFT_FC'
 * '<S2>'   : 'IonKnockFFT/Scheduler'
 * '<S3>'   : 'IonKnockFFT/IonKnockFFT_FC/EOA_Task'
 * '<S4>'   : 'IonKnockFFT/IonKnockFFT_FC/PowerOn_Task'
 * '<S5>'   : 'IonKnockFFT/IonKnockFFT_FC/Subsystem2'
 * '<S6>'   : 'IonKnockFFT/IonKnockFFT_FC/EOA_Task/FFT'
 * '<S7>'   : 'IonKnockFFT/IonKnockFFT_FC/EOA_Task/Hanning'
 * '<S8>'   : 'IonKnockFFT/IonKnockFFT_FC/EOA_Task/Hanning/DataBufferBuild'
 * '<S9>'   : 'IonKnockFFT/IonKnockFFT_FC/EOA_Task/Hanning/Hanning'
 */

/*-
 * Requirements for '<Root>': IonKnockFFT
 *
 * Inherited requirements for '<S3>/FFT':
 *  1. EISB_FCA6CYL_SW_REQ_1158: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1182)

 */
#endif                                 /* RTW_HEADER_IonKnockFFT_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/