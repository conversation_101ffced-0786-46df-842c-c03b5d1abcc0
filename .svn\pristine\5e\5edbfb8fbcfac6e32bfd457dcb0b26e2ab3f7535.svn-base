/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MCAN
**  Filename        :  Mcan.h
**  Created on      :  09-giu-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/

#ifndef _MCAN_H_
#define _MCAN_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "sys.h"
#include "Timing_out.h"
#include "mcan_out.h"
#include <string.h>

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* Driver constants */
#define CAN_SUB_0_RAM            0xFFED4000UL
#define CAN_SUB_0_RAM_SIZE       11520U
#if (TARGET_TYPE == SPC574K2_CUT24)
#define CAN_SA_BIT_POSITION      4U
#elif(TARGET_TYPE == SPC574K2)
#define CAN_SA_BIT_POSITION      2U
#endif
#define CAN_DEDICATED_RXBUFFER   0xFCU
#define CAN_FIFO0_RXBUFFER       0xFDU
#define CAN_FIFO1_RXBUFFER       0xFEU
#define CAN_DEDICATED_TXBUFFER   0xFFU
#define CAN_FIFO_TXBUFFER        0xFBU
#define CAN_QUEUE_TXBUFFER       0xFCU
#define CAN_MIXED_FIFO_TXBUFFER  0xFDU
#define CAN_MIXED_QUEUE_TXBUFFER 0xFEU
#define CAN_ANY_TXBUFFER         0xFFUL
#define CAN_ANY_RXBUFFER         0xFFUL
#define CAN_NO_LOOPBACK          0x00U
#define CAN_INTERNAL_LOOPBACK    0x01U
#define CAN_EXTERNAL_LOOPBACK    0x02U
#define CAN_RX_INT_DISABLE       0xFFU
#define CAN_TX_INT_DISABLE       0xFFU
#define CAN_FIFO0_INT_DISABLE    0xFFU
#define CAN_FIFO1_INT_DISABLE    0xFFU
#define CAN_LINE0_INT            0x00U
#define CAN_LINE1_INT            0x01U
#define CAN_ID_STD               0x00U
#define CAN_ID_XTD               0x01U
#define CAN_OP_NORMAL            0x00U
#define CAN_OP_CANFD             0x01U
#define CAN_MSG_WAIT             1U
#define CAN_MSG_OK               0U
#define CAN_TXMODE_NOT_SUPPORTED 2U
#define CAN_SIZE_NOT_SUPPORTED   3U
#define CAN_INTERRUPT_ENABLED    1U
#define CAN_INTERRUPT_DISABLED   0U
#define CAN_FILTER_RANGE         0U
#define CAN_FILTER_DUALID        1U
#define CAN_FILTER_CLASSIC       2U
#define CAN_FIFO0                1U
#define CAN_FIFO1                2U
#pragma ghs startnomisra
#define CAN_ROUND16(x)  ((uint32_t)x%16UL==0UL? (uint32_t)x:(((uint32_t)x/16UL)+1UL)*16UL)
#pragma ghs endnomisra
#define CAN_LITTLE_ENDIAN        0U
#define CAN_BIG_ENDIAN           1U
#define CAN_MAX_DATA_SIZE_8      8U
#define CAN_MAX_DATA_SIZE_64    64U
#define MCAN_OP_TX             0x3U
#define CAN_NOT_CONFIGURED       0u
#define CAN_CONFIGURED           1u

//Error mask and parameters
#define MCAN_ERROR_ARAE   (1u << 29)  // Bit 29 ARAE: Access to Reserved Address Enable
#define MCAN_ERROR_PEDE   (1u << 28)  // Bit 28 PEDE: Protocol Error in Data Phase Enable
#define MCAN_ERROR_PEAE   (1u << 27)  // Bit 27 PEAE: Protocol Error in Arbitration Phase Enable
#define MCAN_ERROR_WDIE   (1u << 26)  // Bit 26 WDIE: Watchdog Interrupt Enable
#define MCAN_ERROR_BOE    (1u << 25)  // Bit 25 BOE: Bus_Off Status Interrupt Enable
#define MCAN_ERROR_EWE    (1u << 24)  // Bit 24 EWE: Warning Status Interrupt Enable
#define MCAN_ERROR_EPE    (1u << 23)  // Bit 23 EPE: Error Passive Interrupt Enable
#define MCAN_ERROR_ELOE   (1u << 22)  // Bit 22 ELOE: Error Logging Overflow Interrupt Enable
#define MCAN_ERROR_BEUE   (1u << 21)  // Bit 21 BEUE: Bit Error Uncorrected Interrupt Enable
#define MCAN_ERROR_BECE   (1u << 20)  // Bit 20 BECE: Bit Error Corrected Interrupt Enable
#define MCAN_ERROR_DRXE   (1u << 19)  // Bit 19 DRXE: Message stored to Dedicated Rx Buffer Interrupt Enable
#define MCAN_ERROR_TOOE   (1u << 18)  // Bit 18 TOOE: Timeout Occurred Interrupt Enable
#define MCAN_ERROR_MRAFE  (1u << 17)  // Bit 17 MRAFE: Message RAM Access Failure Interrupt Enable
#define MCAN_ERROR_TSWE   (1u << 16)  // Bit 16 TSWE: Timestamp Wraparound Interrupt Enable
#define MCAN_ERROR_TEFLE  (1u << 15)  // Bit 15 TEFLE: Tx Event FIFO Event Lost Interrupt Enable
#define MCAN_ERROR_TEFFE  (1u << 14)  // Bit 14 TEFFE: Tx Event FIFO Full Interrupt Enable
#define MCAN_ERROR_TEFWE  (1u << 13)  // Bit 13 TEFWE: Tx Event FIFO Watermark Reached Interrupt Enable
#define MCAN_ERROR_TEFNE  (1u << 12)  // Bit 12 TEFNE: Tx Event FIFO New Entry Interrupt Enable
#define MCAN_ERROR_TFEE   (1u << 11)  // Bit 11 TFEE: Tx FIFO Empty Interrupt Enable
#define MCAN_ERROR_TCFE   (1u << 10)  // Bit 10 TCFE: Transmission Cancellation Finished Interrupt Enable
#define MCAN_ERROR_TCE    (1u << 9 )  // Bit 9 TCE: Transmission Completed Interrupt Enable
#define MCAN_ERROR_HPME   (1u << 8 )  // Bit 8 HPME: High Priority Message Interrupt Enable
#define MCAN_ERROR_RF1LE  (1u << 7 )  // Bit 7 RF1LE: Rx FIFO 1 Message Lost Interrupt Enable
#define MCAN_ERROR_RF1FE  (1u << 6 )  // Bit 6 RF1FE: Rx FIFO 1 Full Interrupt Enable
#define MCAN_ERROR_RF1WE  (1u << 5 )  // Bit 5 RF1WE: Rx FIFO 1 Watermark Reached Interrupt Enable
#define MCAN_ERROR_RF1NE  (1u << 4 )  // Bit 4 RF1NE: Rx FIFO 1 New Message Interrupt Enable
#define MCAN_ERROR_RF0LE  (1u << 3 )  // Bit 3 RF0LE: Rx FIFO 0 Message Lost Interrupt Enable
#define MCAN_ERROR_RF0FE  (1u << 2 )  // Bit 2 RF0FE: Rx FIFO 0 Full Interrupt Enable
#define MCAN_ERROR_RF0WE  (1u << 1 )  // Bit 1 RF0WE: Rx FIFO 0 Watermark Reached Interrupt Enable
#define MCAN_ERROR_RF0NE  (1u << 0 )  // Bit 0 RF0NE: Rx FIFO 0 New Message Interrupt Enable

//Error mask ISR and parameters 
#define MCAN_ERROR_ARAE_ISR   (1u << 29)  // Bit 29 ARAE: Access to Reserved Address Enable
#define MCAN_ERROR_PEDE_ISR   (1u << 28)  // Bit 28 PEDE: Protocol Error in Data Phase Enable
#define MCAN_ERROR_PEAE_ISR   (1u << 27)  // Bit 27 PEAE: Protocol Error in Arbitration Phase Enable
#define MCAN_ERROR_WDIE_ISR   (1u << 26)  // Bit 26 WDIE: Watchdog Interrupt Enable
#define MCAN_ERROR_BOE_ISR    (1u << 25)  // Bit 25 BOE: Bus_Off Status Interrupt Enable
#define MCAN_ERROR_EWE_ISR    (1u << 24)  // Bit 24 EWE: Warning Status Interrupt Enable
#define MCAN_ERROR_EPE_ISR    (1u << 23)  // Bit 23 EPE: Error Passive Interrupt Enable
#define MCAN_ERROR_ELOE_ISR   (1u << 22)  // Bit 22 ELOE: Error Logging Overflow Interrupt Enable
#define MCAN_ERROR_BEUE_ISR   (1u << 21)  // Bit 21 BEUE: Bit Error Uncorrected Interrupt Enable
#define MCAN_ERROR_BECE_ISR   (1u << 20)  // Bit 20 BECE: Bit Error Corrected Interrupt Enable
#define MCAN_ERROR_DRXE_ISR   (1u << 19)  // Bit 19 DRXE: Message stored to Dedicated Rx Buffer Interrupt Enable
#define MCAN_ERROR_TOOE_ISR   (1u << 18)  // Bit 18 TOOE: Timeout Occurred Interrupt Enable
#define MCAN_ERROR_MRAFE_ISR  (1u << 17)  // Bit 17 MRAFE: Message RAM Access Failure Interrupt Enable
#define MCAN_ERROR_TSWE_ISR   (1u << 16)  // Bit 16 TSWE: Timestamp Wraparound Interrupt Enable
#define MCAN_ERROR_TEFLE_ISR  (1u << 15)  // Bit 15 TEFLE: Tx Event FIFO Event Lost Interrupt Enable
#define MCAN_ERROR_TEFFE_ISR  (1u << 14)  // Bit 14 TEFFE: Tx Event FIFO Full Interrupt Enable
#define MCAN_ERROR_TEFWE_ISR  (1u << 13)  // Bit 13 TEFWE: Tx Event FIFO Watermark Reached Interrupt Enable
#define MCAN_ERROR_TEFNE_ISR  (1u << 12)  // Bit 12 TEFNE: Tx Event FIFO New Entry Interrupt Enable
#define MCAN_ERROR_TFEE_ISR   (1u << 11)  // Bit 11 TFEE: Tx FIFO Empty Interrupt Enable
#define MCAN_ERROR_TCFE_ISR   (1u << 10)  // Bit 10 TCFE: Transmission Cancellation Finished Interrupt Enable
#define MCAN_ERROR_TCE_ISR    (1u << 9 )  // Bit 9 TCE: Transmission Completed Interrupt Enable
#define MCAN_ERROR_HPME_ISR   (1u << 8 )  // Bit 8 HPME: High Priority Message Interrupt Enable
#define MCAN_ERROR_RF1LE_ISR  (1u << 7 )  // Bit 7 RF1LE: Rx FIFO 1 Message Lost Interrupt Enable
#define MCAN_ERROR_RF1FE  (1u << 6 )  // Bit 6 RF1FE: Rx FIFO 1 Full Interrupt Enable
#define MCAN_ERROR_RF1WE  (1u << 5 )  // Bit 5 RF1WE: Rx FIFO 1 Watermark Reached Interrupt Enable
#define MCAN_ERROR_RF1NE  (1u << 4 )  // Bit 4 RF1NE: Rx FIFO 1 New Message Interrupt Enable
#define MCAN_ERROR_RF0LE  (1u << 3 )  // Bit 3 RF0LE: Rx FIFO 0 Message Lost Interrupt Enable
#define MCAN_ERROR_RF0FE  (1u << 2 )  // Bit 2 RF0FE: Rx FIFO 0 Full Interrupt Enable
#define MCAN_ERROR_RF0WE  (1u << 1 )  // Bit 1 RF0WE: Rx FIFO 0 Watermark Reached Interrupt Enable
#define MCAN_ERROR_RF0NE  (1u << 0 )  // Bit 0 RF0NE: Rx FIFO 0 New Message Interrupt Enable

/* Clock Calibration Unit references and constants */
#define SPC5_CCCU_0              CCCU
#define CCCU_RESET_KEY          (0x444U)

/* CAN registers helper macros */
#define CAN_CCCR_INIT       0x1u     //(1UL)
#define CAN_CCCR_CCE        0x2u     //(1UL << 1)
#define CAN_IR_DRX          0x80000u //(1UL << 19)
#define CAN_IR_TC           0x200u   //(1UL << 9)

/* CAN Rx Buffer filters parameters */
#define MCAN_DISABLE_STD_FILTER       (0x0U << 27)
#define MCAN_REJECT_STD_ID            (0X3U << 27)
#define MCAN_ENABLE_STD_FILTER        (0x7U << 27)
#define MCAN_ENABLE_STD_FILTER_MASK   (0x38000000U)
#define MCAN_STD_FILTER_SIZE          (4UL)
#define MCAN_STD_FILTER_OFFSET        (8U + 8U)

#define MCAN_DISABLE_XTD_FILTER       (0x0U << 29)
#define MCAN_REJECT_XTD_ID            (0x3U << 29)
#define MCAN_ENABLE_XTD_FILTER        (0x7U << 29)
#define MCAN_ENABLE_XTD_FILTER_MASK   (0xE0000000U)
#define MCAN_XTD_FILTER_SIZE          (8UL)
#define MCAN_XTD_FILTER_OFFSET        (8U + 64U)

#define MCAN_FILTER_ENABLE_OFFSET     (32)
#define MCAN_FRAMESIZE_OFFSET         (4UL)
#define MCAN_FRAMESIZE_LSB            (16)
#define MCAN_FRAMESIZE_LSB_MASK       (0xFu)
#define MCAN_ID_LSB                   (16)
#define MCAN_ID_TYPE_LSB              (30)
#define MCAN_DATA_OFFSET              (4UL)
#define MCAN_DATA_MSB                 (24)
#define MCAN_DATA_BYTE_SIZE           (8)
#define MCAN_DLC_OFFSET               (4UL)
#define MCAN_DLC_OFFSET_LSB           (16)
#define MCAN_MAX_SIZE_SINGLE_CYCLE    (4U)
#define MCAN_FRAME_NUMBER_PER_CYCLE   (4U)

/* CAN Rx Dedicated Buffer Elements */
#define MCAN_RXB_R0_XTD_LSB         30u
#define MCAN_RXB_R0_XTD_LSB_MASK    0x1u
#define MCAN_RXB_R0_STDID_LSB       18u
#define MCAN_RXB_R0_STDID_LSB_MASK  0x7FFu
#define MCAN_RXB_R0_EXTID_LSB       0u
#define MCAN_RXB_R0_EXTID_LSB_MASK  0x1FFFFFFFu
#define MCAN_RXB_R1_DLCLSB          16u
#define MCAN_RXB_R1_DLCLSB_MASK     0xFu
#define MCAN_RXB_SIZE               (8U + 8U)
#define MCAN_RXB_ADDRESS_OFFSET     (8UL)
#define MCAN_RXB_DLC_OFFSET         (4UL)
#define MCAN_RXB_NDAT1_NUMBUF       (32u)
#define MCAN_RXB_NDAT1_LASTBUF      (31u)
#define MCAN_RXB_DATASIZE_8         0U
#define MCAN_RXB_DATASIZE_64        7U

/* CAN Tx Dedicated Buffer Elements */
#define MCAN_TXB_R0_STDID_LSB       18u
#define MCAN_TXB_R0_EXTID_LSB_MASK  0x40000000U
#define MCAN_TXB_R1_DLCLSB          16u
#define MCAN_TXB_SIZE8_OFFSET       (8U + 8U)
#define MCAN_TXB_SIZE64_OFFSET      (8U + 64U)
#define MCAN_TXB_SIZE               (8U + 8U)
#define MCAN_TXB_ADDRESS_OFFSET     (8UL)
#define MCAN_TXB_UNAVAILABLE        0xFFU
#define MCAN_TXB_DATASIZE_8         0U
#define MCAN_TXB_DATASIZE_64        7U

/* CAN Registers Elements */
#define MCAN_NORMAL_OPMODE          0U
#define MCAN_INIT_OPMODE            1U
#define MCAN_NORMAL_CAN_OP          0U
#define MCAN_RESTRICTED_CAN_OP      1U
#define MCAN_RETX_ENABLE            0U
#define MCAN_RETX_DISABLE           1U
#define MCAN_ISR_DISABLED           0U
#define MCAN_ISR_LINE_DISABLED      0U
#define MCAN_ISR_LINE_DEFAULT       0U
#define MCAN_ISR_LINES_ENABLED      3U
#define MCAN_RX_ISR_OCCURRED        1U
#define MCAN_NEXT_ADDRESS_OFFSET   4UL
#define MCAN_PASSIVE_ERR_OCCURRED 0x1U
#define MCAN_BUSOFF_DISABLE       0x0U
#define MCAN_BUSOFF_ENABLE        0x1U
#define MCAN_BUSOFF_NOT_OCCURRED  0x0U
#define MCAN_BUSOFF_OCCURRED      0x1U
#define MCAN_BUSOFF_CLEAR         0x1U
#define MCAN_TEST_ENABLE          0x1U
#define MCAN_BUS_MONITOR_ENABLE   0x1U
#define MCAN_LOOPBACK_ENABLE      0x1U
#define MCAN_RX_INT_ENABLE        0x1U
#define MCAN_REJECT_UNUSED_MSG    0x3U

/* CAN DLC defines */
#define MCAN_FRAMESIZE_8            (8U)
#define MCAN_FRAMESIZE_12          (12U)
#define MCAN_FRAMESIZE_16          (16U)
#define MCAN_FRAMESIZE_20          (20U)
#define MCAN_FRAMESIZE_24          (24U)
#define MCAN_FRAMESIZE_32          (32U)
#define MCAN_FRAMESIZE_48          (48U)
#define MCAN_FRAMESIZE_64          (64U)
#define MCAN_DLC_0                  (0U)
#define MCAN_DLC_9                  (9U)
#define MCAN_DLC_10                (10U)
#define MCAN_DLC_11                (11U)
#define MCAN_DLC_12                (12U)
#define MCAN_DLC_13                (13U)
#define MCAN_DLC_14                (14U)
#define MCAN_DLC_15                (15U)

/* Frame transmitted */
#define CAN_TX          1U
/* Frame received */
#define CAN_RX          0U

/* Transmission message buffer is not active */
#define TX_BUFFER_CODE_INACTIVE         0x08U
/* Receiving message buffer is not active */
#define RX_BUFFER_CODE_DISABLE          0x00U
/* Transmission message buffer transmit data frame unconditionally once, with RTR = 0 */
#define TX_BUFFER_CODE_TRANSMIT         0x0CU
/* Receiving message buffer is active and empty */
#define RX_BUFFER_CODE_ACTIVE_EMPTY     0x04U

/* Disable the MCAN module */
#define CAN_DISABLE    1U
/* Enable the MCAN module */
#define CAN_ENABLE     0U

/* CAN Baudrate 100 kbit/s */
#define CAN_BR_100k     0U
/* CAN Baudrate 125 kbit/s */
#define CAN_BR_125k     1U
/* CAN Baudrate 250 kbit/s */
#define CAN_BR_250k     2U
/* CAN Baudrate 500 kbit/s */
#define CAN_BR_500k     3U
/* CAN Baudrate 800 kbit/s */
#define CAN_BR_800k     4U
/* CAN Baudrate 1000 kbit/s */
#define CAN_BR_1000k    5U
/* CAN Baudrate A specific configuration */
#define CAN_A_BR_CUSTOMER 6U
/* CAN Baudrate B specific configuration */
#define CAN_B_BR_CUSTOMER 7U
/* CAN Baudrate C specific configuration */
#define CAN_C_BR_CUSTOMER 8U

/* The CAN engine clock source is the bus clock */
#define CAN_CLK_SRC_BUSCLK  2U
/* The CAN engine clock source is the oscillator clock */
#define CAN_CLK_SRC_XOSC    1U

/*   CAN CONFIGURATION PARAMETERS */
#define CAN_BRP_100k        (80U - 1U)
#define CAN_SJW_100k        (1U)
#define CAN_TSEG1_100k      (1U)
#define CAN_TSEG2_100k      (1U)

#define CAN_BRP_125k        (32U - 1U)
#define CAN_SJW_125k        (1U)
#define CAN_TSEG1_125k      (5U)
#define CAN_TSEG2_125k      (2U)

#define CAN_BRP_250k        (16U - 1U)
#define CAN_SJW_250k        (1U)
#define CAN_TSEG1_250k      (5U)
#define CAN_TSEG2_250k      (2U)

#define CAN_BRP_500k        (8U - 1U)
#define CAN_SJW_500k        (1U)
#define CAN_TSEG1_500k      (5U)
#define CAN_TSEG2_500k      (2U)

#define CAN_BRP_800k        (10U - 1U)
#define CAN_SJW_800k        (1U)
#define CAN_TSEG1_800k      (1U)
#define CAN_TSEG2_800k      (1U)

#define CAN_BRP_1000k       (4U - 1U)
#define CAN_SJW_1000k       (1U)
#define CAN_TSEG1_1000k     (5U)
#define CAN_TSEG2_1000k     (2U)

/*  MBx CODE for Tx buffers */
#define MB_CS_TX      0x08000000u //1<<27

/* MB masks for RX buffers */
#define CANMBMASK0    0x00000001u
#define CANMBMASK1    0x00000002u
#define CANMBMASK2    0x00000004u
#define CANMBMASK3    0x00000008u
#define CANMBMASK4    0x00000010u
#define CANMBMASK5    0x00000020u
#define CANMBMASK6    0x00000040u
#define CANMBMASK7    0x00000080u
#define CANMBMASK8    0x00000100u
#define CANMBMASK9    0x00000200u
#define CANMBMASK10   0x00000400u
#define CANMBMASK11   0x00000800u
#define CANMBMASK12   0x00001000u
#define CANMBMASK13   0x00002000u
#define CANMBMASK14   0x00004000u
#define CANMBMASK15   0x00008000u
#define CANMBMASK16   0x00010000u
#define CANMBMASK17   0x00020000u
#define CANMBMASK18   0x00040000u
#define CANMBMASK19   0x00080000u
#define CANMBMASK20   0x00100000u
#define CANMBMASK21   0x00200000u
#define CANMBMASK22   0x00400000u
#define CANMBMASK23   0x00800000u
#define CANMBMASK24   0x01000000u
#define CANMBMASK25   0x02000000u
#define CANMBMASK26   0x04000000u
#define CANMBMASK27   0x08000000u
#define CANMBMASK28   0x10000000u
#define CANMBMASK29   0x20000000u
#define CANMBMASK30   0x40000000u
#define CANMBMASK31   0x80000000u
#if (TARGET_TYPE == MPC563XM)||(TARGET_TYPE == MPC5642A)||(TARGET_TYPE == MPC5644A)
#define CANMBMASK32   CANMBMASK0 
#define CANMBMASK33   CANMBMASK1 
#define CANMBMASK34   CANMBMASK2 
#define CANMBMASK35   CANMBMASK3 
#define CANMBMASK36   CANMBMASK4 
#define CANMBMASK37   CANMBMASK5 
#define CANMBMASK38   CANMBMASK6 
#define CANMBMASK39   CANMBMASK7 
#define CANMBMASK40   CANMBMASK8 
#define CANMBMASK41   CANMBMASK9 
#define CANMBMASK42   CANMBMASK10
#define CANMBMASK43   CANMBMASK11
#define CANMBMASK44   CANMBMASK12
#define CANMBMASK45   CANMBMASK13
#define CANMBMASK46   CANMBMASK14
#define CANMBMASK47   CANMBMASK15
#define CANMBMASK48   CANMBMASK16
#define CANMBMASK49   CANMBMASK17
#define CANMBMASK50   CANMBMASK18
#define CANMBMASK51   CANMBMASK19
#define CANMBMASK52   CANMBMASK20
#define CANMBMASK53   CANMBMASK21
#define CANMBMASK54   CANMBMASK22
#define CANMBMASK55   CANMBMASK23
#define CANMBMASK56   CANMBMASK24
#define CANMBMASK57   CANMBMASK25
#define CANMBMASK58   CANMBMASK26
#define CANMBMASK59   CANMBMASK27
#define CANMBMASK60   CANMBMASK28
#define CANMBMASK61   CANMBMASK29
#define CANMBMASK62   CANMBMASK30
#define CANMBMASK63   CANMBMASK31
#endif

/* Timeout for CAN configuration phase */
#define CAN_TIMEOUT 15u

#pragma ghs startnomisra    // 19.12 - Proven in use

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/*MISRA_RULE_VIOLATED=19.12*/
/* Defines a message buffer "structure": queue with length[x] for MB[n] */
#define CAN_BUF(n,x)\
        uint8_T buffStatus_##n;\
        uint8_T r_##n;\
        uint8_T w_##n;\
        CANBuffer_T buffer_##n[x] ;

/* Initializes the status of the message buffer structure for MB[n] engine[channel] */
#define INIT_BS(channel,n)           CanCh##channel##Rx[n].buffStatus=0u;

/* Updates the status of the message buffer structure for MB[n] engine[channel] at the value[v] */
#define UPDATE_BS(channel,n,v)       CanCh##channel##Rx[n].buffStatus=v

/* Returns the status of the message buffer structure for MB[n] engine[channel] */
#define BS_ID(channel,n)             (CanCh##channel##Rx[n].buffStatus)

/* Check if the status of the message buffer structure for MB[n] engine[channel] is overrun */
#define IS_BUFFER_OVERRUN(channel,n) (BS_ID(channel,n)& BUFFER_OVERRUN)

/* Returns the status of the message buffer structure for MB[n] engine[channel] is full */
#define IS_BUFFER_FULL(channel,n)    (BS_ID(channel,n)& BUFFER_FULL) 

/* Returns the number of reads from the message buffer structure for MB[n] engine[channel] */
#define READ_ID(channel,n)       (CanCh##channel##Rx[n].r)

/* Returns the number of writes on the message buffer structure for MB[n] engine[channel] */
#define WRITE_ID(channel,n)      (CanCh##channel##Rx[n].w)

#define STR_HELPER(channel, n, byte) *(uint32_T*)&(CanCh##channel##_b.buffer_##n[CanCh##channel##_b.w_##n].b[byte])
#define STRINGIFY(channel, n, byte) STR_HELPER(channel, n, byte)

/* Copies the received data from the MB[n] engine[channel] to the associated  message buffer structure */
#define MEM_WRITE(channel,rxBuf,regValue,regValueNext, id, idType, dlc)    {\
    	writeIndex = CanCh##channel##Rx[rxBuf].w;\
    	*(uint32_T*)&(CanCh##channel##Rx[rxBuf].buffer[writeIndex].b[0]) = CAN_ENDIANESS_INVERSION(regValue);\
    	*(uint32_T*)&(CanCh##channel##Rx[rxBuf].buffer[writeIndex].b[4]) = CAN_ENDIANESS_INVERSION(regValueNext);\
    	CanCh##channel##Rx[rxBuf].buffer[writeIndex].dlc = dlc;\
    	CanCh##channel##Rx[rxBuf].buffer[writeIndex].id = id;\
    	CanCh##channel##Rx[rxBuf].buffer[writeIndex].ide = idType;\
}

///Updates the number of writes from the  message buffer structure for MB[n] engine[channel] 
#define UPDATE_WR_ID(channel,n)  CanCh##channel##Rx[n].w++; if (CanCh##channel##Rx[n].w==MCAN_QUELEN){ CanCh##channel##Rx[n].w=0u;}
///Updates the number of reads on the  message buffer structure for MB[n] engine[channel]
#define UPDATE_RD_ID(channel,n)  READ_ID(channel,n) +=1u; if (READ_ID(channel,n)==MCAN_QUELEN) {READ_ID(channel,n)=0u;}
 /* Invert the data endianness. */
#define CAN_ENDIANESS_INVERSION(data)     ((((uint32_t)(data) >> 24) & 0x000000FFUL) | (((uint32_t)(data) >> 8) & 0x0000FF00UL) | \
                                           (((uint32_t)(data) << 8) & 0x00FF0000UL) | (((uint32_t)(data) << 24) & 0xFF000000UL))
#pragma ghs endnomisra

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
///Struct for CAN configuration parameters)
typedef struct
{
    uint32_T ramAddressPointer;
    uint8_T maxDataSize;
    uint8_T stdFilterNumber;
    uint8_T xtdFilterNumber;
    uint8_T txBufferNumber;
    uint8_T rxBufferIntLine;
    uint8_T errCallback;
    uint8_T loopback;
    uint32_T timeoutInit;
} CANCfg_T;

/* Struct for address/id/dlc for every configured Rx Buffer */
typedef struct 
{
    uint32_T rxAddress;
    uint32_T id;
    uint8_T idType;
    uint32_T dlcAddress;
} CANRxBuff_T;

typedef struct {
    uint8_T buffStatus;
    uint8_T r;
    uint8_T w;
    CANBuffer_T buffer[MCAN_QUELEN] ;
} CanChB_T;

typedef struct {
    uint8_T rxLowMbUsed;
    uint8_T rxHighMbUsed;
} CanUsedMb_T;

///Type for peripheral memory mapped register 
typedef volatile struct MCAN_tag * MCANPtr_T;

/* MC - extern per visibilit� in can_events */
#if defined (CAN_CHA_EN)
#if (CAN_CHA_EN == 1u)
#if (MCAN_1_DEDICATED_RXMB_USED > 0u)
extern CanChB_T CanChARx[MCAN_1_DEDICATED_RXMB_USED];
#endif
#endif 
#endif 

#if defined (CAN_CHB_EN)
#if (CAN_CHB_EN == 1u)
extern CanChB_T CanChBRx[MCAN_2_DEDICATED_RXMB_USED];
#endif 
#endif 

#if defined (CAN_CHA_EN)
#if (CAN_CHA_TXRX_EXC)
extern void (*ExTxDoneChA)(void);
extern void (*ExRxDoneChA)(void);
#endif 
#endif 

#if defined (CAN_CHB_EN)
#if (CAN_CHB_TXRX_EXC)
extern void (*ExTxDoneChB)(void);
extern void (*ExRxDoneChB)(void);
#endif 
#endif 

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/


/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static int16_T CAN_ConfigCh (uint8_T channel, uint8_T bRate);
static int16_T CAN_RxBufferIdConfig (uint8_T channel, uint8_T nBuf, uint8_T nBufType, uint8_T idType, uint32_T id);
static int16_T CAN_TxBufferIdConfig (uint8_T channel, uint8_T txbuf, uint8_T idType, uint32_T frameId, uint8_T frameSize);
static uint8_T CAN_GetDLC (uint8_T frameSize);

#endif /* _MCAN_H_ */

/****************************************************************************
 ****************************************************************************/
