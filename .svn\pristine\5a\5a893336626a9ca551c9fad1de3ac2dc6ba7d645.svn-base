/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonKnockEn.c
 **  File Creation Date: 13-Sep-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonKnockEn
 **  Model Description:  This module evaluates enabling condition for knock detection strategy.
   It is triggered by:
   -Power On event, to reset outputs value.
   -10ms event, to calculate enabling thresholds on ion integral, engine load and spark advance correction.
   -End Of Acquisition event, to calculate the enabling condition of Knock detection strategy.

 **  Model Version:      1.988
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Mon Sep 13 12:28:16 2021
 **
 **  Last Saved Modification:  RoccaG - Mon Sep 13 12:27:13 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonKnockEn_out.h"
#include "IonKnockEn_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BIT_CALIB                      1U                        /* Referenced by: '<S1>/EnableCond' */

/* Id for enabling condition on VTIonKnockEnabled bit-mask. */
#define BIT_CAN_ENABLING               1024U                     /* Referenced by: '<S1>/EnableCond' */

/* Id for CAN enabling condition on VTIonKnockEnabled bit-mask. */
#define BIT_DSAOUT                     64U                       /* Referenced by: '<S1>/EnableCond' */

/* Id for stable spark advance correction condition on VTIonKnockEnabled bit-mask. */
#define BIT_ENGINE_AT_LIMITER          512U                      /* Referenced by: '<S1>/EnableCond' */

/* Id for engine at limiter condition on VTIonKnockEnabled bit-mask. */
#define BIT_ION_INT                    16U                       /* Referenced by:
                                                                  * '<S1>/EnableCond'
                                                                  * '<S15>/Constant1'
                                                                  */

/* Id for Ion integral condition on VTIonKnockEnabled bit-mask. */
#define BIT_ION_WINDOW                 32U                       /* Referenced by: '<S1>/EnableCond' */

/* Id for ion window condition on VTIonKnockEnabled bit-mask. */
#define BIT_LOAD                       2U                        /* Referenced by:
                                                                  * '<S1>/EnableCond'
                                                                  * '<S17>/Constant1'
                                                                  */

/* Id for load condition on VTIonKnockEnabled bit-mask. */
#define BIT_NMSPARK                    128U                      /* Referenced by: '<S1>/EnableCond' */

/* Id for spark number condition on VTIonKnockEnabled bit-mask. */
#define BIT_NUMTDCCRK                  256U                      /* Referenced by: '<S1>/EnableCond' */

/* Id for crank number condition on VTIonKnockEnabled bit-mask. */
#define BIT_SAOUT                      4U                        /* Referenced by:
                                                                  * '<S1>/EnableCond'
                                                                  * '<S19>/Constant1'
                                                                  */

/* Id for spark advance condition on VTIonKnockEnabled bit-mask. */
#define BIT_STMISF                     8U                        /* Referenced by: '<S1>/EnableCond' */

/* Id for misfire condition on VTIonKnockEnabled bit-mask. */
#define BKLOADIONKNOCK_dim             4U                        /* Referenced by:
                                                                  * '<S21>/Constant3'
                                                                  * '<S22>/Constant3'
                                                                  * '<S24>/Constant3'
                                                                  */

/* BKLOADIONKNOCK breakpoint dimension. */
#define BKRPMIONKNOCK_dim              11U                       /* Referenced by:
                                                                  * '<S21>/Constant1'
                                                                  * '<S22>/Constant1'
                                                                  * '<S23>/Constant1'
                                                                  * '<S23>/Constant3'
                                                                  * '<S24>/Constant1'
                                                                  */

/* BKRPMIONKNOCK breakpoint dimension. */
#define ID_VER_IONKNOCKEN_DEF          1988U                     /* Referenced by: '<S2>/Constant13' */

/* Model Version. */
#define MAX_LOAD_VALUE                 65280                     /* Referenced by: '<S23>/Constant6' */

/* Maximum value for load threshold */
#define MAX_UINT8                      255U                      /* Referenced by:
                                                                  * '<S8>/Constant3'
                                                                  * '<S14>/Constant5'
                                                                  */

/* Max value for uint8 type. */
#define RPM_TO_DEGREES_1               3U                        /* Referenced by: '<S8>/Constant' */

/* First coefficient (3) used to apply transformation from rpm to degree/s (in dwell time calculation). */
#define RPM_TO_DEGREES_2               32U                       /* Referenced by: '<S8>/Constant2' */

/* Second coefficient (32) used to apply transformation from rpm to degree/s (in dwell time calculation). */
#define SECOND_TO_MICROS               15625U                    /* Referenced by: '<S8>/Constant1' */

/* Transfonrmation from second to micro-second in dwell time calculation (1.000.000/64) taking account of fixed point resolution. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONKNOCKEN_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder
#endif

#if (N_CH_MAX != 4)
#error This code was generated with a different number of acquisition channel
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADIONKNOCK[5] = { 10240U, 15360U,
  20480U, 23680U, 26240U } ;           /* Referenced by: '<S21>/Constant2' */

/* Vector of steps for the load (IonKnock module) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMIONKNOCK[12] = { 1000U, 1500U,
  2000U, 2750U, 3500U, 4000U, 4750U, 5500U, 6000U, 6750U, 7500U, 8000U } ;/* Referenced by: '<S21>/Constant' */

/* Vector of steps for the engine speed (IonKnockEn module) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENIONKNOCK = 1U;/* Referenced by: '<S1>/Constant' */

/* IonKnock module enabling flag */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENSARONKNOCKEN = 0U;/* Referenced by: '<S24>/Constant5' */

/* Flag to enable SARon application to SA threshold */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T HYSTENINT = 5U;/* Referenced by: '<S9>/Constant4' */

/* Hyst on threshold on ion integral to enable the knocking detection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T HYSTENLOAD = 640U;/* Referenced by: '<S10>/Constant4' */

/* Hyst on threshold of load to enable IonKnock */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T HYSTENSAOUT = 48;/* Referenced by: '<S12>/Constant4' */

/* Hyst on threshold of spark advance to enable the knocking detection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T INTIONSEL = 0U;/* Referenced by: '<S15>/Constant4' */

/* Integral selection flag (=1 IntIon, =0 ThInt) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T IONKNOCKENMASK = 65535U;/* Referenced by: '<S1>/Constant1' */

/* IonKnockEnableCond Mask */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T IONKNOCKENTDCTHR = 200U;/* Referenced by: '<S11>/Constant3' */

/* CntTdcCrk threshold to set bit 8 of IonKnockEnableCond */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T MAXANGIGN2EOA = 240U;/* Referenced by:
                                                               * '<S8>/Constant4'
                                                               * '<S13>/Constant6'
                                                               */

/* Max distance between start of ign command and EOA */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TBTHRINTEN[60] = { 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U } ;/* Referenced by: '<S22>/Constant2' */

/* Threshold on ion integral to enable the knocking detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBTHRSAKNOCKEN[60] = { -5, -5, -5, -5,
  -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5,
  -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5,
  -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5, -5 } ;/* Referenced by: '<S24>/Constant2' */

/* Threshold of spark advance to enable the knocking detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T VTDLOADENKRON[5] = { -20, -18, -16, -14,
  -12 } ;                              /* Referenced by: '<S23>/Constant5' */

/* Vector for DLoadEnKRon */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTDTHRINTEN[5] = { -1000, -500, 0, 500,
  1000 } ;                             /* Referenced by: '<S22>/Constant5' */

/* Vector to enable IonKnock for ion integral as a function of RonLevel */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTLOADENKNOCK[12] = { 7680U, 7680U,
  10240U, 11520U, 12800U, 13440U, 14080U, 14080U, 14080U, 14080U, 14080U, 14080U
} ;                                    /* Referenced by: '<S23>/Constant2' */

/* Vector of thresholds of load to enable IonKnock */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T VTMINDLOAD[12] = { 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0 } ;                    /* Referenced by: '<S23>/Constant4' */

/* Min Delta Load correction */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T FlgLoadEnKnock[8];             /* '<S3>/Merge3' */

/* Knock enabled for load condition */
uint8_T IonKnockEnabled;               /* '<S3>/Merge1' */

/* Flag that indicates whether IonKnock is enabled or not. */
uint16_T IonKnockLoadIndex;            /* '<S3>/Merge7' */

/* Index on BKLOADIONKNOCK breakpoints for pre-lookup */
uint16_T IonKnockLoadRatio;            /* '<S3>/Merge8' */

/* Ratio on BKLOADIONKNOCK breakpoints pre-lookup */
uint16_T IonKnockRpmIndex;             /* '<S3>/Merge6' */

/* Index on BKRPMIONKNOCK breakpoints for pre-lookup */
uint16_T IonKnockRpmRatio;             /* '<S3>/Merge2' */

/* Ratio on BKRPMIONKNOCK breakpoints pre-lookup */
uint16_T VtIonKnockEnableCond[8];      /* '<S3>/Merge' */

/* Bitwise variable: BIT_0 = enabled by calibration; BIT_1 = enabled for Load; BIT_2 = enabled for Saout; BIT_3 = enabled for StMisf; BIT_4 = enabled for ion integral; BIT_5 = enabled for valid IonWindow; BIT_6 = enabled for DSAoutCyl; BIT_7 = enabled for NMSparkPrg[cyl]; BIT_8 = enabled for CntTdcCrk */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint8_T EffDwellAngle;/* '<S3>/Merge10' */

/* EffDwellTime converted in angle */
STATIC_TEST_POINT uint8_T FlgDisableKnock4Dwell;/* '<S3>/Merge11' */

/* Knock disabled for dwell too high */
STATIC_TEST_POINT uint32_T IdVer_IonKnockEn;/* '<S2>/Constant13' */

/* Model Version */
STATIC_TEST_POINT uint16_T IntegralThreshold;/* '<S3>/Merge9' */

/* Integral threshold to enable Knock */
STATIC_TEST_POINT uint16_T LoadEnKnock;/* '<S3>/Merge4' */

/* Load threshold to enable IonKnock */
STATIC_TEST_POINT int16_T SAThreshold; /* '<S3>/Merge5' */

/* Spark advance threshold to enable Knock */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void IonKnockEn_10ms(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_h;
  uint16_T rtb_Look2D_IR_U8;
  uint16_T rtb_LookUp_IR_U16;
  uint8_T rtb_RonLevel;
  int16_T rtb_MinMax_h;
  int16_T rtb_Conversion;
  int16_T rtb_Add;
  int32_T rtb_MinMax1_e;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_e;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockEn_10ms' incorporates:
   *  SubSystem: '<Root>/T10ms'
   *
   * Block description for '<Root>/T10ms':
   *  Runnable at 10ms.
   */
  /* Switch: '<S21>/Switch' incorporates:
   *  Constant: '<S21>/Constant4'
   *  Constant: '<S21>/Constant5'
   *  Constant: '<S21>/Constant7'
   *  Inport: '<Root>/FlgRonInheritEE'
   *  Inport: '<Root>/FlgRonStoredEE'
   *  Logic: '<S21>/LogicalOperator'
   *  RelationalOperator: '<S21>/RelationalOperator'
   *  RelationalOperator: '<S21>/RelationalOperator1'
   */
  if ((((int32_T)FlgRonInheritEE) != 0) || (((int32_T)FlgRonStoredEE) != 0)) {
    /* MinMax: '<S21>/MinMax' incorporates:
     *  Constant: '<S21>/Constant6'
     *  Inport: '<Root>/RonLevelUsed'
     */
    if (RonLevelUsed < MAX_RONLEVEL_EXP) {
      rtb_RonLevel = RonLevelUsed;
    } else {
      rtb_RonLevel = MAX_RONLEVEL_EXP;
    }

    /* End of MinMax: '<S21>/MinMax' */
  } else {
    rtb_RonLevel = MAX_RONLEVEL_EXP;
  }

  /* End of Switch: '<S21>/Switch' */

  /* DataTypeConversion: '<S23>/Conversion2' incorporates:
   *  Constant: '<S23>/Constant5'
   *  Selector: '<S23>/Selector3'
   */
  rtb_MinMax_h = (int16_T)((int32_T)(((int32_T)VTDLOADENKRON[(rtb_RonLevel)]) *
    64));

  /* Sum: '<S23>/Add' incorporates:
   *  Inport: '<Root>/DLoadTAir'
   */
  rtb_Add = (int16_T)(DLoadTAir + rtb_MinMax_h);

  /* DataTypeConversion: '<S26>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_PreLookUpIdSearch_U16_o1 = Rpm;

  /* S-Function (PreLookUpIdSearch_U16): '<S26>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S21>/Constant'
   *  Constant: '<S21>/Constant1'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2,
                        rtb_PreLookUpIdSearch_U16_o1, &BKRPMIONKNOCK[0],
                        ((uint8_T)BKRPMIONKNOCK_dim));

  /* DataTypeConversion: '<S25>/Data Type Conversion4' incorporates:
   *  Inport: '<Root>/Load'
   */
  rtb_PreLookUpIdSearch_U16_o1_e = Load;

  /* S-Function (PreLookUpIdSearch_U16): '<S25>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S21>/Constant2'
   *  Constant: '<S21>/Constant3'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_e,
                        &rtb_PreLookUpIdSearch_U16_o2_h,
                        rtb_PreLookUpIdSearch_U16_o1_e, &BKLOADIONKNOCK[0],
                        ((uint8_T)BKLOADIONKNOCK_dim));

  /* S-Function (Look2D_IR_U8): '<S27>/Look2D_IR_U8' incorporates:
   *  Constant: '<S22>/Constant1'
   *  Constant: '<S22>/Constant2'
   *  Constant: '<S22>/Constant3'
   *
   * Block requirements for '<S22>/Constant2':
   *  1. EISB_FCA6CYL_SW_REQ_1068: To calculate enabling conditions for knock detection strategy, sof... (ECU_SW_Requirements#1125)
   */
  Look2D_IR_U8( &rtb_Look2D_IR_U8, &TBTHRINTEN[0], rtb_PreLookUpIdSearch_U16_o1,
               rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKRPMIONKNOCK_dim),
               rtb_PreLookUpIdSearch_U16_o1_e, rtb_PreLookUpIdSearch_U16_o2_h,
               ((uint8_T)BKLOADIONKNOCK_dim));

  /* S-Function (LookUp_IR_U16): '<S30>/LookUp_IR_U16' incorporates:
   *  Constant: '<S23>/Constant1'
   *  Constant: '<S23>/Constant2'
   *
   * Block requirements for '<S23>/Constant2':
   *  1. EISB_FCA6CYL_SW_REQ_1078: To calculate enabling conditions for knock detection strategy, sof... (ECU_SW_Requirements#1127)
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTLOADENKNOCK[0],
                rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                ((uint8_T)BKRPMIONKNOCK_dim));

  /* S-Function (LookUp_IR_S8): '<S29>/LookUp_IR_S8' incorporates:
   *  Constant: '<S23>/Constant3'
   *  Constant: '<S23>/Constant4'
   */
  LookUp_IR_S8( &rtb_MinMax_h, &VTMINDLOAD[0], rtb_PreLookUpIdSearch_U16_o1,
               rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKRPMIONKNOCK_dim));

  /* Product: '<S28>/Divide' */
  rtb_Conversion = (int16_T)(rtb_MinMax_h / 4);

  /* MinMax: '<S23>/MinMax'
   *
   * Block requirements for '<S23>/MinMax':
   *  1. EISB_FCA6CYL_SW_REQ_1081: Software shall correct the engine load threshold (i.e. LoadEnKnock... (ECU_SW_Requirements#1128)
   */
  if (rtb_Add > rtb_Conversion) {
    rtb_MinMax_h = rtb_Add;
  } else {
    rtb_MinMax_h = rtb_Conversion;
  }

  /* End of MinMax: '<S23>/MinMax' */

  /* Sum: '<S23>/Add1' incorporates:
   *  DataTypeConversion: '<S23>/Conversion4'
   */
  rtb_MinMax1_e = ((int32_T)rtb_LookUp_IR_U16) + ((int32_T)rtb_MinMax_h);

  /* MinMax: '<S23>/MinMax1' incorporates:
   *  Constant: '<S23>/Constant'
   */
  if (rtb_MinMax1_e <= 0) {
    rtb_MinMax1_e = 0;
  }

  /* MinMax: '<S23>/MinMax2' incorporates:
   *  Constant: '<S23>/Constant6'
   *  MinMax: '<S23>/MinMax1'
   */
  if (rtb_MinMax1_e < MAX_LOAD_VALUE) {
    /* DataTypeConversion: '<S23>/Conversion3' */
    LoadEnKnock = (uint16_T)rtb_MinMax1_e;
  } else {
    /* DataTypeConversion: '<S23>/Conversion3' */
    LoadEnKnock = (uint16_T)MAX_LOAD_VALUE;
  }

  /* End of MinMax: '<S23>/MinMax2' */

  /* S-Function (Look2D_IR_S8): '<S33>/Look2D_IR_S8' incorporates:
   *  Constant: '<S24>/Constant1'
   *  Constant: '<S24>/Constant2'
   *  Constant: '<S24>/Constant3'
   *
   * Block requirements for '<S24>/Constant2':
   *  1. EISB_FCA6CYL_SW_REQ_1080: To calculate enabling condition for knock detection strategy, soft... (ECU_SW_Requirements#1129)
   */
  Look2D_IR_S8( &rtb_Conversion, &TBTHRSAKNOCKEN[0],
               rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
               ((uint8_T)BKRPMIONKNOCK_dim), rtb_PreLookUpIdSearch_U16_o1_e,
               rtb_PreLookUpIdSearch_U16_o2_h, ((uint8_T)BKLOADIONKNOCK_dim));

  /* Switch: '<S24>/Switch1' incorporates:
   *  Constant: '<S24>/Constant5'
   *  DataTypeConversion: '<S24>/Conversion'
   *  Inport: '<Root>/SARon'
   *  Product: '<S32>/Divide'
   *  Sum: '<S24>/Add'
   *
   * Block requirements for '<S24>/Switch1':
   *  1. EISB_FCA6CYL_SW_REQ_1090: Software shall correct the spark advance threshold  (i.e. SAThresh... (ECU_SW_Requirements#1130)
   */
  if (((int32_T)ENSARONKNOCKEN) != 0) {
    SAThreshold = (int16_T)((rtb_Conversion / 16) + SARon);
  } else {
    SAThreshold = (int16_T)(rtb_Conversion / 16);
  }

  /* End of Switch: '<S24>/Switch1' */

  /* DataTypeConversion: '<S22>/Conversion' */
  rtb_Conversion = (int16_T)((uint32_T)(((uint32_T)rtb_Look2D_IR_U8) >>
    ((uint32_T)3)));

  /* Sum: '<S22>/Add' incorporates:
   *  Constant: '<S22>/Constant5'
   *  Selector: '<S22>/Selector3'
   *
   * Block requirements for '<S22>/Add':
   *  1. EISB_FCA6CYL_SW_REQ_1084: Software shall correct the integral threshold (i.e. IntegralThresh... (ECU_SW_Requirements#1126)
   */
  rtb_MinMax1_e = ((int32_T)rtb_Conversion) + ((int32_T)VTDTHRINTEN
    [(rtb_RonLevel)]);

  /* MinMax: '<S22>/MinMax1' */
  if (rtb_MinMax1_e > 0) {
    /* DataTypeConversion: '<S22>/Conversion1' */
    IntegralThreshold = (uint16_T)rtb_MinMax1_e;
  } else {
    /* DataTypeConversion: '<S22>/Conversion1' */
    IntegralThreshold = 0U;
  }

  /* End of MinMax: '<S22>/MinMax1' */

  /* SignalConversion generated from: '<S4>/IonKnockLoadIndex' */
  IonKnockLoadIndex = rtb_PreLookUpIdSearch_U16_o1_e;

  /* SignalConversion generated from: '<S4>/IonKnockLoadRatio' */
  IonKnockLoadRatio = rtb_PreLookUpIdSearch_U16_o2_h;

  /* SignalConversion generated from: '<S4>/IonKnockRpmIndex' */
  IonKnockRpmIndex = rtb_PreLookUpIdSearch_U16_o1;

  /* SignalConversion generated from: '<S4>/IonKnockRpmRatio' */
  IonKnockRpmRatio = rtb_PreLookUpIdSearch_U16_o2;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockEn_10ms' */
}

/* Model step function */
void IonKnockEn_EOA(void)
{
  uint16_T LoadThreshold;
  int16_T SAHThreshold;
  boolean_T rtb_LoadOnCond;
  uint16_T rtb_IonKnockEnableCond;
  uint8_T rtb_Conversion1;
  int32_T tmp;
  uint16_T u0;
  uint16_T tmp_0;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockEn_EOA' incorporates:
   *  SubSystem: '<Root>/EOA'
   *
   * Block description for '<Root>/EOA':
   *  Runnable at end of acquisition event.
   */
  /* Chart: '<S10>/Chart' incorporates:
   *  Constant: '<S10>/Constant4'
   *  Constant: '<S17>/Constant1'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/Load'
   *  S-Function (sfix_bitop): '<S17>/BitwiseAND'
   *  Selector: '<S17>/Selector3'
   *  SignalConversion generated from: '<S1>/VtIonKnockEnableCond_old'
   *
   * Block requirements for '<S17>/BitwiseAND':
   *  1. EISB_FCA6CYL_SW_REQ_1100: (DC1)
     Software shall disable knock detection strategy, cylinder b... (ECU_SW_Requirements#1134)
   */
  /* Gateway: EOA/EvaluateConditions/LoadOn_Cond/Chart */
  /* During: EOA/EvaluateConditions/LoadOn_Cond/Chart */
  /* Evaluation of condition on engine load. */
  /* Entry Internal: EOA/EvaluateConditions/LoadOn_Cond/Chart */
  /* Transition: '<S18>:8' */
  /*  Verify the old value for load condition
     in order to apply or not an hysteresis  */
  if ((((int32_T)VtIonKnockEnableCond[(IonAbsTdcEOA)]) & ((int32_T)((uint16_T)
         BIT_LOAD))) > 0) {
    /* Transition: '<S18>:10' */
    /*  Before applying the hysteresis verify that
       the theshold value is greater than the hysteresis  */
    if (LoadEnKnock > HYSTENLOAD) {
      /* Transition: '<S18>:15' */
      /* Transition: '<S18>:20' */
      tmp = ((int32_T)LoadEnKnock) - ((int32_T)HYSTENLOAD);
      if (tmp < 0) {
        tmp = 0;
      }

      LoadThreshold = (uint16_T)tmp;
    } else {
      /* Transition: '<S18>:17' */
      LoadThreshold = 0U;

      /* Transition: '<S18>:21' */
    }
  } else {
    /* Transition: '<S18>:13' */
    LoadThreshold = LoadEnKnock;

    /* Transition: '<S18>:18' */
    /* Transition: '<S18>:21' */
  }

  /* Transition: '<S18>:25'
   * Requirements for Transition: '<S18>:25':
   *  1. EISB_FCA6CYL_SW_REQ_1100: (DC1)
     Software shall disable knock detection strategy, cylinder b... (ECU_SW_Requirements#1134)
   */
  rtb_LoadOnCond = (Load > LoadThreshold);

  /* End of Chart: '<S10>/Chart' */

  /* Chart: '<S12>/Chart' incorporates:
   *  Constant: '<S12>/Constant4'
   *  Constant: '<S19>/Constant1'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  S-Function (sfix_bitop): '<S19>/BitwiseAND'
   *  Selector: '<S19>/Selector3'
   *  SignalConversion generated from: '<S1>/VtIonKnockEnableCond_old'
   *
   * Block requirements for '<S19>/BitwiseAND':
   *  1. EISB_FCA6CYL_SW_REQ_1108: (DC2)
     Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1135)
   */
  /* Gateway: EOA/EvaluateConditions/SAOut_Cond/Chart */
  /* During: EOA/EvaluateConditions/SAOut_Cond/Chart */
  /* Evaluation of condition on knock advance correction. */
  /* Entry Internal: EOA/EvaluateConditions/SAOut_Cond/Chart */
  /* Transition: '<S20>:8' */
  /*  Verify the old value for spark advance condition
     in order to apply or not an hysteresis  */
  if ((((int32_T)VtIonKnockEnableCond[(IonAbsTdcEOA)]) & ((int32_T)((uint16_T)
         BIT_SAOUT))) > 0) {
    /* Transition: '<S20>:10' */
    /* Transition: '<S20>:20' */
    SAHThreshold = (int16_T)(SAThreshold - HYSTENSAOUT);
  } else {
    /* Transition: '<S20>:13' */
    SAHThreshold = SAThreshold;

    /* Transition: '<S20>:18' */
  }

  /* Chart: '<S9>/Chart' incorporates:
   *  Constant: '<S15>/Constant1'
   *  Constant: '<S9>/Constant4'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  S-Function (sfix_bitop): '<S15>/BitwiseAND'
   *  Selector: '<S15>/Selector3'
   *  SignalConversion generated from: '<S1>/VtIonKnockEnableCond_old'
   *
   * Block requirements for '<S15>/BitwiseAND':
   *  1. EISB_FCA6CYL_SW_REQ_1102: (DC4)
     Software shall select, for each cylinder, the current ion in... (ECU_SW_Requirements#1137)
   */
  /* Transition: '<S20>:26'
   * Requirements for Transition: '<S20>:26':
   *  1. EISB_FCA6CYL_SW_REQ_1108: (DC2)
     Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1135)
   */
  /* Gateway: EOA/EvaluateConditions/IntIon_Cond/Chart */
  /* During: EOA/EvaluateConditions/IntIon_Cond/Chart */
  /* Evaluation of condition on ion integral. */
  /* Entry Internal: EOA/EvaluateConditions/IntIon_Cond/Chart */
  /* Transition: '<S16>:8' */
  /*  Verify the old value for integral condition
     in order to apply or not an hysteresis  */
  if ((((int32_T)VtIonKnockEnableCond[(IonAbsTdcEOA)]) & ((int32_T)((uint16_T)
         BIT_ION_INT))) > 0) {
    /* Transition: '<S16>:10' */
    /*  Before applying the hysteresis verify that
       the theshold value is greater than the hysteresis  */
    if (IntegralThreshold > HYSTENINT) {
      /* Transition: '<S16>:15' */
      /* Transition: '<S16>:20' */
      tmp = ((int32_T)IntegralThreshold) - ((int32_T)HYSTENINT);
      if (tmp < 0) {
        tmp = 0;
      }

      LoadThreshold = (uint16_T)tmp;
    } else {
      /* Transition: '<S16>:17' */
      LoadThreshold = 0U;

      /* Transition: '<S16>:21' */
    }
  } else {
    /* Transition: '<S16>:13' */
    LoadThreshold = IntegralThreshold;

    /* Transition: '<S16>:18' */
    /* Transition: '<S16>:21' */
  }

  /* Product: '<S8>/Divide1' incorporates:
   *  Constant: '<S8>/Constant'
   *  Constant: '<S8>/Constant1'
   *  Constant: '<S8>/Constant2'
   *  Inport: '<Root>/EffDwellTime'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/Rpm'
   *  Product: '<S8>/Divide'
   *  Product: '<S8>/Product'
   *  Product: '<S8>/Product2'
   *  Selector: '<S8>/Selector'
   */
  /* Transition: '<S16>:26'
   * Requirements for Transition: '<S16>:26':
   *  1. EISB_FCA6CYL_SW_REQ_1102: (DC4)
     Software shall select, for each cylinder, the current ion in... (ECU_SW_Requirements#1137)
   */
  u0 = (uint16_T)(((uint32_T)((uint16_T)(((((uint32_T)Rpm) * EffDwellTime
    [(IonAbsTdcEOA)]) * ((uint32_T)((uint8_T)RPM_TO_DEGREES_1))) / ((uint32_T)
    ((uint16_T)SECOND_TO_MICROS))))) / ((uint32_T)((uint16_T)RPM_TO_DEGREES_2)));

  /* MinMax: '<S8>/MinMax' incorporates:
   *  Constant: '<S8>/Constant3'
   */
  if (u0 < ((uint16_T)((uint8_T)MAX_UINT8))) {
    /* DataTypeConversion: '<S8>/Conversion1' */
    rtb_Conversion1 = (uint8_T)u0;
  } else {
    /* DataTypeConversion: '<S8>/Conversion1' */
    rtb_Conversion1 = ((uint8_T)MAX_UINT8);
  }

  /* If: '<S8>/If' incorporates:
   *  Constant: '<S13>/Constant6'
   *  Constant: '<S14>/Constant5'
   *  Constant: '<S8>/Constant4'
   *  RelationalOperator: '<S8>/RelationaOperator'
   *  SignalConversion generated from: '<S14>/Threshold'
   *  Sum: '<S13>/Add'
   */
  if (MAXANGIGN2EOA <= rtb_Conversion1) {
    /* Outputs for IfAction SubSystem: '<S8>/IfActionSubsystem1' incorporates:
     *  ActionPort: '<S14>/Action Port'
     *
     * Block description for '<S8>/IfActionSubsystem1':
     *  Safety calculation of ion window threshold to avoid negative overflow.
     */
    rtb_Conversion1 = ((uint8_T)MAX_UINT8);

    /* End of Outputs for SubSystem: '<S8>/IfActionSubsystem1' */
  } else {
    /* Outputs for IfAction SubSystem: '<S8>/IfActionSubsystem' incorporates:
     *  ActionPort: '<S13>/Action Port'
     *
     * Block description for '<S8>/IfActionSubsystem':
     *  Nominal calculation of ion window threshold.
     */
    rtb_Conversion1 = (uint8_T)(((uint32_T)MAXANGIGN2EOA) - ((uint32_T)
      rtb_Conversion1));

    /* End of Outputs for SubSystem: '<S8>/IfActionSubsystem' */
  }

  /* End of If: '<S8>/If' */

  /* DataTypeConversion: '<S8>/Conversion2' incorporates:
   *  Inport: '<Root>/IonChannelEOA'
   *  Inport: '<Root>/IonWindow'
   *  RelationalOperator: '<S8>/RelationalOperator1'
   *  Selector: '<S8>/Selector1'
   *
   * Block requirements for '<S8>/RelationalOperator1':
   *  1. EISB_FCA6CYL_SW_REQ_1114: (DC5)
     Software shall disable knock detection strategy every time t... (ECU_SW_Requirements#1138)
   */
  rtb_Conversion1 = (uint8_T)((IonWindow[(IonChannelEOA)] > rtb_Conversion1) ? 1
    : 0);

  /* Chart: '<S1>/EnableCond' incorporates:
   *  Chart: '<S12>/Chart'
   *  Constant: '<S11>/Constant'
   *  Constant: '<S1>/Constant'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/SAoutCyl'
   *  Inport: '<Root>/StMisf'
   *  RelationalOperator: '<S11>/RelationalOperator'
   *  Selector: '<S11>/Selector1'
   *  Selector: '<S19>/Selector1'
   *
   * Block description for '<S1>/EnableCond':
   *  All the enabling condition are evaluated in order to enable/disable the knock detection strategy.
   *
   *  A bit mask is produced in order to store the value of each condition for each cylinder.
   *
   *  Only the condition indicated in the IONKNOCKENMASK are considered in order to enable/disable the knock detection strategy.
   *
   * Block requirements for '<S11>/RelationalOperator':
   *  1. EISB_FCA6CYL_SW_REQ_1109: (DC3)Software shall disable knock detection strategy for the i_th ... (ECU_SW_Requirements#1136)
   */
  /* Gateway: EOA/EnableCond */
  /* During: EOA/EnableCond */
  /* All the enabling conditions are evaluated in order to enable/disable the knock detection strategy.

     A bit mask is produced in order to store the value of each condition for each cylinder.

     Only conditions indicated in the IONKNOCKENMASK are considered in order to enable/disable the knock detection strategy.  */
  /* Entry Internal: EOA/EnableCond */
  /* Transition: '<S5>:2' */
  rtb_IonKnockEnableCond = 0U;
  if (((int32_T)ENIONKNOCK) != 0) {
    /* Transition: '<S5>:6'
     * Requirements for Transition: '<S5>:6':
     *  1. EISB_FCA6CYL_SW_REQ_1152: (DC0)
       Software shall disable knock detection strategy, cylinder b... (ECU_SW_Requirements#1133)
     */
    /* Transition: '<S5>:8' */
    rtb_IonKnockEnableCond = ((uint16_T)BIT_CALIB);

    /* Transition: '<S5>:14' */
  } else {
    /* Transition: '<S5>:12' */
  }

  /* Transition: '<S5>:16' */
  if (rtb_LoadOnCond) {
    /* Transition: '<S5>:18'
     * Requirements for Transition: '<S5>:18':
     *  1. EISB_FCA6CYL_SW_REQ_1100: (DC1)
       Software shall disable knock detection strategy, cylinder b... (ECU_SW_Requirements#1134)
     */
    /* Transition: '<S5>:20' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_LOAD);

    /* Transition: '<S5>:23' */
  } else {
    /* Transition: '<S5>:22' */
  }

  /* Transition: '<S5>:35' */
  if (SAoutCyl[(IonAbsTdcEOA)] > SAHThreshold) {
    /* Transition: '<S5>:32'
     * Requirements for Transition: '<S5>:32':
     *  1. EISB_FCA6CYL_SW_REQ_1108: (DC2)
       Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1135)
     */
    /* Transition: '<S5>:27' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_SAOUT);

    /* Transition: '<S5>:28' */
  } else {
    /* Transition: '<S5>:30' */
  }

  /* Transition: '<S5>:46' */
  if (((uint32_T)StMisf[(IonAbsTdcEOA)]) == NO_MISF) {
    /* Transition: '<S5>:44'
     * Requirements for Transition: '<S5>:44':
     *  1. EISB_FCA6CYL_SW_REQ_1109: (DC3)Software shall disable knock detection strategy for the i_th ... (ECU_SW_Requirements#1136)
     */
    /* Transition: '<S5>:42' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_STMISF);

    /* Transition: '<S5>:38' */
  } else {
    /* Transition: '<S5>:40' */
  }

  /* Switch: '<S15>/Switch' incorporates:
   *  Constant: '<S15>/Constant4'
   *  DataTypeConversion: '<S15>/Conversion'
   *  Inport: '<Root>/IntIon'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/ThInt'
   *  Selector: '<S15>/Selector1'
   *  Selector: '<S15>/Selector2'
   *
   * Block requirements for '<S15>/Switch':
   *  1. EISB_FCA6CYL_SW_REQ_1102: (DC4)
     Software shall select, for each cylinder, the current ion in... (ECU_SW_Requirements#1137)
   */
  /* Transition: '<S5>:51' */
  if (((int32_T)INTIONSEL) != 0) {
    tmp_0 = IntIon[(IonAbsTdcEOA)];
  } else {
    tmp_0 = ThInt[(IonAbsTdcEOA)];
  }

  /* End of Switch: '<S15>/Switch' */

  /* Chart: '<S1>/EnableCond' incorporates:
   *  Chart: '<S9>/Chart'
   *  Constant: '<S11>/Constant1'
   *  Constant: '<S11>/Constant2'
   *  Constant: '<S11>/Constant3'
   *  Constant: '<S11>/Constant4'
   *  Constant: '<S11>/Constant5'
   *  Inport: '<Root>/CntTdcCrk'
   *  Inport: '<Root>/EngineAtLimiter'
   *  Inport: '<Root>/FlgDSAoutDis'
   *  Inport: '<Root>/ISCMKnkPIUsedByECMCAN'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/VtStPlasObjDBuff'
   *  RelationalOperator: '<S11>/RelationalOperator1'
   *  RelationalOperator: '<S11>/RelationalOperator2'
   *  RelationalOperator: '<S11>/RelationalOperator3'
   *  RelationalOperator: '<S11>/RelationalOperator4'
   *  RelationalOperator: '<S11>/RelationalOperator5'
   *  Selector: '<S11>/Selector2'
   *
   * Block description for '<S1>/EnableCond':
   *  All the enabling condition are evaluated in order to enable/disable the knock detection strategy.
   *
   *  A bit mask is produced in order to store the value of each condition for each cylinder.
   *
   *  Only the condition indicated in the IONKNOCKENMASK are considered in order to enable/disable the knock detection strategy.
   *
   * Block requirements for '<S11>/RelationalOperator1':
   *  1. EISB_FCA6CYL_SW_REQ_1111: (DC7)
     Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1140)
   *
   * Block requirements for '<S11>/RelationalOperator2':
   *  1. EISB_FCA6CYL_SW_REQ_1110: (DC6)
     Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1139)
   *
   * Block requirements for '<S11>/RelationalOperator3':
   *  1. EISB_FCA6CYL_SW_REQ_1112: (DC8)
     Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1141)
   *
   * Block requirements for '<S11>/RelationalOperator4':
   *  1. EISB_FCA6CYL_SW_REQ_1113: (DC9)
     Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1142)
   *
   * Block requirements for '<S11>/RelationalOperator5':
   *  1. EISB_FCA6CYL_SW_REQ_1966: (DC10)
     Software shall disable knock detection strategy for the i_t... (ECU_SW_Requirements#8237)
   */
  if (tmp_0 > LoadThreshold) {
    /* Transition: '<S5>:57'
     * Requirements for Transition: '<S5>:57':
     *  1. EISB_FCA6CYL_SW_REQ_1102: (DC4)
       Software shall select, for each cylinder, the current ion in... (ECU_SW_Requirements#1137)
     */
    /* Transition: '<S5>:55' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_ION_INT);

    /* Transition: '<S5>:136' */
  } else {
    /* Transition: '<S5>:54' */
  }

  /* Transition: '<S5>:121' */
  if (((int32_T)rtb_Conversion1) == 0) {
    /* Transition: '<S5>:60'
     * Requirements for Transition: '<S5>:60':
     *  1. EISB_FCA6CYL_SW_REQ_1114: (DC5)
       Software shall disable knock detection strategy every time t... (ECU_SW_Requirements#1138)
     */
    /* Transition: '<S5>:63' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_ION_WINDOW);
  } else {
    /* Transition: '<S5>:62' */
    /* Transition: '<S5>:137' */
  }

  /* Transition: '<S5>:139' */
  /* Transition: '<S5>:79' */
  if (((int32_T)FlgDSAoutDis) == 0) {
    /* Transition: '<S5>:77'
     * Requirements for Transition: '<S5>:77':
     *  1. EISB_FCA6CYL_SW_REQ_1110: (DC6)
       Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1139)
     */
    /* Transition: '<S5>:74' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_DSAOUT);

    /* Transition: '<S5>:72' */
  } else {
    /* Transition: '<S5>:76' */
  }

  /* Transition: '<S5>:88' */
  if (VtStPlasObjDBuff[(IonAbsTdcEOA)] == ION_ST_SEL) {
    /* Transition: '<S5>:80'
     * Requirements for Transition: '<S5>:80':
     *  1. EISB_FCA6CYL_SW_REQ_1111: (DC7)
       Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1140)
     */
    /* Transition: '<S5>:82' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_NMSPARK);

    /* Transition: '<S5>:85' */
  } else {
    /* Transition: '<S5>:86' */
  }

  /* Transition: '<S5>:93' */
  if (CntTdcCrk >= IONKNOCKENTDCTHR) {
    /* Transition: '<S5>:100'
     * Requirements for Transition: '<S5>:100':
     *  1. EISB_FCA6CYL_SW_REQ_1112: (DC8)
       Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1141)
     */
    /* Transition: '<S5>:97' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_NUMTDCCRK);

    /* Transition: '<S5>:96' */
  } else {
    /* Transition: '<S5>:94' */
  }

  /* Transition: '<S5>:113' */
  if (((int32_T)EngineAtLimiter) == 0) {
    /* Transition: '<S5>:105'
     * Requirements for Transition: '<S5>:105':
     *  1. EISB_FCA6CYL_SW_REQ_1113: (DC9)
       Software shall disable knock detection strategy for the i_th... (ECU_SW_Requirements#1142)
     */
    /* Transition: '<S5>:108' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_ENGINE_AT_LIMITER);

    /* Transition: '<S5>:140' */
  } else {
    /* Transition: '<S5>:112' */
  }

  /* Transition: '<S5>:142' */
  if (((int32_T)ISCMKnkPIUsedByECMCAN) == 1) {
    /* Transition: '<S5>:147'
     * Requirements for Transition: '<S5>:147':
     *  1. EISB_FCA6CYL_SW_REQ_1966: (DC10)
       Software shall disable knock detection strategy for the i_t... (ECU_SW_Requirements#8237)
     */
    /* Transition: '<S5>:144' */
    rtb_IonKnockEnableCond |= ((uint16_T)BIT_CAN_ENABLING);
  } else {
    /* Transition: '<S5>:145' */
    /* Transition: '<S5>:150' */
  }

  /* Assignment: '<S10>/Assignment' incorporates:
   *  DataTypeConversion: '<S10>/Conversion'
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  /* Transition: '<S5>:126'
   * Requirements for Transition: '<S5>:126':
   *  1. EISB_FCA6CYL_SW_REQ_1150: Software shall produce a signal (i.e. IonKnockEnabled) whose value... (ECU_SW_Requirements#1132)
   */
  FlgLoadEnKnock[(IonAbsTdcEOA)] = (uint8_T)(rtb_LoadOnCond ? ((uint8_T)1) :
    ((uint8_T)0));

  /* MinMax: '<S8>/MinMax' incorporates:
   *  Constant: '<S8>/Constant3'
   */
  if (u0 < ((uint16_T)((uint8_T)MAX_UINT8))) {
    /* SignalConversion generated from: '<S1>/EffDwellAngle' incorporates:
     *  DataTypeConversion: '<S8>/Conversion1'
     */
    EffDwellAngle = (uint8_T)u0;
  } else {
    /* SignalConversion generated from: '<S1>/EffDwellAngle' */
    EffDwellAngle = ((uint8_T)MAX_UINT8);
  }

  /* SignalConversion generated from: '<S1>/FlgDisableKnock4Dwell' */
  FlgDisableKnock4Dwell = rtb_Conversion1;

  /* SignalConversion generated from: '<S1>/IonKnockEnabled' incorporates:
   *  Chart: '<S1>/EnableCond'
   *  Constant: '<S1>/Constant1'
   *
   * Block description for '<S1>/EnableCond':
   *  All the enabling condition are evaluated in order to enable/disable the knock detection strategy.
   *
   *  A bit mask is produced in order to store the value of each condition for each cylinder.
   *
   *  Only the condition indicated in the IONKNOCKENMASK are considered in order to enable/disable the knock detection strategy.
   */
  IonKnockEnabled = (uint8_T)(((rtb_IonKnockEnableCond & IONKNOCKENMASK) ==
    IONKNOCKENMASK) ? 1 : 0);

  /* Assignment: '<S7>/Assignment' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  VtIonKnockEnableCond[(IonAbsTdcEOA)] = rtb_IonKnockEnableCond;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockEn_EOA' */
}

/* Model step function */
void IonKnockEn_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockEn_PowerOn' incorporates:
   *  SubSystem: '<Root>/Power_On'
   *
   * Block description for '<Root>/Power_On':
   *  Initialization at Power On for each variable (output, test-point) used
   *  in the model.
   *
   * Block requirements for '<Root>/Power_On':
   *  1. EISB_FCA6CYL_SW_REQ_1633: Software shall set to 0 each output produced for Enabling Conditions functionality at ECU power on. (ECU_SW_Requirements#3019)
   */
  /* SignalConversion generated from: '<S2>/VtIonKnockEnableCond' */
  memset((&(VtIonKnockEnableCond[0])), 0, (sizeof(uint16_T)) << 3U);

  /* SignalConversion generated from: '<S2>/FlgLoadEnKnock' */
  memset((&(FlgLoadEnKnock[0])), 0, (sizeof(uint8_T)) << 3U);

  /* SignalConversion generated from: '<S2>/IonKnockEnabled' incorporates:
   *  Constant: '<S2>/Constant10'
   */
  IonKnockEnabled = 0U;

  /* SignalConversion generated from: '<S2>/IonKnockRpmIndex' incorporates:
   *  Constant: '<S2>/Constant1'
   */
  IonKnockRpmIndex = 0U;

  /* SignalConversion generated from: '<S2>/IonKnockRpmRatio' incorporates:
   *  Constant: '<S2>/Constant2'
   */
  IonKnockRpmRatio = 0U;

  /* SignalConversion generated from: '<S2>/IonKnockLoadIndex' incorporates:
   *  Constant: '<S2>/Constant3'
   */
  IonKnockLoadIndex = 0U;

  /* SignalConversion generated from: '<S2>/IonKnockLoadRatio' incorporates:
   *  Constant: '<S2>/Constant4'
   */
  IonKnockLoadRatio = 0U;

  /* SignalConversion generated from: '<S2>/IntegralThreshold' incorporates:
   *  Constant: '<S2>/Constant5'
   */
  IntegralThreshold = 0U;

  /* SignalConversion generated from: '<S2>/LoadEnKnock' incorporates:
   *  Constant: '<S2>/Constant6'
   */
  LoadEnKnock = 0U;

  /* SignalConversion generated from: '<S2>/SAThreshold' incorporates:
   *  Constant: '<S2>/Constant7'
   */
  SAThreshold = 0;

  /* SignalConversion generated from: '<S2>/EffDwellAngle' incorporates:
   *  Constant: '<S2>/Constant11'
   */
  EffDwellAngle = 0U;

  /* SignalConversion generated from: '<S2>/FlgDisableKnock4Dwell' incorporates:
   *  Constant: '<S2>/Constant12'
   */
  FlgDisableKnock4Dwell = 0U;

  /* Constant: '<S2>/Constant13' */
  IdVer_IonKnockEn = ID_VER_IONKNOCKEN_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockEn_PowerOn' */
}

/* Model initialize function */
void IonKnockEn_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T IonKnockEnabled;
uint16_T IonKnockRpmIndex;
uint16_T IonKnockRpmRatio;
uint16_T IonKnockLoadIndex;
uint16_T IonKnockLoadRatio;
uint16_T VtIonKnockEnableCond[N_CYL_MAX];
uint8_T FlgLoadEnKnock[N_CYL_MAX];
void IonKnockEn_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    VtIonKnockEnableCond[idx] = 0u;
    FlgLoadEnKnock[idx] = 0u;
  }

  IonKnockEnabled = 0u;
  IonKnockRpmIndex = 0u;
  IonKnockRpmRatio = 0u;
  IonKnockLoadIndex = 0u;
  IonKnockLoadRatio = 0u;
}

void IonKnockEn_PowerOn(void)
{
  IonKnockEn_Stub();
}

void IonKnockEn_EOA(void)
{
  IonKnockEn_Stub();
}

void IonKnockEn_10ms(void)
{
  IonKnockEn_Stub();
}

#endif                                 /* _BUILD_IONKNOCKEN_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/