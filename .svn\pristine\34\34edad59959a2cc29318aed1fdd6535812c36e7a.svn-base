/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef _IVOR_C2_H_
#define _IVOR_C2_H_


extern  uint8_T     IvorIndex_c2;
extern  uint32_T    SRR0_Value_c2;
extern  uint32_T    SRR1_Value_c2;
extern  uint32_T    CSRR0_Value_c2;
extern  uint32_T    CSRR1_Value_c2;
extern  uint32_T    SPR_ESRValue_c2;
extern  uint32_T    SPR_DEARValue_c2;
extern  uint32_T    SPR_MCSRValue_c2;
extern  uint32_T    SPR_MCARValue_c2;
extern  uint32_T    MCSRR0_Value_c2;
extern  uint32_T    MCSRR1_Value_c2;
extern  uint16_T    IVOR_ConfigStatus_c2;

extern  void (*IVOR_Common_ManagerUserFunction_c2) (void);
extern  void (*IVOR1_UserFunction_c2) (void);
extern  void IVOR_Common_ISR_c2(void);

/*
** =========================================================================
**     Method      :  IVOR_Config_c2
**
**     Description : This method intializes the IVPR register
** =========================================================================
*/
int16_T IVOR_Config_c2(void);

/*
** =========================================================================
**     Method      :  InitIVPR
**
**     Description : This method intializes IVPR address base register
** =========================================================================
*/
static void InitIVPR_c2(void);

/*===========================================================================*/
/**
**    \par Method
**    SYS_GetESRExceptionType 
**
**    \par Description :
**     Discriminate exceptions that can generate the same interrupt type.
**	   Note: only IVOR2,3,5,6,13,32,33,34 change ESR register
**         
**    \param uint8_T IvorType
**    \param uint32_T* ExcepType
**    \return error code
** ===================================================================
*/
int16_T SYS_GetESRExceptionType_c2(uint8_T IvorType,uint32_T* ExcepType);


/*===========================================================================*/
/**
**    \par Method
**    SYS_GetMCSRExceptionType 
**
**    \par Description :
**     diffentiate among machine check conditions; also indicates whether the
**	   source of a machine check condition is recoverable.
**	   Note: MCSR register is IVOR2 dedicated
**         
**    \param uint32_T* ExcepType
**    \return error code
** ===================================================================
*/
int16_T SYS_GetMCSRExceptionType_c2(uint32_T* ExcepType, uint32_T* Mav, uint32_T* Maph);


#endif /* _IVOR_H_ */
