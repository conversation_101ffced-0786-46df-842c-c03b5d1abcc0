/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Mgm_private.h
 **  Date:          20-Jun-2023
 **
 **  Model Version: 1.652
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Mgm_private_h_
#define RTW_HEADER_TLE9278BQX_Mgm_private_h_
#include "rtwtypes.h"
#include "TLE9278BQX_Mgm_out.h"

/* Includes for objects with custom storage classes. */
#include "TLE9278BQX_Cfg_out.h"
#include "Wdt_out.h"
#include "TLE9278BQX_Com_out.h"
#include "TLE9278BQX_Prs_out.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/
extern void TLE9278BQX_Mgm_fc_Bkg(void);
extern void TLE9278BQX_Mgm_fc_Init_Start(void);
extern void TLE9278BQX_Mgm_fc_Init(void);

#endif                                /* RTW_HEADER_TLE9278BQX_Mgm_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/