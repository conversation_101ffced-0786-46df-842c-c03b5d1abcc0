
;----------------------------------------------
; Bit definitions for MCS architecture MCS24-1
;----------------------------------------------

; bit definitions for register STA 
.define EN             0
.define IRQ            1
.define ERR            2
.define MCA            3
.define CY             4
.define Z              5
.define V              6
.define N              7
.define CAT            8
.define CWT            9
.define SAT            10

; bit mask definitions for register STA 
.define EN_MSK          2**EN 
.define IRQ_MSK         2**IRQ
.define ERR_MSK         2**ERR
.define MCA_MSK         2**MCA
.define CY_MSK          2**CY
.define Z_MSK           2**Z
.define V_MSK           2**V
.define N_MSK           2**N
.define CAT_MSK         2**CAT
.define CWT_MSK         2**CWT
.define SAT_MSK         2**SAT

; bit definitions for register ACB 
.define ACB0           0
.define ACB1           1
.define ACB2           2
.define ACB3           3
.define ACB4           4

; bit mask definitions for register ACB 
.define ACB0_MSK           2**ACB0
.define ACB1_MSK           2**ACB1
.define ACB2_MSK           2**ACB2
.define ACB3_MSK           2**ACB3
.define ACB4_MSK           2**ACB4

; ARU write addresses 
.define  ARU_ACCESS $000
.define  TIM0_WRADDR0 $001
.define  TIM0_WRADDR1 $002
.define  TIM0_WRADDR2 $003
.define  TIM0_WRADDR3 $004
.define  TIM0_WRADDR4 $005
.define  TIM0_WRADDR5 $006
.define  TIM0_WRADDR6 $007
.define  TIM0_WRADDR7 $008
.define  TIM1_WRADDR0 $009
.define  TIM1_WRADDR1 $00A
.define  TIM1_WRADDR2 $00B
.define  TIM1_WRADDR3 $00C
.define  TIM1_WRADDR4 $00D
.define  TIM1_WRADDR5 $00E
.define  TIM1_WRADDR6 $00F
.define  TIM1_WRADDR7 $010
.define  TIM2_WRADDR0 $011
.define  TIM2_WRADDR1 $012
.define  TIM2_WRADDR2 $013
.define  TIM2_WRADDR3 $014
.define  TIM2_WRADDR4 $015
.define  TIM2_WRADDR5 $016
.define  TIM2_WRADDR6 $017
.define  TIM2_WRADDR7 $018
.define  TIM3_WRADDR0 $019
.define  TIM3_WRADDR1 $01A
.define  TIM3_WRADDR2 $01B
.define  TIM3_WRADDR3 $01C
.define  TIM3_WRADDR4 $01D
.define  TIM3_WRADDR5 $01E
.define  TIM3_WRADDR6 $01F
.define  TIM3_WRADDR7 $020
.define  TIM4_WRADDR0 $021
.define  TIM4_WRADDR1 $022
.define  TIM4_WRADDR2 $023
.define  TIM4_WRADDR3 $024
.define  TIM4_WRADDR4 $025
.define  TIM4_WRADDR5 $026
.define  TIM4_WRADDR6 $027
.define  TIM4_WRADDR7 $028
.define  TIM5_WRADDR0 $029
.define  TIM5_WRADDR1 $02A
.define  TIM5_WRADDR2 $02B
.define  TIM5_WRADDR3 $02C
.define  TIM5_WRADDR4 $02D
.define  TIM5_WRADDR5 $02E
.define  TIM5_WRADDR6 $02F
.define  TIM5_WRADDR7 $030
.define  TIM6_WRADDR0 $031
.define  TIM6_WRADDR1 $032
.define  TIM6_WRADDR2 $033
.define  TIM6_WRADDR3 $034
.define  TIM6_WRADDR4 $035
.define  TIM6_WRADDR5 $036
.define  TIM6_WRADDR6 $037
.define  TIM6_WRADDR7 $038
.define  F2A0_WRADDR0 $051
.define  F2A0_WRADDR1 $052
.define  F2A0_WRADDR2 $053
.define  F2A0_WRADDR3 $054
.define  F2A0_WRADDR4 $055
.define  F2A0_WRADDR5 $056
.define  F2A0_WRADDR6 $057
.define  F2A0_WRADDR7 $058
.define  F2A1_WRADDR0 $059
.define  F2A1_WRADDR1 $05A
.define  F2A1_WRADDR2 $05B
.define  F2A1_WRADDR3 $05C
.define  F2A1_WRADDR4 $05D
.define  F2A1_WRADDR5 $05E
.define  F2A1_WRADDR6 $05F
.define  F2A1_WRADDR7 $060
.define  BRC_WRADDR0 $061
.define  BRC_WRADDR1 $062
.define  BRC_WRADDR2 $063
.define  BRC_WRADDR3 $064
.define  BRC_WRADDR4 $065
.define  BRC_WRADDR5 $066
.define  BRC_WRADDR6 $067
.define  BRC_WRADDR7 $068
.define  BRC_WRADDR8 $069
.define  BRC_WRADDR9 $06A
.define  BRC_WRADDR10 $06B
.define  BRC_WRADDR11 $06C
.define  BRC_WRADDR12 $06D
.define  BRC_WRADDR13 $06E
.define  BRC_WRADDR14 $06F
.define  BRC_WRADDR15 $070
.define  BRC_WRADDR16 $071
.define  BRC_WRADDR17 $072
.define  BRC_WRADDR18 $073
.define  BRC_WRADDR19 $074
.define  BRC_WRADDR20 $075
.define  BRC_WRADDR21 $076
.define  MCS0_WRADDR0 $077
.define  MCS0_WRADDR1 $078
.define  MCS0_WRADDR2 $079
.define  MCS0_WRADDR3 $07A
.define  MCS0_WRADDR4 $07B
.define  MCS0_WRADDR5 $07C
.define  MCS0_WRADDR6 $07D
.define  MCS0_WRADDR7 $07E
.define  MCS0_WRADDR8 $07F
.define  MCS0_WRADDR9 $080
.define  MCS0_WRADDR10 $081
.define  MCS0_WRADDR11 $082
.define  MCS0_WRADDR12 $083
.define  MCS0_WRADDR13 $084
.define  MCS0_WRADDR14 $085
.define  MCS0_WRADDR15 $086
.define  MCS0_WRADDR16 $087
.define  MCS0_WRADDR17 $088
.define  MCS0_WRADDR18 $089
.define  MCS0_WRADDR19 $08A
.define  MCS0_WRADDR20 $08B
.define  MCS0_WRADDR21 $08C
.define  MCS0_WRADDR22 $08D
.define  MCS0_WRADDR23 $08E
.define  MCS1_WRADDR0 $08F
.define  MCS1_WRADDR1 $090
.define  MCS1_WRADDR2 $091
.define  MCS1_WRADDR3 $092
.define  MCS1_WRADDR4 $093
.define  MCS1_WRADDR5 $094
.define  MCS1_WRADDR6 $095
.define  MCS1_WRADDR7 $096
.define  MCS1_WRADDR8 $097
.define  MCS1_WRADDR9 $098
.define  MCS1_WRADDR10 $099
.define  MCS1_WRADDR11 $09A
.define  MCS1_WRADDR12 $09B
.define  MCS1_WRADDR13 $09C
.define  MCS1_WRADDR14 $09D
.define  MCS1_WRADDR15 $09E
.define  MCS1_WRADDR16 $09F
.define  MCS1_WRADDR17 $0A0
.define  MCS1_WRADDR18 $0A1
.define  MCS1_WRADDR19 $0A2
.define  MCS1_WRADDR20 $0A3
.define  MCS1_WRADDR21 $0A4
.define  MCS1_WRADDR22 $0A5
.define  MCS1_WRADDR23 $0A6
.define  MCS2_WRADDR0 $0A7
.define  MCS2_WRADDR1 $0A8
.define  MCS2_WRADDR2 $0A9
.define  MCS2_WRADDR3 $0AA
.define  MCS2_WRADDR4 $0AB
.define  MCS2_WRADDR5 $0AC
.define  MCS2_WRADDR6 $0AD
.define  MCS2_WRADDR7 $0AE
.define  MCS2_WRADDR8 $0AF
.define  MCS2_WRADDR9 $0B0
.define  MCS2_WRADDR10 $0B1
.define  MCS2_WRADDR11 $0B2
.define  MCS2_WRADDR12 $0B3
.define  MCS2_WRADDR13 $0B4
.define  MCS2_WRADDR14 $0B5
.define  MCS2_WRADDR15 $0B6
.define  MCS2_WRADDR16 $0B7
.define  MCS2_WRADDR17 $0B8
.define  MCS2_WRADDR18 $0B9
.define  MCS2_WRADDR19 $0BA
.define  MCS2_WRADDR20 $0BB
.define  MCS2_WRADDR21 $0BC
.define  MCS2_WRADDR22 $0BD
.define  MCS2_WRADDR23 $0BE
.define  MCS3_WRADDR0 $0BF
.define  MCS3_WRADDR1 $0C0
.define  MCS3_WRADDR2 $0C1
.define  MCS3_WRADDR3 $0C2
.define  MCS3_WRADDR4 $0C3
.define  MCS3_WRADDR5 $0C4
.define  MCS3_WRADDR6 $0C5
.define  MCS3_WRADDR7 $0C6
.define  MCS3_WRADDR8 $0C7
.define  MCS3_WRADDR9 $0C8
.define  MCS3_WRADDR10 $0C9
.define  MCS3_WRADDR11 $0CA
.define  MCS3_WRADDR12 $0CB
.define  MCS3_WRADDR13 $0CC
.define  MCS3_WRADDR14 $0CD
.define  MCS3_WRADDR15 $0CE
.define  MCS3_WRADDR16 $0CF
.define  MCS3_WRADDR17 $0D0
.define  MCS3_WRADDR18 $0D1
.define  MCS3_WRADDR19 $0D2
.define  MCS3_WRADDR20 $0D3
.define  MCS3_WRADDR21 $0D4
.define  MCS3_WRADDR22 $0D5
.define  MCS3_WRADDR23 $0D6
.define  MCS4_WRADDR0 $0D7
.define  MCS4_WRADDR1 $0D8
.define  MCS4_WRADDR2 $0D9
.define  MCS4_WRADDR3 $0DA
.define  MCS4_WRADDR4 $0DB
.define  MCS4_WRADDR5 $0DC
.define  MCS4_WRADDR6 $0DD
.define  MCS4_WRADDR7 $0DE
.define  MCS4_WRADDR8 $0DF
.define  MCS4_WRADDR9 $0E0
.define  MCS4_WRADDR10 $0E1
.define  MCS4_WRADDR11 $0E2
.define  MCS4_WRADDR12 $0E3
.define  MCS4_WRADDR13 $0E4
.define  MCS4_WRADDR14 $0E5
.define  MCS4_WRADDR15 $0E6
.define  MCS4_WRADDR16 $0E7
.define  MCS4_WRADDR17 $0E8
.define  MCS4_WRADDR18 $0E9
.define  MCS4_WRADDR19 $0EA
.define  MCS4_WRADDR20 $0EB
.define  MCS4_WRADDR21 $0EC
.define  MCS4_WRADDR22 $0ED
.define  MCS4_WRADDR23 $0EE
.define  MCS5_WRADDR0 $0EF
.define  MCS5_WRADDR1 $0F0
.define  MCS5_WRADDR2 $0F1
.define  MCS5_WRADDR3 $0F2
.define  MCS5_WRADDR4 $0F3
.define  MCS5_WRADDR5 $0F4
.define  MCS5_WRADDR6 $0F5
.define  MCS5_WRADDR7 $0F6
.define  MCS5_WRADDR8 $0F7
.define  MCS5_WRADDR9 $0F8
.define  MCS5_WRADDR10 $0F9
.define  MCS5_WRADDR11 $0FA
.define  MCS5_WRADDR12 $0FB
.define  MCS5_WRADDR13 $0FC
.define  MCS5_WRADDR14 $0FD
.define  MCS5_WRADDR15 $0FE
.define  MCS5_WRADDR16 $0FF
.define  MCS5_WRADDR17 $100
.define  MCS5_WRADDR18 $101
.define  MCS5_WRADDR19 $102
.define  MCS5_WRADDR20 $103
.define  MCS5_WRADDR21 $104
.define  MCS5_WRADDR22 $105
.define  MCS5_WRADDR23 $106
.define  MCS6_WRADDR0 $107
.define  MCS6_WRADDR1 $108
.define  MCS6_WRADDR2 $109
.define  MCS6_WRADDR3 $10A
.define  MCS6_WRADDR4 $10B
.define  MCS6_WRADDR5 $10C
.define  MCS6_WRADDR6 $10D
.define  MCS6_WRADDR7 $10E
.define  MCS6_WRADDR8 $10F
.define  MCS6_WRADDR9 $110
.define  MCS6_WRADDR10 $111
.define  MCS6_WRADDR11 $112
.define  MCS6_WRADDR12 $113
.define  MCS6_WRADDR13 $114
.define  MCS6_WRADDR14 $115
.define  MCS6_WRADDR15 $116
.define  MCS6_WRADDR16 $117
.define  MCS6_WRADDR17 $118
.define  MCS6_WRADDR18 $119
.define  MCS6_WRADDR19 $11A
.define  MCS6_WRADDR20 $11B
.define  MCS6_WRADDR21 $11C
.define  MCS6_WRADDR22 $11D
.define  MCS6_WRADDR23 $11E
.define  ATOM0_WRADDR0 $11F
.define  ATOM0_WRADDR1 $120
.define  ATOM0_WRADDR2 $121
.define  ATOM0_WRADDR3 $122
.define  ATOM0_WRADDR4 $123
.define  ATOM0_WRADDR5 $124
.define  ATOM0_WRADDR6 $125
.define  ATOM0_WRADDR7 $126
.define  ATOM1_WRADDR0 $127
.define  ATOM1_WRADDR1 $128
.define  ATOM1_WRADDR2 $129
.define  ATOM1_WRADDR3 $12A
.define  ATOM1_WRADDR4 $12B
.define  ATOM1_WRADDR5 $12C
.define  ATOM1_WRADDR6 $12D
.define  ATOM1_WRADDR7 $12E
.define  ATOM2_WRADDR0 $12F
.define  ATOM2_WRADDR1 $130
.define  ATOM2_WRADDR2 $131
.define  ATOM2_WRADDR3 $132
.define  ATOM2_WRADDR4 $133
.define  ATOM2_WRADDR5 $134
.define  ATOM2_WRADDR6 $135
.define  ATOM2_WRADDR7 $136
.define  ATOM3_WRADDR0 $137
.define  ATOM3_WRADDR1 $138
.define  ATOM3_WRADDR2 $139
.define  ATOM3_WRADDR3 $13A
.define  ATOM3_WRADDR4 $13B
.define  ATOM3_WRADDR5 $13C
.define  ATOM3_WRADDR6 $13D
.define  ATOM3_WRADDR7 $13E
.define  ATOM4_WRADDR0 $13F
.define  ATOM4_WRADDR1 $140
.define  ATOM4_WRADDR2 $141
.define  ATOM4_WRADDR3 $142
.define  ATOM4_WRADDR4 $143
.define  ATOM4_WRADDR5 $144
.define  ATOM4_WRADDR6 $145
.define  ATOM4_WRADDR7 $146
.define  ATOM5_WRADDR0 $147
.define  ATOM5_WRADDR1 $148
.define  ATOM5_WRADDR2 $149
.define  ATOM5_WRADDR3 $14A
.define  ATOM5_WRADDR4 $14B
.define  ATOM5_WRADDR5 $14C
.define  ATOM5_WRADDR6 $14D
.define  ATOM5_WRADDR7 $14E
.define  ATOM6_WRADDR0 $14F
.define  ATOM6_WRADDR1 $150
.define  ATOM6_WRADDR2 $151
.define  ATOM6_WRADDR3 $152
.define  ATOM6_WRADDR4 $153
.define  ATOM6_WRADDR5 $154
.define  ATOM6_WRADDR6 $155
.define  ATOM6_WRADDR7 $156
.define  ATOM7_WRADDR0 $157
.define  ATOM7_WRADDR1 $158
.define  ATOM7_WRADDR2 $159
.define  ATOM7_WRADDR3 $15A
.define  ATOM7_WRADDR4 $15B
.define  ATOM7_WRADDR5 $15C
.define  ATOM7_WRADDR6 $15D
.define  ATOM7_WRADDR7 $15E
.define  ATOM8_WRADDR0 $15F
.define  ATOM8_WRADDR1 $160
.define  ATOM8_WRADDR2 $161
.define  ATOM8_WRADDR3 $162
.define  ATOM8_WRADDR4 $163
.define  ATOM8_WRADDR5 $164
.define  ATOM8_WRADDR6 $165
.define  ATOM8_WRADDR7 $166
.define  ATOM9_WRADDR0 $167
.define  ATOM9_WRADDR1 $168
.define  ATOM9_WRADDR2 $169
.define  ATOM9_WRADDR3 $16A
.define  ATOM9_WRADDR4 $16B
.define  ATOM9_WRADDR5 $16C
.define  ATOM9_WRADDR6 $16D
.define  ATOM9_WRADDR7 $16E
.define  ATOM10_WRADDR0 $16F
.define  ATOM10_WRADDR1 $170
.define  ATOM10_WRADDR2 $171
.define  ATOM10_WRADDR3 $172
.define  ATOM10_WRADDR4 $173
.define  ATOM10_WRADDR5 $174
.define  ATOM10_WRADDR6 $175
.define  ATOM10_WRADDR7 $176
.define  ATOM11_WRADDR0 $177
.define  ATOM11_WRADDR1 $178
.define  ATOM11_WRADDR2 $179
.define  ATOM11_WRADDR3 $17A
.define  ATOM11_WRADDR4 $17B
.define  ATOM11_WRADDR5 $17C
.define  ATOM11_WRADDR6 $17D
.define  ATOM11_WRADDR7 $17E
.define  DPLL_WRADDR0 $17F
.define  DPLL_WRADDR1 $180
.define  DPLL_WRADDR2 $181
.define  DPLL_WRADDR3 $182
.define  DPLL_WRADDR4 $183
.define  DPLL_WRADDR5 $184
.define  DPLL_WRADDR6 $185
.define  DPLL_WRADDR7 $186
.define  DPLL_WRADDR8 $187
.define  DPLL_WRADDR9 $188
.define  DPLL_WRADDR10 $189
.define  DPLL_WRADDR11 $18A
.define  DPLL_WRADDR12 $18B
.define  DPLL_WRADDR13 $18C
.define  DPLL_WRADDR14 $18D
.define  DPLL_WRADDR15 $18E
.define  DPLL_WRADDR16 $18F
.define  DPLL_WRADDR17 $190
.define  DPLL_WRADDR18 $191
.define  DPLL_WRADDR19 $192
.define  DPLL_WRADDR20 $193
.define  DPLL_WRADDR21 $194
.define  DPLL_WRADDR22 $195
.define  DPLL_WRADDR23 $196
.define  DPLL_WRADDR24 $197
.define  DPLL_WRADDR25 $198
.define  DPLL_WRADDR26 $199
.define  DPLL_WRADDR27 $19A
.define  DPLL_WRADDR28 $19B
.define  DPLL_WRADDR29 $19C
.define  DPLL_WRADDR30 $19D
.define  DPLL_WRADDR31 $19E
.define  ARU_EMPTY_ADDR $1FE
.define  ARU_FULL_ADDR $1FF

 
