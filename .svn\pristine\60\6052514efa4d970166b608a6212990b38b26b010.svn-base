/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonMisf.h
 **  Date:          07-Nov-2022
 **
 **  Model Version: 1.1137
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonMisf_h_
#define RTW_HEADER_IonMisf_h_
#ifndef IonMisf_COMMON_INCLUDES_
# define IonMisf_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonMisf_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

 
/* Enumerated types definition */
typedef uint8_T enum_StMisf;
#define NO_MISF                        ((enum_StMisf)0U)         /* Default value */
#define PAR_COMB                       ((enum_StMisf)1U)
#define BAD_COMB                       ((enum_StMisf)2U)
#define NO_COMB                        ((enum_StMisf)3U)
#define MIS_FIRE                       ((enum_StMisf)4U)
#define BAD_ION                        ((enum_StMisf)5U)
#define EOA_NOT_EXE                    ((enum_StMisf)255U)
 
/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  uint8_T FlgMisfDetected_f;           /* '<S4>/MisfireTDC_Detection' */
} DW_IonMisf_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_IonMisf_T IonMisf_DW;

/* Model entry point functions */
extern void IonMisf_initialize(void);

/* Exported entry point function */
extern void IonMisf_EOA(void);

/* Exported entry point function */
extern void IonMisf_NoSync(void);

/* Exported entry point function */
extern void IonMisf_PowerOn(void);

/* Exported entry point function */
extern void IonMisf_TDC(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgMisfDetected;        /* '<S3>/Merge1' */

/* Misfire bit mask */
extern enum_StMisf StMisf[8];          /* '<S3>/Merge' */

/* Cylinder Misfire Status Vector */
extern enum_StMisf StMisfEOA[8];       /* '<S3>/Merge2' */

/* Cylinder Misfire Status Vector calculated at EOA event */


/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonMisf'
 * '<S1>'   : 'IonMisf/EOA_fcn'
 * '<S2>'   : 'IonMisf/IonMisf_sched'
 * '<S3>'   : 'IonMisf/Merge'
 * '<S4>'   : 'IonMisf/TDC_fcn'
 * '<S5>'   : 'IonMisf/EOA_fcn/MisfireEOA_Detection'
 * '<S6>'   : 'IonMisf/TDC_fcn/MisfireTDC_Detection'
 */

/*-
 * Requirements for '<Root>': IonMisf
 */
#endif                                 /* RTW_HEADER_IonMisf_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/