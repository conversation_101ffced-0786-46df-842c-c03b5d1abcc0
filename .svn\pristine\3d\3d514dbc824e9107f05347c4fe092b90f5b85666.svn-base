/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Startup
**  Filename        :  entrypoint.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"
#include "sys.h"
#include <string.h>
#include "clock.h"

// external definitions
extern void *__start_c0(void);
extern int32_T mainCore2(void); 
extern void appManagerCore2(void);
int16_T app_CheckVersion(void);
int16_T calib_CheckVersion(void);
#if 0
static void compute_CheckSum(uint32_T dest, uint32_T size, uint32_T *sum)
#endif
static void initCore2(void);
static void runCore0(void);
void app_data_section_init(void);
void app_vletext_section_init(void);
void app_data_c2_section_init(void);
void app_text_c2_section_init(void);
#ifdef _BUILD_DEVELOPMENT_ 
void calibration_section_init(void);
#endif
#ifdef _BUILD_CHECKSUMVALIDATION_
int16_T calibRom_section_verify(void);
#endif
#ifdef _BUILD_SAFETYMNGR_RAMCHECK_ 
void patternSRAM_section_init(void);
#endif

static void IMEMCTL0_Setup(uint32_t value); 

extern void edmaInit(void);
extern void saradc_lld_init(void);



/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : app_start
**
**   Description:
**    Application startup
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR      - no error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T app_start(void)
{
    int16_T error = NO_ERROR;
    int16_T SPCSetPerClkDiv_RetVal=NO_ERROR;
    {
#ifdef _BUILD_CHECKSUMVALIDATION_      
        error = calibRom_section_verify();   // verify calibration checksum
#endif
        if (error == NO_ERROR)
        {

            initCore2();                     // StartUp Core2 (z2) Microcontroller - 2nd Phase
            SPCSetPerClkDiv_RetVal = SPCSetPerClkDiv_AC0(SPC5_AC0_DC2_SAR_CLK_ID, SPC5_MC_CGM_AC0_DC_DIVIDER_17);
            SPCSetPerClockMode_Appl();       // Set the peripherals clock mode

#ifdef _BUILD_DEVELOPMENT_ 
            calibration_section_init();      // Initialize the calib section
#endif

#ifdef _BUILD_SAFETYMNGR_RAMCHECK_
            patternSRAM_section_init();
#endif

            app_data_section_init();         // Initialize the data section
            app_vletext_section_init();      // Initialize the vletext_RAM section
            app_data_c2_section_init();      // Initialize the data_c2 section
            app_text_c2_section_init();      // Initialize the vletext_c2 section

            appManagerCore2();               // BIOS and DD Components Init executed by Core 2
            
            runCore0();                     // Core0 (z4) activation

            mainCore2();                    // Jmp to Core2 main 
        }
    }
    return error;
}



#if 0
// must be aligned
static void compute_CheckSum(uint32_T dest, uint32_T size, uint32_T *sum)
{
    uint32_T i;
    uint32_T local_sum = 0u;
    uint32_T value;

    for (i=0u; i<(size/sizeof(uint32_T)); i++)
    {
        value  = (uint32_T)( *(uint8_T *)(dest));
        value |= (uint32_T)((*(uint8_T *)(dest+1u))<<8);
        value |= (uint32_T)((*(uint8_T *)(dest+2u))<<16);
        value |= (uint32_T)((*(uint8_T *)(dest+3u))<<24);

        local_sum += value;
        dest += sizeof(uint32_T);
    }

    *sum += local_sum;

}
#endif

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : initCore2
**
**   Description:
**    Initialization core 2
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void initCore2(void)
{
    // Memory Controllers configuration (Flash Controller, SRAM Controller)

    /* ECC configuration for IMEM2 */
    IMEMCTL0_Setup(DCR_IMEMCTL0_ISECE | DCR_IMEMCTL0_ICPECE | DCR_IMEMCTL0_ISWCE);

    // MPU configuration

}

#pragma ghs startnomisra

/******************************************************************************
**   Function    : runCore0
**
**   Description:
**    Core0 (z4) activation
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void runCore0(void)
{   
// Core0 (z4) activation
uint32_T mode;

    /* Start core0.*/
    /* Conversion between a pointer to function and another type needed to update
     the CADDR1 register with the address of the core0 reset routine.*/
    /*lint -e9074 -e546 */
    MC_ME.CADDR[1].R = ((uint32_T)&__start_c0 | 0x01UL); /* Set reset vector for core0, core0 will be reset on the next mode transition */
    /*lint +e9074 +e546 */
    MC_ME.CCTL[1].R = 0x00FEU;                                /* Set modes in which core_0 will run.                                         */

    /* Force mode transition to the current mode.*/
    mode = MC_ME.GS.B.S_CURRENT_MODE;
    MC_ME.MCTL.R = (mode << 28 ) | 0x00005AF0UL;              /* Mode & Key                               */
    MC_ME.MCTL.R = (mode << 28 ) | 0x0000A50FUL;              /* Mode & Key                               */
    while(MC_ME.GS.B.S_MTRANS == 1U) {                        /* Waiting for end of transaction           */
    }                               
    while(MC_ME.GS.B.S_CURRENT_MODE != mode) {                /* Check mode has successfully been entered */
    }          

}
#pragma ghs endnomisra

/******************************************************************************
**   Function    : app_data_section_init
**
**   Description:
**    Initialization app data section.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void app_data_section_init(void)
{
    extern uint32_T __DATA_ROM;
    extern uint32_T __SRAM_CPY_START;
    extern uint32_T __ROM_COPY_SIZE;
    memcpy(&__SRAM_CPY_START, &__DATA_ROM, (size_t)&__ROM_COPY_SIZE);
}

/******************************************************************************
**   Function    : app_vletext_section_init
**
**   Description:
**    Initialization app vletext section.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void app_vletext_section_init(void)
{
    extern uint32_T _VLETEXTIMM_ROM;
    extern uint32_T __VLETEXTRAM_TEXT_CPY_START;
    extern uint32_T __VLETEXTRAM_COPY_SIZE;
    memcpy(&__VLETEXTRAM_TEXT_CPY_START, &_VLETEXTIMM_ROM, (size_t)&__VLETEXTRAM_COPY_SIZE);
}

/******************************************************************************
**   Function    : app_data_c2_section_init
**
**   Description:
**    Initialization app data c2 section.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void app_data_c2_section_init(void)
{
    extern uint32_T __DATA_C2_ROM;
    extern uint32_T __DRAM2_DATA_CPY_START;
    extern uint32_T __DATAC2_COPY_SIZE;
    memcpy(&__DRAM2_DATA_CPY_START, &__DATA_C2_ROM, (size_t)&__DATAC2_COPY_SIZE);
}

/******************************************************************************
**   Function    : app_text_c2_section_init
**
**   Description:
**    Initialization app text c2 section.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void app_text_c2_section_init(void)
{
    extern uint32_T __INST_C2_ROM;
    extern uint32_T __IRAM2_TEXT_CPY_START;
    extern uint32_T __TEXTC2_COPY_SIZE;
    memcpy(&__IRAM2_TEXT_CPY_START, &__INST_C2_ROM, (size_t)&__TEXTC2_COPY_SIZE);
}

/******************************************************************************
**   Function    : calibration_section_init
**
**   Description:
**    Initialization calibration section.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#ifdef _BUILD_DEVELOPMENT_ 
void calibration_section_init(void)
{
    extern uint32_T __CALIB_RAM_START;
    extern uint32_T __CALIB_RAM_SIZE;
    //extern uint32_T __CALIB_ROM_START;
    memcpy(&__CALIB_RAM_START, &__CALIB_ROM_START, (size_t)&__CALIB_RAM_SIZE);
}
#endif

/******************************************************************************
**   Function    : calibRom_section_verify
**
**   Description:
**    Verification calibRom section.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#ifdef _BUILD_CHECKSUMVALIDATION_
int16_T calibRom_section_verify(void)
{
    uint32_T checksum;
    BlockDescription* InfoPtr;
    uint32_T source;
    uint32_T size;
    extern uint32_T  __CALIB_ROM_START;  
    extern uint32_T  __CALIB_ROM_SIZE;

    InfoPtr = (BlockDescription *) (((uint32_T)(&__CALIB_ROM_START)+(uint32_T)(&__CALIB_ROM_SIZE))-sizeof(BlockDescription));
    source = (uint32_T)(&__CALIB_ROM_START);
    size = (uint32_T)(&__CALIB_ROM_SIZE) - sizeof(BlockDescription);

    checksum = 0u;
    compute_CheckSum(source, size, &checksum);
    if (InfoPtr->blockChecksum != checksum)
    {
        error = -1;
    }
}
#endif

/******************************************************************************
**   Function    : patternSRAM_section_init
**
**   Description:
**    Initialization pattern SRAM section used by MCU safety test.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#ifdef _BUILD_SAFETYMNGR_RAMCHECK_ 
void patternSRAM_section_init(void)
{
    extern uint32_T __PATTERN_SRAM_START;
    extern uint32_T __PATTERN_SRAM_SIZE;
    extern uint32_T _PATTERNSRAMIMM_ROM;
    memcpy(&__PATTERN_SRAM_START, &_PATTERNSRAMIMM_ROM, (size_t)&__PATTERN_SRAM_SIZE);
}
#endif

/******************************************************************************
**   Function    : IMEMCTL0_Setup
**
**   Description:
**    Writes the DCR 497 IMEMCTL0, configuring IMEM logics such as ECC enable.
**
**   Parameters :
**    [in] uint32_T value: The value to write in IMEMCTL0
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void IMEMCTL0_Setup(uint32_t value) 
{ 
    asm("msync"); 
    asm("se_isync"); 
    asm("mtdcr 497, r3"); 
    asm("msync"); 
} 

/****************************************************************************
 ****************************************************************************/
