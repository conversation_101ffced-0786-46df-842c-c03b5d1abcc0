/*
 * File: rtwtypes.h
 *
 * Real-Time Workshop code generated for Simulink model FOC.
 *
 * Model version                        : 1.3651
 * Real-Time Workshop file version      : 7.6  (R2010b)  03-Aug-2010
 * Real-Time Workshop file generated on : Mon Apr 04 18:40:47 2011
 * TLC version                          : 7.6 (Jul 13 2010)
 * C/C++ source code generated on       : Mon Apr 04 18:40:49 2011
 */

#ifndef __RTWTYPES_H__
#define __RTWTYPES_H__


#define CALQUAL_PRE     static
#define CALQUAL         const
#define CALQUAL_POST    volatile
#define CCPTEST 
#define STATIC_TEST_POINT  static

#define STATIC_TEST_POINT static

#ifndef __TMWTYPES__
#define __TMWTYPES__
#include <limits.h>

/*=======================================================================*
 * Target hardware information
 *   Device type: Freescale->32-bit PowerPC
 *   Number of bits:     char:   8    short:   16    int:  32
 *                       long:  32      native word size:  32
 *   Byte ordering: BigEndian
 *   Signed integer division rounds to: Zero
 *   Shift right on a signed integer as arithmetic shift: on
 *=======================================================================*/

/*=======================================================================*
 * Fixed width word size data types:                                     *
 *   int8_T, int16_T, int32_T     - signed 8, 16, or 32 bit integers     *
 *   uint8_T, uint16_T, uint32_T  - unsigned 8, 16, or 32 bit integers   *
 *   real32_T, real64_T           - 32 and 64 bit floating point numbers *
 *=======================================================================*/
typedef signed char int8_T;
typedef unsigned char uint8_T;
typedef short int16_T;
typedef unsigned short uint16_T;
typedef int int32_T;
typedef unsigned int uint32_T;
typedef float real32_T;
typedef double real64_T;
typedef volatile signed char vint8_T;
typedef volatile unsigned char vuint8_T;
typedef volatile signed short vint16_T;
typedef volatile unsigned short vuint16_T;
typedef volatile signed int vint32_T;
typedef volatile unsigned int vuint32_T;
typedef signed long long int64_T;
typedef unsigned long long uint64_T;

#pragma ghs startnomisra
#include "zero_crossing_types.h"
#pragma ghs endnomisra
/*===========================================================================*
 * Generic type definitions: real_T, time_T, boolean_T, int_T, uint_T,       *
 *                           ulong_T, char_T and byte_T.                     *
 *===========================================================================*/
typedef double real_T;//Aggiunto da E-Shock
typedef double time_T;//Aggiunto da E-Shock
typedef unsigned char boolean_T;  //TO BE CONTROLLED
//typedef _Bool boolean_T;
typedef int int_T;
typedef unsigned int uint_T;
typedef unsigned long ulong_T;
typedef char char_T;
typedef unsigned char uchar_T;
typedef char_T byte_T;

/*=======================================================================*
 * Min and Max:                                                          *
 *   int8_T, int16_T, int32_T     - signed 8, 16, or 32 bit integers     *
 *   uint8_T, uint16_T, uint32_T  - unsigned 8, 16, or 32 bit integers   *
 *=======================================================================*/
#define MAX_int8_T                     ((int8_T)(127))
#define MIN_int8_T                     ((int8_T)(-128))
#define MAX_uint8_T                    ((uint8_T)(255U))
#define MIN_uint8_T                    ((uint8_T)(0U))
#define MAX_int16_T                    ((int16_T)(32767))
#define MIN_int16_T                    ((int16_T)(-32768))
#define MAX_uint16_T                   ((uint16_T)(65535U))
#define MIN_uint16_T                   ((uint16_T)(0U))
#define MAX_int32_T                    ((int32_T)(2147483647))
#define MIN_int32_T                    ((int32_T)(-2147483647-1))
#define MAX_uint32_T                   ((uint32_T)(0xFFFFFFFFU))
#define MIN_uint32_T                   ((uint32_T)(0U))
#define MAX_uint24_T                   ((uint32_T)(0x00FFFFFFU))
#define MIN_uint24_T                   ((uint32_T)(0U))

#define NULL   0u

#define TRUE                          ((boolean_T) 1u)
#define FALSE                         ((boolean_T) 0u)

#define true                          ((boolean_T) 1u)
#define false                         ((boolean_T) 0u)

/*
 * Real-Time Workshop assumes the code is compiled on a target using a 2's compliment representation
 * for signed integer values.
 */
#pragma ghs startnomisra
#if ((SCHAR_MIN + 1) != -SCHAR_MAX)
#error "This code must be compiled using a 2's complement representation for signed integer values"
#endif
#pragma ghs endnomisra

/* This ID is used to detect inclusion of an incompatible rtwtypes.h */
#define RTWTYPES_ID_C08S16I32L32N32F1	// needed for SKYHOOK integration
#define RTWTYPES_ID_C08S16I32L32N32F0	// slight changes made by Eldor
#else                                  /* __TMWTYPES__ */
#define TMWTYPES_PREVIOUSLY_INCLUDED
#endif                                 /* __TMWTYPES__ */

/* Block D-Work pointer type */
typedef void * pointer_T;


#endif                                 /* __RTWTYPES_H__ */

/*
 * File trailer for Real-Time Workshop generated code.
 *
 * [EOF]
 */
