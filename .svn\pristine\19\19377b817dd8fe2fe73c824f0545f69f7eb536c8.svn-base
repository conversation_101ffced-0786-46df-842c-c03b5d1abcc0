/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DSPI
**  Filename        :  Dspi.c
**  Created on      :  16-giu-2020 14:04:00
**  Original author :  CarboniM
******************************************************************************/

#include "rtwtypes.h"   /* used also if not defined _BUILD_SPI_ */

#ifdef _BUILD_SPI_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "dspi.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* Global variable for configuration status */
uint16_T SPIConfigStatus = 0u;

/* Global variable for the Driver Status */
uint8_T SPIChannelStatus[5] = { 0u, 0u, 0u, 0u, 0u };

#ifdef _SPI_USE_DMA_
///Array of pointers to SPI Push Registers
vuint32_T DSPIPushR[]= { DSPIA_PUSHR, DSPIB_PUSHR, DSPIC_PUSHR, DSPID_PUSHR, DSPIE_PUSHR };

///Array of pointers to  SPI Pop Registers
vuint32_T DSPIPopR[] = { DSPIA_POPR, DSPIB_POPR, DSPIC_POPR, DSPID_POPR, DSPIE_POPR };
#endif /* _SPI_USE_DMA_ */

#if SPI_CH_A_EN 
#if SPI_A_FUNC_INT 
///Array of function pointers to the IRQ vector for channel A
FuncSpi_T SPIAIrqVect[SPI_A_CS_NUM];
#endif
#endif //SPI_CH_A_EN
    
#if SPI_CH_B_EN
#if SPI_B_FUNC_INT
///Array of function pointers to the IRQ vector for channel B
FuncSpi_T SPIBIrqVect[SPI_B_CS_NUM];
#endif
#endif //SPI_CH_B_EN

#if SPI_CH_C_EN
#if SPI_C_FUNC_INT
///Array of function pointers to the IRQ vector for channel C
FuncSpi_T SPICIrqVect[SPI_C_CS_NUM];
#endif
#endif //SPI_CH_C_EN
    
#if SPI_CH_D_EN    
#if SPI_D_FUNC_INT
///Array of function pointers to the IRQ vector for channel D
FuncSpi_T SPIDIrqVect[SPI_D_CS_NUM];
#endif
#endif //SPI_CH_D_EN

#if SPI_CH_E_EN
#if SPI_E_FUNC_INT
///Array of function pointers to the IRQ vector for channel D
FuncSpi_T SPIEIrqVect[SPI_D_CS_NUM];
#endif
#endif //SPI_CH_E_EN

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
///Resource flags for SPI channels
static volatile uint8_T SpiResFlagCH[5];
/// Global variable to handle spi timeout for each of 5 channels
static timeoutHandler_t CfgSpiChHandler[5] = { 0u, 0u, 0u, 0u, 0u };


/* Local Buffer for TX and RX */
#if SPI_CH_A_EN 
static vuint32_T DSPITxBufferA[TX_BUFFER_SIZE];
static vuint32_T DSPIRx_BufferA[RX_BUFFER_SIZE];
static TaskType  SPIExTxDoneChAIrqVect[SPI_NUM_OF_CS] = { 0u };
#endif

#if SPI_CH_B_EN 
static vuint32_T DSPITxBufferB[TX_BUFFER_SIZE];
static vuint32_T DSPIRxBufferB[RX_BUFFER_SIZE];
static TaskType  SPIExTxDoneChBIrqVect[SPI_NUM_OF_CS] = { 0u };
#endif

#if SPI_CH_C_EN 
static vuint32_T DSPITxBufferC[TX_BUFFER_SIZE];
static vuint32_T DSPIRxBufferC[RX_BUFFER_SIZE];
static TaskType  SPIExTxDoneChCIrqVect[SPI_NUM_OF_CS] = { 0u };
#endif

#if SPI_CH_D_EN 
static vuint32_T DSPITxBufferD[TX_BUFFER_SIZE];
static vuint32_T DSPIRxBufferD[RX_BUFFER_SIZE];
static TaskType  SPIExTxDoneChDIrqVect[SPI_NUM_OF_CS] = { 0u };
#endif

#if SPI_CH_E_EN
static vuint32_T DSPITxBufferE[TX_BUFFER_SIZE];
static vuint32_T DSPIRxBufferE[RX_BUFFER_SIZE];
static TaskType  SPIExTxDoneChEIrqVect[SPI_NUM_OF_CS] = { 0u };
#endif

static vuint32_T *DSPITxBufferCh[5];
static vuint32_T *DSPIRxBufferCh[5];
static vuint8_T SPILastPcs[5];

#ifdef _SPI_USE_DMA_
static const uint8_T SPI_DMA_CH[] = { DMACH_DSPI_0_TX, DMACH_DSPI_0_RX, DMACH_DSPI_1_TX, DMACH_DSPI_1_RX, DMACH_DSPI_2_TX, DMACH_DSPI_2_RX, DMACH_DSPI_3_TX, DMACH_DSPI_3_RX, DMACH_DSPI_4_TX, DMACH_DSPI_4_RX };
#endif

///Array of pointers to  SPI  registers  A  B  C  D  E
static DspiPtr_T DSPI_PTR_ARRAY[] = { &DSPI_0, &DSPI_1, &DSPI_2, &DSPI_4, &DSPI_5 };

///Array for configuration of chip select continuous mode
static vuint8_T PCS_CONT[] = { (vuint8_T)SPI_CH_A_PCS_CONT, (vuint8_T)SPI_CH_B_PCS_CONT, (vuint8_T)SPI_CH_C_PCS_CONT, (vuint8_T)SPI_CH_D_PCS_CONT, (vuint8_T)SPI_CH_E_PCS_CONT };

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : SPI_Config
**
**   Description:
**    Function for configuration of SPI channels.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR                      - SPI channels correctly configured.
**    PERIPHERAL_ALREADY_CONFIGURED - SPI channels were already configured.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
SpiError_T SPI_Config(void)
{
    SpiError_T retValue = NO_ERROR;

    if (SPIConfigStatus == 0u) 
    {
        uint8_T i;
        SPIConfigStatus = 1u;      
        for (i = 0u; i < 5u; i++)
        {
            DSPITxBufferCh[i] = NULL;
        }


#if SPI_CH_A_EN 
        DSPITxBufferCh[SPI_CH_A] = DSPITxBufferA;
        DSPIRxBufferCh[SPI_CH_A] = DSPIRx_BufferA;
        SPI_ConfigCh(SPI_CH_A, SPI_CH_A_MODE, SPI_CH_A_PCS_CONT, SPI_CH_A_PCSn);

        /* Set Clock and Transfer Attributes */
#if PCSA0_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_A, PCS_0, SPI_CH_A0_FMSZ, SPI_CH_A0_CPOL, SPI_CH_A0_CPHA, SPI_CH_A0_CSSCK, SPI_CH_A0_DT, SPI_CH_A0_ASC, SPI_CH_A0_LSBFE);
        SPI_InitCh(SPI_CH_A, PCS_0, SPI_CH_A0_BR, SPI_CH_A0_BRP, SPI_CH_A0_DBR);                              
        SPI_ConfigPrescalers(SPI_CH_A, PCS_0, SPI_CH_A0_PCSSCK, SPI_CH_A0_PDT, SPI_CH_A0_PASC);
#endif
#if PCSA1_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_A, PCS_1, SPI_CH_A1_FMSZ, SPI_CH_A1_CPOL, SPI_CH_A1_CPHA, SPI_CH_A1_CSSCK, SPI_CH_A1_DT, SPI_CH_A1_ASC, SPI_CH_A1_LSBFE);
        SPI_InitCh(SPI_CH_A, PCS_1, SPI_CH_A1_BR, SPI_CH_A1_BRP, SPI_CH_A1_DBR);    
        SPI_ConfigPrescalers(SPI_CH_A, PCS_1, SPI_CH_A1_PCSSCK, SPI_CH_A1_PDT, SPI_CH_A1_PASC);      
#endif
#if PCSA2_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_A, PCS_2, SPI_CH_A2_FMSZ, SPI_CH_A2_CPOL, SPI_CH_A2_CPHA, SPI_CH_A2_CSSCK, SPI_CH_A2_DT, SPI_CH_A2_ASC, SPI_CH_A2_LSBFE);
        SPI_InitCh(SPI_CH_A, PCS_2, SPI_CH_A2_BR, SPI_CH_A2_BRP, SPI_CH_A2_DBR);
        SPI_ConfigPrescalers(SPI_CH_A, PCS_2, SPI_CH_A2_PCSSCK, SPI_CH_A2_PDT, SPI_CH_A2_PASC);      
#endif
#if PCSA3_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_A, PCS_3, SPI_CH_A3_FMSZ, SPI_CH_A3_CPOL, SPI_CH_A3_CPHA, SPI_CH_A3_CSSCK, SPI_CH_A3_DT, SPI_CH_A3_ASC, SPI_CH_A3_LSBFE);
        SPI_InitCh(SPI_CH_A, PCS_3, SPI_CH_A3_BR, SPI_CH_A3_BRP, SPI_CH_A3_DBR);    
        SPI_ConfigPrescalers(SPI_CH_A, PCS_3, SPI_CH_A3_PCSSCK, SPI_CH_A3_PDT, SPI_CH_A3_PASC);      
#endif
#if PCSA4_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_A, PCS_4, SPI_CH_A4_FMSZ, SPI_CH_A4_CPOL, SPI_CH_A4_CPHA, SPI_CH_A4_CSSCK, SPI_CH_A4_DT, SPI_CH_A4_ASC, SPI_CH_A4_LSBFE);
        SPI_InitCh(SPI_CH_A, PCS_4, SPI_CH_A4_BR, SPI_CH_A4_BRP, SPI_CH_A4_DBR); 
        SPI_ConfigPrescalers(SPI_CH_A, PCS_4, SPI_CH_A4_PCSSCK, SPI_CH_A4_PDT, SPI_CH_A4_PASC);
#endif
#if PCSA5_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_A, PCS_5, SPI_CH_A5_FMSZ, SPI_CH_A5_CPOL, SPI_CH_A5_CPHA, SPI_CH_A5_CSSCK, SPI_CH_A5_DT, SPI_CH_A5_ASC, SPI_CH_A5_LSBFE);
        SPI_InitCh(SPI_CH_A, PCS_5, SPI_CH_A5_BR, SPI_CH_A5_BRP, SPI_CH_A5_DBR);
        SPI_ConfigPrescalers(SPI_CH_A, PCS_5, SPI_CH_A5_PCSSCK, SPI_CH_A5_PDT, SPI_CH_A5_PASC);
#endif

#endif /*SPI_CH_A_EN*/



#if SPI_CH_B_EN
        DSPITxBufferCh[SPI_CH_B] = DSPITxBufferB; 
        DSPIRxBufferCh[SPI_CH_B] = DSPIRxBufferB;
        SPI_ConfigCh(SPI_CH_B, SPI_CH_B_MODE, SPI_CH_B_PCS_CONT, SPI_CH_B_PCSn);
    
        /* Set Clock and Transfer Attributes */
#if PCSB0_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_B, PCS_0, SPI_CH_B0_FMSZ, SPI_CH_B0_CPOL, SPI_CH_B0_CPHA, SPI_CH_B0_CSSCK, SPI_CH_B0_DT, SPI_CH_B0_ASC, SPI_CH_B0_LSBFE);
        SPI_InitCh(SPI_CH_B, PCS_0, SPI_CH_B0_BR, SPI_CH_B0_BRP, SPI_CH_B0_DBR);    
        SPI_ConfigPrescalers(SPI_CH_B, PCS_0, SPI_CH_B0_PCSSCK, SPI_CH_B0_PDT, SPI_CH_B0_PASC);
#endif
#if PCSB1_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_B, PCS_1, SPI_CH_B1_FMSZ, SPI_CH_B1_CPOL, SPI_CH_B1_CPHA, SPI_CH_B1_CSSCK, SPI_CH_B1_DT, SPI_CH_B1_ASC, SPI_CH_B1_LSBFE);
        SPI_InitCh(SPI_CH_B, PCS_1, SPI_CH_B1_BR, SPI_CH_B1_BRP, SPI_CH_B1_DBR);   
        SPI_ConfigPrescalers(SPI_CH_B, PCS_1, SPI_CH_B1_PCSSCK, SPI_CH_B1_PDT, SPI_CH_B1_PASC);
#endif
#if PCSB2_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_B, PCS_2, SPI_CH_B2_FMSZ, SPI_CH_B2_CPOL, SPI_CH_B2_CPHA, SPI_CH_B2_CSSCK, SPI_CH_B2_DT, SPI_CH_B2_ASC, SPI_CH_B2_LSBFE);
        SPI_InitCh(SPI_CH_B, PCS_2, SPI_CH_B2_BR, SPI_CH_B2_BRP, SPI_CH_B2_DBR); 
        SPI_ConfigPrescalers(SPI_CH_B, PCS_2, SPI_CH_B2_PCSSCK, SPI_CH_B2_PDT, SPI_CH_B2_PASC);
#endif
#if PCSB3_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_B, PCS_3, SPI_CH_B3_FMSZ, SPI_CH_B3_CPOL, SPI_CH_B3_CPHA, SPI_CH_B3_CSSCK, SPI_CH_B3_DT, SPI_CH_B3_ASC, SPI_CH_B3_LSBFE);
        SPI_InitCh(SPI_CH_B, PCS_3, SPI_CH_B3_BR, SPI_CH_B3_BRP, SPI_CH_B3_DBR);  
        SPI_ConfigPrescalers(SPI_CH_B, PCS_3, SPI_CH_B3_PCSSCK, SPI_CH_B3_PDT, SPI_CH_B3_PASC);
#endif
#if PCSB4_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_B, PCS_4, SPI_CH_B4_FMSZ, SPI_CH_B4_CPOL, SPI_CH_B4_CPHA, SPI_CH_B4_CSSCK, SPI_CH_B4_DT, SPI_CH_B4_ASC, SPI_CH_B4_LSBFE);
        SPI_InitCh(SPI_CH_B, PCS_4, SPI_CH_B4_BR, SPI_CH_B4_BRP, SPI_CH_B4_DBR); 
        SPI_ConfigPrescalers(SPI_CH_B, PCS_4, SPI_CH_B4_PCSSCK, SPI_CH_B4_PDT, SPI_CH_B4_PASC);
#endif
#if PCSB5_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_B, PCS_5, SPI_CH_B5_FMSZ, SPI_CH_B5_CPOL, SPI_CH_B5_CPHA, SPI_CH_B5_CSSCK, SPI_CH_B5_DT, SPI_CH_B5_ASC, SPI_CH_B5_LSBFE);
        SPI_InitCh(SPI_CH_B, PCS_5, SPI_CH_B5_BR, SPI_CH_B5_BRP, SPI_CH_B5_DBR); 
        SPI_ConfigPrescalers(SPI_CH_B, PCS_5, SPI_CH_B5_PCSSCK, SPI_CH_B5_PDT, SPI_CH_B5_PASC);
#endif

#endif /*SPI_CH_B_EN*/



#if SPI_CH_C_EN
        DSPITxBufferCh[SPI_CH_C] = DSPITxBufferC; 
        DSPIRxBufferCh[SPI_CH_C] = DSPIRxBufferC;
        SPI_ConfigCh(SPI_CH_C, SPI_CH_C_MODE, SPI_CH_C_PCS_CONT, SPI_CH_C_PCSn);
    
        /* Set Clock and Transfer Attributes */
#if PCSC0_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_C, PCS_0, SPI_CH_C0_FMSZ, SPI_CH_C0_CPOL, SPI_CH_C0_CPHA, SPI_CH_C0_CSSCK, SPI_CH_C0_DT, SPI_CH_C0_ASC, SPI_CH_C0_LSBFE);
        SPI_InitCh(SPI_CH_C, PCS_0, SPI_CH_C0_BR, SPI_CH_C0_BRP, SPI_CH_C0_DBR);                              
        SPI_ConfigPrescalers(SPI_CH_C, PCS_0, SPI_CH_C0_PCSSCK, SPI_CH_C0_PDT, SPI_CH_C0_PASC);
#endif
#if PCSC1_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_C, PCS_1, SPI_CH_C1_FMSZ, SPI_CH_C1_CPOL, SPI_CH_C1_CPHA, SPI_CH_C1_CSSCK, SPI_CH_C1_DT, SPI_CH_C1_ASC, SPI_CH_C1_LSBFE);
        SPI_InitCh(SPI_CH_C, PCS_1, SPI_CH_C1_BR, SPI_CH_C1_BRP, SPI_CH_C1_DBR);
        SPI_ConfigPrescalers(SPI_CH_C, PCS_0, SPI_CH_C1_PCSSCK, SPI_CH_C1_PDT, SPI_CH_C1_PASC);
#endif
#if PCSC2_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_C, PCS_2, SPI_CH_C2_FMSZ, SPI_CH_C2_CPOL, SPI_CH_C2_CPHA, SPI_CH_C2_CSSCK, SPI_CH_C2_DT, SPI_CH_C2_ASC, SPI_CH_C2_LSBFE);
        SPI_InitCh(SPI_CH_C, PCS_2, SPI_CH_C2_BR, SPI_CH_C2_BRP, SPI_CH_C2_DBR); 
        SPI_ConfigPrescalers(SPI_CH_C, PCS_2, SPI_CH_C2_PCSSCK, SPI_CH_C2_PDT, SPI_CH_C2_PASC);
#endif
#if PCSC3_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_C, PCS_3, SPI_CH_C3_FMSZ, SPI_CH_C3_CPOL, SPI_CH_C3_CPHA, SPI_CH_C3_CSSCK, SPI_CH_C3_DT, SPI_CH_C3_ASC, SPI_CH_C3_LSBFE);
        SPI_InitCh(SPI_CH_C, PCS_3, SPI_CH_C3_BR, SPI_CH_C3_BRP, SPI_CH_C3_DBR); 
        SPI_ConfigPrescalers(SPI_CH_C, PCS_3, SPI_CH_C3_PCSSCK, SPI_CH_C3_PDT, SPI_CH_C3_PASC);
#endif
#if PCSC4_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_C, PCS_4, SPI_CH_C4_FMSZ, SPI_CH_C4_CPOL, SPI_CH_C4_CPHA, SPI_CH_C4_CSSCK, SPI_CH_C4_DT, SPI_CH_C4_ASC, SPI_CH_C4_LSBFE);
        SPI_InitCh(SPI_CH_C, PCS_4, SPI_CH_C4_BR, SPI_CH_C4_BRP, SPI_CH_C4_DBR); 
        SPI_ConfigPrescalers(SPI_CH_C, PCS_4, SPI_CH_C4_PCSSCK, SPI_CH_C4_PDT, SPI_CH_C4_PASC);
#endif
#if PCSC5_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_C, PCS_5, SPI_CH_C5_FMSZ, SPI_CH_C5_CPOL, SPI_CH_C5_CPHA, SPI_CH_C5_CSSCK, SPI_CH_C5_DT, SPI_CH_C5_ASC, SPI_CH_C5_LSBFE);
        SPI_InitCh(SPI_CH_C, PCS_5, SPI_CH_C5_BR, SPI_CH_C5_BRP, SPI_CH_C5_DBR);
        SPI_ConfigPrescalers(SPI_CH_C, PCS_5, SPI_CH_C5_PCSSCK, SPI_CH_C5_PDT, SPI_CH_C5_PASC);
#endif

#endif /*SPI_CH_C_EN*/



#if SPI_CH_D_EN
        DSPITxBufferCh[SPI_CH_D] = DSPITxBufferD; 
        DSPIRxBufferCh[SPI_CH_D] = DSPIRxBufferD;
        SPI_ConfigCh(SPI_CH_D, SPI_CH_D_MODE, SPI_CH_D_PCS_CONT, SPI_CH_D_PCSn);
    
        /* Set Clock and Transfer Attributes */
#if PCSD0_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_D, PCS_0, SPI_CH_D0_FMSZ, SPI_CH_D0_CPOL, SPI_CH_D0_CPHA, SPI_CH_D0_CSSCK, SPI_CH_D0_DT, SPI_CH_D0_ASC, SPI_CH_D0_LSBFE);
        SPI_InitCh(SPI_CH_D, PCS_0, SPI_CH_D0_BR, SPI_CH_D0_BRP, SPI_CH_D0_DBR);
        SPI_ConfigPrescalers(SPI_CH_D, PCS_0, SPI_CH_D0_PCSSCK, SPI_CH_D0_PDT, SPI_CH_D0_PASC);
#endif
#if PCSD1_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_D, PCS_1, SPI_CH_D1_FMSZ, SPI_CH_D1_CPOL, SPI_CH_D1_CPHA, SPI_CH_D1_CSSCK, SPI_CH_D1_DT, SPI_CH_D1_ASC, SPI_CH_D1_LSBFE);
        SPI_InitCh(SPI_CH_D, PCS_1, SPI_CH_D1_BR, SPI_CH_D1_BRP, SPI_CH_D1_DBR); 
        SPI_ConfigPrescalers(SPI_CH_D, PCS_1, SPI_CH_D1_PCSSCK, SPI_CH_D1_PDT, SPI_CH_D1_PASC);
#endif
#if PCSD2_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_D, PCS_2, SPI_CH_D2_FMSZ, SPI_CH_D2_CPOL, SPI_CH_D2_CPHA, SPI_CH_D2_CSSCK, SPI_CH_D2_DT, SPI_CH_D2_ASC, SPI_CH_D2_LSBFE);
        SPI_InitCh(SPI_CH_D, PCS_2, SPI_CH_D2_BR, SPI_CH_D2_BRP, SPI_CH_D2_DBR);  
        SPI_ConfigPrescalers(SPI_CH_D, PCS_2, SPI_CH_D2_PCSSCK, SPI_CH_D2_PDT, SPI_CH_D2_PASC);
#endif
#if PCSD3_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_D, PCS_3, SPI_CH_D3_FMSZ, SPI_CH_D3_CPOL, SPI_CH_D3_CPHA, SPI_CH_D3_CSSCK, SPI_CH_D3_DT, SPI_CH_D3_ASC, SPI_CH_D3_LSBFE);
        SPI_InitCh(SPI_CH_D, PCS_3, SPI_CH_D3_BR, SPI_CH_D3_BRP, SPI_CH_D3_DBR); 
        SPI_ConfigPrescalers(SPI_CH_D, PCS_3, SPI_CH_D3_PCSSCK, SPI_CH_D3_PDT, SPI_CH_D3_PASC);
#endif
#if PCSD4_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_D, PCS_4, SPI_CH_D4_FMSZ, SPI_CH_D4_CPOL, SPI_CH_D4_CPHA, SPI_CH_D4_CSSCK, SPI_CH_D4_DT, SPI_CH_D4_ASC, SPI_CH_D4_LSBFE);
        SPI_InitCh(SPI_CH_D, PCS_4, SPI_CH_D4_BR, SPI_CH_D4_BRP, SPI_CH_D4_DBR);  
        SPI_ConfigPrescalers(SPI_CH_D, PCS_4, SPI_CH_D4_PCSSCK, SPI_CH_D4_PDT, SPI_CH_D4_PASC);
#endif
#if PCSD5_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_D, PCS_5, SPI_CH_D5_FMSZ, SPI_CH_D5_CPOL, SPI_CH_D5_CPHA, SPI_CH_D5_CSSCK, SPI_CH_D5_DT, SPI_CH_D5_ASC, SPI_CH_D5_LSBFE);
        SPI_InitCh(SPI_CH_D, PCS_5, SPI_CH_D5_BR, SPI_CH_D5_BRP, SPI_CH_D5_DBR);  
        SPI_ConfigPrescalers(SPI_CH_D, PCS_5, SPI_CH_D5_PCSSCK, SPI_CH_D5_PDT, SPI_CH_D5_PASC);
#endif

#endif /*SPI_CH_D_EN*/


#if SPI_CH_E_EN
        DSPITxBufferCh[SPI_CH_E] = DSPITxBufferE;
        DSPIRxBufferCh[SPI_CH_E] = DSPIRxBufferE;
        SPI_ConfigCh(SPI_CH_E, SPI_CH_E_MODE, SPI_CH_E_PCS_CONT, SPI_CH_E_PCSn);

        /* Set Clock and Transfer Attributes */
#if PCSE0_ENABLE
        SPI_ConfigClockTransferParam(SPI_CH_E, PCS_0, SPI_CH_E0_FMSZ, SPI_CH_E0_CPOL, SPI_CH_E0_CPHA, SPI_CH_E0_CSSCK, SPI_CH_E0_DT, SPI_CH_E0_ASC, SPI_CH_E0_LSBFE);
        SPI_InitCh(SPI_CH_E, PCS_0, SPI_CH_E0_BR, SPI_CH_E0_BRP, SPI_CH_E0_DBR);
        SPI_ConfigPrescalers(SPI_CH_E, PCS_0, SPI_CH_E0_PCSSCK, SPI_CH_E0_PDT, SPI_CH_E0_PASC);
#endif
#if PCSE1_ENABLE
        SPI_ConfigClockTransferParam (SPI_CH_E,PCS_1,SPI_CH_E1_FMSZ,SPI_CH_E1_CPOL,SPI_CH_E1_CPHA, SPI_CH_E1_CSSCK,SPI_CH_E1_DT,SPI_CH_E1_ASC,SPI_CH_E1_LSBFE);
        SPI_InitCh (SPI_CH_E,PCS_1,SPI_CH_E1_BR,SPI_CH_E1_BRP, SPI_CH_E1_DBR);
        SPI_ConfigPrescalers(SPI_CH_E, PCS_1, SPI_CH_E1_PCSSCK, SPI_CH_E1_PDT, SPI_CH_E1_PASC);
#endif
#if PCSE2_ENABLE
        SPI_ConfigClockTransferParam (SPI_CH_E,PCS_2,SPI_CH_E2_FMSZ,SPI_CH_E2_CPOL,SPI_CH_E2_CPHA, SPI_CH_E2_CSSCK,SPI_CH_E2_DT,SPI_CH_E2_ASC,SPI_CH_E2_LSBFE);
        SPI_InitCh (SPI_CH_E,PCS_2,SPI_CH_E2_BR,SPI_CH_E2_BRP, SPI_CH_E2_DBR);
        SPI_ConfigPrescalers(SPI_CH_E, PCS_2, SPI_CH_E2_PCSSCK, SPI_CH_E2_PDT, SPI_CH_E2_PASC);
#endif 
#if PCSE3_ENABLE
        SPI_ConfigClockTransferParam (SPI_CH_E,PCS_3,SPI_CH_E3_FMSZ,SPI_CH_E3_CPOL,SPI_CH_E3_CPHA, SPI_CH_E3_CSSCK,SPI_CH_E3_DT,SPI_CH_E3_ASC,SPI_CH_E3_LSBFE);
        SPI_InitCh (SPI_CH_E,PCS_3,SPI_CH_E3_BR,SPI_CH_E3_BRP, SPI_CH_E3_DBR);
        SPI_ConfigPrescalers(SPI_CH_E, PCS_3, SPI_CH_E3_PCSSCK, SPI_CH_E3_PDT, SPI_CH_E3_PASC);
#endif
#if PCSE4_ENABLE
        SPI_ConfigClockTransferParam (SPI_CH_E,PCS_4,SPI_CH_E4_FMSZ,SPI_CH_E4_CPOL,SPI_CH_E4_CPHA, SPI_CH_E4_CSSCK,SPI_CH_E4_DT,SPI_CH_E4_ASC,SPI_CH_E4_LSBFE);
        SPI_InitCh (SPI_CH_E,PCS_4,SPI_CH_E4_BR,SPI_CH_E4_BRP, SPI_CH_E4_DBR);
        SPI_ConfigPrescalers(SPI_CH_E, PCS_4, SPI_CH_E4_PCSSCK, SPI_CH_E4_PDT, SPI_CH_E4_PASC);
#endif
#if PCSE5_ENABLE
        SPI_ConfigClockTransferParam (SPI_CH_E,PCS_5,SPI_CH_E5_FMSZ,SPI_CH_E5_CPOL,SPI_CH_E5_CPHA, SPI_CH_E5_CSSCK,SPI_CH_E5_DT,SPI_CH_E5_ASC,SPI_CH_E5_LSBFE);
        SPI_InitCh (SPI_CH_E,PCS_5,SPI_CH_E5_BR,SPI_CH_E5_BRP, SPI_CH_E5_DBR);
        SPI_ConfigPrescalers(SPI_CH_E, PCS_5, SPI_CH_E5_PCSSCK, SPI_CH_E5_PDT, SPI_CH_E5_PASC);
#endif

#endif /*SPI_CH_E_EN*/


    } 
    else
    {
        retValue = PERIPHERAL_ALREADY_CONFIGURED; 
    }
    return retValue;
    
}



#ifdef _SPI_USE_DMA_ /* Functions not yet adapted for K2 microcontroller */
/******************************************************************************
**   Function    : SPI_RxTxBuffer
**
**   Description:
**    This function configures and enables transmission and reception on selected
**    channel with DMA support.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint16_T* txBuffer : Pointer to transmission buffer
**    [in] uint8_T txSize : Size of buffer to be transmitted
**    [in] uint8_T ctas : Clock Transfer Attribute Select
**
**   Returns:
**    NO_ERROR                   - SPI channel correctly configured and enabled.
**    SPI_WRONG_SIZE             - txSize is greater than the size of configured buffer.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
SpiError_T SPI_RxTxBuffer(uint8_T channel, uint16_T *txBuffer, uint8_T txSize, uint8_T ctas)
{
    int16_T ret;
    uint8_T nPCS;

    if (SPIChannelStatus[channel] != 0u) /* initialized? */
    {
        if ((txSize > 0u) && (txSize <= TX_BUFFER_SIZE))
        {
            DSPI_PTR_ARRAY[channel]->MCR.R |= 0x00000001U; /*Halt Bit is set */    
            DSPI_PTR_ARRAY[channel]->RSER.R = 0x0u;  
            DSPI_PTR_ARRAY[channel]->SR.B.RFOF = 0u; /* clean an eventual overflow condition */
            nPCS =1u << ctas;
            SPILastPcs[channel] = ctas;
            ret = SPI_Write(channel, txBuffer, txSize, nPCS, ctas);
            if (ret == NO_ERROR)
            {
                DSPI_PTR_ARRAY[channel]->SR.R = 0x90000000U; /* Clear EOQF */
                DSPI_PTR_ARRAY[channel]->RSER.R |= 0x03030000U;/* Setup TFFF to cause DMA transfer*/
                                                              /* Setup RFDF to cause DMA transfer*/
                DSPI_PTR_ARRAY[channel]->MCR.R &= 0xFFFFFFFEU; /* Halt Bit is clear */
            }
        }
        else
        {
            ret = SPI_WRONG_SIZE;
        }
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED;
    }
    return ret;
    
}


/******************************************************************************
**   Function    : SPI_GetRxData
**
**   Description:
**    This method  reads the data sent by the slave, previously copied in
**    memory by DMA.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [out] uint16_T rxBuffer[] : Pointer to reception buffer
**    [in] uint32_T rxSize : Size of buffer to be received
**
**   Returns:
**    NO_ERROR                   - SPI channel correctly configured and enabled.
**    SPI_WRONG_SIZE             - rxSize is greater than the size of configured buffer.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
SpiError_T SPI_GetRxData(uint8_T channel, uint16_T rxBuffer[], uint32_T rxSize)
{
    SpiError_T ret = NO_ERROR;

    if (SPIChannelStatus[channel] != 0u)  /* initialized? */
    {
        if ((rxSize > 0u) && (rxSize <= RX_BUFFER_SIZE))
        {
            uint8_T cnt;
            vuint32_T  dspiRxBuffer;
            dspiRxBuffer = (vuint32_T)DSPIRxBufferCh[channel];
            for (cnt = 0u; cnt < rxSize; cnt++)
            {
                rxBuffer[cnt] = (uint16_T)*(vuint32_T*)(dspiRxBuffer);
                dspiRxBuffer += sizeof(uint32_T);
            }
        }
        else
        {
            ret = SPI_WRONG_SIZE;
        }
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED; 
    }
    return ret;
}



/******************************************************************************
**   Function    : SPI_Write
**
**   Description:
**    Configures and enables DMA channels.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint16_T* txBuffer : Pointer to transmission buffer
**    [in] uint8_T wordNumber : Size of buffer to be transmitted
**    [in] uint8_T nPCS : Peripheral Chip Select mask
**    [in] uint8_T ctas : Clock Transfer Attribute Select
**
**   Returns:
**    NO_ERROR                      - SPI channel correctly configured and enabled.
**    PERIPHERAL_ALREADY_CONFIGURED - Peripheral already configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#pragma ghs startnomisra
int16_T SPI_Write(uint8_T channel, uint16_T *txBuffer, uint8_T wordNumber, uint8_T nPCS, uint8_T ctas )
{
    uint8_T index;
    uint8_T end;
    uint8_T dmaTxCh;
    uint8_T dmaRxCh;
    vuint32_T *dspiRxBuffer;
    vuint32_T *dspiTxBuffer;
    vuint32_T contPcs;
    DspiPtr_T dspiPtr;
    int16_T dmaRet;
    uint32_T txCmd;

    dmaTxCh = SPI_DMA_CH[channel * 2u];
    dspiPtr = DSPI_PTR_ARRAY[channel];
    dspiTxBuffer = DSPITxBufferCh[channel];  
    
    dmaRxCh = SPI_DMA_CH[(channel * 2u) + 1u];
    dspiRxBuffer = DSPIRxBufferCh[channel];  

    DMA_Disable(dmaTxCh);
    DMA_Disable(dmaRxCh);


    dmaRet = DMA_Init(dmaRxCh, (uint32_T *) dspiRxBuffer, DSIZE_32, 4u, (uint32_T *) (DSPIPopR[channel]), SSIZE_32, 0u, 1u, (uint16_T)wordNumber, 1u);
    if (dmaRet == NO_ERROR)
    {
        dmaRet = dmaRet | (DMA_Init(dmaTxCh, (uint32_T *)(DSPIPushR[channel]), DSIZE_32, 0u, (uint32_T *) dspiTxBuffer, SSIZE_32, 4u, 1u, (uint16_T)wordNumber, 0u));

        if (dmaRet == NO_ERROR)
        {
            /* Flush TX and RX FIFO by writing a '1' to the CLR_TXF and CLR_RXF bit in the DSPIx_MCR */
            dspiPtr->MCR.R |= 0x00000c00U;
            /* Clear Transfer Count */
            dspiPtr->TCR.R = 0x00000000U;
            end = wordNumber - 1u;
            contPcs = ( (PCS_CONT[channel]&nPCS) == 1u ) ? 0x80000000U : 0x00000000U;
            
            /* set PUSHR TX command in master mode */
            if (DSPI_PTR_ARRAY[channel]->MCR.B.MSTR == SPI_MASTER)
            {
                txCmd = contPcs | ((uint32_T)ctas << 28u) | ((uint32_T)nPCS << 16u);
            }
            else
            {
                txCmd = 0u;
            }
          
            /* Solinas: Prepare the buffer  adding  EOQ=0 nPCS=active Non continuos SCK |Data */
            for (index = 0u; index <= end; index++)
            {
                dspiTxBuffer[index] = txCmd | ((uint32_T)txBuffer[index] & 0x0000ffffU);
            }
            
            /* add last data to queue */
            if (DSPI_PTR_ARRAY[channel]->MCR.B.MSTR == SPI_MASTER)
            {
                /* Clear Transfer Counter in the first tx */
                dspiTxBuffer[0] |= SPI_PUSHR_CTCNT_MASK;
                /* start master transmission setting EOQ flag */
                dspiTxBuffer[end] |= SPI_PUSHR_EOQ_MASK;
            }

            DMA_Enable(dmaRxCh);
            DMA_Enable(dmaTxCh);
        }
    }
    
    return dmaRet;
}
#pragma ghs endnomisra

#endif /* _SPI_USE_DMA_ */

/******************************************************************************
**   Function    : SPI_Disable
**
**   Description:
**    Disables SPI and DMA associated channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel to be disabled
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPI_Disable(uint8_T channel)
{
#ifdef _SPI_USE_DMA_
    uint8_T DMA_Tx_Ch = SPI_DMA_CH[channel * 2u];
    uint8_T DMA_Rx_Ch = SPI_DMA_CH[(channel * 2u) +1u];
#endif

    DSPI_PTR_ARRAY[channel]->MCR.B.HALT = 1u;
#ifdef _SPI_USE_DMA_
    DMA_Disable(DMA_Tx_Ch);
#endif
    DSPI_PTR_ARRAY[channel]->RSER.R = 0x0u;
    DSPI_PTR_ARRAY[channel]->SR.B.RFOF = 0u; /* clean an eventual overflow condition */
#ifdef _SPI_USE_DMA_
    DMA_Disable(DMA_Rx_Ch);
#endif
    DSPI_PTR_ARRAY[channel]->MCR.B.CLR_TXF = 1u;
    DSPI_PTR_ARRAY[channel]->MCR.B.CLR_RXF = 1u;

}


/******************************************************************************
**   Function    : SPI_TxRx
**
**   Description:
**    Transmission and reception of SPI messages.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T nPCS : Peripheral Chip Select mask
**    [in] const uint16_T* txBuffer : Pointer to the buffer to be transmitted
**    [in] uint16_T* rxBuffer : Pointer to the receive buffer
**    [in] uint8_T size : Size of the message
**
**   Returns:
**    NO_ERROR                       - SPI messages correctly transmitted and received.
**    SPI_WRONG_SIZE                 - size is greater than the size of configured buffer.
**    SPI_TIMEOUT_ERROR              - SPI timeout error.
**    PERIPHERAL_NOT_INITIALIZED     - SPI channel is not initialized.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
SpiError_T SPI_TxRx(uint8_T channel, uint8_T nPCS, const uint16_T *txBuffer, uint16_T *rxBuffer, uint8_T size)
{
    int16_T ret = NO_ERROR;
    int16_T i;
    int16_T k = 0;
    
    ret = SPI_Transmit(channel, nPCS, txBuffer, size);
    
    if (ret == NO_ERROR)
    {
        /* Set timeout to wait that RX FIFO size is correct */
        SPI_SetTimeout(channel, nPCS, size);
        uint8_T spiTxStatus = TIMEOUT_PENDING;
        while (spiTxStatus == TIMEOUT_PENDING)
        {
            TIMING_GetTimeoutStatus(CfgSpiChHandler[channel], &spiTxStatus);
            if (DSPI_PTR_ARRAY[channel]->SR.B.RXCTR == size)
            {
                /* RX counter reached desired size */
                ret = NO_ERROR;
                break;
            }
            else
            {
                ret = SPI_TIMEOUT_ERROR;
            }
        }
        if (ret != SPI_TIMEOUT_ERROR)
        {
            ret = SPI_ReadData(channel, rxBuffer, size);
        }
        else
        {
            if (DSPI_PTR_ARRAY[channel]->SR.B.RXCTR == size)
            {
                ret = SPI_ReadData(channel, rxBuffer, size);
            }
            else
            {
                DSPI_PTR_ARRAY[channel]->MCR.B.CLR_RXF = 1u; /* flush RX FIFO */
            }
        }
    }

    return ret;
}


/******************************************************************************
**   Function    : SPI_Enable
**
**   Description:
**    Enables SPI channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel to be enabled
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern void SPI_Enable(uint8_T channel)
{

    DSPI_PTR_ARRAY[channel]->MCR.B.HALT = 0u;

}


/******************************************************************************
**   Function    : SPI_GetChannelStatus
**
**   Description:
**    Gets and returns SPI channel status.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel to get status.
**
**   Returns:
**    SPI_STOPPED                - SPI channel is stopped.
**    SPI_RUNNING                - SPI channel is running.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#pragma ghs section text=FLASH_TEXTRAM_SECTION
SpiError_T SPI_GetChannelStatus(uint8_T channel)
{
    SpiError_T ret;

    if (SPIChannelStatus[channel] != 0u) /* initialized? */
    {
        DspiPtr_T dspiPtr;
        dspiPtr = DSPI_PTR_ARRAY[channel];   
        if ((dspiPtr->SR.R & 0x40000000u) == 0u) 
        {
            ret = SPI_STOPPED;
        }
        else
        {
            ret = SPI_RUNNING;
        }
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED; 
    }
    
    return ret;
}
#pragma ghs section text=default

/******************************************************************************
**   Function    : SPI_SetInterruptHandler
**
**   Description:
**    Sets interrupt handler for specific SPI channel (and chip select).
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T cs : Chip Select
**    [in] TaskType isrFunction : ID for Interrupt Service Routine
**
**   Returns:
**    NO_ERROR                       - Interrupt handler set correctly.
**    SPI_ERROR                      - Interrupt handler not set correctly.
**    PERIPHERAL_NOT_INITIALIZED     - SPI channel is not initialized
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T SPI_SetInterruptHandler(uint8_T channel, uint8_T cs, TaskType isrFunction)
{
    int16_T returnCode = NO_ERROR;

    if (SPIChannelStatus[channel] != 0u) /* initialized? */
    {

        switch (channel)
        {
#if SPI_CH_A_EN
            case SPI_CH_A:
            {
                SPIExTxDoneChAIrqVect[cs] = isrFunction;
            }
            break;
#endif

#if SPI_CH_B_EN
            case SPI_CH_B:
            {
                SPIExTxDoneChBIrqVect[cs] = isrFunction;
            }
            break;
#endif

#if SPI_CH_C_EN
            case SPI_CH_C:
            {
                SPIExTxDoneChCIrqVect[cs] = isrFunction;
            }
            break;
#endif

#if SPI_CH_D_EN
            case SPI_CH_D:
            {
                SPIExTxDoneChDIrqVect[cs] = isrFunction;
            }
            break;
#endif

#if SPI_CH_E_EN
            case SPI_CH_E:
            {
                SPIExTxDoneChEIrqVect[cs] = isrFunction;
            }
            break;
#endif

            default:
                returnCode = SPI_ERROR;
            break;
        }
    }
    else
    {
        returnCode = PERIPHERAL_NOT_INITIALIZED;
    }

    return returnCode;
}

/******************************************************************************
**   Function    : SPI_Transmit
**
**   Description:
**    Transmission of SPI message without DMA support.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T nPCS : Peripheral Chip Select mask
**    [in] const uint16_T txBuffer[] : Pointer to the buffer to be transmitted
**    [in] const uint8_T txSize : Size of the buffer to be transmitted
**
**   Returns:
**    NO_ERROR                   - SPI message correctly transmitted.
**    SPI_WRONG_SIZE             - txSize is greater than the size of configured buffer.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1u)
#pragma ghs section text=FLASH_TEXTRAM_SECTION
#endif
SpiError_T SPI_Transmit(uint8_T channel, uint8_T nPCS, const uint16_T txBuffer[], const uint8_T txSize)
{
    int16_T ret = NO_ERROR;
    int8_T index;
    int8_T end;
    uint8_T lCTAS;
    uint32_T contPCS;
    uint32_T txCmd;

    if (SPIChannelStatus[channel] != 0u) /* initialized? */
    {
        if ((txSize > 0u) && (txSize <= TX_FIFO_SIZE))
        {
            DSPI_PTR_ARRAY[channel]->RSER.R = 0x0u;
            DSPI_PTR_ARRAY[channel]->SR.B.RFOF = 0u; /* clean an eventual overflow condition */
            DSPI_PTR_ARRAY[channel]->MCR.B.CLR_TXF = 1u; /* flush TX FIFO */
            lCTAS = nPCS;
            nPCS = 1u << nPCS;
            end = (int8_T)(txSize - 2u);
            contPCS = ((PCS_CONT[channel]&nPCS) != 0u) ? 0x80000000u : 0x00000000u;

            /* set PUSHR TX command in master mode */
            if (DSPI_PTR_ARRAY[channel]->MCR.B.MSTR == SPI_MASTER)
            {
                txCmd = contPCS | ((uint32_T) (((uint32_T) lCTAS) << 28u)) | ((uint32_T) (((uint32_T) nPCS) << 16u));
            }
            else
            {
                txCmd = 0u;
            }

            /* enqueue data adding nPCS=active, non continuos SCK */
            for (index = 0; index <= end; index++)
            {
                DSPI_PTR_ARRAY[channel]->PUSHR.R = txCmd | ((uint32_T)txBuffer[index] & 0x0000ffffu);
            }

            /* add last data to queue */
            if (DSPI_PTR_ARRAY[channel]->MCR.B.MSTR == SPI_MASTER)
            {
                if (contPCS == 0x80000000u) /* continuous peripheral chip select enabled  */
                {
                    txCmd &= (0x7FFFFFFFu); /* disabling chip select after last frame transfer */
                }
                else
                {
                }
                /* start master transmission setting EOQ flag */
                DSPI_PTR_ARRAY[channel]->PUSHR.R = txCmd | ((uint32_T)txBuffer[index] & 0x0000ffffu) | SPI_PUSHR_EOQ_MASK;
            }
            else
            {
                DSPI_PTR_ARRAY[channel]->PUSHR.R = txCmd | ((uint32_T)txBuffer[index] & 0x0000ffffu);
            }
        }
        else
        {
            ret = SPI_WRONG_SIZE;
        }
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED;
    }

    return ret;
}
#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1u)
#pragma ghs section text=default
#endif

/******************************************************************************
**   Function    : SPIRes_Init
**
**   Description:
**    Initialization of SPI resource status.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPIRes_Init(void)
{
    SpiResFlagCH[0] = SPI_FREE;
    SpiResFlagCH[1] = SPI_FREE;
    SpiResFlagCH[2] = SPI_FREE;
    SpiResFlagCH[3] = SPI_FREE;
    SpiResFlagCH[4] = SPI_FREE;
}



/******************************************************************************
**   Function    : SPIRes_SetFlag
**
**   Description:
**    Sets status of SPI channel as SPI_BUSY.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPIRes_SetFlag(uint8_T channel)
{
    SpiResFlagCH[channel] = SPI_BUSY;    
}

/******************************************************************************
**   Function    : ResetSPIResFlag
**
**   Description:
**    Sets status of SPI channel as SPI_FREE.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void ResetSPIResFlag(uint8_T channel)
{
    SpiResFlagCH[channel] = SPI_FREE;    
}

/******************************************************************************
**   Function    : GetSPIResFlag
**
**   Description:
**    Returns actual status for SPI channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    SPI_FREE   - SPI free.
**    SPI_BUSY   - SPI busy.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint8_T GetSPIResFlag(uint8_T channel)
{
    return SpiResFlagCH[channel];
}

/******************************************************************************
**   Function    : SPIRes_GetFromChannel
**
**   Description:
**    Gets Resource ID from channel you need to protect from concurrent access.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [out] ResourceType* resource : Pointer to resource ID
**
**   Returns:
**    0  - No error
**    -1 - Error.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T SPIRes_GetFromChannel(uint8_T channel, ResourceType *resource)
{
    int16_T ret = 0;

    if (channel == 0u)
    {
        *resource = (ResourceType)RES_SPI_CHA;
    }
    else if (channel == 1u)
    {
        *resource = (ResourceType)RES_SPI_CHB;
    }
    else if (channel == 2u)
    {
        *resource = (ResourceType)RES_SPI_CHC;
    }
    else if (channel == 3u)
    {
        *resource = (ResourceType)RES_SPI_CHD;
    }
    else if (channel == 4u)
    {
        *resource = (ResourceType)RES_SPI_CHE;
    }
    else
    {
        ret = -1;
    }
    return ret;
}

#ifdef _SPI_USE_DMA_

/******************************************************************************
**   Function    : SPI_A_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel A.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPI_A_EOQ_ISR(void)
{
#ifdef _BUILD_SPI_
#if SPI_CH_A_EN
    DMA_0.CINT.R = DMACH_DSPI_0_RX;
    /* GK! to be verified if necessary or could be a problem to disable TX/RX DMA transfers */
    //DSPI_PTR_ARRAY[0]->RSER.R&=(~(0x03030000))
#if SPI_A_FUNC_INT
    if (SPIAIrqVect[SPILastPcs[SPI_CH_A]] != 0)
    {
        SPIAIrqVect[SPILastPcs[SPI_CH_A]]();
    }
#endif
#if SPI_A_FUNC_EXC
    ActivateTask(SPIExTxDoneChAIrqVect[SPILastPcs[SPI_CH_A]]);
#endif
    /* ResetSPIResFlag(SPI_CH_A); */
#endif
#endif
}

/******************************************************************************
**   Function    : SPI_B_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel B.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPI_B_EOQ_ISR(void)
{
#ifdef _BUILD_SPI_
#if SPI_CH_B_EN
    DMA_0.CINT.R = DMACH_DSPI_1_RX;
    /* GK! to be verified if necessary or could be a problem to disable TX/RX DMA transfers */
    //DSPI_PTR_ARRAY[1]->RSER.R&=(~(0x03030000))
#if SPI_B_FUNC_INT
    if (SPIBIrqVect[SPILastPcs[SPI_CH_B]] != 0)
    {
        SPIBIrqVect[SPILastPcs[SPI_CH_B]]();
    }
#endif
#if SPI_B_FUNC_EXC
        ActivateTask(SPIExTxDoneChBIrqVect[SPILastPcs[SPI_CH_B]]);
#endif
    /* ResetSPIResFlag(SPI_CH_B); */
#endif
#endif
}

/******************************************************************************
**   Function    : SPI_C_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel C.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPI_C_EOQ_ISR(void)
{
#ifdef _BUILD_SPI_
#if SPI_CH_C_EN
    DMA_0.CINT.R = DMACH_DSPI_2_RX;
    /* GK! to be verified if necessary or could be a problem to disable TX/RX DMA transfers */
    //DSPI_PTR_ARRAY[2]->RSER.R&=(~(0x03030000))
#if SPI_C_FUNC_INT
    if (SPICIrqVect[SPILastPcs[SPI_CH_C]] != 0)
    {
        SPICIrqVect[SPILastPcs[SPI_CH_C]]();
    }
#endif
#if SPI_C_FUNC_EXC
        ActivateTask(SPIExTxDoneChCIrqVect[SPILastPcs[SPI_CH_C]]);
#endif
    /* ResetSPIResFlag(SPI_CH_C); */
#endif
#endif
}

/******************************************************************************
**   Function    : SPI_D_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel D.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPI_D_EOQ_ISR(void)
{
#ifdef _BUILD_SPI_
#if SPI_CH_D_EN
    DMA_0.CINT.R = DMACH_DSPI_3_RX;
    /* GK! to be verified if necessary or could be a problem to disable TX/RX DMA transfers */
    //DSPI_PTR_ARRAY[3]->RSER.R&=(~(0x03030000))
#if SPI_D_FUNC_INT
    if (SPIDIrqVect[SPILastPcs[SPI_CH_D]] != 0)
    {
        SPIDIrqVect[SPILastPcs[SPI_CH_D]]();
    }
#endif
#if SPI_D_FUNC_EXC
        ActivateTask(SPIExTxDoneChDIrqVect[SPILastPcs[SPI_CH_D]]);
#endif
    /* ResetSPIResFlag(SPI_CH_D); */
#endif
#endif
}

/******************************************************************************
**   Function    : SPI_E_EOQ_ISR
**
**   Description:
**    End of DMA transfer ISR for SPI Channel E.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPI_E_EOQ_ISR(void)
{
#ifdef _BUILD_SPI_
#if SPI_CH_E_EN
    DMA_0.CINT.R = DMACH_DSPI_4_RX;
    /* GK! to be verified if necessary or could be a problem to disable TX/RX DMA transfers */
    //DSPI_PTR_ARRAY[3]->RSER.R&=(~(0x03030000))
#if SPI_E_FUNC_INT
    if (SPIEIrqVect[SPILastPcs[SPI_CH_E]] != 0)
    {
        SPIEIrqVect[SPILastPcs[SPI_CH_E]]();
    }
#endif
#if SPI_E_FUNC_EXC
        ActivateTask(SPIExTxDoneChEIrqVect[SPILastPcs[SPI_CH_E]]);
#endif
    /* ResetSPIResFlag(SPI_CH_D); */
#endif
#endif
}

#else /* _SPI_USE_DMA_ */

/* configured in TASK.cfg */
void SPI_A_EOQ_ISR(void)
{
}
void SPI_B_EOQ_ISR(void)
{
}
void SPI_C_EOQ_ISR(void)
{
}
void SPI_D_EOQ_ISR(void)
{
}
void SPI_E_EOQ_ISR(void)
{
}
#endif /* _SPI_USE_DMA_ */

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : SPI_ConfigCh
**
**   Description:
**    Configures the selected Channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T spiMode : Mode of the selected channel (Master/Slave)
**    [in] uint8_T continuousSck : Continuous clock, 0 Disable - 1 Enable
**    [in] uint8_T nPCS : Peripheral Chip Select mask
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SPI_ConfigCh(uint8_T channel, uint8_T spiMode, uint8_T continuousSck, uint8_T nPCS)
{
    DspiPtr_T DSPI_ptr = DSPI_PTR_ARRAY[channel];

    DSPI_ptr->MCR.R = 0x00000001u;
    DSPI_ptr->MCR.B.MSTR = spiMode;
    DSPI_ptr->MCR.B.CONT_SCKE = continuousSck;
    DSPI_ptr->MCR.R = (DSPI_ptr->MCR.R & PCSIS_MASK_N) | ( (uint32_T) (((uint32_T) nPCS) << 16u));

    DSPI_PTR_ARRAY[channel]->RSER.R = 0x0u;

    switch (channel)
    {
        case SPI_CH_A:
        {
            SPI_VectorInitA();
        }
        break;

        case SPI_CH_B:
        {
            SPI_VectorInitB();
        }
        break;

        case SPI_CH_C:
        {
            SPI_VectorInitC();
        }
        break;

        case SPI_CH_D:
        {
            SPI_VectorInitD();
        }
        break;

        case SPI_CH_E:
        {
            SPI_VectorInitE();
        }
        break;

        default:
        break;
    }
}

/******************************************************************************
**   Function    : SPI_ConfigClockTransferParam
**
**   Description:
**    Configures the clock parameters of the selected Channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T chipSelect : Peripheral Chip Select mask
**    [in] uint8_T frameBitSize : Size
**    [in] uint8_T clockPolarity : 0 Inactive state is low, 1 Inactive state is high
**    [in] uint8_T clockPhase : Phase of clock signal
**    [in] uint8_T cssckDelay : CSSCK Delay
**    [in] uint8_T dtDelay : DT Delay
**    [in] uint8_T ascDelay : ASC Delay
**    [in] uint8_T pLsbfe : LSBFE
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SPI_ConfigClockTransferParam(uint8_T channel, uint8_T chipSelect, uint8_T frameBitSize, uint8_T clockPolarity, uint8_T clockPhase, uint8_T cssckDelay, uint8_T dtDelay, uint8_T ascDelay, uint8_T pLsbfe)
{
    DspiPtr_T DSPI_ptr = DSPI_PTR_ARRAY[channel];

    DSPI_ptr->CTAR[chipSelect].B.FMSZ = frameBitSize ;
    DSPI_ptr->CTAR[chipSelect].B.CPOL = clockPolarity;
    DSPI_ptr->CTAR[chipSelect].B.CPHA = clockPhase;
    DSPI_ptr->CTAR[chipSelect].B.CSSCK = cssckDelay;
    DSPI_ptr->CTAR[chipSelect].B.DT = dtDelay;
    DSPI_ptr->CTAR[chipSelect].B.ASC = ascDelay;
    DSPI_ptr->CTAR[chipSelect].B.LSBFE = pLsbfe;
}

/******************************************************************************
**   Function    : SPI_ConfigPrescalers
**
**   Description:
**    Configures the prescaler parameters of the selected Channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T chipSelect : Peripheral Chip Select mask
**    [in] uint8_T cssckPrescaler : CSSCK Prescaler
**    [in] uint8_T dtPrescaler : DT Prescaler
**    [in] uint8_T ascPrescaler : ASC Prescaler
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void SPI_ConfigPrescalers(uint8_T channel, uint8_T chipSelect, uint8_T cssckPrescaler, uint8_T dtPrescaler, uint8_T ascPrescaler)
{
    DspiPtr_T DSPI_ptr = DSPI_PTR_ARRAY[channel];

    DSPI_ptr->CTAR[chipSelect].B.PCSSCK = cssckPrescaler;
    DSPI_ptr->CTAR[chipSelect].B.PDT = dtPrescaler   ;
    DSPI_ptr->CTAR[chipSelect].B.PASC = ascPrescaler  ;
}

/******************************************************************************
**   Function    : SPI_ReadData
**
**   Description:
**    Reception of SPI message without DMA support
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [out] uint16_T rxBuffer[] : Pointer to the receive buffer
**    [in] uint8_T rxSize : Size of the buffer to be received
**
**   Returns:
**    NO_ERROR                   - SPI message correctly received.
**    SPI_WRONG_SIZE             - rxSize is greater than the size of configured buffer.
**    PERIPHERAL_NOT_INITIALIZED - SPI channel is not initialized.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static SpiError_T SPI_ReadData(uint8_T channel, uint16_T rxBuffer[], uint8_T rxSize)
{
    SpiError_T ret = NO_ERROR;
    uint8_T cnt;

    if (SPIChannelStatus[channel] != 0u)  /* initialized? */
    {
        if ((rxSize > 0u) && (rxSize <= RX_FIFO_SIZE))
        {
            if (rxSize > DSPI_PTR_ARRAY[channel]->SR.B.RXCTR)
            {
                rxSize = (uint8_T) (DSPI_PTR_ARRAY[channel]->SR.B.RXCTR);
            }
            else
            {
                /* MISRA */
            }

            for (cnt = 0u; cnt < rxSize; cnt++)
            {
                /* Read data received by master SPI */
                rxBuffer[cnt] = (uint16_T)DSPI_PTR_ARRAY[channel]->POPR.B.RXDATA;
            }

            if (DSPI_PTR_ARRAY[channel]->MCR.B.MSTR == SPI_MASTER)
            {
                /* Clear TCF, RFDF, EOQ flags by writing 1 */
                DSPI_PTR_ARRAY[channel]->SR.R = 0x90020000u;
            }
            else
            {
                /* Clear TCF, RFDF flags by writing 1 */
                DSPI_PTR_ARRAY[channel]->SR.R = 0x80020000u;
            }
            DSPI_PTR_ARRAY[channel]->MCR.B.CLR_RXF = 1u; /* flush RX FIFO */
        }
        else
        {
            ret = SPI_WRONG_SIZE;
        }
    }
    else
    {
        ret = PERIPHERAL_NOT_INITIALIZED;
    }
    return ret;
}

/******************************************************************************
**   Function    : SPI_InitCh
**
**   Description:
**    Configures the scaler parameters (baud rate) of the selected Channel.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T chipSelect : Peripheral Chip Select mask
**    [in] uint8_T brScaler : BR scaler
**    [in] uint8_T pbrScaler : PBR scaler
**    [in] uint8_T dbrValue : DBR value
**
**   Returns:
**    NO_ERROR - SPI Channel correctly configured.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static SpiError_T SPI_InitCh(uint8_T channel, uint8_T chipSelect, uint8_T brScaler, uint8_T pbrScaler, uint8_T dbrValue)
{
    DspiPtr_T DSPI_ptr = DSPI_PTR_ARRAY[channel];
    DSPI_ptr->CTAR[chipSelect].B.BR = brScaler;
    DSPI_ptr->CTAR[chipSelect].B.PBR = pbrScaler;
    DSPI_ptr->CTAR[chipSelect].B.DBR = dbrValue;
    SPIChannelStatus[channel] = 1u;
    return NO_ERROR;
}

/******************************************************************************
**   Function    : SPI_SetTimeout
**
**   Description:
**    Configures timeout for SPI Channels.
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [in] uint8_T chipSelect : Peripheral Chip Select mask
**    [in] uint8_T size : Size of Tx Buffer (must be < MAX_FIFO dimension)
**
**   Returns:
**    NO_ERROR - SPI Channel timeout correctly configured.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static SpiError_T SPI_SetTimeout(uint8_T channel, uint8_T chipSelect, uint8_T size)
{
    switch (channel)
    {
#if SPI_CH_A_EN
        case (SPI_CH_A):
            switch (chipSelect)
            {
#if PCSA0_ENABLE
                case (PCS_0):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_A0_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSA1_ENABLE
                case (PCS_1):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_A1_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSA2_ENABLE
                case (PCS_2):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_A2_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSA3_ENABLE
                case (PCS_3):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_A3_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSA4_ENABLE
                case (PCS_4):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_A4_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSA5_ENABLE
                case (PCS_5):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_A5_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if (TARGET_TYPE != MPC563XM) /*CS6 and CS7 not in this target*/
#if PCSA6_ENABLE
                case (PCS_6):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_A6_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSA7_ENABLE
                case (PCS_7):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_A7_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#endif /*(TARGET_TYPE != MPC563XM)*/
                default:
                    /* Set framesize values as default condition: this leads to a very small timeout*/
                    TIMING_SetTimeout((uint32_T)(size), &CfgSpiChHandler[channel]);
                break;
            }
        break;
#endif /*SPI_CH_A_EN*/

#if SPI_CH_B_EN
        case (SPI_CH_B):
            switch (chipSelect)
            {
#if PCSB0_ENABLE
                case (PCS_0):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_B0_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSB1_ENABLE
                case (PCS_1):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_B1_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSB2_ENABLE
                case (PCS_2):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_B2_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSB3_ENABLE
                case (PCS_3):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_B3_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSB4_ENABLE
                case (PCS_4):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_B4_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSB5_ENABLE
                case (PCS_5):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_B5_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if (TARGET_TYPE != MPC563XM) /*CS6 and CS7 not in this target*/
#if PCSB6_ENABLE
                case (PCS_6):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_B6_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSB7_ENABLE
                case (PCS_7):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_B7_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#endif /*(TARGET_TYPE != MPC563XM)*/
                default:
                    /* Set framesize values as default condition: this leads to a very small timeout*/
                    TIMING_SetTimeout((uint32_T)(size), &CfgSpiChHandler[channel]);
                break;
            }
        break;
#endif /*SPI_CH_B_EN*/

#if SPI_CH_C_EN
        case (SPI_CH_C):
            switch (chipSelect)
            {
#if PCSC0_ENABLE
                case (PCS_0):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_C0_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSC1_ENABLE
                case (PCS_1):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_C1_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSC2_ENABLE
                case (PCS_2):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_C2_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSC3_ENABLE
                case (PCS_3):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_C3_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSC4_ENABLE
                case (PCS_4):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_C4_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSC5_ENABLE
                case (PCS_5):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_C5_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if (TARGET_TYPE != MPC563XM) /*CS6 and CS7 not in this target*/
#if PCSC6_ENABLE
                case (PCS_6):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_C6_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSC7_ENABLE
                case (PCS_7):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_C7_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#endif /*(TARGET_TYPE != MPC563XM)*/
                default:
                    /* Set framesize values as default condition: this leads to a very small timeout*/
                    TIMING_SetTimeout((uint32_T)(size), &CfgSpiChHandler[channel]);
                break;
            }
        break;
#endif /*SPI_CH_C_EN*/

#if SPI_CH_D_EN
        case (SPI_CH_D):
            switch (chipSelect)
            {
#if PCSD0_ENABLE
                case (PCS_0):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_D0_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSD1_ENABLE
                case (PCS_1):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_D1_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSD2_ENABLE
                case (PCS_2):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_D2_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSD3_ENABLE
                case (PCS_3):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_D3_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSD4_ENABLE
                case (PCS_4):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_D4_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSD5_ENABLE
                case (PCS_5):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_D5_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if (TARGET_TYPE != MPC563XM) /*CS6 and CS7 not in this target*/
#if PCSD6_ENABLE
                case (PCS_6):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_D6_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSD7_ENABLE
                case (PCS_7):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_D7_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#endif /*(TARGET_TYPE != MPC563XM)*/
                default:
                    /* Set framesize values as default condition: this leads to a very small timeout*/
                    TIMING_SetTimeout((uint32_T)(size), &CfgSpiChHandler[channel]);
                break;
            }
        break;
#endif /*SPI_CH_D_EN*/

#if SPI_CH_E_EN
        case (SPI_CH_E):
            switch (chipSelect)
            {
#if PCSE0_ENABLE
                case (PCS_0):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_E0_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSE1_ENABLE
                case (PCS_1):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_E1_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSE2_ENABLE
                case (PCS_2):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_E2_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSE3_ENABLE
                case (PCS_3):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_E3_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSE4_ENABLE
                case (PCS_4):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_E4_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSE5_ENABLE
                case (PCS_5):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_E5_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if (TARGET_TYPE != MPC563XM) /*CS6 and CS7 not in this target*/
#if PCSD6_ENABLE
                case (PCS_6):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_E6_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#if PCSD7_ENABLE
                case (PCS_7):
                    TIMING_SetTimeout((uint32_T)(SPI_CH_E7_TIMEOUT * size), &CfgSpiChHandler[channel]);
                break;
#endif
#endif /*(TARGET_TYPE != MPC563XM)*/
                default:
                    /* Set framesize values as default condition: this leads to a very small timeout*/
                    TIMING_SetTimeout((uint32_T)(size), &CfgSpiChHandler[channel]);
                break;
            }
        break;
#endif /*SPI_CH_D_EN*/


    default:
        break;
    }

    return NO_ERROR;
}


/******************************************************************************
**   Function    : SPI_VectorInitA
**
**   Description:
**    Initializes SPI exception vector table for Channel A.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Vector table correctly configured.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T SPI_VectorInitA(void)
{
#if SPI_CH_A_EN
    uint8_T i;
    for (i = 0u; i < sizeof(SPIExTxDoneChAIrqVect); i++)
    {
        SPIExTxDoneChAIrqVect[i] = 0u;
    }
#endif

    return NO_ERROR;
}

/******************************************************************************
**   Function    : SPI_VectorInitB
**
**   Description:
**    Initializes SPI exception vector table for Channel B.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Vector table correctly configured.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T SPI_VectorInitB(void)
{
#if SPI_CH_B_EN
    uint8_T i;
    for (i = 0u; i < sizeof(SPIExTxDoneChBIrqVect); i++)
    {
        SPIExTxDoneChBIrqVect[i] = 0u;
    }
#endif

    return NO_ERROR;
}

/******************************************************************************
**   Function    : SPI_VectorInitC
**
**   Description:
**    Initializes SPI exception vector table for Channel C.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Vector table correctly configured.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T SPI_VectorInitC(void)
{
#if SPI_CH_C_EN
    uint8_T i;
    for (i = 0u; i < sizeof(SPIExTxDoneChCIrqVect); i++)
    {
        SPIExTxDoneChCIrqVect[i] = 0u;
    }
#endif

    return NO_ERROR;
}

/******************************************************************************
**   Function    : SPI_VectorInitD
**
**   Description:
**    Initializes SPI exception vector table for Channel D.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Vector table correctly configured.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T SPI_VectorInitD(void)
{
#if SPI_CH_D_EN
    uint8_T i;
    for (i = 0u; i < sizeof(SPIExTxDoneChDIrqVect); i++)
    {
        SPIExTxDoneChDIrqVect[i] = 0u;
    }
#endif

    return NO_ERROR;
}

/******************************************************************************
**   Function    : SPI_VectorInitE
**
**   Description:
**    Initialize SPI exception vector table for Channel E.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Vector table correctly configured.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T SPI_VectorInitE(void)
{
#if SPI_CH_E_EN
    uint8_T i;
    for (i = 0u; i < sizeof(SPIExTxDoneChEIrqVect); i++)
    {
        SPIExTxDoneChEIrqVect[i] = 0u;
    }
#endif

    return NO_ERROR;
}

#else /*_BUILD_DSPI_*/

#include "OS_Resources.h"

/******************************************************************************
**   Function    : SPIRes_Init
**
**   Description:
**    Initialization of SPI resource status.
**    (Stub function to make the project compile)
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPIRes_Init(void)
{
    /* Non fare niente. */
}

/******************************************************************************
**   Function    : SPIRes_SetFlag
**
**   Description:
**    Sets status of SPI channel as SPI_BUSY.
**    (Stub function to make the project compile)
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SPIRes_SetFlag(uint8_T channel)
{
    /* Non fare niente. */    
}

/******************************************************************************
**   Function    : ResetSPIResFlag
**
**   Description:
**    Sets status of SPI channel as SPI_FREE.
**    (Stub function to make the project compile)
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void ResetSPIResFlag(uint8_T channel)
{
    /* Non fare niente. */     
}

/******************************************************************************
**   Function    : GetSPIResFlag
**
**   Description:
**    Returns actual status for SPI channel.
**    (Stub function to make the project compile)
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**
**   Returns:
**    SPI_FREE   - SPI free.
**    SPI_BUSY   - SPI busy.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint8_T GetSPIResFlag(uint8_T channel)
{
    return PERIPHERAL_NOT_PRESENT;
}

/******************************************************************************
**   Function    : SPIRes_GetFromChannel
**
**   Description:
**    Gets Resource ID from channel you need to protect from concurrent access.
**    (Stub function to make the project compile)
**
**   Parameters :
**    [in] uint8_T channel : SPI channel
**    [out] ResourceType* resource : Pointer to resource ID
**
**   Returns:
**    0  - No error
**    -1 - Error.
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T SPIRes_GetFromChannel(uint8_T channel, ResourceType *resource)
{
    return -1;
}

#endif

/****************************************************************************
 ****************************************************************************/
