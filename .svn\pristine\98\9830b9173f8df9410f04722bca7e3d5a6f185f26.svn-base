/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef DIGIN_OUT_H
#define DIGIN_OUT_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"

/*!
\defgroup PublicDefines Public Defines
\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

///This is a debounce of Key 
#define TH_DEB_KEYSIGNAL        4u

#define BANK_DX (0u)
#define BANK_SX (1u)

/*!\egroup*/
/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
 
///This is a public typedef 


/*!\egroup*/

/*!
\defgroup PublicInline Public Inline Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC INLINE FUNCTIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   Inline function name
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this inline function 
*/
/**************************************************************************/

/*!\egroup*/


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_T  KeySignal;
extern uint8_T  KeyEnDiagSignal;
extern uint8_T  FlgBankSel;
extern uint8_T  EngTypeRun;
extern uint8_T EngTypeStored;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
void DigIn_T10ms(void);
void DigIn_Initialize(void);
void DigIn_Update_EngType(void);


#endif

/****************************************************************************
 ****************************************************************************/


