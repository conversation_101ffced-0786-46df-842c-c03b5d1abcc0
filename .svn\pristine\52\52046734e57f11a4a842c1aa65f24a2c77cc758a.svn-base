/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  Flashinit.c
**  Created on      :  22-giu-2020 10:00:00
**  Original author :  CarboniM
**  Integration history: 
**  -  Standard software driver for C55 Flash from "STM - SPC57xKLS-FLASH Driver for C55: Package version 0.2.0"
**          
*******************************************************************************************************************/

#ifdef  _BUILD_FLASH_

#pragma ghs startnomisra

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/

#include "ssd_c55.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* C90LC Driver v1.0.1 */
static const unsigned short FlashInit_C[] = 
{
#if (TARGET_TYPE == SPC574K2_CUT24) || (TARGET_TYPE == SPC574K2)     /* Total Size = 117 half words */

    0x0080, 0x1821, 0x06F0, 0xD3F1, 0xD501, 0x0136, 0xC076, 
    0x2077, 0xC057, 0x480F, 0x30E6, 0x0040, 0x2A07, 0xE60F, 
    0xC046, 0xC076, 0xC077, 0x6507, 0x6517, 0x6527, 0xD074, 
    0xC046, 0x4807, 0xD074, 0xC046, 0x4807, 0xD074, 0xE80B, 
    0xC076, 0x1887, 0x8004, 0x4807, 0xD074, 0xC076, 0x1887, 
    0x8004, 0x4807, 0xD074, 0x4807, 0xD276, 0x18A7, 0xC007, 
    0x6C17, 0xD376, 0x18A7, 0xC018, 0x6837, 0x6C17, 0xD476, 
    0x18A7, 0xC0E0, 0x6857, 0x6C17, 0xD576, 0x4807, 0xD676,
    0x18A7, 0xC107, 0x6887, 0x6C17, 0xD776, 0x18A7, 0xC118,
    0x68B7, 0x6C17, 0xD876, 0x18A7, 0xC1E0, 0x68D7, 0x6C17,
    0xD976, 0x18A7, 0xC360, 0x69D7, 0x6C17, 0xDA76, 0x18A7, 
    0xC207, 0x6907, 0x6C17, 0xDB76, 0x18A7, 0xC218, 0x6937, 
    0x6C17, 0xDC76, 0x18A7, 0xC2E0, 0x6957, 0x6C17, 0xDD76, 
    0x18A7, 0xC31F, 0x6987, 0xDE76, 0x30E6, 0x0048, 0x2A17, 
    0xE208, 0x7FE3, 0xFB78, 0x1800, 0xD000, 0x0002, 0x1800, 
    0xD000, 0x01F7, 0x0173, 0xC3F1, 0xC501, 0x20F1, 0x0090, 
    0x0004, 0x3038, 0x3030, 0x3146, 0x4646

#endif
};

void FlashFunctionLoader(unsigned short *functionBuffer, uint32_T functionSize);


static unsigned short FlashFunctionBuffer[500];
void * FlashFunctionPointer = (void*)FlashFunctionBuffer ; 
/***************************************************************************/
//   Function    :   FlashFunctionLoader
//
//   Description:    
/*! \brief Function for driver FLASH execution
 */
//
//  Parameters and Returns:
/*! 
\param functionBuffer  
\param functionSize   
\returns  void
 */
//  Notes:        
/*!

 */
/**************************************************************************/

#if (FLASH_DRIVER_EXECUTE_FROM_RAM == 1)
void FlashFunctionLoader(unsigned short *functionBuffer, uint32_T functionSize)
{
    uint16_T i;
    unsigned short * src = functionBuffer;
    unsigned short * dest = FlashFunctionBuffer;

    for(i=0;i<functionSize;i++)
    {
        *dest = *src;
        dest++;
        src++;
    }
}
#else
void FlashFunctionLoader(unsigned short *functionBuffer, uint32_T functionSize)
{
    FlashFunctionPointer = functionBuffer;
}
#endif

uint32_T FlashInit ( PSSD_CONFIG pSSDConfig )
{
#pragma ghs nowarning 171
    FlashFunctionLoader( (unsigned short*)FlashInit_C, sizeof(FlashInit_C)/2);
    return ((PFLASHINIT)FlashFunctionPointer)(pSSDConfig);
#pragma ghs endnowarning /* warning #171-D: invalid type conversion */
}

#pragma ghs endnomisra
#endif /*  _BUILD_FLASH_ */
