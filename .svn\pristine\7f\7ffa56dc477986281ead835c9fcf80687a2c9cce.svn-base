@echo off

rem echo %CD%

echo Updating SCU KWP2000 kits for ECU...
set NAME=I1TB1D
echo %NAME%

set CALIB_NAME_FILE=Calib_%NAME%   
echo %CALIB_NAME_FILE%

set PRJ_PATH=%CD%
echo %PRJ_PATH%

set CALIB_EXPORT_BIN_PATH=%CD%\CCP_Tool\Vision\EldorECU
echo CALIB_EXPORT_BIN_PATH =  %CALIB_EXPORT_BIN_PATH%

cd %CALIB_EXPORT_BIN_PATH% 
if exist calibram.bin del calibram.bin /Q
copy calib_strategy_export\%NAME%_CALRAM.bin calibram.bin
..\calibBuilder calibram.bin 32768
if exist calib_merged.bin del calib_merged.bin /Q
copy calibram.bin Calib_I1TB1D.bin

set  UPDATE_CALIB_BIN_FILE="%CALIB_EXPORT_BIN_PATH%\Calib_I1TB1D.bin"
echo UPDATE_CALIB_BIN_FILE %UPDATE_CALIB_BIN_FILE%

echo  checking path coherency
if not exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%" echo "Dir di progetto su KWP2000 %PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME% Mancante" 
if not exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%" echo "Dir di progetto su KWP2000 %PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE% Mancante"
  
echo deleting old files and copying last calib version
if exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%\%CALIB_NAME_FILE%" del "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%\%CALIB_NAME_FILE%"
if exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%" copy %UPDATE_CALIB_BIN_FILE% "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\Calib_%NAME%"

echo CALIB : updating bin and prm
cd "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%"
if exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%" call bincrcupdate.bat

echo APPL+CALIB: merging and updating bin and prm
cd "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%"
copy  %NAME%.bin   cpy_%NAME%.bin

set  UPDATE_APPL_BIN_FILE="%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%\%NAME%.bin"
set  CPY_APPL_BIN_FILE="%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%\cpy_%NAME%.bin"

cd  "%PRJ_PATH%\KWP2000\Dianalyzer"
perl bin_append.pl -infiles %UPDATE_CALIB_BIN_FILE% %CPY_APPL_BIN_FILE% -offset 0x8000 -output %UPDATE_APPL_BIN_FILE%
del %CPY_APPL_BIN_FILE% 

cd  "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%"
if exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%" call bincrcupdate.bat

if exist %UPDATE_CALIB_BIN_FILE% del %UPDATE_CALIB_BIN_FILE%

echo SCU KWP2000 kits for ECU successfuly updated!!!


cd ..\..\..\..
set NAME=I1TB1D_EVB

set CALIB_NAME_FILE=Calib_%NAME%   

set PRJ_PATH=%CD%

if not exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%" (
pause
exit
) 
if not exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%" echo "Dir di progetto su KWP2000 %PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE% Mancante"

echo Updating SCU KWP2000 kits for MPC5634M EVB...

set CALIB_EXPORT_BIN_PATH=%CD%\CCP_Tool\Vision\EldorECU
echo CALIB_EXPORT_BIN_PATH =  %CALIB_EXPORT_BIN_PATH%

cd %CALIB_EXPORT_BIN_PATH% 
if exist calibram_EVB.bin del calibram_EVB.bin /Q
copy calib_strategy_export\%NAME%_CALRAM.bin calibram_EVB.bin 
..\calibBuilder calibram_EVB.bin 32768
if exist calib_merged_EVB.bin del calib_merged_EVB.bin /Q
copy calibram_EVB.bin Calib_I1TB1D_EVB.bin

set  UPDATE_CALIB_BIN_FILE="%CALIB_EXPORT_BIN_PATH%\Calib_I1TB1D_EVB.bin"

echo deleting old files and copying last calib version
if exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%\%CALIB_NAME_FILE%" del "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%\%CALIB_NAME_FILE%"
if exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%" copy %UPDATE_CALIB_BIN_FILE% "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\Calib_%NAME%"

echo CALIB: updating bin and prm
cd "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%"
if exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%CALIB_NAME_FILE%" call bincrcupdate.bat

echo APPL+CALIB: merging and updating bin and prm
cd "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%"
copy  %NAME%.bin   cpy_%NAME%.bin

set  UPDATE_APPL_BIN_FILE="%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%\%NAME%.bin"
set  CPY_APPL_BIN_FILE="%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%\cpy_%NAME%.bin"

cd  "%PRJ_PATH%\KWP2000\Dianalyzer"
perl bin_append.pl -infiles %UPDATE_CALIB_BIN_FILE% %CPY_APPL_BIN_FILE% -offset 0x8000 -output %UPDATE_APPL_BIN_FILE%
del %CPY_APPL_BIN_FILE% 

cd  "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%"
if exist "%PRJ_PATH%\KWP2000\Dianalyzer\CAN\%NAME%" call bincrcupdate.bat

if exist %UPDATE_CALIB_BIN_FILE% del %UPDATE_CALIB_BIN_FILE%

echo SCU KWP2000 kits for MPC5634M EVB successfuly updated!!!

cd %PRJ_PATH%
pause