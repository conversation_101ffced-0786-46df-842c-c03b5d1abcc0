/* generated by MCS-Assembler tool ASM-MCS version 0.9 */
/* Copyright (C) 2011-2016 by <PERSON>, Germany */
/* target architecture : mcs24-1 */

#ifndef MCS1_H_
#define MCS1_H_

#define OFFSET_MCS1_MEM     (   0) /* byte address offset for assembled code in array C-array 'mcs1_mem' */
#define SIZE_MCS1_MEM       (6016) /* code size in bytes of assembled code in C-array 'mcs1_mem' */

#define LABEL_MCS1_MEM_CYL5_EPWS1_PERIOD         ( 107) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS1_PERIOD' */
#define LABEL_MCS1_MEM_CYL1_WAIT_NEW_CONFIG       ( 506) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_WAIT_NEW_CONFIG' */
#define LABEL_MCS1_MEM_CYL3_ABORT_COM_OUT        ( 771) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_ABORT_COM_OUT' */
#define LABEL_MCS1_MEM_CYL3_EPWS_LAST_PULSE_END       ( 866) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_LAST_PULSE_END' */
#define LABEL_MCS1_MEM_CYL3_OUTPUT_DELAY         (  57) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_OUTPUT_DELAY' */
#define LABEL_MCS1_MEM_CYL3_EPWS_CLOSE           ( 800) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_CLOSE' */
#define LABEL_MCS1_MEM_CYL3_D2_PERDIOD           (  81) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D2_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_B_DELAY              ( 924) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_B_DELAY' */
#define LABEL_MCS1_MEM_CYL5_EPWS2_DUTY           ( 111) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS2_DUTY' */
#define LABEL_MCS1_MEM_CYL5_CHECK_EDGE           ( 911) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_CHECK_EDGE' */
#define LABEL_MCS1_MEM_CYL1_EPWS_DATA            (  12) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_DATA' */
#define LABEL_MCS1_MEM_CYL1_LOAD_PSM             ( 489) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_LOAD_PSM' */
#define LABEL_MCS1_MEM_CYL3_WAIT_BUCK_OFF_1       ( 778) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_WAIT_BUCK_OFF_1' */
#define LABEL_MCS1_MEM_CYL5_EPWS_LAST_START       (1059) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_LAST_START' */
#define LABEL_MCS1_MEM_CYL3_EPWS2_PERIOD         (  63) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS2_PERIOD' */
#define LABEL_MCS1_MEM_CYL5_INT_VAR              ( 198) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_INT_VAR' */
#define LABEL_MCS1_MEM_CYL3_EPWS_MODE            ( 789) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_MODE' */
#define LABEL_MCS1_MEM_CYL5_D8_DURATION          ( 145) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D8_DURATION' */
#define LABEL_MCS1_MEM_CYL7_EPWS_WAIT_T_OFF_MATCH       ( 456) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_WAIT_T_OFF_MATCH' */
#define LABEL_MCS1_MEM_CYL1_PMOS_DURATION        (  26) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_PMOS_DURATION' */
#define LABEL_MCS1_MEM_CYL1_D6_PERDIOD           (  46) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D6_PERDIOD' */
#define LABEL_MCS1_MEM_CYL3_EPWS_PH2_START       ( 821) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_PH2_START' */
#define LABEL_MCS1_MEM_CYL5_B_DELAY_WA           ( 920) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_B_DELAY_WA' */
#define LABEL_MCS1_MEM_CYL7_EPWS_MODE            (1190) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_MODE' */
#define LABEL_MCS1_MEM_SET_BUCK1_ON              (1277) /* Index into C-array 'mcs1_mem' for assembler label 'SET_BUCK1_ON' */
#define LABEL_MCS1_MEM_CYL1_D3_DUTY              (  38) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D3_DUTY' */
#define LABEL_MCS1_MEM_CYL3_EPWS_WAIT_CPU_TRG       ( 792) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_WAIT_CPU_TRG' */
#define LABEL_MCS1_MEM_CYL7_LOAD_PSM             (1093) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_LOAD_PSM' */
#define LABEL_MCS1_MEM_CYL5_EPWS2_N_PULSE        ( 109) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS2_N_PULSE' */
#define LABEL_MCS1_MEM_CYL3_START                ( 678) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_START' */
#define LABEL_MCS1_MEM_CYL3_D4_PERDIOD           (  87) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D4_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_D3_DUTY              ( 179) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D3_DUTY' */
#define LABEL_MCS1_MEM_CYL3_EPWS_RET             ( 417) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_RET' */
#define LABEL_MCS1_MEM_CYL3_EPWS_PH4_START       ( 845) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_PH4_START' */
#define LABEL_MCS1_MEM_CYL1_D1_DUTY              (  32) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D1_DUTY' */
#define LABEL_MCS1_MEM_CYL7_EPWS_TIMEOUT         ( 166) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_TIMEOUT' */
#define LABEL_MCS1_MEM_CYL3_D4_DURATION          (  86) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D4_DURATION' */
#define LABEL_MCS1_MEM_CYL5_EPWS4_N_PULSE        ( 115) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS4_N_PULSE' */
#define LABEL_MCS1_MEM_SETUP_CYLINDER3           (1441) /* Index into C-array 'mcs1_mem' for assembler label 'SETUP_CYLINDER3' */
#define LABEL_MCS1_MEM_CYL3_CHECK_BUCK_AVB       ( 726) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_CHECK_BUCK_AVB' */
#define LABEL_MCS1_MEM_CYL1_NMOS_DURATION        (  27) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_NMOS_DURATION' */
#define LABEL_MCS1_MEM_B1_L_LOOP                 ( 368) /* Index into C-array 'mcs1_mem' for assembler label 'B1_L_LOOP' */
#define LABEL_MCS1_MEM_CYL5_EPWS_WAIT_BUCK_OFF_1       (1006) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_WAIT_BUCK_OFF_1' */
#define LABEL_MCS1_MEM_CYL7_D5_PERDIOD           ( 184) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D5_PERDIOD' */
#define LABEL_MCS1_MEM_MCS1_PRIM_CURR            (1481) /* Index into C-array 'mcs1_mem' for assembler label 'MCS1_PRIM_CURR' */
#define LABEL_MCS1_MEM_CYL7_EPWS2_N_PULSE        ( 156) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS2_N_PULSE' */
#define LABEL_MCS1_MEM_CYL1_B_DELAY_WA           ( 518) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_B_DELAY_WA' */
#define LABEL_MCS1_MEM_DATA_CYLINDER5            ( 102) /* Index into C-array 'mcs1_mem' for assembler label 'DATA_CYLINDER5' */
#define LABEL_MCS1_MEM_CYL7_EPWS_WAIT_CPU_TRG       (1193) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_WAIT_CPU_TRG' */
#define LABEL_MCS1_MEM_CYL3_DATA_LOADED          ( 201) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_DATA_LOADED' */
#define LABEL_MCS1_MEM_CYL3_CHECK_EDGE           ( 710) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_CHECK_EDGE' */
#define LABEL_MCS1_MEM_CYL5_D7_DUTY              ( 144) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D7_DUTY' */
#define LABEL_MCS1_MEM_CYL3_STOP_OUTPUT          ( 752) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_STOP_OUTPUT' */
#define LABEL_MCS1_MEM_BUCK1_EN_DISABLE          (1413) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_EN_DISABLE' */
#define LABEL_MCS1_MEM_CYL3_D7_DURATION          (  95) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D7_DURATION' */
#define LABEL_MCS1_MEM_CYL5_D2_DUTY              ( 129) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D2_DUTY' */
#define LABEL_MCS1_MEM_CYL3_D1_DURATION          (  77) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D1_DURATION' */
#define LABEL_MCS1_MEM_CYL3_T_ON_START           ( 397) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_T_ON_START' */
#define LABEL_MCS1_MEM_CYL5_COIL_LOADING_STEP       ( 122) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_COIL_LOADING_STEP' */
#define LABEL_MCS1_MEM_CYL7_EPWS1_PERIOD         ( 154) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS1_PERIOD' */
#define LABEL_MCS1_MEM_CYL7_CYLINDER_INDEX       ( 150) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_CYLINDER_INDEX' */
#define LABEL_MCS1_MEM_CYL1_EPWS2_DUTY           (  17) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS2_DUTY' */
#define LABEL_MCS1_MEM_CYL7_T_ON_START           ( 445) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_T_ON_START' */
#define LABEL_MCS1_MEM_DATA_CYLINDER7            ( 149) /* Index into C-array 'mcs1_mem' for assembler label 'DATA_CYLINDER7' */
#define LABEL_MCS1_MEM_CYL3_EPWS_LAST_PULSE       (  71) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_LAST_PULSE' */
#define LABEL_MCS1_MEM_CYL5_WAIT_INPUT_SIGNAL       ( 903) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_WAIT_INPUT_SIGNAL' */
#define LABEL_MCS1_MEM_CYL7_EPWS_END             (1207) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_END' */
#define LABEL_MCS1_MEM_CYL1_EPWS_PH3_START       ( 632) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_PH3_START' */
#define LABEL_MCS1_MEM_CYL7_B_DELAY_WA           (1120) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_B_DELAY_WA' */
#define LABEL_MCS1_MEM_CYL3_COIL_UNLOADING_STEP       (  76) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_COIL_UNLOADING_STEP' */
#define LABEL_MCS1_MEM_CYL3_D3_PERDIOD           (  84) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D3_PERDIOD' */
#define LABEL_MCS1_MEM_CYL3_EPWS2_DUTY           (  64) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS2_DUTY' */
#define LABEL_MCS1_MEM_CYL5_OUTPUT_DELAY         ( 104) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_OUTPUT_DELAY' */
#define LABEL_MCS1_MEM_CYL7_TIMEOUT              (1177) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_TIMEOUT' */
#define LABEL_MCS1_MEM_CYL5_EPWS_TIMEOUT         ( 119) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_TIMEOUT' */
#define LABEL_MCS1_MEM_CYL5_T_OFF_STEP           ( 429) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_T_OFF_STEP' */
#define LABEL_MCS1_MEM_CYL3_DELAY                ( 738) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_DELAY' */
#define LABEL_MCS1_MEM_CYL1_EPWS_CLOSE           ( 599) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_CLOSE' */
#define LABEL_MCS1_MEM_CYL3_D4_DUTY              (  88) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D4_DUTY' */
#define LABEL_MCS1_MEM_DATA_CYLINDER3            (  55) /* Index into C-array 'mcs1_mem' for assembler label 'DATA_CYLINDER3' */
#define LABEL_MCS1_MEM_CYL3_DELAY_WA             ( 734) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_DELAY_WA' */
#define LABEL_MCS1_MEM_CYL7_D7_PERDIOD           ( 190) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D7_PERDIOD' */
#define LABEL_MCS1_MEM_CYL3_TIMEOUT              ( 776) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_TIMEOUT' */
#define LABEL_MCS1_MEM_CYL7_END_CONF_PARAM       ( 195) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_END_CONF_PARAM' */
#define LABEL_MCS1_MEM_CYL7_EPWS4_PERIOD         ( 163) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS4_PERIOD' */
#define LABEL_MCS1_MEM_CYL1_STOP_OUTPUT          ( 551) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_STOP_OUTPUT' */
#define LABEL_MCS1_MEM_CYL3_EPWS1_PERIOD         (  60) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS1_PERIOD' */
#define LABEL_MCS1_MEM_FBUCK1_LOADING            ( 356) /* Index into C-array 'mcs1_mem' for assembler label 'FBUCK1_LOADING' */
#define LABEL_MCS1_MEM_CYL5_D3_PERDIOD           ( 131) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D3_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_EPWS_CHECK           ( 959) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_CHECK' */
#define LABEL_MCS1_MEM_CYL5_WAIT_BUCK_OFF        ( 968) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_WAIT_BUCK_OFF' */
#define LABEL_MCS1_MEM_DATA_CYLINDER1            (   8) /* Index into C-array 'mcs1_mem' for assembler label 'DATA_CYLINDER1' */
#define LABEL_MCS1_MEM_CYL1_PSM_SIZE             (   8) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_PSM_SIZE' */
#define LABEL_MCS1_MEM_CYL7_D3_DURATION          ( 177) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D3_DURATION' */
#define LABEL_MCS1_MEM_CYL1_CYLINDER_INDEX       (   9) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_CYLINDER_INDEX' */
#define LABEL_MCS1_MEM_CYL7_EPWS_DATA            ( 153) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_DATA' */
#define LABEL_MCS1_MEM_CYL3_EPWS_DATA            (  59) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_DATA' */
#define LABEL_MCS1_MEM_CYL1_OUTPUT_DELAY         (  10) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_OUTPUT_DELAY' */
#define LABEL_MCS1_MEM_CYL5_EPWS_PH4_START       (1047) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_PH4_START' */
#define LABEL_MCS1_MEM_CYL7_D7_DURATION          ( 189) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D7_DURATION' */
#define LABEL_MCS1_MEM_CYL1_EPWS                 (  11) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS' */
#define LABEL_MCS1_MEM_CYL7_EPWS_LAST_START       (1258) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_LAST_START' */
#define LABEL_MCS1_MEM_CYL1_EPWS1_N_PULSE        (  12) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS1_N_PULSE' */
#define LABEL_MCS1_MEM_CYL1_WAIT_INPUT_SIGNAL       ( 501) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_WAIT_INPUT_SIGNAL' */
#define LABEL_MCS1_MEM_CYL1_EPWS1_PERIOD         (  13) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS1_PERIOD' */
#define LABEL_MCS1_MEM_CYL5_CHECK_BUCK_AVB       ( 928) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_CHECK_BUCK_AVB' */
#define LABEL_MCS1_MEM_CYL1_EPWS1_DUTY           (  14) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS1_DUTY' */
#define LABEL_MCS1_MEM_CYL1_EPWS2_N_PULSE        (  15) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS2_N_PULSE' */
#define LABEL_MCS1_MEM_BUCK1_WAIT_INPUT_CLOSE       (1382) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_WAIT_INPUT_CLOSE' */
#define LABEL_MCS1_MEM_CYL1_COMM_OUTPUT_DELAY       ( 529) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_COMM_OUTPUT_DELAY' */
#define LABEL_MCS1_MEM_CYL1_EPWS2_PERIOD         (  16) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS2_PERIOD' */
#define LABEL_MCS1_MEM_CYL1_EPWS3_N_PULSE        (  18) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS3_N_PULSE' */
#define LABEL_MCS1_MEM_CYL1_PSM_SETUP            ( 482) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_PSM_SETUP' */
#define LABEL_MCS1_MEM_CYL3_BUCK_DELAY           ( 714) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_BUCK_DELAY' */
#define LABEL_MCS1_MEM_CYL3_LOAD_PSM             ( 691) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_LOAD_PSM' */
#define LABEL_MCS1_MEM_CYL7_START                (1081) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_START' */
#define LABEL_MCS1_MEM_CYL1_EPWS3_PERIOD         (  19) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS3_PERIOD' */
#define LABEL_MCS1_MEM_CYL1_EPWS3_DUTY           (  20) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS3_DUTY' */
#define LABEL_MCS1_MEM_CYL1_EPWS4_N_PULSE        (  21) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS4_N_PULSE' */
#define LABEL_MCS1_MEM_CYL7_PSM_SIZE             ( 149) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_PSM_SIZE' */
#define LABEL_MCS1_MEM_CYL1_EPWS4_PERIOD         (  22) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS4_PERIOD' */
#define LABEL_MCS1_MEM_CYL1_EPWS4_DUTY           (  23) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS4_DUTY' */
#define LABEL_MCS1_MEM_CYL1_EPWS_LAST_PULSE       (  24) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_LAST_PULSE' */
#define LABEL_MCS1_MEM_CYL5_D2_DURATION          ( 127) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D2_DURATION' */
#define LABEL_MCS1_MEM_CYL1_EPWS_TIMEOUT         (  25) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_TIMEOUT' */
#define LABEL_MCS1_MEM_CYL1_COIL_LOADING_STEP       (  28) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_COIL_LOADING_STEP' */
#define LABEL_MCS1_MEM_TRIGGER_BUCK1_EN          (1357) /* Index into C-array 'mcs1_mem' for assembler label 'TRIGGER_BUCK1_EN' */
#define LABEL_MCS1_MEM_CYL1_COIL_UNLOADING_STEP       (  29) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_COIL_UNLOADING_STEP' */
#define LABEL_MCS1_MEM_CYL1_D1_DURATION          (  30) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D1_DURATION' */
#define LABEL_MCS1_MEM_CYL1_D1_PERDIOD           (  31) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D1_PERDIOD' */
#define LABEL_MCS1_MEM_CYL1_EPWS_WAIT_BUCK_OFF_1       ( 603) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_WAIT_BUCK_OFF_1' */
#define LABEL_MCS1_MEM_BUCK1_STEPS               (1358) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_STEPS' */
#define LABEL_MCS1_MEM_CYL1_COIL_SETTINGS        (  33) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_COIL_SETTINGS' */
#define LABEL_MCS1_MEM_CYL1_DONE                 ( 585) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_DONE' */
#define LABEL_MCS1_MEM_SETUP_CYLINDER5           (1451) /* Index into C-array 'mcs1_mem' for assembler label 'SETUP_CYLINDER5' */
#define LABEL_MCS1_MEM_CYL1_D2_DURATION          (  33) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D2_DURATION' */
#define LABEL_MCS1_MEM_CYL1_D2_PERDIOD           (  34) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D2_PERDIOD' */
#define LABEL_MCS1_MEM_CYL1_D2_DUTY              (  35) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D2_DUTY' */
#define LABEL_MCS1_MEM_CYL1_D3_DURATION          (  36) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D3_DURATION' */
#define LABEL_MCS1_MEM_CYL1_D3_PERDIOD           (  37) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D3_PERDIOD' */
#define LABEL_MCS1_MEM_CYL1_D4_DURATION          (  39) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D4_DURATION' */
#define LABEL_MCS1_MEM_CYL1_EPWS_LAST_PULSE_END       ( 665) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_LAST_PULSE_END' */
#define LABEL_MCS1_MEM_CYL1_D4_PERDIOD           (  40) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D4_PERDIOD' */
#define LABEL_MCS1_MEM_CYL1_D4_DUTY              (  41) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D4_DUTY' */
#define LABEL_MCS1_MEM_CYL1_D5_DURATION          (  42) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D5_DURATION' */
#define LABEL_MCS1_MEM_CYL3_B_DELAY              ( 722) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_B_DELAY' */
#define LABEL_MCS1_MEM_CYL1_D5_PERDIOD           (  43) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D5_PERDIOD' */
#define LABEL_MCS1_MEM_CYL1_D5_DUTY              (  44) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D5_DUTY' */
#define LABEL_MCS1_MEM_CYL5_D8_DUTY              ( 147) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D8_DUTY' */
#define LABEL_MCS1_MEM_CYL1_D6_DURATION          (  45) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D6_DURATION' */
#define LABEL_MCS1_MEM_CYL7_WAIT_NEW_CONFIG       (1108) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_WAIT_NEW_CONFIG' */
#define LABEL_MCS1_MEM_CYL1_D6_DUTY              (  47) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D6_DUTY' */
#define LABEL_MCS1_MEM_CYL7_EPWS4_DUTY           ( 164) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS4_DUTY' */
#define LABEL_MCS1_MEM_CYL1_D7_DURATION          (  48) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D7_DURATION' */
#define LABEL_MCS1_MEM_CYL5_BUCK_DELAY           ( 915) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_BUCK_DELAY' */
#define LABEL_MCS1_MEM_CYL1_D7_PERDIOD           (  49) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D7_PERDIOD' */
#define LABEL_MCS1_MEM_CYL1_D7_DUTY              (  50) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D7_DUTY' */
#define LABEL_MCS1_MEM_CYL3_CYLINDER_INDEX       (  56) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_CYLINDER_INDEX' */
#define LABEL_MCS1_MEM_CYL1_D8_DURATION          (  51) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D8_DURATION' */
#define LABEL_MCS1_MEM_CYL1_D8_PERDIOD           (  52) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D8_PERDIOD' */
#define LABEL_MCS1_MEM_CYL1_D8_DUTY              (  53) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_D8_DUTY' */
#define LABEL_MCS1_MEM_CYL7_EPWS_TIME_OUT        (1198) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_TIME_OUT' */
#define LABEL_MCS1_MEM_CYL1_END_CONF_PARAM       (  54) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_END_CONF_PARAM' */
#define LABEL_MCS1_MEM_CYL7_WAIT_BUCK_OFF_1       (1179) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_WAIT_BUCK_OFF_1' */
#define LABEL_MCS1_MEM_CYL3_PSM_SIZE             (  55) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_PSM_SIZE' */
#define LABEL_MCS1_MEM_BUCK1_TOP_LOOP_WA         (1390) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_TOP_LOOP_WA' */
#define LABEL_MCS1_MEM_CYL3_EPWS                 (  58) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS' */
#define LABEL_MCS1_MEM_CYL3_EPWS_TIME_OUT        ( 797) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_TIME_OUT' */
#define LABEL_MCS1_MEM_CYL3_EPWS1_N_PULSE        (  59) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS1_N_PULSE' */
#define LABEL_MCS1_MEM_CYL3_EPWS1_DUTY           (  61) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS1_DUTY' */
#define LABEL_MCS1_MEM_BUCK1_UNLOADING           (1398) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_UNLOADING' */
#define LABEL_MCS1_MEM_CYL5_EPWS_TIME_OUT_RET       ( 443) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_TIME_OUT_RET' */
#define LABEL_MCS1_MEM_CYL3_EPWS2_N_PULSE        (  62) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS2_N_PULSE' */
#define LABEL_MCS1_MEM_CYL3_EPWS3_N_PULSE        (  65) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS3_N_PULSE' */
#define LABEL_MCS1_MEM_CYL3_EPWS3_PERIOD         (  66) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS3_PERIOD' */
#define LABEL_MCS1_MEM_CYL5_EPWS4_PERIOD         ( 116) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS4_PERIOD' */
#define LABEL_MCS1_MEM_CYL3_EPWS3_DUTY           (  67) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS3_DUTY' */
#define LABEL_MCS1_MEM_CYL3_EPWS4_N_PULSE        (  68) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS4_N_PULSE' */
#define LABEL_MCS1_MEM_CYL7_PSM_SETUP            (1086) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_PSM_SETUP' */
#define LABEL_MCS1_MEM_CYL3_EPWS4_PERIOD         (  69) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS4_PERIOD' */
#define LABEL_MCS1_MEM_CYL5_COIL_SETTINGS        ( 127) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_COIL_SETTINGS' */
#define LABEL_MCS1_MEM_CYL3_EPWS4_DUTY           (  70) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS4_DUTY' */
#define LABEL_MCS1_MEM_CYL3_EPWS_FUNC            ( 397) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_FUNC' */
#define LABEL_MCS1_MEM_CYL3_EPWS_TIMEOUT         (  72) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_TIMEOUT' */
#define LABEL_MCS1_MEM_CYL5_RESTART              ( 970) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_RESTART' */
#define LABEL_MCS1_MEM_CYL3_PMOS_DURATION        (  73) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_PMOS_DURATION' */
#define LABEL_MCS1_MEM_CYL3_NMOS_DURATION        (  74) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_NMOS_DURATION' */
#define LABEL_MCS1_MEM_CYL3_COIL_LOADING_STEP       (  75) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_COIL_LOADING_STEP' */
#define LABEL_MCS1_MEM_CYL3_D1_PERDIOD           (  78) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D1_PERDIOD' */
#define LABEL_MCS1_MEM_CYL3_D1_DUTY              (  79) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D1_DUTY' */
#define LABEL_MCS1_MEM_CYL3_COIL_SETTINGS        (  80) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_COIL_SETTINGS' */
#define LABEL_MCS1_MEM_CYL5_EPWS_WAIT_CPU_TRG       ( 994) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_WAIT_CPU_TRG' */
#define LABEL_MCS1_MEM_BUCK1_INIT                (1320) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_INIT' */
#define LABEL_MCS1_MEM_CYL3_D2_DURATION          (  80) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D2_DURATION' */
#define LABEL_MCS1_MEM_CYL3_D2_DUTY              (  82) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D2_DUTY' */
#define LABEL_MCS1_MEM_CYL3_D3_DURATION          (  83) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D3_DURATION' */
#define LABEL_MCS1_MEM_CYL3_D3_DUTY              (  85) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D3_DUTY' */
#define LABEL_MCS1_MEM_CYL5_D4_DUTY              ( 135) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D4_DUTY' */
#define LABEL_MCS1_MEM_CYL3_D5_DURATION          (  89) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D5_DURATION' */
#define LABEL_MCS1_MEM_CYL3_D5_PERDIOD           (  90) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D5_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_EPWS3_PERIOD         ( 113) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS3_PERIOD' */
#define LABEL_MCS1_MEM_CYL3_D5_DUTY              (  91) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D5_DUTY' */
#define LABEL_MCS1_MEM_CYL5_EPWS_LOOP            ( 437) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_LOOP' */
#define LABEL_MCS1_MEM_CYL1_DELAY                ( 538) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_DELAY' */
#define LABEL_MCS1_MEM_CYL3_D6_DURATION          (  92) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D6_DURATION' */
#define LABEL_MCS1_MEM_CYL7_EPWS_LAST_LOOP       (1262) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_LAST_LOOP' */
#define LABEL_MCS1_MEM_CYL3_D6_PERDIOD           (  93) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D6_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_D1_DURATION          ( 171) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D1_DURATION' */
#define LABEL_MCS1_MEM_BUCK_B1_INIT              (1332) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK_B1_INIT' */
#define LABEL_MCS1_MEM_CYL1_WAIT_BUCK_OFF        ( 565) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_WAIT_BUCK_OFF' */
#define LABEL_MCS1_MEM_CYL3_D6_DUTY              (  94) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D6_DUTY' */
#define LABEL_MCS1_MEM_CYL7_ABORT_COM_OUT        (1172) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_ABORT_COM_OUT' */
#define LABEL_MCS1_MEM_CYL3_D7_PERDIOD           (  96) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D7_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_DELAY                ( 940) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_DELAY' */
#define LABEL_MCS1_MEM_CYL5_EPWS_STOP_IPRI       (1002) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_STOP_IPRI' */
#define LABEL_MCS1_MEM_CYL3_D7_DUTY              (  97) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D7_DUTY' */
#define LABEL_MCS1_MEM_CYL3_D8_DURATION          (  98) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D8_DURATION' */
#define LABEL_MCS1_MEM_CYL3_D8_PERDIOD           (  99) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D8_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_D8_PERDIOD           ( 193) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D8_PERDIOD' */
#define LABEL_MCS1_MEM_CYL3_D8_DUTY              ( 100) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_D8_DUTY' */
#define LABEL_MCS1_MEM_CYL3_END_CONF_PARAM       ( 101) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_END_CONF_PARAM' */
#define LABEL_MCS1_MEM_CYL5_PSM_SIZE             ( 102) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_PSM_SIZE' */
#define LABEL_MCS1_MEM_CYL7_EPWS3_N_PULSE        ( 159) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS3_N_PULSE' */
#define LABEL_MCS1_MEM_CYL5_CYLINDER_INDEX       ( 103) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_CYLINDER_INDEX' */
#define LABEL_MCS1_MEM_CYL5_EPWS                 ( 105) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS' */
#define LABEL_MCS1_MEM_CYL7_DELAY                (1140) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_DELAY' */
#define LABEL_MCS1_MEM_CYL5_EPWS_DATA            ( 106) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_DATA' */
#define LABEL_MCS1_MEM_CYL7_INPUT_CLOSE          (1161) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_INPUT_CLOSE' */
#define LABEL_MCS1_MEM_CYL5_EPWS1_N_PULSE        ( 106) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS1_N_PULSE' */
#define LABEL_MCS1_MEM_BUCK1_EN_START            (1276) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_EN_START' */
#define LABEL_MCS1_MEM_CYL5_EPWS1_DUTY           ( 108) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS1_DUTY' */
#define LABEL_MCS1_MEM_CYL5_EPWS2_PERIOD         ( 110) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS2_PERIOD' */
#define LABEL_MCS1_MEM_CYL7_COIL_LOADING_STEP       ( 169) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_COIL_LOADING_STEP' */
#define LABEL_MCS1_MEM_CYL5_EPWS3_N_PULSE        ( 112) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS3_N_PULSE' */
#define LABEL_MCS1_MEM_CYL5_EPWS3_DUTY           ( 114) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS3_DUTY' */
#define LABEL_MCS1_MEM_BUCK1_WAIT_INPUT_START_SIGNAL       (1340) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_WAIT_INPUT_START_SIGNAL' */
#define LABEL_MCS1_MEM_CYL5_D7_PERDIOD           ( 143) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D7_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_EPWS4_DUTY           ( 117) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS4_DUTY' */
#define LABEL_MCS1_MEM_CYL5_EPWS_LAST_PULSE       ( 118) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_LAST_PULSE' */
#define LABEL_MCS1_MEM_CYL5_PMOS_DURATION        ( 120) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_PMOS_DURATION' */
#define LABEL_MCS1_MEM_BUCK1_EN_WAIT_CLOSURE       (1311) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_EN_WAIT_CLOSURE' */
#define LABEL_MCS1_MEM_CYL5_NMOS_DURATION        ( 121) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_NMOS_DURATION' */
#define LABEL_MCS1_MEM_CYL5_COIL_UNLOADING_STEP       ( 123) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_COIL_UNLOADING_STEP' */
#define LABEL_MCS1_MEM_CYL5_D1_DURATION          ( 124) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D1_DURATION' */
#define LABEL_MCS1_MEM_CYL5_D1_PERDIOD           ( 125) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D1_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_EPWS_WAIT_T_ON_MATCH       ( 424) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_WAIT_T_ON_MATCH' */
#define LABEL_MCS1_MEM_CYL5_D1_DUTY              ( 126) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D1_DUTY' */
#define LABEL_MCS1_MEM_CYL5_D2_PERDIOD           ( 128) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D2_PERDIOD' */
#define LABEL_MCS1_MEM_CYL3_EPWS_LAST_START       ( 857) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_LAST_START' */
#define LABEL_MCS1_MEM_CYL5_D3_DURATION          ( 130) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D3_DURATION' */
#define LABEL_MCS1_MEM_CYL5_D3_DUTY              ( 132) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D3_DUTY' */
#define LABEL_MCS1_MEM_CYL5_D4_DURATION          ( 133) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D4_DURATION' */
#define LABEL_MCS1_MEM_CYL5_D4_PERDIOD           ( 134) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D4_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_D5_DURATION          ( 136) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D5_DURATION' */
#define LABEL_MCS1_MEM_CYL5_D5_PERDIOD           ( 137) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D5_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_D5_DUTY              ( 138) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D5_DUTY' */
#define LABEL_MCS1_MEM_CYL5_D6_DURATION          ( 139) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D6_DURATION' */
#define LABEL_MCS1_MEM_CYL7_EPWS_TIME_OUT_RET       ( 467) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_TIME_OUT_RET' */
#define LABEL_MCS1_MEM_CYL5_D6_PERDIOD           ( 140) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D6_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_DELAY_WA             (1136) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_DELAY_WA' */
#define LABEL_MCS1_MEM_CYL3_EPWS_START           ( 807) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_START' */
#define LABEL_MCS1_MEM_CYL5_D6_DUTY              ( 141) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D6_DUTY' */
#define LABEL_MCS1_MEM_CYL5_D7_DURATION          ( 142) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D7_DURATION' */
#define LABEL_MCS1_MEM_CYL5_D8_PERDIOD           ( 146) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_D8_PERDIOD' */
#define LABEL_MCS1_MEM_CYL5_END_CONF_PARAM       ( 148) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_END_CONF_PARAM' */
#define LABEL_MCS1_MEM_CYL7_D5_DURATION          ( 183) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D5_DURATION' */
#define LABEL_MCS1_MEM_CYL3_EPWS_WAIT_T_OFF_MATCH       ( 408) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_WAIT_T_OFF_MATCH' */
#define LABEL_MCS1_MEM_CYL7_OUTPUT_DELAY         ( 151) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_OUTPUT_DELAY' */
#define LABEL_MCS1_MEM_CYL5_INIT                 ( 871) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_INIT' */
#define LABEL_MCS1_MEM_CYL7_EPWS                 ( 152) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS' */
#define LABEL_MCS1_MEM_CYL7_EPWS_LAST_PULSE_END       (1267) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_LAST_PULSE_END' */
#define LABEL_MCS1_MEM_CYL1_BUCK_DELAY           ( 513) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_BUCK_DELAY' */
#define LABEL_MCS1_MEM_CYL7_EPWS1_N_PULSE        ( 153) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS1_N_PULSE' */
#define LABEL_MCS1_MEM_CYL7_D4_DUTY              ( 182) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D4_DUTY' */
#define LABEL_MCS1_MEM_CYL7_EPWS1_DUTY           ( 155) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS1_DUTY' */
#define LABEL_MCS1_MEM_CYL3_EPWS_CHECK           ( 757) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_CHECK' */
#define LABEL_MCS1_MEM_CYL7_EPWS2_PERIOD         ( 157) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS2_PERIOD' */
#define LABEL_MCS1_MEM_CYL1_CHECK_EDGE           ( 509) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_CHECK_EDGE' */
#define LABEL_MCS1_MEM_CYL1_EPWS_MODE            ( 588) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_MODE' */
#define LABEL_MCS1_MEM_CYL7_EPWS2_DUTY           ( 158) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS2_DUTY' */
#define LABEL_MCS1_MEM_CYL1_EPWS_WAIT_T_ON_MATCH       ( 376) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_WAIT_T_ON_MATCH' */
#define LABEL_MCS1_MEM_CYL7_EPWS3_PERIOD         ( 160) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS3_PERIOD' */
#define LABEL_MCS1_MEM_CYL7_EPWS3_DUTY           ( 161) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS3_DUTY' */
#define LABEL_MCS1_MEM_CYL1_ABORT_COM_OUT        ( 570) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_ABORT_COM_OUT' */
#define LABEL_MCS1_MEM_CYL7_EPWS4_N_PULSE        ( 162) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS4_N_PULSE' */
#define LABEL_MCS1_MEM_CYL7_EPWS_LAST_PULSE       ( 165) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_LAST_PULSE' */
#define LABEL_MCS1_MEM_CYL7_EPWS_PH2_START       (1222) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_PH2_START' */
#define LABEL_MCS1_MEM_CYL7_PMOS_DURATION        ( 167) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_PMOS_DURATION' */
#define LABEL_MCS1_MEM_CYL7_NMOS_DURATION        ( 168) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_NMOS_DURATION' */
#define LABEL_MCS1_MEM_BUCK1_CURRENT_CYLINDER       ( 207) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_CURRENT_CYLINDER' */
#define LABEL_MCS1_MEM_CYL7_COIL_UNLOADING_STEP       ( 170) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_COIL_UNLOADING_STEP' */
#define LABEL_MCS1_MEM_CYL7_D1_PERDIOD           ( 172) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D1_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_D1_DUTY              ( 173) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D1_DUTY' */
#define LABEL_MCS1_MEM_CYL7_COIL_SETTINGS        ( 174) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_COIL_SETTINGS' */
#define LABEL_MCS1_MEM_CYL5_WAIT_NEW_CONFIG       ( 908) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_WAIT_NEW_CONFIG' */
#define LABEL_MCS1_MEM_CYL7_D2_DURATION          ( 174) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D2_DURATION' */
#define LABEL_MCS1_MEM_BUCK1_EN_STACK            ( 309) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_EN_STACK' */
#define LABEL_MCS1_MEM_CYL7_D2_PERDIOD           ( 175) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D2_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_D2_DUTY              ( 176) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D2_DUTY' */
#define LABEL_MCS1_MEM_CYL5_STACK                ( 245) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_STACK' */
#define LABEL_MCS1_MEM_CYL7_D3_PERDIOD           ( 178) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D3_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_D4_DURATION          ( 180) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D4_DURATION' */
#define LABEL_MCS1_MEM_CYL7_D4_PERDIOD           ( 181) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D4_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_EPWS_PH1_START       (1210) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_PH1_START' */
#define LABEL_MCS1_MEM_CYL7_D5_DUTY              ( 185) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D5_DUTY' */
#define LABEL_MCS1_MEM_CYL7_D6_DURATION          ( 186) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D6_DURATION' */
#define LABEL_MCS1_MEM_CYL7_D6_PERDIOD           ( 187) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D6_PERDIOD' */
#define LABEL_MCS1_MEM_CYL7_COMM_OUTPUT_DELAY       (1131) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_COMM_OUTPUT_DELAY' */
#define LABEL_MCS1_MEM_CYL7_D6_DUTY              ( 188) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D6_DUTY' */
#define LABEL_MCS1_MEM_CYL7_D7_DUTY              ( 191) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D7_DUTY' */
#define LABEL_MCS1_MEM_CYL7_D8_DURATION          ( 192) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D8_DURATION' */
#define LABEL_MCS1_MEM_CYL7_D8_DUTY              ( 194) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_D8_DUTY' */
#define LABEL_MCS1_MEM_CYL7_DATA_LOADED          ( 203) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_DATA_LOADED' */
#define LABEL_MCS1_MEM_CYL1_INT_VAR              ( 196) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_INT_VAR' */
#define LABEL_MCS1_MEM_SETUP_CYLINDER7           (1461) /* Index into C-array 'mcs1_mem' for assembler label 'SETUP_CYLINDER7' */
#define LABEL_MCS1_MEM_CYL3_INT_VAR              ( 197) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_INT_VAR' */
#define LABEL_MCS1_MEM_CYL7_INT_VAR              ( 199) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_INT_VAR' */
#define LABEL_MCS1_MEM_CYL1_DATA_LOADED          ( 200) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_DATA_LOADED' */
#define LABEL_MCS1_MEM_SETUP_CYLINDER1           (1431) /* Index into C-array 'mcs1_mem' for assembler label 'SETUP_CYLINDER1' */
#define LABEL_MCS1_MEM_CYL5_DATA_LOADED          ( 202) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_DATA_LOADED' */
#define LABEL_MCS1_MEM_BUCK1_IPRI_ISEC_CURRENT_CYLINDER       ( 204) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_IPRI_ISEC_CURRENT_CYLINDER' */
#define LABEL_MCS1_MEM_BUCK1_REF_MEM_ACK         ( 205) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_REF_MEM_ACK' */
#define LABEL_MCS1_MEM_BUCK1_STEP_ISR_VAR        ( 206) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_STEP_ISR_VAR' */
#define LABEL_MCS1_MEM_CYL5_COMM_OUTPUT_DELAY       ( 931) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_COMM_OUTPUT_DELAY' */
#define LABEL_MCS1_MEM_BUCK1_CURRENT_PTR_DATA       ( 208) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_CURRENT_PTR_DATA' */
#define LABEL_MCS1_MEM_BUCK1_CURRENT_LOADING_STEP       ( 209) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_CURRENT_LOADING_STEP' */
#define LABEL_MCS1_MEM_BUCK1_CURRENT_UNLOADING_STEP       ( 210) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_CURRENT_UNLOADING_STEP' */
#define LABEL_MCS1_MEM_CYLINDER3                 (1296) /* Index into C-array 'mcs1_mem' for assembler label 'CYLINDER3' */
#define LABEL_MCS1_MEM_BUCK1_PRIMARY_SAMPLE       ( 211) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_PRIMARY_SAMPLE' */
#define LABEL_MCS1_MEM_PRI_B1_CYL_TH             ( 212) /* Index into C-array 'mcs1_mem' for assembler label 'PRI_B1_CYL_TH' */
#define LABEL_MCS1_MEM_CYL1_STACK                ( 213) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_STACK' */
#define LABEL_MCS1_MEM_CYL1_WAIT_BUCK_OFF_1       ( 577) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_WAIT_BUCK_OFF_1' */
#define LABEL_MCS1_MEM_CYL3_STACK                ( 229) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_STACK' */
#define LABEL_MCS1_MEM_CYL7_STACK                ( 261) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_STACK' */
#define LABEL_MCS1_MEM_CYL3_DONE                 ( 786) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_DONE' */
#define LABEL_MCS1_MEM_TSK4_STACK                ( 277) /* Index into C-array 'mcs1_mem' for assembler label 'TSK4_STACK' */
#define LABEL_MCS1_MEM_BUCK1_PRIMARY_STACK       ( 293) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_PRIMARY_STACK' */
#define LABEL_MCS1_MEM_BUCK1_STACK               ( 325) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_STACK' */
#define LABEL_MCS1_MEM_FBUCK1_UNLOADING          ( 341) /* Index into C-array 'mcs1_mem' for assembler label 'FBUCK1_UNLOADING' */
#define LABEL_MCS1_MEM_B1_U_LOOP_WA              ( 351) /* Index into C-array 'mcs1_mem' for assembler label 'B1_U_LOOP_WA' */
#define LABEL_MCS1_MEM_BUCK1_TOP_LOOP            (1392) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_TOP_LOOP' */
#define LABEL_MCS1_MEM_B1_U_LOOP                 ( 353) /* Index into C-array 'mcs1_mem' for assembler label 'B1_U_LOOP' */
#define LABEL_MCS1_MEM_B1_U_EXIT                 ( 355) /* Index into C-array 'mcs1_mem' for assembler label 'B1_U_EXIT' */
#define LABEL_MCS1_MEM_B1_L_LOOP_WA              ( 364) /* Index into C-array 'mcs1_mem' for assembler label 'B1_L_LOOP_WA' */
#define LABEL_MCS1_MEM_B1_L_EXIT                 ( 372) /* Index into C-array 'mcs1_mem' for assembler label 'B1_L_EXIT' */
#define LABEL_MCS1_MEM_CYL1_EPWS_FUNC            ( 373) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_FUNC' */
#define LABEL_MCS1_MEM_CYL1_T_ON_START           ( 373) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_T_ON_START' */
#define LABEL_MCS1_MEM_CYL1_T_OFF_STEP           ( 381) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_T_OFF_STEP' */
#define LABEL_MCS1_MEM_CYL1_EPWS_WAIT_T_OFF_MATCH       ( 384) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_WAIT_T_OFF_MATCH' */
#define LABEL_MCS1_MEM_CYL1_INIT                 ( 469) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_INIT' */
#define LABEL_MCS1_MEM_CYL1_EPWS_LOOP            ( 389) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_LOOP' */
#define LABEL_MCS1_MEM_CYL1_EPWS_RET             ( 393) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_RET' */
#define LABEL_MCS1_MEM_CYL1_EPWS_TIME_OUT_RET       ( 395) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_TIME_OUT_RET' */
#define LABEL_MCS1_MEM_CYL5_T_ON_START           ( 421) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_T_ON_START' */
#define LABEL_MCS1_MEM_CYL3_EPWS_WAIT_T_ON_MATCH       ( 400) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_WAIT_T_ON_MATCH' */
#define LABEL_MCS1_MEM_CYL3_T_OFF_STEP           ( 405) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_T_OFF_STEP' */
#define LABEL_MCS1_MEM_CYL1_TIMEOUT              ( 575) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_TIMEOUT' */
#define LABEL_MCS1_MEM_CYL3_EPWS_LOOP            ( 413) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_LOOP' */
#define LABEL_MCS1_MEM_CYL3_EPWS_TIME_OUT_RET       ( 419) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_TIME_OUT_RET' */
#define LABEL_MCS1_MEM_CYL5_WAIT_BUCK_OFF_1       ( 980) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_WAIT_BUCK_OFF_1' */
#define LABEL_MCS1_MEM_CYL5_EPWS_FUNC            ( 421) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_FUNC' */
#define LABEL_MCS1_MEM_CYL5_EPWS_WAIT_T_OFF_MATCH       ( 432) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_WAIT_T_OFF_MATCH' */
#define LABEL_MCS1_MEM_CYL5_EPWS_PH2_START       (1023) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_PH2_START' */
#define LABEL_MCS1_MEM_CYL5_EPWS_RET             ( 441) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_RET' */
#define LABEL_MCS1_MEM_CYL7_EPWS_FUNC            ( 445) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_FUNC' */
#define LABEL_MCS1_MEM_CYL7_EPWS_WAIT_T_ON_MATCH       ( 448) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_WAIT_T_ON_MATCH' */
#define LABEL_MCS1_MEM_CYL7_T_OFF_STEP           ( 453) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_T_OFF_STEP' */
#define LABEL_MCS1_MEM_CYL7_EPWS_LOOP            ( 461) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_LOOP' */
#define LABEL_MCS1_MEM_CYL5_EPWS_MODE            ( 991) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_MODE' */
#define LABEL_MCS1_MEM_CYL7_EPWS_RET             ( 465) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_RET' */
#define LABEL_MCS1_MEM_CYL1_START                ( 477) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_START' */
#define LABEL_MCS1_MEM_CYL1_PSM_DATA_LOADED       ( 496) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_PSM_DATA_LOADED' */
#define LABEL_MCS1_MEM_CYL1_B_DELAY              ( 522) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_B_DELAY' */
#define LABEL_MCS1_MEM_CYL1_CHECK_BUCK_AVB       ( 526) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_CHECK_BUCK_AVB' */
#define LABEL_MCS1_MEM_CYL3_B_DELAY_WA           ( 718) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_B_DELAY_WA' */
#define LABEL_MCS1_MEM_CYL1_DELAY_WA             ( 534) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_DELAY_WA' */
#define LABEL_MCS1_MEM_CYL5_PSM_DATA_LOADED       ( 900) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_PSM_DATA_LOADED' */
#define LABEL_MCS1_MEM_CYL1_EPWS_CHECK           ( 556) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_CHECK' */
#define LABEL_MCS1_MEM_CYL3_WAIT_NEW_CONFIG       ( 707) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_WAIT_NEW_CONFIG' */
#define LABEL_MCS1_MEM_CYL1_INPUT_CLOSE          ( 559) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_INPUT_CLOSE' */
#define LABEL_MCS1_MEM_CYL1_RESTART              ( 567) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_RESTART' */
#define LABEL_MCS1_MEM_CYL1_EPWS_WAIT_CPU_TRG       ( 591) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_WAIT_CPU_TRG' */
#define LABEL_MCS1_MEM_CYL7_B_DELAY              (1124) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_B_DELAY' */
#define LABEL_MCS1_MEM_CYL1_EPWS_TIME_OUT        ( 596) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_TIME_OUT' */
#define LABEL_MCS1_MEM_CYL1_EPWS_STOP_IPRI       ( 599) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_STOP_IPRI' */
#define LABEL_MCS1_MEM_CYL1_EPWS_END             ( 605) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_END' */
#define LABEL_MCS1_MEM_CYL1_EPWS_LAST_LOOP       ( 660) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_LAST_LOOP' */
#define LABEL_MCS1_MEM_CYL1_EPWS_START           ( 606) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_START' */
#define LABEL_MCS1_MEM_CYL5_EPWS_CLOSE           (1002) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_CLOSE' */
#define LABEL_MCS1_MEM_CYL1_EPWS_PH1_START       ( 608) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_PH1_START' */
#define LABEL_MCS1_MEM_CYL1_EPWS_PH2_START       ( 620) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_PH2_START' */
#define LABEL_MCS1_MEM_CYL3_PSM_DATA_LOADED       ( 699) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_PSM_DATA_LOADED' */
#define LABEL_MCS1_MEM_CYL1_EPWS_PH4_START       ( 644) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_PH4_START' */
#define LABEL_MCS1_MEM_CYL1_EPWS_LAST_START       ( 656) /* Index into C-array 'mcs1_mem' for assembler label 'CYL1_EPWS_LAST_START' */
#define LABEL_MCS1_MEM_CYL7_WAIT_BUCK_OFF        (1167) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_WAIT_BUCK_OFF' */
#define LABEL_MCS1_MEM_CYL3_INIT                 ( 670) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_INIT' */
#define LABEL_MCS1_MEM_CYL3_PSM_SETUP            ( 683) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_PSM_SETUP' */
#define LABEL_MCS1_MEM_CYL3_WAIT_INPUT_SIGNAL       ( 702) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_WAIT_INPUT_SIGNAL' */
#define LABEL_MCS1_MEM_CYL3_COMM_OUTPUT_DELAY       ( 729) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_COMM_OUTPUT_DELAY' */
#define LABEL_MCS1_MEM_MOS_START                 (1283) /* Index into C-array 'mcs1_mem' for assembler label 'MOS_START' */
#define LABEL_MCS1_MEM_CYL3_INPUT_CLOSE          ( 760) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_INPUT_CLOSE' */
#define LABEL_MCS1_MEM_CYL3_WAIT_BUCK_OFF        ( 766) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_WAIT_BUCK_OFF' */
#define LABEL_MCS1_MEM_CYL3_RESTART              ( 768) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_RESTART' */
#define LABEL_MCS1_MEM_MCS1_PRIMARY              (1488) /* Index into C-array 'mcs1_mem' for assembler label 'MCS1_PRIMARY' */
#define LABEL_MCS1_MEM_CYL3_EPWS_STOP_IPRI       ( 800) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_STOP_IPRI' */
#define LABEL_MCS1_MEM_CYL3_EPWS_WAIT_BUCK_OFF_1       ( 804) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_WAIT_BUCK_OFF_1' */
#define LABEL_MCS1_MEM_CYL3_EPWS_END             ( 806) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_END' */
#define LABEL_MCS1_MEM_CYL3_EPWS_PH1_START       ( 809) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_PH1_START' */
#define LABEL_MCS1_MEM_CYL3_EPWS_PH3_START       ( 833) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_PH3_START' */
#define LABEL_MCS1_MEM_CYL3_EPWS_LAST_LOOP       ( 861) /* Index into C-array 'mcs1_mem' for assembler label 'CYL3_EPWS_LAST_LOOP' */
#define LABEL_MCS1_MEM_CYL5_START                ( 879) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_START' */
#define LABEL_MCS1_MEM_CYL5_PSM_SETUP            ( 884) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_PSM_SETUP' */
#define LABEL_MCS1_MEM_CYL5_LOAD_PSM             ( 892) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_LOAD_PSM' */
#define LABEL_MCS1_MEM_CYL5_DELAY_WA             ( 936) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_DELAY_WA' */
#define LABEL_MCS1_MEM_CYL5_STOP_OUTPUT          ( 954) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_STOP_OUTPUT' */
#define LABEL_MCS1_MEM_CYL5_INPUT_CLOSE          ( 962) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_INPUT_CLOSE' */
#define LABEL_MCS1_MEM_CYL5_ABORT_COM_OUT        ( 973) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_ABORT_COM_OUT' */
#define LABEL_MCS1_MEM_CYL5_TIMEOUT              ( 978) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_TIMEOUT' */
#define LABEL_MCS1_MEM_CYL5_DONE                 ( 988) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_DONE' */
#define LABEL_MCS1_MEM_CYL5_EPWS_TIME_OUT        ( 999) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_TIME_OUT' */
#define LABEL_MCS1_MEM_CYL5_EPWS_END             (1008) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_END' */
#define LABEL_MCS1_MEM_CYL5_EPWS_START           (1009) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_START' */
#define LABEL_MCS1_MEM_CYL5_EPWS_PH1_START       (1011) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_PH1_START' */
#define LABEL_MCS1_MEM_CYL5_EPWS_PH3_START       (1035) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_PH3_START' */
#define LABEL_MCS1_MEM_CYL5_EPWS_LAST_LOOP       (1063) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_LAST_LOOP' */
#define LABEL_MCS1_MEM_CYL5_EPWS_LAST_PULSE_END       (1068) /* Index into C-array 'mcs1_mem' for assembler label 'CYL5_EPWS_LAST_PULSE_END' */
#define LABEL_MCS1_MEM_CYL7_INIT                 (1073) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_INIT' */
#define LABEL_MCS1_MEM_CYL7_EPWS_START           (1208) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_START' */
#define LABEL_MCS1_MEM_CYL7_PSM_DATA_LOADED       (1100) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_PSM_DATA_LOADED' */
#define LABEL_MCS1_MEM_CYL7_CHECK_BUCK_AVB       (1128) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_CHECK_BUCK_AVB' */
#define LABEL_MCS1_MEM_CYL7_WAIT_INPUT_SIGNAL       (1103) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_WAIT_INPUT_SIGNAL' */
#define LABEL_MCS1_MEM_CYL7_CHECK_EDGE           (1111) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_CHECK_EDGE' */
#define LABEL_MCS1_MEM_CYL7_BUCK_DELAY           (1115) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_BUCK_DELAY' */
#define LABEL_MCS1_MEM_BUCK1_LOADING             (1352) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_LOADING' */
#define LABEL_MCS1_MEM_CYL7_STOP_OUTPUT          (1153) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_STOP_OUTPUT' */
#define LABEL_MCS1_MEM_CYL7_EPWS_CHECK           (1158) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_CHECK' */
#define LABEL_MCS1_MEM_CYL7_RESTART              (1169) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_RESTART' */
#define LABEL_MCS1_MEM_CYL7_DONE                 (1187) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_DONE' */
#define LABEL_MCS1_MEM_CYL7_EPWS_CLOSE           (1201) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_CLOSE' */
#define LABEL_MCS1_MEM_CYL7_EPWS_STOP_IPRI       (1201) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_STOP_IPRI' */
#define LABEL_MCS1_MEM_CYL7_EPWS_WAIT_BUCK_OFF_1       (1205) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_WAIT_BUCK_OFF_1' */
#define LABEL_MCS1_MEM_CYL7_EPWS_PH3_START       (1234) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_PH3_START' */
#define LABEL_MCS1_MEM_CYL7_EPWS_PH4_START       (1246) /* Index into C-array 'mcs1_mem' for assembler label 'CYL7_EPWS_PH4_START' */
#define LABEL_MCS1_MEM_BUCK1_EN_INIT             (1272) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_EN_INIT' */
#define LABEL_MCS1_MEM_CYLINDER1                 (1291) /* Index into C-array 'mcs1_mem' for assembler label 'CYLINDER1' */
#define LABEL_MCS1_MEM_CYLINDER5                 (1301) /* Index into C-array 'mcs1_mem' for assembler label 'CYLINDER5' */
#define LABEL_MCS1_MEM_CYLINDER7                 (1306) /* Index into C-array 'mcs1_mem' for assembler label 'CYLINDER7' */
#define LABEL_MCS1_MEM_BUCK1_RESET_COIL_STATUS       (1316) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_RESET_COIL_STATUS' */
#define LABEL_MCS1_MEM_BUCK1_EN_DONE             (1319) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_EN_DONE' */
#define LABEL_MCS1_MEM_BUCK1_START               (1328) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_START' */
#define LABEL_MCS1_MEM_BUCK1_TOP_LOADING         (1370) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_TOP_LOADING' */
#define LABEL_MCS1_MEM_BUCK1_TIMEOUT_CHECK       (1384) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_TIMEOUT_CHECK' */
#define LABEL_MCS1_MEM_B1_UNLOADING              (1395) /* Index into C-array 'mcs1_mem' for assembler label 'B1_UNLOADING' */
#define LABEL_MCS1_MEM_BUCK1_DISABLE             (1409) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_DISABLE' */
#define LABEL_MCS1_MEM_BUCK1_LPD_FAULT           (1415) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_LPD_FAULT' */
#define LABEL_MCS1_MEM_BUCK1_SET_PTR             (1425) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_SET_PTR' */
#define LABEL_MCS1_MEM_BUCK1_END_LPD_FAULT       (1431) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_END_LPD_FAULT' */
#define LABEL_MCS1_MEM_BUCK1_DONE                (1471) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_DONE' */
#define LABEL_MCS1_MEM_BUCK1_PRIMARY_INIT        (1473) /* Index into C-array 'mcs1_mem' for assembler label 'BUCK1_PRIMARY_INIT' */
#define LABEL_MCS1_MEM_MCS1_PRIM_CURR_START       (1485) /* Index into C-array 'mcs1_mem' for assembler label 'MCS1_PRIM_CURR_START' */
#define LABEL_MCS1_MEM_MCS1_PRIM_WAIT_STOP       (1490) /* Index into C-array 'mcs1_mem' for assembler label 'MCS1_PRIM_WAIT_STOP' */
#define LABEL_MCS1_MEM_MCS1_PRIM_DISABLE         (1493) /* Index into C-array 'mcs1_mem' for assembler label 'MCS1_PRIM_DISABLE' */
#define LABEL_MCS1_MEM_MCS1_PRIM_DONE            (1501) /* Index into C-array 'mcs1_mem' for assembler label 'MCS1_PRIM_DONE' */
#define LABEL_MCS1_MEM_TSK3_INIT                 (1502) /* Index into C-array 'mcs1_mem' for assembler label 'TSK3_INIT' */
#define LABEL_MCS1_MEM_TSK4_INIT                 (1503) /* Index into C-array 'mcs1_mem' for assembler label 'TSK4_INIT' */

#endif
