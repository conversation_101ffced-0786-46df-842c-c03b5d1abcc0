/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TempECUMgm.h
 **  Date:          15-Oct-2021
 **
 **  Model Version: 1.1052
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TempECUMgm_h_
#define RTW_HEADER_TempECUMgm_h_
#ifndef TempECUMgm_COMMON_INCLUDES_
# define TempECUMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TempECUMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void TempECUMgm_initialize(void);

/* Exported entry point function */
extern void TempECUMgm_10ms(void);

/* Exported entry point function */
extern void TempECUMgm_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_EE_INTERFACE */
extern uint16_T EEHTempECU;            /* '<S2>/MEEHTempECU' */

/* Counter of Temp Hazard */
extern int16_T EETempECUMax1;          /* '<S2>/MEETempECUMax1' */

/* Temperature ECU 1 Max */
extern int16_T EETempECUMax2;          /* '<S2>/MEETempECUMax2' */

/* Temperature ECU 1 Max */
extern int16_T EETempECUMax3;          /* '<S2>/MEETempECUMax3' */

/* Temperature ECU 1 Max */
extern uint32_T EETimHTempECU;         /* '<S2>/MEETimHTempECU' */

/* Time of Temp Hazard */
extern uint32_T EETimWTempECU;         /* '<S2>/MEETimWTempECU' */

/* Time of Temp Warning */
extern uint16_T EEWTempECU;            /* '<S2>/MEEWTempECU' */

/* Counter of Temp Warning */

/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgTEcuHazard;          /* '<S2>/MFlgTEcuHazard' */

/* Temperature Hazard flag */
extern uint8_T FlgTEcuWarn;            /* '<S2>/MFlgTEcuWarn' */

/* Temperature warning flag */
extern int16_T TempECU;                /* '<S2>/MTempECU' */

/* Temperature ECU */
extern int16_T TempECU1;               /* '<S2>/MTempECU1' */

/* Temperature ECU 1 */
extern int16_T TempECU2;               /* '<S2>/MTempECU2' */

/* Temperature ECU 2 */
extern int16_T TempECU3;               /* '<S2>/MTempECU3' */

/* Temperature ECU 3 */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S11>/Data Type Duplicate' : Unused code path elimination
 * Block '<S14>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Data Type Propagation' : Unused code path elimination
 * Block '<S36>/Data Type Duplicate' : Unused code path elimination
 * Block '<S37>/Data Type Duplicate' : Unused code path elimination
 * Block '<S39>/Data Type Duplicate' : Unused code path elimination
 * Block '<S38>/Data Type Duplicate' : Unused code path elimination
 * Block '<S38>/Data Type Propagation' : Unused code path elimination
 * Block '<S41>/Data Type Duplicate' : Unused code path elimination
 * Block '<S40>/Data Type Duplicate' : Unused code path elimination
 * Block '<S40>/Data Type Propagation' : Unused code path elimination
 * Block '<S42>/Data Type Duplicate' : Unused code path elimination
 * Block '<S34>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S12>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S39>/Conversion' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S34>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S42>/Conversion' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TempECUMgm'
 * '<S1>'   : 'TempECUMgm/Init_fcn'
 * '<S2>'   : 'TempECUMgm/Merge'
 * '<S3>'   : 'TempECUMgm/TempECUMgm_Scheduler'
 * '<S4>'   : 'TempECUMgm/fcn_10ms'
 * '<S5>'   : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation'
 * '<S6>'   : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation'
 * '<S7>'   : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation'
 * '<S8>'   : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Diagnosis_For_TempECU3'
 * '<S9>'   : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/FirstOrderFilter_TempECU3'
 * '<S10>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Temperature_Calculation'
 * '<S11>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Diagnosis_For_TempECU3/SetDiagState'
 * '<S12>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/FirstOrderFilter_TempECU3/FOF_Reset_S16_FXP'
 * '<S13>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/FirstOrderFilter_TempECU3/Saturation'
 * '<S14>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/FirstOrderFilter_TempECU3/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S15>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Temperature_Calculation/Div_SInt_UnSInt'
 * '<S16>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Temperature_Calculation/Division_Implementation'
 * '<S17>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Temperature_Calculation/Protection_Div_By_Zero'
 * '<S18>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Temperature_Calculation/Protection_Div_By_Zero2'
 * '<S19>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Temperature_Calculation/Saturation'
 * '<S20>'  : 'TempECUMgm/fcn_10ms/CPU_Temperature_Calculation/Temperature_Calculation/Division_Implementation/Div_SInt_UnSInt1'
 * '<S21>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/EEHTempECU_Calculation'
 * '<S22>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/EEWTempECU_Calculation'
 * '<S23>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/FlgTEcu3Hazard_Calculation'
 * '<S24>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/FlgTEcuHazard_Calculation'
 * '<S25>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/FlgTEcuWarn_Calculation'
 * '<S26>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/RescalSignedIntRightShift'
 * '<S27>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/RescalSignedIntRightShift1'
 * '<S28>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/RescalSignedIntRightShift2'
 * '<S29>'  : 'TempECUMgm/fcn_10ms/ECU_Temperature_Calculation/Saturation'
 * '<S30>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/Diagnosis_For_TempECU1'
 * '<S31>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/Diagnosis_For_TempECU2'
 * '<S32>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/FirstOrderFilter_TempECU1'
 * '<S33>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/FirstOrderFilter_TempECU2'
 * '<S34>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/LookUp_S16_U1'
 * '<S35>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/LookUp_S16_U16'
 * '<S36>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/Diagnosis_For_TempECU1/SetDiagState'
 * '<S37>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/Diagnosis_For_TempECU2/SetDiagState'
 * '<S38>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/FirstOrderFilter_TempECU1/FOF_Reset_S16_FXP'
 * '<S39>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/FirstOrderFilter_TempECU1/FOF_Reset_S16_FXP/Data Type Conversion Inherited1'
 * '<S40>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/FirstOrderFilter_TempECU2/FOF_Reset_S16_FXP1'
 * '<S41>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/FirstOrderFilter_TempECU2/FOF_Reset_S16_FXP1/Data Type Conversion Inherited1'
 * '<S42>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/LookUp_S16_U1/Data Type Conversion Inherited3'
 * '<S43>'  : 'TempECUMgm/fcn_10ms/NTC_Temperature_Calculation/LookUp_S16_U16/Data Type Conversion Inherited3'
 */

/*-
 * Requirements for '<Root>': TempECUMgm
 */
#endif                                 /* RTW_HEADER_TempECUMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/