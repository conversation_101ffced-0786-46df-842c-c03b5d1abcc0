/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef GTM_EISB_OUT_H
#define GTM_EISB_OUT_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "ionacq_out.h"

/*!
\defgroup PublicDefines Public Defines
\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
//#define EN_DEBUG_PRI_SEC

#define ION0_4_N_SAMPLE (MAX_SAMPLE)
#define ION1_5_N_SAMPLE (MAX_SAMPLE)
#define ION2_6_N_SAMPLE (MAX_SAMPLE)
#define ION3_7_N_SAMPLE (MAX_SAMPLE)

#define N_CHANNEL_SPK_EV            (2u)

/*!\egroup*/
/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
typedef enum {
    ION_CYL_0 = 0,
    ION_CYL_1 = 1,
    ION_CYL_2 = 2,
    ION_CYL_3 = 3,
    ION_CYL_4 = 4,
    ION_CYL_5 = 5,
    ION_CYL_6 = 6,
    ION_CYL_7 = 7
}ion_Channel_T;

/*!\egroup*/
/*!
\defgroup PublicInline Public Inline Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC INLINE FUNCTIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   Inline function name
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this inline function 
*/
/**************************************************************************/


/*!\egroup*/


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint16_T ion0_4_buffer[ION0_4_N_SAMPLE];
extern uint16_T ion1_5_buffer[ION1_5_N_SAMPLE];
extern uint16_T ion2_6_buffer[ION2_6_N_SAMPLE];
extern uint16_T ion3_7_buffer[ION3_7_N_SAMPLE];

extern ion_Channel_T NextIonCyl[N_CHANNEL_ADC];
extern uint16_T VtILeadPeak[N_CYL_MAX];
extern int16_T  VtIShotPeak[N_CYL_MAX];

extern uint32_T EffDwellTime[N_CYL_MAX];
extern uint16_T VtNrStartIonDMAIdx[N_CYL_MAX];
extern uint8_T FlgIShotTout[N_CHANNEL];

extern uint32_T IgnAngle[N_CYL_MAX];

extern uint32_T SparkLength[N_CYL_MAX];
extern uint8_T VtCntIgnWaitSpEvnt[N_CHANNEL_SPK_EV];
extern uint8_T SparkCyl;
/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/******************************************************************************
**   Function    : Gtm_Eisb_TDC
**
**   Description:
**    Activate TIM relative to Cylinder index AbsTdc + 2
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void Gtm_Eisb_TDC(void);

/******************************************************************************
**   Function    : Gtm_Eisb_NoSync
**
**   Description:
**    Activate TIM relative to Cylinder index AbsTdc + 2
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void Gtm_Eisb_NoSync(void);



extern void ipri_b0_saradcconf_ch4_ipri_b0_dma(void);
extern void ipri_b1_saradcconf_ch16_ipri_b1_dma(void);
extern void isec_b0_saradcconf_ch5_isec_b0_dma(void);
extern void isec_b1_saradcconf_ch17_isec_b1_dma(void);
extern void ion0_4_saradcconf_ch44_dma_ion0_4(void);
extern void ion1_5_saradcconf_ch48_dma_ion1_5(void);
extern void ion2_6_saradcconf_ch45_dma_ion2_6(void);
extern void ion3_7_saradcconf_ch52_dma_ion3_7(void);

extern void Gtm_Eisb_TomEnable(void);
extern void DMA_StopGlob(uint8_T DMA_CH);

#endif

/****************************************************************************
 ****************************************************************************/



