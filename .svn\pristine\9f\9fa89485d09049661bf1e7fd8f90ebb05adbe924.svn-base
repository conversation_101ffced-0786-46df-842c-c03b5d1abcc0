Index: tree/BIOS/ADC/Adc.c
===================================================================
--- tree/BIOS/ADC/Adc.c	(revision 206729)
+++ tree/BIOS/ADC/Adc.c	(working copy)
@@ -39,6 +39,8 @@
 
 uint16_T ADCConfigStatus = 0U;
 
+uint8_T CntSar0TimOut, CntSar2TimOut;
+
 /*****************************************************************************
 ** PRIVATE VARIABLES AND TESTPOINTS
 ******************************************************************************/
@@ -334,6 +336,9 @@
 
             /* Read the value if timeout is not expired */
             retSDRead = SDADC_Read(ADCConfig[channelNumber].perNumber, &result);
+
+            SDADC_StopConversion(ADCConfig[channelNumber].perNumber);
+            
             if ((retSDRead != SDADC_DATA_VALID) && (retSDRead != SDADC_DATA_VALID_AFTER_TIMEOUT))
             {
                 retValue = PERIPHERAL_FAILURE;
@@ -931,7 +936,7 @@
 
     sdPtr = SD_PER[channel];
 
-    /* Start conversion by powering on ADC module */
+    /* stop conversion by powering down ADC module */
     sdPtr->MCR.B.EN = 1U;
     
 }
@@ -1226,11 +1231,33 @@
 ******************************************************************************/
 void SAR0_StartConversion(void) 
 {
+timeoutHandler_t sarStopToutHlr;
+uint8_T sarStatus = TIMEOUT_PENDING; 
+
+    CntSar0TimOut++;
 #if (SAR0_DMA_ENABLE == SARADC_DMA_ENABLED)
     SARADC_0.DMAE.B.DMAEN = 1U;
 #endif
 
     SARADC_0.MCR.B.NSTART = 1U;
+
+    TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
+    if (SARADC_0.MSR.B.NSTART != 1U)
+    {
+        while(sarStatus == TIMEOUT_PENDING)
+        {
+            TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
+            if (SARADC_0.MSR.B.NSTART == 1u)
+            {
+                CntSar0TimOut--;
+                break;
+            }
+        }
+    }
+    else
+    {
+        CntSar0TimOut--;
+    }
 }
 
 /******************************************************************************
@@ -1254,11 +1281,33 @@
 ******************************************************************************/
 void SAR2_StartConversion(void) 
 {
+timeoutHandler_t sarStopToutHlr;
+uint8_T sarStatus = TIMEOUT_PENDING; 
+
+    CntSar2TimOut++;
 #if (SAR2_DMA_ENABLE == SARADC_DMA_ENABLED)
     SARADC_2.DMAE.B.DMAEN = 1U;
 #endif
 
     SARADC_2.MCR.B.NSTART = 1U;
+
+    TIMING_SetTimeout(SARADC_STOPTIMEOUT, &sarStopToutHlr);
+    if (SARADC_2.MSR.B.NSTART != 1U)
+    {
+        while(sarStatus == TIMEOUT_PENDING)
+        {
+            TIMING_GetTimeoutStatus(sarStopToutHlr, &sarStatus);
+            if (SARADC_2.MSR.B.NSTART == 1u)
+            {
+                CntSar2TimOut--;
+                break;
+            }
+        }
+    }
+    else
+    {
+        CntSar2TimOut--;
+    }
 }
 
 /******************************************************************************
@@ -1567,7 +1616,6 @@
 #if (SARSV_DMA_ENABLE == SARADC_DMA_ENABLED)
     SARADC_B.DMAE.B.DMAEN = 0U;
 #endif
-	SARADC_B.ISR.R = 0xFFU;
 }
 
 /******************************************************************************
@@ -1786,45 +1834,31 @@
     SARADC_2.MCR.B.MODE = 1u;
 }
 
-
-
 /******************************************************************************
-**   Function    : SDADC_TestApplyCalib
+**   Function    : ADC_SARInit
 **
 **   Description:
-**    Applies calibration gain and offset to SD raw data, returns the voltage.
+**    Configure SAR peripheral in scan mode.
 **
 **   Parameters :
-**    [in] uint8_T perNumber : selected SDADC Engine
-**    [in] uint16_T rawValue : data to be calibrated
+**    void
 **
 **   Returns:
-**   float_T CalibResult : calibration result.
+**    void
 **
-**
 **   SW Requirements:
 **    NA
 **
 **   Implementation Notes:
-**   This is a TEST API with not-optimized floating point divisions.
-**   To be used for test purpose only.
 **
 **   EA GUID:
 ******************************************************************************/
-#ifdef SD_TEST_CALCULATED_VALUE
-float_T SDADC_TestApplyCalib (uint8_T perNumber, uint16_T rawValue)
+void ADC_SARInit(void)
 {
-    float_T CalibResult =(float_T) 0;
-
-    rawValue += (uint16_T)SDCalib[perNumber].offset;
-    CalibResult = (float_T)(rawValue) / (SDCalib[perNumber].gain);
-    CalibResult = ((float_T)CalibResult / (float_T)32768U)*(float_T)(SDADC_VREFP - SDADC_VREFN);
-
-    return CalibResult;
+    SAR0_ConfigScanMode();
+    SAR2_ConfigScanMode();
 }
-#endif /* SD_TEST_CALCULATED_VALUE */
 
-
 /*****************************************************************************
 ** PRIVATE FUNCTION DEFINITIONS
 ******************************************************************************/
@@ -1954,6 +1988,19 @@
     /* enable watchdog */
     sdPtr->MCR.B.WDGEN = SD_CONFIG[channel].sdWdgEn;
 
+#if 0
+    /* set low threshold */
+    step = (float_T)32768U / (float_T)(SDADC_VREFP - SDADC_VREFN);
+    thr = (step * (float_T)SDADC_WATCHDOG_LOWTH * SDCalib[channel].gain) - (float_T)SDCalib[channel].offset;
+    low = (uint16_T)thr;
+    sdPtr->WTHHLR.B.THRL = low;
+  
+    /* set high threshold*/
+    thr = (step * (float_T)SDADC_WATCHDOG_HIGHTH * SDCalib[channel].gain) - (float_T)SDCalib[channel].offset;
+    high = (uint16_T)thr;
+    sdPtr->WTHHLR.B.THRH = high;
+#endif
+
     if (SD_CONFIG[channel].sdTrigEn == SDADC_HW_TRIGGER_ENABLED)
     {
         /* Enable trigger */
@@ -1966,6 +2013,9 @@
         sdPtr->MCR.B.TRIGEDSEL = SD_CONFIG[channel].sdTrigEdge;
     }
 
+    /* enable interrupt */
+    // sdPtr->RSER.B.WTHDIRE = 1U;
+
 }
 
 /******************************************************************************
@@ -2021,6 +2071,26 @@
         sarPtr->CTR[i].B.INPSAMP = SAR_CONFIG[channel].sarCtrlInpsamp;
     }
 
+    /* Configure analog watchdog thresolds */
+    // for (i = 0; i < saradcp->config->numofthresholds; i++) {
+    //    float_T step;
+    //    float_T thr;
+    //    uint16_T low;
+    //    uint16_T high;
+    //    step = (float_T)4096 / saradcp->config->vref;
+    //    thr = step * saradcp->config->thr[i].low;
+    //    low = (uint16_T)(thr);
+    //    if (saradcp->config->thr[i].high >= saradcp->config->vref) {
+    //    high = 0xFFFFU;
+    //    }
+    //    else {
+    //    thr = step * saradcp->config->thr[i].high;
+    //    high = (uint16_T)(thr);
+    //    }
+    //    saradcp->WTHRHLR[i].B.THRL = low;
+    //    saradcp->WTHRHLR[i].B.THRH = high;
+    // }
+
     /* configure channels if DMA is not used */
     SARADC_ConfigAnCh(channel);
 
@@ -2030,6 +2100,13 @@
     /* Enable interrupt for end of conversion */
     sarPtr->IMR.R = SAR_CONFIG[channel].sarIsrEn;
 
+    /* Clear Watchdog interrupt flags */
+    // saradcp->WTISR.R = 0xFFUL;
+
+    /* Enable interrupts for configured thresholds */
+    // for (i = 0; i < saradcp->config->numofthresholds; i++) {
+    //    saradcp->WTIMR.R |= 3UL << (i * 2U);
+    // }
 }
 
 /******************************************************************************
@@ -2527,6 +2604,9 @@
     /* Enable FIFO */
     sdPtr->FCR.B.FE = 1U;
 
+    /* set FIFO size to 16byte*/
+    // sdPtr->FCR.B.FSIZE = SDADC_FIFO_16_BYTE; //FIFO size is 16 set by hardware
+
     /* set fifo threshold to 16 byte*/
     sdPtr->FCR.B.FTHLD = 0x0FU;
 
@@ -2640,6 +2720,9 @@
     /* Enable FIFO */
     sdPtr->FCR.B.FE = 1U;
 
+    /* set FIFO size to 16byte*/
+    // sdPtr->FCR.B.FSIZE = SDADC_FIFO_16_BYTE; //FIFO size is 16 set by hardware
+
     /* set fifo threshold to 16 byte*/
     sdPtr->FCR.B.FTHLD = 0x0FU;
 
@@ -2804,6 +2887,18 @@
             sarPtr->ICDR[SAR0_CHANNELS[i]].B.CTSEL = SARADC_CTR0;
 
             sarPtr->ICDR[SAR0_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;
+
+#if 0 /* Unused functionality  */
+            /* Channel voltage reference Selection (if supported) */
+            /* Select threshold register and activate if enabled */
+            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
+            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
+            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
+#endif
         }
     }
     else if (channel == SAR_ENGINE_2) /* SAR 2 */
@@ -2816,6 +2911,18 @@
             sarPtr->ICDR[SAR2_CHANNELS[i]].B.CTSEL = SARADC_CTR0;
             
             sarPtr->ICDR[SAR2_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;
+
+#if 0 /* Unused functionality  */
+            /* Channel voltage reference Selection (if supported) */
+            /* Select threshold register and activate if enabled */
+            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
+            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
+            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
+#endif
         }
     }
     else if (channel == SAR_ENGINE_4) /* SAR 4 */
@@ -2828,6 +2935,18 @@
             sarPtr->ICDR[SAR4_CHANNELS[i]].B.CTSEL = SARADC_CTR0;
             
             sarPtr->ICDR[SAR4_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;
+
+#if 0 /* Unused functionality  */
+            /* Channel voltage reference Selection (if supported) */
+            /* Select threshold register and activate if enabled */
+            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
+            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
+            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
+#endif
         }
     }
     else if (channel == SAR_ENGINE_6) /* SAR 6 */
@@ -2840,6 +2959,18 @@
             sarPtr->ICDR[SAR6_CHANNELS[i]].B.CTSEL = SARADC_CTR0;
 
             sarPtr->ICDR[SAR6_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;
+
+#if 0 /* Unused functionality  */
+            /* Channel voltage reference Selection (if supported) */
+            /* Select threshold register and activate if enabled */
+            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
+            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
+            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
+#endif
         }
     }
     else if (channel == SAR_ENGINE_SV) /* SARSV */
@@ -2852,6 +2983,19 @@
             sarPtr->ICDR[SARSV_CHANNELS[i]].B.CTSEL = SARADC_CTR0;
 
             sarPtr->ICDR[SARSV_CHANNELS[i]].B.REFSEL = SARADC_REFERENCE_DEFAULT;
+            
+#if 0 /* Unused functionality  */
+            /* Channel voltage reference Selection (if supported) */
+            /* Select threshold register and activate if enabled */
+            // if (saradcp->config->ch[i].thr != SARADC_WATCHDOG_REGISTER_NONE) {
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 8U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 8U) * 4U;
+            //    saradcp->ICWSELR[regIndex].R |= (uint32_t)saradcp->config->ch[i].thr << (bitIndex);
+            //    regIndex = (uint8_T)(SAR_CHANNELS[i] / 32U);
+            //    bitIndex = (uint8_T)(SAR_CHANNELS[i] % 32U);
+            //    saradcp->ICWENR[regIndex].R |= 1UL << bitIndex;
+#endif
+
         }
     }
 }
Index: tree/BIOS/MCAN/Mcan_events.c
===================================================================
--- tree/BIOS/MCAN/Mcan_events.c	(revision 241775)
+++ tree/BIOS/MCAN/Mcan_events.c	(working copy)
@@ -108,9 +108,13 @@
 
 #ifdef _BUILD_INTC_CHECK_TRIG_SET_
         /* Run INT_CHECK_TRIGGER_SET mechanism */
-        if (MCAN_1.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
+        if ((MCAN_1.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
+#ifdef FORCE_SAFETY_ERROR
+        || (SAFEERRIDPOS[SAFE_ERR_CANCHAL0_INTC_TRIGSET_POS] == TRUE)
+#endif
+        )
         {
-            SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
+            SafetyMngr_ReportError((uint16_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE, SF_SM_INTC_CMD, NO_STORED_DIAG, NO_PT_FAULT);
         }
 #endif // _BUILD_INTC_CHECK_TRIG_SET_
 #endif // _BUILD_SAFETYMNGR_INTC_
@@ -213,9 +217,13 @@
 
 #ifdef _BUILD_INTC_CHECK_TRIG_SET_
         /* Run INT_CHECK_TRIGGER_SET mechanism */
-        if (MCAN_1.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
+        if ((MCAN_1.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
+#ifdef FORCE_SAFETY_ERROR
+                    || (SAFEERRIDPOS[SAFE_ERR_CANCHAL1_INTC_TRIGSET_POS] == TRUE)
+#endif
+        )
         {
-            SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
+            SafetyMngr_ReportError((uint16_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE, SF_SM_INTC_CMD, NO_STORED_DIAG, NO_PT_FAULT);
         }
 #endif // _BUILD_INTC_CHECK_TRIG_SET_
 #endif // _BUILD_SAFETYMNGR_INTC_
@@ -374,9 +382,13 @@
 
 #ifdef _BUILD_INTC_CHECK_TRIG_SET_
         /* Run INT_CHECK_TRIGGER_SET mechanism */
-        if (MCAN_2.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
+        if ((MCAN_2.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
+#ifdef FORCE_SAFETY_ERROR
+                || (SAFEERRIDPOS[SAFE_ERR_CANCHBL0_INTC_TRIGSET_POS] == TRUE)
+#endif
+        )
         {
-            SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
+            SafetyMngr_ReportError((uint16_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE, SF_SM_INTC_CMD, NO_STORED_DIAG, NO_PT_FAULT);
         }
 #endif // _BUILD_INTC_CHECK_TRIG_SET_
 #endif // _BUILD_SAFETYMNGR_INTC_
@@ -477,9 +489,13 @@
 
 #ifdef _BUILD_INTC_CHECK_TRIG_SET_
         /* Run INT_CHECK_TRIGGER_SET mechanism */
-        if (MCAN_2.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
+        if ((MCAN_2.IR.B.DRX != MCAN_RX_ISR_OCCURRED)
+#ifdef FORCE_SAFETY_ERROR
+            || (SAFEERRIDPOS[SAFE_ERR_CANCHBL1_INTC_TRIGSET_POS] == TRUE)
+#endif
+        )
         {
-            SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
+            SafetyMngr_ReportError((uint16_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE, SF_SM_INTC_CMD, NO_STORED_DIAG, NO_PT_FAULT);
         }
 #endif // _BUILD_INTC_CHECK_TRIG_SET_
 #endif // _BUILD_SAFETYMNGR_INTC_
Index: tree/BIOS/TTCAN/TTcan_events.c
===================================================================
--- tree/BIOS/TTCAN/TTcan_events.c	(revision 211099)
+++ tree/BIOS/TTCAN/TTcan_events.c	(working copy)
@@ -87,14 +87,6 @@
 #ifdef _BUILD_SAFETYMNGR_INTC_
         /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
         SafetyMngr_INTC_CheckCtx(M_TTCAN_LINE0_ISR_POS); // Interrupt no. 677
-    
-#ifdef _BUILD_INTC_CHECK_TRIG_SET_
-        /* Run INT_CHECK_TRIGGER_SET mechanism */
-        if (M_TTCAN_0.IR.B.DRX != TTCAN_RX_ISR_OCCURRED)
-        {
-            SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
-        }
-#endif // _BUILD_INTC_CHECK_TRIG_SET_
 #endif // _BUILD_SAFETYMNGR_INTC_
 
 
@@ -192,14 +184,6 @@
 #ifdef _BUILD_SAFETYMNGR_INTC_
         /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
         SafetyMngr_INTC_CheckCtx(M_TTCAN_LINE1_ISR_POS); // Interrupt no. 678
-
-#ifdef _BUILD_INTC_CHECK_TRIG_SET_
-        /* Run INT_CHECK_TRIGGER_SET mechanism */
-        if (M_TTCAN_0.IR.B.DRX != TTCAN_RX_ISR_OCCURRED)
-        {
-            SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
-        }
-#endif // _BUILD_INTC_CHECK_TRIG_SET_
 #endif // _BUILD_SAFETYMNGR_INTC_
 
         /* check if Rx buffer interrupt occurred on line 0 */
Index: tree/COMMON/INCLUDE/Adc_out.h
===================================================================
--- tree/COMMON/INCLUDE/Adc_out.h	(revision 206729)
+++ tree/COMMON/INCLUDE/Adc_out.h	(working copy)
@@ -170,6 +170,8 @@
 extern uint16_T SDADCInvGain[NUM_SD];
 extern int16_T  SDADCOffset[NUM_SD];
 
+extern uint8_T CntSar0TimOut, CntSar2TimOut;
+
 /*****************************************************************************
 ** PUBLIC FUNCTION DEFINITIONS
 ******************************************************************************/
@@ -950,31 +952,25 @@
 extern void SAR2_ConfigScanMode(void);
 
 /******************************************************************************
-**   Function    : SDADC_TestApplyCalib
+**   Function    : ADC_SARConfig
 **
 **   Description:
-**    Applies calibration gain and offset to SD raw data, returns the voltage.
+**    Configure SAR peripheral in scan mode.
 **
 **   Parameters :
-**    [in] uint8_T perNumber : selected SDADC Engine
-**    [in] uint16_T rawValue : data to be calibrated
+**    void
 **
 **   Returns:
-**   float_T CalibResult : calibration result.
+**    void
 **
-**
 **   SW Requirements:
 **    NA
 **
 **   Implementation Notes:
-**   This is a TEST API with not-optimized floating point divisions.
-**   To be used for test purpose only.
 **
 **   EA GUID:
 ******************************************************************************/
-#ifdef SD_TEST_CALCULATED_VALUE
-extern float_T SDADC_TestApplyCalib (uint8_T perNumber, uint16_T rawValue);
-#endif /* SD_TEST_CALCULATED_VALUE */
+extern void ADC_SARInit(void);
 
 #endif /* _ADC_OUT_H_ */
 
