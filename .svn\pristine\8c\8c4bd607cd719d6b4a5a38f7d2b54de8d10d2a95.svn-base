﻿<?xml version="1.0" encoding="utf-8"?>
<EcuFlashProject xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" ToolVersion="5.0.2100.0">
  <TemplateInfo>
    <AllowSeveralDriver>false</AllowSeveralDriver>
    <PotentialUseCase>Fiat Native</PotentialUseCase>
    <DefaultLengthOfMemorySize>3</DefaultLengthOfMemorySize>
    <DefaultLengthOfMemoryAddress>3</DefaultLengthOfMemoryAddress>
    <VersionInfo>
      <TemplateName>Fiat SLP2</TemplateName>
      <Version>1.5</Version>
      <ModificationDate>2015-08-10</ModificationDate>
      <BaseToolVersion>2.1.100</BaseToolVersion>
      <CompatibilityToolVersion>2.6</CompatibilityToolVersion>
      <Protocol>UDS</Protocol>
      <SpecificationVersion>SLP2</SpecificationVersion>
      <OEM>Fiat</OEM>
    </VersionInfo>
    <UseSeedExtension>true</UseSeedExtension>
  </TemplateInfo>
  <FlashAttributes>
    <FlashAttribute>
      <Qualifier>Wait_Time_after_ECU_Reset_in_ms</Qualifier>
      <Value>700</Value>
      <DefaultValue>700</DefaultValue>
    </FlashAttribute>
  </FlashAttributes>
  <FaultInjectionAttributes />
  <FileReferences />
  <Use-Case>Fiat Native</Use-Case>
  <Scripting>
    <FlashDB>
      <FilePath AbsolutePath="C:\Codice\EISB8C\Appl\EISB8C_AM_01_UDS\DiagKit\CAN\I5F81D\I5F81D\Fiat_SLP2.cdd" RelativePath="I5F81D\Fiat_SLP2.cdd" />
    </FlashDB>
    <ScriptPackage Name="0">
      <ScriptFile AbsolutePath="C:\Codice\EISB8C\Appl\EISB8C_AM_01_UDS\DiagKit\CAN\I5F81D\I5F81D\Script.dll" RelativePath="I5F81D\Script.dll" />
    </ScriptPackage>
  </Scripting>
  <Communication>
    <Ecu>
      <EcuQualifier>Fiat_SLP2</EcuQualifier>
      <Name>Fiat_SLP2</Name>
      <LogicalLink>CAN_Power_Train_ECUs</LogicalLink>
      <CommunicationParam>
        <Name>Request CAN Id Phys</Name>
        <Value>0x18DA2EF1</Value>
        <Unit />
      </CommunicationParam>
      <CommunicationParam>
        <Name>Response CAN Id</Name>
        <Value>0x18DAF12E</Value>
        <Unit />
      </CommunicationParam>
      <CommunicationParam>
        <Name>Broadcast Address</Name>
        <Value>0xFE</Value>
        <Unit />
      </CommunicationParam>
      <CommunicationParam>
        <Name>Baudrate</Name>
        <Value>500000</Value>
        <Unit>Baud</Unit>
      </CommunicationParam>
      <CommunicationParam>
        <Name>Secondary Baudrate</Name>
        <Value>0</Value>
        <Unit>Baud</Unit>
      </CommunicationParam>
      <CommunicationParam>
        <Name>Secondary Target Address</Name>
        <Value>0x0</Value>
        <Unit />
      </CommunicationParam>
      <CommunicationParam>
        <Name>CAN Id Type Phys</Name>
        <Value>29</Value>
        <Unit>Bit</Unit>
      </CommunicationParam>
      <CommunicationParam>
        <Name>CAN Id Type Func</Name>
        <Value>29</Value>
        <Unit>Bit</Unit>
      </CommunicationParam>
      <CommunicationParam>
        <Name>Tseg1</Name>
        <Value>8</Value>
        <Unit />
      </CommunicationParam>
      <CommunicationParam>
        <Name>Tseg2</Name>
        <Value>7</Value>
        <Unit />
      </CommunicationParam>
      <CommunicationParam>
        <Name>SJW</Name>
        <Value>1</Value>
        <Unit />
      </CommunicationParam>
      <CommunicationParam>
        <Name>SAM</Name>
        <Value>0</Value>
        <Unit />
      </CommunicationParam>
      <CommunicationParam>
        <Name>P2Handling</Name>
        <Value>Override</Value>
      </CommunicationParam>
      <CommunicationParam>
        <Name>P2</Name>
        <Value>150</Value>
      </CommunicationParam>
      <CommunicationParam>
        <Name>P2Star</Name>
        <Value>5100</Value>
      </CommunicationParam>
      <CommunicationParam>
        <Name>StMinHandling</Name>
        <Value>ECU Challenge</Value>
      </CommunicationParam>
      <CommunicationParam>
        <Name>StMinOverride</Name>
        <Value>0xFFFF</Value>
      </CommunicationParam>
      <CommunicationParam>
        <Name>Max TP Message Length</Name>
        <Value>4095</Value>
        <Unit />
      </CommunicationParam>
    </Ecu>
  </Communication>
  <Configuration>
    <FlashData>
      <NativeFlashData>
        <Datablock>
          <FilePath />
          <UseForReprogramming>false</UseForReprogramming>
          <Type>Driver1</Type>
          <LengthOfLogicalBlockIndex>1</LengthOfLogicalBlockIndex>
          <LogicalBlockIndex>0</LogicalBlockIndex>
          <Checksum>
            <ChecksumPath AbsolutePath="" RelativePath="" />
          </Checksum>
          <Signature />
          <Segment>
            <LengthOfMemorySize>3</LengthOfMemorySize>
            <LengthOfMemoryAddress>3</LengthOfMemoryAddress>
            <MemoryAddress>0</MemoryAddress>
          </Segment>
          <CompressionEncryption>
            <DataFormatdentifier>0</DataFormatdentifier>
            <CompressionType>Undefined</CompressionType>
          </CompressionEncryption>
        </Datablock>
        <Datablock>
          <FilePath />
          <UseForReprogramming>false</UseForReprogramming>
          <Type>Driver2</Type>
          <LengthOfLogicalBlockIndex>1</LengthOfLogicalBlockIndex>
          <LogicalBlockIndex>0</LogicalBlockIndex>
          <Checksum>
            <ChecksumPath AbsolutePath="" RelativePath="" />
          </Checksum>
          <Signature />
          <Segment>
            <LengthOfMemorySize>3</LengthOfMemorySize>
            <LengthOfMemoryAddress>3</LengthOfMemoryAddress>
            <MemoryAddress>0</MemoryAddress>
          </Segment>
          <CompressionEncryption>
            <DataFormatdentifier>0</DataFormatdentifier>
            <CompressionType>Undefined</CompressionType>
          </CompressionEncryption>
        </Datablock>
        <Datablock>
          <FilePath AbsolutePath="C:\Codice\EISB8C\Appl\EISB8C_AM_01_UDS\DiagKit\CAN\I5F81D\I5F81D.idx" RelativePath="I5F81D.idx" />
          <UseForReprogramming>true</UseForReprogramming>
          <Type>Data</Type>
          <LengthOfLogicalBlockIndex>1</LengthOfLogicalBlockIndex>
          <LogicalBlockIndex>0</LogicalBlockIndex>
          <Checksum>
            <ChecksumPath AbsolutePath="" RelativePath="" />
          </Checksum>
          <Signature />
          <Segment>
            <LengthOfMemorySize>3</LengthOfMemorySize>
            <LengthOfMemoryAddress>3</LengthOfMemoryAddress>
            <MemoryAddress>0</MemoryAddress>
          </Segment>
          <CompressionEncryption>
            <DataFormatdentifier>0</DataFormatdentifier>
            <CompressionType>Undefined</CompressionType>
          </CompressionEncryption>
        </Datablock>
      </NativeFlashData>
    </FlashData>
    <Procedure>
      <SecurityAccess>
        <SecurityLevel>1</SecurityLevel>
        <KeyProxy>
          <SeedKeyDLL AbsolutePath="C:\Codice\EISB8C\Appl\EISB8C_AM_01_UDS\DiagKit\CAN\I5F81D\secAcc\EISB_SecurityAccess.dll" RelativePath="secAcc\EISB_SecurityAccess.dll" />
        </KeyProxy>
        <Activated>true</Activated>
      </SecurityAccess>
      <Param>
        <Name>TesterSerialNbr</Name>
        <Value>VN1630A</Value>
        <Unit />
      </Param>
    </Procedure>
    <FlashProcessControl>
      <BaudrateSwitchActivated>false</BaudrateSwitchActivated>
      <StayInBoot>
        <ForceBootMode>false</ForceBootMode>
      </StayInBoot>
      <SecondaryFlexRayConnectionActivated>false</SecondaryFlexRayConnectionActivated>
      <AllowIncompatibilityFlashware>true</AllowIncompatibilityFlashware>
    </FlashProcessControl>
    <CustomActions>
      <Ecus>
        <Ecu>
          <EcuQualifier />
          <VariantQualifier />
          <AutomaticVariantIdentificationActivated>false</AutomaticVariantIdentificationActivated>
        </Ecu>
      </Ecus>
      <Assemblies />
      <Activated>false</Activated>
      <ForcePostCustomAction>false</ForcePostCustomAction>
    </CustomActions>
  </Configuration>
</EcuFlashProject>