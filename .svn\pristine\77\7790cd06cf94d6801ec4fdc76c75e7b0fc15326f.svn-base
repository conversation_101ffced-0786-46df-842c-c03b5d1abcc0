/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  CMUCheckSM_MCU_r_xx.c
**  Created on      :  07-Feb-2022 15:39:00
**  Original author :  Mocci A
******************************************************************************/
#ifdef _BUILD_SAFETYMNGR_CMUCHECK_

#ifndef _BUILD_SAFETYMNGR_
#error CMU checks enabled without _BUILD_SAFETYMNGR_ macro enabled
#endif /*  _BUILD_DIAGCANMGM_ */

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "CMUCheckSM_MCU_r_xx.h"

/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
uint32_T g_CMUFccuStatus;
uint32_T g_CMUProcessor;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_44
**
**   Description:
**    This function implements requirement SM_MCU_3_44
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CMUCheck_SM_MCU_3_44(void)
{
#if ( (STD_ON == SAFE_CMU_CHECK)  && (STD_ON == SAFE_FCCU_CHECK))
    uint32_T proccessError = 0;

    /* check if FCCU has already a fault or alarm */
    MaskRegister32(FCCU_CTRL_R, FCCU_CTRL_OP3);
    /* wait until run state has finished */
    proccessError = WaitForFccuOperationEnd();
    if ((proccessError == 0) &&
       ((tFccuStatus)(ReadRegister32(FCCU_STAT_R) & FCCU_STATUS_MASK) == eFCCU_NORMAL))
    {
        /* no error before starting checks */
        uint32_T index;
        for( index = 0; (index < TOTAL_NO_OF_CMUS) && (proccessError == 0); index++)
        {
            switch(index)
            {
                case 0:
                    proccessError = InjectFakeFault(CMU0_BASEADDR, FCCU_FI_MASK_CMU_0_PLL);
                    break;
                case 1:
                    proccessError = InjectFakeFault(CMU1_BASEADDR, FCCU_FI_MASK_CMU_PLATFORM);
                    break;
                case 2:
                    proccessError = InjectFakeFault(CMU2_BASEADDR, FCCU_FI_MASK_CMU_PLATFORM);
                    break;
                case 3:
                    proccessError = InjectFakeFault(CMU3_BASEADDR, FCCU_FI_MASK_CMU_PLATFORM);
                    break;
                case 6:
                    proccessError = InjectFakeFault(CMU6_BASEADDR, FCCU_FI_MASK_CMU_OTHER);
                    break;
                case 11:
                    proccessError = InjectFakeFault(CMU11_BASEADDR, FCCU_FI_MASK_CMU_PLATFORM);
                    break;
                default:
                    break;
            }

            if (proccessError != 0)
            {
                proccessError |= index << CMU_ID_2_HIGH_WORD;
            }
        }
    }

    ClearInjectedErrors(FCCU_FI_MASK_CMUS);

    if (proccessError != 0)
    {
        /* not expected error */
        g_CMUProcessor = proccessError;
        SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuNotExpectedErrorCb, FALSE);
    }
#endif
}

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_45
**
**   Description:
**    This function implements requirement SM_MCU_3_45
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CMUCheck_SM_MCU_3_45(void)
{
#if ( STD_ON == SAFE_CMU_CHECK )
    uint32_T index;
    uint32_T compare;

    /** The value 2 for the threshold calculation is given in RM0421 chapter 27.5.3
     *  CLKMN1 supervisor as well as the complete formula. The reference value
     *  must be programmed considering the following factors:
     *  - 2 cycles of fCLKMN1 which is the built-in tolerance of the monitor implementation
     *  - Frequency variation % of fCLKMT0_RMN across PVT
     */
    uint32_T calculatedLowerTreshold;   /* (LFREFActual = (LFREFIdeal - 2) x 0.95) */
    uint32_T calculatedUpperTreshold;   /* (HFREFActual = (HFREFIdeal + 2) x 1.05) */

    /* The potential CMU thresholds inexactness shall be taken into
       account for all clocks being monitored. */
    for( index = 0; index < TOTAL_NO_OF_CMUS; index++)
    {
        uint32_T cmuBaseAddr;
        switch(index)
        {
            case 0:
                cmuBaseAddr = CMU0_BASEADDR;
                calculatedLowerTreshold = ((CMU_THRESHOLD_CLKMN1_400_CLKMT0_RMN_16 - 2) * ACCURACY_MINUS  / PERCENTAGE_DIVIDER);
                calculatedUpperTreshold = ((CMU_THRESHOLD_CLKMN1_400_CLKMT0_RMN_16 + 2) * ACCURACY_PLUS  / PERCENTAGE_DIVIDER);
                break;
            case 1:
                cmuBaseAddr = CMU1_BASEADDR;
                calculatedLowerTreshold = ((CMU_THRESHOLD_CLKMN1_200_CLKMT0_RMN_16 - 2) * ACCURACY_MINUS  / PERCENTAGE_DIVIDER);
                calculatedUpperTreshold = ((CMU_THRESHOLD_CLKMN1_200_CLKMT0_RMN_16 + 2) * ACCURACY_PLUS  / PERCENTAGE_DIVIDER);
                break;
            case 2:
                cmuBaseAddr = CMU2_BASEADDR;
                calculatedLowerTreshold = ((CMU_THRESHOLD_CLKMN1_100_CLKMT0_RMN_16 - 2) * ACCURACY_MINUS  / PERCENTAGE_DIVIDER);
                calculatedUpperTreshold = ((CMU_THRESHOLD_CLKMN1_100_CLKMT0_RMN_16 + 2) * ACCURACY_PLUS  / PERCENTAGE_DIVIDER);
                break;
            case 3:
                cmuBaseAddr = CMU3_BASEADDR;
                calculatedLowerTreshold = ((CMU_THRESHOLD_CLKMN1_50_CLKMT0_RMN_16 - 2) * ACCURACY_MINUS  / PERCENTAGE_DIVIDER);
                calculatedUpperTreshold = ((CMU_THRESHOLD_CLKMN1_50_CLKMT0_RMN_16 + 2) * ACCURACY_PLUS  / PERCENTAGE_DIVIDER);
                break;
            case 6:
                cmuBaseAddr = CMU6_BASEADDR;
                calculatedLowerTreshold = ((CMU_THRESHOLD_CLKMN1_16_CLKMT0_RMN_16 - 2) * ACCURACY_MINUS  / PERCENTAGE_DIVIDER);
                calculatedUpperTreshold = ((CMU_THRESHOLD_CLKMN1_16_CLKMT0_RMN_16 + 2) * ACCURACY_PLUS  / PERCENTAGE_DIVIDER);
                break;
            case 11:
                cmuBaseAddr = CMU11_BASEADDR;
                calculatedLowerTreshold = ((CMU_THRESHOLD_CLKMN1_200_CLKMT0_RMN_16 - 2) * ACCURACY_MINUS  / PERCENTAGE_DIVIDER);
                calculatedUpperTreshold = ((CMU_THRESHOLD_CLKMN1_200_CLKMT0_RMN_16 + 2) * ACCURACY_PLUS  / PERCENTAGE_DIVIDER);
                break;
            default:
                cmuBaseAddr = ZERO;
                break;
        }
        if (cmuBaseAddr !=ZERO)
        {
            /* CMU Low Frequency Reference Register CLKMN1 */
            compare = (calculatedLowerTreshold >> CMU_LFREF_SHIFT_TO_COMFIG) & CMU_LFREFR_MASK;
            if (compare > ReadRegister32(cmuBaseAddr + CMU_LFREFR))
            {
                /* diff */
                SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuLfRefValue_CMU_LFREFR, FALSE);
            }
            /* CMU High Frequency Reference Register CLKMN1 */
            compare = (calculatedUpperTreshold >> CMU_HFREF_SHIFT_TO_COMFIG) & CMU_HFREFR_MASK;
            if (compare < ReadRegister32(cmuBaseAddr + CMU_HFREFR))
            {
                /* diff */
                SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuHfRefValue, FALSE);
            }
        }
    }
#endif
}

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_46
**
**   Description:
**    This function implements requirement SM_MCU_3_46
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CMUCheck_SM_MCU_3_46(void)
{
#if ( (STD_ON == SAFE_CMU_CHECK)  && (STD_ON == SAFE_FCCU_CHECK))

    uint32_T proccessError = 1;

    /* set the respective input channels of the FCCU */
    /* the FCCU_TRANS register is used for unlocking configuration by writing BCh */
    WriteRegister32(FCCU_TRANS_LOCK_R, FCCU_TRANS_UNLOCK_KEY);
    /* Clear corresponding flags */
    WriteRegister32(FCCU_RFK_R, FCCU_RECOVERABLE_FAULT_KEY);
    WriteRegister32(FCCU_RF_S1_R, FCCU_FI_MASK_CMUS);
    /* wait for the completion of the CLEAR FLAGS operation */
    if (WaitForFccuOperationEnd() == ZERO)
    {
        /* FCCU moves in CONFIG state */
        WriteRegister32(FCCU_CTRLK_R, FCCU_TRANS_TO_CONFIG);
        WriteRegister32(FCCU_CTRL_R, FCCU_CTRL_OP1);
        /* wait for the completion of the transition to config state */
        if (WaitForFccuOperationEnd() == ZERO)
        {
            /* set fault configuration to SW */
            MaskRegister32(FCCU_RF_CFG1_R, FCCU_FI_MASK_CMUS | FCCU_FI_MASK_CMU_0_OSC);
            /* activate fault sources */
            MaskRegister32(FCCU_RF_E1_R,   FCCU_FI_MASK_CMUS | FCCU_FI_MASK_CMU_0_OSC);
            /* set fault leads to an alarm first */
            MaskRegister32(FCCU_RF_TOE1_R, FCCU_FI_MASK_CMUS | FCCU_FI_MASK_CMU_0_OSC);
            /* leave config state, FCCU back to normal state */
            WriteRegister32(FCCU_CTRLK_R, FCCU_TRANS_TO_NORMAL);
            WriteRegister32(FCCU_CTRL_R, FCCU_CTRL_OP2);
            /* wait for the completion of the transition to normal state */
            if (WaitForFccuOperationEnd() == ZERO)
            {
                proccessError = ZERO;
            }
        }
    }
    if (proccessError != 0)
    {
        /* timeout in waiting for FCCU operation finished -> HW fault */
        SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndFccuConfigFailed, FALSE);
    }
#endif
}

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_47
**
**   Description:
**    This function implements requirement SM_MCU_3_47
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CMUCheck_SM_MCU_3_47(void)
{
#if ( STD_ON == SAFE_CMU_CHECK )
    const Mcu_ConfigType* pInitConfig = &MCU_INIT_CONFIG_PC;

    /* CMU0 */
    CMUCheck_SM_MCU_3_47_Sub(CMU0_BASEADDR, pInitConfig->Mcu_ClockConfig->Clk_Monitor);
    /* CMU1 */
    CMUCheck_SM_MCU_3_47_Sub(CMU1_BASEADDR, pInitConfig->Mcu_ModeConfig->Mcu_Cmu1Configuration);
    /* CMU2 */
    CMUCheck_SM_MCU_3_47_Sub(CMU2_BASEADDR, pInitConfig->Mcu_ModeConfig->Mcu_Cmu2Configuration);
    /* CMU3 */
    CMUCheck_SM_MCU_3_47_Sub(CMU3_BASEADDR, pInitConfig->Mcu_ModeConfig->Mcu_Cmu3Configuration);
    /* CMU6 */
    CMUCheck_SM_MCU_3_47_Sub(CMU6_BASEADDR, pInitConfig->Mcu_ClockConfig->Clk_Monitor6);
    /* CMU11 */
    CMUCheck_SM_MCU_3_47_Sub(CMU11_BASEADDR, pInitConfig->Mcu_ModeConfig->Mcu_Cmu11Configuration);
#endif
}

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_48
**
**   Description:
**    This function implements requirement SM_MCU_3_48
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CMUCheck_SM_MCU_3_48(void)
{
#if ( STD_ON == SAFE_CMU_CHECK )
    uint32_T regCSR;
    uint32_T regFDR;

    /* Load the Measurement Duration Register with the desired/proper value */
    WriteRegister32(CMU0_BASEADDR + CMU_MSR, CMU0_MSR_MD);

    /* Start frequency measurement of the frequency of clock sources CLKMT0_RMN
      with CLKMN0_RMT as the reference clock. */
    WriteRegister32(CMU0_BASEADDR + CMU_CSR, (ReadRegister32(CMU0_BASEADDR + CMU_CSR) | CMU_CSR_SFM));
    /* wait until measure is ready */
    do
    {
        regCSR = ReadRegister32(CMU0_BASEADDR + CMU_CSR);

    } while ((regCSR & CMU_CSR_SFM) == CMU_CSR_SFM);

    /* read measured frequency */
    regFDR = ReadRegister32(CMU0_BASEADDR + CMU_FDR);

    /* check if measured frequency corresponds to the configured frequency */
    if ((regFDR > CMU0_FDR_FD_MAX) || (regFDR < CMU0_FDR_FD_MIN))
    {
        SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuFrequencyErrorCb, FALSE);
    }
#endif
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : Delay
**
**   Description:
**    This function implements a delay
**
**   Parameters :
**    uint32_T u32Delay
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void Delay(uint32_T u32Delay)
{
    uint32_T i;
    for (i = 0; i < u32Delay; i++)
    {
        asm("nop");
    }
}

/******************************************************************************
**   Function    : WaitForFccuOperationEnd
**
**   Description:
**    This function waits until run state has finished -> leaves RUN mode
**
**   Parameters :
**    void
**
**   Returns:
**     uint32_T returnCode (0 if operation ends successful 1 if time out occurred)
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static uint32_T WaitForFccuOperationEnd(void)
{
    uint32_T loopCount = ZERO;

    while (((ReadRegister32(FCCU_CTRL_R) & FCCU_CTRL_OPS_MASK) != FCCU_CTRL_OPS_SUCCESS) &&
          ((loopCount) < FCCU_TIMEOUT_MAX_LOOP))
    {
      loopCount++;
    }
    return (loopCount >= FCCU_TIMEOUT_MAX_LOOP ? 1 : 0);
}

/******************************************************************************
**   Function    : InjectFakeFault
**
**   Description:
**    This function injects a fake fault by changing the thresholds of the 
**    CLKMN0_RMT and CLKMN1 supervisors
**
**   Parameters :
**    uint32_T cmuBaseAddr, 
**    uint32_T fccuFiMask
**
**   Returns:
**     uint32_T returnCode (0 if operation ends successful 1 if time out occurred)
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static uint32_T InjectFakeFault(uint32_T cmuBaseAddr, uint32_T fccuFiMask)
{
    uint32_T regFccuRFS1;
    tFccuStatus fccuStatus;
    uint32_T proccessError = 1;
    /* save the configured values of LFREF and HFREF to restore them after test */
    uint32_T lfrefConfigured = ReadRegister32(cmuBaseAddr + CMU_LFREFR);
    uint32_T hfrefConfigured = ReadRegister32(cmuBaseAddr + CMU_HFREFR);
    uint32_T cmsFlag = (ReadRegister32(cmuBaseAddr + CMU_CSR) & CMU_CSR_CME);

    /* CLKMN1 monitor is disabled before changing the thresholds */
    ClearMaskRegister32(cmuBaseAddr + CMU_CSR, CMU_CSR_CME);
    /* Set the low frequency reference value.
       The reference value is calculate by: (LFREF/16) x (fCLKMT0_RMN/4). */
    WriteRegister32(cmuBaseAddr + CMU_LFREFR, CMU_LFREF_FAKE_VALUE);
    /* Set the high frequency reference value.
       The reference value is calculate by: (HFREF/16) x (fCLKMT0_RMN/4). */
    WriteRegister32(cmuBaseAddr + CMU_HFREFR, CMU_HFREF_FAKE_VALUE);
    /* CLKMN1 monitor is enabled */
    MaskRegister32(cmuBaseAddr + CMU_CSR, CMU_CSR_CME);

    /* wait for alarm 5us */
    Delay(1200);

    /* retrieve the FCCU state, should be fault or alarm depending of the
       FCCU configuration -> set operation mode to retrieve state (OP3)
    */
    MaskRegister32(FCCU_CTRL_R, FCCU_CTRL_OP3);
    /* wait until run state has finished */
    if (WaitForFccuOperationEnd() == 0)
    {
        proccessError = 0;
        /* no time out */
        fccuStatus = (tFccuStatus)(ReadRegister32(FCCU_STAT_R) & FCCU_STATUS_MASK);
        if (fccuStatus != eFCCU_ALARM)
        {
            /* we excpect FCCU state Alarm */
            proccessError |= 0x2;
        }
        /* set operation mode to retrieve RF state (OP10)*/
        MaskRegister32(FCCU_CTRL_R, FCCU_CTRL_OP10);
        /* wait until run state has finished */
        if (WaitForFccuOperationEnd() == 0)
        {
            regFccuRFS1 = ReadRegister32(FCCU_RF_S1_R);
            /* check if the expect flag is set */
            if ((regFccuRFS1 & FCCU_FI_MASK_CMUS) != fccuFiMask)
            {
                proccessError |= 0x4;
            }
            /* wait before clearing 100ns? */
            Delay(10);

            /* clear CMU status */
            ClearMaskRegister32(cmuBaseAddr + CMU_CSR, CMU_CSR_CME);
            /* clear FHHI and FLLI */
            WriteRegister32(cmuBaseAddr + CMU_ISR, CMU_ISR_FHHI | CMU_ISR_FLLI);

            /* clear the alarm/fault indication */
            WriteRegister32(FCCU_RFK_R, FCCU_RECOVERABLE_FAULT_KEY);
            WriteRegister32(FCCU_RF_S1_R, fccuFiMask);   /* this starts OP12 */
            if (WaitForFccuOperationEnd() == 0)
            {
                /* wait for 20µs? */
                /* check if there is still a fault or alarm*/
                Delay(500);
                /* check if there is still a fault or alarm*/
                MaskRegister32(FCCU_CTRL_R, FCCU_CTRL_OP3);
                /* wait until run state has finished */
                if (WaitForFccuOperationEnd() == 0)
                {
                    fccuStatus = (tFccuStatus)(ReadRegister32(FCCU_STAT_R) & FCCU_STATUS_MASK);
                    if (fccuStatus != eFCCU_NORMAL)
                    {
                        /* not expected error, there is still a fault or alarm active */
                        proccessError |= 0x8;
                    }
                }
            }
        }
    }

    g_CMUFccuStatus = fccuStatus;

    /* restore the previous saved values of LFREF and HFREF */
    WriteRegister32(cmuBaseAddr + CMU_LFREFR, lfrefConfigured);
    WriteRegister32(cmuBaseAddr + CMU_HFREFR, hfrefConfigured);
    MaskRegister32(cmuBaseAddr + CMU_CSR, cmsFlag);

    return proccessError;
}

/******************************************************************************
**   Function    : ClearInjectedErrors
**
**   Description:
**    This function clears the Errors injected
**
**   Parameters :
**    uint32_T fccuFiMask
**
**   Returns:
**     void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void ClearInjectedErrors( uint32_T fccuFiMask)
{
    /* retrieve the FCCU state, should be fault or alarm depending of the
       FCCU configuration -> set operation mode to retrieve state (OP3)
    */
    MaskRegister32(FCCU_CTRL_R, FCCU_CTRL_OP3);
    /* wait until run state has finished */
    if (WaitForFccuOperationEnd() == 0)
    {
        /* set operation mode to retrieve RF state (OP10)*/
        MaskRegister32(FCCU_CTRL_R, FCCU_CTRL_OP10);
        /* wait until run state has finished */
        if (WaitForFccuOperationEnd() == 0)
        {
            /* clear the alarm/fault indication */
            WriteRegister32(FCCU_RFK_R, FCCU_RECOVERABLE_FAULT_KEY);
            WriteRegister32(FCCU_RF_S1_R, fccuFiMask);   /* this starts OP12 */
            if (WaitForFccuOperationEnd() == 0)
            {
                /* wait for 20µs? */
                /* check if there is still a fault or alarm*/
                Delay(200);
                /* check if there is still a fault or alarm*/
                MaskRegister32(FCCU_CTRL_R, FCCU_CTRL_OP3);
                /* wait until run state has finished */
                WaitForFccuOperationEnd();
            }
        }
    }
}

/******************************************************************************
**   Function    : CMUCheck_SM_MCU_3_47_Sub
**
**   Description:
**    This function implements requirement SM_MCU_3_47 subroutine
**
**   Parameters :
**    uint32_T baseAddress, 
**    uint32_T configValue
**
**   Returns:
**     void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void CMUCheck_SM_MCU_3_47_Sub(uint32_T baseAddress, uint32_T configValue)
{
    /* check the configuration of the CMU*/
    volatile uint32_T compare;

    /* CLKMN1 monitor enable */
    compare = (configValue >> CMU_CSR_CME_SHIFT_TO_COMFIG) & CMU_CSR_CME;
    if (compare != (ReadRegister32(baseAddress + CMU_CSR) & CMU_CSR_CME))
    {
        /* diff */
        if ((ReadRegister32(baseAddress + CMU_CSR) & CMU_CSR_CME) == CMU_CSR_CME)
        {
            /* monitoring in config off, but in register on */
            SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuConfigMismatch_CMU_CSR_CME, FALSE);
        }
        else
        {
            /* monitoring in config on, but in register off */
            SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuConfigMismatch_CMU_CSR, FALSE);
        }
    }
    /* CLKMT0_RMN division factor CMU0 only */
    if (baseAddress == CMU0_BASEADDR)
    {
        compare = (configValue >> CMU_CSR_RCDIV_SHIFT_TO_COMFIG) & CMU_CSR_RCDIV_MASK;
        if (compare != (ReadRegister32(baseAddress + CMU_CSR) & CMU_CSR_RCDIV_MASK))
        {
            /* diff */
            SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuConfigMismatch_CMU_CSR_RCDIV, FALSE);
        }
    }
    /* CMU Low Frequency Reference Register CLKMN1 */
    compare = (configValue >> CMU_LFREF_SHIFT_TO_COMFIG) & CMU_LFREFR_MASK;
    if (compare != ReadRegister32(baseAddress + CMU_LFREFR))
    {
        /* diff */
        SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuConfigMismatch_CMU_LFREFR, FALSE);
    }
    /* CMU High Frequency Reference Register CLKMN1 */
    compare = (configValue >> CMU_HFREF_SHIFT_TO_COMFIG) & CMU_HFREFR_MASK;
    if (compare != ReadRegister32(baseAddress + CMU_HFREFR))
    {
        /* diff */
        SafetyMngr_ReportError(SAFE_ERR_CMU_ErrorIndCmuConfigMismatch_CMU_HFREFR, FALSE);
    }
}

#endif // _BUILD_SAFETYMNGR_CMUCHECK_
/****************************************************************************
 ****************************************************************************/

