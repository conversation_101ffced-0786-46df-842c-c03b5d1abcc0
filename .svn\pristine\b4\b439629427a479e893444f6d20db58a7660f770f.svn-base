/***************************************************************
***** Compiler Designation for Assembler Options ***************
***** Set one compiler to "1" from the four choices below ******/
#ifdef __ghs__
    .equ __GRNHS__,  1  // Designation for the Green Hills compiler
    .equ __PEGNU__,  0  // Designation for the P&E Micro Gnu compiler
    .equ __DIABCC__, 0  // Designation for the Wind River compiler
    .equ __CWWRKS__, 0  // Designation for the Metrowerks CodeWarrior compiler

    .equ FLSH_RUN,         1  // Set to (1) for code in Flash
    .equ FSYS_80,          1  // set to (1) to reduce the FSYS to 80Mhz (0) 128 Mhz
    .equ CACHE_ENABLED     0  
#else
    .equ __GRNHS__,  0  // Designation for the Green Hills compiler
    .equ __PEGNU__,  0  // Designation for the P&E Micro Gnu compiler
    .equ __DIABCC__, 0  // Designation for the Wind River compiler
    .equ __CWWRKS__, 1  // Designation for the Metrowerks CodeWarrior compiler
#endif

#include "..\\..\\MINIBOOT\\src\\__start.s"




