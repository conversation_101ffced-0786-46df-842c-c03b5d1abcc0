/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    SPC574K_GTM_IP.h
 * @brief   SPC574K GTM_IP.
 *
 * @addtogroup GTM
 * @{
 */

#ifndef _SPC574K_GTM_IP_H_
#define _SPC574K_GTM_IP_H_

#include "gtm_common.h"
#if 0
#define SPC5_HAS_GTM_TBU                    TRUE
#define SPC5_GTM_TBU_CH_NUM                 3UL

#if SPC5_GTM_TBU_CH_NUM > 1UL
#define SPC5_GTM_TBU_HAS_CH1 TRUE
#else
#define SPC5_GTM_TBU_HAS_CH1 FALSE
#endif

#if SPC5_GTM_TBU_CH_NUM > 2UL
#define SPC5_GTM_TBU_HAS_CH2 TRUE
#else
#define SPC5_GTM_TBU_HAS_CH2 FALSE
#endif

#if SPC5_GTM_TBU_CH_NUM > 3UL
#define SPC5_GTM_TBU_HAS_CH3 TRUE
#else
#define SPC5_GTM_TBU_HAS_CH3 FALSE
#endif
#endif
/**
 * @brief   GTM TOM IP
 *
 * @{
 */
#define SPC5_HAS_GTM_TOM                    TRUE
#define SPC5_GTM_TOM_NUM                    2UL
#define SPC5_GTM_TOMX_CH_NUM                16UL
/** @} */
/**
 * @brief   GTM TIM IP
 *
 * @{
 */
#define SPC5_HAS_GTM_TIM                    TRUE
#define SPC5_GTM_TIM_NUM                    3UL
#define SPC5_GTM_TIMX_CH_NUM                8UL
/** @} */
/**
 * @brief   GTM ATOM IP
 *
 * @{
 */
#define SPC5_HAS_GTM_ATOM                   TRUE
#define SPC5_GTM_ATOM_NUM                   4UL
#define SPC5_GTM_ATOMX_CH_NUM               8UL
/** @} */
#endif
/** @} */
