/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TIMING
**  Filename        :  Timing.h
**  Created on      :  18-giu-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/

#ifndef _TIMING_H_
#define _TIMING_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Timing_out.h"
#include "sys.h"
#include "pwrmgm_out.h"
#include "Pit_out.h"
#include "SyncMgm_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define TIMING_GetTimeVal()  (PIT_GetTimeVal())

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST uint8_T ENTESTTIMING;
extern CALQUAL CALQUAL_POST uint8_T ENTESTISRTIMING;

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/* None */

#endif // _TIMING_H_

/****************************************************************************
 ****************************************************************************/

