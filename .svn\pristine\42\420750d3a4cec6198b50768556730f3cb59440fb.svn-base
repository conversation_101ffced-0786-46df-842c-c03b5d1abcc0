/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           CombAvgFFS.c
 **  File Creation Date: 21-Sep-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         CombAvgFFS
 **  Model Description:  The aim of this model is to filter the "front flame speed" indicator (signal FFS calculaated by IonIntMgm).
   To perform the filtering of FFS, A median and then a Butterworth filter are applied  obtaining the filtered value AvgFFS.
 **  Model Version:      1.1055
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Tue Sep 21 13:53:57 2021
 **
 **  Last Saved Modification:  RoccaG - Tue Sep 21 13:53:22 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "CombAvgFFS_out.h"
#include "CombAvgFFS_private.h"
#include "mul_ssu32_loSR.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BIT_1_OR_2                     3U                        /* Referenced by: '<S25>/NO_MISF1' */

/* Bit mask for bit-and on bit one and two */
#define BKLOADCTRLINSEL_dim            4U                        /* Referenced by:
                                                                  * '<S26>/NO_MISF3'
                                                                  * '<S26>/NO_MISF5'
                                                                  */

/* Size of breakpoint BKLOADCTRLINSEL -1 */
#define BKRPMCTRLINSEL_dim             7U                        /* Referenced by:
                                                                  * '<S26>/NO_MISF1'
                                                                  * '<S26>/NO_MISF4'
                                                                  */

/* Size of breakpoint BKRPMCTRLINSEL -1 */
#define BKRPMLAMFIL_dim                7U                        /* Referenced by:
                                                                  * '<S12>/BKRPMLAMFIL_dim'
                                                                  * '<S12>/BKRPMLAMFIL_dim1'
                                                                  */

/* Size of breakpoint BKRPMLAMFIL -1 */
#define D1LAM_POLY_P1                  -4615                     /* Referenced by: '<S13>/D1LAM_POLY_P1' */

/* Coefficient D1_1 for butterworth filter */
#define D1LAM_POLY_P2                  18496                     /* Referenced by: '<S13>/D1LAM_POLY_P2' */

/* Coefficient D1_2 for butterworth filter */
#define D1LAM_POLY_P3                  -8393641                  /* Referenced by: '<S13>/D1LAM_POLY_P3' */

/* Coefficient D1_3 for butterworth filter */
#define D2LAM_POLY_P1                  22069                     /* Referenced by: '<S13>/D2LAM_POLY_P1' */

/* Coefficient D2_1 for butterworth filter */
#define D2LAM_POLY_P2                  -16295                    /* Referenced by: '<S13>/D2LAM_POLY_P2' */

/* Coefficient D2_2 for butterworth filter */
#define D2LAM_POLY_P3                  4148586                   /* Referenced by: '<S13>/D2LAM_POLY_P3' */

/* Coefficient D2_3 for butterworth filter */
#define ID_VER_COMBAVGFFS_DEF          11053U                    /* Referenced by: '<S2>/Constant14' */

/* Model Version. */
#define MAX_FFS_VALUE                  134215680U                /* Referenced by: '<S26>/NO_MISF2' */

/* Maximum value for front of flame speed */
#define MAX_FFS_VALUE_HR               1073725440                /* Referenced by: '<S8>/NO_MISF2' */

/* Maximum value for front of flame speed with high rewolution */
#define MAX_MISF_COUNTER               255                       /* Referenced by: '<S25>/NO_MISF3' */

/* Maximum value for misfire counter */
#define MAX_N0_FFS                     2516582400U               /* Referenced by:
                                                                  * '<S10>/NO_MISF1'
                                                                  * '<S10>/NO_MISF2'
                                                                  */

/* Maximum value for In_x_N0_FFS */
#define MAX_N0_RESCALE                 111411U                   /* Referenced by: '<S10>/NO_MISF3' */

/* Maximum value for In_x_N0 and In_Sum rescale when there is a change in cut frequency */
#define MEDIAN_FACTOR                  2U                        /* Referenced by: '<S25>/One2' */

/* Factor used to convert from median length to median index */
#define MISF_COUNT_DEC                 -1                        /* Referenced by: '<S25>/NO_MISF5' */

/* Decrement step for misfire counter */
#define MISF_COUNT_INC                 1                         /* Referenced by: '<S25>/NO_MISF4' */

/* Increment step for misfire counter */
#define N0LAM_POLY                     1                         /* Referenced by: '<S13>/four' */

/* Coefficient N0_1 for butterworth filter */
#define N0_FFS_COEFF                   2U                        /* Referenced by: '<S8>/four' */

/* Gain factor for In_x_N0_FFS */
#define RPM_2_PERC                     85U                       /* Referenced by: '<S11>/RPM_2_PERC' */

/* Conversion from rpm to percentage */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_COMBAVGFFS_

/* Add a check for each important define */
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinders!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint16_T VettSort[7];           /* '<S1>/Median' */

/* Sorted vector, used for median on FFS. */
static uint8_T circularIndex[8];       /* '<S1>/Median' */

/* Circular index used for median on FFS */
static uint16_T i_out;                 /* '<S12>/Hysteresis_Check' */

/* Selected index for cut off frequency selection. */
static uint16_T loadSteadyInput;       /* '<S15>/Memory1' */

/* Old engine load for stability algorithm */
static uint16_T loadSteadyTimer;       /* '<S15>/Memory' */

/* Stability timer for engine load */
static uint16_T medianBuffer[56];      /* '<S1>/Median' */

/* Buffer for median evaluation on FFS  */
static boolean_T medianInitialized;    /* '<S1>/Median' */

/* Median has been initialized. */
static uint16_T rpmSteadyInput;        /* '<S14>/Memory1' */

/* Old engine speed for stability algorithm */
static uint16_T rpmSteadyTimer;        /* '<S14>/Memory' */

/* Stability timer for engine speed */
static uint8_T sstab_load_lam;         /* '<S11>/Unit Delay1' */

/* Stability state for engine load */
static uint8_T sstab_rpm_lam;          /* '<S11>/Unit Delay' */

/* Stability state for engine speed */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADCTRLINSEL[5] = { 1280U, 2560U,
  3840U, 6400U, 10240U } ;             /* Referenced by:
                                        * '<S26>/LENBUFMED2'
                                        * '<S26>/LENBUFMED5'
                                        */

/* Breakpoints of load for FFS/INTION selection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMCTRLINSEL[8] = { 800U, 950U,
  1050U, 1100U, 2000U, 3000U, 3500U, 4000U } ;/* Referenced by:
                                               * '<S26>/LENBUFMED1'
                                               * '<S26>/LENBUFMED4'
                                               */

/* Breakpoints of Rpm for FFS/INTION selection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMLAMFIL[8] = { 800U, 950U, 1050U,
  1100U, 2000U, 3000U, 3500U, 4000U } ;/* Referenced by:
                                        * '<S12>/BKRPMLAMFIL'
                                        * '<S12>/BKRPMLAMFIL1'
                                        */

/* Breakpoints of Rpm for FFS/lambda filtering */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENFILTLAMFREEZE = 1U;
                                    /* Referenced by: '<S25>/ENFILTLAMFREEZE' */

/* Enable FiltLamFreeze */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T FREQNORMLAMTR = 41;
                                      /* Referenced by: '<S12>/FREQNORMLAMTR' */

/* Normalized cutoff frequency for FFS/IntIon filtering during transient */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T FREQRATEMAX = 20;/* Referenced by: '<S13>/FREQRATEMAX' */

/* Maximum variation rate for FreqNorm */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T FREQRATEMIN = -20;/* Referenced by: '<S13>/FREQRATEMIN' */

/* Minimum variation rate for FreqNorm */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T LENBUFMED = 5U;/* Referenced by:
                                                         * '<S1>/Median'
                                                         * '<S25>/LENBUFMED'
                                                         */

/* FFS median length */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T RPMHYSTFN = 200U;/* Referenced by: '<S12>/RPMHYSTFN' */

/* Rpm hysteresis for FreqNorm computation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TBCOMBCTRLINSEL[40] = { 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U } ;/* Referenced by: '<S26>/LENBUFMED3' */

/* Select Input source 0 = FFS, 1 = IntIon */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBINTION2FFS[40] = { 98U, 98U, 98U,
  98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U,
  98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U, 98U,
  98U, 98U, 98U, 98U, 98U } ;          /* Referenced by: '<S26>/LENBUFMED' */

/* Conversion factor from IntIon to FFS (Gain) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TDCSTABLOADLAMTR = 60U;
                                   /* Referenced by: '<S11>/TDCSTABLOADLAMTR' */

/* Number of TDC to declare Load stability */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TDCSTABRPMLAMTR = 90U;
                                    /* Referenced by: '<S11>/TDCSTABRPMLAMTR' */

/* Number of TDC to declare Rpm stability */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABLOADLAMTR = 640U;
                                   /* Referenced by: '<S11>/THRSTABLOADLAMTR' */

/* Stability range for Load */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABRPMLAMTR = 640U;
                                    /* Referenced by: '<S11>/THRSTABRPMLAMTR' */

/* Stability range for Rpm */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T VTENFILBUTTER[8] = { 1U, 1U, 1U, 1U, 1U,
  1U, 1U, 1U } ;                      /* Referenced by: '<S12>/VTENFILBUTTER' */

/* Butterworth filtering enable (disabled == by-passed) */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTFREQNORMCUT[8] = { 35, 35, 35, 35, 32,
  30, 29, 27 } ;                      /* Referenced by: '<S12>/VTFREQNORMCUT' */

/* Normalized cutoff frequency for Butterworth filter */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T AvgFFS[8];                    /* '<S3>/Merge2' */

/* Average FFS */
uint8_T FiltLamFreeze;                 /* '<S3>/Merge' */

/* Filter Lambda Freeze */
uint8_T FlgSteadyStateFFS;             /* '<S3>/Merge1' */

/* Steady state flag for cylinder FFS average */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T Avg_HR_FFS[8];/* '<S3>/Merge12' */

/* Average Filtered FFS */
STATIC_TEST_POINT uint32_T Avg_HR_old_FFS[8];/* '<S3>/Merge13' */

/* Average Filtered FFS[n -1] */
STATIC_TEST_POINT int16_T D1LamFil;    /* '<S3>/Merge5' */

/* D1 Filter Parameter */
STATIC_TEST_POINT int16_T D2LamFil;    /* '<S3>/Merge6' */

/* D2 Filter Parameter */
STATIC_TEST_POINT uint8_T EnFilButter; /* '<S3>/Merge7' */

/* Enable Butterworth Filter */
STATIC_TEST_POINT uint16_T FFSMedian;  /* '<S3>/Merge3' */

/* Median FFS */
STATIC_TEST_POINT uint8_T FiltParReset;/* '<S3>/Merge4' */

/* Filter Parameter Reset */
STATIC_TEST_POINT int16_T FreqNorm;    /* '<S3>/Merge8' */

/* Rate freq */
STATIC_TEST_POINT uint32_T IdVer_CombAvgFFS;/* '<S2>/Constant14' */

/* Model Version */
STATIC_TEST_POINT uint32_T InSum_FFS[8];/* '<S3>/Merge16' */

/* Filter Input sum */
STATIC_TEST_POINT uint32_T In_x_N0_FFS[8];/* '<S3>/Merge15' */

/* N0 x FFS */
STATIC_TEST_POINT uint16_T N0LamFil;   /* '<S3>/Merge9' */

/* N0 Filter Parameter */
STATIC_TEST_POINT uint8_T StabLoadLamTr;/* '<S3>/Merge10' */

/* Stab Load Lam */
STATIC_TEST_POINT uint8_T StabRpmLamTr;/* '<S3>/Merge11' */

/* Stab Rpm Lam */
STATIC_TEST_POINT uint8_T VtMisfCount[8];/* '<S3>/Merge17' */

/* Misfire counter */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<Root>/Reset'
 * Block description for: '<Root>/Reset'
 *   This block performs initialization of each model signals at EISB power on.
 */
void CombAvgFFS_Reset(void)
{
  int32_T i;
  for (i = 0; i < 8; i++) {
    /* SignalConversion generated from: '<S2>/AvgFFS' */
    AvgFFS[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/Avg_HR_FFS' */
    Avg_HR_FFS[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/Avg_HR_old_FFS' */
    Avg_HR_old_FFS[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/In_x_N0_FFS' incorporates:
     *  Constant: '<S2>/Constant15'
     */
    In_x_N0_FFS[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/InSum_FFS' incorporates:
     *  Constant: '<S2>/Constant16'
     */
    InSum_FFS[(i)] = 0U;

    /* SignalConversion generated from: '<S2>/VtMisfCount' */
    VtMisfCount[(i)] = 0U;
  }

  /* SignalConversion generated from: '<S2>/FiltLamFreeze' incorporates:
   *  Constant: '<S2>/Constant'
   */
  FiltLamFreeze = 0U;

  /* SignalConversion generated from: '<S2>/FlgSteadyStateFFS' incorporates:
   *  Constant: '<S2>/Constant1'
   */
  FlgSteadyStateFFS = 0U;

  /* SignalConversion generated from: '<S2>/FFSMedian' incorporates:
   *  Constant: '<S2>/Constant2'
   */
  FFSMedian = 0U;

  /* SignalConversion generated from: '<S2>/FiltParReset' incorporates:
   *  Constant: '<S2>/Constant3'
   */
  FiltParReset = 0U;

  /* SignalConversion generated from: '<S2>/D1LamFil' incorporates:
   *  Constant: '<S2>/Constant4'
   */
  D1LamFil = 0;

  /* SignalConversion generated from: '<S2>/D2LamFil' incorporates:
   *  Constant: '<S2>/Constant5'
   */
  D2LamFil = 0;

  /* SignalConversion generated from: '<S2>/EnFilButter' incorporates:
   *  Constant: '<S2>/Constant6'
   */
  EnFilButter = 0U;

  /* SignalConversion generated from: '<S2>/FreqNorm' incorporates:
   *  Constant: '<S2>/Constant7'
   */
  FreqNorm = 0;

  /* SignalConversion generated from: '<S2>/N0LamFil' incorporates:
   *  Constant: '<S2>/Constant8'
   */
  N0LamFil = 1U;

  /* SignalConversion generated from: '<S2>/StabLoadLamTr' incorporates:
   *  Constant: '<S2>/Constant9'
   */
  StabLoadLamTr = 0U;

  /* SignalConversion generated from: '<S2>/StabRpmLamTr' incorporates:
   *  Constant: '<S2>/Constant10'
   */
  StabRpmLamTr = 0U;

  /* Constant: '<S2>/Constant14' */
  IdVer_CombAvgFFS = ID_VER_COMBAVGFFS_DEF;
}

/* Model step function */
void CombAvgFFS_EOA(void)
{
  /* local block i/o variables */
  uint16_T rtb_SteadyStateDetect_o3;
  uint16_T rtb_SteadyStateDetect_o4;
  uint16_T rtb_SteadyStateDetect_o3_d;
  uint16_T rtb_SteadyStateDetect_o4_c;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_b;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_a;
  uint16_T rtb_Look2D_U16_U16_U16;
  uint16_T rtb_Memory1;
  int16_T rtb_RateLimiter_S16;
  uint8_T rtb_SteadyStateDetect_o1;
  uint8_T rtb_SteadyStateDetect_o2;
  uint8_T rtb_SteadyStateDetect_o1_l;
  uint8_T rtb_SteadyStateDetect_o2_f;
  uint8_T idx2;
  uint8_T idx_median;
  int16_T rtb_Add1;
  uint32_T rtb_Switch5;
  uint32_T rtb_Switch1_e;
  uint16_T rtb_DataTypeConversion3;
  uint32_T rtb_Product1;
  uint32_T rtb_Product2;
  uint32_T rtb_Switch2;
  uint32_T rtb_Avg_HR_FFS_l[8];
  uint16_T rtb_Memory;
  uint8_T rtb_UnitDelay1;
  uint16_T Switch1[8];
  int32_T i;
  uint32_T u0;
  int8_T tmp;
  uint8_T idx2_tmp;

  /* RootInportFunctionCallGenerator generated from: '<Root>/CombAvgFFS_EOA' incorporates:
   *  SubSystem: '<Root>/EOA_Calc'
   *
   * Block description for '<Root>/EOA_Calc':
   *  This block perform filtering of FFS at EOA event.
   */
  /* Outputs for Atomic SubSystem: '<S1>/Median_PreConditions'
   *
   * Block description for '<S1>/Median_PreConditions':
   *  This block evaluates the enabling condition for filtering of FFS.
   *  Conditions are:
   *  1 None of the following error has been detected in phase search algorithm
   *  - phase search algorithm didn't end correctly.
   *  - estimated spark and/or dwell time are too longer.
   *  (signal IonErrorStatus)
   *  2. Misfire counter is lower than the index selected for median evaluation of FFS
   *
   *  Moreover, this block allow to select as front flame speed a different index from FFS and based only on ion integral.
   */
  /* S-Function (Look2D_U8_U16_U16): '<S28>/Look2D_U8_U16_U16' incorporates:
   *  Constant: '<S26>/LENBUFMED3'
   *  Constant: '<S26>/LENBUFMED4'
   *  Constant: '<S26>/LENBUFMED5'
   *  Constant: '<S26>/NO_MISF4'
   *  Constant: '<S26>/NO_MISF5'
   */
  Look2D_U8_U16_U16( &rtb_Memory1, &TBCOMBCTRLINSEL[0], Rpm, &BKRPMCTRLINSEL[0],
                    ((uint8_T)BKRPMCTRLINSEL_dim), Load, &BKLOADCTRLINSEL[0],
                    ((uint8_T)BKLOADCTRLINSEL_dim));

  /* DataTypeConversion: '<S26>/Conversion1' */
  rtb_Memory = (uint16_T)(((uint32_T)rtb_Memory1) >> ((uint32_T)8));

  /* Switch: '<S26>/Switch1' incorporates:
   *  DataTypeConversion: '<S26>/Conversion'
   *  DataTypeConversion: '<S26>/Conversion3'
   *  Inport: '<Root>/FFS'
   */
  if (((int32_T)rtb_Memory) != 0) {
    /* S-Function (Look2D_U16_U16_U16): '<S27>/Look2D_U16_U16_U16' incorporates:
     *  Constant: '<S26>/LENBUFMED'
     *  Constant: '<S26>/LENBUFMED1'
     *  Constant: '<S26>/LENBUFMED2'
     *  Constant: '<S26>/NO_MISF1'
     *  Constant: '<S26>/NO_MISF3'
     */
    Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBINTION2FFS[0], Rpm,
                       &BKRPMCTRLINSEL[0], ((uint8_T)BKRPMCTRLINSEL_dim), Load,
                       &BKLOADCTRLINSEL[0], ((uint8_T)BKLOADCTRLINSEL_dim));
    for (i = 0; i < 8; i++) {
      /* MinMax: '<S26>/MinMax' incorporates:
       *  Inport: '<Root>/IntIon'
       *  Product: '<S26>/Product1'
       */
      u0 = ((uint32_T)IntIon[(i)]) * ((uint32_T)rtb_Look2D_U16_U16_U16);
      if (u0 >= MAX_FFS_VALUE) {
        u0 = MAX_FFS_VALUE;
      }

      /* End of MinMax: '<S26>/MinMax' */
      Switch1[i] = (uint16_T)(u0 >> ((uint32_T)11));
    }
  } else {
    for (i = 0; i < 8; i++) {
      Switch1[i] = FFS[(i)];
    }
  }

  /* End of Switch: '<S26>/Switch1' */

  /* Switch: '<S25>/Switch' incorporates:
   *  Constant: '<S25>/NO_MISF'
   *  Constant: '<S25>/NO_MISF4'
   *  Constant: '<S25>/NO_MISF5'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/StMisf'
   *  RelationalOperator: '<S25>/Relational Operator1'
   *  Selector: '<S25>/Selector_Outold1'
   */
  if (((uint32_T)StMisf[(IonAbsTdcEOA)]) != NO_MISF) {
    tmp = ((int8_T)MISF_COUNT_INC);
  } else {
    tmp = ((int8_T)MISF_COUNT_DEC);
  }

  /* End of Switch: '<S25>/Switch' */

  /* Sum: '<S25>/Sum' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Selector: '<S25>/Selector_Outold3'
   *  SignalConversion generated from: '<S1>/VtMisfCount_old'
   */
  rtb_Add1 = (int16_T)(((int32_T)tmp) + ((int32_T)VtMisfCount[(IonAbsTdcEOA)]));

  /* MinMax: '<S25>/MinMax1' */
  if (rtb_Add1 <= 0) {
    rtb_Add1 = 0;
  }

  /* End of MinMax: '<S25>/MinMax1' */

  /* MinMax: '<S25>/MinMax2' incorporates:
   *  Constant: '<S25>/NO_MISF3'
   */
  if (rtb_Add1 >= ((int16_T)MAX_MISF_COUNTER)) {
    rtb_Add1 = ((int16_T)MAX_MISF_COUNTER);
  }

  /* End of MinMax: '<S25>/MinMax2' */

  /* Product: '<S25>/Product' incorporates:
   *  Constant: '<S25>/LENBUFMED'
   *  Constant: '<S25>/One2'
   */
  rtb_UnitDelay1 = (uint8_T)(((uint32_T)LENBUFMED) / ((uint32_T)((uint8_T)
    MEDIAN_FACTOR)));

  /* DataTypeConversion: '<S25>/Conversion2' incorporates:
   *  Constant: '<S25>/ENFILTLAMFREEZE'
   *  Constant: '<S25>/NO_MISF1'
   *  Constant: '<S25>/One1'
   *  DataTypeConversion: '<S25>/Conversion1'
   *  DataTypeConversion: '<S25>/Conversion4'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/IonErrorStatus'
   *  Logic: '<S25>/Logical Operator'
   *  Logic: '<S25>/Logical Operator2'
   *  RelationalOperator: '<S25>/Relational Operator'
   *  RelationalOperator: '<S25>/Relational Operator2'
   *  S-Function (sfix_bitop): '<S25>/Bitwise Operator'
   *  Selector: '<S25>/Selector_Outold2'
   */
  FiltLamFreeze = (uint8_T)(((((((int32_T)IonErrorStatus[(IonAbsTdcEOA)]) &
    ((int32_T)((uint8_T)BIT_1_OR_2))) != 0) || (rtb_UnitDelay1 <= ((uint8_T)
    rtb_Add1))) && (((int32_T)ENFILTLAMFREEZE) != 0)) ? 1 : 0);

  /* Assignment: '<S25>/Assignment3' incorporates:
   *  DataTypeConversion: '<S25>/Conversion1'
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  VtMisfCount[(IonAbsTdcEOA)] = (uint8_T)rtb_Add1;

  /* End of Outputs for SubSystem: '<S1>/Median_PreConditions' */

  /* Chart: '<S1>/Median' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  /* Gateway: EOA_Calc/Median */
  /* During: EOA_Calc/Median */
  /* Median chart evaluates the median on FFS indicator.
     Median length is defined by calibration LENBUFMED. */
  /* Entry Internal: EOA_Calc/Median */
  /* Transition: '<S6>:3' */
  rtb_UnitDelay1 = 0U;
  if (!medianInitialized) {
    /* Transition: '<S6>:8' */
    while (rtb_UnitDelay1 < LENBUFMED) {
      /* Transition: '<S6>:109' */
      /* Transition: '<S6>:56' */
      for (idx2 = 0U; idx2 < N_CYLINDER; idx2 = (uint8_T)((int32_T)(((int32_T)
              idx2) + 1))) {
        /* Transition: '<S6>:9' */
        /* Transition: '<S6>:59' */
        medianBuffer[((int32_T)rtb_UnitDelay1) + (7 * ((int32_T)idx2))] =
          Switch1[IonAbsTdcEOA];
        circularIndex[(idx2)] = 0U;

        /* Transition: '<S6>:62' */
      }

      /* Transition: '<S6>:61' */
      rtb_UnitDelay1 = (uint8_T)((int32_T)(((int32_T)rtb_UnitDelay1) + 1));
    }

    /* Transition: '<S6>:110' */
    FFSMedian = Switch1[IonAbsTdcEOA];
  } else {
    /* Transition: '<S6>:65' */
    if (((int32_T)FiltLamFreeze) != 0) {
      /* Transition: '<S6>:67' */
      /* Transition: '<S6>:69' */
      /*  Don't updade Median  */
      idx_median = (uint8_T)(((uint32_T)LENBUFMED) >> ((uint32_T)1));

      /* Transition: '<S6>:119' */
    } else {
      /* Transition: '<S6>:71' */
      i = 7 * ((int32_T)IonAbsTdcEOA);
      medianBuffer[((int32_T)circularIndex[(IonAbsTdcEOA)]) + i] =
        Switch1[IonAbsTdcEOA];

      /* Transition: '<S6>:74' */
      /*  Circular index update  */
      if ((((int32_T)circularIndex[(IonAbsTdcEOA)]) + 1) < ((int32_T)LENBUFMED))
      {
        /* Transition: '<S6>:76' */
        /* Transition: '<S6>:78' */
        circularIndex[(IonAbsTdcEOA)] = (uint8_T)((int32_T)(((int32_T)
          circularIndex[(IonAbsTdcEOA)]) + 1));

        /* Transition: '<S6>:81' */
      } else {
        /* Transition: '<S6>:80' */
        circularIndex[(IonAbsTdcEOA)] = 0U;
      }

      /* Transition: '<S6>:21' */
      /*  Extract value for current cylinder  */
      while (rtb_UnitDelay1 < LENBUFMED) {
        /* Transition: '<S6>:22' */
        /* Transition: '<S6>:85' */
        VettSort[(rtb_UnitDelay1)] = medianBuffer[i + ((int32_T)rtb_UnitDelay1)];

        /* Transition: '<S6>:86' */
        rtb_UnitDelay1 = (uint8_T)((int32_T)(((int32_T)rtb_UnitDelay1) + 1));
      }

      /* Transition: '<S6>:88' */
      rtb_UnitDelay1 = 0U;
      idx_median = (uint8_T)(((uint32_T)LENBUFMED) >> ((uint32_T)1));
      while (rtb_UnitDelay1 <= idx_median) {
        /* Transition: '<S6>:90' */
        /* Transition: '<S6>:15' */
        /*  Vector sort  */
        idx2_tmp = (uint8_T)((int32_T)(((int32_T)rtb_UnitDelay1) + 1));
        idx2 = idx2_tmp;
        while (idx2 < LENBUFMED) {
          /* Transition: '<S6>:95' */
          /* Transition: '<S6>:99' */
          if (VettSort[(idx2)] < VettSort[(rtb_UnitDelay1)]) {
            /* Transition: '<S6>:101' */
            /* Transition: '<S6>:103' */
            rtb_Memory = VettSort[(idx2)];
            VettSort[(idx2)] = VettSort[(rtb_UnitDelay1)];
            VettSort[(rtb_UnitDelay1)] = rtb_Memory;
            idx2 = (uint8_T)((int32_T)(((int32_T)idx2) + 1));

            /* Transition: '<S6>:106' */
          } else {
            /* Transition: '<S6>:105' */
            idx2 = (uint8_T)((int32_T)(((int32_T)idx2) + 1));
          }

          /* Transition: '<S6>:107' */
        }

        /* Transition: '<S6>:97' */
        rtb_UnitDelay1 = idx2_tmp;
      }

      /* Transition: '<S6>:118' */
    }

    /* Transition: '<S6>:92' */
    FFSMedian = VettSort[(idx_median)];
  }

  /* End of Chart: '<S1>/Median' */

  /* Outputs for Atomic SubSystem: '<S1>/Filter_Parameter'
   *
   * Block description for '<S1>/Filter_Parameter':
   *  The aim of this block is to calculate the parameters used by Butterworth filter.
   *  It calculates enabling condition of the Butterworth filter (according to engine speed).
   *  Then, it calculate the cut frequency of the filter and the relative parameters (N0,D1,D2).

   */
  /* Product: '<S11>/Product' incorporates:
   *  Constant: '<S11>/RPM_2_PERC'
   *  Inport: '<Root>/Rpm'
   */
  rtb_Memory = (uint16_T)((((uint32_T)Rpm) << ((uint32_T)7)) / ((uint32_T)
    ((uint8_T)RPM_2_PERC)));

  /* UnitDelay: '<S11>/Unit Delay' */
  rtb_UnitDelay1 = sstab_rpm_lam;

  /* Memory: '<S14>/Memory1' */
  rtb_Memory1 = rpmSteadyInput;

  /* S-Function (SteadyStateDetect): '<S14>/SteadyStateDetect' incorporates:
   *  Constant: '<S11>/Constant'
   *  Constant: '<S11>/TDCSTABRPMLAMTR'
   *  Constant: '<S11>/THRSTABRPMLAMTR'
   */
  SteadyStateDetect( &rtb_SteadyStateDetect_o1, &rtb_SteadyStateDetect_o2,
                    &rtb_SteadyStateDetect_o3, &rtb_SteadyStateDetect_o4,
                    rtb_Memory, ((uint8_T)0U), THRSTABRPMLAMTR, TDCSTABRPMLAMTR,
                    rtb_UnitDelay1, rtb_Memory1, rpmSteadyTimer);

  /* UnitDelay: '<S11>/Unit Delay1' */
  rtb_UnitDelay1 = sstab_load_lam;

  /* Memory: '<S15>/Memory1' */
  rpmSteadyTimer = loadSteadyInput;

  /* Memory: '<S15>/Memory' */
  rtb_Memory = loadSteadyTimer;

  /* S-Function (SteadyStateDetect): '<S15>/SteadyStateDetect' incorporates:
   *  Constant: '<S11>/Constant1'
   *  Constant: '<S11>/TDCSTABLOADLAMTR'
   *  Constant: '<S11>/THRSTABLOADLAMTR'
   */
  SteadyStateDetect( &rtb_SteadyStateDetect_o1_l, &rtb_SteadyStateDetect_o2_f,
                    &rtb_SteadyStateDetect_o3_d, &rtb_SteadyStateDetect_o4_c,
                    Load, ((uint8_T)0U), THRSTABLOADLAMTR, TDCSTABLOADLAMTR,
                    rtb_UnitDelay1, rpmSteadyTimer, rtb_Memory);

  /* S-Function (PreLookUpIdSearch_U16): '<S17>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S12>/BKRPMLAMFIL'
   *  Constant: '<S12>/BKRPMLAMFIL_dim'
   *
   * Block description for '<S12>/BKRPMLAMFIL_dim':
   *  This block evaluates by means of two maps (VTFREQNORMCUT and VTENFILBUTTER) the cut frequency of the filter and the enaling condition, appling a interpolation with hysteresis on engine speed.
   *  If rpm and load are not stable, then a different calibration is used for cut frequency.
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                        &rtb_PreLookUpIdSearch_U16_o2, Rpm, &BKRPMLAMFIL[0],
                        ((uint8_T)BKRPMLAMFIL_dim));

  /* Sum: '<S12>/Add4' incorporates:
   *  Constant: '<S12>/RPMHYSTFN'
   *  Inport: '<Root>/Rpm'
   */
  rtb_Memory = (uint16_T)(((uint32_T)Rpm) + ((uint32_T)RPMHYSTFN));

  /* S-Function (PreLookUpIdSearch_U16): '<S18>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S12>/BKRPMLAMFIL1'
   *  Constant: '<S12>/BKRPMLAMFIL_dim1'
   */
  PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_b,
                        &rtb_PreLookUpIdSearch_U16_o2_a, rtb_Memory,
                        &BKRPMLAMFIL[0], ((uint8_T)BKRPMLAMFIL_dim));

  /* Chart: '<S12>/Hysteresis_Check' */
  /* Gateway: EOA_Calc/Filter_Parameter/FilterMgm/Hysteresis_Check */
  /* During: EOA_Calc/Filter_Parameter/FilterMgm/Hysteresis_Check */
  /* Entry Internal: EOA_Calc/Filter_Parameter/FilterMgm/Hysteresis_Check */
  /* Transition: '<S16>:1' */
  if ((rtb_PreLookUpIdSearch_U16_o1 == rtb_PreLookUpIdSearch_U16_o1_b) ||
      ((rtb_PreLookUpIdSearch_U16_o1 != i_out) &&
       (rtb_PreLookUpIdSearch_U16_o1_b != i_out))) {
    /* Transition: '<S16>:2' */
    /* Transition: '<S16>:16' */
    i_out = rtb_PreLookUpIdSearch_U16_o1;

    /* Transition: '<S16>:17' */
  } else {
    /* Transition: '<S16>:3' */
  }

  /* End of Chart: '<S12>/Hysteresis_Check' */

  /* DataTypeConversion: '<S11>/Conversion' incorporates:
   *  DataTypeConversion: '<S11>/Data Type Conversion1'
   *  DataTypeConversion: '<S11>/Data Type Conversion5'
   *  Logic: '<S11>/Logical Operator'
   */
  /* Transition: '<S16>:19' */
  FlgSteadyStateFFS = (uint8_T)(((((int32_T)rtb_SteadyStateDetect_o1) != 0) &&
    (((int32_T)rtb_SteadyStateDetect_o1_l) != 0)) ? 1 : 0);

  /* Switch: '<S12>/Switch' incorporates:
   *  Constant: '<S12>/FREQNORMLAMTR'
   *  Constant: '<S12>/VTFREQNORMCUT'
   *  DataTypeConversion: '<S12>/Data Type Conversion5'
   *  Selector: '<S12>/Selector'
   */
  if (((int32_T)FlgSteadyStateFFS) != 0) {
    rtb_Add1 = VTFREQNORMCUT[(i_out)];
  } else {
    rtb_Add1 = FREQNORMLAMTR;
  }

  /* End of Switch: '<S12>/Switch' */

  /* S-Function (RateLimiter_S16): '<S19>/RateLimiter_S16' incorporates:
   *  Constant: '<S13>/FREQRATEMAX'
   *  Constant: '<S13>/FREQRATEMIN'
   */
  RateLimiter_S16( &rtb_RateLimiter_S16, rtb_Add1, FreqNorm, FREQRATEMIN,
                  FREQRATEMAX);

  /* DataTypeConversion: '<S13>/Conversion1' incorporates:
   *  Product: '<S13>/Mul1'
   */
  rpmSteadyTimer = (uint16_T)(((uint32_T)((int32_T)(((int32_T)
    rtb_RateLimiter_S16) * ((int32_T)rtb_RateLimiter_S16)))) >> ((uint32_T)2));

  /* DataTypeConversion: '<S22>/Conversion2' incorporates:
   *  Constant: '<S13>/D1LAM_POLY_P1'
   *  Constant: '<S13>/D1LAM_POLY_P2'
   *  Constant: '<S13>/D1LAM_POLY_P3'
   *  Product: '<S13>/Mul2'
   *  Product: '<S13>/Mul3'
   *  Product: '<S20>/Divide'
   *  Product: '<S22>/Divide'
   *  Sum: '<S13>/Add2'
   *  Sum: '<S13>/Add3'
   */
  D1LamFil = (int16_T)(((((((int32_T)rpmSteadyTimer) * ((int32_T)((int16_T)
    D1LAM_POLY_P1))) / 256) + (((int32_T)rtb_RateLimiter_S16) * ((int32_T)
    ((int16_T)D1LAM_POLY_P2)))) + D1LAM_POLY_P3) / 256);

  /* DataTypeConversion: '<S23>/Conversion2' incorporates:
   *  Constant: '<S13>/D2LAM_POLY_P1'
   *  Constant: '<S13>/D2LAM_POLY_P2'
   *  Constant: '<S13>/D2LAM_POLY_P3'
   *  Product: '<S13>/Mul4'
   *  Product: '<S13>/Mul5'
   *  Product: '<S21>/Divide'
   *  Product: '<S23>/Divide'
   *  Sum: '<S13>/Add4'
   *  Sum: '<S13>/Add5'
   */
  D2LamFil = (int16_T)(((((((int32_T)rpmSteadyTimer) * ((int32_T)((int16_T)
    D2LAM_POLY_P1))) / 256) + (((int32_T)rtb_RateLimiter_S16) * ((int32_T)
    ((int16_T)D2LAM_POLY_P2)))) + D2LAM_POLY_P3) / 256);

  /* Sum: '<S13>/Add1' incorporates:
   *  Constant: '<S13>/one'
   *  Sum: '<S13>/Add'
   */
  rtb_Add1 = (int16_T)(((int16_T)(D1LamFil + D2LamFil)) + 16384);

  /* Product: '<S13>/Product' incorporates:
   *  Constant: '<S13>/four'
   */
  rtb_Memory = (uint16_T)(((uint16_T)((int32_T)(((int32_T)rtb_Add1) * ((int32_T)
    ((int16_T)N0LAM_POLY))))) << ((uint32_T)2));

  /* DataTypeConversion: '<S13>/Conversion2' incorporates:
   *  RelationalOperator: '<S13>/Relational Operator'
   */
  FiltParReset = (uint8_T)((rtb_RateLimiter_S16 != FreqNorm) ? 1 : 0);

  /* Update for UnitDelay: '<S11>/Unit Delay' */
  sstab_rpm_lam = rtb_SteadyStateDetect_o2;

  /* Update for Memory: '<S14>/Memory1' */
  rpmSteadyInput = rtb_SteadyStateDetect_o3;

  /* Update for Memory: '<S14>/Memory' */
  rpmSteadyTimer = rtb_SteadyStateDetect_o4;

  /* Update for UnitDelay: '<S11>/Unit Delay1' */
  sstab_load_lam = rtb_SteadyStateDetect_o2_f;

  /* Update for Memory: '<S15>/Memory1' */
  loadSteadyInput = rtb_SteadyStateDetect_o3_d;

  /* Update for Memory: '<S15>/Memory' */
  loadSteadyTimer = rtb_SteadyStateDetect_o4_c;

  /* End of Outputs for SubSystem: '<S1>/Filter_Parameter' */

  /* Outputs for Atomic SubSystem: '<S1>/Butterworth_Filtering'
   *
   * Block description for '<S1>/Butterworth_Filtering':
   *  The aim of this block is to apply a Butterworth filter to the median
   *  value of the FFS indicator.
   */
  /* Product: '<S8>/Product7' incorporates:
   *  Product: '<S9>/Product1'
   */
  u0 = ((uint32_T)FFSMedian) * ((uint32_T)rtb_Memory);

  /* Product: '<S10>/Product5' incorporates:
   *  SignalConversion generated from: '<S1>/N0LamFil_old'
   */
  rtb_Switch5 = (((uint32_T)rtb_Memory) << ((uint32_T)16)) / ((uint32_T)N0LamFil);

  /* MinMax: '<S10>/MinMax2' incorporates:
   *  Constant: '<S10>/NO_MISF3'
   */
  if (rtb_Switch5 >= MAX_N0_RESCALE) {
    rtb_Switch5 = MAX_N0_RESCALE;
  }

  /* End of MinMax: '<S10>/MinMax2' */

  /* DataTypeConversion: '<S10>/Data Type Conversion3' */
  rtb_DataTypeConversion3 = (uint16_T)(rtb_Switch5 >> ((uint32_T)8));

  /* Switch: '<S10>/Switch' incorporates:
   *  DataTypeConversion: '<S10>/Data Type Conversion5'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Selector: '<S10>/Selector_Outold1'
   *  Selector: '<S10>/Selector_Outold2'
   *  Selector: '<S10>/Selector_Outold3'
   *  Selector: '<S10>/Selector_Outold4'
   *  SignalConversion generated from: '<S1>/Avg_HR_FFS_Old'
   *  SignalConversion generated from: '<S1>/Avg_HR_old_FFS_old'
   *  SignalConversion generated from: '<S1>/InSum_FFS_Old'
   *  SignalConversion generated from: '<S1>/In_x_N0_FFS_Old'
   *  Switch: '<S10>/Switch1'
   *  Switch: '<S10>/Switch2'
   */
  if (((int32_T)FiltParReset) != 0) {
    /* Product: '<S10>/Product1' incorporates:
     *  DataTypeConversion: '<S10>/Data Type Conversion2'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Selector: '<S10>/Selector_Outold2'
     *  SignalConversion generated from: '<S1>/In_x_N0_FFS_Old'
     */
    rtb_Product1 = (((In_x_N0_FFS[(IonAbsTdcEOA)] >> ((uint32_T)16)) *
                     ((uint32_T)rtb_DataTypeConversion3)) << ((uint32_T)8));

    /* MinMax: '<S10>/MinMax' incorporates:
     *  Constant: '<S10>/NO_MISF2'
     */
    if (rtb_Product1 >= MAX_N0_FFS) {
      rtb_Product1 = MAX_N0_FFS;
    }

    /* End of MinMax: '<S10>/MinMax' */

    /* Product: '<S10>/Product2' incorporates:
     *  DataTypeConversion: '<S10>/Data Type Conversion1'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Selector: '<S10>/Selector_Outold1'
     *  SignalConversion generated from: '<S1>/InSum_FFS_Old'
     */
    rtb_Product2 = (((InSum_FFS[(IonAbsTdcEOA)] >> ((uint32_T)16)) * ((uint32_T)
      rtb_DataTypeConversion3)) << ((uint32_T)8));

    /* MinMax: '<S10>/MinMax1' incorporates:
     *  Constant: '<S10>/NO_MISF1'
     */
    if (rtb_Product2 >= MAX_N0_FFS) {
      rtb_Product2 = MAX_N0_FFS;
    }

    /* End of MinMax: '<S10>/MinMax1' */
    rtb_Switch2 = Avg_HR_FFS[(IonAbsTdcEOA)];
  } else {
    rtb_Product1 = In_x_N0_FFS[(IonAbsTdcEOA)];
    rtb_Product2 = InSum_FFS[(IonAbsTdcEOA)];
    rtb_Switch2 = Avg_HR_old_FFS[(IonAbsTdcEOA)];
  }

  /* End of Switch: '<S10>/Switch' */

  /* DataTypeConversion: '<S9>/Data Type Conversion1' */
  rtb_Switch1_e = (((uint32_T)FFSMedian) << ((uint32_T)14));

  /* Outputs for Atomic SubSystem: '<S1>/Filter_Parameter'
   *
   * Block description for '<S1>/Filter_Parameter':
   *  The aim of this block is to calculate the parameters used by Butterworth filter.
   *  It calculates enabling condition of the Butterworth filter (according to engine speed).
   *  Then, it calculate the cut frequency of the filter and the relative parameters (N0,D1,D2).

   */
  /* Switch: '<S9>/Switch2' incorporates:
   *  Constant: '<S12>/VTENFILBUTTER'
   *  DataTypeConversion: '<S9>/Data Type Conversion2'
   *  Selector: '<S12>/Selector2'
   */
  if (((int32_T)VTENFILBUTTER[(i_out)]) != 0) {
    /* Switch: '<S8>/Switch2' incorporates:
     *  DataTypeConversion: '<S8>/Data Type Conversion1'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Logic: '<S8>/LogicalOperator'
     *  Selector: '<S10>/Selector_Outold3'
     *  SignalConversion generated from: '<S1>/Avg_HR_FFS_Old'
     */
    if (((int32_T)FiltLamFreeze) == 0) {
      /* Sum: '<S8>/Add3' incorporates:
       *  Constant: '<S8>/four'
       *  DataTypeConversion: '<S8>/Data Type Conversion3'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Product: '<S8>/Product1'
       *  Product: '<S8>/Product11'
       *  Product: '<S8>/Product2'
       *  Product: '<S8>/Product7'
       *  Selector: '<S10>/Selector_Outold3'
       *  SignalConversion generated from: '<S1>/Avg_HR_FFS_Old'
       *  Sum: '<S8>/Add'
       *  Sum: '<S8>/Add1'
       *  Sum: '<S8>/Add2'
       */
      i = ((int32_T)((uint32_T)((((((rtb_Product1 >> ((uint32_T)1)) * ((uint32_T)
                  ((uint8_T)N0_FFS_COEFF))) >> ((uint32_T)1)) + (u0 >>
                ((uint32_T)2))) + (rtb_Product2 >> ((uint32_T)2))) >> ((uint32_T)
              2)))) + ((-mul_ssu32_loSR((int32_T)D2LamFil, rtb_Switch2, 14U)) -
                       mul_ssu32_loSR((int32_T)D1LamFil, Avg_HR_FFS
        [(IonAbsTdcEOA)], 14U));

      /* MinMax: '<S8>/MinMax1' incorporates:
       *  Constant: '<S8>/NO_MISF1'
       */
      if (i <= 0) {
        i = 0;
      }

      /* MinMax: '<S8>/MinMax' incorporates:
       *  Constant: '<S8>/NO_MISF2'
       *  DataTypeConversion: '<S8>/Data Type Conversion14'
       *  MinMax: '<S8>/MinMax1'
       */
      if (i < MAX_FFS_VALUE_HR) {
        rtb_Switch5 = (uint32_T)i;
      } else {
        rtb_Switch5 = (uint32_T)MAX_FFS_VALUE_HR;
      }

      /* End of MinMax: '<S8>/MinMax' */
    } else {
      rtb_Switch5 = Avg_HR_FFS[(IonAbsTdcEOA)];
    }

    /* End of Switch: '<S8>/Switch2' */
  } else {
    rtb_Switch5 = rtb_Switch1_e;
  }

  /* End of Switch: '<S9>/Switch2' */
  /* End of Outputs for SubSystem: '<S1>/Filter_Parameter' */

  /* Assignment: '<S9>/Assignment3' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  SignalConversion generated from: '<S1>/Avg_HR_FFS_Old'
   */
  for (i = 0; i < 8; i++) {
    rtb_Avg_HR_FFS_l[i] = Avg_HR_FFS[(i)];
  }

  rtb_Avg_HR_FFS_l[IonAbsTdcEOA] = rtb_Switch5;

  /* End of Assignment: '<S9>/Assignment3' */

  /* DataTypeConversion: '<S9>/Data Type Conversion14' */
  for (i = 0; i < 8; i++) {
    AvgFFS[(i)] = (uint16_T)(rtb_Avg_HR_FFS_l[i] >> ((uint32_T)14));
  }

  /* End of DataTypeConversion: '<S9>/Data Type Conversion14' */

  /* Outputs for Atomic SubSystem: '<S1>/Filter_Parameter'
   *
   * Block description for '<S1>/Filter_Parameter':
   *  The aim of this block is to calculate the parameters used by Butterworth filter.
   *  It calculates enabling condition of the Butterworth filter (according to engine speed).
   *  Then, it calculate the cut frequency of the filter and the relative parameters (N0,D1,D2).

   */
  /* Switch: '<S9>/Switch3' incorporates:
   *  Constant: '<S12>/VTENFILBUTTER'
   *  DataTypeConversion: '<S9>/Data Type Conversion2'
   *  Selector: '<S12>/Selector2'
   */
  if (((int32_T)VTENFILBUTTER[(i_out)]) != 0) {
    /* Switch: '<S8>/Switch3' incorporates:
     *  DataTypeConversion: '<S8>/Data Type Conversion1'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Selector: '<S10>/Selector_Outold3'
     *  SignalConversion generated from: '<S1>/Avg_HR_FFS_Old'
     */
    if (((int32_T)FiltLamFreeze) != 0) {
      rtb_Switch1_e = rtb_Switch2;
    } else {
      rtb_Switch1_e = Avg_HR_FFS[(IonAbsTdcEOA)];
    }

    /* End of Switch: '<S8>/Switch3' */
  }

  /* End of Switch: '<S9>/Switch3' */
  /* End of Outputs for SubSystem: '<S1>/Filter_Parameter' */

  /* Assignment: '<S9>/Assignment4' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  Avg_HR_old_FFS[(IonAbsTdcEOA)] = rtb_Switch1_e;

  /* Product: '<S9>/Product1' */
  rtb_Switch1_e = u0;

  /* Outputs for Atomic SubSystem: '<S1>/Filter_Parameter'
   *
   * Block description for '<S1>/Filter_Parameter':
   *  The aim of this block is to calculate the parameters used by Butterworth filter.
   *  It calculates enabling condition of the Butterworth filter (according to engine speed).
   *  Then, it calculate the cut frequency of the filter and the relative parameters (N0,D1,D2).

   */
  /* Switch: '<S9>/Switch5' incorporates:
   *  Constant: '<S12>/VTENFILBUTTER'
   *  DataTypeConversion: '<S9>/Data Type Conversion2'
   *  Selector: '<S12>/Selector2'
   *  Switch: '<S9>/Switch1'
   */
  if (((int32_T)VTENFILBUTTER[(i_out)]) != 0) {
    /* Switch: '<S8>/Switch1' incorporates:
     *  DataTypeConversion: '<S8>/Data Type Conversion1'
     *  Switch: '<S8>/Switch'
     */
    if (((int32_T)FiltLamFreeze) != 0) {
      /* Assignment: '<S9>/Assignment1' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      In_x_N0_FFS[(IonAbsTdcEOA)] = rtb_Product1;
      rtb_Switch1_e = rtb_Product2;
    } else {
      /* Assignment: '<S9>/Assignment1' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Product: '<S8>/Product7'
       */
      In_x_N0_FFS[(IonAbsTdcEOA)] = u0;
      rtb_Switch1_e = rtb_Product1;
    }

    /* End of Switch: '<S8>/Switch1' */
  } else {
    /* Assignment: '<S9>/Assignment1' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    In_x_N0_FFS[(IonAbsTdcEOA)] = u0;
  }

  /* End of Switch: '<S9>/Switch5' */
  /* End of Outputs for SubSystem: '<S1>/Filter_Parameter' */

  /* Assignment: '<S9>/Assignment2' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   */
  InSum_FFS[(IonAbsTdcEOA)] = rtb_Switch1_e;

  /* End of Outputs for SubSystem: '<S1>/Butterworth_Filtering' */

  /* SignalConversion generated from: '<S1>/Avg_HR_FFS' */
  for (i = 0; i < 8; i++) {
    Avg_HR_FFS[(i)] = rtb_Avg_HR_FFS_l[i];
  }

  /* End of SignalConversion generated from: '<S1>/Avg_HR_FFS' */

  /* Outputs for Atomic SubSystem: '<S1>/Filter_Parameter'
   *
   * Block description for '<S1>/Filter_Parameter':
   *  The aim of this block is to calculate the parameters used by Butterworth filter.
   *  It calculates enabling condition of the Butterworth filter (according to engine speed).
   *  Then, it calculate the cut frequency of the filter and the relative parameters (N0,D1,D2).

   */
  /* SignalConversion generated from: '<S1>/EnFilButter' incorporates:
   *  Constant: '<S12>/VTENFILBUTTER'
   *  Selector: '<S12>/Selector2'
   */
  EnFilButter = VTENFILBUTTER[(i_out)];

  /* End of Outputs for SubSystem: '<S1>/Filter_Parameter' */

  /* SignalConversion generated from: '<S1>/FreqNorm' */
  FreqNorm = rtb_RateLimiter_S16;

  /* SignalConversion generated from: '<S1>/N0LamFil' */
  N0LamFil = rtb_Memory;

  /* SignalConversion generated from: '<S1>/StabLoadLamTr' */
  StabLoadLamTr = rtb_SteadyStateDetect_o1_l;

  /* SignalConversion generated from: '<S1>/StabRpmLamTr' */
  StabRpmLamTr = rtb_SteadyStateDetect_o1;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombAvgFFS_EOA' */
}

/* Model step function */
void CombAvgFFS_NoSync(void)
{
  /* Outputs for Function Call SubSystem: '<Root>/Reset'
   *
   * Block description for '<Root>/Reset':
   *  This block performs initialization of each model signals at EISB power
   *  on.
   */
  /* RootInportFunctionCallGenerator generated from: '<Root>/CombAvgFFS_NoSync' */
  CombAvgFFS_Reset();

  /* End of Outputs for SubSystem: '<Root>/Reset' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombAvgFFS_NoSync' */
}

/* Model step function */
void CombAvgFFS_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/CombAvgFFS_PowerOn' incorporates:
   *  SubSystem: '<Root>/Reset'
   *
   * Block description for '<Root>/Reset':
   *  This block performs initialization of each model signals at EISB power
   *  on.
   */
  CombAvgFFS_Reset();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/CombAvgFFS_PowerOn' */
}

/* Model initialize function */
void CombAvgFFS_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T FiltLamFreeze;
uint8_T FlgSteadyStateFFS;
uint16_T AvgFFS[N_CYL_MAX];
void CombAvgFFS_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    AvgFFS[idx] = 0u;
  }

  FiltLamFreeze = 0u;
  FlgSteadyStateFFS = 0u;
}

void CombAvgFFS_PowerOn(void)
{
  CombAvgFFS_Stub();
}

void CombAvgFFS_NoSync(void)
{
  CombAvgFFS_Stub();
}

void CombAvgFFS_EOA(void)
{
  CombAvgFFS_Stub();
}

#endif                                 /* _BUILD_COMBAVGFFS_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/