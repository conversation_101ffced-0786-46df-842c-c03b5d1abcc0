/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_FCCU_out.h
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Mocci A
******************************************************************************/
/*****************************************************************************
**
**                        SafetyMngr_FCCU Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/

#ifndef SAFETYMNGR_FCCU_OUT_H
#define SAFETYMNGR_FCCU_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RTWTypes.h"
/* add here include files */

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
#define SAFE_FCCU_CODE

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_FCCU_Init
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_FCCU_Init(void);

/******************************************************************************
**   Function    : SafetyMngr_FCCU_DisableAlarmInterrupt
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_FCCU_DisableAlarmInterrupt(void);

/******************************************************************************
**   Function    : SafetyMngr_FCCU_EnableAlarmInterrupt
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_FCCU_EnableAlarmInterrupt(void);

/******************************************************************************
**   Function    : SafetyMngr_FCCU_Runtime_Check
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_FCCU_Runtime_Check(void);

/******************************************************************************
**   Function    : SafetyMngr_FCCU_SIUL2Config
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_FCCU_SIUL2Config(void);

/******************************************************************************
**   Function    : SafetyMngr_FCCU_DeInit
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
//FUNC(void, SAFE_FCCU_CODE) SafetyMngr_FCCU_DeInit(void);
extern void SafetyMngr_FCCU_DeInit(void);


#endif // SAFETYMNGR_FCCU_OUT_H

/****************************************************************************
 ****************************************************************************/
