/*
 * File: div_nzp_s32_sat_floor.c
 *
 * Code generated for Simulink model 'CoilTarget'.
 *
 * Model version                  : 1.112
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Wed Mar 01 12:00:28 2017
 */

#include "rtwtypes.h"
#include "div_nzp_s32_sat_floor.h"

int32_T div_nzp_s32_sat_floor(int32_T numerator, int32_T denominator)
{
  int32_T quotient;
  uint32_T absNumerator;
  uint32_T absDenominator;
  uint32_T tempAbsQuotient;
  boolean_T quotientNeedsNegation;
  absNumerator = (uint32_T)((numerator >= 0) ? numerator : (-numerator));
  absDenominator = (uint32_T)((denominator >= 0) ? denominator : (-denominator));
  quotientNeedsNegation = ((numerator < 0) != (denominator < 0));
  tempAbsQuotient = absNumerator / absDenominator;
  if ((!quotientNeedsNegation) && (tempAbsQuotient >= 2147483647U)) {
    quotient = MAX_int32_T;
  } else if (quotientNeedsNegation && (tempAbsQuotient > 2147483647U)) {
    quotient = MIN_int32_T;
  } else {
    if (quotientNeedsNegation) {
      absNumerator %= absDenominator;
      if (absNumerator > 0U) {
        tempAbsQuotient++;
      }
    }

    quotient = quotientNeedsNegation ? (-((int32_T)tempAbsQuotient)) : ((int32_T)
      tempAbsQuotient);
  }

  return quotient;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
