/****************************************************************************
 ****************************************************************************
 *
 *                              WDT_wrapper.c
 *
 * Author(s): Lana L.
 * 
 * 
 * Description:
 * 
 *
 * Usage notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifdef _BUILD_WDT_SBC_

#include "Timing_out.h"
#include "Dspi_out.h"
#include "Utils_out.h"
#include "wdt_out.h"
#include "pwrmgm_out.h"
#include "ccp.h"
#include "UDS_services.h"
#include "wdt_wrapper_out.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/* WDT refresh command word */
#define WATCHDOG_CMD_REFRESH    38531u   // WDT: period = 1sec, mode = TimeTriggered and parity bit

/* WDT operation */
uint8_T PendingOperation;
uint8_T DtcErasedByCal;

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ResetDiagByCalibration - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/
static int16_T ResetDiagByCalibration(void);


/*--------------------------------------------------------------------------*
 * WDT_GetTimeMs - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void WDT_GetTimeMs(uint32_T *rty_WdtSwitchTime) 
{
    uint64_T timer;

    TIMING_GetAbsTimer(&timer);
    TIMING_TicksToMilliSeconds(timer, &timer);
    *rty_WdtSwitchTime = (uint32_T)timer;    
}

/*--------------------------------------------------------------------------*
 * WDT_ConfigRec - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void WDT_ConfigRec(void) 
{
    uint32_T  waitCnt = 0u;
    uint16_T  WdtTxBuffer;
    uint16_T  WdtDummyRxBuffer;
#if 0
    "..\tree\DD\WDT\WDT_wrapper.c", line 93: error #1721-D: MISRA 2004 Rule 5.2: 
              identifier shadows variable "SBCSpiError" (declared at line 130 of
              "..\tree\DD\COMMON\TLE9278BQX_Com_out.h")
          SpiError_T  SBCSpiError;
                      ^
#endif
    SpiError_T  SBCSpiError_l;

    // Refresh for Watchdog reset
    WdtTxBuffer = WATCHDOG_CMD_REFRESH;
    SBCSpiError_l = SPI_TxRx(SPI_CH_E, PCS_0, &WdtTxBuffer, &WdtDummyRxBuffer, 1u);
    while (waitCnt < 300u)
    {
        UTILS_nop();
        UTILS_nop();
        UTILS_nop();
        UTILS_nop();
        waitCnt++;
    }
}

/*--------------------------------------------------------------------------*
 * WDT_ExtSyncISRReset - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void WDT_ExtSyncISRReset (void)
{
#ifdef _BUILD_WDT_SBC_
    uint32_T SpiStatusCnt = 0u;

    // Check DSPI status; timeout 4.5[ms]
    while (((DSPI_5.SR.R & DSPI5_SR_TXRXON_MASK) != 0u) && (SpiStatusCnt < DSPI_STATUS_TOUT ))
    {
        SpiStatusCnt++;
    }

    while (FlgEcuWrite == 0u)
    {
        WDT_Rec_ISR();
    }
#endif

    /* development version with EVB/board without attached WDT */
    SYS_SwRST();
}

/*--------------------------------------------------------------------------*
 * WDT_ExtSyncISR - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void WDT_ExtSyncISR(void)
{
#ifdef _BUILD_WDT_SBC_
    uint32_T SpiStatusCnt = 0u;

    // Check DSPI status; timeout 4.5[ms]
    while (((DSPI_5.SR.R & DSPI5_SR_TXRXON_MASK) != 0u) && (SpiStatusCnt < DSPI_STATUS_TOUT ))
    {
        SpiStatusCnt++;
    }

    while (FlgEcuWrite == 0u)
    {
        WDT_Rec_ISR();
    }
#endif
}

/*--------------------------------------------------------------------------*
 * WDT_SetPendingOperation - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void WDT_SetPendingOperation(uint8_T operation)
{
    /* set WDT window to 1sec for Flash Operation */
    PwrMgm_Write();
    PendingOperation = operation;
}


/*--------------------------------------------------------------------------*
 * WDT_PendingOperationsCheck - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void WDT_PendingOperationsCheck(void)
{
#ifndef _BUILD_WDT_EVB_
    if (FlgEcuWrite == 0u) // rischio di scrittura in flash con reset non gestiti
    {
    }
    else // scritture in flash senza rischi
    {
#endif
        /* mode change to PWM successful, resume suspended operation */
        switch (PendingOperation)
        {
#ifdef _BUILD_CCP_
#ifndef _BUILD_CCP_PROD_
            case WDT_PENDING_CCP_CLEAR_MEMORY_BOOT:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                ccpClearMemoryBoot();
                break;

            case WDT_PENDING_CCP_CLEAR_MEMORY_CALIB:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                ccpClearMemoryCalib();
                break;

            case WDT_PENDING_CCP_CLEAR_MEMORY_APPL:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                ccpClearMemoryAppl();
                break;
#endif /* _BUILD_CCP_PROD_ */
#endif /* _BUILD_CCP_ */

#ifdef _BUILD_DIAGCANMGM_
            case WDT_PENDING_DIAG_WRITE_DATA:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                UDS_PendingWriteData();
                break;

            case WDT_SECURE_JUMP2BOOT:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                UDS_SecureJump2Boot();
                break;

            case WDT_SECURE_RESET:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                UDS_SecureReset();
                break;

            case WDT_PENDING_DIAG_CLEAR_DIAGINFO:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                UDS_PendingClearDiagInfo();
                break;
#if 0
            case WDT_PENDING_WRITE_DATA_BKGD:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                UDS_PendingWriteData_Bkgd();
                break;

            case WDT_PENDING_RESET_FACTORY_CALIB:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                PendingResetFactoryCalib();
                break;
#endif
#endif
            case WDT_PENDING_DIAG_CLEAR_BYCAL:
                /* reset pending flag, has to be done first */
                PendingOperation = WDT_PENDING_NONE;
                ResetDiagByCalibration();
                break;
            case WDT_PENDING_NONE:
            case WDT_PENDING_RESET:
                /* do nothing, MISRA */
                break;

            default:
                break;
        }
#ifndef _BUILD_WDT_EVB_
    }
#endif
}

/*--------------------------------------------------------------------------*
 * ClearDtcErasedByCalFlg - Function description
 *
 * Implementation notes:
 * This method clears DtcErasedByCal flag
 *--------------------------------------------------------------------------*/
void ClearDtcErasedByCalFlg(void)
{
    DtcErasedByCal= 0u;
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * ResetDiagByCalibration - Function description
 *
 * Implementation notes:
 * Resets Diagnosis by calibration
 *--------------------------------------------------------------------------*/
static int16_T ResetDiagByCalibration(void)
{
int16_T res = NO_ERROR;
uint8_T i,k;

    /* ISO 14229-1:2006(E) par. 11.2
    Permanent DTCs shall be stored in non-volatile memory. These DTCs cannot be cleared by any test
    equipment (e.g. on-board tester, off-board tester). The OBD system shall clear these DTCs itself by
    completing and passing the on-board monitor. This would prevent clearing DTCs simply by disconnecting the
    battery
    */

    EEMGM_SetEventID(EE_INVALIDATE_DIAG);

    res = EEMGM_EETaskCmd();

    /* reset watchdog back to GPIO mode  */
    PwrMgm_Read();

    if((res == NO_ERROR) ||  (res == EE_ID_UNAVAILABLE))
    {
        DtcErasedByCal = 1u;
#ifdef _BUILD_DTC_
        /* Resetting DTC Status to its default value */
        for(i = 0u; i < DIAG_NUMBER; i++)
        {
            resetDTCStatus(i);
        }
#endif
        /* Reset Chrono Stack and EventCounterEE line-by-line */
        for (k = 0u; k < DIAG_FAULT_LENGTH; k++)
        {
#if (SAVE_ENV_DATA_EE == 1u)
            memset((void*)&(DTCSnapshotEE_1[k]), 0, sizeof(DTCSnapshotEE_1[k]));
            memset((void*)&(DTCSnapshotEE_2[k]), 0, sizeof(DTCSnapshotEE_2[k]));
            /* Reset EventCounterEE */
            memset((void*)&(EventCounterEE[k]), 0, sizeof(uint8_T));
#endif
            /* Reset StoredDiag */
            StoredDiag[k] = 255u;
            /* Reset StoredFault */
            StoredFault[k] = 255u;
        }
        /* Reset StoredDiag/StoredFault index */
        StoredDiagIdx = 0u;
        memset(ActiveFault,255,sizeof(ActiveFault));
        // Reset index of ActiveFault
        IdActFault = 0u;
    }

    return res;
}


#endif

/****************************************************************************
 ****************************************************************************/
 
