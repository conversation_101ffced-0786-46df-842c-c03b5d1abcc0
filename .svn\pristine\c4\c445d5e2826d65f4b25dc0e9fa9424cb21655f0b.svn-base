/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

/*! \mainpage ModuleName

\section intro Introduction
\brief A brief description of what this module does 

Explain in detail how this module works and what is supposed to do.  

 */

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "os_exec_ctrl.h"
#include "OS_hooks.h"
#include "task.h"
#include "cpumgm_out.h"
#include "mpc5500_spr_macros.h"

#define FILLED_LIST             -1
#define EMPTY_LIST              -2


#ifdef _OSEK_

#pragma ghs startnomisra

/*!
\defgroup PrivateVariables Private Variables 
\sgroup
 */
/*-----------------------------------*
 * PRIVATE VARIABLE DEFINITIONS
 *-----------------------------------*/
///These variables can be used only by this module 

typedef struct
{
    TaskType buffer[FIFO_HANDLER_DIM];
    uint8_T   r_index;
    uint8_T   w_index;
} OSBuff;

static OSBuff  OSHandler_Manager[SOFT_SET_INTERRUPT_NUM];  

static uint8_T CntOsTaskListFill[SOFT_SET_INTERRUPT_NUM]; 

/*!\egroup*/

extern uint32_T currentTaskStkPtr;
extern TaskCBS  OsTaskTable[OSNUMTSKS];
extern uint32_T terminationAddress;
extern StatusType runningTaskId;


static void OSGetLinkRegister(void (*pFunc)(void));
static StatusType OSSetTaskID( TaskType TaskId);
static int16_T OSTASK_TaskHandlingFunction(TaskType taskId ,uint8_T index);
static int16_T OSSetOnHandlerManager(TaskType taskId,uint8_T index);
static int16_T OSGetFromHandlerManager(uint8_T index,TaskType *tmptaskId);


__asm inline uint32_T getStackPointer(void)
{
    stw sp, r3
}

__asm inline void __blr(void)
{
    blr
}

/*!
\defgroup PublicFunctions Public Functions 
\sgroup
 */
/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   Public function name
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
StatusType ActivateTask( TaskType taskId)
{
    uint8_T index;
    int16_T retFcnValue;
    StatusType retValue = E_OK;

    OSrtiSetServiceWatch(OSServiceId_ActivateTask);
    index = OSTASKPRI(taskId);
    OSTASKSTATUS(taskId) = READY;

    //   OSrtiSetOldServiceID(OSServiceId_ActivateTask);
    retFcnValue=OSTASK_TaskHandlingFunction(taskId,index);
    if (retFcnValue==NO_ERROR) 
    {
        //      OSrtiResetIdOnExit();
        OSrtiServiceWatchOnExit(OSServiceId_ActivateTask);
    }
    else
    {
        OSRETERROR( E_OS_LIMIT, OSServiceId_ActivateTask, taskId );
        retValue = E_OS_LIMIT;
    }
    return retValue;
}
/***************************************************************************/
//   Function    :   Public function name
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
StatusType GetTaskID( TaskRefType  ptrTaskId)
{
    OSrtiSetServiceWatch(OSServiceId_GetTaskID);
    *ptrTaskId = runningTaskId;
    OSrtiServiceWatchOnExit(OSServiceId_GetTaskID);
    return (E_OK);    
}

/***************************************************************************/
//   Function    :   OSSetTaskID
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
static StatusType OSSetTaskID( TaskType  TaskId)
{
    runningTaskId = TaskId;

    return (E_OK);    
}
/***************************************************************************/
//   Function    :   Schedule
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
StatusType Schedule(void)
/* ------------------------------------------------------------------------- */
{
    register TaskType taskId;
    TaskType currentTaskId;

    OSrtiSetServiceWatch(OSServiceId_Schedule);
    OSrtiSetOldServiceID(OSServiceId_Schedule);
    if(OSGetFromHandlerManager(FIFO_QUEUE_ID(), &currentTaskId)==0)
        /* retrieve the user defined   */
        /* ISR from the associate FIFO */
        /* list                        */
    {
        /* check sul current task e deallocazione delle sue risorse */

        taskId = currentTaskId;

        GetTaskID(&currentTaskId);

        OSTASKSTATUS(taskId) = RUNNING;
        OSSetTaskID(taskId);
        {
            DisableAllInterrupts();
            PreTaskHook();
            OSTASKSTACKPTR(taskId) = currentTaskStkPtr;
            EnableAllInterrupts();
        }
        OSGetLinkRegister(OSTASKENTRY(taskId));/* OsTaskTable[taskId].entry */
        {
            DisableAllInterrupts();
            currentTaskStkPtr = OSTASKSTACKPTR(runningTaskId);
            PostTaskHook();
            EnableAllInterrupts();
        }
    }
    DisableAllInterrupts();
    OSSetTaskID(currentTaskId);
    EnableAllInterrupts();
    OSrtiResetIdOnExit()    /* without ; for MISRA 14.3 */

    return (E_OK);
}                                                                                                                  
/***************************************************************************/
//   Function    :   Public function name
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
StatusType TerminateTask(void)
{
    OSrtiSetServiceWatch(OSServiceId_TerminateTask);
    OSTASKSTATUS(runningTaskId) = SUSPENDED;
    OSrtiServiceWatchOnExit(OSServiceId_TerminateTask);
    __MTSPR(SPR_LR,terminationAddress);
    __blr();

    /* never return from this point */
    return(E_OS_STATE);
}

/***************************************************************************/
//   Function    :   Public function name
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
void OSInitHandlerManager(void)
{
    uint8_T elem_index,buf_index;

    for (buf_index=0;buf_index<SOFT_SET_INTERRUPT_NUM;buf_index++)
    {
        for (elem_index=0;elem_index<FIFO_HANDLER_DIM;elem_index++)
        {
            OSHandler_Manager[buf_index].buffer[elem_index]=0;
        }
        OSHandler_Manager[buf_index].r_index=0;
        OSHandler_Manager[buf_index].w_index=0;
    }
}

/***************************************************************************/
//   Function    :   ChainTask
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
StatusType ChainTask( TaskType TaskId)
{
    uint8_T index;
    TaskType currentTaskId;
    StatusType retValue = E_OS_STATE;

    OSrtiSetServiceWatch(OSServiceId_ChainTask);
    GetTaskID(&currentTaskId);

    if (currentTaskId == TaskId)
    {
        /* rem: overrun di task identici */
        OSRETERROR(E_OS_LIMIT,OSServiceId_ChainTask,TaskId);
        retValue = E_OS_LIMIT;
    }
    else
    {
        OSTASKSTATUS(runningTaskId) = SUSPENDED;

        index = OSTASKPRI(TaskId);
        OSTASKSTATUS(TaskId) = READY;

        if (OSTASK_TaskHandlingFunction(TaskId,index)!=NO_ERROR)
        {
            OSRETERROR(E_OS_LIMIT,OSServiceId_ChainTask,TaskId);
            retValue = E_OS_LIMIT;
        }
        else
        {
            OSrtiServiceWatchOnExit(OSServiceId_ChainTask);
            __MTSPR(SPR_LR,terminationAddress);
            __blr();
        }
    }
    /* never return from this point in case of correct execution */
    return(retValue);
}

/* ------------------------------------------------------------------------- */

/*!\egroup*/
/*!
\defgroup PrivateFunctions Private Functions 
\sgroup
 */
/*==================================================================================================
                                       PRIVATE FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   OSSetOnHandlerManager
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this private function 
 */
/**************************************************************************/
static int16_T OSSetOnHandlerManager(TaskType taskId,uint8_T index)
/* ------------------------------------------------------------------------- */
{
    int16_T retValue = NO_ERROR;
#ifdef OSTASKQUEUEMAX_TEST
    uint8_T tmpNumElem = 0u;
#endif
    DisableAllInterrupts();
    if (((OSHandler_Manager[index].w_index+1) == OSHandler_Manager[index].r_index) || \
            (((OSHandler_Manager[index].w_index+1) == FIFO_HANDLER_DIM) && (OSHandler_Manager[index].r_index == 0)
            )
    )
    {
        EnableAllInterrupts();
        CntOsTaskListFill[index]++;
        retValue = FILLED_LIST;
    }
    else
    {
        OSHandler_Manager[index].buffer[OSHandler_Manager[index].w_index] = taskId;
        OSHandler_Manager[index].w_index++;
        if (OSHandler_Manager[index].w_index == FIFO_HANDLER_DIM)
        {
            OSHandler_Manager[index].w_index = 0;
        }

#ifdef OSTASKQUEUEMAX_TEST
        if (OSHandler_Manager[index].w_index >= OSHandler_Manager[index].r_index)
        {
            tmpNumElem = OSHandler_Manager[index].w_index - OSHandler_Manager[index].r_index; 
        }
        else
        {
            tmpNumElem = OSHandler_Manager[index].r_index - OSHandler_Manager[index].w_index; 
            tmpNumElem = FIFO_HANDLER_DIM - tmpNumElem;            
        }

        if (tmpNumElem > EEOsTaskListMaxElem[index])
        {
            EEOsTaskListMaxElem[index] = tmpNumElem;
        }
#endif

        EnableAllInterrupts();
    }
    return (retValue);
}
/***************************************************************************/
//   Function    :   Private function name
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this private function 
 */
/**************************************************************************/
static int16_T OSGetFromHandlerManager(uint8_T index,TaskType *tmptaskId)
/* ------------------------------------------------------------------------- */
{
    int16_T retValue = NO_ERROR;

    DisableAllInterrupts();
    if(OSHandler_Manager[index].r_index == OSHandler_Manager[index].w_index)
    {
        CLR_SSCIR(index)=SET_BIT_ENABLED; /* check for others entry on a FIFO */
        EnableAllInterrupts(); 
        retValue = EMPTY_LIST;
    }
    else
    {
        *tmptaskId = OSHandler_Manager[index].buffer[(OSHandler_Manager[index].r_index)];
        OSHandler_Manager[index].r_index++;

        if (OSHandler_Manager[index].r_index == FIFO_HANDLER_DIM)
        {
            OSHandler_Manager[index].r_index = 0;
        }

        if (OSHandler_Manager[index].r_index == OSHandler_Manager[index].w_index)
        {
            CLR_SSCIR(index)=SET_BIT_ENABLED; 
        }
        EnableAllInterrupts(); 
    }
    return (retValue);
}

/***************************************************************************/
//   Function    :   Private function name
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this private function 
 */
/**************************************************************************/
static int16_T OSTASK_TaskHandlingFunction(TaskType taskId ,uint8_T index)
{
    /* ------------------------------------------------------------------------- */
    int16_T retValue = NO_ERROR;

    if(index > S_IRQ7)
    {   
        retValue = SOFTWARE_INTERRUPT_REQUEST_ERROR;
    }
    else
    {   
        if (OSSetOnHandlerManager(taskId,index)==0)
        {
            SET_SSCIR(index) = SET_BIT_ENABLED;
        }
        else
        {   
            retValue = TASK_NOT_ENABLED_DUE_FILLED_LIST;
        }
    }
    return (retValue);

#if 0
    int16_T status = NO_ERROR;

    switch (index){   /* switch for proper software interrupt request ablitation */
    case (S_IRQ0):
                if (!(status = OSSetOnHandlerManager(taskId,S_IRQ0))){
                    /* if empty, set user ISR on SSCIR0 FIFO list */
                    SET_SSCIR(S_IRQ0) = SET_BIT_ENABLED; /* Enable interrupt request by  */
                    /* writing 1 to the SET bit in  */
                    /* INTC software set/clear      */
                    /* interrupt register           */
                }
                else
                    status = TASK_NOT_ENABLED_DUE_FILLED_LIST; /* full list,return code  */
    /* error                  */
    break;

    case (S_IRQ1):
                if (!(status = OSSetOnHandlerManager(taskId,S_IRQ1))){
                    SET_SSCIR(S_IRQ1) = SET_BIT_ENABLED;
                }
                else
                    status = TASK_NOT_ENABLED_DUE_FILLED_LIST;
    break;

    case (S_IRQ2):
                if (!(status=OSSetOnHandlerManager(taskId,S_IRQ2))){
                    SET_SSCIR(S_IRQ2) = SET_BIT_ENABLED;
                }
                else
                    status = TASK_NOT_ENABLED_DUE_FILLED_LIST;
    break;

    case (S_IRQ3):
                if (!(status=OSSetOnHandlerManager(taskId,S_IRQ3))){
                    SET_SSCIR(S_IRQ3) = SET_BIT_ENABLED;
                }
                else
                    status = TASK_NOT_ENABLED_DUE_FILLED_LIST;
    break;

    case (S_IRQ4):
                if (!(status=OSSetOnHandlerManager(taskId,S_IRQ4))){
                    SET_SSCIR(S_IRQ4) = SET_BIT_ENABLED;
                }
                else
                    status = TASK_NOT_ENABLED_DUE_FILLED_LIST;
    break;

    case (S_IRQ5):
                if (!(status=OSSetOnHandlerManager(taskId,S_IRQ5))){
                    SET_SSCIR(S_IRQ5) = SET_BIT_ENABLED;
                }
                else
                    status = TASK_NOT_ENABLED_DUE_FILLED_LIST;
    break;

    case (S_IRQ6):
                if (!(status=OSSetOnHandlerManager(taskId,S_IRQ6))){
                    SET_SSCIR(S_IRQ6) = SET_BIT_ENABLED;
                }
                else
                    status = TASK_NOT_ENABLED_DUE_FILLED_LIST;
    break;

    case (S_IRQ7):
                if (!(status=OSSetOnHandlerManager(taskId,S_IRQ7))){
                    SET_SSCIR(S_IRQ7) = SET_BIT_ENABLED;
                }
                else
                    status = TASK_NOT_ENABLED_DUE_FILLED_LIST;
    break;
    default:
        status = SOFTWARE_INTERRUPT_REQUEST_ERROR;
        break;
    }

    return (status);
#endif  

}
/***************************************************************************/
//   Function    :   OSGetLinkRegister
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this private function 
 */
/**************************************************************************/
static void OSGetLinkRegister(void (*pFunc)(void))
{
    terminationAddress = getSpecReg32SPR_LR();
    pFunc();
}
/***************************************************************************/
//   Function    :   GetTaskState
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this private function 
 */
/**************************************************************************/
StatusType GetTaskState( TaskType TaskId, TaskStateRefType  ptrState)
/* ------------------------------------------------------------------------- */
{
    OSrtiSetServiceWatch(OSServiceId_GetTaskState);
    *ptrState = OSTASKSTATUS(TaskId);
    OSrtiServiceWatchOnExit(OSServiceId_GetTaskState);

    return (E_OK);    
}

/*!\egroup*/

#else


StatusType ActivateTask( TaskType taskId)
{
    return(0);   
}

StatusType TerminateTask( void )
{
    return(E_OS_STATE);
}


#endif // _OSEK_


/****************************************************************************
 ****************************************************************************/
#pragma ghs endnomisra
