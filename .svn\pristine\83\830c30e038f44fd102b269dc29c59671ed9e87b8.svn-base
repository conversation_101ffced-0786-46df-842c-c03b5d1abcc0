/*****************************************************************************************************************/
/* $HeadURL::                                                                                                              $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  BuckDiagMgm
**  Filename        :  BuckDiagMgm_calib.c
**  Created on      :  31-mar-2021 12:01:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_BUCKDIAGMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "buckdiagmgm.h"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
#pragma ghs section rodata=".calib"

//Bk: VTTHRIBATTDIAG [Rpm]
CALQUAL CALQUAL_POST uint16_T BKTHRIBATTDIAG[BKTHRIBATTDIAG_dim] = 
{
 0u, 1000u, 2000u, 3000u, 5000u, 7000u
};

//Bk: VTTHRISUPPLYCOILDIAG [Rpm]
CALQUAL CALQUAL_POST uint16_T BKTHRISUPPLYCOILDIAG[BKTHRISUPPLYCOILDIAG_dim] = 
{
 0u, 1000u, 2000u, 3000u, 5000u, 7000u
};

/// IBatt diag threshold
CALQUAL CALQUAL_POST uint16_T VTTHRIBATTDIAG[BKTHRIBATTDIAG_dim] = 
{
 4096U, 4096U, 4096U, 4096U, 4096U, 4096U  // 5000[mV] * 4096 / 5000
};

/// ISupplyCoilx diag threshold
CALQUAL CALQUAL_POST uint16_T VTTHRISUPPLYCOILDIAG[BKTHRISUPPLYCOILDIAG_dim] = 
{
 4096U, 4096U, 4096U, 4096U, 4096U, 4096U  // 5000[mV] * 4096 / 5000
};

/// VSupplyCoilx diag threshold
CALQUAL CALQUAL_POST uint16_T THRVSUPPLYCOILDIAG = 4096U; // 5000[mV] * 4096 / 5000

/// Ibatt threshold used in power on check
CALQUAL CALQUAL_POST uint16_T THRIBATTPWRON = 4096U; // 5000[mV] * 4096 / 5000

#endif // _BUILD_MSPARKCMD_
/****************************************************************************
 ****************************************************************************/
 
