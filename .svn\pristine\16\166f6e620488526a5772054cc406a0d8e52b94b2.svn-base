/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonKnockAirCorr.c
 **  File Creation Date: 19-Apr-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonKnockAirCorr
 **  Model Description:  This module detect heastroke event and calculate a pertinent correction both for the engine load threshold used to disable knock detection strategy, and for spark advance correction due to knock detection.
   The module is triggered by:
   - Power On event, for ouputs initialization.
   - 10ms event, for heatstroke detection.
   - End Of Acquisition event, for corrections management.
 **  Model Version:      1.792
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Mon Apr 19 10:26:57 2021
 **
 **  Last Saved Modification:  RoccaG - Mon Apr 19 09:33:39 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonKnockAirCorr_out.h"
#include "IonKnockAirCorr_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKDLOADTAIR_dim                4U                        /* Referenced by:
                                                                  * '<S17>/Constant2'
                                                                  * '<S17>/Constant4'
                                                                  * '<S17>/Constant6'
                                                                  * '<S17>/Constant8'
                                                                  */

/* BKDLOADTAIR breakpoint dimension. */
#define BKLOADSAAIR_dim                5U                        /* Referenced by: '<S8>/Constant3' */

/* BKLOADSAAIR breakpoint dimension. */
#define BKRPMSAAIR_dim                 11U                       /* Referenced by: '<S8>/Constant1' */

/* BKRPMSAAIR breakpoint dimension. */
#define ID_VER_IONKNOCKAIRCORR_DEF     1792U                     /* Referenced by: '<S2>/Constant12' */

/* Model Version. */
#define MIN_TAIRMAX_VALUE              -800                      /* Referenced by:
                                                                  * '<S1>/EOA'
                                                                  * '<S2>/Constant3'
                                                                  */

/* Minimum value for TAir. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONKNOCKAIRCORR_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint16_T cntEOADLoadThr;        /* '<S3>/Merge5' */
static uint16_T cntEOATurboLoad;       /* '<S3>/Merge11' */
static uint16_T cntEOATurboVeh;        /* '<S3>/Merge10' */

/*Calibration memory section */
/*Start of local calbration section*/
#pragma ghs section rodata=".calib"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKDLOADTAIR[5] = { 320, 480, 640, 800,
  960 } ;

/* TAir breakpoint for VTDLOADTAIR */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T BKLOADSAAIR[6] = { 0U, 20U, 40U, 60U,
  75U, 100U } ;

/* Vector of steps for the load */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMSAAIR[12] = { 1000U, 2000U, 3100U,
  4000U, 4500U, 5000U, 5500U, 6000U, 6500U, 7000U, 7500U, 8500U } ;

/* Vector of steps for the engine speed (IonKnockAirCorr module) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CNTEOATURBOLOADTHR = 0U;

/* Air temperature Turbo check EOA counter thr */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CNTEOATURBOVEHSPEEDTHR = 0U;

/* Air temperature Turbo check EOA counter thr */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TAIRDLOADEN = 2400;

/* Min TAir to enable DLoad application */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TAIRDLOADTURBODIS = 1U;

/* Air temperature Turbo engine check difable flag */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TAIRTURBOLOADTHR = 7680U;

/* Air temperature Turbo check Load thr */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TAIRTURBOVEHTHR = 80U;

/* Air temperature Turbo check VehSpeed thr */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBSATAIR[72] = { 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0 } ;

/* Table of SA function of Rpm and Load */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTCNTDLOADTAIR1[5] = { 20000U, 20000U,
  20000U, 20000U, 20000U } ;

/* CntEOADLoad value to apply DLoadTAirMax */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTCNTDLOADTAIR2[5] = { 5000U, 5000U,
  5000U, 5000U, 5000U } ;

/* Duration of DLoadTAir from DLoadTAirMax to 0 */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T VTDLOADTAIR[5] = { 0, 0, 0, 0, 0 } ;

/* Delta Load correction for TAir */
#pragma ghs section rodata=default

/*End of calibration section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
int16_T DLoadTAir;                     /* '<S3>/Merge7' */

/* Delta Load to enable knocking - applied value */
uint8_T FlgDLoadTAir;                  /* '<S3>/Merge1' */

/* DLoadTAir correction running */
int16_T SATAir;                        /* '<S3>/Merge8' */

/* SA correction */

/*Static test point definition*/
/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T CntDLoadTAir1;

/* Output of VTCNTDLOADTAIR1 */
STATIC_TEST_POINT uint16_T CntDLoadTAir2;

/* Output of VTCNTDLOADTAIR2 */
STATIC_TEST_POINT uint16_T CntEOADLoad;

/* Counter for DLoadTAir application */
STATIC_TEST_POINT int16_T DLoadTAirMax;

/* Delta Load to enable knocking - max value */
STATIC_TEST_POINT uint32_T IdVer_IonKnockAirCorr;

/* Model Version */
STATIC_TEST_POINT int16_T SATAirTab;

/* Output of TBSATAIR */
STATIC_TEST_POINT int16_T TAirMax;

/* Air Temperature Max  */

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<S4>/UpdateCounter'
 * Block description for: '<S4>/UpdateCounter'
 *   Counter is resetted every time that all conditions necessary to detect heatstroke are valid, in order to mantain stable the air temperature correction.
 */
void IonKnockAirCorr_UpdateCounter(uint16_T *rty_CntEOADLoad)
{
  /* SignalConversion generated from: '<S16>/CntEOADLoad' incorporates:
   *  Constant: '<S16>/Constant'
   */
  *rty_CntEOADLoad = 0U;
}

/*
 * Output and update for function-call system: '<S4>/UpdateDLoad'
 * Block description for: '<S4>/UpdateDLoad'
 *   This block evaluates the air temperature correction for engine load threshold (used to enable knock detection strategy).
 *   Moreover it calculates threshold used to update air temperature correction:
 *   -the correction for TAirCAN shall be kept constant for VTCNTDLOADTAIR1 combustions and shall reach the neutral value within VTCNTDLOADTAIR2 combustions.
 */
void IonKnockAirCorr_UpdateDLoad(int16_T rtu_TAir, uint8_T *rty_FlgDLoadTAir,
  int16_T *rty_TAirMax, uint16_T *rty_CntDLoadTAir1, uint16_T *rty_CntDLoadTAir2,
  uint16_T *rty_cntEOADLoadThr, int16_T *rty_DLoadTAirMax)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_LookUp_IR_U16_j;
  int16_T rtb_LookUp_IR_S8;

  /* S-Function (PreLookUpIdSearch_S16): '<S22>/PreLookUpIdSearch_S16' incorporates:
   *  Constant: '<S17>/Constant1'
   *  Constant: '<S17>/Constant2'
   */
  PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                        &rtb_PreLookUpIdSearch_S16_o2, rtu_TAir, &BKDLOADTAIR[0],
                        ((uint8_T)BKDLOADTAIR_dim));

  /* S-Function (LookUp_IR_U16): '<S20>/LookUp_IR_U16' incorporates:
   *  Constant: '<S17>/Constant3'
   *  Constant: '<S17>/Constant4'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTCNTDLOADTAIR1[0],
                rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                ((uint8_T)BKDLOADTAIR_dim));

  /* S-Function (LookUp_IR_U16): '<S21>/LookUp_IR_U16' incorporates:
   *  Constant: '<S17>/Constant5'
   *  Constant: '<S17>/Constant6'
   */
  LookUp_IR_U16( &rtb_LookUp_IR_U16_j, &VTCNTDLOADTAIR2[0],
                rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                ((uint8_T)BKDLOADTAIR_dim));

  /* S-Function (LookUp_IR_S8): '<S19>/LookUp_IR_S8' incorporates:
   *  Constant: '<S17>/Constant7'
   *  Constant: '<S17>/Constant8'
   *
   * Block requirements for '<S17>/Constant7':
   *  1. EISB_FCA6CYL_SW_REQ_1117: Software shall calculate air temperature correction (i.e DLoadTAir... (ECU_SW_Requirements#1146)
   */
  LookUp_IR_S8( &rtb_LookUp_IR_S8, &VTDLOADTAIR[0], rtb_PreLookUpIdSearch_S16_o1,
               rtb_PreLookUpIdSearch_S16_o2, ((uint8_T)BKDLOADTAIR_dim));

  /* DataTypeConversion: '<S18>/Conversion2' incorporates:
   *  Product: '<S18>/Divide'
   */
  *rty_DLoadTAirMax = (int16_T)(rtb_LookUp_IR_S8 / 4);

  /* Sum: '<S17>/Add' */
  *rty_cntEOADLoadThr = (uint16_T)(((uint32_T)rtb_LookUp_IR_U16) + ((uint32_T)
    rtb_LookUp_IR_U16_j));

  /* DataTypeConversion: '<S17>/Data Type Conversion2' */
  *rty_CntDLoadTAir2 = rtb_LookUp_IR_U16_j;

  /* DataTypeConversion: '<S17>/Data Type Conversion1' */
  *rty_CntDLoadTAir1 = rtb_LookUp_IR_U16;

  /* SignalConversion generated from: '<S17>/FlgDLoadTAir' incorporates:
   *  Constant: '<S17>/Constant'
   */
  *rty_FlgDLoadTAir = 1U;

  /* DataTypeConversion: '<S17>/Data Type Conversion' */
  *rty_TAirMax = rtu_TAir;
}

/* Model step function */
void IonKnockAirCorr_10ms(void)
{
  boolean_T cntEOATurboLoadFlg;
  boolean_T cntEOATurboVehFlg;
  boolean_T guard1 = false;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockAirCorr_10ms' incorporates:
   *  Chart: '<S4>/T10ms'
   *
   * Block description for '<S4>/T10ms':
   *  This block determines when update air temperature correction values, i.e. when heatstroke happens. It considers the following conditions:
   *  1. air temperature is greater than a tunable parameter (TAIRDLOADEN).
   *  2. air temperature is greater than its maximum recorded value since last power-on event.
   *  3. engine load is lower than a tunable parameter (TAIRTURBOLOADTHR) for a number of combustions greater or equal than a second tunable parameter (CNTEOATURBOLOADTHR).
   *  4. vehicle speed is lower than a tunable parameter (TAIRTURBOVEHTHR) for a number of combustions greater or equal than a second tunable parameter(CNTEOATURBOVEHSPEEDTHR).
   *
   *  Conditions 3 e 4 are overridden if or TAIRDLOADTURBODIS is different from 0.
   */
  /* Chart: '<S4>/T10ms' incorporates:
   *  Constant: '<S4>/Constant'
   *  Constant: '<S4>/Constant1'
   *  Constant: '<S4>/Constant2'
   *  Constant: '<S4>/Constant3'
   *  Constant: '<S4>/Constant4'
   *  Constant: '<S4>/Constant5'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/TAir'
   *  Inport: '<Root>/VehSpeed'
   *
   * Block description for '<S4>/T10ms':
   *  This block determines when update air temperature correction values, i.e. when heatstroke happens. It considers the following conditions:
   *  1. air temperature is greater than a tunable parameter (TAIRDLOADEN).
   *  2. air temperature is greater than its maximum recorded value since last power-on event.
   *  3. engine load is lower than a tunable parameter (TAIRTURBOLOADTHR) for a number of combustions greater or equal than a second tunable parameter (CNTEOATURBOLOADTHR).
   *  4. vehicle speed is lower than a tunable parameter (TAIRTURBOVEHTHR) for a number of combustions greater or equal than a second tunable parameter(CNTEOATURBOVEHSPEEDTHR).
   *
   *  Conditions 3 e 4 are overridden if or TAIRDLOADTURBODIS is different from 0.
   */
  /* Gateway: T10ms/T10ms */
  /* Event: '<S15>:89' */
  /* During: T10ms/T10ms */
  /* This block determines when update air temperature correction values, i.e. when heatstroke happens. It considers the following conditions:
     1. air temperature is greater than a tunable parameter (TAIRDLOADEN).
     2. air temperature is greater than its maximum recorded value since last power-on event.
     3. engine load is lower than a tunable parameter (TAIRTURBOLOADTHR) for a number of combustions greater or equal than a second tunable parameter (CNTEOATURBOLOADTHR).
     4. vehicle speed is lower than a tunable parameter (TAIRTURBOVEHTHR) for a number of combustions greater or equal than a second tunable parameter(CNTEOATURBOVEHSPEEDTHR).

     Conditions 3 e 4 are overridden if or TAIRDLOADTURBODIS is different from 0.  */
  /* Entry Internal: T10ms/T10ms */
  /* Transition: '<S15>:137' */
  /*  Disable conditions on vehicle speed and engine load  */
  guard1 = false;
  if (((int32_T)TAIRDLOADTURBODIS) != 0) {
    /* Transition: '<S15>:133' */
    /* Transition: '<S15>:152' */
    guard1 = true;
  } else {
    /* Transition: '<S15>:113' */
    cntEOATurboLoadFlg = false;
    cntEOATurboVehFlg = false;

    /* Engine load condition */
    if (Load >= TAIRTURBOLOADTHR) {
      /* Transition: '<S15>:140' */
      /* Transition: '<S15>:132' */
      cntEOATurboLoad = 0U;

      /* Transition: '<S15>:118' */
      /* Transition: '<S15>:123' */
    } else {
      /* Transition: '<S15>:134' */
      /* Time counter condition on engine load */
      if (cntEOATurboLoad < CNTEOATURBOLOADTHR) {
        /* Transition: '<S15>:117' */
        /* Transition: '<S15>:139' */
        cntEOATurboLoad = (uint16_T)((int32_T)(((int32_T)cntEOATurboLoad) + 1));

        /* Transition: '<S15>:123' */
      } else {
        /* Transition: '<S15>:141' */
        cntEOATurboLoadFlg = true;
      }
    }

    /* Transition: '<S15>:143' */
    /* Vehicle speed condition  */
    if (VehSpeed >= TAIRTURBOVEHTHR) {
      /* Transition: '<S15>:142' */
      /* Transition: '<S15>:135' */
      cntEOATurboVeh = 0U;

      /* Transition: '<S15>:127' */
      /* Transition: '<S15>:111' */
    } else {
      /* Transition: '<S15>:128' */
      /* Time counter condition on vehicle speed  */
      if (cntEOATurboVeh < CNTEOATURBOVEHSPEEDTHR) {
        /* Transition: '<S15>:114' */
        /* Transition: '<S15>:129' */
        cntEOATurboVeh = (uint16_T)((int32_T)(((int32_T)cntEOATurboVeh) + 1));

        /* Transition: '<S15>:111' */
      } else {
        /* Transition: '<S15>:115' */
        cntEOATurboVehFlg = true;
      }
    }

    /* Transition: '<S15>:162' */
    if (cntEOATurboVehFlg && cntEOATurboLoadFlg) {
      /* Transition: '<S15>:121' */
      guard1 = true;
    } else {
      /* Transition: '<S15>:166' */
    }
  }

  if (guard1) {
    if (TAir >= TAIRDLOADEN) {
      /* Transition: '<S15>:138':
       *  1. EISB_FCA6CYL_SW_REQ_1119: Software shall calculate air temperature correction (i.e. SATAir) ... (ECU_SW_Requirements#1148)
       */
      if (TAir > TAirMax) {
        /* Outputs for Function Call SubSystem: '<S4>/UpdateDLoad'
         *
         * Block description for '<S4>/UpdateDLoad':
         *  This block evaluates the air temperature correction for engine load threshold (used to enable knock detection strategy).
         *  Moreover it calculates threshold used to update air temperature correction:
         *  -the correction for TAirCAN shall be kept constant for VTCNTDLOADTAIR1 combustions and shall reach the neutral value within VTCNTDLOADTAIR2 combustions.
         */
        /* Transition: '<S15>:124' */
        /* Transition: '<S15>:136':
         *  1. EISB_FCA6CYL_SW_REQ_1117: Software shall calculate air temperature correction (i.e DLoadTAir... (ECU_SW_Requirements#1146)
         */
        /* Event: '<S15>:17' */
        IonKnockAirCorr_UpdateDLoad(TAir, (&(FlgDLoadTAir)), (&(TAirMax)),
          (&(CntDLoadTAir1)), (&(CntDLoadTAir2)), (&(cntEOADLoadThr)),
          (&(DLoadTAirMax)));

        /* End of Outputs for SubSystem: '<S4>/UpdateDLoad' */
        /* Transition: '<S15>:130' */
      } else {
        /* Transition: '<S15>:119' */
      }

      /* Outputs for Function Call SubSystem: '<S4>/UpdateCounter'
       *
       * Block description for '<S4>/UpdateCounter':
       *  Counter is resetted every time that all conditions necessary to detect
       *  heatstroke are valid, in order to mantain stable the air temperature
       *  correction.
       */
      /* Transition: '<S15>:120':
       *  1. EISB_FCA6CYL_SW_REQ_1118: Software shall activate the air temperature correction (i.e. DLoad... (ECU_SW_Requirements#1147)
       */
      /* Event: '<S15>:18' */
      IonKnockAirCorr_UpdateCounter((&(CntEOADLoad)));

      /* End of Outputs for SubSystem: '<S4>/UpdateCounter' */
      /* Transition: '<S15>:172' */
    } else {
      /* Transition: '<S15>:160' */
    }

    /* Transition: '<S15>:173' */
  }

  /* End of Chart: '<S4>/T10ms' */
  /* Transition: '<S15>:175' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockAirCorr_10ms' */
}

/* Model step function */
void IonKnockAirCorr_EOA(void)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_PreLookUpIdSearch_U8_o2;
  int16_T rtb_Look2D_IR_S8;
  uint16_T rtb_PreLookUpIdSearch_U8_o1;
  int32_T SATAirTab_tmp;

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockAirCorr_EOA' incorporates:
   *  Chart: '<S1>/EOA'
   *
   * Block description for '<S1>/EOA':
   *  This chart schedules operation needed to calculate:
   *  -engine load threshold correction.
   *  -spark advance correction.
   *  Both correction are related to heatstroke event.
   */
  /* Chart: '<S1>/EOA' incorporates:
   *  Product: '<S10>/Divide'
   *  Product: '<S11>/Divide'
   *  Product: '<S7>/Divide1'
   *  Product: '<S7>/Divide2'
   *  Product: '<S9>/Divide'
   *
   * Block description for '<S1>/EOA':
   *  This chart schedules operation needed to calculate:
   *  -engine load threshold correction.
   *  -spark advance correction.
   *  Both correction are related to heatstroke event.
   *
   * Block requirements for '<S7>/Divide1':
   *  1. EISB_FCA6CYL_SW_REQ_1118: Software shall activate the air temperature correction (i.e. DLoad... (ECU_SW_Requirements#1147)
   *
   * Block requirements for '<S7>/Divide2':
   *  1. EISB_FCA6CYL_SW_REQ_1119: Software shall calculate air temperature correction (i.e. SATAir) ... (ECU_SW_Requirements#1148)
   */
  /* Gateway: EOA/EOA */
  /* Event: '<S5>:49' */
  /* During: EOA/EOA */
  /* This chart schedules operation needed to calculate:
     -engine load threshold correction.
     -spark advance correction.
     Both correction are related to heatstroke event. */
  /* Entry Internal: EOA/EOA */
  /* Transition: '<S5>:2' */
  if ((TAirMax != ((int16_T)MIN_TAIRMAX_VALUE)) && (CntEOADLoad < cntEOADLoadThr))
  {
    /* Outputs for Function Call SubSystem: '<S6>/SATAirTab_Calc'
     *
     * Block description for '<S6>/SATAirTab_Calc':
     *  This block evaluates spark advance correction for heatstroke,
     *  according to engine speed and engine load.
     */
    /* S-Function (PreLookUpIdSearch_U16): '<S13>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S8>/Constant'
     *  Constant: '<S8>/Constant1'
     */
    /* Transition: '<S5>:19':
     *  1. EISB_FCA6CYL_SW_REQ_1118: Software shall activate the air temperature correction (i.e. DLoad... (ECU_SW_Requirements#1147)
     */
    /* Transition: '<S5>:24':
     *  1. EISB_FCA6CYL_SW_REQ_1119: Software shall calculate air temperature correction (i.e. SATAir) ... (ECU_SW_Requirements#1148)
     */
    /* Event: '<S5>:10' */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                          &rtb_PreLookUpIdSearch_U16_o2, Rpm, &BKRPMSAAIR[0],
                          ((uint8_T)BKRPMSAAIR_dim));

    /* DataTypeConversion: '<S14>/Data Type Conversion4' incorporates:
     *  Inport: '<Root>/Load'
     */
    rtb_PreLookUpIdSearch_U8_o1 = Load;

    /* S-Function (PreLookUpIdSearch_U8): '<S14>/PreLookUpIdSearch_U8' incorporates:
     *  Constant: '<S8>/Constant2'
     *  Constant: '<S8>/Constant3'
     *  Constant: '<S8>/Constant5'
     */
    PreLookUpIdSearch_U8( &rtb_PreLookUpIdSearch_U8_o1,
                         &rtb_PreLookUpIdSearch_U8_o2,
                         rtb_PreLookUpIdSearch_U8_o1, &BKLOADSAAIR[0], ((uint8_T)
      8U), ((uint8_T)BKLOADSAAIR_dim));

    /* S-Function (Look2D_IR_S8): '<S12>/Look2D_IR_S8' incorporates:
     *  Constant: '<S8>/Constant1'
     *  Constant: '<S8>/Constant3'
     *  Constant: '<S8>/Constant4'
     *
     * Block requirements for '<S8>/Constant4':
     *  1. EISB_FCA6CYL_SW_REQ_1119: Software shall calculate air temperature correction (i.e. SATAir) ... (ECU_SW_Requirements#1148)
     */
    Look2D_IR_S8( &rtb_Look2D_IR_S8, &TBSATAIR[0], rtb_PreLookUpIdSearch_U16_o1,
                 rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKRPMSAAIR_dim),
                 rtb_PreLookUpIdSearch_U8_o1, rtb_PreLookUpIdSearch_U8_o2,
                 ((uint8_T)BKLOADSAAIR_dim));

    /* Product: '<S11>/Divide' */
    SATAirTab_tmp = ((int32_T)rtb_Look2D_IR_S8) / 16;
    SATAirTab = (int16_T)SATAirTab_tmp;

    /* End of Outputs for SubSystem: '<S6>/SATAirTab_Calc' */
    /*  First part for corrections */
    if (CntEOADLoad <= CntDLoadTAir1) {
      /* Transition: '<S5>:26' */
      /* Transition: '<S5>:28' */
      /* Keep constant corrections */
      DLoadTAir = DLoadTAirMax;
      SATAir = SATAirTab;

      /* Transition: '<S5>:43' */
    } else {
      /* Outputs for Function Call SubSystem: '<S6>/DLoad_SATAir_Calc'
       *
       * Block description for '<S6>/DLoad_SATAir_Calc':
       *  This block calcuates a smooth decrease for spark advance correction
       *  and engine load threshold correction.
       */
      /* MinMax: '<S7>/MinMax' */
      /* Transition: '<S5>:42' */
      /* Evaluate smooth decrease for corrections */
      /* Event: '<S5>:45' */
      if (((int32_T)CntDLoadTAir2) > 1) {
        rtb_PreLookUpIdSearch_U8_o1 = CntDLoadTAir2;
      } else {
        rtb_PreLookUpIdSearch_U8_o1 = 1U;
      }

      /* End of MinMax: '<S7>/MinMax' */

      /* Sum: '<S7>/Add1' incorporates:
       *  Product: '<S7>/Divide'
       *  Sum: '<S7>/Add'
       */
      rtb_PreLookUpIdSearch_U8_o1 = (uint16_T)(32768U - ((uint32_T)((uint16_T)
        ((((uint32_T)((uint16_T)(((uint32_T)CntEOADLoad) - ((uint32_T)
        CntDLoadTAir1)))) << ((uint32_T)15)) / ((uint32_T)
        rtb_PreLookUpIdSearch_U8_o1)))));
      DLoadTAir = (int16_T)((((int32_T)DLoadTAirMax) * ((int32_T)
        rtb_PreLookUpIdSearch_U8_o1)) / 32768);

      /* Outputs for Function Call SubSystem: '<S6>/SATAirTab_Calc'
       *
       * Block description for '<S6>/SATAirTab_Calc':
       *  This block evaluates spark advance correction for heatstroke,
       *  according to engine speed and engine load.
       */
      SATAir = (int16_T)((SATAirTab_tmp * ((int32_T)rtb_PreLookUpIdSearch_U8_o1))
                         / 32768);

      /* End of Outputs for SubSystem: '<S6>/SATAirTab_Calc' */
      /* End of Outputs for SubSystem: '<S6>/DLoad_SATAir_Calc' */
    }

    /* Transition: '<S5>:39' */
    CntEOADLoad = (uint16_T)((int32_T)(((int32_T)CntEOADLoad) + 1));
  } else {
    /* Transition: '<S5>:22' */
    /* Reset outputs */
    DLoadTAir = 0;
    SATAir = 0;
    TAirMax = ((int16_T)MIN_TAIRMAX_VALUE);
    FlgDLoadTAir = 0U;

    /* Transition: '<S5>:48' */
  }

  /* End of Chart: '<S1>/EOA' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockAirCorr_EOA' */
}

/* Model step function */
void IonKnockAirCorr_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockAirCorr_PowerOn' incorporates:
   *  SubSystem: '<Root>/PowerOn'
   *
   * Block description for '<Root>/PowerOn':
   *  Runnable at PowerOn, performs outputs initialization.
   *
   * Block requirements for '<Root>/PowerOn':
   *  1. EISB_FCA6CYL_SW_REQ_1634: Software shall set to 0 each output produced for Air Temperature C... (ECU_SW_Requirements#3042)
   */
  /* SignalConversion generated from: '<S2>/CntEOADLoad' incorporates:
   *  Constant: '<S2>/Constant'
   */
  CntEOADLoad = 0U;

  /* SignalConversion generated from: '<S2>/FlgDLoadTAir' incorporates:
   *  Constant: '<S2>/Constant1'
   */
  FlgDLoadTAir = 0U;

  /* SignalConversion generated from: '<S2>/TAirMax' incorporates:
   *  Constant: '<S2>/Constant3'
   */
  TAirMax = ((int16_T)MIN_TAIRMAX_VALUE);

  /* SignalConversion generated from: '<S2>/CntDLoadTAir1' incorporates:
   *  Constant: '<S2>/Constant4'
   */
  CntDLoadTAir1 = 0U;

  /* SignalConversion generated from: '<S2>/CntDLoadTAir2' incorporates:
   *  Constant: '<S2>/Constant5'
   */
  CntDLoadTAir2 = 0U;

  /* SignalConversion generated from: '<S2>/cntEOADLoadThr' incorporates:
   *  Constant: '<S2>/Constant6'
   */
  cntEOADLoadThr = 0U;

  /* SignalConversion generated from: '<S2>/DLoadTAirMax' incorporates:
   *  Constant: '<S2>/Constant2'
   */
  DLoadTAirMax = 0;

  /* SignalConversion generated from: '<S2>/DLoadTAir' incorporates:
   *  Constant: '<S2>/Constant7'
   */
  DLoadTAir = 0;

  /* SignalConversion generated from: '<S2>/SATAir' incorporates:
   *  Constant: '<S2>/Constant8'
   */
  SATAir = 0;

  /* SignalConversion generated from: '<S2>/SATAirTab' incorporates:
   *  Constant: '<S2>/Constant9'
   */
  SATAirTab = 0;

  /* SignalConversion generated from: '<S2>/cntEOATurboVeh' incorporates:
   *  Constant: '<S2>/Constant10'
   */
  cntEOATurboVeh = 0U;

  /* SignalConversion generated from: '<S2>/cntEOATurboLoad' incorporates:
   *  Constant: '<S2>/Constant11'
   */
  cntEOATurboLoad = 0U;

  /* Constant: '<S2>/Constant12' */
  IdVer_IonKnockAirCorr = ID_VER_IONKNOCKAIRCORR_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockAirCorr_PowerOn' */
}

/* Model initialize function */
void IonKnockAirCorr_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

int16_T DLoadTAir;
int16_T SATAir;
void IonKnockAirCorr_Stub(void)
{
  DLoadTAir = 0;
  SATAir = 0;
}

void IonKnockAirCorr_PowerOn(void)
{
  IonKnockAirCorr_Stub();
}

void IonKnockAirCorr_EOA(void)
{
  IonKnockAirCorr_Stub();
}

void IonKnockAirCorr_10ms(void)
{
  IonKnockAirCorr_Stub();
}

#endif

/* _BUILD_IONKNOCKAIRCORR */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/