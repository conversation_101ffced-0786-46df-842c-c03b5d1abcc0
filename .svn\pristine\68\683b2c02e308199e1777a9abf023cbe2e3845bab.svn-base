/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_dpll.h
 * @brief   SPC5xx GTM DPLL header file.
 *
 * @addtogroup DPLL
 * @{
 */

#ifndef _GTM_DPLL_H_
#define _GTM_DPLL_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"
#include "task.h"

/*lint -e621*/
/**
 * @name    DPLL definitions
 * @{
 */

/** Number of channels */
#define SPC5_GTM_DPLL_CHANNELS                                                    8U


/** DPLL IRQ Disable Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_DISABLE                                          0x00000001UL
/** DPLL IRQ Enable Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_ENABLE                                           0x00000002UL
/** DPLL IRQ Trigger Minimum Hold Time Violation Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_MIN_HOLD_TIME_VIOLATION                  0x00000004UL
/** DPLL IRQ Trigger Maximum Hold Time Violation Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_MAX_HOLD_TIME_VIOLATION                  0x00000008UL
/** DPLL IRQ State Inactive Slope Detected Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_STATE_INACTIVE_SLOPE_DETECTED                    0x00000010UL
/** DPLL IRQ Trigger Inactive Slope Detected Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_INACTIVE_SLOPE_DETECTED                  0x00000020UL
/** DPLL IRQ Missing State Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_MISSING_STATE                                    0x00000040UL
/** DPLL IRQ Missing Trigger Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_MISSING_TRIGGER                                  0x00000080UL
/** DPLL IRQ State Active Slope Detected Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_STATE_ACTIVE_SLOPE_DETECTED                      0x00000100UL
/** DPLL IRQ Trigger Active Slope Detection Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_ACTIVE_SLOPE_DETECTION                   0x00000200UL
/** DPLL IRQ Trigger Plausibility Window Request Violation Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_PLAUSIBILITY_WINDOW_REQUEST_VIOLATION    0x00000400UL
/** DPLL IRQ Write Access RAN Region 2 Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_WRITE_ACCESS_RAM_REGION_2                        0x00000800UL
/** DPLL IRQ Write Access RAM Region 1c 1b Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_WRITE_ACCESS_RAM_REGION_1C_1B                    0x00001000UL
/** DPLL IRQ SUB_INC1 Get Lock Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_SUB_INC1_GET_LOCK                                0x00002000UL
/** DPLL IRQ SUB_INC1 Loss Lock Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_SUB_INC1_LOSS_LOCK                               0x00004000UL
/** DPLL IRQ Error Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_ERROR_INTERRUPT                                  0x00008000UL
/** DPLL IRQ SUB_INC2 Get Lock Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_SUB_INC2_GET_LOCK                                0x00010000UL
/** DPLL IRQ SUB_INC2 Loss Lock Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_SUB_INC2_LOSS_LOCK                               0x00020000UL
/** DPLL IRQ Trigger Event 0 Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_EVENT_0                                  0x00040000UL
/** DPLL IRQ Trigger Event 1 Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_EVENT_1                                  0x00080000UL
/** DPLL IRQ Trigger Event 2 Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_EVENT_2                                  0x00100000UL
/** DPLL IRQ Trigger Event 3 Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_EVENT_3                                  0x00200000UL
/** DPLL IRQ Trigger Event 4 Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_EVENT_4                                  0x00400000UL
/** DPLL IRQ Trigger Calculated Duration Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_CALCULATED_DURATION                      0x00800000UL
/** DPLL IRQ State Calculated Duration Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_STATE_CALCULATED_DURATION                        0x01000000UL
/** DPLL IRQ Trigger Out of Range Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_OUT_OF_RANGE                             0x02000000UL
/** DPLL IRQ State Out of Range Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_STATE_OUT_OF_RANGE                               0x04000000UL
/** DPLL IRQ Trigger Direction Change Interrupt Notified */
#define SPC5_GTM_DPLL_IRQ_STATUS_TRIGGER_DIRECTION_CHANGE                         0x08000000UL


/** DPLL IRQ Disable Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_DISABLE                                          0x00000001UL
/** DPLL IRQ Enable Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_ENABLE                                           0x00000002UL
/** DPLL IRQ Trigger Minimum Hold Time Violation Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_MIN_HOLD_TIME_VIOLATION                  0x00000004UL
/** DPLL IRQ Trigger Maximum Hold Time Violation Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_MAX_HOLD_TIME_VIOLATION                  0x00000008UL
/** DPLL IRQ State Inactive Slope Detected Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_STATE_INACTIVE_SLOPE_DETECTED                    0x00000010UL
/** DPLL IRQ Trigger Inactive Slope Detected Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_INACTIVE_SLOPE_DETECTED                  0x00000020UL
/** DPLL IRQ Missing State Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_MISSING_STATE                                    0x00000040UL
/** DPLL IRQ Missing Trigger Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_MISSING_TRIGGER                                  0x00000080UL
/** DPLL IRQ State Active Slope Detected Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_STATE_ACTIVE_SLOPE_DETECTED                      0x00000100UL
/** DPLL IRQ Trigger Active Slope Detection Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_ACTIVE_SLOPE_DETECTION                   0x00000200UL
/** DPLL IRQ Trigger Plausibility Window Request Violation Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_PLAUSIBILITY_WINDOW_REQUEST_VIOLATION    0x00000400UL
/** DPLL IRQ Write Access RAN Region 2 Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_WRITE_ACCESS_RAM_REGION_2                        0x00000800UL
/** DPLL IRQ Write Access RAM Region 1c 1b Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_WRITE_ACCESS_RAM_REGION_1C_1B                    0x00001000UL
/** DPLL IRQ SUB_INC1 Get Lock Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_SUB_INC1_GET_LOCK                                0x00002000UL
/** DPLL IRQ SUB_INC1 Loss Lock Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_SUB_INC1_LOSS_LOCK                               0x00004000UL
/** DPLL IRQ Error Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_ERROR_INTERRUPT                                  0x00008000UL
/** DPLL IRQ SUB_INC2 Get Lock Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_SUB_INC2_GET_LOCK                                0x00010000UL
/** DPLL IRQ SUB_INC2 Loss Lock Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_SUB_INC2_LOSS_LOCK                               0x00020000UL
/** DPLL IRQ Trigger Event 0 Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_EVENT_0                                  0x00040000UL
/** DPLL IRQ Trigger Event 1 Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_EVENT_1                                  0x00080000UL
/** DPLL IRQ Trigger Event 2 Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_EVENT_2                                  0x00100000UL
/** DPLL IRQ Trigger Event 3 Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_EVENT_3                                  0x00200000UL
/** DPLL IRQ Trigger Event 4 Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_EVENT_4                                  0x00400000UL
/** DPLL IRQ Trigger Calculated Duration Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_CALCULATED_DURATION                      0x00800000UL
/** DPLL IRQ State Calculated Duration Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_STATE_CALCULATED_DURATION                        0x01000000UL
/** DPLL IRQ Trigger Out of Range Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_OUT_OF_RANGE                             0x02000000UL
/** DPLL IRQ State Out of Range Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_STATE_OUT_OF_RANGE                               0x04000000UL
/** DPLL IRQ Trigger Direction Change Interrupt Enabled */
#define SPC5_GTM_DPLL_IRQ_ENABLE_TRIGGER_DIRECTION_CHANGE                         0x08000000UL


/** DPLL IRQ Disable Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_DISABLE                                       0x00000001UL
/** DPLL IRQ Enable Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_ENABLE                                        0x00000002UL
/** DPLL IRQ Trigger Minimum Hold Time Violation Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_MIN_HOLD_TIME_VIOLATION               0x00000004UL
/** DPLL IRQ Trigger Maximum Hold Time Violation Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_MAX_HOLD_TIME_VIOLATION               0x00000008UL
/** DPLL IRQ State Inactive Slope Detected Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_STATE_INACTIVE_SLOPE_DETECTED                 0x00000010UL
/** DPLL IRQ Trigger Inactive Slope Detected Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_INACTIVE_SLOPE_DETECTED               0x00000020UL
/** DPLL IRQ Missing State Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_MISSING_STATE                                 0x00000040UL
/** DPLL IRQ Missing Trigger Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_MISSING_TRIGGER                               0x00000080UL
/** DPLL IRQ State Active Slope Detected Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_STATE_ACTIVE_SLOPE_DETECTED                   0x00000100UL
/** DPLL IRQ Trigger Active Slope Detection Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_ACTIVE_SLOPE_DETECTION                0x00000200UL
/** DPLL IRQ Trigger Plausibility Window Request Violation Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_PLAUSIBILITY_WINDOW_REQUEST_VIOLATION 0x00000400UL
/** DPLL IRQ Write Access RAN Region 2 Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_WRITE_ACCESS_RAM_REGION_2                     0x00000800UL
/** DPLL IRQ Write Access RAM Region 1c 1b Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_WRITE_ACCESS_RAM_REGION_1C_1B                 0x00001000UL
/** DPLL IRQ SUB_INC1 Get Lock Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_SUB_INC1_GET_LOCK                             0x00002000UL
/** DPLL IRQ SUB_INC1 Loss Lock Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_SUB_INC1_LOSS_LOCK                            0x00004000UL
/** DPLL IRQ Error Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_ERROR_INTERRUPT                               0x00008000UL
/** DPLL IRQ SUB_INC2 Get Lock Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_SUB_INC2_GET_LOCK                             0x00010000UL
/** DPLL IRQ SUB_INC2 Loss Lock Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_SUB_INC2_LOSS_LOCK                            0x00020000UL
/** DPLL IRQ Trigger Event 0 Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_EVENT_0                               0x00040000UL
/** DPLL IRQ Trigger Event 1 Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_EVENT_1                               0x00080000UL
/** DPLL IRQ Trigger Event 2 Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_EVENT_2                               0x00100000UL
/** DPLL IRQ Trigger Event 3 Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_EVENT_3                               0x00200000UL
/** DPLL IRQ Trigger Event 4 Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_EVENT_4                               0x00400000UL
/** DPLL IRQ Trigger Calculated Duration Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_CALCULATED_DURATION                   0x00800000UL
/** DPLL IRQ State Calculated Duration Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_STATE_CALCULATED_DURATION                     0x01000000UL
/** DPLL IRQ Trigger Out of Range Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_OUT_OF_RANGE                          0x02000000UL
/** DPLL IRQ State Out of Range Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_STATE_OUT_OF_RANGE                            0x04000000UL
/** DPLL IRQ Trigger Direction Change Interrupt Forced */
#define SPC5_GTM_DPLL_IRQ_FORCE_INT_TRIGGER_DIRECTION_CHANGE                      0x08000000UL


/** DPLL IRQ Mode Level */
#define SPC5_GTM_DPLL_IRQ_MODE_LEVEL                                              0U
/** DPLL IRQ Mode Pulse */
#define SPC5_GTM_DPLL_IRQ_MODE_PULSE                                              1U
/** DPLL IRQ Mode Pulse-Notify */
#define SPC5_GTM_DPLL_IRQ_MODE_PULSE_NOTIFY                                       2U
/** DPLL IRQ Mode Single-Pulse Mode */
#define SPC5_GTM_DPLL_IRQ_MODE_SINGLE_PULSE                                       3U

/** RAM2 offset to 128 for each section (2a, 2b, 2c, 2d) */
#define SPC5_GTM_DPLL_RAM2_OFFSET_128                                             0U
/** RAM2 offset to 256 for each section (2a, 2b, 2c, 2d) */
#define SPC5_GTM_DPLL_RAM2_OFFSET_256                                             1U
/** RAM2 offset to 512 for each section (2a, 2b, 2c, 2d) */
#define SPC5_GTM_DPLL_RAM2_OFFSET_512                                             2U
/** RAM2 offset to 1024 for each section (2a, 2b, 2c, 2d) */
#define SPC5_GTM_DPLL_RAM2_OFFSET_1024                                            3U

/** DPLL RAM Region 2A: RDT_Tx is addressed */
#define SC5_GTM_DPLL_RAM_REGION_2A                                                0UL
/** DPLL RAM Region 2B: TSF_Tx is addressed */
#define SC5_GTM_DPLL_RAM_REGION_2B                                                1UL
/** DPLL RAM Region 2C: ADT_Tx is addressed */
#define SC5_GTM_DPLL_RAM_REGION_2C                                                2UL
/** DPLL RAM Region 2D: DT_Tx is addressed */
#define SC5_GTM_DPLL_RAM_REGION_2D                                                3UL


/** DPLL Trigger/State Slope Mode: No Slope */
#define SPC5_GTM_DPLL_NO_SLOPE                                                    0U
/** DPLL Trigger/State Slope Mode: Low Slope */
#define SPC5_GTM_DPLL_SLOPE_LOW                                                   1U
/** DPLL Trigger/State Slope Mode: High Slope */
#define SPC5_GTM_DPLL_SLOPE_HIGH                                                  2U
/** DPLL Trigger/State Slope Mode: Low & High Slope */
#define SPC5_GTM_DPLL_SLOPE_HIGH_AND_LOW                                          3U


/** DPLL Trigger Plausibility is Position related */
#define SPC5_GTM_DPLL_POSITION_RELATED_PLAUSIBILITY                               0U
/** DPLL Trigger Plausibility is Time related */
#define SPC5_GTM_DPLL_TIME_RELATED_PLAUSIBILITY                                   1U


#define SPC5_GTM_DPLL_PMT_ID_0                                                    0U
#define SPC5_GTM_DPLL_PMT_ID_1                                                    1U
#define SPC5_GTM_DPLL_PMT_ID_2                                                    2U
#define SPC5_GTM_DPLL_PMT_ID_3                                                    3U
#define SPC5_GTM_DPLL_PMT_ID_4                                                    4U
#define SPC5_GTM_DPLL_PMT_ID_5                                                    5U
#define SPC5_GTM_DPLL_PMT_ID_6                                                    6U
#define SPC5_GTM_DPLL_PMT_ID_7                                                    7U
#define SPC5_GTM_DPLL_PMT_ID_8                                                    8U
#define SPC5_GTM_DPLL_PMT_ID_9                                                    9U
#define SPC5_GTM_DPLL_PMT_ID_10                                                   10U
#define SPC5_GTM_DPLL_PMT_ID_11                                                   11U
#define SPC5_GTM_DPLL_PMT_ID_12                                                   12U
#define SPC5_GTM_DPLL_PMT_ID_13                                                   13U
#define SPC5_GTM_DPLL_PMT_ID_14                                                   14U
#define SPC5_GTM_DPLL_PMT_ID_15                                                   15U
#define SPC5_GTM_DPLL_PMT_ID_16                                                   16U
#define SPC5_GTM_DPLL_PMT_ID_17                                                   17U
#define SPC5_GTM_DPLL_PMT_ID_18                                                   18U
#define SPC5_GTM_DPLL_PMT_ID_19                                                   19U
#define SPC5_GTM_DPLL_PMT_ID_20                                                   20U
#define SPC5_GTM_DPLL_PMT_ID_21                                                   21U
#define SPC5_GTM_DPLL_PMT_ID_22                                                   22U
#define SPC5_GTM_DPLL_PMT_ID_23                                                   23U


#define SPC5_GTM_DPLL_ACTION_ID_0                                                 0UL
#define SPC5_GTM_DPLL_ACTION_ID_1                                                 1UL
#define SPC5_GTM_DPLL_ACTION_ID_2                                                 2UL
#define SPC5_GTM_DPLL_ACTION_ID_3                                                 3UL
#define SPC5_GTM_DPLL_ACTION_ID_4                                                 4UL
#define SPC5_GTM_DPLL_ACTION_ID_5                                                 5UL
#define SPC5_GTM_DPLL_ACTION_ID_6                                                 6UL
#define SPC5_GTM_DPLL_ACTION_ID_7                                                 7UL
#define SPC5_GTM_DPLL_ACTION_ID_8                                                 8UL
#define SPC5_GTM_DPLL_ACTION_ID_9                                                 9UL
#define SPC5_GTM_DPLL_ACTION_ID_10                                                10UL
#define SPC5_GTM_DPLL_ACTION_ID_11                                                11UL
#define SPC5_GTM_DPLL_ACTION_ID_12                                                12UL
#define SPC5_GTM_DPLL_ACTION_ID_13                                                13UL
#define SPC5_GTM_DPLL_ACTION_ID_14                                                14UL
#define SPC5_GTM_DPLL_ACTION_ID_15                                                15UL
#define SPC5_GTM_DPLL_ACTION_ID_16                                                16UL
#define SPC5_GTM_DPLL_ACTION_ID_17                                                17UL
#define SPC5_GTM_DPLL_ACTION_ID_18                                                18UL
#define SPC5_GTM_DPLL_ACTION_ID_19                                                19UL
#define SPC5_GTM_DPLL_ACTION_ID_20                                                20UL
#define SPC5_GTM_DPLL_ACTION_ID_21                                                21UL
#define SPC5_GTM_DPLL_ACTION_ID_22                                                22UL
#define SPC5_GTM_DPLL_ACTION_ID_23                                                23UL

/* Adapt and profile: interrupt values for trigger */
#define SPC5_GTM_DPLL_ADT_TINT_0                                                  (1UL << 13)
#define SPC5_GTM_DPLL_ADT_TINT_1                                                  (2UL << 13)
#define SPC5_GTM_DPLL_ADT_TINT_2                                                  (3UL << 13)
#define SPC5_GTM_DPLL_ADT_TINT_3                                                  (4UL << 13)
#define SPC5_GTM_DPLL_ADT_TINT_4                                                  (5UL << 13)

/* DPLL Status register (to be completed) */
#define SPC5_GTM_DPLL_STATUS_LOCK1                                                0x40000000UL


/* Memory mapped registers */
#if (SPC5_HAS_GTM_IP_101 == TRUE) || (SPC5_HAS_GTM_IP_122 == TRUE)
#define SPC5_GTM_DPLL_TRIGGER_TIME_OUT_VALUE_REG                                 0xFFD28430UL
#elif (SPC5_HAS_GTM_IP_343 == TRUE)
#define SPC5_GTM_DPLL_TRIGGER_TIME_OUT_VALUE_REG                                 0xF7D28430UL
#elif (SPC5_HAS_GTM_IP_344 == TRUE)
#define SPC5_GTM_DPLL_TRIGGER_TIME_OUT_VALUE_REG                                 0xF7C88430UL
#endif
/** @} */

/**
 * @brief Type of a structure representing a (GTM-IP) DPLL driver.
 */
typedef struct GTM_DPLLDriver GTM_DPLLDriver;

/**
 * @brief   (GTM-IP) DPLL notification callback type.
 *
 * @param[in] dplld     pointer to the @p DPLLDriver object triggering the callback
 * @param[in] channel   channel triggering the callback
 */
typedef void (*gtm_dpll_callback_t)(GTM_DPLLDriver *dplld, uint32_t irq_num);

/**
 * @brief   Structure representing a (GTM) DPLL driver.
 */
struct GTM_DPLLDriver {

  /**
   * @brief Pointer to the (GTM) DPLL registers block.
   */
	volatile GTM_DPLL_TAG *dpll;

  /**
   * @brief Interrupts callbacks.
   */
	gtm_dpll_callback_t *callbacks;

  /**
   * @brief DPLL RAM base address.
   */
	uint32_t ram_base_address;

  /**
   * @brief Pointer for application private data.
   */
    void *priv;
};

/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_DPLL0 == TRUE) && !defined(__DOXYGEN__)
extern GTM_DPLLDriver DPLLD1;
#endif

#ifdef __cplusplus
extern "C" {
#endif
extern void gtm_dpllInit(void);

extern void gtm_dpllInitRAM(GTM_DPLLDriver *dplld);
extern void gtm_dpllSetRAM2Offset(GTM_DPLLDriver *dplld, uint8_t size);
extern uint32_t *gtm_dpllRAMBaseAddress(GTM_DPLLDriver *dplld, uint32_t region);

extern void gtm_dpllSetIRQMode(GTM_DPLLDriver *dplld, uint8_t mode);

extern void gtm_dpllEnableInt(GTM_DPLLDriver *dplld, uint32_t int_num);
extern void gtm_dpllNotifyInt(GTM_DPLLDriver *dplld, uint32_t int_num);
extern void gtm_dpllAckInt(GTM_DPLLDriver *dplld, uint32_t int_num);
extern void gtm_dpllDisableInt(GTM_DPLLDriver *dplld, uint32_t int_num);

extern uint32_t gtm_dpllGetIntStatus(GTM_DPLLDriver *dplld);
extern uint32_t gtm_dpllGetIntEnabled(GTM_DPLLDriver *dplld);


extern void gtm_dpllSetMultiplier(GTM_DPLLDriver *dplld, uint32_t mlt);
extern void gtm_dpllSetTriggerNumber(GTM_DPLLDriver *dplld, uint32_t tnu);
extern void gtm_dpllSetStateNumber(GTM_DPLLDriver *dplld, uint8_t snu);

extern void gtm_dpllEnableTrigger(GTM_DPLLDriver *dplld);
extern void gtm_dpllDisableTrigger(GTM_DPLLDriver *dplld);

extern void gtm_dpllEnableState(GTM_DPLLDriver *dplld);
extern void gtm_dpllDisableState(GTM_DPLLDriver *dplld);

extern void gtm_dpllTriggerSlope(GTM_DPLLDriver *dplld, uint8_t slope);
extern void gtm_dpllStateSlope(GTM_DPLLDriver *dplld, uint8_t slope);

extern void gtm_dpllTriggerSyncNum(GTM_DPLLDriver *dplld, uint8_t num);
extern void gtm_dpllStateSyncNum(GTM_DPLLDriver *dplld, uint8_t num);

extern void gtm_dpllTriggerPlausibility(GTM_DPLLDriver *dplld, uint8_t value);

extern void gtm_dpllEnable(GTM_DPLLDriver *dplld);
extern void gtm_dpllDisable(GTM_DPLLDriver *dplld);

extern void gtm_dpllEnableSubInc1(GTM_DPLLDriver *dplld);
extern void gtm_dpllDisableSubInc1(GTM_DPLLDriver *dplld);
extern void gtm_dpllEnableSubInc2(GTM_DPLLDriver *dplld);
extern void gtm_dpllDisableSubInc2(GTM_DPLLDriver *dplld);

extern void gtm_dpllTriggerRAM2C(GTM_DPLLDriver *dplld, uint32_t value);

extern void gtm_dpllSetPMT(GTM_DPLLDriver *dplld, uint8_t pmt_id, uint32_t source);
extern void gtm_dpllEnableAction(GTM_DPLLDriver *dplld, uint32_t action_id);

extern void gtm_dpllSetMemoryRegister(GTM_DPLLDriver *dplld, uint32_t reg, uint32_t value);

extern uint32_t gtm_dpllGetStatus(GTM_DPLLDriver *dplld);

#ifdef __cplusplus
}
#endif

/*lint +e621*/
#endif /* _GTM_DPLL_H_ */
/** @} */
