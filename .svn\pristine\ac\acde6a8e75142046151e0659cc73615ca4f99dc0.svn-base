/*****************************************************************************************************************/
/* $HeadURL:: https://172.26.1.29/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_RS_35_PI_0204/tree/DD/CANMGMI#$  */
/* $Revision:: 200525                                                                                         $  */
/* $Date:: 2021-12-23 12:56:33 +0100 (gio, 23 dic 2021)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmIn
**  Filename        :  CanMgmIn_BR.h
**  Created on      :  07-jul-2023 09:37:00
**  Original author :  SantoroR
******************************************************************************/

#ifndef CANMGMIN_BR_H
#define CANMGMIN_BR_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Canmgmin_out.h"
#include "Canmgm_out.h"
#include "Canmgmout_out.h"
#include "Mcan_out.h"
#include "mathlib.h"
#include "AnalogIn_out.h"
#include "DigIn_out.h"
#include "Diagmgm_out.h"
#include "utils.h"
#include "Diagcanmgm_out.h"
#include "SyncMgm_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define     CAN_ERROR_CNT_THR   5u   // = 5*Period message
#define     TIME_E_DATE_100_2_1000MS    10u

#define     MAX_LOAD    (510u*128u)   // = 510% con risoluzione 1/128
#define     CHECK_ANY_ABSENT_THR   5u   // = 5*10ms 50ms non messages

#define     VALID_VD        0u
#define     NOT_VALID_VD    1u

#define     BKRPMTIPIN_dim  5u /* Number of element used in vector. */
#define     BKTOILPOIL_dim  5u
#define     BKRPMPOIL_dim   12u


///ENCRCALIVETEST values
#define     CAN_ALIVE_TEST_MASK 0x01u
#define     CAN_CRC_TEST_MASK 0x02u


/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
#pragma ghs startnomisra

typedef struct ECM_EISB_1_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum_ECM_10   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  EngStSCAN1   :3;
            uint8_T Dummy1_1   :1;
            uint8_T  CANMsgCnt_ECM_11   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  LoadCAN2   :8;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TAirCAN3   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TWatCAN4   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  RpmCAN5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  RpmCAN6   :8;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy7_0   :1;
            uint8_T  IgnCutOff7   :1;
            uint8_T  DesCutOff7   :1;
            uint8_T  VDRpmCAN7   :1;
            uint8_T  VDTWaterCAN7   :1;
            uint8_T  FlgNoTrqCtrSACAN7   :1;
            uint8_T  VDLoadCAN7   :1;
            uint8_T  VDTAirCAN7   :1;
        } B;
    } Byte7;

} ECM_EISB_1_T;

typedef struct ECM_EISB_2_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MsgChecksum_ECM_20   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy1_0   :4;
            uint8_T  CANMsgCnt_ECM_21   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Lam_Obj_12   :8;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Lam_Obj_23   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Lam_Sens_14   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Lam_Sens_25   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  RONLvlSt_OtherEngine6  :2;
            uint8_T  RefuelSt6   :1;
            uint8_T  AFR_CL_Active6   :1;
            uint8_T  ReqResetCylCorrAd6   :1;
            uint8_T  Cyl_Bal_CL_Request6   :1;
            uint8_T  VD_Lam_Sens_16   :1;
            uint8_T  VD_Lam_Sens_26   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy7_0   :5;
            uint8_T  RON_OtherEngine7   :3;
        } B;
    } Byte7;

} ECM_EISB_2_T;

#pragma ghs endnomisra

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST uint16_T TNOCANDIAGAFTKEYON;
extern CALQUAL CALQUAL_POST uint16_T CANVBATTHRMIN;
extern CALQUAL CALQUAL_POST uint8_T  THRCNTRPMCANBLOCKED;
extern CALQUAL CALQUAL_POST uint8_T TIMDIAGRPMDIS;
extern CALQUAL CALQUAL_POST int16_T TAIRREC;
extern CALQUAL CALQUAL_POST int16_T TWATERREC;
extern CALQUAL CALQUAL_POST uint16_T LOADREC;
extern CALQUAL CALQUAL_POST int16_T THDELTALOADCAN;
extern CALQUAL CALQUAL_POST uint8_T THDEBIGNITIONCUTOFF;
extern CALQUAL CALQUAL_POST uint8_T THDEBPTFAULTKEYSIG;
extern CALQUAL CALQUAL_POST uint8_T LOADCANVALASREC;
extern CALQUAL CALQUAL_POST uint8_T TAIRCANVALASREC;
extern CALQUAL CALQUAL_POST uint8_T TWATERCANVALASREC;
extern CALQUAL CALQUAL_POST uint32_T DIAGVEHCANMASK;
extern CALQUAL CALQUAL_POST uint8_T ENCRCALIVETEST;
extern CALQUAL CALQUAL_POST uint8_T NEWLOADSCALING;
extern CALQUAL CALQUAL_POST uint8_T ENOLDGPFINFO;
extern CALQUAL CALQUAL_POST int8_T FOCYLBALCLREQ;
extern CALQUAL CALQUAL_POST int8_T FOAFRCLACTIVE;

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static void CanMgm_ResetAllPri(void);
static int16_T CanMgm_CanRecv_EcmEisb1(void);
static int16_T CanMgm_CanRecv_EcmEisb2(void);
static void CanMgm_EcmEisb1_Reset(void);
static void CanMgm_EcmEisb2_Reset(void);
static void CanMgm_UpdateDisDiagRpm(void);

#endif  // CANMGMIN_BR_H

/****************************************************************************
 ****************************************************************************/


