/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           KnockCorrAdp.c
 **  File Creation Date: 28-May-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         KnockCorrAdp
 **  Model Description:  This model calculates an adaptive correction term on spark advance, according to knock detection state.

   Adaptive learning is activated when engine is in a steady state. This is done
   by averaging the individual cylinder knock correction SAKCorrInd over a period and by
   storing a fraction of the average value in a table interpolated on engine speed and load. A
   dead band is also used in this case to avoid updating the table for too small corrections.

   Actually the fraction of the average value is not stored in a single cell of the adaptive table
   unless the engine operating point corresponds exactly to a point in the grid defined by the
   two breakpoints of engine speed and load. In the general case up to four cells are
   updated depending on the squared distance of each one from the experimental point.
   Practically the fraction of the average value of SAKCorrInd over the given period is
   multiplied by a coefficient gain (weigh function) that depends on the distance of the
   experimental point from the cell and the result is stored in the corresponding cell
 **  Model Version:      1.1298
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Fri May 28 14:50:02 2021
 **
 **  Last Saved Modification:  RoccaG - Fri May 28 14:44:19 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "KnockCorrAdp_out.h"
#include "KnockCorrAdp_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/KnockCorrAdp_Scheduler' */
#define Knoc_event_KnockCorrAdp_PowerOn (0)
#define KnockCo_event_KnockCorrAdp_10ms (3)
#define KnockCor_event_KnockCorrAdp_EOA (2)
#define Knock_event_KnockCorrAdp_NoSync (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKETACSIKNOCK_dim              5U                        /* Referenced by: '<S22>/BKETACSIKNOCK_dim' */

/* Lenght of breakpoint BKETACSIKNOCK - 1 */
#define ID_VER_KNOCKCORRADP_DEF        11298U                    /* Referenced by: '<Root>/KnockCorrAdp_Scheduler' */

/* Model Version. */
#define MAXMAPWEIGHT                   4096U                     /* Referenced by: '<S15>/MAXMAPWEIGHT' */

/* Maximum value for adaptative gain */
#define MAX_RPM_DIV_100                100U                      /* Referenced by: '<S10>/MAX_RPM' */

/* Normalization on rpm, used to define steady state. */
#define MAX_SAKNOCK_CORR               32767                     /* Referenced by: '<S11>/KNOCKLEARNDUR1' */

/* Maximum allowed value for SAKnock signal */
#define MIN_SAKNOCK_CORR               -32768                    /* Referenced by: '<S11>/KNOCKLEARNDUR2' */

/* Minimum allowed value for SAKnock signal */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_KNOCKCORRADP_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

#if (N_REC != 50)
#error This code was generated with a different number of recoveries!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint8_T RecoveryCyl;            /* '<Root>/KnockCorrAdp_Scheduler' */
static uint8_T sstab_load;             /* '<S3>/Merge15' */

/* State for load stability */
static uint8_T sstab_rpm;              /* '<S3>/Merge14' */

/* State for rpm stability */

/*Init of local calibrations section*/
#pragma ghs section rodata=".calib"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKETACSIKNOCK[6] = { 0U, 23665U,
  29799U, 35389U, 41753U, 65470U } ;   /* Referenced by:
                                        * '<S15>/BKETACSIKNOCK'
                                        * '<S15>/BKETACSIKNOCK1'
                                        */

/* Vector of breakpoints for the rpm and load ratios */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENKNOCKAD = 0;/* Referenced by: '<S9>/ENKNOCKAD' */

/* Enables the execution of the learning procedure and the update of the adaptive table (=1) */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENKNOCKADCORR = 0;/* Referenced by:
                                                              * '<S9>/ENKNOCKADCORR1'
                                                              * '<S13>/ENKNOCKADCORR'
                                                              */

/* Enables the adaptive correction for knocking on the spark advance (=1) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T GNKNAD = 512U;/* Referenced by: '<S15>/GNKNAD' */

/* Threshold on the coolant temperature to enable knock learning */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T HYSTLOADRECKNOCK = 384U;/* Referenced by:
                                                                   * '<S37>/HYSTLOADRECKNOCK'
                                                                   * '<S37>/HYSTLOADRECKNOCK1'
                                                                   */

/* Hyst applied to min Load value to apply recovery correction */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T KNOCKLEARNDUR = 20U;/* Referenced by:
                                                               * '<S4>/ENKNOCKADCORR1'
                                                               * '<S11>/KNOCKLEARNDUR'
                                                               */

/* Knock learning duration */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MAXNEGKCORRINDAVG = -36;
                                      /* Referenced by: '<S4>/ENKNOCKADCORR2' */

/* Max negative KCorrIndAvgCyl to adapt */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MINLOADRECKNOCK = 128U;/* Referenced by:
                                                                  * '<S37>/MINLOADRECKNOCK'
                                                                  * '<S37>/MINLOADRECKNOCK1'
                                                                  * '<S37>/MINLOADRECKNOCK2'
                                                                  */

/* Min Load value to apply recovery correction */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBGNAD[36] = { 1024U, 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U,
  1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U, 1024U } ;/* Referenced by: '<S15>/TBGNAD' */

/* Table of gains for adaptive coefficients spreading */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THKCORRINDAD = 0U;
                                      /* Referenced by: '<S4>/ENKNOCKADCORR3' */

/* Threshold on the abs. average knocking corr. to trigger the learning */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABLDKNOCK = 1280U;
                                     /* Referenced by: '<S10>/THRSTABLDKNOCK' */

/* Threshold to detect stability for Load */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABRPMKNOCK = 640U;
                                    /* Referenced by: '<S10>/THRSTABRPMKNOCK' */

/* Threshold to detect stability for RpmF */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T THWATKNOCKAD = 1040;/* Referenced by: '<S9>/THWATKNOCKAD' */

/* Threshold on the coolant temperature to enable knock learning */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTADCORRMAX[12] = { 0, 0, 0, 0, 0, 0, 0,
  0, 0, 0, 0, 0 } ;                    /* Referenced by: '<S16>/TBKCORRMAX' */

/* Maximum value for adaptive knock correction */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T VTADCORRMIN[12] = { -6144, -6144, -6144,
  -6144, -6144, -6144, -6144, -6144, -6144, -6144, -6144, -6144 } ;
                                    /* Referenced by: '<S14>/VTDELTAKCORRMIN' */

/* Minimum value for adaptive knock correction */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTTDCSTABKNOCK[12] = { 50U, 50U, 50U,
  50U, 50U, 50U, 50U, 50U, 50U, 50U, 50U, 50U } ;
                                     /* Referenced by: '<S10>/VTTDCSTABKNOCK' */

/* Number of TDCs to detect stability of Load and RpmF for knock learning */
#pragma ghs section rodata=default

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
int16_T DeltaSAKnockCorrAd[8];         /* '<S3>/Merge1' */

/* Delta knock correction for protection */
uint16_T KnockRecGain;                 /* '<S3>/Merge' */

/* Gain for knock correction during recovery */
int16_T SAKnockCorrAd[8];              /* '<S3>/Merge2' */

/* Adaptive knock correction on spark advance */
uint8_T TrigKnockAdat[8];              /* '<S3>/Merge3' */

/* Trigger for knock learning */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T CntKnockLearn[8];/* '<S3>/Merge10' */

/* Counter for knock learning */
STATIC_TEST_POINT boolean_T EnableAdpCorr;/* '<S3>/Merge4' */

/* Enabling condition for adaptive learning */
STATIC_TEST_POINT uint8_T EndKnockLearn[8];/* '<S3>/Merge11' */

/* End knock learning flag */
STATIC_TEST_POINT uint8_T FStabLoadKnock;/* '<S3>/Merge8' */

/* Load is stable for learning (=1) */
STATIC_TEST_POINT uint8_T FStabRpmKnock;/* '<S3>/Merge7' */

/* RpmF is stable for learning (=1) */
STATIC_TEST_POINT boolean_T FlgRecAdaptEnable;/* '<S3>/Merge5' */

/* Flag to enable adaptation after a recovery */
STATIC_TEST_POINT boolean_T FlgSteadyState;/* '<S3>/Merge6' */

/* Both RpmF and Load are stable for learning (=1) */
STATIC_TEST_POINT uint32_T IdVer_KnockCorrAdp;/* '<Root>/KnockCorrAdp_Scheduler' */

/* Model Version */
STATIC_TEST_POINT int16_T KCorrIndAvgCyl;/* '<S3>/Merge9' */

/* Mean value of SAKCorrInd */
STATIC_TEST_POINT int16_T KCorrIndSum[8];/* '<S3>/Merge13' */

/* Knock correction accumulation during learning */
STATIC_TEST_POINT uint8_T KnockLearnState[8];/* '<S3>/Merge12' */

/* State of the knock learning */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_KnockCorrAdp_T KnockCorrAdp_DW;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void Knock_chartstep_c3_KnockCorrAdp(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/*
 * Output and update for function-call system: '<S8>/AdaptiveCorrectionSingleCell'
 * Block description for: '<S8>/AdaptiveCorrectionSingleCell'
 *   This subsystem stores in a single cell of the adaptive table the right
 *   fraction of adaptive correction.
 */
void Kn_AdaptiveCorrectionSingleCell(int16_T rtu_KCorrIndAvgCyl, uint16_T
  rtu_csi, uint16_T rtu_eta, uint8_T rtu_indr, uint8_T rtu_indc, uint16_T
  rtu_IDZoneKnockRpm, uint16_T rtu_RtZoneKnockRpm, uint16_T rtu_RtZoneKnockLoad)
{
  /* local block i/o variables */
  uint16_T rtb_Look2D_U16_U16_U16;
  int16_T rtb_LookUp_IR_S16;
  int16_T rtb_LookUp_IR_S16_h;
  uint16_T rtb_Product2;
  int32_T rtb_Divide_j;
  int16_T rtb_GetTbKnockAdEE;

  /* S-Function (LookUp_IR_S16): '<S26>/LookUp_IR_S16' incorporates:
   *  Constant: '<S16>/TBKCORRMAX'
   *  Constant: '<S25>/BKRPMKNOCK12_dim'
   *
   * Block requirements for '<S16>/TBKCORRMAX':
   *  1. EISB_FCA6CYL_SW_REQ_1816: Values stored into table TbKnockADEE shall be upper satured by tab... (ECU_SW_Requirements#7013)
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16, &VTADCORRMAX[0], rtu_IDZoneKnockRpm,
                rtu_RtZoneKnockRpm, BKRPMKNOCK12_dim);

  /* S-Function (Look2D_U16_U16_U16): '<S23>/Look2D_U16_U16_U16' incorporates:
   *  Constant: '<S15>/BKETACSIKNOCK'
   *  Constant: '<S15>/BKETACSIKNOCK1'
   *  Constant: '<S15>/TBGNAD'
   *  Constant: '<S22>/BKETACSIKNOCK_dim'
   */
  Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBGNAD[0], rtu_RtZoneKnockRpm,
                     &BKETACSIKNOCK[0], ((uint8_T)BKETACSIKNOCK_dim),
                     rtu_RtZoneKnockLoad, &BKETACSIKNOCK[0], ((uint8_T)
    BKETACSIKNOCK_dim));

  /* Product: '<S15>/Product2' incorporates:
   *  Product: '<S15>/Product1'
   */
  rtb_Product2 = (uint16_T)((((((uint32_T)rtu_eta) * ((uint32_T)rtu_csi)) >>
    ((uint32_T)6)) * ((uint32_T)rtb_Look2D_U16_U16_U16)) >> ((uint32_T)12));

  /* MinMax: '<S15>/MinMax' incorporates:
   *  Constant: '<S15>/MAXMAPWEIGHT'
   */
  if (rtb_Product2 >= ((uint16_T)MAXMAPWEIGHT)) {
    rtb_Product2 = ((uint16_T)MAXMAPWEIGHT);
  }

  /* End of MinMax: '<S15>/MinMax' */

  /* Product: '<S21>/Divide' incorporates:
   *  Constant: '<S15>/GNKNAD'
   *  Product: '<S15>/Product3'
   *  Product: '<S15>/Product4'
   *  Product: '<S20>/Divide'
   *
   * Block requirements for '<S15>/Product4':
   *  1. EISB_FCA6CYL_SW_REQ_1819: The average of the applied Individual cylinder knock correction (i... (ECU_SW_Requirements#7010)
   */
  rtb_Divide_j = (((((int32_T)rtu_KCorrIndAvgCyl) * ((int32_T)GNKNAD)) / 128) *
                  ((int32_T)rtb_Product2)) / 128;

  /* CCaller: '<S15>/GetTbKnockAdEE' */
  rtb_GetTbKnockAdEE = Get_TbKnockAdEE(rtu_indr, rtu_indc);

  /* Sum: '<S15>/Add' */
  rtb_Divide_j += (int32_T)rtb_GetTbKnockAdEE;

  /* MinMax: '<S15>/MinMax1' incorporates:
   *  DataTypeConversion: '<S16>/Conversion'
   */
  if (((int32_T)rtb_LookUp_IR_S16) < rtb_Divide_j) {
    rtb_Divide_j = (int32_T)rtb_LookUp_IR_S16;
  }

  /* S-Function (LookUp_IR_S16): '<S18>/LookUp_IR_S16' incorporates:
   *  Constant: '<S14>/VTDELTAKCORRMIN'
   *  Constant: '<S17>/BKRPMKNOCK12_dim'
   *
   * Block requirements for '<S14>/VTDELTAKCORRMIN':
   *  1. EISB_FCA6CYL_SW_REQ_1817: Values stored into table TbKnockADEE shall be lower satured by tab... (ECU_SW_Requirements#7014)
   */
  LookUp_IR_S16( &rtb_LookUp_IR_S16_h, &VTADCORRMIN[0], rtu_IDZoneKnockRpm,
                rtu_RtZoneKnockRpm, BKRPMKNOCK12_dim);

  /* MinMax: '<S15>/MinMax2' incorporates:
   *  DataTypeConversion: '<S14>/Conversion'
   *  MinMax: '<S15>/MinMax1'
   */
  if (rtb_Divide_j <= ((int32_T)rtb_LookUp_IR_S16_h)) {
    rtb_Divide_j = (int32_T)rtb_LookUp_IR_S16_h;
  }

  /* End of MinMax: '<S15>/MinMax2' */

  /* CCaller: '<S15>/Set_TbKnockAdEE'
   *
   * Block requirements for '<S15>/Set_TbKnockAdEE':
   *  1. EISB_FCA6CYL_SW_REQ_1881: Every time that signal TrigKnockAdat is equal to 1 and that KCorrI... (ECU_SW_Requirements#7009)
   *  2. EISB_FCA6CYL_SW_REQ_1250: Table TbKnockAdEE shall persist over EISB power cycle. (ECU_SW_Requirements#2113)
   */
  Set_TbKnockAdEE(rtu_indr, rtu_indc, (int16_T)rtb_Divide_j);
}

/* Function for Chart: '<Root>/KnockCorrAdp_Scheduler' */
static void Knock_chartstep_c3_KnockCorrAdp(const int32_T *sfEvent)
{
  /* local block i/o variables */
  uint16_T rtb_SigStab_o3;
  uint16_T rtb_SigStab_o4;
  uint16_T rtb_SigStab_o3_h;
  uint16_T rtb_SigStab_o4_k;
  uint16_T rtb_LookUp_IR_U16;
  uint8_T rtb_SigStab_o1;
  uint8_T rtb_SigStab_o2;
  uint8_T rtb_SigStab_o1_p;
  uint8_T rtb_SigStab_o2_a;
  boolean_T rtb_RelationalOperator2[8];
  boolean_T rtb_FlgRecAdaptEnable_b;
  boolean_T rtb_EnableAdpCorr;
  boolean_T rtb_FlgSteadyState;
  uint8_T rtb_TrigKnockAdat_b[8];
  uint16_T rtb_CntKnockLearn_g[8];
  uint8_T rtb_EndKnockLearn_p[8];
  uint8_T rtb_KnockLearnState_o[8];
  int16_T rtb_DeltaSAKnockCorrAd_i[8];
  int16_T rtb_SAKnockCorrAd_b[8];
  int16_T rtb_KCorrIndSum_m[8];
  uint16_T rtb_KnockRecGain_g;
  int16_T rtb_Interpolate_TbKnockAdEE_n;
  int16_T rtb_SAKnockCorrAd_g[8];
  int16_T rtb_Interpolate_TbKnockAdEE;
  uint8_T indr;
  uint8_T rtb_sstab_rpm;
  uint8_T rtb_sstab_load;
  uint16_T rtb_Memory1;
  uint16_T rtb_Memory;
  int32_T i;

  /* Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */
  /* Chart: '<Root>/KnockCorrAdp_Scheduler' incorporates:
   *  Constant: '<S11>/KNOCKLEARNDUR'
   *  Constant: '<S4>/ENKNOCKADCORR1'
   *  Constant: '<S4>/ENKNOCKADCORR2'
   *  Constant: '<S4>/ENKNOCKADCORR3'
   *  DataTypeConversion: '<S11>/Conversion'
   *  Inport: '<Root>/FlgSyncPhased'
   *  Inport: '<Root>/IDZoneKnockLoad'
   *  Inport: '<Root>/IDZoneKnockRpm'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/KnockCorrMode'
   *  Inport: '<Root>/RtZoneKnockLoad'
   *  Inport: '<Root>/RtZoneKnockRpm'
   *  Inport: '<Root>/VtRec'
   *  MinMax: '<S11>/MinMax1'
   *  Product: '<S11>/Product'
   *  SignalConversion generated from: '<S4>/CntKnockLearn_old'
   *  SignalConversion generated from: '<S4>/EndKnockLearn_old'
   *  SignalConversion generated from: '<S4>/KCorrIndAvgCyl_old'
   *  SignalConversion generated from: '<S4>/KCorrIndSum_old'
   *  SignalConversion generated from: '<S4>/KnockLearnState_old'
   *  SignalConversion generated from: '<S4>/TrigKnockAdat_old'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   *
   * Block requirements for '<S11>/Product':
   *  1. EISB_FCA6CYL_SW_REQ_1247: Every time that adaptive correction learning is enabled (see requi... (ECU_SW_Requirements#2111)
   */
  /* During: KnockCorrAdp_Scheduler */
  /* This chart is a simple scheduler used to activate different model behaviours (recovery, normal, disabled). */
  /* Entry Internal: KnockCorrAdp_Scheduler */
  /* Transition: '<S2>:9' */
  switch (*sfEvent) {
   case Knoc_event_KnockCorrAdp_PowerOn:
    /* Transition: '<S2>:11'
     * Requirements for Transition: '<S2>:11':
     *  1. EISB_FCA6CYL_SW_REQ_1878: Software shall set to 0 each software output variable implemented ... (ECU_SW_Requirements#6994)
     */
    /* Transition: '<S2>:13' */
    IdVer_KnockCorrAdp = ID_VER_KNOCKCORRADP_DEF;

    /* Outputs for Function Call SubSystem: '<S1>/Reset'
     *
     * Block description for '<S1>/Reset':
     *  This subsystem applies default value to each model output in case of reset or initialization.
     *  No requirement is linked to this subsystem but to relative scheduler transitions.
     */
    /* SignalConversion generated from: '<S6>/DeltaSAKnockCorrAd' */
    /* Transition: '<S2>:18' */
    /* Event: '<S2>:19' */
    memset((&(DeltaSAKnockCorrAd[0])), 0, (sizeof(int16_T)) << 3U);

    /* SignalConversion generated from: '<S6>/SAKnockCorrAd' */
    memset((&(SAKnockCorrAd[0])), 0, (sizeof(int16_T)) << 3U);

    /* SignalConversion generated from: '<S6>/TrigKnockAdat' */
    memset((&(TrigKnockAdat[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S6>/CntKnockLearn' */
    memset((&(CntKnockLearn[0])), 0, (sizeof(uint16_T)) << 3U);

    /* SignalConversion generated from: '<S6>/EndKnockLearn' */
    memset((&(EndKnockLearn[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S6>/KnockLearnState' */
    memset((&(KnockLearnState[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S6>/KCorrIndSum' */
    memset((&(KCorrIndSum[0])), 0, (sizeof(int16_T)) << 3U);

    /* SignalConversion generated from: '<S6>/KnockRecGain' incorporates:
     *  Constant: '<S6>/Constant'
     */
    KnockRecGain = 0U;

    /* SignalConversion generated from: '<S6>/FlgRecAdaptEnable' incorporates:
     *  Constant: '<S6>/Constant5'
     */
    FlgRecAdaptEnable = false;

    /* SignalConversion generated from: '<S6>/FlgSteadyState' incorporates:
     *  Constant: '<S6>/Constant6'
     */
    FlgSteadyState = false;

    /* SignalConversion generated from: '<S6>/FStabRpmKnock' incorporates:
     *  Constant: '<S6>/Constant7'
     */
    FStabRpmKnock = 0U;

    /* SignalConversion generated from: '<S6>/FStabLoadKnock' incorporates:
     *  Constant: '<S6>/Constant8'
     */
    FStabLoadKnock = 0U;

    /* SignalConversion generated from: '<S6>/KCorrIndAvgCyl' incorporates:
     *  Constant: '<S6>/Constant9'
     */
    KCorrIndAvgCyl = 0;

    /* SignalConversion generated from: '<S6>/sstab_rpm' incorporates:
     *  Constant: '<S6>/Constant14'
     */
    sstab_rpm = 0U;

    /* SignalConversion generated from: '<S6>/sstab_load' incorporates:
     *  Constant: '<S6>/Constant15'
     */
    sstab_load = 0U;

    /* SignalConversion generated from: '<S6>/EnableAdpCorr' incorporates:
     *  Constant: '<S6>/Constant4'
     */
    EnableAdpCorr = false;

    /* End of Outputs for SubSystem: '<S1>/Reset' */
    /* Transition: '<S2>:36' */
    /* Transition: '<S2>:41' */
    /* Transition: '<S2>:45' */
    /* Transition: '<S2>:76' */
    /* Transition: '<S2>:79' */
    break;

   case Knock_event_KnockCorrAdp_NoSync:
    /* Outputs for Function Call SubSystem: '<S1>/Reset'
     *
     * Block description for '<S1>/Reset':
     *  This subsystem applies default value to each model output in case of reset or initialization.
     *  No requirement is linked to this subsystem but to relative scheduler transitions.
     */
    /* SignalConversion generated from: '<S6>/DeltaSAKnockCorrAd' */
    /* Transition: '<S2>:15' */
    /* Transition: '<S2>:16'
     * Requirements for Transition: '<S2>:16':
     *  1. EISB_FCA6CYL_SW_REQ_1883: Software shall set to 0 each software output variable implemented ... (ECU_SW_Requirements#6995)
     */
    /* Transition: '<S2>:18' */
    /* Event: '<S2>:19' */
    memset((&(DeltaSAKnockCorrAd[0])), 0, (sizeof(int16_T)) << 3U);

    /* SignalConversion generated from: '<S6>/SAKnockCorrAd' */
    memset((&(SAKnockCorrAd[0])), 0, (sizeof(int16_T)) << 3U);

    /* SignalConversion generated from: '<S6>/TrigKnockAdat' */
    memset((&(TrigKnockAdat[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S6>/CntKnockLearn' */
    memset((&(CntKnockLearn[0])), 0, (sizeof(uint16_T)) << 3U);

    /* SignalConversion generated from: '<S6>/EndKnockLearn' */
    memset((&(EndKnockLearn[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S6>/KnockLearnState' */
    memset((&(KnockLearnState[0])), 0, (sizeof(uint8_T)) << 3U);

    /* SignalConversion generated from: '<S6>/KCorrIndSum' */
    memset((&(KCorrIndSum[0])), 0, (sizeof(int16_T)) << 3U);

    /* SignalConversion generated from: '<S6>/KnockRecGain' incorporates:
     *  Constant: '<S6>/Constant'
     */
    KnockRecGain = 0U;

    /* SignalConversion generated from: '<S6>/FlgRecAdaptEnable' incorporates:
     *  Constant: '<S6>/Constant5'
     */
    FlgRecAdaptEnable = false;

    /* SignalConversion generated from: '<S6>/FlgSteadyState' incorporates:
     *  Constant: '<S6>/Constant6'
     */
    FlgSteadyState = false;

    /* SignalConversion generated from: '<S6>/FStabRpmKnock' incorporates:
     *  Constant: '<S6>/Constant7'
     */
    FStabRpmKnock = 0U;

    /* SignalConversion generated from: '<S6>/FStabLoadKnock' incorporates:
     *  Constant: '<S6>/Constant8'
     */
    FStabLoadKnock = 0U;

    /* SignalConversion generated from: '<S6>/KCorrIndAvgCyl' incorporates:
     *  Constant: '<S6>/Constant9'
     */
    KCorrIndAvgCyl = 0;

    /* SignalConversion generated from: '<S6>/sstab_rpm' incorporates:
     *  Constant: '<S6>/Constant14'
     */
    sstab_rpm = 0U;

    /* SignalConversion generated from: '<S6>/sstab_load' incorporates:
     *  Constant: '<S6>/Constant15'
     */
    sstab_load = 0U;

    /* SignalConversion generated from: '<S6>/EnableAdpCorr' incorporates:
     *  Constant: '<S6>/Constant4'
     */
    EnableAdpCorr = false;

    /* End of Outputs for SubSystem: '<S1>/Reset' */
    /* Transition: '<S2>:36' */
    /* Transition: '<S2>:41' */
    /* Transition: '<S2>:45' */
    /* Transition: '<S2>:76' */
    /* Transition: '<S2>:79' */
    break;

   case KnockCor_event_KnockCorrAdp_EOA:
    /* Transition: '<S2>:21' */
    /* Transition: '<S2>:23' */
    /* Transition: '<S2>:25' */
    switch (KnockCorrMode) {
     case KNOCK_CORR_OFF:
      /* Outputs for Function Call SubSystem: '<S1>/Reset'
       *
       * Block description for '<S1>/Reset':
       *  This subsystem applies default value to each model output in case of reset or initialization.
       *  No requirement is linked to this subsystem but to relative scheduler transitions.
       */
      /* SignalConversion generated from: '<S6>/DeltaSAKnockCorrAd' */
      /* Transition: '<S2>:28'
       * Requirements for Transition: '<S2>:28':
       *  1. EISB_FCA6CYL_SW_REQ_1884: Software shall set to 0 each software output variable implemented ... (ECU_SW_Requirements#6996)
       */
      /* Transition: '<S2>:30' */
      /* Event: '<S2>:19' */
      memset((&(DeltaSAKnockCorrAd[0])), 0, (sizeof(int16_T)) << 3U);

      /* SignalConversion generated from: '<S6>/SAKnockCorrAd' */
      memset((&(SAKnockCorrAd[0])), 0, (sizeof(int16_T)) << 3U);

      /* SignalConversion generated from: '<S6>/TrigKnockAdat' */
      memset((&(TrigKnockAdat[0])), 0, (sizeof(uint8_T)) << 3U);

      /* SignalConversion generated from: '<S6>/CntKnockLearn' */
      memset((&(CntKnockLearn[0])), 0, (sizeof(uint16_T)) << 3U);

      /* SignalConversion generated from: '<S6>/EndKnockLearn' */
      memset((&(EndKnockLearn[0])), 0, (sizeof(uint8_T)) << 3U);

      /* SignalConversion generated from: '<S6>/KnockLearnState' */
      memset((&(KnockLearnState[0])), 0, (sizeof(uint8_T)) << 3U);

      /* SignalConversion generated from: '<S6>/KCorrIndSum' */
      memset((&(KCorrIndSum[0])), 0, (sizeof(int16_T)) << 3U);

      /* SignalConversion generated from: '<S6>/KnockRecGain' incorporates:
       *  Constant: '<S6>/Constant'
       */
      KnockRecGain = 0U;

      /* SignalConversion generated from: '<S6>/FlgRecAdaptEnable' incorporates:
       *  Constant: '<S6>/Constant5'
       */
      FlgRecAdaptEnable = false;

      /* SignalConversion generated from: '<S6>/FlgSteadyState' incorporates:
       *  Constant: '<S6>/Constant6'
       */
      FlgSteadyState = false;

      /* SignalConversion generated from: '<S6>/FStabRpmKnock' incorporates:
       *  Constant: '<S6>/Constant7'
       */
      FStabRpmKnock = 0U;

      /* SignalConversion generated from: '<S6>/FStabLoadKnock' incorporates:
       *  Constant: '<S6>/Constant8'
       */
      FStabLoadKnock = 0U;

      /* SignalConversion generated from: '<S6>/KCorrIndAvgCyl' incorporates:
       *  Constant: '<S6>/Constant9'
       */
      KCorrIndAvgCyl = 0;

      /* SignalConversion generated from: '<S6>/sstab_rpm' incorporates:
       *  Constant: '<S6>/Constant14'
       */
      sstab_rpm = 0U;

      /* SignalConversion generated from: '<S6>/sstab_load' incorporates:
       *  Constant: '<S6>/Constant15'
       */
      sstab_load = 0U;

      /* SignalConversion generated from: '<S6>/EnableAdpCorr' incorporates:
       *  Constant: '<S6>/Constant4'
       */
      EnableAdpCorr = false;

      /* End of Outputs for SubSystem: '<S1>/Reset' */
      /* Transition: '<S2>:40' */
      /* Transition: '<S2>:45' */
      /* Transition: '<S2>:76' */
      /* Transition: '<S2>:79' */
      break;

     case KNOCK_CORR_REC:
      /* Transition: '<S2>:32' */
      /* Transition: '<S2>:34'
       * Requirements for Transition: '<S2>:34':
       *  1. EISB_FCA6CYL_SW_REQ_1291: The software shall use a recovery value for the adaptive knock cor... (ECU_SW_Requirements#2138)
       */
      /* Transition: '<S2>:38' */
      RecoveryCyl = IonAbsTdcEOA;

      /* Outputs for Function Call SubSystem: '<S1>/Recovery_Mode'
       *
       * Block description for '<S1>/Recovery_Mode':
       *  This subsystem implements recovery mode for adaptive correction.
       */
      /* SignalConversion generated from: '<S5>/DeltaSAKnockCorrAd_old' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      /* Event: '<S2>:43' */
      memcpy(&rtb_DeltaSAKnockCorrAd_i[0], (&(DeltaSAKnockCorrAd[0])), (sizeof
              (int16_T)) << 3U);

      /* SignalConversion generated from: '<S5>/SAKnockCorrAd_old' */
      memcpy(&rtb_SAKnockCorrAd_b[0], (&(SAKnockCorrAd[0])), (sizeof(int16_T)) <<
             3U);

      /* SignalConversion generated from: '<S5>/TrigKnockAdat_old' */
      memcpy(&rtb_TrigKnockAdat_b[0], (&(TrigKnockAdat[0])), (sizeof(uint8_T)) <<
             3U);

      /* SignalConversion generated from: '<S5>/CntKnockLearn_old' */
      memcpy(&rtb_CntKnockLearn_g[0], (&(CntKnockLearn[0])), (sizeof(uint16_T)) <<
             3U);

      /* SignalConversion generated from: '<S5>/EndKnockLearn_old' */
      memcpy(&rtb_EndKnockLearn_p[0], (&(EndKnockLearn[0])), (sizeof(uint8_T)) <<
             3U);

      /* SignalConversion generated from: '<S5>/KnockLearnState_old' */
      memcpy(&rtb_KnockLearnState_o[0], (&(KnockLearnState[0])), (sizeof(uint8_T))
             << 3U);

      /* SignalConversion generated from: '<S5>/KCorrIndSum_old' */
      memcpy(&rtb_KCorrIndSum_m[0], (&(KCorrIndSum[0])), (sizeof(int16_T)) << 3U);

      /* Switch: '<S37>/Switch' incorporates:
       *  Constant: '<S37>/Constant14'
       *  Constant: '<S37>/HYSTLOADRECKNOCK'
       *  Constant: '<S37>/MINLOADRECKNOCK'
       *  Constant: '<S37>/MINLOADRECKNOCK1'
       *  Inport: '<Root>/Load'
       *  RelationalOperator: '<S37>/Relational Operator'
       *  RelationalOperator: '<S37>/Relational Operator1'
       *  Sum: '<S37>/Add3'
       *  Switch: '<S37>/Switch1'
       *
       * Block requirements for '<S37>/Switch':
       *  1. EISB_FCA6CYL_SW_REQ_1873: In case that the recovery REC_KNOCKCORR_OFF_X is active for cylind... (ECU_SW_Requirements#7020)
       */
      if (((int32_T)Load) >= ((int32_T)((uint32_T)(((uint32_T)MINLOADRECKNOCK) +
             ((uint32_T)HYSTLOADRECKNOCK))))) {
        rtb_KnockRecGain_g = 1024U;
      } else if (Load <= MINLOADRECKNOCK) {
        /* Switch: '<S37>/Switch1' incorporates:
         *  Constant: '<S37>/Constant1'
         */
        rtb_KnockRecGain_g = 0U;
      } else {
        /* Switch: '<S37>/Switch1' incorporates:
         *  Constant: '<S37>/HYSTLOADRECKNOCK1'
         *  Constant: '<S37>/MINLOADRECKNOCK2'
         *  Product: '<S37>/Divide'
         *  Sum: '<S37>/Add1'
         */
        rtb_KnockRecGain_g = (uint16_T)((((uint32_T)((uint16_T)(((uint32_T)Load)
          - ((uint32_T)MINLOADRECKNOCK)))) << ((uint32_T)10)) / ((uint32_T)
          HYSTLOADRECKNOCK));
      }

      /* CCaller: '<S38>/Interpolate_TbKnockAdEE' incorporates:
       *  Constant: '<S38>/Constant'
       *  Constant: '<S38>/Constant5'
       *  Inport: '<Root>/IDZoneKnockLoad'
       *  Inport: '<Root>/IDZoneKnockRpm'
       *  Inport: '<Root>/RtZoneKnockLoad'
       *  Inport: '<Root>/RtZoneKnockRpm'
       *  Product: '<S38>/Product'
       *  Sum: '<S38>/Add'
       *  Sum: '<S38>/Add1'
       *
       * Block requirements for '<S38>/Interpolate_TbKnockAdEE':
       *  1. EISB_FCA6CYL_SW_REQ_1292: The software shall use a recovery value for the adaptive knock cor... (ECU_SW_Requirements#2139)
       */
      rtb_Interpolate_TbKnockAdEE_n = Interpolate_TbKnockAdEE((uint16_T)
        (((uint32_T)((uint16_T)(((uint32_T)((uint8_T)(((uint32_T)
        BKRPMKNOCK12_dim) + 1U))) * ((uint32_T)RecoveryCyl)))) + ((uint32_T)
        IDZoneKnockRpm)), RtZoneKnockRpm, IDZoneKnockLoad, RtZoneKnockLoad);

      /* DataTypeConversion: '<S39>/Conversion2' incorporates:
       *  Product: '<S36>/Product'
       *  Product: '<S39>/Divide'
       *  Product: '<S40>/Divide'
       *
       * Block requirements for '<S36>/Product':
       *  1. EISB_FCA6CYL_SW_REQ_1292: The software shall use a recovery value for the adaptive knock cor... (ECU_SW_Requirements#2139)
       */
      rtb_Interpolate_TbKnockAdEE_n = (int16_T)(((((int32_T)
        rtb_Interpolate_TbKnockAdEE_n) / 128) * ((int32_T)rtb_KnockRecGain_g)) /
        1024);

      /* Assignment: '<S36>/Assignment' */
      memcpy((&(SAKnockCorrAd[0])), &rtb_SAKnockCorrAd_b[0], (sizeof(int16_T)) <<
             3U);

      /* Assignment: '<S36>/Assignment6' */
      memcpy((&(DeltaSAKnockCorrAd[0])), &rtb_DeltaSAKnockCorrAd_i[0], (sizeof
              (int16_T)) << 3U);

      /* Assignment: '<S36>/Assignment1' */
      memcpy((&(TrigKnockAdat[0])), &rtb_TrigKnockAdat_b[0], (sizeof(uint8_T)) <<
             3U);

      /* Assignment: '<S36>/Assignment2' */
      memcpy((&(CntKnockLearn[0])), &rtb_CntKnockLearn_g[0], (sizeof(uint16_T)) <<
             3U);

      /* Assignment: '<S36>/Assignment3' */
      memcpy((&(EndKnockLearn[0])), &rtb_EndKnockLearn_p[0], (sizeof(uint8_T)) <<
             3U);

      /* Assignment: '<S36>/Assignment4' */
      memcpy((&(KnockLearnState[0])), &rtb_KnockLearnState_o[0], (sizeof(uint8_T))
             << 3U);

      /* Assignment: '<S36>/Assignment5' */
      memcpy((&(KCorrIndSum[0])), &rtb_KCorrIndSum_m[0], (sizeof(int16_T)) << 3U);

      /* Assignment: '<S36>/Assignment' */
      SAKnockCorrAd[(RecoveryCyl)] = rtb_Interpolate_TbKnockAdEE_n;

      /* Assignment: '<S36>/Assignment6' incorporates:
       *  MultiPortSwitch: '<S36>/Index Vector'
       *  Sum: '<S36>/Add'
       */
      DeltaSAKnockCorrAd[(RecoveryCyl)] = (int16_T)
        (rtb_Interpolate_TbKnockAdEE_n - rtb_SAKnockCorrAd_b[RecoveryCyl]);

      /* SignalConversion generated from: '<S5>/KnockRecGain' */
      KnockRecGain = rtb_KnockRecGain_g;

      /* Assignment: '<S36>/Assignment1' incorporates:
       *  Constant: '<S36>/Constant3'
       */
      TrigKnockAdat[(RecoveryCyl)] = 0U;

      /* Assignment: '<S36>/Assignment2' incorporates:
       *  Constant: '<S36>/Constant10'
       */
      CntKnockLearn[(RecoveryCyl)] = 0U;

      /* Assignment: '<S36>/Assignment3' incorporates:
       *  Constant: '<S36>/Constant11'
       */
      EndKnockLearn[(RecoveryCyl)] = 0U;

      /* Assignment: '<S36>/Assignment4' incorporates:
       *  Constant: '<S36>/Constant12'
       */
      KnockLearnState[(RecoveryCyl)] = 0U;

      /* Assignment: '<S36>/Assignment5' incorporates:
       *  Constant: '<S36>/Constant13'
       */
      KCorrIndSum[(RecoveryCyl)] = 0;

      /* End of Outputs for SubSystem: '<S1>/Recovery_Mode' */
      /* Transition: '<S2>:46' */
      /* Transition: '<S2>:76' */
      /* Transition: '<S2>:79' */
      break;

     default:
      /* Outputs for Function Call SubSystem: '<S1>/NormalMode'
       *
       * Block description for '<S1>/NormalMode':
       *  This subsystem implements normal behaviour for adaptive correction on
       *  spark advance.
       */
      /* SignalConversion generated from: '<S4>/sstab_rpm_old' */
      /* Transition: '<S2>:49' */
      /* Normal Mode  */
      /* Event: '<S2>:50' */
      rtb_sstab_rpm = sstab_rpm;

      /* SignalConversion generated from: '<S4>/sstab_load_old' */
      rtb_sstab_load = sstab_load;
      for (i = 0; i < 8; i++) {
        /* SignalConversion generated from: '<S4>/SAKnockCorrAd_old' */
        rtb_DeltaSAKnockCorrAd_i[i] = SAKnockCorrAd[(i)];

        /* SignalConversion generated from: '<S4>/DeltaSAKnockCorrAd_old' */
        rtb_SAKnockCorrAd_b[i] = DeltaSAKnockCorrAd[(i)];

        /* RelationalOperator: '<S9>/Relational Operator2' incorporates:
         *  Inport: '<Root>/SAKCorrInd'
         */
        rtb_RelationalOperator2[i] = (SAKCorrInd[(i)] == 0);
      }

      /* Logic: '<S9>/Logical Operator4' */
      rtb_FlgRecAdaptEnable_b = rtb_RelationalOperator2[0];
      for (i = 0; i < 7; i++) {
        rtb_FlgRecAdaptEnable_b = (rtb_FlgRecAdaptEnable_b &&
          (rtb_RelationalOperator2[i + 1]));
      }

      /* Logic: '<S9>/Logical Operator1' incorporates:
       *  Constant: '<S9>/Constant'
       *  Inport: '<Root>/FlgResetAdaptParam'
       *  Logic: '<S9>/Logical Operator'
       *  Logic: '<S9>/Logical Operator4'
       *  RelationalOperator: '<S9>/Relational Operator'
       *  SignalConversion generated from: '<S4>/FlgRecAdaptEnable_old'
       *
       * Block requirements for '<S9>/Logical Operator1':
       *  1. EISB_FCA6CYL_SW_REQ_1814: Software shall implement a disabling condition of adaptive correct... (ECU_SW_Requirements#7003)
       */
      rtb_FlgRecAdaptEnable_b = ((((int32_T)FlgResetAdaptParam) == 0) &&
        ((FlgRecAdaptEnable) || rtb_FlgRecAdaptEnable_b));

      /* Logic: '<S9>/Logical Operator3' incorporates:
       *  Constant: '<S9>/Constant1'
       *  Constant: '<S9>/Constant4'
       *  Constant: '<S9>/Constant5'
       *  Constant: '<S9>/ENKNOCKAD'
       *  Constant: '<S9>/ENKNOCKADCORR1'
       *  Constant: '<S9>/THWATKNOCKAD'
       *  Inport: '<Root>/FlgEOL'
       *  Inport: '<Root>/FlgRonInheritEE'
       *  Inport: '<Root>/FlgRonStoredEE'
       *  Inport: '<Root>/TWater'
       *  Logic: '<S9>/Logical Operator2'
       *  RelationalOperator: '<S9>/Relational Operator3'
       *  RelationalOperator: '<S9>/Relational Operator4'
       *  RelationalOperator: '<S9>/Relational Operator5'
       *  RelationalOperator: '<S9>/Relational Operator6'
       *
       * Block requirements for '<S9>/ENKNOCKAD':
       *  1. EISB_FCA6CYL_SW_REQ_1272: The software shall support the possibility to enable/disable knock... (ECU_SW_Requirements#2105)
       *
       * Block requirements for '<S9>/ENKNOCKADCORR1':
       *  1. EISB_FCA6CYL_SW_REQ_1270: The software shall support the possibility to enable/disable the k... (ECU_SW_Requirements#2103)
       *
       * Block requirements for '<S9>/Logical Operator2':
       *  1. EISB_FCA6CYL_SW_REQ_1811: The adaptive correction learn procedure shall not be activated whe... (ECU_SW_Requirements#7001)
       *
       * Block requirements for '<S9>/Relational Operator5':
       *  1. EISB_FCA6CYL_SW_REQ_1812: The adaptive correction learn procedure shall not be activated whe... (ECU_SW_Requirements#7002)
       *
       * Block requirements for '<S9>/Relational Operator6':
       *  1. EISB_FCA6CYL_SW_REQ_1244: The adaptive correction learning procedure shall not be activated ... (ECU_SW_Requirements#2106)
       */
      rtb_EnableAdpCorr = (((((rtb_FlgRecAdaptEnable_b && (ENKNOCKADCORR)) &&
        ((((int32_T)FlgRonInheritEE) != 0) || (((int32_T)FlgRonStoredEE) != 0)))
        && (ENKNOCKAD)) && (((int32_T)FlgEOL) == 0)) && (TWater > THWATKNOCKAD));

      /* Product: '<S10>/Product1' incorporates:
       *  Constant: '<S10>/MAX_RPM'
       *  Inport: '<Root>/RpmF'
       */
      rtb_KnockRecGain_g = (uint16_T)((((uint32_T)RpmF) << ((uint32_T)7)) /
        ((uint32_T)((uint8_T)MAX_RPM_DIV_100)));

      /* S-Function (LookUp_IR_U16): '<S34>/LookUp_IR_U16' incorporates:
       *  Constant: '<S10>/VTTDCSTABKNOCK'
       *  Constant: '<S31>/BKRPMKNOCK12_dim'
       */
      LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTTDCSTABKNOCK[0], IDZoneKnockRpm,
                    RtZoneKnockRpm, BKRPMKNOCK12_dim);

      /* Memory: '<S32>/Memory1' */
      rtb_Memory1 = KnockCorrAdp_DW.Memory1_PreviousInput;

      /* Memory: '<S32>/Memory' */
      rtb_Memory = KnockCorrAdp_DW.Memory_PreviousInput;

      /* S-Function (SigStab): '<S32>/SigStab' incorporates:
       *  Constant: '<S10>/Constant'
       *  Constant: '<S10>/THRSTABRPMKNOCK'
       *
       * Block requirements for '<S10>/THRSTABRPMKNOCK':
       *  1. EISB_FCA6CYL_SW_REQ_1240: The engine speed shall be considered stable (i.e. signal FStabRpmK... (ECU_SW_Requirements#2109)
       */
      SigStab( &rtb_SigStab_o1, &rtb_SigStab_o2, &rtb_SigStab_o3,
              &rtb_SigStab_o4, rtb_KnockRecGain_g, ((uint8_T)0U),
              THRSTABRPMKNOCK, rtb_LookUp_IR_U16, rtb_sstab_rpm, rtb_Memory1,
              rtb_Memory);

      /* Memory: '<S33>/Memory1' */
      rtb_KnockRecGain_g = KnockCorrAdp_DW.Memory1_PreviousInput_f;

      /* Memory: '<S33>/Memory' */
      rtb_Memory1 = KnockCorrAdp_DW.Memory_PreviousInput_e;

      /* S-Function (SigStab): '<S33>/SigStab' incorporates:
       *  Constant: '<S10>/Constant1'
       *  Constant: '<S10>/THRSTABLDKNOCK'
       *
       * Block requirements for '<S10>/THRSTABLDKNOCK':
       *  1. EISB_FCA6CYL_SW_REQ_1242: The engine load shall be considered stable (i.e. signal FStabLoadK... (ECU_SW_Requirements#2110)
       */
      SigStab( &rtb_SigStab_o1_p, &rtb_SigStab_o2_a, &rtb_SigStab_o3_h,
              &rtb_SigStab_o4_k, Load, ((uint8_T)0U), THRSTABLDKNOCK,
              rtb_LookUp_IR_U16, rtb_sstab_load, rtb_KnockRecGain_g, rtb_Memory1);

      /* Logic: '<S10>/Logical Operator2' incorporates:
       *  DataTypeConversion: '<S10>/Conversion'
       *  DataTypeConversion: '<S10>/Conversion1'
       *
       * Block requirements for '<S10>/Logical Operator2':
       *  1. EISB_FCA6CYL_SW_REQ_1239: The engine shall be considered in a steady state (i.e. signal FlgS... (ECU_SW_Requirements#2108)
       */
      rtb_FlgSteadyState = ((((int32_T)rtb_SigStab_o1) != 0) && (((int32_T)
        rtb_SigStab_o1_p) != 0));

      /* Gateway: KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionMgm */
      /* During: KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionMgm */
      /* This stateflow manage adaptive learning.
         Learning is activate for a tunable period when engine is defined in steady state. Then an average of the individual cylinder knock correction (SAKCorrInd) over the learning period is done and
         a fraction of the average value is stored in a table interpolated on engine speed and load. A
         dead band is also used in this case to avoid updating the table for too small corrections. */
      /* Entry Internal: KnockCorrAdp_Eval/NormalMode/AdaptiveCorrectionMgm */
      /* Transition: '<S7>:2' */
      /*  Assign old value to each chart's output to optimize code  */
      rtb_Interpolate_TbKnockAdEE_n = KCorrIndAvgCyl;
      memcpy(&rtb_CntKnockLearn_g[0], (&(CntKnockLearn[0])), (sizeof(uint16_T)) <<
             3U);
      memcpy(&rtb_TrigKnockAdat_b[0], (&(EndKnockLearn[0])), (sizeof(uint8_T)) <<
             3U);
      memcpy(&rtb_EndKnockLearn_p[0], (&(KnockLearnState[0])), (sizeof(uint8_T))
             << 3U);
      memcpy(&rtb_KCorrIndSum_m[0], (&(KCorrIndSum[0])), (sizeof(int16_T)) << 3U);
      memcpy(&rtb_KnockLearnState_o[0], (&(TrigKnockAdat[0])), (sizeof(uint8_T))
             << 3U);
      if (rtb_EnableAdpCorr) {
        /* Transition: '<S7>:6' */
        /* Transition: '<S7>:8' */
        /* Adaptive correction enabled  */
        if (rtb_FlgSteadyState) {
          /* Transition: '<S7>:10'
           * Requirements for Transition: '<S7>:10':
           *  1. EISB_FCA6CYL_SW_REQ_1246: The adaptive correction learn procedure shall start when the engin... (ECU_SW_Requirements#2107)
           */
          /* Transition: '<S7>:13' */
          if (((int32_T)EndKnockLearn[(IonAbsTdcEOA)]) != 0) {
            /* Transition: '<S7>:49' */
            /* Transition: '<S7>:51' */
            rtb_EndKnockLearn_p[IonAbsTdcEOA] = 0U;
            rtb_CntKnockLearn_g[IonAbsTdcEOA] = 0U;
            rtb_TrigKnockAdat_b[IonAbsTdcEOA] = 0U;

            /* Transition: '<S7>:53' */
            /* Transition: '<S7>:61' */
          } else {
            /* Transition: '<S7>:44' */
            rtb_CntKnockLearn_g[IonAbsTdcEOA] = (uint16_T)((int32_T)(((int32_T)
              CntKnockLearn[(IonAbsTdcEOA)]) + 1));
            if (rtb_CntKnockLearn_g[IonAbsTdcEOA] >= KNOCKLEARNDUR) {
              /* Transition: '<S7>:56'
               * Requirements for Transition: '<S7>:56':
               *  1. EISB_FCA6CYL_SW_REQ_1248: The adaptive correction learn procedure shall not learn the engine... (ECU_SW_Requirements#2112)
               */
              /* Transition: '<S7>:58' */
              rtb_TrigKnockAdat_b[IonAbsTdcEOA] = 1U;

              /* Transition: '<S7>:61' */
            } else {
              /* Transition: '<S7>:60' */
              rtb_TrigKnockAdat_b[IonAbsTdcEOA] = 0U;
              rtb_EndKnockLearn_p[IonAbsTdcEOA] = 1U;
            }
          }

          /* Transition: '<S7>:62' */
          /* Transition: '<S7>:45' */
          /* Transition: '<S7>:40' */
        } else {
          /* Transition: '<S7>:16' */
          /* Transition: '<S7>:211' */
          for (rtb_sstab_rpm = 0U; rtb_sstab_rpm < N_CYLINDER; rtb_sstab_rpm =
               (uint8_T)((int32_T)(((int32_T)rtb_sstab_rpm) + 1))) {
            /* Transition: '<S7>:21' */
            /* Transition: '<S7>:25' */
            rtb_EndKnockLearn_p[rtb_sstab_rpm] = 0U;
            rtb_CntKnockLearn_g[rtb_sstab_rpm] = 0U;
            rtb_TrigKnockAdat_b[rtb_sstab_rpm] = 0U;

            /* Transition: '<S7>:27' */
            /* Transition: '<S7>:28' */
          }

          /* Transition: '<S7>:29' */
        }

        /* Transition: '<S7>:64' */
        /*  Calculate correction  */
        if (((int32_T)rtb_EndKnockLearn_p[IonAbsTdcEOA]) == 1) {
          /* Outputs for Function Call SubSystem: '<S7>/CALCULATE_CORRECTION.calculateAvg' */
          /* Sum: '<S11>/Add' incorporates:
           *  Inport: '<Root>/SAKCorrInd'
           */
          /* Transition: '<S7>:67' */
          /* Transition: '<S7>:69' */
          /* Simulink Function 'calculateAvg': '<S7>:84' */
          i = ((int32_T)KCorrIndSum[(IonAbsTdcEOA)]) + ((int32_T)SAKCorrInd
            [(IonAbsTdcEOA)]);

          /* MinMax: '<S11>/MinMax' incorporates:
           *  Constant: '<S11>/KNOCKLEARNDUR1'
           */
          if (i >= MAX_SAKNOCK_CORR) {
            i = MAX_SAKNOCK_CORR;
          }

          /* MinMax: '<S11>/MinMax1' incorporates:
           *  Constant: '<S11>/KNOCKLEARNDUR2'
           *  MinMax: '<S11>/MinMax'
           */
          if (i <= MIN_SAKNOCK_CORR) {
            i = MIN_SAKNOCK_CORR;
          }

          rtb_KCorrIndSum_m[IonAbsTdcEOA] = (int16_T)i;
          rtb_Interpolate_TbKnockAdEE_n = (int16_T)(((int32_T)((int16_T)i)) /
            ((int32_T)KNOCKLEARNDUR));

          /* End of Outputs for SubSystem: '<S7>/CALCULATE_CORRECTION.calculateAvg' */
          /* Transition: '<S7>:76' */
        } else {
          /* Transition: '<S7>:72' */
          rtb_KCorrIndSum_m[IonAbsTdcEOA] = 0;
          rtb_Interpolate_TbKnockAdEE_n = 0;
        }

        /* Transition: '<S7>:92' */
        if (((int32_T)rtb_TrigKnockAdat_b[IonAbsTdcEOA]) == 1) {
          /* Transition: '<S7>:96' */
          /* Transition: '<S7>:98' */
          if ((rtb_Interpolate_TbKnockAdEE_n > ((int16_T)THKCORRINDAD)) ||
              (rtb_Interpolate_TbKnockAdEE_n < (-((int16_T)THKCORRINDAD)))) {
            /* Transition: '<S7>:100' */
            /* Transition: '<S7>:102'
             * Requirements for Transition: '<S7>:102':
             *  1. EISB_FCA6CYL_SW_REQ_1247: Every time that adaptive correction learning is enabled (see requi... (ECU_SW_Requirements#2111)
             */
            rtb_KnockLearnState_o[IonAbsTdcEOA] = 1U;

            /* Transition: '<S7>:108' */
          } else {
            /* Transition: '<S7>:104' */
            rtb_KnockLearnState_o[IonAbsTdcEOA] = 0U;
          }

          /* Transition: '<S7>:109' */
        } else {
          /* Transition: '<S7>:107' */
          rtb_KnockLearnState_o[IonAbsTdcEOA] = 0U;
        }

        /* Transition: '<S7>:110' */
        /*  Assign adaptive correction to EE table  */
        /* Transition: '<S7>:130' */
        rtb_sstab_rpm = (uint8_T)((int32_T)((((int32_T)BKRPMKNOCK12_dim) + 1) *
          ((int32_T)IonAbsTdcEOA)));
        if ((rtb_Interpolate_TbKnockAdEE_n >= 0) ||
            ((rtb_Interpolate_TbKnockAdEE_n <= MAXNEGKCORRINDAVG) && (((int32_T)
               rtb_KnockLearnState_o[IonAbsTdcEOA]) == 1))) {
          /* Transition: '<S7>:134'
           * Requirements for Transition: '<S7>:134':
           *  1. EISB_FCA6CYL_SW_REQ_1819: The average of the applied Individual cylinder knock correction (i... (ECU_SW_Requirements#7010)
           */
          /* Transition: '<S7>:138' */
          rtb_sstab_load = 1U;
          indr = (uint8_T)(IDZoneKnockRpm + ((uint16_T)rtb_sstab_rpm));
          rtb_KnockRecGain_g = (uint16_T)((65536U - ((uint32_T)RtZoneKnockRpm)) >>
            ((uint32_T)6));

          /* Outputs for Function Call SubSystem: '<S8>/AdaptiveCorrectionSingleCell'
           *
           * Block description for '<S8>/AdaptiveCorrectionSingleCell':
           *  This subsystem stores in a single cell of the adaptive table the right
           *  fraction of adaptive correction.
           */
          /* Event: '<S7>:197' */
          Kn_AdaptiveCorrectionSingleCell(rtb_Interpolate_TbKnockAdEE_n,
            (uint16_T)((65536U - ((uint32_T)RtZoneKnockLoad)) >> ((uint32_T)6)),
            rtb_KnockRecGain_g, indr, (uint8_T)IDZoneKnockLoad, IDZoneKnockRpm,
            RtZoneKnockRpm, RtZoneKnockLoad);

          /* End of Outputs for SubSystem: '<S8>/AdaptiveCorrectionSingleCell' */
          if (IDZoneKnockLoad < ((uint16_T)BKLOADADKNOCK_dim)) {
            /* Transition: '<S7>:159' */
            /* Transition: '<S7>:161' */
            rtb_sstab_load = 2U;

            /* Outputs for Function Call SubSystem: '<S8>/AdaptiveCorrectionSingleCell'
             *
             * Block description for '<S8>/AdaptiveCorrectionSingleCell':
             *  This subsystem stores in a single cell of the adaptive table the right
             *  fraction of adaptive correction.
             */
            /*  indr=  IDZoneKnockRpm+ofsr; */
            /*  eta= (1-RtZoneKnockRpm);  */
            /* Event: '<S7>:197' */
            Kn_AdaptiveCorrectionSingleCell(rtb_Interpolate_TbKnockAdEE_n,
              (uint16_T)(((uint32_T)RtZoneKnockLoad) >> ((uint32_T)6)),
              rtb_KnockRecGain_g, indr, (uint8_T)((int32_T)(((int32_T)
              IDZoneKnockLoad) + 1)), IDZoneKnockRpm, RtZoneKnockRpm,
              RtZoneKnockLoad);

            /* End of Outputs for SubSystem: '<S8>/AdaptiveCorrectionSingleCell' */
            /* Transition: '<S7>:164' */
          } else {
            /* Transition: '<S7>:163' */
          }

          /* Transition: '<S7>:166' */
          if (IDZoneKnockRpm < ((uint16_T)BKRPMKNOCK12_dim)) {
            /* Transition: '<S7>:171' */
            /* Transition: '<S7>:175' */
            rtb_sstab_load = (uint8_T)((int32_T)(((int32_T)rtb_sstab_load) + 1));
            indr = (uint8_T)((int32_T)((((int32_T)IDZoneKnockRpm) + ((int32_T)
              rtb_sstab_rpm)) + 1));
            rtb_KnockRecGain_g = (uint16_T)(((uint32_T)RtZoneKnockRpm) >>
              ((uint32_T)6));

            /* Outputs for Function Call SubSystem: '<S8>/AdaptiveCorrectionSingleCell'
             *
             * Block description for '<S8>/AdaptiveCorrectionSingleCell':
             *  This subsystem stores in a single cell of the adaptive table the right
             *  fraction of adaptive correction.
             */
            /* Event: '<S7>:197' */
            Kn_AdaptiveCorrectionSingleCell(rtb_Interpolate_TbKnockAdEE_n,
              (uint16_T)((65536U - ((uint32_T)RtZoneKnockLoad)) >> ((uint32_T)6)),
              rtb_KnockRecGain_g, indr, (uint8_T)IDZoneKnockLoad, IDZoneKnockRpm,
              RtZoneKnockRpm, RtZoneKnockLoad);

            /* End of Outputs for SubSystem: '<S8>/AdaptiveCorrectionSingleCell' */
            if (((int32_T)rtb_sstab_load) == 3) {
              /* Outputs for Function Call SubSystem: '<S8>/AdaptiveCorrectionSingleCell'
               *
               * Block description for '<S8>/AdaptiveCorrectionSingleCell':
               *  This subsystem stores in a single cell of the adaptive table the right
               *  fraction of adaptive correction.
               */
              /* Transition: '<S7>:180' */
              /* Transition: '<S7>:184' */
              /* indr=IDZoneKnockRpm+1+ofsr; */
              /*  eta=RtZoneKnockRpm; */
              /* Event: '<S7>:197' */
              Kn_AdaptiveCorrectionSingleCell(rtb_Interpolate_TbKnockAdEE_n,
                (uint16_T)(((uint32_T)RtZoneKnockLoad) >> ((uint32_T)6)),
                rtb_KnockRecGain_g, indr, (uint8_T)((int32_T)(((int32_T)
                IDZoneKnockLoad) + 1)), IDZoneKnockRpm, RtZoneKnockRpm,
                RtZoneKnockLoad);

              /* End of Outputs for SubSystem: '<S8>/AdaptiveCorrectionSingleCell' */
              /* Transition: '<S7>:185' */
            } else {
              /* Transition: '<S7>:182' */
            }

            /* Transition: '<S7>:186' */
          } else {
            /* Transition: '<S7>:173' */
          }

          /* Transition: '<S7>:178' */
        } else {
          /* Transition: '<S7>:177' */
        }

        /* Transition: '<S7>:187' */
        /* Transition: '<S7>:127' */
      } else {
        /* Transition: '<S7>:120' */
      }

      /* Chart: '<S4>/AdaptiveCorrectionMgm' incorporates:
       *  SubSystem: '<S8>/CalculateAdaptiveCorrection'
       *
       * Block description for '<S4>/AdaptiveCorrectionMgm':
       *  This stateflow manage adaptive learning.
       *  Learning is activate for a tunable period when engine is defined in steady state. Then an average of the individual cylinder knock correction (SAKCorrInd) over the learning period is done and
       *  a fraction of the average value is stored in a table interpolated on engine speed and load. A
       *  dead band is also used in this case to avoid updating the table for too small corrections.
       *
       * Block description for '<S8>/CalculateAdaptiveCorrection':
       *  The aim of this subsystem is to calculate the adaptive term of spark
       *  advance correction according to learning table.
       */
      /* If: '<S13>/If' incorporates:
       *  Constant: '<S11>/KNOCKLEARNDUR'
       *  Constant: '<S13>/ENKNOCKADCORR'
       *  Constant: '<S4>/ENKNOCKADCORR1'
       *  Constant: '<S4>/ENKNOCKADCORR2'
       *  Constant: '<S4>/ENKNOCKADCORR3'
       *  DataTypeConversion: '<S11>/Conversion'
       *  Inport: '<Root>/IDZoneKnockLoad'
       *  Inport: '<Root>/IDZoneKnockRpm'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Inport: '<Root>/RtZoneKnockLoad'
       *  Inport: '<Root>/RtZoneKnockRpm'
       *  Inport: '<S29>/SAKnockCorrAd_old'
       *  MinMax: '<S11>/MinMax1'
       *  Product: '<S11>/Product'
       *  SignalConversion generated from: '<S13>/SAKnockCorrAd_old'
       *  SignalConversion generated from: '<S4>/CntKnockLearn_old'
       *  SignalConversion generated from: '<S4>/EndKnockLearn_old'
       *  SignalConversion generated from: '<S4>/KCorrIndAvgCyl_old'
       *  SignalConversion generated from: '<S4>/KCorrIndSum_old'
       *  SignalConversion generated from: '<S4>/KnockLearnState_old'
       *  SignalConversion generated from: '<S4>/TrigKnockAdat_old'
       *
       * Block requirements for '<S11>/Product':
       *  1. EISB_FCA6CYL_SW_REQ_1247: Every time that adaptive correction learning is enabled (see requi... (ECU_SW_Requirements#2111)
       */
      /* Transition: '<S7>:128' */
      /* Event: '<S7>:212' */
      if (!ENKNOCKADCORR) {
        /* Outputs for IfAction SubSystem: '<S13>/KeepSaKnockCorrAd' incorporates:
         *  ActionPort: '<S29>/Action Port'
         *
         * Block description for '<S13>/KeepSaKnockCorrAd':
         *  This subsystem keep old adaptive correction when adaptive correction
         *  strategy is frozen by means of ENKNOCKADCORR calibration.
         */
        memcpy(&rtb_SAKnockCorrAd_g[0], &rtb_DeltaSAKnockCorrAd_i[0], (sizeof
                (int16_T)) << 3U);

        /* End of Outputs for SubSystem: '<S13>/KeepSaKnockCorrAd' */
      } else {
        /* Outputs for IfAction SubSystem: '<S13>/CalculateSaKnockCorrAd' incorporates:
         *  ActionPort: '<S28>/Action Port'
         *
         * Block description for '<S13>/CalculateSaKnockCorrAd':
         *  This subsystem calculates the adaptive term of spark advance
         *  correction according to learning table.
         */
        /* CCaller: '<S28>/Interpolate_TbKnockAdEE' incorporates:
         *  Constant: '<S28>/Constant'
         *  Constant: '<S28>/Constant5'
         *  Product: '<S28>/Product'
         *  Sum: '<S28>/Add'
         *  Sum: '<S28>/Add1'
         *
         * Block requirements for '<S28>/Interpolate_TbKnockAdEE':
         *  1. EISB_FCA6CYL_SW_REQ_1251: The software shall estimate the Adaptive knock correction on spark... (ECU_SW_Requirements#2115)
         */
        rtb_Interpolate_TbKnockAdEE = Interpolate_TbKnockAdEE((uint16_T)
          ((((uint32_T)((uint8_T)(((uint32_T)BKRPMKNOCK12_dim) + 1U))) *
            ((uint32_T)IonAbsTdcEOA)) + ((uint32_T)IDZoneKnockRpm)),
          RtZoneKnockRpm, IDZoneKnockLoad, RtZoneKnockLoad);

        /* Product: '<S30>/Divide' */
        rtb_Interpolate_TbKnockAdEE = (int16_T)(rtb_Interpolate_TbKnockAdEE /
          128);

        /* Assignment: '<S28>/Assignment' incorporates:
         *  SignalConversion generated from: '<S13>/SAKnockCorrAd_old'
         */
        memcpy(&rtb_SAKnockCorrAd_g[0], &rtb_DeltaSAKnockCorrAd_i[0], (sizeof
                (int16_T)) << 3U);
        rtb_SAKnockCorrAd_g[IonAbsTdcEOA] = rtb_Interpolate_TbKnockAdEE;

        /* Assignment: '<S28>/Assignment1' incorporates:
         *  MultiPortSwitch: '<S28>/IndexVector'
         *  SignalConversion generated from: '<S13>/SAKnockCorrAd_old'
         *  Sum: '<S28>/Add2'
         *
         * Block requirements for '<S28>/Add2':
         *  1. EISB_FCA6CYL_SW_REQ_1815: Software shall calculate, cylinder by cylinder, the difference bet... (ECU_SW_Requirements#7017)
         */
        rtb_SAKnockCorrAd_b[IonAbsTdcEOA] = (int16_T)
          (rtb_Interpolate_TbKnockAdEE - rtb_DeltaSAKnockCorrAd_i[IonAbsTdcEOA]);

        /* End of Outputs for SubSystem: '<S13>/CalculateSaKnockCorrAd' */
      }

      /* End of If: '<S13>/If' */

      /* SignalConversion generated from: '<S4>/EnableAdpCorr' */
      EnableAdpCorr = rtb_EnableAdpCorr;

      /* SignalConversion generated from: '<S4>/FStabLoadKnock' */
      FStabLoadKnock = rtb_SigStab_o1_p;

      /* SignalConversion generated from: '<S4>/FStabRpmKnock' */
      FStabRpmKnock = rtb_SigStab_o1;

      /* SignalConversion generated from: '<S4>/FlgRecAdaptEnable' */
      FlgRecAdaptEnable = rtb_FlgRecAdaptEnable_b;

      /* SignalConversion generated from: '<S4>/FlgSteadyState' */
      FlgSteadyState = rtb_FlgSteadyState;

      /* SignalConversion generated from: '<S4>/KCorrIndAvgCyl' */
      KCorrIndAvgCyl = rtb_Interpolate_TbKnockAdEE_n;

      /* SignalConversion generated from: '<S4>/KnockRecGain' */
      KnockRecGain = 0U;

      /* SignalConversion generated from: '<S4>/CntKnockLearn' */
      memcpy((&(CntKnockLearn[0])), &rtb_CntKnockLearn_g[0], (sizeof(uint16_T)) <<
             3U);

      /* Chart: '<S4>/AdaptiveCorrectionMgm' incorporates:
       *  SubSystem: '<S8>/CalculateAdaptiveCorrection'
       *
       * Block description for '<S4>/AdaptiveCorrectionMgm':
       *  This stateflow manage adaptive learning.
       *  Learning is activate for a tunable period when engine is defined in steady state. Then an average of the individual cylinder knock correction (SAKCorrInd) over the learning period is done and
       *  a fraction of the average value is stored in a table interpolated on engine speed and load. A
       *  dead band is also used in this case to avoid updating the table for too small corrections.
       *
       * Block description for '<S8>/CalculateAdaptiveCorrection':
       *  The aim of this subsystem is to calculate the adaptive term of spark
       *  advance correction according to learning table.
       */
      /* SignalConversion generated from: '<S4>/DeltaSAKnockCorrAd' incorporates:
       *  SignalConversion generated from: '<S13>/DeltaSAKnockCorrAd'
       */
      memcpy((&(DeltaSAKnockCorrAd[0])), &rtb_SAKnockCorrAd_b[0], (sizeof
              (int16_T)) << 3U);

      /* SignalConversion generated from: '<S4>/EndKnockLearn' */
      memcpy((&(EndKnockLearn[0])), &rtb_TrigKnockAdat_b[0], (sizeof(uint8_T)) <<
             3U);

      /* SignalConversion generated from: '<S4>/KCorrIndSum' */
      memcpy((&(KCorrIndSum[0])), &rtb_KCorrIndSum_m[0], (sizeof(int16_T)) << 3U);

      /* SignalConversion generated from: '<S4>/KnockLearnState' */
      memcpy((&(KnockLearnState[0])), &rtb_EndKnockLearn_p[0], (sizeof(uint8_T))
             << 3U);

      /* Chart: '<S4>/AdaptiveCorrectionMgm' incorporates:
       *  SubSystem: '<S8>/CalculateAdaptiveCorrection'
       *
       * Block description for '<S4>/AdaptiveCorrectionMgm':
       *  This stateflow manage adaptive learning.
       *  Learning is activate for a tunable period when engine is defined in steady state. Then an average of the individual cylinder knock correction (SAKCorrInd) over the learning period is done and
       *  a fraction of the average value is stored in a table interpolated on engine speed and load. A
       *  dead band is also used in this case to avoid updating the table for too small corrections.
       *
       * Block description for '<S8>/CalculateAdaptiveCorrection':
       *  The aim of this subsystem is to calculate the adaptive term of spark
       *  advance correction according to learning table.
       */
      /* SignalConversion generated from: '<S4>/SAKnockCorrAd' incorporates:
       *  SignalConversion generated from: '<S13>/SAKnockCorrAd'
       */
      memcpy((&(SAKnockCorrAd[0])), &rtb_SAKnockCorrAd_g[0], (sizeof(int16_T)) <<
             3U);

      /* SignalConversion generated from: '<S4>/TrigKnockAdat' */
      memcpy((&(TrigKnockAdat[0])), &rtb_KnockLearnState_o[0], (sizeof(uint8_T))
             << 3U);

      /* SignalConversion generated from: '<S4>/sstab_load' */
      sstab_load = rtb_SigStab_o2_a;

      /* SignalConversion generated from: '<S4>/sstab_rpm' */
      sstab_rpm = rtb_SigStab_o2;

      /* Update for Memory: '<S32>/Memory1' */
      KnockCorrAdp_DW.Memory1_PreviousInput = rtb_SigStab_o3;

      /* Update for Memory: '<S32>/Memory' */
      KnockCorrAdp_DW.Memory_PreviousInput = rtb_SigStab_o4;

      /* Update for Memory: '<S33>/Memory1' */
      KnockCorrAdp_DW.Memory1_PreviousInput_f = rtb_SigStab_o3_h;

      /* Update for Memory: '<S33>/Memory' */
      KnockCorrAdp_DW.Memory_PreviousInput_e = rtb_SigStab_o4_k;

      /* End of Outputs for SubSystem: '<S1>/NormalMode' */
      /* Transition: '<S2>:51' */
      break;
    }
    break;

   default:
    /* Transition: '<S2>:54' */
    /*  KnockCorrAdp_10ms */
    RecoveryCyl = 0U;
    while ((RecoveryCyl < N_CYLINDER) && (((int32_T)FlgSyncPhased) == 0)) {
      /* Transition: '<S2>:58' */
      /* NEW_PATTERN */
      /* Transition: '<S2>:59' */
      if (((int32_T)VtRec[RecoveryCyl + REC_KNOCKCORR_OFF_0]) == 1) {
        /* Outputs for Function Call SubSystem: '<S1>/Recovery_Mode'
         *
         * Block description for '<S1>/Recovery_Mode':
         *  This subsystem implements recovery mode for adaptive correction.
         */
        /* SignalConversion generated from: '<S5>/DeltaSAKnockCorrAd_old' */
        /* Transition: '<S2>:71'
         * Requirements for Transition: '<S2>:71':
         *  1. EISB_FCA6CYL_SW_REQ_1291: The software shall use a recovery value for the adaptive knock cor... (ECU_SW_Requirements#2138)
         */
        /* Transition: '<S2>:73' */
        /* Event: '<S2>:43' */
        memcpy(&rtb_DeltaSAKnockCorrAd_i[0], (&(DeltaSAKnockCorrAd[0])), (sizeof
                (int16_T)) << 3U);

        /* SignalConversion generated from: '<S5>/SAKnockCorrAd_old' */
        memcpy(&rtb_SAKnockCorrAd_b[0], (&(SAKnockCorrAd[0])), (sizeof(int16_T))
               << 3U);

        /* SignalConversion generated from: '<S5>/TrigKnockAdat_old' */
        memcpy(&rtb_TrigKnockAdat_b[0], (&(TrigKnockAdat[0])), (sizeof(uint8_T))
               << 3U);

        /* SignalConversion generated from: '<S5>/CntKnockLearn_old' */
        memcpy(&rtb_CntKnockLearn_g[0], (&(CntKnockLearn[0])), (sizeof(uint16_T))
               << 3U);

        /* SignalConversion generated from: '<S5>/EndKnockLearn_old' */
        memcpy(&rtb_EndKnockLearn_p[0], (&(EndKnockLearn[0])), (sizeof(uint8_T))
               << 3U);

        /* SignalConversion generated from: '<S5>/KnockLearnState_old' */
        memcpy(&rtb_KnockLearnState_o[0], (&(KnockLearnState[0])), (sizeof
                (uint8_T)) << 3U);

        /* SignalConversion generated from: '<S5>/KCorrIndSum_old' */
        memcpy(&rtb_KCorrIndSum_m[0], (&(KCorrIndSum[0])), (sizeof(int16_T)) <<
               3U);

        /* Switch: '<S37>/Switch' incorporates:
         *  Constant: '<S37>/Constant14'
         *  Constant: '<S37>/HYSTLOADRECKNOCK'
         *  Constant: '<S37>/MINLOADRECKNOCK'
         *  Constant: '<S37>/MINLOADRECKNOCK1'
         *  Inport: '<Root>/Load'
         *  RelationalOperator: '<S37>/Relational Operator'
         *  RelationalOperator: '<S37>/Relational Operator1'
         *  Sum: '<S37>/Add3'
         *  Switch: '<S37>/Switch1'
         *
         * Block requirements for '<S37>/Switch':
         *  1. EISB_FCA6CYL_SW_REQ_1873: In case that the recovery REC_KNOCKCORR_OFF_X is active for cylind... (ECU_SW_Requirements#7020)
         */
        if (((int32_T)Load) >= ((int32_T)((uint32_T)(((uint32_T)MINLOADRECKNOCK)
               + ((uint32_T)HYSTLOADRECKNOCK))))) {
          rtb_KnockRecGain_g = 1024U;
        } else if (Load <= MINLOADRECKNOCK) {
          /* Switch: '<S37>/Switch1' incorporates:
           *  Constant: '<S37>/Constant1'
           */
          rtb_KnockRecGain_g = 0U;
        } else {
          /* Switch: '<S37>/Switch1' incorporates:
           *  Constant: '<S37>/HYSTLOADRECKNOCK1'
           *  Constant: '<S37>/MINLOADRECKNOCK2'
           *  Product: '<S37>/Divide'
           *  Sum: '<S37>/Add1'
           */
          rtb_KnockRecGain_g = (uint16_T)((((uint32_T)((uint16_T)(((uint32_T)
            Load) - ((uint32_T)MINLOADRECKNOCK)))) << ((uint32_T)10)) /
            ((uint32_T)HYSTLOADRECKNOCK));
        }

        /* CCaller: '<S38>/Interpolate_TbKnockAdEE' incorporates:
         *  Constant: '<S38>/Constant'
         *  Constant: '<S38>/Constant5'
         *  Inport: '<Root>/IDZoneKnockLoad'
         *  Inport: '<Root>/IDZoneKnockRpm'
         *  Inport: '<Root>/RtZoneKnockLoad'
         *  Inport: '<Root>/RtZoneKnockRpm'
         *  Product: '<S38>/Product'
         *  Sum: '<S38>/Add'
         *  Sum: '<S38>/Add1'
         *
         * Block requirements for '<S38>/Interpolate_TbKnockAdEE':
         *  1. EISB_FCA6CYL_SW_REQ_1292: The software shall use a recovery value for the adaptive knock cor... (ECU_SW_Requirements#2139)
         */
        rtb_Interpolate_TbKnockAdEE_n = Interpolate_TbKnockAdEE((uint16_T)
          (((uint32_T)((uint16_T)(((uint32_T)((uint8_T)(((uint32_T)
          BKRPMKNOCK12_dim) + 1U))) * ((uint32_T)RecoveryCyl)))) + ((uint32_T)
          IDZoneKnockRpm)), RtZoneKnockRpm, IDZoneKnockLoad, RtZoneKnockLoad);

        /* DataTypeConversion: '<S39>/Conversion2' incorporates:
         *  Product: '<S36>/Product'
         *  Product: '<S39>/Divide'
         *  Product: '<S40>/Divide'
         *
         * Block requirements for '<S36>/Product':
         *  1. EISB_FCA6CYL_SW_REQ_1292: The software shall use a recovery value for the adaptive knock cor... (ECU_SW_Requirements#2139)
         */
        rtb_Interpolate_TbKnockAdEE_n = (int16_T)(((((int32_T)
          rtb_Interpolate_TbKnockAdEE_n) / 128) * ((int32_T)rtb_KnockRecGain_g))
          / 1024);

        /* Assignment: '<S36>/Assignment' */
        memcpy((&(SAKnockCorrAd[0])), &rtb_SAKnockCorrAd_b[0], (sizeof(int16_T))
               << 3U);

        /* Assignment: '<S36>/Assignment6' */
        memcpy((&(DeltaSAKnockCorrAd[0])), &rtb_DeltaSAKnockCorrAd_i[0], (sizeof
                (int16_T)) << 3U);

        /* Assignment: '<S36>/Assignment1' */
        memcpy((&(TrigKnockAdat[0])), &rtb_TrigKnockAdat_b[0], (sizeof(uint8_T))
               << 3U);

        /* Assignment: '<S36>/Assignment2' */
        memcpy((&(CntKnockLearn[0])), &rtb_CntKnockLearn_g[0], (sizeof(uint16_T))
               << 3U);

        /* Assignment: '<S36>/Assignment3' */
        memcpy((&(EndKnockLearn[0])), &rtb_EndKnockLearn_p[0], (sizeof(uint8_T))
               << 3U);

        /* Assignment: '<S36>/Assignment4' */
        memcpy((&(KnockLearnState[0])), &rtb_KnockLearnState_o[0], (sizeof
                (uint8_T)) << 3U);

        /* Assignment: '<S36>/Assignment5' */
        memcpy((&(KCorrIndSum[0])), &rtb_KCorrIndSum_m[0], (sizeof(int16_T)) <<
               3U);

        /* Assignment: '<S36>/Assignment' */
        SAKnockCorrAd[(RecoveryCyl)] = rtb_Interpolate_TbKnockAdEE_n;

        /* Assignment: '<S36>/Assignment6' incorporates:
         *  MultiPortSwitch: '<S36>/Index Vector'
         *  Sum: '<S36>/Add'
         */
        DeltaSAKnockCorrAd[(RecoveryCyl)] = (int16_T)
          (rtb_Interpolate_TbKnockAdEE_n - rtb_SAKnockCorrAd_b[RecoveryCyl]);

        /* SignalConversion generated from: '<S5>/KnockRecGain' */
        KnockRecGain = rtb_KnockRecGain_g;

        /* Assignment: '<S36>/Assignment1' incorporates:
         *  Constant: '<S36>/Constant3'
         */
        TrigKnockAdat[(RecoveryCyl)] = 0U;

        /* Assignment: '<S36>/Assignment2' incorporates:
         *  Constant: '<S36>/Constant10'
         */
        CntKnockLearn[(RecoveryCyl)] = 0U;

        /* Assignment: '<S36>/Assignment3' incorporates:
         *  Constant: '<S36>/Constant11'
         */
        EndKnockLearn[(RecoveryCyl)] = 0U;

        /* Assignment: '<S36>/Assignment4' incorporates:
         *  Constant: '<S36>/Constant12'
         */
        KnockLearnState[(RecoveryCyl)] = 0U;

        /* Assignment: '<S36>/Assignment5' incorporates:
         *  Constant: '<S36>/Constant13'
         */
        KCorrIndSum[(RecoveryCyl)] = 0;

        /* End of Outputs for SubSystem: '<S1>/Recovery_Mode' */
        /* Transition: '<S2>:74' */
      } else {
        /* Transition: '<S2>:80' */
      }

      /* Transition: '<S2>:60' */
      RecoveryCyl = (uint8_T)((int32_T)(((int32_T)RecoveryCyl) + 1));

      /* Transition: '<S2>:84' */
    }

    /* Transition: '<S2>:61' */
    break;
  }

  /* End of Chart: '<Root>/KnockCorrAdp_Scheduler' */
  /* Transition: '<S2>:82' */
}

/*
 * Output and update for function-call system: '<Root>/KnockCorrAdp_Scheduler'
 * Block description for: '<Root>/KnockCorrAdp_Scheduler'
 *   This chart is a simple scheduler used to activate different model
 *   behaviours (recovery, normal, disabled).
 */
void KnockCor_KnockCorrAdp_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[4];
  int32_T i;

  /* Chart: '<Root>/KnockCorrAdp_Scheduler' incorporates:
   *  TriggerPort: '<S2>/input events'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */
  for (i = 0; i < 4; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: KnockCorrAdp_Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S2>:1' */
    i = (int32_T)Knoc_event_KnockCorrAdp_PowerOn;
    Knock_chartstep_c3_KnockCorrAdp(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S2>:2' */
    i = (int32_T)Knock_event_KnockCorrAdp_NoSync;
    Knock_chartstep_c3_KnockCorrAdp(&i);
  }

  if (rtb_inputevents[2U] == 2) {
    /* Event: '<S2>:3' */
    i = (int32_T)KnockCor_event_KnockCorrAdp_EOA;
    Knock_chartstep_c3_KnockCorrAdp(&i);
  }

  if (rtb_inputevents[3U] == 2) {
    /* Event: '<S2>:4' */
    i = (int32_T)KnockCo_event_KnockCorrAdp_10ms;
    Knock_chartstep_c3_KnockCorrAdp(&i);
  }
}

/* Model step function */
void KnockCorrAdp_10ms(void)
{
  /* Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrAdp_10ms' */

  /* Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */
  KnockCor_KnockCorrAdp_Scheduler(3);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrAdp_10ms' */
}

/* Model step function */
void KnockCorrAdp_EOA(void)
{
  /* Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrAdp_EOA' */

  /* Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */
  KnockCor_KnockCorrAdp_Scheduler(2);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrAdp_EOA' */
}

/* Model step function */
void KnockCorrAdp_NoSync(void)
{
  /* Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrAdp_NoSync' */

  /* Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */
  KnockCor_KnockCorrAdp_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrAdp_NoSync' */
}

/* Model step function */
void KnockCorrAdp_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrAdp_PowerOn' incorporates:
   *  Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */

  /* Chart: '<Root>/KnockCorrAdp_Scheduler'
   *
   * Block description for '<Root>/KnockCorrAdp_Scheduler':
   *  This chart is a simple scheduler used to activate different model
   *  behaviours (recovery, normal, disabled).
   */
  KnockCor_KnockCorrAdp_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/KnockCorrAdp_PowerOn' */
}

/* Model initialize function */
void KnockCorrAdp_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

int16_T KnockRecGain;
int16_T DeltaSaKnockCorrAd[N_CYL_MAX];
int16_T SAKnockCorrAd[N_CYL_MAX];
uint8_T TrigKnockAdat[N_CYL_MAX];
int16_T TbKnockAdEE[480];
void KnockCorrAdp_Stub(void)
{
  uint8_T idx,idy;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    DeltaSaKnockCorrAd[idx] = 0;
    SAKnockCorrAd[idx] = 0;
    TrigKnockAdat[idx] = 0u;
  }

  for (idx=0;idx<480;idx++) {
    TbKnockAdEE[idx] = 0;
  }

  KnockRecGain = 0;
}

void KnockCorrAdp_EOA(void)
{
  KnockCorrAdp_Stub();
}

void KnockCorrAdp_PowerOn(void)
{
  KnockCorrAdp_Stub();
}

void KnockCorrAdp_NoSync(void)
{
  KnockCorrAdp_Stub();
}

void KnockCorrAdp_10ms(void)
{
  KnockCorrAdp_Stub();
}

#endif                                 /* _BUILD_KNOCKCORRADP_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/