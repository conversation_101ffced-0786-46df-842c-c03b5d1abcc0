/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DiagCanMgm
**  Filename        :  DiagCanMgm.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  MocciA
******************************************************************************/

#ifndef __DIAGCANMGM_H__
#define __DIAGCANMGM_H__

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "diagcanmgm_out.h"
#include "canmgmin_out.h"   // for VehSpeed variable scope


/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
#define ZERO_OFFSET          0u

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */


/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static void Decoding(void);
static void ResponseEvaluation(void);
static void DST(uint8_T MessageSession);
static void resetDefaultValues(void);
#endif // __DIAGCANMGM_H__
