/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#include    "sys.h"
#include    "ivor_c0.h"
#include    "ivor_c2.h"
#include    "intsrcmgm.h"
#include    "ee_out.h"


#ifdef  _BUILD_INTSRCMGM_

void  SaveIntptContext_c0(uint8_T IvorIdx)
{
    EE_IvorIndex_c0     = IvorIdx;
	EE_IvorCnt_c0[IvorIdx]++;
    EE_SRR0_Value_c0    = SRR0_Value_c0;
    EE_SRR1_Value_c0    = SRR1_Value_c0;
    EE_CSRR0_Value_c0   = CSRR0_Value_c0;
    EE_CSRR1_Value_c0   = CSRR1_Value_c0;
    EE_SPR_ESRValue_c0  = SPR_ESRValue_c0;
    EE_SPR_DEARValue_c0 = SPR_DEARValue_c0;
    EE_SPR_MCSRValue_c0 = SPR_MCSRValue_c0;
    EE_SPR_MCARValue_c0 = SPR_MCARValue_c0;
    EE_MCSRR0_Value_c0  = MCSRR0_Value_c0;
    EE_MCSRR1_Value_c0  = MCSRR1_Value_c0;
}


void  SaveIntptContext_c2(uint8_T IvorIdx)
{
    EE_IvorIndex_c2     = IvorIdx;
	EE_IvorCnt_c2[IvorIdx]++;
    EE_SRR0_Value_c2    = SRR0_Value_c2;
    EE_SRR1_Value_c2    = SRR1_Value_c2;
    EE_CSRR0_Value_c2   = CSRR0_Value_c2;
    EE_CSRR1_Value_c2   = CSRR1_Value_c2;
    EE_SPR_ESRValue_c2  = SPR_ESRValue_c2;
    EE_SPR_DEARValue_c2 = SPR_DEARValue_c2;
    EE_SPR_MCSRValue_c2 = SPR_MCSRValue_c2;
    EE_SPR_MCARValue_c2 = SPR_MCARValue_c2;
    EE_MCSRR0_Value_c2  = MCSRR0_Value_c2;
    EE_MCSRR1_Value_c2  = MCSRR1_Value_c2;
}


void SYS_IvorExHandling(void)
{
  ++GenIvorCnt;
  switch(IvorIndex_c0)
  {
    uint32_T ExcepType;

    case IVOR1_INDX:
    case IVOR6_INDX:
    case IVOR2_INDX:
    case IVOR3_INDX:
    case IVOR5_INDX:
    case IVOR13_INDX:
    case IVOR32_INDX:
    case IVOR33_INDX:
    case IVOR34_INDX:
    case IVOR7_INDX:
    case IVOR8_INDX:
    case IVOR14_INDX:
    case IVOR12_INDX:
        //SaveIntptContext(IvorIndex);
        break;
    case IVOR0_INDX:
    default:
        break;
  }
}

#else
    #warning NO INTSRCMGM
#endif // _BUILD_INTSRCMGM_

