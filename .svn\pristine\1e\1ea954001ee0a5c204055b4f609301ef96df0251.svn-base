/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $ Description:                                                                                             $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
#ifndef _TIMING_EEP_C
#define _TIMING_EEP_C

#ifdef _BUILD_TIMING_

/// Seconds ECU lifetime
uint32_T  SecondsWorkTime = 0u;
/// Total power supply time of the ECU, eeprom copy of ECUTimeStamps
uint32_T  ECUTimeStampsEE   = 0u;
/// Power supply time of the control unit since Key ON, eeprom copy of ECUTimeStampsFromKeyOn
uint16_T  ECUTimeStampsFromKeyOnEE = 0u;

#endif // _BUILD_TIMING_
#endif // _TIMING_EEP_C


