/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_dtm.h
 * @brief   SPC5xx GTM DTM header file.
 *
 * @addtogroup DTM
 * @{
 */

#ifndef _GTM_DTM_H_
#define _GTM_DTM_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"

#if (SPC5_GTM_USE_DTM == TRUE) || defined(__DOXYGEN__)

/*===========================================================================*/
/* Driver constants.                                                         */
/*===========================================================================*/

/**
 * @name    Dead time type
 * @{
 */
#define SPC5_GTM_DTM_DEADTIME_DISABLED           0U
#define SPC5_GTM_DTM_DEADTIME_STANDARD           0U
#define SPC5_GTM_DTM_DEADTIME_CROSS_CHANNEL      1U
/** @} */

/*===========================================================================*/
/* Derived constants and error checks.                                       */
/*===========================================================================*/

#if ((SPC5_GTM_USE_CDTM0 == FALSE) && (SPC5_GTM_USE_CDTM1 == FALSE) && \
     (SPC5_GTM_USE_CDTM2 == FALSE) && (SPC5_GTM_USE_CDTM3 == FALSE) && \
     (SPC5_GTM_USE_CDTM4 == FALSE))
#error "DTM module enabled but no DTM selected"
#endif

/*===========================================================================*/
/* Driver data structures and types.                                         */
/*===========================================================================*/

/**
 * @brief Type of a structure representing a (GTM-IP) CDTM driver.
 */
typedef struct GTM_CDTMDriver GTM_CDTMDriver;

/**
 * @brief   Structure representing a (GTM) CDTM driver.
 */
struct GTM_CDTMDriver {
  /**
   * @brief   Pointer to the (GTM) CDTM DTM registers block.
   */
  volatile GTM_DTM_TAG *dtmp[6];
};

/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_CDTM0 == TRUE) && !defined(__DOXYGEN__)
extern GTM_CDTMDriver CDTMD1;
#endif

#if (SPC5_GTM_USE_CDTM1 == TRUE) && !defined(__DOXYGEN__)
extern GTM_CDTMDriver CDTMD2;
#endif

#if (SPC5_GTM_USE_CDTM2 == TRUE) && !defined(__DOXYGEN__)
extern GTM_CDTMDriver CDTMD3;
#endif

#if (SPC5_GTM_USE_CDTM3 == TRUE) && !defined(__DOXYGEN__)
extern GTM_CDTMDriver CDTMD4;
#endif

#if (SPC5_GTM_USE_CDTM4 == TRUE) && !defined(__DOXYGEN__)
extern GTM_CDTMDriver CDTMD5;
#endif

#ifdef __cplusplus
extern "C" {
#endif
void gtm_dtmInit(void);
extern void gtm_dtmStart(GTM_CDTMDriver *cdtmdp, uint8_t dtm);
extern void gtm_dtmStop(GTM_CDTMDriver *cdtmdp, uint8_t dtm);
extern void gtm_dtmShutOffRelease(GTM_CDTMDriver *cdtmdp, uint8_t dtm);
extern void gtm_dtmSetShiftSel(GTM_CDTMDriver *cdtmdp, uint8_t dtm, uint8_t shiftsel);
extern uint8_t gtm_dtmGetShiftSel(GTM_CDTMDriver *cdtmdp, uint8_t dtm);
extern void gtm_dtmSetRelBLK(GTM_CDTMDriver *cdtmdp, uint8_t dtm, uint16_t relblk);
extern uint16_t gtm_dtmGetRelBLK(GTM_CDTMDriver *cdtmdp, uint8_t dtm);
#ifdef __cplusplus
}
#endif

#endif /* SPC5_GTM_USE_DTM */

#endif /* _GTM_DTM_H_ */
/** @} */
