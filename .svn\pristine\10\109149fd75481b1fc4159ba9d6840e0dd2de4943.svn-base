/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  Flashresume.c
**  Created on      :  22-giu-2020 10:00:00
**  Original author :  CarboniM
**  Integration history: 
**  -  Standard software driver for C55 Flash from "STM - SPC57xKLS-FLASH Driver for C55: Package version 0.2.0"
**          
*******************************************************************************************************************/

#ifdef  _BUILD_FLASH_

#pragma ghs startnomisra

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include    "ssd_c55.h"


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

static const unsigned short FlashResume_C[] = 
{
#if (TARGET_TYPE == SPC574K2_CUT24) || (TARGET_TYPE == SPC574K2)     /* Total Size = 93 half words */

    0x0080, 0x1821, 0x06F0, 0xD3F1, 0xD501, 0x0135, 0x0146, 
    0x480F, 0x30E5, 0x0040, 0x2A07, 0xE604, 0x5165, 0x0000,
    0xE804, 0xC075, 0x1967, 0x8004, 0x518B, 0x0000, 0x48F4, 
    0x4A07, 0x9076, 0x4803, 0x1987, 0xC808, 0xE60A, 0x4A17,
    0x9076, 0x4883, 0x1987, 0xC802, 0xE60D, 0x4A37, 0x9076, 
    0xE80A, 0x1987, 0xC802, 0xE621, 0x1987, 0xC810, 0xE21E,
    0x4A27, 0x9076, 0x4823, 0x50EB, 0x0000, 0x65F7, 0x18E7, 
    0xC51F, 0x54EB, 0x0000, 0x7C66, 0x18F8, 0x50EB, 0x0000,
    0x4667, 0x18E7, 0xC51F, 0x54EB, 0x0000, 0x50EB, 0x0000, 
    0x70E0, 0xCC00, 0xE606, 0x0147, 0x2404, 0x1887, 0xA800,
    0xE5F7, 0x30E5, 0x0048, 0x2A07, 0xE608, 0x7FE3, 0xFB78, 
    0x1800, 0xD000, 0x0002, 0x1800, 0xD000, 0x01F7, 0x0173,
    0xC3F1, 0xC501, 0x20F1, 0x0090, 0x0004, 0x3038, 0x3031, 
    0x3446, 0x4646

#endif
};


extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned short *functionBuffer, uint32_T functionSize);

/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FlashResume - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint32_T FlashResume (PSSD_CONFIG pSSDConfig, uint8_T *resumeState)
{
#pragma ghs nowarning 171
    FlashFunctionLoader( (unsigned short*)FlashResume_C, sizeof(FlashResume_C)/2);
    return ((PFLASHRESUME)FlashFunctionPointer)(pSSDConfig, resumeState);
#pragma ghs endnowarning /* warning #171-D: invalid type conversion */
}
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */
#pragma ghs endnomisra

#endif /*  _BUILD_FLASH_ */
