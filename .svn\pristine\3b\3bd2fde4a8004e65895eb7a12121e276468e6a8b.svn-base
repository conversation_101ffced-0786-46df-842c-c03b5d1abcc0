#-------------------------------------------------------------------------------
# Name:        modulo1
# Purpose:
#
# Author:      SalimbeniT
#
# Created:     19/09/2013
# Copyright:   (c) SalimbeniT 2013
# Licence:     <your licence>
#-------------------------------------------------------------------------------

import win32com.client

from ctypes import *
import time
import glob
import os
import sys
import easygui as eg


""" New Strategy """
def NewStrategy():
    print("\nNew Strategy\n")
    time.sleep(timesleep)
    shell.SendKeys("%f")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")

""" A2L """
def LoadA2L(Path):
    print("Load the A2l file")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(0.5)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Path)
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")

"""S Record Import"""
def ImportSrecord(Path):
    print("Import S-record File")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Path)
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    #more
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")

"""binary Import"""
def ImportBin(Path,region):
    print("Import bin")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Path)
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    for i in range (0,int(region)):
        shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    #more
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")

"""      TEMPORARY!!!!!!!     """
def ImportCalBin(Path):
    print("Import Calibration bin")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Path+"\\GHS\\bin\\"+TargetName+"\\"+CalBinName)
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    for i in range (0,ImpMemoryArc.index(CalBinRegion)):
        shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    #more
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")

""" Export Calib_Rom_offline"""
def ExpCalibRomOffline(Path):
    print("Export Calib Rom Offline")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Path+"\\GHS\\bin\\"+TargetName+"\\ROM.bin")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    for i in range (0,ExpMemoryArc.index(CalRomOffRegion)):
        shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")

    Files=glob.glob(Path+"\\GHS\\bin\\"+TargetName+"\\*.bin")
    nomi_bin=[]
    Flag=0
    lunghezza_percorso=len(Path+"\\GHS\\bin\\"+TargetName+"\\")
    for i in Files:
        if (i[lunghezza_percorso:-4]=="ROM"):
            Flag=1
    if Flag==1:
        time.sleep(timesleep)
        shell.SendKeys("{ENTER}")
    #more
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")


"""Import cal_Ram"""
def ImpCalRam(Path):
    print("Import CalRam")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Path+"\\GHS\\bin\\"+TargetName+"\\ROM.bin")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    for i in range (0,ImpMemoryArc.index(CalRamRegion)):
        shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    #more
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")


"""Import Calib"""
def ImportCalib(Calib):
    print("Import Calibration File")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Calib)
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    #more
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")


""" Export Cal_Ram """
def ExpBin(Path,region):
    print("Export CalRam")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Path)
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    for i in range (1,10):
        shell.SendKeys("{UP}")
    for i in range (0,int(region)-1):
        shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
##    time.sleep(timesleep)
##    shell.SendKeys("{ENTER}")

    Find = Path.split("\\")
    path = Path[:-len(Find[-1])]

    FilesN=glob.glob(path + "*.bin")

    Files=glob.glob(Path)
    nomi_bin=[]
    Flag=0
    #lunghezza_percorso=len(Path+"\\GHS\\bin\\"+TargetName+"\\")
    for i in Files:
        if (i==Path):
            Flag=1
    if Flag==1:
        time.sleep(timesleep)
        shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")



""" Fai Girare Script"""
def ExecuteScript(Path,OutputPath):
    print("Execute Script")
    TimeOld=os.path.getmtime(OutputPath)
    TimeNew=TimeOld
    osCommandString = "start cmd"
    os.system(osCommandString)
    time.sleep(2)
    Find = Path.split("\\")
    path = Path[:-len(Find[-1])]
    shell.SendKeys("cd " + path)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Find[-1])
    shell.SendKeys("{ENTER}")
    while TimeOld==TimeNew:
        TimeNew=os.path.getmtime(OutputPath)
        time.sleep(1.5)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("exit")
    shell.SendKeys("{ENTER}")


    """Import cal_Ram"""
def ImpCalRom(Path):
    print("Import CalRom")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")
    time.sleep(timesleep)
    shell.SendKeys(Path+"\\CCP_Tool\\Vision\\EldorECU\\"+CalRomImpName)
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    shell.SendKeys("{TAB}")
    time.sleep(timesleep)
    for i in range (0,ImpMemoryArc.index(CalRomRegion)):
        shell.SendKeys("{DOWN}")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")

def CloseStrategy():
    print("\nClose Strategy")
    time.sleep(timesleep)
    shell.SendKeys("{ENTER}")




"""da la possibilita di scegliere il path del progetto e nel caso di poterlo salvare come path predefinito"""
def OpenPath(title):
    openpath=eg.diropenbox("Selezionare Cartella Progetto", title)
    if openpath==None:
        sys.exit()
    if (openpath!=""):
        Path=openpath
        msg = "Salvare %s come path di default?" % Path
        choices = ["Yes","No","Close"]
        reply = eg.buttonbox(msg,choices=choices)
        if reply=="Yes":
            PathConf=Path
        elif reply=="Close":
            sys.exit()
        else:
            PathConf=""
    Files=glob.glob(Path+"\\*.*")
    lunghezza_percorso=len(Path+"\\")
    nomi=[]
    for i in Files:
        nomi.append(i[lunghezza_percorso:])
    if"KitCreate.bat" not in nomi:
        eg.msgbox("Path Non Corretto",title)
        sys.exit()

    return(Path,PathConf)

"""da la possibilita di scegliere il path della Calibrazione e nel caso di poterlo salvare come path di calibrazione predefinito"""
def OpenCalibPath(title):
    openpath=eg.diropenbox("Selezionare Cartella delle Calibrazioni", title)
    if openpath==None:
        sys.exit()
    if (openpath!=""):
        PathCalib=openpath
        msg = "Salvare %s come path di calibrazione di default?" % PathCalib
        choices = ["Yes","No","Close"]
        reply = eg.buttonbox(msg,choices=choices)
        if reply=="Yes":
            PathCalibConf=PathCalib
        elif reply=="Close":
            sys.exit()
        else:
            PathCalibConf=""

    return(PathCalib,PathCalibConf)

"""da la possibilita di scegliere la Calibrazione e nel caso di poterla salvare come calibrazione predefinita"""
def OpenCalib(title,PathCalib,CalRead,PathCalibConf):
        Files=glob.glob(PathCalib+"\\"+"*.cal")
        nomi_cal=[]
        lunghezza_percorso=len(PathCalib+"\\")
        for i in Files:
            nomi_cal.append(i[lunghezza_percorso:-4])
        msg = "Selezionare Calibrazione"
        cal = eg.choicebox(msg, title, nomi_cal)
        if (cal!=None):
            if (PathCalibConf!=""):
                msg = "Salvare %s come calibrazione di default?" % cal
                choices = ["Yes","No"]
                reply = eg.buttonbox(msg,choices=choices)
                if reply=="Yes":
                    CalibConf=cal
                else:
                    CalibConf=""
            else:
                CalibConf=""
        else:
            if CalRead=="":
                eg.msgbox("nessuna calibrazione di default specificata",title)
                sys.exit()
            else:
                cal=CalRead
        if cal not in nomi_cal:
            sys.exit("Calibrazione Non Esistente")



        return(cal,CalibConf)

def main():
    pass

if __name__ == '__main__':
    main()
config = None
PathConf = ""
CalibConf = ""
PathCalibConf="" #Dati da salvare nel file di configurazione

title = "CreaStrategy"
eg.msgbox("STRATEGY CREATOR",title,image="Logo.gif")

try:
    config = open("CreaStrategyConfig.txt","r") #apre file di configurazione, se non disponibile lo crea e poi esce

except:
    eg.msgbox("File di Configurazione assente")
    sys.exit()



#parsing del file di configurazione
config.seek(0)
Operations=[]
Files=[]
Regions=[]
Commands=[]
Import=[]
Export=[]
CmdSeq=[]
lineTmp=[]
lineTmpOut=[]
i=0
ListCmdFlag = 0


for line in config:
    if "%%%" in line:
        line=next(config)
        Op=(line.split(':')[1]).strip()
        line=next(config)
        file = line.split(':')[1].strip()
        line=next(config)
        region=(line.split(':')[1]).strip()
        Operations.append(Op)
        Files.append(file)
        Regions.append(region)

    if "Path relativo dello script" in line:
        PathScript = line.split(':')[1].strip()
        

    if "Struttura import:" in line:
        line=next(config)
        while ")" in line:
            Import.append((line.split(')')[1]).strip())
            line=next(config)

    if "Struttura export:" in line:
        line=next(config)
        while ")" in line:
            Export.append((line.split(')')[1]).strip())
            line=next(config)

    if "Lista comandi" in line:
        ListCmdFlag = 1

        line=next(config)

    if (ListCmdFlag == 1) & (":" in line):
        CmdSeq.append(line.split(':')[0])
        lineTmp=line.split(':')[1].split(',')
        for i in lineTmp:
            lineTmpOut.append(int(i))
        Commands.append(lineTmpOut)
        lineTmpOut=[]

path = os.path.dirname(os.path.abspath(__file__))
root = path[:-len(PathScript)]

for i in range(0,len(Files)):
    if Files[i]!="-":
        Files[i]=root+Files[i]

for i in range(0,len(Regions)):
    if "\\" in Regions[i]:
        Regions[i]=root+Regions[i]



indexCmqSeq = 0
msg = "Selezionare una lista di comandi\n"
choices = []
for i in CmdSeq:
    choices.append(i)
reply = eg.buttonbox(msg,choices=choices)
for i in CmdSeq:
    if reply == i:
        indexCmqSeq = CmdSeq.index(i)

#print(CmdSeq)
#print(Commands)
#print(indexCmqSeq)

#print(Operations)
#print(Files)
#print(Regions)
#print(Commands)
#print(Import)
#print(Export)

eg.msgbox("Passa alla finastra ATI vision entro 5 secondi",title)
time.sleep(5)
shell = win32com.client.Dispatch("WScript.Shell")

timesleep=0.4

for com in Commands[indexCmqSeq]:
    index=int(com)-1
    if Operations[index] == str(0): #crea strategy
        NewStrategy()

    if Operations[index] == str(1): #importa A2L
        LoadA2L(Files[index])

    if Operations[index] == str(2): #importa binario
        if Regions[index] == "-":
            ImportSrecord(Files[index])
        else:
            ImportBin(Files[index],Regions[index])

    if Operations[index] == str(3): #importa Calibrazioni
        ImportCalib(Files[index])

    if Operations[index] == str(4): #esporta binario
        ExpBin(Files[index],Regions[index])

    if Operations[index] == str(6): #esegui script
        ExecuteScript(Files[index],Regions[index])

CloseStrategy()










###for i in range (1,len(Operation))
##line=config.readline()
##line=next(config)
##line=next(config)
##PathRead=(line.split(':')[1]).strip()+":"+(line.split(':')[2]).strip()
##line=next(config)
##line=next(config)
##CalPathRead=(line.split(':')[1]).strip()+":"+(line.split(':')[2]).strip()
##line=next(config)
##line=next(config)
##CalRead=(line.split(':')[1]).strip()
##line=next(config)
##line=next(config)
##TargetName=(line.split(':')[1]).strip() #Name of Target
##line=next(config)
##line=next(config)
##SRecName=(line.split(':')[1]).strip() #Name of S-Record file
##line=next(config)
##line=next(config)
##ApplBinName=(line.split(':')[1]).strip() #Name of appl binary file
##line=next(config)
##line=next(config)
##CalBinName=(line.split(':')[1]).strip() #Name of cal binary file
##line=next(config)
##line=next(config)
##CalExpBinName=(line.split(':')[1]).strip() #Name of calib export binary file
##line=next(config)
##line=next(config)
##ScriptsName=(line.split(':')[1]).strip() #Names of Scripts
##line=next(config)
##line=next(config)
##CalRomImpName=(line.split(':')[1]).strip() #Name of calrom import file
##
###Parsing of memory architecture
##ImpMemoryArc=[]
##ExpMemoryArc=[]
##while "MEMORY" not in line:
##    line=next(config)
##while line != "\n":
##    line=next(config)
##    ImpMemoryArc.append(line[:-1])
##
##while "MEMORY" not in line:
##    line=next(config)
##while line != "\n":
##    line=next(config)
##    ExpMemoryArc.append(line[:-1])
##
##ImpMemoryArc=ImpMemoryArc[:-1]
##ExpMemoryArc=ExpMemoryArc[:-1]
### end of Parsing of memory architecture
##
###parsing Commands
##config.seek(0)
##for line in config:
##    if "3)" in line:
##        CalRomOffRegion=line.split(":")[1].strip()
##    if "4)" in line:
##        CalRamRegion=line.split(":")[1].strip()
##    if "8)" in line:
##        CalRomRegion=line.split(":")[1].strip()
##    if "9)" in line:
##        BinRegion=line.split(":")[1].strip()
##    if "10)" in line:
##        CalBinRegion=line.split(":")[1].strip()
### end of Parsing Commands
##
##config.close()
###fine del parsing
##
##msg = "Utilizzare:\n\nPATH \n%s\n\nPATH CALIBRAZIONE \n%s\n\nNOME CALIBRAZIONE \n%s " %(PathRead,CalPathRead,CalRead)
##choices = ["Yes","No","Close"]
##reply = eg.buttonbox(msg,choices=choices)
##if reply=="Close":
##    sys.exit()
##elif reply=="Yes":
##    cal=CalRead
##    Path=PathRead
##    PathCalib=CalPathRead
##
##
##else:
##    msg=""
##    choices = ["Cambia Path","Cambia Path Calibrazione","Cambia Calibrazione","Tutto..."]
##    reply = eg.buttonbox(msg,choices=choices)
##    if reply == "Cambia Path":
##        (Path,PathConf) = OpenPath(title)
##        PathCalib=CalPathRead
##        cal=CalRead
##
##    elif reply == "Cambia Path Calibrazione":
##        (PathCalib,PathCalibConf) = OpenCalibPath(title)
##        (cal,CalibConf) = OpenCalib(title,PathCalib,CalRead,PathCalibConf)
##        Path=PathRead
##
##    elif reply=="Cambia Calibrazione":
##        Path=PathRead
##        PathCalib=CalPathRead
##        PathCalibConf=PathCalib
##        (cal,CalibConf) = OpenCalib(title,PathCalib,CalRead,PathCalibConf)
##
##    else:
##        (Path,PathConf) = OpenPath(title)
##        (PathCalib,PathCalibConf) = OpenCalibPath(title)
##        (cal,CalibConf) = OpenCalib(title,PathCalib,CalRead,PathCalibConf)
##
##
##msg="Selezionare Script"
##choices = ScriptsName.split(",")
##Kit = eg.buttonbox(msg,choices=choices)
##
##SequenzaComandi=[]
##SequenceFile=open("CreaStrategyConfig.txt","r")
##for riga in SequenceFile:
##    if "INSERT" in riga:
##        riga=next(SequenceFile)
##        SequenzaComandi=riga.split(",")
##if "\n" in SequenzaComandi[-1]:
##    SequenzaComandi[-1]=SequenzaComandi[-1][0]
##
##SequenceFile.close()
##
##eg.msgbox("Passa alla finastra ATI vision entro 5 secondi",title)
##time.sleep(5)
##shell = win32com.client.Dispatch("WScript.Shell")
##timesleep=0.4
##
##NewStrategy()
##
##for command in SequenzaComandi:
##    if command=="1":
##        LoadA2L(Path)
##    elif command=="2":
##        ImportSrecord(Path)
##    elif command=="3":
##        ExpCalibRomOffline(Path)
##    elif command=="4":
##        ImpCalRam(Path)
##    elif command=="5":
##        ImportCalib(PathCalib, cal)
##    elif command=="6":
##        ExpCalRam(Path)
##    elif command=="7":
##        ExecuteScript(Path, Kit)
##    elif command=="8":
##        ImpCalRom(Path)
##    elif command=="9":
##        ImportBin(Path)
##    elif command=="10":
##        ImportCalBin(Path)
##CloseStrategy()
##
##
##
##
##config=open("CreaStrategyConfig.txt","r")
##config_temp=open("CreaStrategyConfig.txt.tmp","w")
##for riga in config:
##    if "Default Path:" in riga:
##        if PathConf!="":
##            config_temp.writeline("Default Path: " + PathConf + "\n")
##        else:
##            config_temp.write("Default Path: " + PathRead + "\n")
##    elif "Default Calibration Path" in riga:
##        if PathCalibConf!="":
##            config_temp.write("Default Calibration Path: " + PathCalibConf + "\n")
##        else:
##            config_temp.write("Default Calibration Path: " + CalPathRead + "\n")
##    elif "Default Calibration" in riga:
##        if CalibConf!="":
##            config_temp.write("Default Calibration: " + CalibConf + "\n")
##        else:
##            config_temp.write("Default Calibration: " + CalRead + "\n")
##
##    else:
##        config_temp.write(riga)

config.close()
##config_temp.close()
##actual_path=os.path.abspath(__file__)
##actual_path=actual_path[:-15]
##os.remove(actual_path+"CreaStrategyConfig.txt")
##os.rename(actual_path+"CreaStrategyConfig.txt.tmp",actual_path+"CreaStrategyConfig.txt")