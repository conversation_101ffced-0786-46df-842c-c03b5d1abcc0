/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      PwrMgm.h
 **  Date:          10-Dec-2021
 **
 **  Model Version: 1.1164
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_PwrMgm_h_
#define RTW_HEADER_PwrMgm_h_
#ifndef PwrMgm_COMMON_INCLUDES_
# define PwrMgm_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* PwrMgm_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

 
/* Enumerated types definition */
typedef uint8_T enum_StIgn;
#define IGN_OFF                        ((enum_StIgn)0U)          /* Default value */
#define IGN_ON                         ((enum_StIgn)1U)
#define IGN_WAIT                       ((enum_StIgn)2U)
#define IGN_ERROR                      ((enum_StIgn)3U)
typedef uint8_T enum_StEcu;
#define ECU_PWR_ON                     ((enum_StEcu)0U)          /* Default value */
#define ECU_FAST_OFF                   ((enum_StEcu)1U)
#define ECU_ON                         ((enum_StEcu)2U)
#define ECU_SERVICE_OFF                ((enum_StEcu)3U)
#define ECU_PWR_LATCH                  ((enum_StEcu)4U)
#define ECU_SHUT_DOWN                  ((enum_StEcu)5U)
#define ECU_WAIT_ON                    ((enum_StEcu)6U)
typedef uint8_T enum_StFunc;
#define FUNC_INIT                      ((enum_StFunc)0U)         /* Default value */
#define FUNC_EON                       ((enum_StFunc)1U)
#define FUNC_ENG_OFF                   ((enum_StFunc)2U)
#define FUNC_ENG_RUN                   ((enum_StFunc)3U)
#define FUNC_SERVICE_OFF               ((enum_StFunc)4U)
 
/* Model entry point functions */
extern void PwrMgm_initialize(void);

/* Exported entry point function */
extern void PwrMgm_10ms(void);

/* Exported entry point function */
extern void PwrMgm_PowerOn(void);

/* Exported entry point function */
extern void PwrMgm_Read(void);

/* Exported entry point function */
extern void PwrMgm_Reset(void);

/* Exported entry point function */
extern void PwrMgm_Sleep(void);

/* Exported entry point function */
extern void PwrMgm_Tdc(void);

/* Exported entry point function */
extern void PwrMgm_Write(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgTrigStall;           /* '<S7>/MFlgTrigStall' */

/* Stall weak flag */
extern uint8_T ReqSBCMode;             /* '<S7>/MReqSBCMode' */

/* SBC status feedback */
extern uint32_T SelWinWdt;             /* '<S7>/MSelWinWdt' */

/* Select WDT strategy mode */
extern enum_StEcu StEcu;               /* '<S7>/MStEcu' */

/* ECU state */
extern enum_StFunc StFunc;             /* '<S7>/MStFunc' */

/* Functionality state */
extern enum_StIgn StIgn;               /* '<S7>/MStIgn' */

/* Ignition state */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S24>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Data Type Conversion9' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'PwrMgm'
 * '<S1>'   : 'PwrMgm/Init_fcn'
 * '<S2>'   : 'PwrMgm/PwrMgm_Scheduler'
 * '<S3>'   : 'PwrMgm/SetEcuRead'
 * '<S4>'   : 'PwrMgm/SetEcuReset'
 * '<S5>'   : 'PwrMgm/SetEcuSleep'
 * '<S6>'   : 'PwrMgm/SetEcuWrite'
 * '<S7>'   : 'PwrMgm/Subsystem'
 * '<S8>'   : 'PwrMgm/T10ms_fcn'
 * '<S9>'   : 'PwrMgm/Tdc'
 * '<S10>'  : 'PwrMgm/T10ms_fcn/Calc_Stall'
 * '<S11>'  : 'PwrMgm/T10ms_fcn/Diag_Mgm'
 * '<S12>'  : 'PwrMgm/T10ms_fcn/PowerManagement_Sts'
 * '<S13>'  : 'PwrMgm/T10ms_fcn/Calc_Stall/Select_Cyl_Stall'
 * '<S14>'  : 'PwrMgm/T10ms_fcn/Calc_Stall/flgRpmOn_Calc'
 * '<S15>'  : 'PwrMgm/T10ms_fcn/Calc_Stall/flgVehSpeedOn_Calc'
 * '<S16>'  : 'PwrMgm/T10ms_fcn/Calc_Stall/Select_Cyl_Stall/Stall_flag'
 * '<S17>'  : 'PwrMgm/T10ms_fcn/Calc_Stall/Select_Cyl_Stall/ThrRpm_Calculation'
 * '<S18>'  : 'PwrMgm/T10ms_fcn/Calc_Stall/flgRpmOn_Calc/flgRpmOn_Calculation'
 * '<S19>'  : 'PwrMgm/T10ms_fcn/Calc_Stall/flgVehSpeedOn_Calc/flgVehSpeedOn_Calculation'
 * '<S20>'  : 'PwrMgm/T10ms_fcn/Diag_Mgm/fcn_EEMGMEETaskCmd'
 * '<S21>'  : 'PwrMgm/T10ms_fcn/Diag_Mgm/fcn_EEMGMSetEvID'
 * '<S22>'  : 'PwrMgm/T10ms_fcn/Diag_Mgm/fcn_EON'
 * '<S23>'  : 'PwrMgm/T10ms_fcn/Diag_Mgm/fcn_IgnDiag'
 * '<S24>'  : 'PwrMgm/T10ms_fcn/Diag_Mgm/fcn_IgnDiag/SetDiagState'
 * '<S25>'  : 'PwrMgm/T10ms_fcn/PowerManagement_Sts/ECU_Mgm'
 * '<S26>'  : 'PwrMgm/T10ms_fcn/PowerManagement_Sts/Ign_Mgm'
 * '<S27>'  : 'PwrMgm/T10ms_fcn/PowerManagement_Sts/Reset_Diff_To_Previous'
 * '<S28>'  : 'PwrMgm/T10ms_fcn/PowerManagement_Sts/SBC_Mgm'
 * '<S29>'  : 'PwrMgm/T10ms_fcn/PowerManagement_Sts/Sleep_Diff_To_Previous'
 * '<S30>'  : 'PwrMgm/T10ms_fcn/PowerManagement_Sts/Write_Diff_To_Previous'
 */

/*-
 * Requirements for '<Root>': PwrMgm
 */
#endif                                 /* RTW_HEADER_PwrMgm_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/