/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DMA
**  Filename        :  dma_events.c
**  Created on      :  09-Nov-2020 16:45:00
**  Original author :  MocciA
******************************************************************************/

#ifdef _BUILD_DMA_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Dma.h"
#include "task.h"
#include "gtm.h"
#include "gtm_eisb_out.h"
#include "Adc_out.h"
#include "Digio_out.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
uint8_T Debug_DmaCh0;
uint8_T Debug_DmaCh1;
uint8_T Debug_DmaCh16;
uint8_T Debug_DmaCh17;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : DMA_CH0_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[0].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
#pragma ghs section text=".vletext_c2"
void DMA_CH0_ISR (void) { //IPRI_B0
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(EDMA_IRQRL_INT00_ISR_POS); // Interrupt no. 53

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (DMA_0.INTL.B.INT0 != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    ipri_b0_saradcconf_ch4_ipri_b0_dma();

#ifdef _TEST_DMA_ISR_PIN
    Debug_DmaCh0 = Dio_ReadChannel(OP_PRI0_DMA_CH0);
    if ( Debug_DmaCh0 == SET_BIT_LOW  )
    {
        Dio_WriteChannel(OP_PRI0_DMA_CH0, SET_BIT_HIGH);    
    }
    else
    {
        Dio_WriteChannel(OP_PRI0_DMA_CH0, SET_BIT_LOW); 
    }
#endif

    DMA_0.CINT.R = 0U;
}

/******************************************************************************
**   Function    : DMA_CH16_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[16].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH16_ISR (void) { //IPRI_B1
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(EDMA_IRQRL_INT16_ISR_POS); // Interrupt no. 69

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (DMA_0.INTL.B.INT16 != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    ipri_b1_saradcconf_ch16_ipri_b1_dma();

#ifdef _TEST_DMA_ISR_PIN
    Debug_DmaCh16 = Dio_ReadChannel(OP_PRI1_DMA_CH16);
    if ( Debug_DmaCh16 == SET_BIT_LOW  )
    {
        Dio_WriteChannel(OP_PRI1_DMA_CH16, SET_BIT_HIGH);    
    }
    else
    {
        Dio_WriteChannel(OP_PRI1_DMA_CH16, SET_BIT_LOW); 
    }
#endif

    DMA_0.CINT.R = 16U;
}

/******************************************************************************
**   Function    : DMA_CH1_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[1].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH1_ISR (void) { //ISEC_B0
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(EDMA_IRQRL_INT01_ISR_POS); // Interrupt no. 54

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (DMA_0.INTL.B.INT1 != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    isec_b0_saradcconf_ch5_isec_b0_dma();

#ifdef _TEST_DMA_ISR_PIN
    Debug_DmaCh1 = Dio_ReadChannel(OP_SEC0_DMA_CH1);
    if ( Debug_DmaCh1 == SET_BIT_LOW  )
    {
        Dio_WriteChannel(OP_SEC0_DMA_CH1, SET_BIT_HIGH);    
    }
    else
    {
        Dio_WriteChannel(OP_SEC0_DMA_CH1, SET_BIT_LOW); 
    }
#endif

    DMA_0.CINT.R = 1U;
}

/******************************************************************************
**   Function    : DMA_CH17_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[17].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH17_ISR (void) { //ISEC_B1
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(EDMA_IRQRL_INT17_ISR_POS); // Interrupt no. 70

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (DMA_0.INTL.B.INT17 != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    isec_b1_saradcconf_ch17_isec_b1_dma();

#ifdef _TEST_DMA_ISR_PIN
    Debug_DmaCh17 = Dio_ReadChannel(OP_SEC1_DMA_CH17);
    if ( Debug_DmaCh17 == SET_BIT_LOW  )
    {
        Dio_WriteChannel(OP_SEC1_DMA_CH17, SET_BIT_HIGH);    
    }
    else
    {
        Dio_WriteChannel(OP_SEC1_DMA_CH17, SET_BIT_LOW); 
    }
#endif

    DMA_0.CINT.R = 17U;
}
#pragma ghs section text=default

/******************************************************************************
**   Function    : DMA_CH2_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[2].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH2_ISR (void) { //ION2_6
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(EDMA_IRQRL_INT02_ISR_POS); // Interrupt no. 55

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (DMA_0.INTL.B.INT2 != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    ion2_6_saradcconf_ch45_dma_ion2_6();
    DMA_0.CINT.R = 2U;
}

/******************************************************************************
**   Function    : DMA_CH3_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[3].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH3_ISR (void) { //ION0_4
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(EDMA_IRQRL_INT03_ISR_POS); // Interrupt no. 55

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (DMA_0.INTL.B.INT3 != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    ion0_4_saradcconf_ch44_dma_ion0_4();
    DMA_0.CINT.R = 3U;
}

/******************************************************************************
**   Function    : DMA_CH11_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[16].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH11_ISR (void) { 
    DMA_0.CINT.R = 11U;
}

/******************************************************************************
**   Function    : DMA_CH12_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[16].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH12_ISR (void) { 
    DMA_0.CINT.R = 12U;
}

/******************************************************************************
**   Function    : DMA_CH24_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[24].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH24_ISR (void) { //ION1_5
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(EDMA_IRQRL_INT24_ISR_POS); // Interrupt no. 77

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (DMA_0.INTL.B.INT24 != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    ion1_5_saradcconf_ch48_dma_ion1_5();
    DMA_0.CINT.R = 24U;
}

/******************************************************************************
**   Function    : DMA_CH25_ISR
**
**   Description:
**    ISR handler for interrupt on DMA_CH[25].
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void DMA_CH25_ISR (void) { //ION3_7
#ifdef _BUILD_SAFETYMNGR_INTC_
    /* Run ISR_CHECK_PRIORITY and ISR_CHECK_PRIORITY mechanisms */
    SafetyMngr_INTC_CheckCtx(EDMA_IRQRL_INT25_ISR_POS); // Interrupt no. 78

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (DMA_0.INTL.B.INT25 != 1u)
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_
#endif // _BUILD_SAFETYMNGR_INTC_

    ion3_7_saradcconf_ch52_dma_ion3_7();
    DMA_0.CINT.R = 25U;
}

#endif /* _BUILD_DMA_ */

