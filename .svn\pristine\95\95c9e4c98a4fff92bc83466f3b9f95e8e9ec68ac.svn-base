/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TASK
**  Filename        :  TasksDefs.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef _OSEK_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "sys.h"
#include "os_api.h"
#include "TasksDefs.h"
#include "task.h"

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* Task Configuration table         */
const TaskCfg   OsTaskCfgTable[OSNUMTSKS]=
{
    {(TASKENTRY)FuncTask5ms,           (TaskType)Task5msID,                        S_IRQ4},
    {(TASKENTRY)FuncTask10ms,          (TaskType)Task10msID,                       S_IRQ3},
    {(TASKENTRY)FuncTask100ms,         (TaskType)Task100msID,                      S_IRQ2},
#if(CAN_CHA_BUF0_RX_EXC || \
    CAN_CHA_BUF1_RX_EXC || \
    CAN_CHA_BUF2_RX_EXC || \
    CAN_CHA_BUF3_RX_EXC || \
    CAN_CHA_BUF4_RX_EXC || \
    CAN_CHA_BUF5_RX_EXC || \
    CAN_CHA_BUF6_RX_EXC || \
    CAN_CHA_BUF7_RX_EXC || \
    CAN_CHA_BUF8_RX_EXC || \
    CAN_CHA_BUF9_RX_EXC || \
    CAN_CHA_BUF10_RX_EXC || \
    CAN_CHA_BUF11_RX_EXC || \
    CAN_CHA_BUF12_RX_EXC || \
    CAN_CHA_BUF13_RX_EXC || \
    CAN_CHA_BUF14_RX_EXC || \
    CAN_CHA_BUF15_RX_EXC || \
    CAN_CHA_BUF16_RX_EXC || \
    CAN_CHA_BUF17_RX_EXC || \
    CAN_CHA_BUF18_RX_EXC || \
    CAN_CHA_BUF19_RX_EXC || \
    CAN_CHA_BUF20_RX_EXC || \
    CAN_CHA_BUF21_RX_EXC || \
    CAN_CHA_BUF22_RX_EXC || \
    CAN_CHA_BUF23_RX_EXC || \
    CAN_CHA_BUF24_RX_EXC || \
    CAN_CHA_BUF25_RX_EXC || \
    CAN_CHA_BUF26_RX_EXC || \
    CAN_CHA_BUF27_RX_EXC || \
    CAN_CHA_BUF28_RX_EXC || \
    CAN_CHA_BUF29_RX_EXC || \
    CAN_CHA_BUF30_RX_EXC || \
    CAN_CHA_BUF31_RX_EXC || \
    CAN_CHA_BUF32_RX_EXC || \
    CAN_CHA_BUF33_RX_EXC || \
    CAN_CHA_BUF34_RX_EXC || \
    CAN_CHA_BUF35_RX_EXC || \
    CAN_CHA_BUF36_RX_EXC || \
    CAN_CHA_BUF37_RX_EXC || \
    CAN_CHA_BUF38_RX_EXC || \
    CAN_CHA_BUF39_RX_EXC || \
    CAN_CHA_BUF40_RX_EXC || \
    CAN_CHA_BUF41_RX_EXC || \
    CAN_CHA_BUF42_RX_EXC || \
    CAN_CHA_BUF43_RX_EXC || \
    CAN_CHA_BUF43_RX_EXC || \
    CAN_CHA_BUF44_RX_EXC || \
    CAN_CHA_BUF45_RX_EXC || \
    CAN_CHA_BUF46_RX_EXC || \
    CAN_CHA_BUF47_RX_EXC || \
    CAN_CHA_BUF48_RX_EXC || \
    CAN_CHA_BUF49_RX_EXC || \
    CAN_CHA_BUF50_RX_EXC || \
    CAN_CHA_BUF51_RX_EXC || \
    CAN_CHA_BUF52_RX_EXC || \
    CAN_CHA_BUF53_RX_EXC || \
    CAN_CHA_BUF54_RX_EXC || \
    CAN_CHA_BUF55_RX_EXC || \
    CAN_CHA_BUF56_RX_EXC || \
    CAN_CHA_BUF57_RX_EXC || \
    CAN_CHA_BUF58_RX_EXC || \
    CAN_CHA_BUF59_RX_EXC || \
    CAN_CHA_BUF60_RX_EXC || \
    CAN_CHA_BUF61_RX_EXC || \
    CAN_CHA_BUF62_RX_EXC || \
    CAN_CHA_BUF63_RX_EXC )
    {(TASKENTRY)FuncCAN_ExRxDoneChA,         (TaskType)CAN_ExRxDoneChAID,            S_IRQ4},
    {(TASKENTRY)FuncCAN_ExTxDoneChA,         (TaskType)CAN_ExTxDoneChAID,            S_IRQ4},
#endif
#if(CAN_CHB_BUF0_RX_EXC || \
    CAN_CHB_BUF1_RX_EXC || \
    CAN_CHB_BUF2_RX_EXC || \
    CAN_CHB_BUF3_RX_EXC || \
    CAN_CHB_BUF4_RX_EXC || \
    CAN_CHB_BUF5_RX_EXC || \
    CAN_CHB_BUF6_RX_EXC || \
    CAN_CHB_BUF7_RX_EXC || \
    CAN_CHB_BUF8_RX_EXC || \
    CAN_CHB_BUF9_RX_EXC || \
    CAN_CHB_BUF10_RX_EXC || \
    CAN_CHB_BUF11_RX_EXC || \
    CAN_CHB_BUF12_RX_EXC || \
    CAN_CHB_BUF13_RX_EXC || \
    CAN_CHB_BUF14_RX_EXC || \
    CAN_CHB_BUF15_RX_EXC || \
    CAN_CHB_BUF16_RX_EXC || \
    CAN_CHB_BUF17_RX_EXC || \
    CAN_CHB_BUF18_RX_EXC || \
    CAN_CHB_BUF19_RX_EXC || \
    CAN_CHB_BUF20_RX_EXC || \
    CAN_CHB_BUF21_RX_EXC || \
    CAN_CHB_BUF22_RX_EXC || \
    CAN_CHB_BUF23_RX_EXC || \
    CAN_CHB_BUF24_RX_EXC || \
    CAN_CHB_BUF25_RX_EXC || \
    CAN_CHB_BUF26_RX_EXC || \
    CAN_CHB_BUF27_RX_EXC || \
    CAN_CHB_BUF28_RX_EXC || \
    CAN_CHB_BUF29_RX_EXC || \
    CAN_CHB_BUF30_RX_EXC || \
    CAN_CHB_BUF31_RX_EXC || \
    CAN_CHB_BUF32_RX_EXC || \
    CAN_CHB_BUF33_RX_EXC || \
    CAN_CHB_BUF34_RX_EXC || \
    CAN_CHB_BUF35_RX_EXC || \
    CAN_CHB_BUF36_RX_EXC || \
    CAN_CHB_BUF37_RX_EXC || \
    CAN_CHB_BUF38_RX_EXC || \
    CAN_CHB_BUF39_RX_EXC || \
    CAN_CHB_BUF40_RX_EXC || \
    CAN_CHB_BUF41_RX_EXC || \
    CAN_CHB_BUF42_RX_EXC || \
    CAN_CHB_BUF43_RX_EXC || \
    CAN_CHB_BUF43_RX_EXC || \
    CAN_CHB_BUF44_RX_EXC || \
    CAN_CHB_BUF45_RX_EXC || \
    CAN_CHB_BUF46_RX_EXC || \
    CAN_CHB_BUF47_RX_EXC || \
    CAN_CHB_BUF48_RX_EXC || \
    CAN_CHB_BUF49_RX_EXC || \
    CAN_CHB_BUF50_RX_EXC || \
    CAN_CHB_BUF51_RX_EXC || \
    CAN_CHB_BUF52_RX_EXC || \
    CAN_CHB_BUF53_RX_EXC || \
    CAN_CHB_BUF54_RX_EXC || \
    CAN_CHB_BUF55_RX_EXC || \
    CAN_CHB_BUF56_RX_EXC || \
    CAN_CHB_BUF57_RX_EXC || \
    CAN_CHB_BUF58_RX_EXC || \
    CAN_CHB_BUF59_RX_EXC || \
    CAN_CHB_BUF60_RX_EXC || \
    CAN_CHB_BUF61_RX_EXC || \
    CAN_CHB_BUF62_RX_EXC || \
    CAN_CHB_BUF63_RX_EXC )
    {(TASKENTRY)FuncCAN_ExRxDoneChB,         (TaskType)CAN_ExRxDoneChBID,            S_IRQ4},
    {(TASKENTRY)FuncCAN_ExTxDoneChB,         (TaskType)CAN_ExTxDoneChBID,            S_IRQ4},
#endif
#if(CAN_CHC_BUF0_RX_EXC || \
    CAN_CHC_BUF1_RX_EXC || \
    CAN_CHC_BUF2_RX_EXC || \
    CAN_CHC_BUF3_RX_EXC || \
    CAN_CHC_BUF4_RX_EXC || \
    CAN_CHC_BUF5_RX_EXC || \
    CAN_CHC_BUF6_RX_EXC || \
    CAN_CHC_BUF7_RX_EXC || \
    CAN_CHC_BUF8_RX_EXC || \
    CAN_CHC_BUF9_RX_EXC || \
    CAN_CHC_BUF10_RX_EXC || \
    CAN_CHC_BUF11_RX_EXC || \
    CAN_CHC_BUF12_RX_EXC || \
    CAN_CHC_BUF13_RX_EXC || \
    CAN_CHC_BUF14_RX_EXC || \
    CAN_CHC_BUF15_RX_EXC || \
    CAN_CHC_BUF16_RX_EXC || \
    CAN_CHC_BUF17_RX_EXC || \
    CAN_CHC_BUF18_RX_EXC || \
    CAN_CHC_BUF19_RX_EXC || \
    CAN_CHC_BUF20_RX_EXC || \
    CAN_CHC_BUF21_RX_EXC || \
    CAN_CHC_BUF22_RX_EXC || \
    CAN_CHC_BUF23_RX_EXC || \
    CAN_CHC_BUF24_RX_EXC || \
    CAN_CHC_BUF25_RX_EXC || \
    CAN_CHC_BUF26_RX_EXC || \
    CAN_CHC_BUF27_RX_EXC || \
    CAN_CHC_BUF28_RX_EXC || \
    CAN_CHC_BUF29_RX_EXC || \
    CAN_CHC_BUF30_RX_EXC || \
    CAN_CHC_BUF31_RX_EXC )
    {(TASKENTRY)FuncCAN_ExRxDoneChC,         (TaskType)CAN_ExRxDoneChCID,            S_IRQ4},
    {(TASKENTRY)FuncCAN_ExTxDoneChC,         (TaskType)CAN_ExTxDoneChCID,            S_IRQ4},
#endif
    {(TASKENTRY)FuncTaskKeyOff,              (TaskType)TaskKeyOffID,                 S_IRQ7},
#if defined (_BUILD_SPI_) && defined (_BUILD_SPIMGM_)
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS0,    (TaskType)SPI_ExTxDoneChB_PCS0ID,       S_IRQ3},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS1,    (TaskType)SPI_ExTxDoneChB_PCS1ID,       S_IRQ0},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS2,    (TaskType)SPI_ExTxDoneChB_PCS2ID,       S_IRQ0},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS3,    (TaskType)SPI_ExTxDoneChB_PCS3ID,       S_IRQ0},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS4,    (TaskType)SPI_ExTxDoneChB_PCS4ID,       S_IRQ0},
    {(TASKENTRY)FuncSPI_ExTxDoneChB_PCS5,    (TaskType)SPI_ExTxDoneChB_PCS5ID,       S_IRQ0},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS0,    (TaskType)SPI_ExTxDoneChC_PCS0ID,       S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS1,    (TaskType)SPI_ExTxDoneChC_PCS1ID,       S_IRQ5},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS2,    (TaskType)SPI_ExTxDoneChC_PCS2ID,       S_IRQ0},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS3,    (TaskType)SPI_ExTxDoneChC_PCS3ID,       S_IRQ0},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS4,    (TaskType)SPI_ExTxDoneChC_PCS4ID,       S_IRQ0},
    {(TASKENTRY)FuncSPI_ExTxDoneChC_PCS5,    (TaskType)SPI_ExTxDoneChC_PCS5ID,       S_IRQ0},
#endif
    {(TASKENTRY)FuncBackgroundTask,          (TaskType)BackgroundTaskID,             S_IRQ1},
#ifdef _BUILD_DIAGCANMGM_
    {(TASKENTRY)FuncTaskWaitForReset,        (TaskType)TaskWaitForResetID,           S_IRQ1},
#endif
    {(TASKENTRY)FuncTaskSync,                (TaskType)TaskSyncID,                   S_IRQ2},
    {(TASKENTRY)FuncTaskNoSync,              (TaskType)TaskNoSyncID,                 S_IRQ2},
    {(TASKENTRY)FuncTaskAngle,               (TaskType)TaskAngleID,                  S_IRQ6},

    {(TASKENTRY)FuncTaskTDC,                 (TaskType)TaskTDCID,                    S_IRQ5},
    {(TASKENTRY)FuncTaskHTDC,                (TaskType)TaskHTDCID,                   S_IRQ5},
    {(TASKENTRY)FuncTaskPreTDC,              (TaskType)TaskPreTDCID,                 S_IRQ6},
    {(TASKENTRY)FuncTaskPreHTDC,             (TaskType)TaskPreHTDCID,                S_IRQ6},

    {(TASKENTRY)FuncTaskINJ_PRG,             (TaskType)TaskINJ_PRGID,                S_IRQ5},
#ifdef _BUILD_IGN_
    {(TASKENTRY)FuncTaskSparkOn_0,           (TaskType)SparkTOnID0,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOff_0,          (TaskType)SparkAOffID0,                 S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOn_1,           (TaskType)SparkTOnID1,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOff_1,          (TaskType)SparkAOffID1,                 S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOn_2,           (TaskType)SparkTOnID2,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOff_2,          (TaskType)SparkAOffID2,                 S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOn_3,           (TaskType)SparkTOnID3,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOff_3,          (TaskType)SparkAOffID3,                 S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOn_4,           (TaskType)SparkTOnID4,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOff_4,          (TaskType)SparkAOffID4,                 S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOn_5,           (TaskType)SparkTOnID5,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOff_5,          (TaskType)SparkAOffID5,                 S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOn_6,           (TaskType)SparkTOnID6,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOff_6,          (TaskType)SparkAOffID6,                 S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOn_7,           (TaskType)SparkTOnID7,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkOff_7,          (TaskType)SparkAOffID7,                 S_IRQ7},
#if 0
    {(TASKENTRY)FuncTaskSparkMon_1,          (TaskType)SparkMonID1,                  S_IRQ7},
    {(TASKENTRY)FuncTaskSparkMon_2,          (TaskType)SparkMonID2,                  S_IRQ7},
#endif
    {(TASKENTRY)FuncTaskEnBuck_1,          (TaskType)SparkEnBuckID0,                  S_IRQ7},
    {(TASKENTRY)FuncTaskEnBuck_2,          (TaskType)SparkEnBuckID1,                  S_IRQ7},
#endif
#ifdef _BUILD_IONACQ_
    {(TASKENTRY)FuncTaskEOA_A,             (TaskType)TaskEOAID_A,                    S_IRQ5},
    {(TASKENTRY)FuncTaskEOA_B,             (TaskType)TaskEOAID_B,                    S_IRQ5},
    {(TASKENTRY)FuncTaskEOA_C,             (TaskType)TaskEOAID_C,                    S_IRQ5},
    {(TASKENTRY)FuncTaskEOA_D,             (TaskType)TaskEOAID_D,                    S_IRQ5},
#endif
#ifdef _BUILD_EXT_WDT_
    {(TASKENTRY)FuncTask_RestoreExtWDTServiceGPIO, (TaskType)WDT_PwmOff_ID,          S_IRQ5},
#endif
#ifdef _BUILD_ACTIVE_DIAG_
    {(TASKENTRY)FuncTaskIgnStop,            (TaskType)TaskIgnStop_ID,               S_IRQ7},
#endif
    {(TASKENTRY)FuncTask1ms,                (TaskType)Task1msID,                    S_IRQ7},
    {(TASKENTRY)FuncTaskSparkEv,            (TaskType)TaskSparkEvID,                S_IRQ5},
};

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : GetApplicationTaskTable
**
**   Description:
**    This function returns the application task table.
**
**   Parameters :
**    [out] void **taskTable    : task table
**    [out] uint32_T *tableSize : table size
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#pragma ghs startnomisra
void GetApplicationTaskTable (void **taskTable, uint32_T *tableSize) 
{
    *taskTable = (void*)OsTaskCfgTable;
    *tableSize = (uint32_T)MAX_NUM_TASK;
}
#pragma ghs endnomisra

#endif /* _OSEK_ */

/****************************************************************************
 ****************************************************************************/
