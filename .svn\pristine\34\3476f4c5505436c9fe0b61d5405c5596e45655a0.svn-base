/*******************************************************************************************************************************/
/* $HeadURL::                                                                                                              $   */
/* $ Description:                                                                                                              */
/* $Revision::        $                                                                                                        */
/* $Date::                                                $                                                                    */
/* $Author::                         $                                                                                         */
/*******************************************************************************************************************************/

#ifndef STUB_H_
#define STUB_H_

#include "rtwtypes.h"
#include "ETPU_EngineDefs.h"

extern uint8_T  FlgDisDiagCAN;

/* CANMGM OUTPUT */
extern uint8_T  EthPerc;
extern uint8_T  FlgTipIn;

/* DIAGCOIL OUTPUT*/
extern uint8_T ICCReset;

/* Analogin requested by DiagMgm_10ms function */
extern uint8_T FlgDongle;
extern uint16_T CntNoDiagDongle;
extern uint8_T DisDiagKWP1;

/* Environmental parameters requested to be saved in EE2 */
extern uint8_T WarningLampSwitchOffCycles_stub; // to be implemented from scratch

/* CPUMGM */
extern uint8_T ResetType;

/* ENG FLAG */
extern uint8_T FlgPwrLRetainA;
extern uint8_T FlgPwrLRetainB;
extern uint8_T FlgPwrLRetainC;

/* SYNCMGM */
extern uint32_T NTeethDeleted;
extern uint8_T LastSyncError;

/* ANALOGIN */
extern int16_T VBGCodeLow0;
extern uint16_T VBandGap;
extern int16_T TLow0;
extern int16_T THigh0;
extern int16_T TSensCodeLow0;
extern int16_T TSensCodeHigh0;

extern int16_T FlgNoSpark[N_CYL_MAX];
extern uint16_T VBuck[8][5];

#ifndef _BUILD_SPARKPLUGTEST_
extern uint16_T SparkPlugFaultCntEE[8];
#endif

// Core Z2
extern uint8_T Core2IvorDetected;

void Stub_Init(void);
void Stub_T10ms(void);

#endif

