/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/*
 * File: TempECUMgm_eep.c
 *
 * Code generated for Simulink model 'TempECUMgm'.
 *
 * Model version                  : 1.1052
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Oct 15 14:10:51 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor->Custom Processor
 * Emulation hardware selection:
 *    Differs from embedded hardware (Intel->x86-64 (Windows64))
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. ROM efficiency
 *    5. RAM efficiency
 *    6. Traceability
 *    7. Debugging
 *    8. Polyspace
 * Validation result: Passed (30), Warnings (2), Error (0)
 */
#include "rtwtypes.h"

/* Exported data definition */

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_EE_INTERFACE */
uint16_T EEHTempECU = ((uint16_T)0U);  /* '<S2>/MEEHTempECU' */

/* Counter of Temp Hazard */
int16_T EETempECUMax1 = 0;             /* '<S2>/MEETempECUMax1' */

/* Temperature ECU 1 Max */
int16_T EETempECUMax2 = 0;             /* '<S2>/MEETempECUMax2' */

/* Temperature ECU 1 Max */
int16_T EETempECUMax3 = 0;             /* '<S2>/MEETempECUMax3' */

/* Temperature ECU 1 Max */
uint32_T EETimHTempECU = 0U;           /* '<S2>/MEETimHTempECU' */

/* Time of Temp Hazard */
uint32_T EETimWTempECU = 0U;           /* '<S2>/MEETimWTempECU' */

/* Time of Temp Warning */
uint16_T EEWTempECU = ((uint16_T)0U);  /* '<S2>/MEEWTempECU' */

/* Counter of Temp Warning */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */