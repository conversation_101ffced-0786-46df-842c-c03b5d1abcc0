/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Spec/Application/KnockCorrTot/trunk/KnockCorrTot_codegen/K#$  */
/* $Revision:: 209711                                                                                         $  */
/* $Date:: 2022-02-24 12:13:35 +0100 (gio, 24 feb 2022)                                                       $  */
/* $Author:: MarottaR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      KnockCorrTot.h
 **  Date:          24-Feb-2022
 **
 **  Model Version: 1.1015
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_KnockCorrTot_h_
#define RTW_HEADER_KnockCorrTot_h_
#ifndef KnockCorrTot_COMMON_INCLUDES_
# define KnockCorrTot_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* KnockCorrTot_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void KnockCorrTot_initialize(void);

/* Exported entry point function */
extern void KnockCorrTot_10ms(void);

/* Exported entry point function */
extern void KnockCorrTot_EOA(void);

/* Exported entry point function */
extern void KnockCorrTot_NoSync(void);

/* Exported entry point function */
extern void KnockCorrTot_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T FlgSAKIndInc[8];        /* '<S7>/Merge4' */

/* The individual knock corr on SA is positive (=1) */
extern uint8_T FlgSAKnockInc[8];       /* '<S7>/Merge' */

/* SAKnock is greater than 0 (=1) */
extern int16_T SAKCorrInd[8];          /* '<S7>/Merge5' */

/* Individual cylinder knock correction */
extern int16_T SAKnock[8];             /* '<S7>/Merge2' */

/* Knock correction on Spark Advance */
extern int16_T SAKnockCyl;             /* '<S7>/Merge1' */

/* Knock correction on Spark Advance */
extern int16_T SAKnockMin;             /* '<S7>/Merge3' */

/* Minimum value of SAKnock */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S22>/Data Type Propagation' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S40>/Data Type Duplicate' : Unused code path elimination
 * Block '<S38>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate' : Unused code path elimination
 * Block '<S43>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S43>/Data Type Propagation' : Unused code path elimination
 * Block '<S44>/Data Type Duplicate' : Unused code path elimination
 * Block '<S44>/Data Type Propagation' : Unused code path elimination
 * Block '<S21>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S22>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S22>/Reshape' : Reshape block reduction
 * Block '<S24>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S24>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S25>/Conversion' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S38>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S40>/Conversion' : Eliminate redundant data type conversion
 * Block '<S41>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S43>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S43>/Reshape' : Reshape block reduction
 * Block '<S44>/Conversion' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S44>/Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'KnockCorrTot'
 * '<S1>'   : 'KnockCorrTot/EOA_fcn'
 * '<S2>'   : 'KnockCorrTot/ForceKnock_fcn'
 * '<S3>'   : 'KnockCorrTot/KnockCorrTot_Scheduler'
 * '<S4>'   : 'KnockCorrTot/Recovery_fcn'
 * '<S5>'   : 'KnockCorrTot/Reset_fcn'
 * '<S6>'   : 'KnockCorrTot/Slew_fcn'
 * '<S7>'   : 'KnockCorrTot/Subsystem'
 * '<S8>'   : 'KnockCorrTot/EOA_fcn/CylSignals_Calculation'
 * '<S9>'   : 'KnockCorrTot/EOA_fcn/SAKCorrIndCyl_Calculation'
 * '<S10>'  : 'KnockCorrTot/EOA_fcn/SAKCorrIndSignals_Calculation'
 * '<S11>'  : 'KnockCorrTot/EOA_fcn/SAKnockCyl_Calculation'
 * '<S12>'  : 'KnockCorrTot/EOA_fcn/SAKnockMax_Calculation'
 * '<S13>'  : 'KnockCorrTot/EOA_fcn/SAKnockMin_Calculation'
 * '<S14>'  : 'KnockCorrTot/EOA_fcn/SAKnockSignals_Calculation'
 * '<S15>'  : 'KnockCorrTot/EOA_fcn/SAKCorrIndCyl_Calculation/SAKCorrIndCyl_Saturation'
 * '<S16>'  : 'KnockCorrTot/EOA_fcn/SAKCorrIndSignals_Calculation/FlgSAKIndInc_Calculation'
 * '<S17>'  : 'KnockCorrTot/EOA_fcn/SAKCorrIndSignals_Calculation/SAKCorrIndMax_Calculation'
 * '<S18>'  : 'KnockCorrTot/EOA_fcn/SAKCorrIndSignals_Calculation/SAKCorrInd_Calculation'
 * '<S19>'  : 'KnockCorrTot/EOA_fcn/SAKnockCyl_Calculation/SAKnockCyl_Saturation'
 * '<S20>'  : 'KnockCorrTot/EOA_fcn/SAKnockMax_Calculation/LookUp 2-D TBKCORRMAX'
 * '<S21>'  : 'KnockCorrTot/EOA_fcn/SAKnockMax_Calculation/LookUp 2-D TBKCORRMAX/ArrangeLSB'
 * '<S22>'  : 'KnockCorrTot/EOA_fcn/SAKnockMax_Calculation/LookUp 2-D TBKCORRMAX/Look2D_IR_S8'
 * '<S23>'  : 'KnockCorrTot/EOA_fcn/SAKnockMin_Calculation/LookUp 1-D VTKCORRMIN'
 * '<S24>'  : 'KnockCorrTot/EOA_fcn/SAKnockMin_Calculation/LookUp 1-D VTKCORRMIN/LookUp_IR_S16'
 * '<S25>'  : 'KnockCorrTot/EOA_fcn/SAKnockMin_Calculation/LookUp 1-D VTKCORRMIN/LookUp_IR_S16/Data Type Conversion Inherited3'
 * '<S26>'  : 'KnockCorrTot/EOA_fcn/SAKnockSignals_Calculation/FlgSAKnockInc_Calculation'
 * '<S27>'  : 'KnockCorrTot/EOA_fcn/SAKnockSignals_Calculation/FlgSAKnockSat_Calculation'
 * '<S28>'  : 'KnockCorrTot/EOA_fcn/SAKnockSignals_Calculation/SAKnock_Calculation'
 * '<S29>'  : 'KnockCorrTot/ForceKnock_fcn/ForceKnock_Mgm'
 * '<S30>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation'
 * '<S31>'  : 'KnockCorrTot/Recovery_fcn/Recovery_Mgm'
 * '<S32>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation'
 * '<S33>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRec_Calculation'
 * '<S34>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/ArrangeLSB'
 * '<S35>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm'
 * '<S36>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRLCyl_Calc'
 * '<S37>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRL_Mgm'
 * '<S38>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/RateLimiter_S16'
 * '<S39>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/KnockRecRLCyl_Calc/FlgDisKnock_Mgm'
 * '<S40>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRecRL_Calculation/Rec_Algorithm/RateLimiter_S16/Data Type Conversion Inherited1'
 * '<S41>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRec_Calculation/ArrangeLSB'
 * '<S42>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRec_Calculation/ArrangeLSB1'
 * '<S43>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRec_Calculation/Look2D_S8_U16_U16'
 * '<S44>'  : 'KnockCorrTot/Recovery_fcn/KnockRec_Calculation/KnockRec_Calculation/LookUp_S8_U16'
 * '<S45>'  : 'KnockCorrTot/Reset_fcn/Reset_Variables'
 * '<S46>'  : 'KnockCorrTot/Reset_fcn/Reset_VectorsKnock'
 * '<S47>'  : 'KnockCorrTot/Slew_fcn/SlewInd_Mgm'
 * '<S48>'  : 'KnockCorrTot/Slew_fcn/SlewTot_Mgm'
 */

/*-
 * Requirements for '<Root>': KnockCorrTot
 */
#endif                                 /* RTW_HEADER_KnockCorrTot_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/