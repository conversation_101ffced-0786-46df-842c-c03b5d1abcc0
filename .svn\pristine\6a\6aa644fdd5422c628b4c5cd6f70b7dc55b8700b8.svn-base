/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "eemgm.h"

#pragma ghs section rodata=".calib"


/*!
 * \defgroup Calibrations Calibrations
 \brief Parameters that can be tuned via CCP
 
 * \sgroup
 */
/*-----------------------------------*
 * CALIBRATIONS
 *-----------------------------------*/
/// Development variable to erase single blocks of EE
CALQUAL CALQUAL_POST uint16_T ERASEEEPROM = 0u;

/*!\egroup*/
/****************************************************************************
 ****************************************************************************/

