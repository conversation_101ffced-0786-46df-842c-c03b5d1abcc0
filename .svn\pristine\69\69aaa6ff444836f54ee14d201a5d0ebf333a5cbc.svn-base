/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_RS_14_TEST_MBCAN/tree/COM#$  */
/* $Revision:: 185836                                                                                         $  */
/* $Date:: 2021-10-08 13:09:50 +0200 (ven, 08 ott 2021)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Crank_Isb
**  Filename        :  Crank_Isb_out.h
**  Created on      :  04-nov-2021 14:15:00
**  Original author :  SantoroR
******************************************************************************/
/*****************************************************************************
**
**                        CanMgmIn Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef CRANK_ISB_OUT_H
#define CRANK_ISB_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint16_T CntTdcCrk;
extern uint32_T TdcAngle;
extern uint16_T RpmCalc;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : CrankPhaseCheck
**
**   Description:
**    CAN receive operations at 5ms.
**    This functions performs:
**     - Decide whether to receive or to initialize depending on the CAN status
**     - Diagnose the CAN Busoff.
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void CrankPhaseCheck(uint32_T ToothMin, uint32_T ToothMax, uint32_T currentAngle);


#endif  /* CANMGMIN_OUT_H */

/****************************************************************************
 ****************************************************************************/

