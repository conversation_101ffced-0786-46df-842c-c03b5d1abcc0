/****************************************************************************
 ****************************************************************************
 *
 *                              WDT_wrapper_out.h
 *
 * Author(s): Lana L.
 * 
 * 
 * Description:
 * 
 *
 * Usage notes:
 * 
 *
 ****************************************************************************
 ****************************************************************************/

#ifndef WDT_wrapper_out_h_
#define WDT_wrapper_out_h_
#include "rtwtypes.h"

/* DSPI Status Register running state mask */
#define DSPI5_SR_TXRXON_MASK  0x40000000u
#define DSPI_STATUS_TOUT 400u //-> 5 [ms] (was 60000u //-> 4.5 [ms] )

/* WDT PENDING */
#define WDT_PENDING_NONE                   0U
#define WDT_PENDING_RESET                  1U
#define WDT_PENDING_CCP_CLEAR_MEMORY_BOOT  2U
#define WDT_PENDING_CCP_CLEAR_MEMORY_CALIB 3U
#define WDT_PENDING_CCP_CLEAR_MEMORY_APPL  4U
#define WDT_PENDING_DIAG_WRITE_DATA        5U
#define WDT_PENDING_DIAG_CLEAR_DIAGINFO    6U
#define WDT_PENDING_DIAG_START_ROUTINE     7U
#define WDT_PENDING_DIAG_RESET_PARAMS      8U
#define WDT_PENDING_WRITE_RESTORED_DATA    9U
#define WDT_PENDING_WRITE_DATA_BKGD        10U
#define WDT_PENDING_DIAG_CLEAR_BYCAL       11U
#define WDT_SECURE_JUMP2BOOT               12U
#define WDT_SECURE_RESET                   13U

extern uint8_T DtcErasedByCal;

void WDT_ConfigRec(void);
void WDT_GetTimeMs(uint32_T* rty_WdtSwitchTime);
void WDT_ExtSyncISR(void);
void WDT_ExtSyncISRReset (void);
void WDT_SetPendingOperation(uint8_T operation);
void WDT_PendingOperationsCheck(void);
void ClearDtcErasedByCalFlg(void);

#endif

/****************************************************************************
 ****************************************************************************/
 
