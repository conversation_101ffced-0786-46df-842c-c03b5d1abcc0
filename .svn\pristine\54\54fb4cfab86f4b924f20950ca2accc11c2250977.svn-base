/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef __ACTIVE_DIAG_OUT_H__
#define __ACTIVE_DIAG_OUT_H__

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"


/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* IO control RDIs */
#define CMD_IGN_COIL_01           0x5001u
#define CMD_IGN_COIL_02           0x5002u
#define CMD_IGN_COIL_03           0x5003u
#define CMD_IGN_COIL_04           0x5004u
#define CMD_IGN_COIL_05           0x5005u
#define CMD_IGN_COIL_06           0x5006u
#if (N_CYLINDER == 8u)
#define CMD_IGN_COIL_07           0x5007u
#define CMD_IGN_COIL_08           0x5008u
#endif

#define ENDIAG_BOBINA0            1u // 0 for disabling
#define ENDIAG_BOBINA1            1u // 0 for disabling
#define ENDIAG_BOBINA2            1u // 0 for disabling
#define ENDIAG_BOBINA3            1u // 0 for disabling
#define ENDIAG_BOBINA4            1u // 0 for disabling
#define ENDIAG_BOBINA5            1u // 0 for disabling
#if (N_CYLINDER == 8u)
#define ENDIAG_BOBINA6            1u // 0 for disabling
#define ENDIAG_BOBINA7            1u // 0 for disabling
#endif


/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
///
 typedef enum {
    FREE = 0,
    BUSY = 1
} typBusy_Diag;

///
typedef struct
{
    typBusy_Diag   Ign_Flag_Busy;
    uint8_T        Ign_Test_DIAG;
    uint16_T       Bobina;          // ms
    uint32_T       StopTimeIgn;     // us
    uint32_T       StartAngleIgn;
    uint32_T       Saturation;
    uint16_T       TimePeriodIgn;
    uint16_T       IOLIid;
} diagIO_IGNStruct;

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern diagIO_IGNStruct Ign0;
extern diagIO_IGNStruct Ign1;
extern diagIO_IGNStruct Ign2;
extern diagIO_IGNStruct Ign3;
extern diagIO_IGNStruct Ign4;
extern diagIO_IGNStruct Ign5;
#if (N_CYLINDER == 8u)
extern diagIO_IGNStruct Ign6;
extern diagIO_IGNStruct Ign7;
#endif

extern uint8_T FlgIoliIGN;

/*************************   FUNCTION PROTOTYPES   ****************************/
extern void ActiveDiagMgm_T5ms(void);
extern void Init_ActiveDiag(void);
extern void Reset_DIAG(uint16_T CMD);
extern void Reset_All_DIAG(void);
extern typBusy_Diag Get_ActiveDiag_Enable_Condition(uint16_T CMD);
extern void Reset_DIAG_minusOne(uint16_T CMD);
extern void cmdIOIgn0(void);
extern void cmdIOIgn1(void);
extern void cmdIOIgn2(void);
extern void cmdIOIgn3(void);
extern void cmdIOIgn4(void);
extern void cmdIOIgn5(void);
#if (N_CYLINDER == 8u)
extern void cmdIOIgn6(void);
extern void cmdIOIgn7(void);
#endif
extern void FuncTaskIgnStop(void);


#endif // __ACTIVE_DIAG_OUT_H__
