/****************************************************************************
*
* Copyright © 2018-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_mcs_cfg.h
 * @brief   GTM MCS Driver configuration macros and structures.
 *
 * @addtogroup MCS
 * @{
 */

#ifndef _GTM_MCS_CFG_H_
#define _GTM_MCS_CFG_H_

#include "gtm_mcs.h"

/*lint -e621*/

/*
 * MCS Memory configuration.
 */
#define SPC5_GTM_MCS0_MEMORY_CONFIG                     SPC5_GTM_MCS_MEMORY_CONFIG_DEFAULT
#define SPC5_GTM_MCS1_MEMORY_CONFIG                     SPC5_GTM_MCS_MEMORY_CONFIG_DEFAULT
#define SPC5_GTM_MCS2_MEMORY_CONFIG                     SPC5_GTM_MCS_MEMORY_CONFIG_DEFAULT
#define SPC5_GTM_MCS3_MEMORY_CONFIG                     SPC5_GTM_MCS_MEMORY_CONFIG_DEFAULT
#define SPC5_GTM_MCS4_MEMORY_CONFIG                     SPC5_GTM_MCS_MEMORY_CONFIG_DEFAULT

/* ---- MCS0 Settings ---- */
#define SPC5_GTM_MCS0_SCHEDULER_MODE                    SPC5_GTM_MCS_SCHEDULER_ACCELERATED
/* ---- ---------------------- ---- */

/* ---- MCS0 Channel 0 Settings ---- */
#define SPC5_GTM_MCS0_USE_CHANNEL0                      TRUE
#define SPC5_GTM_MCS0_CHANNEL0_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS0_CHANNEL0_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS0_CHANNEL0_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL0_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS0_CHANNEL0_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL0_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS0_CHANNEL0_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS0 Channel 1 Settings ---- */
#define SPC5_GTM_MCS0_USE_CHANNEL1                      TRUE
#define SPC5_GTM_MCS0_CHANNEL1_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS0_CHANNEL1_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS0_CHANNEL1_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL1_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS0_CHANNEL1_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL1_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS0_CHANNEL1_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS0 Channel 2 Settings ---- */
#define SPC5_GTM_MCS0_USE_CHANNEL2                      TRUE
#define SPC5_GTM_MCS0_CHANNEL2_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS0_CHANNEL2_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS0_CHANNEL2_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL2_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS0_CHANNEL2_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL2_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS0_CHANNEL2_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS0 Channel 3 Settings ---- */
#define SPC5_GTM_MCS0_USE_CHANNEL3                      TRUE
#define SPC5_GTM_MCS0_CHANNEL3_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS0_CHANNEL3_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS0_CHANNEL3_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL3_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS0_CHANNEL3_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL3_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS0_CHANNEL3_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS0 Channel 4 Settings ---- */
#define SPC5_GTM_MCS0_USE_CHANNEL4                      FALSE
#define SPC5_GTM_MCS0_CHANNEL4_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS0_CHANNEL4_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS0_CHANNEL4_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL4_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS0_CHANNEL4_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL4_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS0_CHANNEL4_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS0 Channel 5 Settings ---- */
#define SPC5_GTM_MCS0_USE_CHANNEL5                      TRUE
#define SPC5_GTM_MCS0_CHANNEL5_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS0_CHANNEL5_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS0_CHANNEL5_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL5_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS0_CHANNEL5_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL5_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS0_CHANNEL5_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS0 Channel 6 Settings ---- */
#define SPC5_GTM_MCS0_USE_CHANNEL6                      TRUE
#define SPC5_GTM_MCS0_CHANNEL6_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS0_CHANNEL6_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS0_CHANNEL6_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL6_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS0_CHANNEL6_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL6_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS0_CHANNEL6_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS0 Channel 7 Settings ---- */
#define SPC5_GTM_MCS0_USE_CHANNEL7                      TRUE
#define SPC5_GTM_MCS0_CHANNEL7_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS0_CHANNEL7_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS0_CHANNEL7_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL7_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS0_CHANNEL7_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS0_CHANNEL7_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS0_CHANNEL7_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* Interrupts callbacks */
extern GTM_MCS_Channel_Callbacks *gtm_mcs0_callbacks[SPC5_GTM_MCS_CHANNELS];
extern GTM_MCS_Channel_Callbacks gtm_mcs0_channel0_callbacks;
void cyl0_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs0_channel1_callbacks;
void cyl2_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs0_channel2_callbacks;
void cyl4_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs0_channel3_callbacks;
void cyl6_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs0_channel5_callbacks;
void buck0_prim_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs0_channel6_callbacks;
void buck0_en_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs0_channel7_callbacks;
void buck0_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
/* ---- ---------------------- ---- */

/* ---- MCS1 Settings ---- */
#define SPC5_GTM_MCS1_SCHEDULER_MODE                    SPC5_GTM_MCS_SCHEDULER_ACCELERATED
/* ---- ---------------------- ---- */

/* ---- MCS1 Channel 0 Settings ---- */
#define SPC5_GTM_MCS1_USE_CHANNEL0                      TRUE
#define SPC5_GTM_MCS1_CHANNEL0_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS1_CHANNEL0_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS1_CHANNEL0_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL0_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS1_CHANNEL0_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL0_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS1_CHANNEL0_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS1 Channel 1 Settings ---- */
#define SPC5_GTM_MCS1_USE_CHANNEL1                      TRUE
#define SPC5_GTM_MCS1_CHANNEL1_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS1_CHANNEL1_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS1_CHANNEL1_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL1_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS1_CHANNEL1_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL1_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS1_CHANNEL1_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS1 Channel 2 Settings ---- */
#define SPC5_GTM_MCS1_USE_CHANNEL2                      TRUE
#define SPC5_GTM_MCS1_CHANNEL2_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS1_CHANNEL2_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS1_CHANNEL2_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL2_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS1_CHANNEL2_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL2_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS1_CHANNEL2_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS1 Channel 3 Settings ---- */
#define SPC5_GTM_MCS1_USE_CHANNEL3                      TRUE
#define SPC5_GTM_MCS1_CHANNEL3_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS1_CHANNEL3_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS1_CHANNEL3_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL3_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS1_CHANNEL3_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL3_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS1_CHANNEL3_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS1 Channel 4 Settings ---- */
#define SPC5_GTM_MCS1_USE_CHANNEL4                      FALSE
#define SPC5_GTM_MCS1_CHANNEL4_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS1_CHANNEL4_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS1_CHANNEL4_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL4_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS1_CHANNEL4_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL4_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS1_CHANNEL4_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS1 Channel 5 Settings ---- */
#define SPC5_GTM_MCS1_USE_CHANNEL5                      TRUE
#define SPC5_GTM_MCS1_CHANNEL5_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS1_CHANNEL5_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS1_CHANNEL5_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL5_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS1_CHANNEL5_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL5_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS1_CHANNEL5_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS1 Channel 6 Settings ---- */
#define SPC5_GTM_MCS1_USE_CHANNEL6                      TRUE
#define SPC5_GTM_MCS1_CHANNEL6_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS1_CHANNEL6_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS1_CHANNEL6_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL6_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS1_CHANNEL6_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL6_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS1_CHANNEL6_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS1 Channel 7 Settings ---- */
#define SPC5_GTM_MCS1_USE_CHANNEL7                      TRUE
#define SPC5_GTM_MCS1_CHANNEL7_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS1_CHANNEL7_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS1_CHANNEL7_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL7_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS1_CHANNEL7_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS1_CHANNEL7_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS1_CHANNEL7_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* Interrupts callbacks */
extern GTM_MCS_Channel_Callbacks *gtm_mcs1_callbacks[SPC5_GTM_MCS_CHANNELS];
extern GTM_MCS_Channel_Callbacks gtm_mcs1_channel0_callbacks;
void cyl1_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs1_channel1_callbacks;
void cyl3_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs1_channel2_callbacks;
void cyl5_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs1_channel3_callbacks;
void cyl7_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs1_channel5_callbacks;
void buck1_prim_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs1_channel6_callbacks;
void buck1_en_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs1_channel7_callbacks;
void buck1_mcs_cb(GTM_MCSDriver *mcsd, uint8_t channel);
/* ---- ---------------------- ---- */

/* ---- MCS2 Settings ---- */
#define SPC5_GTM_MCS2_SCHEDULER_MODE                    SPC5_GTM_MCS_SCHEDULER_ROUND_ROBIN
#define SPC5_GTM_MCS2_SCD_CH                            5U
/* ---- ---------------------- ---- */

/* ---- MCS2 Channel 0 Settings ---- */
#define SPC5_GTM_MCS2_USE_CHANNEL0                      TRUE
#define SPC5_GTM_MCS2_CHANNEL0_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS2_CHANNEL0_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS2_CHANNEL0_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL0_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS2_CHANNEL0_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL0_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS2_CHANNEL0_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS2 Channel 1 Settings ---- */
#define SPC5_GTM_MCS2_USE_CHANNEL1                      TRUE
#define SPC5_GTM_MCS2_CHANNEL1_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS2_CHANNEL1_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS2_CHANNEL1_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL1_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS2_CHANNEL1_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL1_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS2_CHANNEL1_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS2 Channel 2 Settings ---- */
#define SPC5_GTM_MCS2_USE_CHANNEL2                      TRUE
#define SPC5_GTM_MCS2_CHANNEL2_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS2_CHANNEL2_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS2_CHANNEL2_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL2_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS2_CHANNEL2_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL2_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS2_CHANNEL2_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS2 Channel 3 Settings ---- */
#define SPC5_GTM_MCS2_USE_CHANNEL3                      TRUE
#define SPC5_GTM_MCS2_CHANNEL3_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS2_CHANNEL3_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS2_CHANNEL3_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL3_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS2_CHANNEL3_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL3_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS2_CHANNEL3_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS2 Channel 4 Settings ---- */
#define SPC5_GTM_MCS2_USE_CHANNEL4                      TRUE
#define SPC5_GTM_MCS2_CHANNEL4_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS2_CHANNEL4_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS2_CHANNEL4_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL4_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS2_CHANNEL4_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL4_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS2_CHANNEL4_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS2 Channel 5 Settings ---- */
#define SPC5_GTM_MCS2_USE_CHANNEL5                      TRUE
#define SPC5_GTM_MCS2_CHANNEL5_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS2_CHANNEL5_INT_MCS_ENABLED          TRUE
#define SPC5_GTM_MCS2_CHANNEL5_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL5_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS2_CHANNEL5_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL5_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS2_CHANNEL5_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS2 Channel 6 Settings ---- */
#define SPC5_GTM_MCS2_USE_CHANNEL6                      FALSE
#define SPC5_GTM_MCS2_CHANNEL6_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS2_CHANNEL6_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS2_CHANNEL6_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL6_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS2_CHANNEL6_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL6_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS2_CHANNEL6_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS2 Channel 7 Settings ---- */
#define SPC5_GTM_MCS2_USE_CHANNEL7                      FALSE
#define SPC5_GTM_MCS2_CHANNEL7_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS2_CHANNEL7_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS2_CHANNEL7_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL7_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS2_CHANNEL7_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS2_CHANNEL7_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS2_CHANNEL7_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* Interrupts callbacks */
extern GTM_MCS_Channel_Callbacks *gtm_mcs2_callbacks[SPC5_GTM_MCS_CHANNELS];
extern GTM_MCS_Channel_Callbacks gtm_mcs2_channel0_callbacks;
void mcs2_ch0_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs2_channel1_callbacks;
void mcs2_ch1_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs2_channel2_callbacks;
void mcs2_ch2_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs2_channel3_callbacks;
void mcs2_ch3_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs2_channel4_callbacks;
void mcs2_4_ipri0_isec_cb(GTM_MCSDriver *mcsd, uint8_t channel);
extern GTM_MCS_Channel_Callbacks gtm_mcs2_channel5_callbacks;
void mcs2_5_ipri1_isec_cb(GTM_MCSDriver *mcsd, uint8_t channel);
/* ---- ---------------------- ---- */

/* ---- MCS3 Settings ---- */
#define SPC5_GTM_MCS3_SCHEDULER_MODE                    SPC5_GTM_MCS_SCHEDULER_ROUND_ROBIN
#define SPC5_GTM_MCS3_SCD_CH                            0U
/* ---- ---------------------- ---- */

/* ---- MCS3 Channel 0 Settings ---- */
#define SPC5_GTM_MCS3_USE_CHANNEL0                      FALSE
#define SPC5_GTM_MCS3_CHANNEL0_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS3_CHANNEL0_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS3_CHANNEL0_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL0_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS3_CHANNEL0_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL0_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS3_CHANNEL0_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS3 Channel 1 Settings ---- */
#define SPC5_GTM_MCS3_USE_CHANNEL1                      FALSE
#define SPC5_GTM_MCS3_CHANNEL1_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS3_CHANNEL1_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS3_CHANNEL1_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL1_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS3_CHANNEL1_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL1_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS3_CHANNEL1_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS3 Channel 2 Settings ---- */
#define SPC5_GTM_MCS3_USE_CHANNEL2                      FALSE
#define SPC5_GTM_MCS3_CHANNEL2_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS3_CHANNEL2_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS3_CHANNEL2_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL2_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS3_CHANNEL2_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL2_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS3_CHANNEL2_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS3 Channel 3 Settings ---- */
#define SPC5_GTM_MCS3_USE_CHANNEL3                      FALSE
#define SPC5_GTM_MCS3_CHANNEL3_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS3_CHANNEL3_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS3_CHANNEL3_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL3_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS3_CHANNEL3_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL3_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS3_CHANNEL3_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS3 Channel 4 Settings ---- */
#define SPC5_GTM_MCS3_USE_CHANNEL4                      FALSE
#define SPC5_GTM_MCS3_CHANNEL4_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS3_CHANNEL4_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS3_CHANNEL4_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL4_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS3_CHANNEL4_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL4_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS3_CHANNEL4_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS3 Channel 5 Settings ---- */
#define SPC5_GTM_MCS3_USE_CHANNEL5                      FALSE
#define SPC5_GTM_MCS3_CHANNEL5_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS3_CHANNEL5_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS3_CHANNEL5_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL5_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS3_CHANNEL5_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL5_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS3_CHANNEL5_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS3 Channel 6 Settings ---- */
#define SPC5_GTM_MCS3_USE_CHANNEL6                      FALSE
#define SPC5_GTM_MCS3_CHANNEL6_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS3_CHANNEL6_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS3_CHANNEL6_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL6_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS3_CHANNEL6_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL6_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS3_CHANNEL6_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS3 Channel 7 Settings ---- */
#define SPC5_GTM_MCS3_USE_CHANNEL7                      FALSE
#define SPC5_GTM_MCS3_CHANNEL7_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS3_CHANNEL7_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS3_CHANNEL7_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL7_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS3_CHANNEL7_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS3_CHANNEL7_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS3_CHANNEL7_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS4 Settings ---- */
#define SPC5_GTM_MCS4_SCHEDULER_MODE                    SPC5_GTM_MCS_SCHEDULER_ROUND_ROBIN
#define SPC5_GTM_MCS4_SCD_CH                            0U
/* ---- ---------------------- ---- */

/* ---- MCS4 Channel 0 Settings ---- */
#define SPC5_GTM_MCS4_USE_CHANNEL0                      FALSE
#define SPC5_GTM_MCS4_CHANNEL0_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS4_CHANNEL0_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS4_CHANNEL0_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL0_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS4_CHANNEL0_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL0_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS4_CHANNEL0_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS4 Channel 1 Settings ---- */
#define SPC5_GTM_MCS4_USE_CHANNEL1                      FALSE
#define SPC5_GTM_MCS4_CHANNEL1_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS4_CHANNEL1_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS4_CHANNEL1_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL1_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS4_CHANNEL1_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL1_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS4_CHANNEL1_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS4 Channel 2 Settings ---- */
#define SPC5_GTM_MCS4_USE_CHANNEL2                      FALSE
#define SPC5_GTM_MCS4_CHANNEL2_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS4_CHANNEL2_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS4_CHANNEL2_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL2_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS4_CHANNEL2_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL2_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS4_CHANNEL2_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS4 Channel 3 Settings ---- */
#define SPC5_GTM_MCS4_USE_CHANNEL3                      FALSE
#define SPC5_GTM_MCS4_CHANNEL3_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS4_CHANNEL3_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS4_CHANNEL3_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL3_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS4_CHANNEL3_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL3_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS4_CHANNEL3_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS4 Channel 4 Settings ---- */
#define SPC5_GTM_MCS4_USE_CHANNEL4                      FALSE
#define SPC5_GTM_MCS4_CHANNEL4_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS4_CHANNEL4_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS4_CHANNEL4_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL4_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS4_CHANNEL4_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL4_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS4_CHANNEL4_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS4 Channel 5 Settings ---- */
#define SPC5_GTM_MCS4_USE_CHANNEL5                      FALSE
#define SPC5_GTM_MCS4_CHANNEL5_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS4_CHANNEL5_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS4_CHANNEL5_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL5_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS4_CHANNEL5_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL5_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS4_CHANNEL5_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS4 Channel 6 Settings ---- */
#define SPC5_GTM_MCS4_USE_CHANNEL6                      FALSE
#define SPC5_GTM_MCS4_CHANNEL6_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS4_CHANNEL6_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS4_CHANNEL6_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL6_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS4_CHANNEL6_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL6_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS4_CHANNEL6_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */

/* ---- MCS4 Channel 7 Settings ---- */
#define SPC5_GTM_MCS4_USE_CHANNEL7                      FALSE
#define SPC5_GTM_MCS4_CHANNEL7_IRQ_MODE                 SPC5_GTM_MCS_IRQ_MODE_LEVEL
#define SPC5_GTM_MCS4_CHANNEL7_INT_MCS_ENABLED          FALSE
#define SPC5_GTM_MCS4_CHANNEL7_INT_MCS_MODE             SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL7_INT_STACK_ERROR_ENABLED  FALSE
#define SPC5_GTM_MCS4_CHANNEL7_INT_STACK_ERROR_MODE     SPC5_GTM_MCS_INT_MODE_NORMAL
#define SPC5_GTM_MCS4_CHANNEL7_INT_MEMORY_ERROR_ENABLED FALSE
#define SPC5_GTM_MCS4_CHANNEL7_INT_MEMORY_ERROR_MODE    SPC5_GTM_MCS_INT_MODE_NORMAL
/* ---- ---------------------- ---- */


/*lint +e621*/
#endif /* _GTM_MCS_CFG_H_ */

/** @} */
