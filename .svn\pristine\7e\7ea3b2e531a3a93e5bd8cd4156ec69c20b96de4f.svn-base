/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Spec/Application/KnockCorrNom/trunk/KnockCorrNom_codegen/K#$  */
/* $Revision:: 210102                                                                                         $  */
/* $Date:: 2022-02-25 14:09:30 +0100 (ven, 25 feb 2022)                                                       $  */
/* $Author:: MarottaR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/*
 * File: KnockCorrNom_eep.c
 *
 * Code generated for Simulink model 'KnockCorrNom'.
 *
 * Model version                  : 1.1096
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Feb 25 14:04:49 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor->Custom Processor
 * Emulation hardware selection:
 *    Differs from embedded hardware (Intel->x86-64 (Windows64))
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. ROM efficiency
 *    5. RAM efficiency
 *    6. Traceability
 *    7. Debugging
 *    8. Polyspace
 * Validation result: Passed (31), Warning (1), Error (0)
 */
#include "rtwtypes.h"

/* Exported data definition */

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_EE_INTERFACE */
uint8_T CntKnockCohEE[8] = { ((uint8_T)0U), ((uint8_T)0U), ((uint8_T)0U),
  ((uint8_T)0U), ((uint8_T)0U), ((uint8_T)0U), ((uint8_T)0U), ((uint8_T)0U) };/* '<S4>/Merge5' */

/* Knocking coherence diagnosis pre-filter */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */