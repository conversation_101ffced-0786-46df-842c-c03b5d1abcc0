/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      CombAdp.h
 **  Date:          15-Sep-2021
 **
 **  Model Version: 1.1042
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_CombAdp_h_
#define RTW_HEADER_CombAdp_h_
#ifndef CombAdp_COMMON_INCLUDES_
# define CombAdp_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* CombAdp_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void CombAdp_initialize(void);

/* Exported entry point function */
extern void CombAdp_EOA(void);

/* Exported entry point function */
extern void CombAdp_NoSync(void);

/* Exported entry point function */
extern void CombAdp_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T InjCorrCylAd;          /* '<S3>/Merge1' */

/* Adaptative correction for cylinder balancing */
extern uint16_T InjCorrCylAdNorm;      /* '<S3>/Merge3' */

/* Normalization factor for adaptative correction on cylinder balancing PI and output */
extern uint8_T VtCylBalTrigAdat[8];    /* '<S3>/Merge15' */

/* Cylinder balancing adaptativity Trigger */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S14>/Data Type Duplicate' : Unused code path elimination
 * Block '<S13>/Data Type Duplicate' : Unused code path elimination
 * Block '<S13>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S21>/Data Type Duplicate' : Unused code path elimination
 * Block '<S16>/Data Type Duplicate' : Unused code path elimination
 * Block '<S17>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Data Type Duplicate' : Unused code path elimination
 * Block '<S19>/Data Type Duplicate' : Unused code path elimination
 * Block '<S6>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S13>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S14>/Conversion' : Eliminate redundant data type conversion
 * Block '<S13>/Reshape' : Reshape block reduction
 * Block '<S11>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S7>/Conversion' : Eliminate redundant data type conversion
 * Block '<S7>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S21>/Conversion' : Eliminate redundant data type conversion
 * Block '<S16>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S16>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S16>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S17>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S17>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S17>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion5' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CombAdp'
 * '<S1>'   : 'CombAdp/EOA'
 * '<S2>'   : 'CombAdp/Reset'
 * '<S3>'   : 'CombAdp/Subsystem'
 * '<S4>'   : 'CombAdp/EOA/AdaptiveCorrectionMgm'
 * '<S5>'   : 'CombAdp/EOA/EnablingCondition'
 * '<S6>'   : 'CombAdp/EOA/EvalAdCorr'
 * '<S7>'   : 'CombAdp/EOA/Interpolate_TbInjCorrCylAd'
 * '<S8>'   : 'CombAdp/EOA/ZonesLearn'
 * '<S9>'   : 'CombAdp/EOA/AdaptiveCorrectionMgm/CALCULATE_CORRECTION.calculateAvg'
 * '<S10>'  : 'CombAdp/EOA/EvalAdCorr/LookUp 2-D TBCYLBALGNAD'
 * '<S11>'  : 'CombAdp/EOA/EvalAdCorr/RescalSignedIntRightShift'
 * '<S12>'  : 'CombAdp/EOA/EvalAdCorr/RescalSignedIntRightShift1'
 * '<S13>'  : 'CombAdp/EOA/EvalAdCorr/LookUp 2-D TBCYLBALGNAD/Look2D_U16_U16_U16'
 * '<S14>'  : 'CombAdp/EOA/EvalAdCorr/LookUp 2-D TBCYLBALGNAD/Look2D_U16_U16_U16/Data Type Conversion Inherited1'
 * '<S15>'  : 'CombAdp/EOA/ZonesLearn/LookUp_VTTDCSTABCYLBALAD'
 * '<S16>'  : 'CombAdp/EOA/ZonesLearn/PreLookUpIdSearch_U1'
 * '<S17>'  : 'CombAdp/EOA/ZonesLearn/PreLookUpIdSearch_U16'
 * '<S18>'  : 'CombAdp/EOA/ZonesLearn/Signal_Stability1'
 * '<S19>'  : 'CombAdp/EOA/ZonesLearn/Signal_Stability2'
 * '<S20>'  : 'CombAdp/EOA/ZonesLearn/LookUp_VTTDCSTABCYLBALAD/LookUp_IR_U16'
 * '<S21>'  : 'CombAdp/EOA/ZonesLearn/LookUp_VTTDCSTABCYLBALAD/LookUp_IR_U16/Data Type Conversion Inherited3'
 */

/*-
 * Requirements for '<Root>': CombAdp
 */
#endif                                 /* RTW_HEADER_CombAdp_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/