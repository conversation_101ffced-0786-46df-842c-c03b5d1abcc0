/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TempMgm
**  Filename        :  TempMgm_out.h
**  Created on      :  24-mar-2021 10:37:00
**  Original author :  SantoroR
******************************************************************************/
/*****************************************************************************
**
**                        TempMgm Description
**
**  Component used to calculate TAir and TWater 
******************************************************************************/
#ifndef TEMPMGM_OUT_H_
#define TEMPMGM_OUT_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern int16_T TAir;
extern int16_T TAtm;                   /* Ambient temperature */
extern int16_T TWater;                 /* Coolant temperature */
extern int16_T TWaterCrk;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : TempMgm_Init
**
**   Description:
**    Reset the values of the module variables. The global variables (outputs) 
**    and the static variables (states) are reset to the default values by this function.
**
**    This functions performs:
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void TempMgm_Init(void);

/******************************************************************************
**   Function    : TempMgm_T100ms
**
**   Description:
**    Temp Managemnt operations at 100ms.
**    This functions performs:
**     - Calculates TAir and TWater
**     - Force Values for testing by mean of forcing calibrations FOTWATER and FOTAIR
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void TempMgm_T100ms(void);

#endif

/****************************************************************************
 ****************************************************************************/

