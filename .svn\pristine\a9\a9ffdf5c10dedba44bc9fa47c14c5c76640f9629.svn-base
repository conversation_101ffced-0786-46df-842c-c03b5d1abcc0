/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Com_private.h
 **  Date:          24-Jan-2024
 **
 **  Model Version: 1.879
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Com_private_h_
#define RTW_HEADER_TLE9278BQX_Com_private_h_
#include "rtwtypes.h"
#include "TLE9278BQX_Com_out.h"

/* Includes for objects with custom storage classes. */
#include "Diagmgm_out.h"
#include "Wdt_out.h"
#include "ivor_c2_out.h"
#include "TLE9278BQX_Com_eep_out.h"
#include "Msparkcmd_out.h"
#include "PwrMgm_out.h"
#include "SyncMgm_out.h"
#include "Canmgmin_out.h"
/* Includes for extra files. */
#include "Dspi_out.h"
#include "TLE9278BQX_Cfg_out.h"
#include "TLE9278BQX_Diag_out.h"
#include "TLE9278BQX_Get_out.h"
#include "TLE9278BQX_IOs_out.h"
#include "TLE9278BQX_IvorEE_out.h"
#include "TLE9278BQX_Mgm_out.h"
#include "TLE9278BQX_Prs_out.h"
#include "WDT_out.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/
#ifdef __cplusplus

extern "C" {

#endif

  extern void Ret_SBCData_Addr_Start_wrapper(void);
  extern void Ret_SBCData_Addr_Outputs_wrapper(const uint16_T *buff,
    const uint8_T *start,
    uint32_T *addr);
  extern void Ret_SBCData_Addr_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

#ifdef __cplusplus

extern "C" {

#endif

  extern void EECntSBCResend_Addr_U16_Start_wrapper(void);
  extern void EECntSBCResend_Addr_U16_Outputs_wrapper(const uint16_T *in,
    uint32_T *addr);
  extern void EECntSBCResend_Addr_U16_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

#ifdef __cplusplus

extern "C" {

#endif

  extern void fc_EECntSBCResend_SetVal_Start_wrapper(void);
  extern void fc_EECntSBCResend_SetVal_Outputs_wrapper(const uint16_T *in,
    const uint32_T *addr,
    uint16_T *out);
  extern void fc_EECntSBCResend_SetVal_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

extern void TLE9278BQX_Co_fc_TLE9278BQX_Mgm(void);
extern void TLE9278BQX_Com_SBC_Compose_Data(const uint16_T rtu_SBCDataTxBuffer
  [30], const uint16_T rtu_SBCDataRxBuffer[30], DW_SBC_Compose_Data_TLE9278BQ_T *
  localDW);
extern void TLE9278BQX_Co_fc_TLE9278BQX_Prs(void);
extern void TLE9278BQX_Com_fc_InitRoutine3(void);
extern void TLE9278BQX_Com_fc_InitRoutine5(void);
extern void TLE9278BQX_Co_fc_TLE9278BQX_Get(void);
extern void TLE9278BQX_C_fc_TLE9278BQX_Diag(void);
extern void TLE9278BQX_Com_fc_Bkg_Init(void);
extern void TLE9278BQX_Com_fc_Bkg(void);
extern void TLE9278BQX_Com_fc_Init_Start(void);
extern void TLE9278BQX_Com_fc_Init(void);

#endif                                /* RTW_HEADER_TLE9278BQX_Com_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/