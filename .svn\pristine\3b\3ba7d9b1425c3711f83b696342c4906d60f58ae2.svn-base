#ifndef _CAN_CFG_H_
#define _CAN_CFG_H_

// CAN CONFIG
#if (CAN_TYPE==CAN_P15)
    #include "CAN_P15.cfg"
#elif (CAN_TYPE==CAN_F173)
    #include "CAN_F17x.cfg"
#elif (CAN_TYPE==CAN_F171)
    #include "CAN_F17x.cfg"
#elif (CAN_TYPE==CAN_LB74x)
    #include "CAN_LB74x.cfg"
#elif (CAN_TYPE == CAN_INEF)
    #include "CAN_I1.cfg"
#elif (CAN_TYPE == CAN_AMG6C)
    #include "CAN_Amg6c.cfg"
#elif (CAN_TYPE == CAN_CH4C)
    #include "CAN_CHx.cfg"
#elif (CAN_TYPE == CAN_CH6C)
    #include "CAN_CH6.cfg"
#elif (CAN_TYPE == CAN_TESTCFG)
    #include "CAN_Test.cfg"
#elif (CAN_TYPE == CAN_BR16C)
    #include "CAN_BR.cfg"
#else
#error ATTENZIONE: Combinazione proibita!!!
#endif

#endif

