/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonAcqBufRec.h
 **  Date:          03-Dec-2021
 **
 **  Model Version: 1.1522
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonAcqBufRec_h_
#define RTW_HEADER_IonAcqBufRec_h_
#include <string.h>
#ifndef IonAcqBufRec_COMMON_INCLUDES_
# define IonAcqBufRec_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* IonAcqBufRec_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void IonAcqBufRec_initialize(void);

/* Exported entry point function */
extern void IonAcqBufRec_5ms(void);

/* Exported entry point function */
extern void IonAcqBufRec_EOA(void);

/* Exported entry point function */
extern void IonAcqBufRec_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern int8_T IonAbsTdcInUse[2];       /* '<S3>/Merge14' */

/* IonSignal sel in use:*0..(N_CYL-1);*N_CYL..(2*N_CYL-1) with knock;*-1 cyl rotation;*-2 StPhase !=4;*-3 OFF;*-4 spike detected;*-5 misfire;*-6 knock coherence diag;*-7 spike detected and knock;*-8 knock;*-9 knock and overwrite if greater knock;*-10 megaknock;*-11 DwellInt>thr;*-12 knockdist;*-13 as -9 (over if DwellInt>thr);*-14 as -13 (also if FlgDSAoutDis!=0);*-15 as -9 with Rpm>thr;*-16 IntIon<NocombThr;*-17 if ThPeak>=SHOWTHPEAKTHR;*-18 if TSpark<=IONSIGNALTSPARKTHR */
extern uint32_T VtIonSignal[2];        /* '<S3>/Merge8' */

/* Last Ion sample (development tool) */
extern uint16_T VtNSample[2];          /* '<S3>/Merge12' */

/* Sample counter (development tool) */
extern uint8_T VtShownAbsTdc[2];       /* '<S3>/Merge13' */

/* TDC index of IonSample */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S6>/scaling' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'IonAcqBufRec'
 * '<S1>'   : 'IonAcqBufRec/EOA'
 * '<S2>'   : 'IonAcqBufRec/PowerOn'
 * '<S3>'   : 'IonAcqBufRec/Subsystem'
 * '<S4>'   : 'IonAcqBufRec/T5ms'
 * '<S5>'   : 'IonAcqBufRec/EOA/Buffer_Record_Mgm'
 * '<S6>'   : 'IonAcqBufRec/EOA/Buffer_Record_Mgm/Buffer_Record.SHOW_THPEAK_GREATER_THR.evalThThr'
 * '<S7>'   : 'IonAcqBufRec/EOA/Buffer_Record_Mgm/CopyIonBuf.copyRawNumber'
 * '<S8>'   : 'IonAcqBufRec/T5ms/Buffer_MarkOut'
 */

/*-
 * Requirements for '<Root>': IonAcqBufRec
 *
 * Inherited requirements for '<Root>/PowerOn':
 *  1. EISB_FCA6CYL_SW_REQ_1734: Software shall initialize each output produced for the "buffer record" strategy at ECU power on. (ECU_SW_Requirements#3940)

 */
#endif                                 /* RTW_HEADER_IonAcqBufRec_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/