; Restore the init status - perform this script on Z2 core

;   script-local macros:
LOCAL &portMZ2 &portMZ4 &portGTM &portMZ2_RCL &portMZ4_RCL &portGTM_RCL &addressMZ2 &addressMZ4 &addressGTM

; intercom setup:
GOSUB setup_intercom

; set up helper commands
ON CMD COREMZ2 GOSUB COREMZ2
ON CMD COREMZ4 GOSUB COREMZ4
ON CMD COREGTM GOSUB coreGTM
ON CMD COREALL GOSUB coreAll

;COREALL Break.Reset
COREALL SYStem.Down

; Synch setting for MPC and GTM
COREALL SYnch.RESet

; reset K2 chip (all core)
SYStem.Up
WAIT 100ms



; --------------------------------------------------------------------------------
; Attach to GTM and enable GTM 
COREGTM SYStem.Mode Attach

Go symbol.exit(gtmInit)
WAIT !STATE.RUN()

COREGTM Break

; --------------------------------------------------------------------------------
; Attach to core Z4
COREMZ4 SYStem.Mode Attach
COREMZ4 Break
Go symbol.exit(runCore0)
WAIT !STATE.RUN()


;EVTO Setting EVTO1 on PF[15] and EVTO0 on PF[14]
;COREMZ2 PER.Set.Field DBG:0x60E %L 0x00000008 0x1 ; DCI_CR EVTO1 enabled
;COREMZ2 PER.Set.Field DBG:0x60E %L 0x00000004 0x1 ; DCI_CR EVTO0 enabled

; --------------------------------------------------------------------------------
; Synch setting for MPC and GTM
COREALL SYnch.RESet
COREALL SYnch.ON
COREMZ2 SYnch.Connect &addressMZ4 &addressGTM
COREMZ4 SYnch.Connect &addressMZ2 &addressGTM
COREGTM SYnch.Connect &addressMZ2 &addressMZ4
COREALL SYnch.MasterGo ON
COREALL SYnch.MasterBreak ON
COREALL SYnch.SlaveGo ON
;COREMZ2 Synch.SlaveGo OFF
COREALL SYnch.SlaveBreak ON
COREALL SYnch.MasterSystemMode ON
COREALL SYnch.SlaveSystemMode ON
COREMZ2 SYnch.XTrack &addressMZ4 &addressGTM
COREMZ4 SYnch.XTrack &addressMZ2 &addressGTM
COREGTM SYnch.XTrack &addressMZ2 &addressMZ4
COREMZ2 SYStem.CONFIG Slave OFF
COREMZ4 SYStem.CONFIG Slave ON
COREGTM SYStem.CONFIG Slave ON

ENDDO




; --------------------------------------------------------------------------------
; helper subroutines:

COREMZ2:
(
  LOCAL &params
  ENTRY %Line &params
  &params ; execute on this GUI
  RETURN
)

COREMZ4:
(
  LOCAL &params
  ENTRY %Line &params
  INTERCOM.execute &addressMZ4 &params ; execute on remote GUI
  RETURN
)

coreGTM:
(
  LOCAL &params
  ENTRY %Line &params
  INTERCOM.execute &addressGTM &params ; execute on remote GUI
  RETURN
)

coreAll:
(
  LOCAL &params
  ENTRY %Line &params
  GOSUB COREMZ2 &params
  GOSUB COREMZ4 &params
  GOSUB coreGTM &params
  RETURN
)

setup_intercom:
(
  &portMZ2=FORMAT.DECIMAL(1.,INTERCOM.PORT())
  &portMZ4=FORMAT.DECIMAL(1.,INTERCOM.PORT()+1.)
  &portGTM=FORMAT.DECIMAL(1.,INTERCOM.PORT()+2.)
  &addressMZ2="127.0.0.1:&portMZ2"
  &addressMZ4="127.0.0.1:&portMZ4"
  &addressGTM="127.0.0.1:&portGTM"
  RETURN
)