/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_atom_cfg.h
 * @brief   GTM ATOM Driver configuration macros and structures.
 *
 * @addtogroup ATOM
 * @{
 */

#ifndef _GTM_ATOM_CFG_H_
#define _GTM_ATOM_CFG_H_

#include "gtm_atom.h"

/*lint -e621*/

/* Interrupts callbacks */
extern GTM_ATOM_Channel_Callbacks *gtm_atom0_callbacks[SPC5_GTM_ATOM_CHANNELS];
extern GTM_ATOM_Channel_Callbacks gtm_atom0_channel1_callbacks;
void crank_ref_pos_atom0_1_cb(GTM_ATOMDriver *atomd, uint8_T channel);

extern GTM_ATOM_Channel_Callbacks *gtm_atom1_callbacks[SPC5_GTM_ATOM_CHANNELS];

extern GTM_ATOM_Channel_Callbacks *gtm_atom2_callbacks[SPC5_GTM_ATOM_CHANNELS];

extern GTM_ATOM_Channel_Callbacks *gtm_atom3_callbacks[SPC5_GTM_ATOM_CHANNELS];

/* ---- ---------------------- ---- */

/* ---- ATOM0 Settings ---- */
/* ATOM0 CHANNEL0 */
#define SPC5_GTM_ATOM0_USE_CHANNEL0                 TRUE
#define SPC5_GTM_ATOM0_CHANNEL0_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM0_CHANNEL0_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM0_CHANNEL0_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_BOTH
#define SPC5_GTM_ATOM0_CHANNEL0_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM0_CHANNEL0_CLK_SOURCE          SPC5_GTM_CMU_CLK0
#define SPC5_GTM_ATOM0_CHANNEL0_PERIOD              0UL
#define SPC5_GTM_ATOM0_CHANNEL0_DUTY_CYCLE          0UL
/* Default settings */
#define SPC5_GTM_ATOM0_CHANNEL0_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM0_CHANNEL0_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL0_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM0_CHANNEL0_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL0_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM0 CHANNEL1 */
#define SPC5_GTM_ATOM0_USE_CHANNEL1                 TRUE
#define SPC5_GTM_ATOM0_CHANNEL1_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE
#define SPC5_GTM_ATOM0_CHANNEL1_COMPARE_0           0UL
#define SPC5_GTM_ATOM0_CHANNEL1_COMPARE_1           0UL
#define SPC5_GTM_ATOM0_CHANNEL1_CM1_TBU_TS          SPC5_GTM_ATOM_COMPARE_USE_TBU_TS1
#define SPC5_GTM_ATOM0_CHANNEL1_COMPARE_STRATEGY    SPC5_GTM_ATOM_COMPARE_STRATEGY_GREATER_EQUAL
#define SPC5_GTM_ATOM0_CHANNEL1_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM0_CHANNEL1_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM0_CHANNEL1_ARU_BLOCKING_MODE   FALSE
#define SPC5_GTM_ATOM0_CHANNEL1_SIGNAL_LEVEL_CTRL   SPC5_GTM_ATOM_SIGNAL_LEVEL_CRTL_HIGH
#define SPC5_GTM_ATOM0_CHANNEL1_COMPARE_CONTROL     SPC5_GTM_ATOM_COMPARE_CCU0_THEN_CCU1_TS12
#define SPC5_GTM_ATOM0_CHANNEL1_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM0_CHANNEL1_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL1_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM0_CHANNEL1_INT_CCU1_ENABLED    TRUE
#define SPC5_GTM_ATOM0_CHANNEL1_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM0 CHANNEL2 */
#define SPC5_GTM_ATOM0_USE_CHANNEL2                 TRUE
#define SPC5_GTM_ATOM0_CHANNEL2_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM0_CHANNEL2_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM0_CHANNEL2_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_BOTH
#define SPC5_GTM_ATOM0_CHANNEL2_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM0_CHANNEL2_CLK_SOURCE          SPC5_GTM_CMU_CLK0
#define SPC5_GTM_ATOM0_CHANNEL2_PERIOD              0UL
#define SPC5_GTM_ATOM0_CHANNEL2_DUTY_CYCLE          0UL
/* Default settings */
#define SPC5_GTM_ATOM0_CHANNEL2_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM0_CHANNEL2_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL2_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM0_CHANNEL2_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL2_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM0 CHANNEL3 */
#define SPC5_GTM_ATOM0_USE_CHANNEL3                 TRUE
#define SPC5_GTM_ATOM0_CHANNEL3_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM0_CHANNEL3_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM0_CHANNEL3_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_BOTH
#define SPC5_GTM_ATOM0_CHANNEL3_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM0_CHANNEL3_CLK_SOURCE          SPC5_GTM_CMU_CLK0
#define SPC5_GTM_ATOM0_CHANNEL3_PERIOD              0UL
#define SPC5_GTM_ATOM0_CHANNEL3_DUTY_CYCLE          0UL
/* Default settings */
#define SPC5_GTM_ATOM0_CHANNEL3_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM0_CHANNEL3_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL3_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM0_CHANNEL3_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL3_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM0 CHANNEL4 */
#define SPC5_GTM_ATOM0_USE_CHANNEL4                 TRUE
#define SPC5_GTM_ATOM0_CHANNEL4_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM0_CHANNEL4_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM0_CHANNEL4_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_BOTH
#define SPC5_GTM_ATOM0_CHANNEL4_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM0_CHANNEL4_CLK_SOURCE          SPC5_GTM_CMU_CLK0
#define SPC5_GTM_ATOM0_CHANNEL4_PERIOD              0UL
#define SPC5_GTM_ATOM0_CHANNEL4_DUTY_CYCLE          0UL
/* Default settings */
#define SPC5_GTM_ATOM0_CHANNEL4_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM0_CHANNEL4_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL4_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM0_CHANNEL4_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL4_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM0 CHANNEL5 */
#define SPC5_GTM_ATOM0_USE_CHANNEL5                 TRUE
#define SPC5_GTM_ATOM0_CHANNEL5_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM0_CHANNEL5_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM0_CHANNEL5_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM0_CHANNEL5_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM0_CHANNEL5_PERIOD              0UL
#define SPC5_GTM_ATOM0_CHANNEL5_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM0_CHANNEL5_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM0_CHANNEL5_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL5_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM0_CHANNEL5_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL5_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM0 CHANNEL6 */
#define SPC5_GTM_ATOM0_USE_CHANNEL6                 TRUE
#define SPC5_GTM_ATOM0_CHANNEL6_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM0_CHANNEL6_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM0_CHANNEL6_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_BOTH
#define SPC5_GTM_ATOM0_CHANNEL6_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM0_CHANNEL6_CLK_SOURCE          SPC5_GTM_CMU_CLK0
#define SPC5_GTM_ATOM0_CHANNEL6_PERIOD              0UL
#define SPC5_GTM_ATOM0_CHANNEL6_DUTY_CYCLE          0UL
/* Default settings */
#define SPC5_GTM_ATOM0_CHANNEL6_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM0_CHANNEL6_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL6_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM0_CHANNEL6_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL6_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM0 CHANNEL7 */
#define SPC5_GTM_ATOM0_USE_CHANNEL7                 TRUE
#define SPC5_GTM_ATOM0_CHANNEL7_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM0_CHANNEL7_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM0_CHANNEL7_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM0_CHANNEL7_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM0_CHANNEL7_CLK_SOURCE          SPC5_GTM_CMU_CLK5   
#define SPC5_GTM_ATOM0_CHANNEL7_PERIOD              32000UL
#define SPC5_GTM_ATOM0_CHANNEL7_DUTY_CYCLE          2500UL
/* Default settings */
#define SPC5_GTM_ATOM0_CHANNEL7_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM0_CHANNEL7_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL7_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM0_CHANNEL7_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM0_CHANNEL7_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ---- ---------------------- ---- */

/* ---- ATOM1 Settings ---- */
/* ATOM1 CHANNEL0 */
#define SPC5_GTM_ATOM1_USE_CHANNEL0                 TRUE
#define SPC5_GTM_ATOM1_CHANNEL0_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM1_CHANNEL0_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED
#define SPC5_GTM_ATOM1_CHANNEL0_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM1_CHANNEL0_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM1_CHANNEL0_PERIOD              0UL
#define SPC5_GTM_ATOM1_CHANNEL0_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM1_CHANNEL0_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM1_CHANNEL0_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL0_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM1_CHANNEL0_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL0_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM1 CHANNEL1 */
#define SPC5_GTM_ATOM1_USE_CHANNEL1                 TRUE
#define SPC5_GTM_ATOM1_CHANNEL1_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM1_CHANNEL1_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED
#define SPC5_GTM_ATOM1_CHANNEL1_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM1_CHANNEL1_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM1_CHANNEL1_PERIOD              0UL
#define SPC5_GTM_ATOM1_CHANNEL1_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM1_CHANNEL1_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM1_CHANNEL1_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL1_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM1_CHANNEL1_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL1_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM1 CHANNEL2 */
#define SPC5_GTM_ATOM1_USE_CHANNEL2                 TRUE
#define SPC5_GTM_ATOM1_CHANNEL2_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM1_CHANNEL2_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED
#define SPC5_GTM_ATOM1_CHANNEL2_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM1_CHANNEL2_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM1_CHANNEL2_PERIOD              0UL
#define SPC5_GTM_ATOM1_CHANNEL2_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM1_CHANNEL2_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM1_CHANNEL2_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL2_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM1_CHANNEL2_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL2_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM1 CHANNEL3 */
#define SPC5_GTM_ATOM1_USE_CHANNEL3                 TRUE
#define SPC5_GTM_ATOM1_CHANNEL3_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM1_CHANNEL3_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED
#define SPC5_GTM_ATOM1_CHANNEL3_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM1_CHANNEL3_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM1_CHANNEL3_PERIOD              0UL
#define SPC5_GTM_ATOM1_CHANNEL3_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM1_CHANNEL3_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM1_CHANNEL3_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL3_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM1_CHANNEL3_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL3_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM1 CHANNEL4 */
#define SPC5_GTM_ATOM1_USE_CHANNEL4                 TRUE
#define SPC5_GTM_ATOM1_CHANNEL4_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM1_CHANNEL4_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED
#define SPC5_GTM_ATOM1_CHANNEL4_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM1_CHANNEL4_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM1_CHANNEL4_PERIOD              0UL
#define SPC5_GTM_ATOM1_CHANNEL4_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM1_CHANNEL4_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM1_CHANNEL4_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL4_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM1_CHANNEL4_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL4_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM1 CHANNEL5 */
#define SPC5_GTM_ATOM1_USE_CHANNEL5                 TRUE
#define SPC5_GTM_ATOM1_CHANNEL5_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM1_CHANNEL5_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED
#define SPC5_GTM_ATOM1_CHANNEL5_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM1_CHANNEL5_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM1_CHANNEL5_PERIOD              0UL
#define SPC5_GTM_ATOM1_CHANNEL5_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM1_CHANNEL5_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM1_CHANNEL5_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL5_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM1_CHANNEL5_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL5_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM1 CHANNEL6 */
#define SPC5_GTM_ATOM1_USE_CHANNEL6                 TRUE
#define SPC5_GTM_ATOM1_CHANNEL6_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM1_CHANNEL6_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED
#define SPC5_GTM_ATOM1_CHANNEL6_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM1_CHANNEL6_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM1_CHANNEL6_PERIOD              0UL
#define SPC5_GTM_ATOM1_CHANNEL6_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM1_CHANNEL6_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM1_CHANNEL6_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL6_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM1_CHANNEL6_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL6_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM1 CHANNEL7 */
#define SPC5_GTM_ATOM1_USE_CHANNEL7                 TRUE
#define SPC5_GTM_ATOM1_CHANNEL7_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM1_CHANNEL7_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED
#define SPC5_GTM_ATOM1_CHANNEL7_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM1_CHANNEL7_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH
#define SPC5_GTM_ATOM1_CHANNEL7_PERIOD              0UL
#define SPC5_GTM_ATOM1_CHANNEL7_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM1_CHANNEL7_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM1_CHANNEL7_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL7_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM1_CHANNEL7_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM1_CHANNEL7_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ---- ---------------------- ---- */

/* ---- ATOM2 Settings ---- */
/* ATOM2 CHANNEL0 */
#define SPC5_GTM_ATOM2_USE_CHANNEL0                 TRUE
#define SPC5_GTM_ATOM2_CHANNEL0_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM2_CHANNEL0_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED // SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM2_CHANNEL0_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM2_CHANNEL0_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH // SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM2_CHANNEL0_PERIOD              0UL
#define SPC5_GTM_ATOM2_CHANNEL0_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM2_CHANNEL0_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM2_CHANNEL0_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL0_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM2_CHANNEL0_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL0_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM2 CHANNEL1 */
#define SPC5_GTM_ATOM2_USE_CHANNEL1                 TRUE
#define SPC5_GTM_ATOM2_CHANNEL1_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM2_CHANNEL1_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM2_CHANNEL1_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM2_CHANNEL1_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM2_CHANNEL1_PERIOD              0UL
#define SPC5_GTM_ATOM2_CHANNEL1_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM2_CHANNEL1_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM2_CHANNEL1_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL1_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM2_CHANNEL1_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL1_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM2 CHANNEL2 */
#define SPC5_GTM_ATOM2_USE_CHANNEL2                 TRUE
#define SPC5_GTM_ATOM2_CHANNEL2_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM2_CHANNEL2_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED // SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM2_CHANNEL2_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM2_CHANNEL2_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH // SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM2_CHANNEL2_PERIOD              0UL
#define SPC5_GTM_ATOM2_CHANNEL2_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM2_CHANNEL2_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM2_CHANNEL2_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL2_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM2_CHANNEL2_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL2_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM2 CHANNEL3 */
#define SPC5_GTM_ATOM2_USE_CHANNEL3                 TRUE
#define SPC5_GTM_ATOM2_CHANNEL3_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM2_CHANNEL3_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM2_CHANNEL3_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM2_CHANNEL3_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM2_CHANNEL3_PERIOD              0UL
#define SPC5_GTM_ATOM2_CHANNEL3_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM2_CHANNEL3_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM2_CHANNEL3_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL3_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM2_CHANNEL3_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL3_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM2 CHANNEL4 */
#define SPC5_GTM_ATOM2_USE_CHANNEL4                 TRUE
#define SPC5_GTM_ATOM2_CHANNEL4_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM2_CHANNEL4_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED // SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM2_CHANNEL4_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM2_CHANNEL4_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH // SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM2_CHANNEL4_PERIOD              0UL
#define SPC5_GTM_ATOM2_CHANNEL4_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM2_CHANNEL4_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM2_CHANNEL4_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL4_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM2_CHANNEL4_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL4_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM2 CHANNEL5 */
#define SPC5_GTM_ATOM2_USE_CHANNEL5                 TRUE
#define SPC5_GTM_ATOM2_CHANNEL5_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM2_CHANNEL5_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM2_CHANNEL5_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM2_CHANNEL5_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM2_CHANNEL5_PERIOD              0UL
#define SPC5_GTM_ATOM2_CHANNEL5_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM2_CHANNEL5_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM2_CHANNEL5_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL5_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM2_CHANNEL5_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL5_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM2 CHANNEL6 */
#define SPC5_GTM_ATOM2_USE_CHANNEL6                 TRUE
#define SPC5_GTM_ATOM2_CHANNEL6_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM2_CHANNEL6_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_SL_INVERTED // SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM2_CHANNEL6_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM2_CHANNEL6_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_HIGH // SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM2_CHANNEL6_PERIOD              0UL
#define SPC5_GTM_ATOM2_CHANNEL6_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM2_CHANNEL6_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM2_CHANNEL6_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL6_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM2_CHANNEL6_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL6_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM2 CHANNEL7 */
#define SPC5_GTM_ATOM2_USE_CHANNEL7                 TRUE
#define SPC5_GTM_ATOM2_CHANNEL7_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM2_CHANNEL7_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL 
#define SPC5_GTM_ATOM2_CHANNEL7_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM2_CHANNEL7_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW 
#define SPC5_GTM_ATOM2_CHANNEL7_PERIOD              0UL
#define SPC5_GTM_ATOM2_CHANNEL7_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM2_CHANNEL7_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM2_CHANNEL7_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL7_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM2_CHANNEL7_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM2_CHANNEL7_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ---- ---------------------- ---- */

/* ---- ATOM3 Settings ---- */
/* ATOM3 CHANNEL0 */
#define SPC5_GTM_ATOM3_USE_CHANNEL0                 TRUE
#define SPC5_GTM_ATOM3_CHANNEL0_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM3_CHANNEL0_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM3_CHANNEL0_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM3_CHANNEL0_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM3_CHANNEL0_CLK_SOURCE          SPC5_GTM_CMU_CLK5   
#define SPC5_GTM_ATOM3_CHANNEL0_PERIOD              32000UL
#define SPC5_GTM_ATOM3_CHANNEL0_DUTY_CYCLE          2500UL
/* Default settings */
#define SPC5_GTM_ATOM3_CHANNEL0_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM3_CHANNEL0_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL0_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM3_CHANNEL0_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL0_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL


/* ATOM3 CHANNEL1 */
#define SPC5_GTM_ATOM3_USE_CHANNEL1                 TRUE
#define SPC5_GTM_ATOM3_CHANNEL1_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM3_CHANNEL1_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM3_CHANNEL1_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM3_CHANNEL1_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM3_CHANNEL1_CLK_SOURCE          SPC5_GTM_CMU_CLK5   
#define SPC5_GTM_ATOM3_CHANNEL1_PERIOD              32000UL
#define SPC5_GTM_ATOM3_CHANNEL1_DUTY_CYCLE          2500UL
/* Default settings */
#define SPC5_GTM_ATOM3_CHANNEL1_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM3_CHANNEL1_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL1_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM3_CHANNEL1_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL1_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM3 CHANNEL2 */
#define SPC5_GTM_ATOM3_USE_CHANNEL2                 TRUE
#define SPC5_GTM_ATOM3_CHANNEL2_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM3_CHANNEL2_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM3_CHANNEL2_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM3_CHANNEL2_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM3_CHANNEL2_CLK_SOURCE          SPC5_GTM_CMU_CLK5   
#define SPC5_GTM_ATOM3_CHANNEL2_PERIOD              32000UL
#define SPC5_GTM_ATOM3_CHANNEL2_DUTY_CYCLE          2500UL
/* Default settings */
#define SPC5_GTM_ATOM3_CHANNEL2_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM3_CHANNEL2_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL2_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM3_CHANNEL2_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL2_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM3 CHANNEL3 */
#define SPC5_GTM_ATOM3_USE_CHANNEL3                 TRUE
#define SPC5_GTM_ATOM3_CHANNEL3_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM3_CHANNEL3_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM3_CHANNEL3_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM3_CHANNEL3_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM3_CHANNEL3_CLK_SOURCE          SPC5_GTM_CMU_CLK5   
#define SPC5_GTM_ATOM3_CHANNEL3_PERIOD              32000UL
#define SPC5_GTM_ATOM3_CHANNEL3_DUTY_CYCLE          2500UL
/* Default settings */
#define SPC5_GTM_ATOM3_CHANNEL3_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM3_CHANNEL3_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL3_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM3_CHANNEL3_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL3_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM3 CHANNEL4 */
#define SPC5_GTM_ATOM3_USE_CHANNEL4                 TRUE
#define SPC5_GTM_ATOM3_CHANNEL4_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM3_CHANNEL4_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM3_CHANNEL4_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM3_CHANNEL4_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM3_CHANNEL4_CLK_SOURCE          SPC5_GTM_CMU_CLK5   
#define SPC5_GTM_ATOM3_CHANNEL4_PERIOD              32000UL
#define SPC5_GTM_ATOM3_CHANNEL4_DUTY_CYCLE          2500UL
/* Default settings */
#define SPC5_GTM_ATOM3_CHANNEL4_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM3_CHANNEL4_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL4_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM3_CHANNEL4_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL4_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM3 CHANNEL5 */
#define SPC5_GTM_ATOM3_USE_CHANNEL5                 TRUE
#define SPC5_GTM_ATOM3_CHANNEL5_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM3_CHANNEL5_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM3_CHANNEL5_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM3_CHANNEL5_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM3_CHANNEL5_CLK_SOURCE          SPC5_GTM_CMU_CLK5   
#define SPC5_GTM_ATOM3_CHANNEL5_PERIOD              32000UL
#define SPC5_GTM_ATOM3_CHANNEL5_DUTY_CYCLE          2500UL
/* Default settings */
#define SPC5_GTM_ATOM3_CHANNEL5_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM3_CHANNEL5_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL5_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM3_CHANNEL5_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL5_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM3 CHANNEL6 */
#define SPC5_GTM_ATOM3_USE_CHANNEL6                 TRUE
#define SPC5_GTM_ATOM3_CHANNEL6_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM3_CHANNEL6_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM3_CHANNEL6_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM3_CHANNEL6_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM3_CHANNEL6_CLK_SOURCE          SPC5_GTM_CMU_CLK5   
#define SPC5_GTM_ATOM3_CHANNEL6_PERIOD              32000UL
#define SPC5_GTM_ATOM3_CHANNEL6_DUTY_CYCLE          2500UL
/* Default settings */
#define SPC5_GTM_ATOM3_CHANNEL6_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM3_CHANNEL6_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL6_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM3_CHANNEL6_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL6_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM3 CHANNEL7 */
#define SPC5_GTM_ATOM3_USE_CHANNEL7                 TRUE
#define SPC5_GTM_ATOM3_CHANNEL7_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE
/* Default settings */
#define SPC5_GTM_ATOM3_CHANNEL7_ARU_MODE            SPC5_GTM_ATOM_ARU_ACB_OUTPUT_TO_SL
#define SPC5_GTM_ATOM3_CHANNEL7_ARU_ENABLE          TRUE
#define SPC5_GTM_ATOM3_CHANNEL7_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM3_CHANNEL7_PERIOD              0UL
#define SPC5_GTM_ATOM3_CHANNEL7_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM3_CHANNEL7_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM3_CHANNEL7_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL7_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM3_CHANNEL7_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM3_CHANNEL7_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ---- ---------------------- ---- */

/* ---- ATOM4 Settings ---- */
/* ATOM4 CHANNEL0 */
#define SPC5_GTM_ATOM4_USE_CHANNEL0                 FALSE
#define SPC5_GTM_ATOM4_CHANNEL0_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM4_CHANNEL0_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM4_CHANNEL0_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM4_CHANNEL0_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM4_CHANNEL0_PERIOD              0UL
#define SPC5_GTM_ATOM4_CHANNEL0_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM4_CHANNEL0_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM4_CHANNEL0_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL0_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM4_CHANNEL0_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL0_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM4 CHANNEL1 */
#define SPC5_GTM_ATOM4_USE_CHANNEL1                 FALSE
#define SPC5_GTM_ATOM4_CHANNEL1_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM4_CHANNEL1_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM4_CHANNEL1_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM4_CHANNEL1_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM4_CHANNEL1_PERIOD              0UL
#define SPC5_GTM_ATOM4_CHANNEL1_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM4_CHANNEL1_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM4_CHANNEL1_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL1_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM4_CHANNEL1_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL1_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM4 CHANNEL2 */
#define SPC5_GTM_ATOM4_USE_CHANNEL2                 FALSE
#define SPC5_GTM_ATOM4_CHANNEL2_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM4_CHANNEL2_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM4_CHANNEL2_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM4_CHANNEL2_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM4_CHANNEL2_PERIOD              0UL
#define SPC5_GTM_ATOM4_CHANNEL2_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM4_CHANNEL2_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM4_CHANNEL2_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL2_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM4_CHANNEL2_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL2_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM4 CHANNEL3 */
#define SPC5_GTM_ATOM4_USE_CHANNEL3                 FALSE
#define SPC5_GTM_ATOM4_CHANNEL3_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM4_CHANNEL3_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM4_CHANNEL3_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM4_CHANNEL3_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM4_CHANNEL3_PERIOD              0UL
#define SPC5_GTM_ATOM4_CHANNEL3_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM4_CHANNEL3_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM4_CHANNEL3_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL3_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM4_CHANNEL3_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL3_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM4 CHANNEL4 */
#define SPC5_GTM_ATOM4_USE_CHANNEL4                 FALSE
#define SPC5_GTM_ATOM4_CHANNEL4_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM4_CHANNEL4_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM4_CHANNEL4_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM4_CHANNEL4_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM4_CHANNEL4_PERIOD              0UL
#define SPC5_GTM_ATOM4_CHANNEL4_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM4_CHANNEL4_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM4_CHANNEL4_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL4_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM4_CHANNEL4_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL4_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM4 CHANNEL5 */
#define SPC5_GTM_ATOM4_USE_CHANNEL5                 FALSE
#define SPC5_GTM_ATOM4_CHANNEL5_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM4_CHANNEL5_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM4_CHANNEL5_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM4_CHANNEL5_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM4_CHANNEL5_PERIOD              0UL
#define SPC5_GTM_ATOM4_CHANNEL5_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM4_CHANNEL5_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM4_CHANNEL5_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL5_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM4_CHANNEL5_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL5_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM4 CHANNEL6 */
#define SPC5_GTM_ATOM4_USE_CHANNEL6                 FALSE
#define SPC5_GTM_ATOM4_CHANNEL6_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM4_CHANNEL6_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM4_CHANNEL6_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM4_CHANNEL6_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM4_CHANNEL6_PERIOD              0UL
#define SPC5_GTM_ATOM4_CHANNEL6_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM4_CHANNEL6_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM4_CHANNEL6_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL6_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM4_CHANNEL6_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL6_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM4 CHANNEL7 */
#define SPC5_GTM_ATOM4_USE_CHANNEL7                 FALSE
#define SPC5_GTM_ATOM4_CHANNEL7_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM4_CHANNEL7_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM4_CHANNEL7_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM4_CHANNEL7_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM4_CHANNEL7_PERIOD              0UL
#define SPC5_GTM_ATOM4_CHANNEL7_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM4_CHANNEL7_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM4_CHANNEL7_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL7_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM4_CHANNEL7_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM4_CHANNEL7_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ---- ---------------------- ---- */

/* ---- ATOM5 Settings ---- */
/* ATOM5 CHANNEL0 */
#define SPC5_GTM_ATOM5_USE_CHANNEL0                 FALSE
#define SPC5_GTM_ATOM5_CHANNEL0_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM5_CHANNEL0_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM5_CHANNEL0_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM5_CHANNEL0_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM5_CHANNEL0_PERIOD              0UL
#define SPC5_GTM_ATOM5_CHANNEL0_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM5_CHANNEL0_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM5_CHANNEL0_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL0_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM5_CHANNEL0_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL0_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM5 CHANNEL1 */
#define SPC5_GTM_ATOM5_USE_CHANNEL1                 FALSE
#define SPC5_GTM_ATOM5_CHANNEL1_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM5_CHANNEL1_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM5_CHANNEL1_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM5_CHANNEL1_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM5_CHANNEL1_PERIOD              0UL
#define SPC5_GTM_ATOM5_CHANNEL1_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM5_CHANNEL1_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM5_CHANNEL1_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL1_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM5_CHANNEL1_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL1_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM5 CHANNEL2 */
#define SPC5_GTM_ATOM5_USE_CHANNEL2                 FALSE
#define SPC5_GTM_ATOM5_CHANNEL2_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM5_CHANNEL2_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM5_CHANNEL2_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM5_CHANNEL2_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM5_CHANNEL2_PERIOD              0UL
#define SPC5_GTM_ATOM5_CHANNEL2_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM5_CHANNEL2_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM5_CHANNEL2_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL2_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM5_CHANNEL2_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL2_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM5 CHANNEL3 */
#define SPC5_GTM_ATOM5_USE_CHANNEL3                 FALSE
#define SPC5_GTM_ATOM5_CHANNEL3_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM5_CHANNEL3_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM5_CHANNEL3_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM5_CHANNEL3_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM5_CHANNEL3_PERIOD              0UL
#define SPC5_GTM_ATOM5_CHANNEL3_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM5_CHANNEL3_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM5_CHANNEL3_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL3_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM5_CHANNEL3_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL3_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM5 CHANNEL4 */
#define SPC5_GTM_ATOM5_USE_CHANNEL4                 FALSE
#define SPC5_GTM_ATOM5_CHANNEL4_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM5_CHANNEL4_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM5_CHANNEL4_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM5_CHANNEL4_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM5_CHANNEL4_PERIOD              0UL
#define SPC5_GTM_ATOM5_CHANNEL4_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM5_CHANNEL4_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM5_CHANNEL4_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL4_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM5_CHANNEL4_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL4_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM5 CHANNEL5 */
#define SPC5_GTM_ATOM5_USE_CHANNEL5                 FALSE
#define SPC5_GTM_ATOM5_CHANNEL5_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM5_CHANNEL5_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM5_CHANNEL5_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM5_CHANNEL5_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM5_CHANNEL5_PERIOD              0UL
#define SPC5_GTM_ATOM5_CHANNEL5_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM5_CHANNEL5_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM5_CHANNEL5_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL5_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM5_CHANNEL5_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL5_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM5 CHANNEL6 */
#define SPC5_GTM_ATOM5_USE_CHANNEL6                 FALSE
#define SPC5_GTM_ATOM5_CHANNEL6_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM5_CHANNEL6_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM5_CHANNEL6_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM5_CHANNEL6_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM5_CHANNEL6_PERIOD              0UL
#define SPC5_GTM_ATOM5_CHANNEL6_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM5_CHANNEL6_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM5_CHANNEL6_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL6_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM5_CHANNEL6_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL6_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ATOM5 CHANNEL7 */
#define SPC5_GTM_ATOM5_USE_CHANNEL7                 FALSE
#define SPC5_GTM_ATOM5_CHANNEL7_OUTPUT_MODE         SPC5_GTM_ATOM_OUTPUT_MODE_PWM
#define SPC5_GTM_ATOM5_CHANNEL7_ARU_MODE            SPC5_GTM_ATOM_ARU_MODE_NONE
#define SPC5_GTM_ATOM5_CHANNEL7_ARU_ENABLE          FALSE
#define SPC5_GTM_ATOM5_CHANNEL7_SIGNAL_LEVEL        SPC5_GTM_ATOM_SIGNAL_LEVEL_LOW
#define SPC5_GTM_ATOM5_CHANNEL7_PERIOD              0UL
#define SPC5_GTM_ATOM5_CHANNEL7_DUTY_CYCLE          0UL
#define SPC5_GTM_ATOM5_CHANNEL7_IRQ_MODE            SPC5_GTM_ATOM_IRQ_MODE_LEVEL
#define SPC5_GTM_ATOM5_CHANNEL7_INT_CCU0_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL7_INT_CCU0_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL
#define SPC5_GTM_ATOM5_CHANNEL7_INT_CCU1_ENABLED    FALSE
#define SPC5_GTM_ATOM5_CHANNEL7_INT_CCU1_MODE       SPC5_GTM_ATOM_INT_MODE_NORMAL

/* ---- ---------------------- ---- */

/*lint +e621*/
#endif /* _GTM_ATOM_CFG_H_ */
/** @} */
