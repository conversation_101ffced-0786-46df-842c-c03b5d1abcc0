/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      CoilTimPattern.h
 **  Date:          19-Jul-2021
 **
 **  Model Version: 1.937
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_CoilTimPattern_h_
#define RTW_HEADER_CoilTimPattern_h_
#ifndef CoilTimPattern_COMMON_INCLUDES_
# define CoilTimPattern_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* CoilTimPattern_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void CoilTimPattern_initialize(void);

/* Exported entry point function */
extern void CoilTimPattern_10ms(void);

/* Exported entry point function */
extern void CoilTimPattern_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern int16_T DegTOutPulseIn;         /* '<S3>/Merge8' */

/* Timeout pulse in */
extern int16_T TDbMKW;                 /* '<S3>/Merge9' */

/* Dead band MKW */
extern uint16_T TDbNPMOS;              /* '<S3>/Merge2' */

/* Dead band NMOS Off to PMOS On */
extern uint16_T TDbShot;               /* '<S3>/Merge1' */

/* Dead band to evaluate Shot */
extern uint16_T TPlaSample;            /* '<S3>/Merge' */

/* High frequency current time sampe */
extern uint16_T ThLeadDiag;            /* '<S3>/Merge3' */

/* Threshold to primary diagnosis */
extern uint16_T ToLstSlope;            /* '<S3>/Merge6' */

/* Buck last slopetimeout */
extern uint16_T ToPlaOn;               /* '<S3>/Merge7' */

/* Plasma threshold timeout */
extern uint16_T ToShotOut;             /* '<S3>/Merge4' */

/* TimeOut Shot threshold out */
extern uint16_T ToSlope;               /* '<S3>/Merge5' */

/* Buck slopetimeout */


/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CoilTimPattern'
 * '<S1>'   : 'CoilTimPattern/CoilTimPattern_Scheduler'
 * '<S2>'   : 'CoilTimPattern/Init_fcn'
 * '<S3>'   : 'CoilTimPattern/Merge'
 * '<S4>'   : 'CoilTimPattern/fcn_10ms'
 * '<S5>'   : 'CoilTimPattern/fcn_10ms/Calc_TOutPulseIn'
 */

/*-
 * Requirements for '<Root>': CoilTimPattern
 */
#endif                                 /* RTW_HEADER_CoilTimPattern_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/