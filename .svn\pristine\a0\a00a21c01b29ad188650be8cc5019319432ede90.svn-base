/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      IonMisf_private.h
 **  Date:          07-Nov-2022
 **
 **  Model Version: 1.1137
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_IonMisf_private_h_
#define RTW_HEADER_IonMisf_private_h_
#include "rtwtypes.h"
#include "IonMisf_out.h"

/* Includes for objects with custom storage classes. */
#include "RecMgm_out.h"
#include "MisfThrMgm_out.h"
#include "KnockCorrNom_out.h"
#include "IonIntMgm_out.h"
#include "IonAcqBufMgm_out.h"
#include "Ionacq_out.h"
#include "Msparkcmd_out.h"
#include "IonPhaseMgm_out.h"
/* Includes for extra files. */
#include "ETPU_EngineDefs.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/
extern void IonMisf_TDC_fcn(void);
extern void IonMisf_IonMisf_sched_Init(void);
extern void IonMisf_IonMisf_sched(int32_T controlPortIdx);

#endif                                 /* RTW_HEADER_IonMisf_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/