/*
 * File: SparkPlugTest_eep.c
 *
 * Code generated for Simulink model 'SparkPlugTest'.
 *
 * Model version                  : 1.959
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Fri Apr 23 07:29:37 2021
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor->Custom Processor
 * Emulation hardware selection:
 *    Differs from embedded hardware (Intel->x86-64 (Windows64))
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. ROM efficiency
 *    5. RAM efficiency
 *    6. Traceability
 *    7. Debugging
 *    8. Polyspace
 * Validation result: Passed (31), Warning (1), Error (0)
 */

#include "rtwtypes.h"

/* Exported data definition */

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_EE_INTERFACE */
uint16_T SparkPlugFaultCntEE[8] = { ((uint16_T)0U), ((uint16_T)0U), ((uint16_T)
  0U), ((uint16_T)0U), ((uint16_T)0U), ((uint16_T)0U), ((uint16_T)0U),
  ((uint16_T)0U) };                    /* '<S12>/Merge1' */

/* Fouled Spark Plug Fault global counter */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */