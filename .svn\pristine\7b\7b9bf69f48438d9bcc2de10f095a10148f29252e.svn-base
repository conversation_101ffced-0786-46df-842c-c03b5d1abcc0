/*****************************************************************************************************************/
/* $HeadURL::                                                                                                  $ */
/* $Revision::                                                                                                 $ */
/* $Date::                                                                                                     $ */
/* $Author::                                                                                                   $ */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  ADC
**  Filename        :  Adc.h
**  Created on      :  20-lug-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifndef _ADC_H_
#define _ADC_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "sys.h"
#include "Timing_out.h"
#include "Adc_out.h"
#include "OS_resources.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* Set to test timeout */
#define SAMPLE_TIMEOUT      FALSE

/* Number of the SD channels */
#define NUM_CHANNELS        200U

/* SD Engine */
#define SD_ENGINE_0        0u
#define SD_ENGINE_3        1u

/* SAR Engine */
#define SAR_ENGINE_0       0u
#define SAR_ENGINE_2       1u
#define SAR_ENGINE_4       2u
#define SAR_ENGINE_6       3u
#define SAR_ENGINE_SV      4u

/* SAR Total Channels */
#define SAR0_ALL_CHANNELS      9u
#define SAR2_ALL_CHANNELS      6u
#define SAR4_ALL_CHANNELS      5u
#define SAR6_ALL_CHANNELS     10u
#define SARSV_ALL_CHANNELS    (SAR0_ALL_CHANNELS + SAR2_ALL_CHANNELS + SAR4_ALL_CHANNELS + SAR6_ALL_CHANNELS)

 /*SAR Number of Analog Channels*/
#define SAR_INT_CHANNEL_NMB               96U
#define SAR_TEST_CHANNEL_NMB              32U

/* Driver constants */
#define SARADC_ENABLED                     1U
#define SARADC_DISABLED                    0U
#define SARADC_MODE_ONESHOT                0U
#define SARADC_MODE_SCAN                   1U
#define SARADC_OW_DISABLED                 0U
#define SARADC_OW_ENABLED                  1U
#define SARADC_CHANNEL_INTERNAL            0U
#define SARADC_CHANNEL_EXTERNAL            1U
#define SARADC_CHANNEL_TEST                2U
#define SARADC_REFERENCE_DEFAULT           0U
#define SARADC_REFERENCE_ALTERNATE         1U
#define SARADC_PRECHARGE_DISABLED          0U
#define SARADC_PRECHARGE_ENABLED           1U
#define SARADC_CTR0                        0U
#define SARADC_CTR1                        1U
#define SARADC_CTR2                        2U
#define SARADC_CTR3                        3U
#define SARADC_WATCHDOG_REGISTER_0         0U
#define SARADC_WATCHDOG_REGISTER_1         1U
#define SARADC_WATCHDOG_REGISTER_2         2U
#define SARADC_WATCHDOG_REGISTER_3         3U
#define SARADC_WATCHDOG_REGISTER_NONE      0xFFU
#define SARADC_CRES_HIGH                   0U
#define SARADC_CRES_LOW                    1U
#define SARADC_DMA_BUFFER_LINEAR           0U
#define SARADC_DMA_BUFFER_CIRCULAR         1U
#define SARADC_TRIGGER_DISABLED         0xFFU
#define SARADC_TRIGGER_ENABLED             1U
#define SARADC_TRIGGER_FALLING_EDGE        0U
#define SARADC_TRIGGER_RISING_EDGE         1U
#define SARADC_TRIGGER_BOTH_EDGES          3U
#define SARADC_PRECHG                      0U
#define SARADC_INPSAMP_85                 85U
#define SARADC_INPSAMP_100               100U
#define SARADC_VALID_DATA                  0
#define SARADC_INVALID_DATA                1
#define SARADC_VALID_DATA_AFTER_TIMEOUT    2
#define SARADC_DMA_ENABLED                1U
#define SARADC_DMA_DISABLED               0U
#define SARADC_ISR_DISABLED               0U
#define SARADC_ISR_EOC_CHAIN              1U
#define SARADC_ISR_EOC_CHANNEL            2U
#define SARADC_ISR_EOC_JCHAIN             4U
#define SARADC_ISR_EOC_JCHANNEL           8U

/* SAR ICIMR/ICNCMR configuration - Specific for K2 */
#define SAR0_ICMR_NUM                    1U
#define SAR2_ICMR_NUM                    1U
#define SAR4_ICMR_NUM                    1U
#define SAR6_ICMR_NUM                    1U
#define SARB_ICMR_NUM                    2U
#define SAR0_ICMR_ID                     0U
#define SAR2_ICMR_ID                     0U
#define SAR4_ICMR_ID                     1U
#define SAR6_ICMR_ID                     1U

#define SARADC_UNUSED_CHANNEL           0xFFu
#define SARADC_DMA_UNUSED_CHANNEL       0xFFu

/* SDADC constant definitions */
#define SDADC_ENABLED                     1U
#define SDADC_DISABLED                    0U
#define SDADC_VREFP                       5U
#define SDADC_VREFN                       0U
#define SDADC_WATCHDOG_LOWTH              1U
#define SDADC_WATCHDOG_HIGHTH             4U
#define SDADC_MODE_DIFFERENTIAL           0U
#define SDADC_MODE_SINGLE                 1U
#define SDADC_VCOMSEL_VREFN               0U
#define SDADC_VCOMSEL_VREFPHALF           1U
#define SDADC_ANCHSEL(x)                 (x)
#define SDADC_OSD_18US                    6U
#define SDADC_OSD_27US                    9U
#define SDADC_OSD_36US                   12U
#define SDADC_OSD_45US                   15U
#define SDADC_OSD_54US                   18U
#define SDADC_OSD_63US                   21U
#define SDADC_OSD_75US                   25U
#define SDADC_PDR_OSR_24                  0U
#define SDADC_PDR_OSR_28                  1U
#define SDADC_PDR_OSR_32                  2U
#define SDADC_PDR_OSR_36                  3U
#define SDADC_PDR_OSR_40                  4U
#define SDADC_PDR_OSR_44                  5U
#define SDADC_PDR_OSR_48                  6U
#define SDADC_PDR_OSR_56                  7U
#define SDADC_PDR_OSR_64                  8U
#define SDADC_PDR_OSR_72                  9U
#define SDADC_PDR_OSR_75                 10U
#define SDADC_PDR_OSR_80                 11U
#define SDADC_PDR_OSR_88                 12U
#define SDADC_PDR_OSR_96                 13U
#define SDADC_PDR_OSR_112                14U
#define SDADC_PDR_OSR_128                15U
#define SDADC_PDR_OSR_144                16U
#define SDADC_PDR_OSR_160                17U
#define SDADC_PDR_OSR_176                18U
#define SDADC_PDR_OSR_192                19U
#define SDADC_PDR_OSR_224                20U
#define SDADC_PDR_OSR_256                21U
#define SDADC_GAIN_1                      0U
#define SDADC_GAIN_2                      1U
#define SDADC_GAIN_4                      2U
#define SDADC_GAIN_8                      3U
#define SDADC_GAIN_16                     7U
#define SDADC_HPASS_DISABLED              0U
#define SDADC_HPASS_ENABLED               1U
#define SDADC_OFFSET_CALIBRATION          1U
#define SDADC_GAIN_CALIBRATION            2U
#define SDADC_BOTH_CALIBRATION           SDADC_OFFSET_CALIBRATION | SDADC_GAIN_CALIBRATION
#define SDADC_CALIBRATION(x)              (x)
#define SDADC_BIAS_DISABLED	              0U
#define SDADC_BIAS_ENABLED                1U
#define SDADC_DATAOUT_UNSIGNED            0U
#define SDADC_DATAOUT_SIGNED              1U
#define SDADC_FIFO_FALSE                  0U
#define SDADC_FIFO_TRUE                   1U
#define SDADC_FIFO_1_BYTE                 0U
#define SDADC_FIFO_4_BYTE                 1U
#define SDADC_FIFO_8_BYTE                 2U
#define SDADC_FIFO_16_BYTE                3U
#define SDADC_FIFO_THRESHOLD_0            0U
#define SDADC_FIFO_THRESHOLD_1            1U
#define SDADC_FIFO_THRESHOLD_2            2U
#define SDADC_FIFO_THRESHOLD_3            3U
#define SDADC_FIFO_THRESHOLD_4            4U
#define SDADC_FIFO_THRESHOLD_5            5U
#define SDADC_FIFO_THRESHOLD_6            6U
#define SDADC_FIFO_THRESHOLD_7            7U
#define SDADC_FIFO_THRESHOLD_8            8U
#define SDADC_FIFO_THRESHOLD_9            9U
#define SDADC_FIFO_THRESHOLD_10          10U
#define SDADC_FIFO_THRESHOLD_11          11U
#define SDADC_FIFO_THRESHOLD_12          12U
#define SDADC_FIFO_THRESHOLD_13          13U
#define SDADC_FIFO_THRESHOLD_14          14U
#define SDADC_FIFO_THRESHOLD_15          15U
#define SDADC_FIFO_OVERWRITE_DISABLED     0U
#define SDADC_FIFO_OVERWRITE_ENABLED      1U
#define SDADC_FIFO_FULL_EVENT_DISABLED    0U
#define SDADC_FIFO_FULL_EVENT_ENABLED     1U
#define SDADC_FIFO_FULL_EVENT_ISR         0U
#define SDADC_FIFO_FULL_EVENT_DMA         1U
#define SDADC_FIFO_OVERRUN_EVENT_DISABLED 0U
#define SDADC_FIFO_OVERRUN_EVENT_ENABLED  1U
#define SDADC_WATCHDOG_EVENT_DISABLED     0U
#define SDADC_WATCHDOG_EVENT_ENABLED      1U
#define SDADC_DATA_VALID                  0
#define SDADC_DATA_NOTVALID               1
#define SDADC_DATA_VALID_AFTER_TIMEOUT    2
#define SDADC_OFFSET_CALIB_ANCH           4U
#define SDADC_GAIN_CALIB_PH1_ANCH         6U
#define SDADC_GAIN_CALIB_PH2_ANCH         7U
#define SDADC_HW_TRIGGER_DISABLED         0U
#define SDADC_HW_TRIGGER_ENABLED          1U
#define SDADC_HW_TRIGGER_SOURCE_SW0       0U
#define SDADC_HW_TRIGGER_SOURCE_SW3       3U
#define SDADC_HW_TRIGGER_SOURCE_GTM       4U
#define SDADC_HW_TRIGGER_FALLING_EDGE     0U
#define SDADC_HW_TRIGGER_RISING_EDGE      1U
#define SDADC_HW_TRIGGER_BOTH_EDGE        2U

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* Structure of a single ADC port */
typedef struct {
    uint8_T peripheral;
    uint8_T perNumber;
    uint16_T analogChannel;
} ADCChannel_T;

/* Configuration structure (SD only) */
typedef struct {
    uint8_T sdMode;
    uint8_T sdVcomsel;
    uint8_T sdGain;
    uint8_T sdPdr;
    uint8_T sdHpassEn;
    uint8_T sdBiasEn;
    uint8_T sdWdgEn;
    uint8_T sdFifoEn;
    uint8_T sdFifoFullEventEn;
    uint8_T sdFifoFullEventType;
    uint8_T sdFifoFullThres;
    uint8_T sdFifoOwEn;
    uint8_T sdTrigEn;
    uint8_T sdTrigSource;
    uint8_T sdTrigEdge;
} SDConfig_T;

/* Configuration structure (SAR only) */
typedef struct {
    uint8_T sarMode;
    uint8_T sarOwrEn;
    uint8_T sarIsrEn;
    uint8_T sarTriggerEn;
    uint8_T sarTriggerSel;
    uint8_T sarDmaEn;
    uint8_T sarDmaChNum;
    uint8_T sarCtrlCres;
    uint8_T sarCtrlPrechg;
    uint8_T sarCtrlInpsamp;
} SARConfig_T;

typedef volatile struct SDADC_tag * SDPtr_T;
typedef volatile struct SARADC_tag * SARPtr_T;
/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/


/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static void ADC_CalibrationSet (uint8_T channel);
static float_T SDADC_OffsetCalib (uint8_T channel);
static float_T SDADC_GainCalib (uint8_T channel);
static void SDADC_Config (uint8_T channel);
static void SARADC_Config (uint8_T channel);
static void SARADC_ConfigAnCh (uint8_T channel);
static void SARADC_StartConversion (uint8_T channel);
static int16_T SDADC_Read (uint8_T channel, uint16_T* convertedValue);
static int16_T SARADC_Read (uint8_T channel, uint16_T anCh, uint16_T* convertedValue);
static void SAR0_ConfigScanCh (void);
static void SAR2_ConfigScanCh (void);
static void SAR4_ConfigScanCh (void);
static void SAR6_ConfigScanCh (void);
static void SARSV_ConfigScanCh (void);
static void SAR0_DMAConfig (void);
static void SAR2_DMAConfig (void);
static void SAR4_DMAConfig (void);
static void SAR6_DMAConfig (void);
static void SARSV_DMAConfig (void);

#endif /* _ADC_H_ */

/****************************************************************************
 ****************************************************************************/
