/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB6C/Appl/branches/EISB6C_EZ_04_EEPROM/tree/DD/COMM#$  */
/* $Revision:: 161389                                                                                         $  */
/* $Date:: 2021-05-14 18:04:04 +0200 (ven, 14 mag 2021)                                                       $  */
/* $Author:: GirasoleG                                                                                        $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DiagCanMgm
**  Filename        :  DiagCanMgm_out.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  MocciA
******************************************************************************/
/*****************************************************************************
**
**                        DiagCanMgm Description
**
**  This SWC takes care of both Diagnostic Request Message Reception and Response Message Transmission using
**  a Client (Tester) - Server (ECU) architecture.
******************************************************************************/
#ifndef __DIAGCANMGM_OUT_H__
#define __DIAGCANMGM_OUT_H__

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "typedefs.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/// Diagnostic signal/state ON
#define DIAG_ON                                    (1u)
/// Diagnostic signal/state OFF
#define DIAG_OFF                                   (0u)
/// Server Diagnostic Response not required (SPRMIB != 0)
#define NO_RESPONSE                                (0u)
/// Server Diagnostic Response required (SPRMIB == 0)
#define SEND_RESPONSE                              (1u)
/// Suspend Positive Response Message Identification Bit Mask
#define SPRMIB_MASK                                0x80u


/* UDS Diagnostic Service Identifiers (SIDs) : */
/// DiagnosticSessionControl SID
#define UDS_SERVICE_START_SESSION                  0x10u
/// ECU Reset SID
#define UDS_SERVICE_ECU_RESET                      0x11u
/// ClearDiagnosticInformation SID
#define UDS_SERVICE_CLEAR_DIAGNOSTIC_INFORMATION   0x14u
/// ReadDTCInformation SID
#define UDS_SERVICE_READ_DTC_INFO                  0x19u
/// ReadDataByIdentifier SID
#define UDS_SERVICE_READ_DATA_BY_ID                0x22u
/// ReadMmoeryByAddress SID
#define UDS_SERVICE_READ_MEM_BY_ADDR               0x23u
/// SecurityAccess SID
#define UDS_SERVICE_SECURITY_ACCESS                0x27u
/// CommunicationControl SID
#define UDS_SERVICE_COMM_CONTROL                   0x28u
/// WriteDatByIdentifier SID
#define UDS_SERVICE_WRITE_DATA_BY_ID               0x2Eu
/// InputOutputControl SID
#define UDS_SERVICE_IOCONTROL                      0x2Fu
/// RoutineControl SID
#define UDS_SERVICE_ROUTINE_CONTROL                0x31u
/// RequestDownload SID
#define UDS_SERVICE_REQUEST_DOWNLOAD               0x34u
/// TransferData SID
#define UDS_SERVICE_TRANSFER_DATA                  0x36u
/// TransferDataExit SID
#define UDS_SERVICE_REQUEST_TRANSFER_EXIT          0x37u
/// WriteMemoryByAddress SID
#define UDS_SERVICE_WRITE_MEM_BY_ADDR              0x3Du
/// TesterPresent SID
#define UDS_SERVICE_TESTER_PRESENT                 0x3Eu
/// ControlDTCSetting SID
#define UDS_SERVICE_CTRL_DTC_SETTING               0x85u

/* UDS Diagnostic Session List*/
/// Default
#define DIAG_DEFAULT_SESSION                       0x01u
/// Reprogramming
#define DIAG_ECUPROGR_SESSION                      0x02u
/// Extended
#define DIAG_EXTENDED_SESSION                      0x03u
/// Manufacture
#define DIAG_MANUFACTURE_EOL_SESSION               0x40u
/// Supplier - EOL
#define DIAG_SUPPLIER_SESSION                      0x60u

/// Diagnostic request received from tester, same value of DIAGNOSE_REQUEST from the TPE level
#define DIAG_MESSAGE_RECEIVED                      0x04u

/// Serviece ECU_Reset defines
#define MCU_RESET_POR                              (1u)
#define MCU_RESET_KEYOFFON                         (2u)
#define MCU_RESET_REQ_SIZE                         (2u)     // request size
#define MCU_RESET_RESP_SIZE                        (2u)     // response size

/// Security Timeout after ECU startup
#if defined (ECU_LOCKED_ON_STARTUP)
#define SEC_TIMEOUT_INIT                           SEC_TIMEOUT_CUSTOMER
#else
#define SEC_TIMEOUT_INIT                           (0u)
#endif

/// Security unclock timeout; once expired, a new security access attempt is allowed
#define SEC_TIMEOUT                                (SEC_TIMEOUT_CUSTOMER)


/// Negative Response codes
#define GENERAL_REJECT                             (0x10u)
#define SERVICE_NOT_SUPPORTED                      (0x11u)
#define SUBFUNCTION_NOT_SUPPORTED                  (0x12u)
#define INCORRECT_MESSAGE_LENGTH                   (0x13u)
#define RESPONSE_TOO_LONG                          (0x14u)
#define BUSY_REPEAT_REQUEST                        (0x21u)
#define CONDITIONS_NOT_CORRECT                     (0x22u)
#define ROUTINE_NOT_COMPLETE                       (0x23u)
#define REQUEST_SEQUENCE_ERROR                     (0x24u)
#define REQUEST_OUT_OF_RANGE                       (0x31u)
#define SECURITY_ACCESS_DENIED                     (0x33u)
#define SECURITY_ACCESS_ALLOWED                    (0x34u)
#define KEY_NOT_VALID                              (0x35u)
#define EXCEEDED_NO_OF_ATTEMPTS                    (0x36u)
#define REQ_TIMEOUT_NOT_EXPIRED                    (0x37u)
#define DOWNLOAD_NOT_ACCEPTED                      (0x70u)
#define CANNOT_DWNLD_SPECIF_ADDR                   (0x42u)
#define CANNOT_DWNLD_NUM_BYTE_REQ                  (0x43u)
#define TRANSFER_DATA_SUSPENDED                    (0x71u)
#define GENERAL_PROGRAMMING_FAILURE                (0x72u)
#define WRONG_BLOCK_SEQUENCE_NUM                   (0x73u)
#define BLOCK_CHECKSUM_ERROR                       (0x77u)
#define REQCORRETLYRCVD                            (0x78u)
#define SUBFUNC_NOT_SUPPORTED_IN_ACTIVE_SESSION    (0x7Eu)
#define NEGATIVE_RESPONSE_SERVICE_ID               (0x7Fu)
#define SERVICE_NOT_SUPPORTED_IN_CURRENT_MODE      (0x7Fu)

#if 0
/// Flash related define
#define SINGLE_PROGRAM_SIZE                        C90FL_PAGE_SIZE_16 //   0x10u //FLASH_PAGE_SIZE // use FLASH page size
/// Number of bytes per $36 Service Request received
#define WORD_COUNT_MAX                             0x400u
/// Server download capabilities in service $34
#define RESPONSEDOWNLOAD                           (WORD_COUNT_MAX + 2u) //blockSequenceCounter byte is not used by KWP2000 
#endif

/// Dignostic RX buffers size
#define RXBUFF_SIZE                                (MAX_TP_MSG_SIZE)
/// Dignostic TX buffers size
#define TXBUFF_SIZE                                ((262u)-5u)
/// Session Layer Ok
#define DIAGCANMGM_SUCCESS                         0x00u
/// Session Layer NOT Ok
#define DIAGCANMGM_NOTSUCCESS                      0x01u
/// Session Timeout error
#define DIAGCANMGM_ERROR_TIMEOUT                   0x02u
/// Diagnostic request pending
#define DIAGCANMGM_PENDING                         0x03u
/// P3 server error timeout
#define DIAGCANMGM_ERROR_TIMEOUT_P3                0x0fu

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/// Decoding struct
typedef struct DecAnswer
{
    uint8_T message;               // received message type
    uint8_T message_session;       // diagnostic session to which received message belongs
} DecAnswer_T;

/// Request Download Transfer parameters
typedef struct message_download_struct
{
    uint32_T  memoryAddress;
    uint8_T   dataFormatIdentifier;
    uint32_T  unCompressedMemorySize;
    uint8_T   addressAndLengthFormatIdentifier;
} message_download_struct_T;

/// Download structure used to exhange data between boot and application level
#pragma ghs startnomisra // MISRA 2004 Rule 18.4: union not allowed
union DownloadStruct_tag
{
    uint32_T CF;  // Complete Field
    struct
    {
        uint8_T negativeResponse     :1;
        uint8_T transferStatus       :1;
        uint8_T secAccessSupplOK     :1;
        uint8_T secAccessExtendedOK  :1;
        uint8_T secAccessECUProgOK   :1;
        uint8_T secAccessManEolOK    :1;
        uint8_T writeTesterCode      :1;
        uint8_T writeProgrammingDate :1;
        uint8_T requestDownload      :1;
        uint8_T sessionDownloadActive:1;
        uint8_T erasedBlock          :1;
        uint8_T checkSum             :1;
        uint8_T DownloadComplete     :1;
        uint8_T NumberOfAttempts     :2;
        uint8_T serviceInProgress    :1;
        uint8_T stopAnswering        :1;
        uint8_T downloadInProgress   :1;
        uint8_T ResetResponseBoot    :1;
        uint8_T ResetResponseAppl    :1;
        uint8_T JumpToBootAndWait    :1;
        uint8_T JumpToApplAndWait    :1;
    }BF;     // Bit Field
}; // __attribute__ ((packed));  <-- AM problem using this attribute with GHS 5.1.7, needs to be treated with pointer to __packed struct
#pragma ghs endnomisra
/// Flash Erase request parameters
typedef struct 
{
    uint32_T          startingAddress;
    uint32_T          size;
    void              (*callback)(void);
} taskFlashEraseParams_t;  // __attribute__ ((packed));  <-- AM problem using this attribute with GHS 5.1.7, needs to be treated with pointer to __packed struct

/// Flash Checksum request parameters
typedef struct {
    uint32_T checkSumStartAddress; // start checksum address
    uint32_T checkSumEndAddress;   // end checksum address
    uint16_T checksum;               // Checksum storage
    uint16_T init_crc;
    uint8_T  *data_ptr; //  crc pointer
    uint32_T data_size; //  crc size
} taskCheckSumParams_t;  // __attribute__ ((packed));  <-- AM problem using this attribute with GHS 5.1.7, needs to be treated with pointer to __packed struct

/// Flash Erase request status
typedef enum 
{
    NO_ERASE_REQ,
    ERASE_FINISHED_SUCCESS,
    ERASE_NOT_FINISHED,
    ERASE_STOPPED_OR_NOT_STARTED,
    ERASE_FINISHED_NOT_SUCCESS
} routineEraseRequested;// = NO_ERASE_REQ;

/// Flash Checksum request status
typedef enum {
    NO_CKSUM_REQ,
    CKSUM_FINISHED_SUCCESS,
    CKSUM_NOT_FINISHED,
    CKSUM_STOPPED_OR_NOT_STARTED,
    CKSUM_FINISHED_NOT_SUCCESS,
    CKSUM_FINISHED_ERROR
} routineChecksumRequested;// = NO_CKSUM_REQ;

/// Struct for diagnosis application indication
typedef struct T_DataInd_tag
{
    uint16_T DataLength;  // Diagnosis message request length
    uint8_T Data[RXBUFF_SIZE];
}T_DataInd_T;

/// Struct for diagnosis message request
typedef struct T_DataREG_tag
{
    uint8_T DataLength;  // Diagnosis message response length
    uint8_T Data[TXBUFF_SIZE];
}T_DataREG_T;

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
//extern T_DataInd_T  T_DataIND;
//extern T_DataREG_T  T_DataREQ;
extern uint8_T DIAGsession;
extern uint32_T SessionEnabledServices;
#pragma ghs startnomisra // MISRA 2004 Rule 18.4: union not allowed
extern union DownloadStruct_tag DownloadStruct;
#pragma ghs endnomisra 
extern uint16_T SecurityTimeout;
extern uint8_T System_Reset;
extern uint8_T response;
extern boolean_T Flg_CANTX_en;
extern boolean_T Flg_CANRX_en;
extern boolean_T Flg_NMTX_en;
extern boolean_T Flg_NMRX_en;
extern uint8_T ServiceID;
extern uint8_T FlgEOL;
extern int8_T IonAbsTdcDT;
extern uint8_T FlgIontelActive;

extern uint8_T EnRonDetectEE;

/******************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : DIAGCANMGM_DiagApl
**
**   Description:
**    Diagnostic Command (both REQs and RESs) processor
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void DIAGCANMGM_DiagApl(void);

/******************************************************************************
**   Function    : DIAGCANMGM_Init
**
**   Description:
**    Diagnostic Command initializer
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void DIAGCANMGM_Init(void);

/******************************************************************************
**   Function    : DIAGCANMGM_APP_Init
**
**   Description:
**    Diagnostic Command initializer
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void DIAGCANMGM_APP_Init(void);

/******************************************************************************
**   Function    : DIAGCANMGM_PowerOn
**
**   Description:
**    Diagnostic Command initializer
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void DIAGCANMGM_PowerOn(void);

/******************************************************************************
**   Function    : DIAGCANMGM_Send
**
**   Description:
**    Diagnostic Command Response Sender entity
**
**   Parameters :
**    [in] uint8_T * data, pointer to the received data container
**    [in] uint8_T * data_len, number of bytes of the diagnostic request
**
**   Returns:
**    DIAGCANMGM_NOTSUCCESS, error occurred while transmitting data to lower layers
**    DIAGCANMGM_SUCCESS, no error
**
******************************************************************************/
extern uint8_T DIAGCANMGM_Send(uint8_T *data, uint8_T data_len);

/******************************************************************************
**   Function    : DIAGCANMGM_Receive
**
**   Description:
**    Diagnostic Command Response Receiver entity
**
**   Parameters :
**    [out] uint8_T * data, pointer to the received data container
**    [out] uint16_T * data_len, number of bytes of the diagnostic request
**
**   Returns:
**    DIAGCANMGM_NOTSUCCESS, error occurred while receiving data from lower layers
**    DIAGCANMGM_SUCCESS, no error
**
******************************************************************************/
extern uint8_T DIAGCANMGM_Receive(uint8_T *data, uint16_T *data_len);

/******************************************************************************
**   Function    : DIAGCANMGM_SendStatus
**
**   Description:
**    Diagnostic Command Response Sender status Reporter
**
**   Parameters :
**    void
**
**   Returns:
**    DIAGCANMGM_NOTSUCCESS, error occurred while transmitting data to lower layers
**    DIAGCANMGM_SUCCESS, no error
**
******************************************************************************/
extern uint8_T DIAGCANMGM_SendStatus(void);

/******************************************************************************
**   Function    : DIAGCANMGM_ResetTPE
**
**   Description:
**    Funtion for TransportProtocol reset requested by Session Layer
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void DIAGCANMGM_ResetTPE(void);

/******************************************************************************
**   Function    : AnswerFunction
**
**   Description:
**    API for TransportProtocol reset requested by Session Layer
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void AnswerFunction(void);

/******************************************************************************
**   Function    : UpdateBootSession
**
**   Description:
**    API for TransportProtocol reset requested by Session Layer
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void UpdateBootSession(void);

/******************************************************************************
**   Function    : DownloadInProgress
**
**   Description:
**    API for TransportProtocol reset requested by Session Layer
**
**   Parameters :
**    void
**
**   Returns:
**    0 -> no active download at time request, 
**    1 -> at least active download at time request

**
******************************************************************************/
extern uint8_T DownloadInProgress(void);

/******************************************************************************
**   Function    : UDS_100ms
**
**   Description:
**    API for TransportProtocol reset requested by Session Layer
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void UDS_100ms(void);

/******************************************************************************
**   Function    : UDS_Init
**
**   Description:
**    API for TransportProtocol reset requested by Session Layer
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void UDS_Init(void);

/******************************************************************************
**   Function    : CallApplicationService
**
**   Description:
**    Diagnostic Service Main State Machine
**
**   Parameters :
**    [in] uint8_T SID
**
**   Returns:
**    void
**
******************************************************************************/
extern void CallApplicationService(uint8_T SID);

/******************************************************************************
**   Function    : DiagResponseAfterReset
**
**   Description:
**    Wrapper for diagnostic response after a reset
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void DiagResponseAfterReset(void);

/******************************************************************************
**   Function    : DiagResponseAfterJump2Bootloader
**
**   Description:
**    Wrapper for diagnostic response after a Programmig Session Start command incoming from 
**    Application Level
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void DiagResponseAfterJump2Appl(void);

/******************************************************************************
**   Function    : UDS_PendingWriteData
**
**   Description:
**    This function calls low level EEPROM drivers for flash operation
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void UDS_PendingWriteData(void);

/******************************************************************************
**   Function    : UDS_PendingClearDiagInfo
**
**   Description:
**    This function calls low level EEPROM drivers for Diagnostic Errors clearing from NVM
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void UDS_PendingClearDiagInfo(void);


/******************************************************************************
**   Function    : UDS_SecureJump2Boot
**
**   Description:
**    This function calls low level EEPROM drivers for flash operation and ECu 
**    self reset for jumping to Bootloader
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void UDS_SecureJump2Boot(void);

/******************************************************************************
**   Function    : UDS_SecureJump2Boot
**
**   Description:
**    This function allows an ECUReset synchronized with external WDT
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void UDS_SecureReset(void);

/******************************************************************************
**   Function    : NegativeResponseRoutine
**
**   Description:
**    This function fills the response with Negative Response Code
**
**   Parameters :
**    [in] uint8_T errorCode
**
**   Returns:
**    void
**
******************************************************************************/
extern void NegativeResponseRoutine(uint8_T errorCode);

/******************************************************************************
**   Function    : UDS_ClearDtcErasedFlg
**
**   Description:
**    This method sets up clear DtcErased flag
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void UDS_ClearDtcErasedFlg(void);


#endif // __DIAGCANMGM_OUT_H__

