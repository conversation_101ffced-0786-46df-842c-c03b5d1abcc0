/****************************************************************************
*
* Copyright © 2019-2020 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
*
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/
#ifndef _CRANK_EVENT_H_
#define _CRANK_EVENT_H_

#include <typedefs.h>

/*
 * Linked list item action
 */
typedef enum {
  EVENT_ACTION_NONE = 0,
  EVENT_ACTION_ONE_SHOT,
  EVENT_ACTION_PERIODIC
} EventAction_t;

typedef enum {
    CRANK_PHASE_SYNC_NOT_EXECUTED = 0U,
    CRANK_PHASE_SYNC_REQUEST = 1U,
    CRANK_PHASE_SYNC_ONGOING = 2u,
    CRANK_PHASE_SYNC_EXECUTED = 3U
} CRANK_PHASE_SYNC_STATUS;

/* extern */
extern CRANK_PHASE_SYNC_STATUS CrankPhaseRqst;

/*
 * Event associated callback
 */
typedef void (*EventCallback_t)(uint32_T ticks);


#ifdef __cplusplus
extern "C" {
#endif

extern void *setEventOnAngle(uint32_T angle, EventAction_t action, EventCallback_t cb);
extern void *setEventOnTooth(uint32_T tooth, EventAction_t action, EventCallback_t cb);
extern void resetEventOnAngle(uint32_T angle);
extern void resetEventOnTooth(uint32_T tooth);
extern void resetEvent(void *event);
extern void *getEventNext(void *event);
extern void *getEventRoot(void);
extern uint32_T getEventAngle(void *event);
extern EventAction_t getEventAction(void *event);
extern EventCallback_t getEventCallback(void *event);
extern void *GetNextElemList( void *elem, uint32_T AngleJump, uint32_T currentAngle);
extern void *setEventNoLock(uint32_T angle,  EventCallback_t cb);
extern void TDN_reinit_DPLL(void);
extern uint32_T GetCurrentAngle(void);
extern void TDNTimeout_mng(void);

#ifdef __cplusplus
}
#endif

#endif /* _CRANK_EVENT_H_ */

