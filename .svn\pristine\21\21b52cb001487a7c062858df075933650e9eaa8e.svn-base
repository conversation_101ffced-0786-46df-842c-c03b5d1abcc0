/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  RamCheckSM_MCU_r_xx_out.h
**  Created on      :  07-Feb-2022 15:39:00
**  Original author :  Mocci A
******************************************************************************/
/*****************************************************************************
**
**                        RamCheckSM_MCU_r_xx Description
**
** Initial check and runtime checks for the RAM module
** based on the requirements document:
** "SSPC574K72xx safety manual (AN4446 r1)"
**
** RAM_Check covers the requirements:
**           - chapter 3.3.8 page 40
******************************************************************************/


#ifndef RAM_CHECK_SM_MCU_4_XX_OUT_H
#define RAM_CHECK_SM_MCU_4_XX_OUT_H


/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RTWtypes.h"

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : RamCheck_SM_MCU_SRCEcheck
**
**   Description:
**    This function implements the mechanism described in chapter 3.3.8 page 40
**    of the k2 Safety Manual (AN4446 rev.1). Performing a pattern read on the 
**    RAM partition affected by Correctable Error
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern void RamCheck_SM_MCU_SRCEcheck(void);

#if 0
/******************************************************************************
**   Function    : RamCheck_SM_MCU_4_21
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void RamCheck_SM_MCU_4_21(void);

/******************************************************************************
**   Function    : RamCheck_SM_MCU_4_22
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void RamCheck_SM_MCU_4_22(void);

/******************************************************************************
**   Function    : RamCheck_SM_MCU_4_23
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void RamCheck_SM_MCU_4_23(void);
#endif

#endif // RAM_CHECK_SM_MCU_4_XX_OUT_H

/****************************************************************************
 ****************************************************************************/

