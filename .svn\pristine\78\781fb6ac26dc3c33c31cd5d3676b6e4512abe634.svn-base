/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           TLE9278BQX_Diag.c
 **  File Creation Date: 25-Oct-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TLE9278BQX_Diag
 **  Model Description:
 **  Model Version:      1.462
 **  Model Author:       SalimbeniT - Tue Jan 20 16:01:59 2015
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: LanaL - Tue Oct 25 13:02:52 2022
 **
 **  Last Saved Modification:  LanaL - Tue Oct 25 12:55:13 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "TLE9278BQX_Diag_out.h"
#include "TLE9278BQX_Diag_private.h"
#include "asr_s32.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_TLE9278BQX_DIAG_DEF     1462U                     /* Referenced by: '<S6>/ID_VER_TLE9278BQX_DIAG_DEF' */

/* ID Version */
#define TLE9278BQX_FAM                 4U                        /* Referenced by: '<S8>/SBC_Diag_Mgm' */

/* mask */
#define TLE9278BQX_PROD                2U                        /* Referenced by: '<S8>/SBC_Diag_Mgm' */

/* mask */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_TLE9278BQX_DIAG_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENTRSFAULT = 1U;/* Referenced by: '<S9>/ENTRSFAULT' */

/* Enable transient fault */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T NUMSBCDC = 10U;/* Referenced by:
                                                         * '<S7>/Chart_CAN'
                                                         * '<S10>/Chart_OT'
                                                         * '<S11>/Chart_SC'
                                                         * '<S12>/Chart_UV'
                                                         */

/* Diagnosis SBC data age */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T FlgSBCICsWrong;                /* '<S8>/SBC_Diag_Mgm' */

/* SBC Component model version wrong */
uint8_T FlguCTransient;                /* '<S9>/Logical Operator1' */

/* Transient fault discovered */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T CntSBCFault;/* '<S3>/Merge2' */

/* Fault counter */
STATIC_TEST_POINT uint32_T IdVer_TLE9278BQX_Diag;/* '<S6>/ID_VER_TLE9278BQX_DIAG_DEF' */

/* ID Version */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_TLE9278BQX_Diag_T TLE9278BQX_Diag_DW;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Output and update for function-call system: '<S7>/fc_EECntSbcCAN' */
void TLE9278BQX_Diag_fc_EECntSbcCAN(uint8_T rtu_EECntSbcCAN, uint32_T
  rtu_EECntSbcCANAddr, uint8_T *rty_EECntSbcCANTp)
{
  /* S-Function (fc_Diag_SetVal): '<S14>/C//C++ Code Block' */
  fc_Diag_SetVal_Outputs_wrapper(&rtu_EECntSbcCAN, &rtu_EECntSbcCANAddr,
    rty_EECntSbcCANTp);
}

/* Output and update for function-call system: '<S7>/fc_EESbcCAN' */
void TLE9278BQX_Diag_fc_EESbcCAN(uint8_T rtu_EESbcCAN, uint32_T rtu_EESbcCANAddr,
  uint8_T *rty_EESbcCANTp)
{
  /* S-Function (fc_Diag_SetVal): '<S15>/C//C++ Code Block' */
  fc_Diag_SetVal_Outputs_wrapper(&rtu_EESbcCAN, &rtu_EESbcCANAddr,
    rty_EESbcCANTp);
}

/* Output and update for function-call system: '<S10>/fc_EECntSbcOT' */
void TLE9278BQX_Diag_fc_EECntSbcOT(uint8_T rtu_EECntSbcOT, uint32_T
  rtu_EECntSbcOTAddr, uint8_T *rty_EECntSbcOTTp)
{
  /* S-Function (fc_Diag_SetVal): '<S32>/C//C++ Code Block' */
  fc_Diag_SetVal_Outputs_wrapper(&rtu_EECntSbcOT, &rtu_EECntSbcOTAddr,
    rty_EECntSbcOTTp);
}

/* Output and update for function-call system: '<S10>/fc_EESbcOT' */
void TLE9278BQX_Diag_fc_EESbcOT(uint8_T rtu_EESbcOT, uint32_T rtu_EESbcOTAddr,
  uint8_T *rty_EESbcOTTp)
{
  /* S-Function (fc_Diag_SetVal): '<S33>/C//C++ Code Block' */
  fc_Diag_SetVal_Outputs_wrapper(&rtu_EESbcOT, &rtu_EESbcOTAddr, rty_EESbcOTTp);
}

/* Output and update for function-call system: '<S11>/fc_EECntSbcSC' */
void TLE9278BQX_Diag_fc_EECntSbcSC(uint8_T rtu_EECntSbcSC, uint32_T
  rtu_EECntSbcSCAddr, uint8_T *rty_EECntSbcSCTp)
{
  /* S-Function (fc_Diag_SetVal): '<S35>/C//C++ Code Block' */
  fc_Diag_SetVal_Outputs_wrapper(&rtu_EECntSbcSC, &rtu_EECntSbcSCAddr,
    rty_EECntSbcSCTp);
}

/* Output and update for function-call system: '<S11>/fc_EESbcSC' */
void TLE9278BQX_Diag_fc_EESbcSC(uint8_T rtu_EESbcSC, uint32_T rtu_EESbcSCAddr,
  uint8_T *rty_EESbcSCTp)
{
  /* S-Function (fc_Diag_SetVal): '<S36>/C//C++ Code Block' */
  fc_Diag_SetVal_Outputs_wrapper(&rtu_EESbcSC, &rtu_EESbcSCAddr, rty_EESbcSCTp);
}

/* Output and update for function-call system: '<S12>/fc_EECntSbcUV' */
void TLE9278BQX_Diag_fc_EECntSbcUV(uint8_T rtu_EECntSbcUV, uint32_T
  rtu_EECntSbcUVAddr, uint8_T *rty_EECntSbcUVTp)
{
  /* S-Function (fc_Diag_SetVal): '<S38>/C//C++ Code Block' */
  fc_Diag_SetVal_Outputs_wrapper(&rtu_EECntSbcUV, &rtu_EECntSbcUVAddr,
    rty_EECntSbcUVTp);
}

/* Output and update for function-call system: '<S12>/fc_EESbcUV' */
void TLE9278BQX_Diag_fc_EESbcUV(uint8_T rtu_EESbcUV, uint32_T rtu_EESbcUVAddr,
  uint8_T *rty_EESbcUVTp)
{
  /* S-Function (fc_Diag_SetVal): '<S39>/C//C++ Code Block' */
  fc_Diag_SetVal_Outputs_wrapper(&rtu_EESbcUV, &rtu_EESbcUVAddr, rty_EESbcUVTp);
}

/* Output and update for function-call system: '<S2>/fc_Bkg' */
void TLE9278BQX_Diag_fc_Bkg(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState;
  uint8_T rtb_DiagMgm_SetDiagState_j;
  uint8_T rtb_DiagMgm_SetDiagState_d;
  uint8_T rtb_DiagMgm_SetDiagState_c;
  uint8_T rtb_DiagMgm_SetDiagState_e;
  uint8_T rtb_DiagMgm_SetDiagState_m;
  uint8_T rtb_DiagMgm_SetDiagState_i;
  uint8_T rtb_DiagMgm_SetDiagState_et;
  uint8_T rtb_DiagMgm_SetDiagState_g;
  uint8_T rtb_DiagMgm_SetDiagState_p;
  uint8_T rtb_DiagMgm_SetDiagState_h;
  uint8_T rtb_DiagMgm_SetDiagState_mi;
  uint8_T tmpEESbc;

  /* Gateway: TLE9278BQX_Diag/fc_Bkg/Calc_Diag/SBC_Diag_Mgm */
  /* During: TLE9278BQX_Diag/fc_Bkg/Calc_Diag/SBC_Diag_Mgm */
  /* Entry Internal: TLE9278BQX_Diag/fc_Bkg/Calc_Diag/SBC_Diag_Mgm */
  /* Transition: '<S16>:2' */
  TLE9278BQX_Diag_DW.ptFault = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultWdt = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultCpu = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultGtm = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultSM1 = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultSM2 = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultSM3 = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultSM4 = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultSM5 = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultSM6 = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultSM7 = NO_PT_FAULT;
  TLE9278BQX_Diag_DW.ptFaultSM8 = NO_PT_FAULT;

  /* Inport: '<Root>/SBCFam' incorporates:
   *  Inport: '<Root>/SBCProd'
   *
   * Block description for '<Root>/SBCFam':
   *  SBC Family product
   *
   * Block description for '<Root>/SBCProd':
   *  SBC model type product
   */
  /*   */
  if ((SBCFam != ((uint8_T)TLE9278BQX_FAM)) || (SBCProd != ((uint8_T)
        TLE9278BQX_PROD))) {
    /* Transition: '<S16>:4' */
    TLE9278BQX_Diag_DW.ptFault = INVALID_SERIAL_DATA;
    TLE9278BQX_Diag_DW.CntSBCFault_n = (uint16_T)((int32_T)(((int32_T)
      TLE9278BQX_Diag_DW.CntSBCFault_n) + 1));
    FlgSBCICsWrong = 1U;
  } else {
    /* Transition: '<S16>:5' */
    FlgSBCICsWrong = 0U;

    /* Inport: '<Root>/StSBCSafeTest' */
    /*  SBCSpiFail != 0 */
    /* Transition: '<S16>:9' */
    /*  FlgSBCProgErr != 0 */
    /* Transition: '<S16>:27' */
    if (((uint32_T)StSBCSafeTest) == SF_TRANS_EXCEPTION) {
      /* Transition: '<S16>:46' */
      TLE9278BQX_Diag_DW.ptFault = GENERAL_ELECTRIC_FAILURE;
      TLE9278BQX_Diag_DW.CntSBCFault_n = (uint16_T)((int32_T)(((int32_T)
        TLE9278BQX_Diag_DW.CntSBCFault_n) + 1));
    } else {
      /* Inport: '<Root>/SBCTpw' */
      /* Transition: '<S16>:48' */
      if (((int32_T)SBCTpw) != 0) {
        /* Transition: '<S16>:140' */
        TLE9278BQX_Diag_DW.ptFault = COMPONENT_INTERNAL_FAILURE;
        TLE9278BQX_Diag_DW.CntSBCFault_n = (uint16_T)((int32_T)(((int32_T)
          TLE9278BQX_Diag_DW.CntSBCFault_n) + 1));
      } else {
        /* Transition: '<S16>:141' */
      }

      /* Transition: '<S16>:139' */
    }

    /* Transition: '<S16>:47' */
    /* Transition: '<S16>:26' */
    /* Transition: '<S16>:13' */
  }

  /* End of Inport: '<Root>/SBCFam' */

  /* Inport: '<Root>/StSBCSafeTest' */
  /* Transition: '<S16>:55' */
  switch (StSBCSafeTest) {
   case SF_WDT_EXCEPTION:
    /* Transition: '<S16>:58' */
    TLE9278BQX_Diag_DW.ptFaultWdt = WDT_SAFETY_FAILURE;

    /* Transition: '<S16>:102' */
    /* Transition: '<S16>:103' */
    /* Transition: '<S16>:104' */
    /* Transition: '<S16>:105' */
    break;

   case SF_WDT_EXPIRED:
    /* Transition: '<S16>:61' */
    /* Transition: '<S16>:62' */
    TLE9278BQX_Diag_DW.ptFaultWdt = COMPONENT_INTERNAL_FAILURE;

    /* Transition: '<S16>:103' */
    /* Transition: '<S16>:104' */
    /* Transition: '<S16>:105' */
    break;

   case SF_ABNORMAL:
    /* Transition: '<S16>:73' */
    /* Transition: '<S16>:80' */
    TLE9278BQX_Diag_DW.ptFaultWdt = INTERNAL_ELECTRONIC_FAILURE;

    /* Transition: '<S16>:104' */
    /* Transition: '<S16>:105' */
    break;

   case SF_NO_COM:
    /* Transition: '<S16>:77' */
    /* Transition: '<S16>:79' */
    TLE9278BQX_Diag_DW.ptFaultWdt = MISSING_MESSAGE;

    /* Transition: '<S16>:105' */
    break;

   case SF_EL_FAILURE:
    /* Transition: '<S16>:81' */
    /* Transition: '<S16>:87' */
    TLE9278BQX_Diag_DW.ptFaultWdt = GENERAL_ELECTRIC_FAILURE;
    break;

   default:
    /* Transition: '<S16>:123' */
    break;
  }

  /* Transition: '<S16>:101' */
  switch (StSBCSafeTest) {
   case SF_DATA_EXCEPTION:
    /* Transition: '<S16>:92' */
    TLE9278BQX_Diag_DW.ptFaultCpu = DATA_MEMORY_FAILURE;

    /* Transition: '<S16>:93' */
    /* Transition: '<S16>:113' */
    /* Transition: '<S16>:118' */
    break;

   case SF_CORE1_EXCEPTION:
    /* Transition: '<S16>:108' */
    /* Transition: '<S16>:109' */
    TLE9278BQX_Diag_DW.ptFaultCpu = PROGRAM_MEMORY_FAILURE;

    /* Transition: '<S16>:113' */
    /* Transition: '<S16>:118' */
    break;

   case SF_CORE2_EXCEPTION:
    /* Transition: '<S16>:111' */
    /* Transition: '<S16>:114' */
    TLE9278BQX_Diag_DW.ptFaultCpu = ALIVE_SEQUENCE_COUNTER_INCORRECT_NOT_UPDATED;

    /* Transition: '<S16>:118' */
    break;

   default:
    /* Transition: '<S16>:117' */
    /* Transition: '<S16>:134' */
    break;
  }

  /* Transition: '<S16>:132' */
  if (((uint32_T)StSBCSafeTest) == SF_GTM_EXCEPTION) {
    /* Transition: '<S16>:119' */
    TLE9278BQX_Diag_DW.ptFaultGtm = SIGNAL_COMPARE_FAILURE;
  } else {
    /* Transition: '<S16>:128' */
    /* Transition: '<S16>:130' */
    /* Transition: '<S16>:168' */
  }

  /* Chart: '<S8>/SBC_Diag_Mgm' incorporates:
   *  SubSystem: '<S8>/Set_Diag1'
   */
  /* S-Function (DiagMgm_SetDiagState): '<S19>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S17>/DIAG_TLE9278BQX'
   */
  /* Transition: '<S16>:169' */
  /* Event: '<S16>:354' */
  DiagMgm_SetDiagState( DIAG_TLE9278BQX, TLE9278BQX_Diag_DW.ptFault,
                       &rtb_DiagMgm_SetDiagState);

  /* S-Function (DiagMgm_SetDiagState): '<S20>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S17>/DIAG_WDT'
   */
  DiagMgm_SetDiagState( DIAG_WDT, TLE9278BQX_Diag_DW.ptFaultWdt,
                       &rtb_DiagMgm_SetDiagState_j);

  /* S-Function (DiagMgm_SetDiagState): '<S21>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S17>/DIAG_CPU'
   */
  DiagMgm_SetDiagState( DIAG_CPU, TLE9278BQX_Diag_DW.ptFaultCpu,
                       &rtb_DiagMgm_SetDiagState_d);

  /* S-Function (DiagMgm_SetDiagState): '<S22>/DiagMgm_SetDiagState' incorporates:
   *  Constant: '<S17>/DIAG_GTM'
   */
  DiagMgm_SetDiagState( DIAG_GTM, TLE9278BQX_Diag_DW.ptFaultGtm,
                       &rtb_DiagMgm_SetDiagState_c);
  if (((int32_T)TLE9278BQX_Diag_DW.initDone) == 0) {
    /* Transition: '<S16>:357' */
    TLE9278BQX_Diag_DW.initDone = 1U;

    /* Inport: '<Root>/StSBCSafeTest' */
    switch (StSBCSafeTest) {
     case SF_SM_INTC:
      /* Transition: '<S16>:142' */
      TLE9278BQX_Diag_DW.ptFaultSM1 = SAFETY_ERROR_1;
      break;

     case SF_SM_PIT2:
      /* Transition: '<S16>:166' */
      /* Transition: '<S16>:164' */
      TLE9278BQX_Diag_DW.ptFaultSM1 = SAFETY_ERROR_2;

      /* Transition: '<S16>:227' */
      break;

     case SF_SM_INIT1:
      /* Transition: '<S16>:165' */
      /* Transition: '<S16>:163' */
      TLE9278BQX_Diag_DW.ptFaultSM1 = SAFETY_ERROR_3;

      /* Transition: '<S16>:226' */
      break;

     case SF_SM_INIT2:
      /* Transition: '<S16>:147' */
      /* Transition: '<S16>:162' */
      TLE9278BQX_Diag_DW.ptFaultSM1 = SAFETY_ERROR_4;

      /* Transition: '<S16>:225' */
      break;

     case SF_SM_CMU:
      /* Transition: '<S16>:146' */
      /* Transition: '<S16>:161' */
      TLE9278BQX_Diag_DW.ptFaultSM1 = SAFETY_ERROR_5;

      /* Transition: '<S16>:224' */
      break;

     default:
      /* Transition: '<S16>:167' */
      break;
    }

    /* Transition: '<S16>:228' */
    switch (StSBCSafeTest) {
     case SF_SM_FCCU1:
      /* Transition: '<S16>:205' */
      TLE9278BQX_Diag_DW.ptFaultSM2 = SAFETY_ERROR_1;
      break;

     case SF_SM_MCU1:
      /* Transition: '<S16>:223' */
      /* Transition: '<S16>:211' */
      TLE9278BQX_Diag_DW.ptFaultSM2 = SAFETY_ERROR_2;

      /* Transition: '<S16>:232' */
      break;

     case SF_SM_MCU2:
      /* Transition: '<S16>:209' */
      /* Transition: '<S16>:201' */
      TLE9278BQX_Diag_DW.ptFaultSM2 = SAFETY_ERROR_3;

      /* Transition: '<S16>:231' */
      break;

     case SF_SM_MCU3:
      /* Transition: '<S16>:212' */
      /* Transition: '<S16>:216' */
      TLE9278BQX_Diag_DW.ptFaultSM2 = SAFETY_ERROR_4;

      /* Transition: '<S16>:230' */
      break;

     case SF_SM_MCU4:
      /* Transition: '<S16>:215' */
      /* Transition: '<S16>:220' */
      TLE9278BQX_Diag_DW.ptFaultSM2 = SAFETY_ERROR_5;

      /* Transition: '<S16>:229' */
      break;

     default:
      /* Transition: '<S16>:222' */
      break;
    }

    /* Transition: '<S16>:240' */
    switch (StSBCSafeTest) {
     case SF_SM_MCU5:
      /* Transition: '<S16>:236' */
      TLE9278BQX_Diag_DW.ptFaultSM3 = SAFETY_ERROR_1;
      break;

     case SF_SM_MCU6:
      /* Transition: '<S16>:246' */
      /* Transition: '<S16>:252' */
      TLE9278BQX_Diag_DW.ptFaultSM3 = SAFETY_ERROR_2;

      /* Transition: '<S16>:237' */
      break;

     case SF_SM_MCU7:
      /* Transition: '<S16>:242' */
      /* Transition: '<S16>:235' */
      TLE9278BQX_Diag_DW.ptFaultSM3 = SAFETY_ERROR_3;

      /* Transition: '<S16>:255' */
      break;

     case SF_SM_MCU8:
      /* Transition: '<S16>:251' */
      /* Transition: '<S16>:256' */
      TLE9278BQX_Diag_DW.ptFaultSM3 = SAFETY_ERROR_4;

      /* Transition: '<S16>:257' */
      break;

     default:
      /* Transition: '<S16>:234' */
      /*  Reserved */
      /* Transition: '<S16>:247' */
      /* Transition: '<S16>:245' */
      break;
    }

    /* Transition: '<S16>:265' */
    switch (StSBCSafeTest) {
     case SF_SM_FCCU3:
      /* Transition: '<S16>:276' */
      TLE9278BQX_Diag_DW.ptFaultSM4 = SAFETY_ERROR_1;
      break;

     case SF_SM_FCCU4:
      /* Transition: '<S16>:261' */
      /* Transition: '<S16>:278' */
      TLE9278BQX_Diag_DW.ptFaultSM4 = SAFETY_ERROR_2;

      /* Transition: '<S16>:269' */
      break;

     case SF_SM_SCLK:
      /* Transition: '<S16>:274' */
      /* Transition: '<S16>:267' */
      TLE9278BQX_Diag_DW.ptFaultSM4 = SAFETY_ERROR_3;

      /* Transition: '<S16>:264' */
      break;

     case SF_SM_LCLK:
      /* Transition: '<S16>:277' */
      /* Transition: '<S16>:260' */
      TLE9278BQX_Diag_DW.ptFaultSM4 = SAFETY_ERROR_4;

      /* Transition: '<S16>:259' */
      break;

     case SF_SM_FCCU2:
      /* Transition: '<S16>:280' */
      /* Transition: '<S16>:268' */
      TLE9278BQX_Diag_DW.ptFaultSM4 = SAFETY_ERROR_5;

      /* Transition: '<S16>:262' */
      break;

     default:
      /* Transition: '<S16>:270' */
      break;
    }

    /* Transition: '<S16>:304' */
    switch (StSBCSafeTest) {
     case SF_SM_FCCU5:
      /* Transition: '<S16>:285' */
      TLE9278BQX_Diag_DW.ptFaultSM5 = SAFETY_ERROR_1;
      break;

     case SF_SM_FCCU6:
      /* Transition: '<S16>:298' */
      /* Transition: '<S16>:287' */
      TLE9278BQX_Diag_DW.ptFaultSM5 = SAFETY_ERROR_2;

      /* Transition: '<S16>:290' */
      break;

     case SF_SM_FCCU8:
      /* Transition: '<S16>:289' */
      /* Transition: '<S16>:293' */
      TLE9278BQX_Diag_DW.ptFaultSM5 = SAFETY_ERROR_3;

      /* Transition: '<S16>:305' */
      break;

     case SF_SM_FCCU9:
      /* Transition: '<S16>:294' */
      /* Transition: '<S16>:299' */
      TLE9278BQX_Diag_DW.ptFaultSM5 = SAFETY_ERROR_4;

      /* Transition: '<S16>:300' */
      break;

     case SF_PARITY_EXCEPTION:
      /* Transition: '<S16>:286' */
      /* Transition: '<S16>:291' */
      TLE9278BQX_Diag_DW.ptFaultSM5 = SAFETY_ERROR_5;

      /* Transition: '<S16>:303' */
      break;

     default:
      /* Transition: '<S16>:297' */
      break;
    }

    /* Transition: '<S16>:316' */
    if (((uint32_T)StSBCSafeTest) == SF_SM_ADC) {
      /* Transition: '<S16>:308' */
      TLE9278BQX_Diag_DW.ptFaultSM6 = SAFETY_ERROR_1;
    } else {
      /* Transition: '<S16>:313' */
      /* Transition: '<S16>:309' */
      /* Transition: '<S16>:311' */
    }

    /* Transition: '<S16>:320' */
    if (((uint32_T)StSBCSafeTest) == SF_SM_FCCU7) {
      /* Transition: '<S16>:324' */
      TLE9278BQX_Diag_DW.ptFaultSM7 = SAFETY_ERROR_1;
    } else {
      /* Transition: '<S16>:318' */
      /* Transition: '<S16>:322' */
      /* Transition: '<S16>:323' */
    }

    /* Transition: '<S16>:352' */
    if (((uint32_T)StSBCSafeTest) == SF_SM_PIT1) {
      /* Transition: '<S16>:344' */
      TLE9278BQX_Diag_DW.ptFaultSM8 = SAFETY_ERROR_1;
    } else {
      /* Transition: '<S16>:341' */
      /* Transition: '<S16>:342' */
      /* Transition: '<S16>:345' */
    }

    /* Outputs for Function Call SubSystem: '<S8>/Set_Diag2' */
    /* S-Function (DiagMgm_SetDiagState): '<S25>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S18>/DIAG_SM1'
     */
    /* Transition: '<S16>:343' */
    /* Event: '<S16>:340' */
    DiagMgm_SetDiagState( DIAG_SM1, TLE9278BQX_Diag_DW.ptFaultSM1,
                         &rtb_DiagMgm_SetDiagState_e);

    /* S-Function (DiagMgm_SetDiagState): '<S26>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S18>/DIAG_SM2'
     */
    DiagMgm_SetDiagState( DIAG_SM2, TLE9278BQX_Diag_DW.ptFaultSM2,
                         &rtb_DiagMgm_SetDiagState_m);

    /* S-Function (DiagMgm_SetDiagState): '<S27>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S18>/DIAG_SM3'
     */
    DiagMgm_SetDiagState( DIAG_SM3, TLE9278BQX_Diag_DW.ptFaultSM3,
                         &rtb_DiagMgm_SetDiagState_i);

    /* S-Function (DiagMgm_SetDiagState): '<S28>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S18>/DIAG_SM4'
     */
    DiagMgm_SetDiagState( DIAG_SM4, TLE9278BQX_Diag_DW.ptFaultSM4,
                         &rtb_DiagMgm_SetDiagState_et);

    /* S-Function (DiagMgm_SetDiagState): '<S29>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S18>/DIAG_SM5'
     */
    DiagMgm_SetDiagState( DIAG_SM5, TLE9278BQX_Diag_DW.ptFaultSM5,
                         &rtb_DiagMgm_SetDiagState_g);

    /* S-Function (DiagMgm_SetDiagState): '<S30>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S18>/DIAG_SM6'
     */
    DiagMgm_SetDiagState( DIAG_SM6, TLE9278BQX_Diag_DW.ptFaultSM6,
                         &rtb_DiagMgm_SetDiagState_p);

    /* S-Function (DiagMgm_SetDiagState): '<S23>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S18>/DIAG_SM7'
     */
    DiagMgm_SetDiagState( DIAG_SM7, TLE9278BQX_Diag_DW.ptFaultSM7,
                         &rtb_DiagMgm_SetDiagState_h);

    /* S-Function (DiagMgm_SetDiagState): '<S24>/DiagMgm_SetDiagState' incorporates:
     *  Constant: '<S18>/DIAG_SM8'
     */
    DiagMgm_SetDiagState( DIAG_SM8, TLE9278BQX_Diag_DW.ptFaultSM8,
                         &rtb_DiagMgm_SetDiagState_mi);

    /* End of Outputs for SubSystem: '<S8>/Set_Diag2' */
  } else {
    /* Transition: '<S16>:358' */
    /* Transition: '<S16>:359' */
  }

  /* S-Function (Diag_Return_Addr_U8): '<S10>/C//C++ Code Block' */
  Diag_Return_Addr_U8_Outputs_wrapper((&(EECntSbcOT)),
    &TLE9278BQX_Diag_DW.CCCodeBlock);

  /* S-Function (Diag_Return_Addr_U8): '<S10>/C//C++ Code Block1' */
  Diag_Return_Addr_U8_Outputs_wrapper((&(EESbcOT)),
    &TLE9278BQX_Diag_DW.CCCodeBlock1);

  /* Gateway: TLE9278BQX_Diag/fc_Bkg/OT/Chart_OT */
  /* During: TLE9278BQX_Diag/fc_Bkg/OT/Chart_OT */
  /* Entry Internal: TLE9278BQX_Diag/fc_Bkg/OT/Chart_OT */
  /* Transition: '<S31>:2' */
  if (((int32_T)TLE9278BQX_Diag_DW.wk_c4) == 0) {
    /* Transition: '<S31>:7' */
    TLE9278BQX_Diag_DW.wk_c4 = 1U;

    /* Inport: '<Root>/EECntSbcOT' */
    if (EECntSbcOT >= NUMSBCDC) {
      /* Transition: '<S31>:5' */
      TLE9278BQX_Diag_DW.eeSbcOT = 0U;

      /* Outputs for Function Call SubSystem: '<S10>/fc_EESbcOT' */
      /* Event: '<S31>:31' */
      TLE9278BQX_Diag_fc_EESbcOT(TLE9278BQX_Diag_DW.eeSbcOT,
        TLE9278BQX_Diag_DW.CCCodeBlock1, &TLE9278BQX_Diag_DW.CCCodeBlock_er);

      /* End of Outputs for SubSystem: '<S10>/fc_EESbcOT' */
    } else {
      /* Transition: '<S31>:4' */
      TLE9278BQX_Diag_DW.eeCntSbcOT = EECntSbcOT;
      TLE9278BQX_Diag_DW.eeCntSbcOT = (uint8_T)((int32_T)(((int32_T)
        TLE9278BQX_Diag_DW.eeCntSbcOT) + 1));

      /* Outputs for Function Call SubSystem: '<S10>/fc_EECntSbcOT' */
      /* Event: '<S31>:30' */
      TLE9278BQX_Diag_fc_EECntSbcOT(TLE9278BQX_Diag_DW.eeCntSbcOT,
        TLE9278BQX_Diag_DW.CCCodeBlock, &TLE9278BQX_Diag_DW.CCCodeBlock_k2);

      /* End of Outputs for SubSystem: '<S10>/fc_EECntSbcOT' */
    }

    /* End of Inport: '<Root>/EECntSbcOT' */
  } else {
    /* Transition: '<S31>:8' */
    /* Transition: '<S31>:36' */
    /* Transition: '<S31>:37' */
  }

  /* Inport: '<Root>/WkSBCTermStat' incorporates:
   *  Inport: '<Root>/SBCBoostOT'
   *  Inport: '<Root>/SBCTpw'
   *  Inport: '<Root>/SBCTsd1'
   *  Inport: '<Root>/SBCTsd2'
   *  Inport: '<Root>/SBCVccOT'
   */
  /* Transition: '<S31>:10' */
  tmpEESbc = (uint8_T)((int32_T)(((((((((int32_T)WkSBCTermStat) & ((int32_T)0x02))
    * 8) | ((((int32_T)WkSBCTermStat) & ((int32_T)0x04)) * 2)) | asr_s32
    (((int32_T)WkSBCTermStat) & ((int32_T)0x20), 3U)) | asr_s32(((int32_T)
    WkSBCTermStat) & ((int32_T)0x40), 5U)) | (((int32_T)WkSBCTermStat) &
    ((int32_T)0x01))) | ((int32_T)((uint8_T)(((((SBCVccOT << ((uint32_T)1)) |
    SBCTpw) | (SBCBoostOT << ((uint32_T)2))) | (SBCTsd2 << ((uint32_T)3))) |
    (SBCTsd1 << ((uint32_T)4)))))));

  /* Inport: '<Root>/EESbcOT' */
  TLE9278BQX_Diag_DW.eeSbcOT = (uint8_T)(EESbcOT | tmpEESbc);

  /* Chart: '<S10>/Chart_OT' incorporates:
   *  SubSystem: '<S10>/fc_EESbcOT'
   */
  /* Event: '<S31>:31' */
  TLE9278BQX_Diag_fc_EESbcOT(TLE9278BQX_Diag_DW.eeSbcOT,
    TLE9278BQX_Diag_DW.CCCodeBlock1, &TLE9278BQX_Diag_DW.CCCodeBlock_er);
  if (((int32_T)tmpEESbc) != 0) {
    /* Transition: '<S31>:16' */
    TLE9278BQX_Diag_DW.eeCntSbcOT = 0U;

    /* Outputs for Function Call SubSystem: '<S10>/fc_EECntSbcOT' */
    /* Event: '<S31>:30' */
    TLE9278BQX_Diag_fc_EECntSbcOT(TLE9278BQX_Diag_DW.eeCntSbcOT,
      TLE9278BQX_Diag_DW.CCCodeBlock, &TLE9278BQX_Diag_DW.CCCodeBlock_k2);

    /* End of Outputs for SubSystem: '<S10>/fc_EECntSbcOT' */
  } else {
    /* Transition: '<S31>:15' */
  }

  /* SignalConversion generated from: '<S5>/CntSBCFault' */
  CntSBCFault = TLE9278BQX_Diag_DW.CntSBCFault_n;

  /* S-Function (Diag_Return_Addr_U8): '<S7>/C//C++ Code Block' */
  Diag_Return_Addr_U8_Outputs_wrapper((&(EECntSbcCAN)),
    &TLE9278BQX_Diag_DW.CCCodeBlock_f);

  /* S-Function (Diag_Return_Addr_U8): '<S7>/C//C++ Code Block1' */
  Diag_Return_Addr_U8_Outputs_wrapper((&(EESbcCAN)),
    &TLE9278BQX_Diag_DW.CCCodeBlock1_f);

  /* Gateway: TLE9278BQX_Diag/fc_Bkg/CAN/Chart_CAN */
  /* During: TLE9278BQX_Diag/fc_Bkg/CAN/Chart_CAN */
  /* Entry Internal: TLE9278BQX_Diag/fc_Bkg/CAN/Chart_CAN */
  /* Transition: '<S13>:2' */
  if (((int32_T)TLE9278BQX_Diag_DW.wk_i) == 0) {
    /* Transition: '<S13>:7' */
    TLE9278BQX_Diag_DW.wk_i = 1U;

    /* Inport: '<Root>/EECntSbcCAN' */
    if (EECntSbcCAN >= NUMSBCDC) {
      /* Transition: '<S13>:5' */
      TLE9278BQX_Diag_DW.eeSbcCAN = 0U;

      /* Outputs for Function Call SubSystem: '<S7>/fc_EESbcCAN' */
      /* Event: '<S13>:35' */
      TLE9278BQX_Diag_fc_EESbcCAN(TLE9278BQX_Diag_DW.eeSbcCAN,
        TLE9278BQX_Diag_DW.CCCodeBlock1_f, &TLE9278BQX_Diag_DW.CCCodeBlock_ch);

      /* End of Outputs for SubSystem: '<S7>/fc_EESbcCAN' */
    } else {
      /* Transition: '<S13>:4' */
      TLE9278BQX_Diag_DW.eeCntSbcCAN = EECntSbcCAN;
      TLE9278BQX_Diag_DW.eeCntSbcCAN = (uint8_T)((int32_T)(((int32_T)
        TLE9278BQX_Diag_DW.eeCntSbcCAN) + 1));

      /* Outputs for Function Call SubSystem: '<S7>/fc_EECntSbcCAN' */
      /* Event: '<S13>:34' */
      TLE9278BQX_Diag_fc_EECntSbcCAN(TLE9278BQX_Diag_DW.eeCntSbcCAN,
        TLE9278BQX_Diag_DW.CCCodeBlock_f, &TLE9278BQX_Diag_DW.CCCodeBlock_ok);

      /* End of Outputs for SubSystem: '<S7>/fc_EECntSbcCAN' */
    }

    /* End of Inport: '<Root>/EECntSbcCAN' */
  } else {
    /* Transition: '<S13>:8' */
    /* Transition: '<S13>:32' */
    /* Transition: '<S13>:33' */
  }

  /* Inport: '<Root>/SBCCan1Fail' incorporates:
   *  Inport: '<Root>/SBCCan0Fail'
   *  Inport: '<Root>/SBCCan2Fail'
   *  Inport: '<Root>/WkSBCBusStat0'
   *  Inport: '<Root>/WkSBCBusStat2'
   */
  /* Transition: '<S13>:10' */
  tmpEESbc = (uint8_T)((int32_T)((((int32_T)((uint8_T)(((SBCCan1Fail <<
    ((uint32_T)2)) | SBCCan0Fail) | (SBCCan2Fail << ((uint32_T)4))))) | asr_s32
    (((int32_T)WkSBCBusStat0) & ((int32_T)0x06), 1U)) | (((int32_T)WkSBCBusStat2)
    & ((int32_T)0x30))));

  /* Inport: '<Root>/EESbcCAN' */
  TLE9278BQX_Diag_DW.eeSbcCAN = (uint8_T)(EESbcCAN | tmpEESbc);

  /* Chart: '<S7>/Chart_CAN' incorporates:
   *  SubSystem: '<S7>/fc_EESbcCAN'
   */
  /* Event: '<S13>:35' */
  TLE9278BQX_Diag_fc_EESbcCAN(TLE9278BQX_Diag_DW.eeSbcCAN,
    TLE9278BQX_Diag_DW.CCCodeBlock1_f, &TLE9278BQX_Diag_DW.CCCodeBlock_ch);
  if (((int32_T)tmpEESbc) != 0) {
    /* Transition: '<S13>:16' */
    TLE9278BQX_Diag_DW.eeCntSbcCAN = 0U;

    /* Outputs for Function Call SubSystem: '<S7>/fc_EECntSbcCAN' */
    /* Event: '<S13>:34' */
    TLE9278BQX_Diag_fc_EECntSbcCAN(TLE9278BQX_Diag_DW.eeCntSbcCAN,
      TLE9278BQX_Diag_DW.CCCodeBlock_f, &TLE9278BQX_Diag_DW.CCCodeBlock_ok);

    /* End of Outputs for SubSystem: '<S7>/fc_EECntSbcCAN' */
  } else {
    /* Transition: '<S13>:15' */
  }

  /* Logic: '<S9>/Logical Operator' incorporates:
   *  Inport: '<Root>/SBCVioOV'
   *  Inport: '<Root>/SBCVioUV'
   *  Inport: '<Root>/SBCVioWarn'
   *  Memory: '<S9>/Memory'
   */
  TLE9278BQX_Diag_DW.Memory_PreviousInput = (uint8_T)(((((((int32_T)SBCVioWarn)
    != 0) || (((int32_T)SBCVioUV) != 0)) || (((int32_T)SBCVioOV) != 0)) ||
    (((int32_T)TLE9278BQX_Diag_DW.Memory_PreviousInput) != 0)) ? 1 : 0);

  /* Logic: '<S9>/Logical Operator1' incorporates:
   *  Constant: '<S9>/ENTRSFAULT'
   */
  FlguCTransient = (uint8_T)(((((int32_T)ENTRSFAULT) != 0) && (((int32_T)
    TLE9278BQX_Diag_DW.Memory_PreviousInput) != 0)) ? 1 : 0);

  /* S-Function (Diag_Return_Addr_U8): '<S11>/C//C++ Code Block' */
  Diag_Return_Addr_U8_Outputs_wrapper((&(EECntSbcSC)),
    &TLE9278BQX_Diag_DW.CCCodeBlock_c);

  /* S-Function (Diag_Return_Addr_U8): '<S11>/C//C++ Code Block1' */
  Diag_Return_Addr_U8_Outputs_wrapper((&(EESbcSC)),
    &TLE9278BQX_Diag_DW.CCCodeBlock1_b);

  /* Gateway: TLE9278BQX_Diag/fc_Bkg/SC/Chart_SC */
  /* During: TLE9278BQX_Diag/fc_Bkg/SC/Chart_SC */
  /* Entry Internal: TLE9278BQX_Diag/fc_Bkg/SC/Chart_SC */
  /* Transition: '<S34>:2' */
  if (((int32_T)TLE9278BQX_Diag_DW.wk_c) == 0) {
    /* Transition: '<S34>:7' */
    TLE9278BQX_Diag_DW.wk_c = 1U;

    /* Inport: '<Root>/EECntSbcSC' */
    if (EECntSbcSC >= NUMSBCDC) {
      /* Transition: '<S34>:5' */
      TLE9278BQX_Diag_DW.eeSbcSC = 0U;

      /* Outputs for Function Call SubSystem: '<S11>/fc_EESbcSC' */
      /* Event: '<S34>:40' */
      TLE9278BQX_Diag_fc_EESbcSC(TLE9278BQX_Diag_DW.eeSbcSC,
        TLE9278BQX_Diag_DW.CCCodeBlock1_b, &TLE9278BQX_Diag_DW.CCCodeBlock_g);

      /* End of Outputs for SubSystem: '<S11>/fc_EESbcSC' */
    } else {
      /* Transition: '<S34>:4' */
      TLE9278BQX_Diag_DW.eeCntSbcSC = EECntSbcSC;
      TLE9278BQX_Diag_DW.eeCntSbcSC = (uint8_T)((int32_T)(((int32_T)
        TLE9278BQX_Diag_DW.eeCntSbcSC) + 1));

      /* Outputs for Function Call SubSystem: '<S11>/fc_EECntSbcSC' */
      /* Event: '<S34>:39' */
      TLE9278BQX_Diag_fc_EECntSbcSC(TLE9278BQX_Diag_DW.eeCntSbcSC,
        TLE9278BQX_Diag_DW.CCCodeBlock_c, &TLE9278BQX_Diag_DW.CCCodeBlock_o);

      /* End of Outputs for SubSystem: '<S11>/fc_EECntSbcSC' */
    }

    /* End of Inport: '<Root>/EECntSbcSC' */
  } else {
    /* Transition: '<S34>:8' */
    /* Transition: '<S34>:37' */
    /* Transition: '<S34>:38' */
  }

  /* Inport: '<Root>/SBCVioSC' incorporates:
   *  Inport: '<Root>/SBCBoostDOL'
   *  Inport: '<Root>/SBCBoostDSC'
   *  Inport: '<Root>/SBCBoostSOL'
   *  Inport: '<Root>/SBCBoostSSC'
   *  Inport: '<Root>/SBCVioOV'
   *  Inport: '<Root>/WkSBCSmpsStat'
   *  Inport: '<Root>/WkSBCSupStat0'
   *  Inport: '<Root>/WkSBCSupStat1'
   */
  /* Transition: '<S34>:10' */
  tmpEESbc = (uint8_T)((int32_T)(((((int32_T)((uint8_T)((((((SBCVioSC <<
    ((uint32_T)1)) | SBCVioOV) | (SBCBoostDSC << ((uint32_T)2))) | (SBCBoostDOL <<
    ((uint32_T)3))) | (SBCBoostSSC << ((uint32_T)4))) | (SBCBoostSOL <<
    ((uint32_T)5))))) | asr_s32(((int32_T)WkSBCSupStat1) & ((int32_T)0x02), 1U))
    | asr_s32(((int32_T)WkSBCSupStat0) & ((int32_T)0x04), 1U)) | (((((((int32_T)
    WkSBCSmpsStat) & ((int32_T)0x02)) * 4) | (((int32_T)WkSBCSmpsStat) &
    ((int32_T)0x04))) | asr_s32(((int32_T)WkSBCSmpsStat) & ((int32_T)0x40), 2U))
    | (((int32_T)WkSBCSmpsStat) & ((int32_T)0x20)))));

  /* Inport: '<Root>/EESbcSC' */
  TLE9278BQX_Diag_DW.eeSbcSC = (uint8_T)(EESbcSC | tmpEESbc);

  /* Chart: '<S11>/Chart_SC' incorporates:
   *  SubSystem: '<S11>/fc_EESbcSC'
   */
  /* Event: '<S34>:40' */
  TLE9278BQX_Diag_fc_EESbcSC(TLE9278BQX_Diag_DW.eeSbcSC,
    TLE9278BQX_Diag_DW.CCCodeBlock1_b, &TLE9278BQX_Diag_DW.CCCodeBlock_g);
  if (((int32_T)tmpEESbc) != 0) {
    /* Transition: '<S34>:16' */
    TLE9278BQX_Diag_DW.eeCntSbcSC = 0U;

    /* Outputs for Function Call SubSystem: '<S11>/fc_EECntSbcSC' */
    /* Event: '<S34>:39' */
    TLE9278BQX_Diag_fc_EECntSbcSC(TLE9278BQX_Diag_DW.eeCntSbcSC,
      TLE9278BQX_Diag_DW.CCCodeBlock_c, &TLE9278BQX_Diag_DW.CCCodeBlock_o);

    /* End of Outputs for SubSystem: '<S11>/fc_EECntSbcSC' */
  } else {
    /* Transition: '<S34>:15' */
  }

  /* S-Function (Diag_Return_Addr_U8): '<S12>/C//C++ Code Block' */
  Diag_Return_Addr_U8_Outputs_wrapper((&(EECntSbcUV)),
    &TLE9278BQX_Diag_DW.CCCodeBlock_e);

  /* S-Function (Diag_Return_Addr_U8): '<S12>/C//C++ Code Block1' */
  Diag_Return_Addr_U8_Outputs_wrapper((&(EESbcUV)),
    &TLE9278BQX_Diag_DW.CCCodeBlock1_fs);

  /* Gateway: TLE9278BQX_Diag/fc_Bkg/UV/Chart_UV */
  /* During: TLE9278BQX_Diag/fc_Bkg/UV/Chart_UV */
  /* Entry Internal: TLE9278BQX_Diag/fc_Bkg/UV/Chart_UV */
  /* Transition: '<S37>:2' */
  if (((int32_T)TLE9278BQX_Diag_DW.wk) == 0) {
    /* Transition: '<S37>:7' */
    TLE9278BQX_Diag_DW.wk = 1U;

    /* Inport: '<Root>/EECntSbcUV' */
    if (EECntSbcUV >= NUMSBCDC) {
      /* Transition: '<S37>:5' */
      TLE9278BQX_Diag_DW.eeSbcUV = 0U;

      /* Outputs for Function Call SubSystem: '<S12>/fc_EESbcUV' */
      /* Event: '<S37>:32' */
      TLE9278BQX_Diag_fc_EESbcUV(TLE9278BQX_Diag_DW.eeSbcUV,
        TLE9278BQX_Diag_DW.CCCodeBlock1_fs, &TLE9278BQX_Diag_DW.CCCodeBlock_k);

      /* End of Outputs for SubSystem: '<S12>/fc_EESbcUV' */
    } else {
      /* Transition: '<S37>:4' */
      TLE9278BQX_Diag_DW.eeCntSbcUV = EECntSbcUV;
      TLE9278BQX_Diag_DW.eeCntSbcUV = (uint8_T)((int32_T)(((int32_T)
        TLE9278BQX_Diag_DW.eeCntSbcUV) + 1));

      /* Outputs for Function Call SubSystem: '<S12>/fc_EECntSbcUV' */
      /* Event: '<S37>:31' */
      TLE9278BQX_Diag_fc_EECntSbcUV(TLE9278BQX_Diag_DW.eeCntSbcUV,
        TLE9278BQX_Diag_DW.CCCodeBlock_e, &TLE9278BQX_Diag_DW.CCCodeBlock_j);

      /* End of Outputs for SubSystem: '<S12>/fc_EECntSbcUV' */
    }

    /* End of Inport: '<Root>/EECntSbcUV' */
  } else {
    /* Transition: '<S37>:8' */
    /* Transition: '<S37>:35' */
    /* Transition: '<S37>:36' */
  }

  /* Inport: '<Root>/SBCVCanUV' incorporates:
   *  Inport: '<Root>/SBCVBSensUV'
   *  Inport: '<Root>/SBCVioUV'
   *  Inport: '<Root>/SBCVioWarn'
   *  Inport: '<Root>/WkSBCBusStat0'
   *  Inport: '<Root>/WkSBCSupStat0'
   *  Inport: '<Root>/WkSBCSupStat1'
   *  Inport: '<Root>/WkSBCWKStat2'
   */
  /* Transition: '<S37>:10' */
  tmpEESbc = (uint8_T)((int32_T)((((((int32_T)((uint8_T)((((SBCVCanUV <<
    ((uint32_T)1)) | SBCVioUV) | (SBCVBSensUV << ((uint32_T)2))) | (SBCVioWarn <<
    ((uint32_T)3))))) | (((int32_T)WkSBCSupStat0) & ((int32_T)0x01))) |
    (((int32_T)WkSBCSupStat1) & ((int32_T)0x01))) | asr_s32(((int32_T)
    WkSBCWKStat2) & ((int32_T)0x80), 5U)) | ((((int32_T)WkSBCBusStat0) &
    ((int32_T)0x01)) * 2)));

  /* Inport: '<Root>/EESbcUV' */
  TLE9278BQX_Diag_DW.eeSbcUV = (uint8_T)(EESbcUV | tmpEESbc);

  /* Chart: '<S12>/Chart_UV' incorporates:
   *  SubSystem: '<S12>/fc_EESbcUV'
   */
  /* Event: '<S37>:32' */
  TLE9278BQX_Diag_fc_EESbcUV(TLE9278BQX_Diag_DW.eeSbcUV,
    TLE9278BQX_Diag_DW.CCCodeBlock1_fs, &TLE9278BQX_Diag_DW.CCCodeBlock_k);
  if (((int32_T)tmpEESbc) != 0) {
    /* Transition: '<S37>:14' */
    TLE9278BQX_Diag_DW.eeCntSbcUV = 0U;

    /* Outputs for Function Call SubSystem: '<S12>/fc_EECntSbcUV' */
    /* Event: '<S37>:31' */
    TLE9278BQX_Diag_fc_EECntSbcUV(TLE9278BQX_Diag_DW.eeCntSbcUV,
      TLE9278BQX_Diag_DW.CCCodeBlock_e, &TLE9278BQX_Diag_DW.CCCodeBlock_j);

    /* End of Outputs for SubSystem: '<S12>/fc_EECntSbcUV' */
  } else {
    /* Transition: '<S37>:15' */
  }

  /* user code (Output function Trailer for TID1) */

  /* System '<S2>/fc_Bkg' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Start for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Diag_fc_Init_Start(void)
{
  /* Start for Constant: '<S6>/ID_VER_TLE9278BQX_DIAG_DEF' */
  IdVer_TLE9278BQX_Diag = ID_VER_TLE9278BQX_DIAG_DEF;
}

/* Output and update for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Diag_fc_Init(void)
{
  {
    /* user code (Output function Header for TID2) */

    /* System '<S2>/fc_Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    TLE9278BQX_Diag_initialize();

    /* Constant: '<S6>/ID_VER_TLE9278BQX_DIAG_DEF' */
    IdVer_TLE9278BQX_Diag = ID_VER_TLE9278BQX_DIAG_DEF;

    /* Constant: '<S6>/ZERO' */
    CntSBCFault = 0U;

    /* user code (Output function Trailer for TID2) */

    /* System '<S2>/fc_Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Model step function */
void TLE9278BQX_Diag_Bkg(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' incorporates:
   *  SubSystem: '<S2>/fc_Bkg'
   */
  TLE9278BQX_Diag_fc_Bkg();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' */
}

/* Model step function */
void TLE9278BQX_Diag_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_Diag_fc_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model initialize function */
void TLE9278BQX_Diag_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_Diag_fc_Init_Start();

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_TLE9278BQX_DIAG_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/