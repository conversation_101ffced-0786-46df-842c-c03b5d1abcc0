/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           LoadMgm.c
 **  File Creation Date: 01-Oct-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         LoadMgm
 **  Model Description:  Load variables are updated on the angular events, if engine is running, otherwise on 5ms event.
 **  Model Version:      1.52
 **  Model Author:       PanettaM - Thu Jul 29 13:37:42 2021
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: PanettaM - Fri Oct 01 10:02:50 2021
 **
 **  Last Saved Modification:  PanettaM - Fri Oct 01 09:12:03 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "LoadMgm_out.h"
#include "LoadMgm_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_LOADMGM_DEF             152U                      /* Referenced by: '<S4>/ID_VER_LOADMGM_DEF' */

/* Model id version define */
#define LOADBUF_SIZE                   2                         /* Referenced by:
                                                                  * '<S2>/LoadMisf_calc'
                                                                  * '<S10>/LoadMisf_calc'
                                                                  */

/* Load buffer size */
#define ONE                            1                         /* Referenced by:
                                                                  * '<S2>/LoadMisf_calc'
                                                                  * '<S10>/LoadMisf_calc'
                                                                  */

/* Define for 1 value */
#define TWO                            2                         /* Referenced by:
                                                                  * '<S2>/LoadMisf_calc'
                                                                  * '<S10>/LoadMisf_calc'
                                                                  */

/* Define for 2 value */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_LOADMGM_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENLOADMISF = 1;/* Referenced by:
                                                           * '<S2>/ENLOADMISF'
                                                           * '<S10>/ENLOADMISF'
                                                           */

/* Enable load storage for misfire detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T FOLOAD = -1;/* Referenced by:
                                                      * '<S2>/FOLOAD'
                                                      * '<S4>/FOLOAD'
                                                      * '<S10>/FOLOAD'
                                                      */

/* Force Load */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T Load;                         /* '<S1>/Merge' */

/* Engine Load */
uint8_T LoadByte;                      /* '<S1>/Merge2' */

/* Engine Load at low precision */
uint16_T LoadMisf;                     /* '<S1>/Merge1' */

/* Engine Load for misfire functionalities */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_LoadMgm;/* '<S1>/Merge3' */

/* Model id version */
STATIC_TEST_POINT uint16_T VtLoadBuf[2];/* '<S1>/Merge4' */

/* Buffer of the Load old values */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void LoadMgm_Init(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/LoadMgm_Init' incorporates:
   *  SubSystem: '<Root>/loadmgm_init'
   *
   * Block description for '<Root>/loadmgm_init':
   *  Load variables are calculated from the information retrieved from CAN
   *  or, if forcing is enabled (FOLOAD>=0), from the related tuneable
   *  parameter.
   */
  /* Switch: '<S4>/Switch' incorporates:
   *  Constant: '<S13>/Constant'
   *  Constant: '<S4>/FOLOAD'
   *  Inport: '<Root>/LoadCAN'
   *  RelationalOperator: '<S13>/Compare'
   */
  if (FOLOAD >= 0) {
    LoadMisf = (uint16_T)(((uint16_T)FOLOAD) << ((uint32_T)7));
  } else {
    LoadMisf = LoadCAN;
  }

  /* End of Switch: '<S4>/Switch' */

  /* Assignment: '<S4>/Assignment' */
  for (i = 0; i < 2; i++) {
    VtLoadBuf[(i)] = LoadMisf;
  }

  /* End of Assignment: '<S4>/Assignment' */

  /* DataTypeConversion: '<S4>/Data Type Conversion' */
  LoadByte = (uint8_T)(((uint32_T)LoadMisf) >> ((uint32_T)8));

  /* SignalConversion generated from: '<S4>/Load' */
  Load = LoadMisf;

  /* SignalConversion generated from: '<S4>/IdVer_LoadMgm' incorporates:
   *  Constant: '<S4>/ID_VER_LOADMGM_DEF'
   */
  IdVer_LoadMgm = ID_VER_LOADMGM_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/LoadMgm_Init' */
}

/* Model step function */
void LoadMgm_T5ms(void)
{
  int8_T VtLoadBuf_idx;

  /* RootInportFunctionCallGenerator generated from: '<Root>/LoadMgm_T5ms' incorporates:
   *  SubSystem: '<Root>/loadmgm_calc_on5ms'
   *
   * Block description for '<Root>/loadmgm_calc_on5ms':
   *  Load variables are calculated each 5ms event only if the engine is
   *  stopped. The information is retrieved from CAN or, if forcing is
   *  enabled (FOLOAD>=0), from the related tuneable parameter.
   */
  /* Outputs for Enabled SubSystem: '<S3>/loadmgm_calc' incorporates:
   *  EnablePort: '<S10>/Enable'
   *
   * Block description for '<S3>/loadmgm_calc':
   *  Load variables are calculated from the information retrieved from CAN
   *  or, if forcing is enabled (FOLOAD>=0), from the related tuneable
   *  parameter.
   */
  /* Outputs for Enabled SubSystem: '<S3>/load_calc_held' incorporates:
   *  EnablePort: '<S9>/Enable'
   *
   * Block description for '<S3>/load_calc_held':
   *  Variables are not updated when engine is running.
   */
  /* Logic: '<S3>/Logical Operator' incorporates:
   *  Constant: '<S7>/Constant'
   *  Inport: '<Root>/FlgSyncPhased'
   *  Inport: '<Root>/Rpm'
   *  RelationalOperator: '<S7>/Compare'
   *  RelationalOperator: '<S8>/Compare'
   */
  if ((((int32_T)Rpm) == 0) || (!FlgSyncPhased)) {
    /* Switch: '<S10>/Switch' incorporates:
     *  Constant: '<S10>/FOLOAD'
     *  Constant: '<S11>/Constant'
     *  Inport: '<Root>/LoadCAN'
     *  RelationalOperator: '<S11>/Compare'
     */
    if (FOLOAD >= 0) {
      Load = (uint16_T)(((uint16_T)FOLOAD) << ((uint32_T)7));
    } else {
      Load = LoadCAN;
    }

    /* End of Switch: '<S10>/Switch' */

    /* Chart: '<S10>/LoadMisf_calc' incorporates:
     *  Constant: '<S10>/ENLOADMISF'
     */
    /* Gateway: loadmgm_calc_on5ms/loadmgm_calc/LoadMisf_calc */
    /* During: loadmgm_calc_on5ms/loadmgm_calc/LoadMisf_calc */
    /* This stateflow stores the last LOADBUF_SIZE samples of the Load variable in the buffer VtLoadBuf, inserting these from the most recent (first position) to the oldest one (last position).
       At each step and before updating the buffer with the new Load value, this function calcates the output LoadMisf. This is equal to the current Load value, if the parameter ENLOADMISF is false, else to the MISF_TDC_DELAY-th element of the VtLoadBuf. */
    /* Entry Internal: loadmgm_calc_on5ms/loadmgm_calc/LoadMisf_calc */
    /* Transition: '<S12>:2' */
    if (((ENLOADMISF) && (MISF_TDC_DELAY > 0)) && (MISF_TDC_DELAY <= ((int8_T)
          LOADBUF_SIZE))) {
      /* SignalConversion generated from: '<S10>/LoadMisf' incorporates:
       *  SignalConversion generated from: '<S3>/VtLoadBuf_old'
       */
      /* Transition: '<S12>:10' */
      LoadMisf = VtLoadBuf[(int8_T)(MISF_TDC_DELAY - ((int8_T)ONE))];
    } else {
      /* SignalConversion generated from: '<S10>/LoadMisf' */
      /* Transition: '<S12>:12' */
      LoadMisf = Load;
    }

    /* Transition: '<S12>:25' */
    for (VtLoadBuf_idx = (int8_T)(((int8_T)LOADBUF_SIZE) - ((int8_T)TWO));
         VtLoadBuf_idx >= 0; VtLoadBuf_idx -= ((int8_T)ONE)) {
      /* Transition: '<S12>:4' */
      VtLoadBuf[VtLoadBuf_idx + ((int8_T)ONE)] = VtLoadBuf[(VtLoadBuf_idx)];

      /* Transition: '<S12>:5' */
    }

    /* Transition: '<S12>:7' */
    VtLoadBuf[0] = Load;

    /* End of Chart: '<S10>/LoadMisf_calc' */

    /* DataTypeConversion: '<S10>/Data Type Conversion' */
    LoadByte = (uint8_T)(((uint32_T)Load) >> ((uint32_T)8));
  }

  /* End of Logic: '<S3>/Logical Operator' */
  /* End of Outputs for SubSystem: '<S3>/load_calc_held' */
  /* End of Outputs for SubSystem: '<S3>/loadmgm_calc' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/LoadMgm_T5ms' */
}

/* Model step function */
void LoadMgm_TDC(void)
{
  int8_T VtLoadBuf_idx;

  /* RootInportFunctionCallGenerator generated from: '<Root>/LoadMgm_TDC' incorporates:
   *  SubSystem: '<Root>/loadmgm_calc'
   *
   * Block description for '<Root>/loadmgm_calc':
   *  Load variables are calculated each angular event from the information
   *  retrieved from CAN or, if forcing is enabled (FOLOAD>=0), from the
   *  related tuneable parameter.
   */
  /* Switch: '<S2>/Switch' incorporates:
   *  Constant: '<S2>/FOLOAD'
   *  Constant: '<S5>/Constant'
   *  Inport: '<Root>/LoadCAN'
   *  RelationalOperator: '<S5>/Compare'
   */
  if (FOLOAD >= 0) {
    Load = (uint16_T)(((uint16_T)FOLOAD) << ((uint32_T)7));
  } else {
    Load = LoadCAN;
  }

  /* End of Switch: '<S2>/Switch' */

  /* Chart: '<S2>/LoadMisf_calc' incorporates:
   *  Constant: '<S2>/ENLOADMISF'
   */
  /* Gateway: loadmgm_calc/LoadMisf_calc */
  /* During: loadmgm_calc/LoadMisf_calc */
  /* This stateflow stores the last LOADBUF_SIZE samples of the Load variable in the buffer VtLoadBuf, inserting these from the most recent (first position) to the oldest one (last position).
     At each step and before updating the buffer with the new Load value, this function calcates the output LoadMisf. This is equal to the current Load value, if the parameter ENLOADMISF is false, else to the MISF_TDC_DELAY-th element of the VtLoadBuf. */
  /* Entry Internal: loadmgm_calc/LoadMisf_calc */
  /* Transition: '<S6>:2' */
  if (((ENLOADMISF) && (MISF_TDC_DELAY > 0)) && (MISF_TDC_DELAY <= ((int8_T)
        LOADBUF_SIZE))) {
    /* SignalConversion generated from: '<S2>/LoadMisf' incorporates:
     *  SignalConversion generated from: '<S2>/VtLoadBuf_old'
     */
    /* Transition: '<S6>:10' */
    LoadMisf = VtLoadBuf[(int8_T)(MISF_TDC_DELAY - ((int8_T)ONE))];
  } else {
    /* SignalConversion generated from: '<S2>/LoadMisf' */
    /* Transition: '<S6>:12' */
    LoadMisf = Load;
  }

  /* Transition: '<S6>:25' */
  for (VtLoadBuf_idx = (int8_T)(((int8_T)LOADBUF_SIZE) - ((int8_T)TWO));
       VtLoadBuf_idx >= 0; VtLoadBuf_idx -= ((int8_T)ONE)) {
    /* Transition: '<S6>:4' */
    VtLoadBuf[VtLoadBuf_idx + ((int8_T)ONE)] = VtLoadBuf[(VtLoadBuf_idx)];

    /* Transition: '<S6>:5' */
  }

  /* Transition: '<S6>:7' */
  VtLoadBuf[0] = Load;

  /* End of Chart: '<S2>/LoadMisf_calc' */

  /* DataTypeConversion: '<S2>/Data Type Conversion' */
  LoadByte = (uint8_T)(((uint32_T)Load) >> ((uint32_T)8));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/LoadMgm_TDC' */
}

/* Model initialize function */
void LoadMgm_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint16_T Load, LoadMisf;
uint8_T LoadByte;
void LoadMgm_Init(void);
void LoadMgm_T5ms(void);
void LoadMgm_TDC(void);
void LoadMgm_Init(void)
{
  Load= 0;
  LoadMisf= 0;
  LoadByte= 0;
}

void LoadMgm_T5ms(void)
{
}

void LoadMgm_TDC(void)
{
}

#endif                                 /* _BUILD_LOADMGM_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_coverage                                                          *
 * simulink_requirements                                                      *
 * simulink_test                                                              *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/