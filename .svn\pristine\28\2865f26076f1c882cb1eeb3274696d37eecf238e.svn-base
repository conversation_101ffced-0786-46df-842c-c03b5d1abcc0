/******************************************************************************************************************************/
/* $HeadURL:: http://***********:8080/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_LL_03_WDT/tree/EEPCOM/diagmgm_eep.c   $   */
/* $ Description:                                                                                                             */
/* $Revision:: 164860 $                                                                                                       */
/* $Date:: 2021-06-11 17:17:26 +0200 (ven, 11 giu 2021)   $                                                                   */
/* $Author:: SantoroR                $                                                                                        */
/******************************************************************************************************************************/

#ifndef _TLE9278BQX_COM_EEP_C
#define _TLE9278BQX_COM_EEP_C

#define __TLE9278BQX_COM_EE_C
#include "rtwtypes.h"

#ifdef _BUILD_TLE9278BQX_COM_

uint16_T EECntSBCResend = 0u;

#endif

#undef  __TLE9278BQX_COM_EE_C

#endif

