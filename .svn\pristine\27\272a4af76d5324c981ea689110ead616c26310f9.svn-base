#-------------------------------------------------------------------------------
# Name:        modulo1
# Purpose:
#
# Author:      SalimbeniT
#
# Created:     05/08/2013
# Copyright:   (c) SalimbeniT 2013
# Licence:     <your licence>
#-------------------------------------------------------------------------------
from ctypes import *
import time
import math
import csv
import os
import webbrowser
def main():
    pass

if __name__ == '__main__':
    main()
PeriodoADC=10
righe=[]

s=input("\nRecorder File Name:") #input del file contenente i dati della prova
in_file = open("RecorderExport\\"+s+".txt","r") #apertura in lettura dei file contenente la registrazione
RecorderName=s

configFile=open("CanMgmInTestConfig.txt","r")


StartFound=0
StopFound=0

s=input("\nTest Start Time:") #input dell'istante di inizio della prova
if s!="": #nel caso l'utente specifichi l'istante lo start sara quello specificato
    Start=float(s)
    StartFound=1

s=input("\nTest Stop Time:") #input dell'istante di fine della prova
if s!="": #nel caso l'utente specifichi l'istante lo stop sara quello specificato
    Stop=float(s)
    StopFound=1

Data=[]
in_file.seek(0)
Flag=0
for line in in_file:

    if ("TimeStamp") in line:
        line=line[1:-2]
        Intestazione=line.split('"\t"') #tupla contenete le stringhe contenenti il nome delle colonne del file di acquisizione
        line=next(in_file)
        line=next(in_file)
        Flag=1
    if Flag==1:
        Data.append((line[:-1].split('\t')))  #Data e' una lista di tuple contenete i dati parsati dal file di acquisizione


"""Associa nomi segnali a nomi variabili acquisite"""
Coppie=[]
for line in configFile:
    if "Name" in line:
        NomeVariabile=line.split(':')[1].strip()
        line=next(configFile)
        line=next(configFile)
        NomeSegnale=line.split(':')[1].strip()
        line=next(configFile)
        Periodo=int(line.split(':')[1].strip())
        Coppie.append((NomeVariabile,NomeSegnale,Periodo))


#NB: la tupla di Data ha la stessa dimensione della variabile Intestazione

for riga in Data:
    if (riga[1]!="0.00")&(riga[2]!="0.00")&(StartFound==0)&(StopFound==0): #se non e stato specificato un istante di start della prova si cerca la prima riga diversa da zero e si setta lo start
        Start=int(float(riga[0])+1)
        StartFound=1

    if (riga[1]=="0.00")&(riga[2]=="0.00")&(StartFound==1)&(StopFound==0): #se non e stato specificato un istante di stop della prova si cerca la prima riga uguale a zero dopo l'istante di start e si setta lo stop
        Stop=int(float(riga[0]))
        StopFound=1

print("\nStart: %s" %Start)
print("\nStop: %s" %Stop)

""" INIZIO TEST"""

in_file.seek(0)
Fail=0
Failsegnale=[0]*len(Coppie)
Errore=[0]*len(Coppie)
err=0
outfile=open("CanMgmInTestReport.txt","w")
#
for k in range (0,len(Data)-1): #per ogni riga contenuta in Data
    for j in range (1,len(Coppie)): #per ogni segnale sotto test
        if (float(Data[k][0])>=Start)&(float(Data[k][0])<=Stop): #inizia a prendere in considerazione i dati successivi all'istante di start del test deciso dall'utente.


            Var=float(Data[k][Intestazione.index(Coppie[j][0])])
            Sig=float(Data[k][Intestazione.index(Coppie[j][1])])

            if (Errore[j]==(Coppie[j][2]/PeriodoADC))&(abs(Var-Sig)>=1):
                Failsegnale[j]=1
                err=1
                Fail=1
            else:
                Errore[j]=0
                err=0


            if abs(Var-Sig)>=1:

                Errore[j]=Errore[j]+1

            if err==1:
                outfile.write("*********ERRORE***********\n")
                outfile.write("TIME = %s \n%s = %s \nCorrect Value: %s\n\n"% (Data[k][0],Coppie[j][0],Var,Sig))
                err=0



outfile.write("\nRecorder File Name: %s.txt"%RecorderName)
outfile.write("\n\n-------------- TEST RESULT --------------\n\n")
for i in range (0,len(Coppie)):
    if Failsegnale[i]==0:
        outfile.write("Test PASSED for %s                \n\n" %(Coppie[i][0]))
    else:
        outfile.write("Test FAILED for %s <---- !!! \n\n" %(Coppie[i][0]))






if Fail==0:
    outfile.write("*******************************************\n")
    outfile.write("*              TEST PASSED                *\n")
    outfile.write("*******************************************\n")
    outfile.write("Test Completed Successfully for all Signals")

else:
    outfile.write("*******************************************\n")
    outfile.write("*              TEST FAILED                *\n")
    outfile.write("*******************************************\n")


print("\n\nCAN TEST COMPLETED\n\n")




outfile.close()
in_file.close()
configFile.close()

path=str(os.path.abspath(__file__)[0:-27])

s=input("Open Report File? [y/n]:")
if s=="y":
    osCommandString = "notepad.exe " + path + "CanMgmInTestReport.txt"
    os.system(osCommandString)

