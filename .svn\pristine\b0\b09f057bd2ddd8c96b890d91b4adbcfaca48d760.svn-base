/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************************************************
**  SWC             :  FLASH
**  Filename        :  Flashsuspend.c
**  Created on      :  22-giu-2020 10:00:00
**  Original author :  CarboniM
**  Integration history: 
**  -  Standard software driver for C55 Flash from "STM - SPC57xKLS-FLASH Driver for C55: Package version 0.2.0"
**          
*******************************************************************************************************************/

#ifdef  _BUILD_FLASH_

#pragma ghs startnomisra

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include    "ssd_c55.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/

static const unsigned short FlashSuspend_C[] = 
{
#if (TARGET_TYPE == SPC574K2_CUT24) || (TARGET_TYPE == SPC574K2)     /* Total Size = 126 half words */

    0x0080, 0x1821, 0x06F0, 0xD3F1, 0xD501, 0x0136, 0x4807, 
    0x480F, 0x4907, 0x9074, 0x30E6, 0x0040, 0x2A07, 0xE603,
    0xC056, 0xE804, 0xC076, 0x18A7, 0x8004, 0xC035, 0x1867, 
    0xC814, 0xE652, 0x1867, 0xC810, 0xE604, 0x1867, 0xC808,
    0xE607, 0x1867, 0xC804, 0xE62F, 0x1867, 0xC802, 0xE22C, 
    0x1867, 0xC801, 0xE20F, 0x1867, 0xC810, 0xE609, 0x4917,
    0x9074, 0x1867, 0xC804, 0xE632, 0x4937, 0x9074, 0xE82F, 
    0x4927, 0x9074, 0xE82C, 0x1867, 0xC810, 0xE60D, 0xC075,
    0x65C7, 0x18E7, 0xC51F, 0xD075, 0x4807, 0x2007, 0x00E7, 
    0x1887, 0xA8A0, 0xE4FC, 0xE80C, 0xC075, 0x65E7, 0x18E7,
    0xC51F, 0xD075, 0x4807, 0x2007, 0x00E7, 0x7007, 0xA940, 
    0xE4FC, 0xC035, 0x1867, 0xC808, 0xE609, 0x4947, 0x9074,
    0x1867, 0xC802, 0xE609, 0x4967, 0x9074, 0xE806, 0x1867, 
    0xC802, 0xE603, 0x4957, 0x9074, 0xC075, 0x70E0, 0xCC00,
    0xE6FD, 0xC075, 0x61F7, 0x18E7, 0xC51F, 0xD075, 0x30E6, 
    0x0048, 0x2A07, 0xE608, 0x7FE3, 0xFB78, 0x1800, 0xD000,
    0x0002, 0x1800, 0xD000, 0x01F7, 0x0173, 0xC3F1, 0xC501, 
    0x20F1, 0x0090, 0x0004, 0x3038, 0x3031, 0x3346, 0x4646

#endif

};

extern void * FlashFunctionPointer;
extern void FlashFunctionLoader(unsigned long *functionBuffer, uint32_T functionSize);


/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FlashSuspend - Function description
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/    
uint32_T FlashSuspend (PSSD_CONFIG pSSDConfig, uint8_T *suspendState)
{
#pragma ghs nowarning 171
    FlashFunctionLoader( (unsigned long*)FlashSuspend_C, sizeof(FlashSuspend_C)/2);
    return ((PFLASHSUSPEND)FlashFunctionPointer)(pSSDConfig, suspendState);
#pragma ghs endnowarning /* warning #171-D: invalid type conversion */
}
/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/* None */
#pragma ghs endnomisra

#endif /*  _BUILD_FLASH_ */
