/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  MSparkCmd
**  Filename        :  MSparkCmd.c
**  Created on      :  30-mar-2021 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifdef _BUILD_MSPARKCMD_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "msparkcmd.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
uint8_T  NMSparkPrg[N_CYL_MAX];
uint8_T  VtThrMisfPlaSel[N_CYL_MAX];
uint32_T CntVCapInOn[PMOS_MAX];
uint32_T CylPlaMOS_idx;
uint32_T CylPlaMOSRes_idx;
uint32_T DeltaSparkTime[N_CYL_MAX];
uint8_T  VtStPlasObjDBuff[N_CYL_MAX];
uint8_T  EffNMSpark[N_CYL_MAX];
uint8_T  PtFaultTrigger;
uint8_T  FlgWrongIGNIn[N_CYL_MAX];
/* Ignition input command counter */
uint32_T CntIGNInOff[N_CYL_MAX];
uint32_T CntIGNTrgInOn[N_CYL_MAX];
uint16_T VtDelayBkEnEOA[N_CYL_MAX];
uint16_T VtDeltaBkEnEOA[N_CYL_MAX];
/* TSpark correction */
uint16_T IPriCorrCyl = 0U;
/* Cylinder indexes */
uint32_T CylPlaAbsOff_idx;
int16_T TimeFixedTrigIn;
const uint8_T DiagCoil[N_CYL_MAX] = {DIAG_COIL_0, DIAG_COIL_1, DIAG_COIL_2, DIAG_COIL_3, DIAG_COIL_4, DIAG_COIL_5, DIAG_COIL_6, DIAG_COIL_7};
const uint8_T DiagPri[N_CYL_MAX] = {DIAG_PRI_A, DIAG_PRI_B, DIAG_PRI_A, DIAG_PRI_B, DIAG_PRI_A, DIAG_PRI_B, DIAG_PRI_A, DIAG_PRI_B};

/* STUB */
uint8_T  EffNIgnError[N_CYL_MAX];
uint8_T SACmdInLevErrNo = 0u;
uint8_T  FlgWrongSpark[N_CYL_MAX];
uint8_T  VtILPGlitch[N_CYL_MAX];
uint8_T SACmdInLevCirBuff[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
/* Flag used to reset the system in case of GTM diagnosis */
uint8_T Flg5PreIgnition;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/* CONST */
static const uint8_T DiagSecOl[N_CYL_MAX] = {DIAG_SEC_OL_0, DIAG_SEC_OL_1, DIAG_SEC_OL_2, DIAG_SEC_OL_3, DIAG_SEC_OL_4, DIAG_SEC_OL_5, DIAG_SEC_OL_6, DIAG_SEC_OL_7};
static const uint8_T DiagTrigger[N_CYL_MAX] = {DIAG_TRIGGER_0, DIAG_TRIGGER_1, DIAG_TRIGGER_2, DIAG_TRIGGER_3, DIAG_TRIGGER_4, DIAG_TRIGGER_5, DIAG_TRIGGER_6, DIAG_TRIGGER_7};
static const uint8_T CylToCmdInPin[N_CYL_MAX] = { IP_IgnCmd_1, IP_IgnCmd_2, IP_IgnCmd_3, IP_IgnCmd_4, IP_IgnCmd_5, IP_IgnCmd_6, IP_IgnCmd_7, IP_IgnCmd_8};
static const uint8_T DiagSparkEv[N_CYL_MAX] = {DIAG_SPARK_EV_A, DIAG_SPARK_EV_B, DIAG_SPARK_EV_A, DIAG_SPARK_EV_B, DIAG_SPARK_EV_A, DIAG_SPARK_EV_B, DIAG_SPARK_EV_A, DIAG_SPARK_EV_B};
static const uint8_T DiagIonChannel[N_CH_MAX] = {DIAG_ION_CH_A, DIAG_ION_CH_B, DIAG_ION_CH_C, DIAG_ION_CH_D};

static uint8_T  CntIGNInTot[N_CYL_MAX];
static uint8_T  FlgVtILeadPeakOL[N_CYL_MAX];
static uint8_T  VtCmdInPinLev[N_CYL_MAX];
static uint16_T SACmdInLevErr[N_CYL_MAX];
static uint16_T SACmdInLevOk[N_CYL_MAX];

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : MSparkCmd_Init
**
**   Description:
**    Reset the values of the module variables. The global variables (outputs) 
**    and the static variables (states) are reset to the default values by this function.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_Init(void)
{
    uint8_T cyl;
    
    SAout = 0;
    
    for(cyl=0u;cyl<N_CYLINDER;cyl++)
    {
        NMSparkPrg[cyl] = 0u;
        FlgWrongIGNIn[cyl] = 0u;
    }
    TimeFixedTrigIn = TIMFIXEDTRIGIN;
}

/******************************************************************************
**   Function    : MSparkCmd_SparkOn
**
**   Description:
**    Increments Trigger ON edge counter
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_SparkOn(uint8_T cyl)
{
    CntIGNTrgInOn[cyl] = CntIGNTrgInOn[cyl] + 1u;
    CntIGNInTot[cyl] = 1u;
}

/******************************************************************************
**   Function    : MSparkCmd_SparkOff
**
**   Description:
**    - Increments Trigger OFF edge counter
**    - Manage CntIGNInTot
**    - Call CoilTarget Closed Loop
**    - Call PlaCtrl
**    - Manage DIAG_COIL
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_SparkOff(uint8_T cyl)
{
    uint8_T  stdiag;
    uint8_T  ptfault;

    CntIGNInOff[cyl] = CntIGNInOff[cyl] + 1u;
    CylPlaAbsOff_idx = cyl;
    
    /* Writes in scalar variable SparkPlug aging correction factor */
    IPriCorrCyl = VtIPriCorr[cyl];

    if (CntIGNInTot[cyl] == 1u)
    {
        if ((((uint32_T)((uint32_T)GNMINEFFTTM * (uint32_T)TIMFIXEDTRIGIN) >> 7) <= EffDwellTrigTime[cyl]) && (EffDwellTrigTime[cyl] <= ((uint32_T)((uint32_T)GNMAXEFFTTM * (uint32_T)TIMFIXEDTRIGIN) >> 7)))
        {
            CntIGNInTot[cyl] = 2u;
        }
        else
        { 
            CntIGNInTot[cyl] = 4u;
        }
    }

#ifdef _BUILD_COILTARGET_
    CoilTarget_Tdc();       /* Should be called CoilTarget_SparkOff */
#endif

#ifdef _BUILD_COILANGPATTERN_
    VtStPlasObjDBuff[cyl] = VtStPlasObj[cyl];   /* Latch Plasma configuration of next EOA IonSignal */
    CoilAngPattern_SparkOff();          /* Should be called CoilAngPattern_SparkOff */
#endif

    if (VtILeadPeak[cyl] > THRILPOLDIAG)
    {
        FlgVtILeadPeakOL[cyl] = 0u;
        if (VtILeadPeak[cyl] < ThLeadDiag)
        {
            DiagMgm_SetDiagState(DiagCoil[cyl], NO_PT_FAULT, &stdiag);
        }
        else { /* MISRA */ }
    }
    else
    {
        ptfault = CIRCUIT_OPEN;
        FlgVtILeadPeakOL[cyl] = 1u;
        DiagMgm_SetDiagState(DiagCoil[cyl], ptfault, &stdiag);
    }
}

/******************************************************************************
**   Function    : MSparkCmd_TDC
**
**   Description:
**    All the vectors are indexed using MisfAbsTdc
**    - Manage DIAG_TRIGGER_cyl
**    - Manage DIAG_SPARK_cyl
**    - Manage DIAG_PRI_A/B
**    - Manage FlgWrongIGNIn and PtFaultTrigger
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_TDC(void)
{
    uint8_T diagPriEn;
    uint8_T stDiag = NO_FAULT;
    uint8_T ptfaultsec = NO_PT_FAULT;
    uint8_T ptfaultpri = NO_PT_FAULT;
    uint8_T ptfaulttrigger = NO_PT_FAULT;
    uint8_T flgDiagCoilBuck = 0u;
    uint8_T pMosIdx;
    uint8_T diagSAEn = 0u;
    uint8_T ptfaultspark = NO_PT_FAULT;

    /* To be removed */
    TimeFixedTrigIn = TIMFIXEDTRIGIN;

    if ((MisfAbsTdc & 0x01u) != 0u)
    {
        if((VtRec[REC_IGN_OFF_0 + MisfAbsTdc] != 0U) || (StDiag[DIAG_BUCK_B] == FAULT))
        {
            flgDiagCoilBuck = 1u;
        }
        else
        {
            flgDiagCoilBuck = 0u;
        }
    }
    else
    {
        if((VtRec[REC_IGN_OFF_0 + MisfAbsTdc] != 0U) || (StDiag[DIAG_BUCK_A] == FAULT))
        {
            flgDiagCoilBuck = 1u;
        }
        else
        {
            flgDiagCoilBuck = 0u;
        }
    }

    if ((FlgAbsTdc[MisfAbsTdc] != 0u) && (KeyEnDiagSignal != 0u))
    {
        /* Verify Input Trigger coherence */
        if (CntIGNInTot[MisfAbsTdc] < 2u)
        {
            /* DICHIARO MIS_FIRE */
            PtFaultTrigger = SIGNAL_INVALID;

            if (flgDiagCoilBuck == 0u)
            {
                FlgWrongIGNIn[MisfAbsTdc] = 1u;
                ptfaulttrigger = GENERAL_ELECTRIC_FAILURE;
            }
            else
            {
                FlgWrongIGNIn[MisfAbsTdc] = 0u;
                ptfaulttrigger = NO_PT_FAULT;
            }
        }
        else if (CntIGNInTot[MisfAbsTdc] == 3u)
        {
            FlgWrongIGNIn[MisfAbsTdc] = 1u;
            /* DICHIARO MIS_FIRE */
            PtFaultTrigger = SIGNAL_INVALID;
            ptfaulttrigger = GENERAL_ELECTRIC_FAILURE; //CIRCUIT_SHORT_TO_VCC
        }
        else
        {
            if (CntIGNInTot[MisfAbsTdc] >= 4u)
            {
                FlgWrongIGNIn[MisfAbsTdc] = 1u;
                ptfaulttrigger = SIGNAL_FREQUENCY_INCORRECT;
            }
            else
            {
                FlgWrongIGNIn[MisfAbsTdc] = 0u;
                ptfaulttrigger = NO_PT_FAULT;
            }

            if (FlgVtILeadPeakOL[MisfAbsTdc] == 0u)
            {
                /* Analisi Misfire */
                if (EffNMSpark[MisfAbsTdc] == 0u)
                {
                    /* No spark detected by secondary current
                       Spark event circuit diagnosis not possible */
                    diagSAEn = 0u;

                    if (ENIONPHASEMISF != 0u)
                    {
                        if ((StPhase[MisfAbsTdc] == TH_START_FOUND) && (IntIon[MisfAbsTdc] >= THRINTSTARTFOUND) && (StMisfEOA[MisfAbsTdc] != EOA_NOT_EXE))
                        {
                            PtFaultTrigger = NO_PT_FAULT;
                        }
                        else
                        {
                            PtFaultTrigger = SIGNAL_INVALID;
                        }
                    }
                    else
                    {
                        PtFaultTrigger = SIGNAL_INVALID;
                    }
                }
                else
                {
                    if (VtILeadPeak[MisfAbsTdc] > THRILPSPDIAG)
                    {
                        /* Spark event circuit diagnosis can be performed */
                        diagSAEn = 1u;
                        if(VtCntIgnWaitSpEvnt[MisfAbsTdc & 0x01u] > THRCNTSPEVNT)
                        {
                            ptfaultspark = INTERNAL_ELECTRONIC_FAILURE;
                        }
                        else
                        {
                            ptfaultspark = NO_PT_FAULT;
                        }
                    }

                    PtFaultTrigger = NO_PT_FAULT;
                }

                diagPriEn = 1u;
                ptfaultpri = NO_PT_FAULT;
            }
            else
            {
                /* Analisi Misfire */
                if (EffNMSpark[MisfAbsTdc] == 0u)
                {
                    PtFaultTrigger = SIGNAL_INVALID;
                    diagPriEn = 0u;
                }
                else
                {
                    if ((StPhase[MisfAbsTdc] == TH_START_FOUND) && (IntIon[MisfAbsTdc] >= THRINTSTARTFOUND) && (StMisfEOA[MisfAbsTdc] != EOA_NOT_EXE))
                    {
                        PtFaultTrigger = NO_PT_FAULT;
                        diagPriEn = 1u;
                        ptfaultpri = INTERNAL_ELECTRONIC_FAILURE;
                    }
                    else if (StMisfEOA[MisfAbsTdc] == EOA_NOT_EXE)
                    {
                        PtFaultTrigger = NO_PT_FAULT;
                        diagPriEn = 0u;
                    }
                    else
                    {
                        PtFaultTrigger = SIGNAL_INVALID;
                        diagPriEn = 0u;
                    }
                }
            }

            if (diagPriEn != 0u)
            {
                DiagMgm_SetDiagState(DiagPri[MisfAbsTdc], ptfaultpri, &stDiag);
            }
            else
            {
                /* N.D. */
            }

            if (diagSAEn != 0u)
            {
                DiagMgm_SetDiagState(DiagSparkEv[MisfAbsTdc], ptfaultspark, &stDiag);
            }

            if(MisfAbsTdc >= PMOS_NUMBER)
            {
                pMosIdx = MisfAbsTdc - PMOS_NUMBER;
            }
            else
            {
                pMosIdx = MisfAbsTdc;
            }
            
            MSparkCmd_DiagIonChannel();
        }

        #ifdef _BUILD_DIAGMGM_
        DiagMgm_SetDiagState(DiagTrigger[MisfAbsTdc], ptfaulttrigger, &stDiag);
        #endif
    }
    else
    {
        PtFaultTrigger = NO_PT_FAULT;
        EffNIgnError[MisfAbsTdc] = 0u;
        FlgWrongSpark[MisfAbsTdc] = 0u;
        DeltaSparkTime[MisfAbsTdc] = 0u;
        FlgWrongIGNIn[MisfAbsTdc] = 0u;
    }

    MSparkCmd_DiagSecOl();

    MSparkCmd_DiagTrigRec();

    EffNMSpark[MisfAbsTdc] = 0u;
    
    CntIGNInTot[MisfAbsTdc] = 0u;
}


/******************************************************************************
**   Function    : MSparkCmd_ISecDma
**
**   Description:
**    - calculates EffNMSpark based on VtIShotPeak
**
**   Parameters :
**    cyl: cylinder index
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#pragma ghs section text=".vletext_c2"
void MSparkCmd_ISecDma(uint8_T cyl)
{
    if(VtIShotPeak[cyl] >= THRPEAKISEC)
    {
        FlgOLSpark[cyl] = 0u;
        EffNMSpark[cyl]++; /* SPARK EXEC */
    }
}
#pragma ghs section text=default

/******************************************************************************
**   Function    : MSparkCmd_PostIgnitionParameter
**
**   Description:
**    Calculates: * VtCmdInPinLev
**                * VtDelayBkEnEOA
**                * VtDeltaBkEnEOA
**                * VtDwellTimeEOA
**
**   Parameters :
**    cyl: cylinder index
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_PostIgnitionParameter(uint8_T cyl)
{
    uint8_T cmdInPinLev;
    uint8_T tmpDiag;

    /* Safety : Spark advance check */
    /* Get TrigIn pin level */
    cmdInPinLev = Dio_ReadChannel((uint16_T)CylToCmdInPin[cyl]);
    VtCmdInPinLev[cyl] = cmdInPinLev;
}

/******************************************************************************
**   Function    : MSparkCmd_ThrMisfPlaSel
**
**   Description:
**    Calculates: * VtThrMisfPlaSel
**                * NMSparkPrg
**
**   Parameters :
**    cyl: cylinder index
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_ThrMisfPlaSel(uint8_T cyl)
{
    if(VtStPlasObj[cyl] != ION_ST_SEL)
    {
        if(EPWSMISFEN != 0u)
        {
            NMSparkPrg[cyl] = 0u;
            /* Calculate misfire thr from TBMISFTHRPLA */
            VtThrMisfPlaSel[cyl] = 1u;
        }
        else
        {
            NMSparkPrg[cyl] = 1u;
            /* Reset thr from TBMISFTHRPLA selector */
            VtThrMisfPlaSel[cyl] = 0u;
        }
    }
    else
    {
        NMSparkPrg[cyl] = 0u;
        VtThrMisfPlaSel[cyl] = 0u;
    }
}

/******************************************************************************
**   Function    : MSparkCmd_SparkEv
**
**   Description:
**    Performs GTM diagnostic routine
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_SparkEv(void)

{
    static uint32_T SACmdInLevErrSumStart = MAX_uint32_T;
    static uint8_T circularBuffIndex[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
    static uint8_T circularBuffIndexTest[N_CYL_MAX] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
    static uint8_T oldSaCmdInLevErrTestEn = 0u;
    uint32_T tmpEESACmdInLevErrSum;
    uint8_T testErrVal;
    uint8_T stdiag;
    uint8_T i;

    /* Safety : Spark advance check */
    /* Increment Cricular buffer indexer up to the number of cylecs to be observed*/
    circularBuffIndex[SparkCyl] = (circularBuffIndex[SparkCyl] + 1u);
    if(circularBuffIndex[SparkCyl] >= SACMDINLEVERRCYLES)
    {
        circularBuffIndex[SparkCyl] = 0u;
    }
    if ((SACMDINLEVERRTESTEN == 0u) && (oldSaCmdInLevErrTestEn == 1u))
    {
        // Test Buffer was running on previous cycle, reset of counter for correct behaviour is needed
        SACmdInLevErrNo = 0u;
        for (i = 0u; i< N_CYL_MAX; i++)
        {
            SACmdInLevErr[i] = 0u;
            SACmdInLevOk[i] = 0u;
            SACmdInLevCirBuff[i] = 0u;
        }
    }
    else if ((SACMDINLEVERRTESTEN == 1u) && (oldSaCmdInLevErrTestEn == 0u))
    {
        for (i = 0u; i< N_CYL_MAX; i++)
        {
            // Alligne counters
            circularBuffIndexTest[i] = circularBuffIndex[i];
            // Reset counters
            SACmdInLevErr[i] = 0u;
            SACmdInLevOk[i] = 0u;
            SACmdInLevCirBuff[i] = 0u;
        }
        // Reset accumulator
        SACmdInLevErrNo = 0u;
    }
    else
    {
        // Remove old value from counter
        SACmdInLevErrNo = SACmdInLevErrNo - UTILS_CountSetBits((uint32_T)SACmdInLevCirBuff[SparkCyl]);
    }
    
    if (SACMDINLEVERRTESTEN == 0u)
    {
        /* Confronto col valore atteso */
        if (VtCmdInPinLev[SparkCyl] != CMDINLEVELOFF )
        {
            //caso d'errore
            SACmdInLevErr[SparkCyl]++;
            // Set corresponding bit in circular buffer
            SACmdInLevCirBuff[SparkCyl] = (SACmdInLevCirBuff[SparkCyl] | (uint8_T)(1u << circularBuffIndex[SparkCyl]));
        }
        else
        {
            SACmdInLevOk[SparkCyl]++;
            // Clear corresponding bit in circular buffer
            SACmdInLevCirBuff[SparkCyl] = (SACmdInLevCirBuff[SparkCyl] & (uint8_T)(~(uint8_T)(1u << circularBuffIndex[SparkCyl])));
        }
    }
    else
    {
        circularBuffIndexTest[SparkCyl] = (circularBuffIndexTest[SparkCyl] + 1u) & 0x0Fu;

        switch(SparkCyl)
        {
            case 0:
            testErrVal = (uint8_T)((SACMDINERRCIRCULARBUF0 & ((uint16_T)((uint16_T)1u << circularBuffIndexTest[SparkCyl]))) != 0u);
            break;
            case 1:
            testErrVal = (uint8_T)((SACMDINERRCIRCULARBUF1 & ((uint16_T)((uint16_T)1u << circularBuffIndexTest[SparkCyl]))) != 0u);
            break;
            case 2:
            testErrVal = (uint8_T)((SACMDINERRCIRCULARBUF2 & ((uint16_T)((uint16_T)1u << circularBuffIndexTest[SparkCyl]))) != 0u);
            break;
            case 3:
            testErrVal = (uint8_T)((SACMDINERRCIRCULARBUF3 & ((uint16_T)((uint16_T)1u << circularBuffIndexTest[SparkCyl]))) != 0u);
            break;
            case 4:
            testErrVal = (uint8_T)((SACMDINERRCIRCULARBUF4 & ((uint16_T)((uint16_T)1u << circularBuffIndexTest[SparkCyl]))) != 0u);
            break;
            case 5:
            testErrVal = (uint8_T)((SACMDINERRCIRCULARBUF5 & ((uint16_T)((uint16_T)1u << circularBuffIndexTest[SparkCyl]))) != 0u);
            break;
            case 6:
            testErrVal = (uint8_T)((SACMDINERRCIRCULARBUF6 & ((uint16_T)((uint16_T)1u << circularBuffIndexTest[SparkCyl]))) != 0u);
            break;
            case 7:
            testErrVal = (uint8_T)((SACMDINERRCIRCULARBUF7 & ((uint16_T)((uint16_T)1u << circularBuffIndexTest[SparkCyl]))) != 0u);
            break;
            default:
            testErrVal = 0u;
            break;
        }
        
        /* Confronto col valore atteso */
        if (testErrVal != 0u)
        {
            //caso d'errore
            SACmdInLevErr[SparkCyl]++;
            // Set corresponding bit in circular buffer
            SACmdInLevCirBuff[SparkCyl] = (SACmdInLevCirBuff[SparkCyl] | (uint8_T)((uint8_T)1u << circularBuffIndex[SparkCyl]));
        }
        else
        {
            SACmdInLevOk[SparkCyl]++;
            // Clear corresponding bit in circular buffer
            SACmdInLevCirBuff[SparkCyl] = (SACmdInLevCirBuff[SparkCyl] & (uint8_T)(~(uint8_T)((uint8_T)1u << circularBuffIndex[SparkCyl])));
        }
    }

    oldSaCmdInLevErrTestEn = SACMDINLEVERRTESTEN;
    
    // Update counter with updated information
    SACmdInLevErrNo = SACmdInLevErrNo + UTILS_CountSetBits(((uint32_T)SACmdInLevCirBuff[SparkCyl]) & 0x000000FFu);

    EESACmdInLevErrNoMax = max(SACmdInLevErrNo, EESACmdInLevErrNoMax);

    // Test if counter is over the threshols
    if (SACmdInLevErrNo >= SACMDINLEVERRNOTHR)
    {
        /* Set reset flag */
        Flg5PreIgnition = 1U;
    }

    /* Update total GTM error counter in EEPROM */
    if(SACmdInLevErrSumStart == MAX_uint32_T)
    {
        /* Initialize value with stored EE counter, this is done only the first time since poweron */
        SACmdInLevErrSumStart = EESACmdInLevErrSum;
    }
    /* Sum in temporary accumulator the counters */
    tmpEESACmdInLevErrSum = SACmdInLevErrSumStart + SACmdInLevErr[0] + SACmdInLevErr[1] + SACmdInLevErr[2] + \
                         SACmdInLevErr[3] + SACmdInLevErr[4]+ SACmdInLevErr[5]+ SACmdInLevErr[6]+ SACmdInLevErr[7];

    /* Sturate counter */
    EESACmdInLevErrSum = (uint16_T)(min((uint32_T)MAX_uint16_T, tmpEESACmdInLevErrSum));
}

/******************************************************************************
**   Function    : MSparkCmd_BKEnLatch
**
**   Description:
**    Latch: * VtDelayBkEn
**           * VtDeltaBkEn
**
**   Parameters :
**    cyl: cylinder index
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void MSparkCmd_BKEnLatch(uint8_T cyl)
{
    VtDelayBkEnEOA[cyl] = (uint16_T)VtDelayBkEn[cyl];
    VtDeltaBkEnEOA[cyl] = (uint16_T)VtDeltaBkEn[cyl];
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : MSparkCmd_DiagSecOl
**
**   Description:
**    All the vectors are indexed using MisfAbsTdc
**    - Manage DIAG_SEC_OL
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void MSparkCmd_DiagSecOl(void)
{
    uint8_T stDiag = NO_FAULT;

    if (PtFaultTrigger == NO_PT_FAULT)
    {
        if (FlgOLSpark[MisfAbsTdc] != 0u)
        {
            DiagMgm_SetDiagState(DiagSecOl[MisfAbsTdc], CIRCUIT_OPEN, &stDiag);
        }
        else
        {
            DiagMgm_SetDiagState(DiagSecOl[MisfAbsTdc], NO_PT_FAULT, &stDiag);
        }
    }
}

/******************************************************************************
**   Function    : MSparkCmd_DiagTrigRec
**
**   Description:
**    Trigger recovery test
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void MSparkCmd_DiagTrigRec(void)
{
    if (VtRec[REC_IGN_OFF_0 + MisfAbsTdc] != 0u)
    {
        PORT_IgnOffRec(MisfAbsTdc);
    }
    else
    {
        /* MISRA */
    }
}

/******************************************************************************
**   Function    : MSparkCmd_DiagIonChannel
**
**   Description:
**    Ion channel diagnostic check
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void MSparkCmd_DiagIonChannel(void)
{
    static uint32_T cntIgnOld[N_CYL_MAX];
    static uint32_T cntEffEOAOld[N_CH_MAX];
    uint8_T stDiag = NO_FAULT;
    uint8_T idx = MisfAbsTdc & 0x03u;
    uint8_T ptFaultChann = PtFaultChannel[idx];

    if (CntIGNTrgInOn[MisfAbsTdc] != cntIgnOld[MisfAbsTdc])
    {
        if (CntEffEOA[idx] == cntEffEOAOld[idx])
        {
            ptFaultChann = INTERNAL_ELECTRONIC_FAILURE;
        }
    }

    cntEffEOAOld[idx] = CntEffEOA[idx];
    cntIgnOld[MisfAbsTdc] = CntIGNTrgInOn[MisfAbsTdc];

    DiagMgm_SetDiagState(DiagIonChannel[idx], ptFaultChann, &stDiag);
}


#endif // _BUILD_MSPARKCMD_

