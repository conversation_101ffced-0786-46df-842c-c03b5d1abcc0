/*****************************************************************************************************************/
/* $HeadURL::                                                                                                                                 $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifdef _BUILD_VSRAMMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"

/*!
\defgroup PublicVariables Public Variables
\sgroup
*/
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/// Global counter of core 0-z4 IVORs (used for diagnosis)
uint8_T GenIvorCnt;
/// Event VSRAM
uint8_T EvPwrVSRReset;
///
uint8_T  VR_IvorIndex;
///
uint32_T VR_SRR0_Value;   
///
uint32_T VR_SRR1_Value;
///
uint32_T VR_CSRR0_Value;  
///
uint32_T VR_CSRR1_Value; 
///
uint32_T VR_SPR_ESRValue; 
///
uint32_T VR_SPR_DEARValue;
///
uint32_T VR_SPR_MCSRValue;
///
uint32_T VR_SPR_MCARValue;
///
uint32_T VR_MCSRR0_Value;
///
uint32_T VR_MCSRR1_Value;

/*!\egroup*/
#endif //_BUILD_VSRAMMGM_
