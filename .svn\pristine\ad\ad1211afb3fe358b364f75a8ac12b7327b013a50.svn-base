/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Analog In
**  Filename        :  AnalogIn_calib.c
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Aragon J
******************************************************************************/
#ifdef _BUILD_ANALOGIN_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "analogin.h"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
#pragma ghs section rodata=".calib"

#if ((BOARD_TYPE == BOARD_EISB_A) || (BOARD_TYPE == BOARD_EISB_B))
#define G_VCHARGE (52.282f)
#elif (BOARD_TYPE == BOARD_EISB_C)
#define G_VCHARGE (77.153f)
#elif (BOARD_TYPE == BOARD_EISB6C_A) //MC
#define G_VCHARGE (77.153f)
#elif (BOARD_TYPE == BOARD_EISB8F_A) //MC
#define G_VCHARGE (77.153f)
#else
#error WRONG CONFIGURATION!!!
#endif

/// VBattery to angle or time
CALQUAL CALQUAL_POST uint8_T     RDVBATTATANG = 1u;

///Low diagnosis threshold for VVTempECU1
CALQUAL CALQUAL_POST uint16_T    VINFVTEMPECU1 = 0u;
/// High diagnosis threshold for VVTempECU1
CALQUAL CALQUAL_POST uint16_T    VSUPVTEMPECU1 = 4096u;
/// Gain for converting VVTempECU1
CALQUAL CALQUAL_POST int16_T     GNVTEMPECU1 = 10000;
/// Offset for converting VVTempECU1
CALQUAL CALQUAL_POST int16_T     OFSVTEMPECU1 = 0;
/// Flag to force VVTempECU1
CALQUAL CALQUAL_POST uint8_T     FLGFOVVTEMPECU1 = 0u;
/// Forced value of VVTempECU1
CALQUAL CALQUAL_POST uint16_T    FOVVTEMPECU1 = 0u;

///Low diagnosis threshold for VVTempECU2
CALQUAL CALQUAL_POST uint16_T    VINFVTEMPECU2 = 0u;
/// High diagnosis threshold for VVTempECU2
CALQUAL CALQUAL_POST uint16_T    VSUPVTEMPECU2 = 4096u;
/// Gain for converting VVTempECU2
CALQUAL CALQUAL_POST int16_T     GNVTEMPECU2 = 10000;
/// Offset for converting VVTempECU2
CALQUAL CALQUAL_POST int16_T     OFSVTEMPECU2 = 0;
/// Flag to force VVTempECU2
CALQUAL CALQUAL_POST uint8_T     FLGFOVVTEMPECU2 = 0u;
/// Forced value of VVTempECU2
CALQUAL CALQUAL_POST uint16_T    FOVVTEMPECU2 = 0u;

///Low diagnosis threshold for VVTempECU3
CALQUAL CALQUAL_POST uint16_T    VINFVTEMPECU3 = 0u;
/// High diagnosis threshold for VVTempECU3
CALQUAL CALQUAL_POST uint16_T    VSUPVTEMPECU3 = 4096u;
/// Gain for converting VVTempECU3
CALQUAL CALQUAL_POST int16_T     GNVTEMPECU3 = 10000;
/// Offset for converting VVTempECU3
CALQUAL CALQUAL_POST int16_T     OFSVTEMPECU3 = 0;
/// Flag to force VVTempECU3
CALQUAL CALQUAL_POST uint8_T     FLGFOVVTEMPECU3 = 0u;
/// Forced value of VVTempECU3
CALQUAL CALQUAL_POST uint16_T    FOVVTEMPECU3 = 0u;

///Low diagnosis threshold for VVBoardSel
CALQUAL CALQUAL_POST uint16_T    VINFVBOARDSEL = 0u;
/// High diagnosis threshold for VVBoardSel
CALQUAL CALQUAL_POST uint16_T    VSUPVBOARDSEL = 4096u;
/// Gain for converting VVBoardSel
CALQUAL CALQUAL_POST int16_T     GNVBOARDSEL = 10000;
/// Offset for converting VVBoardSel
CALQUAL CALQUAL_POST int16_T     OFSVBOARDSEL = 0;
/// Flag to force VVBoardSel
CALQUAL CALQUAL_POST uint8_T     FLGFOVVBOARDSEL = 0u;
/// Forced value of VVBoardSel
CALQUAL CALQUAL_POST uint16_T    FOVVBOARDSEL = 0u;

///Low diagnosis threshold for VVBoardSel
CALQUAL CALQUAL_POST int16_T     VINFVBUCKCM0 = 0;
/// High diagnosis threshold for VVBoardSel
CALQUAL CALQUAL_POST int16_T     VSUPVBUCKCM0 = 4096;
///Low diagnosis threshold for VVBoardSel
CALQUAL CALQUAL_POST int16_T     VINFVBUCKCM1 = 0;
/// High diagnosis threshold for VVBoardSel
CALQUAL CALQUAL_POST int16_T     VSUPVBUCKCM1 = 4096;
///Low diagnosis threshold for VVBoardSel
CALQUAL CALQUAL_POST int16_T     VINFVBUCKCM2 = 0;
/// High diagnosis threshold for VVBoardSel
CALQUAL CALQUAL_POST int16_T     VSUPVBUCKCM2 = 4096;
/// Gain for converting VVBoardSel
CALQUAL CALQUAL_POST int16_T     GNVBUCKCM = 10000;
/// Offset for converting VVBoardSel
CALQUAL CALQUAL_POST int16_T     OFSVBUCKCM = 0;
/// Flag to force VVBoardSel
CALQUAL CALQUAL_POST uint8_T     FLGFOVVBUCKCM = 0u;
/// Forced value of VVBoardSel
CALQUAL CALQUAL_POST uint16_T    FOVVBUCKCM = 0u;

///Low diagnosis threshold for VVBandGap
CALQUAL CALQUAL_POST uint16_T    VINFVBANDGAP = 0u;
/// High diagnosis threshold for VVBandGap
CALQUAL CALQUAL_POST uint16_T    VSUPVBANDGAP = 4096u;
/// Gain for converting VVBandGap
CALQUAL CALQUAL_POST int16_T     GNVBANDGAP = 10000;
/// Offset for converting VVBandGap
CALQUAL CALQUAL_POST int16_T     OFSVBANDGAP = 0;
/// Flag to force VVBandGap
CALQUAL CALQUAL_POST uint8_T     FLGFOVVBANDGAP = 0u;
/// Forced value of VVBandGap
CALQUAL CALQUAL_POST uint16_T    FOVVBANDGAP = 0u;

///Low diagnosis threshold for VVBattery
CALQUAL CALQUAL_POST uint16_T    VINFVBATTERY = ((uint16_T)(3.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))); // 3V
/// High diagnosis threshold for VVBattery
CALQUAL CALQUAL_POST uint16_T    VSUPVBATTERY = ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))); // 32V
/// Gain for converting VVBattery
CALQUAL CALQUAL_POST int16_T     GNVBATTERY = ((int16_T)(6.641f * (2621440.0f / 4096.0f))); // G = 6.641
/// Offset for converting VVBattery
CALQUAL CALQUAL_POST int16_T     OFSVBATTERY = ((int16_T)(0.0f * (16.0f))); // 0V
/// Flag to force VVBattery
CALQUAL CALQUAL_POST uint8_T     FLGFOVVBATTERY = 0u;
/// Forced value of VVBattery
CALQUAL CALQUAL_POST int16_T    FOVVBATTERY = -1;

/// VCoil monitor threshold 
CALQUAL CALQUAL_POST int16_T     THRDGVCMON = 400; // 400mV

///Low diagnosis threshold for VVCoil0
CALQUAL CALQUAL_POST uint16_T    VINFVCOIL0 = ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f)));

///High diagnosis threshold for VVCoil0
CALQUAL CALQUAL_POST uint16_T    VSUPVCOIL0 = ((uint16_T)(12.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f)));

///Low diagnosis threshold for VVCoil
CALQUAL CALQUAL_POST uint16_T    TBVINFVCOIL[5][2] = 
{
    {
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    },
    {
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    },
    {
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    },
    {
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    },
    {
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(0.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    }
};
/// High diagnosis threshold for VVCoil
CALQUAL CALQUAL_POST uint16_T    TBVSUPVCOIL[5][2] = 
{
    {
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    },
    {
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    },
    {
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    },
    {
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),  
    },
    {
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    ((uint16_T)(32.0f * (0.15058f * (4096.0f / 5000.0f) * 1000.0f))),
    }
};
/// Gain for converting VVCoil
CALQUAL CALQUAL_POST int16_T     GNVCOIL = ((int16_T)(6.641f * (2621440.0f / 4096.0f))); // G = 6.641
/// Offset for converting VVCoil
CALQUAL CALQUAL_POST int16_T     OFSVCOIL = ((int16_T)(0.0f * (16.0f))); // 0V
/// Flag to force VVCoil
CALQUAL CALQUAL_POST uint8_T     FLGFOVVCOIL = 0u;
/// Forced value of VVCoil
CALQUAL CALQUAL_POST uint16_T    FOVVCOIL = 0u;

///Low diagnosis threshold for VVRefH
CALQUAL CALQUAL_POST uint16_T    VINFVREFH = 0u;
/// High diagnosis threshold for VVRefH
CALQUAL CALQUAL_POST uint16_T    VSUPVREFH = 4096u;
/// Gain for converting VVRefH
CALQUAL CALQUAL_POST int16_T     GNVREFH = 10000;
/// Offset for converting VVRefH
CALQUAL CALQUAL_POST int16_T     OFSVREFH = 0;
/// Flag to force VVRefH
CALQUAL CALQUAL_POST uint8_T     FLGFOVVREFH = 0u;
/// Forced value of VVRefH
CALQUAL CALQUAL_POST uint16_T    FOVVREFH = 0u;

///Low diagnosis threshold for VVRefL
CALQUAL CALQUAL_POST uint16_T    VINFVREFL = 0u;
/// High diagnosis threshold for VVRefL
CALQUAL CALQUAL_POST uint16_T    VSUPVREFL = 4096u;
/// Gain for converting VVRefL
CALQUAL CALQUAL_POST int16_T     GNVREFL = 10000;
/// Offset for converting VVRefL
CALQUAL CALQUAL_POST int16_T     OFSVREFL = 0;
/// Flag to force VVRefL
CALQUAL CALQUAL_POST uint8_T     FLGFOVVREFL = 0u;
/// Forced value of VVRefL
CALQUAL CALQUAL_POST uint16_T    FOVVREFL = 0u;

///Low diagnosis threshold for VVCharge
CALQUAL CALQUAL_POST uint16_T    VINFVCHARGE = ((uint16_T)(0.0f * (0.01913f * (4096.0f / 5000.0f) * 1000.0f))); // 0V
/// High diagnosis threshold for VVCharge
CALQUAL CALQUAL_POST uint16_T    VSUPVCHARGE = ((uint16_T)(300.0f * (0.01913f * (4096.0f / 5000.0f) * 1000.0f))); // 300V
/// Gain for converting VVCharge
CALQUAL CALQUAL_POST uint16_T     GNVCHARGE = ((uint16_T)(G_VCHARGE * 512.0f)); // scaling 2^-9
/// Offset for converting VVCharge
CALQUAL CALQUAL_POST int16_T     OFSVCHARGE = ((int16_T)(0.0f * (16.0f))); // scaling 2^-4
/// Flag to force VVCharge
CALQUAL CALQUAL_POST uint8_T     FLGFOVVCHARGE = 0u;
/// Forced value of VVCharge
CALQUAL CALQUAL_POST uint16_T    FOVVCHARGE = 0u;
/// Glith threshold for VVCharge
CALQUAL CALQUAL_POST uint16_T    THRVCHARGEGLITCH = ((uint16_T)(12.0f * (0.01913f * (4096.0f / 5000.0f) * 1000.0f))); // 12V
/// Median for VVCharge, size 0,1,2; 0=Disable.
CALQUAL CALQUAL_POST uint8_T     MEDIANCAPLENGTH = 1u;  // formula x = 2^1 +1 

/// Threshold on GainCC for ADC diagnosis (abs delta from the nominal gain)
CALQUAL CALQUAL_POST uint16_T    THERRGAINCC = (uint16_T)(0.06f * 16384.0f);
/// Threshold on OffsetCC for ADC diagnosis (abs delta)
CALQUAL CALQUAL_POST uint16_T    THERROFFSETCC = (uint16_T)(200.0f * (16384.0f / 5000.0f));

/// Lower threshold on rpm to enable the functional vbat diagnosis
CALQUAL CALQUAL_POST uint16_T    THRPMVBDIAG = 3000u;

/// Lower threshold on VBattery to enable the functional vbat diagnosis
CALQUAL CALQUAL_POST uint16_T    VBATTERYEONLOW = (9u * 16u); // 9*16

///K of battery votage filter
CALQUAL CALQUAL_POST uint16_T    KFILTVBATTERY = ((uint16_T)(0.125f * (16384.0f))); // Kf = 0.125

///Median size 0,1,2; 0=Disable.
CALQUAL CALQUAL_POST uint8_T     MEDIANLENGTH = 1u;  // formula x = 2^1 +1

///TOff Buck diagnosis
CALQUAL CALQUAL_POST uint16_T    TIMTSTBKENOFF = 10u;

///TOn Buck diagnosis
CALQUAL CALQUAL_POST uint16_T    TIMTSTBKENON = 10u;

///Add Diag Delay IGN
CALQUAL CALQUAL_POST uint8_T     CNTWAITIGN = 10u;

/// Threshold of secondary current used for short circuit diagnosis
CALQUAL CALQUAL_POST uint16_T THRVSECDIAG = 480u;     // 30V

/// Threshold of secondary peak Current used for short circuit to VBat 
CALQUAL CALQUAL_POST int16_T THRISHOTPEAKSCVBAT = 4050;

/// Threshold of secondary peak Current used for short circuit to GND 
CALQUAL CALQUAL_POST int16_T THRISHOTPEAKSCGND = 2080;

#endif //_BUILD_ANALOGIN_
/****************************************************************************
 ****************************************************************************/

