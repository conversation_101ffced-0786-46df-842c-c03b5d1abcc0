/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/


#ifdef _BUILD_FLASHMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "flashmgm.h"
#include "mathlib.h"
#include "sys.h"

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
uint32_T checksumCalib = 0u;
uint32_T checksumAppl = 0u;
uint32_T checksumCalibAppl = 0u;
uint8_T calcChecksumState = 0u;
uint32_T checksumCalibTag = 0u;
uint32_T checksumApplTag = 0u;

uint8_T FlashFaultCnt = 0u;
uint8_T FlashCorruptFlg = 0u;
uint32_T crc_appli_complete;
uint8_T start_crc_appli = 0u;
uint8_T cntUpdateCRC = 0u;

extern  uint32_T    __APP_START;
extern  uint32_T    __APP_SIZE;
extern  uint32_T    __APP_TAG_START;
extern  uint32_T    __CALIB_ROM_START;
extern  uint32_T    __CALIB_ROM_SIZE;



/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/* PAY ATTENTION */
/* (CHECKSUM_BLOCK_LEN % 8) must be equal to 0 */
#define CHECKSUM_BLOCK_LEN (0x100u)      //256 byte
#define CRC_START_ADDR ((uint32_T)(&__CALIB_ROM_START ))
#define CRC_STOP_ADDR ((uint32_T)(&__CALIB_ROM_START ) + (uint32_T)(&__CALIB_ROM_SIZE) + (uint32_T)(&__APP_SIZE)) 

#define CHECKSUM_CALIB_ROM_START_ADDR ((uint32_T)(&__CALIB_ROM_START ))
#define CRC_CALIB_ROM_STOP_ADDR ((uint32_T)(&__CALIB_ROM_START ) + (uint32_T)(&__CALIB_ROM_SIZE))
#define APP_START_ADDR ((uint32_T)(&__APP_START ))
#define APP_STOP_ADDR ((uint32_T)(&__APP_START ) + (uint32_T)(&__APP_SIZE))

#define CALC_CHECKSUM_START      0u
#define CALC_CHECKSUM_CALIB      1u
#define CALC_CHECKSUM_APPL       2u
#define CALC_CHECKSUM_COMPLETED  3u

/*-----------------------------------*
 * PRIVATE MACROS
 *-----------------------------------*/
// None

/*-----------------------------------*
 * PRIVATE TYPEDEFS
 *-----------------------------------*/
// None

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/

/*-----------------------------------*
 * PRIVATE VARIABLES
 *-----------------------------------*/
static  uint16_T test_crc_callcount = 0u;
static  uint16_T crc_tmp = 0u;
static  uint16_T    crc_calib_appl;
static  uint8_T     flg_crc_complete;


/*-----------------------------------*
 * PUBLIC FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FLASHMGM_FlashTestStart - Initialize FlashTest
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FLASHMGM_FlashTestStart(void)
{
    if (test_crc_callcount == 0u)
    {
        test_crc_callcount = TESTCRCPERIODCNT;
        start_crc_appli = 1u;
    }

    test_crc_callcount--;
}

/*--------------------------------------------------------------------------*
 * FLASHMGM_FlashTest - Flash Test
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void FLASHMGM_FlashTest(void)
{
    uint32_T addr_next;
    uint32_T size;
    static uint8_T flgFirstCall = 1u;
    static  uint32_T addr = 0xFFFFFFFFu;

    if(addr == 0xFFFFFFFFu)
    {
        addr = CRC_START_ADDR;
    }
    /* when start calculate crc, first time, add also BANKSEL value */
    if (flgFirstCall != 0u)
    {
        crc_tmp = 0u; //FlgBankSel;
        flgFirstCall = 0u;
    }
    crc_appli_complete = 0u;
    if (start_crc_appli == 1u)
    {
        if (cntUpdateCRC == 0u)
        {
            size = CHECKSUM_BLOCK_LEN;
            addr_next = addr + CHECKSUM_BLOCK_LEN;
            if (addr_next >= CRC_STOP_ADDR)
            {
                size = (CRC_STOP_ADDR - addr);
                crc_appli_complete = 1u;
            }
            crc_tmp = update_crc16(crc_tmp, (uint8_T *)addr, size, UTILS_CRC_REVERSED);
            addr += size;
            cntUpdateCRC = WAITCNT;
        }
        if (crc_appli_complete != 0u)
        {
            crc_calib_appl = crc_tmp;
            crc_tmp = 0u;
            addr = CRC_START_ADDR;
            start_crc_appli = 0u;
            flg_crc_complete = 1u;
            flgFirstCall = 1u;
            cntUpdateCRC = 0u;
        }
        cntUpdateCRC--;
    }
}


/*--------------------------------------------------------------------------*
 * FLASHMGM_GetCrc - Get Crc when completed
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
int16_T FLASHMGM_GetCrc(uint16_T *crc_16)
{
    int16_T retVal = NO_ERROR;
    
    if (flg_crc_complete != 0u)
    {
        *crc_16 = crc_calib_appl;
    }
    else
    {
        retVal = FLASH_CRC_NOT_VALID;
    }
    return(retVal);
}

/*-----------------------------------*
 * PRIVATE FUNCTION DEFINITIONS
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * CalcChecksum - Calculate checksum
 *
 * Implementation notes:
 * 
 *--------------------------------------------------------------------------*/

#endif /* _BUILD_FLASHMGM_ */
