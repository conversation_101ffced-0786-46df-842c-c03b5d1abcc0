/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#include "rtwtypes.h"


#pragma ghs section data=".ee_id7_data"

#ifdef _TEST_EEPROM_
/*  MC Test Struct  */
uint32_T Prova_ID7[8] = {  
                            0x77777777u, 0x77777777u, 0x77777777u, 0x77777777u ,
                            0x77777777u, 0x77777777u, 0x77777777u, 0x77777777u
                        };
#endif

// Declare here all the variables to be stored in EEPROM with ID7
///Dummy_EE ID7
uint32_T EEDummyID7_00 = 0u;
uint32_T EEDummyID7_01 = 0u;
uint32_T EEDummyID7_02 = 0u;
uint32_T EEDummyID7_03 = 0u;

#ifdef _BUILD_TEMPECUMGM_
#pragma ghs startnomisra
#include "tempecumgm_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_TLE9278BQX_DIAG_
#pragma ghs startnomisra
#include "tle9278bqx_diag_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_CPUMGM_
#pragma ghs startnomisra
#include "Cpu_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_DIGIN_
#pragma ghs startnomisra
#include "digin_eepID7.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_UTILS_
#pragma ghs startnomisra
#include "utils_eep.c"
#pragma ghs endnomisra
#endif

#ifdef _BUILD_TLE9278BQX_COM_
#pragma ghs startnomisra
#include "tle9278bqx_com_eep.c"
#pragma ghs endnomisra
#endif

/* EOF EEPROM */

