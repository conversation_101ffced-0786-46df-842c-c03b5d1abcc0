/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm.c
 * @brief   SPC5xx GTM low level driver code.
 *
 * @addtogroup GTM
 * @{
 */

#include "gtm.h"
#include "clock.h"
 
/**
 * @brief   GTM driver identifier.
 */
GTMDriver GTMD;

/**
 * @brief   GTM set TIM AUX_IN signal
 *
 * @param[in] gtmd        GTM driver pointer
 *
 * @param[in] tim         GTM TIM number
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] value       GTM TIM AUX_IN source
 *
 * @api
 */
void gtm_SetAuxIN(GTMDriver *gtmd, uint32_t tim, uint32_t channel, uint32_t value) {
	uint32_t val;

	switch(tim) {
	case 0:
		val = GTM_GET_TIM_AUX_IN_SRC(0) | (value << channel);
		GTM_SET_TIM_AUX_IN_SRC(0, val);
		break;
	case 1:
		val = GTM_GET_TIM_AUX_IN_SRC(1) | (value << channel);
		GTM_SET_TIM_AUX_IN_SRC(1, val);
		break;
#if ((SPC5_HAS_GTM_IP_122 == TRUE) || (SPC5_HAS_GTM_IP_343 == TRUE) || (SPC5_HAS_GTM_IP_344 == TRUE))
	case 2:
		val = GTM_GET_TIM_AUX_IN_SRC(2) | (value << channel);
		GTM_SET_TIM_AUX_IN_SRC(2, val);
		break;
#endif
#if ((SPC5_HAS_GTM_IP_343 == TRUE) || (SPC5_HAS_GTM_IP_344 == TRUE))
	case 3:
		val = GTM_GET_TIM_AUX_IN_SRC(3) | (value << channel);
		GTM_SET_TIM_AUX_IN_SRC(3, val);
		break;
	case 4:
		val = GTM_GET_TIM_AUX_IN_SRC(4) | (value << channel);
		GTM_SET_TIM_AUX_IN_SRC(4, val);
		break;
#endif
#if (SPC5_HAS_GTM_IP_344 == TRUE)
	case 5:
		val = GTM_GET_TIM_AUX_IN_SRC(5) | (value << channel);
		GTM_SET_TIM_AUX_IN_SRC(5, val);
		break;
#endif
	default:
		/*MISRA Check*/
		break;
	}
}

/**
 * @brief   GTM Set SW RF Protection
 *
 * @param[in] gtmd        GTM driver pointer
 *
 * @param[in] rf_prot       GTM RF_PROT setting
 *
 * @api
 */
void gtm_SetRFPROT(GTMDriver *gtmd, uint32_t rf_prot) {
	gtmd->gtm->CTRL.B.RF_PROT = (uint8_t)rf_prot;

}

/**
 * @brief   GTM Get SW RF Protection
 *
 * @param[in] gtmd       GTM driver pointer
 *
 * @return uint32_t      RF_PROT bit
 *
 * @api
 */
uint32_t gtm_GetRFPROT(GTMDriver *gtmd) {
	return gtmd->gtm->CTRL.B.RF_PROT;
}

/**
 * @brief   GTM AUXIN_SRC Initialization
 *
 * @param[in] gtmd        GTM driver pointer
 *
 * @api
 */
static void gtm_aux_inInit(GTMDriver *gtmd) {
	gtm_aux_in_src();
	gtmd->gtm_auxin[0] = GTM_GET_TIM_AUX_IN_SRC(0);
	gtmd->gtm_auxin[1] = GTM_GET_TIM_AUX_IN_SRC(1);
#if ((SPC5_HAS_GTM_IP_122 == TRUE) || (SPC5_HAS_GTM_IP_343 == TRUE) || (SPC5_HAS_GTM_IP_344 == TRUE))
	gtmd->gtm_auxin[2] = GTM_GET_TIM_AUX_IN_SRC(2);
#endif
#if ((SPC5_HAS_GTM_IP_343 == TRUE) || (SPC5_HAS_GTM_IP_344 == TRUE))
	gtmd->gtm_auxin[3] = GTM_GET_TIM_AUX_IN_SRC(3);
	gtmd->gtm_auxin[4] = GTM_GET_TIM_AUX_IN_SRC(4);
#endif
#if (SPC5_HAS_GTM_IP_344 == TRUE)
	gtmd->gtm_auxin[5] = GTM_GET_TIM_AUX_IN_SRC(5);
#endif
}

/**
 * @brief   Low level GTM driver initialization.
 *
 * @init
 */
 void gtmInit(void) {

	 /* Setting RUN Configuration Register 
	 SPCSetPeripheralClockMode(SPC5_GTMINT_PCTL,  //MC. moved to clock configuration
	 			      (SPC5_ME_PCTL_RUN(1) | SPC5_ME_PCTL_LP(2)))*/;

	 GTMINT.MCR.R = 0x0UL;     /* Enable GTM Module, MDIS = 0 */
	 GTM.CTRL.R = 0x0UL;       /* Clean GTM Control Register  */
	 GTM.RST.R = 0x00000001UL; /* Reset All GTM devices       */

	 if ((GTM.REV.R & 0xFFFFF000UL) != SPC5_GTM_REV) {
		 for(;;){}
	 }
	 GTMD.ip = SPC5_GTM_REV;
	 GTMD.gtm = &GTM;

	 /* Set cluster clock dividers.*/
#if (SPC5_HAS_GTM_IP_343 == TRUE || SPC5_HAS_GTM_IP_344 == TRUE)
	 GTM.CTRL.B.RF_PROT = 0;
	 GTM.CLS_CLK_CFG.B.CLS0_CLK_DIV = SPC5_GTM_CLS0_CLK_DIV;
	 GTM.CTRL.B.RF_PROT = 1;
#endif

	 /*
	  * After global reset mechanism for triggering interrupts
	  * with IRQ_FORCINT, MCS RAM reset and MCS set scheduling
	  * option is globally disabled.
	  * Explicitly enabled it by clearing the bit RF_PROT.
	  */
	 gtm_SetRFPROT(&GTMD, SPC5_GTM_IP_SW_RFPROT_EN);

	 /* Enable CMU Unit */
	 gtm_cmuInit();

#if (SPC5_GTM_USE_ICM == TRUE)
	 /* Init ICM module */
	 gtm_icmInit();
#endif

	 /* Set GTM GLobal AUX_IN TIM signal */
	 gtm_aux_inInit(&GTMD);

#if (SPC5_GTM_USE_TBU == TRUE)
	 /* Init TBU module */
	 gtm_tbuInit();
#endif

#if (SPC5_GTM_USE_ARU == TRUE)
	 /* Init ARU module */
	 gtm_aruInit();
#endif

#if (SPC5_GTM_USE_TOM == TRUE)
	 /* Init TOM module */
	 gtm_tomInit();
#endif

#if (SPC5_GTM_USE_MCS == TRUE)
	 /* Init MCS module */
	 gtm_mcsInit();
#endif

#if (SPC5_GTM_USE_PSM == TRUE)
	 /* Init PSM module */
	 gtm_psmInit();
#endif

#if (SPC5_GTM_USE_TIM == TRUE)
	 /* Init TIM module */
	 gtm_timInit();
#endif

#if (SPC5_GTM_USE_MAP == TRUE)
	 /* Init MAP module */
	 gtm_mapInit();
#endif

#if (SPC5_GTM_USE_ATOM == TRUE)
	 /* Init ATOM module */
	 gtm_atomInit();
#endif

#if (SPC5_GTM_USE_DPLL == TRUE)
	 /* Init DPLL module */
	 gtm_dpllInit();
#endif

#if (SPC5_GTM_USE_DTM == TRUE)
	 /* Init DTM module */
	 gtm_dtmInit();
#endif

#if (SPC5_GTM_USE_BRC == TRUE)
	 /* Init BRC module */
	 gtm_brcInit();
#endif

}
/** @} */

