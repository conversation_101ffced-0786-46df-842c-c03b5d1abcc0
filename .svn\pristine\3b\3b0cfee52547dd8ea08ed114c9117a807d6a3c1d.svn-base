/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      P2NoiseDetect.h
 **  Date:          06-Sep-2021
 **
 **  Model Version: 1.1345
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_P2NoiseDetect_h_
#define RTW_HEADER_P2NoiseDetect_h_
#include <string.h>
#ifndef P2NoiseDetect_COMMON_INCLUDES_
# define P2NoiseDetect_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* P2NoiseDetect_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void P2NoiseDetect_initialize(void);

/* Exported entry point function */
extern void P2NoiseDetect_10ms(void);

/* Exported entry point function */
extern void P2NoiseDetect_EOA(void);

/* Exported entry point function */
extern void P2NoiseDetect_PowerOn(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T VtP2NoiseDetFlg[8];     /* '<S2>/Merge' */

/* P2 engine Noise detected correction active */
extern int16_T VtP2NoiseDetRec[8];     /* '<S2>/Merge1' */

/* P2 engine Noise correction  */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S9>/Data Type Duplicate' : Unused code path elimination
 * Block '<S8>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Data Type Duplicate' : Unused code path elimination
 * Block '<S18>/Data Type Duplicate1' : Unused code path elimination
 * Block '<S18>/Data Type Propagation' : Unused code path elimination
 * Block '<S20>/Data Type Duplicate' : Unused code path elimination
 * Block '<S19>/Data Type Duplicate' : Unused code path elimination
 * Block '<S5>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S9>/Conversion' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S18>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S18>/Reshape' : Reshape block reduction
 * Block '<S19>/Conversion' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S19>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S20>/Conversion' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'P2NoiseDetect'
 * '<S1>'   : 'P2NoiseDetect/Scheduler'
 * '<S2>'   : 'P2NoiseDetect/Subsystem'
 * '<S3>'   : 'P2NoiseDetect/Task_EOA'
 * '<S4>'   : 'P2NoiseDetect/Task_PowerOn'
 * '<S5>'   : 'P2NoiseDetect/Task_EOA/calc_Output'
 * '<S6>'   : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32'
 * '<S7>'   : 'P2NoiseDetect/Task_EOA/calc_Output/ArrangeOutputLSB'
 * '<S8>'   : 'P2NoiseDetect/Task_EOA/calc_Output/RateLimiter_S32'
 * '<S9>'   : 'P2NoiseDetect/Task_EOA/calc_Output/RateLimiter_S32/Data Type Conversion Inherited1'
 * '<S10>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/EnablingStrategy'
 * '<S11>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyDisabled'
 * '<S12>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyEnabled'
 * '<S13>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/Subsystem'
 * '<S14>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyEnabled/NoiseDetected'
 * '<S15>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyEnabled/NoiseDetection'
 * '<S16>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyEnabled/NoiseNotDetected'
 * '<S17>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyEnabled/Subsystem'
 * '<S18>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyEnabled/NoiseDetected/Look2D_S8_U16_U16'
 * '<S19>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyEnabled/NoiseDetection/LookUp_U16_U16'
 * '<S20>'  : 'P2NoiseDetect/Task_EOA/calc_P2NoiseCorr32/StrategyEnabled/NoiseDetection/LookUp_U16_U16/Data Type Conversion Inherited3'
 */

/*-
 * Requirements for '<Root>': P2NoiseDetect
 */
#endif                                 /* RTW_HEADER_P2NoiseDetect_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/