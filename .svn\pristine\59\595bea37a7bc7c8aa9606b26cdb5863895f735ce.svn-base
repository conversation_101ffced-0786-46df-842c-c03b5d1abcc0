/** ###################################################################
**     Filename  : TIMING.cfg
**     Project   :
**     Processor : MPC5554
**     Version   : 
**     Compiler  : Metrowerks PowerPC C Compiler
**     Date/Time : 28/02/2005
**     Abstract  :
**
**
**     Settings  :
**     Contents  :
**
**
**     (c) Copyright
** ###################################################################*/
#ifndef _TIMING_CFG_H_
#define _TIMING_CFG_H_

#define     TIMING_4MS      22u   /* EMIOS_UC22 used as timing source */

#define     TIMING_RESOLUTION     1u   /* timing resolution [ms]    */
                      

#define     TIMING_1ms_RATE       1u    /* rate task 1ms period   */
#define     TIMING_VHIGH_RATE     2u     /* rate task 2ms period   */
#define     TIMING_HIGH_RATE      4u     /* High rate (4ms) task period   */
#define     TIMING_MIDHIGH_RATE   5u     /* High rate (5ms) task period   */
#define     TIMING_MIDDLE_RATE    10u    /* Middle rate (10ms) task period */
#define     TIMING_LOW_RATE       100u   /* Low rate (100ms) task period      */
#ifdef _BUILD_SAF2MGM_
#define     TIMING_SAFETY2_RATE   50u    /* SAFETY 2 (50ms)  task period     */
#endif /* _BUILD_SAF2MGM_ */

#define     USER_HIGH_RATE_ISR      (Task4ms)     /* High rate period user function     */
#define     USER_MIDHIGH_RATE_ISR   (Task5ms)     /* High rate period user function     */
#define     USER_MIDDLE_RATE_ISR    (Task10ms)    /* Middle rate period user function   */
#define     USER_LOW_RATE_ISR       (Task100ms)   /* Low rate task period user function */

#define     USER_2MS_RATE_ISR_PRIORITY       (S_IRQ7) /* Very High rate period user function priority  MS!! */
#define     USER_HIGH_RATE_ISR_PRIORITY      (S_IRQ6) /* High rate period user function priority          */
#define     USER_MIDHIGH_RATE_ISR_PRIORITY   (S_IRQ5) /* High rate period user function priority          */
#define     USER_MIDDLE_RATE_ISR_PRIORITY    (S_IRQ3) /* Middle rate period user function priority        */
#define     USER_LOW_RATE_ISR_PRIORITY       (S_IRQ2) /* Low rate task period user function priority */

/* ------------------------------------------------------------------ */
/*     Common errors defines                                            */
/* ------------------------------------------------------------------ */


#endif /* _TIMING_CFG_H_ */
