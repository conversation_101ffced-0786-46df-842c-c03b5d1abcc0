/*****************************************************************************************************************/
/* $HeadURL::                                                                                                $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                           */
/*****************************************************************************************************************/

#include "sys.h"
#include "Utils.h"

#ifdef _BUILD_UTILS_

#ifdef FSYS
#define UTILS_1MS_CLOCK_CYLCES    ((uint32_T)(FSYS * 1000u))
#endif

uint32_T debug00,debug01,debug02,debug03,debug04,debug05,debug06,debug07;
int32_T  debug08,debug09;

inline void UTILS_nop(void)
{
    __asm("nop");
}


void Delay_ms(uint32_T ms)
{
    uint32_T i = 0u;
    
    for (; ms > 0u; ms--)
    {
#ifdef UTILS_1MS_CLOCK_CYLCES
        for (i = 0u; i < UTILS_1MS_CLOCK_CYLCES; i++)
        {
            UTILS_nop();
        }
#endif
    }
}

void DigDebounce(uint8_T *pOut, uint8_T *pOldval,uint8_T newval, uint8_T *pCntDeb, uint8_T nf) 
{
/* This function applies a debounce filtering to a digital signal       */
/* input parameters:                                                    */
/*      newval  = new value just sampled            */
/*      oldval  = old value hold                    */
/*      nf = number of consecutive samples to change the output value   */
/*                                                                      */
/* input/output parameters:                         */
/*      *pCntDeb = debounce counter related to the signal */
/* WARNING: *pCntDeb holds the old value and the debounced value. */
/*            pCntDeb must refer to a static or global variable!! */
/*                                                      */
/* output parameters:                                                   */
/*      *pOut = digital debounced output value                          */
/*      exit value = error code                                         */ 
/*                                                                      */
    if (nf == 0u)
    {
        *pOut = newval;
        *pOldval = newval;
        *pCntDeb = 0u;
    }
    else
    {
        if(newval == (*pOldval))
        {   
            if ((*pCntDeb) >= (nf-1u))
            {
                *pOut = newval;
                *pCntDeb = 0u;
            }
            else
            {   
                *pCntDeb = (*pCntDeb) + 1u;
            }
        }
        else /* newval != oldval */
        {
            *pOldval = newval;
            *pCntDeb = 0u;
        }    
    }
}

void DigDebounceTwoWay(uint8_T *pOut, uint8_T *pOldval,uint8_T newval, uint8_T *pCntDeb, uint8_T nf0, uint8_T nf1) 
{
    if (newval != 0u)
    {
        DigDebounce(pOut, pOldval, newval, pCntDeb, nf1);
    }
    else
    {
        DigDebounce(pOut, pOldval, newval, pCntDeb, nf0);
    }
}


/*--------------------------------------------------------------------------*
 * UTILS_vectcat - Function to copy a vector
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
void UTILS_vectcat(uint8_T dest[], uint16_T dest_size,uint8_T offset,const volatile uint8_T src[], uint8_T size)
{
    uint8_T i;

    if (dest_size < ((uint16_T)(offset+size))) // avoid unexpected data overwritings
    {
        /* DO NOTHING */
    }
    else
    {
        for(i=0u; i<size; i++)
        {
            //*(dest+i+offset) = *(src+i);
            dest[i+offset] = src[i];
        }
    }
}

//#pragma ghs startnomisra
/*--------------------------------------------------------------------------*
 * UTILS_hexCharToInt - Fuction to convert a hex number character into 4 bit 
 *                      unsigned integer
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_T UTILS_hexCharToInt(uint8_T c)
{
    uint8_T retVal;
    // test if character is letter or number
    if(c <= 0x39u)
    {
        retVal = (c - 0x30u);
    }
    else
    {
        retVal = ((c - 0x41u) + 0xau);
        //uncomment next line and comment previous line if c params is lowercase
        //return (c - 'a' + 0xa);
    }
    return(retVal);
}

/*--------------------------------------------------------------------------*
 * UTILS_hexByteToInt - Fuction to convert a hex byte (two characters) into 8 bit 
 *                      unsigned integer
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_T UTILS_hexByteToInt(uint8_T hi, uint8_T lo)
{
    return ( (uint8_T)(UTILS_hexCharToInt(hi) << 4) + UTILS_hexCharToInt(lo) );  // return the unsigned integer   
}

/*--------------------------------------------------------------------------*
 * UTILS_CountSetBits - Function to get no of set bits in binary 
                        representation of passed binary no
 *
 * Implementation notes:
 * None
 *--------------------------------------------------------------------------*/
uint8_T UTILS_CountSetBits(uint32_T n)
{ 
    uint8_T count = 0u; 
    while(n != 0u)
    { 
        n = (n & (n-1u));
        count++;
    }
    return count;
}
//#pragma ghs endnomisra

#endif /* _BUILD_UTILS_ */
