/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_EZ_02_LIBINT_CODEREV/tree#$  */
/* $Revision:: 185736                                                                                         $  */
/* $Date:: 2021-10-08 10:05:18 +0200 (ven, 08 ott 2021)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  UTILS
**  Filename        :  Utils_out.h
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/
/*****************************************************************************
**
**                        UTILS Description
**
**  This SWC contains several utilities. 
**  
******************************************************************************/

#ifndef _UTILS_OUT_H_
#define _UTILS_OUT_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint32_T debug00;
extern uint32_T debug01;
extern uint32_T debug02;
extern uint32_T debug03;
extern uint32_T debug04;
extern uint32_T debug05;
extern uint32_T debug06;
extern uint32_T debug07;
extern int32_T  debug08;
extern int32_T  debug09;

extern uint32_T EEDebug00;
extern int32_T  EEDebug01;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : UTILS_nop
**
**   Description:
**    This function calls the assembler instruction "nop".
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
void    UTILS_nop(void);

/******************************************************************************
**   Function    : Delay_ms
**
**   Description:
**    This function delays of N ms. 
**
**   Parameters :
**    [in] uint32_T ms : delay in ms
**
**   Returns:
**    void
**
******************************************************************************/
void    Delay_ms(uint32_T ms);

/******************************************************************************
**   Function    : DigDebounce
**
**   Description:
**    This function applies a debounce filtering to a digital signal. 
**
**   Parameters :
**    [out] uint8_T *pOut    : digital debounced output value
**    [in] uint8_T *pOldval  : old value hold
**    [in] uint8_T newval    : new sampled value
**    [out] uint8_T *pCntDeb : debounce counter related to the signal
**    [in] uint8_T nf        : number of consecutive samples used to change the output value
**
**   Returns:
**    void
**
******************************************************************************/
void    DigDebounce(uint8_T *pOut, uint8_T *pOldval,uint8_T newval, uint8_T *pCntDeb, uint8_T nf);

/******************************************************************************
**   Function    : DigDebounceTwoWay
**
**   Description:
**    This function applies a two-way debounce filtering to a digital signal. 
**
**   Parameters :
**    [out] uint8_T *pOut    : digital debounced output value
**    [in] uint8_T *pOldval  : old value hold
**    [in] uint8_T newval    : new sampled value
**    [out] uint8_T *pCntDeb : debounce counter related to the signal
**    [in] uint8_T nf0       : number of consecutive samples used to change the output value
**    [in] uint8_T nf1       : number of consecutive samples used to change the output value
**
**   Returns:
**    void
**
******************************************************************************/
void    DigDebounceTwoWay(uint8_T *pOut, uint8_T *pOldval,uint8_T newval, uint8_T *pCntDeb, uint8_T nf0, uint8_T nf1);

/******************************************************************************
**   Function    : UTILS_vectcat
**
**   Description:
**    This function copies a vector.
**
**   Parameters :
**    [out] uint8_T dest[] : destination
**    [in] uint8_T offset : offset
**    [in] const uint8_T src[] : source 
**    [in] uint8_T size : size
**
**   Returns:
**    void
**
******************************************************************************/
void    UTILS_vectcat(uint8_T dest[], uint16_T dest_size,uint8_T offset,const volatile uint8_T src[], uint8_T size);

/******************************************************************************
**   Function    : UTILS_hexCharToInt
**
**   Description:
**    This function converts an hex character into a 4-bit unsigned integer.
**
**   Parameters :
**    [in] uint8_T c : hex character to be converted
**
**   Returns:
**    uint8_T retVal : Converted value
**
******************************************************************************/
uint8_T UTILS_hexCharToInt(uint8_T c);

/******************************************************************************
**   Function    : UTILS_hexByteToInt
**
**   Description:
**    This function converts two hex characters into a 8-bit unsigned integer.
**
**   Parameters :
**    [in] uint8_T hi : hex character to be converted
**    [in] uint8_T lo : hex character to be converted
**
**   Returns:
**    uint8_T return value : Converted value
**
******************************************************************************/
uint8_T UTILS_hexByteToInt(uint8_T hi, uint8_T lo);

/******************************************************************************
**   Function    : UTILS_CountSetBits
**
**   Description:
**    This function returns the number of set bits from a passed binary number. 
**
**   Parameters :
**    [in] uint32_T n : binary number
**
**   Returns:
**    void
**
******************************************************************************/
uint8_T UTILS_CountSetBits(uint32_T n);

#endif /* _UTILS_OUT_H_ */

/****************************************************************************
 ****************************************************************************/
