/*****************************************************************************************************************/
/* $HeadURL:: https://172.26.1.29/svn/Rep_GeOr/Coil/EISB8C/Appl/branches/EISB8C_RS_35_PI_0204/tree/DD/CANMGMI#$  */
/* $Revision:: 229285                                                                                         $  */
/* $Date:: 2022-06-16 11:44:10 +0200 (gio, 16 giu 2022)                                                       $  */
/* $Author:: SantoroR                                                                                         $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmIn
**  Filename        :  CanMgmIn_BR.c
**  Created on      :  07-jul-2023 09:37:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_CANMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "CanMgmIn_BR.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
uint16_T    LoadCAN;
uint16_T    LoadSxCAN;
uint16_T    LoadDxCAN;
uint16_T    RpmCAN;
int16_T     TWaterCAN;
int16_T     TAirCAN;
uint8_T     VDLoadCAN;
uint8_T     VDLoadDxCAN;
uint8_T     VDLoadSxCAN;
uint8_T     VDRpmCAN;
uint8_T     VDTWaterCAN;
uint8_T     VDTAirCAN;
uint8_T     FlgNoTrqCtrSA;
uint8_T     FlgRpmCANBlocked;
uint8_T     EngstsCAN;
uint8_T     EDriveSts;
uint8_T     DesCutOffCAN;
int16_T     DSAEcu;
uint8_T     FlgAnyAbsentPri;
uint32_T    FlgAnyAbsentVeh;
uint16_T    GasPos;
uint8_T     IgnitionCutOffDxCAN;
uint8_T     IgnitionCutOffSxCAN;
uint8_T     EngineAtLimiter;
uint8_T     EraseFaultsCAN;
uint8_T     NotEraseFaultsCAN;
uint8_T     WarmingUpCycleCAN;
uint8_T     NcmLivenessFaultCAN;
uint8_T     FlgRonStoredIn;
uint8_T     RonLevelIn;

uint8_T  StEraseFaults = 0u;
uint8_T  DtcErasedST = 0u;

/// DCTMOT
uint8_T  FlgDCTShifting;
uint8_T  FlgDiagRpmDis;

/// MOTDCT
uint8_T  NmaxControllerActive;

/// MOT1
uint8_T  VDGasPosCAN;
uint16_T AngThrottle;

/// STATUS_B_CAN
uint8_T     FuelLevelRawValueCAN;
uint8_T     FuelLevelFailStsCAN;
uint8_T     KeyStatusCAN;

/// STATUS_B_CAN
uint32_T    TotOdometerCAN;

// CombCtrl Stub
uint8_T AckReqResetCylCorrAd;

/// STUB
uint8_T     BEngineFuelCutoffStatusCAN = 0u;
uint8_T     FlgMKnockDis = 0u;
uint8_T     EngineTypeCAN = 0u;
uint8_T     ActivePhaseSearchCAN;
uint8_T     ResetTSparkAdat = 0u;
uint8_T     ISCMKnkPIUsedByECMCAN = 1u;

uint8_T     OLLamX1CAN;
uint8_T     OLLamX2CAN;
uint16_T    LamObjX1CAN;
uint16_T    LamObjX2CAN;
uint16_T    LamObj1CAN;
uint16_T    LamObj2CAN;
uint16_T    LamObjRichX2CAN;
uint16_T    LamObjLeanX2CAN;
uint16_T    LamSensX1CAN;
uint16_T    LamSensX2CAN;
uint8_T     VDLamSens1CAN;
uint8_T     VDLamSens2CAN;
uint8_T     AfrClActiveCAN;
uint8_T     CylBalCLReqCAN;
uint8_T     EGRPercCAN;
uint8_T     VDEGRPercCAN;
uint8_T     ReqResetCylCorrAdCAN;

/// DCTMOT
uint8_T  DCTState;
uint8_T  GearPos;
uint8_T  CntRpmCAN = 0u;

/// TIME_E_DATE
uint8_T  Hour1;
uint8_T  Hour2;
uint8_T  Minute1;
uint8_T  Minute2;
uint8_T  Day1;
uint8_T  Day2;
uint8_T  Month1;
uint8_T  Month2;
uint8_T  Year1;
uint8_T  Year2;
uint8_T  Year3;
uint8_T  Year4;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/// NCM_INFO Signals
static uint8_T  EcmEisb1RxFlg = 0u;
static uint8_T  EcmEisb1BufferEmpty = 0u;
static uint8_T  EcmEisb1BusOff = 0u;
static uint8_T  EcmEisb1BufferOverRun = 0u;
static uint8_T  EcmEisb1CntError = 0u;
static uint8_T  EcmEisb1CrcError = 0u;

static uint8_T  EcmEisb1MsgCntCAN = 0u;
static uint8_T  EcmEisb1MsgCntOld = 0u;
static uint8_T  EcmEisb1CrcRx = 0u;
static uint8_T  EcmEisb1CrcCalc = 0u;

static uint8_T  EcmEisb2RxFlg = 0u;
static uint8_T  EcmEisb2BufferEmpty = 0u;
static uint8_T  EcmEisb2BusOff = 0u;
static uint8_T  EcmEisb2BufferOverRun = 0u;
static uint8_T  EcmEisb2CntError = 0u;
static uint8_T  EcmEisb2CrcError = 0u;

static uint8_T  EcmEisb2MsgCntCAN = 0u;
static uint8_T  EcmEisb2MsgCntOld = 0u;
static uint8_T  EcmEisb2CrcRx = 0u;
static uint8_T  EcmEisb2CrcCalc = 0u;

static uint8_T  FlgNoTrqCtrSACAN;

static uint16_T RpmCANOld = 0u;
static uint8_T  CntDiagRpmDis = 0u;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : CanMgm_Initialize
**
**   Description:
**    Reset the values of the module variables. The global variables (outputs) 
**    and the static variables (states) are reset to the default values by this function.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_Initialize(void)
{
    CanMgm_ResetAllPri();
    CanMgm_EcmEisb2_Reset();
    CAN_EngineEnableReceive(POWER_TRAIN_CAN);
}

/******************************************************************************
**   Function    : CanMgm_CanRecv5ms
**
**   Description:
**    CAN receive operations at 5ms.
**    This functions performs:
**     - Decide whether to receive or to initialize depending on the CAN status
**     - Diagnose the CAN Busoff.
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CanRecv5ms(void)
{
uint8_T stDiag = NO_FAULT;
int16_T canStatus = NO_ERROR;

    canStatus = CAN_GetStatus(POWER_TRAIN_CAN);
    
    if(canStatus == NO_ERROR)
    {
#ifdef _BUILD_DIAGMGM_
        DiagMgm_SetDiagState(DIAG_PRIVATE_CAN, NO_PT_FAULT, &stDiag);
#endif
    }
    else if(canStatus == CAN_BUSOFF)
    {
        if((VBattery >= CANVBATTHRMIN) && (CntNoDiagAfterKeyOn == 0u))
        {
            CAN_BusOffRecovery(POWER_TRAIN_CAN);
#ifdef _BUILD_DIAGMGM_
            DiagMgm_SetDiagState(DIAG_PRIVATE_CAN, BUS_OFF, &stDiag);
#endif
        }
        if(stDiag == FAULT)
        {
            CanMgm_ResetAllPri();
            CanMgm_EcmEisb2_Reset();
        }
    }
    else
    {
        /* errori di CAN_ERR_PASSIVE e CAN_ERR_ACTIVE non gestiti*/
    }
}

/******************************************************************************
**   Function    : CanMgm_CanRecv10ms
**
**   Description:
**    CAN receive operations at 10ms.
**    This functions performs:
**     - Decide whether to receive or to initialize depending on the CAN status
**     - Diagnose the CAN Busoff.
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CanRecv10ms(void)
{
    int16_T canRxErr;
    uint8_T stDiag = NO_PT_FAULT;
    
    // PRIVATE CAN MSG

    canRxErr = CanMgm_CanRecv_EcmEisb1();
    // Raise a flag when a message EcmEisb1 has been successfully read
    if((EcmEisb1RxFlg != 1u) && (canRxErr != CAN_RX_BUFFER_EMPTY) && (canRxErr != CAN_BUSOFF))
    {
        EcmEisb1RxFlg = 1u;
    }
    
    /* EcmEisb1 Buffer Empty Diag management */
    if (KeySignal != 0u)
    {
        CanMgm_RunCanDiagAdv(canRxErr, &EcmEisb1BufferEmpty, &EcmEisb1BusOff, &EcmEisb1BufferOverRun, &EcmEisb1CrcError, &EcmEisb1CntError);
    }

        // ECM_EISB_1
    canRxErr = CanMgm_CanRecv_EcmEisb2();

    // Raise a flag when a message MOTION has been successfully read
    if((EcmEisb2RxFlg != 1u) && (canRxErr != CAN_RX_BUFFER_EMPTY) && (canRxErr != CAN_BUSOFF))
    {
        EcmEisb2RxFlg = 1u;
    }

    /* EcmEisb2 Buffer Empty Diag management */
    if (KeySignal != 0u)
    {
    CanMgm_RunCanDiagAdv(canRxErr, &EcmEisb2BufferEmpty, &EcmEisb2BusOff, &EcmEisb2BufferOverRun, &EcmEisb2CrcError, &EcmEisb2CntError);
    }
    
    /* Diag BufferEmpty Management */
    if((EcmEisb1BufferEmpty == 0u) && (EcmEisb2BufferEmpty == 0u))
    {
#ifdef _BUILD_DIAGMGM_
        DiagMgm_SetDiagState(DIAG_CAN_NODE_1, NO_PT_FAULT, &stDiag);
#endif
    }
    else if((EcmEisb1BufferEmpty >= CAN_ERROR_CNT_THR) || (EcmEisb2BufferEmpty >= CAN_ERROR_CNT_THR))
    {
        if((VBattery >= CANVBATTHRMIN) && (CntNoDiagAfterKeyOn == 0u))
        {
#ifdef _BUILD_DIAGMGM_
            DiagMgm_SetDiagState(DIAG_CAN_NODE_1, MISSING_MESSAGE, &stDiag);
#endif
            if(stDiag == FAULT)
            {
                CanMgm_EcmEisb1_Reset();
            }
            if(stDiag == FAULT)
            {
                CanMgm_EcmEisb2_Reset();
            }
        }
    }
    else
    {
    }

    /* EcmEisb1 Buffer Overrun, CRC ancd CNT Diag management */
    if((EcmEisb1BufferOverRun == 0u) && (EcmEisb2BufferOverRun == 0u) &&
        (EcmEisb1CrcError == 0u) && (EcmEisb2CrcError == 0u) &&
        (EcmEisb1CntError == 0u) && (EcmEisb2CntError == 0u))
    {
#ifdef _BUILD_DIAGMGM_
        DiagMgm_SetDiagState(DIAG_CAN_NODE_OVER_RUN, NO_PT_FAULT, &stDiag);
#endif
    }
    else if(EcmEisb1BufferOverRun >= CAN_ERROR_CNT_THR)
    {
        if((VBattery >= CANVBATTHRMIN) && (CntNoDiagAfterKeyOn == 0u))
        {
#ifdef _BUILD_DIAGMGM_
            DiagMgm_SetDiagState(DIAG_CAN_NODE_OVER_RUN, BUS_SIGNAL_MSG_FAILURE, &stDiag);
#endif
            if(stDiag == FAULT)
            {
                CanMgm_EcmEisb1_Reset();
            }
            if(stDiag == FAULT)
            {
                CanMgm_EcmEisb2_Reset();
            }
        }
    }
    else if((EcmEisb1CrcError >= CAN_ERROR_CNT_THR) || (EcmEisb2BufferEmpty >= CAN_ERROR_CNT_THR))
    {
        if((VBattery >= CANVBATTHRMIN) && (CntNoDiagAfterKeyOn == 0u))
        {
#ifdef _BUILD_DIAGMGM_
            DiagMgm_SetDiagState(DIAG_CAN_NODE_OVER_RUN, VALUE_OF_SIGNAL_PROTECTION_CALCULATION_INCORRECT, &stDiag);
#endif
            if(stDiag == FAULT)
            {
                CanMgm_EcmEisb1_Reset();
            }
            if(stDiag == FAULT)
            {
                CanMgm_EcmEisb2_Reset();
            }
        }
    }
    else if((EcmEisb1CntError >= CAN_ERROR_CNT_THR) || (EcmEisb2CntError >= CAN_ERROR_CNT_THR))
    {
        if((VBattery >= CANVBATTHRMIN) && (CntNoDiagAfterKeyOn == 0u))
        {
#ifdef _BUILD_DIAGMGM_
            DiagMgm_SetDiagState(DIAG_CAN_NODE_OVER_RUN, ALIVE_SEQUENCE_COUNTER_INCORRECT_NOT_UPDATED, &stDiag);
#endif
            if(stDiag == FAULT)
            {
                CanMgm_EcmEisb1_Reset();
            }
            if(stDiag == FAULT)
            {
                CanMgm_EcmEisb2_Reset();
            }
        }
    }
    else
    {
    }
    // Calculates FlgDisDiagRpm
    CanMgm_UpdateDisDiagRpm();

}

/******************************************************************************
**   Function    : CanMgm_CanRecv20ms
**
**   Description:
**    CAN receive operations at 20ms.
**    This functions performs:
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CanRecv20ms(void)
{
}

/******************************************************************************
**   Function    : CanMgm_CanRecv50ms
**
**   Description:
**    CAN receive operations at 50ms.
**    This functions performs:
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CanRecv50ms(void)
{
}

/******************************************************************************
**   Function    : CanMgm_CanRecv100ms
**
**   Description:
**    CAN receive operations at 100ms.
**    This functions performs:
**     - Call of single message receive function
**     - Call diagnosis functions
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_CanRecv100ms(void)
{
}

/******************************************************************************
**   Function    : CanMgm_SetClearDtcErasedST
**
**   Description:
**    Set DtcErasedST to its input value
**
**   Parameters :
**    [IN]value: TRUE or FALSE
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void CanMgm_SetClearDtcErasedST(uint8_T value)
{
    DtcErasedST = value;
}

/******************************************************************************
**   Function    : CanMgm_CheckConditions
**
**   Description:
**    This function checks parameter consistency for the activation of diagnostic 
**    jobs (download enabling, DTCs erasing and IOLIs activation
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint8_T CanMgm_CheckConditions(void)
{
uint8_T res;

    if((KeySignal == 1u) && 
      (((EngstsCAN == 0u) && (EcmEisb1BufferEmpty != MAX_uint8_T) && (EcmEisb1BufferOverRun != MAX_uint8_T) && (EcmEisb1BusOff != MAX_uint8_T)) ||
        (((EcmEisb1BufferEmpty == MAX_uint8_T) || (EcmEisb1BufferOverRun == MAX_uint8_T) || (EcmEisb1BusOff == MAX_uint8_T)) && (Rpm == 0u))))
    {
        res = 0u;
    }
    else
    {
        res = 1u;
    }
    return (res);
} 

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : CanMgm_ResetAllPri
**
**   Description:
**    Reset of all the Powertrain CAN output variables
**
**   Parameters :
**    [in]  void
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void CanMgm_ResetAllPri(void)
{
/******************************************************************/
    LoadSxCAN = 0u;
    LoadDxCAN = 0u;
    LoadCAN = LOADREC;             // Recovery load
    
    VDLoadSxCAN = 0u;
    VDLoadDxCAN = 0u;
    VDLoadCAN = 0u;
        
    RpmCAN = 0u;
    
    EngineAtLimiter = 0u;
    IgnitionCutOffDxCAN = 0u;
    EngstsCAN = 4u;
    IgnitionCutOffSxCAN = 0u;
    
    VDTAirCAN = 0u;
    FlgNoTrqCtrSACAN = 0u;
    FlgNoTrqCtrSA = FlgNoTrqCtrSACAN;
    NcmLivenessFaultCAN= 0u;
    
    VDTWaterCAN = 0u;
    VDRpmCAN = 0u;
    DesCutOffCAN = 0u;
    EraseFaultsCAN = 0u;
    NotEraseFaultsCAN = 1u;
     
    TAirCAN = TAIRREC;              // = 20� con risoluzione 1/16
    TWaterCAN = TWATERREC;
/****************************************************************************/
    FlgAnyAbsentPri = 0u;
    RpmCANOld = 0u;
    FlgRpmCANBlocked = 0u;
    CntRpmCAN = 0u;

/* Stub */
    DSAEcu = 0;
}

/******************************************************************************
**   Function    : CanMgm_CanRecv_EcmEisb1
**
**   Description:
**    Message TimeEDate receive function, performed operations:
**          - Read requested message from the CAN buffer
**          - Decode variables from the message
**          - Perform Bus off diagnosis
**          - Perform buffer underrun diagnosis
**          - Perform buffer overrun diagnosis
**
**   Parameters :
**    [in]  void
**    [out] void
**
**   Returns:
**    int_16_T error code: NO_ERROR
**                         CAN_RX_BUFFER_OVERRUN
**                         CAN_RX_BUFFER_EMPTY
**                         CAN_BUSOFF
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T CanMgm_CanRecv_EcmEisb1(void)
{
ECM_EISB_1_T EcmEisb1;
int16_T retval;
uint16_T NRxFr;
struct CANBuff_T *ptrDataBuf     = NULL;     // pointer to message data structure
struct CANBuff_T *pDataBufNew    = NULL;     // aux pointer
int16_T         canRxErrtmp = NO_ERROR;
uint16_T        canQueLen;                  // length of queue related to a buffer
boolean_T       endFifoFlag     = FALSE;
uint16_T tmpLoadCAN_raw;
int32_T  tmpLoadCAN_out;
int16_T tmpTAirCAN_raw;
int32_T  tmpTAirCAN_out;
int16_T tmpTWaterCAN_raw;
int32_T  tmpTWaterCAN_out;


    NRxFr = 0u;
    canQueLen = ECM_EISB_1_BUF_QUELEN;
    
    canRxErrtmp = CAN_RxData(POWER_TRAIN_CAN, ECM_EISB_1_BUF, &ptrDataBuf);

    if((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else    // no errors  
        {
            retval = NO_ERROR; 
        }
        
        ++NRxFr;  

        EcmEisb1.Byte1.R = ptrDataBuf->b[1];
        EcmEisb1MsgCntCAN = EcmEisb1.Byte1.B.CANMsgCnt_ECM_11;

        // Note:  In case of more than one message in queue, the most recent must
        //        be taken;
        endFifoFlag = FALSE;
        while ( (endFifoFlag == FALSE) && (NRxFr < canQueLen))
        {
            if (CAN_RxData(POWER_TRAIN_CAN, ECM_EISB_1_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = TRUE;
            }
            else
            {
                ++NRxFr;
                ptrDataBuf = pDataBufNew;   // save the previous pointer
                /* Test Counter */
                EcmEisb1MsgCntOld = EcmEisb1MsgCntCAN;
                EcmEisb1.Byte1.R = ptrDataBuf->b[1];
                EcmEisb1MsgCntCAN = EcmEisb1.Byte1.B.CANMsgCnt_ECM_11;
            }
        }

        // Copies data from temporary buffer to the message structure
        EcmEisb1.Byte0.R = ptrDataBuf->b[0];
        EcmEisb1.Byte1.R = ptrDataBuf->b[1];
        EcmEisb1.Byte2.R = ptrDataBuf->b[2];
        EcmEisb1.Byte3.R = ptrDataBuf->b[3];
        EcmEisb1.Byte4.R = ptrDataBuf->b[4];
        EcmEisb1.Byte5.R = ptrDataBuf->b[5];
        EcmEisb1.Byte6.R = ptrDataBuf->b[6];
        EcmEisb1.Byte7.R = ptrDataBuf->b[7];
        
        EcmEisb1CrcRx = EcmEisb1.Byte0.B.MsgChecksum_ECM_10;

        /* Calculate CRC for Master Ecu Frame */
        EcmEisb1CrcCalc = CRC8_SAE_J1850_bit(0u, (uint8_T *) &EcmEisb1.Byte0.R, 7u);
        
        if(((ENCRCALIVETEST & CAN_CRC_TEST_MASK) != 0u) && (FlgEOL == 0u) && (EcmEisb1CrcCalc != EcmEisb1CrcRx) && (retval == NO_ERROR))
        {
            retval = CAN_RX_CRC_ERR;
        }
        else if(((ENCRCALIVETEST & CAN_ALIVE_TEST_MASK) != 0u) && (FlgEOL == 0u) && (EcmEisb1MsgCntOld != 0xFFu) && (EcmEisb1MsgCntCAN != ((EcmEisb1MsgCntOld + 1u) & 0x0fu)))
        {
            retval = CAN_RX_CNT_ERR;
        }
        else
        {
            // Decodes the variables            
            /* FlgNoTrqCtrSACAN */
            FlgNoTrqCtrSACAN = EcmEisb1.Byte7.B.FlgNoTrqCtrSACAN7;
            FlgNoTrqCtrSA = FlgNoTrqCtrSACAN;

            /* TWater */
            VDTWaterCAN = (uint8_T)(EcmEisb1.Byte7.B.VDTWaterCAN7 == 0u);
            tmpTWaterCAN_raw =  (12* (int16_T)EcmEisb1.Byte4.B.TWatCAN4) - 768;
            CANMGM_Var_Diag((int32_T)tmpTWaterCAN_raw, (int32_T)TWaterCAN, TWATERCANVALASREC, (int32_T)TWATERREC, &tmpTWaterCAN_out, VDTWaterCAN, DIAG_T_WATER);
            TWaterCAN = (int16_T)tmpTWaterCAN_out;

            /* TAir */
            VDTAirCAN = (uint8_T)(EcmEisb1.Byte7.B.VDTAirCAN7 == 0u);
            tmpTAirCAN_raw = (12 * (int16_T)EcmEisb1.Byte3.B.TAirCAN3) - 768;
            CANMGM_Var_Diag((int32_T)tmpTAirCAN_raw, (int32_T)TAirCAN, TAIRCANVALASREC, (int32_T)TAIRREC, &tmpTAirCAN_out, VDTAirCAN, DIAG_T_AIR);
            TAirCAN = (int16_T)tmpTAirCAN_out;
            
            /* Load */
            VDLoadCAN = (uint8_T)(EcmEisb1.Byte7.B.VDLoadCAN7 == 0u);
            tmpLoadCAN_raw = (uint16_T)((uint16_T)EcmEisb1.Byte2.B.LoadCAN2 << 7);
            CANMGM_Var_Diag((int32_T)tmpLoadCAN_raw, (int32_T)LoadCAN, LOADCANVALASREC, (int32_T)LOADREC, &tmpLoadCAN_out, VDLoadCAN, DIAG_LOAD);
            if(tmpLoadCAN_out > (int32_T)MAX_LOAD)
            {
                LoadCAN = MAX_LOAD;
            }    
            else
            {
                LoadCAN = (uint16_T)tmpLoadCAN_out;
            }
            
            /* Rpm */
            VDRpmCAN = (uint8_T)(EcmEisb1.Byte7.B.VDRpmCAN7 == 0u);
            RpmCAN = ((uint16_T)((uint16_T)((uint16_T)EcmEisb1.Byte6.B.RpmCAN6 << 8) | ((uint16_T)EcmEisb1.Byte5.B.RpmCAN5)) >> 2);
            if(VDRpmCAN != 0u)
            {
                /* Test RpmCAN */
                CANMGM_RpmCANBlocked(Rpm, RpmCAN, RpmCANOld, &RpmCANOld, CntRpmCAN, &CntRpmCAN, THRCNTRPMCANBLOCKED, &FlgRpmCANBlocked);
            }

            /* EngstsCAN */
            EngstsCAN = EcmEisb1.Byte1.B.EngStSCAN1;
            
            IgnitionCutOffDxCAN = EcmEisb1.Byte7.B.IgnCutOff7;
            IgnitionCutOffSxCAN = IgnitionCutOffDxCAN;
            
            DesCutOffCAN = EcmEisb1.Byte7.B.DesCutOff7;
        }
        EcmEisb1MsgCntOld = EcmEisb1MsgCntCAN;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            retval = CAN_RX_BUFFER_EMPTY;
        }
        else
        {
            retval = CAN_BUSOFF;
        }
    }
    return retval;
}

/******************************************************************************
**   Function    : CanMgm_CanRecv_EcmEisb2
**
**   Description:
**    Message TimeEDate receive function, performed operations:
**          - Read requested message from the CAN buffer
**          - Decode variables from the message
**          - Perform Bus off diagnosis
**          - Perform buffer underrun diagnosis
**          - Perform buffer overrun diagnosis
**
**   Parameters :
**    [in]  void
**    [out] void
**
**   Returns:
**    int_16_T error code: NO_ERROR
**                         CAN_RX_BUFFER_OVERRUN
**                         CAN_RX_BUFFER_EMPTY
**                         CAN_BUSOFF
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static int16_T CanMgm_CanRecv_EcmEisb2(void)
{
ECM_EISB_2_T EcmEisb2;
int16_T retval;
uint16_T NRxFr;
struct CANBuff_T *ptrDataBuf     = NULL;     // pointer to message data structure
struct CANBuff_T *pDataBufNew    = NULL;     // aux pointer
int16_T         canRxErrtmp = NO_ERROR;
uint16_T        canQueLen;                  // length of queue related to a buffer
boolean_T       endFifoFlag     = FALSE;


    NRxFr = 0u;
    canQueLen = ECM_EISB_2_BUF_QUELEN;
    
    canRxErrtmp = CAN_RxData(POWER_TRAIN_CAN, ECM_EISB_2_BUF, &ptrDataBuf);

    if((canRxErrtmp != CAN_RX_BUFFER_EMPTY) && (canRxErrtmp != CAN_BUSOFF))
    {
        if (canRxErrtmp == CAN_RX_BUFFER_OVERRUN)
        {
            retval = CAN_RX_BUFFER_OVERRUN;
        }
        else    // no errors  
        {
            retval = NO_ERROR; 
        }
        
        ++NRxFr;  

        EcmEisb2.Byte1.R = ptrDataBuf->b[1];
        EcmEisb2MsgCntCAN = EcmEisb2.Byte1.B.CANMsgCnt_ECM_21;

        // Note:  In case of more than one message in queue, the most recent must
        //        be taken;
        endFifoFlag = FALSE;
        while ( (endFifoFlag == FALSE) && (NRxFr < canQueLen))
        {
            if (CAN_RxData(POWER_TRAIN_CAN, ECM_EISB_2_BUF, &pDataBufNew) == CAN_RX_BUFFER_EMPTY)
            {
                endFifoFlag = TRUE;
            }
            else
            {
                ++NRxFr;
                ptrDataBuf = pDataBufNew;   // save the previous pointer
                /* Test Counter */
                EcmEisb2MsgCntOld = EcmEisb2MsgCntCAN;
                EcmEisb2.Byte1.R = ptrDataBuf->b[1];
                EcmEisb2MsgCntCAN = EcmEisb2.Byte1.B.CANMsgCnt_ECM_21;
            }
        }

        // Copies data from temporary buffer to the message structure
        EcmEisb2.Byte0.R = ptrDataBuf->b[0];
        EcmEisb2.Byte1.R = ptrDataBuf->b[1];
        EcmEisb2.Byte2.R = ptrDataBuf->b[2];
        EcmEisb2.Byte3.R = ptrDataBuf->b[3];
        EcmEisb2.Byte4.R = ptrDataBuf->b[4];
        EcmEisb2.Byte5.R = ptrDataBuf->b[5];
        EcmEisb2.Byte6.R = ptrDataBuf->b[6];
        EcmEisb2.Byte7.R = ptrDataBuf->b[7];
        
        EcmEisb2CrcRx = EcmEisb2.Byte0.B.MsgChecksum_ECM_20;

        /* Calculate CRC for Master Ecu Frame */
        EcmEisb2CrcCalc = CRC8_SAE_J1850_bit(0u, (uint8_T *) &EcmEisb2.Byte0.R, 7u);
        
        if(((ENCRCALIVETEST & CAN_CRC_TEST_MASK) != 0u) && (FlgEOL == 0u) && (EcmEisb2CrcCalc != EcmEisb2CrcRx) && (retval == NO_ERROR))
        {
            retval = CAN_RX_CRC_ERR;
        }
        else if(((ENCRCALIVETEST & CAN_ALIVE_TEST_MASK) != 0u) && (FlgEOL == 0u) && (EcmEisb2MsgCntOld != 0xFFu) && (EcmEisb2MsgCntCAN != ((EcmEisb2MsgCntOld + 1u) & 0x0fu)))
        {
            retval = CAN_RX_CNT_ERR;
        }
        else
        {
            LamObjX1CAN = (((uint16_T)EcmEisb2.Byte2.B.Lam_Obj_12*2048u)/1000u)+717u;
            LamObjX2CAN = (((uint16_T)EcmEisb2.Byte3.B.Lam_Obj_23*2048u)/1000u)+717u;
            
            LamSensX1CAN = (((uint16_T)EcmEisb2.Byte4.B.Lam_Sens_14*2048u)/1000u)+717u;
            LamSensX2CAN = (((uint16_T)EcmEisb2.Byte5.B.Lam_Sens_25*2048u)/1000u)+717u;

            VDLamSens1CAN = (uint8_T)(EcmEisb2.Byte6.B.VD_Lam_Sens_16 == 0u);
            VDLamSens2CAN = (uint8_T)(EcmEisb2.Byte6.B.VD_Lam_Sens_26 == 0u);

           
            ReqResetCylCorrAdCAN = EcmEisb2.Byte6.B.ReqResetCylCorrAd6;

            if(FOCYLBALCLREQ < 0)
            {
                CylBalCLReqCAN = EcmEisb2.Byte6.B.Cyl_Bal_CL_Request6;
            }
            else
            {
                CylBalCLReqCAN = (uint8_T)FOCYLBALCLREQ;
            }
            
            if(FOAFRCLACTIVE < 0)
            {
                AfrClActiveCAN = EcmEisb2.Byte6.B.AFR_CL_Active6;
            }
            else
            {
                AfrClActiveCAN = (uint8_T)FOAFRCLACTIVE;
            }
            /* Stub CombCtrl Old, to be removed when CombCtrl is updated with new signals */
            OLLamX1CAN = (uint8_T)(AfrClActiveCAN == 0u);
            OLLamX2CAN = OLLamX1CAN;

            LamObj1CAN = LamObjX1CAN;
            LamObj2CAN = LamObjX1CAN;

            RefuelDetected = EcmEisb2.Byte6.B.RefuelSt6;
            FlgRonStoredIn = (uint8_T)(EcmEisb2.Byte6.B.RONLvlSt_OtherEngine6 == CAN_RON_DETECTED);
            RonLevelIn = EcmEisb2.Byte7.B.RON_OtherEngine7;
        }
        EcmEisb2MsgCntOld = EcmEisb2MsgCntCAN;
    }
    else
    {
        if (canRxErrtmp == CAN_RX_BUFFER_EMPTY) 
        {
            retval = CAN_RX_BUFFER_EMPTY;
        }
        else
        {
            retval = CAN_BUSOFF;
        }
    }
    return retval;
}

/******************************************************************************
**   Function    : CanMgm_EcmEisb1_Reset
**
**   Description:
**    Reset of all EcmEisb1 frame output variables to its recovery value
**    This function should be called after a confirmed error on Private Can
**
**   Parameters :
**    [in]  void
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void CanMgm_EcmEisb1_Reset(void)
{
    LoadSxCAN = 0u;
    LoadDxCAN = 0u;
    LoadCAN = LOADREC;             // Recovery load
    
    VDLoadSxCAN = 0u;
    VDLoadDxCAN = 0u;
    VDLoadCAN = 0u;

    RpmCAN = 0u;
    
    EngineAtLimiter = 0u;
    IgnitionCutOffDxCAN = 0u;
    EngstsCAN = 4u;
    IgnitionCutOffSxCAN = 0u;
    
    VDTAirCAN = 0u;
    FlgNoTrqCtrSACAN = 0u;
    FlgNoTrqCtrSA = 0u;
    NcmLivenessFaultCAN = 0u;
    
    VDTWaterCAN = 0u;
    VDRpmCAN = 0u;
    DesCutOffCAN = 0u;
    EraseFaultsCAN = 0u;
    NotEraseFaultsCAN = 1u;
     
    TAirCAN = TAIRREC;              // = 20� con risoluzione 1/16
    TWaterCAN = TWATERREC;

    /* Stub */
    DSAEcu = 0;
}

/***************************************************************************/
//   Function    :   CanMgm_EcmEisb1_Reset
//
//   Description:    
/*! \brief ResetMotIon
*/
//
//  Parameters and Returns:
/*! 
\param void
\returns void
*/
//  Notes:        
/*!
Resets message signals
*/
/**************************************************************************/
static void CanMgm_EcmEisb2_Reset(void)
{
    EcmEisb2MsgCntCAN = 0u;
    ReqResetCylCorrAdCAN = 0u;
    LamSensX1CAN = 0u;
    LamSensX2CAN = 0u;
    VDLamSens1CAN = 0u;
    VDLamSens2CAN = 0u;
    AfrClActiveCAN = 0u;
    CylBalCLReqCAN = 0u;
    OLLamX1CAN = 0u;
    OLLamX2CAN = 0u;
    RefuelDetected = 0u;
    FlgRonStoredIn = 0xFFu;
    RonLevelIn = 0xFFu;
}

/******************************************************************************
**   Function    : CanMgm_UpdateDisDiagRpm
**
**   Description:
**    Calculates FlgDiagRpmDis. In case of FlgDCTShifting or EngstsCAN in cranking
**    the DiagRpm is disabled. The signal is disabled with a timeout TIMDIAGRPMDIS
**
**   Parameters :
**    [in]  void
**    [out] void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void CanMgm_UpdateDisDiagRpm(void)
{
    /* TIMEOUT x DISABILITAZIONE DIAG_RPM */
    if(FlgDiagRpmDis == 0u)
    {
        if((FlgDCTShifting != 0u) || (EngstsCAN == ENGSTS_CRANKING))
        {
            FlgDiagRpmDis = 1u;
            CntDiagRpmDis = (uint8_T)TIMDIAGRPMDIS;
        }
        else
        {
            CntDiagRpmDis = 0u;
        }
    }
    else
    {
        if((FlgDCTShifting != 0u) || (EngstsCAN == ENGSTS_CRANKING))
        {
            CntDiagRpmDis = (uint8_T)TIMDIAGRPMDIS;
        }
        else
        {
            CntDiagRpmDis--;
        }
        
        if(CntDiagRpmDis == 0u)
        {
            FlgDiagRpmDis = 0u;
        }
        else
        {
            /* DO NOTHING */
        }
    }
}

#endif // _BUILD_CanMgm_

