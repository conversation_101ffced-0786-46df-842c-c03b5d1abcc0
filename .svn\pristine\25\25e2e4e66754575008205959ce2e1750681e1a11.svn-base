/****************************************************************************
*
* Copyright © 2018-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_map_cfg.h
 * @brief   GTM MAP Driver configuration macros and structures.
 *
 * @addtogroup MAP
 * @{
 */

#ifndef _GTM_MAP_CFG_H_
#define _GTM_MAP_CFG_H_

#include "gtm_map.h"

#define SPC5_GTM_MAP_CTRL                   (0UL | (SPC5_GTM_MAP_TRIGGER_SIGNAL_TIM0_CH0 << TSEL_BIT) | (SPC5_GTM_MAP_STATE_SIGNAL_TIM0_CH1 << SSL_BIT))

#endif /* _GTM_MAP_CFG_H_ */
/** @} */
