/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr.h
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Mocci A
******************************************************************************/

#ifndef SAFETYMNGR_H
#define SAFETYMNGR_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_out.h"

//#include "Rte_SafetyMngr_CDD.h"
#include "string.h"
//#include "Reg_Macros.h"
//#include "Reg_eSys_MagicCarpet.h"
//#include "Mcu_MagicCarpet_LLD.h"
#include "SafetyMngr_CommLib_out.h"
//#include "SafetyMngr_MCU.h"
#include "SafetyMngr_Cache_out.h"
//#include "SafetyMngr_MEMU.h"
#include "FlashCheckSM_MCU_r_xx_out.h"
#include "RamCheckSM_MCU_4_xx_out.h"
//#include "CMUCheckSM_MCU_r_xx.h"
#include "SafetyMngr_ADC_out.h"
#include "SafetyMngr_INTC_out.h"
//#include "SafetyMngr_MC_ME.h"
//#include "SafetyMngr_MC_RGM.h"
//#include "SafetyMngr_Wdg.h"
//#include "SafetyMngr_Temp.h"
//#include "Appl_Mcu.h"
#include "SafetyMngr_FCCU_out.h"
#include "SafetyMngr_PIT_out.h"
//#include "SafetyMngr_STCU.h"
//#include "SafetyMngr_SysClock.h"
//#include "SafetyMngr_CMU.h"
//#include "SafetyMngr_SMPU.h"
//#include "SafetyMngr_REG_PROT.h"
//#include "SafetyMngr_IMA.h"
//#include "SPC58NN_1.0D11_RMrev1.h"
//#include "Os_Types_Lcfg.h"
//#include "SafetyMngr_CDD_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_19.1 */

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/* None */


#endif // SAFETYMNGR_H

/****************************************************************************
 ****************************************************************************/
