/****************************************************************************
*
* Copyright � 2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
*
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

#ifndef ISB_H_
#define ISB_H_

#include <typedefs.h>
#include <gtm.h>
#include <crank_defs.h>
#include <crank_event.h>

#define ISB_CYLINDER                              8UL

#define COIL_SUPPLY_REMODULATION_LOADING_NUMBER   4UL
#define COIL_SUPPLY_REMODULATION_UNLOADING_NUMBER 4UL
#define COIL_SUPPLY_REMODULATION_STEP             (COIL_SUPPLY_REMODULATION_LOADING_NUMBER + COIL_SUPPLY_REMODULATION_UNLOADING_NUMBER)
#define EPWS_PHASE_NUM                              (4u)

/*===========================================================================*/
/* Module constants.                                                         */
/*===========================================================================*/

/*===========================================================================*/
/* Module pre-compile settings.                                         */
/*===========================================================================*/

/*===========================================================================*/
/* Derived constants and error checks.                                       */
/*===========================================================================*/

/*===========================================================================*/
/* Module data structures and types.                                         */
/*===========================================================================*/

/**
 * @brief   Driver state machine possible states.
 */
typedef enum {
		ISB_UNINIT          = 0,    /**< Not initialized                    */
		ISB_INIT            = 1,    /**< Initialized                        */
		ISB_READY           = 2,
		ISB_CONF_READY      = 3,
		ISB_WAIT_NEW_CONFIG = 4,
		ISB_LAST            = 5,     /**< last index status                  */
        ISB_WAIT_FIRST_TRIG = 6,
        ISB_RUNNING         = 7,     /**< First trigger received             */
        ISB_BYPASSED        = 8      /**< Bypass circuit active              */
} isb_state_t;

/**
 * @brief   Type of a structure representing a ISB driver state.
 */
typedef struct {
	isb_state_t state;   /*!< isb state */
}ISB_Status;

typedef struct {
	uint32_t duration;
	uint32_t period;
	uint32_t duty;
}ISB_COIL_SUPPLY;

typedef struct {
	uint32_t num_pulse;
	uint32_t epws_period;
	uint32_t epws_duty;
}ISB_EPWS_Config;


typedef struct {
	uint32_t        cylinder_index;

	GTM_PSMDriver   *psmd;
	uint32_t        psm_data_channel;

	GTM_MCSDriver   *mcsd;
	uint32_t        mcs_cyl_channel;

	GTM_MCSDriver   *mcs_mosd;
	uint32_t        mcs_mos_channel;

	GTM_TIMDriver   *timd;
	uint32_t        tim_channel;

	uint32_t        output_delay;

	uint32_t        epws;

	ISB_EPWS_Config *epws_data[EPWS_PHASE_NUM];

	uint32_t        epws_last_pulse_Ton;

	uint32_t        epws_timeout;

	uint32_t        pmos_duration;
	uint32_t        nmos_duration;

	uint32_t        coil_loading_step;
	uint32_t        coil_unloading_step;
	ISB_COIL_SUPPLY *coil_prog[COIL_SUPPLY_REMODULATION_STEP];
}ISB_CYLINDER_Config;


typedef struct {
	uint32_t system_cylinder;
	ISB_CYLINDER_Config *cylinder_config[ISB_CYLINDER];
}ISB_Config;

/**
 * @brief   Structure representing an ISB driver.
 */
typedef struct {
	/**
	 * @brief Driver state.
	 */
	ISB_Status *status;

	ISB_Config *config;
}ISBDriver;

/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

/* Inclusion of the configurable data header, it contains mainly external
   declarations so it belongs to this section.*/

#if !defined(__DOXYGEN__)
extern ISBDriver ISBD;
#endif




#ifdef __cplusplus
extern "C" {
#endif
void isbInit( ISBDriver *isbd);
void isbStart(ISBDriver *isbd, ISB_Config *config);
void isbSetup(ISBDriver *isbd);
void isbSetupCylinder(ISBDriver *isbd, ISB_Config *config, uint32_T cylinder);
isb_state_t isbGetState(ISBDriver *isbd);
void isbSetState(ISBDriver *isbd, isb_state_t state);
void isbSetTimeout(ISBDriver *isbd, uint32_T cylinder, uint8_T timeout_mode, uint32_T timeout_value);
void isbCrankStart(ISBDriver *isbd);
void isb_crank_init_DPLL(void);
#ifdef __cplusplus
}
#endif
#endif /* ISB_H_ */
