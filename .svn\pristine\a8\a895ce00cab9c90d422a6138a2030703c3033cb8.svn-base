/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "digin.h"

#ifdef _BUILD_DIGIN_
#pragma ghs section rodata=".calib"


/*!
 * \defgroup Calibrations Calibrations
 \brief Parameters that can be tuned via CCP
 
 * \sgroup
 */
/*-----------------------------------*
 * CALIBRATIONS
 *-----------------------------------*/
/// Threshold for the debouncing of KeySignal
CALQUAL CALQUAL_POST uint8_T    THDEBKEYSIGNAL = 5u;

/// Threshold for the debouncing of KeyEnDiagSignal
CALQUAL CALQUAL_POST uint8_T    THDEBKEYENDIAGSIGNAL = 10u;

/// Flag to force KeySignal
CALQUAL CALQUAL_POST uint8_T    FLGFOKEYSIGNAL = 0u;

/// Forced value of KeySignal
CALQUAL CALQUAL_POST uint8_T    FOKEYSIGNAL = 0u;


/// Threshold for the debouncing of BankSel
CALQUAL CALQUAL_POST uint8_T    THDEBBANKSEL = 5u;

/// Flag to force BankSel
CALQUAL CALQUAL_POST uint8_T    FLGFOBANKSEL = 1u;

/// Forced value of BankSel
CALQUAL CALQUAL_POST uint8_T    FOBANKSEL = 0u;

CALQUAL CALQUAL_POST  uint8_T  ENCANENGTYPE = 0u;

CALQUAL CALQUAL_POST uint8_T FORCEENGINETYPERUN = 0u;
/// Low diagnosis threshold for OL VVBankSel
CALQUAL CALQUAL_POST uint16_T    VMIDLOVBANKSEL = BANKSEL_OL_THRLOW;
/// High diagnosis threshold for OL VVBankSel
CALQUAL CALQUAL_POST uint16_T    VMIDHIVBANKSEL = BANKSEL_OL_THRHI;

#endif  /* _BUILD_DIGIN_ */
/*!\egroup*/

