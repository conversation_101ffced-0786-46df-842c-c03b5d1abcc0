/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef _EEMGM_H_
#define _EEMGM_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "string.h"
#include "recovery.h"
#include "eemgm_out.h"
#include "Dspi_out.h"
#include "WDT_out.h"
#include "diagmgm_out.h"


/*!
\defgroup PrivateDefines Private Defines
\brief Defines with module scope
\sgroup
*/
/*-----------------------------------*
 * PRIVATE DEFINES
 *-----------------------------------*/
/// Definiton of ID0
#define EEMGM_ID0           (0u)
/// Definiton of ID1
#define EEMGM_ID1           (1u)
/// Definiton of ID2
#define EEMGM_ID2           (2u)
/// Definiton of ID3
#define EEMGM_ID3           (3u)
/// Definiton of ID4
#define EEMGM_ID4           (4u)
/// Definiton of ID5
#define EEMGM_ID5           (5u)
/// Definiton of ID6
#define EEMGM_ID6           (6u)
/// Definiton of ID7
#define EEMGM_ID7           (7u)
/// Definiton of ID8
#define EEMGM_ID8           (8u)
/// Definiton of ID9
#define EEMGM_ID9           (9u)
/// Definiton of ID10
#define EEMGM_ID10          (10u)
/// Definiton of ID11
#define EEMGM_ID11          (11u)
/// Number of IDs
#define EE_ID_TASK_NUMBER   (12u) //must be alligned with  EE_ID_TOT_NUMBER

/// BLA
#define EE_IDTASK_NOT_ALLOWED   ((int16_T)(-1))
/// BLA
#define EEMGM_ID_NOT_INITIALIZED  ((int16_T)(-1))

/// No operation to be done on the ID
#define EEMGM_NO_OP         (0u) 
/// Write the ID to EE
#define EEMGM_WRITE         (1u)
/// Invalidate the ID in EE
#define EEMGM_INVAL         (2u)
/// Invalidate the ID in EE and copy default values in RAM variables
#define EEMGM_INVRN         (3u)
///
#define EEMGM_REC_MAX_ATTEMPTS (3u)
///
#define EEMGM_CNT_THRESHOLD     (2u)

/* EEMGM sw layer    */
#define EE_ID_PAD_LSH_BOOT  (0u)
#define EE_ID_PAD_LSH_APPL  (16u)

/*!\egroup*/


/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
/* Labels from Linker File */
extern uint32_T __EE_ID0_START;
extern uint32_T __EE_ID0_END;
extern uint32_T __EE_ID0_DATA_START;
extern uint32_T __EE_ID0_DATA_END;
extern uint32_T __EE_ID1_START;
extern uint32_T __EE_ID1_END;
extern uint32_T __EE_ID1_DATA_START;
extern uint32_T __EE_ID1_DATA_END;
extern uint32_T __EE_ID2_START;
extern uint32_T __EE_ID2_END;
extern uint32_T __EE_ID2_DATA_START;
extern uint32_T __EE_ID2_DATA_END;
extern uint32_T __EE_ID3_START;
extern uint32_T __EE_ID3_END;
extern uint32_T __EE_ID3_DATA_START;
extern uint32_T __EE_ID3_DATA_END;
extern uint32_T __EE_ID4_START;
extern uint32_T __EE_ID4_END;
extern uint32_T __EE_ID4_DATA_START;
extern uint32_T __EE_ID4_DATA_END;
extern uint32_T __EE_ID5_START;
extern uint32_T __EE_ID5_END;
extern uint32_T __EE_ID5_DATA_START;
extern uint32_T __EE_ID5_DATA_END;
extern uint32_T __EE_ID6_START;
extern uint32_T __EE_ID6_END;
extern uint32_T __EE_ID6_DATA_START;
extern uint32_T __EE_ID6_DATA_END;
extern uint32_T __EE_ID7_START;
extern uint32_T __EE_ID7_END;
extern uint32_T __EE_ID7_DATA_START;
extern uint32_T __EE_ID7_DATA_END;
extern uint32_T __EE_ID8_START;
extern uint32_T __EE_ID8_END;
extern uint32_T __EE_ID8_DATA_START;
extern uint32_T __EE_ID8_DATA_END;
extern uint32_T __EE_ID9_START;
extern uint32_T __EE_ID9_END;
extern uint32_T __EE_ID9_DATA_START;
extern uint32_T __EE_ID9_DATA_END;
extern uint32_T __EE_ID10_START;
extern uint32_T __EE_ID10_END;
extern uint32_T __EE_ID10_DATA_START;
extern uint32_T __EE_ID10_DATA_END;
extern uint32_T __EE_ID11_START;
extern uint32_T __EE_ID11_END;
extern uint32_T __EE_ID11_DATA_START;
extern uint32_T __EE_ID11_DATA_END;

/*-----------------------------------*
* IMPORTED CALIBRATIONS
*-----------------------------------*/
extern CALQUAL CALQUAL_POST uint16_T ERASEEEPROM;

/*-----------------------------------*
 * PRIVATE FUNCTION PROTOTYPES
 *-----------------------------------*/
static int16_T EEMGM_InitID(uint16_T ID);

 
#endif
