/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           TLE9278BQX_Prs.c
 **  File Creation Date: 03-Jul-2023
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TLE9278BQX_Prs
 **  Model Description:
 **  Model Version:      1.427
 **  Model Author:       SalimbeniT - Tue Jan 20 16:01:59 2015
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: LanaL - Mon Jul 03 15:03:27 2023
 **
 **  Last Saved Modification:  LanaL - Mon Jul 03 15:01:53 2023
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "TLE9278BQX_Prs_out.h"
#include "TLE9278BQX_Prs_private.h"
#include "asr_s32.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ADDR_MASK                      127U                      /* Referenced by: '<S4>/Parse_Frame' */

/* mask */
#define DATA_MASK                      65280U                    /* Referenced by: '<S4>/Parse_Frame' */

/* mask */
#define IDX_START_CMD                  1U                        /* Referenced by: '<S4>/Parse_Frame' */

/* idx */
#define ID_VER_TLE9278BQX_PRS_DEF      1426U                     /* Referenced by: '<S5>/ID_VER_TLE9278BQX_PRS_DEF' */

/* ID model version define */
#define M_S_CTRL_ID                    0U                        /* Referenced by: '<S12>/Calc_Mode' */

/* mask */
#define READ_MASK                      0U                        /* Referenced by: '<S4>/Parse_Frame' */

/* mask */
#define STATUS_MASK                    255U                      /* Referenced by: '<S4>/Parse_Frame' */

/* mask */
#define STATUS_START_ADDR              32U                       /* Referenced by: '<S4>/Parse_Frame' */

/* mask */
#define WD_CTRL_ID                     2U                        /* Referenced by: '<S4>/Parse_Frame' */

/* mask */
#define WRITE_CLEAR_MASK               128U                      /* Referenced by: '<S4>/Parse_Frame' */

/* mask */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_TLE9278BQX_PRS_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_CONST */
static const uint16_T RESET_GLOBAL_REG[25] = { 65535U, 65535U, 65535U, 65535U,
  65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U,
  65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U, 65535U,
  65535U } ;                        /* Referenced by: '<S5>/RESET_GLOBAL_REG' */

/* mask */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXSBCNUMREC = 10U;/* Referenced by: '<S4>/Parse_Frame' */

/* Max number of recovery messages */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T FlgSBCResend;                  /* '<S3>/Merge1' */

/* SBC Status */
uint16_T SBCGlobalCfgReg[25];          /* '<S3>/Merge5' */

/* Config data message */
uint16_T SBCIndexSearch;               /* '<S13>/PreLookUpIdSearch_U16' */

/* SBC SBCIndex Search */
uint16_T SBCIndexSet;                  /* '<S15>/SBCIndexSet' */

/* Index to write data */
uint16_T SBCStatusReg;                 /* '<S3>/Merge8' */

/* SBC Status diagnosis */
uint16_T SBCValSet;                    /* '<S15>/SBCValSet' */

/* Data to write */
uint8_T StSBCMode;                     /* '<S3>/Merge2' */

/* M_S_CTRL: MODE */
uint16_T VtRecSBCMsg[25];              /* '<S4>/Parse_Frame' */

/* SBC Counter Rec */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T CntSBCFrmChg;/* '<S3>/Merge3' */

/* SBC Frame changed */
STATIC_TEST_POINT uint32_T IdVer_TLE9278BQX_Prs;/* '<S5>/ID_VER_TLE9278BQX_PRS_DEF' */

/* ID model version */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_TLE9278BQX_Prs_T TLE9278BQX_Prs_DW;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Output and update for function-call system: '<S4>/fc_SBCGlobalStatusReg' */
void TLE9278BQ_fc_SBCGlobalStatusReg(uint16_T rtu_SBCIndexSet, uint16_T
  rtu_SBCValSet)
{
  /* Chart: '<S10>/fc_seq_call' incorporates:
   *  SubSystem: '<S10>/data'
   */
  /* Inport: '<S15>/SBCIndexSet' */
  /* Gateway: TLE9278BQX_Prs/fc_Bkg/fc_SBCGlobalStatusReg/fc_seq_call */
  /* During: TLE9278BQX_Prs/fc_Bkg/fc_SBCGlobalStatusReg/fc_seq_call */
  /* Entry Internal: TLE9278BQX_Prs/fc_Bkg/fc_SBCGlobalStatusReg/fc_seq_call */
  /* Transition: '<S16>:2' */
  /* Transition: '<S16>:4' */
  /* '<S16>:4:1' fc_UpdData; */
  /* Event: '<S16>:5' */
  SBCIndexSet = rtu_SBCIndexSet;

  /* Inport: '<S15>/SBCValSet' */
  SBCValSet = rtu_SBCValSet;

  /* Chart: '<S10>/fc_seq_call' incorporates:
   *  SubSystem: '<S10>/body'
   */
  /* CCaller: '<S14>/TLE9278BQX_Cfg_Set' */
  /* '<S16>:4:2' fc_Body; */
  /* Event: '<S16>:6' */
  TLE9278BQX_Cfg_Set();
}

/* Output and update for function-call system: '<S4>/fc_BinarySearchData' */
void TLE9278BQX__fc_BinarySearchData(uint16_T rtu_addrTag)
{
  /* local block i/o variables */
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint8_T tmp;

  /* S-Function (PreLookUpIdSearch_U16): '<S13>/PreLookUpIdSearch_U16' incorporates:
   *  Constant: '<S7>/SBC_REG'
   *  Constant: '<S7>/SBC_REG_SIZE - 1'
   */
  tmp = (uint8_T)(((uint32_T)SBC_REG_SIZE) - 1U);
  PreLookUpIdSearch_U16( (&(SBCIndexSearch)), &rtb_PreLookUpIdSearch_U16_o2,
                        rtu_addrTag, (&(SBC_REG[0])), tmp);
}

/* Output and update for function-call system: '<S2>/fc_Bkg' */
void TLE9278BQX_Prs_fc_Bkg(void)
{
  uint16_T numCmd;
  uint16_T i;
  uint16_T rtb_sbcStatusReg;
  int32_T i_0;

  /* Inport: '<Root>/CntTrgSBCRes'
   *
   * Block description for '<Root>/CntTrgSBCRes':
   *  Resend data pattern trigger (as conter)
   */
  /* Gateway: TLE9278BQX_Prs/fc_Bkg/Parse_Frame */
  /* During: TLE9278BQX_Prs/fc_Bkg/Parse_Frame */
  /* Entry Internal: TLE9278BQX_Prs/fc_Bkg/Parse_Frame */
  /* Transition: '<S6>:136' */
  if (CntTrgSBCRes != TLE9278BQX_Prs_DW.oldCntTrgSBCRes) {
    /* Transition: '<S6>:137' */
    TLE9278BQX_Prs_DW.oldCntTrgSBCRes = CntTrgSBCRes;
    TLE9278BQX_Prs_DW.flgSBCResend = 0U;
  } else {
    /* Transition: '<S6>:135' */
  }

  /* End of Inport: '<Root>/CntTrgSBCRes' */
  /* Transition: '<S6>:4' */
  i = ((uint16_T)IDX_START_CMD);

  /* Inport: '<Root>/SBCDataTxBuffer'
   *
   * Block description for '<Root>/SBCDataTxBuffer':
   *  SBC Data buffer sends by SPI
   */
  numCmd = SBCDataTxBuffer[0];
  rtb_sbcStatusReg = 0U;

  /* Inport: '<Root>/SBCSpiError'
   *
   * Block description for '<Root>/SBCSpiError':
   *  Com Error
   */
  while ((i <= numCmd) && (SBCSpiError == 0)) {
    /* Inport: '<Root>/SBCDataTxBuffer'
     *
     * Block description for '<Root>/SBCDataTxBuffer':
     *  SBC Data buffer sends by SPI
     */
    /* Transition: '<S6>:13' */
    i_0 = ((int32_T)SBCDataTxBuffer[(i)]) & ((int32_T)((uint16_T)ADDR_MASK));
    if (((((int32_T)SBCDataTxBuffer[(i)]) & ((int32_T)((uint16_T)
            WRITE_CLEAR_MASK))) != 0) && (i_0 < ((int32_T)((uint16_T)
           STATUS_START_ADDR)))) {
      /* Transition: '<S6>:21' */
      /*  Cmd Control Write */
      /* Transition: '<S6>:16' */
      TLE9278BQX_Prs_DW.addrTag = (uint16_T)i_0;

      /* Outputs for Function Call SubSystem: '<S4>/fc_BinarySearchData' */
      /*  Estract Addr */
      /* Event: '<S6>:14' */
      TLE9278BQX__fc_BinarySearchData(TLE9278BQX_Prs_DW.addrTag);

      /* End of Outputs for SubSystem: '<S4>/fc_BinarySearchData' */
      /*  Estract idx */
      TLE9278BQX_Prs_DW.sbcGlobalCfgReg[SBCIndexSearch] = SBCDataTxBuffer[(i)];

      /* Inport: '<Root>/SBCDataRxBuffer'
       *
       * Block description for '<Root>/SBCDataRxBuffer':
       *  SBC Data buffer received by SPI
       */
      /*  Store config data */
      i_0 = ((int32_T)SBCDataRxBuffer[(i)]) & ((int32_T)((uint16_T)STATUS_MASK));
      if (i_0 != 0) {
        /* Transition: '<S6>:51' */
        rtb_sbcStatusReg = (uint16_T)((int32_T)(i_0 | ((int32_T)rtb_sbcStatusReg)));

        /* Transition: '<S6>:52' */
      } else {
        /* Transition: '<S6>:23' */
        /* Transition: '<S6>:99' */
      }

      /* Transition: '<S6>:100' */
      /* Transition: '<S6>:103' */
      /* Transition: '<S6>:101' */
      /* Transition: '<S6>:104' */
      /* Transition: '<S6>:105' */
      /* Transition: '<S6>:106' */
    } else {
      /* Transition: '<S6>:24' */
      if (i_0 < ((int32_T)((uint16_T)STATUS_START_ADDR))) {
        /* Transition: '<S6>:43' */
        /*  Cmd Control Read */
        /* Transition: '<S6>:48' */
        TLE9278BQX_Prs_DW.addrTag = (uint16_T)(SBCDataTxBuffer[(i)] & ((uint16_T)
          ADDR_MASK));

        /* Outputs for Function Call SubSystem: '<S4>/fc_BinarySearchData' */
        /*  Estract Addr */
        /* Event: '<S6>:14' */
        TLE9278BQX__fc_BinarySearchData(TLE9278BQX_Prs_DW.addrTag);

        /* End of Outputs for SubSystem: '<S4>/fc_BinarySearchData' */

        /* Constant: '<S4>/SBC_REG_MASK' incorporates:
         *  Inport: '<Root>/SBCDataRxBuffer'
         *
         * Block description for '<Root>/SBCDataRxBuffer':
         *  SBC Data buffer received by SPI
         */
        /*  Estract idx */
        if (((TLE9278BQX_Prs_DW.sbcGlobalCfgReg[SBCIndexSearch] & SBC_REG_MASK
              [(SBCIndexSearch)]) != (SBCDataRxBuffer[(i)] & SBC_REG_MASK
              [(SBCIndexSearch)])) && (((int32_T)
              TLE9278BQX_Prs_DW.sbcGlobalCfgReg[SBCIndexSearch]) != 0)) {
          /* Outputs for Function Call SubSystem: '<S4>/fc_SBCGlobalStatusReg' */
          /* Transition: '<S6>:49' */
          /* Event: '<S6>:141' */
          TLE9278BQ_fc_SBCGlobalStatusReg(SBCIndexSearch, (uint16_T)
            (SBCDataRxBuffer[(i)] & ((uint16_T)DATA_MASK)));

          /* End of Outputs for SubSystem: '<S4>/fc_SBCGlobalStatusReg' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_SBCFrameChanged' */
          /* Sum: '<S8>/Add' incorporates:
           *  Constant: '<S8>/Constant1'
           *  Memory: '<S8>/Memory'
           */
          /*  Store config changed */
          /* Event: '<S6>:66' */
          TLE9278BQX_Prs_DW.Add = (uint16_T)(((uint32_T)
            TLE9278BQX_Prs_DW.Memory_PreviousInput) + 1U);

          /* Update for Memory: '<S8>/Memory' */
          TLE9278BQX_Prs_DW.Memory_PreviousInput = TLE9278BQX_Prs_DW.Add;

          /* End of Outputs for SubSystem: '<S4>/fc_SBCFrameChanged' */
          VtRecSBCMsg[(SBCIndexSearch)] = (uint16_T)((int32_T)(((int32_T)
            VtRecSBCMsg[(SBCIndexSearch)]) + 1));
          if (VtRecSBCMsg[(SBCIndexSearch)] < MAXSBCNUMREC) {
            VtRecSBCMsg[(SBCIndexSearch)] = VtRecSBCMsg[(SBCIndexSearch)];
          } else {
            VtRecSBCMsg[(SBCIndexSearch)] = MAXSBCNUMREC;
          }

          TLE9278BQX_Prs_DW.flgSBCResend = (uint8_T)((MAXSBCNUMREC -
            VtRecSBCMsg[(SBCIndexSearch)]) | ((uint16_T)
            TLE9278BQX_Prs_DW.flgSBCResend));
        } else {
          /* Outputs for Function Call SubSystem: '<S4>/fc_SBCGlobalStatusReg' */
          /* Transition: '<S6>:59' */
          /* Event: '<S6>:141' */
          TLE9278BQ_fc_SBCGlobalStatusReg(SBCIndexSearch, (uint16_T)
            (SBCDataRxBuffer[(i)] & ((uint16_T)DATA_MASK)));

          /* End of Outputs for SubSystem: '<S4>/fc_SBCGlobalStatusReg' */
          /*  Store info data */
        }

        /* End of Constant: '<S4>/SBC_REG_MASK' */

        /* Inport: '<Root>/SBCDataRxBuffer'
         *
         * Block description for '<Root>/SBCDataRxBuffer':
         *  SBC Data buffer received by SPI
         */
        if ((((int32_T)SBCDataRxBuffer[(i)]) & ((int32_T)((uint16_T)STATUS_MASK)))
            != 0) {
          /* Transition: '<S6>:57' */
          rtb_sbcStatusReg = (uint16_T)((SBCDataRxBuffer[(i)] & ((uint16_T)
            STATUS_MASK)) | rtb_sbcStatusReg);

          /* Transition: '<S6>:64' */
          /* Transition: '<S6>:101' */
          /* Transition: '<S6>:104' */
          /* Transition: '<S6>:105' */
          /* Transition: '<S6>:106' */
        } else {
          /* Transition: '<S6>:61' */
          /* Transition: '<S6>:103' */
          /* Transition: '<S6>:101' */
          /* Transition: '<S6>:104' */
          /* Transition: '<S6>:105' */
          /* Transition: '<S6>:106' */
        }
      } else {
        /* Transition: '<S6>:45' */
        if (i_0 >= ((int32_T)((uint16_T)STATUS_START_ADDR))) {
          /* Transition: '<S6>:72' */
          /*  Status Read/Clear */
          /* Transition: '<S6>:75' */
          TLE9278BQX_Prs_DW.addrTag = (uint16_T)(SBCDataTxBuffer[(i)] &
            ((uint16_T)ADDR_MASK));

          /* Outputs for Function Call SubSystem: '<S4>/fc_BinarySearchData' */
          /*  Estract Addr */
          /* Event: '<S6>:14' */
          TLE9278BQX__fc_BinarySearchData(TLE9278BQX_Prs_DW.addrTag);

          /* End of Outputs for SubSystem: '<S4>/fc_BinarySearchData' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_SBCGlobalStatusReg' */
          /* Inport: '<Root>/SBCDataRxBuffer'
           *
           * Block description for '<Root>/SBCDataRxBuffer':
           *  SBC Data buffer received by SPI
           */
          /*  Estract idx */
          /* Event: '<S6>:141' */
          TLE9278BQ_fc_SBCGlobalStatusReg(SBCIndexSearch, (uint16_T)
            (SBCDataRxBuffer[(i)] & ((uint16_T)DATA_MASK)));

          /* End of Outputs for SubSystem: '<S4>/fc_SBCGlobalStatusReg' */
          /*  Store info data */
          if ((((int32_T)SBCDataRxBuffer[(i)]) & ((int32_T)((uint16_T)
                 STATUS_MASK))) != 0) {
            /* Transition: '<S6>:76' */
            rtb_sbcStatusReg = (uint16_T)((SBCDataRxBuffer[(i)] & ((uint16_T)
              STATUS_MASK)) | rtb_sbcStatusReg);

            /* Transition: '<S6>:104' */
            /* Transition: '<S6>:105' */
            /* Transition: '<S6>:106' */
          } else {
            /* Transition: '<S6>:80' */
            /* Transition: '<S6>:106' */
          }
        } else {
          /* Transition: '<S6>:77' */
          /*  Cmd Skiped
             Address data Error */
          /* Event: '<S6>:132' */
        }
      }
    }

    /* Transition: '<S6>:17' */
    i = (uint16_T)((int32_T)(((int32_T)i) + 1));
  }

  /* End of Inport: '<Root>/SBCSpiError' */

  /* SignalConversion generated from: '<S4>/CntSBCFrmChg' */
  /* Transition: '<S6>:149' */
  /* Event: '<S6>:90' */
  /* Gateway: TLE9278BQX_Prs/fc_Bkg/fc_SelMode/Calc_Mode */
  /* During: TLE9278BQX_Prs/fc_Bkg/fc_SelMode/Calc_Mode */
  /* Entry Internal: TLE9278BQX_Prs/fc_Bkg/fc_SelMode/Calc_Mode */
  /* Transition: '<S17>:2' */
  /* Transition: '<S17>:4' */
  CntSBCFrmChg = TLE9278BQX_Prs_DW.Add;

  /* SignalConversion generated from: '<S4>/FlgSBCResend' */
  FlgSBCResend = TLE9278BQX_Prs_DW.flgSBCResend;

  /* SignalConversion generated from: '<S4>/SBCGlobalCfgReg' */
  for (i_0 = 0; i_0 < 25; i_0++) {
    SBCGlobalCfgReg[(i_0)] = TLE9278BQX_Prs_DW.sbcGlobalCfgReg[i_0];
  }

  /* End of SignalConversion generated from: '<S4>/SBCGlobalCfgReg' */

  /* SignalConversion generated from: '<S4>/SBCStatusReg' */
  SBCStatusReg = rtb_sbcStatusReg;

  /* Chart: '<S4>/Parse_Frame' incorporates:
   *  SubSystem: '<S4>/fc_SelMode'
   */
  /* SignalConversion generated from: '<S4>/StSBCMode' incorporates:
   *  Chart: '<S12>/Calc_Mode'
   *  Inport: '<Root>/SBCGlobalStatusReg'
   *
   * Block description for '<Root>/SBCGlobalStatusReg':
   *  Data sorted and parsed
   */
  StSBCMode = (uint8_T)asr_s32((int32_T)(((int32_T)SBCGlobalStatusReg[(((uint8_T)
    M_S_CTRL_ID))]) & 0xE000), 14U);

  /* user code (Output function Trailer for TID1) */

  /* System '<S2>/fc_Bkg' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Start for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Prs_fc_Init_Start(void)
{
  /* Start for Constant: '<S5>/ID_VER_TLE9278BQX_PRS_DEF' */
  IdVer_TLE9278BQX_Prs = ID_VER_TLE9278BQX_PRS_DEF;
}

/* Output and update for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Prs_fc_Init(void)
{
  int32_T i;

  {
    /* user code (Output function Header for TID2) */

    /* System '<S2>/fc_Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    TLE9278BQX_Prs_initialize();

    /* Constant: '<S5>/ID_VER_TLE9278BQX_PRS_DEF' */
    IdVer_TLE9278BQX_Prs = ID_VER_TLE9278BQX_PRS_DEF;

    /* Constant: '<S5>/ZERO' */
    StSBCMode = 0U;

    /* Constant: '<S5>/RESET_GLOBAL_REG' */
    for (i = 0; i < 25; i++) {
      SBCGlobalCfgReg[(i)] = RESET_GLOBAL_REG[(i)];
    }

    /* End of Constant: '<S5>/RESET_GLOBAL_REG' */

    /* Constant: '<S5>/RESET_STATUS_REG' */
    SBCStatusReg = 0U;

    /* Constant: '<S5>/ZERO1' */
    FlgSBCResend = 0U;

    /* Constant: '<S5>/ZERO2' */
    CntSBCFrmChg = 0U;

    /* user code (Output function Trailer for TID2) */

    /* System '<S2>/fc_Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Model step function */
void TLE9278BQX_Prs_Bkg(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' incorporates:
   *  SubSystem: '<S2>/fc_Bkg'
   */
  TLE9278BQX_Prs_fc_Bkg();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' */
}

/* Model step function */
void TLE9278BQX_Prs_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_Prs_fc_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model initialize function */
void TLE9278BQX_Prs_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_Prs_fc_Init_Start();

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_TLE9278BQX_PRS_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/