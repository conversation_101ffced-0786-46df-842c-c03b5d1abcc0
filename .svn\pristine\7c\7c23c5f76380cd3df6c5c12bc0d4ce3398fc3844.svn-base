#!gbuild
[Subproject]

	-I ..\tree\BIOS\GTM\include
	-I ..\tree\BIOS\GTM\cfg
	--misra_2004=-8.1,-12.7,-19.4,-19.13
BIOS\ADC\Adc.c
	--misra_2004=-14.1,-14.6
BIOS\ADC\Adc_events.c
BIOS\ADC\Adc_test.c
BIOS\DIGIO\Digio.c
BIOS\DSPI\dspi.c
	-farcalls
	--misra_2004=-10.5,-14.1
BIOS\DSPI\dspi_test.c
BIOS\CLOCK\clock.c
	--misra_2004=-10.1
BIOS\PORT\port.c
BIOS\PORT\IGNLoadTest.c
	--misra_2004=-2.1
BIOS\PORT\IGNLoadTest_Calib.c
BIOS\DMA\dma.c
	--misra_2004=-10.1,-11.4-11.5,-12.9,-17.4
BIOS\DMA\dma_events.c
	-farcalls
BIOS\EE\ee.c
	--misra_2004=-17.4
BIOS\FLASH\flashtest.c
BIOS\FLASH\Flash.c
	-farcalls
BIOS\FLASH\flash_asynch.c
	-farcalls
BIOS\FLASH\Flash_asynchCbk.c
BIOS\FLASH\flasherase.c
BIOS\FLASH\checksum.c
BIOS\FLASH\flashsuspend.c
BIOS\FLASH\flashinit.c
BIOS\FLASH\flashcheckstatus.c
BIOS\FLASH\setlock.c
BIOS\FLASH\flashresume.c
BIOS\FLASH\blankcheck.c
BIOS\FLASH\flashprogram.c
BIOS\FLASH\programverify.c
BIOS\FLASH\getlock.c
BIOS\MCAN\Mcan.c
	--misra_2004=-10.1,-10.5,-11.4,-12.1,-12.6,-17.4
BIOS\MCAN\Mcan_events.c
BIOS\MCAN\Mcan_test.c
BIOS\TTCAN\TTcan.c
	--misra_2004=-10.1,-10.5,-11.4,-12.1,-12.6,-17.4
BIOS\TTCAN\TTcan_events.c
BIOS\TTCAN\TTcan_test.c
BIOS\UTILS\utils.c
BIOS\PIT\Pit.c
BIOS\PIT\Pit_events.c
BIOS\STARTUP\app_checkVersion.c
	--misra_2004=-10.1,-11.4
BIOS\STARTUP\app_tag.c
BIOS\STARTUP\calib_checkVersion.c
BIOS\STARTUP\calib_tag.c
BIOS\STARTUP\get_app_startup.c
BIOS\STARTUP\ivor_c0.c
BIOS\STARTUP\IVOR_c0_handlers_GHS.s
BIOS\STARTUP\ivor_c2.c
	-Onone
BIOS\STARTUP\IVOR_c2_handlers_GHS.s
BIOS\STARTUP\mpc5500_user_init.c
BIOS\STARTUP\entrypoint.c
	--misra_2004=-10.5,-12.8
BIOS\STARTUP\mpc5500_asmcfg_mmu_GHS.s
BIOS\STARTUP\recovery.c
	--misra_2004=-11.4,-12.4,-14.7,-17.4
BIOS\STARTUP\recovery_Ivor2_test.c
	-Onone
BIOS\STARTUP\__start_z4_GHS.s
BIOS\STM\stm.c
BIOS\STM\stm_events.c
BIOS\SYS\src\GTM_HostInterface.c
BIOS\SYS\src\sys.c
	--misra_2004=-10.1
BIOS\TASK\task.c
BIOS\TASK\Task_Isr_PriTable.c
BIOS\TASK\Task_Isr_VecTable_c0.c
	--misra_2004=-16.9
BIOS\TASK\Task_Isr_VecTable_c2.c
	--misra_2004=-16.9
BIOS\TASK\TasksDefs.c
	--misra_2004=-12.5-12.6,-16.9,-19.11
BIOS\UTILS\timing.c
BIOS\UTILS\timing_calib.c
BIOS\VSRAM\vsram.c                           #MISRA 17.4 violation with pointers
	--misra_2004=-17.4
