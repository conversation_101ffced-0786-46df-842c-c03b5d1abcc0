/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Tpe
**  Filename        :  tpe.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  MocciA
******************************************************************************/

#ifdef _BUILD_TPE_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "../include/tpe.h"


#pragma ghs startnomisra

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/

/// TPE reception context struct pointer
tpeRx_t * T_DataIND_ptr = NULL;
/// TPE transmission context struct pointer
tpeTx_t * T_DataREQ_ptr = NULL;

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/// Global TPE communication status
static uint8_T tpeCommStatus;
/// Global Struct for TPE reception context
static tpeRx_t tpeRx;
/// Global Struct for TPE transmission context
static tpeTx_t tpeTx;
/// Transport Layer generic error
static tpeError_T tpeError = TPE_OK;
/// Received message dlc
static uint8_T tpeMsgDlc = 0u;
/// TPE CAN buffer for transmission
static uint8_T tpeTxBufIdx = TPE_CAN_TXBUF;
/// Flag for broadcast messages signalling
static tpeReqType_T tpeReqType = PHYSICAL;
/// Functional request disable timer, it is set once a physical request is received
static uint16_T tpeFuncReqDisTimer = 0u;
/// Functional request disable timer Enable Flag, set high once a physical request is received
static uint8_T tpeFlgFuncReqDisEn = 0u;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : tpeInit
**
**   Description:
**    This method applies TPE first configuration, both internal variables initialization
**    and exception to be execute once packet has been received
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**     associated config file.
**
**   EA GUID:
******************************************************************************/
void tpeInit (void)
{
uint16_T i;
struct CANBuff_T * iptr;
#ifdef USE_FUNCTIONAL_ADDRESS
struct CANBuff_T * iptr1;
#endif


    /* TPE global configuration status */
    tpeCommStatus = TPE_OFF;

    /* Rx Mngr Initialization */
    tpeRx.status = TPE_IDLE;
    tpeRx.tpdu_len = 0;
    for (i=0U; i<TPE_TPDU_RXBUFF_SIZE; i++)
    {
        tpeRx.tpdu_data[i] = 0U;
    }

#ifdef ISO_TP_MIXED_MODE
    tpeRx.FC_data[0U] = TPE_TPDU_SA;
    tpeRx.FC_data[1U] = TPE_TPDU_TPCI_FC;
    tpeRx.FC_data[2U] = TPE_TPDU_FC_BS_MIN;
    tpeRx.FC_data[3U] = TPE_TPDU_FC_ST_MIN;
    tpeRx.FC_data[4U] = 0U;
    tpeRx.FC_data[5U] = 0U;
    tpeRx.FC_data[6U] = 0U;
    tpeRx.FC_data[7U] = 0U;
#else
    tpeRx.FC_data[0U] = TPE_TPDU_TPCI_FC;
    tpeRx.FC_data[1U] = TPE_TPDU_FC_BS_MIN;
    tpeRx.FC_data[2U] = TPE_TPDU_FC_ST_MIN;
    tpeRx.FC_data[3U] = 0U;
    tpeRx.FC_data[4U] = 0U;
    tpeRx.FC_data[5U] = 0U;
    tpeRx.FC_data[6U] = 0U;
    tpeRx.FC_data[7U] = 0U;
#endif

    tpeRx.num_data_rcv = 0U;
    tpeRx.next_seg = 0U;
    tpeRx.seg_counter = 0U;
    tpeRx.rx_buf = 0U;
    tpeRx.CR_timer = 0U;

    // Tx Mngr Initialization
    tpeTx.status = TPE_IDLE;
    tpeTx.num_seg_snd = 0U;
    tpeTx.block_size = 0U;

    tpeTx.ST_timer = 0U;
    tpeTx.ST_timeout = 0U;
    tpeTx.CN_timer = 0U;
    tpeTx.FC_timer = 0U;
    tpeTx.seg_counter = 0U;
    tpeTx.next_seg = 0U;

    tpeTx.tpdu_len = 0;
    for (i=0U; i<TPE_TPDU_TXBUFF_SIZE; i++)
    {
        tpeTx.tpdu_data[i] = 0U;
    }
    tpeTx.error = TPE_SUCCESS;

    T_DataIND_ptr = &tpeRx;
    T_DataREQ_ptr = &tpeTx;

#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
    CAN_EX_Tx_Rx_Config(TPE_CAN ,(void(*)(void))tpeTxDataException,(void(*)(void))tpeRxDataException);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
    TTCAN_EX_Tx_Rx_Config(TPE_CAN ,(void(*)(void))tpeTxDataException,(void(*)(void))tpeRxDataException);
#else
#error TPE peripheral not defined
#endif

    /* If there are pending diagnostic requests on buffer while booting,
     * synchronization is lost between R/W pointers in the CAN buffer
     * this patch corrects this error
     */
#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
    /*res = (int8_T)*/CAN_RxData(TPE_CAN,TPE_CAN_RXBUF,&iptr);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
    /*res = (int8_T)*/TTCAN_RxData(TPE_CAN,TPE_CAN_RXBUF,&iptr);
#else
#error TPE peripheral not defined
#endif

#ifdef USE_FUNCTIONAL_ADDRESS
#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
    CAN_RxData(TPE_CAN,TPE_CAN_RXBROADCASTBUF,&iptr1);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
    TTCAN_RxData(TPE_CAN,TPE_CAN_RXBROADCASTBUF,&iptr1);
#else
#error TPE peripheral not defined
#endif
#endif
}

/******************************************************************************
**   Function    : tpeReceive
**
**   Description:
**    This method manages incoming protocol data units (PDUs)
**
**   Parameters :
**    [out] uint8_T * data, pointer to the received data container
**    [out] uint16_T * data_len, number of bytes of the diagnostic request
**
**   Returns:
**    TPE_SUCCESS, no error occurred
**    TPE_NOTSUCCESS, one (or more) error(s) occurred
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**     associated config file.
**
**   EA GUID:
******************************************************************************/
uint8_T tpeReceive (uint8_T *data, uint16_T *data_len)
{
    uint16_T i;
    uint8_T res = TPE_SUCCESS;

    if (tpeRx.tpdu_len != 0u)
    {
        if( (tpeRx.rx_buf & DIAGNOSE_REQUEST) && (tpeRx.status != TPE_RECEIVING) )
        {
            for (i=0U; i<tpeRx.tpdu_len; i++)
            {
                data[i] = tpeRx.tpdu_data[i];
            }

            *data_len = tpeRx.tpdu_len;
            tpeRx.rx_buf &= ~DIAGNOSE_REQUEST;
        }
        else
        {
            res = TPE_NOTSUCCESS;
        }
    }
    else
    {
        res = TPE_NOTSUCCESS;
    }

    return res;
}

/******************************************************************************
**   Function    : tpeSend
**
**   Description:
**    This method manages outcoming protocol data units (PDUs)
**
**   Parameters :
**    [in] uint8_T * data, pointer to the received data container
**    [in] uint16_T * data_len, number of bytes of the diagnostic request
**
**   Returns:
**    TPE_SUCCESS, no error occurred
**    TPE_NOTSUCCESS, one (or more) error(s) occurred
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**     associated config file.
**
**   EA GUID:
******************************************************************************/
uint8_T tpeSend (uint8_T *data, uint16_T data_len)
{
    uint8_T res = TPE_SUCCESS;
    uint8_T i;

    if(tpeTx.status == TPE_IDLE)
    {
#if 0
        for (i=0U; i<TPE_TPDU_TXBUFF_SIZE; i++)
        {
            tpeTx.tpdu_data[i] = 0U;
        }

        for (i=0U; i<data_len; i++)
        {
            tpeTx.tpdu_data[i] = data[i];
        }

        tpeTx.tpdu_len = data_len;
#endif
        tpeTx.error = TPE_PENDING;
        tpeTxMngr();

        res = tpeTx.error;
    }
    else
    {
        res = TPE_NOTSUCCESS;
    }

    return res;
}

/******************************************************************************
**   Function    : tpeSendStatus
**
**   Description:
**    This method returns outcoming transmission status 
**
**   Parameters :
**    void
**
**   Returns:
**    tpeTx.error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file.
**
**   EA GUID:
******************************************************************************/
uint8_T tpeSendStatus(void)
{
    return tpeTx.error;
}

/******************************************************************************
**   Function    : tpeReceiveStatus
**
**   Description:
**    This method returns incoming reception status 
**
**   Parameters :
**    void
**
**   Returns:
**    actual receive/reception status, DIAGNOSE_REQUEST -> request has been fully 
**    decoded and could be processed by higher levels (Layer 5/DiagCanMgm and Layer 7/
**    DiagCanMgm_<customer>)
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file.
**
**   EA GUID:
******************************************************************************/
uint8_T tpeReceiveStatus(void)
{
    // 1=>data is present, 0=>data is absent
    return (tpeRx.rx_buf & DIAGNOSE_REQUEST);
}

/******************************************************************************
**   Function    : tpeSetCommStatus
**
**   Description:
**    This method sets global Transport Protocol status 
**
**   Parameters :
**    [in] uint8_T status, TPE status (ON/OFF)
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file.
**
**   EA GUID:
******************************************************************************/
void tpeSetCommStatus(uint8_T status)
{
    tpeCommStatus = status;
}

/******************************************************************************
**   Function    : tpeGetCommStatus
**
**   Description:
**    This method returns global Transport Protocol status 
**
**   Parameters :
**    void
**
**   Returns:
**    status, TPE status (ON/OFF)
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file.
**
**   EA GUID:
******************************************************************************/
uint8_T tpeGetCommStatus(void)
{
    return(tpeCommStatus);
}

/******************************************************************************
**   Function    : tpeTickTimer
**
**   Description:
**    This method updates internal timers and timeouts; function shall be called periodically in order to 
**    refresh tpe timeouts
**
**   Parameters :
**    void
**
**   Returns:
**     void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**     associated config file.
**
**   EA GUID:
******************************************************************************/
void tpeTickTimer(void)
{
    /* TPDU shall be sent according to STmin received with the FC from the tester */
    if( (tpeTx.status != TPE_IDLE) && (tpeTx.status != TPE_WAIT_CONNECT) )
    {
        if(tpeTx.ST_timer < tpeTx.ST_timeout)
        {
            /* refreshing ST_timer */
            tpeTx.ST_timer++;
        }
        else
        {
            /* sending consecutive frames */
            tpeTxMngr();
            /* resetting Separation Time internal timer for next transmission */
            tpeTx.ST_timer = 0U;
        }
    }

    /* Event S8 Connection Timeout expired since FC TPDU not received in time */
    if(tpeTx.status == TPE_WAIT_CONNECT)
    {
        if(tpeTx.CN_timer > 0U)
        {
            /* refreshing CN_timer */
            tpeTx.CN_timer--;
        }
        else
        {
            /* Set Global error */
            tpeError = TPE_SERVER_CONNECTION_TIMEOUT;
            /* reset TPE */
            tpeReInit();
        }
    }

    /* Event S17 Flow Control expired timeout */
    if(tpeTx.status == TPE_WAIT_FLOWCONTROL)
    {
        if((tpeTx.FC_timer > 0U))
        {
            /* refreshing FC_timer */
            tpeTx.FC_timer--;
        }
        else
        {
            /* Set Global error */
            tpeError = TPE_FC_TIMEOUT;
            /* reset TPE */
            tpeReInit();
        }
   }

    /* TPDU shall be received according to N_Cr_TIMEOUT defined by the customer */
    if(tpeRx.status == TPE_RECEIVING)
    {
        if (tpeRx.CR_timer > 0U)
        {
            /* refreshing CR_timer */
            tpeRx.CR_timer --;
        }
        else
        {
            /* Set Global error */
            tpeError = TPE_CF_TIMEOUT;
            /* reset TPE */
            tpeReInit();
        }
    }

    if(tpeFuncReqDisTimer > 0u) // functional disable timer activated after physical request (SF or FF) reception
    {
        /* refreshing Functional Request Disable Timer */
        tpeFuncReqDisTimer--;

        if(tpeFuncReqDisTimer == 0u)
        {
            /* Reset Functional Request Disable Timer flag*/
            tpeFlgFuncReqDisEn = 0u;
            /* Re enable functional Request Rx from Link Layer */
#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
            CAN_EnableReceive(TPE_CAN,TPE_CAN_RXBROADCASTBUF, CAN_XTD);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
        CAN_EnableReceive(TPE_CAN,TPE_CAN_RXBROADCASTBUF, TTCAN_XTD);
#else
#error TPE peripheral not defined
#endif
        }
    }
}

/******************************************************************************
**   Function    : tpeGetReqType
**
**   Description:
**    This method gets received request type flag
**
**   Parameters :
**    void
**
**   Returns:
**    tpeReqType_T 0-> PHYSICAL 1 -> FUNCTIONAL
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file.
**
**   EA GUID:
******************************************************************************/
tpeReqType_T tpeGetReqType(void)
{
    return(tpeReqType);
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : tpeReInit
**
**   Description:
**    This method tpeInit non-recursive function, called in order to reinitialize transfer protocol unit
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file.
**
**   EA GUID:
******************************************************************************/
static void tpeReInit(void)
{
uint16_T i;

    // Rx Mngr Initialization
    tpeRx.status = TPE_IDLE;
    tpeRx.tpdu_len = 0;
    for (i=0U; i<TPE_TPDU_RXBUFF_SIZE; i++)
    {
        tpeRx.tpdu_data[i] = 0U;
    }

#ifdef ISO_TP_MIXED_MODE
    tpeRx.FC_data[0U] = TPE_TPDU_SA;
    tpeRx.FC_data[1U] = TPE_TPDU_TPCI_FC;
    tpeRx.FC_data[2U] = TPE_TPDU_FC_BS_MIN;
    tpeRx.FC_data[3U] = TPE_TPDU_FC_ST_MIN;
    tpeRx.FC_data[4U] = 0U;
    tpeRx.FC_data[5U] = 0U;
    tpeRx.FC_data[6U] = 0U;
    tpeRx.FC_data[7U] = 0U;
#else
    tpeRx.FC_data[0U] = TPE_TPDU_TPCI_FC;
    tpeRx.FC_data[1U] = TPE_TPDU_FC_BS_MIN;
    tpeRx.FC_data[2U] = TPE_TPDU_FC_ST_MIN;
    tpeRx.FC_data[3U] = 0U;
    tpeRx.FC_data[4U] = 0U;
    tpeRx.FC_data[5U] = 0U;
    tpeRx.FC_data[6U] = 0U;
    tpeRx.FC_data[7U] = 0U;
#endif

    tpeRx.num_data_rcv = 0U;
    tpeRx.next_seg = 0U;
    tpeRx.seg_counter = 0U;
    tpeRx.rx_buf = 0U;
    tpeRx.CR_timer = 0U;


    // Tx Mngr Initialization
    tpeTx.status = TPE_IDLE;
    tpeTx.num_seg_snd = 0U;
    tpeTx.block_size = 0U;

    tpeTx.ST_timer = 0U;
    tpeTx.ST_timeout = 0U;
    tpeTx.CN_timer = 0U;
    tpeTx.FC_timer = 0U;
    tpeTx.seg_counter = 0U;
    tpeTx.next_seg = 0U;

    tpeTx.tpdu_len = 0;
    for (i=0U; i<TPE_TPDU_TXBUFF_SIZE; i++)
    {
        tpeTx.tpdu_data[i] = 0U;
    }
    tpeTx.error = TPE_SUCCESS;

    /* Reset TPE error flags */
    tpeError = TPE_OK;

    return;
}

/******************************************************************************
**   Function    : tpeRxMngr
**
**   Description:
**    This method performs incoming data reassembling
**
**   Parameters :
**    [in] uint8_T * tpdu, pointer to received packet
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file. For implementation details, please refer to Fiat Norm 07274_E_Ed4, 
**    Appendix A, chapter A.1 "Receiver Transport Protocol Entity"
**
**   EA GUID:
******************************************************************************/
static void tpeRxMngr(uint8_T *tpdu)
{
uint16_T i;

    if (tpeGetReqType() == PHYSICAL)
    {
        /* in case of physical request received (both SFs and FFs) we disable functional CAN buffer in order to process only unicast messages; it will be re-enable after Functional Request Disable Timer timeout */
#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
        CAN_DisableReceive(TPE_CAN,TPE_CAN_RXBROADCASTBUF, CAN_XTD);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
        TTCAN_DisableReceive(TPE_CAN,TPE_CAN_RXBROADCASTBUF, TTCAN_XTD);
#else
#error TPE peripheral not defined
#endif
        if(tpeFlgFuncReqDisEn == 0u)
        {
            /* Functional Request Disable Timer Enable Flag */
            tpeFlgFuncReqDisEn = 1u;
            /* Set timer */
            tpeFuncReqDisTimer = TPE_FUNC_REQ_DIS_TIMEOUT;
        }
    }

    switch (tpeRx.status)
    {
        case TPE_IDLE:
            if (((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_SF_MASK) == 0U) && ((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_SF_DL_MASK ) <= TPE_DATABUFF_SIZE)) // Event R1 Single frame data transfer received
            {
                for (i=0U; i<TPE_TPDU_RXBUFF_SIZE; i++)
                {
                    tpeRx.tpdu_data[i] = 0U;   // Reset receive buffer
                }

                tpeRx.tpdu_len = (tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_SF_DL_MASK);  // ISO 15765-2, SF_DL

                if (tpeMsgDlc <= tpeRx.tpdu_len) // if Tester sends a Single frame with a CAN-DLC shorter and equal to the transport protocol data length field. ECU must not send a response
                {
                    /* Set Global error */
                    tpeError = TPE_MSG_DLC_MISMATCH;
                    /* reset TPE */
                    tpeReInit();
                }
                else
                {
                    for (i=0U; i<tpeRx.tpdu_len; i++)
                    {
                        tpeRx.tpdu_data[i] = tpdu[i+TPE_TPDU_SF_DATA_POS]; // copy data in the receive buffer
                    }

                    tpeRx.rx_buf |= DIAGNOSE_REQUEST; // Set flag diagnostic request received
                }
            }
            else if ((((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_NO_SF_MASK) == TPE_TPDU_FF_ID) /*&& ((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_FF_XDL_MASK) == 0U)*/) && (tpdu[TPE_TPDU_FF_DL_POS] <= TPE_TPDU_RXBUFF_SIZE)) // Event R2 First frame received
            {
                if (tpeGetReqType() == PHYSICAL)
                {
                    for (i=0U; i<TPE_TPDU_RXBUFF_SIZE; i++)
                    {
                        tpeRx.tpdu_data[i] = 0U;   // Reset receive buffer
                    }

                    tpeRx.tpdu_len = ((uint16_T)(tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_FF_XDL_MASK))<< 8u;
                    tpeRx.tpdu_len |= tpdu[TPE_TPDU_FF_DL_POS];  // ISO 15765-2, FF_DL

                    if (tpeRx.tpdu_len <= (TPE_DATABUFF_SIZE-1)) // error, we cannot have a FF with a length of a SF
                    {
                        /* Set Global error */
                        tpeError = TPE_FF_DL_NOT_VALID;
                        /* Reset TPE */
                        tpeReInit();
                    }
                    else
                    {
                        for (i=0U; i<(TPE_DATABUFF_SIZE-1); i++)
                        {
                            tpeRx.tpdu_data[i] = tpdu[i+TPE_TPDU_FF_DATA_POS];  // copy data in the receive buffer
                        }

                        tpeRx.num_data_rcv = TPE_DATABUFF_SIZE-1;
                        tpeRx.next_seg = 1U;
                        tpeRx.seg_counter = 1U;

                        if(tpeRx.tpdu_len > MAX_TP_MSG_SIZE)
                        {
                            tpeRx.FC_data[0] |= TPE_TPDU_FC_OF;
                            tpeRx.FC_data[1U] = 0u;
                            tpeRx.FC_data[2U] = 0u;

                            tpeSendDataOnLL (tpeRx.FC_data, TPE_FC_DLC); // Send connection response 
                            tpeError = TPE_RX_BUFFER_OVERFLOW;
                            tpeReInit();
                        }
                        else
                        {
#ifdef USE_TPE_OPTIMIZATION
                            if (tpeSendDataOnLL (tpeRx.FC_data, TPE_FC_DLC) != TPE_SUCCESS) // Send connection response 
#else
                            if (tpeSendDataOnLL (tpeRx.FC_data, TPE_DLC_ALL_BYTES) != TPE_SUCCESS) // Send connection response 
#endif
                            {
                                /* If error occurs while sending FC, TPE shall be reinit */
                                tpeReInit();
                            }
                            else
                            {
                                tpeRx.status = TPE_RECEIVING;
                                /* Start timeout for the first consecutive frame */
                                tpeRx.CR_timer = TPE_CR_COUNTER;
                            }
                        }
                    }
                }
                else
                {
                    /* Set Global error */
                    tpeError = TPE_FUNC_FF_NOT_COMPLIANT;
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else
            {
                /* unreachable state */
            }
            break;

        case TPE_RECEIVING :
            if (((((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_NO_SF_MASK) == TPE_TPDU_TPCI_CF) && ((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_CF_SN_MASK) == tpeRx.next_seg))) && (((tpeRx.tpdu_len - tpeRx.num_data_rcv) > TPE_DATABUFF_SIZE) && (tpeRx.seg_counter < TPE_TPDU_FC_BS_MAX))) // Event R3 Not last Consecutive frame received
            {
                if(tpeGetReqType() == PHYSICAL)
                {
                    for (i=0U; i<TPE_DATABUFF_SIZE; i++)
                    {
                        tpeRx.tpdu_data[i+tpeRx.num_data_rcv] = tpdu[i+TPE_TPDU_CF_DATA_POS];
                    }

                    tpeRx.num_data_rcv += TPE_DATABUFF_SIZE;
                    tpeRx.next_seg = (tpeRx.next_seg + 1U) % 16U;
                    tpeRx.seg_counter += 1U;
                    tpeRx.status = TPE_RECEIVING;
                    /* Start timeout for the first consecutive frame */
                    tpeRx.CR_timer = TPE_CR_COUNTER;
                }
                else
                {
                    /* Set Global error */
                    tpeError = TPE_FUNC_CF_NOT_COMPLIANT;
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else if ((((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_NO_SF_MASK) == TPE_TPDU_TPCI_CF) && ((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_CF_SN_MASK) == tpeRx.next_seg)) && ((tpeRx.tpdu_len - tpeRx.num_data_rcv) > TPE_DATABUFF_SIZE) && (tpeRx.seg_counter == TPE_TPDU_FC_BS_MAX)) // Event R4 Not last Consecutive frame received but last segment
            {
                if(tpeGetReqType() == PHYSICAL)
                {
                    for (i=0U; i<TPE_DATABUFF_SIZE; i++)
                    {
                        tpeRx.tpdu_data[i+tpeRx.num_data_rcv] = tpdu[i+TPE_TPDU_CF_DATA_POS];
                    }
                    tpeRx.num_data_rcv += TPE_DATABUFF_SIZE;
                    tpeRx.next_seg = (tpeRx.next_seg + 1U) % 16U;
                    tpeRx.seg_counter = 1U;

                    if (tpeRx.tpdu_len != 0)
                    {
#ifdef USE_TPE_OPTIMIZATION
                        if (tpeSendDataOnLL (tpeRx.FC_data, TPE_FC_DLC) != TPE_SUCCESS) // Send connection response 
#else
                        if (tpeSendDataOnLL (tpeRx.FC_data, TPE_DLC_ALL_BYTES) != TPE_SUCCESS) // Send connection response 
#endif
                        {
                            /* If error occurs while sending FC, TPE shall be reinit */
                            tpeReInit();
                        }
                        else
                        {
                            tpeRx.status = TPE_RECEIVING;
                            /* Start timeout for the first consecutive frame */
                            tpeRx.CR_timer = TPE_CR_COUNTER;
                        }
                    }
                }
                else
                {
                    /* Set Global error */
                    tpeError = TPE_FUNC_CF_NOT_COMPLIANT;
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else if (((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_NO_SF_MASK) == TPE_TPDU_TPCI_CF) && ((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_CF_SN_MASK) == tpeRx.next_seg) && ((tpeRx.tpdu_len - tpeRx.num_data_rcv) <= TPE_DATABUFF_SIZE)) // Event R5 Last consecutive frame received
            {
                if(tpeGetReqType() == PHYSICAL)
                {
                    if (tpeMsgDlc <= (tpeRx.tpdu_len - tpeRx.num_data_rcv)) // if Tester sends a Consecutive frame with a CAN-DLC shorter and equal to the transport protocol data length field. ECU must not send a response
                    {
                        /* Set Global error */
                        tpeError = TPE_FF_DL_NOT_VALID;
                        /* Reset TPE */
                        tpeReInit();
                    }
                    else
                    {
                        for (i=0U; i<(tpeRx.tpdu_len - tpeRx.num_data_rcv); i++)
                        {
                            tpeRx.tpdu_data[i+tpeRx.num_data_rcv] = tpdu[i+TPE_TPDU_CF_DATA_POS];
                        }

                        tpeRx.rx_buf |= DIAGNOSE_REQUEST; // Set flag diagnostic request received
                        tpeRx.status = TPE_IDLE; // Set status idle
                    }
                }
                else
                {
                    /* Set Global error */
                    tpeError = TPE_FUNC_CF_NOT_COMPLIANT;
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else if (((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_NO_SF_MASK) == TPE_TPDU_TPCI_CF) && ((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_CF_SN_MASK) != tpeRx.next_seg)) // Event R6 Incorrect Consecutive frame received
            {
                if(tpeGetReqType() == PHYSICAL)
                {
                    /* Set Global error */
                    tpeError = TPE_CF_SN_NOT_VALID;
                    /* Reset TPE */
                    tpeReInit();
                }
                else
                {
                    /* Set Global error */
                    tpeError = TPE_FUNC_CF_NOT_COMPLIANT;
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else if (((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_SF_MASK) == 0U) && ((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_SF_DL_MASK) <= TPE_DATABUFF_SIZE)) // Event R7 Single frame received event: abort current transfer
            {
                for (i=0U; i<TPE_TPDU_RXBUFF_SIZE; i++) // Reset received buffer
                {
                    tpeRx.tpdu_data[i] = 0U;
                }
                tpeRx.tpdu_len = (tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_SF_DL_MASK); // ISO 15765-2 SF_DL
                for (i=0U; i<tpeRx.tpdu_len; i++)
                {
                    tpeRx.tpdu_data[i] = tpdu[i+TPE_TPDU_SF_DATA_POS];
                }

                tpeRx.rx_buf |= DIAGNOSE_REQUEST; // Set diag request received
                tpeRx.status = TPE_IDLE; // Set status idle
            }
            else if (((((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_NO_SF_MASK) == TPE_TPDU_FF_ID) /*&& ((tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_FF_XDL_MASK) == 0U)*/)) && (tpdu[TPE_TPDU_FF_DL_POS] <= TPE_TPDU_RXBUFF_SIZE)) // Event R8 First frame received event: abort current transfer
            {
                if(tpeGetReqType() == PHYSICAL)
                {
                    for (i=0U; i<TPE_TPDU_RXBUFF_SIZE; i++)
                    {
                        tpeRx.tpdu_data[i] = 0U; // Reset received buffer
                    }

                    tpeRx.tpdu_len = ((uint16_T)(tpdu[TPE_TPDU_TPCI_POS] & TPE_TPDU_FF_XDL_MASK))<< 8u;
                    tpeRx.tpdu_len |= tpdu[TPE_TPDU_FF_DL_POS];  // ISO 15765-2 FF_DL

                    if (tpeRx.tpdu_len <= (TPE_DATABUFF_SIZE-1)) // error, we cannot have a FF with a length of a SF
                    {
                        /* Set Global error */
                        tpeError = TPE_FF_DL_NOT_VALID;
                        /* Reset TPE */
                        tpeReInit();
                    }
                    else
                    {
                        for (i=0U; i<(TPE_DATABUFF_SIZE-1); i++)
                        {
                            tpeRx.tpdu_data[i] = tpdu[i+TPE_TPDU_FF_DATA_POS];
                        }

                        tpeRx.num_data_rcv = TPE_DATABUFF_SIZE-1;
                        tpeRx.next_seg = 1U;
                        tpeRx.seg_counter = 1U;

                        if (tpeRx.tpdu_len != 0)
                        {
#ifdef USE_TPE_OPTIMIZATION
                            if (tpeSendDataOnLL (tpeRx.FC_data, TPE_FC_DLC) != TPE_SUCCESS) // Send connection response 
#else
                            if (tpeSendDataOnLL (tpeRx.FC_data, TPE_DLC_ALL_BYTES) != TPE_SUCCESS) // Send connection response 
#endif
                            {
                                /* If error occurs while sending FC, TPE shall be reinit */
                                tpeReInit();
                            }
                            else
                            {
                                tpeRx.status = TPE_RECEIVING;
                                /* Init CR_timer for the first consecutive frame */
                                tpeRx.CR_timer = TPE_CR_COUNTER;
                            }
                        }
                    }
                }
                else
                {
                    /* Set Global error */
                    tpeError = TPE_FUNC_FF_NOT_COMPLIANT;
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else
            {
                /* unreachable branch */
            }
            break;

        default:
            break;
    }
}

/******************************************************************************
**   Function    : tpeTxMngr
**
**   Description:
**    This method performs outcoming data packetization
**
**   Parameters :
**    void
**
**   Returns:
**    uint8_T tpeTx.error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file. For implementation details, please refer to Fiat Norm 07274_E_Ed4, 
**    Appendix A, chapter A.2 "Sender Transport Protocol Entity"
**
**   EA GUID:
******************************************************************************/
static uint8_T tpeTxMngr(void)
{
uint8_T i;
uint8_T tpdu[TPE_CANBUFF_SIZE] = {0U,0U,0U,0U,0U,0U,0U,0U};


    switch (tpeTx.status)
    {
        case TPE_IDLE:
        {
            if (tpeTx.tpdu_len <= TPE_DATABUFF_SIZE) // Event S1 Single Frame data transfer
            {
#ifdef ISO_TP_MIXED_MODE
                tpdu[TPE_TPDU_TA_POS] = TPE_TPDU_SA;
#endif
                tpdu[TPE_TPDU_TPCI_POS] = TPE_TPDU_SF_ID|tpeTx.tpdu_len;
                for (i=0U; i<tpeTx.tpdu_len; i++)
                {
                    tpdu[i+TPE_TPDU_SF_DATA_POS] = tpeTx.tpdu_data[i];
                }

                /* TPE_WAIT_IDLE sub-state */
#ifdef USE_TPE_OPTIMIZATION
                if (tpeSendDataOnLL(tpdu, tpeTx.tpdu_len + 1u) == TPE_SUCCESS)
#else
                if (tpeSendDataOnLL(tpdu, TPE_DLC_ALL_BYTES) == TPE_SUCCESS)
#endif
                {
                    /* Event S2 D_Data.Conf(Success), message correctly sent on the Link Layer :) */
                    tpeTx.error = TPE_SUCCESS;
                }
                else
                {
                    /* Event S3 D_Data.Conf(NotSuccess) */
                    tpeReInit();
                }

                /* next state */
                tpeTx.status = TPE_IDLE;

            }
            else if (tpeTx.tpdu_len > TPE_DATABUFF_SIZE) // Event S4 T_Data.Conf received - First Frame
            {
#ifdef ISO_TP_MIXED_MODE
                tpdu[TPE_TPDU_TA_POS] = TPE_TPDU_SA;
#endif
                tpdu[TPE_TPDU_TPCI_POS] = TPE_TPDU_FF_ID| ((uint8_T)((tpeTx.tpdu_len >> 8u) /*&& TPE_TPDU_TPCI_XDL*/)); //XDL (eXtendedDataLength) field for message size bigger than 255 bytes
                tpdu[TPE_TPDU_FF_DL_POS] = tpeTx.tpdu_len;
                for (i = 0U; i < (TPE_CANBUFF_SIZE - TPE_TPDU_FF_DATA_POS); i++)
                {
                    tpdu[i+TPE_TPDU_FF_DATA_POS] = tpeTx.tpdu_data[i];
                }

                /* TPE_WAIT_WAIT_CONNECT sub-state */
                if (tpeSendDataOnLL(tpdu, TPE_DLC_ALL_BYTES) == TPE_SUCCESS)
                {
                    /* Event S5 D_Data.Conf(Success) */
                    tpeTx.num_seg_snd = TPE_DATABUFF_SIZE-1;
                    tpeRx.rx_buf &= ~DIAGNOSE_REQUEST_FC;
                    tpeTx.CN_timer = TPE_CN_COUNTER;
                    tpeTx.status = TPE_WAIT_CONNECT;
                }
                else
                {
                    /* Event S6 D_Data.Conf(NotSuccess) */
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else
            {
                /* Unreachable branch */
            }
        }
        break;

        case TPE_WAIT_CONNECT :
        {
            if (tpeRx.rx_buf & DIAGNOSE_REQUEST_FC) // Event S7 Connect FC TPDU received
            {
                tpeRx.rx_buf &= ~DIAGNOSE_REQUEST_FC;

                tpeTx.seg_counter = 1U;
                tpeTx.next_seg = 1U;
                tpeTx.status = TPE_SEND_BLOCK;
             }
        }
        break;

        case TPE_SEND_BLOCK :
        {
            if (((tpeTx.tpdu_len - tpeTx.num_seg_snd) > TPE_DATABUFF_SIZE) && ((tpeTx.seg_counter < tpeTx.block_size))) // Event S9 Not last segment to send in message, and not last segment to send in block
            {
#ifdef ISO_TP_MIXED_MODE
                tpdu[TPE_TPDU_TA_POS] = TPE_TPDU_SA;
#endif
                tpdu[TPE_TPDU_TPCI_POS] = TPE_TPDU_TPCI_CF | tpeTx.next_seg;
                for (i=0U; i<TPE_DATABUFF_SIZE; i++)
                {
                    tpdu[i+TPE_TPDU_CF_DATA_POS] = tpeTx.tpdu_data[tpeTx.num_seg_snd + i];
                }

                /* TPE_WAIT_SEND_BLOCK sub-state */
                if (tpeSendDataOnLL(tpdu, TPE_DLC_ALL_BYTES) == TPE_SUCCESS)
                {
                    /* Event S10 D_Data.Conf(Success) */
                    tpeTx.num_seg_snd += TPE_DATABUFF_SIZE;
                    tpeTx.seg_counter +=1U;
                    tpeTx.next_seg = (tpeTx.next_seg + 1U) % 16U;
                    tpeTx.status = TPE_SEND_BLOCK;
                }
                else
                {
                    /* Event S11 D_Data.Conf(NotSuccess) */
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else if (((tpeTx.tpdu_len - tpeTx.num_seg_snd) > TPE_DATABUFF_SIZE) && tpeTx.seg_counter == tpeTx.block_size) // Event S12 Not last segment to send in message, but last segment to send in block 
            {
#ifdef ISO_TP_MIXED_MODE
                tpdu[TPE_TPDU_TA_POS] = TPE_TPDU_SA;
#endif
                tpdu[TPE_TPDU_TPCI_POS] = TPE_TPDU_TPCI_CF | tpeTx.next_seg;
                for(i=0U; i<TPE_DATABUFF_SIZE; i++)
                {
                    tpdu[i+TPE_TPDU_CF_DATA_POS] = tpeTx.tpdu_data[tpeTx.num_seg_snd + i];
                }

                /* TPE_WAIT_WAIT_FLOW_CONTROL sub-state */
                if (tpeSendDataOnLL(tpdu, TPE_DLC_ALL_BYTES) == TPE_SUCCESS)
                {
                    /* Event S13 D_Data.Conf(Success) */
                    tpeTx.num_seg_snd += TPE_DATABUFF_SIZE;
                    tpeTx.FC_timer = TPE_FC_COUNTER;
                    tpeTx.next_seg = (tpeTx.next_seg + 1U) % 16U;
                    tpeTx.status = TPE_WAIT_FLOWCONTROL;
                }
                else
                {
                    /* Event S14 D_Data.Conf(NotSuccess) */
                    /* Reset TPE */
                    tpeReInit();
                }
            }
            else if (((tpeTx.tpdu_len - tpeTx.num_seg_snd) <= TPE_DATABUFF_SIZE)) // Event S15 Last segment to send in message
            {
#ifdef ISO_TP_MIXED_MODE
                tpdu[TPE_TPDU_TA_POS] = TPE_TPDU_SA;
#endif
                tpdu[TPE_TPDU_TPCI_POS] = TPE_TPDU_TPCI_CF | tpeTx.next_seg;
                for (i=0U; i<(tpeTx.tpdu_len - tpeTx.num_seg_snd); i++)
                {
                    tpdu[i+TPE_TPDU_CF_DATA_POS] = tpeTx.tpdu_data[tpeTx.num_seg_snd + i];
                }

                /* TPE_WAIT_IDLE sub-state*/
#ifdef USE_TPE_OPTIMIZATION
                if (tpeSendDataOnLL(tpdu, (tpeTx.tpdu_len - tpeTx.num_seg_snd) + 1u)== TPE_SUCCESS)
#else
                if (tpeSendDataOnLL(tpdu, TPE_DLC_ALL_BYTES) == TPE_SUCCESS)
#endif
                {
                    /* Event S2 D_Data.Conf(Success), message correctly sent on the Link Layer :) */
                    tpeTx.error = TPE_SUCCESS;
                }
                else
                {
                    /* Event S3 D_Data.Conf(NotSuccess) */
                    tpeReInit();
                }
                /* Next State */
                tpeTx.status = TPE_IDLE;
            }
            else
            {
                /* Unreachable branch */
            }
        }
        break;

        case TPE_WAIT_FLOWCONTROL:
        {
            if (tpeRx.rx_buf & DIAGNOSE_REQUEST_FC) // Event S16 Flow Control received
            {
                tpeRx.rx_buf &= ~DIAGNOSE_REQUEST_FC;
                tpeTx.seg_counter = 1U;
                tpeTx.status = TPE_SEND_BLOCK;
                break;
            }
        }
        break;

    default:
        break;

    }

    return tpeTx.error;
}

/******************************************************************************
**   Function    : tpeSendDataOnLL
**
**   Description:
**    This method forwards data to lower physical layer interfaces
**
**   Parameters :
**    [in] uint8_T * data, pointer to data to be sent
**    [in] uint8_T data_length, number of bytes to be transmitted
**
**   Returns:
**    uint8_T error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**     associated config file.
**
**   EA GUID:
******************************************************************************/
static uint8_T tpeSendDataOnLL(uint8_T* data, uint8_T data_length)
{
uint8_T i = 0;
int16_T res = 0;
    
    do
    {
#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
        res = CAN_GetTxBufferStatus(TPE_CAN, tpeTxBufIdx);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
        res = TTCAN_GetTxBufferStatus(TPE_CAN, tpeTxBufIdx);
#else
#error TPE peripheral not defined
#endif
        i++;
        UTILS_nop();
    } while ((res == CAN_TX_BUSY) && (i/*++*/ <= TPE_CAN_TX_MAX_TRIAL) );

    if(tpeError == TPE_OK)
    {
#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
        res = CAN_TxDataOptimized(TPE_CAN, tpeTxBufIdx, data, data_length);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
        res = TTCAN_TxDataOptimized(TPE_CAN, tpeTxBufIdx, data, data_length);
#else
#error TPE peripheral not defined
#endif
    }
    return res;
}

/******************************************************************************
**   Function    : tpeReceiveDataFromLL
**
**   Description:
**    This method retrieves data from lower physical layer interfaces
**
**   Parameters :
**    [out] uint8_T * data, pointer to data to be sent
**
**   Returns:
**    uint8_T error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**     associated config file.
**
**   EA GUID:
******************************************************************************/
static uint8_T tpeReceiveDataFromLL(uint8_T* data)
{
uint8_T res;
int8_T   i;
struct CANBuff_T * iptr = NULL;
struct CANBuff_T * iptr1 = NULL;

#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
    res = (int8_T)CAN_RxData(TPE_CAN,TPE_CAN_RXBROADCASTBUF,&iptr1);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
    res = (int8_T)TTCAN_RxData(TPE_CAN,TPE_CAN_RXBROADCASTBUF,&iptr1);
#else
#error TPE peripheral not defined
#endif


    if (res == NO_ERROR) //a broadcast message has been received
    {
        /* Get CAN message DLC */
        tpeMsgDlc = iptr1->dlc;

        /* Set Functional Request Rx flag */
        tpeSetReqType(FUNCTIONAL);

        /* Reset global Error */
        tpeError = TPE_OK;

        /* Copy data from CAN MB */
        for(i = 0; i < TPE_CANBUFF_SIZE; i++)
        {
            data[i] = (iptr1->b)[i];
        }

#ifdef TPE_CHECK_DLC
        if (tpeMsgDlc < 8u) // Diagnostic messages shall be sent using 8 bytes, i.e. DLC = 8
        {
            /* Reset global Error */
            tpeError = TPE_CHECK_DLC_FAILED;

            /* Reset TPE */
            tpeReInit();

            /* flush data from the LPDU */
            for(i = 0; i < TPE_CANBUFF_SIZE; i++)
            {
                data[i] = 0U;
            }

            /* return error != 0 */
            res = 1u;
        }
#endif //TPE_CHECK_DLC
    }
    else
    {
#if(TPE_USE_ENGCAN == TPE_USE_MCAN)
        res = (int8_T)CAN_RxData(TPE_CAN,TPE_CAN_RXBUF,&iptr);
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
      res = (int8_T)TTCAN_RxData(TPE_CAN,TPE_CAN_RXBUF,&iptr);
#else
#error TPE peripheral not defined
#endif

        if (res == NO_ERROR) //an unicast message has been received
        {
            /* Get CAN message DLC */
            tpeMsgDlc = iptr->dlc;

            /* Set Physical Request Rx flag */
            tpeSetReqType(PHYSICAL);

            /* Copy data from CAN MB */
            for(i = 0; i < TPE_CANBUFF_SIZE; i++)
            {
                data[i] = (iptr->b)[i];
            }

#ifdef TPE_CHECK_DLC
            if (tpeMsgDlc < 8u) // Diagnostic messages shall be sent using 8 bytes, i.e. DLC = 8
            {
                /* Reset global Error */
                tpeError = TPE_CHECK_DLC_FAILED;

                /* Reset TPE */
                tpeReInit();

                /* flush data from the LPDU */
                for(i = 0; i < TPE_CANBUFF_SIZE; i++)
                {
                    data[i] = 0U;
                }

                /* return error != 0 */
                res = 1u;
            }
#endif //TPE_CHECK_DLC
        }
        else
        {
            /* flush data from the LPDU */
            for(i = 0; i < TPE_CANBUFF_SIZE; i++)
            {
                data[i] = 0;
            }
        }
    }
    return res;
}

/******************************************************************************
**   Function    : tpeRxDataException
**
**   Description:
**    Transport Protocol Exception for incoming packets
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**     associated config file.
**
**   EA GUID:
******************************************************************************/
static void tpeRxDataException (void)
{
uint8_T data[TPE_CANBUFF_SIZE] = {0,0,0,0,0,0,0,0};
uint8_T rx_buff[TPE_CANBUFF_SIZE];
uint8_T i;
uint8_T StFC; // 0-->CTS, 1-->WAIT, 2-->OverFlow/Abort, Otherwise-->Invalid
uint8_T StMin; // Separation Time requested by the external tool

#ifdef ISO_TP_MIXED_MODE
    if((tpeReceiveDataFromLL(data) == TPE_SUCCESS) && ((data[0] == TPE_TPDU_TA))) // second condition shall be used if extended addresing mode is used */
#else
    if(tpeReceiveDataFromLL(data) == TPE_SUCCESS)
#endif
    {
        if(tpeGetCommStatus() == TPE_OFF)
        {
            tpeSetCommStatus(TPE_ON);
        }


        for (i=0U; i<TPE_CANBUFF_SIZE; i++)
        {
            rx_buff[i] = data[i];
        }

        if(tpeTx.status == TPE_IDLE)
        {
            /* Receiving phase */
            tpeRxMngr(rx_buff);
        }
        else
        {
            /* Sending phase */
            if(((rx_buff[TPE_TPDU_TPCI_POS] & TPE_TPDU_TPCI_NO_SF_MASK) == TPE_TPDU_TPCI_FC ) && ((tpeTx.status == TPE_WAIT_CONNECT)||(tpeTx.status == TPE_WAIT_FLOWCONTROL)))
            {
                StFC = (rx_buff[TPE_TPDU_TPCI_POS] & TPE_TPDU_FC_STATUS_MASK);

                if (tpeGetReqType() == FUNCTIONAL)  // aborting transmission if tester sends FC with a Functional/Broadcast message
                {
                    /* Set global RX error */
                    tpeError = TPE_FUNC_FC_NOT_COMPLIANT;
                    /* reset TPE */
                    tpeReInit();
                }
                else if (tpeMsgDlc < TPE_FC_PDU_LEN)
                {
                    /* Set global RX error */
                    tpeError = TPE_FC_INVALID_LENGTH;
                    /* reset TPE */
                    tpeReInit();
                }
                else
                {
                    switch(StFC)
                    {
                        case TPE_TPDU_FC_CTS: // Clear To Send
                        {
                            /* Flow Control BS */
                            tpeTx.block_size = rx_buff[TPE_TPDU_FC_BS_POS];
                            if (tpeTx.block_size == 0u)
                            {
                                tpeTx.block_size = TPE_TPDU_FC_BS_MAX; // Set max reception capabiliets of the Client
                            }
                            /* Flow Control ST min */
                            StMin = tpeSTMinCheck(rx_buff[TPE_TPDU_FC_ST_POS]);
                            tpeTx.ST_timeout = StMin/STMIN_DISCRETE_STEP; // in task da 5 msec

                            tpeRx.rx_buf |= DIAGNOSE_REQUEST_FC;
                            tpeTxMngr();
                            break;
                        }

                        case TPE_TPDU_FC_WAIT: // Wait
                        {
                            /* Set global RX error */
                            tpeError = TPE_FC_FS_NOT_VALID;
                            /* reset TPE */
                            tpeReInit();
                        }
                        break;

                        case TPE_TPDU_FC_OF: // Overflow/Abort
                        {
                            /* Set global RX error */
                            tpeError = TPE_FC_FS_NOT_VALID;
                            /* reset TPE */
                            tpeReInit();
                        }
                        break;

                        default: // Invalid Status
                        {
                            /* Set global RX error */
                            tpeError = TPE_FC_FS_NOT_VALID;
                            /* reset TPE */
                            tpeReInit();
                        }
                        break;
                    }
                }
            }
        }
    }
}

/******************************************************************************
**   Function    : tpeTxDataException
**
**   Description:
**    Transport Protocol Exception for outcoming packets
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**     associated config file.
**
**   EA GUID:
******************************************************************************/
static void tpeTxDataException (void)
{
    /* TPE uses only Rx exception since communication is clinet/server and it is always started by the client (external tool)
       and server just sends responses */
}

/******************************************************************************
**   Function    : tpeSTMinCheck
**
**   Description:
**    This method checks requested STmin encoded in the Flow Control sent by external tool
**
**   Parameters :
**    [in] uint8_T stmin_req
**
**   Returns:
**    uint8_T calculated STmin to be applied
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file. Reference: ISO 15765-2:2011(E) Table 15 -- Definition of STmin 
**    values. In case of invalid STmin, no response is sent after the FF, but communication 
**    is kept active
**
**   EA GUID:
******************************************************************************/
static uint8_T tpeSTMinCheck(uint8_T stmin_req)
{
uint8_T res;

#ifdef TPE_TIME_BASE_MS
#if (TPE_TIME_BASE_MS == 5u) //5msec transmission task

    if((stmin_req >= 0u) && (stmin_req < 5u))
    {
        res = 5u;
    }
    else if ((stmin_req >= 5u) && (stmin_req <= 125u)) // actually 127, but with discrete step of 5msec, 130msec are not allowed by ISO 15765-2:2011(E) Table 15
    {
        if ((stmin_req % STMIN_DISCRETE_STEP) == 0u)
        {
            res = stmin_req;
        }
        else
        {
            res = (stmin_req - (stmin_req % STMIN_DISCRETE_STEP) + STMIN_DISCRETE_STEP);
        }
    }
    else //if ((stmin_req >= 128u) && (stmin_req <= 240u))
    {
        /* ISO 15765-2_2011
        8.5.5.6 SeparationTime minimum (STmin) error handling
        If an FC N_PDU message is received with a reserved STmin parameter value, then the sending network entity
        shall use the longest STmin value specified by this part of ISO 15765 (0x7F = 127 ms) instead of the value
        received from the receiving network entity for the duration of the on-going segmented message transmission. */
        res =  STMIN_MAX - 1u;
    }

#elif (TPE_TIME_BASE_MS == 1u) //1msec transmission task
    if(stmin_req == 0u)
    {
        res = 1u;
    }
    else if ((stmin_req > 0u) && (stmin_req <= 127u))
    {
        res = stmin_req;
    }
    else //if ((stmin_req >= 128u) && (stmin_req <= 240u))
    {
        /* ISO 15765-2_2011
        8.5.5.6 SeparationTime minimum (STmin) error handling
        If an FC N_PDU message is received with a reserved STmin parameter value, then the sending network entity
        shall use the longest STmin value specified by this part of ISO 15765 (0x7F = 127 ms) instead of the value
        received from the receiving network entity for the duration of the on-going segmented message transmission. */
        res =  STMIN_MAX - 1u;
    }

#else
#warning Timing resolution not defined for TPE state machine
#endif
#endif


    return res;

}

/******************************************************************************
**   Function    : tpeSetReqType
**
**   Description:
**    This method sets flag for signalling received request type
**
**   Parameters :
**    [in] tpeReqType_T stmin_req
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: Configuration values, project dependents, are defined in the 
**    associated config file.
**
**   EA GUID:
******************************************************************************/
static void tpeSetReqType(tpeReqType_T req)
{
    tpeReqType = req;
}




#pragma ghs endnomisra

#endif  // _BUILD_TPE_

