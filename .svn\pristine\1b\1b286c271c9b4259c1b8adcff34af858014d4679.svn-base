/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      CoilTarget.h
 **  Date:          08-Sep-2021
 **
 **  Model Version: 1.339
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_CoilTarget_h_
#define RTW_HEADER_CoilTarget_h_
#ifndef CoilTarget_COMMON_INCLUDES_
# define CoilTarget_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* CoilTarget_COMMON_INCLUDES_ */


/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Model entry point functions */
extern void CoilTarget_initialize(void);

/* Exported entry point function */
extern void CoilTarget_Init(void);

/* Exported entry point function */
extern void CoilTarget_T10ms(void);

/* Exported entry point function */
extern void CoilTarget_Tdc(void);

/* Exported data declaration */


/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint16_T DwellTimeBase;         /* '<S4>/Merge' */

/* Basic dwell time related to the primary current target calculated */
extern int16_T DwellTimeObj[8];        /* '<S4>/Merge15' */

/* Dwell time target calculated by the PI control (if SELPIFFWCTRL is set, its value is used as an offset, that can be also negative) */
extern uint16_T ILeadObj;              /* '<S4>/Merge3' */

/* Primary current target corrected and filtered */
extern uint16_T LoadDPlaEn;            /* '<S15>/Data Type Conversion1' */

/* Load plasma enable compensated */
extern boolean_T SelPIFFWCtrl;         /* '<S1>/SELPIFFWCTRL' */

/* Enable feed forward path of primary winding current control  */
extern uint8_T StPlasObj;              /* '<S4>/Merge1' */

/* Current ignition mode: 1=EPWS+ION; 2=single spark+ION. */
extern boolean_T VtSelPlaCtrlOL[8];    /* '<S4>/Merge14' */

/* Flag to Enable OL */
extern uint32_T VtTimCmdStall[8];      /* '<S4>/Merge16' */

/* Time to stall primary current PI control */


/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S8>/Data Type Duplicate' : Unused code path elimination
 * Block '<S6>/Data Type Duplicate' : Unused code path elimination
 * Block '<S7>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Propagation' : Unused code path elimination
 * Block '<S35>/Data Type Duplicate' : Unused code path elimination
 * Block '<S36>/Data Type Duplicate' : Unused code path elimination
 * Block '<S31>/Data Type Duplicate' : Unused code path elimination
 * Block '<S32>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Duplicate' : Unused code path elimination
 * Block '<S33>/Data Type Propagation' : Unused code path elimination
 * Block '<S55>/Data Type Duplicate' : Unused code path elimination
 * Block '<S56>/Data Type Duplicate' : Unused code path elimination
 * Block '<S59>/Data Type Duplicate' : Unused code path elimination
 * Block '<S58>/Data Type Duplicate' : Unused code path elimination
 * Block '<S62>/Data Type Duplicate' : Unused code path elimination
 * Block '<S61>/Data Type Duplicate' : Unused code path elimination
 * Block '<S63>/Constant' : Unused code path elimination
 * Block '<S67>/Data Type Duplicate' : Unused code path elimination
 * Block '<S63>/Data Type Duplicate' : Unused code path elimination
 * Block '<S64>/Data Type Duplicate' : Unused code path elimination
 * Block '<S65>/Data Type Duplicate' : Unused code path elimination
 * Block '<S66>/Data Type Duplicate' : Unused code path elimination
 * Block '<S70>/Data Type Duplicate' : Unused code path elimination
 * Block '<S69>/Data Type Duplicate' : Unused code path elimination
 * Block '<S5>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S5>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S5>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S5>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S5>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S5>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S5>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S8>/Conversion' : Eliminate redundant data type conversion
 * Block '<S5>/Reshape' : Reshape block reduction
 * Block '<S6>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S6>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S6>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S6>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S29>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S35>/Conversion' : Eliminate redundant data type conversion
 * Block '<S29>/Reshape' : Reshape block reduction
 * Block '<S30>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S30>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S36>/Conversion' : Eliminate redundant data type conversion
 * Block '<S30>/Reshape' : Reshape block reduction
 * Block '<S31>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S31>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S31>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S31>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S32>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S52>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S55>/Conversion' : Eliminate redundant data type conversion
 * Block '<S53>/Conversion1' : Eliminate redundant data type conversion
 * Block '<S53>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S53>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S53>/Conversion4' : Eliminate redundant data type conversion
 * Block '<S56>/Conversion' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S57>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S59>/Conversion' : Eliminate redundant data type conversion
 * Block '<S57>/Reshape' : Reshape block reduction
 * Block '<S58>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S58>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S58>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S58>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S60>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S62>/Conversion' : Eliminate redundant data type conversion
 * Block '<S60>/Reshape' : Reshape block reduction
 * Block '<S61>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S61>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S61>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S61>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S63>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S67>/Conversion' : Eliminate redundant data type conversion
 * Block '<S63>/Reshape' : Reshape block reduction
 * Block '<S64>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S64>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S64>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S64>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S65>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S65>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S65>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S65>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S66>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S66>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S66>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S66>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S16>/Data Type Conversion' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion2' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion3' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion5' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion6' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion7' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion8' : Eliminate redundant data type conversion
 * Block '<S68>/Conversion9' : Eliminate redundant data type conversion
 * Block '<S70>/Conversion' : Eliminate redundant data type conversion
 * Block '<S68>/Reshape' : Reshape block reduction
 * Block '<S69>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S69>/Data Type Conversion4' : Eliminate redundant data type conversion
 * Block '<S69>/Data Type Conversion5' : Eliminate redundant data type conversion
 * Block '<S69>/Data Type Conversion8' : Eliminate redundant data type conversion
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CoilTarget'
 * '<S1>'   : 'CoilTarget/CoilTarget_Init_mgm'
 * '<S2>'   : 'CoilTarget/CoilTarget_T10ms_mgm'
 * '<S3>'   : 'CoilTarget/CoilTarget_Tdc_mgm'
 * '<S4>'   : 'CoilTarget/Merge'
 * '<S5>'   : 'CoilTarget/CoilTarget_Init_mgm/Look2D_IR_U16'
 * '<S6>'   : 'CoilTarget/CoilTarget_Init_mgm/PreLookUpIdSearch_U16'
 * '<S7>'   : 'CoilTarget/CoilTarget_Init_mgm/PreLookUpIdSearch_U16_3'
 * '<S8>'   : 'CoilTarget/CoilTarget_Init_mgm/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S9>'   : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control'
 * '<S10>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Select_OL_CL'
 * '<S11>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Switch_idx'
 * '<S12>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Bypass_OpenLoopCtrl'
 * '<S13>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj'
 * '<S14>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj'
 * '<S15>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_Ratio'
 * '<S16>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Max_Saturation'
 * '<S17>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_DwellObj'
 * '<S18>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_Error'
 * '<S19>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI'
 * '<S20>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Select_Element'
 * '<S21>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Set_Element'
 * '<S22>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_DwellObj/Compare To Zero'
 * '<S23>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_DwellObj/InversionPI_TDwell'
 * '<S24>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_DwellObj/OpenLoop_TDwell'
 * '<S25>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_DwellObj/InversionPI_TDwell/Div_SInt_UnSInt'
 * '<S26>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_DwellObj/InversionPI_TDwell/Saturation Dynamic'
 * '<S27>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/Calc_Error/rate_limiter_and_ctrl_reset'
 * '<S28>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/InvPIErrDwellObj_calc'
 * '<S29>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/Look2D_IR_U1'
 * '<S30>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/Look2D_IR_U16'
 * '<S31>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/PreLookUpIdSearch_S1'
 * '<S32>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/PreLookUpIdSearch_S16'
 * '<S33>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/Saturation Dynamic1'
 * '<S34>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/Signed_rescal_shiftL'
 * '<S35>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/Look2D_IR_U1/Data Type Conversion Inherited1'
 * '<S36>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_DWellTimeObj/PI/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S37>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_FlgNoMap'
 * '<S38>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj'
 * '<S39>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target'
 * '<S40>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_FlgNoMap/Compare To Zero'
 * '<S41>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_FlgNoMap/Compare To Zero1'
 * '<S42>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_FlgNoMap/Compare To Zero2'
 * '<S43>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/EnableIgnitionModeMaps'
 * '<S44>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/IgnitionModeDefault'
 * '<S45>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/IgnitionModeMaps'
 * '<S46>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/EnableIgnitionModeMaps/find_element_table'
 * '<S47>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Calc_StPlasObj/IgnitionModeMaps/find_element_table'
 * '<S48>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Add_Compensations'
 * '<S49>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Calc_DwellTimeBase'
 * '<S50>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Calc_ILeadBase'
 * '<S51>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Add_Compensations/FOF'
 * '<S52>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Add_Compensations/LookUp_IR_S16_1'
 * '<S53>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Add_Compensations/LookUp_IR_U16'
 * '<S54>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Add_Compensations/FOF/PolyAssert'
 * '<S55>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Add_Compensations/LookUp_IR_S16_1/Data Type Conversion Inherited3'
 * '<S56>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Add_Compensations/LookUp_IR_U16/Data Type Conversion Inherited3'
 * '<S57>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Calc_DwellTimeBase/Look2D_IR_U16'
 * '<S58>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Calc_DwellTimeBase/PreLookUpIdSearch_U16'
 * '<S59>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Calc_DwellTimeBase/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S60>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Calc_ILeadBase/Look2D_IR_U16'
 * '<S61>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Calc_ILeadBase/PreLookUpIdSearch_U2'
 * '<S62>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_ILeadObj_StPlasObj/Set_FFW_And_Target/Calc_ILeadBase/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S63>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_Ratio/Look2D_IR_S16'
 * '<S64>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_Ratio/PreLookUpIdSearch_S16_2'
 * '<S65>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_Ratio/PreLookUpIdSearch_U16_3'
 * '<S66>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_Ratio/PreLookUpIdSearch_U3'
 * '<S67>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Calc_Ratio/Look2D_IR_S16/Data Type Conversion Inherited1'
 * '<S68>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Max_Saturation/Look2D_IR_U16'
 * '<S69>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Max_Saturation/PreLookUpIdSearch_U16_3'
 * '<S70>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Current_Control/Max_Saturation/Look2D_IR_U16/Data Type Conversion Inherited1'
 * '<S71>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Select_OL_CL/Memory_Counter'
 * '<S72>'  : 'CoilTarget/CoilTarget_Tdc_mgm/Select_OL_CL/Memory_Counter/Calc_DiagOLReset'
 */

/*-
 * Requirements for '<Root>': CoilTarget
 *
 * Inherited requirements for '<Root>/CoilTarget_T10ms_mgm':
 *  1. EISB_FCA6CYL_SW_REQ_1784: The software shall increase on 10ms event the timer VtTimCmdStall ... (ECU_SW_Requirements#6343)
 *
 * Inherited requirements for '<S17>/InversionPI_TDwell':
 *  1. EISB_FCA6CYL_SW_REQ_1790: In closed loop the dwell time target (DwellTimeObj) is calculated ... (ECU_SW_Requirements#6364)
 *
 * Inherited requirements for '<S17>/OpenLoop_TDwell':
 *  1. EISB_FCA6CYL_SW_REQ_1780: In open loop control the current calculated dwell time (VtDwellTim... (ECU_SW_Requirements#6350)
 *
 * Inherited requirements for '<S18>/rate_limiter_and_ctrl_reset':
 *  1. EISB_FCA6CYL_SW_REQ_1778: The software shall calculate the signal ILeadObjRt, which definiti... (ECU_SW_Requirements#6359)
 *
 * Inherited requirements for '<S38>/IgnitionModeDefault':
 *  1. EISB_FCA6CYL_SW_REQ_1869: When the signal EnIgnModeCnd is false, the ignition mode (StPlasOb... (ECU_SW_Requirements#7290)
 *
 * Inherited requirements for '<S38>/IgnitionModeMaps':
 *  1. EISB_FCA6CYL_SW_REQ_1870: When the signal EnIgnModeCnd is true, the ignition mode (StPlasObj... (ECU_SW_Requirements#7291)

 */
#endif                                 /* RTW_HEADER_CoilTarget_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 * text_analytics_toolbox                                                     *
 *============================================================================*/