/* generated by MCS-Assembler tool ASM-MCS version 0.9 */
/* Copyright (C) 2011-2016 by <PERSON>, Germany */
/* target architecture : mcs24-1 */

#ifndef MCS2_H_
#define MCS2_H_

#define OFFSET_MCS2_MEM     (   0) /* byte address offset for assembled code in array C-array 'mcs2_mem' */
#define SIZE_MCS2_MEM       (2496) /* code size in bytes of assembled code in C-array 'mcs2_mem' */

#define LABEL_MCS2_MEM_PMOS0_4_STOP_DELAY        ( 189) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4_STOP_DELAY' */
#define LABEL_MCS2_MEM_PMOS0_4_LOOP_WA           ( 173) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4_LOOP_WA' */
#define LABEL_MCS2_MEM_SPARK_B0_LOOP             ( 550) /* Index into C-array 'mcs2_mem' for assembler label 'SPARK_B0_LOOP' */
#define LABEL_MCS2_MEM_PMOS3_7_LOOP              ( 451) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7_LOOP' */
#define LABEL_MCS2_MEM_TSK2_STACK                (  40) /* Index into C-array 'mcs2_mem' for assembler label 'TSK2_STACK' */
#define LABEL_MCS2_MEM_TSK7_STACK                ( 120) /* Index into C-array 'mcs2_mem' for assembler label 'TSK7_STACK' */
#define LABEL_MCS2_MEM_MOS5_WAIT_PARAM           ( 253) /* Index into C-array 'mcs2_mem' for assembler label 'MOS5_WAIT_PARAM' */
#define LABEL_MCS2_MEM_NMOS0_4_ENABLE            ( 207) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS0_4_ENABLE' */
#define LABEL_MCS2_MEM_TSK0_STACK                (   8) /* Index into C-array 'mcs2_mem' for assembler label 'TSK0_STACK' */
#define LABEL_MCS2_MEM_ISEC0_ADC_RECON_WAIT       ( 537) /* Index into C-array 'mcs2_mem' for assembler label 'ISEC0_ADC_RECON_WAIT' */
#define LABEL_MCS2_MEM_PMOS0_4_WAIT_TRG_REOPEN       ( 209) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4_WAIT_TRG_REOPEN' */
#define LABEL_MCS2_MEM_PMOS3_7_STOP_DELAY        ( 461) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7_STOP_DELAY' */
#define LABEL_MCS2_MEM_TSK3_INIT                 ( 408) /* Index into C-array 'mcs2_mem' for assembler label 'TSK3_INIT' */
#define LABEL_MCS2_MEM_TSK6_STACK                ( 104) /* Index into C-array 'mcs2_mem' for assembler label 'TSK6_STACK' */
#define LABEL_MCS2_MEM_ISEC_DISABLE              ( 553) /* Index into C-array 'mcs2_mem' for assembler label 'ISEC_DISABLE' */
#define LABEL_MCS2_MEM_TSK3_STACK                (  56) /* Index into C-array 'mcs2_mem' for assembler label 'TSK3_STACK' */
#define LABEL_MCS2_MEM_PMOS0_4_REOPEN            ( 220) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4_REOPEN' */
#define LABEL_MCS2_MEM_IPRI1_START               ( 574) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI1_START' */
#define LABEL_MCS2_MEM_MOS7_WAIT_PARAM           ( 435) /* Index into C-array 'mcs2_mem' for assembler label 'MOS7_WAIT_PARAM' */
#define LABEL_MCS2_MEM_TSK0_INIT                 ( 136) /* Index into C-array 'mcs2_mem' for assembler label 'TSK0_INIT' */
#define LABEL_MCS2_MEM_TSK1_STACK                (  24) /* Index into C-array 'mcs2_mem' for assembler label 'TSK1_STACK' */
#define LABEL_MCS2_MEM_IPRI0_LOOP                ( 556) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI0_LOOP' */
#define LABEL_MCS2_MEM_TASK5_DONE                ( 620) /* Index into C-array 'mcs2_mem' for assembler label 'TASK5_DONE' */
#define LABEL_MCS2_MEM_PMOS2_6_STOP_TRIGGER       ( 367) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6_STOP_TRIGGER' */
#define LABEL_MCS2_MEM_MOS1_5                    ( 238) /* Index into C-array 'mcs2_mem' for assembler label 'MOS1_5' */
#define LABEL_MCS2_MEM_SPARK_B1_LOOP             ( 612) /* Index into C-array 'mcs2_mem' for assembler label 'SPARK_B1_LOOP' */
#define LABEL_MCS2_MEM_MOS0_4                    ( 148) /* Index into C-array 'mcs2_mem' for assembler label 'MOS0_4' */
#define LABEL_MCS2_MEM_PMOS2_6_REOPEN_DELAY_WA       ( 397) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6_REOPEN_DELAY_WA' */
#define LABEL_MCS2_MEM_PMOS3_7_REOPEN            ( 493) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7_REOPEN' */
#define LABEL_MCS2_MEM_TSK5_STACK                (  88) /* Index into C-array 'mcs2_mem' for assembler label 'TSK5_STACK' */
#define LABEL_MCS2_MEM_PMOS0_4                   ( 164) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4' */
#define LABEL_MCS2_MEM_PMOS1_5_REOPEN            ( 311) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5_REOPEN' */
#define LABEL_MCS2_MEM_TASK0_DONE                ( 224) /* Index into C-array 'mcs2_mem' for assembler label 'TASK0_DONE' */
#define LABEL_MCS2_MEM_ISEC0_START               ( 535) /* Index into C-array 'mcs2_mem' for assembler label 'ISEC0_START' */
#define LABEL_MCS2_MEM_NMOS0_4_WAIT_EVENT_WA       ( 195) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS0_4_WAIT_EVENT_WA' */
#define LABEL_MCS2_MEM_MOS2_6                    ( 329) /* Index into C-array 'mcs2_mem' for assembler label 'MOS2_6' */
#define LABEL_MCS2_MEM_TASK1_START               ( 234) /* Index into C-array 'mcs2_mem' for assembler label 'TASK1_START' */
#define LABEL_MCS2_MEM_MOS3_WAIT_PARAM           ( 429) /* Index into C-array 'mcs2_mem' for assembler label 'MOS3_WAIT_PARAM' */
#define LABEL_MCS2_MEM_TSK4_STACK                (  72) /* Index into C-array 'mcs2_mem' for assembler label 'TSK4_STACK' */
#define LABEL_MCS2_MEM_MOS1_WAIT_PARAM           ( 247) /* Index into C-array 'mcs2_mem' for assembler label 'MOS1_WAIT_PARAM' */
#define LABEL_MCS2_MEM_PMOS0_4_REOPEN_DELAY_WA       ( 215) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4_REOPEN_DELAY_WA' */
#define LABEL_MCS2_MEM_TASK0_START               ( 144) /* Index into C-array 'mcs2_mem' for assembler label 'TASK0_START' */
#define LABEL_MCS2_MEM_MOS0                      ( 153) /* Index into C-array 'mcs2_mem' for assembler label 'MOS0' */
#define LABEL_MCS2_MEM_PMOS3_7                   ( 436) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7' */
#define LABEL_MCS2_MEM_MOS0_WAIT_PARAM           ( 157) /* Index into C-array 'mcs2_mem' for assembler label 'MOS0_WAIT_PARAM' */
#define LABEL_MCS2_MEM_MOS4                      ( 159) /* Index into C-array 'mcs2_mem' for assembler label 'MOS4' */
#define LABEL_MCS2_MEM_MOS4_WAIT_PARAM           ( 163) /* Index into C-array 'mcs2_mem' for assembler label 'MOS4_WAIT_PARAM' */
#define LABEL_MCS2_MEM_PMOS0_4_LOOP              ( 179) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4_LOOP' */
#define LABEL_MCS2_MEM_PMOS0_4_STOP_TRIGGER       ( 186) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4_STOP_TRIGGER' */
#define LABEL_MCS2_MEM_NMOS0_4_WAIT_EVENT        ( 201) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS0_4_WAIT_EVENT' */
#define LABEL_MCS2_MEM_PMOS0_4_REOPEN_DELAY       ( 217) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS0_4_REOPEN_DELAY' */
#define LABEL_MCS2_MEM_IPRI0_START               ( 514) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI0_START' */
#define LABEL_MCS2_MEM_TSK1_INIT                 ( 226) /* Index into C-array 'mcs2_mem' for assembler label 'TSK1_INIT' */
#define LABEL_MCS2_MEM_MOS1                      ( 243) /* Index into C-array 'mcs2_mem' for assembler label 'MOS1' */
#define LABEL_MCS2_MEM_MOS5                      ( 249) /* Index into C-array 'mcs2_mem' for assembler label 'MOS5' */
#define LABEL_MCS2_MEM_PMOS1_5                   ( 254) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5' */
#define LABEL_MCS2_MEM_PMOS1_5_LOOP_WA           ( 263) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5_LOOP_WA' */
#define LABEL_MCS2_MEM_PMOS1_5_LOOP              ( 269) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5_LOOP' */
#define LABEL_MCS2_MEM_PMOS1_5_STOP_TRIGGER       ( 276) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5_STOP_TRIGGER' */
#define LABEL_MCS2_MEM_PMOS1_5_STOP_DELAY        ( 279) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5_STOP_DELAY' */
#define LABEL_MCS2_MEM_NMOS1_5_WAIT_EVENT_WA       ( 285) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS1_5_WAIT_EVENT_WA' */
#define LABEL_MCS2_MEM_TASK2_START               ( 325) /* Index into C-array 'mcs2_mem' for assembler label 'TASK2_START' */
#define LABEL_MCS2_MEM_MOS2_WAIT_PARAM           ( 338) /* Index into C-array 'mcs2_mem' for assembler label 'MOS2_WAIT_PARAM' */
#define LABEL_MCS2_MEM_NMOS1_5_WAIT_EVENT        ( 291) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS1_5_WAIT_EVENT' */
#define LABEL_MCS2_MEM_NMOS1_5_ENABLE            ( 298) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS1_5_ENABLE' */
#define LABEL_MCS2_MEM_PMOS1_5_WAIT_TRG_REOPEN       ( 300) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5_WAIT_TRG_REOPEN' */
#define LABEL_MCS2_MEM_PMOS1_5_REOPEN_DELAY_WA       ( 306) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5_REOPEN_DELAY_WA' */
#define LABEL_MCS2_MEM_PMOS1_5_REOPEN_DELAY       ( 308) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS1_5_REOPEN_DELAY' */
#define LABEL_MCS2_MEM_TASK1_DONE                ( 315) /* Index into C-array 'mcs2_mem' for assembler label 'TASK1_DONE' */
#define LABEL_MCS2_MEM_TSK2_INIT                 ( 317) /* Index into C-array 'mcs2_mem' for assembler label 'TSK2_INIT' */
#define LABEL_MCS2_MEM_MOS2                      ( 334) /* Index into C-array 'mcs2_mem' for assembler label 'MOS2' */
#define LABEL_MCS2_MEM_MOS6                      ( 340) /* Index into C-array 'mcs2_mem' for assembler label 'MOS6' */
#define LABEL_MCS2_MEM_MOS6_WAIT_PARAM           ( 344) /* Index into C-array 'mcs2_mem' for assembler label 'MOS6_WAIT_PARAM' */
#define LABEL_MCS2_MEM_PMOS2_6                   ( 345) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6' */
#define LABEL_MCS2_MEM_PMOS2_6_LOOP_WA           ( 354) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6_LOOP_WA' */
#define LABEL_MCS2_MEM_TASK4_START               ( 507) /* Index into C-array 'mcs2_mem' for assembler label 'TASK4_START' */
#define LABEL_MCS2_MEM_PMOS2_6_LOOP              ( 360) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6_LOOP' */
#define LABEL_MCS2_MEM_PMOS2_6_STOP_DELAY        ( 370) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6_STOP_DELAY' */
#define LABEL_MCS2_MEM_PMOS3_7_REOPEN_DELAY       ( 490) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7_REOPEN_DELAY' */
#define LABEL_MCS2_MEM_NMOS2_6_WAIT_EVENT_WA       ( 376) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS2_6_WAIT_EVENT_WA' */
#define LABEL_MCS2_MEM_NMOS2_6_WAIT_EVENT        ( 382) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS2_6_WAIT_EVENT' */
#define LABEL_MCS2_MEM_NMOS2_6_ENABLE            ( 389) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS2_6_ENABLE' */
#define LABEL_MCS2_MEM_PMOS2_6_WAIT_TRG_REOPEN       ( 391) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6_WAIT_TRG_REOPEN' */
#define LABEL_MCS2_MEM_PMOS2_6_REOPEN_DELAY       ( 399) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6_REOPEN_DELAY' */
#define LABEL_MCS2_MEM_PMOS2_6_REOPEN            ( 402) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS2_6_REOPEN' */
#define LABEL_MCS2_MEM_TASK2_DONE                ( 406) /* Index into C-array 'mcs2_mem' for assembler label 'TASK2_DONE' */
#define LABEL_MCS2_MEM_TASK3_START               ( 416) /* Index into C-array 'mcs2_mem' for assembler label 'TASK3_START' */
#define LABEL_MCS2_MEM_ISEC1_ADC_RECON_WAIT       ( 599) /* Index into C-array 'mcs2_mem' for assembler label 'ISEC1_ADC_RECON_WAIT' */
#define LABEL_MCS2_MEM_MOS3_7                    ( 420) /* Index into C-array 'mcs2_mem' for assembler label 'MOS3_7' */
#define LABEL_MCS2_MEM_MOS3                      ( 425) /* Index into C-array 'mcs2_mem' for assembler label 'MOS3' */
#define LABEL_MCS2_MEM_MOS7                      ( 431) /* Index into C-array 'mcs2_mem' for assembler label 'MOS7' */
#define LABEL_MCS2_MEM_SPARK_B0_LOOP_WA          ( 548) /* Index into C-array 'mcs2_mem' for assembler label 'SPARK_B0_LOOP_WA' */
#define LABEL_MCS2_MEM_PMOS3_7_LOOP_WA           ( 445) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7_LOOP_WA' */
#define LABEL_MCS2_MEM_PMOS3_7_STOP_TRIGGER       ( 458) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7_STOP_TRIGGER' */
#define LABEL_MCS2_MEM_NMOS3_7_WAIT_EVENT_WA       ( 467) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS3_7_WAIT_EVENT_WA' */
#define LABEL_MCS2_MEM_NMOS3_7_WAIT_EVENT        ( 473) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS3_7_WAIT_EVENT' */
#define LABEL_MCS2_MEM_NMOS3_7_ENABLE            ( 480) /* Index into C-array 'mcs2_mem' for assembler label 'NMOS3_7_ENABLE' */
#define LABEL_MCS2_MEM_PMOS3_7_WAIT_TRG_REOPEN       ( 482) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7_WAIT_TRG_REOPEN' */
#define LABEL_MCS2_MEM_PMOS3_7_REOPEN_DELAY_WA       ( 488) /* Index into C-array 'mcs2_mem' for assembler label 'PMOS3_7_REOPEN_DELAY_WA' */
#define LABEL_MCS2_MEM_TASK3_DONE                ( 497) /* Index into C-array 'mcs2_mem' for assembler label 'TASK3_DONE' */
#define LABEL_MCS2_MEM_TSK4_INIT                 ( 499) /* Index into C-array 'mcs2_mem' for assembler label 'TSK4_INIT' */
#define LABEL_MCS2_MEM_IPRI0_WAIT_EVENT          ( 511) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI0_WAIT_EVENT' */
#define LABEL_MCS2_MEM_IPRI0_ADC_RECON_WAIT       ( 516) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI0_ADC_RECON_WAIT' */
#define LABEL_MCS2_MEM_ISEC1_START               ( 597) /* Index into C-array 'mcs2_mem' for assembler label 'ISEC1_START' */
#define LABEL_MCS2_MEM_IPRI0_WAIT_STOP           ( 527) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI0_WAIT_STOP' */
#define LABEL_MCS2_MEM_IPRI0_STOP                ( 530) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI0_STOP' */
#define LABEL_MCS2_MEM_TASK4_DONE                ( 557) /* Index into C-array 'mcs2_mem' for assembler label 'TASK4_DONE' */
#define LABEL_MCS2_MEM_TSK5_INIT                 ( 559) /* Index into C-array 'mcs2_mem' for assembler label 'TSK5_INIT' */
#define LABEL_MCS2_MEM_TASK5_START               ( 567) /* Index into C-array 'mcs2_mem' for assembler label 'TASK5_START' */
#define LABEL_MCS2_MEM_IPRI1_WAIT_EVENT          ( 571) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI1_WAIT_EVENT' */
#define LABEL_MCS2_MEM_IPRI1_ADC_RECON_WAIT       ( 576) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI1_ADC_RECON_WAIT' */
#define LABEL_MCS2_MEM_IPRI1_WAIT_STOP           ( 587) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI1_WAIT_STOP' */
#define LABEL_MCS2_MEM_IPRI1_STOP                ( 590) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI1_STOP' */
#define LABEL_MCS2_MEM_SPARK_B1_LOOP_WA          ( 610) /* Index into C-array 'mcs2_mem' for assembler label 'SPARK_B1_LOOP_WA' */
#define LABEL_MCS2_MEM_ISEC1_STOP                ( 615) /* Index into C-array 'mcs2_mem' for assembler label 'ISEC1_STOP' */
#define LABEL_MCS2_MEM_IPRI1_ERROR               ( 619) /* Index into C-array 'mcs2_mem' for assembler label 'IPRI1_ERROR' */
#define LABEL_MCS2_MEM_TSK6_INIT                 ( 622) /* Index into C-array 'mcs2_mem' for assembler label 'TSK6_INIT' */
#define LABEL_MCS2_MEM_TSK7_INIT                 ( 623) /* Index into C-array 'mcs2_mem' for assembler label 'TSK7_INIT' */

#endif
