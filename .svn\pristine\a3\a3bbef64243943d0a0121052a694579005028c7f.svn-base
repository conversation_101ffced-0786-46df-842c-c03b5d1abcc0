/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           TLE9278BQX_IOs.c
 **  File Creation Date: 10-Sep-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TLE9278BQX_IOs
 **  Model Description:
 **  Model Version:      1.432
 **  Model Author:       SalimbeniT - Tue Jan 20 16:01:59 2015
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: LanaL - Fri Sep 10 16:28:33 2021
 **
 **  Last Saved Modification:  LanaL - Fri Sep 10 16:27:41 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "TLE9278BQX_IOs_out.h"
#include "TLE9278BQX_IOs_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ID_VER_TLE9278BQX_IOS_DEF      1432U                     /* Referenced by: '<S4>/ID_VER_TLE9278BQX_IOS_DEF' */

/* ID Version */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_TLE9278BQX_IOS_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRVVLVNSPOR = 1638U;
                                   /* Referenced by: '<S6>/calc_FdbkLiveness' */

/* Threshold liveness at POR */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T FdbkLiveness;                  /* '<S3>/Merge' */

/* Liveness feedback */
uint8_T StLoadTstError;                /* '<S3>/Merge1' */

/* Load test status error */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_TLE9278BQX_IOs;/* '<S4>/ID_VER_TLE9278BQX_IOS_DEF' */

/* ID Version */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Start for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_IOs_fc_Init_Start(void)
{
  /* Start for Constant: '<S4>/ID_VER_TLE9278BQX_IOS_DEF' */
  IdVer_TLE9278BQX_IOs = ID_VER_TLE9278BQX_IOS_DEF;
}

/* Output and update for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_IOs_fc_Init(void)
{
  {
    /* user code (Output function Header for TID1) */

    /* System '<S2>/fc_Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    TLE9278BQX_IOs_initialize();

    /* SignalConversion generated from: '<S4>/FdbkLiveness' incorporates:
     *  Constant: '<S4>/ZERO'
     */
    FdbkLiveness = 0U;

    /* SignalConversion generated from: '<S4>/StLoadTstError' incorporates:
     *  Constant: '<S4>/LT_NORMAL'
     */
    StLoadTstError = ((uint8_T)LT_NORMAL);

    /* Constant: '<S4>/ID_VER_TLE9278BQX_IOS_DEF' */
    IdVer_TLE9278BQX_IOs = ID_VER_TLE9278BQX_IOS_DEF;

    /* user code (Output function Trailer for TID1) */

    /* System '<S2>/fc_Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Output and update for function-call system: '<S2>/fc_RT1' */
void TLE9278BQX_IOs_fc_RT1(void)
{
  /* user code (Output function Body for TID2) */

  /* System '<S2>/fc_RT1' */
  Dio_WriteChannel(OP_Liveness, 0u);

  /* user code (Output function Trailer for TID2) */

  /* System '<S2>/fc_RT1' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S2>/fc_RT2' */
void TLE9278BQX_IOs_fc_RT2(void)
{
  /* user code (Output function Body for TID3) */

  /* System '<S2>/fc_RT2' */
  AnalogIn_IDE_FS_Liveness_Acq();
  Dio_WriteChannel(OP_Liveness, 1u);

  /* Chart: '<S6>/calc_FdbkLiveness' incorporates:
   *  Inport: '<Root>/VVFdbkLiveness'
   */
  /* Gateway: TLE9278BQX_IOs/fc_RT2/calc_FdbkLiveness */
  /* During: TLE9278BQX_IOs/fc_RT2/calc_FdbkLiveness */
  /* Entry Internal: TLE9278BQX_IOs/fc_RT2/calc_FdbkLiveness */
  /* Transition: '<S11>:2' */
  /* '<S11>:4:1' sf_internal_predicateOutput = VVFdbkLiveness > THRVVLVNSPOR; */
  if (VVFdbkLiveness > THRVVLVNSPOR) {
    /* SignalConversion generated from: '<S6>/FdbkLiveness' */
    /* Transition: '<S11>:4' */
    /* '<S11>:4:2' FdbkLiveness = 1; */
    FdbkLiveness = 1U;
  } else {
    /* SignalConversion generated from: '<S6>/FdbkLiveness' */
    /* Transition: '<S11>:5' */
    /* '<S11>:5:1' FdbkLiveness = 0; */
    FdbkLiveness = 0U;
  }

  /* End of Chart: '<S6>/calc_FdbkLiveness' */
  /* user code (Output function Trailer for TID3) */

  /* System '<S2>/fc_RT2' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S2>/fc_RT3' */
void TLE9278BQX_IOs_fc_RT3(void)
{
  /* CCaller: '<S7>/IGNBuckTest' */
  StLoadTstError = IGNBuckTest();

  /* user code (Output function Trailer for TID4) */

  /* System '<S2>/fc_RT3' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S2>/fc_RT4' */
void TLE9278BQX_IOs_fc_RT4(void)
{
  /* CCaller: '<S8>/IGNLoadTest' */
  StLoadTstError = IGNLoadTest();

  /* user code (Output function Trailer for TID5) */

  /* System '<S2>/fc_RT4' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Output and update for function-call system: '<S2>/fc_RT5' */
void TLE9278BQX_IOs_fc_RT5(void)
{
  /* user code (Output function Body for TID6) */

  /* System '<S2>/fc_RT5' */
  VSRAMMGM_Update();

  /* user code (Output function Trailer for TID6) */

  /* System '<S2>/fc_RT5' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Model step function */
void TLE9278BQX_IOs_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_IOs_fc_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model step function */
void TLE9278BQX_IOs_RT1(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_RT1' incorporates:
   *  SubSystem: '<S2>/fc_RT1'
   */
  TLE9278BQX_IOs_fc_RT1();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_RT1' */
}

/* Model step function */
void TLE9278BQX_IOs_RT2(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_RT2' incorporates:
   *  SubSystem: '<S2>/fc_RT2'
   */
  TLE9278BQX_IOs_fc_RT2();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_RT2' */
}

/* Model step function */
void TLE9278BQX_IOs_RT3(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_RT3' incorporates:
   *  SubSystem: '<S2>/fc_RT3'
   */
  TLE9278BQX_IOs_fc_RT3();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_RT3' */
}

/* Model step function */
void TLE9278BQX_IOs_RT4(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_RT4' incorporates:
   *  SubSystem: '<S2>/fc_RT4'
   */
  TLE9278BQX_IOs_fc_RT4();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_RT4' */
}

/* Model step function */
void TLE9278BQX_IOs_RT5(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_RT5' incorporates:
   *  SubSystem: '<S2>/fc_RT5'
   */
  TLE9278BQX_IOs_fc_RT5();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_RT5' */
}

/* Model initialize function */
void TLE9278BQX_IOs_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_IOs_fc_Init_Start();

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_TLE9278BQX_IOS_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/