/****************************************************************************
*
* Copyright � 2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
*
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    isb_cfg.h
 * @brief   ISB Management configuration structure code.
 *
 * @addtogroup ISB
 * @{
 */

#ifndef _ISB_CFG_H_
#define _ISB_CFG_H_

/*===========================================================================*/
/* Module exported definitions.                                              */
/*===========================================================================*/
#define MOS_TIMEOUT     100u

#define ISB_USE_EPWS                              0UL
#define ISB_EPWS_PHASE_NUM                        4UL

#define BUCK_CH_PERIOD          67U //[usec]*0.1 149253 Hz 

#define BUCK_CHARGE_PH_NUMBER     4U
#define BUCK_DISCHARGE_PH_NUMBER  4U

#define BUCK_CH_D2_DURATION     400U
#define BUCK_CH_D2_PERIOD        BUCK_CH_PERIOD
#define BUCK_CH_D2_DUTY           60U 

#define BUCK_CH_D3_DURATION     500U
#define BUCK_CH_D3_PERIOD        BUCK_CH_PERIOD
#define BUCK_CH_D3_DUTY           80U 

#define BUCK_CH_D4_DURATION     600U
#define BUCK_CH_D4_PERIOD        BUCK_CH_PERIOD
#define BUCK_CH_D4_DUTY          120U 

#define BUCK_CH_D5_DURATION     300U
#define BUCK_CH_D5_PERIOD        BUCK_CH_PERIOD
#define BUCK_CH_D5_DUTY          140U

#define BUCK_DISCH_D6_DURATION  200U
#define BUCK_DISCH_D6_PERIOD     BUCK_CH_PERIOD
#define BUCK_DISCH_D6_DUTY       120U

#define BUCK_DISCH_D7_DURATION  200U
#define BUCK_DISCH_D7_PERIOD     BUCK_CH_PERIOD
#define BUCK_DISCH_D7_DUTY        80U

#define BUCK_DISCH_D8_DURATION  200U
#define BUCK_DISCH_D8_PERIOD     BUCK_CH_PERIOD
#define BUCK_DISCH_D8_DUTY        60U

/*===========================================================================*/
/* Module exported types.                                                    */
/*===========================================================================*/

/*===========================================================================*/
/* Module exported variables.                                                */
/*===========================================================================*/
extern ISB_COIL_SUPPLY cyl0_coil_d1_config;
extern ISB_COIL_SUPPLY cyl0_coil_d2_config;
extern ISB_COIL_SUPPLY cyl0_coil_d3_config;
extern ISB_COIL_SUPPLY cyl0_coil_d4_config;
extern ISB_COIL_SUPPLY cyl0_coil_d5_config;
extern ISB_COIL_SUPPLY cyl0_coil_d6_config;
extern ISB_COIL_SUPPLY cyl0_coil_d7_config;
extern ISB_COIL_SUPPLY cyl0_coil_d8_config;

extern ISB_EPWS_Config cyl0_epws1_config;
extern ISB_EPWS_Config cyl0_epws2_config;
extern ISB_EPWS_Config cyl0_epws3_config;
extern ISB_EPWS_Config cyl0_epws4_config;

extern ISB_COIL_SUPPLY cyl1_coil_d1_config;
extern ISB_COIL_SUPPLY cyl1_coil_d2_config;
extern ISB_COIL_SUPPLY cyl1_coil_d3_config;
extern ISB_COIL_SUPPLY cyl1_coil_d4_config;
extern ISB_COIL_SUPPLY cyl1_coil_d5_config;
extern ISB_COIL_SUPPLY cyl1_coil_d6_config;
extern ISB_COIL_SUPPLY cyl1_coil_d7_config;
extern ISB_COIL_SUPPLY cyl1_coil_d8_config;

extern ISB_EPWS_Config cyl1_epws1_config;
extern ISB_EPWS_Config cyl1_epws2_config;
extern ISB_EPWS_Config cyl1_epws3_config;
extern ISB_EPWS_Config cyl1_epws4_config;

extern ISB_COIL_SUPPLY cyl2_coil_d1_config;
extern ISB_COIL_SUPPLY cyl2_coil_d2_config;
extern ISB_COIL_SUPPLY cyl2_coil_d3_config;
extern ISB_COIL_SUPPLY cyl2_coil_d4_config;
extern ISB_COIL_SUPPLY cyl2_coil_d5_config;
extern ISB_COIL_SUPPLY cyl2_coil_d6_config;
extern ISB_COIL_SUPPLY cyl2_coil_d7_config;
extern ISB_COIL_SUPPLY cyl2_coil_d8_config;

extern ISB_EPWS_Config cyl2_epws1_config;
extern ISB_EPWS_Config cyl2_epws2_config;
extern ISB_EPWS_Config cyl2_epws3_config;
extern ISB_EPWS_Config cyl2_epws4_config;

extern ISB_COIL_SUPPLY cyl3_coil_d1_config;
extern ISB_COIL_SUPPLY cyl3_coil_d2_config;
extern ISB_COIL_SUPPLY cyl3_coil_d3_config;
extern ISB_COIL_SUPPLY cyl3_coil_d4_config;
extern ISB_COIL_SUPPLY cyl3_coil_d5_config;
extern ISB_COIL_SUPPLY cyl3_coil_d6_config;
extern ISB_COIL_SUPPLY cyl3_coil_d7_config;
extern ISB_COIL_SUPPLY cyl3_coil_d8_config;

extern ISB_EPWS_Config cyl3_epws1_config;
extern ISB_EPWS_Config cyl3_epws2_config;
extern ISB_EPWS_Config cyl3_epws3_config;
extern ISB_EPWS_Config cyl3_epws4_config;

extern ISB_COIL_SUPPLY cyl4_coil_d1_config;
extern ISB_COIL_SUPPLY cyl4_coil_d2_config;
extern ISB_COIL_SUPPLY cyl4_coil_d3_config;
extern ISB_COIL_SUPPLY cyl4_coil_d4_config;
extern ISB_COIL_SUPPLY cyl4_coil_d5_config;
extern ISB_COIL_SUPPLY cyl4_coil_d6_config;
extern ISB_COIL_SUPPLY cyl4_coil_d7_config;
extern ISB_COIL_SUPPLY cyl4_coil_d8_config;

extern ISB_EPWS_Config cyl4_epws1_config;
extern ISB_EPWS_Config cyl4_epws2_config;
extern ISB_EPWS_Config cyl4_epws3_config;
extern ISB_EPWS_Config cyl4_epws4_config;

extern ISB_COIL_SUPPLY cyl5_coil_d1_config;
extern ISB_COIL_SUPPLY cyl5_coil_d2_config;
extern ISB_COIL_SUPPLY cyl5_coil_d3_config;
extern ISB_COIL_SUPPLY cyl5_coil_d4_config;
extern ISB_COIL_SUPPLY cyl5_coil_d5_config;
extern ISB_COIL_SUPPLY cyl5_coil_d6_config;
extern ISB_COIL_SUPPLY cyl5_coil_d7_config;
extern ISB_COIL_SUPPLY cyl5_coil_d8_config;

extern ISB_EPWS_Config cyl5_epws1_config;
extern ISB_EPWS_Config cyl5_epws2_config;
extern ISB_EPWS_Config cyl5_epws3_config;
extern ISB_EPWS_Config cyl5_epws4_config;

extern ISB_COIL_SUPPLY cyl6_coil_d1_config;
extern ISB_COIL_SUPPLY cyl6_coil_d2_config;
extern ISB_COIL_SUPPLY cyl6_coil_d3_config;
extern ISB_COIL_SUPPLY cyl6_coil_d4_config;
extern ISB_COIL_SUPPLY cyl6_coil_d5_config;
extern ISB_COIL_SUPPLY cyl6_coil_d6_config;
extern ISB_COIL_SUPPLY cyl6_coil_d7_config;
extern ISB_COIL_SUPPLY cyl6_coil_d8_config;

extern ISB_EPWS_Config cyl6_epws1_config;
extern ISB_EPWS_Config cyl6_epws2_config;
extern ISB_EPWS_Config cyl6_epws3_config;
extern ISB_EPWS_Config cyl6_epws4_config;

extern ISB_COIL_SUPPLY cyl7_coil_d1_config;
extern ISB_COIL_SUPPLY cyl7_coil_d2_config;
extern ISB_COIL_SUPPLY cyl7_coil_d3_config;
extern ISB_COIL_SUPPLY cyl7_coil_d4_config;
extern ISB_COIL_SUPPLY cyl7_coil_d5_config;
extern ISB_COIL_SUPPLY cyl7_coil_d6_config;
extern ISB_COIL_SUPPLY cyl7_coil_d7_config;
extern ISB_COIL_SUPPLY cyl7_coil_d8_config;

extern ISB_EPWS_Config cyl7_epws1_config;
extern ISB_EPWS_Config cyl7_epws2_config;
extern ISB_EPWS_Config cyl7_epws3_config;
extern ISB_EPWS_Config cyl7_epws4_config;



extern ISB_CYLINDER_Config cylinder_0_config;
extern ISB_CYLINDER_Config cylinder_1_config;
extern ISB_CYLINDER_Config cylinder_2_config;
extern ISB_CYLINDER_Config cylinder_3_config;
extern ISB_CYLINDER_Config cylinder_4_config;
extern ISB_CYLINDER_Config cylinder_5_config;
extern ISB_CYLINDER_Config cylinder_6_config;
extern ISB_CYLINDER_Config cylinder_7_config;


extern ISB_Config eisb_config;

/*===========================================================================*/
/* Module exported functions.                                                */
/*===========================================================================*/

#endif /* _ISB_CFG_H_ */

/** @} */
