/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Description::                                                                                             $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/


#ifdef _BUILD_DIAGCANMGM_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "diagcanee.h"
//#include "diagcanmgm_utils.h"
#include "ee_out.h"
#include <string.h>
#include "App_tag.h"
#include "Utils_out.h"


/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
extern CALQUAL CALQUAL_POST uint8_T CALVER[4];

/*-----------------------------------*
 * PRIVATE VARIABLE DEFINITIONS
 *-----------------------------------*/

/*-----------------------------------*
 * CCP TESTPOINT DEFINITIONS
 *-----------------------------------*/

/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   DIAGCANMGM_EE_PowerOn
//
//   Description:
/*! \brief This method updates few fields of the electronic label with calibrated values
*/
//
//  Parameters and Returns:
/*!
\param param1: param1 meaning
\param param2: param2 meaning
\returns: what function returns
*/
//  Notes:
/*!
*/
/**************************************************************************/

void DIAGCANMGM_EE_PowerOn(void) 
{
    DIAGCANMGM_EE_SWVers_Update();
}

/***************************************************************************/
//   Function    :   DIAGCANMGM_EE_SWVers_Update
//
//   Description:    
/*! \brief Reset of all the output variables
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning
\param param2: param2 meaning
\returns: what function returns
*/
//  Notes:
/*!
*/
/**************************************************************************/
void DIAGCANMGM_EE_SWVers_Update(void)
{
uint16_T tmp_size;
#if 0
uint8_T CALVER_converted[SUPPLIER_ECU_SW_VER_LEN];

    getBootVersion(&(ECUcodeID1.BOOT_VERSION[0]), &tmp_size);
    getApplVersion(&(ECUcodeID1.APPL_VERSION[0]), &tmp_size);
    getApplVersion(&(ECUcodeSupData.SUPPLIER_ECU_SW_NUM[0]), &tmp_size);
#if 0 // EISB-490
    memset((void *)ECUcodeID1.CALIB_VERSION, 0x30, sizeof(ECUcodeID1.CALIB_VERSION));
#else
    memset((void *)ECUcodeID1.CALIB_VERSION, 0x20, sizeof(ECUcodeID1.CALIB_VERSION));
#endif
    memcpy((void *)ECUcodeID1.CALIB_VERSION, (void *)APP_VER, 4u);
    memcpy(&ECUcodeID1.CALIB_VERSION[4], (void *)CALVER, sizeof(CALVER));

    memcpy((void *)ECUcodeID2.ECU_DRAW_NUMBER,(void *)KWPCALDRAWNUMB,ECU_DRAW_NUMBER_LEN);

    CALVER_converted[0] = UTILS_hexByteToInt(CALVER[0], CALVER[1]);
    CALVER_converted[1] = UTILS_hexByteToInt(CALVER[2], CALVER[3]);

    memcpy((void *)ECUcodeSupData.SUPPLIER_ECU_SW_VER, (void *)CALVER_converted, SUPPLIER_ECU_SW_VER_LEN);
    memcpy((void *)ECUcodeSupData.HOMOLOGATION_NUMBER, (void *)KWPCALHOMOLNUMB, HOMOLOGATION_NUMBER_LEN);
    memcpy((void *)ECUcodeID2.ISO_CODE, (void *)KWPCALISOCODE, ISO_CODE_LEN);
#if ((ENGINE_TYPE == ML_P15_8C) || (ENGINE_TYPE == AM_AMG_6C))
#else
    memcpy((void *)ECUcodeID2.FIAT_ECU_SW_NUM, (void *)ECUcodeSupData.SUPPLIER_ECU_SW_NUM, FIAT_ECU_SW_NUM_LEN);
    memcpy((void *)ECUcodeID2.FIAT_ECU_SW_CALIB_NUM, (void *)ECUcodeID1.CALIB_VERSION, FIAT_ECU_SW_CALIB_NUM_LEN);
#endif
#ifdef HWNUM_COMPATIBILITY_CHECK
//    memcpy((void *)ECUcodeSupData.SUPPLIER_ECU_HW_NUM, (void *)ELDOR_ECU_HW_NUM, SUPPLIER_ECU_HW_NUM_LEN);
//    memcpy((void *)ECUcodeSupData.SUPPLIER_ECU_HW_VER, (void *)ELDOR_ECU_HW_VER, SUPPLIER_ECU_HW_VER_LEN);
#endif
#endif
}


#endif // _BUILD_DIAGCANMGM_

