/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef APP_TAG_H
#define APP_TAG_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/




/*!
\defgroup PublicDefines Public Defines
\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

#ifdef TEST_FCA_7Z00071

#if (KIT == KIT1)
#define APP_VER         "0503"
#elif (KIT == KIT2)
#define APP_VER         "8901" // only test value
#elif (KIT == KIT3)
#define APP_VER         "8901" // only test value
#elif (KIT == KIT4)
#define APP_VER         "8901" // only test value
#elif (KIT == KIT5)
#define APP_VER         "4321" // only test value
#endif
#else
#define     APP_VER     "0207"
#endif
/*!\egroup*/
/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
///This is a public typedef 


/*!\egroup*/
/*!
\defgroup PublicInline Public Inline Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC INLINE FUNCTIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   Inline function name
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this inline function 
*/
/**************************************************************************/


/*!\egroup*/


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/

/* STUB */

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/

#endif

/****************************************************************************
 ****************************************************************************/


