/************************************************************************************/
/* $HeadURL$   */
/* $ Description:                                                                   */
/* $Revision::        $                                                             */
/* $Date::                                                $                         */
/* $Author::                             $                                          */
/************************************************************************************/

#ifndef _RECMGMOUT_H_
#define _RECMGMOUT_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "diagmgm_out.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
/* Recovery IDs */ 
#define REC_KNOCKCORR_OFF_0     0u   // 0  - Recovery KnockCorr OFF cyl 0
#define REC_KNOCKCORR_OFF_1     1u   // 1  - Recovery KnockCorr OFF cyl 1
#define REC_KNOCKCORR_OFF_2     2u   // 2  - Recovery KnockCorr OFF cyl 2
#define REC_KNOCKCORR_OFF_3     3u   // 3  - Recovery KnockCorr OFF cyl 3
#define REC_KNOCKCORR_OFF_4     4u   // 4  - Recovery KnockCorr OFF cyl 4
#define REC_KNOCKCORR_OFF_5     5u   // 5  - Recovery KnockCorr OFF cyl 5
#define REC_KNOCKCORR_OFF_6     6u   // 6  - Recovery KnockCorr OFF cyl 6
#define REC_KNOCKCORR_OFF_7     7u   // 7  - Recovery KnockCorr OFF cyl 7
#define REC_NO_MISSPARK_A       8u   // 8  - Recovery disabilitazione misfire ch A
#define REC_NO_MISSPARK_B       9u   // 9  - Recovery disabilitazione misfire ch B
#define REC_VCHARGE_OFF_1       10u  // 10 - Recovery to disable VChargeObjOff actuation Cap1
#define REC_VCHARGE_OFF_2       11u  // 11 - Recovery to disable VChargeObjOff actuation Cap2
#define REC_VCHARGE_OFF_3       12u  // 12 - Recovery to disable VChargeObjOff actuation Cap3
#define REC_VCHARGE_OFF_4       13u  // 13 - Recovery to disable VChargeObjOff actuation Cap4
#define REC_NO_LOAD             14u  // 14 - Recovery Load signal unavailable
#define REC_PLASMA_OFF          15u  // 15 - Recovery plasma off
#define REC_TSPARKCTRL_OFF_0    16u  // 16 - Recovery TSparkCtrol Off cyl 0
#define REC_TSPARKCTRL_OFF_1    17u  // 17 - Recovery TSparkCtrol Off cyl 1
#define REC_TSPARKCTRL_OFF_2    18u  // 18 - Recovery TSparkCtrol Off cyl 2
#define REC_TSPARKCTRL_OFF_3    19u  // 19 - Recovery TSparkCtrol Off cyl 3
#define REC_TSPARKCTRL_OFF_4    20u  // 20 - Recovery TSparkCtrol Off cyl 4
#define REC_TSPARKCTRL_OFF_5    21u  // 21 - Recovery TSparkCtrol Off cyl 5
#define REC_TSPARKCTRL_OFF_6    22u  // 22 - Recovery TSparkCtrol Off cyl 6
#define REC_TSPARKCTRL_OFF_7    23u  // 23 - Recovery TSparkCtrol Off cyl 7
#define REC_MEGAKNOCK_DIS_0     24u  // 24 - Recovery disabilitazione Megaknock_0
#define REC_MEGAKNOCK_DIS_1     25u  // 25 - Recovery disabilitazione Megaknock_1
#define REC_MEGAKNOCK_DIS_2     26u  // 26 - Recovery disabilitazione Megaknock_2
#define REC_MEGAKNOCK_DIS_3     27u  // 27 - Recovery disabilitazione Megaknock_3
#define REC_MEGAKNOCK_DIS_4     28u  // 28 - Recovery disabilitazione Megaknock_4
#define REC_MEGAKNOCK_DIS_5     29u  // 29 - Recovery disabilitazione Megaknock_5
#define REC_MEGAKNOCK_DIS_6     30u  // 30 - Recovery disabilitazione Megaknock_6
#define REC_MEGAKNOCK_DIS_7     31u  // 31 - Recovery disabilitazione Megaknock_7
#define REC_NO_MISFUEL_0        32u  // 32 - Recovery disabilitazione misfuel cyl 0
#define REC_NO_MISFUEL_1        33u  // 33 - Recovery disabilitazione misfuel cyl 1
#define REC_NO_MISFUEL_2        34u  // 34 - Recovery disabilitazione misfuel cyl 2
#define REC_NO_MISFUEL_3        35u  // 35 - Recovery disabilitazione misfuel cyl 3
#define REC_NO_MISFUEL_4        36u  // 36 - Recovery disabilitazione misfuel cyl 4
#define REC_NO_MISFUEL_5        37u  // 37 - Recovery disabilitazione misfuel cyl 5
#define REC_NO_MISFUEL_6        38u  // 38 - Recovery disabilitazione misfuel cyl 6
#define REC_NO_MISFUEL_7        39u  // 39 - Recovery disabilitazione misfuel cyl 7
#define REC_CAN_LIVENESS_OFF    40u  // 40 - Recovery liveness and can Tx switched OFF
#define REC_IGN_OFF_0           41u  // 41 - Recovery to disable IGN cyl 0
#define REC_IGN_OFF_1           42u  // 42 - Recovery to disable IGN cyl 1
#define REC_IGN_OFF_2           43u  // 43 - Recovery to disable IGN cyl 2
#define REC_IGN_OFF_3           44u  // 44 - Recovery to disable IGN cyl 3
#define REC_IGN_OFF_4           45u  // 45 - Recovery to disable IGN cyl 4
#define REC_IGN_OFF_5           46u  // 46 - Recovery to disable IGN cyl 5
#define REC_IGN_OFF_6           47u  // 47 - Recovery to disable IGN cyl 6
#define REC_IGN_OFF_7           48u  // 48 - Recovery to disable IGN cyl 7
#define REC_FREE_49             49u  // 49 - FREE
#define N_REC                   50u

#define ACTIVEREC_dim           (8u)

/// Number of "external" diagnosis
#define EXT_DIAG_NUMB 12u

/// Number of rows of TBACTREC (equal to ceil(tot_diag/8))
#define ROW_REC (((DIAG_NUMBER + EXT_DIAG_NUMB) >> 3) + 1u)

/*!\egroup*/

extern CALQUAL CALQUAL_POST uint8_T TBACTREC[ROW_REC][N_REC]; //eccezionalmente...

/*!
\defgroup PublicInline Public Inline Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC INLINE FUNCTIONS
 *-----------------------------------*/


/*!\egroup*/


/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/* None */

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_T  VtRec[N_REC];
extern uint8_T  FlgResetAdaptParam;

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
void RecMgm_Initialize(void);
void RecMgm_T10ms(void);
void RecMgm_SetExtDiag(uint8_T extdiag_id, uint8_T extdiag_value);
uint8_T MIL_LampStsIsOn(uint8_T diagLineId, const uint8_T REC_MIL);


#endif

