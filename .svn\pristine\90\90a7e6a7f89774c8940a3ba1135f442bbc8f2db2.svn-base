/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Analog In
**  Filename        :  Analog_In_out.h
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  AragonJ
******************************************************************************/
/*****************************************************************************
**
**                        Analog In Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/

#ifndef ANALOGIN_OUT_H
#define ANALOGIN_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"
#include "IgnHEInterface.h"
#include "Adc_out.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/// Max buffer dimension
#define MEDIAN_BUFF_MAX_SIZE 5u

/// Diagnosis thresholds compare strategy
#define THR_D_HL 0u
#define THR_D_L  1u
#define THR_D_H  2u

///Key threshold [mV]
#define KEY_LEVEL_THR ((uint16_T)(2500.0f * (4096.0f / 5000.0f)))
///BankSel threshold OL High [mV]
#define BANKSEL_OL_THRHI ((uint16_T)(3000.0f * (4096.0f / 5000.0f)))
///BankSel threshold OL Low [mV]
#define BANKSEL_OL_THRLOW ((uint16_T)(2000.0f * (4096.0f / 5000.0f)))
///Liveness threshold OL High [mV]
#define LIVENESS_OL_THRHI ((uint16_T)(3000.0f * (4096.0f / 5000.0f)))
///Liveness threshold OL Low [mV]
#define LIVENESS_OL_THRLOW ((uint16_T)(2000.0f * (4096.0f / 5000.0f)))

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* FdbkLiveness voltage raw reading+ */
extern uint16_T VVFdbkLiveness;

/* Voltage ION capacitors */
extern uint16_T  VCharge[PMOS_NUMBER];

/* Battery voltage */
extern uint16_T VBattery;

/* Battery voltage filtered */
extern uint16_T VBatteryF;

/* Voltage NTC temperature ECU 1 (NTC) */
extern uint16_T VTempECU1;

/* Voltage NTC temperature ECU 2 (NTC) */
extern uint16_T VTempECU2;

/* Voltage NTC temperature ECU 3 (CPU) */
extern uint16_T VTempECU3;

/* Supply Coil 1 */
extern uint16_T  VSupplyCoil1ADC;

/* Supply Coil 2 */
extern uint16_T  VSupplyCoil2ADC;

/* Buck 1 */
extern uint16_T  ISupplyCoil1ADC;

/* Buck 2 */
extern uint16_T  ISupplyCoil2ADC;

/* IBattADC */
extern uint16_T IBattADC;

/* VKeySignal */
extern uint16_T VKeySignal;

/* VBankSel */
extern uint16_T VBankSel;


extern CALQUAL CALQUAL_POST uint8_T   RDVBATTATANG;
extern CALQUAL CALQUAL_POST uint16_T  VINFVCOIL0;
extern CALQUAL CALQUAL_POST uint16_T  VSUPVCOIL0;
extern CALQUAL CALQUAL_POST uint16_T  TBVINFVCOIL[5][2];
extern CALQUAL CALQUAL_POST uint16_T  TBVSUPVCOIL[5][2];
extern CALQUAL CALQUAL_POST int16_T   GNVCOIL;
extern CALQUAL CALQUAL_POST int16_T   OFSVCOIL;
extern CALQUAL CALQUAL_POST uint8_T   FLGFOVVCOIL;
extern CALQUAL CALQUAL_POST uint16_T  FOVVCOIL;
extern CALQUAL CALQUAL_POST int16_T   VINFVBUCKCM0;
extern CALQUAL CALQUAL_POST int16_T   VSUPVBUCKCM0;
extern CALQUAL CALQUAL_POST int16_T   VINFVBUCKCM1;
extern CALQUAL CALQUAL_POST int16_T   VSUPVBUCKCM1;
extern CALQUAL CALQUAL_POST int16_T   VINFVBUCKCM2;
extern CALQUAL CALQUAL_POST int16_T   VSUPVBUCKCM2;
extern CALQUAL CALQUAL_POST int16_T   GNVBUCKCM;
extern CALQUAL CALQUAL_POST int16_T   OFSVBUCKCM;
extern CALQUAL CALQUAL_POST uint8_T   FLGFOVVBUCKCM;
extern CALQUAL CALQUAL_POST uint16_T  FOVVBUCKCM;
extern CALQUAL CALQUAL_POST uint8_T   MEDIANLENGTH;
extern CALQUAL CALQUAL_POST int16_T   THRDGVCMON;
extern CALQUAL CALQUAL_POST uint16_T  TIMTSTBKENOFF;
extern CALQUAL CALQUAL_POST uint16_T  TIMTSTBKENON;
extern CALQUAL CALQUAL_POST uint8_T   CNTWAITIGN;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : AnalogIn_Init
**
**   Description:
**    Reset the values of the module variables. The global variables (outputs) 
**    and the static variables (states) are reset to the default values by this function.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_Init(void);

/******************************************************************************
**   Function    : AnalogIn_T10ms
**
**   Description:
**    10ms periodical function
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_T10ms(void);

/******************************************************************************
**   Function    : AnalogIn_T100ms
**
**   Description:
**    100ms periodical function
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_T100ms(void);

/******************************************************************************
**   Function    : AnalogIn_VCapAcq
**
**   Description:
**    Capacitor voltage analog read
**
**   Parameters :
**    idx: Ion circuit index
**    reset: Reset VCharge on VChargeObj value
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_VCapAcq(uint8_T idx, uint8_T reset);

/******************************************************************************
**   Function    : AnalogIn_IBuck1_Acq
**
**   Description:
**    Buck 1 current analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_IBuck1_Acq(void);

/******************************************************************************
**   Function    : AnalogIn_SupplyCoil1_Acq
**
**   Description:
**    Buck 1 Voltage analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_SupplyCoil1_Acq(void);

/******************************************************************************
**   Function    : AnalogIn_IBuck2_Acq
**
**   Description:
**    Buck 2 current analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_IBuck2_Acq(void);

/******************************************************************************
**   Function    : AnalogIn_SupplyCoil2_Acq
**
**   Description:
**    Buck 2 Voltage analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_SupplyCoil2_Acq(void);

/******************************************************************************
**   Function    : AnalogIn_IBatt_Acq
**
**   Description:
**    Battery current analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_IBatt_Acq(void);

/******************************************************************************
**   Function    : AnalogIn_IDE_FS_Liveness_Acq
**
**   Description:
**    Feedback liveness signal analog read
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void AnalogIn_IDE_FS_Liveness_Acq(void);

/******************************************************************************
**   Function    : Median
**
**   Description:
**    Computes arithmetical median
**
**   Parameters :
**    vtInData
**    cntI
**    inData
**    len
**
**   Returns:
**    Median value
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint16_T Median(uint16_T *vtInData, uint8_T *cntI, uint16_T inData, uint8_T len);

/******************************************************************************
**   Function    : NoiseGlitch_Filter
**
**   Description:
**    Noise glitch filter
**
**   Parameters :
**    vtInData
**    cntI
**    oldData
**    glitch
**    inData
**    thrData
**    len
**    rst
**
**   Returns:
**    dataOut
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint16_T NoiseGlitch_Filter(uint16_T *vtInData, uint8_T *cntI, uint16_T *oldData, uint8_T *glitch, uint16_T inData, uint16_T thrData, uint8_T len, uint8_T rst);

/******************************************************************************
**   Function    : AnalogIn_LoadTest
**
**   Description:
**    Load test used for safe path test
**
**   Parameters :
**    IgnIn
**
**   Returns:
**    Load current read
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
uint16_T AnalogIn_LoadTest(uint8_T IgnIn);

#endif

/****************************************************************************
 ****************************************************************************/

