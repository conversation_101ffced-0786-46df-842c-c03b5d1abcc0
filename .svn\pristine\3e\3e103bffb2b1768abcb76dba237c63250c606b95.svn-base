/*****************************************************************************************************************/
/* $HeadURL:: http://172.26.1.29:8080/svn/Rep_GeOr/Coil/EISB6C/Appl/branches/EISB6C_LL_09_GEN/tree/COMMON/INC#$  */
/* $Revision:: 168030                                                                                         $  */
/* $Date:: 2021-07-03 12:57:32 +0200 (sab, 03 lug 2021)                                                       $  */
/* $Author:: GirasoleG                                                                                        $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TLE9278BQX
**  Filename        :  TLE9278BQX_Diag_eep_out.h
**  Created on      :  16-giu-2020 14:04:00
**  Original author :  LanaL
******************************************************************************/
/*****************************************************************************
**
**                        TLE9278BQX Description
**
**
**
******************************************************************************/

#ifndef _TLE9278BQX_COM_EEP_H
#define _TLE9278BQX_COM_EEP_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint16_T EECntSBCResend;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/* None */

#endif /* _TLE9278BQX_COM_EEP_H */

/****************************************************************************
 ****************************************************************************/

