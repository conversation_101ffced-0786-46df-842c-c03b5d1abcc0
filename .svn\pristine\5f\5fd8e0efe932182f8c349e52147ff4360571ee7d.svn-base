/*******************************************************************
 *
 *    DESCRIPTION:
 *
 *    AUTHOR:
 *
 *    HISTORY:
 *
 *******************************************************************/
#ifndef _COMB_CTRL_H_
#define _COMB_CTRL_H_

/** include files **/

/** local definitions **/

/** default settings **/

/** external functions **/

/** external data **/
extern uint16_T VtInjCorrCylTot[8]; /* Correzione da applicare a singolo cilindro. */
extern uint8_T CylBalEn;            /* Cylinder in balance enable */

/** internal functions **/

/** public data **/

/** private data **/

/** public functions **/

void CombCtrl_Init(void);
void CombCtrl_EOA(void);
void CombCtrl_NoSync(void);

#endif

