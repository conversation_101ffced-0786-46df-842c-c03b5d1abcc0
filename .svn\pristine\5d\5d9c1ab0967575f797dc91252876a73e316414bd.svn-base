/****************************************************************************
*
* Copyright � 2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
*
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/
#include <typedefs.h>
#include <isb.h>
#include "isb_cfg.h"

#pragma ghs section data=".data_c0"
// Add here variables to link in the section .data_c0 of DMEM0(core z4)
/*******************************************************************/
#define D1_DELAY_BUCKEN 100u
#define D2_DELAY_CMDOUT 400u //sommato a D1_DELAY_BUCKEN

#define PMOS_TIMEON     1500u //settato al BUCKEN

//#define NMOS_TIMEON     1000u
//#define MOS_DEAD_BAND   40u
//#define PMOS_TIMEOFF    NMOS_TIMEON + 2*MOS_DEAD_BAND

#define EPWS_OFF 0u
#define EPWS_ON  1u

#define EPWS_LAST_PULSE_DURATION    250u
#define EPWS_TIMEOUT                2500u           

#define EPWS_PH1_PULSENMB       1U
#define EPWS_PH1_PERIOD         400U
#define EPWS_PH1_DUTY           0U

#define EPWS_PH2_PULSENMB       2U
#define EPWS_PH2_PERIOD         160U
#define EPWS_PH2_DUTY           80U

#define EPWS_PH3_PULSENMB       1U
#define EPWS_PH3_PERIOD         160U
#define EPWS_PH3_DUTY           80U

#define EPWS_PH4_PULSENMB       1U
#define EPWS_PH4_PERIOD         160U
#define EPWS_PH4_DUTY           80U

#define UNUSED  0u

#define CYL_NUMBER 8u

#define CYL0_IDX 0U
#define CYL1_IDX 1U
#define CYL2_IDX 2U
#define CYL3_IDX 3U
#define CYL4_IDX 4U
#define CYL5_IDX 5U
#define CYL6_IDX 6U
#define CYL7_IDX 7U

/*******************************************************************/

/******************************************************************
 *                              CYLINDER 0
 *
 *******************************************************************/
ISB_COIL_SUPPLY cyl0_coil_d1_config = {
    D1_DELAY_BUCKEN,
    UNUSED,
    UNUSED
};

ISB_COIL_SUPPLY cyl0_coil_d2_config = {
    BUCK_CH_D2_DURATION,
    BUCK_CH_D2_PERIOD,
    BUCK_CH_D2_DUTY
};

ISB_COIL_SUPPLY cyl0_coil_d3_config = {
    BUCK_CH_D3_DURATION,
    BUCK_CH_D3_PERIOD,
    BUCK_CH_D3_DUTY
};

ISB_COIL_SUPPLY cyl0_coil_d4_config = {
    BUCK_CH_D4_DURATION,
    BUCK_CH_D4_PERIOD,
    BUCK_CH_D4_DUTY
};

ISB_COIL_SUPPLY cyl0_coil_d5_config = {
    BUCK_CH_D5_DURATION,
    BUCK_CH_D5_PERIOD,
    BUCK_CH_D5_DUTY
};

ISB_COIL_SUPPLY cyl0_coil_d6_config = {
    BUCK_DISCH_D6_DURATION,
    BUCK_DISCH_D6_PERIOD,
    BUCK_DISCH_D6_DUTY
};

ISB_COIL_SUPPLY cyl0_coil_d7_config = {
    BUCK_DISCH_D7_DURATION,
    BUCK_DISCH_D7_PERIOD,
    BUCK_DISCH_D7_DUTY
};

ISB_COIL_SUPPLY cyl0_coil_d8_config = {
    BUCK_DISCH_D8_DURATION,
    BUCK_DISCH_D8_PERIOD,
    BUCK_DISCH_D8_DUTY
};

ISB_EPWS_Config cyl0_epws1_config = {
    EPWS_PH1_PULSENMB,
    EPWS_PH1_PERIOD,
    EPWS_PH1_DUTY
};

ISB_EPWS_Config cyl0_epws2_config = {
    EPWS_PH2_PULSENMB,
    EPWS_PH2_PERIOD,
    EPWS_PH2_DUTY
};

ISB_EPWS_Config cyl0_epws3_config = {
    EPWS_PH3_PULSENMB,
    EPWS_PH3_PERIOD,
    EPWS_PH3_DUTY
};

ISB_EPWS_Config cyl0_epws4_config = {
    EPWS_PH4_PULSENMB,
    EPWS_PH4_PERIOD,
    EPWS_PH4_DUTY
};


ISB_CYLINDER_Config cylinder_0_config = {
    CYL0_IDX,             // cylinder index
    &PSMD1,               // PSM driver used for Data swap
    PSM_CHANNEL0,         // PSM channel
    &MCSD1,               // MCS driver used for Input signals
    MCS_CHANNEL0,         // MCS Channel for cylinder
    &MCSD3,               // MCS driver used for MOS signals
    MCS_CHANNEL0,         // MCS channel for MOS management
    &TIMD2,               // TIM driver used as Input signal
    TIM_CHANNEL0,         // TIM Channel
    D2_DELAY_CMDOUT,      // Output delay
    ISB_USE_EPWS,         // epws
    {// EPWS configuration phase
        &cyl0_epws1_config,
        &cyl0_epws2_config,
        &cyl0_epws3_config,
        &cyl0_epws4_config,
    },
    EPWS_LAST_PULSE_DURATION,   // EPWS LAST PULSE
    EPWS_TIMEOUT,               // EPWS TIMEOUT
    PMOS_TIMEON,                // PMOS duration
    MOS_TIMEOUT,                // MOS timeout
    BUCK_CHARGE_PH_NUMBER,      // loading steps
    BUCK_DISCHARGE_PH_NUMBER,   // unloading steps
    { // coils supply remodulation
        &cyl0_coil_d1_config,
        &cyl0_coil_d2_config,
        &cyl0_coil_d3_config,
        &cyl0_coil_d4_config,
        &cyl0_coil_d5_config,
        &cyl0_coil_d6_config,
        &cyl0_coil_d7_config,
        &cyl0_coil_d8_config,
    }
};

/******************************************************************
 *                              CYLINDER 1
 *
 *******************************************************************/
ISB_COIL_SUPPLY cyl1_coil_d1_config = {
    D1_DELAY_BUCKEN,
    UNUSED,
    UNUSED
};

ISB_COIL_SUPPLY cyl1_coil_d2_config = {
    BUCK_CH_D2_DURATION,
    BUCK_CH_D2_PERIOD,
    BUCK_CH_D2_DUTY
};

ISB_COIL_SUPPLY cyl1_coil_d3_config = {
    BUCK_CH_D3_DURATION,
    BUCK_CH_D3_PERIOD,
    BUCK_CH_D3_DUTY
};

ISB_COIL_SUPPLY cyl1_coil_d4_config = {
    BUCK_CH_D4_DURATION,
    BUCK_CH_D4_PERIOD,
    BUCK_CH_D4_DUTY
};

ISB_COIL_SUPPLY cyl1_coil_d5_config = {
    BUCK_CH_D5_DURATION,
    BUCK_CH_D5_PERIOD,
    BUCK_CH_D5_DUTY
};

ISB_COIL_SUPPLY cyl1_coil_d6_config = {
    BUCK_DISCH_D6_DURATION,
    BUCK_DISCH_D6_PERIOD,
    BUCK_DISCH_D6_DUTY
};

ISB_COIL_SUPPLY cyl1_coil_d7_config = {
    BUCK_DISCH_D7_DURATION,
    BUCK_DISCH_D7_PERIOD,
    BUCK_DISCH_D7_DUTY
};

ISB_COIL_SUPPLY cyl1_coil_d8_config = {
    BUCK_DISCH_D8_DURATION,
    BUCK_DISCH_D8_PERIOD,
    BUCK_DISCH_D8_DUTY
};


ISB_EPWS_Config cyl1_epws1_config = {
    EPWS_PH1_PULSENMB,
    EPWS_PH1_PERIOD,
    EPWS_PH1_DUTY
};

ISB_EPWS_Config cyl1_epws2_config = {
    EPWS_PH2_PULSENMB,
    EPWS_PH2_PERIOD,
    EPWS_PH2_DUTY
};

ISB_EPWS_Config cyl1_epws3_config = {
    EPWS_PH3_PULSENMB,
    EPWS_PH3_PERIOD,
    EPWS_PH3_DUTY
};

ISB_EPWS_Config cyl1_epws4_config = {
    EPWS_PH4_PULSENMB,
    EPWS_PH4_PERIOD,
    EPWS_PH4_DUTY
};

ISB_CYLINDER_Config cylinder_1_config = {
    CYL1_IDX,              // cylinder index
    &PSMD1,               // PSM driver used for Data swap
    PSM_CHANNEL1,         // PSM channel
    &MCSD2,               // MCS driver used for Input signals
    MCS_CHANNEL0,         // MCS Channel for cylinder
    &MCSD3,               // MCS driver used for MOS signals
    MCS_CHANNEL2,         // MCS channel for MOS management
    &TIMD2,               // TIM driver used as Input signal
    TIM_CHANNEL1,         // TIM Channel
    D2_DELAY_CMDOUT,      // Output delay
    ISB_USE_EPWS,         // epws
    {// EPWS configuration phase
        &cyl1_epws1_config,
        &cyl1_epws2_config,
        &cyl1_epws3_config,
        &cyl1_epws4_config,
    },
    EPWS_LAST_PULSE_DURATION,   // EPWS LAST PULSE
    EPWS_TIMEOUT,               // EPWS TIMEOUT
    PMOS_TIMEON,                // PMOS duration
    MOS_TIMEOUT,                // MOS timeout
    BUCK_CHARGE_PH_NUMBER,      // loading steps
    BUCK_DISCHARGE_PH_NUMBER,   // unloading steps
    { // coils supply remodulation
        &cyl1_coil_d1_config,
        &cyl1_coil_d2_config,
        &cyl1_coil_d3_config,
        &cyl1_coil_d4_config,
        &cyl1_coil_d5_config,
        &cyl1_coil_d6_config,
        &cyl1_coil_d7_config,
        &cyl1_coil_d8_config,
    }
};

/******************************************************************
 *                              CYLINDER 2
 *
 *******************************************************************/
ISB_COIL_SUPPLY cyl2_coil_d1_config = {
    D1_DELAY_BUCKEN,
    UNUSED,
    UNUSED
};

ISB_COIL_SUPPLY cyl2_coil_d2_config = { //10
    BUCK_CH_D2_DURATION,
    BUCK_CH_D2_PERIOD,
    BUCK_CH_D2_DUTY
};

ISB_COIL_SUPPLY cyl2_coil_d3_config = {
    BUCK_CH_D3_DURATION,
    BUCK_CH_D3_PERIOD,
    BUCK_CH_D3_DUTY
};

ISB_COIL_SUPPLY cyl2_coil_d4_config = {
    BUCK_CH_D4_DURATION,
    BUCK_CH_D4_PERIOD,
    BUCK_CH_D4_DUTY
};

ISB_COIL_SUPPLY cyl2_coil_d5_config = {
    BUCK_CH_D5_DURATION,
    BUCK_CH_D5_PERIOD,
    BUCK_CH_D5_DUTY
};

ISB_COIL_SUPPLY cyl2_coil_d6_config = {
    BUCK_DISCH_D6_DURATION,
    BUCK_DISCH_D6_PERIOD,
    BUCK_DISCH_D6_DUTY
};

ISB_COIL_SUPPLY cyl2_coil_d7_config = {
    BUCK_DISCH_D7_DURATION,
    BUCK_DISCH_D7_PERIOD,
    BUCK_DISCH_D7_DUTY
};

ISB_COIL_SUPPLY cyl2_coil_d8_config = {
    BUCK_DISCH_D8_DURATION,
    BUCK_DISCH_D8_PERIOD,
    BUCK_DISCH_D8_DUTY
};

ISB_EPWS_Config cyl2_epws1_config = {
    EPWS_PH1_PULSENMB,
    EPWS_PH1_PERIOD,
    EPWS_PH1_DUTY
};

ISB_EPWS_Config cyl2_epws2_config = {
    EPWS_PH2_PULSENMB,
    EPWS_PH2_PERIOD,
    EPWS_PH2_DUTY
};

ISB_EPWS_Config cyl2_epws3_config = {
    EPWS_PH3_PULSENMB,
    EPWS_PH3_PERIOD,
    EPWS_PH3_DUTY
};

ISB_EPWS_Config cyl2_epws4_config = {
    EPWS_PH4_PULSENMB,
    EPWS_PH4_PERIOD,
    EPWS_PH4_DUTY
};

ISB_CYLINDER_Config cylinder_2_config = {
    CYL2_IDX,             // cylinder index
    &PSMD1,               // PSM driver used for Data swap
    PSM_CHANNEL2,         // PSM channel
    &MCSD1,               // MCS driver used for Input signals
    MCS_CHANNEL1,         // MCS Channel for cylinder
    &MCSD3,               // MCS driver used for MOS signals
    MCS_CHANNEL1,         // MCS channel for MOS management
    &TIMD2,               // TIM driver used as Input signal
    TIM_CHANNEL2,         // TIM Channel
    D2_DELAY_CMDOUT,      // Output delay
    ISB_USE_EPWS,         // epws
    {// EPWS configuration phase
        &cyl2_epws1_config,
        &cyl2_epws2_config,
        &cyl2_epws3_config,
        &cyl2_epws4_config,
    },
    EPWS_LAST_PULSE_DURATION,   // EPWS LAST PULSE
    EPWS_TIMEOUT,               // EPWS TIMEOUT
    PMOS_TIMEON,                // PMOS duration 
    MOS_TIMEOUT,                // MOS timeout
    BUCK_CHARGE_PH_NUMBER,      // loading steps
    BUCK_DISCHARGE_PH_NUMBER,   // unloading steps
    { // coils supply remodulation
        &cyl2_coil_d1_config,
        &cyl2_coil_d2_config,
        &cyl2_coil_d3_config,
        &cyl2_coil_d4_config,
        &cyl2_coil_d5_config,
        &cyl2_coil_d6_config,
        &cyl2_coil_d7_config,
        &cyl2_coil_d8_config,
    }
};

/******************************************************************
 *                              CYLINDER 3
 *
 *******************************************************************/
ISB_COIL_SUPPLY cyl3_coil_d1_config = {
    D1_DELAY_BUCKEN,
    UNUSED,
    UNUSED
};

ISB_COIL_SUPPLY cyl3_coil_d2_config = {
    BUCK_CH_D2_DURATION,
    BUCK_CH_D2_PERIOD,
    BUCK_CH_D2_DUTY
};

ISB_COIL_SUPPLY cyl3_coil_d3_config = {
    BUCK_CH_D3_DURATION,
    BUCK_CH_D3_PERIOD,
    BUCK_CH_D3_DUTY
};

ISB_COIL_SUPPLY cyl3_coil_d4_config = {
    BUCK_CH_D4_DURATION,
    BUCK_CH_D4_PERIOD,
    BUCK_CH_D4_DUTY
};

ISB_COIL_SUPPLY cyl3_coil_d5_config = {
    BUCK_CH_D5_DURATION,
    BUCK_CH_D5_PERIOD,
    BUCK_CH_D5_DUTY
};

ISB_COIL_SUPPLY cyl3_coil_d6_config = {
    BUCK_DISCH_D6_DURATION,
    BUCK_DISCH_D6_PERIOD,
    BUCK_DISCH_D6_DUTY
};

ISB_COIL_SUPPLY cyl3_coil_d7_config = {
    BUCK_DISCH_D7_DURATION,
    BUCK_DISCH_D7_PERIOD,
    BUCK_DISCH_D7_DUTY
};

ISB_COIL_SUPPLY cyl3_coil_d8_config = {
    BUCK_DISCH_D8_DURATION,
    BUCK_DISCH_D8_PERIOD,
    BUCK_DISCH_D8_DUTY
};
ISB_EPWS_Config cyl3_epws1_config = {
    EPWS_PH1_PULSENMB,
    EPWS_PH1_PERIOD,
    EPWS_PH1_DUTY
};

ISB_EPWS_Config cyl3_epws2_config = {
    EPWS_PH2_PULSENMB,
    EPWS_PH2_PERIOD,
    EPWS_PH2_DUTY
};

ISB_EPWS_Config cyl3_epws3_config = {
    EPWS_PH3_PULSENMB,
    EPWS_PH3_PERIOD,
    EPWS_PH3_DUTY
};

ISB_EPWS_Config cyl3_epws4_config = {
    EPWS_PH4_PULSENMB,
    EPWS_PH4_PERIOD,
    EPWS_PH4_DUTY
};

ISB_CYLINDER_Config cylinder_3_config = {
    CYL3_IDX,             // cylinder index
    &PSMD1,               // PSM driver used for Data swap
    PSM_CHANNEL3,         // PSM channel
    &MCSD2,               // MCS driver used for Input signals
    MCS_CHANNEL1,         // MCS Channel for cylinder
    &MCSD3,               // MCS driver used for MOS signals
    MCS_CHANNEL0,         // MCS channel for MOS management
    &TIMD2,               // TIM driver used as Input signal
    TIM_CHANNEL3,         // TIM Channel
    D2_DELAY_CMDOUT,      // Output delay
    ISB_USE_EPWS,         // epws
    {// EPWS configuration phase
        &cyl3_epws1_config,
        &cyl3_epws2_config,
        &cyl3_epws3_config,
        &cyl3_epws4_config,
    },
    EPWS_LAST_PULSE_DURATION,   // EPWS LAST PULSE
    EPWS_TIMEOUT,               // EPWS TIMEOUT
    PMOS_TIMEON,                // PMOS duration
    MOS_TIMEOUT,                // MOS timeout
    BUCK_CHARGE_PH_NUMBER,      // loading steps
    BUCK_DISCHARGE_PH_NUMBER,   // unloading steps
    { // coils supply remodulation
        &cyl3_coil_d1_config,
        &cyl3_coil_d2_config,
        &cyl3_coil_d3_config,
        &cyl3_coil_d4_config,
        &cyl3_coil_d5_config,
        &cyl3_coil_d6_config,
        &cyl3_coil_d7_config,
        &cyl3_coil_d8_config,
    }
};

/******************************************************************
 *                              CYLINDER 4
 *
 *******************************************************************/
ISB_COIL_SUPPLY cyl4_coil_d1_config = {
    D1_DELAY_BUCKEN,
    UNUSED,
    UNUSED
};

ISB_COIL_SUPPLY cyl4_coil_d2_config = {
    BUCK_CH_D2_DURATION,
    BUCK_CH_D2_PERIOD,
    BUCK_CH_D2_DUTY
};

ISB_COIL_SUPPLY cyl4_coil_d3_config = {
    BUCK_CH_D3_DURATION,
    BUCK_CH_D3_PERIOD,
    BUCK_CH_D3_DUTY
};

ISB_COIL_SUPPLY cyl4_coil_d4_config = {
    BUCK_CH_D4_DURATION,
    BUCK_CH_D4_PERIOD,
    BUCK_CH_D4_DUTY
};

ISB_COIL_SUPPLY cyl4_coil_d5_config = {
    BUCK_CH_D5_DURATION,
    BUCK_CH_D5_PERIOD,
    BUCK_CH_D5_DUTY
};

ISB_COIL_SUPPLY cyl4_coil_d6_config = {
    BUCK_DISCH_D6_DURATION,
    BUCK_DISCH_D6_PERIOD,
    BUCK_DISCH_D6_DUTY
};

ISB_COIL_SUPPLY cyl4_coil_d7_config = {
    BUCK_DISCH_D7_DURATION,
    BUCK_DISCH_D7_PERIOD,
    BUCK_DISCH_D7_DUTY
};

ISB_COIL_SUPPLY cyl4_coil_d8_config = {
    BUCK_DISCH_D8_DURATION,
    BUCK_DISCH_D8_PERIOD,
    BUCK_DISCH_D8_DUTY
};

ISB_EPWS_Config cyl4_epws1_config = {
    EPWS_PH1_PULSENMB,
    EPWS_PH1_PERIOD,
    EPWS_PH1_DUTY
};

ISB_EPWS_Config cyl4_epws2_config = {
    EPWS_PH2_PULSENMB,
    EPWS_PH2_PERIOD,
    EPWS_PH2_DUTY
};

ISB_EPWS_Config cyl4_epws3_config = {
    EPWS_PH3_PULSENMB,
    EPWS_PH3_PERIOD,
    EPWS_PH3_DUTY
};

ISB_EPWS_Config cyl4_epws4_config = {
    EPWS_PH4_PULSENMB,
    EPWS_PH4_PERIOD,
    EPWS_PH4_DUTY
};

ISB_CYLINDER_Config cylinder_4_config = {
    CYL4_IDX,             // cylinder index
    &PSMD1,               // PSM driver used for Data swap
    PSM_CHANNEL4,         // PSM channel
    &MCSD1,               // MCS driver used for Input signals
    MCS_CHANNEL2,         // MCS Channel for cylinder
    &MCSD3,               // MCS driver used for MOS signals
    MCS_CHANNEL2,         // MCS channel for MOS management
    &TIMD2,               // TIM driver used as Input signal
    TIM_CHANNEL4,         // TIM Channel
    D2_DELAY_CMDOUT,      // Output delay
    ISB_USE_EPWS,         // epws
    {// EPWS configuration phase
        &cyl4_epws1_config,
        &cyl4_epws2_config,
        &cyl4_epws3_config,
        &cyl4_epws4_config,
    },
    EPWS_LAST_PULSE_DURATION,   // EPWS LAST PULSE
    EPWS_TIMEOUT,               // EPWS TIMEOUT
    PMOS_TIMEON,                // PMOS duration
    MOS_TIMEOUT,                // MOS timeout
    BUCK_CHARGE_PH_NUMBER,      // loading steps
    BUCK_DISCHARGE_PH_NUMBER,   // unloading steps
    { // coils supply remodulation
        &cyl4_coil_d1_config,
        &cyl4_coil_d2_config,
        &cyl4_coil_d3_config,
        &cyl4_coil_d4_config,
        &cyl4_coil_d5_config,
        &cyl4_coil_d6_config,
        &cyl4_coil_d7_config,
        &cyl4_coil_d8_config,
    }
};

/******************************************************************
 *                              CYLINDER 5
 *
 *******************************************************************/
ISB_COIL_SUPPLY cyl5_coil_d1_config = {
    D1_DELAY_BUCKEN,
    UNUSED,
    UNUSED
};

ISB_COIL_SUPPLY cyl5_coil_d2_config = {
    BUCK_CH_D2_DURATION,
    BUCK_CH_D2_PERIOD,
    BUCK_CH_D2_DUTY
};

ISB_COIL_SUPPLY cyl5_coil_d3_config = {
    BUCK_CH_D3_DURATION,
    BUCK_CH_D3_PERIOD,
    BUCK_CH_D3_DUTY
};

ISB_COIL_SUPPLY cyl5_coil_d4_config = {
    BUCK_CH_D4_DURATION,
    BUCK_CH_D4_PERIOD,
    BUCK_CH_D4_DUTY
};

ISB_COIL_SUPPLY cyl5_coil_d5_config = {
    BUCK_CH_D5_DURATION,
    BUCK_CH_D5_PERIOD,
    BUCK_CH_D5_DUTY
};

ISB_COIL_SUPPLY cyl5_coil_d6_config = {
    BUCK_DISCH_D6_DURATION,
    BUCK_DISCH_D6_PERIOD,
    BUCK_DISCH_D6_DUTY
};

ISB_COIL_SUPPLY cyl5_coil_d7_config = {
    BUCK_DISCH_D7_DURATION,
    BUCK_DISCH_D7_PERIOD,
    BUCK_DISCH_D7_DUTY
};

ISB_COIL_SUPPLY cyl5_coil_d8_config = {
    BUCK_DISCH_D8_DURATION,
    BUCK_DISCH_D8_PERIOD,
    BUCK_DISCH_D8_DUTY
};

ISB_EPWS_Config cyl5_epws1_config = {
    EPWS_PH1_PULSENMB,
    EPWS_PH1_PERIOD,
    EPWS_PH1_DUTY
};

ISB_EPWS_Config cyl5_epws2_config = {
    EPWS_PH2_PULSENMB,
    EPWS_PH2_PERIOD,
    EPWS_PH2_DUTY
};

ISB_EPWS_Config cyl5_epws3_config = {
    EPWS_PH3_PULSENMB,
    EPWS_PH3_PERIOD,
    EPWS_PH3_DUTY
};

ISB_EPWS_Config cyl5_epws4_config = {
    EPWS_PH4_PULSENMB,
    EPWS_PH4_PERIOD,
    EPWS_PH4_DUTY
};

ISB_CYLINDER_Config cylinder_5_config = {
    CYL5_IDX,             // cylinder index
    &PSMD1,               // PSM driver used for Data swap
    PSM_CHANNEL5,         // PSM channel
    &MCSD2,               // MCS driver used for Input signals
    MCS_CHANNEL2,         // MCS Channel for cylinder
    &MCSD3,               // MCS driver used for MOS signals
    MCS_CHANNEL1,         // MCS channel for MOS management
    &TIMD2,               // TIM driver used as Input signal
    TIM_CHANNEL5,         // TIM Channel
    D2_DELAY_CMDOUT,      // Output delay
    ISB_USE_EPWS,         // epws
    {// EPWS configuration phase
        &cyl5_epws1_config,
        &cyl5_epws2_config,
        &cyl5_epws3_config,
        &cyl5_epws4_config,
    },
    EPWS_LAST_PULSE_DURATION,    // EPWS LAST PULSE
    EPWS_TIMEOUT,                // EPWS TIMEOUT
    PMOS_TIMEON,                 // PMOS duration
    MOS_TIMEOUT,                 // MOS timeout
    BUCK_CHARGE_PH_NUMBER,       // loading steps
    BUCK_DISCHARGE_PH_NUMBER,    // unloading steps
    { // coils supply remodulation
        &cyl5_coil_d1_config,
        &cyl5_coil_d2_config,
        &cyl5_coil_d3_config,
        &cyl5_coil_d4_config,
        &cyl5_coil_d5_config,
        &cyl5_coil_d6_config,
        &cyl5_coil_d7_config,
        &cyl5_coil_d8_config,
    },
};

/******************************************************************
 *                              CYLINDER 6
 *
 *******************************************************************/
ISB_COIL_SUPPLY cyl6_coil_d1_config = {
    D1_DELAY_BUCKEN,
    UNUSED,
    UNUSED
};

ISB_COIL_SUPPLY cyl6_coil_d2_config = { //10
    BUCK_CH_D2_DURATION,
    BUCK_CH_D2_PERIOD,
    BUCK_CH_D2_DUTY
};

ISB_COIL_SUPPLY cyl6_coil_d3_config = {
    BUCK_CH_D3_DURATION,
    BUCK_CH_D3_PERIOD,
    BUCK_CH_D3_DUTY
};

ISB_COIL_SUPPLY cyl6_coil_d4_config = {
    BUCK_CH_D4_DURATION,
    BUCK_CH_D4_PERIOD,
    BUCK_CH_D4_DUTY
};

ISB_COIL_SUPPLY cyl6_coil_d5_config = {
    BUCK_CH_D5_DURATION,
    BUCK_CH_D5_PERIOD,
    BUCK_CH_D5_DUTY
};

ISB_COIL_SUPPLY cyl6_coil_d6_config = {
    BUCK_DISCH_D6_DURATION,
    BUCK_DISCH_D6_PERIOD,
    BUCK_DISCH_D6_DUTY
};

ISB_COIL_SUPPLY cyl6_coil_d7_config = {
    BUCK_DISCH_D7_DURATION,
    BUCK_DISCH_D7_PERIOD,
    BUCK_DISCH_D7_DUTY
};

ISB_COIL_SUPPLY cyl6_coil_d8_config = {
    BUCK_DISCH_D8_DURATION,
    BUCK_DISCH_D8_PERIOD,
    BUCK_DISCH_D8_DUTY
};

ISB_EPWS_Config cyl6_epws1_config = {
    EPWS_PH1_PULSENMB,
    EPWS_PH1_PERIOD,
    EPWS_PH1_DUTY
};

ISB_EPWS_Config cyl6_epws2_config = {
    EPWS_PH2_PULSENMB,
    EPWS_PH2_PERIOD,
    EPWS_PH2_DUTY
};

ISB_EPWS_Config cyl6_epws3_config = {
    EPWS_PH3_PULSENMB,
    EPWS_PH3_PERIOD,
    EPWS_PH3_DUTY
};

ISB_EPWS_Config cyl6_epws4_config = {
    EPWS_PH4_PULSENMB,
    EPWS_PH4_PERIOD,
    EPWS_PH4_DUTY
};


ISB_CYLINDER_Config cylinder_6_config = {
    CYL6_IDX,             // cylinder index
    &PSMD1,               // PSM driver used for Data swap
    PSM_CHANNEL6,         // PSM channel
    &MCSD1,               // MCS driver used for Input signals
    MCS_CHANNEL3,         // MCS Channel for cylinder
    &MCSD3,               // MCS driver used for MOS signals
    MCS_CHANNEL3,         // MCS channel for MOS management
    &TIMD2,               // TIM driver used as Input signal
    TIM_CHANNEL6,         // TIM Channel
    D2_DELAY_CMDOUT,      // Output delay
    ISB_USE_EPWS,         // epws
    {// EPWS configuration phase
        &cyl6_epws1_config,
        &cyl6_epws2_config,
        &cyl6_epws3_config,
        &cyl6_epws4_config,
    },
    EPWS_LAST_PULSE_DURATION,    // EPWS LAST PULSE
    EPWS_TIMEOUT,                // EPWS TIMEOUT
    PMOS_TIMEON,                 // PMOS duration
    MOS_TIMEOUT,                 // MOS timeout
    BUCK_CHARGE_PH_NUMBER,       // loading steps
    BUCK_DISCHARGE_PH_NUMBER,    // unloading steps
    { // coils supply remodulation
        &cyl6_coil_d1_config,
        &cyl6_coil_d2_config,
        &cyl6_coil_d3_config,
        &cyl6_coil_d4_config,
        &cyl6_coil_d5_config,
        &cyl6_coil_d6_config,
        &cyl6_coil_d7_config,
        &cyl6_coil_d8_config,
    }
};


/******************************************************************
 *                              CYLINDER 7
 *
 *******************************************************************/
ISB_COIL_SUPPLY cyl7_coil_d1_config = {
    D1_DELAY_BUCKEN,
    UNUSED,
    UNUSED
};

ISB_COIL_SUPPLY cyl7_coil_d2_config = { //10
    BUCK_CH_D2_DURATION,
    BUCK_CH_D2_PERIOD,
    BUCK_CH_D2_DUTY
};

ISB_COIL_SUPPLY cyl7_coil_d3_config = {
    BUCK_CH_D3_DURATION,
    BUCK_CH_D3_PERIOD,
    BUCK_CH_D3_DUTY
};

ISB_COIL_SUPPLY cyl7_coil_d4_config = {
    BUCK_CH_D4_DURATION,
    BUCK_CH_D4_PERIOD,
    BUCK_CH_D4_DUTY
};

ISB_COIL_SUPPLY cyl7_coil_d5_config = {
    BUCK_CH_D5_DURATION,
    BUCK_CH_D5_PERIOD,
    BUCK_CH_D5_DUTY
};

ISB_COIL_SUPPLY cyl7_coil_d6_config = {
    BUCK_DISCH_D6_DURATION,
    BUCK_DISCH_D6_PERIOD,
    BUCK_DISCH_D6_DUTY
};

ISB_COIL_SUPPLY cyl7_coil_d7_config = {
    BUCK_DISCH_D7_DURATION,
    BUCK_DISCH_D7_PERIOD,
    BUCK_DISCH_D7_DUTY
};

ISB_COIL_SUPPLY cyl7_coil_d8_config = {
    BUCK_DISCH_D8_DURATION,
    BUCK_DISCH_D8_PERIOD,
    BUCK_DISCH_D8_DUTY
};

ISB_EPWS_Config cyl7_epws1_config = {
    EPWS_PH1_PULSENMB,
    EPWS_PH1_PERIOD,
    EPWS_PH1_DUTY
};

ISB_EPWS_Config cyl7_epws2_config = {
    EPWS_PH2_PULSENMB,
    EPWS_PH2_PERIOD,
    EPWS_PH2_DUTY
};

ISB_EPWS_Config cyl7_epws3_config = {
    EPWS_PH3_PULSENMB,
    EPWS_PH3_PERIOD,
    EPWS_PH3_DUTY
};

ISB_EPWS_Config cyl7_epws4_config = {
    EPWS_PH4_PULSENMB,
    EPWS_PH4_PERIOD,
    EPWS_PH4_DUTY
};


ISB_CYLINDER_Config cylinder_7_config = {
    CYL7_IDX,             // cylinder index
    &PSMD1,               // PSM driver used for Data swap
    PSM_CHANNEL7,         // PSM channel
    &MCSD2,               // MCS driver used for Input signals
    MCS_CHANNEL3,         // MCS Channel for cylinder
    &MCSD3,               // MCS driver used for MOS signals
    MCS_CHANNEL3,         // MCS channel for MOS management
    &TIMD2,               // TIM driver used as Input signal
    TIM_CHANNEL7,         // TIM Channel
    D2_DELAY_CMDOUT,      // Output delay
    ISB_USE_EPWS,         // epws
    {// EPWS configuration phase
        &cyl7_epws1_config,
        &cyl7_epws2_config,
        &cyl7_epws3_config,
        &cyl7_epws4_config,
    },
    EPWS_LAST_PULSE_DURATION,    // EPWS LAST PULSE
    EPWS_TIMEOUT,                // EPWS TIMEOUT
    PMOS_TIMEON,                 // PMOS duration
    MOS_TIMEOUT,                 // MOS timeout
    BUCK_CHARGE_PH_NUMBER,       // loading steps
    BUCK_DISCHARGE_PH_NUMBER,    // unloading steps
    { // coils supply remodulation
        &cyl7_coil_d1_config,
        &cyl7_coil_d2_config,
        &cyl7_coil_d3_config,
        &cyl7_coil_d4_config,
        &cyl7_coil_d5_config,
        &cyl7_coil_d6_config,
        &cyl7_coil_d7_config,
        &cyl7_coil_d8_config,
    }
};

/******************************************************************
 *                        SYSTEM DATA STRUCTURE
 *
 *******************************************************************/
ISB_Config eisb_config = {
    CYL_NUMBER,
    {
        &cylinder_0_config,
        &cylinder_1_config,
        &cylinder_2_config,
        &cylinder_3_config,
        &cylinder_4_config,
        &cylinder_5_config,
        &cylinder_6_config,
        &cylinder_7_config,
    }
};
#pragma ghs section data=default
// Add here variables to link in the section .data of SRAM



/****************************************************************************
 ****************************************************************************/

