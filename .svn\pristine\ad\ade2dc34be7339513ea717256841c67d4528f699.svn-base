/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  BuckDiagMgm
**  Filename        :  BuckDiagMgm.h
**  Created on      :  31-mar-2021 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifndef _BUCKDIAGMGM_H_
#define _BUCKDIAGMGM_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "BuckDiagMgm_out.h"
#include "AnalogIn_out.h"
#include "Port_out.h"
#include "diagmgm_out.h"
#include "gtm_eisb_out.h"
#include "TLE9278BQX_IOs_out.h"
#include "mathlib.h"
#include "syncmgm_out.h"
#include "CanMgmIn_out.h"
#include "PwrMgm_out.h"
#include "active_diag_out.h"
#include "timing_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/* VtStBuckFault */
#define NO_BUCK_FAULT               (0u)
#define BUCK_MON_FAULT_OFF          (1u)
#define BATT_MON_FAULT_OFF          (2u)
#define BUCK_MON_FAULT_ON           (3u)
#define BATT_MON_FAULT_ON           (4u)

#define IBATT_SAMPLES           (5U)
#define SAMPLES_CONFAULT        (IBATT_SAMPLES - 2U) // Samples used for fault confirmation

#define MAX_BUCK_DIAG_LOOP          250u

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* None */


/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST uint16_T BKTHRIBATTDIAG[BKTHRIBATTDIAG_dim];
extern CALQUAL CALQUAL_POST uint16_T BKTHRISUPPLYCOILDIAG[BKTHRISUPPLYCOILDIAG_dim];
extern CALQUAL CALQUAL_POST uint16_T VTTHRIBATTDIAG[BKTHRIBATTDIAG_dim];
extern CALQUAL CALQUAL_POST uint16_T VTTHRISUPPLYCOILDIAG[BKTHRISUPPLYCOILDIAG_dim];
extern CALQUAL CALQUAL_POST uint16_T THRVSUPPLYCOILDIAG;
extern CALQUAL CALQUAL_POST uint16_T THRIBATTPWRON;

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : BuckDiagMgm_Buck0
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
static void BuckDiagMgm_Buck0(void);

/******************************************************************************
**   Function    : BuckDiagMgm_Buck1
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
static void BuckDiagMgm_Buck1(void);

/******************************************************************************
**   Function    : BuckDiagMgm_PeriodicCheck
**
**   Description:
**    Periodic check in case of no trigger
**
**   Parameters :
**    BuckIdx
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void BuckDiagMgm_PeriodicCheck(uint8_T buckIdx);

#endif
