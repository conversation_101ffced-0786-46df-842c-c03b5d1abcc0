/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#include "sys.h"
#include "Flash_out.h"


#ifdef  _TEST_FLASH_

static vuint8_T  FLASHTEST_DOERASE;
static vuint8_T  FLASHTEST_DOBLANKCHECK;
static vuint8_T  FLASHTEST_DOPROGRAM;
static vuint8_T  FLASHTEST_DOCHECKSUM;
static vuint32_T FLASHTEST_STARTADDRESS  = 0x800000u;
static vuint32_T FLASHTEST_SIZE = 0x100u;
static int16_T   FLASHTEST_RETURNCODE;
static int16_T   FLASHTEST_RETURNCODE_BLANCKCHECK;
static int16_T   FLASHTEST_RETURNCODE_CHECKSUM;
static int16_T   FLASHTEST_CB;
static vuint32_T FLASHTESTCHK_SIZE = 0x1000u;
static uint8_T   FLASHTEST_DOERASECBK = FLASH_ERASE_NO_CBK ;

static uint8_T   FLASHTEST_SOURCE[0x100]  __attribute__ ((aligned(32)));
static uint32_T  FLASHTEST_CHECKSUMVALUE = 0u;


void FLASH_TestCB(void)
{
    FLASHTEST_CB++;
}


void FLASH_Test(void)
{

    if (FLASHTEST_DOERASE!=0u)
    {
        DisableAllInterrupts();
        /* time measurement */
        
        FLASHTEST_RETURNCODE = FLASH_Erase(FLASHTEST_STARTADDRESS, FLASHTEST_SIZE, FLASHTEST_DOERASECBK);
        
        FLASHTEST_RETURNCODE_BLANCKCHECK = FLASH_BlankCheck(FLASHTEST_STARTADDRESS, FLASHTEST_SIZE);
        
        EnableAllInterrupts();
        
        FLASHTEST_DOERASE = 0u;
    }
        
      
    
    if (FLASHTEST_DOPROGRAM!=0u)
    {
        DisableAllInterrupts();

        FLASHTEST_RETURNCODE = FLASH_Program(FLASHTEST_STARTADDRESS, FLASHTEST_SIZE, (uint32_T)(FLASHTEST_SOURCE));
        
        FLASHTEST_RETURNCODE |=  FLASH_ProgramVerify ( FLASHTEST_STARTADDRESS, FLASHTEST_SIZE, (uint32_T)(FLASHTEST_SOURCE));
        
        FLASHTEST_RETURNCODE_BLANCKCHECK = FLASH_BlankCheck(FLASHTEST_STARTADDRESS, FLASHTEST_SIZE);

        EnableAllInterrupts();

        FLASHTEST_DOPROGRAM = 0u;
    }
    
    if (FLASHTEST_DOBLANKCHECK!=0u)
    {
        DisableAllInterrupts();
        FLASHTEST_RETURNCODE_BLANCKCHECK = FLASH_BlankCheck(FLASHTEST_STARTADDRESS, FLASHTEST_SIZE);
        EnableAllInterrupts();

        FLASHTEST_DOBLANKCHECK = 0u;
    }

    if (FLASHTEST_DOCHECKSUM !=0u)
    {
        DisableAllInterrupts();
        FLASHTEST_RETURNCODE_CHECKSUM = FLASH_CheckSum(FLASHTEST_STARTADDRESS, FLASHTESTCHK_SIZE, &FLASHTEST_CHECKSUMVALUE);
        EnableAllInterrupts();

        FLASHTEST_DOCHECKSUM = 0u;
    }


}

#endif
