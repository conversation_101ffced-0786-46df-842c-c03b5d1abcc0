/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           RonDetectEn.c
 **  File Creation Date: 09-Feb-2022
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         RonDetectEn
 **  Model Description:
 **  Model Version:      1.1016
 **  Model Author:       MarottaR - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: MarottaR - Wed Feb 09 14:38:43 2022
 **
 **  Last Saved Modification:  MarottaR - Wed Feb 09 14:37:31 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "RonDetectEn_out.h"
#include "RonDetectEn_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/RonDetectEn_Scheduler' */
#define RonDe_event_RonDetectEn_PowerOn (0)
#define RonDetect_event_RonDetectEn_EOA (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKLOADRON_dim                  4U                        /* Referenced by: '<S12>/BKLOADRON_dim' */

/* Length for breakpoint BKLOADRON */
#define BKRPMMINLOADRON_dim            11U                       /* Referenced by: '<S14>/BKRPMMINLOADRON_dim' */

/* Length for breakpoint BKRPMMINLOADRON */
#define BKRPMRON_dim                   11U                       /* Referenced by:
                                                                  * '<S11>/BKRPMRON_dim'
                                                                  * '<S12>/BKRPMRON_dim'
                                                                  * '<S13>/BKRPMRON_dim'
                                                                  */

/* Length for breakpoint BKRPMRON */
#define BKTAIRRON_dim                  3U                        /* Referenced by: '<S14>/BKTAIRRON_dim' */

/* Length for breakpoint BKTAIRRON */
#define ID_VER_RONDETECTEN_DEF         11015U                    /* Referenced by: '<Root>/RonDetectEn_Scheduler' */

/* ID model version define */
#define MAX_RPM_DIV_100                100U                      /* Referenced by: '<S22>/MAX_RPM_DIV_100' */

/* Constant value 100 */
#define TEMP_DISABLED                  1U                        /* Referenced by: '<S7>/TempEnableRon_Logics' */

/* State for calculation of variable TempEnableRon */
#define TEMP_ENABLED                   2U                        /* Referenced by: '<S7>/TempEnableRon_Logics' */

/* State for calculation of variable TempEnableRon */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_RONDETECTEN_

/* Add a check for each important define */
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static uint8_T RonDetectEn_is_active;  /* '<S7>/TempEnableRon_Logics' */

/* Local signal for activation condition of temperature enabling for RON estimation stateflow */
static uint8_T RonSstabLoad_old;       /* '<S9>/Memory' */

/* Old value of the signal RonSstabLoad */
static uint8_T RonSstabRpm_old;        /* '<S10>/Memory' */

/* Old value of the signal RonSstabRpm */
static uint8_T TempEnableRon_local;    /* '<S7>/TempEnableRon_Logics' */

/* Local signal of temperature enabling condition for RON estimation */
static uint8_T Temp_Status_local;      /* '<S7>/TempEnableRon_Logics' */

/* Local signal for status of temperature enabling for RON estimation stateflow */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADRON[5] = { 7680U, 8960U, 10240U,
  11520U, 12800U } ;                   /* Referenced by: '<S12>/BKLOADRON' */

/* Breakpoints of load for ron detection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMMINLOADRON[12] = { 1000U, 2000U,
  2500U, 3000U, 3500U, 4000U, 5000U, 5500U, 6000U, 7000U, 8000U, 9000U } ;
                                    /* Referenced by: '<S14>/BKRPMMINLOADRON' */

/* Breakpoints of engine speed for TBMINLOADRON */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMRON[12] = { 1000U, 2000U, 2500U,
  3000U, 3500U, 4000U, 5000U, 5500U, 6000U, 7000U, 8000U, 9000U } ;/* Referenced by: '<S11>/BKRPMRON' */

/* Breakpoints of engine speed for ron detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKTAIRRON[4] = { 320, 800, 1120, 1440 }
;                                      /* Referenced by: '<S14>/BKTAIRRON' */

/* Breakpoints of TAir */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T CNTABSTDCRON = 2000U;/* Referenced by: '<S8>/CNTABSTDCRON' */

/* Number of combustions to ensure that fuel system works with new fuel mix (RonDetect disabled) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENRONDETECT = 1U;/* Referenced by: '<S5>/ENRONDETECT' */

/* RonDetect strategy enable flag */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T HYSTTAIRRON = 32U;/* Referenced by: '<S7>/HYSTTAIRRON' */

/* TAir hysteresis to enable Ron detection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T HYSTTWATRON = 48U;/* Referenced by: '<S7>/HYSTTWATRON' */

/* TWater hysteresis to enable Ron detection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXLOADRON = 15360U;/* Referenced by: '<S15>/MAXLOADRON' */

/* Max Load value for stability detection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXRPMRON = 10000U;/* Referenced by: '<S21>/MAXRPMRON' */

/* Max Rpm value for stability detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MAXTAIRRON = 960;/* Referenced by: '<S7>/MAXTAIRRON' */

/* Max TAir to enable Ron detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MAXTWATRON = 1520;/* Referenced by: '<S7>/MAXTWATRON' */

/* Max TWater to enable Ron detection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint32_T MINKMTHRRONEN = 50U;/* Referenced by: '<S8>/MINKMTHRRONEN' */

/* Minimum Km Threshold to enable RonDetection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MINRPMRON = 500U;/* Referenced by: '<S21>/MINRPMRON' */

/* Min Rpm value for stability detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MINTAIRRON = 80;/* Referenced by: '<S7>/MINTAIRRON' */

/* Min TAir to enable Ron detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T MINTWATRON = 640;/* Referenced by: '<S7>/MINTWATRON' */

/* Min TWater to enable Ron detection */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T TBMINLOADRON[48] = { 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U, 0U,
  0U, 0U, 0U } ;                       /* Referenced by: '<S14>/TBMINLOADRON' */

/* Min Load value to enable steady-state detection */
CALQUAL_PRE CALQUAL CALQUAL_POST int8_T TBSAKNOCKLEVEL[60] = { -64, -64, -64,
  -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64,
  -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64,
  -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64, -64,
  -64, -64, -64, -64, -64, -64, -64, -64, -64 } ;
                                     /* Referenced by: '<S12>/TBSAKNOCKLEVEL' */

/* SAKnockLevel table */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABLDRON = 1280U;/* Referenced by: '<S16>/THRSTABLDRON' */

/* Threshold to detect stability for Load */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABLDRONSUSP = 1280U;
                                   /* Referenced by: '<S16>/THRSTABLDRONSUSP' */

/* Threshold to detect stability for Load - background mode */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABRPMRON = 640U;
                                      /* Referenced by: '<S23>/THRSTABRPMRON' */

/* Threshold to detect stability for Rpm (% with 100% ==> 10000) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABRPMRONSUSP = 640U;
                                  /* Referenced by: '<S23>/THRSTABRPMRONSUSP' */

/* Threshold to detect stability for Rpm - background mode (% with 100% ==> 10000) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTTDCSTABRON[12] = { 10U, 20U, 30U,
  40U, 50U, 60U, 85U, 100U, 115U, 135U, 150U, 160U } ;/* Referenced by: '<S13>/VTTDCSTABRON' */

/* Number of TDCs to detect stability of Load and Rpm for ron detection */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T EnRonDetect;                   /* '<S3>/MEnRonDetect' */

/* Enabling condition for ron strategy by calibration */
uint8_T FlgSteadyStateRon;             /* '<S3>/MFlgSteadyStateRon' */

/* Both Rpm and Load are stable for ron detection */
uint16_T IDZoneRonLoad;                /* '<S3>/MIDZoneRonLoad' */

/* Load zone index for ron detection */
uint16_T IDZoneRonRpm;                 /* '<S3>/MIDZoneRonRpm' */

/* Rpm zone index for ron detection */
uint8_T OdomEnableRon;                 /* '<S3>/MOdomEnableRon' */

/* Ron enabling condition according to odometer value */
uint16_T RtZoneRonLoad;                /* '<S3>/MRtZoneRonLoad' */

/* Load zone ratio for ron detection */
uint16_T RtZoneRonRpm;                 /* '<S3>/MRtZoneRonRpm' */

/* Rpm zone ratio for ron detection */
int16_T SAKnockLevel;                  /* '<S3>/MSAKnockLevel' */

/* SA knock correction value to enable ron detection */
uint8_T TempEnableRon;                 /* '<S3>/MTempEnableRon' */

/* Temperature enabling condition for RON estimation */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint8_T FStabLoadRon;/* '<S3>/MFStabLoadRon' */

/* Load is stable for ron detection */
STATIC_TEST_POINT uint8_T FStabRpmRon; /* '<S3>/MFStabRpmRon' */

/* Rpm is stable for ron detection */
STATIC_TEST_POINT uint32_T IdVer_RonDetectEn;/* '<Root>/RonDetectEn_Scheduler' */

/* ID model version */
STATIC_TEST_POINT uint16_T MinLoadRon; /* '<S3>/MMinLoadRon' */

/* Min Load value for stability detection */
STATIC_TEST_POINT uint8_T RonSstabLoad;/* '<S3>/MRonSstabLoad' */

/* Steady state with Load stable for ron detection */
STATIC_TEST_POINT uint8_T RonSstabRpm; /* '<S3>/MRonSstabRpm' */

/* Steady state with Rpm stable for ron detection */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_RonDetectEn_T RonDetectEn_DW;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void RonDet_chartstep_c3_RonDetectEn(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/RonDetectEn_Scheduler' */
static void RonDet_chartstep_c3_RonDetectEn(const int32_T *sfEvent)
{
  /* local block i/o variables */
  uint16_T rtb_SteadyStateDetect_o3;
  uint16_T rtb_SteadyStateDetect_o4;
  uint16_T rtb_SteadyStateDetect_o3_i;
  uint16_T rtb_SteadyStateDetect_o4_m;
  uint16_T rtb_PreLookUpIdSearch_U16_o1;
  uint16_T rtb_PreLookUpIdSearch_U16_o2;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_PreLookUpIdSearch_S16_o1;
  uint16_T rtb_PreLookUpIdSearch_S16_o2;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_g;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_a;
  uint16_T rtb_Look2D_IR_U8;
  uint16_T rtb_PreLookUpIdSearch_U16_o1_l;
  uint16_T rtb_PreLookUpIdSearch_U16_o2_k;
  int16_T rtb_Look2D_IR_S8;
  uint8_T rtb_SteadyStateDetect_o1;
  uint8_T rtb_SteadyStateDetect_o2;
  uint8_T rtb_SteadyStateDetect_o1_b;
  uint8_T rtb_SteadyStateDetect_o2_a;
  boolean_T rtb_RelationalOperator1_l;
  uint8_T rtb_Conversion4;
  uint16_T rtb_Product1;
  uint16_T rtb_Switch;

  /* Chart: '<Root>/RonDetectEn_Scheduler'
   *
   * Block description for '<Root>/RonDetectEn_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  /* Chart: '<Root>/RonDetectEn_Scheduler'
   *
   * Block description for '<Root>/RonDetectEn_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  /* During: RonDetectEn_Scheduler */
  /* Entry Internal: RonDetectEn_Scheduler */
  /* Transition: '<S4>:8' */
  if ((*sfEvent) == ((int32_T)RonDe_event_RonDetectEn_PowerOn)) {
    /* Outputs for Function Call SubSystem: '<Root>/Init_fcn'
     *
     * Block description for '<Root>/Init_fcn':
     *  This block performs outputs initialization.
     */
    /* SignalConversion generated from: '<S2>/TempEnableRon' incorporates:
     *  Constant: '<S2>/Constant'
     */
    /* Transition: '<S4>:10' */
    /* Transition: '<S4>:14' */
    /* Event: '<S4>:3' */
    TempEnableRon = 0U;

    /* SignalConversion generated from: '<S2>/EnRonDetect' incorporates:
     *  Constant: '<S2>/Constant5'
     */
    EnRonDetect = 0U;

    /* SignalConversion generated from: '<S2>/FlgSteadyStateRon' incorporates:
     *  Constant: '<S2>/Constant1'
     */
    FlgSteadyStateRon = 0U;

    /* SignalConversion generated from: '<S2>/IDZoneRonLoad' incorporates:
     *  Constant: '<S2>/Constant2'
     */
    IDZoneRonLoad = 0U;

    /* SignalConversion generated from: '<S2>/IDZoneRonRpm' incorporates:
     *  Constant: '<S2>/Constant3'
     */
    IDZoneRonRpm = 0U;

    /* SignalConversion generated from: '<S2>/OdomEnableRon' incorporates:
     *  Constant: '<S2>/Constant4'
     */
    OdomEnableRon = 0U;

    /* SignalConversion generated from: '<S2>/RtZoneRonLoad' incorporates:
     *  Constant: '<S2>/Constant6'
     */
    RtZoneRonLoad = 0U;

    /* SignalConversion generated from: '<S2>/RtZoneRonRpm' incorporates:
     *  Constant: '<S2>/Constant7'
     */
    RtZoneRonRpm = 0U;

    /* SignalConversion generated from: '<S2>/SAKnockLevel' incorporates:
     *  Constant: '<S2>/Constant8'
     */
    SAKnockLevel = 0;

    /* SignalConversion generated from: '<S2>/FStabLoadRon' incorporates:
     *  Constant: '<S2>/Constant9'
     */
    FStabLoadRon = 0U;

    /* SignalConversion generated from: '<S2>/FStabRpmRon' incorporates:
     *  Constant: '<S2>/Constant10'
     */
    FStabRpmRon = 0U;

    /* SignalConversion generated from: '<S2>/RonSstabRpm' incorporates:
     *  Constant: '<S2>/Constant11'
     */
    RonSstabRpm = 0U;

    /* SignalConversion generated from: '<S2>/RonSstabLoad' incorporates:
     *  Constant: '<S2>/Constant12'
     */
    RonSstabLoad = 0U;

    /* SignalConversion generated from: '<S2>/MinLoadRon' incorporates:
     *  Constant: '<S2>/Constant13'
     */
    MinLoadRon = 0U;

    /* End of Outputs for SubSystem: '<Root>/Init_fcn' */
    IdVer_RonDetectEn = ID_VER_RONDETECTEN_DEF;

    /* Transition: '<S4>:17' */
  } else {
    /* Outputs for Function Call SubSystem: '<Root>/EOA_fcn'
     *
     * Block description for '<Root>/EOA_fcn':
     *  This block performs EOA functionalities.
     */
    /* Product: '<S22>/Product1' incorporates:
     *  Constant: '<S22>/MAX_RPM_DIV_100'
     *  Inport: '<Root>/Rpm'
     */
    /* Transition: '<S4>:12' */
    /* RonDetectEn_EOA */
    /* Transition: '<S4>:16' */
    /* Event: '<S4>:4' */
    rtb_Product1 = (uint16_T)((((uint32_T)Rpm) << ((uint32_T)7)) / ((uint32_T)
      ((uint8_T)MAX_RPM_DIV_100)));

    /* DataTypeConversion: '<S24>/Conversion4' incorporates:
     *  Constant: '<S21>/MAXRPMRON'
     *  Constant: '<S21>/MINRPMRON'
     *  Inport: '<Root>/Rpm'
     *  Logic: '<S21>/Logical Operator'
     *  RelationalOperator: '<S21>/Relational Operator'
     *  RelationalOperator: '<S21>/Relational Operator1'
     */
    rtb_Conversion4 = (uint8_T)(((Rpm >= MAXRPMRON) || (Rpm <= MINRPMRON)) ? 1 :
      0);

    /* Switch: '<S23>/Switch' incorporates:
     *  Constant: '<S23>/THRSTABRPMRON'
     *  Constant: '<S23>/THRSTABRPMRONSUSP'
     *  Inport: '<Root>/FlgRonStoredEE'
     */
    if (((int32_T)FlgRonStoredEE) != 0) {
      rtb_Switch = THRSTABRPMRONSUSP;
    } else {
      rtb_Switch = THRSTABRPMRON;
    }

    /* End of Switch: '<S23>/Switch' */

    /* S-Function (PreLookUpIdSearch_U16): '<S25>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S11>/BKRPMRON'
     *  Constant: '<S11>/BKRPMRON_dim'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1,
                          &rtb_PreLookUpIdSearch_U16_o2, Rpm, &BKRPMRON[0],
                          ((uint8_T)BKRPMRON_dim));

    /* S-Function (LookUp_IR_U16): '<S29>/LookUp_IR_U16' incorporates:
     *  Constant: '<S13>/BKRPMRON_dim'
     *  Constant: '<S13>/VTTDCSTABRON'
     */
    LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTTDCSTABRON[0],
                  rtb_PreLookUpIdSearch_U16_o1, rtb_PreLookUpIdSearch_U16_o2,
                  ((uint8_T)BKRPMRON_dim));

    /* S-Function (SteadyStateDetect): '<S24>/SteadyStateDetect' */
    SteadyStateDetect( &rtb_SteadyStateDetect_o1, &rtb_SteadyStateDetect_o2,
                      &rtb_SteadyStateDetect_o3, &rtb_SteadyStateDetect_o4,
                      rtb_Product1, rtb_Conversion4, rtb_Switch,
                      rtb_LookUp_IR_U16, RonSstabRpm_old,
                      RonDetectEn_DW.Memory1_PreviousInput,
                      RonDetectEn_DW.Memory_PreviousInput);

    /* S-Function (PreLookUpIdSearch_S16): '<S19>/PreLookUpIdSearch_S16' incorporates:
     *  Constant: '<S14>/BKTAIRRON'
     *  Constant: '<S14>/BKTAIRRON_dim'
     */
    PreLookUpIdSearch_S16( &rtb_PreLookUpIdSearch_S16_o1,
                          &rtb_PreLookUpIdSearch_S16_o2, TAir, &BKTAIRRON[0],
                          ((uint8_T)BKTAIRRON_dim));

    /* S-Function (PreLookUpIdSearch_U16): '<S20>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S14>/BKRPMMINLOADRON'
     *  Constant: '<S14>/BKRPMMINLOADRON_dim'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_g,
                          &rtb_PreLookUpIdSearch_U16_o2_a, Rpm,
                          &BKRPMMINLOADRON[0], ((uint8_T)BKRPMMINLOADRON_dim));

    /* S-Function (Look2D_IR_U8): '<S18>/Look2D_IR_U8' incorporates:
     *  Constant: '<S14>/BKRPMMINLOADRON_dim'
     *  Constant: '<S14>/BKTAIRRON_dim'
     *  Constant: '<S14>/TBMINLOADRON'
     */
    Look2D_IR_U8( &rtb_Look2D_IR_U8, &TBMINLOADRON[0],
                 rtb_PreLookUpIdSearch_S16_o1, rtb_PreLookUpIdSearch_S16_o2,
                 ((uint8_T)BKTAIRRON_dim), rtb_PreLookUpIdSearch_U16_o1_g,
                 rtb_PreLookUpIdSearch_U16_o2_a, ((uint8_T)BKRPMMINLOADRON_dim));

    /* DataTypeConversion: '<S17>/Conversion4' incorporates:
     *  Constant: '<S15>/MAXLOADRON'
     *  Inport: '<Root>/Load'
     *  Logic: '<S15>/Logical Operator1'
     *  RelationalOperator: '<S15>/Relational Operator2'
     *  RelationalOperator: '<S15>/Relational Operator3'
     */
    rtb_Conversion4 = (uint8_T)(((Load >= MAXLOADRON) || (Load <=
      rtb_Look2D_IR_U8)) ? 1 : 0);

    /* Switch: '<S16>/Switch1' incorporates:
     *  Constant: '<S16>/THRSTABLDRON'
     *  Constant: '<S16>/THRSTABLDRONSUSP'
     *  Inport: '<Root>/FlgRonStoredEE'
     */
    if (((int32_T)FlgRonStoredEE) != 0) {
      rtb_Product1 = THRSTABLDRONSUSP;
    } else {
      rtb_Product1 = THRSTABLDRON;
    }

    /* End of Switch: '<S16>/Switch1' */

    /* S-Function (SteadyStateDetect): '<S17>/SteadyStateDetect' */
    SteadyStateDetect( &rtb_SteadyStateDetect_o1_b, &rtb_SteadyStateDetect_o2_a,
                      &rtb_SteadyStateDetect_o3_i, &rtb_SteadyStateDetect_o4_m,
                      Load, rtb_Conversion4, rtb_Product1, rtb_LookUp_IR_U16,
                      RonSstabLoad_old, RonDetectEn_DW.Memory1_PreviousInput_b,
                      RonDetectEn_DW.Memory_PreviousInput_c);

    /* RelationalOperator: '<S8>/Relational Operator1' incorporates:
     *  Constant: '<S8>/MINKMTHRRONEN'
     *  Inport: '<Root>/TotOdometerCAN'
     */
    rtb_RelationalOperator1_l = (TotOdometerCAN >= MINKMTHRRONEN);

    /* DataTypeConversion: '<S8>/Data Type Conversion1' incorporates:
     *  Constant: '<S8>/CNTABSTDCRON'
     *  Constant: '<S8>/Constant'
     *  Constant: '<S8>/Constant1'
     *  Constant: '<S8>/RD_FUEL_INC'
     *  Inport: '<Root>/CntAbsTdc'
     *  Inport: '<Root>/FlgDLoadTAir'
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Inport: '<Root>/StReFuel'
     *  Inport: '<Root>/VtP2NoiseDetFlg'
     *  Logic: '<S8>/Logical Operator2'
     *  Logic: '<S8>/Logical Operator3'
     *  RelationalOperator: '<S8>/Relational Operator2'
     *  RelationalOperator: '<S8>/Relational Operator3'
     *  RelationalOperator: '<S8>/Relational Operator4'
     *  RelationalOperator: '<S8>/Relational Operator5'
     *  Selector: '<S8>/Selector'
     */
    FlgSteadyStateRon = (uint8_T)(((((((((int32_T)VtP2NoiseDetFlg[(IonAbsTdcEOA)])
      == 0) && (((int32_T)FlgDLoadTAir) == 0)) && (((int32_T)
      rtb_SteadyStateDetect_o1) != 0)) && (((int32_T)rtb_SteadyStateDetect_o1_b)
      != 0)) && ((((uint32_T)StReFuel) != RD_FUEL_INC) || (CntAbsTdc >=
      ((uint32_T)CNTABSTDCRON)))) && rtb_RelationalOperator1_l) ? 1 : 0);

    /* DataTypeConversion: '<S8>/Data Type Conversion' */
    OdomEnableRon = (uint8_T)(rtb_RelationalOperator1_l ? ((uint8_T)1) :
      ((uint8_T)0));

    /* S-Function (PreLookUpIdSearch_U16): '<S28>/PreLookUpIdSearch_U16' incorporates:
     *  Constant: '<S12>/BKLOADRON'
     *  Constant: '<S12>/BKLOADRON_dim'
     */
    PreLookUpIdSearch_U16( &rtb_PreLookUpIdSearch_U16_o1_l,
                          &rtb_PreLookUpIdSearch_U16_o2_k, Load, &BKLOADRON[0],
                          ((uint8_T)BKLOADRON_dim));

    /* S-Function (Look2D_IR_S8): '<S27>/Look2D_IR_S8' incorporates:
     *  Constant: '<S12>/BKLOADRON_dim'
     *  Constant: '<S12>/BKRPMRON_dim'
     *  Constant: '<S12>/TBSAKNOCKLEVEL'
     */
    Look2D_IR_S8( &rtb_Look2D_IR_S8, &TBSAKNOCKLEVEL[0],
                 rtb_PreLookUpIdSearch_U16_o1_l, rtb_PreLookUpIdSearch_U16_o2_k,
                 ((uint8_T)BKLOADRON_dim), rtb_PreLookUpIdSearch_U16_o1,
                 rtb_PreLookUpIdSearch_U16_o2, ((uint8_T)BKRPMRON_dim));

    /* DataTypeConversion: '<S26>/Conversion2' incorporates:
     *  Product: '<S26>/Divide'
     */
    SAKnockLevel = (int16_T)((rtb_Look2D_IR_S8 + 15360) / 32);

    /* Chart: '<S7>/TempEnableRon_Logics' incorporates:
     *  Constant: '<S7>/HYSTTAIRRON'
     *  Constant: '<S7>/HYSTTWATRON'
     *  Constant: '<S7>/MAXTAIRRON'
     *  Constant: '<S7>/MAXTWATRON'
     *  Constant: '<S7>/MINTAIRRON'
     *  Constant: '<S7>/MINTWATRON'
     *  Inport: '<Root>/TAir'
     *  Inport: '<Root>/TWater'
     *
     * Block description for '<S7>/TempEnableRon_Logics':
     *  In this block is calculated the temperature enabling condition for RON
     *  estimation.
     */
    /* Gateway: EOA_fcn/TempEnableRon_Calculation/TempEnableRon_Logics */
    /* During: EOA_fcn/TempEnableRon_Calculation/TempEnableRon_Logics */
    /* Entry Internal: EOA_fcn/TempEnableRon_Calculation/TempEnableRon_Logics */
    /* Transition: '<S31>:26' */
    if (((int32_T)RonDetectEn_is_active) == 0) {
      /* Transition: '<S31>:28' */
      /* Transition: '<S31>:32' */
      RonDetectEn_is_active = 1U;
      Temp_Status_local = ((uint8_T)TEMP_DISABLED);
      TempEnableRon_local = 0U;

      /* Transition: '<S31>:46' */
      /* Transition: '<S31>:56' */
      /* Transition: '<S31>:58' */
      /* Transition: '<S31>:59' */
    } else {
      /* Transition: '<S31>:30' */
      if (Temp_Status_local == ((uint8_T)TEMP_DISABLED)) {
        /* Transition: '<S31>:34' */
        if ((((TWater >= MINTWATRON) && (TWater <= MAXTWATRON)) && (TAir >=
              MINTAIRRON)) && (TAir <= MAXTAIRRON)) {
          /* Transition: '<S31>:38' */
          /* Transition: '<S31>:42' */
          Temp_Status_local = ((uint8_T)TEMP_ENABLED);
          TempEnableRon_local = 1U;
        } else {
          /* Transition: '<S31>:40' */
          Temp_Status_local = ((uint8_T)TEMP_DISABLED);
          TempEnableRon_local = 0U;

          /* Transition: '<S31>:43' */
        }

        /* Transition: '<S31>:45' */
        /* Transition: '<S31>:56' */
        /* Transition: '<S31>:58' */
        /* Transition: '<S31>:59' */
      } else {
        /* Transition: '<S31>:36' */
        /* Temp_Status_local == TEMP_ENABLED */
        if ((((((int32_T)TWater) < (((int32_T)MINTWATRON) - ((int32_T)
                 HYSTTWATRON))) || (((int32_T)TWater) > (((int32_T)MAXTWATRON) +
                ((int32_T)HYSTTWATRON)))) || (((int32_T)TAir) < (((int32_T)
                MINTAIRRON) - ((int32_T)HYSTTAIRRON)))) || (((int32_T)TAir) >
             (((int32_T)MAXTAIRRON) + ((int32_T)HYSTTAIRRON)))) {
          /* Transition: '<S31>:48' */
          /* Transition: '<S31>:52' */
          Temp_Status_local = ((uint8_T)TEMP_DISABLED);
          TempEnableRon_local = 0U;

          /* Transition: '<S31>:59' */
        } else {
          /* Transition: '<S31>:50' */
          Temp_Status_local = ((uint8_T)TEMP_ENABLED);
          TempEnableRon_local = 1U;
        }
      }
    }

    /* End of Chart: '<S7>/TempEnableRon_Logics' */

    /* DataTypeConversion: '<S7>/Data Type Conversion' */
    /* Transition: '<S31>:60' */
    TempEnableRon = TempEnableRon_local;

    /* SignalConversion generated from: '<S1>/IDZoneRonLoad' */
    IDZoneRonLoad = rtb_PreLookUpIdSearch_U16_o1_l;

    /* SignalConversion generated from: '<S1>/RtZoneRonLoad' */
    RtZoneRonLoad = rtb_PreLookUpIdSearch_U16_o2_k;

    /* SignalConversion generated from: '<S1>/FStabLoadRon' */
    FStabLoadRon = rtb_SteadyStateDetect_o1_b;

    /* SignalConversion generated from: '<S1>/RonSstabLoad' */
    RonSstabLoad = rtb_SteadyStateDetect_o2_a;

    /* SignalConversion generated from: '<S1>/MinLoadRon' */
    MinLoadRon = rtb_Look2D_IR_U8;

    /* SignalConversion generated from: '<S1>/FStabRpmRon' */
    FStabRpmRon = rtb_SteadyStateDetect_o1;

    /* SignalConversion generated from: '<S1>/RonSstabRpm' */
    RonSstabRpm = rtb_SteadyStateDetect_o2;

    /* SignalConversion generated from: '<S1>/IDZoneRonRpm' */
    IDZoneRonRpm = rtb_PreLookUpIdSearch_U16_o1;

    /* SignalConversion generated from: '<S1>/RtZoneRonRpm' */
    RtZoneRonRpm = rtb_PreLookUpIdSearch_U16_o2;

    /* SignalConversion generated from: '<S1>/EnRonDetect' incorporates:
     *  Constant: '<S5>/ENRONDETECT'
     */
    EnRonDetect = ENRONDETECT;

    /* Update for Memory: '<S10>/Memory' */
    RonSstabRpm_old = rtb_SteadyStateDetect_o2;

    /* Update for Memory: '<S24>/Memory1' */
    RonDetectEn_DW.Memory1_PreviousInput = rtb_SteadyStateDetect_o3;

    /* Update for Memory: '<S24>/Memory' */
    RonDetectEn_DW.Memory_PreviousInput = rtb_SteadyStateDetect_o4;

    /* Update for Memory: '<S9>/Memory' */
    RonSstabLoad_old = rtb_SteadyStateDetect_o2_a;

    /* Update for Memory: '<S17>/Memory1' */
    RonDetectEn_DW.Memory1_PreviousInput_b = rtb_SteadyStateDetect_o3_i;

    /* Update for Memory: '<S17>/Memory' */
    RonDetectEn_DW.Memory_PreviousInput_c = rtb_SteadyStateDetect_o4_m;

    /* End of Outputs for SubSystem: '<Root>/EOA_fcn' */
  }

  /* End of Chart: '<Root>/RonDetectEn_Scheduler' */
  /* Transition: '<S4>:19' */
}

/*
 * Output and update for function-call system: '<Root>/RonDetectEn_Scheduler'
 * Block description for: '<Root>/RonDetectEn_Scheduler'
 *   This block is the scheduler for the models functions.
 */
void RonDetect_RonDetectEn_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[2];
  int32_T i;

  /* Chart: '<Root>/RonDetectEn_Scheduler' incorporates:
   *  TriggerPort: '<S4>/input events'
   *
   * Block description for '<Root>/RonDetectEn_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  for (i = 0; i < 2; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: RonDetectEn_Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S4>:1' */
    i = (int32_T)RonDe_event_RonDetectEn_PowerOn;
    RonDet_chartstep_c3_RonDetectEn(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S4>:2' */
    i = (int32_T)RonDetect_event_RonDetectEn_EOA;
    RonDet_chartstep_c3_RonDetectEn(&i);
  }
}

/* Model step function */
void RonDetectEn_EOA(void)
{
  /* Chart: '<Root>/RonDetectEn_Scheduler'
   *
   * Block description for '<Root>/RonDetectEn_Scheduler':
   *  This block is the scheduler for the models functions.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEn_EOA' */

  /* Chart: '<Root>/RonDetectEn_Scheduler'
   *
   * Block description for '<Root>/RonDetectEn_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  RonDetect_RonDetectEn_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEn_EOA' */
}

/* Model step function */
void RonDetectEn_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEn_PowerOn' incorporates:
   *  Chart: '<Root>/RonDetectEn_Scheduler'
   *
   * Block description for '<Root>/RonDetectEn_Scheduler':
   *  This block is the scheduler for the models functions.
   */

  /* Chart: '<Root>/RonDetectEn_Scheduler'
   *
   * Block description for '<Root>/RonDetectEn_Scheduler':
   *  This block is the scheduler for the models functions.
   */
  RonDetect_RonDetectEn_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/RonDetectEn_PowerOn' */
}

/* Model initialize function */
void RonDetectEn_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint8_T EnRonDetect;
uint8_T FlgSteadyStateRon;
uint8_T FStabLoadRon;
uint8_T FStabRpmRon;
uint16_T IDZoneRonLoad;
uint16_T IDZoneRonRpm;
uint8_T OdomEnableRon;
uint16_T RtZoneRonLoad;
uint16_T RtZoneRonRpm;
int16_T SAKnockLevel;
uint8_T TempEnableRon;
uint8_T RonSstabRpm;
uint8_T RonSstabLoad;
uint16_T MinLoadRon;
uint8_T RonDetectCyl[N_CYL_MAX];
void RonDetectEn_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<N_CYL_MAX;idx++) {
    RonDetectCyl[idx] = 0u;
  }

  EnRonDetect = 0u;
  FlgSteadyStateRon = 0u;
  FStabLoadRon = 0u;
  FStabRpmRon = 0u;
  IDZoneRonLoad = 0u;
  IDZoneRonRpm = 0u;
  OdomEnableRon = 0u;
  RtZoneRonLoad = 0u;
  RtZoneRonRpm = 0u;
  SAKnockLevel = 0;
  TempEnableRon = 0u;
  RonSstabRpm = 0u;
  RonSstabLoad = 0u;
  MinLoadRon = 0u;
}

void RonDetectEn_PowerOn(void)
{
  RonDetectEn_Stub();
}

void RonDetectEn_EOA(void)
{
  RonDetectEn_Stub();
}

#endif                                 /* _BUILD_RONDETECTEN_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/