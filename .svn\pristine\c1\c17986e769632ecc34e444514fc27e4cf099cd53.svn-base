/**
 ******************************************************************************
 **  Filename:      mul_s32_s32_s32_sr12.h
 **  Date:          15-Mar-2019
 **
 **  Model Version: 1.140
 ******************************************************************************
 **/

#ifndef SHARE_mul_s32_s32_s32_sr12
#define SHARE_mul_s32_s32_s32_sr12
#include "rtwtypes.h"

extern int32_T mul_s32_s32_s32_sr12(int32_T a, int32_T b);

#endif

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 8.4 (R2014b)08-Sep-2014                                             *
 * Simulink 8.4 (R2014b)08-Sep-2014                                           *
 * Simulink Coder 8.7 (R2014b)08-Sep-2014                                     *
 * Embedded Coder 6.7 (R2014b)08-Sep-2014                                     *
 * Stateflow 8.4 (R2014b)08-Sep-2014                                          *
 * Fixed-Point Designer 4.3 (R2014b)08-Sep-2014                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/
