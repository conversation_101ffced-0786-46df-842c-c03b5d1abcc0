/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Startup
**  Filename        :  get_app_startup.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"
#include "OS_exec_ctrl.h"

extern void app_start(void);
typedef void(*pFunc)(void);

const pFunc pApplicationStartup = &app_start;

/****************************************************************************
 ****************************************************************************/
