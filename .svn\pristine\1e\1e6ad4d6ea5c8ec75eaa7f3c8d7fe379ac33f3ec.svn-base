/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef KWP2000_OUT_H
#define KWP2000_OUT_H

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "diagcanmgm.h"

/*!
\defgroup PublicDefines Public Defines
\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
///This is a public define 


/*!\egroup*/
/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
///This is a public typedef 
//typedef templateinteger_T int; 


/*!\egroup*/
/*!
\defgroup PublicInline Public Inline Functions 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC INLINE FUNCTIONS
 *-----------------------------------*/
/***************************************************************************/
//   Function    :   Inline function name
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this inline function 
*/
/**************************************************************************/
/*inline void publicInlineFunction(void)

    //Remember to put some comments inside function
    //In this moment YOU and GOD know how this code works.
    //Within six months only GOD will.      

*/

/*!\egroup*/


/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint8_T FlgPLAFactCal;
extern uint8_T FlgEOL;

/*-----------------------------------*
 * IMPORTED CALIBRATION
 *-----------------------------------*/
// Force EOL (=1) or leave unchanged (=0)
extern CALQUAL CALQUAL_POST uint8_T FORCEEOL;


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
void Kwp2000PendingWriteData(void);
void Kwp2000PendingClearDiagInfo(void);
void Kwp2000PendingStartRoutine(void);
uint8_T KWP2000GetDTCSetting(void);


#endif

/****************************************************************************
 ****************************************************************************/


