/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Startup
**  Filename        :  mpc5500_user_init.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  CarboniM
******************************************************************************/

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include <string.h>
#include "mpc5500_spr_macros.h"

#ifdef _BUILD_WDT
#include "wdt.h"        
#endif /* _BUILD_WDT */

#ifdef _BUILD_WDT_SBC_
#include "WDT_out.h" 
#endif

#include "eemgm_out.h"

#ifdef  _BUILD_ADC_ 
#include "Adc_out.h"
#endif /* _BUILD_ADC_ */


#ifdef  _BUILD_CAN_
#include "Mcan_out.h"
#include "TTcan_out.h"
#endif /* _BUILD_CAN_ */

#ifdef  _BUILD_DIGIO_
#include "Digio_out.h"
#endif /* _BUILD_DIGIO_ */

#ifdef  _BUILD_DMA_
#include "Dma_out.h"
#endif /* _BUILD_DMA_ */

#ifdef  _BUILD_EXTIRQ_
#include "extirq.h"
#endif /* _BUILD_EXTIRQ_ */

#ifdef  _BUILD_FLASH_
#include "Flash_out.h"
#endif /* _BUILD_FLASH_ */

#ifdef  _BUILD_MATHLIB_
#include "mathlib.h"
#endif /* _BUILD_MATHLIB_ */

#ifdef  _BUILD_PIT_
#include "Pit_out.h"
#endif /* _BUILD_PIT_ */

#ifdef  _BUILD_SCILIN_
#include "sci.h"
#endif /* _BUILD_SCILIN_ */

#ifdef  _BUILD_SPI_
#include "Dspi_out.h"
#ifdef _BUILD_PORT_
#include "Port_out.h"
#endif
#endif /* _BUILD_SPI_ */

#ifdef _BUILD_SPIMGM_
#include "spimgm.h"
#endif /* _BUILD_SPIMGM_ */

#include "ivor_c0.h"
#include "ivor_c2.h"

#ifdef  _BUILD_SYS_
#include "sys.h"
#endif /* _BUILD_SYS_ */

#ifdef  _BUILD_TIMING_
#include "Timing_out.h"
#endif /* _BUILD_TIMING_ */

#ifdef  _BUILD_UART_
#include "uart.h"
#endif /* _BUILD_UART_ */

#ifdef  _BUILD_PHASE_
#include "phase.h"
#endif /* _BUILD_PHASE */

#include "ee_out.h"
#include "wdt_wrapper_out.h"

#ifdef  _BUILD_RECOVERY_
#include "recovery.h"
#endif /* _BUILD_RECOVERY_ */

#ifdef  _BUILD_TASK_
#include "task.h"
#endif /* _BUILD_TASK_ */

#ifdef _BUILD_VSRAMMGM_
#include "vsrammgm.h"
#endif

#ifdef _BUILD_CPUMGM_
#include "cpumgm_out.h"
#endif

#ifdef _BUILD_UTILS_
#include "Utils_out.h"
#endif

#ifdef _BUILD_DIAGCANMGM_
#include "Diagcanmgm_out.h"
#endif

#ifdef _BUILD_INTSRCMGM_
#include "intsrcmgm.h"
#endif

#ifdef _BUILD_STM_
#include "stm_out.h"
#endif

#ifdef _BUILD_GTM_
extern void gtmInit(void);
extern void Gtm_Eisb_Tim_Tout_Disable(void);
#endif

extern uint32_T __CALIB_ROM_START;
extern uint32_T __BACKUP_START;
extern uint32_T __CALIB_ROM_SIZE;

/* Local function declarations */
void app_data_c0_section_init(void);
void app_text_c0_section_init(void);

static int16_T SIU_Config(void);
void CPU_TIMER_Config(void);
int_T main(void);
//static void appManager(void); MC

typedef void(*pFunc)(void);
void Init_Global_start(void);
void Init_Global_start(void);
void Init_calib_RAM_start(void); 
void Init_calib_RAM_end(void);
/* Peripheral configuration status */
static uint16_T SIU_ConfigStatus;

/// This flag signals that an ECU reset request incoming from boot (for eg. after finishing successful a reprogramming procedure) needs a response
uint8_T RstRespFlag = 0u;
/// This flag signals that a DiagnosticSessionControl, Default Session from boot (or a S3 server timeout or a ECU reset) needs a response
uint8_T DefaultFromBootRespFlag = 0u;


//pFunc GetApplicationStartup = &appManager; MC




/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : IVOR_Common_ISR
**
**   Description:
**    IVOR Common ISR
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void IVOR_Common_ISR(void)      
{
uint16_T WdtTxBuffer[2];
uint8_T cyl = 0u;

	DisableAllInterrupts();
	TASK_DisablePeripherals();
	SaveIntptContext_c0(IvorIndex_c0);

    WdtTxBuffer[0] = 0x2B82u; //HW_CTRL_0
    WdtTxBuffer[1] = 0x409Eu; //SF_CORE1_EXCEPTION
    SPI_Transmit_Inl(SPI_CH_E, PCS_0, &WdtTxBuffer[0], 2u);
	Port_IvorSafetyDisable();
	WDT_ExtSyncISR();	
	EEMGM_SetEventID(EE_BEFORE_SW_RESET_IVOR);
	EEMGM_EETaskCmd();		

#ifdef _BUILD_WDT_SBC_
#ifdef _BUILD_WDT_EVB_
    SYS_SwRST();
#else
    for(;;){}
#endif
#else
    SYS_SwRST();
#endif
}

#ifdef _BUILD_RECOVERY_
#if (TARGET_TYPE != SPC574K2_CUT24)&&(TARGET_TYPE != SPC574K2)
static void SetECSM(void)
{
    DisableAllInterrupts();

    /* enable FLASH/RAM ECC error report */
    ECSM.ECR.R = ECSM_ECR_EFNCR_MASK ; // non-correctable FLASH ECC error 

#ifdef _BUILD_RAM_RECOVERY_
    ECSM.ECR.R |= ECSM_ECR_ERNCR_MASK; // non-correctable RAM ECC error
#endif

    EnableAllInterrupts();
} 
#endif
#endif //_BUILD_RECOVERY_


/* ------------------------------------------------------------------------- **
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void appManagerCore2(void) 
{
    int16_T errtmp = 0;
    uint8_T EE_error = 0u;

#ifdef _BUILD_RECOVERY_
    IVOR1_UserFunction_c2 = &IVOR_Common_ISR_c2;
#else /*_BUILD_RECOVERY_*/
    IVOR1_UserFunction_c2 = &IVOR_Common_ISR_c2;
#endif

    IVOR_Common_ManagerUserFunction_c2 = &IVOR_Common_ISR_c2;

    IVOR_ConfigStatus_c2 = 0u;
    errtmp = IVOR_Config_c2();
    if (errtmp < 0)
    {
#ifdef  _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,IVOR_IDX);
#endif
    }

#ifdef _BUILD_RECOVERY_
#if (TARGET_TYPE != SPC574K2_CUT24)&&(TARGET_TYPE != SPC574K2)
    SetECSM();
#endif // (TARGET_TYPE != SPC574K2_CUT24)&&(TARGET_TYPE != SPC574K2)
#endif // _BUILD_RECOVERY_

#ifdef _BUILD_CPUMGM_
    /* Init ResetType. */
    CpuMgm_Initialize();
#endif

#ifdef _BUILD_VSRAMMGM_
    if (VSRAMMGM_Verify() != NO_ERROR)
    {
        VSRAMMGM_Init(0u);
        VSRAMMGM_Update();
    }
#endif //_BUILD_VSRAMMGM_

#ifdef _BUILD_EEPROM_
    EE_SetupToDo = &EE_Setup;
#endif

#ifdef  _BUILD_STM_
    STM_ConfigStatus = 0u;
    errtmp = 0;
    errtmp |= STM_Config();
    if (errtmp < 0)
    {
#ifdef _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,STM_IDX);
#endif
    }
#endif /* _BUILD_STM_ */


#ifdef _BUILD_PIT_
    PITConfigStatus = 0u;
    errtmp = 0;
    errtmp |= PIT_Config();
    if (errtmp < 0)
    {
#ifdef _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,PIT_IDX);
#endif
    }
#endif /* _BUILD_PIT_ */

#ifdef _BUILD_TIMING_
    /* Activate core timing handler to serve SPI timeouts */
    errtmp = 0;
    errtmp |= TIMING_Init();
    if (errtmp < 0)
    {
#ifdef  _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,TMR_IDX);
#endif
    }
#endif


#ifdef  _BUILD_SPI_ 
    PORT_DisableDSPIPort(); //Sets DSPI port (already used in MB/Boot) to GPIO to avoid spikes. The complete reconfiguration will be done in PORT_Init. 
    errtmp = 0;
    SPIConfigStatus=0u;
    SPIChannelStatus[SPI_CH_A]=0u;
    SPIChannelStatus[SPI_CH_B]=0u;
    SPIChannelStatus[SPI_CH_C]=0u;
    SPIChannelStatus[SPI_CH_D]=0u;
    errtmp |= SPI_Config();
    if (errtmp < 0)
    {
#ifdef  _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,SPI_IDX);
#endif
    }
#endif /* _BUILD_SPI_ */

#ifdef _BUILD_SPIMGM_
    SPIMGM_Init();
#endif

    SIU_ConfigStatus = 0u;
    errtmp = 0;
#ifdef BUILD_BIOS_
    errtmp |= SIU_Config();   
    if (errtmp < 0)
    {
#ifdef  _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,SIU_IDX);
#endif
    }     
#endif

#ifdef  _BUILD_ADC_
    ADCConfigStatus = 0u;
    ADC_Config();

#endif /* _BUILD_ADC_ */

#ifdef  _BUILD_DMA_
    DMAConfigStatus = 0u;
    errtmp = 0;
    errtmp |= DMA_Config();

    /* Configure DMA multiplexer for the ION channels*/
    /* ION_0_4 */
    errtmp |= DMAMUX0_PeripheralConfig(DMA_CH3, DMAMUX0_ADC_SAR_4_EOC);
    errtmp |= DMAMUX_Enable(DMAMUX0,DMA_CH3, 1u);
    /* ION_1_5 */
    errtmp |= DMAMUX3_PeripheralConfig(DMA_CH24, DMAMUX3_ADC_SAR_6_EOC);
    errtmp |= DMAMUX_Enable(DMAMUX3,DMA_CH24, 1u);
    /* ION_2_6 */
    errtmp |= DMAMUX0_PeripheralConfig(DMA_CH2, DMAMUX0_ADC_SAR_4_EOC);
    errtmp |= DMAMUX_Enable(DMAMUX0,DMA_CH2, 1u);
    /* ION_3_7 */
    errtmp |= DMAMUX3_PeripheralConfig(DMA_CH25, DMAMUX3_ADC_SAR_6_EOC);
    errtmp |= DMAMUX_Enable(DMAMUX3,DMA_CH25, 1u);

    if (errtmp < 0)
    {
#ifdef  _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,DMA_IDX);
#endif
    } 
#endif /* _BUILD_DMA_ */

#ifdef _BUILD_TASK_
    TASK_ConfigStatus = 0u;
    errtmp = 0;
    errtmp |=  TASK_Config(); 
    if (errtmp < 0)
    {
#ifdef  _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,TASK_IDX);
#endif
    }
#endif /* _BUILD_TASK_ */

#ifdef _BUILD_DIAGCANMGM_

    if (DownloadStruct.BF.ResetResponseAppl == DIAG_ON)
    {
        DownloadStruct.BF.ResetResponseAppl = DIAG_OFF;
        DIAGsession = DIAG_DEFAULT_SESSION;

#ifdef USE_VSRAM_SWUPDATE
        VSRAMMGM_Update();
#endif

#ifdef USE_EEPROM_SWUPDATE
        ResetSwUpdateVars();
#endif
        /* set flag in order to send reset response only after TLE CAN tranciever activation */
        RstRespFlag = 1u;
    }

    if (DownloadStruct.BF.JumpToApplAndWait == DIAG_ON)
    {
        DownloadStruct.BF.JumpToApplAndWait = DIAG_OFF;
        DIAGsession = DIAG_DEFAULT_SESSION;

#ifdef USE_VSRAM_SWUPDATE
        VSRAMMGM_Update();
#endif

        /* set flag in order to send reset response only after TLE CAN tranciever activation */
        DefaultFromBootRespFlag = 1u;
    }
#endif

#ifdef  _BUILD_CAN_
    //CAN RAM initialization
    CAN_InitRAM();

    CANConfigStatus=0u;
    errtmp = 0;
    /* MCAN Config and Enable */
    errtmp |= CAN_Config();
    if (errtmp < 0)
    {
#ifdef  _BUILD_MATHLIB_
        SETBIT(&EE_BiosErr,CAN_IDX);
#endif
    }
    CAN_Enable(VEHICLE_CAN);        //MCAN_1
    CAN_Enable(POWER_TRAIN_CAN);    //MCAN_2
    
#ifdef _BUILD_CCP_
#if (CCP_USE_ENGCAN == CCP_USE_MCAN)
//    CAN_Config();         /* Has already been configured */
    CAN_Enable(CCP_CAN);
#elif (CCP_USE_ENGCAN == CCP_USE_TTCAN)
    TTCAN_Config();
    TTCAN_Enable(CCP_CAN);
#else
#error CAN engine not defined
#endif
#endif /* _BUILD_CCP_ */

#ifdef _BUILD_TPE_
#if (TPE_USE_ENGCAN == TPE_USE_MCAN)
//    CAN_Config();         /* Has already been configured */
//    CAN_Enable(TPE_CAN);  /* Has already been enabled */
#elif (TPE_USE_ENGCAN == TPE_USE_TTCAN)
    TTCAN_Config();
    TTCAN_Enable(TPE_CAN);
#else
#error TPE peripheral not defined
#endif
#endif

#endif /* _BUILD_CAN_ */

#ifdef _BUILD_GTM_
    gtmInit();
    Gtm_Eisb_Tim_Tout_Disable();
#endif

    return;

}

/******************************************************************************
**   Function    : SIU_Config
**
**   Description:
**    This method comprises all the entry points for
**    the SIU System Integration Unit user initialization
**    Ported from the SIU bean of the Processor Expert
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR                        - no error
**    PERIPHERAL_ALREADY_CONFIGURED   - peripherl already configured
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
#ifdef BUILD_BIOS_
static int16_T  SIU_Config(void)
{   
int16_T retCode = NO_ERROR;
#if 0 //MC, da adattare
 
    if (SIU_ConfigStatus == 0u)
    {
        /* SIU_DIRER: EIRE15=0,EIRE14=0,EIRE13=0,EIRE12=0,EIRE11=0,EIRE10=0,EIRE9=0,EIRE8=0,EIRE7=0,EIRE6=0,EIRE5=0,EIRE4=0,EIRE3=0,EIRE2=0,EIRE1=0,EIRE0=0 */
        SIU.DIRER.R= 0x00u;
        /* Solinas : DMA/interrupt request enable register
         */

        /* SIU_DIRSR: DIRS3=0,DIRS2=0,DIRS1=0,DIRS0=0 */
        SIU.DIRSR.R= 0x00u;
        /* Solinas : DMA/interrupt request select register
         */

        /* SIU_ORER: ORE15=0,ORE14=0,ORE13=0,ORE12=0,ORE11=0,ORE10=0,ORE9=0,ORE8=0,ORE7=0,ORE6=0,ORE5=0,ORE4=0,ORE3=0,ORE2=0,ORE1=0,ORE0=0 */
        SIU.ORER.R= 0x00u;
        /* Solinas : Overrun request enable register
         */

        /* SIU_IREER: IREE15=1,IREE14=1,IREE13=1,IREE12=1,IREE11=1,IREE10=1,IREE9=1,IREE8=1,IREE7=1,IREE6=1,IREE5=1,IREE4=1,IREE3=1,IREE2=1,IREE1=1,IREE0=1 */
        SIU.IREER.R=0x00u;
        /* Solinas : IRQ rising-edge event enable register
                 IRQ pins: [0-15] */

        /* SIU_IFEER: IREE15=0,IREE14=0,IREE13=0,IREE12=0,IREE11=0,IREE10=0,IREE9=0,IREE8=0,IREE7=0,IREE6=0,IREE5=0,IREE4=0,IREE3=0,IREE2=0,IREE1=0,IREE0=0 */
        SIU.IFEER.R=0x00u;
        /* Solinas : IRQ falling-edge event enable register
         */

        /* SIU_IDFR: DFL=0 */
        SIU.IDFR.R= 0x00u;
        /* Solinas : IRQ digital filter register
         */


        /* SIU_PCR4: PA=1,IBE=0,DSC=0,ODE=0,HYS=0,WPE=0,WPS=0 */
        /* EBI  initialization */
        /* Solinas : Pad configuration registers 0-230
                 Note: check this configuiration cause conflict with
                 a previous initialization
         */
    }
    else
    {
        retCode = PERIPHERAL_ALREADY_CONFIGURED;
    }
#endif
    return (retCode);

}  
#endif //BUILD_BIOS_

/******************************************************************************
**   Function    : CPU_TIMER_Config
**
**   Description:
**    CPU TIMER configuration
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void  CPU_TIMER_Config(void)
{

    /*Insert here the initialization code for the CPU Timer*/
    /*
     ** -------------------------------------
     ** Timer facilities initialization:
     ** Time Base disabled
     ** Decrementer initialization:
     ** Decrementer interval: 0 ns
     ** Auto-reload:          Disabled
     ** Reloaded interval:    0 ns
     ** Interrupt:            Disabled
     ** Fixed-interval timer initialization:
     ** Timer period:         (1/FSYS)*1000 ns
     ** Interrupt:            Disabled
     ** Watchdog timer initialization:
     ** Timer period:         (1/FSYS)*1000 ns
     ** After time-out:       No action
     ** Interrupt:            Disabled
     */

    //#ifdef _BUILD_WDT_

    //  setSpecReg32SPR_DECAR(0x00);
    //  setSpecReg32SPR_DEC(0x00);
    //#else
    //  union SPR_HID0VAL tmp;

    //  setSpecReg32SPR_HID0(getSpecReg32SPR_HID0() & ~0x4000);
    //  setSpecReg32SPR_DECAR(0x00);
    //  setSpecReg32SPR_DEC(0x00);
    //  setSpecReg32SPR_TCR(0x00);

    //
    //  setSpecReg32TBU(0);
    //  setSpecReg32TBL(0);
    //
    //  tmp.R = getSpecReg32(SPR_HID0);
    //  tmp.B.TBEN = 1;
    //  setSpecReg32(SPR_HID0,tmp.R);
    //#endif // _BUILD_WDT_

}                    

/******************************************************************************
**   Function    : app_data_c0_section_init
**
**   Description:
**    Initialization app data c0 section
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void app_data_c0_section_init(void)
{
    extern uint32_T __DATA_C0_ROM;
    extern uint32_T __DRAM0_DATA_CPY_START;
    extern uint32_T __DATAC0_COPY_SIZE;
    memcpy(&__DRAM0_DATA_CPY_START , &__DATA_C0_ROM, (size_t)&__DATAC0_COPY_SIZE);
}


/******************************************************************************
**   Function    : app_text_c0_section_init
**
**   Description:
**    Initialization app text c0 section 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void app_text_c0_section_init(void)
{
    extern uint32_T __INST_C0_ROM;
    extern uint32_T __IRAM0_TEXT_CPY_START;
    extern uint32_T __TEXTC0_COPY_SIZE;
    memcpy(&__IRAM0_TEXT_CPY_START, &__INST_C0_ROM, (size_t)&__TEXTC0_COPY_SIZE);
}


#if 0
void * GetApplicationStartup (void)
{
    return ((void*)appManager);
}
#endif
#ifdef __MWERKS__

#pragma force_active on

uint8_T* emty_src;

/******************************************************************************
**   Function    : Init_Global_start
**
**   Description:
**    Initialization Global Start
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void Init_Global_start(void)
{
    extern uint32_T __GLOBAL_START;

    emty_src = (uint8_T* ) (& __GLOBAL_START);

    return;         
} 

/******************************************************************************
**   Function    : Init_Global_end
**
**   Description:
**    Initialization Global End
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void Init_Global_end(void)
{
    extern uint32_T __GLOBAL_END;

    emty_src = (uint8_T* ) (& __GLOBAL_END);

    return;         
}  

/******************************************************************************
**   Function    : Init_calib_RAM_start
**
**   Description:
**    Initialization Calib Ram Start
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void Init_calib_RAM_start(void)
{
    extern uint32_T __CALIB_RAM_START;

    emty_src = (uint8_T* ) (& __CALIB_RAM_START);

    return;         
} 

/******************************************************************************
**   Function    : Init_calib_RAM_end
**
**   Description:
**    Initialization Calib Ram End
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void Init_calib_RAM_end(void)
{
    extern uint32_T __CALIB_RAM_END; 

    emty_src = (uint8_T* ) (& __CALIB_RAM_END);

    return;         
} 

#pragma force_active off


#endif
 
/****************************************************************************
 ****************************************************************************/
