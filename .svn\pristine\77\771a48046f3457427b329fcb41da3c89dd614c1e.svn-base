#-------------------------------------------------------------------------------
# Name:        modulo1
# Purpose:
#
# Author:      SalimbeniT
#
# Created:     04/12/2013
# Copyright:   (c) SalimbeniT 2013
# Licence:     <your licence>
#-------------------------------------------------------------------------------
import os
import glob

def main():
    pass

if __name__ == '__main__':
    main()


path=str(os.path.abspath(__file__))
path=path[:-18]
file_c=[]


for files in os.walk(path+"tree"):

    Nomi=files[2]
    Directory=files[0]
    file_c.append((Nomi, Directory))


file_opened=[]
cal=[]
calibrazioni=[]
flg_parse_err=0
flg_parse_err_test=0
errori=[]
test=[]
for i in file_c:
    for j in i[0]:
        if j[-1]=="c":
            parse_file=open(str(i[1])+"\\"+str(j),"r")
            for riga in parse_file:
                if ("CALQUAL" in riga)&(";" in riga):
                    if"///" in riga_old:
                        Commento=riga_old.split("///")[1].strip()
                    else:
                        Commento="COMMENTO MANCANTE"
                    nome_cal=(riga.split()[2])
                    if "[" in nome_cal:
                        nome_cal=(nome_cal.split("[")[0]).strip()
                    if nome_cal=="":
                        flg_parse_err=1
                    if (nome_cal[-1]==";")|(nome_cal[-1]=="="):
                        nome_cal=nome_cal[:-2]
                    cal.append(nome_cal)
                    calibrazioni.append((nome_cal,str(j),Commento))
                riga_old=riga

            testo=parse_file.readline()
            file_opened.append(testo)
            parse_file.close()

for i in file_c:
    for j in i[0]:
        if j[-1]=="c":
            parse_file=open(str(i[1])+"\\"+str(j),"r")
            for riga in parse_file:
                if ("CCPTEST" in riga)&(";" in riga):

                    nome_test=(riga.split()[2])
                    if nome_test=="":
                        flg_parse_err_test=1
                    if "[" in nome_test:
                        nome_test=(nome_test.split("[")[0]).strip()
                    if (nome_test[-1]==";")|(nome_test[-1]=="="):
                        nome_test=nome_test[:-1]


                    test.append(nome_test)
            testo=parse_file.readline()
            file_opened.append(testo)
            parse_file.close()


if flg_parse_err==1:
    print("errore di parsing dei CALQUAL")

if flg_parse_err_test==1:
    print("errore di parsing dei CCPTEST")

a2l=open(path+"GHS\\bin\\I1TB1D\\I1TB1D.a2l","r")
mancanti_cal=[]
mancanti_test=[]
for cal in cal:
    flg=0
    a2l.seek(0)
    for riga in a2l:
        if cal in riga:
            flg=1
    if flg==0:
        mancanti_cal.append(cal)


for test in test:
    flg=0
    a2l.seek(0)
    for riga in a2l:
        if test in riga:
            flg=1
    if flg==0:
        mancanti_test.append(test)


output=open("A2l_Report.txt","w")

output.write("CALIBRAZIONI MANCANTI:\n\n")

for i in mancanti_cal:
    output.write(i+"\n")

output.write("\n\nTEST POINT MANCANTI:\n\n")

for i in mancanti_test:
    output.write(i+"\n")


output.write("\n\nTUTTE LE CALIBRAZIONI:\n\n")
for i in calibrazioni:
    tmp=(i[0]+"."*(30-len(i[0]))+ "Definito in: "+i[1])
    output.write(tmp+"."*(80-len(tmp))+"Commento:  "+i[2]+"\n")
output.close()
a2l.close()

##osCommandString = "notepad.exe "+path+"AAU\\A2l_Report.txt"
##os.system(osCommandString)
