/*****************************************************************************************************************/
/* $HeadURL::                                                                                                                                  $   */
/* $ Description:                                                                                                                                                                                    */
/* $Revision::        $                                                                                                                                                                            */
/* $Date::                                                $                                                                                                                       */
/* $Author::                         $                                                                                                                                                            */
/*****************************************************************************************************************/

#ifdef _BUILD_CPUMGM_

/*! \mainpage CpuMgm
 
\section intro Introduction
\brief CPU reset causes management and relative diagnosis


*/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "cpumgm.h"

/*!
\defgroup PublicVariables Public Variables
\brief Global variables (output).

This group contains the variables with a \a global scope that are exported for the other modules
They represent the \a output of the module, according with the SW architecture.

\sgroup
*/

/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/// uC reset type
uint8_T ResetType = 0xffu;

/*!
\egroup
*/

/*!
\defgroup PrivateVariables Private Variables
\brief Variables with module scope

\sgroup
*/
/*-----------------------------------*
 * PRIVATE VARIABLE DEFINITIONS
 *-----------------------------------*/

/*!\egroup*/ 

/*!
\defgroup PublicFunctions Public Functions
\brief Functions exported to other modules

\sgroup
*/
/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/

/***************************************************************************/
//   Function    :   CpuMgm_Initialize
//
//   Description:    
/*! \brief CPU reset causes management and relative diagnosis
*/

//  Parameters and Returns:
/*! 
\param void
\returns void
*/
//  Notes:
/*!
This function does:
 - Setting of the global variable ResetType with the reset cause that can be read in the special register
 - Grouping of reset causes to set the variable TmpPowerOnType
 - Setting of a flag to force the EE writing at shutdown
 - Calling, in case of repeated illegal interrupts, of the diagnosis machine.

Usually this function is called by the power on task but it could be also called at particular events.
*/
/**************************************************************************/
void CpuMgm_Initialize(void)
{   
    // Reads the rsr register and writes the global variables ResetType
    CpuMgm_GetLastReset(&ResetType);
}

/*==================================================================================================
                                       PRIVATE FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   Private function name
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this private function 
*/
/**************************************************************************/
static int16_T CpuMgm_GetLastReset (uint8_T *lastReset)
{
    if (SIU.RSR.B.PORS == 1u) /*  Power-On Reset Status */
    {
        *lastReset = POWERONRESET;
    }
    else if (SIU.RSR.B.LLRS == 1u) /*  Loss of Lock Reset Status */
    {
        *lastReset = LOSSOFLOCKRESET;
    }
    else if (SIU.RSR.B.LCRS == 1u) /*  Loss of Clock Reset Status */
    {
        *lastReset = LOSSOFCLOCKRESET;
    }
    else if (SIU.RSR.B.ERS == 1u) /*  External Reset Status */
    {
        *lastReset = EXTERNALRESET;
    }
    else if (SIU.RSR.B.WDRS == 1u) /*  Watchdog Timer/Debug Reset Status */
    {
        *lastReset = WATCHDOGRESET;
    }
    else if (SIU.RSR.B.SSRS == 1u) /*  Software System Reset Status */
    {
        *lastReset = SWSYSRESET;
    }
    else
    {
        /* NONE */
    }

    return NO_ERROR;
}

void FuncEON(uint8_T *ptFautRam)
{
    uint8_T stdiag;

    AnalogIn_Diag_ADC();

    /* Diagnosi DIAG_RAM */
}

#else
#endif

