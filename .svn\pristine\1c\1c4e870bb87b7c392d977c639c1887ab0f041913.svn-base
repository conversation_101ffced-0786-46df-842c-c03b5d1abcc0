/*****************************************************************************************************************/
/* $HeadURL::                                                                                                                                        $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmOut
**  Filename        :  CanMgmOut_FTel_calib.c
**  Created on      :  01-dec-2020 12:01:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_CANMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "CanMgmOut_FTel.h"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
#pragma ghs section rodata=".calib"

/* Maximum odometer Telemetry MSG TX enable (if TELTXxEN = 2) */
CALQUAL CALQUAL_POST uint32_T TELTXENODOMAX = 1000u;
/* Raster transmission rate (0 = disabled) */
CALQUAL CALQUAL_POST uint8_T VTCANTXRASTER[TOT_NUM_RASTERS] = {0u, 0u, 0u, 0u};

/* Telemetry Raster SET 1 Enable (1, 2 & TotOdometer <= TELTXENODOMAX): [ID1 ID2 ID3 ID4 ID5 ID6 ID7 ID8 ID9 ID10 ID11 ID12 ID13] */
CALQUAL CALQUAL_POST uint8_T VTCANTXSET1MESSAGE[MAX_RASTER_SIZE] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
/* Telemetry Raster SET 2 Enable (1, 2 & TotOdometer <= TELTXENODOMAX): [ID1 ID2 ID3 ID4 ID5 ID6 ID7 ID8 ID9 ID10 ID11 ID12 ID13] */
CALQUAL CALQUAL_POST uint8_T VTCANTXSET2MESSAGE[MAX_RASTER_SIZE] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
/* Telemetry Raster SET 3 Enable (1, 2 & TotOdometer <= TELTXENODOMAX): [ID1 ID2 ID3 ID4 ID5 ID6 ID7 ID8 ID9 ID10 ID11 ID12 ID13] */
CALQUAL CALQUAL_POST uint8_T VTCANTXSET3MESSAGE[MAX_RASTER_SIZE] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};
/* Telemetry Raster SET 4 Enable (1, 2 & TotOdometer <= TELTXENODOMAX): [ID1 ID2 ID3 ID4 ID5 ID6 ID7 ID8 ID9 ID10 ID11 ID12 ID13] */
CALQUAL CALQUAL_POST uint8_T VTCANTXSET4MESSAGE[MAX_RASTER_SIZE] = {0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u, 0u};

#endif
/****************************************************************************
 ****************************************************************************/

