/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           LivenessMgm.c
 **  File Creation Date: 13-Dec-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         LivenessMgm
 **  Model Description:
 **  Model Version:      1.119
 **  Model Author:       PanettaM - Mon Aug 23 14:16:58 2021
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Mon Dec 13 16:28:05 2021
 **
 **  Last Saved Modification:  RoccaG - Mon Dec 13 16:14:12 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "LivenessMgm_out.h"
#include "LivenessMgm_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define CNT_LIVENESS_STEP              1U                        /* Referenced by: '<S3>/LevelLiveness_mgm' */

/* Step to increase the counter for period and duty calculation (this step is equal to the time between two  task executions) */
#define ID_VER_LIVENESSMGM_DEF         1119U                     /* Referenced by: '<S1>/Constant8' */

/* ID model version define */
#define IN_LIVENESS_OFF                0                         /* Referenced by: '<S3>/LevelLiveness_mgm' */

/* Level OFF of the polarity */
#define IN_LIVENESS_ON                 1                         /* Referenced by: '<S3>/LevelLiveness_mgm' */

/* Level ON of the polarity */
#define LEV_LIVENESS_0                 0U                        /* Referenced by:
                                                                  * '<S3>/LevelLiveness_mgm'
                                                                  * '<S3>/LEV_LIVENESS_0'
                                                                  */

/* Define for level liveness 0 */
#define LEV_LIVENESS_1                 1U                        /* Referenced by:
                                                                  * '<S3>/LevelLiveness_mgm'
                                                                  * '<S3>/LEV_LIVENESS_1'
                                                                  */

/* Define for level liveness 1 */
#define LEV_LIVENESS_2                 2U                        /* Referenced by:
                                                                  * '<S3>/LevelLiveness_mgm'
                                                                  * '<S3>/LEV_LIVENESS_2'
                                                                  */

/* Define for level liveness 2 */
#define LIVENESSPERIOD_DEF             1U                        /* Referenced by: '<S3>/LevelLiveness_mgm' */

/* Default value for liveness period */
#define ONE                            1U                        /* Referenced by: '<S3>/LevelLiveness_mgm' */

/* Define for number 1 */
#define PERCENT_DEF                    100U                      /* Referenced by: '<S3>/LevelLiveness_mgm' */

/* Define for number 100 */
#define ZERO                           0U                        /* Referenced by: '<S3>/LevelLiveness_mgm' */

/* Define value for number 0 */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_LIVENESSMGM_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static boolean_T inLevelLiveness;      /* '<S3>/LevelLiveness_mgm' */
static boolean_T levelliveness_init;   /* '<S2>/Merge8' */

/* Used to indicate the first 5ms event after the PowerOn event, in order to disable diagnosis for this task and start the liveness period. */
static uint8_T write_channel_liveness; /* '<S3>/LevelLiveness_mgm' */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T LIVENESSDUTY = 50U;/* Referenced by: '<S3>/LIVENESSDUTY' */

/* Liveness duty */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T LIVENESSMAXRETRY = 4U;
                                    /* Referenced by: '<S3>/LIVENESSMAXRETRY' */

/* Maximum Liveness retry for SC_TO_VBATT */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T LIVENESSPERIOD = 2U;
                                      /* Referenced by: '<S3>/LIVENESSPERIOD' */

/* Liveness Period */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T LIVENESSPERIODREC = 4U;
                                   /* Referenced by: '<S3>/LIVENESSPERIODREC' */

/* Liveness Period recovery */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T LIVENESSPOLARITY = 0;
                                    /* Referenced by: '<S3>/LIVENESSPOLARITY' */

/* Liveness polarity */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T LIVENESSPOLARITYDUTY = 1;
                                /* Referenced by: '<S3>/LIVENESSPOLARITYDUTY' */

/* Liveness polarity duty */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T LIVENESSRECPEROFF = 20U;
                                   /* Referenced by: '<S3>/LIVENESSRECPEROFF' */

/* Number of periods in STUCK_HIGH fault where the liveness signal is mainteined at OFF level. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T LIVENESSRECPERONGAIN = 2U;
                                /* Referenced by: '<S3>/LIVENESSRECPERONGAIN' */

/* Gain for number of periods in STUCK_HIGH fault after LIVENESSRECPEROFF, where the liveness signal has the PWM profile . */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VMIDHIVLIVENESS = 2013U;
                                     /* Referenced by: '<S3>/VMIDHIVLIVENESS' */

/* Threshold over that the liveness signal is considered high. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VMIDLOVLIVENESS = 1342U;
                                     /* Referenced by: '<S3>/VMIDLOVLIVENESS' */

/* Threshold below that the liveness signal is considered low. */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
boolean_T LevelLiveness;               /* '<S2>/Merge1' */

/* Liveness logical level */
uint8_T LevelLivenessFbk;              /* '<S2>/Merge' */

/* Status of Liveness feedback */
uint8_T LivenessDuty;                  /* '<S2>/Merge3' */

/* Liveness duty */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_LivenessMgm;/* '<S1>/Constant8' */

/* ID model version */
STATIC_TEST_POINT enum_PtFault LivenessPtFault;/* '<S2>/Merge4' */

/* Punctual fault for Liveness diagnosis */
STATIC_TEST_POINT uint16_T cntLivenessDelay;/* '<S2>/Merge6' */

/* Number of periods to recover the liveness signal */
STATIC_TEST_POINT uint8_T cntLivenessPeriod;/* '<S2>/Merge2' */

/* Timer to count the liveness signal period */
STATIC_TEST_POINT uint8_T cntLivenessRetry;/* '<S2>/Merge7' */

/* Number of maximum attempts to try to recover the liveness signal. */
STATIC_TEST_POINT uint8_T cntLivenessTAct;/* '<S2>/Merge5' */

/* Timer to count the liveness signal duty */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void LivenessMgm_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/LivenessMgm_PowerOn' incorporates:
   *  SubSystem: '<Root>/SetupData'
   *
   * Block description for '<Root>/SetupData':
   *  Initialization at Power On for each variable (output, test-point) used
   *  in the model.
   */
  /* SignalConversion generated from: '<S1>/LevelLivenessFbk' incorporates:
   *  Constant: '<S1>/Constant1'
   */
  LevelLivenessFbk = 0U;

  /* SignalConversion generated from: '<S1>/LevelLiveness' incorporates:
   *  Constant: '<S1>/Constant10'
   */
  LevelLiveness = false;

  /* SignalConversion generated from: '<S1>/LivenessDuty' incorporates:
   *  Constant: '<S1>/Constant2'
   */
  LivenessDuty = 0U;

  /* SignalConversion generated from: '<S1>/LivenessPtFault' incorporates:
   *  Constant: '<S1>/Constant4'
   */
  LivenessPtFault = NO_PT_FAULT;

  /* SignalConversion generated from: '<S1>/cntLivenessPeriod' incorporates:
   *  Constant: '<S1>/Constant3'
   */
  cntLivenessPeriod = 0U;

  /* SignalConversion generated from: '<S1>/cntLivenessTAct' incorporates:
   *  Constant: '<S1>/Constant5'
   */
  cntLivenessTAct = 0U;

  /* SignalConversion generated from: '<S1>/cntLivenessDelay' incorporates:
   *  Constant: '<S1>/Constant6'
   */
  cntLivenessDelay = 0U;

  /* SignalConversion generated from: '<S1>/cntLivenessRetry' incorporates:
   *  Constant: '<S1>/Constant7'
   */
  cntLivenessRetry = 0U;

  /* SignalConversion generated from: '<S1>/levelliveness_init' incorporates:
   *  Constant: '<S1>/Constant9'
   */
  levelliveness_init = true;

  /* Constant: '<S1>/Constant8' */
  IdVer_LivenessMgm = ID_VER_LIVENESSMGM_DEF;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/LivenessMgm_PowerOn' */
}

/* Model step function */
void LivenessMgm_T5ms(void)
{
  /* local block i/o variables */
  uint8_T rtb_DiagMgm_SetDiagState;
  uint8_T livenessPeriod_tmp;
  uint8_T livenessDuty_tmp;
  boolean_T LevelLiveness_h;
  uint8_T LevelLivenessFbk_o;
  uint16_T cntLivenessDelay_d;
  uint8_T cntLivenessRetry_p;
  boolean_T levelliveness_init_h;
  enum_PtFault LivenessPtFault_p;
  uint8_T cntLivenessPeriod_e;
  uint8_T cntLivenessTAct_ij;

  /* RootInportFunctionCallGenerator generated from: '<Root>/LivenessMgm_T5ms' incorporates:
   *  SubSystem: '<Root>/T5ms'
   *
   * Block description for '<Root>/T5ms':
   *  Liveness management on 5ms event.
   */
  /* Switch: '<S3>/Switch' incorporates:
   *  Constant: '<S3>/LEV_LIVENESS_1'
   *  Constant: '<S3>/LEV_LIVENESS_2'
   *  Constant: '<S3>/VMIDHIVLIVENESS'
   *  Constant: '<S3>/VMIDLOVLIVENESS'
   *  Inport: '<Root>/VVFdbkLiveness'
   *  RelationalOperator: '<S3>/Relational Operator'
   *  RelationalOperator: '<S3>/Relational Operator1'
   *  Switch: '<S3>/Switch1'
   *
   * Block requirements for '<S3>/VMIDHIVLIVENESS':
   *  1. EISB_FCA6CYL_SW_REQ_1518: The software shall compare the feedback liveness signal IDE_FS_LIV... (ECU_SW_Requirements#2979)
   *
   * Block requirements for '<S3>/VMIDLOVLIVENESS':
   *  1. EISB_FCA6CYL_SW_REQ_1517: The software shall compare the feedback liveness signal IDE_FS_LIV... (ECU_SW_Requirements#2978)
   */
  if (VVFdbkLiveness > VMIDHIVLIVENESS) {
    LevelLivenessFbk_o = ((uint8_T)LEV_LIVENESS_1);
  } else if (VVFdbkLiveness < VMIDLOVLIVENESS) {
    /* Switch: '<S3>/Switch1' incorporates:
     *  Constant: '<S3>/LEV_LIVENESS_0'
     */
    LevelLivenessFbk_o = ((uint8_T)LEV_LIVENESS_0);
  } else {
    LevelLivenessFbk_o = ((uint8_T)LEV_LIVENESS_2);
  }

  /* End of Switch: '<S3>/Switch' */

  /* Chart: '<S3>/LevelLiveness_mgm' incorporates:
   *  Constant: '<S3>/LIVENESSDUTY'
   *  Constant: '<S3>/LIVENESSMAXRETRY'
   *  Constant: '<S3>/LIVENESSPERIOD'
   *  Constant: '<S3>/LIVENESSPERIODREC'
   *  Constant: '<S3>/LIVENESSPOLARITY'
   *  Constant: '<S3>/LIVENESSPOLARITYDUTY'
   *  Constant: '<S3>/LIVENESSRECPEROFF'
   *  Constant: '<S3>/LIVENESSRECPERONGAIN'
   *  Inport: '<Root>/BrokenFuseDX'
   *  Inport: '<Root>/BrokenFuseSX'
   *  Inport: '<Root>/PtFault'
   *  Inport: '<Root>/StDiag'
   *  Inport: '<Root>/StSBC'
   *  Inport: '<Root>/VtRec'
   *  SignalConversion generated from: '<S3>/LevelLiveness_old'
   *  SignalConversion generated from: '<S3>/LivenessPtFault_old'
   *  SignalConversion generated from: '<S3>/cntLivenessDelay_old'
   *  SignalConversion generated from: '<S3>/cntLivenessPeriod_old'
   *  SignalConversion generated from: '<S3>/cntLivenessRetry_old'
   *  SignalConversion generated from: '<S3>/cntLivenessTAct_old'
   *  SignalConversion generated from: '<S3>/levelliveness_init_old'
   */
  /* Gateway: T5ms/LevelLiveness_mgm */
  /* During: T5ms/LevelLiveness_mgm */
  /* This stateflow performs three main operations:
     1. Call of the diagnosis machine;
     2. Liveness period definition;
     3. Calculation of the liveness signal.
     These are performed only if the Loads Test, which commands also the Liveness digital output, is concluded (StSBC==SBC_MODE_NORMAL). */
  /* Entry Internal: T5ms/LevelLiveness_mgm */
  /* Transition: '<S6>:162' */
  LivenessPtFault_p = LivenessPtFault;
  LevelLiveness_h = LevelLiveness;
  cntLivenessPeriod_e = cntLivenessPeriod;
  cntLivenessTAct_ij = cntLivenessTAct;
  cntLivenessDelay_d = cntLivenessDelay;
  cntLivenessRetry_p = cntLivenessRetry;
  if (((uint32_T)StSBC) != SBC_MODE_NORMAL) {
    /* Transition: '<S6>:202' */
    levelliveness_init_h = true;
  } else {
    /* Transition: '<S6>:182' */
    if (!levelliveness_init) {
      /* Transition: '<S6>:197' */
      /* Diagnosis is enabled at the second 5ms event after the PowerOn */
      if (LevelLivenessFbk_o == ((uint8_T)LEV_LIVENESS_2)) {
        /* Transition: '<S6>:19':
         *  1. EISB_FCA6CYL_SW_REQ_1519: The software shall compare the feedback liveness signal IDE_FS_LIV... (ECU_SW_Requirements#2980)
         */
        LivenessPtFault_p = CIRCUIT_OPEN;

        /* Outputs for Function Call SubSystem: '<S3>/DiagMachine'
         *
         * Block description for '<S3>/DiagMachine':
         *  Diagnosis machine calling.
         */
        /* DataTypeConversion: '<S4>/Data Type Conversion' */
        /* Event: '<S6>:45' */
        cntLivenessPeriod_e = CIRCUIT_OPEN;

        /* S-Function (DiagMgm_SetDiagState): '<S7>/DiagMgm_SetDiagState' incorporates:
         *  Constant: '<S4>/DIAG_LIVENESS'
         */
        DiagMgm_SetDiagState( DIAG_LIVENESS, cntLivenessPeriod_e,
                             &rtb_DiagMgm_SetDiagState);

        /* End of Outputs for SubSystem: '<S3>/DiagMachine' */
        /* Transition: '<S6>:188' */
        /* Transition: '<S6>:54' */
        /* Transition: '<S6>:189' */
        /* Transition: '<S6>:58' */
        /* Transition: '<S6>:55' */
      } else {
        /* Transition: '<S6>:194' */
        if (LevelLivenessFbk_o == ((uint8_T)LEV_LIVENESS_0)) {
          /* Transition: '<S6>:4':
           *  1. EISB_FCA6CYL_SW_REQ_1517: The software shall compare the feedback liveness signal IDE_FS_LIV... (ECU_SW_Requirements#2978)
           */
          if (!LevelLiveness) {
            /* Transition: '<S6>:6' */
            if ((((uint32_T)LivenessPtFault) == SIGNAL_STUCK_HIGH) ||
                (((uint32_T)LivenessPtFault) == NO_PT_FAULT)) {
              /* Transition: '<S6>:8' */
              LivenessPtFault_p = NO_PT_FAULT;

              /* Outputs for Function Call SubSystem: '<S3>/DiagMachine'
               *
               * Block description for '<S3>/DiagMachine':
               *  Diagnosis machine calling.
               */
              /* DataTypeConversion: '<S4>/Data Type Conversion' */
              /* Event: '<S6>:45' */
              cntLivenessPeriod_e = NO_PT_FAULT;

              /* S-Function (DiagMgm_SetDiagState): '<S7>/DiagMgm_SetDiagState' incorporates:
               *  Constant: '<S4>/DIAG_LIVENESS'
               */
              DiagMgm_SetDiagState( DIAG_LIVENESS, cntLivenessPeriod_e,
                                   &rtb_DiagMgm_SetDiagState);

              /* End of Outputs for SubSystem: '<S3>/DiagMachine' */
            } else {
              /* Transition: '<S6>:9' */
            }

            /* Transition: '<S6>:54' */
            /* Transition: '<S6>:189' */
            /* Transition: '<S6>:58' */
            /* Transition: '<S6>:55' */
          } else {
            /* Transition: '<S6>:11' */
            /* Transition: '<S6>:13' */
            /* [LevelLiveness==true] */
            LivenessPtFault_p = SIGNAL_STUCK_LOW;

            /* Outputs for Function Call SubSystem: '<S3>/DiagMachine'
             *
             * Block description for '<S3>/DiagMachine':
             *  Diagnosis machine calling.
             */
            /* DataTypeConversion: '<S4>/Data Type Conversion' */
            /* Event: '<S6>:45' */
            cntLivenessPeriod_e = SIGNAL_STUCK_LOW;

            /* S-Function (DiagMgm_SetDiagState): '<S7>/DiagMgm_SetDiagState' incorporates:
             *  Constant: '<S4>/DIAG_LIVENESS'
             */
            DiagMgm_SetDiagState( DIAG_LIVENESS, cntLivenessPeriod_e,
                                 &rtb_DiagMgm_SetDiagState);

            /* End of Outputs for SubSystem: '<S3>/DiagMachine' */
            /* Transition: '<S6>:189' */
            /* Transition: '<S6>:58' */
            /* Transition: '<S6>:55' */
          }
        } else {
          /* Transition: '<S6>:21':
           *  1. EISB_FCA6CYL_SW_REQ_1518: The software shall compare the feedback liveness signal IDE_FS_LIV... (ECU_SW_Requirements#2979)
           */
          /* [LevelLivenessFbk==LEV_LIVENESS_1] */
          if (LevelLiveness) {
            /* Transition: '<S6>:30' */
            if (((((uint32_T)LivenessPtFault) == SIGNAL_STUCK_LOW) ||
                 (((uint32_T)LivenessPtFault) == CIRCUIT_OPEN)) || (((uint32_T)
                  LivenessPtFault) == NO_PT_FAULT)) {
              /* Transition: '<S6>:34' */
              LivenessPtFault_p = NO_PT_FAULT;

              /* Outputs for Function Call SubSystem: '<S3>/DiagMachine'
               *
               * Block description for '<S3>/DiagMachine':
               *  Diagnosis machine calling.
               */
              /* DataTypeConversion: '<S4>/Data Type Conversion' */
              /* Event: '<S6>:45' */
              cntLivenessPeriod_e = NO_PT_FAULT;

              /* S-Function (DiagMgm_SetDiagState): '<S7>/DiagMgm_SetDiagState' incorporates:
               *  Constant: '<S4>/DIAG_LIVENESS'
               */
              DiagMgm_SetDiagState( DIAG_LIVENESS, cntLivenessPeriod_e,
                                   &rtb_DiagMgm_SetDiagState);

              /* End of Outputs for SubSystem: '<S3>/DiagMachine' */
            } else {
              /* Transition: '<S6>:23' */
            }

            /* Transition: '<S6>:50' */
            /* Transition: '<S6>:55' */
          } else {
            /* Transition: '<S6>:26' */
            /* [LevelLiveness==false] */
            LivenessPtFault_p = SIGNAL_STUCK_HIGH;

            /* Outputs for Function Call SubSystem: '<S3>/DiagMachine'
             *
             * Block description for '<S3>/DiagMachine':
             *  Diagnosis machine calling.
             */
            /* DataTypeConversion: '<S4>/Data Type Conversion' */
            /* Event: '<S6>:45' */
            cntLivenessPeriod_e = SIGNAL_STUCK_HIGH;

            /* S-Function (DiagMgm_SetDiagState): '<S7>/DiagMgm_SetDiagState' incorporates:
             *  Constant: '<S4>/DIAG_LIVENESS'
             */
            DiagMgm_SetDiagState( DIAG_LIVENESS, cntLivenessPeriod_e,
                                 &rtb_DiagMgm_SetDiagState);

            /* End of Outputs for SubSystem: '<S3>/DiagMachine' */
          }
        }
      }
    } else {
      /* Transition: '<S6>:195' */
    }

    /* Transition: '<S6>:60' */
    if ((((int32_T)BrokenFuseDX) != 0) || (((int32_T)BrokenFuseSX) != 0)) {
      /* Transition: '<S6>:64':
       *  1. EISB_FCA6CYL_SW_REQ_1319: The software shall change the PWM period at the parameter LIVENESS... (ECU_SW_Requirements#2244)
       */
      livenessPeriod_tmp = LIVENESSPERIODREC;
    } else {
      /* Transition: '<S6>:66':
       *  1. EISB_FCA6CYL_SW_REQ_1318: The software shall command, after the completion of the loads test... (ECU_SW_Requirements#2243)
       */
      livenessPeriod_tmp = LIVENESSPERIOD;
    }

    /* Transition: '<S6>:78' */
    /* Transition: '<S6>:178':
     *  1. EISB_FCA6CYL_SW_REQ_1318: The software shall command, after the completion of the loads test... (ECU_SW_Requirements#2243)
     */
    livenessDuty_tmp = (uint8_T)((int32_T)(((int32_T)((uint32_T)(((uint32_T)
      livenessPeriod_tmp) * ((uint32_T)LIVENESSDUTY)))) / ((int32_T)((uint8_T)
      PERCENT_DEF))));
    if ((((int32_T)VtRec[(REC_CAN_LIVENESS_OFF)]) == 1) || (livenessDuty_tmp <
         ((uint8_T)CNT_LIVENESS_STEP))) {
      /* Transition: '<S6>:84' */
      cntLivenessPeriod_e = ((uint8_T)CNT_LIVENESS_STEP);
      cntLivenessTAct_ij = ((uint8_T)CNT_LIVENESS_STEP);

      /* Transition: '<S6>:99' */
      inLevelLiveness = IN_LIVENESS_OFF;

      /* Transition: '<S6>:148' */
      /* Transition: '<S6>:146' */
      /* Transition: '<S6>:147' */
      /* Transition: '<S6>:129' */
    } else {
      /* Transition: '<S6>:89' */
      if ((cntLivenessPeriod < livenessPeriod_tmp) && (!levelliveness_init)) {
        /* Transition: '<S6>:93':
         *  1. EISB_FCA6CYL_SW_REQ_1318: The software shall command, after the completion of the loads test... (ECU_SW_Requirements#2243)
         */
        cntLivenessPeriod_e = (uint8_T)(cntLivenessPeriod + ((uint8_T)
          CNT_LIVENESS_STEP));
        if (cntLivenessTAct < livenessDuty_tmp) {
          /* Transition: '<S6>:95' */
          cntLivenessTAct_ij = (uint8_T)(cntLivenessTAct + ((uint8_T)
            CNT_LIVENESS_STEP));

          /* Transition: '<S6>:102' */
          /* Transition: '<S6>:146' */
          /* Transition: '<S6>:147' */
          /* Transition: '<S6>:129' */
        } else {
          /* Transition: '<S6>:96' */
          /* Transition: '<S6>:97' */
          /* Transition: '<S6>:99' */
          inLevelLiveness = IN_LIVENESS_OFF;

          /* Transition: '<S6>:148' */
          /* Transition: '<S6>:146' */
          /* Transition: '<S6>:147' */
          /* Transition: '<S6>:129' */
        }
      } else {
        /* Transition: '<S6>:105' */
        cntLivenessPeriod_e = ((uint8_T)CNT_LIVENESS_STEP);
        cntLivenessTAct_ij = ((uint8_T)CNT_LIVENESS_STEP);
        if ((((uint32_T)StDiag[(DIAG_LIVENESS)]) != FAULT) || (((uint32_T)
              PtFault[(DIAG_LIVENESS)]) != SIGNAL_STUCK_HIGH)) {
          /* Transition: '<S6>:108':
           *  1. EISB_FCA6CYL_SW_REQ_1318: The software shall command, after the completion of the loads test... (ECU_SW_Requirements#2243)
           */
          inLevelLiveness = IN_LIVENESS_ON;
        } else {
          /* Transition: '<S6>:110':
           *  1. EISB_FCA6CYL_SW_REQ_2027: The software shall implement a recovery action in case of a confir... (ECU_SW_Requirements#10138)
           */
          /* Recovery procedure */
          if (cntLivenessDelay > ((uint16_T)((int32_T)((((int32_T)((uint32_T)
                    (((uint32_T)LIVENESSRECPERONGAIN) * ((uint32_T)
                      livenessPeriod_tmp)))) / ((int32_T)((uint8_T)
                    CNT_LIVENESS_STEP))) + ((int32_T)LIVENESSRECPEROFF))))) {
            /* Transition: '<S6>:115' */
            inLevelLiveness = IN_LIVENESS_OFF;
            if (cntLivenessRetry <= LIVENESSMAXRETRY) {
              /* Transition: '<S6>:117' */
              cntLivenessRetry_p = (uint8_T)(cntLivenessRetry + ((uint8_T)ONE));
              cntLivenessDelay_d = 0U;
            } else {
              /* Transition: '<S6>:118' */
            }

            /* Transition: '<S6>:126' */
          } else {
            /* Transition: '<S6>:120' */
            if (cntLivenessDelay > ((uint16_T)LIVENESSRECPEROFF)) {
              /* Transition: '<S6>:122' */
              inLevelLiveness = IN_LIVENESS_ON;
            } else {
              /* Transition: '<S6>:123' */
              inLevelLiveness = IN_LIVENESS_OFF;
            }

            /* Transition: '<S6>:125' */
            cntLivenessDelay_d = (uint16_T)(cntLivenessDelay + ((uint16_T)
              ((uint8_T)ONE)));
          }

          /* Transition: '<S6>:128' */
          /* Transition: '<S6>:129' */
        }
      }
    }

    if (inLevelLiveness == LIVENESSPOLARITYDUTY) {
      /* Transition: '<S6>:131' */
      write_channel_liveness = 1U;
      LevelLiveness_h = LIVENESSPOLARITY;
    } else {
      /* Transition: '<S6>:132' */
      write_channel_liveness = 0U;
      LevelLiveness_h = !LIVENESSPOLARITY;
    }

    /* Transition: '<S6>:134' */
    levelliveness_init_h = false;

    /* Outputs for Function Call SubSystem: '<S3>/Dio_WriteChannel'
     *
     * Block description for '<S3>/Dio_WriteChannel':
     *  Write the liveness signal on digital resource.
     */
    /* CCaller: '<S5>/Dio_WriteChannel' incorporates:
     *  Constant: '<S3>/LIVENESSDUTY'
     *  Constant: '<S3>/LIVENESSMAXRETRY'
     *  Constant: '<S3>/LIVENESSPERIOD'
     *  Constant: '<S3>/LIVENESSPERIODREC'
     *  Constant: '<S3>/LIVENESSPOLARITY'
     *  Constant: '<S3>/LIVENESSPOLARITYDUTY'
     *  Constant: '<S3>/LIVENESSRECPEROFF'
     *  Constant: '<S3>/LIVENESSRECPERONGAIN'
     *  Constant: '<S5>/OP_Liveness'
     *  Inport: '<Root>/BrokenFuseDX'
     *  Inport: '<Root>/BrokenFuseSX'
     *  Inport: '<Root>/PtFault'
     *  Inport: '<Root>/StDiag'
     *  Inport: '<Root>/VtRec'
     *  SignalConversion generated from: '<S3>/levelliveness_init_old'
     */
    /* Event: '<S6>:138' */
    Dio_WriteChannel(OP_Liveness, write_channel_liveness);

    /* End of Outputs for SubSystem: '<S3>/Dio_WriteChannel' */
    LivenessDuty = (uint8_T)((int32_T)(((int32_T)((uint32_T)(((uint32_T)
      livenessDuty_tmp) * ((uint32_T)((uint8_T)PERCENT_DEF))))) / ((int32_T)
      livenessPeriod_tmp)));
  }

  /* End of Chart: '<S3>/LevelLiveness_mgm' */

  /* SignalConversion generated from: '<S3>/LevelLiveness' */
  LevelLiveness = LevelLiveness_h;

  /* SignalConversion generated from: '<S3>/LevelLivenessFbk' */
  LevelLivenessFbk = LevelLivenessFbk_o;

  /* SignalConversion generated from: '<S3>/LivenessPtFault' */
  LivenessPtFault = LivenessPtFault_p;

  /* SignalConversion generated from: '<S3>/cntLivenessDelay' */
  cntLivenessDelay = cntLivenessDelay_d;

  /* SignalConversion generated from: '<S3>/cntLivenessPeriod' */
  cntLivenessPeriod = cntLivenessPeriod_e;

  /* SignalConversion generated from: '<S3>/cntLivenessRetry' */
  cntLivenessRetry = cntLivenessRetry_p;

  /* SignalConversion generated from: '<S3>/cntLivenessTAct' */
  cntLivenessTAct = cntLivenessTAct_ij;

  /* SignalConversion generated from: '<S3>/levelliveness_init' */
  levelliveness_init = levelliveness_init_h;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/LivenessMgm_T5ms' */
}

/* Model initialize function */
void LivenessMgm_initialize(void)
{
  /* SystemInitialize for Merge: '<S2>/Merge4' */
  LivenessPtFault = NO_PT_FAULT;
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

boolean_T LevelLiveness;
uint8_T LevelLivenessFbk;
uint8_T LivenessDuty;
void LivenessMgm_initialize(void);
void LivenessMgm_PowerOn(void);
void LivenessMgm_T5ms(void);
void LivenessMgm_initialize(void)
{
}

void LivenessMgm_PowerOn(void)
{
  LevelLiveness= false;
  LevelLivenessFbk= 0U;
  LivenessDuty= 0U;
}

void LivenessMgm_T5ms(void)
{
}

#endif                                 /* _BUILD_LIVENESSMGM_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/