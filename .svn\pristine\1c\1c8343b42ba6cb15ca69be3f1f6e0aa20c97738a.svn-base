/*****************************************************************************************************************/
/* $HeadURL:: file:///E:/Archivi/SVN_Repository/Documentation/SWCG/SWCTemplate/SwcName.h                      $  */
/* $Revision:: XXXX                                                                                           $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/*
 * File: RonDetectMgm_eep.c
 *
 * Code generated for Simulink model 'RonDetectMgm'.
 *
 * Model version                  : 1.1173
 * Simulink Coder version         : 9.2 (R2019b) 18-Jul-2019
 * C/C++ source code generated on : Mon Feb 28 08:11:02 2022
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Custom Processor->Custom Processor
 * Emulation hardware selection:
 *    Differs from embedded hardware (Intel->x86-64 (Windows64))
 * Code generation objectives:
 *    1. Safety precaution
 *    2. MISRA C:2012 guidelines
 *    3. Execution efficiency
 *    4. ROM efficiency
 *    5. RAM efficiency
 *    6. Traceability
 *    7. Debugging
 *    8. Polyspace
 * Validation result: Passed (31), Warning (1), Error (0)
 */
#include "rtwtypes.h"
 
/* Enumerated types definition */
typedef uint8_T enum_StRonDetect;
#define RD_INIT                        ((enum_StRonDetect)0U)    /* Default value */
#define RD_WAIT_STAB                   ((enum_StRonDetect)1U)
#define RD_STAB                        ((enum_StRonDetect)2U)
#define RD_TEST_STOP                   ((enum_StRonDetect)3U)
#define RD_INHERIT                     ((enum_StRonDetect)4U)
 

/* Exported data definition */

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_EE_INTERFACE */
uint8_T FlgRonStoredEE = ((uint8_T)0U);/* '<S3>/Merge44' */

/* Ron stored in EE */
uint32_T OdomRonStartEE = 0U;          /* '<S1>/RonDetectMgm' */

/* Odometer value when Ron detection starts */
uint32_T OdomRonStopEE = 0U;           /* '<S1>/RonDetectMgm' */

/* Odometer value when Ron detection stops */
uint32_T SecRonStartEE = 0U;           /* '<S1>/RonDetectMgm' */

/* Seconds run-time value when Ron detection starts */
uint32_T SecRonStopEE = 0U;            /* '<S1>/RonDetectMgm' */

/* Seconds run-time value when Ron detection stops */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */