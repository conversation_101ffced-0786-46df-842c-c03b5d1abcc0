/*
 * File: mul_s32_s32_s32_sr29.c
 *
 * Code generated for Simulink model 'CoilTarget'.
 *
 * Model version                  : 1.181
 * Simulink Coder version         : 8.7 (R2014b) 08-Sep-2014
 * C/C++ source code generated on : Fri Oct 26 14:46:42 2018
 */

#include "rtwtypes.h"
#include "mul_wide_s32.h"
#include "mul_s32_s32_s32_sr29.h"

int32_T mul_s32_s32_s32_sr29(int32_T a, int32_T b)
{
  uint32_T u32_chi;
  uint32_T u32_clo;
  mul_wide_s32(a, b, &u32_chi, &u32_clo);
  u32_clo = (u32_chi << 3U) | (u32_clo >> 29U);
  return (int32_T)u32_clo;
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
