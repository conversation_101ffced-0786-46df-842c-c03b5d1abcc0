/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_Cache_out.h
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Mocci A
******************************************************************************/
/*****************************************************************************
**
**                     SafetyMngr_Cache Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/
#ifndef SAFETYMNGR_CACHE_OUT_H
#define SAFETYMNGR_CACHE_OUT_H


/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RTWtypes.h"
/* add here include files */

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_Cache_Check_Error
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    uint32_T * p_CacheRegVal
**
**   Returns:
**    void
**
******************************************************************************/
extern void SafetyMngr_Cache_Check_Error(uint32_T * p_CacheRegVal);

#endif // SAFETYMNGR_CACHE_OUT_H
/****************************************************************************
 ****************************************************************************/

