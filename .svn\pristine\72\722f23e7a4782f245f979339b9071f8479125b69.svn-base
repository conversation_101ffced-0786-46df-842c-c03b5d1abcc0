/************************************************************************************/
/* $HeadURL$   */
/* $Description:                                                                $   */
/* $Revision::                                                                  $   */
/* $Date::                                                                      $   */
/* $Author::                                                                    $   */
/************************************************************************************/

#ifndef _DIAGMGMOUT_H_
#define _DIAGMGMOUT_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"
#include "SAE_j2012_122007.h"

/*!
\defgroup PublicDefines Public Defines
\sgroup
 */
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/

/* Identificativi diagnosi */
#define     DIAG_T_AIR              0u  /* Diagnosi TAir                               */
#define     DIAG_T_WATER            1u  /* Diagnosi TWater                             */
#define     DIAG_LOAD               2u  /* Diagnosi Load                               */
#define     DIAG_RPM                3u  /* Diagnosi Rpm                                */
#define     DIAG_CAMLEVEL           4u  /* Diagnosi CamLevel                           */
#define     DIAG_FLGBANKSEL         5u  /* Diagnosi FlgBankSel                         */
#define     DIAG_VBATTERY           6u  /* Diagnosi VBattery                           */
#define     DIAG_DUMMY              7u  /* Diagnosi Dummy                              */
#define     DIAG_ADC                8u  /* Diagnosi ADC                                */
#define     DIAG_FREE_9             9u  /* FREE                                        */
#define     DIAG_FREE_10            10u /* FREE                                        */
#define     DIAG_ION_0              11u /* Diagnosi ion cyl 0                          */
#define     DIAG_ION_1              12u /* Diagnosi ion cyl 1                          */
#define     DIAG_ION_2              13u /* Diagnosi ion cyl 2                          */
#define     DIAG_ION_3              14u /* Diagnosi ion cyl 3                          */
#define     DIAG_ION_4              15u /* Diagnosi ion cyl 4                          */
#define     DIAG_ION_5              16u /* Diagnosi ion cyl 5                          */
#define     DIAG_ION_6              17u /* Diagnosi ion cyl 6                          */
#define     DIAG_ION_7              18u /* Diagnosi ion cyl 7                          */
#define     DIAG_VBAT_CIRCUIT       19u /* Diagnosi circuito batteria                  */
#define     DIAG_COIL_0             20u /* Diagnosi COIL cyl 0                         */
#define     DIAG_COIL_1             21u /* Diagnosi COIL cyl 1                         */
#define     DIAG_COIL_2             22u /* Diagnosi COIL cyl 2                         */
#define     DIAG_COIL_3             23u /* Diagnosi COIL cyl 3                         */
#define     DIAG_COIL_4             24u /* Diagnosi COIL cyl 4                         */
#define     DIAG_COIL_5             25u /* Diagnosi COIL cyl 5                         */
#define     DIAG_COIL_6             26u /* Diagnosi COIL cyl 6                         */
#define     DIAG_COIL_7             27u /* Diagnosi COIL cyl 7                         */
#define     DIAG_ELDOR_SW           28u /* Diagnosi SW altra bancata diverso           */
#define     DIAG_WDT                29u /* Diagnosi WDT                                */
#define     DIAG_PRI_A              30u /* Diagnosi circuito primario                  */
#define     DIAG_PRI_B              31u /* Diagnosi circuito primario                  */
#define     DIAG_LIVENESS           32u /* Diagnosis Liveness pin                      */
#define     DIAG_BANK_MISF          33u /* Diagnosi Misfire su bancata                 */
#define     DIAG_TEMP_ECU_1         34u /* Diagnosi NTC 1                              */
#define     DIAG_TEMP_ECU_2         35u /* Diagnosi NTC 2                              */
#define     DIAG_PRIVATE_CAN        36u /* Diagnosi CAN privato                        */
#define     DIAG_CAN_NODE_1         37u /* Diagnosi nodo assente                       */
#define     DIAG_CPU                38u /* Diagnosi CPU                                */
#define     DIAG_EEPROM             39u /* Diagnosi DIAG_EEPROM                        */
#define     DIAG_KNOCK_COH_0        40u /* Diagnosi coerenza ion cyl 0                 */
#define     DIAG_KNOCK_COH_1        41u /* Diagnosi coerenza ion cyl 1                 */
#define     DIAG_KNOCK_COH_2        42u /* Diagnosi coerenza ion cyl 2                 */
#define     DIAG_KNOCK_COH_3        43u /* Diagnosi coerenza ion cyl 3                 */
#define     DIAG_KNOCK_COH_4        44u /* Diagnosi coerenza ion cyl 4                 */
#define     DIAG_KNOCK_COH_5        45u /* Diagnosi coerenza ion cyl 5                 */
#define     DIAG_KNOCK_COH_6        46u /* Diagnosi coerenza ion cyl 6                 */
#define     DIAG_KNOCK_COH_7        47u /* Diagnosi coerenza ion cyl 7                 */
#define     DIAG_SYNC               48u /* Diagnosi contatore perdite di sincronismo   */
#define     DIAG_TEMP_ECU_3         49u /* Diagnosi Temp CPU                           */
#define     DIAG_SPARK_EV_A         50u /* Diagnosi spark assente                      */
#define     DIAG_SPARK_EV_B         51u /* Diagnosi spark assente                      */
#define     DIAG_BUCK_A             52u /* Diagnosi buck by voltage                    */
#define     DIAG_BUCK_B             53u /* Diagnosi buck by voltage                    */
#define     DIAG_TRIGGER_0          54u /* Diagnosi trigger                            */
#define     DIAG_TRIGGER_1          55u /* Diagnosi trigger                            */
#define     DIAG_TRIGGER_2          56u /* Diagnosi trigger                            */
#define     DIAG_TRIGGER_3          57u /* Diagnosi trigger                            */
#define     DIAG_TRIGGER_4          58u /* Diagnosi trigger                            */
#define     DIAG_TRIGGER_5          59u /* Diagnosi trigger                            */
#define     DIAG_TRIGGER_6          60u /* Diagnosi trigger                            */
#define     DIAG_TRIGGER_7          61u /* Diagnosi trigger                            */
#define     DIAG_SEC_OL_0           62u /* Diagnosi scintilla                          */
#define     DIAG_SEC_OL_1           63u /* Diagnosi scintilla                          */
#define     DIAG_SEC_OL_2           64u /* Diagnosi scintilla                          */
#define     DIAG_SEC_OL_3           65u /* Diagnosi scintilla                          */
#define     DIAG_SEC_OL_4           66u /* Diagnosi scintilla                          */
#define     DIAG_SEC_OL_5           67u /* Diagnosi scintilla                          */
#define     DIAG_SEC_OL_6           68u /* Diagnosi scintilla                          */
#define     DIAG_SEC_OL_7           69u /* Diagnosi scintilla                          */
#define     DIAG_CAN_NODE_OVER_RUN  70u /* Diagnosi overrun Can node 1                 */
#define     DIAG_VEHICLE_CAN        71u /* Diagnosi CAN veicolo                        */
#define     DIAG_SEC_0_4            72u /* Diagnosi coil secondario                    */
#define     DIAG_SEC_1_5            73u /* Diagnosi coil secondario                    */
#define     DIAG_SEC_2_6            74u /* Diagnosi coil secondario                    */
#define     DIAG_SEC_3_7            75u /* Diagnosi coil secondario                    */
#define     DIAG_VCAP_0_4           76u /* Diagnosi VCap                               */
#define     DIAG_VCAP_1_5           77u /* Diagnosi VCap                               */
#define     DIAG_VCAP_2_6           78u /* Diagnosi VCap                               */
#define     DIAG_VCAP_3_7           79u /* Diagnosi VCap                               */
#define     DIAG_IGN                80u /* Diagnosi KL15                               */
#define     DIAG_VCOIL_A_MON        81u /* Diagnosi circuito VCoil                     */
#define     DIAG_VCOIL_B_MON        82u /* Diagnosi circuito VCoil                     */
#define     DIAG_FREE_83            83u /* FREE                                        */
#define     DIAG_FREE_84            84u /* FREE                                        */
#define     DIAG_BOARD_SEL          85u /* Diagnosis Board                             */
#define     DIAG_FREE_86            86u /* FREE                                        */
#define     DIAG_FREE_87            87u /* FREE                                        */
#define     DIAG_KEY_SIGNAL         88u /* Diagnosi KeySignal                          */
#define     DIAG_GTM                89u /* Diagnosi Gtm                                */
#define     DIAG_TLE9278BQX         90u /* Diagnosi SBC                                */
#define     DIAG_ION_CH_A           91u /* Diagnosi IonBufferV CH A                    */
#define     DIAG_ION_CH_B           92u /* Diagnosi IonBufferV CH B                    */
#define     DIAG_ION_CH_C           93u /* Diagnosi IonBufferV CH C                    */
#define     DIAG_ION_CH_D           94u /* Diagnosi IonBufferV CH D                    */
#define     DIAG_FREE_95            95u /* FREE                                        */
#define     DIAG_NUMBER             96u

#define     DIAG_SM1                (DIAG_DUMMY)
#define     DIAG_SM2                (DIAG_DUMMY)
#define     DIAG_SM3                (DIAG_DUMMY)
#define     DIAG_SM4                (DIAG_DUMMY)
#define     DIAG_SM5                (DIAG_DUMMY)
#define     DIAG_SM6                (DIAG_DUMMY)
#define     DIAG_SM7                (DIAG_DUMMY)
#define     DIAG_SM8                (DIAG_DUMMY)

/// NUmber of entries in error memories (two snapshots and one extended data)
#define DIAG_FAULT_LENGTH  12u
/// None diagnosis stored
#define NO_STORED_DIAG      255u

#define FAULT_SYMPTOM_NUMBER    5u /* if FAULT_SYMPTOM_NUMBER changes, must
 * change the getDiagFaultyStatus(...) function
 * implementation too!
 */
#define N_VT_SM_DIAG_CODE 0x22

/*SYMPTOM*/     /* SCU Names */
#define  NO_PT_FAULT                 0u  /*0x0000*/
#if 0
#define  CC_TO_GND                   1u  /*0x0010*/
#define  CC_TO_VCC                   2u  /*0x0001*/
#define  SIGNAL_NOT_VALID            3u  /*0x1000*/
#define  FAULT_CAN_BUSOFF            4u  /*0x0100*/
#define  FAULT_CAN_BUFFER_EMPTY      5u  /*0x0100*/
#define  SIG_NOT_PLAUSIBLE           6u  /*0x1000*/
#define  SIGNAL_NOT_PRESENT          7u  /*0x0100*/
#define  OPEN_CIRCUIT                8u  /*0x0100*/
#define  SHORT_CIRCUIT               9u  /*0x1000*/
//#define  TWO_COHERENT_SENS        10  /*------*/      /*not present*/
//#define  NO_COHERENT_SENS         11  /*------*/      /*not present*/
#define  CC_TO_VBAT                 12u  /*0x0001*/
#define  CIRCUIT_MALFUNCTION        13u  /*0x1000*/
#define  UNDER_VOLTAGE              14u  /*0x0010*/
#define  OVER_TEMPERATURE           15u  /*0x0001*/
//#define  SAFETY_2_FAULT           16  /*------*/
//#define  ECU_BLANK                17  /*------*/      /*not present*/
//#define  INVALID_KEY              18  /*------*/      /*not present*/
#define  OVER_CURRENT               19u  /*0x0001*/      
//#define  SIGNAL_NOT_PLAUSIBLE_0     20u  /*0x0001*/
//#define  SIGNAL_NOT_PLAUSIBLE_1     21u  /*0x0010*/
//#define  SIGNAL_NOT_PLAUSIBLE_2     22u  /*0x0100*/
//#define  SIGNAL_NOT_PLAUSIBLE_3     23u  /*0x1000*/
#define  OVER_VOLTAGE               24u  /*0x0001*/
#define  OVER_LOAD                  25u  /*0x0010*/
#define  FAULT_CAN_BUFFER_OVERRUN   26u  /*0x1000*/
#define  OPEN_CIRCUIT_CURR          27u  /*0x0100*/
#define  OVER_LOAD_CURR             28u  /*0x0010*/
#endif

#define  NO_FAULT                   0u
#define  FAULT_FILTERING            1u
#define  FAULT                      2u

#pragma ghs startnomisra //MISRA 19.4
#define GENERAL_ELECTRIC_FAILURE                            J2012_0x01
#define BUS_SIGNAL_MSG_FAILURE                              J2012_0x08
#define CIRCUIT_SHORT_TO_GND                                J2012_0x11
#define CIRCUIT_SHORT_TO_VCC                                J2012_0x12
#define CIRCUIT_OPEN                                        J2012_0x13
#define CIRCUIT_VOLTAGE_BELOW_THR                           J2012_0x16
#define CIRCUIT_VOLTAGE_ABOVE_THR                           J2012_0x17
#define CIRCUIT_CURRENT_ABOVE_THR                           J2012_0x19
#define MIN_SIGNAL_AMPLITUDE                                J2012_0x21
#define MAX_SIGNAL_AMPLITUDE                                J2012_0x22
#define SIGNAL_STUCK_LOW                                    J2012_0x23
#define SIGNAL_STUCK_HIGH                                   J2012_0x24
#define SIGNAL_INVALID                                      J2012_0x29
#define NO_SIGNAL                                           J2012_0x31
#define SIGNAL_FREQUENCY_INCORRECT                          J2012_0x38
#define DATA_MEMORY_FAILURE                                 J2012_0x44
#define PROGRAM_MEMORY_FAILURE                              J2012_0x45
#define WDT_SAFETY_FAILURE                                  J2012_0x47
#define INTERNAL_ELECTRONIC_FAILURE                         J2012_0x49
#define SIGNAL_COMPARE_FAILURE                              J2012_0x62
#define INVALID_SERIAL_DATA                                 J2012_0x81
#define ALIVE_SEQUENCE_COUNTER_INCORRECT_NOT_UPDATED        J2012_0x82
#define VALUE_OF_SIGNAL_PROTECTION_CALCULATION_INCORRECT    J2012_0x83
#define MISSING_MESSAGE                                     J2012_0x87
#define BUS_OFF                                             J2012_0x88
#define COMPONENT_INTERNAL_FAILURE                          J2012_0x96
#define SIGNAL_NOT_PLAUSIBLE_0                              J2012_0xF0
#define SIGNAL_NOT_PLAUSIBLE_1                              J2012_0xF1
#define SIGNAL_NOT_PLAUSIBLE_2                              J2012_0xF2
#define SIGNAL_NOT_PLAUSIBLE_3                              J2012_0xF3
#define SIGNAL_NOT_PLAUSIBLE_4                              J2012_0xF4

// Alias
#define SAFETY_ERROR_1                          SIGNAL_NOT_PLAUSIBLE_0
#define SAFETY_ERROR_2                          SIGNAL_NOT_PLAUSIBLE_1
#define SAFETY_ERROR_3                          SIGNAL_NOT_PLAUSIBLE_2
#define SAFETY_ERROR_4                          SIGNAL_NOT_PLAUSIBLE_3
#define SAFETY_ERROR_5                          SIGNAL_NOT_PLAUSIBLE_4
#pragma ghs endnomisra //MISRA 19.4


// Numero di colonne di TBDISDIAG
#define  TBDISDIAG_CULS             10u

/* Numero iniziale per i flag di disabilitazione diagnosi */
#define FICT_DIAG_NUMBER            200u

/*****************************************************************/
/**************** DTCStatus and statusOfDTC defines **************/
/*****************************************************************/
/// Bit0 - This bit shall indicate the result of the most recently performed test
#define TEST_FAILED_MASK                                0x01u
/// Bit1 - This bit shall indicate whether or not a diagnostic test has reported a testFailed result at any time during the current operation cycle
#define TEST_FAILED_THIS_OPERATION_CYCLE_MASK           0x02u
/// Bit2 - This bit shall indicate whether or not a diagnostic test has reported a testFailed result at any time during the current or last completed operation cycle
#define PENDING_DTC_MASK                                0x04u
/// Bit3 - This bit shall indicate whether a malfunction was detected enough times to warrant that the DTC is stored in long-term memory (e.g. pendingDTC = 1 one or more times, depending on the DTC confirmation criteria)
#define CONFIRMED_DTC_MASK                              0x08u
/// Bit4 - This bit shall indicate whether a DTC test has ever run and been completed since the last time a call was made to ClearDiagnosticInformation
#define TEST_NOT_COMPLETED_SINCE_LAST_CLEAR_MASK        0x10u
/// Bit 5 - This bit shall indicate whether a DTC test has ever been completed with a failed result since the last time a call was made to ClearDiagnosticInformation (i.e. this is a latched testFailedThisOperationCycle = 1).
#define TEST_FAILED_SINCE_LAST_CLEAR_MASK               0x20u
/// Bit6 - This bit shall indicate whether a DTC test has ever run and been completed during the current operation cycle
#define TEST_NOT_COMPLETED_THIS_OPERATION_CYCLE_MASK    0x40u
/// Bit7 - This bit shall report the status of any warning indicators associated with a particular DTC
#define WARNING_INDICATOR_REQUESTED_MASK                0x80u

#define DIAG_TEST_INIT          0u
#define DIAG_TEST_FILTERING     1u
#define DIAG_TEST_FINISHED      2u

#define NO_RESULT          0u
#define FAILED             1u
#define PASSED             2u

/// VTDIAGENABLE values
#define  DIAG_DISABLED              0u
#define  ENABLE_KEEP_PWON           1u
#define  ENABLE_RESET_PWON          2u
#define  ENABLE_RESET_IGNON         3u

#define SNAP1 1u
#define SNAP2 2u
#define ALL_SNAP 0xffu

/// DTC severity mask according to Table D.12  of ISO 14229-1:2013(E)
#define NSA     0x0u // no  severity available
#define MONLY   0x1u // Maintenance Only
#define CHLANH  0x2u // Check At Next Halt
#define CHKI    0x3u // Check Immediatly

#define ACTIVEFAULT_DIM 5u

/*!\egroup*/
/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
 */
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
/// Valori di PtFault
typedef uint8_T typPtFault;
/// Valori di StDiag
typedef uint8_T typStDiag;

//Nuovo naming convention  per enumerativi
typedef uint8_T enum_StDiag;
typedef uint8_T enum_PtFault;

/// DTC status struct
#pragma ghs startnomisra // MISRA rule 18.4 union not allowed
typedef union 
{
    uint8_T CF;  // Complete Field
    struct
    {
        uint8_T warningIndicatorRequested          :1; //bit7
        uint8_T testNotCompletedThisOperationCycle :1; //bit6
        uint8_T testFailedSinceLastClear           :1; //bit5
        uint8_T testNotCompletedSinceLastClear     :1; //bit4
        uint8_T confirmedDTC                       :1; //bit3
        uint8_T pendingDTC                         :1; //bit2
        uint8_T testFailedThisOperationCycle       :1; //bit1
        uint8_T testFailed                         :1; //bit0
    }BF;     // Bit Field
} DTCStatus; //__attribute__ ((packed));

typedef union 
{
    uint8_T CF;  // Complete Field
    struct
    {
        uint8_T confFTB7 :1; //bit7
        uint8_T confFTB6 :1; //bit6
        uint8_T confFTB5 :1; //bit5
        uint8_T confFTB4 :1; //bit4
        uint8_T confFTB3 :1; //bit3
        uint8_T confFTB2 :1; //bit2
        uint8_T confFTB1 :1; //bit1
        uint8_T confFTB0 :1; //bit0
    }BF;     // Bit Field
} confirmedFTB_T;

#pragma ghs endnomisra

#pragma ghs startnomisra //MISRA 2004 Rule 18.4: union not allowed
typedef union 
{
    uint16_T CF;  // Complete Field
    struct
    {
        uint16_T free          :1; // bit15
        uint16_T RONLevel_4    :3; // bit12-14
        uint16_T RONLevel_3    :3; // bit9-11
        uint16_T RONLevel_2    :3; // bit6-8
        uint16_T RONLevel_1    :3; // bit3-5
        uint16_T RONLevelEE    :3; // bit0-2
    }BF;     // Bit Field
}Snap6002_T;

typedef union 
{
    uint16_T CF;  // Complete Field
    struct
    {
        uint16_T free          :5; // bit11-15
        uint16_T GearPos       :4; // bit7-10
        uint16_T FlgBankSel    :1; // bit6
        uint16_T KeySignal     :1; // bit5
        uint16_T StPlasObj     :2; // bit3-4
        uint16_T ResetType     :3; // bit0-2
    }BF;     // Bit Field
}Snap6003_T;

typedef union 
{
    uint8_T CF;  // Complete Field
    struct
    {
        uint8_T free             :4; //bit4-7
        uint8_T IonSelectCyl     :4; //bit0-3
    }BF;     // Bit Field
}Snap6004_T;


typedef union 
{
    uint8_T CF;  // Complete Field
    struct
    {
        uint8_T free            :1; // bit7
        uint8_T FlgSyncPhased   :1; // bit6
        uint8_T VDGasPosCAN     :1; // bit5
        uint8_T VDTWaterCAN     :1; // bit4
        uint8_T VDTAirCAN       :1; // bit3
        uint8_T VDRpmCAN        :1; // bit2
        uint8_T VDLoadDxCAN     :1; // bit1
        uint8_T VDLoadSxCAN     :1; // bit0
    }BF;     // Bit Field
}Snap6005_T;

typedef union 
{
    uint8_T CF;  // Complete Field
    struct
    {
        uint8_T LastSyncError   :3; // bit5-7
        uint8_T StSync          :2; // bit3-4
        uint8_T NTeethDeleted   :3; // bit0-2
    }BF;     // Bit Field
}Snap6007_T;



#pragma ghs endnomisra 

/// DTC Snapshot base Data definition same for all DTC types
typedef struct
{
    uint32_T  EcuTimeStamps;                   // id 1008
    uint16_T  EcuTimeStampsFromKeyON;          // id 1009
    uint16_T  KeyOnCnt;                        // id 200A
    //uint8_T   ftb;                             // id 6082 AM already stored in 
    uint8_T Hour;                              // id 1060
    uint8_T Minutes;
    uint8_T Day;
    uint8_T Month;
    uint8_T Year;
    uint16_T RpmCalc;                          // id 1010
    uint8_T RpmCAN;                            // id 1011
    uint8_T Load;                              // id 1012
    uint32_T TotOdometerCAN;                   // id 1013
    uint8_T TWater;                            // id 1014
    uint8_T TAir;                              // id 1015
    uint8_T TempECU;                           // id 1016
    uint8_T VBattery;                          // id 1018
    uint8_T GasPos;                            // id 1019
    uint8_T DCTState;                          // id 101A
    uint8_T ILeadObj;                          // id 101B
    uint8_T IPriCorrCyl;                       // id 1024
    uint8_T SAout;                             // id 1025
    uint8_T VehSpeed;                          // id 1026
    uint32_T Seconds1stRunTime;                // id 1027
    Snap6002_T Snap6002;                       // id 6002
    Snap6003_T Snap6003;                       // id 6003
    uint8_T EESACmdInLevErrNoMAX;              // id 41B7
    uint16_T EESACmdInLevErrSum;               // id 41B8
}DTC_Base_T;

/// DTC Snapshot CAN_LVNSS_TEMP Data definition for customized DTC types
typedef struct
{
    uint8_T FlgAnyAbsentPri;                   // id 1035
    uint32_T FlgAnyAbsentVeh;                  // id 1036
    uint8_T VVFdbkLiveness;                    // id 1037
    uint8_T EETempECUMax1;                     // id 1038
    uint8_T EETempECUMax2;                     // id 1039
    uint8_T EETempECUMax3;                     // id 103A
    uint8_T VVTempECU1;                        // id 103B
    uint8_T VVTempECU2;                        // id 103C
    uint8_T VVTempECU3;                        // id 103D
    uint8_T CntRpmCAN;                         // id 103E
    uint8_T CntNoSyncNOsts;                    // id 103F
    uint8_T LoadSx;                            // id 1040
    uint8_T LoadDx;                            // id 1041
    Snap6005_T Snap6005;                       // id 6005
    Snap6007_T Snap6007;                       // id 6007
    uint16_T VBankSel;                         // id 10FE
    uint8_T cntForceResyncSAout;               // id 41B9
    uint16_T SAoutCyl_0;                       // id 41BA
    uint16_T SAoutCyl_1;                       // id 41BB
    uint16_T SAoutCyl_2;                       // id 41BC
    uint16_T SAoutCyl_3;                       // id 41BD
    uint16_T SAoutCyl_4;                       // id 41BE
    uint16_T SAoutCyl_5;                       // id 41BF
    uint16_T SAoutCyl_6;                       // id 41C0
    uint16_T SAoutCyl_7;                       // id 41C1
    uint16_T VtIonKnockEnableCond_0;           // id 41E1
    uint16_T VtIonKnockEnableCond_1;           // id 41E2
    uint16_T VtIonKnockEnableCond_2;           // id 41E3
    uint16_T VtIonKnockEnableCond_3;           // id 41E4
    uint16_T VtIonKnockEnableCond_4;           // id 41E5
    uint16_T VtIonKnockEnableCond_5;           // id 41E6
    uint16_T VtIonKnockEnableCond_6;           // id 41E7
    uint16_T VtIonKnockEnableCond_7;           // id 41E8
} DTC_Can_Lvnss_Temp_T;

/// DTC Snapshot ION_SEC_SPARK_CAP Data definition for customized DTC types
typedef struct
{
    uint16_T ThPeakCyl;                        // id 104D
    uint16_T ChPeakCyl;                        // id 104E
    uint8_T DThPeakCyl;                        // id 104F
    uint8_T DwellIntCyl;                       // id 1050
    uint8_T Start_ionCyl;                      // id 1051
    uint16_T StartChIonCyl;                    // id 1052
    Snap6004_T Snap6004;                       // id 6004
    uint8_T VCharge_0;                         // id 1056
    uint8_T VCharge_1;                         // id 1064
    uint8_T VCharge_2;                         // id 1057
    uint8_T VCharge_3;                         // id 1065
    uint8_T SparkLength_0;                     // id 1058
    uint8_T SparkLength_1;                     // id 1066
    uint8_T SparkLength_2;                     // id 1059
    uint8_T SparkLength_3;                     // id 1067
    uint8_T SparkLength_4;                     // id 105A
    uint8_T SparkLength_5;                     // id 1068
    uint8_T SparkLength_6;                     // id 105B
    uint8_T SparkLength_7;                     // id 1069
    uint8_T VtIShotPeak_0;                     // id 105C
    uint8_T VtIShotPeak_1;                     // id 106A
    uint8_T VtIShotPeak_2;                     // id 105D
    uint8_T VtIShotPeak_3;                     // id 106B
    uint8_T VtIShotPeak_4;                     // id 105E
    uint8_T VtIShotPeak_5;                     // id 106C
    uint8_T VtIShotPeak_6;                     // id 105F
    uint8_T VtIShotPeak_7;                     // id 106D
    uint16_T IntIon_0;                         // id 1060
    uint16_T IntIon_1;                         // id 106E
    uint16_T IntIon_2;                         // id 1061
    uint16_T IntIon_3;                         // id 106F
    uint16_T IntIon_4;                         // id 1062
    uint16_T IntIon_5;                         // id 1070
    uint16_T IntIon_6;                         // id 1063
    uint16_T IntIon_7;                         // id 1071
    uint8_T VtILeadPeak_0;                     // id 101C
    uint8_T VtILeadPeak_1;                     // id 101D
    uint8_T VtILeadPeak_2;                     // id 101E
    uint8_T VtILeadPeak_3;                     // id 101F
    uint8_T VtILeadPeak_4;                     // id 1020
    uint8_T VtILeadPeak_5;                     // id 1021
    uint8_T VtILeadPeak_6;                     // id 1022
    uint8_T VtILeadPeak_7;                     // id 1023
    uint8_T VtIPriCorr_0;                      // id 11A5
    uint8_T VtIPriCorr_1;                      // id 11A6
    uint8_T VtIPriCorr_2;                      // id 11A7
    uint8_T VtIPriCorr_3;                      // id 11A8
    uint8_T VtIPriCorr_4;                      // id 11A9
    uint8_T VtIPriCorr_5;                      // id 11AA
    uint8_T VtIPriCorr_6;                      // id 11AB
    uint8_T VtIPriCorr_7;                      // id 11AC
    uint8_T VtTSparkFilt_0;                    // id 119D
    uint8_T VtTSparkFilt_1;                    // id 119E
    uint8_T VtTSparkFilt_2;                    // id 119F
    uint8_T VtTSparkFilt_3;                    // id 11A0
    uint8_T VtTSparkFilt_4;                    // id 11A1
    uint8_T VtTSparkFilt_5;                    // id 11A2
    uint8_T VtTSparkFilt_6;                    // id 11A3
    uint8_T VtTSparkFilt_7;                    // id 11A4
    uint16_T VChargeObj_0;                     // id 41C2
    uint16_T VChargeObj_1;                     // id 41C3
    uint16_T VChargeObj_2;                     // id 41C4
    uint16_T VChargeObj_3;                     // id 41C5
    uint8_T FlgIShotTout_0;                    // id 41C6
    uint8_T FlgIShotTout_1;                    // id 41C7
    uint32_T VtOLSecInt_0;                     // id 41C8
    uint32_T VtOLSecInt_1;                     // id 41C9
    uint32_T VtOLSecInt_2;                     // id 41CA
    uint32_T VtOLSecInt_3;                     // id 41CB
    uint32_T VtOLSecInt_4;                     // id 41CC
    uint32_T VtOLSecInt_5;                     // id 41CD
    uint32_T VtOLSecInt_6;                     // id 41CE
    uint32_T VtOLSecInt_7;                     // id 41CF
    uint16_T VtIonKnockEnableCond_0;           // id 41E1
    uint16_T VtIonKnockEnableCond_1;           // id 41E2
    uint16_T VtIonKnockEnableCond_2;           // id 41E3
    uint16_T VtIonKnockEnableCond_3;           // id 41E4
    uint16_T VtIonKnockEnableCond_4;           // id 41E5
    uint16_T VtIonKnockEnableCond_5;           // id 41E6
    uint16_T VtIonKnockEnableCond_6;           // id 41E7
    uint16_T VtIonKnockEnableCond_7;           // id 41E8
} DTC_Ion_Sec_Spark_Cap_T;

/// DTC Snapshot COIL-PRI-BUCK-TRIG Data definition for customized DTC types
typedef struct
{
    uint8_T CntIGNTrgInOn_0;                   // id 1072
    uint8_T CntIGNTrgInOn_1;                   // id 1086
    uint8_T CntIGNTrgInOn_2;                   // id 1073
    uint8_T CntIGNTrgInOn_3;                   // id 1087
    uint8_T CntIGNTrgInOn_4;                   // id 1074
    uint8_T CntIGNTrgInOn_5;                   // id 1088
    uint8_T CntIGNTrgInOn_6;                   // id 1075
    uint8_T CntIGNTrgInOn_7;                   // id 1089
    uint8_T EffDwellTime_0;                    // id 1076
    uint8_T EffDwellTime_1;                    // id 108A
    uint8_T EffDwellTime_2;                    // id 1077
    uint8_T EffDwellTime_3;                    // id 108B
    uint8_T EffDwellTime_4;                    // id 1078
    uint8_T EffDwellTime_5;                    // id 108C
    uint8_T EffDwellTime_6;                    // id 1079
    uint8_T EffDwellTime_7;                    // id 108D
    uint8_T VBuck_0;                           // id 107E
    uint8_T VBuck_4;                            // id 107F
    uint8_T VBuck_5;                            // id 1092
    uint8_T VBuck_9;                           // id 1093
    uint8_T VBuck_10;                          // id 1080
    uint8_T VBuck_14;                          // id 1081
    uint8_T VBuck_15;                          // id 1094
    uint8_T VBuck_19;                          // id 1095
    uint8_T VBuck_20;                          // id 1082
    uint8_T VBuck_24;                          // id 1083
    uint8_T VBuck_25;                          // id 1096
    uint8_T VBuck_29;                          // id 1097
    uint8_T VBuck_30;                          // id 1084
    uint8_T VBuck_34;                          // id 1085
    uint8_T VBuck_35;                          // id 1098
    uint8_T VBuck_39;                          // id 1099
    uint8_T VtILeadPeak_0;                     // id 101C
    uint8_T VtILeadPeak_1;                     // id 101D
    uint8_T VtILeadPeak_2;                     // id 101E
    uint8_T VtILeadPeak_3;                     // id 101F
    uint8_T VtILeadPeak_4;                     // id 1020
    uint8_T VtILeadPeak_5;                     // id 1021
    uint8_T VtILeadPeak_6;                     // id 1022
    uint8_T VtILeadPeak_7;                     // id 1023
    uint8_T VtIPriCorr_0;                      // id 11A5
    uint8_T VtIPriCorr_1;                      // id 11A6
    uint8_T VtIPriCorr_2;                      // id 11A7
    uint8_T VtIPriCorr_3;                      // id 11A8
    uint8_T VtIPriCorr_4;                      // id 11A9
    uint8_T VtIPriCorr_5;                      // id 11AA
    uint8_T VtIPriCorr_6;                      // id 11AB
    uint8_T VtIPriCorr_7;                      // id 11AC
    uint8_T VtTSparkFilt_0;                    // id 119D
    uint8_T VtTSparkFilt_1;                    // id 119E
    uint8_T VtTSparkFilt_2;                    // id 119F
    uint8_T VtTSparkFilt_3;                    // id 11A0
    uint8_T VtTSparkFilt_4;                    // id 11A1
    uint8_T VtTSparkFilt_5;                    // id 11A2
    uint8_T VtTSparkFilt_6;                    // id 11A3
    uint8_T VtTSparkFilt_7;                    // id 11A4
    uint16_T ISupplyCoil1ADC;                  // id 41D0
    uint16_T VSupplyCoil1ADC;                  // id 41D1
    uint16_T IBattADC;                         // id 41D2
    uint16_T ISupplyCoil2ADC;                  // id 41D3
    uint16_T VSupplyCoil2ADC;                  // id 41D4
    uint16_T VtIonKnockEnableCond_0;           // id 41E1
    uint16_T VtIonKnockEnableCond_1;           // id 41E2
    uint16_T VtIonKnockEnableCond_2;           // id 41E3
    uint16_T VtIonKnockEnableCond_3;           // id 41E4
    uint16_T VtIonKnockEnableCond_4;           // id 41E5
    uint16_T VtIonKnockEnableCond_5;           // id 41E6
    uint16_T VtIonKnockEnableCond_6;           // id 41E7
    uint16_T VtIonKnockEnableCond_7;           // id 41E8
} DTC_Coil_Pri_Buck_Trig_T;

/// DTC Extended KNOCK Data definition for customized DTC types
typedef struct
{
    uint8_T KCohDiagCnt_0;                     // id 109A
    uint8_T KCohDiagCnt_1;                     // id 109B
    uint8_T KCohDiagCnt_2;                     // id 109C
    uint8_T KCohDiagCnt_3;                     // id 109D
    uint8_T KCohDiagCnt_4;                     // id 109E
    uint8_T KCohDiagCnt_5;                     // id 109F
    uint8_T KCohDiagCnt_6;                     // id 10A0
    uint8_T KCohDiagCnt_7;                     // id 10A1
    uint8_T CntKnockCohEE_0;                   // id 10A2
    uint8_T CntKnockCohEE_1;                   // id 10A3
    uint8_T CntKnockCohEE_2;                   // id 10A4
    uint8_T CntKnockCohEE_3;                   // id 10A5
    uint8_T CntKnockCohEE_4;                   // id 10A6
    uint8_T CntKnockCohEE_5;                   // id 10A7
    uint8_T CntKnockCohEE_6;                   // id 10A8
    uint8_T CntKnockCohEE_7;                   // id 10A9
    uint8_T KnockInt_0;                        // id 10AA
    uint8_T KnockInt_1;                        // id 10AB
    uint8_T KnockInt_2;                        // id 10AC
    uint8_T KnockInt_3;                        // id 10AD
    uint8_T KnockInt_4;                        // id 10AE
    uint8_T KnockInt_5;                        // id 10AF
    uint8_T KnockInt_6;                        // id 10B0
    uint8_T KnockInt_7;                        // id 10B1
    uint8_T VtILeadPeak_0;                     // id 101C
    uint8_T VtILeadPeak_1;                     // id 101D
    uint8_T VtILeadPeak_2;                     // id 101E
    uint8_T VtILeadPeak_3;                     // id 101F
    uint8_T VtILeadPeak_4;                     // id 1020
    uint8_T VtILeadPeak_5;                     // id 1021
    uint8_T VtILeadPeak_6;                     // id 1022
    uint8_T VtILeadPeak_7;                     // id 1023
    uint8_T VtIPriCorr_0;                      // id 11A5
    uint8_T VtIPriCorr_1;                      // id 11A6
    uint8_T VtIPriCorr_2;                      // id 11A7
    uint8_T VtIPriCorr_3;                      // id 11A8
    uint8_T VtIPriCorr_4;                      // id 11A9
    uint8_T VtIPriCorr_5;                      // id 11AA
    uint8_T VtIPriCorr_6;                      // id 11AB
    uint8_T VtIPriCorr_7;                      // id 11AC
    uint8_T VtTSparkFilt_0;                    // id 119D
    uint8_T VtTSparkFilt_1;                    // id 119E
    uint8_T VtTSparkFilt_2;                    // id 119F
    uint8_T VtTSparkFilt_3;                    // id 11A0
    uint8_T VtTSparkFilt_4;                    // id 11A1
    uint8_T VtTSparkFilt_5;                    // id 11A2
    uint8_T VtTSparkFilt_6;                    // id 11A3
    uint8_T VtTSparkFilt_7;                    // id 11A4
    uint16_T VtIonKnockEnableCond_0;           // id 41E1
    uint16_T VtIonKnockEnableCond_1;           // id 41E2
    uint16_T VtIonKnockEnableCond_2;           // id 41E3
    uint16_T VtIonKnockEnableCond_3;           // id 41E4
    uint16_T VtIonKnockEnableCond_4;           // id 41E5
    uint16_T VtIonKnockEnableCond_5;           // id 41E6
    uint16_T VtIonKnockEnableCond_6;           // id 41E7
    uint16_T VtIonKnockEnableCond_7;           // id 41E8
} DTC_Knock_T;

/// DTC Extended SAFETY Data definition for customized DTC types
typedef struct
{
    uint8_T SACmdInLevErrNo;                     // id 11AD
    uint8_T SACmdInLevCirBuff_0;                 // id 11AE
    uint8_T SACmdInLevCirBuff_1;                 // id 11AF
    uint8_T SACmdInLevCirBuff_2;                 // id 11B0
    uint8_T SACmdInLevCirBuff_3;                 // id 11B1
    uint8_T SACmdInLevCirBuff_4;                 // id 11B2
    uint8_T SACmdInLevCirBuff_5;                 // id 11B3
    uint8_T SACmdInLevCirBuff_6;                 // id 11B4
    uint8_T SACmdInLevCirBuff_7;                 // id 11B5
    uint8_T StWdt;                               // id 11B6
    uint8_T StSBCSafeTest;                       // id 41D5
    uint8_T SBCSysStat;                          // id 41D6
    uint8_T StSBC;                               // id 41D7
    uint8_T StSBCMode;                           // id 41D8
    uint8_T EESbcOT;                             // id 41D9
    uint8_T EESbcSC;                             // id 41DA
    uint8_T EESbcUV;                             // id 41DB
    uint8_T EESbcCAN;                            // id 41DC
    uint8_T EECntSbcUV;                          // id 41DD
    uint8_T EECntSbcSC;                          // id 41DE
    uint8_T EECntSbcOT;                          // id 41DF
    uint8_T EECntSbcCAN;                         // id 41E0
    uint16_T VtIonKnockEnableCond_0;             // id 41E1
    uint16_T VtIonKnockEnableCond_1;             // id 41E2
    uint16_T VtIonKnockEnableCond_2;             // id 41E3
    uint16_T VtIonKnockEnableCond_3;             // id 41E4
    uint16_T VtIonKnockEnableCond_4;             // id 41E5
    uint16_T VtIonKnockEnableCond_5;             // id 41E6
    uint16_T VtIonKnockEnableCond_6;             // id 41E7
    uint16_T VtIonKnockEnableCond_7;             // id 41E8
} DTC_Safety_T; //47 bytes

/// DTC Snapshot Data definition for customized DTC types
/*
DTC_Bases number of bytes = 52
DTC_Can_Lvnss_Temp number of bytes (considering Base) = 96
DTC_Ion_Sec_Spark_Cap number of bytes (considering Base) = 168
DTC_Coil_Pri_Buck_Trig number of bytes (considering Base) = 118
DTC_Knock_T number of bytes (considering Base) = 100
DTC_Safety number of bytes (considering Base) = 74
*/
typedef struct
{
    uint16_T  pcode;
    uint8_T   fault;
    uint8_T   diagIdx;
    DTCStatus diagSts;
    uint32_T  timestamp;
    uint8_T severity;
    uint8_T DTCSnapshotData[180]; // we need enough soace in order to store the biggest snapshot, in this case DTC_Ion_Sec_Spark_Cap with 168 bytes
} DTCSnapshotEE_T;

/// DTC Extended Data definition for customized DTC types
typedef struct
{
    uint16_T pcode;
    uint8_T  fault;
    uint8_T  diagIdx;
    /*        Extended mandatory part defined by FCA       */
    uint8_T  EventCounter;                           // id 6080
    uint8_T  WarningLampSwitchOffCycles;             // id 6081
} DTCExtendedEE_T;


/// DTC snapshot type according to env_condition_v7.xlsx
typedef enum
{
    BASE                  = 0x0001u,
    CAN_LVNSS_TEMPECU     = 0x0002u,
    ION_SEC_SPARK_CAP     = 0x0004u,
    COIL_PRI_BUCK_TRIG    = 0x0008u,
    KNOCK                 = 0x0010u,
    SAFETY                = 0x0020u
}SnapshotType_T;

#if 0
/// DTC severity mask according to Table D.12  of ISO 14229-1:2013(E)
typedef enum
{
    NSA     = 0x0u, // no  severity available
    MO      = 0x1u, // Maintenance Only
    CHLANH  = 0x2u, // Check At Next Halt
    CHKI    = 0x4u  // Check Immediatly
}DTCSeverity_T;
#endif

/*!\egroup*/

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/

/* Exporting RLI */
extern typPtFault  PtFault[DIAG_NUMBER];
extern uint8_T TestStatus[DIAG_NUMBER]; // TEST_NOT_COMPLETED, TEST_COMPLETED
extern uint8_T TestResult[DIAG_NUMBER]; // NO_RESULT, FAILED, PASSED
extern uint8_T UpdateFailedOCCnt[DIAG_NUMBER]; // 0-> Keep old value, 1-> Increment Failed OCs counter
extern uint8_T TValidKnockCyl;
extern uint16_T IntIonMeanCyl;
extern uint8_T SnapshotType[DIAG_NUMBER];
extern uint8_T StoredDiag[DIAG_FAULT_LENGTH];
extern uint8_T StoredFault[DIAG_FAULT_LENGTH];
extern uint8_T StoredDiagIdx;
extern uint8_T IdActFault;
extern uint8_T ActiveFault[ACTIVEFAULT_DIM + 1u];
/* exporting EE variables */
extern uint8_T StDiag[DIAG_NUMBER];       /* ex EE */
extern DTCStatus DTCStatusEE[DIAG_NUMBER];
extern uint8_T FailedOCCntEE[DIAG_NUMBER];
extern uint8_T DtcSymptomEE[DIAG_NUMBER];
extern int16_T DiagCntEE[DIAG_NUMBER];
#ifdef SAVE_ENV_DATA_EE
#if (SAVE_ENV_DATA_EE == 1u)
extern DTCSnapshotEE_T DTCSnapshotEE_1[DIAG_FAULT_LENGTH];
extern DTCSnapshotEE_T DTCSnapshotEE_2[DIAG_FAULT_LENGTH];
extern DTCExtendedEE_T DTCExtendedEE[DIAG_FAULT_LENGTH];
#endif // (SAVE_ENV_DATA_EE == 1u)
#endif // SAVE_ENV_DATA_EE
extern confirmedFTB_T  confirmedDTC_oldEE[DIAG_NUMBER];
extern uint8_T EventCounterEE[DIAG_FAULT_LENGTH];
extern uint16_T AbsCntWUC;

extern uint8_T VtSMDiagCode[N_VT_SM_DIAG_CODE];

extern CALQUAL CALQUAL_POST uint8_T  VTDIAGENABLE[DIAG_NUMBER];
#ifdef SAVE_ENV_DATA_EE
#if (SAVE_ENV_DATA_EE == 1u)
extern CALQUAL CALQUAL_POST uint8_T DTCSEVERITY[DIAG_NUMBER];
#endif // (SAVE_ENV_DATA_EE == 1u)
#endif // #ifdef SAVE_ENV_DATA_EE
extern CALQUAL CALQUAL_POST uint8_T THRDIAGWUC;

extern CALQUAL CALQUAL_POST uint8_T  THRCNTKNOCKCOH;

/*!\egroup*/
/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
extern void DiagMgm_Init(void);
extern void DiagMgm_T10ms(void);
extern void DiagMgm_100ms(void);
extern void DiagMgm_ResetOneDiag(uint8_T id);
extern void DiagMgm_ResetOneDiagIgnOffOn(uint8_T id);
extern void DiagMgm_SetDiagState(uint8_T id, typPtFault fault, uint8_T *state);
extern void DiagMgm_RangeCheck_S16(typPtFault *fault, int16_T input, int16_T min_input, int16_T max_input, int16_T med_lo_input, int16_T med_hi_input, typPtFault min_error, typPtFault max_error, typPtFault med_error, uint8_T STR, uint8_T DIAG);
extern void DiagMgm_EOA(void);
extern int16_T EraseFaultsScanTool(void);

#endif
