/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
#ifdef _BUILD_VSRAMMGM_
/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "rtwtypes.h"

/*!
\defgroup PublicVariables Public Variables 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
/// VSRAM data checksum, it is checked at power-on and updated every time a new data is written in.
/// VSRAM Checksum calculation considers both VSRAM sections (shared and not)
uint32_T VSRAM_CheckSumVal;
/*!\egroup*/


#endif  //_BUILD_VSRAMMGM_
