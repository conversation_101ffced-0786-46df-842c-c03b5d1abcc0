/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  FLASH_OUT
**  Filename        :  Flash_out.h
**  Created on      :  08-apr-2021 12:01:00
**  Original author :  CarboniM
******************************************************************************/
/*****************************************************************************
**
**                        SwcName Description
**
**  FLASH Sw module provides driver and interface to manage FLASH 
**  peripheral on chip.
******************************************************************************/
#ifndef _FLASH_OUT_H_
#define _FLASH_OUT_H_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"
#include "ssd_c55.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
/* Flash status definitions */
#define FLASH_STATUS_NOT_CONFIGURED   0u
#define FLASH_STATUS_CONFIGURED       1u
#define FLASH_STATUS_ERROR          255u

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint16_T FLASH_ConfigStatus;

/* This is a public typedef for funtion pointer associated to Flash callback used for erase, program, blankcheck and checksum operation */
typedef void(*pFuncCallback_tag)(void);
//extern pFuncCallback_tag pFuncCallback;

#ifdef _TEST_CACHE_
extern uint8_T Debug_DisCache_Err;
extern uint8_T Debug_EnCache_Err;
#endif


#pragma ghs startnomisra

#pragma ghs endnomisra

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : FLASH_Config
**
**   Description:
**    Function for configuration of FLASH
**
**   Parameters :
**
**   Returns:
**    C55_OK                        - Indicates successful completion of operation
**    C55_ERROR_BLOCK_INDICATOR     - The input blkLockIndicator is invalid
**    C55_ERROR_ALTERNATE           - User calls this function to set lock for 
**                                    Large block space via alternate interface
**    PERIPHERAL_ALREADY_CONFIGURED - Peripheral already configured
**
******************************************************************************/
int16_T FLASH_Config (void) ;

/******************************************************************************
**   Function    : FLASH_Erase
**
**   Description:
**    This function erases the memory blocks which contain the range address from "address" to  "address" + "size"
**
**   Parameters :
**    [in] uint32_T _address : start address of the range to be erased
**    [in] uint32_T _size : size of the range to be erased
**    [in] uint8_T _callback_module : callback  address of void callback function pointer
**
**   Returns:
**    NO_ERROR                     - No error
**    ARG_ERROR                    - Argument error
**    C55_OK                       - Successful completion
**    C55_ERROR_BLOCK_INDICATOR    - The input blkLockIndicator is invalid
**    C55_ERROR_ALTERNATE          - User calls this function to get/set
**                                   lock status for Large block
**                                   space via alternate interface.
**    C55_ERROR_ERASE_OPTION       - Invalid erase option
**    C55_ERROR_BUSY               - New erase operation
**                                   cannot be performed
**                                   because there is
**                                   program/erase sequence in
**                                   progress on the Flash module.
**    C55_ERROR_FACTORY_OP         - The factory erase could not
**                                   be performed
**
******************************************************************************/
int16_T FLASH_Erase ( uint32_T _address, uint32_T _size, uint8_T _callback_type);

/******************************************************************************
**   Function    : FLASH_BlankCheck
**
**   Description:
**    This function checks if the range memory from "_address" to  "_address" + "_size" is blank
**
**   Parameters :
**    [in] uint32_T _address : start address of the range to be checked
**    [in] uint32_T _size : size of the range to be checked
**
**   Returns:
**    NO_ERROR                     - No error
**    ARG_ERROR                    - Argument error
**    C55_OK                       - Successful completion
**    C55_ERROR_ALIGNMENT          - The dest, size provided
**                                   by user is not aligned by word
**    C55_ERROR_NOT_BLANK          - There is a non-blank area within targeted
**                                   Flash range
**
******************************************************************************/                
int16_T FLASH_BlankCheck ( uint32_T _address, uint32_T _size );

/******************************************************************************
**   Function    : FLASH_Program
**
**   Description:
**    This function programs the specified area with the provided source data
**
**   Parameters :
**    [in] uint32_T dest : destination address to be programmed
**    [in] uint32_T size : size of the flash region to be programmed
**    [in] uint32_T source : source program buffer address
**
**   Returns:
**    NO_ERROR                     - No error
**    ARG_ERROR                    - Argument error
**    C55_OK                       - Successful completion
**    C55_ERROR_BLOCK_INDICATOR    - The input blkLockIndicator is invalid
**    C55_ERROR_ALTERNATE          - User calls this function to get/set
**                                   lock status for Large block
**                                   space via alternate interface.
**    C55_ERROR_ALIGNMENT          - This error indicates that
**                                   dest/size/source isn’t properly aligned.
**    C55_ERROR_BUSY               - There is program operation is in progress
**                                   or erase operation is going on and not in 
**                                   suspended state.
**    C55_ERROR_FACTORY_OP         - The factory program could not be performed
**                                   due to the data at the ‘diary’ location in the
**                                   UTest NVM contains at least one zero.
**
******************************************************************************/
int16_T FLASH_Program ( uint32_T dest, uint32_T size, uint32_T source);

/******************************************************************************
**   Function    : FLASH_ProgramVerify
**
**   Description:
**    This function programs checks if a programmed flash range matches the corresponding source data buffer
**
**   Parameters :
**    [in] uint32_T dest : destination address to be verified in flash memory
**    [in] uint32_T size : size of the flash region to be verify
**    [in] uint32_T source : verify source buffer address
**
**   Returns:
**    NO_ERROR                     - No error
**    ARG_ERROR                    - Argument error
**    C55_OK                       - Successful completion
**    C55_ERROR_ALIGNMENT          - This error indicates that dest/size/source isn’t
**                                   properly aligned
**    C55_ERROR_VERIFY             - There is a mismatch between destination and
**                                   source data
**
******************************************************************************/                
int16_T FLASH_ProgramVerify ( uint32_T dest, uint32_T size, uint32_T source);

/******************************************************************************
**   Function    : FLASH_CheckSum
**
**   Description:
**    This function performs a 32-bit sum over the specified flash memory range without carry
**
**   Parameters :
**    [in] uint32_T dest : destination address to be summed in flash memory
**    [in] uint32_T size : size of the flash region to check sum
**    [out] uint32_T sum : return sum value
**
**   Returns:
**    NO_ERROR                     - No error
**    ARG_ERROR                    - Argument error
**    C55_OK                       - Successful completion
**    C55_ERROR_ALIGNMENT          - Alignment error
**    C90FL_ERROR_RANGE            - Address range error
**
******************************************************************************/
int16_T FLASH_CheckSum ( uint32_T dest, uint32_T size, uint32_T * sum);

/******************************************************************************
**   Function    : FLASH_Suspend
**
**   Description:
**    This function suspends the ongoing operation if it can be suspended.
**
**   Parameters :
**    [in] PSSD_CONFIG pSSDConfig : pointer to the SSD Configuration Structure 
**    [out] uint32_T suspendState0 : the suspend state after the function being called
**
**   Returns:
**    C55_OK                       - Successful completion
**
******************************************************************************/
int16_T FLASH_Suspend ( PSSD_CONFIG pSSDConfig, uint8_T *suspendState0 );

/******************************************************************************
**   Function    : FLASH_Resume
**
**   Description:
**    This function checks if there is any suspended erase or program operation and will resume the suspended operation if there is any
**
**   Parameters :
**    [in] PSSD_CONFIG pSSDConfig : pointer to the SSD Configuration Structure 
**    [out] uint8_T resumeState0 : the resume state after the function being called
**
**   Returns:
**    C55_OK                       - Successful completion
**
******************************************************************************/
int16_T FLASH_Resume ( PSSD_CONFIG pSSDConfig, uint8_T *resumeState0 );


#endif /* _FLASH_OUT_H_ */

/****************************************************************************
 ****************************************************************************/
