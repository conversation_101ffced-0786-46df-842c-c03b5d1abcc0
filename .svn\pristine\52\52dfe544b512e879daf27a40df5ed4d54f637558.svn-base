/*****************************************************************************************************************/
/* $HeadURL::                                                                                                              $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TempMgm
**  Filename        :  TempMgm_calib.c
**  Created on      :  24-mar-2021 10:01:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_TEMPMGM_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "TempMgm.h"

#pragma ghs section rodata=".calib"

/*****************************************************************************
** CALIBRATIONS
******************************************************************************/
//Force TAir
CALQUAL CALQUAL_POST int16_T FOTAIR = (-100 * 16);

//Force TWater
CALQUAL CALQUAL_POST int16_T FOTWATER = (-100 * 16);

#endif // _BUILD_TEMPMGM_
/****************************************************************************
 ****************************************************************************/

