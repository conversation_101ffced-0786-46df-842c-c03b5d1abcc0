/******************************************************************************
** COPYRIGHT
** Copyright (c) 2019 by Eldor Corporation S.P.A. , All rights reserved.
**
******************************************************************************/
/******************************************************************************
** SWC : SafetyMngr
** Filename : RamCheckSM_MCU_test.c
** Created on : 02-feb-2022 12:00:00
** Original author : ZandaE
******************************************************************************/
#ifdef _BUILD_RAMCHECK_SM_TEST_
/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "RamCheckSM_MCU_test.h"

/* No other .h files shall be added here */
/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* None */
/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
static uint8_T DEBUG_RecoverySM_SRamSingleSBE = 0U;

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/

/******************************************************************************
** Function:   SafetyMngr_Test_RamSBE
**
** Description:
** generates a single bit ECC error in the chosen memory region
** that will be flagged in the ECC module
**
** Parameters :
** void
**
** Returns:
** void
**
** SW Requirements:
** NA
**
** Implementation Notes:
**
** EA GUID:
******************************************************************************/
void SafetyMngr_Test_RamSBE(uint32_T targetAddr)
{
uint32_T TmpVal = 0U;

    if (DEBUG_RecoverySM_SRamSingleSBE != 0U) 
    {
        if ((targetAddr >= ((uint32_T)&__RAM_START)) && (targetAddr <=((uint32_T)&__RAM_END)))
        {
            DisableAllInterrupts();

            TmpVal = RamReadMem32(targetAddr);
            Test_write_e2eecsr0(0x00001001U);
            *(uint32_T*)targetAddr = 0xA0A0A0A0U;
            *(uint32_T*)targetAddr = TmpVal;
            TmpVal = RamReadMem32(targetAddr);
            
            EnableAllInterrupts();
        }
        DEBUG_RecoverySM_SRamSingleSBE = 0U;
    }

}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : Test_write_e2eecsr0
**
**   Description:
**    This function injects ECC error in RAM by writing E2EECSR0 register
**
**   Parameters :
**    [in] uint32_T value: value to be written in E2EECSR0
**
**   Returns:
**
**   SW Requirements:
**    NA
**
**   Implementation Notes: This function is for test only.
**
**   EA GUID:
******************************************************************************/
static void Test_write_e2eecsr0(uint32_T value) 
{ 
    asm("msync"); 
    asm("se_isync"); 
    asm("mtdcr 511, r3"); 
    asm("msync"); 
} 

#endif /*_BUILD_RAMCHECK_SM_TEST_*/
/****************************************************************************
****************************************************************************/

