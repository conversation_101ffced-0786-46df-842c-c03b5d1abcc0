;******************************
;**** Eldor CCP EISB (1MBaud) V1.0 CCP Flashing     ****
;******************************
ECU_ADDR:,             0x01;           ; for CCP only to select the config
PROJECT_NAME:,         Eldor CCP EISB (1000KBaud) V1.0;     ; project name
;
;Timing,Baudrate and DIAG mode  only Dummy to serve the parser
;                      Index  p1Max   p2Min   p2Max   p3Min   p3Max   p4Min
KWP2000_TP:,           1,     0x0002, 0x0000, 0x0032, 0x0027, 0x1388, 0x0002;
;
;                 ; parameter for key71 stimulation
;                      baud  w1    w2    w3    w4Min w4Max w5
KWP2000_STP:,          0x14, 0x1e, 0x14, 0x14, 0x19, 0x32, 0x1e;
;
;                 ; parameter for KWP2000 stimulation
;              Index  tiniL   wuP     stimLine stimType
KWP2000_SP:,           1,     0x0012, 0x0028, 0x00,    0x81;
;
;              Index   Baudrate       i     k  ; some baud rates for this ECU
HIGH_BDR:,             1,     10400,      0,   20;
;
DIAG_MODE:,        0x85;       ; diagnostic mode for Flash programming
;-----------------------------------------------------------------------
; 
;
;                    index memoryType transferType startAddress   endAddress
;496k internal program Flash
ERASE_MEM_AREA:,       1,    0x00,      0x0,        0x30000L,     0x3FFFFL;
ERASE_MEM_AREA:,       2,    0x00,      0x0,        0x40000L,     0x1BFFFF;
;ERASE_MEM_AREA:,       3,    0x00,      0x0,        0x30000L,     0x9FFFFL;

;496k internal program Flash
DEST_MEM_AREA:,        1,    0x00,      0x0,        0x30000L,   0x3FFFFL;
DEST_MEM_AREA:,        2,    0x00,      0x0,        0x40000L,     0x1BFFFF;
;DEST_MEM_AREA:,        3,    0x00,      0x0,        0x30000L,     0x9FFFFL;

;496k internal program Flash
SOURCE_MEM_AREA:,      1,    0x00,      0x0,        0x30000L,   0x3FFFFL;
SOURCE_MEM_AREA:,      2,    0x00,      0x0,        0x40000L,     0x1BFFFF;
;SOURCE_MEM_AREA:,      3,    0x00,      0x0,        0x30000L,     0x9FFFFL;


;Pattern
SOURCE_MEM_AREA:,      8,    0x00,      0x0,        0x000000L,     0x000010L;
DEST_MEM_AREA:,        8,    0x00,      0x0,        0x902000L,     0x903010L;

;
ECU_TO_INCA_CAN_ID:,   0x00000201;
TGT_INCA:,                  0x00;
INCA_TO_ECU_CAN_ID:,   0x00000200;
TGT_ECU:,                   0x00;

;                     baudRate samplePoint samplesPerBit bitCycles  sjw  syncEdge
KWP_CAN_BUS_TIMING:,  1000000,     70,        1,           14,       2,     0;

;                    index     As       B1       B2      C       D1       D2
KWP_CAN_TL_TIMING:,    1,    0x2000,  0x100,   0x100,   0x100,  0x100,   0x100;
KWP_CAN_TL_TIMING:,    2,    0x2000,  0x100,   0x100,   0x100,  0x100,   0x100;
KWP_CAN_TL_TIMING:,    3,    0x2000,  0x100,   0x100,   0x100,  0x100,   0x100;
;
KWP2000_END;            ;end of data set

