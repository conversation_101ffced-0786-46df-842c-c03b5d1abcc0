/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tbu.h
 * @brief   SPC5xx GTM TBU header file.
 *
 * @addtogroup TBU
 * @{
 */

#ifndef _GTM_TBU_H_
#define _GTM_TBU_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"

/**
 * @name    TBU definitions
 * @{
 */

/** TBU channel 0 identifier */
#define TBU_CHANNEL0                        0U
/** TBU channel 1 identifier */
#define TBU_CHANNEL1                        1U
/** TBU channel 2 identifier */
#define TBU_CHANNEL2                        2U

#if !defined (__DOXYGEN__)
#define SPC5_GTM_TBU0_CH0_LOW_RES           0U
#define SPC5_GTM_TBU0_CH0_HIGH_RES          1U
#endif /* (__DOXYGEN__) */

/** TBU count mode free running   */
#define SPC5_GTM_TBU_MODE_FREE_RUNNING      0U
/** TBU count mode forward/backward */
#define SPC5_GTM_TBU_MODE_FORWARD_BACKWARD  1U

/** @} */

/*===========================================================================*/
/* Driver data structures and types.                                         */
/*===========================================================================*/

/**
 * @brief Type of a structure representing a (GTM-IP) TBU driver.
 */
typedef struct GTM_TBUDriver GTM_TBUDriver;

/**
 * @brief   Structure representing a (GTM) TBU driver.
 */
struct GTM_TBUDriver {

  /**
   * @brief Pointer to the (GTM) TBU registers block.
   */
	volatile GTM_TBU_TAG *tbu;

  /**
   * @brief Pointer for application private data.
   */
    void *priv;
};

/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_TBU0 == TRUE) && !defined(__DOXYGEN__)
extern GTM_TBUDriver TBUD1;
#endif

#ifdef __cplusplus
extern "C" {
#endif
extern void gtm_tbuInit(void);
extern void gtm_tbuStart(GTM_TBUDriver *tbud);
extern void gtm_tbuStop(GTM_TBUDriver *tbud);
extern void gtm_tbuStartChannel(GTM_TBUDriver *tbud, uint8_t channel);
extern void gtm_tbuStopChannel(GTM_TBUDriver *tbud, uint8_t channel);
extern void gtm_tbuSetClockChannel(GTM_TBUDriver *tbud, uint8_t channel, uint8_t clock_source);
extern uint32_t gtm_tbuGetClockChannel(GTM_TBUDriver *tbud, uint8_t channel);
extern void gtm_tbuSetTimeBaseChannel(GTM_TBUDriver *tbud, uint8_t channel, uint32_t time_base);
extern uint32_t gtm_tbuGetTimeBaseChannel(GTM_TBUDriver *tbud, uint8_t channel);
extern void gtm_tbuSetLowResChannel_0(GTM_TBUDriver *tbud, uint32_t low_res);
extern uint32_t gtm_tbuGetLowResChannel_0(GTM_TBUDriver *tbud);
extern void gtm_tbuSetRunningModeChannel_12(GTM_TBUDriver *tbud, uint8_t channel, uint8_t running_mode);
extern uint8_t gtm_tbuGetRunningModeChannel_12(GTM_TBUDriver *tbud, uint8_t channel);
#ifdef __cplusplus
}
#endif

#endif /* _GTM_TBU_H_ */
/** @} */

