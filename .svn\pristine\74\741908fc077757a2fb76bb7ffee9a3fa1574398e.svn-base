/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

/*! \mainpage ModuleName

\section intro Introduction
\brief A brief description of what this module does 

Explain in detail how this module works and what is supposed to do.  

 */

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "sys.h"
#include "OS_api.h"
#include "OS_exec_ctrl.h"
#include "OS_resources.h"
#include "OS_alarms.h"
#include "OS_Hooks.h"
#include "OS_errors.h"
#include "Utils_out.h"
#include "vsrammgm.h"
#include "TasksDefs.h"
#include "eemgm_out.h"
#include "intsrcmgm.h"
#ifdef _BUILD_WDT_SBC_
#include "WDT_out.h"
#include "WDT_wrapper_out.h"
#endif  

#ifdef _OSEK_


/*!
\defgroup PublicVariables Public Variables 
\sgroup
 */
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
///These variables can be used also by other SW modules 
TaskCBS  OsTaskTable[OSNUMTSKS];                /* Tasks table     */
ResCBS   OsResTable[OSNUMRES];                  /* Resources table */
AlmCBS   OsAlmTable[OSNUMALMS];                 /* Alarms table    */
extern CTRCBS   OsCounters[OSNUMCTRS];          /* Counters table  */       
uint32_T terminationAddress;
StatusType runningTaskId;
OSSERVICEIDTYPE        OsService;        /* for OSErrorGetServiceId() from ErrorHook */
uint32_T                 OsObjId;        /* for first parameter                      */

/*!\egroup*/

#define STCKPTRDFLTVAL  0

/* External function declarations */
void OSInitHandlerManager(void);
void TaskPowerOff(void);

/*!
\defgroup PrivateVariables Private Variables 
\sgroup
 */
/*-----------------------------------*
 * PRIVATE VARIABLE DEFINITIONS
 *-----------------------------------*/
///These variables can be used only by this module 
static AppModeType  OS_ActiveMode;
vuint8_T   OsrtiRunningServiceId;
vuint8_T       OsrtiOldServiceId;
uint8_T           OsSuspendLevel;


/*!\egroup*/


void GetApplicationTaskTable (void **taskTable, uint32_T *tableSize); 

void app_data_section_init(void);

/*!
\defgroup PublicFunctions Public Functions 
\sgroup
 */
/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   StartOS
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
void StartOS( AppModeType mode )
{
    uint8_T   taskCounter,resCounter,almCounter;


    OS_ActiveMode = mode;
    OSCLEARPARAM();
    OsSuspendLevel  = 0;

    /* boot_data_section_init(); */
    //app_data_section_init();

    /* -------- Resources section ---------- */
    for (resCounter = 0; resCounter < OSNUMRES; resCounter++)
    {
        OSRESID(resCounter)       = OsResCfgTable[resCounter].ResID;
        OSRESTASKID(resCounter)   = NULLTASK;
        OSRESPRI(resCounter)      = OsResCfgTable[resCounter].ResPri;
        OSRESSTATE(resCounter)    = RES_FREE;
        OSRESCURRPRI(resCounter)  = DEFAULT_CURR_PRI;
    }

    if (mode == OSAPPMODE)
    {
        /* -------- Alarms section ---------- */
        for (almCounter = 0; almCounter < OSNUMALMS; almCounter++)
        {
            OSALMTASKID(almCounter)  =  OsAlmCfgTable[almCounter].TaskId;
            OSALMCNTRID(almCounter)  =  OsAlmCfgTable[almCounter].cntrId;
            OSALMSTATE(almCounter)   =  ALM_FREE;
            OSALMACTION(almCounter)  =  OsAlmCfgTable[almCounter].action;
        }

        for (almCounter = 0; almCounter < OSNUMALMS; almCounter++)
        {
            //SetRelAlarm(OsAlmTable+almCounter,  TIMING_RESOLUTION, OSALMCNTRID(almCounter));
            /*new configuration with OsAlmCfgCycleTable*/
            SetRelAlarm(&OsAlmTable[almCounter],  TIMING_RESOLUTION, OsAlmCfgCycleTable[almCounter]);
        }

        /* -------- Tasks section ---------- */
        for (taskCounter = 0; taskCounter < MAX_NUM_TASK; taskCounter++)
        {
            OSTASKENTRY(taskCounter)    = OsTaskCfgTable[taskCounter].entry; /* entry point of task */
            OSTASKID(taskCounter)       = OsTaskCfgTable[taskCounter].tskId;
            OSTASKPRI(taskCounter)      = OsTaskCfgTable[taskCounter].pri;
            OSTASKSTATUS(taskCounter)   = SUSPENDED;
            OSTASKSTACKPTR(taskCounter) = STCKPTRDFLTVAL;
        }
    }


    /* -------- Tasks section ---------- */
    OSInitHandlerManager();

    runningTaskId = NULLTASK;
    terminationAddress = 0;
    
    StartupHook();
}


/***************************************************************************/
//   Function    :   ShutdownOS
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
void ShutdownOS(StatusType error)
{
    DisableAllInterrupts();   /* Disable external interrupts   */
    ShutdownHook(error);
    EnableAllInterrupts();    /* Re-enable external interrupts */

#ifdef _BUILD_WDT_SBC_
#ifdef _BUILD_WDT_EVB_
    SYS_SwRST();
#else
    WDT_ExtSyncISRReset();
#endif
#else
    SYS_SwRST();
#endif

}

/***************************************************************************/
//   Function    :   ShutdownOSerrorHandler
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/
void ShutdownOSerrorHandler( StatusType error)
{
    switch (error)
    {
        case(E_OS_SYS_KEY_OFF):
        {
            TaskPowerOff(); 
        }
        break;
        
        case E_OS_TIMEDOUT_KEY_OFF:
        {
            TaskPowerOff(); 
        }
        break;
        
        case(E_OS_SYS_IVOR_ERROR):
        {
            #ifdef _BUILD_INTSRCMGM_
            //SYS_IvorExHandling();
            #endif /*_BUILD_INTSRCMGM_*/
            
            #ifdef  _BUILD_VSRAMMGM_
            VSRAMMGM_Update();
            #endif /* _BUILD_VSRAMMGM_ */
            
            #ifdef _BUILD_EEMGM_
            EEMGM_EETaskCmd();
            #endif /*_BUILD_EEMGM_*/
            
            #ifdef _BUILD_WDT_SBC_ 
#ifdef _BUILD_WDT_EVB_
            SYS_SwRST();
#else
            WDT_ExtSyncISRReset();
#endif
            #else
            SYS_SwRST();
            #endif
        }
        break;
        
        #ifdef _BUILD_SAF2MGM_
        case(E_OS_SYS_SAF2_SHTDWN):
        {
            /* */    
        }
        #endif /* _BUILD_SAF2MGM_ */

        case(E_OS_ATI_SW_DWLOAD):
        case(E_OS_KWP2000_SW_DWLOAD):
        {
            #ifdef _BUILD_EEMGM_
            EEMGM_EETaskCmd();
            #endif /*_BUILD_EEMGM_*/

            #ifdef _BUILD_WDT_SBC_ 
#ifdef _BUILD_WDT_EVB_
            SYS_SwRST();
#else
            WDT_ExtSyncISRReset();
#endif
            #else
            SYS_SwRST();
            #endif
        }
        break;
        
        default:
        {
            #ifdef _BUILD_MC33904_U3_
            #ifndef FORCE_EVB_RST    
            Spi_MC33904_U3_Reset();
            #else
            SYS_SwRST();
            #endif
            #else
            SYS_SwRST();
            #warning WDT power Off not enable        
            #endif  
        }
        break;
    }
}

/***************************************************************************/
//   Function    :   GetActiveApplicationMode
//
//   Description:    
/*! \brief Report a short description of this function
 */
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
 */
//  Notes:        
/*!
Write here a more detailed description of this public function 
 */
/**************************************************************************/

AppModeType GetActiveApplicationMode(void)
{
    return (OS_ActiveMode);
}
#endif /* _OSEK_ */

/****************************************************************************
 ****************************************************************************/


