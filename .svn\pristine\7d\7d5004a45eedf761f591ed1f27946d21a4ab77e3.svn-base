/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  SafetyMngr_Cache.c
**  Created on      :  27-lug-2021 16:39:00
**  Original author :  Mocci A
******************************************************************************/


#ifdef _BUILD_SAFETYMNGR_CACHE_

#ifndef _BUILD_SAFETYMNGR_
#error CACHE Safety Module enabled without _BUILD_SAFETYMNGR_ macro enabled
#endif /*  _BUILD_DIAGCANMGM_ */

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "SafetyMngr_Cache.h"

/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_Cache_Check_Error
**
**   Description:
**    Check the DataCache and InstructionCache Error Checking BIT and covers
**    SM_MCU_3_63 requirement.
**
**   Parameters :
**    [inout] uint32_T* p_CacheRegVal : Setting of the Data Cache Error
**    (Field DCECE of DataCacheReg  or  InstructionCacheReg)
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
void SafetyMngr_Cache_Check_Error(uint32_T * p_CacheRegVal)
{
    /* code construction start */
#if 0
/**
* @brief            Check the Error Checking BIT
* @details            SM_MCU_3_63 Safety Reqs
* requirement       ECC/EDC protection of caches is assumed to be enabled
*                    (setting of the Data Cache Error Checking Enable field in the L1 Cache Control and Status Register 0, L1CSR0[DCECE] = 1).
* @param[in]        NoneA
* @note             Data chache not used, similar check can be done on instruction cahce but is not mandatory. Requirement can be skipped.
* @return           None
*
*/
    volatile uint32_T l_RegCheck = 0x00u;

    if (NULL != p_CacheRegVal)
    {

#if ( STD_ON == DCACHE_CHECK )
        l_RegCheck = SafetyMngr_Cache_GetDataCacheReg();

        /* Check if Data Cache Error Checking is Enable*/
        if (0u == GET_BIT(DCECE, l_RegCheck))
        {
            /* Raise up an error */
            SafetyMngr_ReportError(SAFE_ERR_CACHE_DATACACHEERRNENABLED, FALSE);

            *p_CacheRegVal = 0u;
        }
        else
        {
            *p_CacheRegVal = 1u;
        }
#endif

#if ( STD_ON == ICACHE_CHECK )

        l_RegCheck = SafetyMngr_Cache_GetInstructionCacheReg();
        if (0u < l_RegCheck)
        {
            /* Check if Data Cache Error Checking is Enable*/
            if (0u == GET_BIT(DCECE, l_RegCheck))
            {
                /* Raise up an error */
                SafetyMngr_ReportError(SAFE_ERR_CACHE_INSTCACHEERRNENABLED, FALSE);
                *p_CacheRegVal = 0u;
            }
            else
            {
                *p_CacheRegVal = 1u;
            }
        }

#endif

    }

#endif
    /* code construction end */
}


/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : SafetyMngr_Cache_GetDataCacheReg
**
**   Description:
**    Unit read the L1CSR0 Register content
**
**   Parameters :
**    void
**
**   Returns:
**    volatile uint32_T
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static volatile uint32_T SafetyMngr_Cache_GetDataCacheReg(void)
{
volatile uint32_T l_retVal;

    /* code construction start */
#if 0
    /**
    * @brief            Get Data cache function.
    * @details
    *
    * @param[in]        None
    * @return           L1CSR0 Register content
    *
    */
    ASM_KEYWORD("msync");
    ASM_KEYWORD("mfspr r3,l1csr0");        /* read current config */
    ASM_KEYWORD("se_blr");
    l_retVal = (0xFF);
    /* code construction end */
#endif
    return  l_retVal;
}

/******************************************************************************
**   Function    : SafetyMngr_Cache_GetInstructionCacheReg
**
**   Description:
**    Unit read and return the L1CSR1 Register content
**
**   Parameters :
**    void
**
**   Returns:
**    volatile uint32_T
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static volatile uint32_T SafetyMngr_Cache_GetInstructionCacheReg(void)
{
volatile uint32_T l_retVal;

    /* code construction start */
#if 0
    /**
    * @brief            Get Data cache function.
    * @details
    *
    * @param[in]        None
    * @return           L1CSR0 Register content
    *
    */
    ASM_KEYWORD("msync");
    ASM_KEYWORD("mfspr r3,l1csr1");        /* read current config */
    ASM_KEYWORD("se_blr");
    l_retVal = (0xFF);
    /* code construction end */
#endif
    return  l_retVal;
}

#endif // _BUILD_SAFETYMNGR_CACHE_
/****************************************************************************
 ****************************************************************************/
