/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  DSPI
**  Filename        :  Dspi.h
**  Created on      :  16-giu-2020 14:04:00
**  Original author :  CarboniM
******************************************************************************/

#ifndef _DSPI_H_
#define _DSPI_H_


/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "dspi_out.h" 
#include "OS_api.h"
#include "tasksdefs.h"
#include "task.h"
#include "events.h"
#include "Timing.h"
#ifdef _SPI_USE_DMA_
#include "dma_out.h"
#endif

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
///Baud Rate Scaler : scaler X2
#define BR_SCALER_2         0x0u
///Baud Rate Scaler : scaler X4
#define BR_SCALER_4         0x1u
///Baud Rate Scaler : scaler X6
#define BR_SCALER_6         0x2u
///Baud Rate Scaler : scaler X8
#define BR_SCALER_8         0x3u
///Baud Rate Scaler : scaler X16
#define BR_SCALER_16        0x4u
///Baud Rate Scaler : scaler X32
#define BR_SCALER_32        0x5u
///Baud Rate Scaler : scaler X64
#define BR_SCALER_64        0x6u
///Baud Rate Scaler : scaler X128
#define BR_SCALER_128       0x7u
///Baud Rate Scaler : scaler X256
#define BR_SCALER_256       0x8u
///Baud Rate Scaler : scaler X512
#define BR_SCALER_512       0x9u
///Baud Rate Scaler : scaler X1024
#define BR_SCALER_1024      0xAu
///Baud Rate Scaler : scaler X2048
#define BR_SCALER_2048      0xBu
///Baud Rate Scaler : scaler X4096
#define BR_SCALER_4096      0xCu
///Baud Rate Scaler : scaler X8192
#define BR_SCALER_8192      0xDu
///Baud Rate Scaler : scaler X16384
#define BR_SCALER_16384     0xEu
///Baud Rate Scaler : scaler X32768
#define BR_SCALER_32768     0xFu


///Baud Rate Prescaler : scaler X2
#define BRP_SCALER_2        0x0u
///Baud Rate Prescaler : scaler X3
#define BRP_SCALER_3        0x1u
///Baud Rate Prescaler : scaler X5
#define BRP_SCALER_5        0x2u
///Baud Rate Prescaler : scaler X7
#define BRP_SCALER_7        0x3u

///Double Baud Rate disabled
#define DBR_DISABLED        0x0u
///Double Baud Rate enabled
#define DBR_ENABLED         0x1u

///size of frame in bit : 4bits
#define FRAMESIZE_4         0x3u
///size of frame in bit : 5bits
#define FRAMESIZE_5         0x4u
///size of frame in bit : 6bits
#define FRAMESIZE_6         0x5u
///size of frame in bit : 7bits
#define FRAMESIZE_7         0x6u
///size of frame in bit : 8bits
#define FRAMESIZE_8         0x7u
///size of frame in bit : 9bits
#define FRAMESIZE_9         0x8u
///size of frame in bit : 10bits
#define FRAMESIZE_10        0x9u
///size of frame in bit : 11bits
#define FRAMESIZE_11        0xau
///size of frame in bit : 12bits
#define FRAMESIZE_12        0xbu
///size of frame in bit : 13bits
#define FRAMESIZE_13        0xcu
///size of frame in bit : 14bits
#define FRAMESIZE_14        0xdu
///size of frame in bit : 15bits
#define FRAMESIZE_15        0xeu
///size of frame in bit : 16bits
#define FRAMESIZE_16        0xfu


///Prescalers values for CSSCK, ASC and DT : prescaler x1
#define CTAR_PRESCALER_1        0x0u
///Prescalers values for CSSCK, ASC and DT : prescaler x3 
#define CTAR_PRESCALER_3        0x1u
///Prescalers values for CSSCK, ASC and DT : prescaler x5
#define CTAR_PRESCALER_5        0x2u
///Prescalers values for CSSCK, ASC and DT : prescaler x7 
#define CTAR_PRESCALER_7        0x3u


///Scaler values for CSSCK, ASC and DT : scaler X2
#define CTAR_SCALER_2           0x0u
///Scaler values for CSSCK, ASC and DT : scaler X4
#define CTAR_SCALER_4           0x1u
///Scaler values for CSSCK, ASC and DT : scaler X8
#define CTAR_SCALER_8           0x2u
///Scaler values for CSSCK, ASC and DT : scaler X16
#define CTAR_SCALER_16          0x3u
///Scaler values for CSSCK, ASC and DT : scaler X32
#define CTAR_SCALER_32          0x4u
///Scaler values for CSSCK, ASC and DT : scaler X64
#define CTAR_SCALER_64          0x5u
///Scaler values for CSSCK, ASC and DT : scaler X128
#define CTAR_SCALER_128         0x6u
///Scaler values for CSSCK, ASC and DT : scaler X256
#define CTAR_SCALER_256         0x7u
///Scaler values for CSSCK, ASC and DT : scaler X512
#define CTAR_SCALER_512         0x8u
///Scaler values for CSSCK, ASC and DT : scaler X1024
#define CTAR_SCALER_1024        0x9u
///Scaler values for CSSCK, ASC and DT : scaler X2048
#define CTAR_SCALER_2048        0xau
///Scaler values for CSSCK, ASC and DT : scaler X4096
#define CTAR_SCALER_4096        0xbu
///Scaler values for CSSCK, ASC and DT : scaler X8192
#define CTAR_SCALER_8192        0xcu
///Scaler values for CSSCK, ASC and DT : scaler X16384
#define CTAR_SCALER_16384       0xdu
///Scaler values for CSSCK, ASC and DT : scaler X32768
#define CTAR_SCALER_32768       0xeu
///Scaler values for CSSCK, ASC and DT : scaler X65536
#define CTAR_SCALER_65536       0xfu

/// SPI master mode, configuration parameter
#define SPI_MASTER        1u
/// SPI slave mode, configuration parameter
#define SPI_SLAVE         0u

///Data is transferred LSB first, configuration parameter
#define SPI_LSB_FIRST   1u
///Data is transferred MSB first, configuration parameter
#define SPI_MSB_FIRST   0u          

///Clock Polarity. The active state value of the SCK is high
#define SPI_CPOL_HIGH   0u           
///Clock Polarity. The active state value of the SCK is low 
#define SPI_CPOL_LOW    1u           

///Clock Phase. Data is changed on the leading edge and captured on the following edge
#define SPI_CPHA_HIGH   1u  
/// Clock Phase. Data is captured on the leading edge and changed on the following    
#define SPI_CPHA_LOW    0u          

///Chip Select Active Low  
#define PCS_ACTIVE_LOW   1U   
///Chip Select Active High
#define PCS_ACTIVE_HIGH  0U

///Chip Select Active State Defines , channel A
#if SPI_CH_A_EN
#define SPI_CH_A_PCSn  (PCSA0_ACT_STATE|(PCSA1_ACT_STATE<<1)|(PCSA2_ACT_STATE<<2)|(PCSA3_ACT_STATE<<3)|(PCSA4_ACT_STATE<<4)|(PCSA5_ACT_STATE<<5)|(PCSA6_ACT_STATE<<6)|(PCSA7_ACT_STATE<<7))
#endif

///Chip Select Active State Defines , channel B
#if SPI_CH_B_EN
#define SPI_CH_B_PCSn  (PCSB0_ACT_STATE|(PCSB1_ACT_STATE<<1)|(PCSB2_ACT_STATE<<2)|(PCSB3_ACT_STATE<<3)|(PCSB4_ACT_STATE<<4)|(PCSB5_ACT_STATE<<5)|(PCSB6_ACT_STATE<<6)|(PCSB7_ACT_STATE<<7))
#endif

///Chip Select Active State Defines , channel C
#if SPI_CH_C_EN
#define SPI_CH_C_PCSn  (PCSC0_ACT_STATE|(PCSC1_ACT_STATE<<1)|(PCSC2_ACT_STATE<<2)|(PCSC3_ACT_STATE<<3)|(PCSC4_ACT_STATE<<4)|(PCSC5_ACT_STATE<<5)|(PCSC6_ACT_STATE<<6)|(PCSC7_ACT_STATE<<7))
#endif

///Chip Select Active State Defines , channel D
#if SPI_CH_D_EN
#define SPI_CH_D_PCSn  (PCSD0_ACT_STATE|(PCSD1_ACT_STATE<<1)|(PCSD2_ACT_STATE<<2)|(PCSD3_ACT_STATE<<3)|(PCSD4_ACT_STATE<<4)|(PCSD5_ACT_STATE<<5)|(PCSD6_ACT_STATE<<6)|(PCSD7_ACT_STATE<<7))
#endif

///Chip Select Active State Defines , channel E
#if SPI_CH_E_EN
#define SPI_CH_E_PCSn  (PCSE0_ACT_STATE|(PCSE1_ACT_STATE<<1)|(PCSE2_ACT_STATE<<2)|(PCSE3_ACT_STATE<<3)|(PCSE4_ACT_STATE<<4)|(PCSE5_ACT_STATE<<5)|(PCSE6_ACT_STATE<<6)|(PCSE7_ACT_STATE<<7))
#endif


///Chip Select PCS Continuous Mode, channel A
#define SPI_CH_A_PCS_CONT  (PCSA0_CONT_EN|(PCSA1_CONT_EN<<1)|(PCSA2_CONT_EN<<2)|(PCSA3_CONT_EN<<3)|(PCSA4_CONT_EN<<4)|(PCSA5_CONT_EN<<5)|(PCSA6_CONT_EN<<6)|(PCSA7_CONT_EN<<7))

///Chip Select PCS Continuous Mode, channel B
#define SPI_CH_B_PCS_CONT  (PCSB0_CONT_EN|(PCSB1_CONT_EN<<1)|(PCSB2_CONT_EN<<2)|(PCSB3_CONT_EN<<3)|(PCSB4_CONT_EN<<4)|(PCSB5_CONT_EN<<5)|(PCSB6_CONT_EN<<6)|(PCSB7_CONT_EN<<7))

///Chip Select PCS Continuous Mode, channel C
#define SPI_CH_C_PCS_CONT  (PCSC0_CONT_EN|(PCSC1_CONT_EN<<1)|(PCSC2_CONT_EN<<2)|(PCSC3_CONT_EN<<3)|(PCSC4_CONT_EN<<4)|(PCSC5_CONT_EN<<5)|(PCSC6_CONT_EN<<6)|(PCSC7_CONT_EN<<7))

///Chip Select PCS Continuous Mode, channel D
#define SPI_CH_D_PCS_CONT  (PCSD0_CONT_EN|(PCSD1_CONT_EN<<1)|(PCSD2_CONT_EN<<2)|(PCSD3_CONT_EN<<3)|(PCSD4_CONT_EN<<4)|(PCSD5_CONT_EN<<5)|(PCSD6_CONT_EN<<6)|(PCSD7_CONT_EN<<7))

///Chip Select PCS Continuous Mode, channel E
#define SPI_CH_E_PCS_CONT  (PCSE0_CONT_EN|(PCSE1_CONT_EN<<1)|(PCSE2_CONT_EN<<2)|(PCSE3_CONT_EN<<3)|(PCSE4_CONT_EN<<4)|(PCSE5_CONT_EN<<5)|(PCSE6_CONT_EN<<6)|(PCSE7_CONT_EN<<7))


///PCSIS mask (8 chip select lines)
#define PCSIS_MASK_N 0xFF00FFFFu
///mask for EOQ bit in PUSHR register
#define SPI_PUSHR_EOQ_MASK   0x08000000u
///mask for CTCNT bit in PUSHR register
#define SPI_PUSHR_CTCNT_MASK 0x04000000u

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/* Type for peripheral memory mapped register */
typedef volatile struct DSPI_tag * DspiPtr_T;

/* Type for pointer to isr function */
typedef void (*IsrFunction_T)(void);

/* Type for priority of isr function */
typedef uint8_T IsrFunctionPri_T;

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */


/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static SpiError_T SPI_InitCh(uint8_T channel, uint8_T chipSelect, uint8_T brScaler, uint8_T pbrScaler, uint8_T dbrValue);
static void SPI_ConfigPrescalers(uint8_T channel, uint8_T chipSelect, uint8_T cssckPrescaler, uint8_T dtPrescaler, uint8_T ascPrescaler);
static void SPI_ConfigCh(uint8_T channel, uint8_T spiMode, uint8_T continuousSck, uint8_T nPCS);
static void SPI_ConfigClockTransferParam(uint8_T channel, uint8_T chipSelect, uint8_T frameBitSize, uint8_T clockPolarity, uint8_T clockPhase, uint8_T cssckDelay, uint8_T dtDelay, uint8_T ascDelay, uint8_T pLsbfe);
static int16_T SPI_VectorInitA(void);
static int16_T SPI_VectorInitB(void);
static int16_T SPI_VectorInitC(void);
static int16_T SPI_VectorInitD(void);
static int16_T SPI_VectorInitE(void);

static SpiError_T SPI_SetTimeout(uint8_T channel, uint8_T chipSelect, uint8_T size);
static SpiError_T SPI_ReadData(uint8_T channel, uint16_T rxBuffer[], uint8_T rxSize);

#endif /*_DSPI_H_ */

/****************************************************************************
 ****************************************************************************/
