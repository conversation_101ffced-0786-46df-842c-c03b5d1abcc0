// T3210001 Fri Aug 21 14:23:00 2020

B::
 VAR.ADDWATCH %H %E %SL  EE_IDtable_bl0 
 VAR.ADDWATCH %H %E %SL  EE_IDtable_bl1
 VAR.ADDWATCH %H %E %SL  EE_blk0_free_cell 
 VAR.ADDWATCH %H %E %SL  EE_blk1_free_cell 
 VAR.ADDWATCH %H %E %SL  EE_TaskToExec 
 VAR.ADDWATCH %H %E %SL  Prova_ID0
 VAR.ADDWATCH %H %E %SL  testEEmgm 
TOOLBAR   ON
STATUSBAR ON
FramePOS 0.0 0.0 96. 46.
WinPAGE.RESet

WinPAGE.Create P000
WinCLEAR

WinPOS 18.286 0.0 72. 20. 0. 0. W000
Register

WinPOS 0.57143 28.667 90. 11. 14. 1. W001
WinTABS 10. 10. 25.
List

WinPOS 1.0 3.1667 85. 18. 0. 0. W002
Var.Watch

VAR.ADDWATCH %e %SL %hex Debug_event_tooth_callback
VAR.ADDWATCH %e %SL %hex Debug_DMA_Ch0_ISR_Ipri0
VAR.ADDWATCH %e %SL %hex Debug_DMA_Ch1_ISR_Isec
VAR.ADDWATCH %e %SL %hex Debug_DMA_Ch2_ISR_Ion_25 
VAR.ADDWATCH %e %SL %hex Debug_DMA_Ch3_ISR_Ion_03 
VAR.ADDWATCH %e %SL %hex Debug_DMA_Ch16_ISR_IpriB1
VAR.ADDWATCH %e %SL %hex Debug_DMA_Ch24_ISR_Ion_14
VAR.ADDWATCH %e %SL %hex Debug_MCS2_Isr
VAR.ADDWATCH %e %SL %hex Debug_MCS1_Isr
VAR.ADDWATCH %e %SL %hex Debug_MCS0_Isr
VAR.ADDWATCH %e %SL %hex Debug_Tim1_Isr
VAR.ADDWATCH %e %SL %hex CntMainLoop
VAR.ADDWATCH %e %SL %hex CntTaskT5ms 
VAR.ADDWATCH %e %SL %hex CntTaskT10ms 
VAR.ADDWATCH %e %SL %hex CntTaskT100ms 
VAR.ADDWATCH %e %SL %hex isec_bo_thr
VAR.ADDWATCH %e %SL %hex ion3_7_buffer
VAR.ADDWATCH %e %SL %hex ion2_6_buffer
VAR.ADDWATCH %e %SL %hex ion1_5_buffer
VAR.ADDWATCH %e %SL %hex ion0_4_buffer
VAR.ADDWATCH %e %SL %hex VtILeadPeak
VAR.ADDWATCH %e %SL %hex VtISecThr
VAR.ADDWATCH %e %SL %hex VtISecMin
VAR.ADDWATCH %e %SL %hex FOSBCRESET
VAR.ADDWATCH %e %SL EE_IvorCnt_c0
VAR.ADDWATCH %e %SL EE_IvorIndex_c0    
VAR.ADDWATCH %e %SL EE_SRR0_Value_c0   
VAR.ADDWATCH %e %SL EE_SRR1_Value_c0   
VAR.ADDWATCH %e %SL EE_CSRR0_Value_c0  
VAR.ADDWATCH %e %SL EE_CSRR1_Value_c0  
VAR.ADDWATCH %e %SL EE_SPR_ESRValue_c0 
VAR.ADDWATCH %e %SL EE_SPR_DEARValue_c0
VAR.ADDWATCH %e %SL EE_SPR_MCSRValue_c0
VAR.ADDWATCH %e %SL EE_SPR_MCARValue_c0
VAR.ADDWATCH %e %SL EE_MCSRR0_Value_c0 
VAR.ADDWATCH %e %SL EE_MCSRR1_Value_c0 
VAR.ADDWATCH %e %SL EE_IvorCnt_c2
VAR.ADDWATCH %e %SL EE_IvorIndex_c2    
VAR.ADDWATCH %e %SL EE_SRR0_Value_c2   
VAR.ADDWATCH %e %SL EE_SRR1_Value_c2   
VAR.ADDWATCH %e %SL EE_CSRR0_Value_c2  
VAR.ADDWATCH %e %SL EE_CSRR1_Value_c2  
VAR.ADDWATCH %e %SL EE_SPR_ESRValue_c2 
VAR.ADDWATCH %e %SL EE_SPR_DEARValue_c2
VAR.ADDWATCH %e %SL EE_SPR_MCSRValue_c2
VAR.ADDWATCH %e %SL EE_SPR_MCARValue_c2
VAR.ADDWATCH %e %SL EE_MCSRR0_Value_c2 
VAR.ADDWATCH %e %SL EE_MCSRR1_Value_c2
VAR.ADDWATCH %e %SL %hex VTFORCEPTFAULT
VAR.ADDWATCH %e %SL %hex VTDIAGENABLE
VAR.ADDWATCH %e %SL %hex TBDISDIAG
VAR.ADDWATCH %e %SL %hex FORCEPTFAULT10MS
VAR.ADDWATCH %e %SL %hex DTCSnapshotEE_1
VAR.ADDWATCH %e %SL %hex DTCSnapshotEE_2
VAR.ADDWATCH %e %SL %hex DTCExtendedEE
WinPAGE.select P000

ENDDO
