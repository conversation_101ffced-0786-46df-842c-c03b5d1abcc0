/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

/*! \mainpage ModuleName
 
\section intro Introduction
\brief A brief description of what this module does 

Explain in detail how this module works and what is supposed to do.  
 
*/

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "OS_hooks.h"

/*################ nedeed for StartupHook ################*/
#include "sys.h"
#include "mathlib.h"
#include "intsrcmgm.h"

#include "eemgm_out.h"

#ifdef _OSEK_

/*!
\defgroup PublicVariables Public Variables 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC VARIABLE DEFINITIONS
 *-----------------------------------*/
///These variables can be used also by other SW modules 

uint32_T currentTaskStkPtr;

#ifdef _TEST_CACHE_
uint8_T Debug_CacheCfg_Ok = 0u;
uint8_T Debug_CacheCfg_Err = 0u;
#endif

/*!\egroup*/

void cfg_MMU (void);
void cfg_CACHE (void);

/*!
\defgroup PublicFunctions Public Functions 
\sgroup
*/
/*==================================================================================================
                                       PUBLIC FUNCTIONS
==================================================================================================*/
/***************************************************************************/
//   Function    :   PreTaskHook
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void PreTaskHook(void)
{
#pragma asm
    stwu sp,-80(sp)
    stmw r12,0(sp)

    lis  r3,currentTaskStkPtr@ha    
    addi r3,r3,currentTaskStkPtr@l
    stw  sp,0(r3)
    blr
#pragma endasm
}

/***************************************************************************/
//   Function    :   PostTaskHook
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void PostTaskHook(void)
{
#pragma asm
    lis  r3, currentTaskStkPtr@h
    //ori  r3, r3, currentTaskStkPtr@l
    e_or2i  r3, currentTaskStkPtr@l
    lwz  sp,0(r3)                              

    lmw  r12,0(sp)
    addi sp,sp,80
    blr
#pragma endasm
}

/***************************************************************************/
//   Function    :   StartupHook
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void StartupHook(void)
/* ------------------------------------------------------------------------- */
{

#if 0
  //cfg_MMU();  MC - K2 use MPU -> to be repalced
#endif

#ifdef _ENABLE_CACHE_  
#ifdef _TEST_CACHE_
#pragma ghs startnomisra
  if (SYS_IsCacheEnabled() == 1u)
  {
      Debug_CacheCfg_Err++;

  }else
  {
      Debug_CacheCfg_Ok++;
  }
#pragma ghs endnomisra
#endif

    cfg_CACHE();

#ifdef _TEST_CACHE_
#pragma ghs startnomisra
    if (SYS_IsCacheEnabled()== 1u)
    {
        Debug_CacheCfg_Ok++;
  
    }else
    {
        Debug_CacheCfg_Err++;
    }
#pragma ghs endnomisra
#endif

#endif

}

/***************************************************************************/
//   Function    :   ShutdownHook
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void ShutdownHook(StatusType event)
{
    if (event != E_OS_TIMEDOUT_KEY_OFF)
    {
        SYS_ProgDelayedShutdown();
    }

    ShutdownOSerrorHandler(event);    
}
/***************************************************************************/
//   Function    :   ErrorHook
//
//   Description:    
/*! \brief Report a short description of this function
*/
//
//  Parameters and Returns:
/*! 
\param param1: param1 meaning 
\param param2: param2 meaning 
\returns: what function returns
*/
//  Notes:        
/*!
Write here a more detailed description of this public function 
*/
/**************************************************************************/
void ErrorHook(StatusType error) 
/* ------------------------------------------------------------------------- */
{
    if (error == E_OS_STATE)
    {
        tempOsService = OsService;
        tempOsObjId = OsObjId;
    }
    SETBIT(&EE_BiosErr, OSEK_IDX);
}

/****************************************************************************
 ****************************************************************************/
#endif // _OSEK_


