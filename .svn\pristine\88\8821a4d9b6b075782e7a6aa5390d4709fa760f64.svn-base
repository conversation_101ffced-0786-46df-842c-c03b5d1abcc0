/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

//#pragma ghs nowarning 550 /* warning #550-D: entity-kind "entity" was set but never used */

/* include BIOS */
#include "sys.h"
#include "task.h"
#include "ivor_c2_out.h"
/* include DD */

/* include APPL */

uint32_T CntMain2Loop;
uint8_T Debug_SysReset_c2 = 0u;
uint8_T Debug_SetIRQ_to_C0 = 0u;
uint8_T Debug_SetIRQ8_c2 = 0u;
int32_T mainCore2(void);

#ifdef _TEST_FLASH_
extern void FLASH_Test(void);
#endif

#ifdef _BUILD_RECOVERY_
#ifdef  _BUILD_RECOVERY_IVOR2_TEST_
extern void FuncRecoveryTest_c2(void);
#endif
#endif



int32_T mainCore2(void) //z2
{    
    EnableAllInterrupts();

  for(;;)
  {
    CntMain2Loop++;
    Core2AliveCounter++;

#ifdef _BUILD_RECOVERY_
#ifdef  _BUILD_RECOVERY_IVOR2_TEST_
    FuncRecoveryTest_c2();
#endif
#endif

    if (Debug_SysReset_c2 == 1u)
    {
       SYS_SwRST();
       Debug_SysReset_c2 = 0u;
       CntMain2Loop = 0u;
    }

#ifdef TASK_SWSETCLRINT_TEST
    if (Debug_SetIRQ_to_C0 == 1u)
    {
        SetIRQ_to_c0();
        Debug_SetIRQ_to_C0 = 0u;
    }

    if (Debug_SetIRQ8_c2 == 1u)
    {
        SetIRQ_c2();
        Debug_SetIRQ8_c2 = 0u;
    }
#endif

    /* Calcolo CpuLoad 
    CpuLoad();*/
    
    /* Task background 
    BackgroundActivities();*/
  }
}


