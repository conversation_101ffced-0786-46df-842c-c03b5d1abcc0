
#----------------------------------------------
# Bit definitions for MCS architecture MCS24-3
#----------------------------------------------

# bit definitions for register STA 
.define EN             0
.define IRQ            1
.define ERR            2
.define MCA            3
.define CY             4
.define Z              5
.define V              6
.define N              7
.define CAT            8
.define CWT            9
.define SAT            10

# bit mask definitions for register STA 
.define EN_MSK          2**EN 
.define IRQ_MSK         2**IRQ
.define ERR_MSK         2**ERR
.define MCA_MSK         2**MCA
.define CY_MSK          2**CY
.define Z_MSK           2**Z
.define V_MSK           2**V
.define N_MSK           2**N
.define CAT_MSK         2**CAT
.define CWT_MSK         2**CWT
.define SAT_MSK         2**SAT

# bit definitions for register ACB 
.define ACB0           0
.define ACB1           1
.define ACB2           2
.define ACB3           3
.define ACB4           4

# bit mask definitions for register ACB 
.define ACB0_MSK           2**ACB0
.define ACB1_MSK           2**ACB1
.define ACB2_MSK           2**ACB2
.define ACB3_MSK           2**ACB3
.define ACB4_MSK           2**ACB4

# ARU write addresses 
.define  ARU_WRADDR     0x000
.define  TIM0_WRADDR0   0x001
.define  TIM0_WRADDR1   0x002
.define  TIM0_WRADDR2   0x003
.define  TIM0_WRADDR3   0x004
.define  TIM0_WRADDR4   0x005
.define  TIM0_WRADDR5   0x006
.define  TIM0_WRADDR6   0x007
.define  TIM0_WRADDR7   0x008
.define  TIM1_WRADDR0   0x009
.define  TIM1_WRADDR1   0x00A
.define  TIM1_WRADDR2   0x00B
.define  TIM1_WRADDR3   0x00C
.define  TIM1_WRADDR4   0x00D
.define  TIM1_WRADDR5   0x00E
.define  TIM1_WRADDR6   0x00F
.define  TIM1_WRADDR7   0x010
.define  TIM2_WRADDR0   0x011
.define  TIM2_WRADDR1   0x012
.define  TIM2_WRADDR2   0x013
.define  TIM2_WRADDR3   0x014
.define  TIM2_WRADDR4   0x015
.define  TIM2_WRADDR5   0x016
.define  TIM2_WRADDR6   0x017
.define  TIM2_WRADDR7   0x018
.define  TIM3_WRADDR0   0x019
.define  TIM3_WRADDR1   0x01A
.define  TIM3_WRADDR2   0x01B
.define  TIM3_WRADDR3   0x01C
.define  TIM3_WRADDR4   0x01D
.define  TIM3_WRADDR5   0x01E
.define  TIM3_WRADDR6   0x01F
.define  TIM3_WRADDR7   0x020
.define  TIM4_WRADDR0   0x021
.define  TIM4_WRADDR1   0x022
.define  TIM4_WRADDR2   0x023
.define  TIM4_WRADDR3   0x024
.define  TIM4_WRADDR4   0x025
.define  TIM4_WRADDR5   0x026
.define  TIM4_WRADDR6   0x027
.define  TIM4_WRADDR7   0x028
.define  TIM5_WRADDR0   0x029
.define  TIM5_WRADDR1   0x02A
.define  TIM5_WRADDR2   0x02B
.define  TIM5_WRADDR3   0x02C
.define  TIM5_WRADDR4   0x02D
.define  TIM5_WRADDR5   0x02E
.define  TIM5_WRADDR6   0x02F
.define  TIM5_WRADDR7   0x030
.define  TIM6_WRADDR0   0x031
.define  TIM6_WRADDR1   0x032
.define  TIM6_WRADDR2   0x033
.define  TIM6_WRADDR3   0x034
.define  TIM6_WRADDR4   0x035
.define  TIM6_WRADDR5   0x036
.define  TIM6_WRADDR6   0x037
.define  TIM6_WRADDR7   0x038
.define  TIM7_WRADDR0   0x19F
.define  TIM7_WRADDR1   0x1A0
.define  TIM7_WRADDR2   0x1A1
.define  TIM7_WRADDR3   0x1A2
.define  TIM7_WRADDR4   0x1A3
.define  TIM7_WRADDR5   0x1A4
.define  TIM7_WRADDR6   0x1A5
.define  TIM7_WRADDR7   0x1A6
.define  F2A0_WRADDR0   0x051
.define  F2A0_WRADDR1   0x052
.define  F2A0_WRADDR2   0x053
.define  F2A0_WRADDR3   0x054
.define  F2A0_WRADDR4   0x055
.define  F2A0_WRADDR5   0x056
.define  F2A0_WRADDR6   0x057
.define  F2A0_WRADDR7   0x058
.define  F2A1_WRADDR0   0x059
.define  F2A1_WRADDR1   0x05A
.define  F2A1_WRADDR2   0x05B
.define  F2A1_WRADDR3   0x05C
.define  F2A1_WRADDR4   0x05D
.define  F2A1_WRADDR5   0x05E
.define  F2A1_WRADDR6   0x05F
.define  F2A1_WRADDR7   0x060
.define  F2A2_WRADDR0   0x1A7
.define  F2A2_WRADDR1   0x1A8
.define  F2A2_WRADDR2   0x1A9
.define  F2A2_WRADDR3   0x1AA
.define  F2A2_WRADDR4   0x1AB
.define  F2A2_WRADDR5   0x1AC
.define  F2A2_WRADDR6   0x1AD
.define  F2A2_WRADDR7   0x1AE
.define  BRC_WRADDR0    0x061
.define  BRC_WRADDR1    0x062
.define  BRC_WRADDR2    0x063
.define  BRC_WRADDR3    0x064
.define  BRC_WRADDR4    0x065
.define  BRC_WRADDR5    0x066
.define  BRC_WRADDR6    0x067
.define  BRC_WRADDR7    0x068
.define  BRC_WRADDR8    0x069
.define  BRC_WRADDR9    0x06A
.define  BRC_WRADDR10   0x06B
.define  BRC_WRADDR11   0x06C
.define  BRC_WRADDR12   0x06D
.define  BRC_WRADDR13   0x06E
.define  BRC_WRADDR14   0x06F
.define  BRC_WRADDR15   0x070
.define  BRC_WRADDR16   0x071
.define  BRC_WRADDR17   0x072
.define  BRC_WRADDR18   0x073
.define  BRC_WRADDR19   0x074
.define  BRC_WRADDR20   0x075
.define  BRC_WRADDR21   0x076
.define  MCS0_WRADDR0   0x077
.define  MCS0_WRADDR1   0x078
.define  MCS0_WRADDR2   0x079
.define  MCS0_WRADDR3   0x07A
.define  MCS0_WRADDR4   0x07B
.define  MCS0_WRADDR5   0x07C
.define  MCS0_WRADDR6   0x07D
.define  MCS0_WRADDR7   0x07E
.define  MCS0_WRADDR8   0x07F
.define  MCS0_WRADDR9   0x080
.define  MCS0_WRADDR10  0x081
.define  MCS0_WRADDR11  0x082
.define  MCS0_WRADDR12  0x083
.define  MCS0_WRADDR13  0x084
.define  MCS0_WRADDR14  0x085
.define  MCS0_WRADDR15  0x086
.define  MCS0_WRADDR16  0x087
.define  MCS0_WRADDR17  0x088
.define  MCS0_WRADDR18  0x089
.define  MCS0_WRADDR19  0x08A
.define  MCS0_WRADDR20  0x08B
.define  MCS0_WRADDR21  0x08C
.define  MCS0_WRADDR22  0x08D
.define  MCS0_WRADDR23  0x08E
.define  MCS1_WRADDR0   0x08F
.define  MCS1_WRADDR1   0x090
.define  MCS1_WRADDR2   0x091
.define  MCS1_WRADDR3   0x092
.define  MCS1_WRADDR4   0x093
.define  MCS1_WRADDR5   0x094
.define  MCS1_WRADDR6   0x095
.define  MCS1_WRADDR7   0x096
.define  MCS1_WRADDR8   0x097
.define  MCS1_WRADDR9   0x098
.define  MCS1_WRADDR10  0x099
.define  MCS1_WRADDR11  0x09A
.define  MCS1_WRADDR12  0x09B
.define  MCS1_WRADDR13  0x09C
.define  MCS1_WRADDR14  0x09D
.define  MCS1_WRADDR15  0x09E
.define  MCS1_WRADDR16  0x09F
.define  MCS1_WRADDR17  0x0A0
.define  MCS1_WRADDR18  0x0A1
.define  MCS1_WRADDR19  0x0A2
.define  MCS1_WRADDR20  0x0A3
.define  MCS1_WRADDR21  0x0A4
.define  MCS1_WRADDR22  0x0A5
.define  MCS1_WRADDR23  0x0A6
.define  MCS2_WRADDR0   0x0A7
.define  MCS2_WRADDR1   0x0A8
.define  MCS2_WRADDR2   0x0A9
.define  MCS2_WRADDR3   0x0AA
.define  MCS2_WRADDR4   0x0AB
.define  MCS2_WRADDR5   0x0AC
.define  MCS2_WRADDR6   0x0AD
.define  MCS2_WRADDR7   0x0AE
.define  MCS2_WRADDR8   0x0AF
.define  MCS2_WRADDR9   0x0B0
.define  MCS2_WRADDR10  0x0B1
.define  MCS2_WRADDR11  0x0B2
.define  MCS2_WRADDR12  0x0B3
.define  MCS2_WRADDR13  0x0B4
.define  MCS2_WRADDR14  0x0B5
.define  MCS2_WRADDR15  0x0B6
.define  MCS2_WRADDR16  0x0B7
.define  MCS2_WRADDR17  0x0B8
.define  MCS2_WRADDR18  0x0B9
.define  MCS2_WRADDR19  0x0BA
.define  MCS2_WRADDR20  0x0BB
.define  MCS2_WRADDR21  0x0BC
.define  MCS2_WRADDR22  0x0BD
.define  MCS2_WRADDR23  0x0BE
.define  MCS3_WRADDR0   0x0BF
.define  MCS3_WRADDR1   0x0C0
.define  MCS3_WRADDR2   0x0C1
.define  MCS3_WRADDR3   0x0C2
.define  MCS3_WRADDR4   0x0C3
.define  MCS3_WRADDR5   0x0C4
.define  MCS3_WRADDR6   0x0C5
.define  MCS3_WRADDR7   0x0C6
.define  MCS3_WRADDR8   0x0C7
.define  MCS3_WRADDR9   0x0C8
.define  MCS3_WRADDR10  0x0C9
.define  MCS3_WRADDR11  0x0CA
.define  MCS3_WRADDR12  0x0CB
.define  MCS3_WRADDR13  0x0CC
.define  MCS3_WRADDR14  0x0CD
.define  MCS3_WRADDR15  0x0CE
.define  MCS3_WRADDR16  0x0CF
.define  MCS3_WRADDR17  0x0D0
.define  MCS3_WRADDR18  0x0D1
.define  MCS3_WRADDR19  0x0D2
.define  MCS3_WRADDR20  0x0D3
.define  MCS3_WRADDR21  0x0D4
.define  MCS3_WRADDR22  0x0D5
.define  MCS3_WRADDR23  0x0D6
.define  MCS4_WRADDR0   0x0D7
.define  MCS4_WRADDR1   0x0D8
.define  MCS4_WRADDR2   0x0D9
.define  MCS4_WRADDR3   0x0DA
.define  MCS4_WRADDR4   0x0DB
.define  MCS4_WRADDR5   0x0DC
.define  MCS4_WRADDR6   0x0DD
.define  MCS4_WRADDR7   0x0DE
.define  MCS4_WRADDR8   0x0DF
.define  MCS4_WRADDR9   0x0E0
.define  MCS4_WRADDR10  0x0E1
.define  MCS4_WRADDR11  0x0E2
.define  MCS4_WRADDR12  0x0E3
.define  MCS4_WRADDR13  0x0E4
.define  MCS4_WRADDR14  0x0E5
.define  MCS4_WRADDR15  0x0E6
.define  MCS4_WRADDR16  0x0E7
.define  MCS4_WRADDR17  0x0E8
.define  MCS4_WRADDR18  0x0E9
.define  MCS4_WRADDR19  0x0EA
.define  MCS4_WRADDR20  0x0EB
.define  MCS4_WRADDR21  0x0EC
.define  MCS4_WRADDR22  0x0ED
.define  MCS4_WRADDR23  0x0EE
.define  MCS5_WRADDR0   0x0EF
.define  MCS5_WRADDR1   0x0F0
.define  MCS5_WRADDR2   0x0F1
.define  MCS5_WRADDR3   0x0F2
.define  MCS5_WRADDR4   0x0F3
.define  MCS5_WRADDR5   0x0F4
.define  MCS5_WRADDR6   0x0F5
.define  MCS5_WRADDR7   0x0F6
.define  MCS5_WRADDR8   0x0F7
.define  MCS5_WRADDR9   0x0F8
.define  MCS5_WRADDR10  0x0F9
.define  MCS5_WRADDR11  0x0FA
.define  MCS5_WRADDR12  0x0FB
.define  MCS5_WRADDR13  0x0FC
.define  MCS5_WRADDR14  0x0FD
.define  MCS5_WRADDR15  0x0FE
.define  MCS5_WRADDR16  0x0FF
.define  MCS5_WRADDR17  0x100
.define  MCS5_WRADDR18  0x101
.define  MCS5_WRADDR19  0x102
.define  MCS5_WRADDR20  0x103
.define  MCS5_WRADDR21  0x104
.define  MCS5_WRADDR22  0x105
.define  MCS5_WRADDR23  0x106
.define  MCS6_WRADDR0   0x107
.define  MCS6_WRADDR1   0x108
.define  MCS6_WRADDR2   0x109
.define  MCS6_WRADDR3   0x10A
.define  MCS6_WRADDR4   0x10B
.define  MCS6_WRADDR5   0x10C
.define  MCS6_WRADDR6   0x10D
.define  MCS6_WRADDR7   0x10E
.define  MCS6_WRADDR8   0x10F
.define  MCS6_WRADDR9   0x110
.define  MCS6_WRADDR10  0x111
.define  MCS6_WRADDR11  0x112
.define  MCS6_WRADDR12  0x113
.define  MCS6_WRADDR13  0x114
.define  MCS6_WRADDR14  0x115
.define  MCS6_WRADDR15  0x116
.define  MCS6_WRADDR16  0x117
.define  MCS6_WRADDR17  0x118
.define  MCS6_WRADDR18  0x119
.define  MCS6_WRADDR19  0x11A
.define  MCS6_WRADDR20  0x11B
.define  MCS6_WRADDR21  0x11C
.define  MCS6_WRADDR22  0x11D
.define  MCS6_WRADDR23  0x11E
.define  MCS7_WRADDR0   0x1AF
.define  MCS7_WRADDR1   0x1B0
.define  MCS7_WRADDR2   0x1B1
.define  MCS7_WRADDR3   0x1B2
.define  MCS7_WRADDR4   0x1B3
.define  MCS7_WRADDR5   0x1B4
.define  MCS7_WRADDR6   0x1B5
.define  MCS7_WRADDR7   0x1B6
.define  MCS7_WRADDR8   0x1B7
.define  MCS7_WRADDR9   0x1B8
.define  MCS7_WRADDR10  0x1B9
.define  MCS7_WRADDR11  0x1BA
.define  MCS7_WRADDR12  0x1BB
.define  MCS7_WRADDR13  0x1BC
.define  MCS7_WRADDR14  0x1BD
.define  MCS7_WRADDR15  0x1BE
.define  MCS7_WRADDR16  0x1BF
.define  MCS7_WRADDR17  0x1C0
.define  MCS7_WRADDR18  0x1C1
.define  MCS7_WRADDR19  0x1C2
.define  MCS7_WRADDR20  0x1C3
.define  MCS7_WRADDR21  0x1C4
.define  MCS7_WRADDR22  0x1C5
.define  MCS7_WRADDR23  0x1C6
.define  MCS8_WRADDR0   0x1C7
.define  MCS8_WRADDR1   0x1C8
.define  MCS8_WRADDR2   0x1C9
.define  MCS8_WRADDR3   0x1CA
.define  MCS8_WRADDR4   0x1CB
.define  MCS8_WRADDR5   0x1CC
.define  MCS8_WRADDR6   0x1CD
.define  MCS8_WRADDR7   0x1CE
.define  MCS8_WRADDR8   0x1CF
.define  MCS8_WRADDR9   0x1D0
.define  MCS8_WRADDR10  0x1D1
.define  MCS8_WRADDR11  0x1D2
.define  MCS8_WRADDR12  0x1D3
.define  MCS8_WRADDR13  0x1D4
.define  MCS8_WRADDR14  0x1D5
.define  MCS8_WRADDR15  0x1D6
.define  MCS8_WRADDR16  0x1D7
.define  MCS8_WRADDR17  0x1D8
.define  MCS8_WRADDR18  0x1D9
.define  MCS8_WRADDR19  0x1DA
.define  MCS8_WRADDR20  0x1DB
.define  MCS8_WRADDR21  0x1DC
.define  MCS8_WRADDR22  0x1DD
.define  MCS8_WRADDR23  0x1DE
.define  MCS9_WRADDR0   0x1DF
.define  MCS9_WRADDR1   0x1E0
.define  MCS9_WRADDR2   0x1E1
.define  MCS9_WRADDR3   0x1E2
.define  MCS9_WRADDR4   0x1E3
.define  MCS9_WRADDR5   0x1E4
.define  MCS9_WRADDR6   0x1E5
.define  MCS9_WRADDR7   0x1E6
.define  MCS9_WRADDR8   0x1E7
.define  MCS9_WRADDR9   0x1E8
.define  MCS9_WRADDR10  0x1E9
.define  MCS9_WRADDR11  0x1EA
.define  MCS9_WRADDR12  0x1EB
.define  MCS9_WRADDR13  0x1EC
.define  MCS9_WRADDR14  0x1ED
.define  MCS9_WRADDR15  0x1EE
.define  MCS9_WRADDR16  0x1EF
.define  MCS9_WRADDR17  0x1F0
.define  MCS9_WRADDR18  0x1F1
.define  MCS9_WRADDR19  0x1F2
.define  MCS9_WRADDR20  0x1F3
.define  MCS9_WRADDR21  0x1F4
.define  MCS9_WRADDR22  0x1F5
.define  MCS9_WRADDR23  0x1F6
.define  ATOM0_WRADDR0  0x11F
.define  ATOM0_WRADDR1  0x120
.define  ATOM0_WRADDR2  0x121
.define  ATOM0_WRADDR3  0x122
.define  ATOM0_WRADDR4  0x123
.define  ATOM0_WRADDR5  0x124
.define  ATOM0_WRADDR6  0x125
.define  ATOM0_WRADDR7  0x126
.define  ATOM1_WRADDR0  0x127
.define  ATOM1_WRADDR1  0x128
.define  ATOM1_WRADDR2  0x129
.define  ATOM1_WRADDR3  0x12A
.define  ATOM1_WRADDR4  0x12B
.define  ATOM1_WRADDR5  0x12C
.define  ATOM1_WRADDR6  0x12D
.define  ATOM1_WRADDR7  0x12E
.define  ATOM2_WRADDR0  0x12F
.define  ATOM2_WRADDR1  0x130
.define  ATOM2_WRADDR2  0x131
.define  ATOM2_WRADDR3  0x132
.define  ATOM2_WRADDR4  0x133
.define  ATOM2_WRADDR5  0x134
.define  ATOM2_WRADDR6  0x135
.define  ATOM2_WRADDR7  0x136
.define  ATOM3_WRADDR0  0x137
.define  ATOM3_WRADDR1  0x138
.define  ATOM3_WRADDR2  0x139
.define  ATOM3_WRADDR3  0x13A
.define  ATOM3_WRADDR4  0x13B
.define  ATOM3_WRADDR5  0x13C
.define  ATOM3_WRADDR6  0x13D
.define  ATOM3_WRADDR7  0x13E
.define  ATOM4_WRADDR0  0x13F
.define  ATOM4_WRADDR1  0x140
.define  ATOM4_WRADDR2  0x141
.define  ATOM4_WRADDR3  0x142
.define  ATOM4_WRADDR4  0x143
.define  ATOM4_WRADDR5  0x144
.define  ATOM4_WRADDR6  0x145
.define  ATOM4_WRADDR7  0x146
.define  ATOM5_WRADDR0  0x147
.define  ATOM5_WRADDR1  0x148
.define  ATOM5_WRADDR2  0x149
.define  ATOM5_WRADDR3  0x14A
.define  ATOM5_WRADDR4  0x14B
.define  ATOM5_WRADDR5  0x14C
.define  ATOM5_WRADDR6  0x14D
.define  ATOM5_WRADDR7  0x14E
.define  ATOM6_WRADDR0  0x14F
.define  ATOM6_WRADDR1  0x150
.define  ATOM6_WRADDR2  0x151
.define  ATOM6_WRADDR3  0x152
.define  ATOM6_WRADDR4  0x153
.define  ATOM6_WRADDR5  0x154
.define  ATOM6_WRADDR6  0x155
.define  ATOM6_WRADDR7  0x156
.define  ATOM7_WRADDR0  0x157
.define  ATOM7_WRADDR1  0x158
.define  ATOM7_WRADDR2  0x159
.define  ATOM7_WRADDR3  0x15A
.define  ATOM7_WRADDR4  0x15B
.define  ATOM7_WRADDR5  0x15C
.define  ATOM7_WRADDR6  0x15D
.define  ATOM7_WRADDR7  0x15E
.define  ATOM8_WRADDR0  0x15F
.define  ATOM8_WRADDR1  0x160
.define  ATOM8_WRADDR2  0x161
.define  ATOM8_WRADDR3  0x162
.define  ATOM8_WRADDR4  0x163
.define  ATOM8_WRADDR5  0x164
.define  ATOM8_WRADDR6  0x165
.define  ATOM8_WRADDR7  0x166
.define  ATOM9_WRADDR0  0x167
.define  ATOM9_WRADDR1  0x168
.define  ATOM9_WRADDR2  0x169
.define  ATOM9_WRADDR3  0x16A
.define  ATOM9_WRADDR4  0x16B
.define  ATOM9_WRADDR5  0x16C
.define  ATOM9_WRADDR6  0x16D
.define  ATOM9_WRADDR7  0x16E
.define  ATOM10_WRADDR0 0x16F
.define  ATOM10_WRADDR1 0x170
.define  ATOM10_WRADDR2 0x171
.define  ATOM10_WRADDR3 0x172
.define  ATOM10_WRADDR4 0x173
.define  ATOM10_WRADDR5 0x174
.define  ATOM10_WRADDR6 0x175
.define  ATOM10_WRADDR7 0x176
.define  ATOM11_WRADDR0 0x177
.define  ATOM11_WRADDR1 0x178
.define  ATOM11_WRADDR2 0x179
.define  ATOM11_WRADDR3 0x17A
.define  ATOM11_WRADDR4 0x17B
.define  ATOM11_WRADDR5 0x17C
.define  ATOM11_WRADDR6 0x17D
.define  ATOM11_WRADDR7 0x17E
.define  DPLL_WRADDR0   0x17F
.define  DPLL_WRADDR1   0x180
.define  DPLL_WRADDR2   0x181
.define  DPLL_WRADDR3   0x182
.define  DPLL_WRADDR4   0x183
.define  DPLL_WRADDR5   0x184
.define  DPLL_WRADDR6   0x185
.define  DPLL_WRADDR7   0x186
.define  DPLL_WRADDR8   0x187
.define  DPLL_WRADDR9   0x188
.define  DPLL_WRADDR10  0x189
.define  DPLL_WRADDR11  0x18A
.define  DPLL_WRADDR12  0x18B
.define  DPLL_WRADDR13  0x18C
.define  DPLL_WRADDR14  0x18D
.define  DPLL_WRADDR15  0x18E
.define  DPLL_WRADDR16  0x18F
.define  DPLL_WRADDR17  0x190
.define  DPLL_WRADDR18  0x191
.define  DPLL_WRADDR19  0x192
.define  DPLL_WRADDR20  0x193
.define  DPLL_WRADDR21  0x194
.define  DPLL_WRADDR22  0x195
.define  DPLL_WRADDR23  0x196
.define  DPLL_WRADDR24  0x197
.define  DPLL_WRADDR25  0x198
.define  DPLL_WRADDR26  0x199
.define  DPLL_WRADDR27  0x19A
.define  DPLL_WRADDR28  0x19B
.define  DPLL_WRADDR29  0x19C
.define  DPLL_WRADDR30  0x19D
.define  DPLL_WRADDR31  0x19E
.define  ARU_EMPTY_ADDR 0x1FE
.define  ARU_FULL_ADDR  0x1FF

# bus master addresses
.define TIM_CH0_GPR0                  0x00000000
.define TIM_CH0_GPR1                  0x00000004
.define TIM_CH0_CNT                   0x00000008
.define TIM_CH0_ECNT                  0x0000000C
.define TIM_CH0_CNTS                  0x00000010
.define TIM_CH0_TDUC                  0x00000014
.define TIM_CH0_TDUV                  0x00000018
.define TIM_CH0_FLT_RE                0x0000001C
.define TIM_CH0_FLT_FE                0x00000020
.define TIM_CH0_CTRL                  0x00000024
.define TIM_CH0_ECTRL                 0x00000028
.define TIM_CH0_IRQ_NOTIFY            0x0000002C
.define TIM_CH0_IRQ_EN                0x00000030
.define TIM_CH0_IRQ_FORCINT           0x00000034
.define TIM_CH0_IRQ_MODE              0x00000038
.define TIM_CH0_EIRQ_EN               0x0000003C
.define TIM_INP_VAL                   0x00000074
.define TIM_IN_SRC                    0x00000078
.define TIM_RST                       0x0000007C
.define TIM_CH1_GPR0                  0x00000080
.define TIM_CH1_GPR1                  0x00000084
.define TIM_CH1_CNT                   0x00000088
.define TIM_CH1_ECNT                  0x0000008C
.define TIM_CH1_CNTS                  0x00000090
.define TIM_CH1_TDUC                  0x00000094
.define TIM_CH1_TDUV                  0x00000098
.define TIM_CH1_FLT_RE                0x0000009C
.define TIM_CH1_FLT_FE                0x000000A0
.define TIM_CH1_CTRL                  0x000000A4
.define TIM_CH1_ECTRL                 0x000000A8
.define TIM_CH1_IRQ_NOTIFY            0x000000AC
.define TIM_CH1_IRQ_EN                0x000000B0
.define TIM_CH1_IRQ_FORCINT           0x000000B4
.define TIM_CH1_IRQ_MODE              0x000000B8
.define TIM_CH1_EIRQ_EN               0x000000BC
.define TIM_CH2_GPR0                  0x00000100
.define TIM_CH2_GPR1                  0x00000104
.define TIM_CH2_CNT                   0x00000108
.define TIM_CH2_ECNT                  0x0000010C
.define TIM_CH2_CNTS                  0x00000110
.define TIM_CH2_TDUC                  0x00000114
.define TIM_CH2_TDUV                  0x00000118
.define TIM_CH2_FLT_RE                0x0000011C
.define TIM_CH2_FLT_FE                0x00000120
.define TIM_CH2_CTRL                  0x00000124
.define TIM_CH2_ECTRL                 0x00000128
.define TIM_CH2_IRQ_NOTIFY            0x0000012C
.define TIM_CH2_IRQ_EN                0x00000130
.define TIM_CH2_IRQ_FORCINT           0x00000134
.define TIM_CH2_IRQ_MODE              0x00000138
.define TIM_CH2_EIRQ_EN               0x0000013C
.define TIM_CH3_GPR0                  0x00000180
.define TIM_CH3_GPR1                  0x00000184
.define TIM_CH3_CNT                   0x00000188
.define TIM_CH3_ECNT                  0x0000018C
.define TIM_CH3_CNTS                  0x00000190
.define TIM_CH3_TDUC                  0x00000194
.define TIM_CH3_TDUV                  0x00000198
.define TIM_CH3_FLT_RE                0x0000019C
.define TIM_CH3_FLT_FE                0x000001A0
.define TIM_CH3_CTRL                  0x000001A4
.define TIM_CH3_ECTRL                 0x000001A8
.define TIM_CH3_IRQ_NOTIFY            0x000001AC
.define TIM_CH3_IRQ_EN                0x000001B0
.define TIM_CH3_IRQ_FORCINT           0x000001B4
.define TIM_CH3_IRQ_MODE              0x000001B8
.define TIM_CH3_EIRQ_EN               0x000001BC
.define TIM_CH4_GPR0                  0x00000200
.define TIM_CH4_GPR1                  0x00000204
.define TIM_CH4_CNT                   0x00000208
.define TIM_CH4_ECNT                  0x0000020C
.define TIM_CH4_CNTS                  0x00000210
.define TIM_CH4_TDUC                  0x00000214
.define TIM_CH4_TDUV                  0x00000218
.define TIM_CH4_FLT_RE                0x0000021C
.define TIM_CH4_FLT_FE                0x00000220
.define TIM_CH4_CTRL                  0x00000224
.define TIM_CH4_ECTRL                 0x00000228
.define TIM_CH4_IRQ_NOTIFY            0x0000022C
.define TIM_CH4_IRQ_EN                0x00000230
.define TIM_CH4_IRQ_FORCINT           0x00000234
.define TIM_CH4_IRQ_MODE              0x00000238
.define TIM_CH4_EIRQ_EN               0x0000023C
.define TIM_CH5_GPR0                  0x00000280
.define TIM_CH5_GPR1                  0x00000284
.define TIM_CH5_CNT                   0x00000288
.define TIM_CH5_ECNT                  0x0000028C
.define TIM_CH5_CNTS                  0x00000290
.define TIM_CH5_TDUC                  0x00000294
.define TIM_CH5_TDUV                  0x00000298
.define TIM_CH5_FLT_RE                0x0000029C
.define TIM_CH5_FLT_FE                0x000002A0
.define TIM_CH5_CTRL                  0x000002A4
.define TIM_CH5_ECTRL                 0x000002A8
.define TIM_CH5_IRQ_NOTIFY            0x000002AC
.define TIM_CH5_IRQ_EN                0x000002B0
.define TIM_CH5_IRQ_FORCINT           0x000002B4
.define TIM_CH5_IRQ_MODE              0x000002B8
.define TIM_CH5_EIRQ_EN               0x000002BC
.define TIM_CH6_GPR0                  0x00000300
.define TIM_CH6_GPR1                  0x00000304
.define TIM_CH6_CNT                   0x00000308
.define TIM_CH6_ECNT                  0x0000030C
.define TIM_CH6_CNTS                  0x00000310
.define TIM_CH6_TDUC                  0x00000314
.define TIM_CH6_TDUV                  0x00000318
.define TIM_CH6_FLT_RE                0x0000031C
.define TIM_CH6_FLT_FE                0x00000320
.define TIM_CH6_CTRL                  0x00000324
.define TIM_CH6_ECTRL                 0x00000328
.define TIM_CH6_IRQ_NOTIFY            0x0000032C
.define TIM_CH6_IRQ_EN                0x00000330
.define TIM_CH6_IRQ_FORCINT           0x00000334
.define TIM_CH6_IRQ_MODE              0x00000338
.define TIM_CH6_EIRQ_EN               0x0000033C
.define TIM_CH7_GPR0                  0x00000380
.define TIM_CH7_GPR1                  0x00000384
.define TIM_CH7_CNT                   0x00000388
.define TIM_CH7_ECNT                  0x0000038C
.define TIM_CH7_CNTS                  0x00000390
.define TIM_CH7_TDUC                  0x00000394
.define TIM_CH7_TDUV                  0x00000398
.define TIM_CH7_FLT_RE                0x0000039C
.define TIM_CH7_FLT_FE                0x000003A0
.define TIM_CH7_CTRL                  0x000003A4
.define TIM_CH7_ECTRL                 0x000003A8
.define TIM_CH7_IRQ_NOTIFY            0x000003AC
.define TIM_CH7_IRQ_EN                0x000003B0
.define TIM_CH7_IRQ_FORCINT           0x000003B4
.define TIM_CH7_IRQ_MODE              0x000003B8
.define TIM_CH7_EIRQ_EN               0x000003BC
.define TOM_CH0_CTRL                  0x00000C00
.define TOM_CH0_SR0                   0x00000C04
.define TOM_CH0_SR1                   0x00000C08
.define TOM_CH0_CM0                   0x00000C0C
.define TOM_CH0_CM1                   0x00000C10
.define TOM_CH0_CN0                   0x00000C14
.define TOM_CH0_STAT                  0x00000C18
.define TOM_CH0_IRQ_NOTIFY            0x00000C1C
.define TOM_CH0_IRQ_EN                0x00000C20
.define TOM_CH0_IRQ_FORCINT           0x00000C24
.define TOM_CH0_IRQ_MODE              0x00000C28
.define TOM_TGC0_GLB_CTRL             0x00000C30
.define TOM_TGC0_ACT_TB               0x00000C34
.define TOM_TGC0_FUPD_CTRL            0x00000C38
.define TOM_TGC0_INT_TRIG             0x00000C3C
.define TOM_CH1_CTRL                  0x00000C40
.define TOM_CH1_SR0                   0x00000C44
.define TOM_CH1_SR1                   0x00000C48
.define TOM_CH1_CM0                   0x00000C4C
.define TOM_CH1_CM1                   0x00000C50
.define TOM_CH1_CN0                   0x00000C54
.define TOM_CH1_STAT                  0x00000C58
.define TOM_CH1_IRQ_NOTIFY            0x00000C5C
.define TOM_CH1_IRQ_EN                0x00000C60
.define TOM_CH1_IRQ_FORCINT           0x00000C64
.define TOM_CH1_IRQ_MODE              0x00000C68
.define TOM_TGC0_ENDIS_CTRL           0x00000C70
.define TOM_TGC0_ENDIS_STAT           0x00000C74
.define TOM_TGC0_OUTEN_CTRL           0x00000C78
.define TOM_TGC0_OUTEN_STAT           0x00000C7C
.define TOM_CH2_CTRL                  0x00000C80
.define TOM_CH2_SR0                   0x00000C84
.define TOM_CH2_SR1                   0x00000C88
.define TOM_CH2_CM0                   0x00000C8C
.define TOM_CH2_CM1                   0x00000C90
.define TOM_CH2_CN0                   0x00000C94
.define TOM_CH2_STAT                  0x00000C98
.define TOM_CH2_IRQ_NOTIFY            0x00000C9C
.define TOM_CH2_IRQ_EN                0x00000CA0
.define TOM_CH2_IRQ_FORCINT           0x00000CA4
.define TOM_CH2_IRQ_MODE              0x00000CA8
.define TOM_CH3_CTRL                  0x00000CC0
.define TOM_CH3_SR0                   0x00000CC4
.define TOM_CH3_SR1                   0x00000CC8
.define TOM_CH3_CM0                   0x00000CCC
.define TOM_CH3_CM1                   0x00000CD0
.define TOM_CH3_CN0                   0x00000CD4
.define TOM_CH3_STAT                  0x00000CD8
.define TOM_CH3_IRQ_NOTIFY            0x00000CDC
.define TOM_CH3_IRQ_EN                0x00000CE0
.define TOM_CH3_IRQ_FORCINT           0x00000CE4
.define TOM_CH3_IRQ_MODE              0x00000CE8
.define TOM_CH4_CTRL                  0x00000D00
.define TOM_CH4_SR0                   0x00000D04
.define TOM_CH4_SR1                   0x00000D08
.define TOM_CH4_CM0                   0x00000D0C
.define TOM_CH4_CM1                   0x00000D10
.define TOM_CH4_CN0                   0x00000D14
.define TOM_CH4_STAT                  0x00000D18
.define TOM_CH4_IRQ_NOTIFY            0x00000D1C
.define TOM_CH4_IRQ_EN                0x00000D20
.define TOM_CH4_IRQ_FORCINT           0x00000D24
.define TOM_CH4_IRQ_MODE              0x00000D28
.define TOM_CH5_CTRL                  0x00000D40
.define TOM_CH5_SR0                   0x00000D44
.define TOM_CH5_SR1                   0x00000D48
.define TOM_CH5_CM0                   0x00000D4C
.define TOM_CH5_CM1                   0x00000D50
.define TOM_CH5_CN0                   0x00000D54
.define TOM_CH5_STAT                  0x00000D58
.define TOM_CH5_IRQ_NOTIFY            0x00000D5C
.define TOM_CH5_IRQ_EN                0x00000D60
.define TOM_CH5_IRQ_FORCINT           0x00000D64
.define TOM_CH5_IRQ_MODE              0x00000D68
.define TOM_CH6_CTRL                  0x00000D80
.define TOM_CH6_SR0                   0x00000D84
.define TOM_CH6_SR1                   0x00000D88
.define TOM_CH6_CM0                   0x00000D8C
.define TOM_CH6_CM1                   0x00000D90
.define TOM_CH6_CN0                   0x00000D94
.define TOM_CH6_STAT                  0x00000D98
.define TOM_CH6_IRQ_NOTIFY            0x00000D9C
.define TOM_CH6_IRQ_EN                0x00000DA0
.define TOM_CH6_IRQ_FORCINT           0x00000DA4
.define TOM_CH6_IRQ_MODE              0x00000DA8
.define TOM_CH7_CTRL                  0x00000DC0
.define TOM_CH7_SR0                   0x00000DC4
.define TOM_CH7_SR1                   0x00000DC8
.define TOM_CH7_CM0                   0x00000DCC
.define TOM_CH7_CM1                   0x00000DD0
.define TOM_CH7_CN0                   0x00000DD4
.define TOM_CH7_STAT                  0x00000DD8
.define TOM_CH7_IRQ_NOTIFY            0x00000DDC
.define TOM_CH7_IRQ_EN                0x00000DE0
.define TOM_CH7_IRQ_FORCINT           0x00000DE4
.define TOM_CH7_IRQ_MODE              0x00000DE8
.define TOM_CH8_CTRL                  0x00000E00
.define TOM_CH8_SR0                   0x00000E04
.define TOM_CH8_SR1                   0x00000E08
.define TOM_CH8_CM0                   0x00000E0C
.define TOM_CH8_CM1                   0x00000E10
.define TOM_CH8_CN0                   0x00000E14
.define TOM_CH8_STAT                  0x00000E18
.define TOM_CH8_IRQ_NOTIFY            0x00000E1C
.define TOM_CH8_IRQ_EN                0x00000E20
.define TOM_CH8_IRQ_FORCINT           0x00000E24
.define TOM_CH8_IRQ_MODE              0x00000E28
.define TOM_TGC1_GLB_CTRL             0x00000E30
.define TOM_TGC1_ACT_TB               0x00000E34
.define TOM_TGC1_FUPD_CTRL            0x00000E38
.define TOM_TGC1_INT_TRIG             0x00000E3C
.define TOM_CH9_CTRL                  0x00000E40
.define TOM_CH9_SR0                   0x00000E44
.define TOM_CH9_SR1                   0x00000E48
.define TOM_CH9_CM0                   0x00000E4C
.define TOM_CH9_CM1                   0x00000E50
.define TOM_CH9_CN0                   0x00000E54
.define TOM_CH9_STAT                  0x00000E58
.define TOM_CH9_IRQ_NOTIFY            0x00000E5C
.define TOM_CH9_IRQ_EN                0x00000E60
.define TOM_CH9_IRQ_FORCINT           0x00000E64
.define TOM_CH9_IRQ_MODE              0x00000E68
.define TOM_TGC1_ENDIS_CTRL           0x00000E70
.define TOM_TGC1_ENDIS_STAT           0x00000E74
.define TOM_TGC1_OUTEN_CTRL           0x00000E78
.define TOM_TGC1_OUTEN_STAT           0x00000E7C
.define TOM_CH10_CTRL                 0x00000E80
.define TOM_CH10_SR0                  0x00000E84
.define TOM_CH10_SR1                  0x00000E88
.define TOM_CH10_CM0                  0x00000E8C
.define TOM_CH10_CM1                  0x00000E90
.define TOM_CH10_CN0                  0x00000E94
.define TOM_CH10_STAT                 0x00000E98
.define TOM_CH10_IRQ_NOTIFY           0x00000E9C
.define TOM_CH10_IRQ_EN               0x00000EA0
.define TOM_CH10_IRQ_FORCINT          0x00000EA4
.define TOM_CH10_IRQ_MODE             0x00000EA8
.define TOM_CH11_CTRL                 0x00000EC0
.define TOM_CH11_SR0                  0x00000EC4
.define TOM_CH11_SR1                  0x00000EC8
.define TOM_CH11_CM0                  0x00000ECC
.define TOM_CH11_CM1                  0x00000ED0
.define TOM_CH11_CN0                  0x00000ED4
.define TOM_CH11_STAT                 0x00000ED8
.define TOM_CH11_IRQ_NOTIFY           0x00000EDC
.define TOM_CH11_IRQ_EN               0x00000EE0
.define TOM_CH11_IRQ_FORCINT          0x00000EE4
.define TOM_CH11_IRQ_MODE             0x00000EE8
.define TOM_CH12_CTRL                 0x00000F00
.define TOM_CH12_SR0                  0x00000F04
.define TOM_CH12_SR1                  0x00000F08
.define TOM_CH12_CM0                  0x00000F0C
.define TOM_CH12_CM1                  0x00000F10
.define TOM_CH12_CN0                  0x00000F14
.define TOM_CH12_STAT                 0x00000F18
.define TOM_CH12_IRQ_NOTIFY           0x00000F1C
.define TOM_CH12_IRQ_EN               0x00000F20
.define TOM_CH12_IRQ_FORCINT          0x00000F24
.define TOM_CH12_IRQ_MODE             0x00000F28
.define TOM_CH13_CTRL                 0x00000F40
.define TOM_CH13_SR0                  0x00000F44
.define TOM_CH13_SR1                  0x00000F48
.define TOM_CH13_CM0                  0x00000F4C
.define TOM_CH13_CM1                  0x00000F50
.define TOM_CH13_CN0                  0x00000F54
.define TOM_CH13_STAT                 0x00000F58
.define TOM_CH13_IRQ_NOTIFY           0x00000F5C
.define TOM_CH13_IRQ_EN               0x00000F60
.define TOM_CH13_IRQ_FORCINT          0x00000F64
.define TOM_CH13_IRQ_MODE             0x00000F68
.define TOM_CH14_CTRL                 0x00000F80
.define TOM_CH14_SR0                  0x00000F84
.define TOM_CH14_SR1                  0x00000F88
.define TOM_CH14_CM0                  0x00000F8C
.define TOM_CH14_CM1                  0x00000F90
.define TOM_CH14_CN0                  0x00000F94
.define TOM_CH14_STAT                 0x00000F98
.define TOM_CH14_IRQ_NOTIFY           0x00000F9C
.define TOM_CH14_IRQ_EN               0x00000FA0
.define TOM_CH14_IRQ_FORCINT          0x00000FA4
.define TOM_CH14_IRQ_MODE             0x00000FA8
.define TOM_CH15_CTRL                 0x00000FC0
.define TOM_CH15_SR0                  0x00000FC4
.define TOM_CH15_SR1                  0x00000FC8
.define TOM_CH15_CM0                  0x00000FCC
.define TOM_CH15_CM1                  0x00000FD0
.define TOM_CH15_CN0                  0x00000FD4
.define TOM_CH15_STAT                 0x00000FD8
.define TOM_CH15_IRQ_NOTIFY           0x00000FDC
.define TOM_CH15_IRQ_EN               0x00000FE0
.define TOM_CH15_IRQ_FORCINT          0x00000FE4
.define TOM_CH15_IRQ_MODE             0x00000FE8
.define ATOM_CH0_RDADDR               0x00001800
.define ATOM_CH0_CTRL                 0x00001804
.define ATOM_CH0_SR0                  0x00001808
.define ATOM_CH0_SR1                  0x0000180C
.define ATOM_CH0_CM0                  0x00001810
.define ATOM_CH0_CM1                  0x00001814
.define ATOM_CH0_CN0                  0x00001818
.define ATOM_CH0_STAT                 0x0000181C
.define ATOM_CH0_IRQ_NOTIFY           0x00001820
.define ATOM_CH0_IRQ_EN               0x00001824
.define ATOM_CH0_IRQ_FORCINT          0x00001828
.define ATOM_CH0_IRQ_MODE             0x0000182C
.define ATOM_AGC_GLB_CTRL             0x00001840
.define ATOM_AGC_ENDIS_CTRL           0x00001844
.define ATOM_AGC_ENDIS_STAT           0x00001848
.define ATOM_AGC_ACT_TB               0x0000184C
.define ATOM_AGC_OUTEN_CTRL           0x00001850
.define ATOM_AGC_OUTEN_STAT           0x00001854
.define ATOM_AGC_FUPD_CTRL            0x00001858
.define ATOM_AGC_INT_TRIG             0x0000185C
.define ATOM_CH1_RDADDR               0x00001880
.define ATOM_CH1_CTRL                 0x00001884
.define ATOM_CH1_SR0                  0x00001888
.define ATOM_CH1_SR1                  0x0000188C
.define ATOM_CH1_CM0                  0x00001890
.define ATOM_CH1_CM1                  0x00001894
.define ATOM_CH1_CN0                  0x00001898
.define ATOM_CH1_STAT                 0x0000189C
.define ATOM_CH1_IRQ_NOTIFY           0x000018A0
.define ATOM_CH1_IRQ_EN               0x000018A4
.define ATOM_CH1_IRQ_FORCINT          0x000018A8
.define ATOM_CH1_IRQ_MODE             0x000018AC
.define ATOM_CH2_RDADDR               0x00001900
.define ATOM_CH2_CTRL                 0x00001904
.define ATOM_CH2_SR0                  0x00001908
.define ATOM_CH2_SR1                  0x0000190C
.define ATOM_CH2_CM0                  0x00001910
.define ATOM_CH2_CM1                  0x00001914
.define ATOM_CH2_CN0                  0x00001918
.define ATOM_CH2_STAT                 0x0000191C
.define ATOM_CH2_IRQ_NOTIFY           0x00001920
.define ATOM_CH2_IRQ_EN               0x00001924
.define ATOM_CH2_IRQ_FORCINT          0x00001928
.define ATOM_CH2_IRQ_MODE             0x0000192C
.define ATOM_CH3_RDADDR               0x00001980
.define ATOM_CH3_CTRL                 0x00001984
.define ATOM_CH3_SR0                  0x00001988
.define ATOM_CH3_SR1                  0x0000198C
.define ATOM_CH3_CM0                  0x00001990
.define ATOM_CH3_CM1                  0x00001994
.define ATOM_CH3_CN0                  0x00001998
.define ATOM_CH3_STAT                 0x0000199C
.define ATOM_CH3_IRQ_NOTIFY           0x000019A0
.define ATOM_CH3_IRQ_EN               0x000019A4
.define ATOM_CH3_IRQ_FORCINT          0x000019A8
.define ATOM_CH3_IRQ_MODE             0x000019AC
.define ATOM_CH4_RDADDR               0x00001A00
.define ATOM_CH4_CTRL                 0x00001A04
.define ATOM_CH4_SR0                  0x00001A08
.define ATOM_CH4_SR1                  0x00001A0C
.define ATOM_CH4_CM0                  0x00001A10
.define ATOM_CH4_CM1                  0x00001A14
.define ATOM_CH4_CN0                  0x00001A18
.define ATOM_CH4_STAT                 0x00001A1C
.define ATOM_CH4_IRQ_NOTIFY           0x00001A20
.define ATOM_CH4_IRQ_EN               0x00001A24
.define ATOM_CH4_IRQ_FORCINT          0x00001A28
.define ATOM_CH4_IRQ_MODE             0x00001A2C
.define ATOM_CH5_RDADDR               0x00001A80
.define ATOM_CH5_CTRL                 0x00001A84
.define ATOM_CH5_SR0                  0x00001A88
.define ATOM_CH5_SR1                  0x00001A8C
.define ATOM_CH5_CM0                  0x00001A90
.define ATOM_CH5_CM1                  0x00001A94
.define ATOM_CH5_CN0                  0x00001A98
.define ATOM_CH5_STAT                 0x00001A9C
.define ATOM_CH5_IRQ_NOTIFY           0x00001AA0
.define ATOM_CH5_IRQ_EN               0x00001AA4
.define ATOM_CH5_IRQ_FORCINT          0x00001AA8
.define ATOM_CH5_IRQ_MODE             0x00001AAC
.define ATOM_CH6_RDADDR               0x00001B00
.define ATOM_CH6_CTRL                 0x00001B04
.define ATOM_CH6_SR0                  0x00001B08
.define ATOM_CH6_SR1                  0x00001B0C
.define ATOM_CH6_CM0                  0x00001B10
.define ATOM_CH6_CM1                  0x00001B14
.define ATOM_CH6_CN0                  0x00001B18
.define ATOM_CH6_STAT                 0x00001B1C
.define ATOM_CH6_IRQ_NOTIFY           0x00001B20
.define ATOM_CH6_IRQ_EN               0x00001B24
.define ATOM_CH6_IRQ_FORCINT          0x00001B28
.define ATOM_CH6_IRQ_MODE             0x00001B2C
.define ATOM_CH7_RDADDR               0x00001B80
.define ATOM_CH7_CTRL                 0x00001B84
.define ATOM_CH7_SR0                  0x00001B88
.define ATOM_CH7_SR1                  0x00001B8C
.define ATOM_CH7_CM0                  0x00001B90
.define ATOM_CH7_CM1                  0x00001B94
.define ATOM_CH7_CN0                  0x00001B98
.define ATOM_CH7_STAT                 0x00001B9C
.define ATOM_CH7_IRQ_NOTIFY           0x00001BA0
.define ATOM_CH7_IRQ_EN               0x00001BA4
.define ATOM_CH7_IRQ_FORCINT          0x00001BA8
.define ATOM_CH7_IRQ_MODE             0x00001BAC
.define CDTM_DTM0_CTRL                0x00003000
.define CDTM_DTM0_CH_CTRL1            0x00003004
.define CDTM_DTM0_CH_CTRL2            0x00003008
.define CDTM_DTM0_CH_CTRL2_SR         0x0000300C
.define CDTM_DTM0_PS_CTRL             0x00003010
.define CDTM_DTM0_CH0_DTV             0x00003014
.define CDTM_DTM0_CH1_DTV             0x00003018
.define CDTM_DTM0_CH2_DTV             0x0000301C
.define CDTM_DTM0_CH3_DTV             0x00003020
.define CDTM_DTM0_CH_SR               0x00003024
.define CDTM_DTM0_CH_CTRL3            0x00003028
.define CDTM_DTM1_CTRL                0x00003040
.define CDTM_DTM1_CH_CTRL1            0x00003044
.define CDTM_DTM1_CH_CTRL2            0x00003048
.define CDTM_DTM1_CH_CTRL2_SR         0x0000304C
.define CDTM_DTM1_PS_CTRL             0x00003050
.define CDTM_DTM1_CH0_DTV             0x00003054
.define CDTM_DTM1_CH1_DTV             0x00003058
.define CDTM_DTM1_CH2_DTV             0x0000305C
.define CDTM_DTM1_CH3_DTV             0x00003060
.define CDTM_DTM1_CH_SR               0x00003064
.define CDTM_DTM1_CH_CTRL3            0x00003068
.define CDTM_DTM2_CTRL                0x00003080
.define CDTM_DTM2_CH_CTRL1            0x00003084
.define CDTM_DTM2_CH_CTRL2            0x00003088
.define CDTM_DTM2_CH_CTRL2_SR         0x0000308C
.define CDTM_DTM2_PS_CTRL             0x00003090
.define CDTM_DTM2_CH0_DTV             0x00003094
.define CDTM_DTM2_CH1_DTV             0x00003098
.define CDTM_DTM2_CH2_DTV             0x0000309C
.define CDTM_DTM2_CH3_DTV             0x000030A0
.define CDTM_DTM2_CH_SR               0x000030A4
.define CDTM_DTM2_CH_CTRL3            0x000030A8
.define CDTM_DTM3_CTRL                0x000030C0
.define CDTM_DTM3_CH_CTRL1            0x000030C4
.define CDTM_DTM3_CH_CTRL2            0x000030C8
.define CDTM_DTM3_CH_CTRL2_SR         0x000030CC
.define CDTM_DTM3_PS_CTRL             0x000030D0
.define CDTM_DTM3_CH0_DTV             0x000030D4
.define CDTM_DTM3_CH1_DTV             0x000030D8
.define CDTM_DTM3_CH2_DTV             0x000030DC
.define CDTM_DTM3_CH3_DTV             0x000030E0
.define CDTM_DTM3_CH_SR               0x000030E4
.define CDTM_DTM3_CH_CTRL3            0x000030E8
.define CDTM_DTM4_CTRL                0x00003100
.define CDTM_DTM4_CH_CTRL1            0x00003104
.define CDTM_DTM4_CH_CTRL2            0x00003108
.define CDTM_DTM4_CH_CTRL2_SR         0x0000310C
.define CDTM_DTM4_PS_CTRL             0x00003110
.define CDTM_DTM4_CH0_DTV             0x00003114
.define CDTM_DTM4_CH1_DTV             0x00003118
.define CDTM_DTM4_CH2_DTV             0x0000311C
.define CDTM_DTM4_CH3_DTV             0x00003120
.define CDTM_DTM4_CH_SR               0x00003124
.define CDTM_DTM4_CH_CTRL3            0x00003128
.define CDTM_DTM5_CTRL                0x00003140
.define CDTM_DTM5_CH_CTRL1            0x00003144
.define CDTM_DTM5_CH_CTRL2            0x00003148
.define CDTM_DTM5_CH_CTRL2_SR         0x0000314C
.define CDTM_DTM5_PS_CTRL             0x00003150
.define CDTM_DTM5_CH0_DTV             0x00003154
.define CDTM_DTM5_CH1_DTV             0x00003158
.define CDTM_DTM5_CH2_DTV             0x0000315C
.define CDTM_DTM5_CH3_DTV             0x00003160
.define CDTM_DTM5_CH_SR               0x00003164
.define CDTM_DTM5_CH_CTRL3            0x00003168
.define MCS_CH0_R0                    0x00003800
.define MCS_CH0_R1                    0x00003804
.define MCS_CH0_R2                    0x00003808
.define MCS_CH0_R3                    0x0000380C
.define MCS_CH0_R4                    0x00003810
.define MCS_CH0_R5                    0x00003814
.define MCS_CH0_R6                    0x00003818
.define MCS_CH0_R7                    0x0000381C
.define MCS_CH0_CTRL                  0x00003820
.define MCS_CH0_ACB                   0x00003824
.define MCS_CTRG                      0x00003828
.define MCS_STRG                      0x0000382C
.define MCS_CH0_MHB                   0x0000383C
.define MCS_CH0_PC                    0x00003840
.define MCS_CH0_IRQ_NOTIFY            0x00003844
.define MCS_CH0_IRQ_EN                0x00003848
.define MCS_CH0_IRQ_FORCINT           0x0000384C
.define MCS_CH0_IRQ_MODE              0x00003850
.define MCS_CH0_EIRQ_EN               0x00003854
.define MCS_REG_PROT                  0x00003860
.define MCS_CTRL_STAT                 0x00003864
.define MCS_RESET                     0x00003868
.define MCS_CAT                       0x0000386C
.define MCS_CWT                       0x00003870
.define MCS_ERR                       0x0000387C
.define MCS_CH1_R0                    0x00003880
.define MCS_CH1_R1                    0x00003884
.define MCS_CH1_R2                    0x00003888
.define MCS_CH1_R3                    0x0000388C
.define MCS_CH1_R4                    0x00003890
.define MCS_CH1_R5                    0x00003894
.define MCS_CH1_R6                    0x00003898
.define MCS_CH1_R7                    0x0000389C
.define MCS_CH1_CTRL                  0x000038A0
.define MCS_CH1_ACB                   0x000038A4
.define MCS_CH1_MHB                   0x000038BC
.define MCS_CH1_PC                    0x000038C0
.define MCS_CH1_IRQ_NOTIFY            0x000038C4
.define MCS_CH1_IRQ_EN                0x000038C8
.define MCS_CH1_IRQ_FORCINT           0x000038CC
.define MCS_CH1_IRQ_MODE              0x000038D0
.define MCS_CH1_EIRQ_EN               0x000038D4
.define MCS_CH2_R0                    0x00003900
.define MCS_CH2_R1                    0x00003904
.define MCS_CH2_R2                    0x00003908
.define MCS_CH2_R3                    0x0000390C
.define MCS_CH2_R4                    0x00003910
.define MCS_CH2_R5                    0x00003914
.define MCS_CH2_R6                    0x00003918
.define MCS_CH2_R7                    0x0000391C
.define MCS_CH2_CTRL                  0x00003920
.define MCS_CH2_ACB                   0x00003924
.define MCS_CH2_MHB                   0x0000393C
.define MCS_CH2_PC                    0x00003940
.define MCS_CH2_IRQ_NOTIFY            0x00003944
.define MCS_CH2_IRQ_EN                0x00003948
.define MCS_CH2_IRQ_FORCINT           0x0000394C
.define MCS_CH2_IRQ_MODE              0x00003950
.define MCS_CH2_EIRQ_EN               0x00003954
.define MCS_CH3_R0                    0x00003980
.define MCS_CH3_R1                    0x00003984
.define MCS_CH3_R2                    0x00003988
.define MCS_CH3_R3                    0x0000398C
.define MCS_CH3_R4                    0x00003990
.define MCS_CH3_R5                    0x00003994
.define MCS_CH3_R6                    0x00003998
.define MCS_CH3_R7                    0x0000399C
.define MCS_CH3_CTRL                  0x000039A0
.define MCS_CH3_ACB                   0x000039A4
.define MCS_CH3_MHB                   0x000039BC
.define MCS_CH3_PC                    0x000039C0
.define MCS_CH3_IRQ_NOTIFY            0x000039C4
.define MCS_CH3_IRQ_EN                0x000039C8
.define MCS_CH3_IRQ_FORCINT           0x000039CC
.define MCS_CH3_IRQ_MODE              0x000039D0
.define MCS_CH3_EIRQ_EN               0x000039D4
.define MCS_CH4_R0                    0x00003A00
.define MCS_CH4_R1                    0x00003A04
.define MCS_CH4_R2                    0x00003A08
.define MCS_CH4_R3                    0x00003A0C
.define MCS_CH4_R4                    0x00003A10
.define MCS_CH4_R5                    0x00003A14
.define MCS_CH4_R6                    0x00003A18
.define MCS_CH4_R7                    0x00003A1C
.define MCS_CH4_CTRL                  0x00003A20
.define MCS_CH4_ACB                   0x00003A24
.define MCS_CH4_MHB                   0x00003A3C
.define MCS_CH4_PC                    0x00003A40
.define MCS_CH4_IRQ_NOTIFY            0x00003A44
.define MCS_CH4_IRQ_EN                0x00003A48
.define MCS_CH4_IRQ_FORCINT           0x00003A4C
.define MCS_CH4_IRQ_MODE              0x00003A50
.define MCS_CH4_EIRQ_EN               0x00003A54
.define MCS_CH5_R0                    0x00003A80
.define MCS_CH5_R1                    0x00003A84
.define MCS_CH5_R2                    0x00003A88
.define MCS_CH5_R3                    0x00003A8C
.define MCS_CH5_R4                    0x00003A90
.define MCS_CH5_R5                    0x00003A94
.define MCS_CH5_R6                    0x00003A98
.define MCS_CH5_R7                    0x00003A9C
.define MCS_CH5_CTRL                  0x00003AA0
.define MCS_CH5_ACB                   0x00003AA4
.define MCS_CH5_MHB                   0x00003ABC
.define MCS_CH5_PC                    0x00003AC0
.define MCS_CH5_IRQ_NOTIFY            0x00003AC4
.define MCS_CH5_IRQ_EN                0x00003AC8
.define MCS_CH5_IRQ_FORCINT           0x00003ACC
.define MCS_CH5_IRQ_MODE              0x00003AD0
.define MCS_CH5_EIRQ_EN               0x00003AD4
.define MCS_CH6_R0                    0x00003B00
.define MCS_CH6_R1                    0x00003B04
.define MCS_CH6_R2                    0x00003B08
.define MCS_CH6_R3                    0x00003B0C
.define MCS_CH6_R4                    0x00003B10
.define MCS_CH6_R5                    0x00003B14
.define MCS_CH6_R6                    0x00003B18
.define MCS_CH6_R7                    0x00003B1C
.define MCS_CH6_CTRL                  0x00003B20
.define MCS_CH6_ACB                   0x00003B24
.define MCS_CH6_MHB                   0x00003B3C
.define MCS_CH6_PC                    0x00003B40
.define MCS_CH6_IRQ_NOTIFY            0x00003B44
.define MCS_CH6_IRQ_EN                0x00003B48
.define MCS_CH6_IRQ_FORCINT           0x00003B4C
.define MCS_CH6_IRQ_MODE              0x00003B50
.define MCS_CH6_EIRQ_EN               0x00003B54
.define MCS_CH7_R0                    0x00003B80
.define MCS_CH7_R1                    0x00003B84
.define MCS_CH7_R2                    0x00003B88
.define MCS_CH7_R3                    0x00003B8C
.define MCS_CH7_R4                    0x00003B90
.define MCS_CH7_R5                    0x00003B94
.define MCS_CH7_R6                    0x00003B98
.define MCS_CH7_R7                    0x00003B9C
.define MCS_CH7_CTRL                  0x00003BA0
.define MCS_CH7_ACB                   0x00003BA4
.define MCS_CH7_MHB                   0x00003BBC
.define MCS_CH7_PC                    0x00003BC0
.define MCS_CH7_IRQ_NOTIFY            0x00003BC4
.define MCS_CH7_IRQ_EN                0x00003BC8
.define MCS_CH7_IRQ_FORCINT           0x00003BCC
.define MCS_CH7_IRQ_MODE              0x00003BD0
.define MCS_CH7_EIRQ_EN               0x00003BD4
.define SPE_CTRL_STAT                 0x00005000
.define SPE_PAT                       0x00005004
.define SPE_OUT_PAT0                  0x00005008
.define SPE_OUT_PAT1                  0x0000500C
.define SPE_OUT_PAT2                  0x00005010
.define SPE_OUT_PAT3                  0x00005014
.define SPE_OUT_PAT4                  0x00005018
.define SPE_OUT_PAT5                  0x0000501C
.define SPE_OUT_PAT6                  0x00005020
.define SPE_OUT_PAT7                  0x00005024
.define SPE_OUT_CTRL                  0x00005028
.define SPE_IRQ_NOTIFY                0x0000502C
.define SPE_IRQ_EN                    0x00005030
.define SPE_IRQ_FORCINT               0x00005034
.define SPE_IRQ_MODE                  0x00005038
.define SPE_EIRQ_EN                   0x0000503C
.define SPE_REV_CNT                   0x00005040
.define SPE_REV_CMP                   0x00005044
.define SPE_CTRL_STAT2                0x00005048
.define SPE_CMD                       0x0000504C
.define CCM_ARP0_CTRL                 0x00005200
.define CCM_ARP0_PROT                 0x00005204
.define CCM_ARP1_CTRL                 0x00005208
.define CCM_ARP1_PROT                 0x0000520C
.define CCM_ARP2_CTRL                 0x00005210
.define CCM_ARP2_PROT                 0x00005214
.define CCM_ARP3_CTRL                 0x00005218
.define CCM_ARP3_PROT                 0x0000521C
.define CCM_ARP4_CTRL                 0x00005220
.define CCM_ARP4_PROT                 0x00005224
.define CCM_ARP5_CTRL                 0x00005228
.define CCM_ARP5_PROT                 0x0000522C
.define CCM_ARP6_CTRL                 0x00005230
.define CCM_ARP6_PROT                 0x00005234
.define CCM_ARP7_CTRL                 0x00005238
.define CCM_ARP7_PROT                 0x0000523C
.define CCM_ARP8_CTRL                 0x00005240
.define CCM_ARP8_PROT                 0x00005244
.define CCM_ARP9_CTRL                 0x00005248
.define CCM_ARP9_PROT                 0x0000524C
.define CCM_AEIM_STA                  0x000053D8
.define CCM_HW_CONF                   0x000053DC
.define CCM_TIM_AUX_IN_SRC            0x000053E0
.define CCM_EXT_CAP_EN                0x000053E4
.define CCM_TOM_OUT                   0x000053E8
.define CCM_ATOM_OUT                  0x000053EC
.define CCM_CMU_CLK_CFG               0x000053F0
.define CCM_CMU_FXCLK_CFG             0x000053F4
.define CCM_CFG                       0x000053F8
.define CCM_PROT                      0x000053FC
.define ADC_CH0_DATA                   0x00005400
.define ADC_CH0_STA                    0x00005404
.define ADC_CH1_DATA                   0x00005408
.define ADC_CH1_STA                    0x0000540C
.define ADC_CH2_DATA                   0x00005410
.define ADC_CH2_STA                    0x00005414
.define ADC_CH3_DATA                   0x00005418
.define ADC_CH3_STA                    0x0000541C
.define ADC_CH4_DATA                   0x00005420
.define ADC_CH4_STA                    0x00005424
.define ADC_CH5_DATA                   0x00005428
.define ADC_CH5_STA                    0x0000542C
.define ADC_CH6_DATA                   0x00005430
.define ADC_CH6_STA                    0x00005434
.define ADC_CH7_DATA                   0x00005438
.define ADC_CH7_STA                    0x0000543C
.define ADC_CH8_DATA                   0x00005440
.define ADC_CH8_STA                    0x00005444
.define ADC_CH9_DATA                   0x00005448
.define ADC_CH9_STA                    0x0000544C
.define ADC_CH10_DATA                  0x00005450
.define ADC_CH10_STA                   0x00005454
.define ADC_CH11_DATA                  0x00005458
.define ADC_CH11_STA                   0x0000545C
.define ADC_CH12_DATA                  0x00005460
.define ADC_CH12_STA                   0x00005464
.define ADC_CH13_DATA                  0x00005468
.define ADC_CH13_STA                   0x0000546C
.define ADC_CH14_DATA                  0x00005470
.define ADC_CH14_STA                   0x00005474
.define ADC_CH15_DATA                  0x00005478
.define ADC_CH15_STA                   0x0000547C
.define ADC_CH16_DATA                  0x00005480
.define ADC_CH16_STA                   0x00005484
.define ADC_CH17_DATA                  0x00005488
.define ADC_CH17_STA                   0x0000548C
.define ADC_CH18_DATA                  0x00005490
.define ADC_CH18_STA                   0x00005494
.define ADC_CH19_DATA                  0x00005498
.define ADC_CH19_STA                   0x0000549C
.define ADC_CH20_DATA                  0x000054A0
.define ADC_CH20_STA                   0x000054A4
.define ADC_CH21_DATA                  0x000054A8
.define ADC_CH21_STA                   0x000054AC
.define ADC_CH22_DATA                  0x000054B0
.define ADC_CH22_STA                   0x000054B4
.define ADC_CH23_DATA                  0x000054B8
.define ADC_CH23_STA                   0x000054BC
.define ADC_CH24_DATA                  0x000054C0
.define ADC_CH24_STA                   0x000054C4
.define ADC_CH25_DATA                  0x000054C8
.define ADC_CH25_STA                   0x000054CC
.define ADC_CH26_DATA                  0x000054D0
.define ADC_CH26_STA                   0x000054D4
.define ADC_CH27_DATA                  0x000054D8
.define ADC_CH27_STA                   0x000054DC
.define ADC_CH28_DATA                  0x000054E0
.define ADC_CH28_STA                   0x000054E4
.define ADC_CH29_DATA                  0x000054E8
.define ADC_CH29_STA                   0x000054EC
.define ADC_CH30_DATA                  0x000054F0
.define ADC_CH30_STA                   0x000054F4
.define ADC_CH31_DATA                  0x000054F8
.define ADC_CH31_STA                   0x000054FC
.define ADC_CH32_DATA                  0x00005500
.define ADC_CH32_STA                   0x00005504
.define ADC_CH33_DATA                  0x00005508
.define ADC_CH33_STA                   0x0000550C
.define ADC_CH34_DATA                  0x00005510
.define ADC_CH34_STA                   0x00005514
.define ADC_CH35_DATA                  0x00005518
.define ADC_CH35_STA                   0x0000551C
.define ADC_CH36_DATA                  0x00005520
.define ADC_CH36_STA                   0x00005524
.define ADC_CH37_DATA                  0x00005528
.define ADC_CH37_STA                   0x0000552C
.define ADC_CH38_DATA                  0x00005530
.define ADC_CH38_STA                   0x00005534
.define ADC_CH39_DATA                  0x00005538
.define ADC_CH39_STA                   0x0000553C
.define ADC_CH40_DATA                  0x00005540
.define ADC_CH40_STA                   0x00005544
.define ADC_CH41_DATA                  0x00005548
.define ADC_CH41_STA                   0x0000554C
.define ADC_CH42_DATA                  0x00005550
.define ADC_CH42_STA                   0x00005554
.define ADC_CH43_DATA                  0x00005558
.define ADC_CH43_STA                   0x0000555C
.define ADC_CH44_DATA                  0x00005560
.define ADC_CH44_STA                   0x00005564
.define ADC_CH45_DATA                  0x00005568
.define ADC_CH45_STA                   0x0000556C
.define ADC_CH46_DATA                  0x00005570
.define ADC_CH46_STA                   0x00005574
.define ADC_CH47_DATA                  0x00005578
.define ADC_CH47_STA                   0x0000557C
.define ADC_CH48_DATA                  0x00005580
.define ADC_CH48_STA                   0x00005584
.define ADC_CH49_DATA                  0x00005588
.define ADC_CH49_STA                   0x0000558C
.define ADC_CH50_DATA                  0x00005590
.define ADC_CH50_STA                   0x00005594
.define ADC_CH51_DATA                  0x00005598
.define ADC_CH51_STA                   0x0000559C
.define ADC_CH52_DATA                  0x000055A0
.define ADC_CH52_STA                   0x000055A4
.define ADC_CH53_DATA                  0x000055A8
.define ADC_CH53_STA                   0x000055AC
.define ADC_CH54_DATA                  0x000055B0
.define ADC_CH54_STA                   0x000055B4
.define ADC_CH55_DATA                  0x000055B8
.define ADC_CH55_STA                   0x000055BC
.define ADC_CH56_DATA                  0x000055C0
.define ADC_CH56_STA                   0x000055C4
.define ADC_CH57_DATA                  0x000055C8
.define ADC_CH57_STA                   0x000055CC
.define ADC_CH58_DATA                  0x000055D0
.define ADC_CH58_STA                   0x000055D4
.define ADC_CH59_DATA                  0x000055D8
.define ADC_CH59_STA                   0x000055DC
.define ADC_CH60_DATA                  0x000055E0
.define ADC_CH60_STA                   0x000055E4
.define ADC_CH61_DATA                  0x000055E8
.define ADC_CH61_STA                   0x000055EC
.define ADC_CH62_DATA                  0x000055F0
.define ADC_CH62_STA                   0x000055F4
.define ADC_CH63_DATA                  0x000055F8
.define ADC_CH63_STA                   0x000055FC
.define F2A_CH0_ARU_RD_FIFO           0x00005800
.define F2A_CH1_ARU_RD_FIFO           0x00005804
.define F2A_CH2_ARU_RD_FIFO           0x00005808
.define F2A_CH3_ARU_RD_FIFO           0x0000580C
.define F2A_CH4_ARU_RD_FIFO           0x00005810
.define F2A_CH5_ARU_RD_FIFO           0x00005814
.define F2A_CH6_ARU_RD_FIFO           0x00005818
.define F2A_CH7_ARU_RD_FIFO           0x0000581C
.define F2A_CH0_STR_CFG               0x00005820
.define F2A_CH1_STR_CFG               0x00005824
.define F2A_CH2_STR_CFG               0x00005828
.define F2A_CH3_STR_CFG               0x0000582C
.define F2A_CH4_STR_CFG               0x00005830
.define F2A_CH5_STR_CFG               0x00005834
.define F2A_CH6_STR_CFG               0x00005838
.define F2A_CH7_STR_CFG               0x0000583C
.define F2A_ENABLE                    0x00005840
.define F2A_CTRL                      0x00005844
.define AFD_CH0_BUF_ACC               0x00005880
.define AFD_CH1_BUF_ACC               0x00005890
.define AFD_CH2_BUF_ACC               0x000058A0
.define AFD_CH3_BUF_ACC               0x000058B0
.define AFD_CH4_BUF_ACC               0x000058C0
.define AFD_CH5_BUF_ACC               0x000058D0
.define AFD_CH6_BUF_ACC               0x000058E0
.define AFD_CH7_BUF_ACC               0x000058F0
.define FIFO_CH0_CTRL                 0x00005C00
.define FIFO_CH0_END_ADDR             0x00005C04
.define FIFO_CH0_START_ADDR           0x00005C08
.define FIFO_CH0_UPPER_WM             0x00005C0C
.define FIFO_CH0_LOWER_WM             0x00005C10
.define FIFO_CH0_STATUS               0x00005C14
.define FIFO_CH0_FILL_LEVEL           0x00005C18
.define FIFO_CH0_WR_PTR               0x00005C1C
.define FIFO_CH0_RD_PTR               0x00005C20
.define FIFO_CH0_IRQ_NOTIFY           0x00005C24
.define FIFO_CH0_IRQ_EN               0x00005C28
.define FIFO_CH0_IRQ_FORCINT          0x00005C2C
.define FIFO_CH0_IRQ_MODE             0x00005C30
.define FIFO_CH0_EIRQ_EN              0x00005C34
.define FIFO_CH1_CTRL                 0x00005C40
.define FIFO_CH1_END_ADDR             0x00005C44
.define FIFO_CH1_START_ADDR           0x00005C48
.define FIFO_CH1_UPPER_WM             0x00005C4C
.define FIFO_CH1_LOWER_WM             0x00005C50
.define FIFO_CH1_STATUS               0x00005C54
.define FIFO_CH1_FILL_LEVEL           0x00005C58
.define FIFO_CH1_WR_PTR               0x00005C5C
.define FIFO_CH1_RD_PTR               0x00005C60
.define FIFO_CH1_IRQ_NOTIFY           0x00005C64
.define FIFO_CH1_IRQ_EN               0x00005C68
.define FIFO_CH1_IRQ_FORCINT          0x00005C6C
.define FIFO_CH1_IRQ_MODE             0x00005C70
.define FIFO_CH1_EIRQ_EN              0x00005C74
.define FIFO_CH2_CTRL                 0x00005C80
.define FIFO_CH2_END_ADDR             0x00005C84
.define FIFO_CH2_START_ADDR           0x00005C88
.define FIFO_CH2_UPPER_WM             0x00005C8C
.define FIFO_CH2_LOWER_WM             0x00005C90
.define FIFO_CH2_STATUS               0x00005C94
.define FIFO_CH2_FILL_LEVEL           0x00005C98
.define FIFO_CH2_WR_PTR               0x00005C9C
.define FIFO_CH2_RD_PTR               0x00005CA0
.define FIFO_CH2_IRQ_NOTIFY           0x00005CA4
.define FIFO_CH2_IRQ_EN               0x00005CA8
.define FIFO_CH2_IRQ_FORCINT          0x00005CAC
.define FIFO_CH2_IRQ_MODE             0x00005CB0
.define FIFO_CH2_EIRQ_EN              0x00005CB4
.define FIFO_CH3_CTRL                 0x00005CC0
.define FIFO_CH3_END_ADDR             0x00005CC4
.define FIFO_CH3_START_ADDR           0x00005CC8
.define FIFO_CH3_UPPER_WM             0x00005CCC
.define FIFO_CH3_LOWER_WM             0x00005CD0
.define FIFO_CH3_STATUS               0x00005CD4
.define FIFO_CH3_FILL_LEVEL           0x00005CD8
.define FIFO_CH3_WR_PTR               0x00005CDC
.define FIFO_CH3_RD_PTR               0x00005CE0
.define FIFO_CH3_IRQ_NOTIFY           0x00005CE4
.define FIFO_CH3_IRQ_EN               0x00005CE8
.define FIFO_CH3_IRQ_FORCINT          0x00005CEC
.define FIFO_CH3_IRQ_MODE             0x00005CF0
.define FIFO_CH3_EIRQ_EN              0x00005CF4
.define FIFO_CH4_CTRL                 0x00005D00
.define FIFO_CH4_END_ADDR             0x00005D04
.define FIFO_CH4_START_ADDR           0x00005D08
.define FIFO_CH4_UPPER_WM             0x00005D0C
.define FIFO_CH4_LOWER_WM             0x00005D10
.define FIFO_CH4_STATUS               0x00005D14
.define FIFO_CH4_FILL_LEVEL           0x00005D18
.define FIFO_CH4_WR_PTR               0x00005D1C
.define FIFO_CH4_RD_PTR               0x00005D20
.define FIFO_CH4_IRQ_NOTIFY           0x00005D24
.define FIFO_CH4_IRQ_EN               0x00005D28
.define FIFO_CH4_IRQ_FORCINT          0x00005D2C
.define FIFO_CH4_IRQ_MODE             0x00005D30
.define FIFO_CH4_EIRQ_EN              0x00005D34
.define FIFO_CH5_CTRL                 0x00005D40
.define FIFO_CH5_END_ADDR             0x00005D44
.define FIFO_CH5_START_ADDR           0x00005D48
.define FIFO_CH5_UPPER_WM             0x00005D4C
.define FIFO_CH5_LOWER_WM             0x00005D50
.define FIFO_CH5_STATUS               0x00005D54
.define FIFO_CH5_FILL_LEVEL           0x00005D58
.define FIFO_CH5_WR_PTR               0x00005D5C
.define FIFO_CH5_RD_PTR               0x00005D60
.define FIFO_CH5_IRQ_NOTIFY           0x00005D64
.define FIFO_CH5_IRQ_EN               0x00005D68
.define FIFO_CH5_IRQ_FORCINT          0x00005D6C
.define FIFO_CH5_IRQ_MODE             0x00005D70
.define FIFO_CH5_EIRQ_EN              0x00005D74
.define FIFO_CH6_CTRL                 0x00005D80
.define FIFO_CH6_END_ADDR             0x00005D84
.define FIFO_CH6_START_ADDR           0x00005D88
.define FIFO_CH6_UPPER_WM             0x00005D8C
.define FIFO_CH6_LOWER_WM             0x00005D90
.define FIFO_CH6_STATUS               0x00005D94
.define FIFO_CH6_FILL_LEVEL           0x00005D98
.define FIFO_CH6_WR_PTR               0x00005D9C
.define FIFO_CH6_RD_PTR               0x00005DA0
.define FIFO_CH6_IRQ_NOTIFY           0x00005DA4
.define FIFO_CH6_IRQ_EN               0x00005DA8
.define FIFO_CH6_IRQ_FORCINT          0x00005DAC
.define FIFO_CH6_IRQ_MODE             0x00005DB0
.define FIFO_CH6_EIRQ_EN              0x00005DB4
.define FIFO_CH7_CTRL                 0x00005DC0
.define FIFO_CH7_END_ADDR             0x00005DC4
.define FIFO_CH7_START_ADDR           0x00005DC8
.define FIFO_CH7_UPPER_WM             0x00005DCC
.define FIFO_CH7_LOWER_WM             0x00005DD0
.define FIFO_CH7_STATUS               0x00005DD4
.define FIFO_CH7_FILL_LEVEL           0x00005DD8
.define FIFO_CH7_WR_PTR               0x00005DDC
.define FIFO_CH7_RD_PTR               0x00005DE0
.define FIFO_CH7_IRQ_NOTIFY           0x00005DE4
.define FIFO_CH7_IRQ_EN               0x00005DE8
.define FIFO_CH7_IRQ_FORCINT          0x00005DEC
.define FIFO_CH7_IRQ_MODE             0x00005DF0
.define FIFO_CH7_EIRQ_EN              0x00005DF4
.define CMU_CLK_EN                     0x00007000
.define CMU_GCLK_NUM                   0x00007004
.define CMU_GCLK_DEN                   0x00007008
.define CMU_CLK_0_CTRL                 0x0000700C
.define CMU_CLK_1_CTRL                 0x00007010
.define CMU_CLK_2_CTRL                 0x00007014
.define CMU_CLK_3_CTRL                 0x00007018
.define CMU_CLK_4_CTRL                 0x0000701C
.define CMU_CLK_5_CTRL                 0x00007020
.define CMU_CLK_6_CTRL                 0x00007024
.define CMU_CLK_7_CTRL                 0x00007028
.define CMU_ECLK_0_NUM                 0x0000702C
.define CMU_ECLK_0_DEN                 0x00007030
.define CMU_ECLK_1_NUM                 0x00007034
.define CMU_ECLK_1_DEN                 0x00007038
.define CMU_ECLK_2_NUM                 0x0000703C
.define CMU_ECLK_2_DEN                 0x00007040
.define CMU_FXCLK_CTRL                 0x00007044
.define CMU_GLB_CTRL                   0x00007048
.define CMU_CLK_CTRL                   0x0000704C
.define TBU_CHEN                       0x00007080
.define TBU_CH0_CTRL                   0x00007084
.define TBU_CH0_BASE                   0x00007088
.define TBU_CH1_CTRL                   0x0000708C
.define TBU_CH1_BASE                   0x00007090
.define TBU_CH2_CTRL                   0x00007094
.define TBU_CH2_BASE                   0x00007098
.define TBU_CH3_CTRL                   0x0000709C
.define TBU_CH3_BASE                   0x000070A0
.define TBU_CH3_BASE_MARK              0x000070A4
.define TBU_CH3_BASE_CAPTURE           0x000070A8
.define BRC_SRC_0_ADDR                 0x00007100
.define BRC_SRC_0_DEST                 0x00007104
.define BRC_SRC_1_ADDR                 0x00007108
.define BRC_SRC_1_DEST                 0x0000710C
.define BRC_SRC_2_ADDR                 0x00007110
.define BRC_SRC_2_DEST                 0x00007114
.define BRC_SRC_3_ADDR                 0x00007118
.define BRC_SRC_3_DEST                 0x0000711C
.define BRC_SRC_4_ADDR                 0x00007120
.define BRC_SRC_4_DEST                 0x00007124
.define BRC_SRC_5_ADDR                 0x00007128
.define BRC_SRC_5_DEST                 0x0000712C
.define BRC_SRC_6_ADDR                 0x00007130
.define BRC_SRC_6_DEST                 0x00007134
.define BRC_SRC_7_ADDR                 0x00007138
.define BRC_SRC_7_DEST                 0x0000713C
.define BRC_SRC_8_ADDR                 0x00007140
.define BRC_SRC_8_DEST                 0x00007144
.define BRC_SRC_9_ADDR                 0x00007148
.define BRC_SRC_9_DEST                 0x0000714C
.define BRC_SRC_10_ADDR                0x00007150
.define BRC_SRC_10_DEST                0x00007154
.define BRC_SRC_11_ADDR                0x00007158
.define BRC_SRC_11_DEST                0x0000715C
.define BRC_IRQ_NOTIFY                 0x00007160
.define BRC_IRQ_EN                     0x00007164
.define BRC_IRQ_FORCINT                0x00007168
.define BRC_IRQ_MODE                   0x0000716C
.define BRC_RST                        0x00007170
.define BRC_EIRQ_EN                    0x00007174
.define ARU_ACCESS                     0x00007180
.define ARU_DATA_H                     0x00007184
.define ARU_DATA_L                     0x00007188
.define ARU_DBG_ACCESS0                0x0000718C
.define ARU_DBG_DATA0_H                0x00007190
.define ARU_DBG_DATA0_L                0x00007194
.define ARU_DBG_ACCESS1                0x00007198
.define ARU_DBG_DATA1_H                0x0000719C
.define ARU_DBG_DATA1_L                0x000071A0
.define ARU_IRQ_NOTIFY                 0x000071A4
.define ARU_IRQ_EN                     0x000071A8
.define ARU_IRQ_FORCINT                0x000071AC
.define ARU_IRQ_MODE                   0x000071B0
.define ARU_CADDR_END                  0x000071B4
.define ARU_CTRL                       0x000071BC
.define ARU_0_DYN_CTRL                 0x000071C0
.define ARU_1_DYN_CTRL                 0x000071C4
.define ARU_0_DYN_ROUTE_LOW            0x000071C8
.define ARU_1_DYN_ROUTE_LOW            0x000071CC
.define ARU_0_DYN_ROUTE_HIGH           0x000071D0
.define ARU_1_DYN_ROUTE_HIGH           0x000071D4
.define ARU_0_DYN_ROUTE_SR_LOW         0x000071D8
.define ARU_1_DYN_ROUTE_SR_LOW         0x000071DC
.define ARU_0_DYN_ROUTE_SR_HIGH        0x000071E0
.define ARU_1_DYN_ROUTE_SR_HIGH        0x000071E4
.define ARU_0_DYN_RDADDR               0x000071E8
.define ARU_1_DYN_RDADDR               0x000071EC
.define ARU_CADDR                      0x000071FC
.define MCFG_CTRL                      0x00007200
.define MAP_CTRL                       0x00007240
.define MON_STATUS                     0x00007280
.define MON_ACTIVITY_0                 0x00007284
.define MON_ACTIVITY_1                 0x00007288
.define MON_ACTIVITY_MCS0              0x0000728C
.define MON_ACTIVITY_MCS1              0x00007290
.define MON_ACTIVITY_MCS2              0x00007294
.define MON_ACTIVITY_MCS3              0x00007298
.define MON_ACTIVITY_MCS4              0x0000729C
.define MON_ACTIVITY_MCS5              0x000072A0
.define MON_ACTIVITY_MCS6              0x000072A4
.define MON_ACTIVITY_MCS7              0x000072A8
.define MON_ACTIVITY_MCS8              0x000072AC
.define MON_ACTIVITY_MCS9              0x000072B0
.define CMP_EN                         0x000072C0
.define CMP_IRQ_NOTIFY                 0x000072C4
.define CMP_IRQ_EN                     0x000072C8
.define CMP_IRQ_FORCINT                0x000072CC
.define CMP_IRQ_MODE                   0x000072D0
.define CMP_EIRQ_EN                    0x000072D4
.define GTM_REV                        0x00007300
.define GTM_RST                        0x00007304
.define GTM_CTRL                       0x00007308
.define GTM_AEI_ADDR_XPT               0x0000730C
.define GTM_IRQ_NOTIFY                 0x00007310
.define GTM_IRQ_EN                     0x00007314
.define GTM_IRQ_FORCINT                0x00007318
.define GTM_IRQ_MODE                   0x0000731C
.define GTM_EIRQ_EN                    0x00007320
.define GTM_HW_CONF                    0x00007324
.define GTM_CFG                        0x00007328
.define GTM_AEI_STA_XPT                0x0000732C
.define GTM_TIM0_AUX_IN_SRC            0x00007340
.define GTM_TIM1_AUX_IN_SRC            0x00007344
.define GTM_TIM2_AUX_IN_SRC            0x00007348
.define GTM_TIM3_AUX_IN_SRC            0x0000734C
.define GTM_TIM4_AUX_IN_SRC            0x00007350
.define GTM_TIM5_AUX_IN_SRC            0x00007354
.define GTM_TIM6_AUX_IN_SRC            0x00007358
.define GTM_EXT_CAP_EN_0               0x0000735C
.define GTM_EXT_CAP_EN_1               0x00007360
.define GTM_EXT_CAP_EN_2               0x00007364
.define GTM_EXT_CAP_EN_3               0x00007368
.define GTM_EXT_CAP_EN_4               0x0000736C
.define GTM_EXT_CAP_EN_5               0x00007370
.define GTM_EXT_CAP_EN_6               0x00007374
.define GTM_EXT_CAP_EN_7               0x00007378
.define GTM_TOM0_OUT                   0x00007380
.define GTM_TOM1_OUT                   0x00007384
.define GTM_TOM2_OUT                   0x00007388
.define GTM_TOM3_OUT                   0x0000738C
.define GTM_TOM4_OUT                   0x00007390
.define GTM_TOM5_OUT                   0x00007394
.define GTM_ATOM0_OUT                  0x00007398
.define GTM_ATOM2_OUT                  0x0000739C
.define GTM_ATOM4_OUT                  0x000073A0
.define GTM_ATOM6_OUT                  0x000073A4
.define GTM_ATOM8_OUT                  0x000073A8
.define GTM_ATOM10_OUT                 0x000073AC
.define GTM_CLS_CLK_CFG                0x000073B0
.define MCS2DPLL_DEB0                  0x00007800
.define MCS2DPLL_DEB1                  0x00007804
.define MCS2DPLL_DEB2                  0x00007808
.define MCS2DPLL_DEB3                  0x0000780C
.define MCS2DPLL_DEB4                  0x00007810
.define MCS2DPLL_DEB5                  0x00007814
.define MCS2DPLL_DEB6                  0x00007818
.define MCS2DPLL_DEB7                  0x0000781C
.define MCS2DPLL_DEB8                  0x00007820
.define MCS2DPLL_DEB9                  0x00007824
.define MCS2DPLL_DEB10                 0x00007828
.define MCS2DPLL_DEB11                 0x0000782C
.define MCS2DPLL_DEB12                 0x00007830
.define MCS2DPLL_DEB13                 0x00007834
.define MCS2DPLL_DEB14                 0x00007838
.define MCS2DPLL_DEB15                 0x0000783C
.define DPLL_CTRL_0                    0x00008000
.define DPLL_CTRL_1                    0x00008004
.define DPLL_CTRL_2                    0x00008008
.define DPLL_CTRL_3                    0x0000800C
.define DPLL_CTRL_4                    0x00008010
.define DPLL_CTRL_5                    0x00008014
.define DPLL_ACT_STA                   0x00008018
.define DPLL_OSW                       0x0000801C
.define DPLL_AOSV_2                    0x00008020
.define DPLL_APT                       0x00008024
.define DPLL_APS                       0x00008028
.define DPLL_APT_2C                    0x0000802C
.define DPLL_APS_1C3                   0x00008030
.define DPLL_NUTC                      0x00008034
.define DPLL_NUSC                      0x00008038
.define DPLL_NTI_CNT                   0x0000803C
.define DPLL_IRQ_NOTIFY                0x00008040
.define DPLL_IRQ_EN                    0x00008044
.define DPLL_IRQ_FORCINT               0x00008048
.define DPLL_IRQ_MODE                  0x0000804C
.define DPLL_EIRQ_EN                   0x00008050
.define DPLL_INC_CNT1                  0x000080B0
.define DPLL_INC_CNT2                  0x000080B4
.define DPLL_APT_SYNC                  0x000080B8
.define DPLL_APS_SYNC                  0x000080BC
.define DPLL_TBU_TS0_T                 0x000080C0
.define DPLL_TBU_TS0_S                 0x000080C4
.define DPLL_ADD_IN_LD1                0x000080C8
.define DPLL_ADD_IN_LD2                0x000080CC
.define DPLL_STATUS                    0x000080FC
.define DPLL_ID_PMTR_0                 0x00008100
.define DPLL_ID_PMTR_1                 0x00008104
.define DPLL_ID_PMTR_2                 0x00008108
.define DPLL_ID_PMTR_3                 0x0000810C
.define DPLL_ID_PMTR_4                 0x00008110
.define DPLL_ID_PMTR_5                 0x00008114
.define DPLL_ID_PMTR_6                 0x00008118
.define DPLL_ID_PMTR_7                 0x0000811C
.define DPLL_ID_PMTR_8                 0x00008120
.define DPLL_ID_PMTR_9                 0x00008124
.define DPLL_ID_PMTR_10                0x00008128
.define DPLL_ID_PMTR_11                0x0000812C
.define DPLL_ID_PMTR_12                0x00008130
.define DPLL_ID_PMTR_13                0x00008134
.define DPLL_ID_PMTR_14                0x00008138
.define DPLL_ID_PMTR_15                0x0000813C
.define DPLL_ID_PMTR_16                0x00008140
.define DPLL_ID_PMTR_17                0x00008144
.define DPLL_ID_PMTR_18                0x00008148
.define DPLL_ID_PMTR_19                0x0000814C
.define DPLL_ID_PMTR_20                0x00008150
.define DPLL_ID_PMTR_21                0x00008154
.define DPLL_ID_PMTR_22                0x00008158
.define DPLL_ID_PMTR_23                0x0000815C
.define DPLL_ID_PMTR_24                0x00008160
.define DPLL_ID_PMTR_25                0x00008164
.define DPLL_ID_PMTR_26                0x00008168
.define DPLL_ID_PMTR_27                0x0000816C
.define DPLL_ID_PMTR_28                0x00008170
.define DPLL_ID_PMTR_29                0x00008174
.define DPLL_ID_PMTR_30                0x00008178
.define DPLL_ID_PMTR_31                0x0000817C
.define DPLL_CTRL_0_SHADOW_TRIGGER     0x000081E0
.define DPLL_CTRL_0_SHADOW_STATE       0x000081E4
.define DPLL_CTRL_1_SHADOW_TRIGGER     0x000081E8
.define DPLL_CTRL_1_SHADOW_STATE       0x000081EC
.define DPLL_RAM_INI                   0x000081FC
.define DPLL_RR1A                      0x00008200
.define DPLL_RR1A_END                  0x000083FC
.define DPLL_TS_T                      0x00008400
.define DPLL_TS_T_OLD                  0x00008404
.define DPLL_FTV_T                     0x00008408
.define DPLL_TS_S                      0x00008410
.define DPLL_TS_S_OLD                  0x00008414
.define DPLL_FTV_S                     0x00008418
.define DPLL_THMI                      0x00008420
.define DPLL_THMA                      0x00008424
.define DPLL_THVAL                     0x00008428
.define DPLL_TOV                       0x00008430
.define DPLL_TOV_S                     0x00008434
.define DPLL_ADD_IN_CAL1               0x00008438
.define DPLL_ADD_IN_CAL2               0x0000843C
.define DPLL_MPVAL1                    0x00008440
.define DPLL_MPVAL2                    0x00008444
.define DPLL_NMB_T_TAR                 0x00008448
.define DPLL_NMB_T_TAR_OLD             0x0000844C
.define DPLL_NMB_S_TAR                 0x00008450
.define DPLL_NMB_S_TAR_OLD             0x00008454
.define DPLL_RCDT_TX                   0x00008460
.define DPLL_RCDT_SX                   0x00008464
.define DPLL_RCDT_TX_NOM               0x00008468
.define DPLL_RCDT_SX_NOM               0x0000846C
.define DPLL_RDT_T_ACT                 0x00008470
.define DPLL_RDT_S_ACT                 0x00008474
.define DPLL_DT_T_ACT                  0x00008478
.define DPLL_DT_S_ACT                  0x0000847C
.define DPLL_EDT_T                     0x00008480
.define DPLL_MEDT_T                    0x00008484
.define DPLL_EDT_S                     0x00008488
.define DPLL_MEDT_S                    0x0000848C
.define DPLL_CDT_TX                    0x00008490
.define DPLL_CDT_SX                    0x00008494
.define DPLL_CDT_TX_NOM                0x00008498
.define DPLL_CDT_SX_NOM                0x0000849C
.define DPLL_TLR                       0x000084A0
.define DPLL_SLR                       0x000084A4
.define DPLL_PDT_0                     0x00008500
.define DPLL_PDT_1                     0x00008504
.define DPLL_PDT_2                     0x00008508
.define DPLL_PDT_3                     0x0000850C
.define DPLL_PDT_4                     0x00008510
.define DPLL_PDT_5                     0x00008514
.define DPLL_PDT_6                     0x00008518
.define DPLL_PDT_7                     0x0000851C
.define DPLL_PDT_8                     0x00008520
.define DPLL_PDT_9                     0x00008524
.define DPLL_PDT_10                    0x00008528
.define DPLL_PDT_11                    0x0000852C
.define DPLL_PDT_12                    0x00008530
.define DPLL_PDT_13                    0x00008534
.define DPLL_PDT_14                    0x00008538
.define DPLL_PDT_15                    0x0000853C
.define DPLL_PDT_16                    0x00008540
.define DPLL_PDT_17                    0x00008544
.define DPLL_PDT_18                    0x00008548
.define DPLL_PDT_19                    0x0000854C
.define DPLL_PDT_20                    0x00008550
.define DPLL_PDT_21                    0x00008554
.define DPLL_PDT_22                    0x00008558
.define DPLL_PDT_23                    0x0000855C
.define DPLL_PDT_24                    0x00008560
.define DPLL_PDT_25                    0x00008564
.define DPLL_PDT_26                    0x00008568
.define DPLL_PDT_27                    0x0000856C
.define DPLL_PDT_28                    0x00008570
.define DPLL_PDT_29                    0x00008574
.define DPLL_PDT_30                    0x00008578
.define DPLL_PDT_31                    0x0000857C
.define DPLL_MLS1                      0x000085C0
.define DPLL_MLS2                      0x000085C4
.define DPLL_CNT_NUM_1                 0x000085C8
.define DPLL_CNT_NUM_2                 0x000085CC
.define DPLL_PVT                       0x000085D0
.define DPLL_PSTC                      0x000085E0
.define DPLL_PSSC                      0x000085E4
.define DPLL_PSTM                      0x000085E8
.define DPLL_PSTM_OLD                  0x000085EC
.define DPLL_PSSM                      0x000085F0
.define DPLL_PSSM_OLD                  0x000085F4
.define DPLL_NMB_T                     0x000085F8
.define DPLL_NMB_S                     0x000085FC
.define DPLL_RDT_S0                    0x00008600
.define DPLL_RDT_S1                    0x00008604
.define DPLL_RDT_S2                    0x00008608
.define DPLL_RDT_S3                    0x0000860C
.define DPLL_RDT_S4                    0x00008610
.define DPLL_RDT_S5                    0x00008614
.define DPLL_RDT_S6                    0x00008618
.define DPLL_RDT_S7                    0x0000861C
.define DPLL_RDT_S8                    0x00008620
.define DPLL_RDT_S9                    0x00008624
.define DPLL_RDT_S10                   0x00008628
.define DPLL_RDT_S11                   0x0000862C
.define DPLL_RDT_S12                   0x00008630
.define DPLL_RDT_S13                   0x00008634
.define DPLL_RDT_S14                   0x00008638
.define DPLL_RDT_S15                   0x0000863C
.define DPLL_RDT_S16                   0x00008640
.define DPLL_RDT_S17                   0x00008644
.define DPLL_RDT_S18                   0x00008648
.define DPLL_RDT_S19                   0x0000864C
.define DPLL_RDT_S20                   0x00008650
.define DPLL_RDT_S21                   0x00008654
.define DPLL_RDT_S22                   0x00008658
.define DPLL_RDT_S23                   0x0000865C
.define DPLL_RDT_S24                   0x00008660
.define DPLL_RDT_S25                   0x00008664
.define DPLL_RDT_S26                   0x00008668
.define DPLL_RDT_S27                   0x0000866C
.define DPLL_RDT_S28                   0x00008670
.define DPLL_RDT_S29                   0x00008674
.define DPLL_RDT_S30                   0x00008678
.define DPLL_RDT_S31                   0x0000867C
.define DPLL_RDT_S32                   0x00008680
.define DPLL_RDT_S33                   0x00008684
.define DPLL_RDT_S34                   0x00008688
.define DPLL_RDT_S35                   0x0000868C
.define DPLL_RDT_S36                   0x00008690
.define DPLL_RDT_S37                   0x00008694
.define DPLL_RDT_S38                   0x00008698
.define DPLL_RDT_S39                   0x0000869C
.define DPLL_RDT_S40                   0x000086A0
.define DPLL_RDT_S41                   0x000086A4
.define DPLL_RDT_S42                   0x000086A8
.define DPLL_RDT_S43                   0x000086AC
.define DPLL_RDT_S44                   0x000086B0
.define DPLL_RDT_S45                   0x000086B4
.define DPLL_RDT_S46                   0x000086B8
.define DPLL_RDT_S47                   0x000086BC
.define DPLL_RDT_S48                   0x000086C0
.define DPLL_RDT_S49                   0x000086C4
.define DPLL_RDT_S50                   0x000086C8
.define DPLL_RDT_S51                   0x000086CC
.define DPLL_RDT_S52                   0x000086D0
.define DPLL_RDT_S53                   0x000086D4
.define DPLL_RDT_S54                   0x000086D8
.define DPLL_RDT_S55                   0x000086DC
.define DPLL_RDT_S56                   0x000086E0
.define DPLL_RDT_S57                   0x000086E4
.define DPLL_RDT_S58                   0x000086E8
.define DPLL_RDT_S59                   0x000086EC
.define DPLL_RDT_S60                   0x000086F0
.define DPLL_RDT_S61                   0x000086F4
.define DPLL_RDT_S62                   0x000086F8
.define DPLL_RDT_S63                   0x000086FC
.define DPLL_TSF_S0                    0x00008700
.define DPLL_TSF_S1                    0x00008704
.define DPLL_TSF_S2                    0x00008708
.define DPLL_TSF_S3                    0x0000870C
.define DPLL_TSF_S4                    0x00008710
.define DPLL_TSF_S5                    0x00008714
.define DPLL_TSF_S6                    0x00008718
.define DPLL_TSF_S7                    0x0000871C
.define DPLL_TSF_S8                    0x00008720
.define DPLL_TSF_S9                    0x00008724
.define DPLL_TSF_S10                   0x00008728
.define DPLL_TSF_S11                   0x0000872C
.define DPLL_TSF_S12                   0x00008730
.define DPLL_TSF_S13                   0x00008734
.define DPLL_TSF_S14                   0x00008738
.define DPLL_TSF_S15                   0x0000873C
.define DPLL_TSF_S16                   0x00008740
.define DPLL_TSF_S17                   0x00008744
.define DPLL_TSF_S18                   0x00008748
.define DPLL_TSF_S19                   0x0000874C
.define DPLL_TSF_S20                   0x00008750
.define DPLL_TSF_S21                   0x00008754
.define DPLL_TSF_S22                   0x00008758
.define DPLL_TSF_S23                   0x0000875C
.define DPLL_TSF_S24                   0x00008760
.define DPLL_TSF_S25                   0x00008764
.define DPLL_TSF_S26                   0x00008768
.define DPLL_TSF_S27                   0x0000876C
.define DPLL_TSF_S28                   0x00008770
.define DPLL_TSF_S29                   0x00008774
.define DPLL_TSF_S30                   0x00008778
.define DPLL_TSF_S31                   0x0000877C
.define DPLL_TSF_S32                   0x00008780
.define DPLL_TSF_S33                   0x00008784
.define DPLL_TSF_S34                   0x00008788
.define DPLL_TSF_S35                   0x0000878C
.define DPLL_TSF_S36                   0x00008790
.define DPLL_TSF_S37                   0x00008794
.define DPLL_TSF_S38                   0x00008798
.define DPLL_TSF_S39                   0x0000879C
.define DPLL_TSF_S40                   0x000087A0
.define DPLL_TSF_S41                   0x000087A4
.define DPLL_TSF_S42                   0x000087A8
.define DPLL_TSF_S43                   0x000087AC
.define DPLL_TSF_S44                   0x000087B0
.define DPLL_TSF_S45                   0x000087B4
.define DPLL_TSF_S46                   0x000087B8
.define DPLL_TSF_S47                   0x000087BC
.define DPLL_TSF_S48                   0x000087C0
.define DPLL_TSF_S49                   0x000087C4
.define DPLL_TSF_S50                   0x000087C8
.define DPLL_TSF_S51                   0x000087CC
.define DPLL_TSF_S52                   0x000087D0
.define DPLL_TSF_S53                   0x000087D4
.define DPLL_TSF_S54                   0x000087D8
.define DPLL_TSF_S55                   0x000087DC
.define DPLL_TSF_S56                   0x000087E0
.define DPLL_TSF_S57                   0x000087E4
.define DPLL_TSF_S58                   0x000087E8
.define DPLL_TSF_S59                   0x000087EC
.define DPLL_TSF_S60                   0x000087F0
.define DPLL_TSF_S61                   0x000087F4
.define DPLL_TSF_S62                   0x000087F8
.define DPLL_TSF_S63                   0x000087FC
.define DPLL_ADT_S0                    0x00008800
.define DPLL_ADT_S1                    0x00008804
.define DPLL_ADT_S2                    0x00008808
.define DPLL_ADT_S3                    0x0000880C
.define DPLL_ADT_S4                    0x00008810
.define DPLL_ADT_S5                    0x00008814
.define DPLL_ADT_S6                    0x00008818
.define DPLL_ADT_S7                    0x0000881C
.define DPLL_ADT_S8                    0x00008820
.define DPLL_ADT_S9                    0x00008824
.define DPLL_ADT_S10                   0x00008828
.define DPLL_ADT_S11                   0x0000882C
.define DPLL_ADT_S12                   0x00008830
.define DPLL_ADT_S13                   0x00008834
.define DPLL_ADT_S14                   0x00008838
.define DPLL_ADT_S15                   0x0000883C
.define DPLL_ADT_S16                   0x00008840
.define DPLL_ADT_S17                   0x00008844
.define DPLL_ADT_S18                   0x00008848
.define DPLL_ADT_S19                   0x0000884C
.define DPLL_ADT_S20                   0x00008850
.define DPLL_ADT_S21                   0x00008854
.define DPLL_ADT_S22                   0x00008858
.define DPLL_ADT_S23                   0x0000885C
.define DPLL_ADT_S24                   0x00008860
.define DPLL_ADT_S25                   0x00008864
.define DPLL_ADT_S26                   0x00008868
.define DPLL_ADT_S27                   0x0000886C
.define DPLL_ADT_S28                   0x00008870
.define DPLL_ADT_S29                   0x00008874
.define DPLL_ADT_S30                   0x00008878
.define DPLL_ADT_S31                   0x0000887C
.define DPLL_ADT_S32                   0x00008880
.define DPLL_ADT_S33                   0x00008884
.define DPLL_ADT_S34                   0x00008888
.define DPLL_ADT_S35                   0x0000888C
.define DPLL_ADT_S36                   0x00008890
.define DPLL_ADT_S37                   0x00008894
.define DPLL_ADT_S38                   0x00008898
.define DPLL_ADT_S39                   0x0000889C
.define DPLL_ADT_S40                   0x000088A0
.define DPLL_ADT_S41                   0x000088A4
.define DPLL_ADT_S42                   0x000088A8
.define DPLL_ADT_S43                   0x000088AC
.define DPLL_ADT_S44                   0x000088B0
.define DPLL_ADT_S45                   0x000088B4
.define DPLL_ADT_S46                   0x000088B8
.define DPLL_ADT_S47                   0x000088BC
.define DPLL_ADT_S48                   0x000088C0
.define DPLL_ADT_S49                   0x000088C4
.define DPLL_ADT_S50                   0x000088C8
.define DPLL_ADT_S51                   0x000088CC
.define DPLL_ADT_S52                   0x000088D0
.define DPLL_ADT_S53                   0x000088D4
.define DPLL_ADT_S54                   0x000088D8
.define DPLL_ADT_S55                   0x000088DC
.define DPLL_ADT_S56                   0x000088E0
.define DPLL_ADT_S57                   0x000088E4
.define DPLL_ADT_S58                   0x000088E8
.define DPLL_ADT_S59                   0x000088EC
.define DPLL_ADT_S60                   0x000088F0
.define DPLL_ADT_S61                   0x000088F4
.define DPLL_ADT_S62                   0x000088F8
.define DPLL_ADT_S63                   0x000088FC
.define DPLL_DT_S0                     0x00008900
.define DPLL_DT_S1                     0x00008904
.define DPLL_DT_S2                     0x00008908
.define DPLL_DT_S3                     0x0000890C
.define DPLL_DT_S4                     0x00008910
.define DPLL_DT_S5                     0x00008914
.define DPLL_DT_S6                     0x00008918
.define DPLL_DT_S7                     0x0000891C
.define DPLL_DT_S8                     0x00008920
.define DPLL_DT_S9                     0x00008924
.define DPLL_DT_S10                    0x00008928
.define DPLL_DT_S11                    0x0000892C
.define DPLL_DT_S12                    0x00008930
.define DPLL_DT_S13                    0x00008934
.define DPLL_DT_S14                    0x00008938
.define DPLL_DT_S15                    0x0000893C
.define DPLL_DT_S16                    0x00008940
.define DPLL_DT_S17                    0x00008944
.define DPLL_DT_S18                    0x00008948
.define DPLL_DT_S19                    0x0000894C
.define DPLL_DT_S20                    0x00008950
.define DPLL_DT_S21                    0x00008954
.define DPLL_DT_S22                    0x00008958
.define DPLL_DT_S23                    0x0000895C
.define DPLL_DT_S24                    0x00008960
.define DPLL_DT_S25                    0x00008964
.define DPLL_DT_S26                    0x00008968
.define DPLL_DT_S27                    0x0000896C
.define DPLL_DT_S28                    0x00008970
.define DPLL_DT_S29                    0x00008974
.define DPLL_DT_S30                    0x00008978
.define DPLL_DT_S31                    0x0000897C
.define DPLL_DT_S32                    0x00008980
.define DPLL_DT_S33                    0x00008984
.define DPLL_DT_S34                    0x00008988
.define DPLL_DT_S35                    0x0000898C
.define DPLL_DT_S36                    0x00008990
.define DPLL_DT_S37                    0x00008994
.define DPLL_DT_S38                    0x00008998
.define DPLL_DT_S39                    0x0000899C
.define DPLL_DT_S40                    0x000089A0
.define DPLL_DT_S41                    0x000089A4
.define DPLL_DT_S42                    0x000089A8
.define DPLL_DT_S43                    0x000089AC
.define DPLL_DT_S44                    0x000089B0
.define DPLL_DT_S45                    0x000089B4
.define DPLL_DT_S46                    0x000089B8
.define DPLL_DT_S47                    0x000089BC
.define DPLL_DT_S48                    0x000089C0
.define DPLL_DT_S49                    0x000089C4
.define DPLL_DT_S50                    0x000089C8
.define DPLL_DT_S51                    0x000089CC
.define DPLL_DT_S52                    0x000089D0
.define DPLL_DT_S53                    0x000089D4
.define DPLL_DT_S54                    0x000089D8
.define DPLL_DT_S55                    0x000089DC
.define DPLL_DT_S56                    0x000089E0
.define DPLL_DT_S57                    0x000089E4
.define DPLL_DT_S58                    0x000089E8
.define DPLL_DT_S59                    0x000089EC
.define DPLL_DT_S60                    0x000089F0
.define DPLL_DT_S61                    0x000089F4
.define DPLL_DT_S62                    0x000089F8
.define DPLL_DT_S63                    0x000089FC
.define DPLL_TSAC0                     0x00008E00
.define DPLL_TSAC1                     0x00008E04
.define DPLL_TSAC2                     0x00008E08
.define DPLL_TSAC3                     0x00008E0C
.define DPLL_TSAC4                     0x00008E10
.define DPLL_TSAC5                     0x00008E14
.define DPLL_TSAC6                     0x00008E18
.define DPLL_TSAC7                     0x00008E1C
.define DPLL_TSAC8                     0x00008E20
.define DPLL_TSAC9                     0x00008E24
.define DPLL_TSAC10                    0x00008E28
.define DPLL_TSAC11                    0x00008E2C
.define DPLL_TSAC12                    0x00008E30
.define DPLL_TSAC13                    0x00008E34
.define DPLL_TSAC14                    0x00008E38
.define DPLL_TSAC15                    0x00008E3C
.define DPLL_TSAC16                    0x00008E40
.define DPLL_TSAC17                    0x00008E44
.define DPLL_TSAC18                    0x00008E48
.define DPLL_TSAC19                    0x00008E4C
.define DPLL_TSAC20                    0x00008E50
.define DPLL_TSAC21                    0x00008E54
.define DPLL_TSAC22                    0x00008E58
.define DPLL_TSAC23                    0x00008E5C
.define DPLL_TSAC24                    0x00008E60
.define DPLL_TSAC25                    0x00008E64
.define DPLL_TSAC26                    0x00008E68
.define DPLL_TSAC27                    0x00008E6C
.define DPLL_TSAC28                    0x00008E70
.define DPLL_TSAC29                    0x00008E74
.define DPLL_TSAC30                    0x00008E78
.define DPLL_TSAC31                    0x00008E7C
.define DPLL_PSAC0                     0x00008E80
.define DPLL_PSAC1                     0x00008E84
.define DPLL_PSAC2                     0x00008E88
.define DPLL_PSAC3                     0x00008E8C
.define DPLL_PSAC4                     0x00008E90
.define DPLL_PSAC5                     0x00008E94
.define DPLL_PSAC6                     0x00008E98
.define DPLL_PSAC7                     0x00008E9C
.define DPLL_PSAC8                     0x00008EA0
.define DPLL_PSAC9                     0x00008EA4
.define DPLL_PSAC10                    0x00008EA8
.define DPLL_PSAC11                    0x00008EAC
.define DPLL_PSAC12                    0x00008EB0
.define DPLL_PSAC13                    0x00008EB4
.define DPLL_PSAC14                    0x00008EB8
.define DPLL_PSAC15                    0x00008EBC
.define DPLL_PSAC16                    0x00008EC0
.define DPLL_PSAC17                    0x00008EC4
.define DPLL_PSAC18                    0x00008EC8
.define DPLL_PSAC19                    0x00008ECC
.define DPLL_PSAC20                    0x00008ED0
.define DPLL_PSAC21                    0x00008ED4
.define DPLL_PSAC22                    0x00008ED8
.define DPLL_PSAC23                    0x00008EDC
.define DPLL_PSAC24                    0x00008EE0
.define DPLL_PSAC25                    0x00008EE4
.define DPLL_PSAC26                    0x00008EE8
.define DPLL_PSAC27                    0x00008EEC
.define DPLL_PSAC28                    0x00008EF0
.define DPLL_PSAC29                    0x00008EF4
.define DPLL_PSAC30                    0x00008EF8
.define DPLL_PSAC31                    0x00008EFC
.define DPLL_ACB_0                     0x00008F00
.define DPLL_ACB_1                     0x00008F04
.define DPLL_ACB_2                     0x00008F08
.define DPLL_ACB_3                     0x00008F0C
.define DPLL_ACB_4                     0x00008F10
.define DPLL_ACB_5                     0x00008F14
.define DPLL_ACB_6                     0x00008F18
.define DPLL_ACB_7                     0x00008F1C
.define DPLL_CTRL_11                   0x00008F20
.define DPLL_THVAL2                    0x00008F24
.define DPLL_TIDEL                     0x00008F28
.define DPLL_SIDEL                     0x00008F2C
.define DPLL_APS_SYNC_EXT              0x00008F30
.define DPLL_CTRL_EXT                  0x00008F34
.define DPLL_APS_EXT                   0x00008F38
.define DPLL_APS_1C3_EXT               0x00008F3C
.define DPLL_STA                       0x00008F40
.define DPLL_INCF1_OFFSET              0x00008F44
.define DPLL_INCF2_OFFSET              0x00008F48
.define DPLL_DT_T_START                0x00008F4C
.define DPLL_DT_S_START                0x00008F50
.define DPLL_STA_MASK                  0x00008F54
.define DPLL_STA_FLAG                  0x00008F58
.define DPLL_INC_CNT1_MASK             0x00008F5C
.define DPLL_INC_CNT2_MASK             0x00008F60
.define DPLL_NUSC_EXT1                 0x00008F64
.define DPLL_NUSC_EXT2                 0x00008F68
.define DPLL_CTN_MIN                   0x00008F6C
.define DPLL_CTN_MAX                   0x00008F70
.define DPLL_CSN_MIN                   0x00008F74
.define DPLL_CSN_MAX                   0x00008F78
.define DPLL_RR2                       0x0000C000
.define DPLL_RR2_END                   0x0000FFFC
