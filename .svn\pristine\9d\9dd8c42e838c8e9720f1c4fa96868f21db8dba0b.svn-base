/*
 * File: CombCtrl_data.c
 *
 * Code generated for Simulink model 'CombCtrl'.
 *
 * Model version                  : 1.1908
 * Simulink Coder version         : 8.13 (R2017b) 24-Jul-2017
 * C/C++ source code generated on : Tue Oct 16 15:23:00 2018
 *
 * Target selection: ert.tlc
 * Embedded hardware selection: Specified
 * Code generation objectives:
 *    1. Execution efficiency
 *    2. Safety precaution
 *    3. MISRA C:2012 guidelines
 *    4. RAM efficiency
 *    5. ROM efficiency
 *    6. Traceability
 *    7. Debugging
 * Validation result: Passed (27), Warnings (5), Error (0)
 */

#include "CombCtrl.h"
#include "CombCtrl_private.h"

/* Invariant block signals (auto storage) */
const ConstBlockIO_CombCtrl CombCtrl_ConstB = {
  0U,                                  /* '<S12>/Gateway In3' */
  0U,                                  /* '<S12>/Gateway In2' */
  0,                                   /* '<S12>/Gateway In1' */
  0U                                   /* '<S12>/Gateway In5' */
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
