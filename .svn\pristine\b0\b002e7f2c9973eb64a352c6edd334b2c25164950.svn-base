/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Get.h
 **  Date:          13-Jul-2022
 **
 **  Model Version: 1.469
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Get_h_
#define RTW_HEADER_TLE9278BQX_Get_h_
#ifndef TLE9278BQX_Get_COMMON_INCLUDES_
# define TLE9278BQX_Get_COMMON_INCLUDES_
#include "rtwtypes.h"
#endif                                 /* TLE9278BQX_Get_COMMON_INCLUDES_ */


/* Includes for objects with custom storage classes. */
#include "TLE9278BQX_Get_out.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */

/* Block signals and states (default storage) for system '<Root>' */
typedef struct {
  uint16_T data;                       /* '<S5>/Drive_Status_Messages' */
  uint8_T wk;                          /* '<S50>/GetData' */
  uint8_T wk_d;                        /* '<S44>/GetData' */
  uint8_T wk_a;                        /* '<S43>/GetData' */
  uint8_T wk_i;                        /* '<S62>/GetData' */
  uint8_T wk_n;                        /* '<S59>/GetData' */
  uint8_T wk_dz;                       /* '<S61>/GetData' */
  uint8_T wk_al;                       /* '<S42>/GetData' */
  uint8_T wk_m;                        /* '<S45>/GetData' */
  uint8_T wk_d5;                       /* '<S54>/GetData' */
  uint8_T wk_h;                        /* '<S51>/GetData' */
  uint8_T wk_c;                        /* '<S52>/GetData' */
  uint8_T wk_f;                        /* '<S53>/GetData' */
  uint8_T wk_co;                       /* '<S48>/GetData1' */
  uint8_T wk_o;                        /* '<S41>/GetData1' */
  uint8_T wk_cu;                       /* '<S40>/GetData' */
  uint8_T wk_mr;                       /* '<S58>/GetData' */
  uint8_T wk_cl;                       /* '<S57>/GetData' */
  uint8_T wk_b;                        /* '<S39>/GetData' */
  uint8_T wk_b5;                       /* '<S56>/GetData' */
  uint8_T wk_a0;                       /* '<S47>/GetData' */
  uint8_T wk_mx;                       /* '<S49>/GetData' */
} DW_TLE9278BQX_Get_T;

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/

/* Block signals and states (default storage) */
extern DW_TLE9278BQX_Get_T TLE9278BQX_Get_DW;

/* Model entry point functions */
extern void TLE9278BQX_Get_initialize(void);

/* Exported entry point function */
extern void TLE9278BQX_Get_Bkg(void);

/* Exported entry point function */
extern void TLE9278BQX_Get_Init(void);

/* Exported data declaration */

/*Memory section for Output interface*/
/* Declaration for custom storage class: ELD_OUT_INTERFACE */
extern uint8_T SBCAdc;                 /* '<S38>/GetData' */

/* ADC_STAT: ADC */
extern uint8_T SBCBoostDOL;            /* '<S50>/GetData' */

/* SMPS_STAT: BST_OP */
extern uint8_T SBCBoostDSC;            /* '<S50>/GetData' */

/* SMPS_STAT: BST_SH */
extern uint8_T SBCBoostOT;             /* '<S54>/GetData' */

/* THERM_STAT: BOOST_OT */
extern uint8_T SBCBoostSOL;            /* '<S50>/GetData' */

/* SMPS_STAT: BCK_OP */
extern uint8_T SBCBoostSSC;            /* '<S50>/GetData' */

/* SMPS_STAT: BCK_SH */
extern uint8_T SBCCan0Fail;            /* '<S42>/GetData' */

/* BUS_STAT_0: CAN_0_FAIL */
extern uint8_T SBCCan1;                /* '<S40>/GetData' */

/* BUS_CTRL_2: CAN1 */
extern uint8_T SBCCan1Fail;            /* '<S43>/GetData' */

/* BUS_STAT_2: CAN_1_FAIL */
extern uint8_T SBCCan2Fail;            /* '<S43>/GetData' */

/* BUS_STAT_2: CAN_2_FAIL */
extern uint8_T SBCFOOn;                /* '<S45>/GetData' */

/* DEV_STAT: FO_ON_STATE */
extern uint8_T SBCFOSwOn;              /* '<S47>/GetData' */

/* HW_CTRL_0: FO_ON */
extern uint8_T SBCFam;                 /* '<S46>/GetData' */

/* FAM_PROD_STAT: FAM */
extern uint16_T SBCIndexReset;         /* '<S36>/idCase' */

/* Data reset status index */
extern uint8_T SBCProd;                /* '<S46>/GetData' */

/* FAM_PROD_STAT: PROD */
extern uint8_T SBCSpiFail;             /* '<S45>/GetData' */

/* DEV_STAT: SPI_FAIL */
extern uint8_T SBCSysStat;             /* '<S53>/GetData' */

/* SYS_STATUS_CTRL: SYS_STAT */
extern uint8_T SBCTpw;                 /* '<S54>/GetData' */

/* THERM_STAT: TPW */
extern uint8_T SBCTsd1;                /* '<S54>/GetData' */

/* THERM_STAT: TSD1 */
extern uint8_T SBCTsd2;                /* '<S54>/GetData' */

/* THERM_STAT: TSD2 */
extern uint8_T SBCVBSensUV;            /* '<S62>/GetData' */

/* WK_STAT_2: VBAT_UV_LATCH */
extern uint8_T SBCVBatt;               /* '<S38>/GetData' */

/* SBC Vbattery */
extern uint8_T SBCVCanUV;              /* '<S42>/GetData' */

/* BUS_STAT_0: VCAN_UV */
extern uint8_T SBCVWk;                 /* '<S38>/GetData' */

/* SBC VWake */
extern uint8_T SBCVccOT;               /* '<S54>/GetData' */

/* THERM_STAT: VCC1_OT */
extern uint8_T SBCVioOV;               /* '<S52>/GetData' */

/* SUP_STAT_1: VIO_OV */
extern uint8_T SBCVioSC;               /* '<S51>/GetData' */

/* SUP_STAT_0: VIO_SC */
extern uint8_T SBCVioUV;               /* '<S51>/GetData' */

/* SUP_STAT_0: VIO_UV */
extern uint8_T SBCVioWarn;             /* '<S52>/GetData' */

/* SUP_STAT_1: VIO_WARN */
extern uint8_T SBCWDTimer;             /* '<S56>/GetData' */

/* WD_CTRL: WD_TIMER */
extern uint8_T WkSBCBusStat0;          /* '<S42>/GetData' */

/* Wake Up feedback status at PowerOn */
extern uint8_T WkSBCBusStat2;          /* '<S43>/GetData' */

/* Wake Up feedback status at PowerOn */
extern uint8_T WkSBCSmpsStat;          /* '<S50>/GetData' */

/* Wake Up feedback status at PowerOn */
extern uint8_T WkSBCSupStat0;          /* '<S51>/GetData' */

/* Wake Up feedback status at PowerOn */
extern uint8_T WkSBCSupStat1;          /* '<S52>/GetData' */

/* Wake Up feedback status at PowerOn */
extern uint8_T WkSBCTermStat;          /* '<S54>/GetData' */

/* Wake Up feedback status at PowerOn */
extern uint8_T WkSBCWKStat2;           /* '<S62>/GetData' */

/* Wake Up feedback status at PowerOn */


/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'TLE9278BQX_Get'
 * '<S1>'   : 'TLE9278BQX_Get/Model Info'
 * '<S2>'   : 'TLE9278BQX_Get/TLE9278BQX_Get'
 * '<S3>'   : 'TLE9278BQX_Get/TLE9278BQX_Get/Merger'
 * '<S4>'   : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack'
 * '<S5>'   : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg'
 * '<S6>'   : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Init'
 * '<S7>'   : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/ADC_STAT'
 * '<S8>'   : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/BUS_CTRL_0'
 * '<S9>'   : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/BUS_CTRL_2'
 * '<S10>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/BUS_CTRL_3'
 * '<S11>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/BUS_STAT_0'
 * '<S12>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/BUS_STAT_2'
 * '<S13>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/BUS_STAT_3'
 * '<S14>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/DEV_STAT'
 * '<S15>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/FAM_PROD_STAT'
 * '<S16>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/HW_CTRL_0'
 * '<S17>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/HW_CTRL_1'
 * '<S18>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/M_S_CTRL'
 * '<S19>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/SMPS_STAT'
 * '<S20>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/SUP_STAT_0'
 * '<S21>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/SUP_STAT_1'
 * '<S22>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/SYS_STAT_CTRL'
 * '<S23>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/THERM_STAT'
 * '<S24>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/TIMER_CTRL_0'
 * '<S25>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/WD_CTRL'
 * '<S26>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/WK_CTRL_0'
 * '<S27>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/WK_CTRL_1'
 * '<S28>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/WK_LVL_STAT'
 * '<S29>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/WK_PUPD_CTRL'
 * '<S30>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/WK_STAT_0'
 * '<S31>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/Output_Stack/WK_STAT_2'
 * '<S32>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/Drive_Status_Messages'
 * '<S33>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_ResetStatus'
 * '<S34>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData'
 * '<S35>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_ResetStatus/body'
 * '<S36>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_ResetStatus/data'
 * '<S37>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_ResetStatus/fc_seq_call'
 * '<S38>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/ADC_STAT'
 * '<S39>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_CTRL_0'
 * '<S40>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_CTRL_2'
 * '<S41>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_CTRL_3'
 * '<S42>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_STAT_0'
 * '<S43>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_STAT_2'
 * '<S44>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_STAT_3'
 * '<S45>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/DEV_STAT'
 * '<S46>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/FAM_PROD_STAT'
 * '<S47>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/HW_CTRL_0'
 * '<S48>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/HW_CTRL_1'
 * '<S49>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/M_S_CTRL'
 * '<S50>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/SMPS_STAT'
 * '<S51>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/SUP_STAT_0'
 * '<S52>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/SUP_STAT_1'
 * '<S53>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/SYS_STAT_CTRL'
 * '<S54>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/THERM_STAT'
 * '<S55>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/TIMER_CTRL_0'
 * '<S56>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WD_CTRL'
 * '<S57>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_CTRL_0'
 * '<S58>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_CTRL_1'
 * '<S59>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_LVL_STAT'
 * '<S60>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_PUPD_CTRL'
 * '<S61>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_STAT_0'
 * '<S62>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_STAT_2'
 * '<S63>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/default'
 * '<S64>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/ADC_STAT/GetData'
 * '<S65>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_CTRL_0/GetData'
 * '<S66>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_CTRL_2/GetData'
 * '<S67>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_CTRL_3/GetData1'
 * '<S68>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_STAT_0/GetData'
 * '<S69>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_STAT_2/GetData'
 * '<S70>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/BUS_STAT_3/GetData'
 * '<S71>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/DEV_STAT/GetData'
 * '<S72>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/FAM_PROD_STAT/GetData'
 * '<S73>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/HW_CTRL_0/GetData'
 * '<S74>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/HW_CTRL_1/GetData1'
 * '<S75>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/M_S_CTRL/GetData'
 * '<S76>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/SMPS_STAT/GetData'
 * '<S77>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/SUP_STAT_0/GetData'
 * '<S78>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/SUP_STAT_1/GetData'
 * '<S79>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/SYS_STAT_CTRL/GetData'
 * '<S80>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/THERM_STAT/GetData'
 * '<S81>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/TIMER_CTRL_0/GetData1'
 * '<S82>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WD_CTRL/GetData'
 * '<S83>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_CTRL_0/GetData'
 * '<S84>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_CTRL_1/GetData'
 * '<S85>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_LVL_STAT/GetData'
 * '<S86>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_PUPD_CTRL/GetData'
 * '<S87>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_STAT_0/GetData'
 * '<S88>'  : 'TLE9278BQX_Get/TLE9278BQX_Get/fc_Bkg/fc_SBCGetData/WK_STAT_2/GetData'
 */

/*-
 * Requirements for '<Root>': TLE9278BQX_Get
 */
#endif                                 /* RTW_HEADER_TLE9278BQX_Get_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/