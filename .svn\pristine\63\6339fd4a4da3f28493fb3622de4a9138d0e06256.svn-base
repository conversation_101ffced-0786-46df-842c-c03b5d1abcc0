/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_map.h
 * @brief   SPC5xx GTM MAP header file.
 *
 * @addtogroup MAP
 * @{
 */

#ifndef _GTM_MAP_H_
#define _GTM_MAP_H_

#include "gtm_tag.h"
#include "gtm_cfg.h"

/*lint -e621*/

/**
 * @name    MAP definitions
 * @{
 */
#define TSEL_BIT                                 0UL
#define SSL_BIT                                  1UL
#define LSEL_BIT                                 4UL
#define TPSSO_EN_BIT                             16UL
#define TPSS0_DLD_BIT                            17UL
#define TPSS0_I0V_BIT                            20UL
#define TPSS0_I1V_BIT                            21UL
#define TPSS0_I2V_BIT                            22UL
#define TPSS1_EN_BIT                             24UL
#define TPSS1_DLD_BIT                            25UL
#define TPSS1_I0V_BIT                            28UL
#define TPSS1_I1V_BIT                            29UL
#define TPSS1_I2V_BIT                            30UL

/** MAP TRIGGER SIGNAL OUTPUT TIM0 CH0 */
#define SPC5_GTM_MAP_TRIGGER_SIGNAL_TIM0_CH0     0UL
/** MAP TRIGGER SIGNAL OUTPUT TSPP0_TSPP0 */
#define SPC5_GTM_MAP_TRIGGER_SIGNAL_TSPP0_TSPP0  1UL

/** MAP STATE SIGNAL OUTPUT TIM0 CH1 */
#define SPC5_GTM_MAP_STATE_SIGNAL_TIM0_CH1       0UL
/** MAP STATE SIGNAL OUTPUT TIM0 CH2 */
#define SPC5_GTM_MAP_STATE_SIGNAL_TIM0_CH2       1UL
/** MAP STATE SIGNAL OUTPUT TIM0 CH3 */
#define SPC5_GTM_MAP_STATE_SIGNAL_TIM0_CH3       2UL
/** MAP STATE SIGNAL OUTPUT TIM0 CH4 */
#define SPC5_GTM_MAP_STATE_SIGNAL_TIM0_CH4       3UL
/** MAP STATE SIGNAL OUTPUT TIM0 CH5 */
#define SPC5_GTM_MAP_STATE_SIGNAL_TIM0_CH5       4UL
/** MAP STATE SIGNAL OUTPUT TSPP1_TSPP0 */
#define SPC5_GTM_MAP_STATE_SIGNAL_TSPP1_TSPP0    5UL
/** @} */

/*===========================================================================*/
/* Driver data structures and types.                                         */
/*===========================================================================*/
/**
 * @brief Type of a structure representing a (GTM-IP) MAP driver.
 */
typedef struct GTM_MAPDriver GTM_MAPDriver;

/**
 * @brief   Structure representing a (GTM) MAP driver.
 */
struct GTM_MAPDriver {
  /**
   * @brief Pointer to the (GTM) MAP registers block.
   */
	volatile GTM_MAP_TAG *map;

  /**
   * @brief Pointer for application private data.
   */
    void *priv;
};

/*===========================================================================*/
/* External declarations.                                                    */
/*===========================================================================*/

#if (SPC5_GTM_USE_MAP == TRUE) && !defined(__DOXYGEN__)
extern GTM_MAPDriver MAPD1;
#endif


#ifdef __cplusplus
extern "C" {
#endif
void gtm_mapInit(void);
uint32_t gtm_mapGetCfg(GTM_MAPDriver *mapd);
void gtm_mapSetCfg(GTM_MAPDriver *mapd, uint32_t map_ctrl_reg);
#ifdef __cplusplus
}
#endif

/*lint +e621*/
#endif /* _GTM_MAP_H_ */
/** @} */
