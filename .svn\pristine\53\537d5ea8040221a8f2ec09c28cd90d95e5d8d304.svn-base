/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
#ifndef _PIT_CFG_H_
#define _PIT_CFG_H_

/*********************************************************************/
/*  PIT TIMER LDVAL- some possible configurations                    */
/*  PIT_CLK in MHz -> PIT_CLK * 1000000 / 1000 = PIT_CLK * 1000      */
/*********************************************************************/
#define PIT_MAX_TIMEOUT     ((uint32_T)(0xFFFFFFFFU))
#define PIT_100MS_TIMEBASE  ((uint32_T)(PER_CLK * 100000u) - 1u)
#define PIT_10MS_TIMEBASE   ((uint32_T)(PER_CLK * 10000u) - 1u)
#define PIT_1MS_TIMEBASE    ((uint32_T)(PER_CLK * 1000u) - 1u)
#define PIT_5MS_TIMEBASE    ((uint32_T)(PER_CLK * 5000u) - 1u)
#define PIT_10US_TIMEBASE   ((uint32_T)(PER_CLK * 10u) - 1u)
#define PIT_20US_TIMEBASE   ((uint32_T)(PER_CLK * 20u) - 1u)
#define PIT_30US_TIMEBASE   ((uint32_T)(PER_CLK * 30u) - 1u)
#define PIT_40US_TIMEBASE   ((uint32_T)(PER_CLK * 40u) - 1u)
#define PIT_50US_TIMEBASE   ((uint32_T)(PER_CLK * 50u) - 1u)
#define PIT_60US_TIMEBASE   ((uint32_T)(PER_CLK * 60u) - 1u)
#define PIT_70US_TIMEBASE   ((uint32_T)(PER_CLK * 70u) - 1u)
#define PIT_80US_TIMEBASE   ((uint32_T)(PER_CLK * 80u) - 1u)
#define PIT_90US_TIMEBASE   ((uint32_T)(PER_CLK * 90u) - 1u)
#define PIT_100US_TIMEBASE  ((uint32_T)(PER_CLK * 100u) - 1u)

/*********************************************************************/
/*  PIT TIMER TCTRL -  possible configurations                       */
/*********************************************************************/
#define PIT_COUNT_ISR 0x3u
#define PIT_COUNT     0x1u
#define PIT_STOP      0x0u

/*********************************************************************/
/*  NUMBER OF PIT MODULES IN K2 MICROCONTROLLER                      */
/*********************************************************************/
#define PIT_NUM 2u

/*********************************************************************/
/*  NUMBER OF PIT CHANNELS PER ENGINE IN K2 MICROCONTROLLER          */
/*********************************************************************/
#define PIT0_NUM_OF_CHANNELS   6u  /* PIT Engine 0 number of channels*/
#define PIT1_NUM_OF_CHANNELS   2u  /* PIT Engine 1 number of channels*/

/*********************************************************************/
/*  PIT1 CH1 FUNCTION MODE                                           */
/*********************************************************************/
#define PIT1_CH1_NORMALMODE  0u
#define PIT1_CH1_CHAINMODE   1u

/*********************************************************************/
/*PIT0 ENABLING AND CONFIGURATION                                    */
/*********************************************************************/
/* PIT0 ENABLING */
#define PIT0_ENABLE    1u

#if (PIT0_ENABLE == 1u)

/* PIT0 CHANNELS ENABLING */
#define PIT0_CH0_ENABLE 1u
#define PIT0_CH1_ENABLE 1u
#define PIT0_CH2_ENABLE 0u
#define PIT0_CH3_ENABLE 0u
#define PIT0_CH4_ENABLE 0u
#define PIT0_CH5_ENABLE 0u

/* PIT0 CHANNELS CONFIGURATIONS */
#if(PIT0_CH0_ENABLE == 1u)
#define PIT0_CHANNEL0                   0u                /*assigned channel*/
#define PIT0_TIMEOUT0                  (PIT_1MS_TIMEBASE) /*assigned timeout*/
#define PIT0_DEFAULT_START0            (PIT_STOP)
#define PIT0_FUNCINT_0                  0u
/*TIMER FOR OPERATING SYSTEM TIMEBASE*/
#define PIT0_CHANNEL_OS_TIMEBASE       (PIT0_CHANNEL0)
#define PIT0_TIMEOUT_OS_TIMEBASE       (PIT0_TIMEOUT0)
#endif

#if(PIT0_CH1_ENABLE == 1u)
#define PIT0_CHANNEL1                  1u                 /*assigned channel*/
#define PIT0_TIMEOUT1                  (PIT_MAX_TIMEOUT)  /*assigned timeout*/
#define PIT0_DEFAULT_START1            (PIT_STOP)
#define PIT0_FUNCINT_1                  1u
/*TIMER FOR ABSOLUTE SYSTEM TIMING*/
#define PIT0_CHANNEL_ABSOLUTE_TIMER    (PIT0_CHANNEL1)
#define PIT0_TIMEOUT_ABSOLUTE_TIMER    (PIT_STOP)
#endif

#if(PIT0_CH2_ENABLE == 1u)
#define PIT0_CHANNEL2                  2u                  /*assigned channel*/
#define PIT0_TIMEOUT2                  (PIT_10MS_TIMEBASE)   /*assigned timeout*/
#define PIT0_DEFAULT_START2            (PIT_STOP)
#define PIT0_FUNCINT_2                   0u
#endif

#if(PIT0_CH3_ENABLE == 1u)
#define PIT0_CHANNEL3                  3u                   /*assigned channel*/
#define PIT0_TIMEOUT3                  (PIT_MAX_TIMEOUT)    /*assigned timeout*/
#define PIT0_DEFAULT_START3            (PIT_STOP)
#define PIT0_FUNCINT_3                  0u 
#endif

#if(PIT0_CH4_ENABLE == 1u)
#define PIT0_CHANNEL4                  4u                   /*assigned channel*/
#define PIT0_TIMEOUT4                  (PIT_MAX_TIMEOUT)    /*assigned timeout*/
#define PIT0_DEFAULT_START4            (PIT_STOP)
#define PIT0_FUNCINT_4                  0u
#endif

#if(PIT0_CH5_ENABLE == 1u)
#define PIT0_CHANNEL5                  5u                   /*assigned channel*/
#define PIT0_TIMEOUT5                  (PIT_MAX_TIMEOUT)   /*assigned timeout*/
#define PIT0_DEFAULT_START5            (PIT_STOP)
#define PIT0_FUNCINT_5                  0u
#endif

#endif //PIT0_ENABLE

/*********************************************************************/
/*PIT1 ENABLING AND CONFIGURATION                                    */
/*********************************************************************/
/* PIT0 ENABLING */
#define PIT1_ENABLE     1u

#if (PIT1_ENABLE == 1u)

/* PIT1 CHANNELS ENABLING */
#define PIT1_CH0_ENABLE 1u
#define PIT1_CH1_ENABLE 1u

/* PIT0 CHANNELS CONFIGURATIONS */
#if(PIT1_CH0_ENABLE == 1u)
#define PIT1_CHANNEL0                  0u                    /*assigned channel*/
#define PIT1_TIMEOUT0                  (PIT_MAX_TIMEOUT)    /*assigned timeout*/
#define PIT1_DEFAULT_START0            (PIT_STOP)
#define PIT1_FUNCINT_0                 0u
#endif

#if(PIT1_CH1_ENABLE == 1u)
#define PIT1_CHANNEL1                  1u                    /*assigned channel*/
#define PIT1_TIMEOUT1                  (PIT_MAX_TIMEOUT)     /*assigned timeout*/
#define PIT1_DEFAULT_START1            (PIT_STOP)
#define PIT1_FUNCINT_1                 0u
#define PIT1_CHANNEL1_MODE             (PIT1_CH1_CHAINMODE)
/* CHAIN MODE */
#if (PIT1_CHANNEL1_MODE == PIT1_CH1_CHAINMODE)
#define PIT1_CHAIN_ENABLE              1u
#endif
#endif

#endif //PIT1_ENABLE


#endif	/*_PIT_CFG_H_*/
/****************************************************************************
 ****************************************************************************/

