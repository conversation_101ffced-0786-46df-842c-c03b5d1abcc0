#ifdef _BUILD_DIAGCANMGM_
#include "../include/diagcanmgm_Ferrari.h"

/* Electronic Label OEM section */
ECUcodeStrucTagID1     ECUcodeID1 =
{
    {"           "},                                // UDS 0xF180       BOOT_VERSION 11u
    {"           "},                                // UDS 0xF181       APPL_VERSION 11u
    {"           "},                                // UDS 0xF182       CALIB_VERSION 11u
    {0x01,{"         "}, 0x01,0x01,0x01},           // UDS 0xF183       Boot SW FingerPrint(Module ID + "Tester Code" + "Programming Date"), January, 1 as default month/day
    {0x01,{"         "}, 0x01,0x01,0x01},           // UDS 0xF184       Application SW FingerPrint(Module ID + "Tester Code" + "Programming Date"), January, 1 as default month/day
    {0x01,{"         "}, 0x01,0x01,0x01},           // UDS 0xF185       Calib/Application Data FingerPrint(Module ID + "Tester Code" + "Programming Date"), January, 1 as default month/day
};

/* Please refer to table 9 "Programming Status " of FCA CS.00052 */
ProgrammingStatus_T ProgrammingStatusEE =           // UDS 0x2010       Programming Status 
{
    {0xF9u},                                        // Application Status
    {0xFFu},                                        // Failure Programming 3
    {0xFFu},                                        // Failure Programming 2
    {0xFFu}                                         // Failure Programming 1
};

#endif // _BUILD_DIAGCANMGM_

