/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Cfg_private.h
 **  Date:          21-Jun-2023
 **
 **  Model Version: 1.334
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Cfg_private_h_
#define RTW_HEADER_TLE9278BQX_Cfg_private_h_
#include "rtwtypes.h"
#include "TLE9278BQX_Cfg_out.h"

/* Includes for objects with custom storage classes. */
#include "TLE9278BQX_Get_out.h"
#include "TLE9278BQX_Prs_out.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/
#ifdef __cplusplus

extern "C" {

#endif

  extern void Cfg_Return_Addr_U16_Start_wrapper(void);
  extern void Cfg_Return_Addr_U16_Outputs_wrapper(const uint16_T *in,
    uint32_T *addr);
  extern void Cfg_Return_Addr_U16_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

#ifdef __cplusplus

extern "C" {

#endif

  extern void Cfg_SkipVal_Start_wrapper(void);
  extern void Cfg_SkipVal_Outputs_wrapper(const uint16_T *SBCIndexReset,
    const uint32_T *SBCGlobalStatusRegAddr,
    uint16_T *SBCGlobalStatusReg);
  extern void Cfg_SkipVal_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

#ifdef __cplusplus

extern "C" {

#endif

  extern void Cfg_UpdateVal_Start_wrapper(void);
  extern void Cfg_UpdateVal_Outputs_wrapper(const uint16_T *SBCIndexSet,
    const uint32_T *SBCGlobalStatusRegAddr,
    const uint16_T *SBCValSet,
    uint16_T *SBCGlobalStatusReg);
  extern void Cfg_UpdateVal_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

extern void TLE9278BQX_Cfg_fc_Bkg(void);
extern void TLE9278BQX_Cfg_fc_Init_Start(void);
extern void TLE9278BQX_Cfg_fc_Init(void);
extern void TLE9278BQX_Cfg_fc_Reset(void);
extern void TLE9278BQX_Cfg_fc_Set(void);

#endif                                /* RTW_HEADER_TLE9278BQX_Cfg_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/