
Err	^
	|
	|
	|					  /
	|				     /
	|			        /	
	|                  /
	|		          / err=GN_ERR*Signal
	|                /
	|______________ /
	|err=ERR_CONST |
	|		       |
	|______________|_____________________> abs(Signal)	
			SIGNAL_ERROR_TH


***DAC1***
Signal Name: AccBodyFront
Raw Signal Name: VAccFrontBody
Gain: GNVACCFRONTBODY
Offset: OFSVACCFRONTBODY
High Admissible Value: VACCFRONTBODYHIGH
Low Admissible Value: VACCFRONTBODYLOW
ERR_CONST: 6
GN_ERR: 0.4
SIGNAL_ERROR_TH: 15


***DAC2***
Signal Name: AccBodyRear
Raw Signal Name: VAccRearBody
Gain: GNVACCREARBODY
Offset: OFSVACCREARBODY
High Admissible Value: VACCREARBODYHIGH
Low Admissible Value: VACCREARBODYLOW
ERR_CONST: 6
GN_ERR: 0.4
SIGNAL_ERROR_TH: 15

***DAC3***
Signal Name: StrokeFront
Raw Signal Name: VFrontStroke
Gain: GNVFRONTSTROKE
Offset: OFSVFRONTSTROKE
High Admissible Value: VFRONTSTROKEHIGH
Low Admissible Value: VFRONTSTROKELOW
ERR_CONST: 3.6
GN_ERR: 0.25
SIGNAL_ERROR_TH: 14

***DAC4***
Signal Name: StrokeRear
Raw Signal Name: VRearStroke
Gain: GNVREARSTROKE
Offset: OFSVREARSTROKE
High Admissible Value: VREARSTROKEHIGH
Low Admissible Value: VREARSTROKELOW
ERR_CONST: 3.6
GN_ERR: 0.25
SIGNAL_ERROR_TH: 14

***DAC5***
Signal Name:
Raw Signal Name: 
Gain: GNACCFRONTBODY
Offset: OFFACCFRONTBODY
High Admissible Value:
Low Admissible Value:
ERR_CONST: 
GN_ERR:
SIGNAL_ERROR_TH:

***DAC6***
Signal Name:
Raw Signal Name: 
Gain: GNACCFRONTBODY
Offset: OFFACCFRONTBODY
High Admissible Value:
Low Admissible Value:
ERR_CONST: 
GN_ERR:
SIGNAL_ERROR_TH:

***DAC7***
Signal Name:
Raw Signal Name: 
Gain: GNACCFRONTBODY
Offset: OFFACCFRONTBODY
High Admissible Value:
Low Admissible Value:
ERR_CONST: 
GN_ERR:
SIGNAL_ERROR_TH:

***DAC8***
Signal Name:
Raw Signal Name: 
Gain: GNACCFRONTBODY
Offset: OFFACCFRONTBODY
High Admissible Value:
Low Admissible Value:
ERR_CONST: 
GN_ERR:
SIGNAL_ERROR_TH:
