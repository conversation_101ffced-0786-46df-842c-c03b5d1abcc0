/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           TSparkCtrlAdat.c
 **  File Creation Date: 23-Jul-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TSparkCtrlAdat
 **  Model Description:  This function can work in two exclusive modes:
   1. energy saving. The primary current target is tuned at "low" value considering the spark plugs are new and then step by step, as they are aging, the current is increased in order to guarantee the respect of the TSpark. Therefore, there is an adaptive strategy, which defines the spark plug factor aging.
   2. Maximum performance. The primary current target is tuned at "high" value, as the spark plugs are old, and then the current is decreased in order to guarantee the respect of the TSpark. Therefore, there is not an adaptive strategy, as per the previous point.
   There is a minimum set of parameters that must be properly tuned to gurantee the correct behaviour of each of the two modes, as described below:
   Parameter           -----------------      value for Energy saving       ------------------ value for Maximum performance
   ENTSPARKADATMISFIRE                               1                                                               1
   ENSPARKAGINGCALC                                    1                                                               0
   ENTSPARKADATMISFOFFCALC                       1                                                               0
 **  Model Version:      1.795
 **  Model Author:       SaccoP - Mon Feb 04 13:16:50 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: PanettaM - Fri Jul 23 11:39:53 2021
 **
 **  Last Saved Modification:  PanettaM - Fri Jul 23 11:38:29 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "TSparkCtrlAdat_out.h"
#include "TSparkCtrlAdat_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define BKLOADTSPARKNOM_dim            6U                        /* Referenced by:
                                                                  * '<S3>/BKLOADTSPARKNOM_dim'
                                                                  * '<S47>/BKLOADTSPARKNOM_dim'
                                                                  * '<S38>/BKLOADTSPARKNOM_dim'
                                                                  */

/* Last index of breakpoint BKLOADTSPARKNOM */
#define BKRPMTSPARKNOM_dim             7U                        /* Referenced by:
                                                                  * '<S3>/BKRPMTSPARKNOM_dim'
                                                                  * '<S47>/BKRPMTSPARKNOM_dim'
                                                                  * '<S38>/BKRPMTSPARKNOM_dim'
                                                                  */

/* Last index of breakpoint BKRPMTSPARKNOM */
#define BKRSATOTCORR_dim               4U                        /* Referenced by: '<S3>/BKRSATOTCORR_dim' */

/* Last index of breakpoint BKRSATOTCORR */
#define BKTSPARKERRKPI_dim             4U                        /* Referenced by: '<S31>/BKTSPARKERRKPI_dim' */

/* Last index of breakpoint BKTSPARKERRKPI */
#define ID_VER_TSPARKCTRLADAT_DEF      1795U                     /* Referenced by: '<S4>/ID_VER_TSPARKCTRLADAT_DEF' */

/* ID model version define */
#define LOAD_2_PERC                    5U                        /* Referenced by: '<S16>/LOAD_2_PERC' */

/* Define to divide the load value */
#define MAX_TSPARK_ERRPI               32640                     /* Referenced by: '<S8>/MAX_TSPARK_ERRPI' */

/* Maximum TSpark PI error */
#define MIN_TSPARK_ERR                 -32767                    /* Referenced by: '<S8>/MIN_TSPARK_ERR' */

/* Minimum TSpark error */
#define MIN_TSPARK_ERRPI               -32640                    /* Referenced by: '<S8>/MIN_TSPARK_ERRPI' */

/* Minimum TSpark PI error */
#define ONE                            1U                        /* Referenced by:
                                                                  * '<S5>/TSparkCtrlAdat_T10ms_mgm'
                                                                  * '<S12>/TSpark_corr_calc'
                                                                  */

/* Define for value 1 */
#define RING_BUFFERSIZE_SBLOCK         32U                       /* Referenced by: '<S12>/TSpark_corr_calc' */

/* Single block of the ring buffer size */
#define RPM_2_PERC                     85U                       /* Referenced by: '<S17>/RPM_2_PERC' */

/* Define to divide the engine speed value */
#define SAT_SPARK_PLUG_FACT            1024U                     /* Referenced by: '<S9>/SAT_SPARK_PLUG_FACT' */

/* Saturation value for spark plug factor */
#define TWO                            2U                        /* Referenced by:
                                                                  * '<S12>/TSpark_corr_calc'
                                                                  * '<S47>/two1'
                                                                  * '<S47>/two2'
                                                                  */

/* Define for value 2 */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_TSPARKCTRLADAT_
#if (N_CYL_MAX != 8)
#error This code was generated with a different number of cylinder!
#endif

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_VAR */
static int16_T PiTSpark;               /* '<S11>/Data Type Conversion2' */
static uint8_T TSparkCtrlAdat_stabLoad_mem;/* '<S16>/Memory' */
static uint16_T TSparkCtrlAdat_stabLoad_memInt1;/* '<S24>/Memory' */
static uint16_T TSparkCtrlAdat_stabLoad_memInt2;/* '<S24>/Memory1' */
static uint8_T TSparkCtrlAdat_stabRpm_mem;/* '<S17>/Memory' */
static uint16_T TSparkCtrlAdat_stabRpm_memInt1;/* '<S25>/Memory' */
static uint16_T TSparkCtrlAdat_stabRpm_memInt2;/* '<S25>/Memory1' */
static boolean_T spark_fact_learning;  /* '<S2>/eoa_adapt_mgm' */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKLOADTSPARKNOM[7] = { 0U, 6400U,
  15360U, 25600U, 38400U, 51200U, 64000U } ;/* Referenced by:
                                             * '<S3>/BKLOADTSPARKNOM'
                                             * '<S47>/BKLOADTSPARKNOM'
                                             * '<S38>/BKLOADTSPARKNOM'
                                             */

/* Breakpoints f(Load) */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T BKRPMTSPARKNOM[8] = { 0U, 2000U, 4000U,
  6000U, 8000U, 10000U, 12000U, 14000U } ;/* Referenced by:
                                           * '<S3>/BKRPMTSPARKNOM'
                                           * '<S47>/BKRPMTSPARKNOM'
                                           * '<S38>/BKRPMTSPARKNOM'
                                           */

/* Breakpoints f(Rpm) */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKRSATOTCORR[5] = { 0, 80, 160, 320,
  480 } ;                              /* Referenced by: '<S3>/BKRSATOTCORR' */

/* Breakpoints f(SATotCyl) */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T BKTSPARKERRKPI[5] = { -16000, -8000, 0,
  8000, 16000 } ;                    /* Referenced by: '<S31>/BKTSPARKERRKPI' */

/* Breakpoints f(VtTSparkErrPi[cyl]) */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T DISCLPLASPARKCTRL = 0;
                                  /* Referenced by: '<S15>/DISCLPLASPARKCTRL' */

/* Disable plasma control condition for closed loop activation */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T DISMISFCNDSPARKCTRL = 1;
                                /* Referenced by: '<S15>/DISMISFCNDSPARKCTRL' */

/* Disable misfire condition for closed loop activation */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENFREEZEOLMISF = 0;
                                     /* Referenced by: '<S10>/ENFREEZEOLMISF' */

/* Enable the freezing of the Open loop when it's enabled only for punctual misfire detection */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENGINEATLIMITERDIS = 0;
                                 /* Referenced by: '<S15>/ENGINEATLIMITERDIS' */

/* Enable engine limiter condition for closed loop activation */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENRESCTRLIGNCUTOFF = 0;
                                  /* Referenced by: '<S5>/ENRESCTRLIGNCUTOFF' */

/* Enable the control reset when ignition is cut off. */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENSPARKAGINGCALC = 1;/* Referenced by:
                                                                 * '<S2>/ENSPARKAGINGCALC'
                                                                 * '<S12>/ENSPARKAGINGCALC'
                                                                 */

/* Enable the spark plug aging factor calculation */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENTSPARKADATMISFIRE = 1;/* Referenced by:
                                                                    * '<S4>/ENTSPARKADATMISFIRE'
                                                                    * '<S12>/ENTSPARKADATMISFIRE'
                                                                    */

/* Enable the adaptive routine for TSpark correction due to misfire events  */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T ENTSPARKADATMISFOFFCALC = 1;
                            /* Referenced by: '<S12>/ENTSPARKADATMISFOFFCALC' */

/* Enable the calculation of the offset to be applied at the Nominal TSpark */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T FLGDTCSHIFTINGDIS = 0;
                                  /* Referenced by: '<S15>/FLGDTCSHIFTINGDIS' */

/* Disable test on FlgDCTShifting */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T FLGSTABCONDCLBYP = 1;
                                   /* Referenced by: '<S15>/FLGSTABCONDCLBYP' */

/* Bypass (not consider) conditions about engine stability to enable closed loop */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T FORCEDSPARKPLUGFACTOR = 0U;
                              /* Referenced by: '<S28>/FORCEDSPARKPLUGFACTOR' */

/* Forced Spark Plug factor for Open loop VtIPriCorr calculation */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T FORCEDSPFACTORMEM = 0U;
                                  /* Referenced by: '<S47>/FORCEDSPFACTORMEM' */

/* Forced Spark Plug factor for Memorization in EE tests and validations */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T FORCESPARKPLUGFACTOR = 0;
                               /* Referenced by: '<S28>/FORCESPARKPLUGFACTOR' */

/* Enable force Spark Plug aging factor */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T FORCESPFACTORMEM = 0;
                                    /* Referenced by: '<S5>/FORCESPFACTORMEM' */

/* Enable force Spark Plug aging factor for Memorization in EE tests and validations */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T MAXTSPARKADATSMP = 65534U;
                                    /* Referenced by: '<S2>/MAXTSPARKADATSMP' */

/* Maximum number of samples to be considered for calculating SparkPlugFactor average */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T MISFEVENTSCNTTHR = 20U;
                                   /* Referenced by: '<S12>/MISFEVENTSCNTTHR' */

/* Threshold of misfire events on the observation window over that applying a TSparkNom offset */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T MISFOVERTHRDIS = 0;
                                     /* Referenced by: '<S15>/MISFOVERTHRDIS' */

/* Enable misfire over threshold condition for closed loop activation */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T MISFOVERTHRPERMEN = 0;
                                  /* Referenced by: '<S12>/MISFOVERTHRPERMEN' */

/* Maintain the flag of the misfire events over the threshold for all the trip once it's set for the first time. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SPARKPLUGRECFACTOR = 1024U;
                                 /* Referenced by: '<S28>/SPARKPLUGRECFACTOR' */

/* Spark plug factor used in open loop while one of the REC_TSPARK_CTRL_X is set */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SPARKPLUGWGTH = 512U;/* Referenced by: '<S2>/SPARKPLUGWGTH' */

/* Learnt spark plug weight factor */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBTSPARKCORR[56] = { 32768U, 32768U,
  32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U,
  32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U,
  32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U,
  32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U,
  32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U, 32768U,
  32768U, 32768U, 32768U, 32768U } ;   /* Referenced by:
                                        * '<S47>/TBTSPARKCORR'
                                        * '<S38>/TBTSPARKCORR'
                                        */

/* Coil target gain to be applied to obtain a nominal Spark duration at end of spark plugs life. */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TBTSPARKNOM[56] = { 900U, 900U, 900U,
  900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U,
  900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U,
  900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U,
  900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U, 900U,
  900U } ;                             /* Referenced by: '<S3>/TBTSPARKNOM' */

/* Table of gains for adaptive coefficients spreading */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TDCSTABLOADTSPARK = 20U;
                                  /* Referenced by: '<S16>/TDCSTABLOADTSPARK' */

/* Number of TDC to declare Load stability */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TDCSTABRPMTSPARK = 20U;
                                   /* Referenced by: '<S17>/TDCSTABRPMTSPARK' */

/* Number of TDC to declare Rpm stability */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABLOADTSPARK = 6400U;
                                  /* Referenced by: '<S16>/THRSTABLOADTSPARK' */

/* Stability range for Load */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T THRSTABRPMTSPARK = 6400U;
                                   /* Referenced by: '<S17>/THRSTABRPMTSPARK' */

/* Stability range for Rpm */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T TSPARKCTRLDESCUTOFFRST = 0;
                             /* Referenced by: '<S15>/TSPARKCTRLDESCUTOFFRST' */

/* Enable TSpark control reset when DesCutOffCAN == 1 */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T TSPARKCTRLEN = 0;/* Referenced by: '<S15>/TSPARKCTRLEN' */

/* Enable TSpark control strategy */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKCTRLLOADMAX = 12800U;/* Referenced by:
                                                                      * '<S47>/TSPARKCTRLLOADMAX'
                                                                      * '<S15>/TSPARKCTRLLOADMAX'
                                                                      */

/* TSpark control max load */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKCTRLLOADMIN = 0U;/* Referenced by:
                                                                  * '<S47>/TSPARKCTRLLOADMIN'
                                                                  * '<S15>/TSPARKCTRLLOADMIN'
                                                                  */

/* TSpark control min load */
CALQUAL_PRE CALQUAL CALQUAL_POST boolean_T TSPARKCTRLPLASMAONRST = 1;
                              /* Referenced by: '<S15>/TSPARKCTRLPLASMAONRST' */

/* Enable TSpark control reset when Plasma is actuve */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKCTRLRPMMAX = 2000U;/* Referenced by:
                                                                    * '<S47>/TSPARKCTRLRPMMAX'
                                                                    * '<S15>/TSPARKCTRLRPMMAX'
                                                                    */

/* TSpark control max Rpm */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKCTRLRPMMIN = 500U;/* Referenced by:
                                                                   * '<S47>/TSPARKCTRLRPMMIN'
                                                                   * '<S15>/TSPARKCTRLRPMMIN'
                                                                   */

/* TSpark control min Rpm */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TSPARKCTRTWATLTHR = 1440;
                                  /* Referenced by: '<S15>/TSPARKCTRTWATLTHR' */

/* Low threshold of water temperature to enable closed loop */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKERRGAIN = 1U;/* Referenced by: '<S8>/TSPARKERRGAIN' */

/* TSpark error conversion gain */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TSPARKERRNEGDB = -100;
                                      /* Referenced by: '<S8>/TSPARKERRNEGDB' */

/* TSpark control error negative dead band */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TSPARKERRPOSDB = 100;
                                      /* Referenced by: '<S8>/TSPARKERRPOSDB' */

/* TSpark control error positive dead band */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKKFILT = 1638U;/* Referenced by: '<S13>/TSPARKKFILT' */

/* TSpark filtering constant */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKNOMINCSTEP = 50U;
                                   /* Referenced by: '<S12>/TSPARKNOMINCSTEP' */

/* Step to increase offset on Nominal TSpark */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKNOMSATOFF = 1000U;
                                    /* Referenced by: '<S12>/TSPARKNOMSATOFF' */

/* Saturation value of offset on Nominal TSpark */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TSPARKPIMAXSAT = 8192;
                                     /* Referenced by: '<S11>/TSPARKPIMAXSAT' */

/* TSpark gain correction saturation max */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TSPARKPIMINSAT = 0;
                                     /* Referenced by: '<S11>/TSPARKPIMINSAT' */

/* TSpark gain correction saturation min */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T TSPARKRSTCORRRATEMAX = 32767U;/* Referenced by:
                                                                      * '<S49>/TSPARKRSTCORRRATEMAX'
                                                                      * '<S28>/TSPARKRSTCORRRATEMAX'
                                                                      */

/* Maximum variation rate for VtIPriCorr in open loop control */
CALQUAL_PRE CALQUAL CALQUAL_POST int16_T TSPARKRSTCORRRATEMIN = -32768;/* Referenced by:
                                                                      * '<S49>/TSPARKRSTCORRRATEMIN'
                                                                      * '<S28>/TSPARKRSTCORRRATEMIN'
                                                                      */

/* Minimum variation rate for VtIPriCorr in open loop control */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTPITSPARKKI[5] = { 1U, 1U, 1U, 1U, 1U
} ;                                    /* Referenced by: '<S31>/VTPITSPARKKI' */

/* PID regulator integral gain factor */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTPITSPARKKP[5] = { 2U, 2U, 2U, 2U, 2U
} ;                                    /* Referenced by: '<S31>/VTPITSPARKKP' */

/* PID regulator proportional gain factor */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T VTTSPARKNOMSACORR[5] = { 27853U,
  29491U, 31130U, 31949U, 32768U } ;
                                   /* Referenced by: '<S3>/VTTSPARKNOMSACORR' */

/* TSparkNom correction in function of SaTotCyl */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T WINSTMISFSIZE = 96U;
                                      /* Referenced by: '<S12>/WINSTMISFSIZE' */

/* Number of observations for misfire status of each cylinder */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T TSparkNomTot;                 /* '<S3>/Product' */

/* Nominal Spark duration corrected with gain output of VTTSPARKNOMSACORR  */
uint16_T VtIPriCorr[8];                /* '<S1>/Merge3' */

/* TSpark Primary current gain correction */
uint16_T VtTSparkFilt[8];              /* '<S1>/Merge8' */

/* Filtered TSpak */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint32_T IdVer_TSparkCtrlAdat;/* '<S4>/ID_VER_TSPARKCTRLADAT_DEF' */

/* ID model version */
STATIC_TEST_POINT uint16_T SparkPlugFactorCorr;/* '<S12>/TSpark_corr_calc' */

/* Correction factor on the spark plug aging calculation due to the offset on the TSparkNom for misfire. */
STATIC_TEST_POINT boolean_T StabLoadTSpark;/* '<S16>/Data Type Conversion' */

/* Load stability flag */
STATIC_TEST_POINT boolean_T StabRpmTSpark;/* '<S17>/Data Type Conversion' */

/* Rpm stability flag */
STATIC_TEST_POINT uint16_T TSparkAgedFactor;/* '<S1>/Merge14' */

/* Current coil target gain to be applied to obtain a nominal Spark duration at end of spark plugs life considering also the offset VtTSparkNomOffAdEE. */
STATIC_TEST_POINT boolean_T TSparkEngStab;/* '<S15>/Logical Operator' */

/* Engine stability conditions fullfilled */
STATIC_TEST_POINT int16_T TSparkErrPi; /* '<S8>/Data Type Conversion1' */

/* Spark duration error after dead band and TSPARKERRGAIN */
STATIC_TEST_POINT uint16_T TSparkNom;  /* '<S3>/Gain' */

/* Nominal Spark duration */
STATIC_TEST_POINT uint16_T TSparkNomTotCorr;/* '<S12>/TSpark_corr_calc' */

/* Nominal Spark duration corrected with an offset due to misfire events  */
STATIC_TEST_POINT uint32_T TbRingBuffMisfEvents[24];/* '<S1>/Merge11' */

/* Table containing for each cylinder three bitmasks of 32 elements, where each bit set corresponds to a cycle where a misfire event occurred.  */
STATIC_TEST_POINT uint8_T VtCntStMisfCycles[8];/* '<S1>/Merge10' */

/* Number of observation done on misfire status on the last window for each cylinder */
STATIC_TEST_POINT uint8_T VtCntStMisfEvents[8];/* '<S1>/Merge12' */

/* Array indicating the number of misfire events recognized for each cylinder in the last observation window */
STATIC_TEST_POINT int16_T VtPiTSpark[8];/* '<S1>/Merge2' */

/* TSpark Pi array */
STATIC_TEST_POINT uint32_T VtSparkPlugFactorAccum[8];/* '<S1>/Merge7' */

/* Accumulator for SparkPlugFactor during learning window. */
STATIC_TEST_POINT uint16_T VtSparkPlugFactorSamples[8];/* '<S1>/Merge6' */

/* Number of samples used for calculating SparkPlugFactor average */
STATIC_TEST_POINT boolean_T VtStMisfOverThr[8];/* '<S1>/Merge15' */

/* Misfire events over the threshold */
STATIC_TEST_POINT boolean_T VtTSparkCtrlEn[8];/* '<S1>/Merge5' */

/* Spark duration closed loop control active flag */
STATIC_TEST_POINT int16_T VtTSparkErr[8];/* '<S1>/Merge4' */

/* TSpark Error */
STATIC_TEST_POINT int16_T VtTSparkErrPi[8];/* '<S1>/Merge16' */

/* TSpark Error after Dead Band */
STATIC_TEST_POINT uint32_T VtTSparkFiltHR[8];/* '<S1>/Merge9' */

/* Filtered TSpak High Res */

/*End of static test points section*/

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Model step function */
void TSparkCtrlAdat_EOA(void)
{
  /* local block i/o variables */
  uint16_T rtb_SteadyStateDetect_o3;
  uint16_T rtb_SteadyStateDetect_o4;
  uint16_T rtb_SteadyStateDetect_o3_p;
  uint16_T rtb_SteadyStateDetect_o4_k;
  uint16_T rtb_LookUp_IR_U16;
  uint16_T rtb_LookUp_IR_U16_b;
  uint8_T rtb_SteadyStateDetect_o2;
  uint8_T rtb_SteadyStateDetect_o2_h;
  uint8_T TbRingBuffMisfEvents_row;
  uint32_T TbRingBuffMisfEvents_mask;
  uint8_T TbRingBuffMisfEvents_bit;
  uint32_T TbRingBuffMisfEvents_cv;
  uint16_T rtb_Sum6;
  boolean_T TSparkCtrlEn;
  int16_T rtb_FOF_Reset_S16_FXP_o1;
  int16_T rtb_Conversion2_o;
  int32_T rtb_FOF_Reset_S16_FXP_o2;
  boolean_T Selector_Outold1;
  boolean_T TSparkCtrlEn_noStMisf;
  uint16_T rtb_Conversion2;
  uint16_T rtb_Look2D_U16_U16_U16;
  enum_StMisf tmp;
  int32_T TbRingBuffMisfEvents_cv_tmp;
  boolean_T guard1 = false;

  /* RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_EOA' incorporates:
   *  SubSystem: '<Root>/eoa'
   *
   * Block description for '<Root>/eoa':
   *  Function called on the angular event: it calculates the primary
   *  current correction and spark plug aging factor in closed loop with
   *  engine stability conditions.
   */
  /* Chart: '<S2>/eoa_adapt_mgm' incorporates:
   *  SubSystem: '<S2>/TSpark_Corr'
   *
   * Block description for '<S2>/TSpark_Corr':
   *  This block corrects the nominal spark time by adding an adaptive offset (VtTSparkNomOffAdEE).
   *  This is used to compensate different coil behaviour due to working conditions and\or physical characteristic respect the reference ones, which causes misfire events for unsufficient charge time.
   *  This function can be disabled by imposing the calibration ENTSPARKADATMISFIRE at false.
   */
  /* Chart: '<S12>/TSpark_corr_calc' incorporates:
   *  Constant: '<S12>/ENSPARKAGINGCALC'
   *  Constant: '<S12>/ENTSPARKADATMISFIRE'
   *  Constant: '<S12>/ENTSPARKADATMISFOFFCALC'
   *  Constant: '<S12>/MISFOVERTHRPERMEN'
   *  Constant: '<S12>/TSPARKNOMINCSTEP'
   *  Constant: '<S12>/TSPARKNOMSATOFF'
   *  Constant: '<S12>/WINSTMISFSIZE'
   *  Constant: '<S38>/Constant'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/StMisf'
   *  Sum: '<S38>/Subtract1'
   */
  /* Gateway: eoa/eoa_adapt_mgm */
  /* During: eoa/eoa_adapt_mgm */
  /* This stateflow scheduls all the sub-functions called each 10 ms event. In detail, it calls in order:
     1. the TSpark filtering;
     2. TSpark error calculation;
     3. closed loop enabling and engine stability conditions;
     4. primary current target correction calculation (in closed loop, if enabled, or in open loop , otherwise);
     5.spark plug aging factor, if it's enabled (ENSPARKAGINGCALC at true), closed loop enabled (TSparkCtrlEn==1) and engine stability conditions are fullfilled (TSparkEngStab==1).
     The first four operations are performed by the related sub-systems, instead, the last one is done directly in this stateflow. In detail, the aging factor is calculated by the average of the last MAXTSPARKADATSMP Spark plug factor samples and, if the difference between this average and the last aging factor learnt is positive, a part of this (defined by the calibration SPARKPLUGWGTH) is added to the last spark plug factor. Therefore, the learning of the new spark plug factor is performed on blocks of MAXTSPARKADATSMP samples windows; if a window is not completed because conditions are not long satisfied, the samples acquired until that moment are discarded.  */
  /* Entry Internal: eoa/eoa_adapt_mgm */
  /* Transition: '<S14>:25' */
  /* TSparkCtrlAdat_EOA */
  /* Event: '<S14>:75' */
  /* Gateway: eoa/TSpark_Corr/TSpark_corr_calc */
  /* During: eoa/TSpark_Corr/TSpark_corr_calc */
  /* The offset is calculated when a series of MISFEVENTSCNTTHR misfire events is detected (StMisf(IonAbsTdcEOA)==MIS_FIRE)
     over the last WINSTMISFSIZE combustions of the IonAbsTdcEOA cylinder.
     The array VtCntStMisfEvents reports the number of misfire occurred during the last observation window,
     VtCntStMisfCycles indicates the current id combustion (updated circularly) and TbRingBuffMisfEvents is a matrix reporting for each column (that's related to a specific cylinder) three bitmasks of 32 elements for each row, where  bitX corresponds to the X combustion cycle and is
     set when a misfire event occurred on it.
     The relation between cycle X and bitX over the three bitmask rows for a given column cyl is:
     - TbRingBuffMisfEvents(0,cyl)=bit31,bit30,.....,bit1,bit0 for cycle X=31,30,...,1,0;
     - TbRingBuffMisfEvents(1,cyl)=bit63,bit62,.....,bit33,bit32 for cycle X=63,62,...,33,32;
     - TbRingBuffMisfEvents(2,cyl)=bit95,bit94,.....,bit65,bit64 for cycle X=95,94,...,65,64.
     When VtCntStMisfEvents(cyl)>=MISFEVENTSCNTTHR, the related flag of VtStMisfOverThr is set for just one task or for the entire driving cycle if parameter MISFOVERTHRPERMEN is set and if the parameter ENTSPARKADATMISFOFFCALC is set, the related element of VtTSparkNomOffAdEE is increased by the step
     TSPARKNOMINCSTEP and added to the nominal TSpark (TSparkNomTot) to obtain the corrected value (TSparkNomTotCorr). The counters are then all initialized to observe the misfire behaviour after the offset added and if it's not sufficient to avoid them (VtCntStMisfEvents(cyl)>=MISFEVENTSCNTTHR again) the previous offset is further increased of the same step. This process continues  as long as the saturation value TSPARKNOMSATOFF on the offset is reached.
     VtTSparkNomOffAdEE is stored in the Permanent Memory.
     Moreover, this part corrects the output of the table TBTSPARKCORR considering the adaptive offset and produces the test point TSparkAgedFactor (only if the parameter ENTSPARKADATMISFOFFCALC is set). */
  /* Entry Internal: eoa/TSpark_Corr/TSpark_corr_calc */
  /* Transition: '<S37>:2' */
  if (ENTSPARKADATMISFIRE) {
    /* Transition: '<S37>:59' */
    /* Reset the window */
    if ((VtCntStMisfCycles[(IonAbsTdcEOA)] + ((uint8_T)ONE)) > WINSTMISFSIZE) {
      /* Transition: '<S37>:4' */
      VtCntStMisfCycles[(IonAbsTdcEOA)] = ((uint8_T)ONE);
      TbRingBuffMisfEvents_row = 0U;
      TbRingBuffMisfEvents_bit = 0U;
    } else {
      /* Transition: '<S37>:5' */
      /* Increase the cycle and calculate related row and bit of the table */
      TbRingBuffMisfEvents_row = (uint8_T)(((uint32_T)VtCntStMisfCycles
        [(IonAbsTdcEOA)]) / ((uint32_T)((uint8_T)RING_BUFFERSIZE_SBLOCK)));
      TbRingBuffMisfEvents_bit = (uint8_T)(VtCntStMisfCycles[(IonAbsTdcEOA)] %
        ((uint8_T)RING_BUFFERSIZE_SBLOCK));
      VtCntStMisfCycles[(IonAbsTdcEOA)] = (uint8_T)(VtCntStMisfCycles
        [(IonAbsTdcEOA)] + ((uint8_T)ONE));
    }

    /* Transition: '<S37>:22' */
    TbRingBuffMisfEvents_mask = (uint32_T)((uint32_T)(((uint32_T)((uint8_T)ONE))
      << ((uint32_T)TbRingBuffMisfEvents_bit)));
    rtb_FOF_Reset_S16_FXP_o2 = 3 * ((int32_T)IonAbsTdcEOA);
    TbRingBuffMisfEvents_cv_tmp = rtb_FOF_Reset_S16_FXP_o2 + ((int32_T)
      TbRingBuffMisfEvents_row);
    TbRingBuffMisfEvents_cv = TbRingBuffMisfEvents[(TbRingBuffMisfEvents_cv_tmp)]
      & TbRingBuffMisfEvents_mask;
    tmp = StMisf[(IonAbsTdcEOA)];
    guard1 = false;
    if ((((uint32_T)tmp) == MIS_FIRE) || ((!ENTSPARKADATMISFOFFCALC) &&
         (((uint32_T)tmp) == NO_COMB))) {
      /* Transition: '<S37>:17' */
      TbRingBuffMisfEvents[(TbRingBuffMisfEvents_cv_tmp)] =
        TbRingBuffMisfEvents[(TbRingBuffMisfEvents_cv_tmp)] |
        TbRingBuffMisfEvents_mask;

      /* Misfire detected */
      if (TbRingBuffMisfEvents_cv == 0U) {
        /* Transition: '<S37>:29' */
        VtCntStMisfEvents[(IonAbsTdcEOA)] = (uint8_T)(VtCntStMisfEvents
          [(IonAbsTdcEOA)] + ((uint8_T)ONE));

        /* No misfire occurred at the previous window, so increase now */
        if (VtCntStMisfEvents[(IonAbsTdcEOA)] >= MISFEVENTSCNTTHR) {
          /* Transition: '<S37>:39' */
          /* Increase offset */
          VtCntStMisfCycles[(IonAbsTdcEOA)] = 0U;
          VtCntStMisfEvents[(IonAbsTdcEOA)] = 0U;
          TbRingBuffMisfEvents[(rtb_FOF_Reset_S16_FXP_o2)] = 0U;
          TbRingBuffMisfEvents[rtb_FOF_Reset_S16_FXP_o2 + 1] = 0U;
          TbRingBuffMisfEvents[rtb_FOF_Reset_S16_FXP_o2 + 2] = 0U;
          VtStMisfOverThr[(IonAbsTdcEOA)] = true;
          if (ENTSPARKADATMISFOFFCALC) {
            /* Transition: '<S37>:75' */
            rtb_FOF_Reset_S16_FXP_o2 = ((int32_T)VtTSparkNomOffAdEE
              [(IonAbsTdcEOA)]) + ((int32_T)TSPARKNOMINCSTEP);
            if (((int32_T)TSPARKNOMSATOFF) < rtb_FOF_Reset_S16_FXP_o2) {
              VtTSparkNomOffAdEE[(IonAbsTdcEOA)] = TSPARKNOMSATOFF;
            } else {
              VtTSparkNomOffAdEE[(IonAbsTdcEOA)] = (uint16_T)
                rtb_FOF_Reset_S16_FXP_o2;
            }
          } else {
            /* Transition: '<S37>:77' */
          }
        } else {
          /* Transition: '<S37>:53' */
          /* Transition: '<S37>:33' */
          guard1 = true;
        }
      } else {
        /* Transition: '<S37>:28' */
        /* Transition: '<S37>:33' */
        guard1 = true;
      }
    } else {
      /* Transition: '<S37>:13' */
      /* No misfire occurred on current window, so reset the related bit */
      TbRingBuffMisfEvents[(TbRingBuffMisfEvents_cv_tmp)] =
        TbRingBuffMisfEvents[(TbRingBuffMisfEvents_cv_tmp)] &
        (~TbRingBuffMisfEvents_mask);

      /* Misfire occurred at the previous window, but not now,
         so decrease the counter */
      if ((TbRingBuffMisfEvents_cv > 0U) && (((int32_T)VtCntStMisfEvents
            [(IonAbsTdcEOA)]) > 0)) {
        /* Transition: '<S37>:31' */
        VtCntStMisfEvents[(IonAbsTdcEOA)] = (uint8_T)(VtCntStMisfEvents
          [(IonAbsTdcEOA)] - ((uint8_T)ONE));
      } else {
        /* Transition: '<S37>:32' */
      }

      guard1 = true;
    }

    if (guard1) {
      if (!MISFOVERTHRPERMEN) {
        /* Transition: '<S37>:71' */
        VtStMisfOverThr[(IonAbsTdcEOA)] = false;
      } else {
        /* Transition: '<S37>:35' */
      }
    }
  } else {
    /* Transition: '<S37>:61' */
    /* Transition: '<S37>:63' */
    /* Transition: '<S37>:64' */
  }

  if (ENSPARKAGINGCALC) {
    /* Outputs for Function Call SubSystem: '<S12>/calc_aged_factor'
     *
     * Block description for '<S12>/calc_aged_factor':
     *  This block calculates the output of the table TBTSPARKCORR.
     */
    /* DataTypeConversion: '<S39>/Conversion2' incorporates:
     *  Inport: '<Root>/Load'
     */
    /* Transition: '<S37>:51' */
    /* Apply correction */
    /* Event: '<S37>:54' */
    rtb_Conversion2 = Load;

    /* DataTypeConversion: '<S39>/Conversion3' incorporates:
     *  Inport: '<Root>/Rpm'
     */
    rtb_Look2D_U16_U16_U16 = Rpm;

    /* S-Function (Look2D_U16_U16_U16): '<S39>/Look2D_U16_U16_U16' incorporates:
     *  Constant: '<S38>/BKLOADTSPARKNOM'
     *  Constant: '<S38>/BKLOADTSPARKNOM_dim'
     *  Constant: '<S38>/BKRPMTSPARKNOM'
     *  Constant: '<S38>/BKRPMTSPARKNOM_dim'
     *  Constant: '<S38>/TBTSPARKCORR'
     *
     * Block requirements for '<S38>/TBTSPARKCORR':
     *  1. EISB_FCA6CYL_SW_REQ_1220: When the spark duration control algorithm is disabled, the primary... (ECU_SW_Requirements#1764)
     */
    Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBTSPARKCORR[0],
                       rtb_Look2D_U16_U16_U16, &BKRPMTSPARKNOM[0], ((uint8_T)
      BKRPMTSPARKNOM_dim), rtb_Conversion2, &BKLOADTSPARKNOM[0], ((uint8_T)
      BKLOADTSPARKNOM_dim));
    TSparkAgedFactor = (uint16_T)((int32_T)(((int32_T)rtb_Look2D_U16_U16_U16) -
      32768));

    /* End of Outputs for SubSystem: '<S12>/calc_aged_factor' */
  } else {
    /* Transition: '<S37>:79' */
    TSparkAgedFactor = 0U;
  }

  if (((int32_T)VtTSparkNomOffAdEE[(IonAbsTdcEOA)]) > 0) {
    /* Transition: '<S37>:67' */
    TSparkNomTotCorr = (uint16_T)(TSparkNomTot + VtTSparkNomOffAdEE
      [(IonAbsTdcEOA)]);
    SparkPlugFactorCorr = (uint16_T)((((uint32_T)TSparkNomTot) << ((uint32_T)15))
      / ((uint32_T)TSparkNomTotCorr));
  } else {
    /* Transition: '<S37>:66' */
    SparkPlugFactorCorr = 32768U;
    TSparkNomTotCorr = TSparkNomTot;
  }

  /* End of Chart: '<S12>/TSpark_corr_calc' */

  /* Chart: '<S2>/eoa_adapt_mgm' incorporates:
   *  SubSystem: '<S2>/VtSparkFilt_Calc'
   *
   * Block description for '<S2>/VtSparkFilt_Calc':
   *  Calculate filtered spark duration from its median value. The First
   *  Order Filter is reset when the coil actuation is in Plasma mode.
   *
   * Block requirements for '<S2>/VtSparkFilt_Calc':
   *  1. EISB_FCA6CYL_SW_REQ_245: The spark duration time feedback (signal VtTSparkMedian, calculate... (ECU_SW_Requirements#323)
   */
  /* DataTypeConversion: '<S41>/Conversion5' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/VtTSparkMedian'
   *  Selector: '<S13>/Selector_Outold4'
   */
  /* Event: '<S14>:29' */
  rtb_FOF_Reset_S16_FXP_o1 = (int16_T)VtTSparkMedian[(IonAbsTdcEOA)];

  /* DataTypeConversion: '<S41>/Conversion2' */
  rtb_Conversion2_o = (int16_T)TSparkNomTotCorr;

  /* DataTypeConversion: '<S41>/Conversion3' incorporates:
   *  Constant: '<S13>/two'
   *  Inport: '<Root>/StPlasObj'
   *  RelationalOperator: '<S13>/Relational Operator1'
   */
  TbRingBuffMisfEvents_row = (uint8_T)((ION_ST_SEL != StPlasObj) ? 1 : 0);

  /* DataTypeConversion: '<S41>/Conversion4' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Selector: '<S13>/Selector_Outold1'
   */
  rtb_FOF_Reset_S16_FXP_o2 = (int32_T)VtTSparkFiltHR[(IonAbsTdcEOA)];

  /* S-Function (FOF_Reset_S16_FXP): '<S41>/FOF_Reset_S16_FXP' incorporates:
   *  Constant: '<S13>/TSPARKKFILT'
   */
  FOF_Reset_S16_FXP( &rtb_FOF_Reset_S16_FXP_o1, &rtb_FOF_Reset_S16_FXP_o2,
                    rtb_FOF_Reset_S16_FXP_o1, TSPARKKFILT, rtb_Conversion2_o,
                    TbRingBuffMisfEvents_row, rtb_FOF_Reset_S16_FXP_o2);

  /* Assignment: '<S13>/Assignment1' incorporates:
   *  DataTypeConversion: '<S41>/Conversion7'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  SignalConversion generated from: '<S2>/VtTSparkFiltHR'
   */
  VtTSparkFiltHR[(IonAbsTdcEOA)] = (uint32_T)rtb_FOF_Reset_S16_FXP_o2;

  /* DataTypeConversion: '<S42>/Conversion' */
  rtb_Conversion2 = (uint16_T)rtb_FOF_Reset_S16_FXP_o1;

  /* Assignment: '<S13>/Assignment3' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  SignalConversion generated from: '<S2>/VtTSparkFilt'
   */
  VtTSparkFilt[(IonAbsTdcEOA)] = rtb_Conversion2;

  /* Chart: '<S2>/eoa_adapt_mgm' incorporates:
   *  SubSystem: '<S2>/TSparkErr_Calc'
   *
   * Block description for '<S2>/TSparkErr_Calc':
   *  Calculates error between Filtered spark duration signal and nominal
   *  spark duration under current engine conditions with the use of a
   *  dead-band.
   *
   * Block requirements for '<S2>/TSparkErr_Calc':
   *  1. EISB_FCA6CYL_SW_REQ_246: Closed loop error shall be estimated for each coil, using the corr... (ECU_SW_Requirements#324)
   */
  /* Sum: '<S8>/Sum' */
  /* Event: '<S14>:31' */
  rtb_FOF_Reset_S16_FXP_o2 = ((int32_T)TSparkNomTotCorr) - ((int32_T)
    rtb_Conversion2);

  /* MinMax: '<S8>/MinMax1' incorporates:
   *  Constant: '<S8>/MIN_TSPARK_ERR'
   */
  if (rtb_FOF_Reset_S16_FXP_o2 > MIN_TSPARK_ERR) {
    /* DataTypeConversion: '<S8>/Data Type Conversion' */
    rtb_FOF_Reset_S16_FXP_o1 = (int16_T)rtb_FOF_Reset_S16_FXP_o2;
  } else {
    /* DataTypeConversion: '<S8>/Data Type Conversion' */
    rtb_FOF_Reset_S16_FXP_o1 = (int16_T)MIN_TSPARK_ERR;
  }

  /* End of MinMax: '<S8>/MinMax1' */

  /* Assignment: '<S8>/Assignment4' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  SignalConversion generated from: '<S2>/VtTSparkErr'
   */
  VtTSparkErr[(IonAbsTdcEOA)] = rtb_FOF_Reset_S16_FXP_o1;

  /* Switch: '<S26>/Switch' incorporates:
   *  Constant: '<S8>/TSPARKERRNEGDB'
   *  Constant: '<S8>/TSPARKERRPOSDB'
   *  RelationalOperator: '<S26>/u_GTE_up'
   *  RelationalOperator: '<S26>/u_GT_lo'
   *  Switch: '<S26>/Switch1'
   */
  if (rtb_FOF_Reset_S16_FXP_o1 >= TSPARKERRPOSDB) {
    rtb_Conversion2_o = TSPARKERRPOSDB;
  } else if (rtb_FOF_Reset_S16_FXP_o1 > TSPARKERRNEGDB) {
    /* Switch: '<S26>/Switch1' */
    rtb_Conversion2_o = rtb_FOF_Reset_S16_FXP_o1;
  } else {
    rtb_Conversion2_o = TSPARKERRNEGDB;
  }

  /* End of Switch: '<S26>/Switch' */

  /* Product: '<S8>/Product2' incorporates:
   *  Constant: '<S8>/TSPARKERRGAIN'
   *  Sum: '<S26>/Diff'
   */
  rtb_FOF_Reset_S16_FXP_o2 = ((int32_T)((int16_T)(rtb_FOF_Reset_S16_FXP_o1 -
    rtb_Conversion2_o))) * ((int32_T)TSPARKERRGAIN);

  /* MinMax: '<S8>/MinMax2' incorporates:
   *  Constant: '<S8>/MAX_TSPARK_ERRPI'
   */
  if (MAX_TSPARK_ERRPI < rtb_FOF_Reset_S16_FXP_o2) {
    rtb_FOF_Reset_S16_FXP_o2 = MAX_TSPARK_ERRPI;
  }

  /* End of MinMax: '<S8>/MinMax2' */

  /* MinMax: '<S8>/MinMax3' incorporates:
   *  Constant: '<S8>/MIN_TSPARK_ERRPI'
   */
  if (rtb_FOF_Reset_S16_FXP_o2 > MIN_TSPARK_ERRPI) {
    /* DataTypeConversion: '<S8>/Data Type Conversion1' */
    TSparkErrPi = (int16_T)rtb_FOF_Reset_S16_FXP_o2;
  } else {
    /* DataTypeConversion: '<S8>/Data Type Conversion1' */
    TSparkErrPi = (int16_T)MIN_TSPARK_ERRPI;
  }

  /* End of MinMax: '<S8>/MinMax3' */

  /* Chart: '<S2>/eoa_adapt_mgm' incorporates:
   *  SubSystem: '<S2>/TSparkCtrl_EnCond'
   *
   * Block description for '<S2>/TSparkCtrl_EnCond':
   *  TSpark closed loop control enable condition calculation
   *  (TSparkCtrlEn==1) and detection of engine stability conditions
   *  (TSparkEngStab==1)
   */
  /* Selector: '<S15>/Selector_Outold1' incorporates:
   *  Constant: '<S15>/REC_TSPARKCTRL_OFF_0'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/VtRec'
   *  Sum: '<S15>/Sum'
   */
  /* Event: '<S14>:34' */
  Selector_Outold1 = VtRec[(uint8_T)(((uint32_T)IonAbsTdcEOA) + ((uint32_T)
    REC_TSPARKCTRL_OFF_0))];

  /* Product: '<S17>/Product' incorporates:
   *  Constant: '<S17>/RPM_2_PERC'
   *  Inport: '<Root>/Rpm'
   */
  rtb_Conversion2 = (uint16_T)((((uint32_T)Rpm) << ((uint32_T)7)) / ((uint32_T)
    ((uint8_T)RPM_2_PERC)));

  /* S-Function (SteadyStateDetect): '<S25>/SteadyStateDetect' incorporates:
   *  Constant: '<S17>/Constant'
   *  Constant: '<S17>/TDCSTABRPMTSPARK'
   *  Constant: '<S17>/THRSTABRPMTSPARK'
   *
   * Block requirements for '<S17>/TDCSTABRPMTSPARK':
   *  1. EISB_FCA6CYL_SW_REQ_1332: The engine speed shall be considered stable (i.e. signal StabRpmTS... (ECU_SW_Requirements#1752)
   *
   * Block requirements for '<S17>/THRSTABRPMTSPARK':
   *  1. EISB_FCA6CYL_SW_REQ_1332: The engine speed shall be considered stable (i.e. signal StabRpmTS... (ECU_SW_Requirements#1752)
   */
  SteadyStateDetect( (&(TSparkCtrlAdat_stabRpm_mem)), &rtb_SteadyStateDetect_o2,
                    &rtb_SteadyStateDetect_o3, &rtb_SteadyStateDetect_o4,
                    rtb_Conversion2, ((uint8_T)0U), THRSTABRPMTSPARK,
                    TDCSTABRPMTSPARK, TSparkCtrlAdat_stabRpm_mem,
                    TSparkCtrlAdat_stabRpm_memInt2,
                    TSparkCtrlAdat_stabRpm_memInt1);

  /* DataTypeConversion: '<S17>/Data Type Conversion' */
  StabRpmTSpark = (((int32_T)TSparkCtrlAdat_stabRpm_mem) != 0);

  /* Product: '<S16>/Product' incorporates:
   *  Constant: '<S16>/LOAD_2_PERC'
   *  Inport: '<Root>/Load'
   */
  TSparkCtrlAdat_stabRpm_memInt1 = (uint16_T)(((uint32_T)Load) / ((uint32_T)
    ((uint8_T)LOAD_2_PERC)));

  /* Memory: '<S16>/Memory' */
  TSparkCtrlAdat_stabRpm_mem = TSparkCtrlAdat_stabLoad_mem;

  /* Memory: '<S24>/Memory1' */
  TSparkCtrlAdat_stabRpm_memInt2 = TSparkCtrlAdat_stabLoad_memInt2;

  /* Memory: '<S24>/Memory' */
  rtb_Conversion2 = TSparkCtrlAdat_stabLoad_memInt1;

  /* S-Function (SteadyStateDetect): '<S24>/SteadyStateDetect' incorporates:
   *  Constant: '<S16>/Constant'
   *  Constant: '<S16>/TDCSTABLOADTSPARK'
   *  Constant: '<S16>/THRSTABLOADTSPARK'
   *
   * Block requirements for '<S16>/TDCSTABLOADTSPARK':
   *  1. EISB_FCA6CYL_SW_REQ_1335: The engine load shall be considered stable (i.e. signal StabLoadTS... (ECU_SW_Requirements#1754)
   *
   * Block requirements for '<S16>/THRSTABLOADTSPARK':
   *  1. EISB_FCA6CYL_SW_REQ_1335: The engine load shall be considered stable (i.e. signal StabLoadTS... (ECU_SW_Requirements#1754)
   */
  SteadyStateDetect( (&(TSparkCtrlAdat_stabRpm_mem)),
                    &rtb_SteadyStateDetect_o2_h, &rtb_SteadyStateDetect_o3_p,
                    &rtb_SteadyStateDetect_o4_k, TSparkCtrlAdat_stabRpm_memInt1,
                    ((uint8_T)0U), THRSTABLOADTSPARK, TDCSTABLOADTSPARK,
                    TSparkCtrlAdat_stabRpm_mem, TSparkCtrlAdat_stabRpm_memInt2,
                    rtb_Conversion2);

  /* DataTypeConversion: '<S16>/Data Type Conversion' */
  StabLoadTSpark = (((int32_T)TSparkCtrlAdat_stabRpm_mem) != 0);

  /* Logic: '<S15>/Logical Operator' incorporates:
   *  Constant: '<S15>/TSPARKCTRLLOADMAX'
   *  Constant: '<S15>/TSPARKCTRLLOADMIN'
   *  Constant: '<S15>/TSPARKCTRLRPMMAX'
   *  Constant: '<S15>/TSPARKCTRLRPMMIN'
   *  Inport: '<Root>/Load'
   *  Inport: '<Root>/Rpm'
   *  RelationalOperator: '<S15>/Relational Operator2'
   *  RelationalOperator: '<S15>/Relational Operator3'
   *  RelationalOperator: '<S15>/Relational Operator4'
   *  RelationalOperator: '<S15>/Relational Operator5'
   *
   * Block requirements for '<S15>/Logical Operator':
   *  1. EISB_FCA6CYL_SW_REQ_1333: The spark duration control algorithm shall be disabled when the en... (ECU_SW_Requirements#1751)
   *  2. EISB_FCA6CYL_SW_REQ_1334: The spark duration control algorithm shall be disabled when the en... (ECU_SW_Requirements#1753)
   *  3. EISB_FCA6CYL_SW_REQ_1216: The spark duration control algorithm shall be disabled if the engi... (ECU_SW_Requirements#1750)
   *  4. EISB_FCA6CYL_SW_REQ_1217: The spark duration control algorithm shall be disabled if the engi... (ECU_SW_Requirements#1755)
   *
   * Block requirements for '<S15>/TSPARKCTRLLOADMAX':
   *  1. EISB_FCA6CYL_SW_REQ_1216: The spark duration control algorithm shall be disabled if the engi... (ECU_SW_Requirements#1750)
   *
   * Block requirements for '<S15>/TSPARKCTRLLOADMIN':
   *  1. EISB_FCA6CYL_SW_REQ_1216: The spark duration control algorithm shall be disabled if the engi... (ECU_SW_Requirements#1750)
   *
   * Block requirements for '<S15>/TSPARKCTRLRPMMAX':
   *  1. EISB_FCA6CYL_SW_REQ_1217: The spark duration control algorithm shall be disabled if the engi... (ECU_SW_Requirements#1755)
   *
   * Block requirements for '<S15>/TSPARKCTRLRPMMIN':
   *  1. EISB_FCA6CYL_SW_REQ_1217: The spark duration control algorithm shall be disabled if the engi... (ECU_SW_Requirements#1755)
   */
  TSparkEngStab = ((((((Load <= TSPARKCTRLLOADMAX) && (Load >= TSPARKCTRLLOADMIN))
                      && (Rpm <= TSPARKCTRLRPMMAX)) && (Rpm >= TSPARKCTRLRPMMIN))
                    && (StabRpmTSpark)) && (StabLoadTSpark));

  /* Logic: '<S15>/Logical Operator1' incorporates:
   *  Constant: '<S15>/DISCLPLASPARKCTRL'
   *  Constant: '<S15>/ENGINEATLIMITERDIS'
   *  Constant: '<S15>/FLGDTCSHIFTINGDIS'
   *  Constant: '<S15>/FLGSTABCONDCLBYP'
   *  Constant: '<S15>/ION_ST_SEL'
   *  Constant: '<S15>/MISFOVERTHRDIS'
   *  Constant: '<S15>/TSPARKCTRLDESCUTOFFRST'
   *  Constant: '<S15>/TSPARKCTRLEN'
   *  Constant: '<S15>/TSPARKCTRLPLASMAONRST'
   *  Constant: '<S15>/TSPARKCTRTWATLTHR'
   *  Constant: '<S23>/Constant'
   *  Inport: '<Root>/DesCutOffCAN'
   *  Inport: '<Root>/EngineAtLimiter'
   *  Inport: '<Root>/FlgDCTShifting'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/StPlasObj'
   *  Inport: '<Root>/TSparkFreezeFlg'
   *  Inport: '<Root>/TWater'
   *  Inport: '<Root>/VtSelPlaCtrlOL'
   *  Logic: '<S15>/Logical Operator2'
   *  Logic: '<S15>/Logical Operator3'
   *  Logic: '<S15>/Logical Operator5'
   *  Logic: '<S15>/Logical Operator6'
   *  Logic: '<S15>/Logical Operator7'
   *  Logic: '<S15>/Logical Operator8'
   *  Logic: '<S15>/Logical Operator9'
   *  RelationalOperator: '<S15>/Relational Operator1'
   *  RelationalOperator: '<S15>/Relational Operator7'
   *  RelationalOperator: '<S18>/Compare'
   *  RelationalOperator: '<S19>/Compare'
   *  RelationalOperator: '<S20>/Compare'
   *  RelationalOperator: '<S21>/Compare'
   *  RelationalOperator: '<S23>/Compare'
   *  Selector: '<S15>/Selector_Outold2'
   *  Selector: '<S15>/Selector_Outold3'
   *
   * Block requirements for '<S15>/TSPARKCTRLEN':
   *  1. EISB_FCA6CYL_SW_REQ_533: The spark duration control shall have the possibility to be disabl... (ECU_SW_Requirements#320)
   *
   * Block requirements for '<S15>/Logical Operator6':
   *  1. EISB_FCA6CYL_SW_REQ_1218: The spark duration control algorithm shall be disabled when the si... (ECU_SW_Requirements#1756)
   */
  TSparkCtrlEn_noStMisf = (((((((((((TSPARKCTRLEN) && ((!TSPARKCTRLDESCUTOFFRST)
    || (!DesCutOffCAN))) && ((!TSPARKCTRLPLASMAONRST) || (ION_ST_SEL ==
    StPlasObj))) && ((!ENGINEATLIMITERDIS) || (((int32_T)EngineAtLimiter) == 0)))
    && ((!FLGDTCSHIFTINGDIS) || (!FlgDCTShifting))) && (!TSparkFreezeFlg)) &&
    (!Selector_Outold1)) && ((DISCLPLASPARKCTRL) || (!VtSelPlaCtrlOL
    [(IonAbsTdcEOA)]))) && (TWater >= TSPARKCTRTWATLTHR)) && ((!MISFOVERTHRDIS) ||
    (!VtStMisfOverThr[(IonAbsTdcEOA)]))) && ((FLGSTABCONDCLBYP) ||
    (TSparkEngStab)));

  /* Logic: '<S15>/Logical Operator10' incorporates:
   *  Constant: '<S15>/DISMISFCNDSPARKCTRL'
   *  Constant: '<S15>/NO_MISF'
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  Inport: '<Root>/StMisf'
   *  Logic: '<S15>/Logical Operator4'
   *  RelationalOperator: '<S15>/Relational Operator6'
   *  Selector: '<S15>/Selector_Outold5'
   */
  TSparkCtrlEn = (((DISMISFCNDSPARKCTRL) || (NO_MISF == ((uint32_T)StMisf
    [(IonAbsTdcEOA)]))) && TSparkCtrlEn_noStMisf);

  /* Assignment: '<S15>/Assignment3' incorporates:
   *  Inport: '<Root>/IonAbsTdcEOA'
   *  SignalConversion generated from: '<S2>/VtTSparkCtrlEn'
   */
  VtTSparkCtrlEn[(IonAbsTdcEOA)] = TSparkCtrlEn;

  /* Update for Memory: '<S17>/Memory' */
  TSparkCtrlAdat_stabRpm_mem = rtb_SteadyStateDetect_o2;

  /* Update for Memory: '<S25>/Memory1' */
  TSparkCtrlAdat_stabRpm_memInt2 = rtb_SteadyStateDetect_o3;

  /* Update for Memory: '<S25>/Memory' */
  TSparkCtrlAdat_stabRpm_memInt1 = rtb_SteadyStateDetect_o4;

  /* Update for Memory: '<S16>/Memory' */
  TSparkCtrlAdat_stabLoad_mem = rtb_SteadyStateDetect_o2_h;

  /* Update for Memory: '<S24>/Memory1' */
  TSparkCtrlAdat_stabLoad_memInt2 = rtb_SteadyStateDetect_o3_p;

  /* Update for Memory: '<S24>/Memory' */
  TSparkCtrlAdat_stabLoad_memInt1 = rtb_SteadyStateDetect_o4_k;
  guard1 = false;
  if (TSparkCtrlEn) {
    /* Outputs for Function Call SubSystem: '<S2>/TSparkPi_calc'
     *
     * Block description for '<S2>/TSparkPi_calc':
     *  Closed loop control subsystems.
     *
     * Block requirements for '<S2>/TSparkPi_calc':
     *  1. EISB_FCA6CYL_SW_REQ_532: The software shall implement a spark duration control for each coil. (ECU_SW_Requirements#319)
     */
    /* S-Function (PreLookUpIdSearch_S16): '<S34>/PreLookUpIdSearch_S16' incorporates:
     *  Constant: '<S31>/BKTSPARKERRKPI'
     *  Constant: '<S31>/BKTSPARKERRKPI_dim'
     */
    /* Transition: '<S14>:26' */
    /* Event: '<S14>:33' */
    PreLookUpIdSearch_S16( &rtb_LookUp_IR_U16_b, &rtb_Sum6, TSparkErrPi,
                          &BKTSPARKERRKPI[0], ((uint8_T)BKTSPARKERRKPI_dim));

    /* S-Function (LookUp_IR_U16): '<S33>/LookUp_IR_U16' incorporates:
     *  Constant: '<S31>/BKTSPARKERRKPI_dim'
     *  Constant: '<S31>/VTPITSPARKKI'
     *
     * Block requirements for '<S31>/VTPITSPARKKI':
     *  1. EISB_FCA6CYL_SW_REQ_247: A PI closed loop control shall be implemented for each coil, using... (ECU_SW_Requirements#325)
     */
    LookUp_IR_U16( &rtb_LookUp_IR_U16, &VTPITSPARKKI[0], rtb_LookUp_IR_U16_b,
                  rtb_Sum6, ((uint8_T)BKTSPARKERRKPI_dim));

    /* S-Function (LookUp_IR_U16): '<S32>/LookUp_IR_U16' incorporates:
     *  Constant: '<S31>/BKTSPARKERRKPI_dim'
     *  Constant: '<S31>/VTPITSPARKKP'
     *
     * Block requirements for '<S31>/VTPITSPARKKP':
     *  1. EISB_FCA6CYL_SW_REQ_247: A PI closed loop control shall be implemented for each coil, using... (ECU_SW_Requirements#325)
     */
    LookUp_IR_U16( &rtb_LookUp_IR_U16_b, &VTPITSPARKKP[0], rtb_LookUp_IR_U16_b,
                  rtb_Sum6, ((uint8_T)BKTSPARKERRKPI_dim));

    /* Sum: '<S11>/Sum6' */
    rtb_Sum6 = (uint16_T)(((uint32_T)rtb_LookUp_IR_U16) + ((uint32_T)
      rtb_LookUp_IR_U16_b));

    /* Outputs for Function Call SubSystem: '<S2>/TSparkErr_Calc'
     *
     * Block description for '<S2>/TSparkErr_Calc':
     *  Calculates error between Filtered spark duration signal and nominal
     *  spark duration under current engine conditions with the use of a
     *  dead-band.
     *
     * Block requirements for '<S2>/TSparkErr_Calc':
     *  1. EISB_FCA6CYL_SW_REQ_246: Closed loop error shall be estimated for each coil, using the corr... (ECU_SW_Requirements#324)
     */
    /* Sum: '<S11>/Sum5' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     *  Product: '<S11>/Product'
     *  Product: '<S11>/Product1'
     *  Selector: '<S11>/Selector_Outold5'
     *  Selector: '<S8>/Selector_Outold4'
     *  Sum: '<S11>/Sum4'
     *
     * Block requirements for '<S11>/Sum5':
     *  1. EISB_FCA6CYL_SW_REQ_247: A PI closed loop control shall be implemented for each coil, using... (ECU_SW_Requirements#325)
     */
    rtb_FOF_Reset_S16_FXP_o2 = ((((int32_T)TSparkErrPi) * ((int32_T)rtb_Sum6)) -
      (((int32_T)VtTSparkErrPi[(IonAbsTdcEOA)]) * ((int32_T)rtb_LookUp_IR_U16_b)))
      + ((int32_T)VtPiTSpark[(IonAbsTdcEOA)]);

    /* End of Outputs for SubSystem: '<S2>/TSparkErr_Calc' */

    /* MinMax: '<S11>/MinMax1' incorporates:
     *  Constant: '<S11>/TSPARKPIMINSAT'
     *  DataTypeConversion: '<S11>/Data Type Conversion3'
     */
    if (rtb_FOF_Reset_S16_FXP_o2 <= ((int32_T)TSPARKPIMINSAT)) {
      rtb_FOF_Reset_S16_FXP_o2 = (int32_T)TSPARKPIMINSAT;
    }

    /* MinMax: '<S11>/MinMax' incorporates:
     *  Constant: '<S11>/TSPARKPIMAXSAT'
     *  DataTypeConversion: '<S11>/Data Type Conversion1'
     *  MinMax: '<S11>/MinMax1'
     *
     * Block requirements for '<S11>/MinMax':
     *  1. EISB_FCA6CYL_SW_REQ_248: Closed loop control output (i.e. signal PiTSparkPreSat) shall be s... (ECU_SW_Requirements#326)
     */
    if (((int32_T)TSPARKPIMAXSAT) < rtb_FOF_Reset_S16_FXP_o2) {
      /* DataTypeConversion: '<S11>/Data Type Conversion2' */
      PiTSpark = TSPARKPIMAXSAT;
    } else {
      /* DataTypeConversion: '<S11>/Data Type Conversion2' */
      PiTSpark = (int16_T)rtb_FOF_Reset_S16_FXP_o2;
    }

    /* End of MinMax: '<S11>/MinMax' */

    /* Sum: '<S11>/Sum1' incorporates:
     *  Constant: '<S11>/ONE'
     */
    rtb_Sum6 = (uint16_T)((int32_T)(((int32_T)PiTSpark) + 32768));

    /* Assignment: '<S11>/Assignment1' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    VtIPriCorr[(IonAbsTdcEOA)] = rtb_Sum6;

    /* Assignment: '<S11>/Assignment4' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    VtPiTSpark[(IonAbsTdcEOA)] = PiTSpark;

    /* Assignment: '<S11>/Assignment2' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    VtTSparkErrPi[(IonAbsTdcEOA)] = TSparkErrPi;

    /* End of Outputs for SubSystem: '<S2>/TSparkPi_calc' */

    /* SignalConversion generated from: '<S2>/VtIPriCorrAdEE' incorporates:
     *  Inport: '<Root>/IonAbsTdcEOA'
     */
    VtIPriCorrAdEE[(IonAbsTdcEOA)] = rtb_Sum6;
    if ((ENSPARKAGINGCALC) && (TSparkEngStab)) {
      /* Outputs for Function Call SubSystem: '<S2>/TSparkFactorCalc'
       *
       * Block description for '<S2>/TSparkFactorCalc':
       *  Calculates Spark plug usage in terms of percentage factor.
       */
      /* Switch: '<S9>/Switch' incorporates:
       *  Constant: '<S9>/SAT_SPARK_PLUG_FACT'
       *  RelationalOperator: '<S27>/Compare'
       */
      /* Transition: '<S14>:38' */
      /* Event: '<S14>:69' */
      if (((int32_T)TSparkAgedFactor) > 0) {
        /* MinMax: '<S9>/MinMax1' */
        if (PiTSpark > 0) {
          rtb_FOF_Reset_S16_FXP_o2 = (int32_T)PiTSpark;
        } else {
          rtb_FOF_Reset_S16_FXP_o2 = 0;
        }

        /* End of MinMax: '<S9>/MinMax1' */

        /* Product: '<S9>/Divide1' incorporates:
         *  Product: '<S9>/Divide'
         */
        rtb_FOF_Reset_S16_FXP_o2 = (int32_T)((uint32_T)((((((uint32_T)
          rtb_FOF_Reset_S16_FXP_o2) << ((uint32_T)15)) / ((uint32_T)
          TSparkAgedFactor)) * ((uint32_T)SparkPlugFactorCorr)) >> ((uint32_T)15)));

        /* DataTypeConversion: '<S9>/Data Type Conversion' incorporates:
         *  Constant: '<S9>/SAT_SPARK_PLUG_FACT'
         */
        TbRingBuffMisfEvents_mask = (((uint32_T)((uint16_T)SAT_SPARK_PLUG_FACT))
          << ((uint32_T)5));

        /* MinMax: '<S9>/MinMax' */
        if (((uint32_T)rtb_FOF_Reset_S16_FXP_o2) < TbRingBuffMisfEvents_mask) {
          rtb_Sum6 = (uint16_T)(((uint32_T)rtb_FOF_Reset_S16_FXP_o2) >>
                                ((uint32_T)5));
        } else {
          rtb_Sum6 = (uint16_T)(TbRingBuffMisfEvents_mask >> ((uint32_T)5));
        }

        /* End of MinMax: '<S9>/MinMax' */
      } else {
        rtb_Sum6 = ((uint16_T)SAT_SPARK_PLUG_FACT);
      }

      /* End of Switch: '<S9>/Switch' */
      /* End of Outputs for SubSystem: '<S2>/TSparkFactorCalc' */

      /* Inport: '<Root>/IonAbsTdcEOA' */
      VtSparkPlugFactorSamples[(IonAbsTdcEOA)] = (uint16_T)((int32_T)(((int32_T)
        VtSparkPlugFactorSamples[(IonAbsTdcEOA)]) + 1));
      VtSparkPlugFactorAccum[(IonAbsTdcEOA)] = VtSparkPlugFactorAccum
        [(IonAbsTdcEOA)] + ((uint32_T)rtb_Sum6);
      spark_fact_learning = true;

      /* Inport: '<Root>/IonAbsTdcEOA' incorporates:
       *  Constant: '<S2>/MAXTSPARKADATSMP'
       */
      if (VtSparkPlugFactorSamples[(IonAbsTdcEOA)] >= MAXTSPARKADATSMP) {
        /* Transition: '<S14>:48' */
        VtSparkPlugFactorAccum[(IonAbsTdcEOA)] = VtSparkPlugFactorAccum
          [(IonAbsTdcEOA)] / ((uint32_T)VtSparkPlugFactorSamples[(IonAbsTdcEOA)]);
        if (VtSparkPlugFactorAccum[(IonAbsTdcEOA)] > ((uint32_T)
             VtSparkPlugFactorEE[(IonAbsTdcEOA)])) {
          /* Constant: '<S2>/SPARKPLUGWGTH' */
          /* Transition: '<S14>:50' */
          VtSparkPlugFactorAccum[(IonAbsTdcEOA)] = (((VtSparkPlugFactorAccum
            [(IonAbsTdcEOA)] - ((uint32_T)VtSparkPlugFactorEE[(IonAbsTdcEOA)])) *
            ((uint32_T)SPARKPLUGWGTH)) >> ((uint32_T)10));
          VtSparkPlugFactorEE[(IonAbsTdcEOA)] = (uint16_T)(((uint32_T)
            VtSparkPlugFactorEE[(IonAbsTdcEOA)]) + VtSparkPlugFactorAccum
            [(IonAbsTdcEOA)]);
        } else {
          /* Transition: '<S14>:52' */
        }

        /* Transition: '<S14>:51' */
        VtSparkPlugFactorSamples[(IonAbsTdcEOA)] = 0U;
        VtSparkPlugFactorAccum[(IonAbsTdcEOA)] = 0U;
      } else {
        /* Transition: '<S14>:45' */
      }

      /* Transition: '<S14>:46' */
    } else {
      /* Transition: '<S14>:42' */
      /* Transition: '<S14>:43' */
      guard1 = true;
    }
  } else {
    /* Outputs for Function Call SubSystem: '<S2>/TSparkPercCalc'
     *
     * Block description for '<S2>/TSparkPercCalc':
     *  Open loop calculation.
     *  This can be tuned such as the variables remain frozen, when open loop is enabled only for the punctual misfire detection.
     */
    /* Outputs for Enabled SubSystem: '<S10>/open_loop_calc' incorporates:
     *  EnablePort: '<S28>/open_loop_calc'
     *
     * Block description for '<S10>/open_loop_calc':
     *  Calculate coil primary current target correction in Open Loop. The
     *  correction has a rate limitation and is based on the estimation of the
     *  spark plug aging in terms of percentage multiplied with the map of end
     *  of life gain.
     */
    /* Outputs for Enabled SubSystem: '<S10>/open_loop_freezing' incorporates:
     *  EnablePort: '<S29>/open_loop_freezing'
     *
     * Block description for '<S10>/open_loop_freezing':
     *  Freezing of the variable when open loop is enabled only for punctual
     *  misfire detection.
     */
    /* Logic: '<S10>/Logical Operator1' incorporates:
     *  Constant: '<S10>/ENFREEZEOLMISF'
     *  Logic: '<S10>/Logical Operator'
     */
    /* Transition: '<S14>:27' */
    /* Event: '<S14>:32' */
    if ((!ENFREEZEOLMISF) || (!TSparkCtrlEn_noStMisf)) {
      /* Switch: '<S28>/Switch' incorporates:
       *  Constant: '<S28>/FORCEDSPARKPLUGFACTOR'
       *  Constant: '<S28>/FORCESPARKPLUGFACTOR'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  RelationalOperator: '<S30>/Compare'
       *  Selector: '<S28>/Selector_Outold5'
       *  Switch: '<S28>/Switch1'
       */
      if (FORCESPARKPLUGFACTOR) {
        rtb_Sum6 = FORCEDSPARKPLUGFACTOR;
      } else if (Selector_Outold1) {
        /* Switch: '<S28>/Switch1' incorporates:
         *  Constant: '<S28>/SPARKPLUGRECFACTOR'
         */
        rtb_Sum6 = SPARKPLUGRECFACTOR;
      } else {
        rtb_Sum6 = VtSparkPlugFactorEE[(IonAbsTdcEOA)];
      }

      /* End of Switch: '<S28>/Switch' */

      /* Product: '<S28>/Divide' */
      rtb_FOF_Reset_S16_FXP_o1 = (int16_T)((uint32_T)((((uint32_T)
        TSparkAgedFactor) * ((uint32_T)rtb_Sum6)) >> ((uint32_T)10)));

      /* Assignment: '<S28>/Assignment1' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      VtPiTSpark[(IonAbsTdcEOA)] = rtb_FOF_Reset_S16_FXP_o1;

      /* Sum: '<S28>/Subtract2' incorporates:
       *  Constant: '<S28>/Constant1'
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Selector: '<S28>/Selector_Outold1'
       *  Sum: '<S28>/Subtract1'
       */
      rtb_FOF_Reset_S16_FXP_o2 = (((int32_T)rtb_FOF_Reset_S16_FXP_o1) + 32768) -
        ((int32_T)VtIPriCorr[(IonAbsTdcEOA)]);

      /* MinMax: '<S28>/MinMax' incorporates:
       *  Constant: '<S28>/TSPARKRSTCORRRATEMAX'
       *  DataTypeConversion: '<S28>/Data Type Conversion'
       */
      if (((int32_T)TSPARKRSTCORRRATEMAX) < rtb_FOF_Reset_S16_FXP_o2) {
        rtb_FOF_Reset_S16_FXP_o2 = (int32_T)TSPARKRSTCORRRATEMAX;
      }

      /* MinMax: '<S28>/MinMax1' incorporates:
       *  Constant: '<S28>/TSPARKRSTCORRRATEMIN'
       *  DataTypeConversion: '<S28>/Data Type Conversion1'
       *  MinMax: '<S28>/MinMax'
       */
      if (rtb_FOF_Reset_S16_FXP_o2 <= ((int32_T)TSPARKRSTCORRRATEMIN)) {
        rtb_FOF_Reset_S16_FXP_o2 = (int32_T)TSPARKRSTCORRRATEMIN;
      }

      /* End of MinMax: '<S28>/MinMax1' */

      /* Assignment: '<S28>/Assignment5' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       *  Selector: '<S28>/Selector_Outold1'
       *  Sum: '<S28>/Subtract3'
       */
      VtIPriCorr[(IonAbsTdcEOA)] = (uint16_T)((int32_T)(rtb_FOF_Reset_S16_FXP_o2
        + ((int32_T)VtIPriCorr[(IonAbsTdcEOA)])));

      /* Assignment: '<S28>/Assignment2' incorporates:
       *  Inport: '<Root>/IonAbsTdcEOA'
       */
      VtTSparkErrPi[(IonAbsTdcEOA)] = TSparkErrPi;
    }

    /* End of Logic: '<S10>/Logical Operator1' */
    /* End of Outputs for SubSystem: '<S10>/open_loop_freezing' */
    /* End of Outputs for SubSystem: '<S10>/open_loop_calc' */
    /* End of Outputs for SubSystem: '<S2>/TSparkPercCalc' */
    guard1 = true;
  }

  if (guard1) {
    if (spark_fact_learning) {
      /* Transition: '<S14>:39' */
      for (rtb_FOF_Reset_S16_FXP_o2 = 0; rtb_FOF_Reset_S16_FXP_o2 < 8;
           rtb_FOF_Reset_S16_FXP_o2++) {
        VtSparkPlugFactorSamples[(rtb_FOF_Reset_S16_FXP_o2)] = 0U;
        VtSparkPlugFactorAccum[(rtb_FOF_Reset_S16_FXP_o2)] = 0U;
      }

      spark_fact_learning = false;
    } else {
      /* Transition: '<S14>:66' */
    }
  }

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_EOA' */
}

/* Model step function */
void TSparkCtrlAdat_EOA_PreCalc(void)
{
  /* local block i/o variables */
  uint16_T TSparkNom_tmp;
  uint16_T rtb_Conversion3;
  uint16_T rtb_LookUp_U16_S16;

  /* RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_EOA_PreCalc' incorporates:
   *  SubSystem: '<Root>/eoaPrecalc'
   *
   * Block description for '<Root>/eoaPrecalc':
   *  Calculates prom Map Nominal spark duration.
   */
  /* DataTypeConversion: '<S43>/Conversion3' incorporates:
   *  Inport: '<Root>/Rpm'
   */
  rtb_Conversion3 = Rpm;

  /* DataTypeConversion: '<S43>/Conversion2' incorporates:
   *  Inport: '<Root>/Load'
   */
  rtb_LookUp_U16_S16 = Load;

  /* S-Function (Look2D_U16_U16_U16): '<S43>/Look2D_U16_U16_U16' incorporates:
   *  Constant: '<S3>/BKLOADTSPARKNOM'
   *  Constant: '<S3>/BKLOADTSPARKNOM_dim'
   *  Constant: '<S3>/BKRPMTSPARKNOM'
   *  Constant: '<S3>/BKRPMTSPARKNOM_dim'
   *  Constant: '<S3>/TBTSPARKNOM'
   *
   * Block requirements for '<S3>/TBTSPARKNOM':
   *  1. EISB_FCA6CYL_SW_REQ_242: The spark duration target (i.e. signal TSparkNom) shall be estimat... (ECU_SW_Requirements#321)
   */
  Look2D_U16_U16_U16( (&(TSparkNom_tmp)), &TBTSPARKNOM[0], rtb_Conversion3,
                     &BKRPMTSPARKNOM[0], ((uint8_T)BKRPMTSPARKNOM_dim),
                     rtb_LookUp_U16_S16, &BKLOADTSPARKNOM[0], ((uint8_T)
    BKLOADTSPARKNOM_dim));

  /* S-Function (LookUp_U16_S16): '<S44>/LookUp_U16_S16' incorporates:
   *  Constant: '<S3>/BKRSATOTCORR'
   *  Constant: '<S3>/BKRSATOTCORR_dim'
   *  Constant: '<S3>/VTTSPARKNOMSACORR'
   *
   * Block requirements for '<S3>/VTTSPARKNOMSACORR':
   *  1. EISB_FCA6CYL_SW_REQ_244: The spark duration target (i.e. signal TSparkNom) shall be correct... (ECU_SW_Requirements#322)
   */
  LookUp_U16_S16( &rtb_LookUp_U16_S16, &VTTSPARKNOMSACORR[0], SATotCyl,
                 &BKRSATOTCORR[0], ((uint8_T)BKRSATOTCORR_dim));

  /* Product: '<S3>/Product'
   *
   * Block requirements for '<S3>/Product':
   *  1. EISB_FCA6CYL_SW_REQ_244: The spark duration target (i.e. signal TSparkNom) shall be correct... (ECU_SW_Requirements#322)
   */
  TSparkNomTot = (uint16_T)((((uint32_T)TSparkNom_tmp) * ((uint32_T)
    rtb_LookUp_U16_S16)) >> ((uint32_T)15));

  /* Gain: '<S3>/Gain' */
  TSparkNom = TSparkNom_tmp;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_EOA_PreCalc' */
}

/* Model step function */
void TSparkCtrlAdat_Init(void)
{
  int32_T i;

  /* RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_Init' incorporates:
   *  SubSystem: '<Root>/init'
   *
   * Block description for '<Root>/init':
   *  Initialize Pi control signals to their stored adaption signals.
   */
  /* Constant: '<S4>/ID_VER_TSPARKCTRLADAT_DEF' */
  IdVer_TSparkCtrlAdat = ID_VER_TSPARKCTRLADAT_DEF;
  for (i = 0; i < 8; i++) {
    /* Switch: '<S4>/Switch' incorporates:
     *  Constant: '<S4>/ENTSPARKADATMISFIRE'
     */
    if (!ENTSPARKADATMISFIRE) {
      VtTSparkNomOffAdEE[(i)] = 0U;
    }

    /* End of Switch: '<S4>/Switch' */

    /* Sum: '<S4>/Sum1' incorporates:
     *  Constant: '<S4>/ONE'
     */
    VtPiTSpark[(i)] = (int16_T)(((int32_T)VtIPriCorrAdEE[(i)]) - 32768);

    /* DataTypeConversion: '<S4>/Data Type Conversion' */
    VtIPriCorr[(i)] = VtIPriCorrAdEE[(i)];

    /* Constant: '<S4>/ZEROS' */
    VtTSparkErr[(i)] = 0;

    /* Constant: '<S4>/ZEROS1' */
    VtTSparkCtrlEn[(i)] = false;

    /* Constant: '<S4>/ZEROS2' */
    VtSparkPlugFactorSamples[(i)] = 0U;

    /* Constant: '<S4>/ZEROS3' */
    VtSparkPlugFactorAccum[(i)] = 0U;

    /* Constant: '<S4>/ZEROS4' */
    VtTSparkFilt[(i)] = 0U;

    /* Constant: '<S4>/ZEROS5' */
    VtTSparkFiltHR[(i)] = 0U;

    /* Constant: '<S4>/ZEROS6' */
    VtCntStMisfCycles[(i)] = 0U;
  }

  /* Constant: '<S4>/ZEROS7' */
  for (i = 0; i < 24; i++) {
    TbRingBuffMisfEvents[(i)] = 0U;
  }

  /* End of Constant: '<S4>/ZEROS7' */

  /* Constant: '<S4>/ONE1' */
  TSparkAgedFactor = 32768U;
  for (i = 0; i < 8; i++) {
    /* Constant: '<S4>/ZEROS8' */
    VtCntStMisfEvents[(i)] = 0U;

    /* Constant: '<S4>/false' */
    VtStMisfOverThr[(i)] = false;

    /* Constant: '<S4>/ZEROS10' */
    VtTSparkErrPi[(i)] = 0;
  }

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_Init' */
}

/* Model step function */
void TSparkCtrlAdat_T10ms(void)
{
  boolean_T flag_odd;
  uint8_T idx_cyl;
  int32_T rtb_MinMax1_i;
  uint16_T rtb_Look2D_U16_U16_U16;
  uint16_T rtb_Divide1_m;
  boolean_T guard1 = false;

  /* RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_T10ms' incorporates:
   *  SubSystem: '<Root>/t10ms'
   *
   * Block description for '<Root>/t10ms':
   *  Operations performed on 10ms task.
   */
  /* Chart: '<S5>/TSparkCtrlAdat_T10ms_mgm' incorporates:
   *  Constant: '<S5>/ENRESCTRLIGNCUTOFF'
   *  Constant: '<S5>/FORCESPFACTORMEM'
   *  Inport: '<Root>/IgnitionCutOffDxCAN'
   *  Inport: '<Root>/IgnitionCutOffSxCAN'
   *  Inport: '<Root>/ResetTSparkAdat'
   *  SignalConversion generated from: '<S5>/VtPiTSpark'
   *  SignalConversion generated from: '<S5>/VtTSparkErr'
   *  SignalConversion generated from: '<S5>/VtTSparkErrPi'
   *  Sum: '<S49>/Subtract3'
   */
  /* Gateway: t10ms/TSparkCtrlAdat_T10ms_mgm */
  /* During: t10ms/TSparkCtrlAdat_T10ms_mgm */
  /* Reset Adaptive learning arrays in case of external request (ResetTSparkAdat).
     Force Sparkplugs aging factor and adapt CL variables to the forced falue (only for debug).
     Reset the corrections when ignition is in cut-off state and paramater ENRESCTRLIGNCUTOFF is set. */
  /* Entry Internal: t10ms/TSparkCtrlAdat_T10ms_mgm */
  /* Transition: '<S48>:2' */
  if (FORCESPFACTORMEM) {
    /* Outputs for Function Call SubSystem: '<S5>/ForcedValues_Calc'
     *
     * Block description for '<S5>/ForcedValues_Calc':
     *  Calculates values to be forced, if forcing is enabled.
     */
    /* Product: '<S47>/Divide2' incorporates:
     *  Constant: '<S47>/TSPARKCTRLRPMMAX'
     *  Constant: '<S47>/TSPARKCTRLRPMMIN'
     *  Constant: '<S47>/two1'
     *  Sum: '<S47>/Subtract3'
     */
    /* Transition: '<S48>:8' */
    /* Event: '<S48>:24' */
    rtb_Look2D_U16_U16_U16 = (uint16_T)((((uint32_T)TSPARKCTRLRPMMAX) +
      ((uint32_T)TSPARKCTRLRPMMIN)) / ((uint32_T)((uint8_T)TWO)));

    /* Product: '<S47>/Divide1' incorporates:
     *  Constant: '<S47>/TSPARKCTRLLOADMAX'
     *  Constant: '<S47>/TSPARKCTRLLOADMIN'
     *  Constant: '<S47>/two2'
     *  DataTypeConversion: '<S47>/Data Type Conversion'
     *  Sum: '<S47>/Subtract2'
     */
    rtb_Divide1_m = (uint16_T)((((uint32_T)((uint16_T)(((uint32_T)
      TSPARKCTRLLOADMAX) + ((uint32_T)TSPARKCTRLLOADMIN)))) << ((uint32_T)7)) /
      ((uint32_T)(((uint32_T)((uint8_T)TWO)) << ((uint32_T)7))));

    /* S-Function (Look2D_U16_U16_U16): '<S50>/Look2D_U16_U16_U16' incorporates:
     *  Constant: '<S47>/BKLOADTSPARKNOM'
     *  Constant: '<S47>/BKLOADTSPARKNOM_dim'
     *  Constant: '<S47>/BKRPMTSPARKNOM'
     *  Constant: '<S47>/BKRPMTSPARKNOM_dim'
     *  Constant: '<S47>/TBTSPARKCORR'
     */
    Look2D_U16_U16_U16( &rtb_Look2D_U16_U16_U16, &TBTSPARKCORR[0],
                       rtb_Look2D_U16_U16_U16, &BKRPMTSPARKNOM[0], ((uint8_T)
      BKRPMTSPARKNOM_dim), rtb_Divide1_m, &BKLOADTSPARKNOM[0], ((uint8_T)
      BKLOADTSPARKNOM_dim));

    /* Product: '<S47>/Divide' incorporates:
     *  Constant: '<S47>/Constant3'
     *  Constant: '<S47>/FORCEDSPFACTORMEM'
     *  Sum: '<S47>/Subtract'
     */
    rtb_Look2D_U16_U16_U16 = (uint16_T)((((uint32_T)((uint16_T)((int32_T)
      (((int32_T)rtb_Look2D_U16_U16_U16) - 32768)))) * ((uint32_T)
      FORCEDSPFACTORMEM)) >> ((uint32_T)10));

    /* Sum: '<S47>/Subtract1' incorporates:
     *  Constant: '<S47>/Constant1'
     */
    rtb_Divide1_m = (uint16_T)(((uint32_T)rtb_Look2D_U16_U16_U16) + 32768U);

    /* End of Outputs for SubSystem: '<S5>/ForcedValues_Calc' */
    for (rtb_MinMax1_i = 0; rtb_MinMax1_i < 8; rtb_MinMax1_i++) {
      /* SignalConversion generated from: '<S5>/VtIPriCorrAdEE' */
      VtIPriCorrAdEE[(rtb_MinMax1_i)] = rtb_Divide1_m;

      /* Outputs for Function Call SubSystem: '<S5>/ForcedValues_Calc'
       *
       * Block description for '<S5>/ForcedValues_Calc':
       *  Calculates values to be forced, if forcing is enabled.
       */
      /* SignalConversion generated from: '<S5>/VtSparkPlugFactorEE' incorporates:
       *  Constant: '<S47>/FORCEDSPFACTORMEM'
       */
      VtSparkPlugFactorEE[(rtb_MinMax1_i)] = FORCEDSPFACTORMEM;

      /* End of Outputs for SubSystem: '<S5>/ForcedValues_Calc' */

      /* SignalConversion generated from: '<S5>/VtPiTSpark' */
      VtPiTSpark[(rtb_MinMax1_i)] = (int16_T)rtb_Look2D_U16_U16_U16;

      /* SignalConversion generated from: '<S5>/VtTSparkNomOffAdEE' */
      VtTSparkNomOffAdEE[(rtb_MinMax1_i)] = 0U;
    }

    /* Transition: '<S48>:12' */
  } else {
    /* Transition: '<S48>:9' */
    if (ResetTSparkAdat) {
      /* Transition: '<S48>:4' */
      for (rtb_MinMax1_i = 0; rtb_MinMax1_i < 8; rtb_MinMax1_i++) {
        /* SignalConversion generated from: '<S5>/VtIPriCorrAdEE' */
        VtIPriCorrAdEE[(rtb_MinMax1_i)] = 32768U;

        /* SignalConversion generated from: '<S5>/VtSparkPlugFactorEE' */
        VtSparkPlugFactorEE[(rtb_MinMax1_i)] = 0U;

        /* SignalConversion generated from: '<S5>/VtPiTSpark' */
        VtPiTSpark[(rtb_MinMax1_i)] = 0;
        VtIPriCorr[(rtb_MinMax1_i)] = 32768U;

        /* SignalConversion generated from: '<S5>/VtTSparkNomOffAdEE' */
        VtTSparkNomOffAdEE[(rtb_MinMax1_i)] = 0U;
      }
    } else {
      /* Transition: '<S48>:10' */
      idx_cyl = 0U;
      flag_odd = false;
      while (((ENRESCTRLIGNCUTOFF) && (idx_cyl < N_CYL_MAX)) &&
             ((IgnitionCutOffDxCAN) || (IgnitionCutOffSxCAN))) {
        /* Transition: '<S48>:33' */
        guard1 = false;
        if (flag_odd) {
          /* Transition: '<S48>:37' */
          if (IgnitionCutOffDxCAN) {
            /* Transition: '<S48>:35' */
            guard1 = true;
          } else {
            /* Transition: '<S48>:49' */
            /* Transition: '<S48>:45' */
          }
        } else {
          /* Transition: '<S48>:39' */
          if (IgnitionCutOffSxCAN) {
            /* Transition: '<S48>:40' */
            guard1 = true;
          } else {
            /* Transition: '<S48>:44' */
          }
        }

        if (guard1) {
          /* Outputs for Function Call SubSystem: '<S5>/reset_ctrl'
           *
           * Block description for '<S5>/reset_ctrl':
           *  Reset the correction parameter using a rate limiter to reach the
           *  neutral value of 1.
           */
          /* Sum: '<S49>/Subtract2' incorporates:
           *  Constant: '<S49>/Constant1'
           */
          /* Transition: '<S48>:42' */
          /* Event: '<S48>:53' */
          rtb_MinMax1_i = 32768 - ((int32_T)VtIPriCorr[(idx_cyl)]);

          /* MinMax: '<S49>/MinMax' incorporates:
           *  Constant: '<S49>/TSPARKRSTCORRRATEMAX'
           *  DataTypeConversion: '<S49>/Data Type Conversion'
           */
          if (((int32_T)TSPARKRSTCORRRATEMAX) < rtb_MinMax1_i) {
            rtb_MinMax1_i = (int32_T)TSPARKRSTCORRRATEMAX;
          }

          /* MinMax: '<S49>/MinMax1' incorporates:
           *  Constant: '<S49>/TSPARKRSTCORRRATEMIN'
           *  DataTypeConversion: '<S49>/Data Type Conversion1'
           *  MinMax: '<S49>/MinMax'
           */
          if (rtb_MinMax1_i <= ((int32_T)TSPARKRSTCORRRATEMIN)) {
            rtb_MinMax1_i = (int32_T)TSPARKRSTCORRRATEMIN;
          }

          /* End of MinMax: '<S49>/MinMax1' */
          VtIPriCorr[(idx_cyl)] = (uint16_T)((int32_T)(rtb_MinMax1_i + ((int32_T)
            VtIPriCorr[(idx_cyl)])));

          /* End of Outputs for SubSystem: '<S5>/reset_ctrl' */
          VtPiTSpark[(idx_cyl)] = 0;
          VtTSparkErr[(idx_cyl)] = 0;
          VtTSparkErrPi[(idx_cyl)] = 0;

          /* Transition: '<S48>:45' */
        }

        /* Transition: '<S48>:46' */
        idx_cyl += ((uint8_T)ONE);
        if (flag_odd) {
          /* Transition: '<S48>:66' */
          flag_odd = false;
        } else {
          /* Transition: '<S48>:65' */
          flag_odd = true;
        }
      }

      /* Transition: '<S48>:48' */
    }
  }

  /* End of Chart: '<S5>/TSparkCtrlAdat_T10ms_mgm' */
  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_T10ms' */
}

/* Model initialize function */
void TSparkCtrlAdat_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_Init' incorporates:
   *  SubSystem: '<Root>/init'
   *
   * Block description for '<Root>/init':
   *  Initialize Pi control signals to their stored adaption signals.
   */
  /* Start for Constant: '<S4>/ID_VER_TSPARKCTRLADAT_DEF' */
  IdVer_TSparkCtrlAdat = ID_VER_TSPARKCTRLADAT_DEF;

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/TSparkCtrlAdat_Init' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

uint16_T TSparkNomTot;
uint16_T VtTSparkFilt[N_CYL_MAX];
uint16_T VtIPriCorr[N_CYL_MAX];
void TSparkCtrlAdat_initialize(void);
void TSparkCtrlAdat_EOA(void);
void TSparkCtrlAdat_EOA_PreCalc(void);
void TSparkCtrlAdat_Init(void);
void TSparkCtrlAdat_T10ms(void);
void TSparkCtrlAdat_initialize(void)
{
  uint8_T index;
  TSparkNomTot= 0U;
  for (index=0U;index<N_CYL_MAX;index++) {
    VtTSparkFilt[index]= 0U;
    VtIPriCorr[index]= 32768U;
  }
}

void TSparkCtrlAdat_EOA(void)
{
}

void TSparkCtrlAdat_EOA_PreCalc(void)
{
}

void TSparkCtrlAdat_Init(void)
{
  TSparkCtrlAdat_initialize();
}

void TSparkCtrlAdat_T10ms(void)
{
}

#endif                                 /*_BUILD_TSPARKCTRLADAT_*/

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * simulink_requirements                                                      *
 * sl_verification_validation                                                 *
 * stateflow                                                                  *
 *============================================================================*/