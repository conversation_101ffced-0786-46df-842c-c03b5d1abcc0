/****************************************************************************
*
* Copyright © 2018-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_dpll_cfg.h
 * @brief   GTM DPLL Driver configuration macros and structures.
 *
 * @addtogroup DPLL
 * @{
 */

#ifndef _GTM_DPLL_CFG_H_
#define _GTM_DPLL_CFG_H_

#include "gtm_dpll.h"

/*lint -e621*/

/*
 * Enabled Interrupts
 */
#define SPC5_DPLL0_TRIGGER_DIRECTION_CHANGE_ENABLED                       FALSE
#define SPC5_DPLL0_ENABLE_DISABLE_ENABLED                                 FALSE
#define SPC5_DPLL0_TRIGGER_MIN_HOLD_TIME_VIOLATION_ENABLED                FALSE
#define SPC5_DPLL0_TRIGGER_MAX_HOLD_TIME_VIOLATION_ENABLED                FALSE
#define SPC5_DPLL0_STATE_INACTIVE_SLOPE_DETECTED_ENABLED                  FALSE
#define SPC5_DPLL0_TRIGGER_INACTIVE_SLOPE_DETECTED_ENABLED                FALSE
#define SPC5_DPLL0_MISSING_STATE_ENABLED                                  FALSE
#define SPC5_DPLL0_MISSING_TRIGGER_ENABLED                                FALSE
#define SPC5_DPLL0_STATE_ACTIVE_SLOPE_DETECTION_ENABLED                   FALSE
#define SPC5_DPLL0_TRIGGER_ACTIVE_SLOPE_DETECTION_ENABLED                 FALSE
#define SPC5_DPLL0_TRIGGER_PLAUSIBILITY_WINDOW_REQUEST_VIOLATION_ENABLED  FALSE
#define SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_2_ENABLED                      FALSE
#define SPC5_DPLL0_WRITE_ACCESS_RAM_REGION_1C_1B_ENABLED                  FALSE
#define SPC5_DPLL0_SUB_INC1_GET_LOCK_ENABLED                              TRUE
#define SPC5_DPLL0_SUB_INC1_LOSS_LOCK_ENABLED                             TRUE
#define SPC5_DPLL0_ERROR_INTERRUPT_ENABLED                                FALSE
#define SPC5_DPLL0_SUB_INC2_GET_LOCK_ENABLED                              FALSE
#define SPC5_DPLL0_SUB_INC2_LOSS_LOCK_ENABLED                             FALSE
#define SPC5_DPLL0_TRIGGER_EVENT_0_ENABLED                                FALSE
#define SPC5_DPLL0_TRIGGER_EVENT_1_ENABLED                                FALSE
#define SPC5_DPLL0_TRIGGER_EVENT_2_ENABLED                                FALSE
#define SPC5_DPLL0_TRIGGER_EVENT_3_ENABLED                                FALSE
#define SPC5_DPLL0_TRIGGER_EVENT_4_ENABLED                                FALSE
#define SPC5_DPLL0_TRIGGER_CALCULATED_DURATION_ENABLED                    FALSE
#define SPC5_DPLL0_STATE_CALCULATED_DURATION_ENABLED                      FALSE
#define SPC5_DPLL0_TRIGGER_OUT_OF_RANGE_ENABLED                           FALSE
#define SPC5_DPLL0_STATE_OUT_OF_RANGE_ENABLED                             FALSE

/*
 * Interrupt callbacks
 */
extern void dpll_sub_inc1_get_lock(GTM_DPLLDriver *dplld, uint32_T int_num);
extern void dpll_sub_inc1_loss_lock(GTM_DPLLDriver *dplld, uint32_T int_num);

extern gtm_dpll_callback_t gtm_dpll_callbacks[27];

/*lint +e621*/
#endif /* _GTM_DPLL_CFG_H_ */
/** @} */
