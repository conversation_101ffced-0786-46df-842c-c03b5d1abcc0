/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Safety Manager
**  Filename        :  CMUCheckSM_MCU_r_xx.h
**  Created on      :  07-Feb-2022 12:22:00
**  Original author :  Mocci A
******************************************************************************/

#ifndef CMU_CHECK_SM_MCU_R_XX_H
#define CMU_CHECK_SM_MCU_R_XX_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "CMUCheckSM_MCU_r_xx_out.h"
#include <stdint.h>
#include <Reg_eSys_FCCU.h>
#include "SafetyMngr.h"
#include "CommLib.h"
#include <Mcu_Cfg.h>
#include "SafetyMngr_CDD_MemMap.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
/** disable (0), enable (1) FCCU state check
 * \note to get a FCCU state change the corresponding bits in FCCU_RF_CFG1_R,
 *       FCCU_RF_E1_R, FCCU_RF_TOE1_R. But currently that leads to a stop of
 *       the CPU.
 *       Therefore the test is deactivated.
 */

/** simple define for 0 */
#define ZERO                        ((uint32_T)0)
/** to shift a byte to the high byte of the low word of a 32 byte value */
#define CMU_ID_2_HIGH_WORD          ((uint32_T)16)
/** value to calculate 5% */
#define PERCENTAGE_DIVIDER          ((uint32_T)100)
#define ACCURACY                    ((uint32_T)5)
#define ACCURACY_MINUS              (PERCENTAGE_DIVIDER - ACCURACY)
#define ACCURACY_PLUS               (PERCENTAGE_DIVIDER + ACCURACY)

/** CMU_TIMEOUT_MAX_LOOP is necessary to AVOID ENDLESS LOOP if CPU never
 *  set the corresponding bit(s) to expected value - no info from HW */
#define CMU_TIMEOUT_MAX_LOOP   20L     /* arbitrary value */
#if (CMU_TIMEOUT_MAX_LOOP == 0)
#error "max value has to be > 0"
#endif

/** FCCU_TIMEOUT_MAX_LOOP is necessary to AVOID ENDLESS LOOP if CPU never
 *  set the corresponding bit(s) to expected value - no info from HW */
#define FCCU_TIMEOUT_MAX_LOOP   200L    /* arbitrary value */
#if (FCCU_TIMEOUT_MAX_LOOP == 0)
#error "max value has to be > 0"
#endif



/** Threshold values for the CMU fake fault injection
 *  @{  */
#define CMU_LFREF_FAKE_VALUE        ((uint32_T)0UL)   /**< CMU low frequency reference */
#define CMU_HFREF_FAKE_VALUE        ((uint32_T)10UL)  /**< CMU high frequency reference */
/** @}  */

/** \defgroup CMU_REG CMU Registers
 *  \ingroup CMUInitialCheck
 *  @{  */
/** Number of available CMUs of the controller */
#define TOTAL_NO_OF_CMUS  ((uint32_T)14UL)
/** CMU register addresses */
/** CMU Control Status Register (CMU_CSR) */
#define CMU_CSR     ((uint32_T)0x0000)
/** CMU Frequency Display Register (CMU_FDR) - only for CMU 0 */
#define CMU_FDR     ((uint32_T)0x0004)
/** CMU High Frequency Reference Register CLKMN1 (CMU_HFREFR) */
#define CMU_HFREFR  ((uint32_T)0x0008)
/** CMU Low Frequency Reference Register CLKMN1 (CMU_LFREFR) */
#define CMU_LFREFR  ((uint32_T)0x000C)
/** CMU Interrupt Status Register (CMU_ISR) */
#define CMU_ISR     ((uint32_T)0x0010)
/** CMU Measurement Duration Register (CMU_MDR) - only for CMU 0*/
#define CMU_MSR     ((uint32_T)0x0018)
/** @}  */

/** \defgroup CSRMask Bit masks CMU Control Status Register (CMU_CSR)
 *  \ingroup CMU_REG
 *  @{  */
/** CMU_CSR.CME if set (0x00000001) => CLKMN1 monitor enable
 * \note CMU_HFREF and CMU_LFREF registers must be written before enabling the
 *       clock monitoring (CSR[CME] =1) */
#define CMU_CSR_CME   ((uint32_T)0x00000001UL)
#define CMU_CSR_CME_SHIFT_TO_COMFIG    ((uint32_T)26)

/** CLKMT0_RMN division factor. These bits specify the CLKMT0_RMN division
 *  factor. The output clock frequency is fCLKMT0_RMN divided by the factor
 *  2^RCDIV. This output clock is used as reference clock to compare with
 *  CLKMN0_RMT for crystal clock monitor feature.
 *  Ensure clock source for CLKMN0_RMT is off before writing CMU_CSR[RCDIV].
 *  The clock division coding is as follows:
 *    - 00 CLKMT0_RMN divided by 1 (No division).
 *    - 01 CLKMT0_RMN divided by 2.
 *    - 10 CLKMT0_RMN divided by 4.
 *    - 11 CLKMT0_RMN divided by 8.
 */
#define CMU_CSR_RCDIV_MASK    ((uint32_T)0x00000006UL)
#define CMU_CSR_RCDIV_SHIFT_TO_COMFIG  ((uint32_T)23)

/** Frequency measure clock selection bit. CKSEL1 selects the clock to be
 *  measured by the frequency meter. This only effects CMU instances that
 *  utilizes clock metering.
 *  Not all CMU blocks will utilize this feature. See the Clocking chapter
 *  for device specific CMU implementation details.
 *    - 00 CLKMT0_RMN is selected.
 *    - 01 Reserved
 *    - 10 Reserved.
 *    - 11 CLKMT0_RMN is selected.
 */
#define CMU_CSR_CKSEL1_MASK   ((uint32_T)0x00000300UL)
/** Start Frequency Measure. The software can only set this bit to start a
 *  clock frequency measmeasure. It is reset by hardware when the measure is
 *  ready in the CMU_FDR register.
 *  \note MDR[MD] must be written before enabling frequency measurement
 *        Do not write MDR[MD] while CSR[SFM] = 1.
 */
#define CMU_CSR_SFM   ((uint32_T)0x00800000UL)
/** @}  */

/** \defgroup FREFMask CMU High/Low Frequency Reference Register CLKMN1
 *            (CMU_HFREFR) and (CMU_LFREFR)
 *  \ingroup CMU_REG
 *  @{  */
/** bit mask for HFREF in register CMU_HFREFR*/
#define CMU_HFREFR_MASK             ((uint32_T)0x0FFFUL)
#define CMU_HFREF_SHIFT_TO_COMFIG   ((uint32_T)0UL)
/** bit mask for LFREF in register CMU_LFREFR*/
#define CMU_LFREFR_MASK             ((uint32_T)0x0FFFUL)
#define CMU_LFREF_SHIFT_TO_COMFIG   ((uint32_T)12UL)
/** @}  */

/** \defgroup ISRMask Bit masks CMU Interrupt Status Register (CMU_ISR)
 *  \ingroup CMU_REG
 *  @{  */
/** Oscillator frequency less than fCLKMT0_RMN / 2^RCDIV event status */
#define CMU_ISR_OLRI  ((uint32_T)0x00000002UL)
/** CLKMN1 frequency less than low reference event status */
#define CMU_ISR_FLLI  ((uint32_T)0x00000002UL)
/** CLKMN1 frequency higher than high reference event status */
#define CMU_ISR_FHHI  ((uint32_T)0x00000004UL)
/** @}  */

/** \defgroup MSRMask (Bits 12:31) MU Measurement Duration Register (CMU_MDR)
 *  \ingroup CMU_REG
 *  \details This field displays the measurement duration in terms of selected
 *           clock (CLKMT0_RMN) cycles. This value is loaded in the frequency
 *           meter down-counter.
 *           Measurement duration value -> larger value = higher precession
 *  @{  */
/** Measurement duration value */
#define CMU0_MSR_MD           ((uint32_T)256)
#define CMU0_FDR_FD_EXPECTED  ((CMU0_MSR_MD * (uint32_T)25) / (uint32_T)10)
#define CMU0_FDR_FD_MAX       ((CMU0_FDR_FD_EXPECTED * ACCURACY_PLUS) / PERCENTAGE_DIVIDER)
#define CMU0_FDR_FD_MIN       ((CMU0_FDR_FD_EXPECTED * ACCURACY_MINUS) / PERCENTAGE_DIVIDER)
/** @}  */

/** \defgroup FCCU_DEF Fault Collection and Control Unit (FCCU)
 *  \ingroup CMUInitialCheck
 * @{  */
/** Transition Unlocking Key value, 0xBC Configuration unlocked */
#define FCCU_TRANS_UNLOCK_KEY       ((uint32_T)0xBC)
/** Control register key, Key for the operation OP1 */
#define FCCU_TRANS_TO_CONFIG        ((uint32_T)0x913756AFUL)
/** Control register key, Key for the operation OP2 */
#define FCCU_TRANS_TO_NORMAL        ((uint32_T)0x825A132BUL)
/** @}  */


/** \defgroup FCCU_CTRL FCCU Control Register (FCCU_CTRL)
 *  \ingroup FCCU_DEF
 * @{  */
/** 00001 Set the FCCU into the CONFIG state [OP1] */
#define FCCU_CTRL_OP1               ((uint32_T)0x00000001UL)
/** 00010 Set the FCCU into the NORMAL state [OP2] */
#define FCCU_CTRL_OP2               ((uint32_T)0x00000002UL)
/** 00011 Read the FCCU state (see the FCCU_STAT register) [OP3] */
#define FCCU_CTRL_OP3               ((uint32_T)0x00000003UL)
/** 01010 Read the RF status register (see the FCCU_RF_S register) [OP10] */
#define FCCU_CTRL_OP10              ((uint32_T)0x0000000AUL)
/** Operation status 00 Idle*/
#define FCCU_CTRL_OPS_IDLE          ((uint32_T)0x00000000UL)
/** Operation status 01 In progress*/
#define FCCU_CTRL_OPS_RUN           ((uint32_T)0x00000040UL)
/** Operation status 10 Aborted*/
#define FCCU_CTRL_OPS_ABORTED       ((uint32_T)0x00000080UL)
/** Operation status 11 Successful*/
#define FCCU_CTRL_OPS_SUCCESS       ((uint32_T)0x000000C0UL)
/** Operation status mask */
#define FCCU_CTRL_OPS_MASK          ((uint32_T)0x000000C0UL)
/** @}  */
/** \defgroup FCCU_STATUS FCCU Status Register (FCCU_STAT)
 *  \ingroup FCCU_DEF
 * @{  */
/** FCCU status mask */
#define FCCU_STATUS_MASK            ((uint32_T)0x00000007UL)
/** @}  */

/** \defgroup FCCU_CHANNEL FCCU channels
 *  \ingroup FCCU_DEF
 * @{  */
/** FCCU channel bit in FCCU RF xxx Register 1 for failure CMU_0_OSC */
#define FCCU_FI_MASK_CMU_0_OSC      ((uint32_T)(1<<(51 % 32)))
/** FCCU channel bit in FCCU RF xxx Register 1 for failure CMU_0_PLL */
#define FCCU_FI_MASK_CMU_0_PLL      ((uint32_T)(1<<(52 % 32)))
/** FCCU channel bit in FCCU RF xxx Register 1 for failure CMU_0_PLATFORM */
#define FCCU_FI_MASK_CMU_PLATFORM   ((uint32_T)(1<<(53 % 32)))
/** FCCU channel bit in FCCU RF xxx Register 1 for failure CMU_0_OTHER */
#define FCCU_FI_MASK_CMU_OTHER      ((uint32_T)(1<<(54 % 32)))
#define FCCU_FI_MASK_CMUS           (FCCU_FI_MASK_CMU_0_PLL | FCCU_FI_MASK_CMU_PLATFORM | FCCU_FI_MASK_CMU_OTHER)
/** @}  */


/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* Marco for 32 bit register read access. The macro is used for
   performance reasons
*/
#define ReadRegister32(address) \
    (*((volatile uint32_T *)(address)))

#define WriteRegister32(address, value) \
    (*((volatile uint32_T *)(address)) = (value))

#define MaskRegister32(address, mask) \
    (*((volatile uint32_T *)(address)) |= (mask))

#define ClearMaskRegister32(address, mask) \
    (*((volatile uint32_T *)(address)) &= ~(mask))

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
/// Possible FCCU Status
typedef enum FCCUStatus_e
{
    eFCCU_NORMAL,
    eFCCU_CONFIG,
    eFCCU_ALARM,
    eFCCU_FAULT,
    eFCCU_UNKNOWN_STATE
} tFccuStatus;

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static void Delay(uint32_T u32Delay);
static uint32_T WaitForFccuOperationEnd(void);
static uint32_T InjectFakeFault(uint32_T cmuBaseAddr, uint32_T fccuFiMask);
static void ClearInjectedErrors( uint32_T fccuFiMask);
static void CMUCheck_SM_MCU_3_47_Sub(uint32_T baseAddress, uint32_T configValue);

#endif // CMU_CHECK_SM_MCU_R_XX_H

/****************************************************************************
 ****************************************************************************/

