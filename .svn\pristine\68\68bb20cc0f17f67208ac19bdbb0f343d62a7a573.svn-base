/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  TASK
**  Filename        :  Task_Isr_PriTable_c0.c
**  Created on      :  24-mar-2020 12:01:00
**  Original author :  SantoroR
******************************************************************************/

#ifdef _BUILD_TASK_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "sys.h"
#include "rtwtypes.h"
#include "task.h"
#include "events.h"


#ifdef _BUILD_PIT_
#include "Pit_out.h"
#endif
#ifdef _BUILD_STM_
#include "stm_out.h"
#endif


#ifdef _BUILD_ADC_
void CFIFO_Underflow(void);
void EndOfAcqBuf_A(void);
void EndOfAcqBuf_B(void);
void EndOfAcq_A(void);
void EndOfAcq_B(void);
void ALLFIFOFault_Isr(void);
void EndOfAcquisition_450u_Isr(void);
void EndOfAcquisition_2m_Isr(void);
void EndOfAcquisition_10m_Isr(void);
void EndOfAngAcq(void);
#endif /* _BUILD_ADC_ */


static void dummy (void);

#ifdef __MWERKS__
#pragma section const_type ".isrvectbl"
#endif

#ifdef __DCC__
#pragma section const_type ".isrvectbl" ".isrvectbl"
#pragma use_section const_type IntcIsrVectorTable_c0
#endif

#ifdef __GHS__
#pragma ghs section const ".isrvectbl_c0"
#endif

#ifdef __GNU__
#pragma section const_type ".isrvectbl"
#endif

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/
#define DUMMY_FUNC      dummy
#define RESERVED        dummy

#pragma ghs nowarning 32
const uint32_T IntcIsrVectorTable_c0[1024] = 
{
        /* Interrupt vector table */
        /************************************************************************************************/
        /*************************************** OSEK ISR ************************************************/
        /************************************************************************************************/
        (uint32_T) INTC_SSCIR0_ISR_C0,                /* Interrupt no. 0     - ivINT_SSCIR0_CLR0    */
        (uint32_T) INTC_SSCIR1_ISR_C0,                /* Interrupt no. 1     - ivINT_SSCIR1_CLR1    */
        (uint32_T) INTC_SSCIR2_ISR_C0,                /* Interrupt no. 2     - ivINT_SSCIR2_CLR2    */
        (uint32_T) INTC_SSCIR3_ISR_C0,                /* Interrupt no. 3     - ivINT_SSCIR3_CLR3    */
        (uint32_T) INTC_SSCIR4_ISR_C0,                /* Interrupt no. 4     - ivINT_SSCIR4_CLR4    */
        (uint32_T) INTC_SSCIR5_ISR_C0,                /* Interrupt no. 5     - ivINT_SSCIR5_CLR5    */
        (uint32_T) INTC_SSCIR6_ISR_C0,                /* Interrupt no. 6     - ivINT_SSCIR6_CLR6    */
        (uint32_T) INTC_SSCIR7_ISR_C0,                /* Interrupt no. 7     - ivINT_SSCIR7_CLR7    */
        (uint32_T) INTC_SSCIR8_ISR_C0,                /* Interrupt no. 8     - ivINT_SSCIR8_CLR8    */
        (uint32_T) INTC_SSCIR9_ISR_C0,                /* Interrupt no. 9     - ivINT_SSCIR9_CLR9    */
        (uint32_T) INTC_SSCIR10_ISR_C0,               /* Interrupt no. 10    - ivINT_SSCIR10_CLR10  */
        (uint32_T) INTC_SSCIR11_ISR_C0,               /* Interrupt no. 11    - ivINT_SSCIR11_CLR11  */
        (uint32_T) INTC_SSCIR12_ISR_C0,               /* Interrupt no. 12    - ivINT_SSCIR12_CLR12  */
        (uint32_T) INTC_SSCIR13_ISR_C0,               /* Interrupt no. 13    - ivINT_SSCIR13_CLR13  */
        (uint32_T) INTC_SSCIR14_ISR_C0,               /* Interrupt no. 14    - ivINT_SSCIR14_CLR14  */
        (uint32_T) INTC_SSCIR15_ISR_C0,               /* Interrupt no. 15    - ivINT_SSCIR15_CLR15  */
        (uint32_T) INTC_SSCIR16_ISR_C0,               /* Interrupt no. 16    - ivINT_SSCIR16_CLR16  */
        (uint32_T) INTC_SSCIR17_ISR_C0,               /* Interrupt no. 17    - ivINT_SSCIR17_CLR17  */
        (uint32_T) INTC_SSCIR18_ISR_C0,               /* Interrupt no. 18    - ivINT_SSCIR18_CLR18  */
        (uint32_T) INTC_SSCIR19_ISR_C0,               /* Interrupt no. 19    - ivINT_SSCIR19_CLR19  */
        (uint32_T) INTC_SSCIR20_ISR_C0,               /* Interrupt no. 20    - ivINT_SSCIR20_CLR20  */
        (uint32_T) INTC_SSCIR21_ISR_C0,               /* Interrupt no. 21    - ivINT_SSCIR21_CLR21  */
        (uint32_T) INTC_SSCIR22_ISR_C0,               /* Interrupt no. 22    - ivINT_SSCIR22_CLR22  */
        (uint32_T) INTC_SSCIR23_ISR_C0,               /* Interrupt no. 23    - ivINT_SSCIR23_CLR23  */
        (uint32_T) INTC_SSCIR24_ISR_C0,               /* Interrupt no. 24    - ivINT_SSCIR24_CLR24  */
        (uint32_T) INTC_SSCIR25_ISR_C0,               /* Interrupt no. 25    - ivINT_SSCIR25_CLR25  */
        (uint32_T) INTC_SSCIR26_ISR_C0,               /* Interrupt no. 26    - ivINT_SSCIR26_CLR26  */
        (uint32_T) INTC_SSCIR27_ISR_C0,               /* Interrupt no. 27    - ivINT_SSCIR27_CLR27  */
        (uint32_T) INTC_SSCIR28_ISR_C0,               /* Interrupt no. 28    - ivINT_SSCIR28_CLR28  */
        (uint32_T) INTC_SSCIR29_ISR_C0,               /* Interrupt no. 29    - ivINT_SSCIR29_CLR29  */
        (uint32_T) INTC_SSCIR30_ISR_C0,               /* Interrupt no. 30    - ivINT_SSCIR30_CLR30  */
        (uint32_T) INTC_SSCIR31_ISR_C0,               /* Interrupt no. 31    - ivINT_SSCIR31_CLR31  */
        /************************************************************************************************/
        /*************************************** SWT ISR ***********************************************/
        /************************************************************************************************/
        (uint32_T) SWT0_ISR_C0,                       /* Interrupt no. 32    -   */
        (uint32_T) RESERVED,                       /* Interrupt no. 33    - RESERVED  */
        (uint32_T) SWT2_ISR_C0,                       /* Interrupt no. 34    -   */
        (uint32_T) SWT3_ISR_C0,                       /* Interrupt no. 35    -   */
        /************************************************************************************************/
        /*************************************** STM ISR ***********************************************/
        /************************************************************************************************/
        (uint32_T) STM_INT_0_CIR0_ISR_C0,             /* Interrupt no. 36    -   */
        (uint32_T) STM_INT_0_CIR1_ISR_C0,             /* Interrupt no. 37    -   */
        (uint32_T) STM_INT_0_CIR2_ISR_C0,             /* Interrupt no. 38    -   */
        (uint32_T) STM_INT_0_CIR3_ISR_C0,             /* Interrupt no. 39    -   */
        (uint32_T) RESERVED,                       /* Interrupt no. 40    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 41    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 42    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 43    - RESERVED  */
        (uint32_T) STM_INT_2_CIR0_ISR_C0,             /* Interrupt no. 44    -   */
        (uint32_T) STM_INT_2_CIR1_ISR_C0,             /* Interrupt no. 45    -   */
        (uint32_T) STM_INT_2_CIR2_ISR_C0,             /* Interrupt no. 46    -   */
        (uint32_T) STM_INT_2_CIR3_ISR_C0,             /* Interrupt no. 47    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                       /* Interrupt no. 48    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 49    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 50    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 51    - RESERVED  */
        /************************************************************************************************/
        /*************************************** EDMA ISR ************************************************/
        /************************************************************************************************/
        (uint32_T) EDMA_ERL_ERR31_ERR0_ISR_C0,        /* Interrupt no. 52   - ivINT_EDMA_ERRL_ERR31_0  */
        (uint32_T) EDMA_IRQRL_INT00_ISR_C0,           /* Interrupt no. 53   - ivINT_EDMA_IRQRL_INT0  */
        (uint32_T) EDMA_IRQRL_INT01_ISR_C0,           /* Interrupt no. 54   - ivINT_EDMA_IRQRL_INT1  */
        (uint32_T) EDMA_IRQRL_INT02_ISR_C0,           /* Interrupt no. 55   - ivINT_EDMA_IRQRL_INT2  */
        (uint32_T) EDMA_IRQRL_INT03_ISR_C0,           /* Interrupt no. 56   - ivINT_EDMA_IRQRL_INT3  */
        (uint32_T) EDMA_IRQRL_INT04_ISR_C0,           /* Interrupt no. 57   - ivINT_EDMA_IRQRL_INT4  */
        (uint32_T) EDMA_IRQRL_INT05_ISR_C0,           /* Interrupt no. 58   - ivINT_EDMA_IRQRL_INT5  */
        (uint32_T) EDMA_IRQRL_INT06_ISR_C0,           /* Interrupt no. 59   - ivINT_EDMA_IRQRL_INT6  */
        (uint32_T) EDMA_IRQRL_INT07_ISR_C0,           /* Interrupt no. 60   - ivINT_EDMA_IRQRL_INT7  */
        (uint32_T) EDMA_IRQRL_INT08_ISR_C0,           /* Interrupt no. 61   - ivINT_EDMA_IRQRL_INT8  */
        (uint32_T) EDMA_IRQRL_INT09_ISR_C0,           /* Interrupt no. 62   - ivINT_EDMA_IRQRL_INT9  */
        (uint32_T) EDMA_IRQRL_INT10_ISR_C0,           /* Interrupt no. 63   - ivINT_EDMA_IRQRL_INT10  */
        (uint32_T) EDMA_IRQRL_INT11_ISR_C0,           /* Interrupt no. 64   - ivINT_EDMA_IRQRL_INT11  */
        (uint32_T) EDMA_IRQRL_INT12_ISR_C0,           /* Interrupt no. 65   - ivINT_EDMA_IRQRL_INT12  */
        (uint32_T) EDMA_IRQRL_INT13_ISR_C0,           /* Interrupt no. 66   - ivINT_EDMA_IRQRL_INT13  */
        (uint32_T) EDMA_IRQRL_INT14_ISR_C0,           /* Interrupt no. 67   - ivINT_EDMA_IRQRL_INT14  */
        (uint32_T) EDMA_IRQRL_INT15_ISR_C0,           /* Interrupt no. 68   - ivINT_EDMA_IRQRL_INT15  */
        (uint32_T) EDMA_IRQRL_INT16_ISR_C0,           /* Interrupt no. 69   - ivINT_EDMA_IRQRL_INT16  */
        (uint32_T) EDMA_IRQRL_INT17_ISR_C0,           /* Interrupt no. 70   - ivINT_EDMA_IRQRL_INT17  */
        (uint32_T) EDMA_IRQRL_INT18_ISR_C0,           /* Interrupt no. 71   - ivINT_EDMA_IRQRL_INT18  */
        (uint32_T) EDMA_IRQRL_INT19_ISR_C0,           /* Interrupt no. 72   - ivINT_EDMA_IRQRL_INT19  */
        (uint32_T) EDMA_IRQRL_INT20_ISR_C0,           /* Interrupt no. 73   - ivINT_EDMA_IRQRL_INT20  */
        (uint32_T) EDMA_IRQRL_INT21_ISR_C0,           /* Interrupt no. 74   - ivINT_EDMA_IRQRL_INT21  */
        (uint32_T) EDMA_IRQRL_INT22_ISR_C0,           /* Interrupt no. 75   - ivINT_EDMA_IRQRL_INT22  */
        (uint32_T) EDMA_IRQRL_INT23_ISR_C0,           /* Interrupt no. 76   - ivINT_EDMA_IRQRL_INT23  */
        (uint32_T) EDMA_IRQRL_INT24_ISR_C0,           /* Interrupt no. 77   - ivINT_EDMA_IRQRL_INT24  */
        (uint32_T) EDMA_IRQRL_INT25_ISR_C0,           /* Interrupt no. 78   - ivINT_EDMA_IRQRL_INT25  */
        (uint32_T) EDMA_IRQRL_INT26_ISR_C0,           /* Interrupt no. 79   - ivINT_EDMA_IRQRL_INT26  */
        (uint32_T) EDMA_IRQRL_INT27_ISR_C0,           /* Interrupt no. 80   - ivINT_EDMA_IRQRL_INT27  */
        (uint32_T) EDMA_IRQRL_INT28_ISR_C0,           /* Interrupt no. 81   - ivINT_EDMA_IRQRL_INT28  */
        (uint32_T) EDMA_IRQRL_INT29_ISR_C0,           /* Interrupt no. 82   - ivINT_EDMA_IRQRL_INT29  */
        (uint32_T) EDMA_IRQRL_INT30_ISR_C0,           /* Interrupt no. 83   - ivINT_EDMA_IRQRL_INT30  */
        (uint32_T) EDMA_IRQRL_INT31_ISR_C0,           /* Interrupt no. 84   - ivINT_EDMA_IRQRL_INT31  */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                       /* Interrupt no. 85    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 86    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 87    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 88    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 89    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 90    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 91    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 92    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 93    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 94    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 95    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 96    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 97    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 98    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 99    - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 100   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 101   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 102   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 103   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 104   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 105   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 106   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 107   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 108   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 109   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 110   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 111   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 112   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 113   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 114   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 115   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 116   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 117   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 118   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 119   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 120   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 121   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 122   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 123   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 124   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 125   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 126   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 127   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 128   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 129   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 130   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 131   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 132   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 133   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 134   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 135   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 136   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 137   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 138   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 139   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 140   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 141   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 142   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 143   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 144   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 145   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 146   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 147   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 148   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 149   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 150   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 151   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 152   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 153   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 154   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 155   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 156   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 157   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 158   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 159   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 160   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 161   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 162   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 163   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 164   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 165   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 166   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 167   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 168   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 169   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 170   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 171   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 172   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 173   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 174   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 175   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 176   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 177   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 178   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 179   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 180   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 181   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 182   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 183   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 184   - RESERVED  */
        /************************************************************************************************/
        /*************************************** FLASH ISR ***********************************************/
        /************************************************************************************************/
        (uint32_T) FLASH_SRAM_ECC_INT_ISR_C0,        /* Interrupt no. 185  - ivINT_FLASH_ECC        */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                       /* Interrupt no. 186   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 187   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 188   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 189   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 190   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 191   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 192   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 193   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 194   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 195   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 196   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 197   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 198   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 199   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 200   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 201   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 202   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 203   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 204   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 205   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 206   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 207   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 208   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 209   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 210   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 211   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 212   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 213   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 214   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 215   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 216   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 217   - RESERVED  */
        /************************************************************************************************/
        /*************************************** ETHERNET ISR *******************************************/
        /************************************************************************************************/
        (uint32_T) ETH_EIR_TX_ISR_C0,                 /* Interrupt no. 218   -   */
        (uint32_T) ETH_EIR_RX_ISR_C0,                 /* Interrupt no. 219   -   */
        (uint32_T) ETH_EIR_COMB_ISR_C0,               /* Interrupt no. 220   -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                       /* Interrupt no. 221   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 222   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 223   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 224   - RESERVED  */
        (uint32_T) RESERVED,                       /* Interrupt no. 225   - RESERVED  */
        /************************************************************************************************/
        /*************************************** PIT-RTI ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) PIT_INT0_PIT0_ISR_C0,               /* Interrupt no. 226  -        */
        (uint32_T) PIT_INT1_PIT0_ISR_C0,               /* Interrupt no. 227  -        */
        (uint32_T) PIT_INT2_PIT0_ISR_C0,               /* Interrupt no. 228  -        */
        (uint32_T) PIT_INT3_PIT0_ISR_C0,               /* Interrupt no. 229  -        */
        (uint32_T) PIT_INT4_PIT0_ISR_C0,               /* Interrupt no. 230  -        */
        (uint32_T) PIT_INT5_PIT0_ISR_C0,               /* Interrupt no. 231  -        */
        (uint32_T) RESERVED,                        /* Interrupt no. 232  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 233  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 234  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 235  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 236  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 237  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 238  - RESERVED  */
        (uint32_T) PIT_RTI_PIT0_ISR_C0,                /* Interrupt no. 239  -        */
        (uint32_T) PIT_INT0_PIT1_ISR_C0,               /* Interrupt no. 240  -        */
        (uint32_T) PIT_INT1_PIT1_ISR_C0,               /* Interrupt no. 241  -        */
        /************************************************************************************************/
        /*************************************** XOSC ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) XOSC_CTL_ISR_C0,                    /* Interrupt no. 242   - ivFMPLL_SYNSR_LOCF  */
        /************************************************************************************************/
        /*************************************** SIUL ISR *************************************************/
        /************************************************************************************************/
        (uint32_T) SIUL2_COMB_EXT0_ISR_C0,             /* Interrupt no. 243   - ivINT_SIU_OSR_OVF15_0  */
        (uint32_T) SIUL2_COMB_EXT1_ISR_C0,             /* Interrupt no. 244   - ivINT_SIU_EISR_EIF0  */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 245    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 246    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 247    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 248    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 249    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 250    - RESERVED  */
        /************************************************************************************************/
        /*************************************** MC ISR *************************************************/
        /************************************************************************************************/
        (uint32_T) MC_ME_SAFE_ISR_C0,                  /* Interrupt no. 251   -   */
        (uint32_T) MC_ME_MTC_ISR_C0,                   /* Interrupt no. 252   -   */
        (uint32_T) MC_ME_IMODE_ISR_C0,                 /* Interrupt no. 253   -   */
        (uint32_T) MC_ME_ICONF_ISR_C0,                 /* Interrupt no. 254   -   */
        (uint32_T) MC_RGM_ISR_C0,                      /* Interrupt no. 255   -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 256    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 257    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 258    - RESERVED  */
        /************************************************************************************************/
        /*************************************** DSPI ISR ***********************************************/
        /************************************************************************************************/
        (uint32_T) DSPI_0_ISR_TFUF_RFOF_ISR_C0,        /* Interrupt no. 259  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
        (uint32_T) DSPI_0_ISR_EOQF_ISR_C0,             /* Interrupt no. 260  - ivINT_DSPI_B_ISR_EOQF  */
        (uint32_T) DSPI_0_ISR_TFFF_ISR_C0,             /* Interrupt no. 261  - ivINT_DSPI_B_ISR_TFFF  */
        (uint32_T) DSPI_0_ISR_TCF_ISR_C0,              /* Interrupt no. 262  - ivINT_DSPI_B_ISR_TCF  */
        (uint32_T) DSPI_0_ISR_RFDF_ISR_C0,             /* Interrupt no. 263  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_0_ISR_CMDTCF_SPITCF_ISR_C0,    /* Interrupt no. 264  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_0_ISR_CMDFFF_ISR_C0,           /* Interrupt no. 265  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_0_ISR_SPEF_ISR_C0,             /* Interrupt no. 266  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) RESERVED,                        /* Interrupt no. 267  - RESERVED  */
        (uint32_T) DSPI_1_ISR_TFUF_RFOF_ISR_C0,        /* Interrupt no. 268  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
        (uint32_T) DSPI_1_ISR_EOQF_ISR_C0,             /* Interrupt no. 269  - ivINT_DSPI_B_ISR_EOQF  */
        (uint32_T) DSPI_1_ISR_TFFF_ISR_C0,             /* Interrupt no. 270  - ivINT_DSPI_B_ISR_TFFF  */
        (uint32_T) DSPI_1_ISR_TCF_ISR_C0,              /* Interrupt no. 271  - ivINT_DSPI_B_ISR_TCF  */
        (uint32_T) DSPI_1_ISR_RFDF_ISR_C0,             /* Interrupt no. 272  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_1_ISR_CMDTCF_SPITCF_ISR_C0,    /* Interrupt no. 273  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_1_ISR_CMDFFF_ISR_C0,           /* Interrupt no. 274  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_1_ISR_SPEF_ISR_C0,             /* Interrupt no. 275  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) RESERVED,                        /* Interrupt no. 276  - RESERVED  */
        (uint32_T) DSPI_2_ISR_TFUF_RFOF_ISR_C0,        /* Interrupt no. 277  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
        (uint32_T) DSPI_2_ISR_EOQF_ISR_C0,             /* Interrupt no. 278  - ivINT_DSPI_B_ISR_EOQF  */
        (uint32_T) DSPI_2_ISR_TFFF_ISR_C0,             /* Interrupt no. 279  - ivINT_DSPI_B_ISR_TFFF  */
        (uint32_T) DSPI_2_ISR_TCF_ISR_C0,              /* Interrupt no. 280  - ivINT_DSPI_B_ISR_TCF  */
        (uint32_T) DSPI_2_ISR_RFDF_ISR_C0,             /* Interrupt no. 281  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_2_ISR_CMDTCF_SPITCF_ISR_C0,    /* Interrupt no. 282  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_2_ISR_CMDFFF_ISR_C0,           /* Interrupt no. 283  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_2_ISR_SPEF_ISR_C0,             /* Interrupt no. 284  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) RESERVED,                        /* Interrupt no. 285  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 286  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 287  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 288  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 289  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 290  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 291  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 292  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 293  - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 294  - RESERVED  */
        (uint32_T) DSPI_4_ISR_TFUF_RFOF_ISR_C0,        /* Interrupt no. 295  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
        (uint32_T) DSPI_4_ISR_EOQF_ISR_C0,             /* Interrupt no. 296  - ivINT_DSPI_B_ISR_EOQF  */
        (uint32_T) DSPI_4_ISR_TFFF_ISR_C0,             /* Interrupt no. 297  - ivINT_DSPI_B_ISR_TFFF  */
        (uint32_T) DSPI_4_ISR_TCF_ISR_C0,              /* Interrupt no. 298  - ivINT_DSPI_B_ISR_TCF  */
        (uint32_T) DSPI_4_ISR_RFDF_ISR_C0,             /* Interrupt no. 299  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_4_ISR_CMDTCF_SPITCF_ISR_C0,    /* Interrupt no. 300  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_4_ISR_CMDFFF_DSITCF_ISR_C0,    /* Interrupt no. 301  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_4_ISR_SPEF_DPEF_ISR_C0,        /* Interrupt no. 302  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_4_ISR_DDIF_ISR_C0,             /* Interrupt no. 303  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_5_ISR_TFUF_RFOF_ISR_C0,        /* Interrupt no. 304  - ivINT_DSPI_B_ISR_TFUF_RFOF  */
        (uint32_T) DSPI_5_ISR_EOQF_ISR_C0,             /* Interrupt no. 305  - ivINT_DSPI_B_ISR_EOQF  */
        (uint32_T) DSPI_5_ISR_TFFF_ISR_C0,             /* Interrupt no. 306  - ivINT_DSPI_B_ISR_TFFF  */
        (uint32_T) DSPI_5_ISR_TCF_ISR_C0,              /* Interrupt no. 307  - ivINT_DSPI_B_ISR_TCF  */
        (uint32_T) DSPI_5_ISR_RFDF_ISR_C0,             /* Interrupt no. 308  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_5_ISR_CMDTCF_SPITCF_ISR_C0,    /* Interrupt no. 309  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_5_ISR_CMDFFF_DSITCF_ISR_C0,    /* Interrupt no. 310  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_5_ISR_SPEF_DPEF_ISR_C0,        /* Interrupt no. 311  - ivINT_DSPI_B_ISR_RFDF  */
        (uint32_T) DSPI_5_ISR_DDIF_ISR_C0,             /* Interrupt no. 312  - ivINT_DSPI_B_ISR_RFDF  */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 313    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 314    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 315    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 316    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 317    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 318    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 319    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 320    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 321    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 322    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 323    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 324    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 325    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 326    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 327    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 328    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 329    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 330    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 331    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 332    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 333    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 334    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 335    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 336    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 337    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 338    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 339    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 340    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 341    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 342    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 343    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 344    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 345    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 346    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 347    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 348    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 349    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 350    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 351    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 352    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 353    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 354    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 355    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 356    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 357    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 358    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 359    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 360    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 361    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 362    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 363    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 364    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 365    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 366    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 367    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 368    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 369    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 370    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 371    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 372    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 373    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 374    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 375    - RESERVED  */
        /************************************************************************************************/
        /*************************************** LINFLEX ISR ********************************************/
        /************************************************************************************************/
        (uint32_T) LINFLEX_0_RX_COMB_ISR_C0,           /* Interrupt no. 376    -   */
        (uint32_T) LINFLEX_0_TX_COMB_ISR_C0,           /* Interrupt no. 377    -   */
        (uint32_T) LINFLEX_0_ERROR_COMB_ISR_C0,        /* Interrupt no. 378    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 379    - RESERVED  */
        (uint32_T) LINFLEX_1_RX_COMB_ISR_C0,           /* Interrupt no. 380    -   */
        (uint32_T) LINFLEX_1_TX_COMB_ISR_C0,           /* Interrupt no. 381    -   */
        (uint32_T) LINFLEX_1_ERROR_COMB_ISR_C0,        /* Interrupt no. 382    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 383    - RESERVED  */
        (uint32_T) LINFLEX_2_RX_COMB_ISR_C0,           /* Interrupt no. 384    -   */
        (uint32_T) LINFLEX_2_TX_COMB_ISR_C0,           /* Interrupt no. 385    -   */
        (uint32_T) LINFLEX_2_ERROR_COMB_ISR_C0,        /* Interrupt no. 386    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 387    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 388    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 389    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 390    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 391    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 392    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 393    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 394    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 395    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 396    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 397    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 398    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 399    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 400    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 401    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 402    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 403    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 404    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 405    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 406    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 407    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 408    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 409    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 410    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 411    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 412    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 413    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 414    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 415    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 416    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 417    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 418    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 419    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 420    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 421    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 422    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 423    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 424    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 425    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 426    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 427    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 428    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 429    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 430    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 431    - RESERVED  */
        (uint32_T) LINFLEX_14_RX_COMB_ISR_C0,          /* Interrupt no. 432    -   */
        (uint32_T) LINFLEX_14_TX_COMB_ISR_C0,          /* Interrupt no. 433    -   */
        (uint32_T) LINFLEX_14_ERROR_COMB_ISR_C0,       /* Interrupt no. 434    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 435    - RESERVED  */
        (uint32_T) LINFLEX_15_RX_COMB_ISR_C0,          /* Interrupt no. 436    -   */
        (uint32_T) LINFLEX_15_TX_COMB_ISR_C0,          /* Interrupt no. 437    -   */
        (uint32_T) LINFLEX_15_ERROR_COMB_ISR_C0,       /* Interrupt no. 438    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 439    - RESERVED  */
        /************************************************************************************************/
        /*************************************** I2C ISR ***********************************************/
        /************************************************************************************************/
        (uint32_T) I2C_IBIF_IAAS_IBAL_ISR_C0,          /* Interrupt no. 440    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 441    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 442    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 443    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 444    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 445    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 446    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 447    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 448    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 449    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 450    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 451    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 452    - RESERVED  */
        /************************************************************************************************/
        /*************************************** FLEXRAY ISR *********************************************/
        /************************************************************************************************/
        (uint32_T)FLEXRAY_LRNE_ISR_C0,                 /* Interrupt no. 453 - ivINT_FlexRAY LRNE   */
        (uint32_T)FLEXRAY_LRCE_ISR_C0,                 /* Interrupt no. 454 - ivINT_FlexRAY LRCE   */
        (uint32_T)FLEXRAY_FAFAIF_ISR_C0,               /* Interrupt no. 455 - ivINT_FlexRAY FAFAIF */
        (uint32_T)FLEXRAY_FAFBIF_ISR_C0,               /* Interrupt no. 456 - ivINT_FlexRAY FAFBIF */
        (uint32_T)FLEXRAY_WUPIF_ISR_C0,                /* Interrupt no. 457 - ivINT_FlexRAY WUPIF  */
        (uint32_T)FLEXRAY_PRIF_ISR_C0,                 /* Interrupt no. 458 - ivINT_FlexRAY PRIF   */
        (uint32_T)FLEXRAY_CHIF_ISR_C0,                 /* Interrupt no. 459 - ivINT_FlexRAY CHIF   */
        (uint32_T)FLEXRAY_TBIF_ISR_C0,                 /* Interrupt no. 460 - ivINT_FlexRAY TBIF   */
        (uint32_T)FLEXRAY_RBIF_ISR_C0,                 /* Interrupt no. 461 - ivINT_FlexRAY RBIF   */
        (uint32_T)FLEXRAY_MIF_ISR_C0,                  /* Interrupt no. 462 - ivINT_FlexRAY MIF    */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 463    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 464    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 465    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 466    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 467    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 468    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 469    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 470    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 471    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 472    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 473    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 474    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 475    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 476    - RESERVED  */
        /************************************************************************************************/
        /*************************************** GR ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) GR_VD_ISR_C0,                       /* Interrupt no. 477   - ivGR_VD  */
        /************************************************************************************************/
        /*************************************** EPR ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) EPR_TEMP_ISR_C0,                    /* Interrupt no. 478   - ivEPR_TEMP  */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 479    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 480    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 481    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 482    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 483    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 484    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 485    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 486    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 487    - RESERVED  */
        /************************************************************************************************/
        /*************************************** FCCU ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) FCCU_ALRM_STAT_ISR_C0,              /* Interrupt no. 488   - ivFMPLL_SYNSR_LOCF  */
        (uint32_T) FCCU_CFG_TO_STAT_ISR_C0,            /* Interrupt no. 489   - ivFMPLL_SYNSR_LOLF  */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 490    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 491    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 492    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 493    - RESERVED  */
        /************************************************************************************************/
        /*************************************** STCU ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) STCU_LBIE_ISR_C0,                   /* Interrupt no. 494   - ivSTCU_LBIE  */
        (uint32_T) STCU_MBIE_ISR_C0,                   /* Interrupt no. 495   - ivSTCU_MBIE  */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 496    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 497    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 498    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 499    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 500    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 501    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 502    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 503    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 504    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 505    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 506    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 507    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 508    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 509    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 510    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 511    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 512    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 513    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 514    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 515    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 516    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 517    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 518    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 519    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 520    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 521    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 522    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 523    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 524    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 525    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 526    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 527    - RESERVED  */
        /************************************************************************************************/
        /*************************************** SAR ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) SAR_0_INT_ISR_C0,                   /* Interrupt no. 528    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 529    - RESERVED  */
        (uint32_T) SAR_2_INT_ISR_C0,                   /* Interrupt no. 530    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 531    - RESERVED  */
        (uint32_T) SAR_4_INT_ISR_C0,                   /* Interrupt no. 532    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 533    - RESERVED  */
        (uint32_T) SAR_6_INT_ISR_C0,                   /* Interrupt no. 534    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 535    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 536    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 537    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 538    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 539    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 540    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 541    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 542    - RESERVED  */
        (uint32_T) SAR_B_INT_ISR_C0,                   /* Interrupt no. 543    -   */
        /************************************************************************************************/
        /*************************************** SD ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) SD_0_INT_ISR_C0,                    /* Interrupt no. 544  - ivSD_0_INT  */
        (uint32_T) RESERVED,                        /* Interrupt no. 545    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 546    - RESERVED  */
        (uint32_T) SD_3_INT_ISR_C0,                    /* Interrupt no. 547  - ivSD_3_INT  */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 548    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 549    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 550    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 551    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 552    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 553    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 554    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 555    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 556    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 557    - RESERVED  */
        /************************************************************************************************/
        /*************************************** SENT ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) SENT_0_FAST_COMB_ISR_C0,            /* Interrupt no. 558    - ivSENT_0_FAST_COMB  */
        (uint32_T) SENT_0_SLOW_COMB_ISR_C0,            /* Interrupt no. 559    -   */
        (uint32_T) SENT_0_GBL_ERROR_ISR_C0,            /* Interrupt no. 560    -   */
        (uint32_T) SENT_1_SLOW_RDY_ISR_C0,             /* Interrupt no. 562    -   */
        (uint32_T) SENT_1_FAST_RDY_ISR_C0,             /* Interrupt no. 561    -   */
        (uint32_T) SENT_1_GBL_ERROR_ISR_C0,            /* Interrupt no. 563    -   */
        (uint32_T) SENT_0_FMSG_0_ISR_C0,               /* Interrupt no. 564    -   */
        (uint32_T) SENT_0_SMSG_0_ISR_C0,               /* Interrupt no. 565    -   */
        (uint32_T) SENT_0_ERROR_0_ISR_C0,              /* Interrupt no. 566    -   */
        (uint32_T) SENT_0_FMSG_1_ISR_C0,               /* Interrupt no. 567    -   */
        (uint32_T) SENT_0_SMSG_1_ISR_C0,               /* Interrupt no. 568    -   */
        (uint32_T) SENT_0_ERROR_1_ISR_C0,              /* Interrupt no. 569    -   */
        (uint32_T) SENT_0_FMSG_2_ISR_C0,               /* Interrupt no. 570    -   */
        (uint32_T) SENT_0_SMSG_2_ISR_C0,               /* Interrupt no. 571    -   */
        (uint32_T) SENT_0_ERROR_2_ISR_C0,              /* Interrupt no. 572    -   */
        (uint32_T) SENT_0_FMSG_3_ISR_C0,               /* Interrupt no. 573    -   */
        (uint32_T) SENT_0_SMSG_3_ISR_C0,               /* Interrupt no. 574    -   */
        (uint32_T) SENT_0_ERROR_3_ISR_C0,              /* Interrupt no. 575    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 576    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 577    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 578    - RESERVED  */
        (uint32_T) SENT_1_FMSG_0_ISR_C0,               /* Interrupt no. 579    -   */
        (uint32_T) SENT_1_SMSG_0_ISR_C0,               /* Interrupt no. 580    -   */
        (uint32_T) SENT_1_ERROR_0_ISR_C0,              /* Interrupt no. 581    -   */
        (uint32_T) SENT_1_FMSG_1_ISR_C0,               /* Interrupt no. 582    -   */
        (uint32_T) SENT_1_SMSG_1_ISR_C0,               /* Interrupt no. 583    -   */
        (uint32_T) SENT_1_ERROR_1_ISR_C0,              /* Interrupt no. 584    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 585    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 586    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 587    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 588    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 589    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 590    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 591    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 592    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 593    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 594    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 595    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 596    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 597    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 598    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 599    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 600    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 601    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 602    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 603    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 604    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 605    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 606    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 607    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 608    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 609    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 610    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 611    - RESERVED  */
        /************************************************************************************************/
        /*************************************** PSI ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) PSI5_0_DMA_0_ISR_C0,                /* Interrupt no. 612    -   */
        (uint32_T) PSI5_0_GEN_0_ISR_C0,                /* Interrupt no. 613    -   */
        (uint32_T) PSI5_0_NEW_MSG_0_ISR_C0,            /* Interrupt no. 614    -   */
        (uint32_T) PSI5_0_MSG_OW_0_ISR_C0,             /* Interrupt no. 615    -   */
        (uint32_T) PSI5_0_ERROR_COMB_0_ISR_C0,         /* Interrupt no. 616    -   */
        (uint32_T) PSI5_0_GLOBAL_0_ISR_C0,             /* Interrupt no. 617    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 618    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 619    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 620    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 621    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 622    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 623    - RESERVED  */
        (uint32_T) PSI5_1_DMA_0_ISR_C0,                /* Interrupt no. 624    -   */
        (uint32_T) PSI5_1_GEN_0_ISR_C0,                /* Interrupt no. 625    -   */
        (uint32_T) PSI5_1_NEW_MSG_0_ISR_C0,            /* Interrupt no. 626    -   */
        (uint32_T) PSI5_1_MSG_OW_0_ISR_C0,             /* Interrupt no. 627    -   */
        (uint32_T) PSI5_1_ERROR_COMB_0_ISR_C0,         /* Interrupt no. 628    -   */
        (uint32_T) PSI5_1_GLOBAL_0_ISR_C0,             /* Interrupt no. 629    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 630    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 631    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 632    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 633    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 634    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 635    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 636    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 637    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 638    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 639    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 640    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 641    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 642    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 643    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 644    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 645    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 646    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 647    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 648    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 649    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 650    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 651    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 652    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 653    - RESERVED  */
        /************************************************************************************************/
        /*************************************** SIPI ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) SIPI_ERROR_COMB_ISR_C0,             /* Interrupt no. 654    -   */
        (uint32_T) SIPI_CRC_ERROR_ISR_C0,              /* Interrupt no. 655    -   */
        (uint32_T) SIPI_CH0_RX_ISR_C0,                 /* Interrupt no. 656    -   */
        (uint32_T) SIPI_CH1_RX_ISR_C0,                 /* Interrupt no. 657    -   */
        (uint32_T) SIPI_CH2_RX_ISR_C0,                 /* Interrupt no. 658    -   */
        (uint32_T) SIPI_CH3_RX_ISR_C0,                 /* Interrupt no. 659    -   */
        (uint32_T) SIPI_EVENT_COMB_ISR_C0,             /* Interrupt no. 660    -   */
        /************************************************************************************************/
        /*************************************** LFAST ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) LFAST_0_TX_ISR_C0,                  /* Interrupt no. 661    -   */
        (uint32_T) LFAST_0_TX_ERROR_ISR_C0,            /* Interrupt no. 662    -   */
        (uint32_T) LFAST_0_RX_ISR_C0,                  /* Interrupt no. 663    -   */
        (uint32_T) LFAST_0_RX_ERROR_ISR_C0,            /* Interrupt no. 664    -   */
        (uint32_T) LFAST_0_ICLC_RX_ISR_C0,             /* Interrupt no. 665    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 666    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 667    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 668    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 669    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 670    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 671    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 672    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 673    - RESERVED  */
        /************************************************************************************************/
        /*************************************** JTAG ISR ***********************************************/
        /************************************************************************************************/
        (uint32_T) JTAG_GM_ISR_C0,                     /* Interrupt no. 674    -   */
        (uint32_T) JTAG_DC_ISR_C0,                     /* Interrupt no. 675    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 676    - RESERVED  */
        /************************************************************************************************/
        /*************************************** M_TTCAN ISR ********************************************/
        /************************************************************************************************/
        (uint32_T) M_TTCAN_LINE0_ISR_C0,               /* Interrupt no. 677    -   */
        (uint32_T) M_TTCAN_LINE1_ISR_C0,               /* Interrupt no. 678    -   */
        (uint32_T) M_TTCAN_RTMI_ISR_C0,                /* Interrupt no. 679    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 680    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 681    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 682    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 683    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 684    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 685    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 686    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 687    - RESERVED  */
        /************************************************************************************************/
        /**************************************** M_CAN ISR *********************************************/
        /************************************************************************************************/
        (uint32_T) MCAN1_LINE0_ISR_C0,                 /* Interrupt no. 688    -   */
        (uint32_T) MCAN1_LINE1_ISR_C0,                 /* Interrupt no. 689    -   */
        (uint32_T) MCAN2_LINE0_ISR_C0,                 /* Interrupt no. 690    -   */
        (uint32_T) MCAN2_LINE1_ISR_C0,                 /* Interrupt no. 691    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 692    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 693    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 694    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 695    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 696    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 697    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 698    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 699    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 700    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 701    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 702    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 703    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 704    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 705    - RESERVED  */
        /************************************************************************************************/
        /*************************************** GTM ISR **********************************************/
        /************************************************************************************************/
        (uint32_T) GTM_AEI_ISR_C0,                     /* Interrupt no. 706    -   */
        (uint32_T) GTM_ARU_NEW_DATA0_ISR_C0,           /* Interrupt no. 707    -   */
        (uint32_T) GTM_ARU_NEW_DATA1_ISR_C0,           /* Interrupt no. 708    -   */
        (uint32_T) GTM_ARU_ACC_ACK_ISR_C0,             /* Interrupt no. 709    -   */
        (uint32_T) GTM_BRC_ISR_C0,                     /* Interrupt no. 710    -   */
        (uint32_T) GTM_CMP_ISR_C0,                     /* Interrupt no. 711    -   */
        (uint32_T) GTM_SPE0_ISR_C0,                    /* Interrupt no. 712    -   */
        (uint32_T) GTM_SPE1_ISR_C0,                    /* Interrupt no. 713    -   */
        (uint32_T) GTM_PSM0_CH0_ISR_C0,                /* Interrupt no. 714    -   */
        (uint32_T) GTM_PSM0_CH1_ISR_C0,                /* Interrupt no. 715    -   */
        (uint32_T) GTM_PSM0_CH2_ISR_C0,                /* Interrupt no. 716    -   */
        (uint32_T) GTM_PSM0_CH3_ISR_C0,                /* Interrupt no. 717    -   */
        (uint32_T) GTM_PSM0_CH4_ISR_C0,                /* Interrupt no. 718    -   */
        (uint32_T) GTM_PSM0_CH5_ISR_C0,                /* Interrupt no. 719    -   */
        (uint32_T) GTM_PSM0_CH6_ISR_C0,                /* Interrupt no. 720    -   */
        (uint32_T) GTM_PSM0_CH7_ISR_C0,                /* Interrupt no. 721    -   */
        (uint32_T) GTM_DPLL_DCGI_ISR_C0,               /* Interrupt no. 722    -   */
        (uint32_T) GTM_DPLL_EDI_ISR_C0,                /* Interrupt no. 723    -   */
        (uint32_T) GTM_DPLL_TINI_ISR_C0,               /* Interrupt no. 724    -   */
        (uint32_T) GTM_DPLL_TAXI_ISR_C0,               /* Interrupt no. 725    -   */
        (uint32_T) GTM_DPLL_SISI_ISR_C0,               /* Interrupt no. 726    -   */
        (uint32_T) GTM_DPLL_TISI_ISR_C0,               /* Interrupt no. 727    -   */
        (uint32_T) GTM_DPLL_MSI_ISR_C0,                /* Interrupt no. 728    -   */
        (uint32_T) GTM_DPLL_MTI_ISR_C0,                /* Interrupt no. 729    -   */
        (uint32_T) GTM_DPLL_SASI_ISR_C0,               /* Interrupt no. 730    -   */
        (uint32_T) GTM_DPLL_TASI_ISR_C0,               /* Interrupt no. 731    -   */
        (uint32_T) GTM_DPLL_PWI_ISR_C0,                /* Interrupt no. 732    -   */
        (uint32_T) GTM_DPLL_W2I_ISR_C0,                /* Interrupt no. 733    -   */
        (uint32_T) GTM_DPLL_W1I_ISR_C0,                /* Interrupt no. 734    -   */
        (uint32_T) GTM_DPLL_GL1I_ISR_C0,               /* Interrupt no. 735    -   */
        (uint32_T) GTM_DPLL_LL1I_ISR_C0,               /* Interrupt no. 736    -   */
        (uint32_T) GTM_DPLL_EI_ISR_C0,                 /* Interrupt no. 737    -   */
        (uint32_T) GTM_DPLL_GL2I_ISR_C0,               /* Interrupt no. 738    -   */
        (uint32_T) GTM_DPLL_LL2I_ISR_C0,               /* Interrupt no. 739    -   */
        (uint32_T) GTM_DPLL_TE0I_ISR_C0,               /* Interrupt no. 740    -   */
        (uint32_T) GTM_DPLL_TE1I_ISR_C0,               /* Interrupt no. 741    -   */
        (uint32_T) GTM_DPLL_TE2I_ISR_C0,               /* Interrupt no. 742    -   */
        (uint32_T) GTM_DPLL_TE3I_ISR_C0,               /* Interrupt no. 743    -   */
        (uint32_T) GTM_DPLL_TE4I_ISR_C0,               /* Interrupt no. 744    -   */
        (uint32_T) GTM_DPLL_CDTI_ISR_C0,               /* Interrupt no. 745    -   */
        (uint32_T) GTM_DPLL_CDSI_ISR_C0,               /* Interrupt no. 746    -   */
        (uint32_T) GTM_DPLL_TORI_ISR_C0,               /* Interrupt no. 747    -   */
        (uint32_T) GTM_DPLL_SORI_ISR_C0,               /* Interrupt no. 748    -   */
        (uint32_T) GTM_TIM0_CH0_ISR_C0,                /* Interrupt no. 749    -   */
        (uint32_T) GTM_TIM0_CH1_ISR_C0,                /* Interrupt no. 750    -   */
        (uint32_T) GTM_TIM0_CH2_ISR_C0,                /* Interrupt no. 751    -   */
        (uint32_T) GTM_TIM0_CH3_ISR_C0,                /* Interrupt no. 752    -   */
        (uint32_T) GTM_TIM0_CH4_ISR_C0,                /* Interrupt no. 753    -   */
        (uint32_T) GTM_TIM0_CH5_ISR_C0,                /* Interrupt no. 754    -   */
        (uint32_T) GTM_TIM0_CH6_ISR_C0,                /* Interrupt no. 755    -   */
        (uint32_T) GTM_TIM0_CH7_ISR_C0,                /* Interrupt no. 756    -   */
        (uint32_T) GTM_TIM1_CH0_ISR_C0,                /* Interrupt no. 757    -   */
        (uint32_T) GTM_TIM1_CH1_ISR_C0,                /* Interrupt no. 758    -   */
        (uint32_T) GTM_TIM1_CH2_ISR_C0,                /* Interrupt no. 759    -   */
        (uint32_T) GTM_TIM1_CH3_ISR_C0,                /* Interrupt no. 760    -   */
        (uint32_T) GTM_TIM1_CH4_ISR_C0,                /* Interrupt no. 761    -   */
        (uint32_T) GTM_TIM1_CH5_ISR_C0,                /* Interrupt no. 762    -   */
        (uint32_T) GTM_TIM1_CH6_ISR_C0,                /* Interrupt no. 763    -   */
        (uint32_T) GTM_TIM1_CH7_ISR_C0,                /* Interrupt no. 764    -   */
        (uint32_T) GTM_TIM2_CH0_ISR_C0,                /* Interrupt no. 765    -   */
        (uint32_T) GTM_TIM2_CH1_ISR_C0,                /* Interrupt no. 766    -   */
        (uint32_T) GTM_TIM2_CH2_ISR_C0,                /* Interrupt no. 767    -   */
        (uint32_T) GTM_TIM2_CH3_ISR_C0,                /* Interrupt no. 768    -   */
        (uint32_T) GTM_TIM2_CH4_ISR_C0,                /* Interrupt no. 769    -   */
        (uint32_T) GTM_TIM2_CH5_ISR_C0,                /* Interrupt no. 770    -   */
        (uint32_T) GTM_TIM2_CH6_ISR_C0,                /* Interrupt no. 771    -   */
        (uint32_T) GTM_TIM2_CH7_ISR_C0,                /* Interrupt no. 772    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 773    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 774    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 775    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 776    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 777    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 778    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 779    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 780    - RESERVED  */
        (uint32_T) GTM_MCS0_CH0_ISR_C0,                /* Interrupt no. 781    -   */
        (uint32_T) GTM_MCS0_CH1_ISR_C0,                /* Interrupt no. 782    -   */
        (uint32_T) GTM_MCS0_CH2_ISR_C0,                /* Interrupt no. 783    -   */
        (uint32_T) GTM_MCS0_CH3_ISR_C0,                /* Interrupt no. 784    -   */
        (uint32_T) GTM_MCS0_CH4_ISR_C0,                /* Interrupt no. 785    -   */
        (uint32_T) GTM_MCS0_CH5_ISR_C0,                /* Interrupt no. 786    -   */
        (uint32_T) GTM_MCS0_CH6_ISR_C0,                /* Interrupt no. 787    -   */
        (uint32_T) GTM_MCS0_CH7_ISR_C0,                /* Interrupt no. 788    -   */
        (uint32_T) GTM_MCS1_CH0_ISR_C0,                /* Interrupt no. 789    -   */
        (uint32_T) GTM_MCS1_CH1_ISR_C0,                /* Interrupt no. 790    -   */
        (uint32_T) GTM_MCS1_CH2_ISR_C0,                /* Interrupt no. 791    -   */
        (uint32_T) GTM_MCS1_CH3_ISR_C0,                /* Interrupt no. 792    -   */
        (uint32_T) GTM_MCS1_CH4_ISR_C0,                /* Interrupt no. 793    -   */
        (uint32_T) GTM_MCS1_CH5_ISR_C0,                /* Interrupt no. 794    -   */
        (uint32_T) GTM_MCS1_CH6_ISR_C0,                /* Interrupt no. 795    -   */
        (uint32_T) GTM_MCS1_CH7_ISR_C0,                /* Interrupt no. 796    -   */
        (uint32_T) GTM_MCS2_CH0_ISR_C0,                /* Interrupt no. 797    -   */
        (uint32_T) GTM_MCS2_CH1_ISR_C0,                /* Interrupt no. 798    -   */
        (uint32_T) GTM_MCS2_CH2_ISR_C0,                /* Interrupt no. 799    -   */
        (uint32_T) GTM_MCS2_CH3_ISR_C0,                /* Interrupt no. 800    -   */
        (uint32_T) GTM_MCS2_CH4_ISR_C0,                /* Interrupt no. 801    -   */
        (uint32_T) GTM_MCS2_CH5_ISR_C0,                /* Interrupt no. 802    -   */
        (uint32_T) GTM_MCS2_CH6_ISR_C0,                /* Interrupt no. 803    -   */
        (uint32_T) GTM_MCS2_CH7_ISR_C0,                /* Interrupt no. 804    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 805    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 806    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 807    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 808    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 809    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 810    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 811    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 812    - RESERVED  */
        (uint32_T) GTM_TOM0_CH0_1_ISR_C0,              /* Interrupt no. 813    -   */
        (uint32_T) GTM_TOM0_CH2_3_ISR_C0,              /* Interrupt no. 814    -   */
        (uint32_T) GTM_TOM0_CH4_5_ISR_C0,              /* Interrupt no. 815    -   */
        (uint32_T) GTM_TOM0_CH6_7_ISR_C0,              /* Interrupt no. 816    -   */
        (uint32_T) GTM_TOM0_CH8_9_ISR_C0,              /* Interrupt no. 817    -   */
        (uint32_T) GTM_TOM0_CH10_11_ISR_C0,            /* Interrupt no. 818    -   */
        (uint32_T) GTM_TOM0_CH12_13_ISR_C0,            /* Interrupt no. 819    -   */
        (uint32_T) GTM_TOM0_CH14_15_ISR_C0,            /* Interrupt no. 820    -   */
        (uint32_T) GTM_TOM1_CH0_1_ISR_C0,              /* Interrupt no. 821    -   */
        (uint32_T) GTM_TOM1_CH2_3_ISR_C0,              /* Interrupt no. 822    -   */
        (uint32_T) GTM_TOM1_CH4_5_ISR_C0,              /* Interrupt no. 823    -   */
        (uint32_T) GTM_TOM1_CH6_7_ISR_C0,              /* Interrupt no. 824    -   */
        (uint32_T) GTM_TOM1_CH8_9_ISR_C0,              /* Interrupt no. 825    -   */
        (uint32_T) GTM_TOM1_CH10_11_ISR_C0,            /* Interrupt no. 826    -   */
        (uint32_T) GTM_TOM1_CH12_13_ISR_C0,            /* Interrupt no. 827    -   */
        (uint32_T) GTM_TOM1_CH14_15_ISR_C0,            /* Interrupt no. 828    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 829    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 830    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 831    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 832    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 833    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 834    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 835    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 836    - RESERVED  */
        (uint32_T) GTM_ATOM0_CH0_1_ISR_C0,             /* Interrupt no. 837    -   */
        (uint32_T) GTM_ATOM0_CH2_3_ISR_C0,             /* Interrupt no. 838    -   */
        (uint32_T) GTM_ATOM0_CH4_5_ISR_C0,             /* Interrupt no. 839    -   */
        (uint32_T) GTM_ATOM0_CH6_7_ISR_C0,             /* Interrupt no. 840    -   */
        (uint32_T) GTM_ATOM1_CH0_1_ISR_C0,             /* Interrupt no. 841    -   */
        (uint32_T) GTM_ATOM1_CH2_3_ISR_C0,             /* Interrupt no. 842    -   */
        (uint32_T) GTM_ATOM1_CH4_5_ISR_C0,             /* Interrupt no. 843    -   */
        (uint32_T) GTM_ATOM1_CH6_7_ISR_C0,             /* Interrupt no. 844    -   */
        (uint32_T) GTM_ATOM2_CH0_1_ISR_C0,             /* Interrupt no. 845    -   */
        (uint32_T) GTM_ATOM2_CH2_3_ISR_C0,             /* Interrupt no. 846    -   */
        (uint32_T) GTM_ATOM2_CH4_5_ISR_C0,             /* Interrupt no. 847    -   */
        (uint32_T) GTM_ATOM2_CH6_7_ISR_C0,             /* Interrupt no. 848    -   */
        (uint32_T) GTM_ATOM3_CH0_1_ISR_C0,             /* Interrupt no. 849    -   */
        (uint32_T) GTM_ATOM3_CH2_3_ISR_C0,             /* Interrupt no. 850    -   */
        (uint32_T) GTM_ATOM3_CH4_5_ISR_C0,             /* Interrupt no. 851    -   */
        (uint32_T) GTM_ATOM3_CH6_7_ISR_C0,             /* Interrupt no. 852    -   */
        (uint32_T) RESERVED,                        /* Interrupt no. 853    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 854    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 855    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 856    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 857    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 858    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 859    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 860    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 861    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 862    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 863    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 864    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 865    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 866    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 867    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 868    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 869    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 870    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 871    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 872    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 873    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 874    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 875    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 876    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 877    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 878    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 879    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 880    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 881    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 882    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 883    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 884    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 885    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 886    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 887    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 888    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 889    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 890    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 891    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 892    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 893    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 894    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 895    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 896    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 897    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 898    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 899    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 900    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 901    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 902    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 903    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 904    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 905    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 906    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 907    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 908    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 909    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 910    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 911    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 912    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 913    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 914    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 915    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 916    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 917    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 918    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 919    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 920    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 921    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 922    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 923    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 924    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 925    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 926    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 927    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 928    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 929    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 930    - RESERVED  */
        (uint32_T) GTM_ERR_ISR_C0,                     /* Interrupt no. 931    -   */
        /************************************************************************************************/
        /*************************************** RESERVED ***********************************************/
        /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 932    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 933    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 934    - RESERVED  */
    /************************************************************************************************/
    /**************************************** CCCU ISR *********************************************/
    /************************************************************************************************/
    (uint32_T) CCCU_CSCE_CWEE_ISR_C0,              /* Interrupt no. 935    -   */  
    /************************************************************************************************/
    /*************************************** RESERVED ***********************************************/
    /************************************************************************************************/
        (uint32_T) RESERVED,                        /* Interrupt no. 936    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 937    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 938    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 939    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 940    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 941    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 942    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 943    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 944    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 945    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 946    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 947    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 948    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 949    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 950    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 951    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 952    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 953    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 954    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 955    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 956    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 957    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 958    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 959    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 960    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 961    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 962    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 963    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 964    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 965    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 966    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 967    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 968    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 969    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 970    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 971    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 972    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 973    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 974    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 975    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 976    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 977    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 978    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 979    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 980    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 981    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 982    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 983    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 984    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 985    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 986    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 987    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 988    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 989    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 990    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 991    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 992    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 993    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 994    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 995    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 996    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 997    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 998    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 999    - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1000   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1001   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1002   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1003   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1004   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1005   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1006   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1007   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1008   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1009   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1010   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1011   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1012   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1013   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1014   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1015   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1016   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1017   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1018   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1019   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1020   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1021   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1022   - RESERVED  */
        (uint32_T) RESERVED,                        /* Interrupt no. 1023   - RESERVED  */
};
#pragma ghs endnowarning /* warning #32-D: expression  must have arithmetic type */

/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : dummy
**
**   Description:
**    Dummy function that waits forever or for timeout.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
static void dummy (void)  {
    uint8_T dummyVar = 1u;

    DisableAllInterrupts();
    while (dummyVar == 1u)
    {
    }                 /* Wait forever or for timeout */
}

#endif // _BUILD_TASK_

/****************************************************************************
 ****************************************************************************/
 
