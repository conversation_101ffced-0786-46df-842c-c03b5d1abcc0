/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_atom.c
 * @brief   SPC5xx GTM ATOM low level driver code.
 *
 * @addtogroup ATOM
 * @{
 */

#include "gtm.h"
//#include <irq.h> MC

/*lint -e621*/

#if (SPC5_GTM_USE_ATOM == TRUE) || defined(__DOXYGEN__)

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/**
 * @brief   ATOM0 driver identifier.
 */
#if (SPC5_GTM_USE_ATOM0 == TRUE) || defined(__DOXYGEN__)
GTM_ATOMDriver ATOMD1;
#endif

/**
 * @brief   ATOM1 driver identifier.
 */
#if (SPC5_GTM_USE_ATOM1 == TRUE) || defined(__DOXYGEN__)
GTM_ATOMDriver ATOMD2;
#endif

/**
 * @brief   ATOM2 driver identifier.
 */
#if (SPC5_GTM_USE_ATOM2 == TRUE) || defined(__DOXYGEN__)
GTM_ATOMDriver ATOMD3;
#endif

/**
 * @brief   ATOM3 driver identifier.
 */
#if (SPC5_GTM_USE_ATOM3 == TRUE) || defined(__DOXYGEN__)
GTM_ATOMDriver ATOMD4;
#endif

/**
 * @brief   ATOM4 driver identifier.
 */
#if (SPC5_GTM_USE_ATOM4 == TRUE) || defined(__DOXYGEN__)
GTM_ATOMDriver ATOMD5;
#endif

/**
 * @brief   ATOM5 driver identifier.
 */
#if (SPC5_GTM_USE_ATOM5 == TRUE) || defined(__DOXYGEN__)
GTM_ATOMDriver ATOMD6;
#endif


/*===========================================================================*/
/* Driver local variables and types.                                         */
/*===========================================================================*/
#if !defined(__DOXYGEN__)

#if ((SPC5_GTM_USE_ATOM0 == TRUE) && \
	 (SPC5_GTM_ATOM0_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM0_CHANNEL0_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU1_ENABLED == TRUE)))
/**
 * @brief   ATOM0 CHANNEL0 Interrupt definition.
 */
#define SPC5_GTM_ATOM0_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_ATOM0_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM0 == TRUE) && \
	 (SPC5_GTM_ATOM0_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM0_CHANNEL1_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU1_ENABLED == TRUE)))
/**
 * @brief   ATOM0 CHANNEL1 Interrupt definition.
 */
#define SPC5_GTM_ATOM0_CHANNEL1_INT    TRUE
#else

#define SPC5_GTM_ATOM0_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM0 == TRUE) && \
	 (SPC5_GTM_ATOM0_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM0_CHANNEL2_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM0 CHANNEL2 Interrupt definition.
 */
#define SPC5_GTM_ATOM0_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_ATOM0_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM0 == TRUE) && \
	 (SPC5_GTM_ATOM0_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM0_CHANNEL3_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM0 CHANNEL3 Interrupt definition.
 */
#define SPC5_GTM_ATOM0_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_ATOM0_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM0 == TRUE) && \
	 (SPC5_GTM_ATOM0_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM0_CHANNEL4_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM0 CHANNEL4 Interrupt definition.
 */
#define SPC5_GTM_ATOM0_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_ATOM0_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM0 == TRUE) && \
	 (SPC5_GTM_ATOM0_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM0_CHANNEL5_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM0 CHANNEL5 Interrupt definition.
 */
#define SPC5_GTM_ATOM0_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_ATOM0_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM0 == TRUE) && \
	 (SPC5_GTM_ATOM0_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM0_CHANNEL6_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM0 CHANNEL6 Interrupt definition.
 */
#define SPC5_GTM_ATOM0_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_ATOM0_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM0 == TRUE) && \
	 (SPC5_GTM_ATOM0_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM0_CHANNEL7_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM0 CHANNEL7 Interrupt definition.
 */
#define SPC5_GTM_ATOM0_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_ATOM0_CHANNEL7_INT    FALSE
#endif


#if ((SPC5_GTM_USE_ATOM1 == TRUE) && \
	 (SPC5_GTM_ATOM1_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM1_CHANNEL0_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM1 CHANNEL0 Interrupt definition.
 */
#define SPC5_GTM_ATOM1_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_ATOM1_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM1 == TRUE) && \
	 (SPC5_GTM_ATOM1_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM1_CHANNEL1_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM1 CHANNEL1 Interrupt definition.
 */
#define SPC5_GTM_ATOM1_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_ATOM1_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM1 == TRUE) && \
	 (SPC5_GTM_ATOM1_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM1_CHANNEL2_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM1 CHANNEL2 Interrupt definition.
 */
#define SPC5_GTM_ATOM1_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_ATOM1_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM1 == TRUE) && \
	 (SPC5_GTM_ATOM1_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM1_CHANNEL3_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM1 CHANNEL3 Interrupt definition.
 */
#define SPC5_GTM_ATOM1_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_ATOM1_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM1 == TRUE) && \
	 (SPC5_GTM_ATOM1_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM1_CHANNEL4_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM1 CHANNEL4 Interrupt definition.
 */
#define SPC5_GTM_ATOM1_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_ATOM1_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM1 == TRUE) && \
	 (SPC5_GTM_ATOM1_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM1_CHANNEL5_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM1 CHANNEL5 Interrupt definition.
 */
#define SPC5_GTM_ATOM1_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_ATOM1_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM1 == TRUE) && \
	 (SPC5_GTM_ATOM1_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM1_CHANNEL6_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM1 CHANNEL6 Interrupt definition.
 */
#define SPC5_GTM_ATOM1_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_ATOM1_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM1 == TRUE) && \
	 (SPC5_GTM_ATOM1_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM1_CHANNEL7_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM1 CHANNEL7 Interrupt definition.
 */
#define SPC5_GTM_ATOM1_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_ATOM1_CHANNEL7_INT    FALSE
#endif


#if ((SPC5_GTM_USE_ATOM2 == TRUE) && \
	 (SPC5_GTM_ATOM2_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM2_CHANNEL0_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM2 CHANNEL0 Interrupt definition.
 */
#define SPC5_GTM_ATOM2_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_ATOM2_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM2 == TRUE) && \
	 (SPC5_GTM_ATOM2_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM2_CHANNEL1_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM2 CHANNEL1 Interrupt definition.
 */
#define SPC5_GTM_ATOM2_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_ATOM2_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM2 == TRUE) && \
	 (SPC5_GTM_ATOM2_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM2_CHANNEL2_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM2 CHANNEL2 Interrupt definition.
 */
#define SPC5_GTM_ATOM2_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_ATOM2_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM2 == TRUE) && \
	 (SPC5_GTM_ATOM2_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM2_CHANNEL3_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM2 CHANNEL3 Interrupt definition.
 */
#define SPC5_GTM_ATOM2_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_ATOM2_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM2 == TRUE) && \
	 (SPC5_GTM_ATOM2_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM2_CHANNEL4_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM2 CHANNEL4 Interrupt definition.
 */
#define SPC5_GTM_ATOM2_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_ATOM2_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM2 == TRUE) && \
	 (SPC5_GTM_ATOM2_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM2_CHANNEL5_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM2 CHANNEL5 Interrupt definition.
 */
#define SPC5_GTM_ATOM2_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_ATOM2_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM2 == TRUE) && \
	 (SPC5_GTM_ATOM2_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM2_CHANNEL6_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM2 CHANNEL6 Interrupt definition.
 */
#define SPC5_GTM_ATOM2_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_ATOM2_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM2 == TRUE) && \
	 (SPC5_GTM_ATOM2_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM2_CHANNEL7_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM2 CHANNEL7 Interrupt definition.
 */
#define SPC5_GTM_ATOM2_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_ATOM2_CHANNEL7_INT    FALSE
#endif


#if ((SPC5_GTM_USE_ATOM3 == TRUE) && \
	 (SPC5_GTM_ATOM3_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM3_CHANNEL0_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM3 CHANNEL0 Interrupt definition.
 */
#define SPC5_GTM_ATOM3_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_ATOM3_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM3 == TRUE) && \
	 (SPC5_GTM_ATOM3_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM3_CHANNEL1_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM3 CHANNEL1 Interrupt definition.
 */
#define SPC5_GTM_ATOM3_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_ATOM3_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM3 == TRUE) && \
	 (SPC5_GTM_ATOM3_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM3_CHANNEL2_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM3 CHANNEL2 Interrupt definition.
 */
#define SPC5_GTM_ATOM3_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_ATOM3_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM3 == TRUE) && \
	 (SPC5_GTM_ATOM3_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM3_CHANNEL3_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM3 CHANNEL3 Interrupt definition.
 */
#define SPC5_GTM_ATOM3_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_ATOM3_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM3 == TRUE) && \
	 (SPC5_GTM_ATOM3_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM3_CHANNEL4_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM3 CHANNEL4 Interrupt definition.
 */
#define SPC5_GTM_ATOM3_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_ATOM3_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM3 == TRUE) && \
	 (SPC5_GTM_ATOM3_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM3_CHANNEL5_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM3 CHANNEL5 Interrupt definition.
 */
#define SPC5_GTM_ATOM3_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_ATOM3_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM3 == TRUE) && \
	 (SPC5_GTM_ATOM3_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM3_CHANNEL6_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM3 CHANNEL6 Interrupt definition.
 */
#define SPC5_GTM_ATOM3_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_ATOM3_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM3 == TRUE) && \
	 (SPC5_GTM_ATOM3_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM3_CHANNEL7_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM3 CHANNEL7 Interrupt definition.
 */
#define SPC5_GTM_ATOM3_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_ATOM3_CHANNEL7_INT    FALSE
#endif


#if ((SPC5_GTM_USE_ATOM4 == TRUE) && \
	 (SPC5_GTM_ATOM4_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM4_CHANNEL0_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU1_ENABLED == TRUE)))
/**
 * @brief   ATOM4 CHANNEL0 Interrupt definition.
 */
#define SPC5_GTM_ATOM4_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_ATOM4_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM4 == TRUE) && \
	 (SPC5_GTM_ATOM4_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM4_CHANNEL1_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU1_ENABLED == TRUE)))
/**
 * @brief   ATOM4 CHANNEL1 Interrupt definition.
 */
#define SPC5_GTM_ATOM4_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_ATOM4_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM4 == TRUE) && \
	 (SPC5_GTM_ATOM4_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM4_CHANNEL2_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM4 CHANNEL2 Interrupt definition.
 */
#define SPC5_GTM_ATOM4_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_ATOM4_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM4 == TRUE) && \
	 (SPC5_GTM_ATOM4_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM4_CHANNEL3_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM4 CHANNEL3 Interrupt definition.
 */
#define SPC5_GTM_ATOM4_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_ATOM4_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM4 == TRUE) && \
	 (SPC5_GTM_ATOM4_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM4_CHANNEL4_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM4 CHANNEL4 Interrupt definition.
 */
#define SPC5_GTM_ATOM4_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_ATOM4_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM4 == TRUE) && \
	 (SPC5_GTM_ATOM4_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM4_CHANNEL5_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM4 CHANNEL5 Interrupt definition.
 */
#define SPC5_GTM_ATOM4_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_ATOM4_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM4 == TRUE) && \
	 (SPC5_GTM_ATOM4_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM4_CHANNEL6_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM4 CHANNEL6 Interrupt definition.
 */
#define SPC5_GTM_ATOM4_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_ATOM4_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM4 == TRUE) && \
	 (SPC5_GTM_ATOM4_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM4_CHANNEL7_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM4 CHANNEL7 Interrupt definition.
 */
#define SPC5_GTM_ATOM4_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_ATOM4_CHANNEL7_INT    FALSE
#endif


#if ((SPC5_GTM_USE_ATOM5 == TRUE) && \
	 (SPC5_GTM_ATOM5_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM5_CHANNEL0_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU1_ENABLED == TRUE)))
/**
 * @brief   ATOM5 CHANNEL0 Interrupt definition.
 */
#define SPC5_GTM_ATOM5_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_ATOM5_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM5 == TRUE) && \
	 (SPC5_GTM_ATOM5_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM5_CHANNEL1_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU1_ENABLED == TRUE)))
/**
 * @brief   ATOM5 CHANNEL1 Interrupt definition.
 */
#define SPC5_GTM_ATOM5_CHANNEL1_INT    TRUE
#else

#define SPC5_GTM_ATOM5_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM5 == TRUE) && \
	 (SPC5_GTM_ATOM5_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM5_CHANNEL2_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM5 CHANNEL2 Interrupt definition.
 */
#define SPC5_GTM_ATOM5_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_ATOM5_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM5 == TRUE) && \
	 (SPC5_GTM_ATOM5_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM5_CHANNEL3_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM5 CHANNEL3 Interrupt definition.
 */
#define SPC5_GTM_ATOM5_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_ATOM5_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM5 == TRUE) && \
	 (SPC5_GTM_ATOM5_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM5_CHANNEL4_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM5 CHANNEL4 Interrupt definition.
 */
#define SPC5_GTM_ATOM5_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_ATOM5_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM5 == TRUE) && \
	 (SPC5_GTM_ATOM5_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM5_CHANNEL5_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM5 CHANNEL5 Interrupt definition.
 */
#define SPC5_GTM_ATOM5_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_ATOM5_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM5 == TRUE) && \
	 (SPC5_GTM_ATOM5_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM5_CHANNEL6_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM5 CHANNEL6 Interrupt definition.
 */
#define SPC5_GTM_ATOM5_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_ATOM5_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_ATOM5 == TRUE) && \
	 (SPC5_GTM_ATOM5_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE) && \
	((SPC5_GTM_ATOM5_CHANNEL7_INT_CCU0_ENABLED == TRUE) || \
	 (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU1_ENABLED == TRUE)))

/**
 * @brief   ATOM5 CHANNEL7 Interrupt definition.
 */
#define SPC5_GTM_ATOM5_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_ATOM5_CHANNEL7_INT    FALSE
#endif
#endif /* __DOXYGEN__ */

/*===========================================================================*/
/* Driver local functions.                                                   */
/*===========================================================================*/
/*lint -e9023 */

/**
 * @brief   ATOM Interrupt driver local function and macro.
 *
 * @{
 */
#define ATOM_CHANNEL_ID(a1)     ATOM_##a1
#define ATOM_DEFINE(a1, a2, a3) SPC5_GTM_##a1##_##a2##a3
/*lint +e9023 */
/**
 * @brief   ATOM Channel initialization in SOMI Mode
 *
 * @param[in] dd     GTM ATOM driver pointer
 *
 * @param[in] atom   GTM ATOM index module (ATOM0,..)
 *
 * @param[in] ch     GTM ATOM channel number
 *
 * @sa
 *
 */
#define ATOM_CHANNEL_INIT_SOMI(dd, atom, ch) \
do { \
	gtm_atomSetMode(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _OUTPUT_MODE)); \
	gtm_atomARUEnable(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _ARU_ENABLE)); \
	gtm_atomARUMode(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _ARU_MODE)); \
	gtm_atomSetSignalLevel(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _SIGNAL_LEVEL)); \
} while (false)

/**
 * @brief   ATOM Channel initialization in SOMC Mode
 *
 * @param[in] dd     GTM ATOM driver pointer
 *
 * @param[in] atom   GTM ATOM index module (ATOM0,..)
 *
 * @param[in] ch     GTM ATOM channel number
 *
 * @sa
 *
 */
#define ATOM_CHANNEL_INIT_SOMC(dd, atom, ch) \
do { \
	gtm_atomSetMode(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _OUTPUT_MODE)); \
	gtm_atomSetCM1TimeBaseSource(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _CM1_TBU_TS)); \
	gtm_atomARUEnable(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _ARU_ENABLE)); \
	gtm_atomARUMode(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _ARU_BLOCKING_MODE)); \
	gtm_atomSetCompareStrategy(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _COMPARE_STRATEGY)); \
	gtm_atomCompareControl(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _COMPARE_CONTROL)); \
	gtm_atomSetSignalLevel(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _SIGNAL_LEVEL)); \
	gtm_atomSignalLevelControl(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _SIGNAL_LEVEL_CTRL)); \
	gtm_atomSetCompare0(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _COMPARE_0)); \
	gtm_atomSetCompare1(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _COMPARE_1)); \
} while (false)

/**
 * @brief   ATOM Channel initialization in SOMP Mode
 *
 * @param[in] dd     GTM ATOM driver pointer
 *
 * @param[in] atom   GTM ATOM index module (ATOM0,..)
 *
 * @param[in] ch     GTM ATOM channel number
 *
 * @sa
 *
 */
#define ATOM_CHANNEL_INIT_SOMP(dd, atom, ch) \
do { \
	gtm_atomSetMode(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _OUTPUT_MODE)); \
	gtm_atomSetSignalLevel(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _SIGNAL_LEVEL)); \
	gtm_atomARUEnable(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _ARU_ENABLE)); \
	gtm_atomARUMode(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _ARU_MODE)); \
	gtm_atomClkSrc(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _ARU_MODE), ATOM_DEFINE(atom, ch, _CLK_SOURCE)); \
	gtm_atomSetCompare0(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _PERIOD)); \
	gtm_atomSetCompare1(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _DUTY_CYCLE)); \
	gtm_atomSetShadowReg0(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _PERIOD)); \
	gtm_atomSetShadowReg1(dd, ATOM_CHANNEL_ID(ch), ATOM_DEFINE(atom, ch, _DUTY_CYCLE)); \
} while (false)

/**
 * @brief   ATOM Channel initialization in SOMS Mode
 *
 * @param[in] dd     GTM ATOM driver pointer
 *
 * @param[in] atom   GTM ATOM index module (ATOM0,..)
 *
 * @param[in] ch     GTM ATOM channel number
 *
 * @sa
 *
 */
#define ATOM_CHANNEL_INIT_SOMS(dd, atom, ch) \
do { \
} while (false)

/** @} */

#if (SPC5_GTM_ATOM0_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM0_CHANNEL1_INT == TRUE) || \
    (SPC5_GTM_ATOM0_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM0_CHANNEL3_INT == TRUE) || \
    (SPC5_GTM_ATOM0_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM0_CHANNEL5_INT == TRUE) || \
    (SPC5_GTM_ATOM0_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM0_CHANNEL7_INT == TRUE) || \
    (SPC5_GTM_ATOM1_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM1_CHANNEL1_INT == TRUE) || \
    (SPC5_GTM_ATOM1_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM1_CHANNEL3_INT == TRUE) || \
    (SPC5_GTM_ATOM1_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM1_CHANNEL5_INT == TRUE) || \
    (SPC5_GTM_ATOM1_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM1_CHANNEL7_INT == TRUE) || \
    (SPC5_GTM_ATOM2_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM2_CHANNEL1_INT == TRUE) || \
    (SPC5_GTM_ATOM2_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM2_CHANNEL3_INT == TRUE) || \
    (SPC5_GTM_ATOM2_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM2_CHANNEL5_INT == TRUE) || \
    (SPC5_GTM_ATOM2_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM2_CHANNEL7_INT == TRUE) || \
    (SPC5_GTM_ATOM3_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM3_CHANNEL1_INT == TRUE) || \
    (SPC5_GTM_ATOM3_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM3_CHANNEL3_INT == TRUE) || \
    (SPC5_GTM_ATOM3_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM3_CHANNEL5_INT == TRUE) || \
    (SPC5_GTM_ATOM3_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM3_CHANNEL7_INT == TRUE) || \
    (SPC5_GTM_ATOM4_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM4_CHANNEL1_INT == TRUE) || \
    (SPC5_GTM_ATOM4_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM4_CHANNEL3_INT == TRUE) || \
    (SPC5_GTM_ATOM4_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM4_CHANNEL5_INT == TRUE) || \
    (SPC5_GTM_ATOM4_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM4_CHANNEL7_INT == TRUE) || \
    (SPC5_GTM_ATOM5_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM5_CHANNEL1_INT == TRUE) || \
    (SPC5_GTM_ATOM5_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM5_CHANNEL3_INT == TRUE) || \
    (SPC5_GTM_ATOM5_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM5_CHANNEL5_INT == TRUE) || \
    (SPC5_GTM_ATOM5_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM5_CHANNEL7_INT == TRUE)
/**
 * @brief   Low level interrupt handler.
 *
 * @param[in] atomd      GTM ATOM driver pointer
 *
 * @param[in] channel    ATOM channel
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1, ATOM_CHANNEL2, ATOM_CHANNEL3, ATOM_CHANNEL4, ATOM_CHANNEL5, ATOM_CHANNEL6, ATOM_CHANNEL7
 *
 */
/*static*/ void spc5_gtm_atom_interrupt_channel_handler(GTM_ATOMDriver *atomd, uint8_t channel) {

	uint32_t status;
	uint32_t enabled;
	/* MISRA requirement */
	GTM_ATOM_Channel_Callbacks *callback;

	/* Read interrupt status */
	status = gtm_atomGetIntStatus(atomd, channel);

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (status == 0u) // ISR is set but its status flag or register is not set
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_

	/* Mask disabled interrupts */
	enabled = gtm_atomGetIntEnabled(atomd, channel) & status;
	callback = atomd->callbacks[channel];

	if (callback != NULL) {

		if ((enabled & SPC5_GTM_ATOM_IRQ_STATUS_CCU0) != 0UL) {
			if (atomd->callbacks[channel]->ccu0 != NULL) {
				atomd->callbacks[channel]->ccu0(atomd, channel);
			}
		}

		if ((enabled & SPC5_GTM_ATOM_IRQ_STATUS_CCU1) != 0UL) {
			if (atomd->callbacks[channel]->ccu1 != NULL) {
				atomd->callbacks[channel]->ccu1(atomd, channel);
			}
		}
	}

	/* Acknowledge the interrupts */
	gtm_atomAckInt(atomd, channel, status);
}
#endif


/*===========================================================================*/
/* Driver interrupt handlers.                                                */
/*===========================================================================*/
#if 0
#if (SPC5_GTM_ATOM0_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM0_CHANNEL1_INT == TRUE) || defined(__DOXYGEN__)

/**
 * @brief   ATOM0 Channel 0 and Channel 1 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_ATOM0_EVENT0_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM0);

	if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_0) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL0);
	}

	if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_1) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL1);
	}

	IRQ_EPILOGUE();
}
#endif
#endif
#if (SPC5_GTM_ATOM0_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM0_CHANNEL3_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM0 Channel 2 and Channel 3 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL2, ATOM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_ATOM0_EVENT1_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM0);

	if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_2) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL2);
	}

	if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_3) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL3);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM0_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM0_CHANNEL5_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM0 Channel 4 and Channel 5 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL4, ATOM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_ATOM0_EVENT2_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM0);

	if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_4) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL4);
	}

	if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_5) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL5);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM0_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM0_CHANNEL7_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM0 Channel 6 and Channel 7 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL6, ATOM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_ATOM0_EVENT3_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM0);

	if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_6) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL6);
	}

	if ((event & SPC5_GTM_ICM_ATOM0_CHANNEL_7) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL7);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM1_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM1_CHANNEL1_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM1 Channel 0 and Channel 1 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_ATOM1_EVENT0_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD2.icmd, SPC5_GTM_ICM_ATOM1);

	if ((event & SPC5_GTM_ICM_ATOM1_CHANNEL_0) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD2, ATOM_CHANNEL0);
	}

	if ((event & SPC5_GTM_ICM_ATOM1_CHANNEL_1) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD2, ATOM_CHANNEL1);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM1_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM1_CHANNEL3_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM1 Channel 2 and Channel 3 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL2, ATOM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_ATOM1_EVENT1_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD2.icmd, SPC5_GTM_ICM_ATOM1);

	if ((event & SPC5_GTM_ICM_ATOM1_CHANNEL_2) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD2, ATOM_CHANNEL2);
	}

	if ((event & SPC5_GTM_ICM_ATOM1_CHANNEL_3) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD2, ATOM_CHANNEL3);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM1_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM1_CHANNEL5_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM1 Channel 4 and Channel 5 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL4, ATOM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_ATOM1_EVENT2_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD2.icmd, SPC5_GTM_ICM_ATOM1);

	if ((event & SPC5_GTM_ICM_ATOM1_CHANNEL_4) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD2, ATOM_CHANNEL4);
	}

	if ((event & SPC5_GTM_ICM_ATOM1_CHANNEL_5) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD2, ATOM_CHANNEL5);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM1_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM1_CHANNEL7_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM1 Channel 6 and Channel 7 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL6, ATOM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_ATOM1_EVENT3_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD2.icmd, SPC5_GTM_ICM_ATOM1);

	if ((event & SPC5_GTM_ICM_ATOM1_CHANNEL_6) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD2, ATOM_CHANNEL6);
	}

	if ((event & SPC5_GTM_ICM_ATOM1_CHANNEL_7) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD2, ATOM_CHANNEL7);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM2_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM2_CHANNEL1_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM2 Channel 0 and Channel 1 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_ATOM2_EVENT0_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD3.icmd, SPC5_GTM_ICM_ATOM2);

	if ((event & SPC5_GTM_ICM_ATOM2_CHANNEL_0) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD3, ATOM_CHANNEL0);
	}

	if ((event & SPC5_GTM_ICM_ATOM2_CHANNEL_1) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD3, ATOM_CHANNEL1);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM2_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM2_CHANNEL3_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM2 Channel 2 and Channel 3 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL2, ATOM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_ATOM2_EVENT1_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD3.icmd, SPC5_GTM_ICM_ATOM2);

	if ((event & SPC5_GTM_ICM_ATOM2_CHANNEL_2) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD3, ATOM_CHANNEL2);
	}

	if ((event & SPC5_GTM_ICM_ATOM2_CHANNEL_3) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD3, ATOM_CHANNEL3);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM2_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM2_CHANNEL5_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM2 Channel 4 and Channel 5 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL4, ATOM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_ATOM2_EVENT2_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD3.icmd, SPC5_GTM_ICM_ATOM2);

	if ((event & SPC5_GTM_ICM_ATOM2_CHANNEL_4) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD3, ATOM_CHANNEL4);
	}

	if ((event & SPC5_GTM_ICM_ATOM2_CHANNEL_5) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD3, ATOM_CHANNEL5);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM2_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM2_CHANNEL7_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM2 Channel 6 and Channel 7 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL6, ATOM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_ATOM2_EVENT3_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD3.icmd, SPC5_GTM_ICM_ATOM2);

	if ((event & SPC5_GTM_ICM_ATOM2_CHANNEL_6) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD3, ATOM_CHANNEL6);
	}

	if ((event & SPC5_GTM_ICM_ATOM2_CHANNEL_7) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD3, ATOM_CHANNEL7);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM3_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM3_CHANNEL1_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM3 Channel 0 and Channel 1 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_ATOM3_EVENT0_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM3);

	if ((event & SPC5_GTM_ICM_ATOM3_CHANNEL_0) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL0);
	}

	if ((event & SPC5_GTM_ICM_ATOM3_CHANNEL_1) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL1);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM3_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM3_CHANNEL3_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM3 Channel 2 and Channel 3 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL2, ATOM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_ATOM3_EVENT1_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM3);

	if ((event & SPC5_GTM_ICM_ATOM3_CHANNEL_2) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL2);
	}

	if ((event & SPC5_GTM_ICM_ATOM3_CHANNEL_3) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL3);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM3_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM3_CHANNEL5_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM3 Channel 4 and Channel 5 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL4, ATOM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_ATOM3_EVENT2_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM3);

	if ((event & SPC5_GTM_ICM_ATOM3_CHANNEL_4) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL4);
	}

	if ((event & SPC5_GTM_ICM_ATOM3_CHANNEL_5) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL5);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM3_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM3_CHANNEL7_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM3 Channel 6 and Channel 7 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL6, ATOM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_ATOM3_EVENT3_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM3);

	if ((event & SPC5_GTM_ICM_ATOM3_CHANNEL_6) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL6);
	}

	if ((event & SPC5_GTM_ICM_ATOM3_CHANNEL_7) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL7);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM4_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM4_CHANNEL1_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM4 Channel 0 and Channel 1 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_ATOM4_EVENT0_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM4);

	if ((event & SPC5_GTM_ICM_ATOM4_CHANNEL_0) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL0);
	}

	if ((event & SPC5_GTM_ICM_ATOM4_CHANNEL_1) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL1);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM4_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM4_CHANNEL3_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM4 Channel 2 and Channel 3 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL2, ATOM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_ATOM4_EVENT1_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM4);

	if ((event & SPC5_GTM_ICM_ATOM4_CHANNEL_2) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL2);
	}

	if ((event & SPC5_GTM_ICM_ATOM4_CHANNEL_3) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL3);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM4_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM4_CHANNEL5_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM4 Channel 4 and Channel 5 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL4, ATOM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_ATOM4_EVENT2_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM4);

	if ((event & SPC5_GTM_ICM_ATOM4_CHANNEL_4) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL4);
	}

	if ((event & SPC5_GTM_ICM_ATOM4_CHANNEL_5) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL5);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM4_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM4_CHANNEL7_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM4 Channel 6 and Channel 7 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL6, ATOM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_ATOM4_EVENT3_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM4);

	if ((event & SPC5_GTM_ICM_ATOM4_CHANNEL_6) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL6);
	}

	if ((event & SPC5_GTM_ICM_ATOM4_CHANNEL_7) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL7);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM5_CHANNEL0_INT == TRUE) || (SPC5_GTM_ATOM5_CHANNEL1_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM5 Channel 0 and Channel 1 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_ATOM5_EVENT0_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM5);

	if ((event & SPC5_GTM_ICM_ATOM5_CHANNEL_0) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL0);
	}

	if ((event & SPC5_GTM_ICM_ATOM5_CHANNEL_1) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL1);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM5_CHANNEL2_INT == TRUE) || (SPC5_GTM_ATOM5_CHANNEL3_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM5 Channel 2 and Channel 3 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL2, ATOM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_ATOM5_EVENT1_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM5);

	if ((event & SPC5_GTM_ICM_ATOM5_CHANNEL_2) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL2);
	}

	if ((event & SPC5_GTM_ICM_ATOM5_CHANNEL_3) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL3);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM5_CHANNEL4_INT == TRUE) || (SPC5_GTM_ATOM5_CHANNEL5_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM5 Channel 4 and Channel 5 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL4, ATOM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_ATOM5_EVENT2_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM5);

	if ((event & SPC5_GTM_ICM_ATOM5_CHANNEL_4) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL4);
	}

	if ((event & SPC5_GTM_ICM_ATOM5_CHANNEL_5) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL5);
	}

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_ATOM5_CHANNEL6_INT == TRUE) || (SPC5_GTM_ATOM5_CHANNEL7_INT == TRUE) || defined(__DOXYGEN__)
/**
 * @brief   ATOM5 Channel 6 and Channel 7 interrupt hander.
 *
 * @sa
 * ATOM_CHANNEL6, ATOM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_ATOM5_EVENT3_HANDLER) {

	uint32_t event;

	IRQ_PROLOGUE();

	event = gtm_icmGetEvent(ATOMD1.icmd, SPC5_GTM_ICM_ATOM5);

	if ((event & SPC5_GTM_ICM_ATOM5_CHANNEL_6) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL6);
	}

	if ((event & SPC5_GTM_ICM_ATOM5_CHANNEL_7) != 0UL) {
		spc5_gtm_atom_interrupt_channel_handler(&ATOMD1, ATOM_CHANNEL7);
	}

	IRQ_EPILOGUE();
}
#endif



/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/

/**
 * @brief   Low level GTM ATOM driver initialization.
 *
 * @init
 */
void gtm_atomInit(void) {

#if (SPC5_GTM_USE_ATOM0 == TRUE)
	ATOMD1.atom = &(GTM_ATOM_0);

	/* Get a reference to Interrupt Concentrator Module */
	ATOMD1.icmd = &ICMD1;

	/* Interrupt callbacks */
	ATOMD1.callbacks = (GTM_ATOM_Channel_Callbacks **)gtm_atom0_callbacks;

/* ATOM0 CHANNEL0 */
#if (SPC5_GTM_ATOM0_USE_CHANNEL0 == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD1, ATOM0, CHANNEL0);
#elif (SPC5_GTM_ATOM0_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD1, ATOM0, CHANNEL0);

	gtm_atomSetIRQMode(&ATOMD1, ATOM_CHANNEL0, SPC5_GTM_ATOM0_CHANNEL0_IRQ_MODE);
	INTC_PSR(SPC5_ATOM0_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM0_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL0_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL0_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM0_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD1, ATOM0, CHANNEL0);
#elif (SPC5_GTM_ATOM0_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD1, ATOM0, CHANNEL0);
#endif
#endif /* SPC5_GTM_ATOM0_USE_CHANNEL0 */

/* ATOM0 CHANNEL1 */
#if (SPC5_GTM_ATOM0_USE_CHANNEL1 == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD1, ATOM0, CHANNEL1);
#elif (SPC5_GTM_ATOM0_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD1, ATOM0, CHANNEL1);

	gtm_atomSetIRQMode(&ATOMD1, ATOM_CHANNEL1, SPC5_GTM_ATOM0_CHANNEL1_IRQ_MODE);
	//INTC_PSR(SPC5_ATOM0_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM0_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL1_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL1_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM0_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD1, ATOM0, CHANNEL1);
#elif (SPC5_GTM_ATOM0_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD1, ATOM0, CHANNEL1);
#endif
#endif /* SPC5_GTM_ATOM0_USE_CHANNEL1 */

/* ATOM0 CHANNEL2 */
#if (SPC5_GTM_ATOM0_USE_CHANNEL2 == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD1, ATOM0, CHANNEL2);
#elif (SPC5_GTM_ATOM0_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD1, ATOM0, CHANNEL2);

	gtm_atomSetIRQMode(&ATOMD1, ATOM_CHANNEL2, SPC5_GTM_ATOM0_CHANNEL2_IRQ_MODE);
	INTC_PSR(SPC5_ATOM0_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM0_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL2_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL2_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM0_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD1, ATOM0, CHANNEL2);
#elif (SPC5_GTM_ATOM0_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD1, ATOM0, CHANNEL2);
#endif
#endif /* SPC5_GTM_ATOM0_USE_CHANNEL2 */

/* ATOM0 CHANNEL3 */
#if (SPC5_GTM_ATOM0_USE_CHANNEL3 == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD1, ATOM0, CHANNEL3);
#elif (SPC5_GTM_ATOM0_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD1, ATOM0, CHANNEL3);

	gtm_atomSetIRQMode(&ATOMD1, ATOM_CHANNEL3, SPC5_GTM_ATOM0_CHANNEL3_IRQ_MODE);
	INTC_PSR(SPC5_ATOM0_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM0_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL3_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL3_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM0_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD1, ATOM0, CHANNEL3);
#elif (SPC5_GTM_ATOM0_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD1, ATOM0, CHANNEL3);
#endif
#endif /* SPC5_GTM_ATOM0_USE_CHANNEL3 */

/* ATOM0 CHANNEL4 */
#if (SPC5_GTM_ATOM0_USE_CHANNEL4 == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD1, ATOM0, CHANNEL4);
#elif (SPC5_GTM_ATOM0_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD1, ATOM0, CHANNEL4);

	gtm_atomSetIRQMode(&ATOMD1, ATOM_CHANNEL4, SPC5_GTM_ATOM0_CHANNEL4_IRQ_MODE);
	INTC_PSR(SPC5_ATOM0_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM0_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL4_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL4_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM0_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD1, ATOM0, CHANNEL4);
#elif (SPC5_GTM_ATOM0_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD1, ATOM0, CHANNEL4);
#endif
#endif /* SPC5_GTM_ATOM0_USE_CHANNEL4 */

/* ATOM0 CHANNEL5 */
#if (SPC5_GTM_ATOM0_USE_CHANNEL5 == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD1, ATOM0, CHANNEL5);
#elif (SPC5_GTM_ATOM0_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD1, ATOM0, CHANNEL5);

	gtm_atomSetIRQMode(&ATOMD1, ATOM_CHANNEL5, SPC5_GTM_ATOM0_CHANNEL5_IRQ_MODE);
	INTC_PSR(SPC5_ATOM0_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM0_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL5_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL5_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM0_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD1, ATOM0, CHANNEL5);
#elif (SPC5_GTM_ATOM0_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD1, ATOM0, CHANNEL5);
#endif
#endif /* SPC5_GTM_ATOM0_USE_CHANNEL5 */

/* ATOM0 CHANNEL6 */
#if (SPC5_GTM_ATOM0_USE_CHANNEL6 == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD1, ATOM0, CHANNEL6);
#elif (SPC5_GTM_ATOM0_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD1, ATOM0, CHANNEL6);

	gtm_atomSetIRQMode(&ATOMD1, ATOM_CHANNEL6, SPC5_GTM_ATOM0_CHANNEL6_IRQ_MODE);
	INTC_PSR(SPC5_ATOM0_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM0_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL6_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL6_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM0_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD1, ATOM0, CHANNEL6);
#elif (SPC5_GTM_ATOM0_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD1, ATOM0, CHANNEL6);
#endif
#endif /* SPC5_GTM_ATOM0_USE_CHANNEL6 */

/* ATOM0 CHANNEL7 */
#if (SPC5_GTM_ATOM0_USE_CHANNEL7 == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD1, ATOM0, CHANNEL7);
#elif (SPC5_GTM_ATOM0_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD1, ATOM0, CHANNEL7);

	gtm_atomSetIRQMode(&ATOMD1, ATOM_CHANNEL7, SPC5_GTM_ATOM0_CHANNEL7_IRQ_MODE);
	INTC_PSR(SPC5_ATOM0_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM0_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL7_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD1, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM0_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM0_CHANNEL7_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM0_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD1, ATOM0, CHANNEL7);
#elif (SPC5_GTM_ATOM0_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD1, ATOM0, CHANNEL7);
#endif
#endif /* SPC5_GTM_ATOM0_USE_CHANNEL7 */

#endif /* SPC5_GTM_USE_ATOM0 */


#if (SPC5_GTM_USE_ATOM1 == TRUE)
	ATOMD2.atom = &(GTM_ATOM_1);

	/* Get a reference to Interrupt Concentrator Module */
	ATOMD2.icmd = &ICMD1;

	/* Interrupt callbacks */
	ATOMD2.callbacks = (GTM_ATOM_Channel_Callbacks **)gtm_atom1_callbacks;

/* ATOM1 CHANNEL0 */
#if (SPC5_GTM_ATOM1_USE_CHANNEL0 == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD2, ATOM1, CHANNEL0);
#elif (SPC5_GTM_ATOM1_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD2, ATOM1, CHANNEL0);

	gtm_atomSetIRQMode(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_ATOM1_CHANNEL0_IRQ_MODE);
	INTC_PSR(SPC5_ATOM1_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM1_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL0_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL0_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM1_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD2, ATOM1, CHANNEL0);
#elif (SPC5_GTM_ATOM1_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD2, ATOM1, CHANNEL0);
#endif
#endif /* SPC5_GTM_ATOM1_USE_CHANNEL0 */

/* ATOM1 CHANNEL1 */
#if (SPC5_GTM_ATOM1_USE_CHANNEL1 == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD2, ATOM1, CHANNEL1);
#elif (SPC5_GTM_ATOM1_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD2, ATOM1, CHANNEL1);

	gtm_atomSetIRQMode(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_ATOM1_CHANNEL1_IRQ_MODE);
	INTC_PSR(SPC5_ATOM1_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM1_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL1_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL1_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM1_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD2, ATOM1, CHANNEL1);
#elif (SPC5_GTM_ATOM1_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD2, ATOM1, CHANNEL1);
#endif
#endif /* SPC5_GTM_ATOM1_USE_CHANNEL1 */

/* ATOM1 CHANNEL2 */
#if (SPC5_GTM_ATOM1_USE_CHANNEL2 == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD2, ATOM1, CHANNEL2);
#elif (SPC5_GTM_ATOM1_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD2, ATOM1, CHANNEL2);

	gtm_atomSetIRQMode(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_ATOM1_CHANNEL2_IRQ_MODE);
	INTC_PSR(SPC5_ATOM1_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM1_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL2_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL2_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM1_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD2, ATOM1, CHANNEL2);
#elif (SPC5_GTM_ATOM1_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD2, ATOM1, CHANNEL2);
#endif
#endif /* SPC5_GTM_ATOM1_USE_CHANNEL2 */

/* ATOM1 CHANNEL3 */
#if (SPC5_GTM_ATOM1_USE_CHANNEL3 == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD2, ATOM1, CHANNEL3);
#elif (SPC5_GTM_ATOM1_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD2, ATOM1, CHANNEL3);

	gtm_atomSetIRQMode(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_ATOM1_CHANNEL3_IRQ_MODE);
	INTC_PSR(SPC5_ATOM1_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM1_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL3_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL3_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM1_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD2, ATOM1, CHANNEL3);
#elif (SPC5_GTM_ATOM1_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD2, ATOM1, CHANNEL3);
#endif
#endif /* SPC5_GTM_ATOM1_USE_CHANNEL3 */

/* ATOM1 CHANNEL4 */
#if (SPC5_GTM_ATOM1_USE_CHANNEL4 == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD2, ATOM1, CHANNEL4);
#elif (SPC5_GTM_ATOM1_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD2, ATOM1, CHANNEL4);

	gtm_atomSetIRQMode(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_ATOM1_CHANNEL4_IRQ_MODE);
	INTC_PSR(SPC5_ATOM1_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM1_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL4_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL4_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM1_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD2, ATOM1, CHANNEL4);
#elif (SPC5_GTM_ATOM1_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD2, ATOM1, CHANNEL4);
#endif
#endif /* SPC5_GTM_ATOM1_USE_CHANNEL4 */

/* ATOM1 CHANNEL5 */
#if (SPC5_GTM_ATOM1_USE_CHANNEL5 == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD2, ATOM1, CHANNEL5);
#elif (SPC5_GTM_ATOM1_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD2, ATOM1, CHANNEL5);

	gtm_atomSetIRQMode(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_ATOM1_CHANNEL5_IRQ_MODE);
	INTC_PSR(SPC5_ATOM1_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM1_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL5_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL5_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM1_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD2, ATOM1, CHANNEL5);
#elif (SPC5_GTM_ATOM1_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD2, ATOM1, CHANNEL5);
#endif
#endif /* SPC5_GTM_ATOM1_USE_CHANNEL5 */

/* ATOM1 CHANNEL6 */
#if (SPC5_GTM_ATOM1_USE_CHANNEL6 == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD2, ATOM1, CHANNEL6);
#elif (SPC5_GTM_ATOM1_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD2, ATOM1, CHANNEL6);

	gtm_atomSetIRQMode(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_ATOM1_CHANNEL6_IRQ_MODE);
	INTC_PSR(SPC5_ATOM1_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM1_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL6_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL6_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM1_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD2, ATOM1, CHANNEL6);
#elif (SPC5_GTM_ATOM1_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD2, ATOM1, CHANNEL6);
#endif
#endif /* SPC5_GTM_ATOM1_USE_CHANNEL6 */

/* ATOM1 CHANNEL7 */
#if (SPC5_GTM_ATOM1_USE_CHANNEL7 == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD2, ATOM1, CHANNEL7);
#elif (SPC5_GTM_ATOM1_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD2, ATOM1, CHANNEL7);

	gtm_atomSetIRQMode(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_ATOM1_CHANNEL7_IRQ_MODE);
	INTC_PSR(SPC5_ATOM1_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM1_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL7_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD2, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM1_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM1_CHANNEL7_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM1_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD2, ATOM1, CHANNEL7);
#elif (SPC5_GTM_ATOM1_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD2, ATOM1, CHANNEL7);
#endif
#endif /* SPC5_GTM_ATOM1_USE_CHANNEL7 */

#endif /* SPC5_GTM_USE_ATOM1 */


#if (SPC5_GTM_USE_ATOM2 == TRUE)
	ATOMD3.atom = &(GTM_ATOM_2);

	/* Get a reference to Interrupt Concentrator Module */
	ATOMD3.icmd = &ICMD1;

	/* Interrupt callbacks */
	ATOMD3.callbacks = (GTM_ATOM_Channel_Callbacks **)gtm_atom2_callbacks;

/* ATOM2 CHANNEL0 */
#if (SPC5_GTM_ATOM2_USE_CHANNEL0 == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD3, ATOM2, CHANNEL0);
#elif (SPC5_GTM_ATOM2_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD3, ATOM2, CHANNEL0);

	gtm_atomSetIRQMode(&ATOMD3, ATOM_CHANNEL0, SPC5_GTM_ATOM2_CHANNEL0_IRQ_MODE);
	INTC_PSR(SPC5_ATOM2_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM2_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL0_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL0_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM2_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD3, ATOM2, CHANNEL0);
#elif (SPC5_GTM_ATOM2_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD3, ATOM2, CHANNEL0);
#endif
#endif /* SPC5_GTM_ATOM2_USE_CHANNEL0 */

/* ATOM2 CHANNEL1 */
#if (SPC5_GTM_ATOM2_USE_CHANNEL1 == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD3, ATOM2, CHANNEL1);
#elif (SPC5_GTM_ATOM2_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD3, ATOM2, CHANNEL1);

	gtm_atomSetIRQMode(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_ATOM2_CHANNEL1_IRQ_MODE);
	INTC_PSR(SPC5_ATOM2_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM2_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL1_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL1_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM2_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD3, ATOM2, CHANNEL1);
#elif (SPC5_GTM_ATOM2_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD3, ATOM2, CHANNEL1);
#endif
#endif /* SPC5_GTM_ATOM2_USE_CHANNEL1 */

/* ATOM2 CHANNEL2 */
#if (SPC5_GTM_ATOM2_USE_CHANNEL2 == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD3, ATOM2, CHANNEL2);
#elif (SPC5_GTM_ATOM2_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD3, ATOM2, CHANNEL2);

	gtm_atomSetIRQMode(&ATOMD3, ATOM_CHANNEL2, SPC5_GTM_ATOM2_CHANNEL2_IRQ_MODE);
	INTC_PSR(SPC5_ATOM2_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM2_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL2_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL2_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM2_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD3, ATOM2, CHANNEL2);
#elif (SPC5_GTM_ATOM2_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD3, ATOM2, CHANNEL2);
#endif
#endif /* SPC5_GTM_ATOM2_USE_CHANNEL2 */

/* ATOM2 CHANNEL3 */
#if (SPC5_GTM_ATOM2_USE_CHANNEL3 == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD3, ATOM2, CHANNEL3);
#elif (SPC5_GTM_ATOM2_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD3, ATOM2, CHANNEL3);

	gtm_atomSetIRQMode(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_ATOM2_CHANNEL3_IRQ_MODE);
	INTC_PSR(SPC5_ATOM2_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM2_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL3_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL3_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM2_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD3, ATOM2, CHANNEL3);
#elif (SPC5_GTM_ATOM2_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD3, ATOM2, CHANNEL3);
#endif
#endif /* SPC5_GTM_ATOM2_USE_CHANNEL3 */

/* ATOM2 CHANNEL4 */
#if (SPC5_GTM_ATOM2_USE_CHANNEL4 == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD3, ATOM2, CHANNEL4);
#elif (SPC5_GTM_ATOM2_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD3, ATOM2, CHANNEL4);

	gtm_atomSetIRQMode(&ATOMD3, ATOM_CHANNEL4, SPC5_GTM_ATOM2_CHANNEL4_IRQ_MODE);
	INTC_PSR(SPC5_ATOM2_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM2_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL4_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL4_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM2_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD3, ATOM2, CHANNEL4);
#elif (SPC5_GTM_ATOM2_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD3, ATOM2, CHANNEL4);
#endif
#endif /* SPC5_GTM_ATOM2_USE_CHANNEL4 */

/* ATOM2 CHANNEL5 */
#if (SPC5_GTM_ATOM2_USE_CHANNEL5 == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD3, ATOM2, CHANNEL5);
#elif (SPC5_GTM_ATOM2_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD3, ATOM2, CHANNEL5);

	gtm_atomSetIRQMode(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_ATOM2_CHANNEL5_IRQ_MODE);
	INTC_PSR(SPC5_ATOM2_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM2_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL5_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL5_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM2_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD3, ATOM2, CHANNEL5);
#elif (SPC5_GTM_ATOM2_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD3, ATOM2, CHANNEL5);
#endif
#endif /* SPC5_GTM_ATOM2_USE_CHANNEL5 */

/* ATOM2 CHANNEL6 */
#if (SPC5_GTM_ATOM2_USE_CHANNEL6 == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD3, ATOM2, CHANNEL6);
#elif (SPC5_GTM_ATOM2_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD3, ATOM2, CHANNEL6);

	gtm_atomSetIRQMode(&ATOMD3, ATOM_CHANNEL6, SPC5_GTM_ATOM2_CHANNEL6_IRQ_MODE);
	INTC_PSR(SPC5_ATOM2_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM2_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL6_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL6_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM2_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD3, ATOM2, CHANNEL6);
#elif (SPC5_GTM_ATOM2_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD3, ATOM2, CHANNEL6);
#endif
#endif /* SPC5_GTM_ATOM2_USE_CHANNEL6 */

/* ATOM2 CHANNEL7 */
#if (SPC5_GTM_ATOM2_USE_CHANNEL7 == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD3, ATOM2, CHANNEL7);
#elif (SPC5_GTM_ATOM2_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD3, ATOM2, CHANNEL7);

	gtm_atomSetIRQMode(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_ATOM2_CHANNEL7_IRQ_MODE);
	INTC_PSR(SPC5_ATOM2_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM2_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL7_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD3, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM2_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM2_CHANNEL7_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM2_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD3, ATOM2, CHANNEL7);
#elif (SPC5_GTM_ATOM2_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD3, ATOM2, CHANNEL7);
#endif
#endif /* SPC5_GTM_ATOM2_USE_CHANNEL7 */

#endif /* SPC5_GTM_USE_ATOM2 */


#if (SPC5_GTM_USE_ATOM3 == TRUE)
	ATOMD4.atom = &(GTM_ATOM_3);

	/* Get a reference to Interrupt Concentrator Module */
	ATOMD4.icmd = &ICMD1;

	/* Interrupt callbacks */
	ATOMD4.callbacks = (GTM_ATOM_Channel_Callbacks **)gtm_atom3_callbacks;

/* ATOM3 CHANNEL0 */
#if (SPC5_GTM_ATOM3_USE_CHANNEL0 == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD4, ATOM3, CHANNEL0);
#elif (SPC5_GTM_ATOM3_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD4, ATOM3, CHANNEL0);

	gtm_atomSetIRQMode(&ATOMD4, ATOM_CHANNEL0, SPC5_GTM_ATOM3_CHANNEL0_IRQ_MODE);
	INTC_PSR(SPC5_ATOM3_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM3_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL0_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL0_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM3_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD4, ATOM3, CHANNEL0);
#elif (SPC5_GTM_ATOM3_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD4, ATOM3, CHANNEL0);
#endif
#endif /* SPC5_GTM_ATOM3_USE_CHANNEL0 */

/* ATOM3 CHANNEL1 */
#if (SPC5_GTM_ATOM3_USE_CHANNEL1 == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD4, ATOM3, CHANNEL1);
#elif (SPC5_GTM_ATOM3_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD4, ATOM3, CHANNEL1);

	gtm_atomSetIRQMode(&ATOMD4, ATOM_CHANNEL1, SPC5_GTM_ATOM3_CHANNEL1_IRQ_MODE);
	INTC_PSR(SPC5_ATOM3_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM3_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL1_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL1_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM3_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD4, ATOM3, CHANNEL1);
#elif (SPC5_GTM_ATOM3_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD4, ATOM3, CHANNEL1);
#endif
#endif /* SPC5_GTM_ATOM3_USE_CHANNEL1 */

/* ATOM3 CHANNEL2 */
#if (SPC5_GTM_ATOM3_USE_CHANNEL2 == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD4, ATOM3, CHANNEL2);
#elif (SPC5_GTM_ATOM3_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD4, ATOM3, CHANNEL2);

	gtm_atomSetIRQMode(&ATOMD4, ATOM_CHANNEL2, SPC5_GTM_ATOM3_CHANNEL2_IRQ_MODE);
	INTC_PSR(SPC5_ATOM3_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM3_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL2_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL2_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM3_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD4, ATOM3, CHANNEL2);
#elif (SPC5_GTM_ATOM3_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD4, ATOM3, CHANNEL2);
#endif
#endif /* SPC5_GTM_ATOM3_USE_CHANNEL2 */

/* ATOM3 CHANNEL3 */
#if (SPC5_GTM_ATOM3_USE_CHANNEL3 == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD4, ATOM3, CHANNEL3);
#elif (SPC5_GTM_ATOM3_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD4, ATOM3, CHANNEL3);

	gtm_atomSetIRQMode(&ATOMD4, ATOM_CHANNEL3, SPC5_GTM_ATOM3_CHANNEL3_IRQ_MODE);
	INTC_PSR(SPC5_ATOM3_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM3_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL3_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL3_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM3_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD4, ATOM3, CHANNEL3);
#elif (SPC5_GTM_ATOM3_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD4, ATOM3, CHANNEL3);
#endif
#endif /* SPC5_GTM_ATOM3_USE_CHANNEL3 */

/* ATOM3 CHANNEL4 */
#if (SPC5_GTM_ATOM3_USE_CHANNEL4 == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD4, ATOM3, CHANNEL4);
#elif (SPC5_GTM_ATOM3_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD4, ATOM3, CHANNEL4);

	gtm_atomSetIRQMode(&ATOMD4, ATOM_CHANNEL4, SPC5_GTM_ATOM3_CHANNEL4_IRQ_MODE);
	INTC_PSR(SPC5_ATOM3_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM3_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL4_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL4_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM3_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD4, ATOM3, CHANNEL4);
#elif (SPC5_GTM_ATOM3_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD4, ATOM3, CHANNEL4);
#endif
#endif /* SPC5_GTM_ATOM3_USE_CHANNEL4 */

/* ATOM3 CHANNEL5 */
#if (SPC5_GTM_ATOM3_USE_CHANNEL5 == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD4, ATOM3, CHANNEL5);
#elif (SPC5_GTM_ATOM3_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD4, ATOM3, CHANNEL5);

	gtm_atomSetIRQMode(&ATOMD4, ATOM_CHANNEL5, SPC5_GTM_ATOM3_CHANNEL5_IRQ_MODE);
	INTC_PSR(SPC5_ATOM3_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM3_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL5_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL5_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM3_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD4, ATOM3, CHANNEL5);
#elif (SPC5_GTM_ATOM3_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD4, ATOM3, CHANNEL5);
#endif
#endif /* SPC5_GTM_ATOM3_USE_CHANNEL5 */

/* ATOM3 CHANNEL6 */
#if (SPC5_GTM_ATOM3_USE_CHANNEL6 == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD4, ATOM3, CHANNEL6);
#elif (SPC5_GTM_ATOM3_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD4, ATOM3, CHANNEL6);

	gtm_atomSetIRQMode(&ATOMD4, ATOM_CHANNEL6, SPC5_GTM_ATOM3_CHANNEL6_IRQ_MODE);
	INTC_PSR(SPC5_ATOM3_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM3_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL6_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL6_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM3_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD4, ATOM3, CHANNEL6);
#elif (SPC5_GTM_ATOM3_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD4, ATOM3, CHANNEL6);
#endif
#endif /* SPC5_GTM_ATOM3_USE_CHANNEL6 */

/* ATOM3 CHANNEL7 */
#if (SPC5_GTM_ATOM3_USE_CHANNEL7 == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD4, ATOM3, CHANNEL7);
#elif (SPC5_GTM_ATOM3_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD4, ATOM3, CHANNEL7);

	gtm_atomSetIRQMode(&ATOMD4, ATOM_CHANNEL7, SPC5_GTM_ATOM3_CHANNEL7_IRQ_MODE);
	INTC_PSR(SPC5_ATOM3_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM3_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL7_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD4, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM3_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM3_CHANNEL7_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM3_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD4, ATOM3, CHANNEL7);
#elif (SPC5_GTM_ATOM3_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD4, ATOM3, CHANNEL7);
#endif
#endif /* SPC5_GTM_ATOM3_USE_CHANNEL7 */

#endif /* SPC5_GTM_USE_ATOM3 */


/* ATOM4 CHANNEL3 */
#if (SPC5_GTM_USE_ATOM4 == TRUE)
	ATOMD5.atom = &(GTM_ATOM_4);

	/* Get a reference to Interrupt Concentrator Module */
	ATOMD5.icmd = &ICMD1;

	/* Interrupt callbacks */
	ATOMD5.callbacks = (GTM_ATOM_Channel_Callbacks **)gtm_atom4_callbacks;

/* ATOM4 CHANNEL0 */
#if (SPC5_GTM_ATOM4_USE_CHANNEL0 == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD5, ATOM4, CHANNEL0);
#elif (SPC5_GTM_ATOM4_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD5, ATOM4, CHANNEL0);

	gtm_atomSetIRQMode(&ATOMD5, ATOM_CHANNEL0, SPC5_GTM_ATOM4_CHANNEL0_IRQ_MODE);
	INTC_PSR(SPC5_ATOM4_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM4_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL0_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL0_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM4_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD5, ATOM4, CHANNEL0);
#elif (SPC5_GTM_ATOM4_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD5, ATOM4, CHANNEL0);
#endif
#endif /* SPC5_GTM_ATOM4_USE_CHANNEL0 */

/* ATOM4 CHANNEL1 */
#if (SPC5_GTM_ATOM4_USE_CHANNEL1 == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD5, ATOM4, CHANNEL1);
#elif (SPC5_GTM_ATOM4_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD5, ATOM4, CHANNEL1);

	gtm_atomSetIRQMode(&ATOMD5, ATOM_CHANNEL1, SPC5_GTM_ATOM4_CHANNEL1_IRQ_MODE);
	INTC_PSR(SPC5_ATOM4_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM4_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL1_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL1_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM4_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD5, ATOM4, CHANNEL1);
#elif (SPC5_GTM_ATOM4_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD5, ATOM4, CHANNEL1);
#endif
#endif /* SPC5_GTM_ATOM4_USE_CHANNEL1 */

/* ATOM4 CHANNEL2 */
#if (SPC5_GTM_ATOM4_USE_CHANNEL2 == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD5, ATOM4, CHANNEL2);
#elif (SPC5_GTM_ATOM4_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD5, ATOM4, CHANNEL2);

	gtm_atomSetIRQMode(&ATOMD5, ATOM_CHANNEL2, SPC5_GTM_ATOM4_CHANNEL2_IRQ_MODE);
	INTC_PSR(SPC5_ATOM4_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM4_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL2_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL2_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM4_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD5, ATOM4, CHANNEL2);
#elif (SPC5_GTM_ATOM4_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD5, ATOM4, CHANNEL2);
#endif
#endif /* SPC5_GTM_ATOM4_USE_CHANNEL2 */

/* ATOM4 CHANNEL3 */
#if (SPC5_GTM_ATOM4_USE_CHANNEL3 == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD5, ATOM4, CHANNEL3);
#elif (SPC5_GTM_ATOM4_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD5, ATOM4, CHANNEL3);

	gtm_atomSetIRQMode(&ATOMD5, ATOM_CHANNEL3, SPC5_GTM_ATOM4_CHANNEL3_IRQ_MODE);
	INTC_PSR(SPC5_ATOM4_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM4_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL3_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL3_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM4_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD5, ATOM4, CHANNEL3);
#elif (SPC5_GTM_ATOM4_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD5, ATOM4, CHANNEL3);
#endif
#endif /* SPC5_GTM_ATOM4_USE_CHANNEL3 */

/* ATOM4 CHANNEL4 */
#if (SPC5_GTM_ATOM4_USE_CHANNEL4 == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD5, ATOM4, CHANNEL4);
#elif (SPC5_GTM_ATOM4_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD5, ATOM4, CHANNEL4);

	gtm_atomSetIRQMode(&ATOMD5, ATOM_CHANNEL4, SPC5_GTM_ATOM4_CHANNEL4_IRQ_MODE);
	INTC_PSR(SPC5_ATOM4_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM4_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL4_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL4_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM4_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD5, ATOM4, CHANNEL4);
#elif (SPC5_GTM_ATOM4_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD5, ATOM4, CHANNEL4);
#endif
#endif /* SPC5_GTM_ATOM4_USE_CHANNEL4 */

/* ATOM4 CHANNEL5 */
#if (SPC5_GTM_ATOM4_USE_CHANNEL5 == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD5, ATOM4, CHANNEL5);
#elif (SPC5_GTM_ATOM4_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD5, ATOM4, CHANNEL5);

	gtm_atomSetIRQMode(&ATOMD5, ATOM_CHANNEL5, SPC5_GTM_ATOM4_CHANNEL5_IRQ_MODE);
	INTC_PSR(SPC5_ATOM4_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM4_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL5_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL5_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM4_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD5, ATOM4, CHANNEL5);
#elif (SPC5_GTM_ATOM4_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD5, ATOM4, CHANNEL5);
#endif
#endif /* SPC5_GTM_ATOM4_USE_CHANNEL5 */

/* ATOM4 CHANNEL6 */
#if (SPC5_GTM_ATOM4_USE_CHANNEL6 == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD5, ATOM4, CHANNEL6);
#elif (SPC5_GTM_ATOM4_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD5, ATOM4, CHANNEL6);

	gtm_atomSetIRQMode(&ATOMD5, ATOM_CHANNEL6, SPC5_GTM_ATOM4_CHANNEL6_IRQ_MODE);
	INTC_PSR(SPC5_ATOM4_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM4_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL6_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL6_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM4_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD5, ATOM4, CHANNEL6);
#elif (SPC5_GTM_ATOM4_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD5, ATOM4, CHANNEL6);
#endif
#endif /* SPC5_GTM_ATOM4_USE_CHANNEL6 */

/* ATOM4 CHANNEL7 */
#if (SPC5_GTM_ATOM4_USE_CHANNEL7 == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD5, ATOM4, CHANNEL7);
#elif (SPC5_GTM_ATOM4_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD5, ATOM4, CHANNEL7);

	gtm_atomSetIRQMode(&ATOMD5, ATOM_CHANNEL7, SPC5_GTM_ATOM4_CHANNEL7_IRQ_MODE);
	INTC_PSR(SPC5_ATOM4_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM4_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL7_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD5, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM4_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM4_CHANNEL7_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM4_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD5, ATOM4, CHANNEL7);
#elif (SPC5_GTM_ATOM4_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD5, ATOM4, CHANNEL7);
#endif
#endif /* SPC5_GTM_ATOM4_USE_CHANNEL7 */

#endif /* SPC5_GTM_USE_ATOM4 */


/* ATOM5 CHANNEL3 */
#if (SPC5_GTM_USE_ATOM5 == TRUE)
	ATOMD6.atom = &(GTM_ATOM_5);

	/* Get a reference to Interrupt Concentrator Module */
	ATOMD6.icmd = &ICMD1;

	/* Interrupt callbacks */
	ATOMD6.callbacks = (GTM_ATOM_Channel_Callbacks **)gtm_atom5_callbacks;

/* ATOM5 CHANNEL0 */
#if (SPC5_GTM_ATOM5_USE_CHANNEL0 == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD6, ATOM5, CHANNEL0);
#elif (SPC5_GTM_ATOM5_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD6, ATOM5, CHANNEL0);

	gtm_atomSetIRQMode(&ATOMD6, ATOM_CHANNEL0, SPC5_GTM_ATOM5_CHANNEL0_IRQ_MODE);
	INTC_PSR(SPC5_ATOM5_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM5_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL0_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL0, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL0_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL0_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM5_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD6, ATOM5, CHANNEL0);
#elif (SPC5_GTM_ATOM5_CHANNEL0_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD6, ATOM5, CHANNEL0);
#endif
#endif /* SPC5_GTM_ATOM5_USE_CHANNEL0 */

/* ATOM5 CHANNEL1 */
#if (SPC5_GTM_ATOM5_USE_CHANNEL1 == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD6, ATOM5, CHANNEL1);
#elif (SPC5_GTM_ATOM5_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD6, ATOM5, CHANNEL1);

	gtm_atomSetIRQMode(&ATOMD6, ATOM_CHANNEL1, SPC5_GTM_ATOM5_CHANNEL1_IRQ_MODE);
	INTC_PSR(SPC5_ATOM5_EVENT0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM5_EVENT0_INT_PRIORITY);

#if (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL1_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL1, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL1_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL1_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM5_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD6, ATOM5, CHANNEL1);
#elif (SPC5_GTM_ATOM5_CHANNEL1_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD6, ATOM5, CHANNEL1);
#endif
#endif /* SPC5_GTM_ATOM5_USE_CHANNEL1 */

/* ATOM5 CHANNEL2 */
#if (SPC5_GTM_ATOM5_USE_CHANNEL2 == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD6, ATOM5, CHANNEL2);
#elif (SPC5_GTM_ATOM5_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD6, ATOM5, CHANNEL2);

	gtm_atomSetIRQMode(&ATOMD6, ATOM_CHANNEL2, SPC5_GTM_ATOM5_CHANNEL2_IRQ_MODE);
	INTC_PSR(SPC5_ATOM5_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM5_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL2_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL2, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL2_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL2_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM5_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD6, ATOM5, CHANNEL2);
#elif (SPC5_GTM_ATOM5_CHANNEL2_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD6, ATOM5, CHANNEL2);
#endif
#endif /* SPC5_GTM_ATOM5_USE_CHANNEL2 */

/* ATOM5 CHANNEL3 */
#if (SPC5_GTM_ATOM5_USE_CHANNEL3 == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD6, ATOM5, CHANNEL3);
#elif (SPC5_GTM_ATOM5_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD6, ATOM5, CHANNEL3);

	gtm_atomSetIRQMode(&ATOMD6, ATOM_CHANNEL3, SPC5_GTM_ATOM5_CHANNEL3_IRQ_MODE);
	INTC_PSR(SPC5_ATOM5_EVENT1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM5_EVENT1_INT_PRIORITY);

#if (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL3_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL3, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL3_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL3_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM5_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD6, ATOM5, CHANNEL3);
#elif (SPC5_GTM_ATOM5_CHANNEL3_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD6, ATOM5, CHANNEL3);
#endif
#endif /* SPC5_GTM_ATOM5_USE_CHANNEL3 */

/* ATOM5 CHANNEL4 */
#if (SPC5_GTM_ATOM5_USE_CHANNEL4 == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD6, ATOM5, CHANNEL4);
#elif (SPC5_GTM_ATOM5_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD6, ATOM5, CHANNEL4);

	gtm_atomSetIRQMode(&ATOMD6, ATOM_CHANNEL4, SPC5_GTM_ATOM5_CHANNEL4_IRQ_MODE);
	INTC_PSR(SPC5_ATOM5_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM5_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL4_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL4, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL4_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL4_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM5_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD6, ATOM5, CHANNEL4);
#elif (SPC5_GTM_ATOM5_CHANNEL4_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD6, ATOM5, CHANNEL4);
#endif
#endif /* SPC5_GTM_ATOM5_USE_CHANNEL4 */

/* ATOM5 CHANNEL5 */
#if (SPC5_GTM_ATOM5_USE_CHANNEL5 == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD6, ATOM5, CHANNEL5);
#elif (SPC5_GTM_ATOM5_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD6, ATOM5, CHANNEL5);

	gtm_atomSetIRQMode(&ATOMD6, ATOM_CHANNEL5, SPC5_GTM_ATOM5_CHANNEL5_IRQ_MODE);
	INTC_PSR(SPC5_ATOM5_EVENT2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM5_EVENT2_INT_PRIORITY);

#if (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL5_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL5, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL5_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL5_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM5_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD6, ATOM5, CHANNEL5);
#elif (SPC5_GTM_ATOM5_CHANNEL5_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD6, ATOM5, CHANNEL5);
#endif
#endif /* SPC5_GTM_ATOM5_USE_CHANNEL5 */

/* ATOM5 CHANNEL6 */
#if (SPC5_GTM_ATOM5_USE_CHANNEL6 == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD6, ATOM5, CHANNEL6);
#elif (SPC5_GTM_ATOM5_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD6, ATOM5, CHANNEL6);

	gtm_atomSetIRQMode(&ATOMD6, ATOM_CHANNEL6, SPC5_GTM_ATOM5_CHANNEL6_IRQ_MODE);
	INTC_PSR(SPC5_ATOM5_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM5_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL6_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL6, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL6_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL6_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM5_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD6, ATOM5, CHANNEL6);
#elif (SPC5_GTM_ATOM5_CHANNEL6_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD6, ATOM5, CHANNEL6);
#endif
#endif /* SPC5_GTM_ATOM5_USE_CHANNEL6 */

/* ATOM5 CHANNEL7 */
#if (SPC5_GTM_ATOM5_USE_CHANNEL7 == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE)
	ATOM_CHANNEL_INIT_SOMI(&ATOMD6, ATOM5, CHANNEL7);
#elif (SPC5_GTM_ATOM5_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE)
	ATOM_CHANNEL_INIT_SOMC(&ATOMD6, ATOM5, CHANNEL7);

	gtm_atomSetIRQMode(&ATOMD6, ATOM_CHANNEL7, SPC5_GTM_ATOM5_CHANNEL7_IRQ_MODE);
	INTC_PSR(SPC5_ATOM5_EVENT3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_ATOM5_EVENT3_INT_PRIORITY);

#if (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU0_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU0);
#elif (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU0_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL7_INT_CCU0_ENABLED */

#if (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU1_ENABLED == TRUE)
#if (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL)
	gtm_atomEnableInt(&ATOMD6, ATOM_CHANNEL7, SPC5_GTM_ATOM_IRQ_ENABLE_CCU1);
#elif (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_ERROR)
#elif (SPC5_GTM_ATOM5_CHANNEL7_INT_CCU1_MODE == SPC5_GTM_ATOM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif /* SPC5_GTM_ATOM5_CHANNEL7_INT_CCU1_ENABLED */

#elif (SPC5_GTM_ATOM5_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_PWM)
	ATOM_CHANNEL_INIT_SOMP(&ATOMD6, ATOM5, CHANNEL7);
#elif (SPC5_GTM_ATOM5_CHANNEL7_OUTPUT_MODE == SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL)
	ATOM_CHANNEL_INIT_SOMS(&ATOMD6, ATOM5, CHANNEL7);
#endif
#endif /* SPC5_GTM_ATOM5_USE_CHANNEL7 */

#endif /* SPC5_GTM_USE_ATOM5 */
}

/**
 * @brief   Start ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @api
 */
void gtm_atomStart(GTM_ATOMDriver *atomd, uint8_t channel) {
	atomd->atom->GTM_ATOM_AGC_REG(GLB_CTRL).R = (ATOM_CH_ENABLE << (16UL + (ATOM_BIT_SHIFT * channel)));
	atomd->atom->GTM_ATOM_AGC_REG(OUTEN_STAT).R = (ATOM_CH_ENABLE << (ATOM_BIT_SHIFT * channel));
	atomd->atom->GTM_ATOM_AGC_REG(ENDIS_STAT).R = (ATOM_CH_ENABLE << (ATOM_BIT_SHIFT * channel));
}

/**
 * @brief   Stop ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @api
 */
void gtm_atomStop(GTM_ATOMDriver *atomd, uint8_t channel) {
	atomd->atom->GTM_ATOM_AGC_REG(GLB_CTRL).R = (ATOM_CH_DISABLE << (16UL + (ATOM_BIT_SHIFT * channel)));
	atomd->atom->GTM_ATOM_AGC_REG(OUTEN_STAT).R = (ATOM_CH_DISABLE << (ATOM_BIT_SHIFT * channel));
	atomd->atom->GTM_ATOM_AGC_REG(ENDIS_STAT).R = (ATOM_CH_DISABLE << (ATOM_BIT_SHIFT * channel));
}

/**
 * @brief   Set ATOM operating mode for specified channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] out_mode    Operating mode
 *
 * @sa
 * SPC5_GTM_ATOM_OUTPUT_MODE_IMMEDIATE, SPC5_GTM_ATOM_OUTPUT_MODE_COMPARE,
 * SPC5_GTM_ATOM_OUTPUT_MODE_PWM, SPC5_GTM_ATOM_OUTPUT_MODE_SERIAL
 *
 * @api
 */
void gtm_atomSetMode(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t out_mode) {

	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,CTRL).B.MODE = out_mode;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,CTRL).B.MODE = out_mode;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,CTRL).B.MODE = out_mode;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,CTRL).B.MODE = out_mode;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,CTRL).B.MODE = out_mode;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,CTRL).B.MODE = out_mode;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,CTRL).B.MODE = out_mode;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,CTRL).B.MODE = out_mode;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set time base source for compare unit 1
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] tbase   Time base source (TBS_TS1/TBS_TS2)
 *
 * @sa
 * SPC5_GTM_ATOM_COMPARE_USE_TBU_TS1, SPC5_GTM_ATOM_COMPARE_USE_TBU_TS2
 *
 * @api
 */
void gtm_atomSetCM1TimeBaseSource(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t tbase) {

	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,CTRL).B.TB12_SEL = tbase;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,CTRL).B.TB12_SEL = tbase;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,CTRL).B.TB12_SEL = tbase;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,CTRL).B.TB12_SEL = tbase;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,CTRL).B.TB12_SEL = tbase;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,CTRL).B.TB12_SEL = tbase;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,CTRL).B.TB12_SEL = tbase;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,CTRL).B.TB12_SEL = tbase;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set ATOM comparing strategy for specified channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] strategy    Operating mode
 *
 * @sa
 * SPC5_GTM_ATOM_COMPARE_STRATEGY_GREATER_EQUAL, SPC5_GTM_ATOM_COMPARE_STRATEGY_LESS_EQUAL
 *
 * @api
 */
void gtm_atomSetCompareStrategy(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t strategy) {

	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,CTRL).B.CMP_CTRL = strategy;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,CTRL).B.CMP_CTRL = strategy;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,CTRL).B.CMP_CTRL = strategy;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,CTRL).B.CMP_CTRL = strategy;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,CTRL).B.CMP_CTRL = strategy;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,CTRL).B.CMP_CTRL = strategy;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,CTRL).B.CMP_CTRL = strategy;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,CTRL).B.CMP_CTRL = strategy;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set ATOM comparing strategy for specified channel
 *
 * @param[in] atomd           GTM ATOM driver pointer
 *
 * @param[in] channel         GTM ATOM channel number
 *
 * @param[in] cmp_strategy    Compare strategy mode
 *
 * @sa
 * SPC5_GTM_ATOM_COMPARE_CCU0_TS0_CCU1_TS12_PARALLEL_0, SPC5_GTM_ATOM_COMPARE_CCU0_TS0_CCU1_TS12_PARALLEL_1,
 * SPC5_GTM_ATOM_COMPARE_ONLY_CCU0_TS0, SPC5_GTM_ATOM_COMPARE_ONLY_CCU1_TS12,
 * SPC5_GTM_ATOM_COMPARE_CCU0_THEN_CCU1_TS0, SPC5_GTM_ATOM_COMPARE_CCU0_THEN_CCU1_TS12,
 * SPC5_GTM_ATOM_COMPARE_CCU0_TS0_THEN_CCU1_TS12
 *
 * @api
 */
void gtm_atomCompareControl(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t cmp_strategy) {
	uint8_t value;
	uint8_t acb;

	switch(channel) {
	case 0:
		acb = atomd->atom->GTM_CH_REG(0,CTRL).B.ACB & 0x03U;
		cmp_strategy <<= 2U;
		value = (acb | cmp_strategy);
		atomd->atom->GTM_CH_REG(0,CTRL).B.ACB = value;
		break;
	case 1:
		acb = atomd->atom->GTM_CH_REG(1,CTRL).B.ACB & 0x03U;
		cmp_strategy <<= 2U;
		value = (acb | cmp_strategy);
		atomd->atom->GTM_CH_REG(1,CTRL).B.ACB = value;
		break;
	case 2:
		acb = atomd->atom->GTM_CH_REG(2,CTRL).B.ACB & 0x03U;
		cmp_strategy <<= 2U;
		value = (acb | cmp_strategy);
		atomd->atom->GTM_CH_REG(2,CTRL).B.ACB = value;
		break;
	case 3:
		acb = atomd->atom->GTM_CH_REG(3,CTRL).B.ACB & 0x03U;
		cmp_strategy <<= 2U;
		value = (acb | cmp_strategy);
		atomd->atom->GTM_CH_REG(3,CTRL).B.ACB = value;
		break;
	case 4:
		acb = atomd->atom->GTM_CH_REG(4,CTRL).B.ACB & 0x03U;
		cmp_strategy <<= 2U;
		value = (acb | cmp_strategy);
		atomd->atom->GTM_CH_REG(4,CTRL).B.ACB = value;
		break;
	case 5:
		acb = atomd->atom->GTM_CH_REG(5,CTRL).B.ACB & 0x03U;
		cmp_strategy <<= 2U;
		value = (acb | cmp_strategy);
		atomd->atom->GTM_CH_REG(5,CTRL).B.ACB = value;
		break;
	case 6:
		acb = atomd->atom->GTM_CH_REG(6,CTRL).B.ACB & 0x03U;
		cmp_strategy <<= 2U;
		value = (acb | cmp_strategy);
		atomd->atom->GTM_CH_REG(6,CTRL).B.ACB = value;
		break;
	case 7:
		acb = atomd->atom->GTM_CH_REG(7,CTRL).B.ACB & 0x03U;
		cmp_strategy <<= 2U;
		value = (acb | cmp_strategy);
		atomd->atom->GTM_CH_REG(7,CTRL).B.ACB = value;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set Signal Level value
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] sig_level   GTM ATOM Signal Level value
 *
 * @sa
 *
 * @api
 */
void gtm_atomSetSignalLevel(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t sig_level) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,CTRL).B.SL = sig_level;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,CTRL).B.SL = sig_level;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,CTRL).B.SL = sig_level;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,CTRL).B.SL = sig_level;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,CTRL).B.SL = sig_level;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,CTRL).B.SL = sig_level;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,CTRL).B.SL = sig_level;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,CTRL).B.SL = sig_level;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set Signal Level Control mode
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] sl_ctrl     Signal Level Control value
 *
 * @sa
 *
 * @api
 */
void gtm_atomSignalLevelControl(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t sl_ctrl) {
	uint8_t value;
	uint8_t acb;

	switch(channel) {
	case 0:
		acb = atomd->atom->GTM_CH_REG(0,CTRL).B.ACB & 0x1CU;
		value = (acb | sl_ctrl);
		atomd->atom->GTM_CH_REG(0,CTRL).B.ACB = value;
		break;
	case 1:
		acb = atomd->atom->GTM_CH_REG(1,CTRL).B.ACB & 0x1CU;
		value = (acb | sl_ctrl);
		atomd->atom->GTM_CH_REG(1,CTRL).B.ACB = value;
		break;
	case 2:
		acb = atomd->atom->GTM_CH_REG(2,CTRL).B.ACB & 0x1CU;
		value = (acb | sl_ctrl);
		atomd->atom->GTM_CH_REG(2,CTRL).B.ACB = value;
		break;
	case 3:
		acb = atomd->atom->GTM_CH_REG(3,CTRL).B.ACB & 0x1CU;
		value = (acb | sl_ctrl);
		atomd->atom->GTM_CH_REG(3,CTRL).B.ACB = value;
		break;
	case 4:
		acb = atomd->atom->GTM_CH_REG(4,CTRL).B.ACB & 0x1CU;
		value = (acb | sl_ctrl);
		atomd->atom->GTM_CH_REG(4,CTRL).B.ACB = value;
		break;
	case 5:
		acb = atomd->atom->GTM_CH_REG(5,CTRL).B.ACB & 0x1CU;
		value = (acb | sl_ctrl);
		atomd->atom->GTM_CH_REG(5,CTRL).B.ACB = value;
		break;
	case 6:
		acb = atomd->atom->GTM_CH_REG(6,CTRL).B.ACB & 0x1CU;
		value = (acb | sl_ctrl);
		atomd->atom->GTM_CH_REG(6,CTRL).B.ACB = value;
		break;
	case 7:
		acb = atomd->atom->GTM_CH_REG(7,CTRL).B.ACB & 0x1CU;
		value = (acb | sl_ctrl);
		atomd->atom->GTM_CH_REG(7,CTRL).B.ACB = value;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Enable/Disable ARU mode for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] enable      Enable/Disable
 *
 * @sa
 *
 * @api
 */
void gtm_atomARUEnable(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t enable) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,CTRL).B.ARU_EN = enable;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,CTRL).B.ARU_EN = enable;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,CTRL).B.ARU_EN = enable;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,CTRL).B.ARU_EN = enable;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,CTRL).B.ARU_EN = enable;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,CTRL).B.ARU_EN = enable;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,CTRL).B.ARU_EN = enable;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,CTRL).B.ARU_EN = enable;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set ARU control bits for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] mode        ARU mode
 *
 * @api
 */
void gtm_atomARUMode(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t mode) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,CTRL).B.ACB = mode;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,CTRL).B.ACB = mode;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,CTRL).B.ACB = mode;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,CTRL).B.ACB = mode;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,CTRL).B.ACB = mode;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,CTRL).B.ACB = mode;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,CTRL).B.ACB = mode;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,CTRL).B.ACB = mode;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set Clock Source for a specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] mode        GTM ARU mode
 *
 * @param[in] clk_src     Clock Source to be set (CMU_CLK0-7)
 *
 * @api
 */
void gtm_atomClkSrc(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t mode, uint8_t clk_src) {
	if(mode == SPC5_GTM_ATOM_ARU_MODE_NONE) {
		switch(channel) {
		case 0:
			atomd->atom->GTM_CH_REG(0,CTRL).B.CLK_SRC_SR = clk_src;
			break;
		case 1:
			atomd->atom->GTM_CH_REG(1,CTRL).B.CLK_SRC_SR = clk_src;
			break;
		case 2:
			atomd->atom->GTM_CH_REG(2,CTRL).B.CLK_SRC_SR = clk_src;
			break;
		case 3:
			atomd->atom->GTM_CH_REG(3,CTRL).B.CLK_SRC_SR = clk_src;
			break;
		case 4:
			atomd->atom->GTM_CH_REG(4,CTRL).B.CLK_SRC_SR = clk_src;
			break;
		case 5:
			atomd->atom->GTM_CH_REG(5,CTRL).B.CLK_SRC_SR = clk_src;
			break;
		case 6:
			atomd->atom->GTM_CH_REG(6,CTRL).B.CLK_SRC_SR = clk_src;
			break;
		case 7:
			atomd->atom->GTM_CH_REG(7,CTRL).B.CLK_SRC_SR = clk_src;
			break;
		default:
			/* MISRA check */
			break;
		}
	}
}

/**
 * @brief   Set Compare Register 0 value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] value       Compare value
 *
 * @api
 */
void gtm_atomSetCompare0(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t value) {
	value &= 0x00FFFFFFUL;
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,CM0).R = value;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,CM0).R = value;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,CM0).R = value;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,CM0).R = value;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,CM0).R = value;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,CM0).R = value;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,CM0).R = value;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,CM0).R = value;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set Compare Register 1 value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] value      Compare value
 *
 * @api
 */
void gtm_atomSetCompare1(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t value) {
	value &= 0x00FFFFFFUL;
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,CM1).R = value;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,CM1).R = value;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,CM1).R = value;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,CM1).R = value;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,CM1).R = value;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,CM1).R = value;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,CM1).R = value;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,CM1).R = value;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Get Compare Register 0 value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @return compared value for CM0
 *
 * @api
 */
uint32_t gtm_atomGetCompare0(GTM_ATOMDriver *atomd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = atomd->atom->GTM_CH_REG(0,CM0).R;
		break;
	case 1:
		value = atomd->atom->GTM_CH_REG(1,CM0).R;
		break;
	case 2:
		value = atomd->atom->GTM_CH_REG(2,CM0).R;
		break;
	case 3:
		value = atomd->atom->GTM_CH_REG(3,CM0).R;
		break;
	case 4:
		value = atomd->atom->GTM_CH_REG(4,CM0).R;
		break;
	case 5:
		value = atomd->atom->GTM_CH_REG(5,CM0).R;
		break;
	case 6:
		value = atomd->atom->GTM_CH_REG(6,CM0).R;
		break;
	case 7:
		value = atomd->atom->GTM_CH_REG(7,CM0).R;
		break;
	default:
		value = 0;
		break;
	}

	return value & 0x00FFFFFFUL;
}

/**
 * @brief   Get Compare Register 1 value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @return compared value for CM1
 *
 * @api
 */
uint32_t gtm_atomGetCompare1(GTM_ATOMDriver *atomd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = atomd->atom->GTM_CH_REG(0,CM1).R;
		break;
	case 1:
		value = atomd->atom->GTM_CH_REG(1,CM1).R;
		break;
	case 2:
		value = atomd->atom->GTM_CH_REG(2,CM1).R;
		break;
	case 3:
		value = atomd->atom->GTM_CH_REG(3,CM1).R;
		break;
	case 4:
		value = atomd->atom->GTM_CH_REG(4,CM1).R;
		break;
	case 5:
		value = atomd->atom->GTM_CH_REG(5,CM1).R;
		break;
	case 6:
		value = atomd->atom->GTM_CH_REG(6,CM1).R;
		break;
	case 7:
		value = atomd->atom->GTM_CH_REG(7,CM1).R;
		break;
	default:
		value = 0;
		break;
	}

	return value & 0x00FFFFFFUL;
}

/**
 * @brief   Set Shadow Register 0 value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] value       Shadow Register value
 *
 * @api
 */
void gtm_atomSetShadowReg0(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t value) {
	value &= 0x00FFFFFFUL;
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,SR0).R = value;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,SR0).R = value;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,SR0).R = value;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,SR0).R = value;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,SR0).R = value;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,SR0).R = value;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,SR0).R = value;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,SR0).R = value;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set Shadow Register 1 value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] value       Shadow Register value
 *
 * @api
 */
void gtm_atomSetShadowReg1(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t value) {
	value &= 0x00FFFFFFUL;
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,SR1).R = value;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,SR1).R = value;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,SR1).R = value;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,SR1).R = value;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,SR1).R = value;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,SR1).R = value;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,SR1).R = value;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,SR1).R = value;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Get Shadow Register 0 value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @return shadow register
 *
 * @api
 */
uint32_t gtm_atomGetShadowReg0(GTM_ATOMDriver *atomd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = atomd->atom->GTM_CH_REG(0,SR0).R;
		break;
	case 1:
		value = atomd->atom->GTM_CH_REG(1,SR0).R;
		break;
	case 2:
		value = atomd->atom->GTM_CH_REG(2,SR0).R;
		break;
	case 3:
		value = atomd->atom->GTM_CH_REG(3,SR0).R;
		break;
	case 4:
		value = atomd->atom->GTM_CH_REG(4,SR0).R;
		break;
	case 5:
		value = atomd->atom->GTM_CH_REG(5,SR0).R;
		break;
	case 6:
		value = atomd->atom->GTM_CH_REG(6,SR0).R;
		break;
	case 7:
		value = atomd->atom->GTM_CH_REG(7,SR0).R;
		break;
	default:
		value = 0;
		break;
	}

	return value & 0x00FFFFFFUL;
}

/**
 * @brief   Get Shadow Register 1 value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @return shadow register
 *
 * @api
 */
uint32_t gtm_atomGetShadowReg1(GTM_ATOMDriver *atomd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = atomd->atom->GTM_CH_REG(0,SR1).R;
		break;
	case 1:
		value = atomd->atom->GTM_CH_REG(1,SR1).R;
		break;
	case 2:
		value = atomd->atom->GTM_CH_REG(2,SR1).R;
		break;
	case 3:
		value = atomd->atom->GTM_CH_REG(3,SR1).R;
		break;
	case 4:
		value = atomd->atom->GTM_CH_REG(4,SR1).R;
		break;
	case 5:
		value = atomd->atom->GTM_CH_REG(5,SR1).R;
		break;
	case 6:
		value = atomd->atom->GTM_CH_REG(6,SR1).R;
		break;
	case 7:
		value = atomd->atom->GTM_CH_REG(7,SR1).R;
		break;
	default:
		value = 0;
		break;
	}

	return value & 0x00FFFFFFUL;
}

/**
 * @brief   Set ARU data source for ATOM channel (read address 0)
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] source      GTM ARU data source
 *
 * @api
 */
void gtm_atomSetDataSource(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t source) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,RDADDR).R &= 0xFFFFFE00UL;
		atomd->atom->GTM_CH_REG(0,RDADDR).R |= (0x000001FFUL & source);
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,RDADDR).R &= 0xFFFFFE00UL;
		atomd->atom->GTM_CH_REG(1,RDADDR).R |= (0x000001FFUL & source);
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,RDADDR).R &= 0xFFFFFE00UL;
		atomd->atom->GTM_CH_REG(2,RDADDR).R |= (0x000001FFUL & source);
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,RDADDR).R &= 0xFFFFFE00UL;
		atomd->atom->GTM_CH_REG(3,RDADDR).R |= (0x000001FFUL & source);
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,RDADDR).R &= 0xFFFFFE00UL;
		atomd->atom->GTM_CH_REG(4,RDADDR).R |= (0x000001FFUL & source);
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,RDADDR).R &= 0xFFFFFE00UL;
		atomd->atom->GTM_CH_REG(5,RDADDR).R |= (0x000001FFUL & source);
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,RDADDR).R &= 0xFFFFFE00UL;
		atomd->atom->GTM_CH_REG(6,RDADDR).R |= (0x000001FFUL & source);
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,RDADDR).R &= 0xFFFFFE00UL;
		atomd->atom->GTM_CH_REG(7,RDADDR).R |= (0x000001FFUL & source);
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set ARU data source for ATOM channel (read address 1)
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] source      GTM ARU data source
 *
 * @api
 */
void gtm_atomSetDataSource1(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t source) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,RDADDR).R &= 0xFE00FFFFUL;
		atomd->atom->GTM_CH_REG(0,RDADDR).R |= ((0x000001FFUL & source) << 16UL);
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,RDADDR).R &= 0xFE00FFFFUL;
		atomd->atom->GTM_CH_REG(1,RDADDR).R |= ((0x000001FFUL & source) << 16UL);
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,RDADDR).R &= 0xFE00FFFFUL;
		atomd->atom->GTM_CH_REG(2,RDADDR).R |= ((0x000001FFUL & source) << 16UL);
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,RDADDR).R &= 0xFE00FFFFUL;
		atomd->atom->GTM_CH_REG(3,RDADDR).R |= ((0x000001FFUL & source) << 16UL);
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,RDADDR).R &= 0xFE00FFFFUL;
		atomd->atom->GTM_CH_REG(4,RDADDR).R |= ((0x000001FFUL & source) << 16UL);
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,RDADDR).R &= 0xFE00FFFFUL;
		atomd->atom->GTM_CH_REG(5,RDADDR).R |= ((0x000001FFUL & source) << 16UL);
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,RDADDR).R &= 0xFE00FFFFUL;
		atomd->atom->GTM_CH_REG(6,RDADDR).R |= ((0x000001FFUL & source) << 16UL);
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,RDADDR).R &= 0xFE00FFFFUL;
		atomd->atom->GTM_CH_REG(7,RDADDR).R |= ((0x000001FFUL & source) << 16UL);
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Set ATOM Channel IRQ mode (Level, Pulse, Notify, Single)
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] mode        GTM ATOM channel IRQ mode
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1, ATOM_CHANNEL2, ATOM_CHANNEL3, ATOM_CHANNEL4, ATOM_CHANNEL5, ATOM_CHANNEL6, ATOM_CHANNEL7,
 * <br>
 * SPC5_GTM_ATOM_IRQ_MODE_LEVEL, SPC5_GTM_ATOM_IRQ_MODE_PULSE,
 * SPC5_GTM_ATOM_IRQ_MODE_PULSE_NOTIFY, SPC5_GTM_ATOM_IRQ_MODE_SINGLE_PULSE
 *
 * @api
 */
void gtm_atomSetIRQMode(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t mode) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Enable ATOM Channel interrupt
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] int_num   GTM ATOM interrupt to enable
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1, ATOM_CHANNEL2, ATOM_CHANNEL3, ATOM_CHANNEL4, ATOM_CHANNEL5, ATOM_CHANNEL6, ATOM_CHANNEL7,
 * <br>
 * SPC5_GTM_ATOM_IRQ_STATUS_CCU0, SPC5_GTM_ATOM_IRQ_STATUS_CCU1
 *
 * @api
 */
void gtm_atomEnableInt(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t int_num) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,IRQ_EN).R |= int_num;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,IRQ_EN).R |= int_num;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,IRQ_EN).R |= int_num;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,IRQ_EN).R |= int_num;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,IRQ_EN).R |= int_num;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,IRQ_EN).R |= int_num;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,IRQ_EN).R |= int_num;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,IRQ_EN).R |= int_num;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Software notification of ATOM Channel interrupt
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] int_num     GTM ATOM interrupt to notify
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1, ATOM_CHANNEL2, ATOM_CHANNEL3, ATOM_CHANNEL4, ATOM_CHANNEL5, ATOM_CHANNEL6, ATOM_CHANNEL7,
 * <br>
 * SPC5_GTM_ATOM_IRQ_STATUS_CCU0, SPC5_GTM_ATOM_IRQ_STATUS_CCU1
 *
 * @api
 */
void gtm_atomNotifyInt(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t int_num) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,IRQ_FORCINT).R = int_num;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,IRQ_FORCINT).R = int_num;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,IRQ_FORCINT).R = int_num;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,IRQ_FORCINT).R = int_num;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,IRQ_FORCINT).R = int_num;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,IRQ_FORCINT).R = int_num;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,IRQ_FORCINT).R = int_num;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,IRQ_FORCINT).R = int_num;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Acknowledge ATOM Channel interrupt
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] int_num   GTM ATOM interrupt to acknowledge
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1, ATOM_CHANNEL2, ATOM_CHANNEL3, ATOM_CHANNEL4, ATOM_CHANNEL5, ATOM_CHANNEL6, ATOM_CHANNEL7,
 * <br>
 * SPC5_GTM_ATOM_IRQ_STATUS_CCU0, SPC5_GTM_ATOM_IRQ_STATUS_CCU1
 *
 * @api
 */
void gtm_atomAckInt(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t int_num) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,IRQ_NOTIFY).R = int_num;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,IRQ_NOTIFY).R = int_num;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,IRQ_NOTIFY).R = int_num;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,IRQ_NOTIFY).R = int_num;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,IRQ_NOTIFY).R = int_num;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,IRQ_NOTIFY).R = int_num;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,IRQ_NOTIFY).R = int_num;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,IRQ_NOTIFY).R = int_num;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Disable ATOM Channel interrupt
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] int_num   GTM ATOM interrupt to disable
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1, ATOM_CHANNEL2, ATOM_CHANNEL3, ATOM_CHANNEL4, ATOM_CHANNEL5, ATOM_CHANNEL6, ATOM_CHANNEL7,
 * <br>
 * SPC5_GTM_ATOM_IRQ_STATUS_CCU0, SPC5_GTM_ATOM_IRQ_STATUS_CCU1
 *
 * @api
 */
void gtm_atomDisableInt(GTM_ATOMDriver *atomd, uint8_t channel, uint8_t int_num) {
	switch(channel) {
	case 0:
		atomd->atom->GTM_CH_REG(0,IRQ_EN).R &= ~int_num;
		break;
	case 1:
		atomd->atom->GTM_CH_REG(1,IRQ_EN).R &= ~int_num;
		break;
	case 2:
		atomd->atom->GTM_CH_REG(2,IRQ_EN).R &= ~int_num;
		break;
	case 3:
		atomd->atom->GTM_CH_REG(3,IRQ_EN).R &= ~int_num;
		break;
	case 4:
		atomd->atom->GTM_CH_REG(4,IRQ_EN).R &= ~int_num;
		break;
	case 5:
		atomd->atom->GTM_CH_REG(5,IRQ_EN).R &= ~int_num;
		break;
	case 6:
		atomd->atom->GTM_CH_REG(6,IRQ_EN).R &= ~int_num;
		break;
	case 7:
		atomd->atom->GTM_CH_REG(7,IRQ_EN).R &= ~int_num;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Return ATOM Channel interrupt status
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @return Interrupt status
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1, ATOM_CHANNEL2, ATOM_CHANNEL3, ATOM_CHANNEL4, ATOM_CHANNEL5, ATOM_CHANNEL6, ATOM_CHANNEL7,
 * <br>
 * SPC5_GTM_ATOM_IRQ_STATUS_CCU0, SPC5_GTM_ATOM_IRQ_STATUS_CCU1
 *
 * @api
 */
uint32_t gtm_atomGetIntStatus(GTM_ATOMDriver *atomd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = atomd->atom->GTM_CH_REG(0,IRQ_NOTIFY).R;
		break;
	case 1:
		value = atomd->atom->GTM_CH_REG(1,IRQ_NOTIFY).R;
		break;
	case 2:
		value = atomd->atom->GTM_CH_REG(2,IRQ_NOTIFY).R;
		break;
	case 3:
		value = atomd->atom->GTM_CH_REG(3,IRQ_NOTIFY).R;
		break;
	case 4:
		value = atomd->atom->GTM_CH_REG(4,IRQ_NOTIFY).R;
		break;
	case 5:
		value = atomd->atom->GTM_CH_REG(5,IRQ_NOTIFY).R;
		break;
	case 6:
		value = atomd->atom->GTM_CH_REG(6,IRQ_NOTIFY).R;
		break;
	case 7:
		value = atomd->atom->GTM_CH_REG(7,IRQ_NOTIFY).R;
		break;
	default:
		value = 0;
		break;
	}

	return value;
}

/**
 * @brief   Return ATOM Channel interrupt enabled
 *
 * @param[in] atomd      GTM ATOM driver pointer
 *
 * @param[in] channel    GTM ATOM channel number
 *
 * @return Interrupt enabled
 *
 * @sa
 * ATOM_CHANNEL0, ATOM_CHANNEL1, ATOM_CHANNEL2, ATOM_CHANNEL3, ATOM_CHANNEL4, ATOM_CHANNEL5, ATOM_CHANNEL6, ATOM_CHANNEL7,
 * <br>
 * SPC5_GTM_ATOM_IRQ_STATUS_CCU0, SPC5_GTM_ATOM_IRQ_STATUS_CCU1
 *
 * @api
 */
uint32_t gtm_atomGetIntEnabled(GTM_ATOMDriver *atomd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = atomd->atom->GTM_CH_REG(0,IRQ_EN).R;
		break;
	case 1:
		value = atomd->atom->GTM_CH_REG(1,IRQ_EN).R;
		break;
	case 2:
		value = atomd->atom->GTM_CH_REG(2,IRQ_EN).R;
		break;
	case 3:
		value = atomd->atom->GTM_CH_REG(3,IRQ_EN).R;
		break;
	case 4:
		value = atomd->atom->GTM_CH_REG(4,IRQ_EN).R;
		break;
	case 5:
		value = atomd->atom->GTM_CH_REG(5,IRQ_EN).R;
		break;
	case 6:
		value = atomd->atom->GTM_CH_REG(6,IRQ_EN).R;
		break;
	case 7:
		value = atomd->atom->GTM_CH_REG(7,IRQ_EN).R;
		break;
	default:
		value = 0;
		break;
	}

	return value;
}

/**
 * @brief   Set Period value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] period      Period value in ticks
 *
 * @api
 */
void gtm_atomSetPeriod(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t period) {
	gtm_atomSetShadowReg0(atomd, channel, period);
}

/**
 * @brief   Set Duty cycle value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @param[in] duty       Duty cycle value in ticks
 *
 * @api
 */
void gtm_atomSetDuty(GTM_ATOMDriver *atomd, uint8_t channel, uint32_t duty) {
	gtm_atomSetShadowReg1(atomd, channel, duty);

}

/**
 * @brief   Get Period value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @return shadow reg0 register
 *
 * @api
 */
uint32_t gtm_atomGetPeriod(GTM_ATOMDriver *atomd, uint8_t channel) {
	return gtm_atomGetShadowReg0(atomd, channel);
}

/**
 * @brief   Get Duty cycle value for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @return shadow reg1 register
 *
 * @api
 */
uint32_t gtm_atomGetDuty(GTM_ATOMDriver *atomd, uint8_t channel) {
	return gtm_atomGetShadowReg1(atomd, channel);
}

/**
 * @brief   Disable Output for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @api
 */
void gtm_atomOutputDisable(GTM_ATOMDriver *atomd, uint8_t channel) {
       atomd->atom->GTM_ATOM_AGC_REG(OUTEN_STAT).R = (ATOM_CH_DISABLE << (ATOM_BIT_SHIFT * channel));
}

/**
 * @brief   Enable Output for specified ATOM channel
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] channel     GTM ATOM channel number
 *
 * @api
 */
void gtm_atomOutputEnable(GTM_ATOMDriver *atomd, uint8_t channel) {
       atomd->atom->GTM_ATOM_AGC_REG(OUTEN_STAT).R = (ATOM_CH_ENABLE << (ATOM_BIT_SHIFT * channel));
}

/**
 * @brief   Disable Output for specified number of ATOM channels
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] mask        GTM ATOM channels mask
 *
 * @api
 */
void gtm_atomOutputDisableExt(GTM_ATOMDriver *atomd, uint32_t mask) {
       atomd->atom->GTM_ATOM_AGC_REG(OUTEN_STAT).R = mask;
}

/**
 * @brief   Enable Output for specified number of ATOM channels
 *
 * @param[in] atomd       GTM ATOM driver pointer
 *
 * @param[in] mask        GTM ATOM channels mask
 *
 * @api
 */
void gtm_atomOutputEnableExt(GTM_ATOMDriver *atomd, uint32_t mask) {
       atomd->atom->GTM_ATOM_AGC_REG(OUTEN_STAT).R = mask;
}
/*lint +e621*/
#endif
/** @} */
