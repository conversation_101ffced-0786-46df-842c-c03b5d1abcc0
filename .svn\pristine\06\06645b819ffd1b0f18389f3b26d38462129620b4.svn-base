/****************************************************************************
*
* Copyright © 2019-2020 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
*
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED,
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/
#include "isb.h"
#include "isb_cfg.h"
#include "../sys/auto/mcs2.h"
/*===========================================================================*/
/* Module local definitions.                                                 */
/*===========================================================================*/
#define ISB_PSM_BASE_OFFSET                       0UL

/*===========================================================================*/
/* Module exported variables.                                                */
/*===========================================================================*/

/*===========================================================================*/
/* Module local types.                                                       */
/*===========================================================================*/

/*===========================================================================*/
/* Module local variables.                                                   */
/*===========================================================================*/

static ISB_Status isb_status = {
	ISB_UNINIT
};

/*===========================================================================*/
/* Module local functions.                                                   */
/*===========================================================================*/

/*===========================================================================*/
/* Module exported functions.                                                */
/*===========================================================================*/

/**
 * @brief : ENGINE Driver instance.
 *
 */
ISBDriver ISBD;


void isbInit( ISBDriver *isbd) {
	isbd->status = &isb_status;
	isbd->config = NULL;
	isbd->status->state = ISB_INIT;
}

void isbStart(ISBDriver *isbd, ISB_Config *config) {
	if (config == NULL) {
		return;
	}
	isbd->config = config;
	isbd->status->state = ISB_WAIT_NEW_CONFIG;
}

void isbSetup(ISBDriver *isbd) {
    uint32_T i;

	for(i = 0; i < isbd->config->system_cylinder; i++) {
		if(isbd->config->cylinder_config[i]!= NULL) {
			isbSetupCylinder(isbd, isbd->config, i);
		}
	}
}

void isbSetupCylinder(ISBDriver *isbd, ISB_Config *config, uint32_T cylinder) {
	(void) config;
	uint32_T psm_start_address = ISB_PSM_BASE_OFFSET;  /* could be different */
	uint32_T scratch = 0UL;
	uint32_T psm_size;
	uint32_T j;
	uint32_T duration, period, duty, n_pulse;

	uint32_T psm_data_word = 22UL; /*scratch var, cylinder_index, output_delay, epws, epws_last_pulse_Ton,
	                               epws_timeout, coil_loading_step, coil_unloading_step, pmos_duration,
	                               nmos_duration + 12(4 * 3) epws_data_word */
	GTM_PSMDriver *psmd;
	uint8_T psm_channel;
	uint32_T coil_supply_remodulation;

	psmd = config->cylinder_config[cylinder]->psmd;
	psm_channel = (uint8_T) config->cylinder_config[cylinder]->psm_data_channel;

	coil_supply_remodulation = (config->cylinder_config[cylinder]->coil_loading_step + config->cylinder_config[cylinder]->coil_unloading_step);

	/*
	 * 3 = is the word of remodulation, duration, period and duty
	 * multiply x 2 to take in account the fake word (0x0000000),
	 *
	 */
	psm_size = ((psm_data_word + (coil_supply_remodulation * 3UL)) * 2UL);

	gtm_psmStop(psmd, psm_channel);

	gtm_psmFlush(psmd, psm_channel);
	if(cylinder != 0) {
		psm_start_address += (cylinder * psm_size);
	}
	gtm_psmSetStartAddress(psmd, psm_channel, psm_start_address);
	gtm_psmSetEndAddress(psmd, psm_channel, (psm_start_address + (psm_size - 1UL)));
	for(j=0;j< psm_size;j++) {
		gtm_psmWrite(psmd, psm_channel, 0xFFFFFFFF);
	}
	gtm_psmFlush(psmd, psm_channel);

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, (psm_size / 2UL));

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->cylinder_index); /* Cylinder index */

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->output_delay);   /* output delay   */

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->epws);           /* epws mode      */

	/*Upload EPWS data configuration */
	for (j = 0; j < ISB_EPWS_PHASE_NUM; j++) {
		n_pulse  = config->cylinder_config[cylinder]->epws_data[j]->num_pulse;
		period   = config->cylinder_config[cylinder]->epws_data[j]->epws_period;
		duty     = config->cylinder_config[cylinder]->epws_data[j]->epws_duty;

		gtm_psmWrite(psmd, psm_channel, scratch);
		gtm_psmWrite(psmd, psm_channel, n_pulse);

		gtm_psmWrite(psmd, psm_channel, scratch);
		gtm_psmWrite(psmd, psm_channel, period);

		gtm_psmWrite(psmd, psm_channel, scratch);
		gtm_psmWrite(psmd, psm_channel, duty);
	}

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->epws_last_pulse_Ton); /* epws_last_pulse  */

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->epws_timeout); /* epws timeout  */

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->pmos_duration); /* pmos_duration  */

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->nmos_duration); /* nmos_duration  */


	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->coil_loading_step);

	gtm_psmWrite(psmd, psm_channel, scratch);
	gtm_psmWrite(psmd, psm_channel, config->cylinder_config[cylinder]->coil_unloading_step);
	/* dx */
	for(j = 0; j < coil_supply_remodulation; j++ ) {
		duration = config->cylinder_config[cylinder]->coil_prog[j]->duration;
		period   = config->cylinder_config[cylinder]->coil_prog[j]->period;
		duty     = config->cylinder_config[cylinder]->coil_prog[j]->duty;
		if(j == 1){ /* D2 */
			duration = duration - period;
		}
		if(j == (coil_supply_remodulation-1)){ /* D8 */
			duration = ((duration - period) + duty);
		}

		gtm_psmWrite(psmd, psm_channel, scratch);
		gtm_psmWrite(psmd, psm_channel, duration);

		gtm_psmWrite(psmd, psm_channel, scratch);
		gtm_psmWrite(psmd, psm_channel, period);

		gtm_psmWrite(psmd, psm_channel, scratch);
		gtm_psmWrite(psmd, psm_channel, duty);
	}

	/* Start providing data */
	gtm_psmStart(psmd, psm_channel);

    if (isbGetState(isbd) == ISB_WAIT_NEW_CONFIG)
    {
        isbSetState(isbd, ISB_WAIT_FIRST_TRIG);
    }
    else if (isbGetState(isbd) == ISB_RUNNING)
    {
        isbSetState(isbd, ISB_CONF_READY);
    }
}

isb_state_t isbGetState(ISBDriver *isbd) {
	return isbd->status->state;
}

void isbSetState(ISBDriver *isbd, isb_state_t state) {
	isbd->status->state = state;
}

void isbSetTimeout(ISBDriver *isbd, uint32_T cylinder, uint8_T timeout_mode, uint32_T timeout_value ) {
	gtm_timSetTimeoutValue(isbd->config->cylinder_config[cylinder]->timd, isbd->config->cylinder_config[cylinder]->tim_channel, timeout_value);
	gtm_timSetTimeoutMode(isbd->config->cylinder_config[cylinder]->timd, isbd->config->cylinder_config[cylinder]->tim_channel, timeout_mode);
}

void isbCrankStart(ISBDriver *isbd){
	(void)isbd;
	isb_crank_init_DPLL();
	/* Start TIM0_0 input */
	gtm_timStart(&TIMD1, TIM_CHANNEL0);
}
