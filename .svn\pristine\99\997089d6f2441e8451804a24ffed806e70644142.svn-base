/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef _OS_EXEC_CTRL_H_
#define _OS_EXEC_CTRL_H_


/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "OS_api.h"

/*!
\defgroup PublicDefines Public Defines
\sgroup
*/
/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
///This is a public define 
#define OSAPPMODE    0x00
#define OSBOOTMODE   0x01

#pragma ghs startnomisra    // 19.4 - A solution should be found

#define DisableAllInterrupts() __asm(" wrteei 0")
#define EnableAllInterrupts()  __asm(" wrteei 1")

#pragma ghs endnomisra

/*!\egroup*/
/*!
\defgroup PublicTypedef Public Typedefs 
\sgroup
*/
/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
///This is a public typedef 
typedef unsigned char      AppModeType;       /* OSEK: Application mode type  */


/*!\egroup*/

/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
AppModeType GetActiveApplicationMode ( void );
void StartOS ( AppModeType mode );
void ShutdownOS(StatusType error);
void ShutdownOSerrorHandler( StatusType error);


#endif // _OS_EXEC_CTRL_H_


/****************************************************************************
 ****************************************************************************/


