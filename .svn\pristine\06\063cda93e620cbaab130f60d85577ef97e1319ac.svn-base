/* generated by MCS-Assembler tool ASM-MCS version 0.9 */
/* Copyright (C) 2011-2016 by Robert <PERSON> GmbH, Germany */
/* target architecture : mcs24-1 */

const uint32_t mcs2_mem[624] = { 
    0xE0000220,
    0xE0000388,
    0xE00004F4,
    0xE0000660,
    0xE00007CC,
    0xE00008BC,
    0xE00009B8,
    0xE00009BC,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x00000000,
    0x10000000,
    0x11000000,
    0x12000000,
    0x13000000,
    0x14000000,
    0x15000000,
    0x16000000,
    0x1700001C,
    0x10000001,
    0xF0B00001,
    0xE8910220,
    0x1A000001,
    0xB0120065,
    0xE8A1027C,
    0xB0120061,
    0xE8A10264,
    0xE0000250,
    0x70FFFFFF,
    0xE8520274,
    0x71FFFFFF,
    0xE8510250,
    0xB3500061,
    0xE0000290,
    0x70FFFFFF,
    0xE852028C,
    0x71FFFFFF,
    0xE8510250,
    0xB3500065,
    0x19000000,
    0xB0110001,
    0x18000003,
    0x19000001,
    0xB0110000,
    0xA0E00000,
    0xC0300000,
    0xE84102B4,
    0xE00002CC,
    0xB0120061,
    0xE8A102E8,
    0xB0120065,
    0xE8A102E8,
    0xD0E00000,
    0xE84102B4,
    0xB0120061,
    0xE8A102E8,
    0xB0120065,
    0xE8A102E8,
    0xDE000000,
    0xE84202F4,
    0xE00002CC,
    0x19000000,
    0xB0110000,
    0xE000033C,
    0x19000000,
    0xB0110000,
    0xA1E00000,
    0xC1500000,
    0xE841030C,
    0xE0000324,
    0xB0120061,
    0xE8A1033C,
    0xB0120065,
    0xE8A1033C,
    0xD1E00000,
    0xE841030C,
    0xB0120061,
    0xE8A1033C,
    0xB0120065,
    0xE8A1033C,
    0xDE100000,
    0xE8410324,
    0x19000001,
    0xB0110001,
    0x9B001000,
    0xE8510344,
    0x10000032,
    0xC0E00000,
    0xE841035C,
    0xE0000364,
    0xD0E00000,
    0xE842035C,
    0xDE000000,
    0xE8420370,
    0xE0000364,
    0x1A001000,
    0x19000001,
    0xB0110000,
    0xE0000250,
    0xE0000240,
    0x480FFFFE,
    0x10000000,
    0x11000000,
    0x12000000,
    0x13000000,
    0x14000000,
    0x15000000,
    0x16000000,
    0x1700005C,
    0x10000002,
    0xF0B00002,
    0xE8910388,
    0x1A000002,
    0xB0120062,
    0xE8A103CC,
    0xB0120066,
    0xE8A103E4,
    0xE00003B8,
    0x70FFFFFF,
    0xE85203DC,
    0x71FFFFFF,
    0xE85103B8,
    0xB3500062,
    0xE00003F8,
    0x70FFFFFF,
    0xE85203F4,
    0x71FFFFFF,
    0xE85103B8,
    0xB3500066,
    0x19000000,
    0xB0110003,
    0x18000003,
    0x19000001,
    0xB0110002,
    0xA0E00000,
    0xC0300000,
    0xE841041C,
    0xE0000434,
    0xB2120062,
    0xE8A10450,
    0xB2120066,
    0xE8A10450,
    0xD0E00000,
    0xE841041C,
    0xB2120062,
    0xE8A10450,
    0xB2120066,
    0xE8A10450,
    0xDE000000,
    0xE842045C,
    0xE0000434,
    0x19000000,
    0xB0110002,
    0xE00004A8,
    0x19000000,
    0xB0110002,
    0xA1E00000,
    0xC1500000,
    0xE8410474,
    0xE000048C,
    0xB0220062,
    0xE8A104A8,
    0xB0220066,
    0xE8A104A8,
    0xD1E00000,
    0xE8410474,
    0xB0220062,
    0xE8A104A8,
    0xB0220066,
    0xE8A104A8,
    0xDE100000,
    0xE84204A8,
    0xE000048C,
    0x19000001,
    0xB0110003,
    0x9B002000,
    0xE85104B0,
    0x10000032,
    0xC0E00000,
    0xE84104C8,
    0xE00004D0,
    0xD0E00000,
    0xE84204C8,
    0xDE000000,
    0xE84204DC,
    0xE00004D0,
    0x1A002000,
    0x19000001,
    0xB0110002,
    0xE00003B8,
    0xE00003A8,
    0x480FFFFE,
    0x10000000,
    0x11000000,
    0x12000000,
    0x13000000,
    0x14000000,
    0x15000000,
    0x16000000,
    0x1700009C,
    0x10000004,
    0xF0B00004,
    0xE89104F4,
    0x1A000004,
    0xB0120063,
    0xE8A10538,
    0xB0120067,
    0xE8A10550,
    0xE0000524,
    0x70FFFFFF,
    0xE8520548,
    0x71FFFFFF,
    0xE8510524,
    0xB3500063,
    0xE0000564,
    0x70FFFFFF,
    0xE8520560,
    0x71FFFFFF,
    0xE8510524,
    0xB3500067,
    0x19000000,
    0xB0110005,
    0x18000003,
    0x19000001,
    0xB0110004,
    0xA0E00000,
    0xC0300000,
    0xE8410588,
    0xE00005A0,
    0xB0120063,
    0xE8A105BC,
    0xB0120067,
    0xE8A105BC,
    0xD0E00000,
    0xE8410588,
    0xB0120063,
    0xE8A105BC,
    0xB0120067,
    0xE8A105BC,
    0xDE000000,
    0xE84205C8,
    0xE00005A0,
    0x19000000,
    0xB0110004,
    0xE0000614,
    0x19000000,
    0xB0110004,
    0xA1E00000,
    0xC1500000,
    0xE84105E0,
    0xE00005F8,
    0xB0120063,
    0xE8A10614,
    0xB0120067,
    0xE8A10614,
    0xD1E00000,
    0xE84105E0,
    0xB0120063,
    0xE8A10614,
    0xB0120067,
    0xE8A10614,
    0xDE100000,
    0xE8420614,
    0xE00005F8,
    0x19000001,
    0xB0110005,
    0x9B004000,
    0xE851061C,
    0x10000032,
    0xC0E00000,
    0xE8410634,
    0xE000063C,
    0xD0E00000,
    0xE8420634,
    0xDE000000,
    0xE8420648,
    0xE000063C,
    0x1A004000,
    0x19000001,
    0xB0110004,
    0xE0000524,
    0xE0000514,
    0x480FFFFE,
    0x10000000,
    0x11000000,
    0x12000000,
    0x13000000,
    0x14000000,
    0x15000000,
    0x16000000,
    0x170000DC,
    0x10000008,
    0xF0B00008,
    0xE8910660,
    0x1A000008,
    0xB0120064,
    0xE8A106A4,
    0xB0120068,
    0xE8A106BC,
    0xE0000690,
    0x70FFFFFF,
    0xE85206B4,
    0x71FFFFFF,
    0xE8510690,
    0xB3500064,
    0xE00006D0,
    0x70FFFFFF,
    0xE85206CC,
    0x71FFFFFF,
    0xE8510690,
    0xB3500068,
    0x19000000,
    0xB0110007,
    0x18000003,
    0x19000001,
    0xB0110006,
    0xA0E00000,
    0xC0300000,
    0xE84106F4,
    0xE000070C,
    0xB2120064,
    0xE8A10728,
    0xB2120068,
    0xE8A10728,
    0xD0E00000,
    0xE84106F4,
    0xB2120064,
    0xE8A10728,
    0xB2120068,
    0xE8A10728,
    0xDE000000,
    0xE8420734,
    0xE000070C,
    0x19000000,
    0xB0110006,
    0xE0000780,
    0x19000000,
    0xB0110006,
    0xA1E00000,
    0xC1500000,
    0xE841074C,
    0xE0000764,
    0xB0220064,
    0xE8A10780,
    0xB0220068,
    0xE8A10780,
    0xD1E00000,
    0xE841074C,
    0xB0220064,
    0xE8A10780,
    0xB0220068,
    0xE8A10780,
    0xDE100000,
    0xE8420780,
    0xE0000764,
    0x19000001,
    0xB0110007,
    0x9B008000,
    0xE8510788,
    0x10000032,
    0xC0E00000,
    0xE84107A0,
    0xE00007A8,
    0xD0E00000,
    0xE84207A0,
    0xDE000000,
    0xE84207B4,
    0xE00007A8,
    0x1A008000,
    0x19000001,
    0xB0110006,
    0xE0000690,
    0xE0000680,
    0x480FFFFE,
    0x10000000,
    0x11000000,
    0x12000000,
    0x13000000,
    0x14000000,
    0x15000000,
    0x16000000,
    0x1700011C,
    0x10000010,
    0xF0B00010,
    0xE89107CC,
    0x1A000010,
    0xB0120069,
    0xE8A10808,
    0xE00007FC,
    0x1B000100,
    0x68000002,
    0x9B000100,
    0xE8520810,
    0x19000014,
    0x12000001,
    0x13000001,
    0xB2310008,
    0x19000014,
    0x1200000A,
    0xA3200000,
    0xC3060001,
    0xB2310008,
    0xB0120069,
    0xE8A10848,
    0xE000083C,
    0x19000014,
    0x13000000,
    0xB3310008,
    0x70999999,
    0xE85108B0,
    0x1B000200,
    0x68000002,
    0x9B000200,
    0xE8520864,
    0x19000014,
    0x1200000A,
    0xA3200000,
    0xC3060001,
    0xB2310008,
    0xA4E00000,
    0x240005DC,
    0xE8410890,
    0xE0000898,
    0xD4E00000,
    0xE8410890,
    0xDE400000,
    0xE84208A4,
    0xE0000898,
    0x19000014,
    0x13000000,
    0xB3310008,
    0xE00007FC,
    0xE00007EC,
    0x480FFFFE,
    0x10000000,
    0x11000000,
    0x12000000,
    0x13000000,
    0x14000000,
    0x15000000,
    0x16000000,
    0x1700015C,
    0x10000020,
    0xF0B00020,
    0xE89108BC,
    0x1A000020,
    0xB012006A,
    0xE8A108F8,
    0xE00008EC,
    0x1B000400,
    0x68000002,
    0x9B000400,
    0xE8520900,
    0x19000014,
    0x12000001,
    0x13000001,
    0xB2310009,
    0x19000014,
    0x1200000A,
    0xA3200000,
    0xC3060001,
    0xB2310009,
    0xB012006A,
    0xE8A10938,
    0xE000092C,
    0x700000FF,
    0xE85209AC,
    0x710000FF,
    0xE85209AC,
    0x19000014,
    0x13000000,
    0xB3310009,
    0x1B000800,
    0x68000002,
    0x9B000800,
    0xE852095C,
    0x19000014,
    0x1200000A,
    0xA3200000,
    0xC3060001,
    0xB2310009,
    0x140005DC,
    0xC4E00000,
    0xE8410988,
    0xE0000990,
    0xD4E00000,
    0xE8410988,
    0xDE400000,
    0xE842099C,
    0xE0000990,
    0x19000014,
    0x13000000,
    0xB3310009,
    0xE00008EC,
    0xE00008F8,
    0xE00008DC,
    0x480FFFFE,
    0x480FFFFE,
    0x480FFFFE
};
