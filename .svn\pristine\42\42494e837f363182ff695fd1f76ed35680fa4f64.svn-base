/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 ******************************************************************************
 **  Filename:      TLE9278BQX_Diag_private.h
 **  Date:          25-Oct-2022
 **
 **  Model Version: 1.462
 ******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#ifndef RTW_HEADER_TLE9278BQX_Diag_private_h_
#define RTW_HEADER_TLE9278BQX_Diag_private_h_
#include "rtwtypes.h"
#include "TLE9278BQX_Diag_out.h"

/* Includes for objects with custom storage classes. */
#include "diagmgm_out.h"
#include "Diagmgm_out.h"
#include "TLE9278BQX_Diag_eep_out.h"
#include "TLE9278BQX_Com_out.h"
#include "TLE9278BQX_Get_out.h"

/*****************************************************************************
 ** DEFINES
 ******************************************************************************/

/*****************************************************************************
 ** EXPORTED DATA AND FUNCTIONS DECLARATION
 ******************************************************************************/
#ifdef __cplusplus

extern "C" {

#endif

  extern void fc_Diag_SetVal_Start_wrapper(void);
  extern void fc_Diag_SetVal_Outputs_wrapper(const uint8_T *in,
    const uint32_T *addr,
    uint8_T *out);
  extern void fc_Diag_SetVal_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

#ifdef __cplusplus

extern "C" {

#endif

  extern void Diag_Return_Addr_U8_Start_wrapper(void);
  extern void Diag_Return_Addr_U8_Outputs_wrapper(const uint8_T *in,
    uint32_T *addr);
  extern void Diag_Return_Addr_U8_Terminate_wrapper(void);

#ifdef __cplusplus

}
#endif

extern void TLE9278BQX_Diag_fc_EECntSbcCAN(uint8_T rtu_EECntSbcCAN, uint32_T
  rtu_EECntSbcCANAddr, uint8_T *rty_EECntSbcCANTp);
extern void TLE9278BQX_Diag_fc_EESbcCAN(uint8_T rtu_EESbcCAN, uint32_T
  rtu_EESbcCANAddr, uint8_T *rty_EESbcCANTp);
extern void TLE9278BQX_Diag_fc_EECntSbcOT(uint8_T rtu_EECntSbcOT, uint32_T
  rtu_EECntSbcOTAddr, uint8_T *rty_EECntSbcOTTp);
extern void TLE9278BQX_Diag_fc_EESbcOT(uint8_T rtu_EESbcOT, uint32_T
  rtu_EESbcOTAddr, uint8_T *rty_EESbcOTTp);
extern void TLE9278BQX_Diag_fc_EECntSbcSC(uint8_T rtu_EECntSbcSC, uint32_T
  rtu_EECntSbcSCAddr, uint8_T *rty_EECntSbcSCTp);
extern void TLE9278BQX_Diag_fc_EESbcSC(uint8_T rtu_EESbcSC, uint32_T
  rtu_EESbcSCAddr, uint8_T *rty_EESbcSCTp);
extern void TLE9278BQX_Diag_fc_EECntSbcUV(uint8_T rtu_EECntSbcUV, uint32_T
  rtu_EECntSbcUVAddr, uint8_T *rty_EECntSbcUVTp);
extern void TLE9278BQX_Diag_fc_EESbcUV(uint8_T rtu_EESbcUV, uint32_T
  rtu_EESbcUVAddr, uint8_T *rty_EESbcUVTp);
extern void TLE9278BQX_Diag_fc_Bkg(void);
extern void TLE9278BQX_Diag_fc_Init_Start(void);
extern void TLE9278BQX_Diag_fc_Init(void);

#endif                               /* RTW_HEADER_TLE9278BQX_Diag_private_h_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/