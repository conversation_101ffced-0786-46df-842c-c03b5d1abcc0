/******************************************************************************************************************************/
/* $HeadURL::                                                                                                             $   */
/* $ Description:                                                                                                             */
/* $Revision::        $                                                                                                       */
/* $Date::                                                $                                                                   */
/* $Author::                         $                                                                                        */
/******************************************************************************************************************************/

#ifndef _ASM_GHS_ABSTRACTION_
#define _ASM_GHS_ABSTRACTION_

#pragma ghs startnomisra

/* .align directive compiler dependent */
#ifdef __ghs__
#define ALIGN_SIXTEEN_BYTES     4
#else
#define ALIGN_SIXTEEN_BYTES     16
#endif



#ifdef __vle__

/* VLE attributes and instruction set */
#define OS_ASM_VLE          .vle
#define OS_ASM_SECTATTR_AX  "axv"
#define OS_ASM_SECTATTR_A   "av"

#define addi    e_add16i
#define andi    e_andi
#define andis   e_andis
#define bdnz    e_bdnz
#define b       e_b
#define bctrl   se_bctrl
#define beq     e_beq
#define bge     se_bge
#define bgt     e_bgt
#define bl      e_bl
#define ble     e_ble
#define blrl    se_blrl
#define blr     se_blr
#define bne     e_bne
#define cmpi    e_cmpi
#define cmpwi   e_cmpwi
#define isync   se_isync
#define lbz     e_lbz
#define lbzu    e_lbzu
#define lhz     e_lhz
#define lhzu    e_lhzu
#define li      e_li
#define lis     e_lis
#define lmw     e_lmw
#define lwz     e_lwz
#define lwzu    e_lwzu
#define rfci    se_rfci
#define rfi     se_rfi
#define sc      se_sc
#define srwi    e_srwi
#define stb     e_stb
#define stbu    e_stbu
#define sth     e_sth
#define sthu    e_sthu
#define stmw    e_stmw
#define stw     e_stw
#define stwu    e_stwu

#else

#define OS_ASM_VLE
#define OS_ASM_SECTATTR_AX  "ax"
#define OS_ASM_SECTATTR_A   "a"

#endif /* __vle__ */

#pragma ghs endnomisra
#endif

