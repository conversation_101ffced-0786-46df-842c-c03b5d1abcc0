/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           TLE9278BQX_Com.c
 **  File Creation Date: 24-Jan-2024
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TLE9278BQX_Com
 **  Model Description:
 **  Model Version:      1.879
 **  Model Author:       SalimbeniT - Tue Jan 20 16:01:59 2015
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: LanaL - Wed Jan 24 14:25:19 2024
 **
 **  Last Saved Modification:  LanaL - Wed Jan 24 14:23:58 2024
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "TLE9278BQX_Com_out.h"
#include "TLE9278BQX_Com_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<S26>/TLE9278BQX_SendData_Machine' */
#define TLE9278BQX_C_IN_SBC_MODE_NORMAL ((uint8_T)4U)
#define TLE9278BQX_Co_IN_SBC_DEBUG_DATA ((uint8_T)3U)
#define TLE9278BQX_Co_IN_SBC_MODE_RESET ((uint8_T)5U)
#define TLE9278BQX_Co_IN_SBC_MODE_SLEEP ((uint8_T)6U)
#define TLE9278BQX_Com_IN_SBC_MODE_INIT ((uint8_T)2U)
#define TLE9278BQX_Com_IN_SBC_MODE_STOP ((uint8_T)7U)
#define TLE9278BQX__IN_SBC_MODE_EXPIRED ((uint8_T)1U)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define IDX_START_CMD                  1U                        /* Referenced by: '<S6>/IDX_START_CMD' */

/* idx */
#define ID_VER_TLE9278BQX_COM_DEF      1878U                     /* Referenced by: '<S5>/ID_VER_TLE9278BQX_COM_DEF' */

/* ID version */
#define SBC_MAX_DIM                    25U                       /* Referenced by: '<S20>/Iterator' */

/* mask */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_TLE9278BQX_COM_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENSBCPARITYCHK = 0U;
                        /* Referenced by: '<S26>/TLE9278BQX_SendData_Machine' */

/* Enable parity check in SBCSysStat */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENSBCSELFTST = 2U;
                        /* Referenced by: '<S26>/TLE9278BQX_SendData_Machine' */

/* Enable SBC self test */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T ENSMLOOP = 0U;
                        /* Referenced by: '<S26>/TLE9278BQX_SendData_Machine' */

/* Enable SM Loop */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCCAN01EN = 19U;
                        /* Referenced by: '<S26>/TLE9278BQX_SendData_Machine' */

/* BUS CTRL 2 Can1 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint8_T SBCNRESEND = 1U;/* Referenced by:
                                                          * '<S24>/Chart_Resend_Data'
                                                          * '<S26>/TLE9278BQX_SendData_Machine'
                                                          */

/* N resend data */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint8_T CntTrgSBCRes;                  /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Enable WDT Machine */
uint16_T FlgSBCFOEn;                   /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Force FO */
uint8_T FlgSBCProgErr;                 /* '<S24>/Chart_Resend_Data' */

/* Message error */
uint8_T RunBkgWDT;                     /* '<S3>/Merge1' */

/* Enable WDT Machine */
uint8_T SBCMsgIdx;                     /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Message router */
int16_T SBCSpiError;                   /* '<S20>/Logical Operator' */

/* SPI Communication error */
uint16_T SetAdcSel;                    /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Select Adc SBC source: 0=Wk & VBatt; 1=VBatt */
uint16_T SetSBCCan1;                   /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Can01 status setting */
uint16_T SetSBCSysStat;                /* '<S26>/TLE9278BQX_SendData_Machine' */

/* SBC System status setting */
enum_StSBC StSBC;                      /* '<S3>/Merge2' */

/* SBC Status */
enum_StSBCSafeTest StSBCSafeTest;      /* '<S26>/TLE9278BQX_SendData_Machine' */

/* Sefe status */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T CntSBCResend;/* '<S3>/Merge3' */

/* SBC resend counter */
STATIC_TEST_POINT uint32_T IdVer_TLE9278BQX_Com;/* '<S5>/ID_VER_TLE9278BQX_COM_DEF' */

/* ID version */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_TLE9278BQX_Com_T TLE9278BQX_Com_DW;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void TLE9278BQX_Com_SBC_MODE_INIT(void);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Output and update for function-call system: '<S4>/fc_TLE9278BQX_Mgm' */
void TLE9278BQX_Co_fc_TLE9278BQX_Mgm(void)
{
  /* Chart: '<S17>/fc_seq_call' incorporates:
   *  SubSystem: '<S17>/body'
   */
  /* CCaller: '<S33>/TLE9278BQX_Mgm_Bkg' */
  /* Gateway: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Mgm/fc_seq_call */
  /* During: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Mgm/fc_seq_call */
  /* Entry Internal: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Mgm/fc_seq_call */
  /* Transition: '<S35>:2' */
  /* Transition: '<S35>:4' */
  /* '<S35>:4:1' fc_UpdData; */
  /* Event: '<S35>:5' */
  /* '<S35>:4:2' fc_Body; */
  /* Event: '<S35>:6' */
  TLE9278BQX_Mgm_Bkg();
}

/* Output and update for function-call system: '<S4>/SBC_Compose_Data' */
void TLE9278BQX_Com_SBC_Compose_Data(const uint16_T rtu_SBCDataTxBuffer[30],
  const uint16_T rtu_SBCDataRxBuffer[30], DW_SBC_Compose_Data_TLE9278BQ_T
  *localDW)
{
  uint8_T dStep;
  uint8_T i;
  uint16_T rtb_Selector;
  uint8_T rtb_DataTypeConversion;
  int16_T rtb_err;
  uint8_T dSize;

  /* Selector: '<S6>/Selector' incorporates:
   *  Constant: '<S6>/IDX_0'
   */
  rtb_Selector = rtu_SBCDataTxBuffer[0];

  /* DataTypeConversion: '<S6>/Data Type Conversion' */
  rtb_DataTypeConversion = (uint8_T)rtb_Selector;

  /* Chart: '<S20>/Iterator' */
  /* Gateway: TLE9278BQX_Com/fc_Bkg/SBC_Compose_Data/FIFO_Mgm/Iterator */
  /* During: TLE9278BQX_Com/fc_Bkg/SBC_Compose_Data/FIFO_Mgm/Iterator */
  /* Entry Internal: TLE9278BQX_Com/fc_Bkg/SBC_Compose_Data/FIFO_Mgm/Iterator */
  /* Transition: '<S21>:2' */
  dStep = (uint8_T)(rtb_DataTypeConversion >> ((uint32_T)2));

  /*  4 Command */
  i = 0U;

  /* DataTypeConversion: '<S6>/Data Type Conversion1' incorporates:
   *  Constant: '<S6>/IDX_START_CMD'
   */
  rtb_Selector = ((uint16_T)IDX_START_CMD);
  if (((int32_T)((uint16_T)IDX_START_CMD)) > 255) {
    rtb_Selector = 255U;
  }

  /* Chart: '<S20>/Iterator' incorporates:
   *  DataTypeConversion: '<S6>/Data Type Conversion1'
   */
  localDW->idx = (uint8_T)rtb_Selector;
  if ((((int32_T)rtb_DataTypeConversion) == 0) || (rtb_DataTypeConversion >=
       ((uint8_T)SBC_MAX_DIM))) {
    /* Transition: '<S21>:27' */
    rtb_err = -1;
  } else {
    /* Transition: '<S21>:26' */
    rtb_err = 0;
    while (i <= dStep) {
      /* Transition: '<S21>:6' */
      if (i == dStep) {
        /* Transition: '<S21>:8' */
        /*  Mod 4 */
        dSize = (uint8_T)(rtb_DataTypeConversion & ((uint8_T)0x03));
      } else {
        /* Transition: '<S21>:9' */
        dSize = 4U;
      }

      if (((int32_T)dSize) != 0) {
        /* Outputs for Function Call SubSystem: '<S20>/SPI_TxRx' */
        /* S-Function (Ret_SBCData_Addr): '<S22>/C//C++ Code Block' */
        /* Transition: '<S21>:12' */
        /* Event: '<S21>:19' */
        Ret_SBCData_Addr_Outputs_wrapper(&rtu_SBCDataTxBuffer[0], &localDW->idx,
          &localDW->CCCodeBlock);

        /* S-Function (Ret_SBCData_Addr): '<S22>/C//C++ Code Block1' */
        Ret_SBCData_Addr_Outputs_wrapper(&rtu_SBCDataRxBuffer[0], &localDW->idx,
          &localDW->CCCodeBlock1);

        /* CCaller: '<S23>/SPI_TxRx' incorporates:
         *  Constant: '<S22>/PCS_0'
         *  Constant: '<S22>/SPI_CH_E'
         */
        localDW->SPI_TxRx = SPI_TxRx(SPI_CH_E, PCS_0, localDW->CCCodeBlock,
          localDW->CCCodeBlock1, dSize);

        /* End of Outputs for SubSystem: '<S20>/SPI_TxRx' */
      } else {
        /* Transition: '<S21>:11' */
      }

      /* Transition: '<S21>:13' */
      localDW->idx += dSize;
      i = (uint8_T)((int32_T)(((int32_T)i) + 1));
    }

    /* Transition: '<S21>:4' */
  }

  /* Logic: '<S20>/Logical Operator' */
  SBCSpiError = (int16_T)(((rtb_err != 0) || (localDW->SPI_TxRx != 0)) ? 1 : 0);
}

/* Output and update for function-call system: '<S4>/fc_TLE9278BQX_Prs' */
void TLE9278BQX_Co_fc_TLE9278BQX_Prs(void)
{
  /* Chart: '<S18>/fc_seq_call' incorporates:
   *  SubSystem: '<S18>/body'
   */
  /* CCaller: '<S36>/TLE9278BQX_Prs_Bkg' */
  /* Gateway: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Prs/fc_seq_call */
  /* During: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Prs/fc_seq_call */
  /* Entry Internal: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Prs/fc_seq_call */
  /* Transition: '<S38>:2' */
  /* Transition: '<S38>:4' */
  /* '<S38>:4:1' fc_UpdData; */
  /* Event: '<S38>:5' */
  /* '<S38>:4:2' fc_Body; */
  /* Event: '<S38>:6' */
  TLE9278BQX_Prs_Bkg();
}

/* Output and update for function-call system: '<S4>/fc_TLE9278BQX_Get' */
void TLE9278BQX_Co_fc_TLE9278BQX_Get(void)
{
  /* CCaller: '<S15>/TLE9278BQX_Get_Bkg' */
  TLE9278BQX_Get_Bkg();
}

/* Output and update for function-call system: '<S4>/fc_TLE9278BQX_Diag' */
void TLE9278BQX_C_fc_TLE9278BQX_Diag(void)
{
  /* Chart: '<S14>/fc_seq_call' incorporates:
   *  SubSystem: '<S14>/body'
   */
  /* CCaller: '<S30>/TLE9278BQX_Diag_Bkg' */
  /* Gateway: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Diag/fc_seq_call */
  /* During: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Diag/fc_seq_call */
  /* Entry Internal: TLE9278BQX_Com/fc_Bkg/fc_TLE9278BQX_Diag/fc_seq_call */
  /* Transition: '<S32>:2' */
  /* Transition: '<S32>:4' */
  /* '<S32>:4:1' fc_UpdData; */
  /* Event: '<S32>:5' */
  /* '<S32>:4:2' fc_Body; */
  /* Event: '<S32>:6' */
  TLE9278BQX_Diag_Bkg();
}

/* Output and update for function-call system: '<S4>/fc_InitRoutine3' */
void TLE9278BQX_Com_fc_InitRoutine3(void)
{
  /* CCaller: '<S10>/TLE9278BQX_IOs_RT3' */
  TLE9278BQX_IOs_RT3();
}

/* Output and update for function-call system: '<S4>/fc_InitRoutine5' */
void TLE9278BQX_Com_fc_InitRoutine5(void)
{
  /* CCaller: '<S12>/TLE9278BQX_IOs_RT5' */
  TLE9278BQX_IOs_RT5();
}

/* Function for Chart: '<S26>/TLE9278BQX_SendData_Machine' */
static void TLE9278BQX_Com_SBC_MODE_INIT(void)
{
  boolean_T guard1 = false;
  boolean_T guard2 = false;

  /* During 'SBC_MODE_INIT': '<S29>:1' */
  /* Transition: '<S29>:312' */
  CntTrgSBCRes = (uint8_T)((int32_T)(((int32_T)CntTrgSBCRes) + 1));
  SBCMsgIdx = SBC_PRE_INIT_READ_ID;

  /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
  /* Event: '<S29>:157' */
  TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

  /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

  /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
  /* Event: '<S29>:151' */
  TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])), (&(SBCDataRxBuffer[0])),
    &TLE9278BQX_Com_DW.SBC_Compose_Data);

  /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

  /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
  /* Event: '<S29>:152' */
  TLE9278BQX_Co_fc_TLE9278BQX_Prs();

  /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */

  /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Get' */
  /* Event: '<S29>:283' */
  TLE9278BQX_Co_fc_TLE9278BQX_Get();

  /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Get' */

  /* Inport: '<Root>/SBCSysStat'
   *
   * Block description for '<Root>/SBCSysStat':
   *  System status information feedback
   */
  if ((((int32_T)ENSBCSELFTST) == 2) && (((int32_T)SBCSysStat) == ((int32_T)0x01)))
  {
    /* Outputs for Function Call SubSystem: '<S4>/fc_InitRoutine1' */
    /* CCaller: '<S8>/TLE9278BQX_IOs_RT1' */
    /* Transition: '<S29>:314' */
    /* Transition: '<S29>:336' */
    /* Event: '<S29>:339' */
    TLE9278BQX_IOs_RT1();

    /* End of Outputs for SubSystem: '<S4>/fc_InitRoutine1' */
  } else {
    /* Transition: '<S29>:318' */
    /* Transition: '<S29>:319' */
    /* Transition: '<S29>:338' */
  }

  /* Transition: '<S29>:10' */
  SBCMsgIdx = SBC_INIT_READ_ID;

  /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
  /* Event: '<S29>:157' */
  TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

  /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

  /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
  /* Event: '<S29>:151' */
  TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])), (&(SBCDataRxBuffer[0])),
    &TLE9278BQX_Com_DW.SBC_Compose_Data);

  /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

  /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
  /* Event: '<S29>:152' */
  TLE9278BQX_Co_fc_TLE9278BQX_Prs();

  /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */

  /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Get' */
  /* Event: '<S29>:283' */
  TLE9278BQX_Co_fc_TLE9278BQX_Get();

  /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Get' */

  /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Diag' */
  /* Event: '<S29>:1167' */
  TLE9278BQX_C_fc_TLE9278BQX_Diag();

  /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Diag' */

  /* Outputs for Function Call SubSystem: '<S4>/fc_InitRoutine2' */
  /* CCaller: '<S9>/TLE9278BQX_IOs_RT2' */
  /* Event: '<S29>:340' */
  TLE9278BQX_IOs_RT2();

  /* End of Outputs for SubSystem: '<S4>/fc_InitRoutine2' */
  SetAdcSel = (uint16_T)((((int32_T)SetAdcSel) == 0) ? 1 : 0);

  /* Inport: '<Root>/SBCSysStat'
   *
   * Block description for '<Root>/SBCSysStat':
   *  System status information feedback
   */
  guard1 = false;
  guard2 = false;
  if ((((int32_T)ENSBCSELFTST) < 2) || (((int32_T)SBCSysStat) == ((int32_T)0x03)))
  {
    /* Inport: '<Root>/SBCFOOn'
     *
     * Block description for '<Root>/SBCFOOn':
     *  FO Pin status feedback
     */
    /* Transition: '<S29>:237' */
    /* Transition: '<S29>:424' */
    if ((((int32_T)SBCFOOn) != 0) || (((int32_T)SBCSysStat) > 4)) {
      /* Transition: '<S29>:417' */
      switch (SBCSysStat) {
       case 0x05:
        /* Transition: '<S29>:1064' */
        StSBCSafeTest = SF_SM_INTC;

        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x06:
        /* Transition: '<S29>:1067' */
        /* Transition: '<S29>:1071' */
        StSBCSafeTest = SF_SM_ADC;

        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x07:
        /* Transition: '<S29>:1074' */
        /* Transition: '<S29>:1079' */
        StSBCSafeTest = SF_SM_INIT1;

        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x08:
        /* Transition: '<S29>:1081' */
        /* Transition: '<S29>:1085' */
        StSBCSafeTest = SF_SM_INIT2;

        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x09:
        /* Transition: '<S29>:1087' */
        /* Transition: '<S29>:1091' */
        StSBCSafeTest = SF_SM_CMU;

        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x0A:
        /* Transition: '<S29>:1093' */
        /* Transition: '<S29>:1097' */
        StSBCSafeTest = SF_SM_FCCU1;

        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x0B:
        /* Transition: '<S29>:1099' */
        /* Transition: '<S29>:1103' */
        StSBCSafeTest = SF_SM_FCCU2;

        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x0C:
        /* Transition: '<S29>:1105' */
        /* Transition: '<S29>:1109' */
        StSBCSafeTest = SF_SM_MCU1;

        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x0D:
        /* Transition: '<S29>:1111' */
        /* Transition: '<S29>:1115' */
        StSBCSafeTest = SF_SM_MCU2;

        /* Transition: '<S29>:1112' */
        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x0E:
        /* Transition: '<S29>:1117' */
        /* Transition: '<S29>:1121' */
        StSBCSafeTest = SF_SM_MCU3;

        /* Transition: '<S29>:1118' */
        /* Transition: '<S29>:1112' */
        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x0F:
        /* Transition: '<S29>:1123' */
        /* Transition: '<S29>:1127' */
        StSBCSafeTest = SF_SM_MCU4;

        /* Transition: '<S29>:1124' */
        /* Transition: '<S29>:1118' */
        /* Transition: '<S29>:1112' */
        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x10:
        /* Transition: '<S29>:1129' */
        /* Transition: '<S29>:1133' */
        StSBCSafeTest = SF_SM_MCU5;

        /* Transition: '<S29>:1130' */
        /* Transition: '<S29>:1124' */
        /* Transition: '<S29>:1118' */
        /* Transition: '<S29>:1112' */
        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x11:
        /* Transition: '<S29>:1135' */
        /* Transition: '<S29>:1139' */
        StSBCSafeTest = SF_SM_MCU6;

        /* Transition: '<S29>:1136' */
        /* Transition: '<S29>:1130' */
        /* Transition: '<S29>:1124' */
        /* Transition: '<S29>:1118' */
        /* Transition: '<S29>:1112' */
        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x12:
        /* Transition: '<S29>:1141' */
        /* Transition: '<S29>:1145' */
        StSBCSafeTest = SF_SM_MCU7;

        /* Transition: '<S29>:1142' */
        /* Transition: '<S29>:1136' */
        /* Transition: '<S29>:1130' */
        /* Transition: '<S29>:1124' */
        /* Transition: '<S29>:1118' */
        /* Transition: '<S29>:1112' */
        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x13:
        /* Transition: '<S29>:1147' */
        /* Transition: '<S29>:1152' */
        StSBCSafeTest = SF_SM_MCU8;

        /* Transition: '<S29>:1148' */
        /* Transition: '<S29>:1142' */
        /* Transition: '<S29>:1136' */
        /* Transition: '<S29>:1130' */
        /* Transition: '<S29>:1124' */
        /* Transition: '<S29>:1118' */
        /* Transition: '<S29>:1112' */
        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x14:
        /* Transition: '<S29>:1156' */
        /* Transition: '<S29>:1160' */
        StSBCSafeTest = SF_SM_PIT1;

        /* Transition: '<S29>:1157' */
        /* Transition: '<S29>:1148' */
        /* Transition: '<S29>:1142' */
        /* Transition: '<S29>:1136' */
        /* Transition: '<S29>:1130' */
        /* Transition: '<S29>:1124' */
        /* Transition: '<S29>:1118' */
        /* Transition: '<S29>:1112' */
        /* Transition: '<S29>:1106' */
        /* Transition: '<S29>:1100' */
        /* Transition: '<S29>:1094' */
        /* Transition: '<S29>:1088' */
        /* Transition: '<S29>:1082' */
        /* Transition: '<S29>:1075' */
        /* Transition: '<S29>:1068' */
        /* Transition: '<S29>:1065' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x15:
        /* Transition: '<S29>:1163' */
        /* Transition: '<S29>:1076' */
        /* Transition: '<S29>:1072' */
        /* Transition: '<S29>:1073' */
        StSBCSafeTest = SF_SM_PIT2;

        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x16:
        /* Transition: '<S29>:1077' */
        /* Transition: '<S29>:1080' */
        StSBCSafeTest = SF_SM_FCCU3;

        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x17:
        /* Transition: '<S29>:1083' */
        /* Transition: '<S29>:1086' */
        StSBCSafeTest = SF_SM_FCCU4;

        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x18:
        /* Transition: '<S29>:1089' */
        /* Transition: '<S29>:1092' */
        StSBCSafeTest = SF_SM_SCLK;

        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x19:
        /* Transition: '<S29>:1095' */
        /* Transition: '<S29>:1098' */
        StSBCSafeTest = SF_SM_LCLK;

        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x1A:
        /* Transition: '<S29>:1101' */
        /* Transition: '<S29>:1104' */
        StSBCSafeTest = SF_SM_FCCU5;

        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x1B:
        /* Transition: '<S29>:1107' */
        /* Transition: '<S29>:1110' */
        StSBCSafeTest = SF_SM_FCCU6;

        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x1C:
        /* Transition: '<S29>:1113' */
        /* Transition: '<S29>:1116' */
        StSBCSafeTest = SF_DATA_EXCEPTION;

        /* Transition: '<S29>:1114' */
        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x1D:
        /* Transition: '<S29>:1119' */
        /* Transition: '<S29>:1122' */
        StSBCSafeTest = SF_CORE1_EXCEPTION;

        /* Transition: '<S29>:1120' */
        /* Transition: '<S29>:1114' */
        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x1E:
        /* Transition: '<S29>:1125' */
        /* Transition: '<S29>:1128' */
        StSBCSafeTest = SF_CORE2_EXCEPTION;

        /* Transition: '<S29>:1126' */
        /* Transition: '<S29>:1120' */
        /* Transition: '<S29>:1114' */
        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x1F:
        /* Transition: '<S29>:1131' */
        /* Transition: '<S29>:1134' */
        StSBCSafeTest = SF_SM_FCCU7;

        /* Transition: '<S29>:1132' */
        /* Transition: '<S29>:1126' */
        /* Transition: '<S29>:1120' */
        /* Transition: '<S29>:1114' */
        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x20:
        /* Transition: '<S29>:1137' */
        /* Transition: '<S29>:1140' */
        StSBCSafeTest = SF_SM_FCCU8;

        /* Transition: '<S29>:1138' */
        /* Transition: '<S29>:1132' */
        /* Transition: '<S29>:1126' */
        /* Transition: '<S29>:1120' */
        /* Transition: '<S29>:1114' */
        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x21:
        /* Transition: '<S29>:1143' */
        /* Transition: '<S29>:1146' */
        StSBCSafeTest = SF_SM_FCCU9;

        /* Transition: '<S29>:1144' */
        /* Transition: '<S29>:1138' */
        /* Transition: '<S29>:1132' */
        /* Transition: '<S29>:1126' */
        /* Transition: '<S29>:1120' */
        /* Transition: '<S29>:1114' */
        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x22:
        /* Transition: '<S29>:1149' */
        /* Transition: '<S29>:1153' */
        StSBCSafeTest = SF_TRANS_EXCEPTION;

        /* Transition: '<S29>:1150' */
        /* Transition: '<S29>:1144' */
        /* Transition: '<S29>:1138' */
        /* Transition: '<S29>:1132' */
        /* Transition: '<S29>:1126' */
        /* Transition: '<S29>:1120' */
        /* Transition: '<S29>:1114' */
        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       case 0x23:
        /* Transition: '<S29>:1158' */
        /* Transition: '<S29>:1161' */
        StSBCSafeTest = SF_GTM_EXCEPTION;

        /* Transition: '<S29>:1159' */
        /* Transition: '<S29>:1150' */
        /* Transition: '<S29>:1144' */
        /* Transition: '<S29>:1138' */
        /* Transition: '<S29>:1132' */
        /* Transition: '<S29>:1126' */
        /* Transition: '<S29>:1120' */
        /* Transition: '<S29>:1114' */
        /* Transition: '<S29>:1108' */
        /* Transition: '<S29>:1102' */
        /* Transition: '<S29>:1096' */
        /* Transition: '<S29>:1090' */
        /* Transition: '<S29>:1084' */
        /* Transition: '<S29>:1078' */
        /* Transition: '<S29>:1069' */
        /* Transition: '<S29>:1066' */
        break;

       default:
        /* Inport: '<Root>/FlguCTransient'
         *
         * Block description for '<Root>/FlguCTransient':
         *  Discovered error to the transient 5V
         */
        /* Transition: '<S29>:1164' */
        /* Transition: '<S29>:1166' */
        /* Transition: '<S29>:1165' */
        if ((((int32_T)SBCSysStat) <= ((int32_T)0x04)) && (((int32_T)
              FlguCTransient) == 0)) {
          /* Transition: '<S29>:1255' */
          StSBCSafeTest = SF_WDT_EXCEPTION;

          /* Transition: '<S29>:1256' */
          /* Transition: '<S29>:1253' */
          /* Transition: '<S29>:1243' */
        } else {
          /* Transition: '<S29>:1257' */
          /*  (SBCSysStat == 0x04) &&... */
          if (((int32_T)FlguCTransient) != 0) {
            /* Transition: '<S29>:1254' */
            StSBCSafeTest = SF_TRANS_EXCEPTION;

            /* Transition: '<S29>:1253' */
            /* Transition: '<S29>:1243' */
          } else {
            /* Transition: '<S29>:1258' */
            if (((int32_T)ENSBCPARITYCHK) != 0) {
              /* Transition: '<S29>:1252' */
              StSBCSafeTest = SF_PARITY_EXCEPTION;

              /* Transition: '<S29>:1243' */
            } else {
              /* Transition: '<S29>:1259' */
              /* Transition: '<S29>:1244' */
              StSBCSafeTest = SF_WDT_EXCEPTION;
            }
          }
        }

        /* Transition: '<S29>:1070' */
        break;
      }

      /* Transition: '<S29>:1062' */
      /* Transition: '<S29>:471' */
    } else {
      /* Transition: '<S29>:426' */
      if (((int32_T)ENSBCSELFTST) < 2) {
        /* Outputs for Function Call SubSystem: '<S4>/fc_InitRoutine3' */
        /* Transition: '<S29>:398' */
        /* Event: '<S29>:341' */
        TLE9278BQX_Com_fc_InitRoutine3();

        /* End of Outputs for SubSystem: '<S4>/fc_InitRoutine3' */
      } else {
        /* Transition: '<S29>:523' */
      }

      /* Transition: '<S29>:522' */
    }

    /* End of Inport: '<Root>/SBCFOOn' */
    /* Transition: '<S29>:428' */
    guard2 = true;
  } else {
    /* Transition: '<S29>:236' */
    if (((int32_T)SBCSysStat) == ((int32_T)0x01)) {
      /* Transition: '<S29>:223' */
      if (((int32_T)SBCFOOn) != 0) {
        /* Outputs for Function Call SubSystem: '<S4>/fc_InitRoutine4' */
        /* CCaller: '<S11>/TLE9278BQX_IOs_RT4' */
        /* Transition: '<S29>:367' */
        /* Event: '<S29>:342' */
        TLE9278BQX_IOs_RT4();

        /* End of Outputs for SubSystem: '<S4>/fc_InitRoutine4' */

        /* Inport: '<Root>/StLoadTstError'
         *
         * Block description for '<Root>/StLoadTstError':
         *  Load Test status diagnosis
         */
        if (StLoadTstError != LT_NORMAL) {
          /* Transition: '<S29>:377' */
          StSBCSafeTest = SF_EL_FAILURE;
        } else {
          /* Transition: '<S29>:387' */
        }

        /* End of Inport: '<Root>/StLoadTstError' */

        /* Inport: '<Root>/SBCCan1' incorporates:
         *  Inport: '<Root>/FdbkLiveness'
         *
         * Block description for '<Root>/SBCCan1':
         *  CAN 1 Configuration feedback
         *
         * Block description for '<Root>/FdbkLiveness':
         *  Liveness feedback
         */
        if ((((int32_T)SBCCan1) == 3) || (((int32_T)FdbkLiveness) != 0)) {
          /* Transition: '<S29>:388' */
          StSBCSafeTest = SF_NO_COM;
        } else {
          /* Transition: '<S29>:355' */
        }

        /* End of Inport: '<Root>/SBCCan1' */
        guard2 = true;
      } else {
        /* Transition: '<S29>:369' */
        /* Transition: '<S29>:358' */
        StSBCSafeTest = SF_ABNORMAL;

        /* Transition: '<S29>:372' */
        guard2 = true;
      }
    } else {
      /* Transition: '<S29>:225' */
      if ((((int32_T)SBCFOOn) != 0) || (((int32_T)SBCSysStat) > 4)) {
        /* Transition: '<S29>:412' */
        switch (SBCSysStat) {
         case 0x05:
          /* Transition: '<S29>:594' */
          StSBCSafeTest = SF_SM_INTC;

          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x06:
          /* Transition: '<S29>:597' */
          /* Transition: '<S29>:598' */
          StSBCSafeTest = SF_SM_ADC;

          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x07:
          /* Transition: '<S29>:603' */
          /* Transition: '<S29>:604' */
          StSBCSafeTest = SF_SM_INIT1;

          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x08:
          /* Transition: '<S29>:610' */
          /* Transition: '<S29>:607' */
          StSBCSafeTest = SF_SM_INIT2;

          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x09:
          /* Transition: '<S29>:611' */
          /* Transition: '<S29>:613' */
          StSBCSafeTest = SF_SM_CMU;

          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x0A:
          /* Transition: '<S29>:618' */
          /* Transition: '<S29>:617' */
          StSBCSafeTest = SF_SM_FCCU1;

          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x0B:
          /* Transition: '<S29>:621' */
          /* Transition: '<S29>:624' */
          StSBCSafeTest = SF_SM_FCCU2;

          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x0C:
          /* Transition: '<S29>:627' */
          /* Transition: '<S29>:628' */
          StSBCSafeTest = SF_SM_MCU1;

          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x0D:
          /* Transition: '<S29>:631' */
          /* Transition: '<S29>:632' */
          StSBCSafeTest = SF_SM_MCU2;

          /* Transition: '<S29>:635' */
          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x0E:
          /* Transition: '<S29>:639' */
          /* Transition: '<S29>:640' */
          StSBCSafeTest = SF_SM_MCU3;

          /* Transition: '<S29>:636' */
          /* Transition: '<S29>:635' */
          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x0F:
          /* Transition: '<S29>:645' */
          /* Transition: '<S29>:642' */
          StSBCSafeTest = SF_SM_MCU4;

          /* Transition: '<S29>:644' */
          /* Transition: '<S29>:636' */
          /* Transition: '<S29>:635' */
          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x10:
          /* Transition: '<S29>:653' */
          /* Transition: '<S29>:655' */
          StSBCSafeTest = SF_SM_MCU5;

          /* Transition: '<S29>:647' */
          /* Transition: '<S29>:644' */
          /* Transition: '<S29>:636' */
          /* Transition: '<S29>:635' */
          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x11:
          /* Transition: '<S29>:650' */
          /* Transition: '<S29>:654' */
          StSBCSafeTest = SF_SM_MCU6;

          /* Transition: '<S29>:648' */
          /* Transition: '<S29>:647' */
          /* Transition: '<S29>:644' */
          /* Transition: '<S29>:636' */
          /* Transition: '<S29>:635' */
          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x12:
          /* Transition: '<S29>:665' */
          /* Transition: '<S29>:667' */
          StSBCSafeTest = SF_SM_MCU7;

          /* Transition: '<S29>:668' */
          /* Transition: '<S29>:648' */
          /* Transition: '<S29>:647' */
          /* Transition: '<S29>:644' */
          /* Transition: '<S29>:636' */
          /* Transition: '<S29>:635' */
          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x13:
          /* Transition: '<S29>:658' */
          /* Transition: '<S29>:669' */
          StSBCSafeTest = SF_SM_MCU8;

          /* Transition: '<S29>:656' */
          /* Transition: '<S29>:668' */
          /* Transition: '<S29>:648' */
          /* Transition: '<S29>:647' */
          /* Transition: '<S29>:644' */
          /* Transition: '<S29>:636' */
          /* Transition: '<S29>:635' */
          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x14:
          /* Transition: '<S29>:664' */
          /* Transition: '<S29>:657' */
          StSBCSafeTest = SF_SM_PIT1;

          /* Transition: '<S29>:662' */
          /* Transition: '<S29>:656' */
          /* Transition: '<S29>:668' */
          /* Transition: '<S29>:648' */
          /* Transition: '<S29>:647' */
          /* Transition: '<S29>:644' */
          /* Transition: '<S29>:636' */
          /* Transition: '<S29>:635' */
          /* Transition: '<S29>:629' */
          /* Transition: '<S29>:622' */
          /* Transition: '<S29>:620' */
          /* Transition: '<S29>:612' */
          /* Transition: '<S29>:606' */
          /* Transition: '<S29>:605' */
          /* Transition: '<S29>:599' */
          /* Transition: '<S29>:985' */
          /* Transition: '<S29>:986' */
          break;

         case 0x15:
          /* Transition: '<S29>:964' */
          /* Transition: '<S29>:967' */
          /* Transition: '<S29>:968' */
          /* Transition: '<S29>:694' */
          StSBCSafeTest = SF_SM_PIT2;

          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x16:
          /* Transition: '<S29>:700' */
          /* Transition: '<S29>:693' */
          StSBCSafeTest = SF_SM_FCCU3;

          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x17:
          /* Transition: '<S29>:696' */
          /* Transition: '<S29>:703' */
          StSBCSafeTest = SF_SM_FCCU4;

          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x18:
          /* Transition: '<S29>:708' */
          /* Transition: '<S29>:701' */
          StSBCSafeTest = SF_SM_SCLK;

          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x19:
          /* Transition: '<S29>:683' */
          /* Transition: '<S29>:685' */
          StSBCSafeTest = SF_SM_LCLK;

          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x1A:
          /* Transition: '<S29>:690' */
          /* Transition: '<S29>:686' */
          StSBCSafeTest = SF_SM_FCCU5;

          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x1B:
          /* Transition: '<S29>:680' */
          /* Transition: '<S29>:677' */
          StSBCSafeTest = SF_SM_FCCU6;

          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x1C:
          /* Transition: '<S29>:672' */
          /* Transition: '<S29>:675' */
          StSBCSafeTest = SF_DATA_EXCEPTION;

          /* Transition: '<S29>:976' */
          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x1D:
          /* Transition: '<S29>:600' */
          /* Transition: '<S29>:414' */
          StSBCSafeTest = SF_CORE1_EXCEPTION;

          /* Transition: '<S29>:975' */
          /* Transition: '<S29>:976' */
          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x1E:
          /* Transition: '<S29>:420' */
          /* Transition: '<S29>:415' */
          StSBCSafeTest = SF_CORE2_EXCEPTION;

          /* Transition: '<S29>:974' */
          /* Transition: '<S29>:975' */
          /* Transition: '<S29>:976' */
          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x1F:
          /* Transition: '<S29>:440' */
          /* Transition: '<S29>:441' */
          StSBCSafeTest = SF_SM_FCCU7;

          /* Transition: '<S29>:973' */
          /* Transition: '<S29>:974' */
          /* Transition: '<S29>:975' */
          /* Transition: '<S29>:976' */
          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x20:
          /* Transition: '<S29>:445' */
          /* Transition: '<S29>:446' */
          StSBCSafeTest = SF_SM_FCCU8;

          /* Transition: '<S29>:972' */
          /* Transition: '<S29>:973' */
          /* Transition: '<S29>:974' */
          /* Transition: '<S29>:975' */
          /* Transition: '<S29>:976' */
          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x21:
          /* Transition: '<S29>:478' */
          /* Transition: '<S29>:480' */
          StSBCSafeTest = SF_SM_FCCU9;

          /* Transition: '<S29>:971' */
          /* Transition: '<S29>:972' */
          /* Transition: '<S29>:973' */
          /* Transition: '<S29>:974' */
          /* Transition: '<S29>:975' */
          /* Transition: '<S29>:976' */
          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x22:
          /* Transition: '<S29>:511' */
          /* Transition: '<S29>:513' */
          StSBCSafeTest = SF_TRANS_EXCEPTION;

          /* Transition: '<S29>:970' */
          /* Transition: '<S29>:971' */
          /* Transition: '<S29>:972' */
          /* Transition: '<S29>:973' */
          /* Transition: '<S29>:974' */
          /* Transition: '<S29>:975' */
          /* Transition: '<S29>:976' */
          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         case 0x23:
          /* Transition: '<S29>:546' */
          /* Transition: '<S29>:547' */
          StSBCSafeTest = SF_GTM_EXCEPTION;

          /* Transition: '<S29>:969' */
          /* Transition: '<S29>:970' */
          /* Transition: '<S29>:971' */
          /* Transition: '<S29>:972' */
          /* Transition: '<S29>:973' */
          /* Transition: '<S29>:974' */
          /* Transition: '<S29>:975' */
          /* Transition: '<S29>:976' */
          /* Transition: '<S29>:977' */
          /* Transition: '<S29>:978' */
          /* Transition: '<S29>:979' */
          /* Transition: '<S29>:980' */
          /* Transition: '<S29>:981' */
          /* Transition: '<S29>:982' */
          /* Transition: '<S29>:984' */
          /* Transition: '<S29>:986' */
          break;

         default:
          /* Inport: '<Root>/FlguCTransient'
           *
           * Block description for '<Root>/FlguCTransient':
           *  Discovered error to the transient 5V
           */
          /* Transition: '<S29>:716' */
          /* Transition: '<S29>:724' */
          /* Transition: '<S29>:726' */
          if ((((int32_T)SBCSysStat) <= ((int32_T)0x04)) && (((int32_T)
                FlguCTransient) == 0)) {
            /* Transition: '<S29>:748' */
            StSBCSafeTest = SF_WDT_EXCEPTION;

            /* Transition: '<S29>:753' */
            /* Transition: '<S29>:1191' */
            /* Transition: '<S29>:1219' */
          } else {
            /* Transition: '<S29>:751' */
            /*  (SBCSysStat == 0x04) &&... */
            if (((int32_T)FlguCTransient) != 0) {
              /* Transition: '<S29>:752' */
              StSBCSafeTest = SF_TRANS_EXCEPTION;

              /* Transition: '<S29>:1191' */
              /* Transition: '<S29>:1219' */
            } else {
              /* Transition: '<S29>:1189' */
              if (((int32_T)ENSBCPARITYCHK) != 0) {
                /* Transition: '<S29>:1192' */
                StSBCSafeTest = SF_PARITY_EXCEPTION;

                /* Transition: '<S29>:1219' */
              } else {
                /* Transition: '<S29>:1222' */
                /* Transition: '<S29>:1223' */
                StSBCSafeTest = SF_WDT_EXCEPTION;
              }
            }
          }

          /* Transition: '<S29>:987' */
          break;
        }

        /* Transition: '<S29>:240' */
        TLE9278BQX_Com_DW.stSbc = SBC_MODE_BYPASS;
        FlgSBCFOEn = 1U;
        SetSBCSysStat = (uint16_T)0x02;
        SetSBCCan1 = (uint16_T)((SBCCAN01EN & ((uint16_T)0x0030)) | ((uint16_T)
          0x0002));
        SBCMsgIdx = SBC_INIT_WRITE_ID;

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
        /* Event: '<S29>:157' */
        TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

        /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
        /* Event: '<S29>:151' */
        TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
          (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

        /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Event: '<S29>:152' */
        TLE9278BQX_Co_fc_TLE9278BQX_Prs();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Transition: '<S29>:346' */
        guard1 = true;
      } else {
        /* Outputs for Function Call SubSystem: '<S4>/fc_InitRoutine3' */
        /* Transition: '<S29>:241' */
        /* Event: '<S29>:341' */
        TLE9278BQX_Com_fc_InitRoutine3();

        /* End of Outputs for SubSystem: '<S4>/fc_InitRoutine3' */
        if (StLoadTstError != LT_NORMAL) {
          /* Transition: '<S29>:396' */
          /* Transition: '<S29>:395' */
          /* Transition: '<S29>:372' */
          guard2 = true;
        } else {
          /* Inport: '<Root>/FlguCTransient'
           *
           * Block description for '<Root>/FlguCTransient':
           *  Discovered error to the transient 5V
           */
          /* Transition: '<S29>:393' */
          /* Transition: '<S29>:1213' */
          if (((int32_T)FlguCTransient) == 0) {
            /* Transition: '<S29>:1215' */
            SetSBCSysStat = (uint16_T)0x01;
          } else {
            /* Transition: '<S29>:1216' */
          }

          /* Transition: '<S29>:244' */
          TLE9278BQX_Com_DW.StSbc = SBC_MODE_EXPIRED;
          TLE9278BQX_Com_DW.cntTO = 0U;
          FlgSBCFOEn = 0U;
          SetSBCCan1 = SBCCAN01EN;
          SBCMsgIdx = SBC_INIT_WRITE_ID;

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
          /* Event: '<S29>:157' */
          TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

          /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
          /* Event: '<S29>:151' */
          TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
            (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

          /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          /* Event: '<S29>:152' */
          TLE9278BQX_Co_fc_TLE9278BQX_Prs();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_InitRoutine5' */
          /* Event: '<S29>:403' */
          TLE9278BQX_Com_fc_InitRoutine5();

          /* End of Outputs for SubSystem: '<S4>/fc_InitRoutine5' */
          TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX__IN_SBC_MODE_EXPIRED;
        }
      }
    }
  }

  if (guard2) {
    /* Transition: '<S29>:234' */
    TLE9278BQX_Com_DW.stSbc = SBC_MODE_NORMAL;
    FlgSBCFOEn = 0U;
    SetSBCSysStat = (uint16_T)0x02;
    SetSBCCan1 = SBCCAN01EN;
    SBCMsgIdx = SBC_INIT_WRITE_ID;

    /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
    /* Event: '<S29>:157' */
    TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

    /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

    /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
    /* Event: '<S29>:151' */
    TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])), (&(SBCDataRxBuffer
      [0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

    /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

    /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
    /* Event: '<S29>:152' */
    TLE9278BQX_Co_fc_TLE9278BQX_Prs();

    /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
    guard1 = true;
  }

  if (guard1) {
    /* Transition: '<S29>:348' */
    /* Transition: '<S29>:9' */
    TLE9278BQX_Com_DW.StSbc = TLE9278BQX_Com_DW.stSbc;

    /* Inport: '<Root>/CntSBCWrite'
     *
     * Block description for '<Root>/CntSBCWrite':
     *  Request in Normal_Mode & STOP_Mode to write of additional data buffer
     *  by SPI
     */
    TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
    TLE9278BQX_Com_DW.tstWDTExpired = 0U;
    SetSBCSysStat = (uint16_T)0x04;
    TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
  }
}

/* System initialize for function-call system: '<S2>/fc_Bkg' */
void TLE9278BQX_Com_fc_Bkg_Init(void)
{
  /* SystemInitialize for Atomic SubSystem: '<S7>/Calc_Resend_Data'
   *
   * Block description for '<S7>/Calc_Resend_Data':
   *  <Inline Rules> Because volatile input
   */
  /* SystemInitialize for Chart: '<S24>/Chart_Resend_Data' incorporates:
   *  SubSystem: '<S7>/fc_SendData'
   *
   * Block description for '<S7>/fc_SendData':
   *  <Inline Rules> Because volatile input
   */
  /* SystemInitialize for Chart: '<S26>/TLE9278BQX_SendData_Machine' */
  TLE9278BQX_Com_DW.stSbc = SBC_MODE_INIT;
  TLE9278BQX_Com_DW.StSbc = TLE9278BQX_Com_DW.stSbc;
  StSBCSafeTest = SF_NORMAL;

  /* End of SystemInitialize for SubSystem: '<S7>/Calc_Resend_Data' */
}

/* Output and update for function-call system: '<S2>/fc_Bkg' */
void TLE9278BQX_Com_fc_Bkg(void)
{
  uint8_T cntRetryData;
  uint8_T n;
  boolean_T guard1 = false;
  boolean_T guard2 = false;
  int32_T entryg1;
  int32_T exitg2;
  int32_T exitg3;
  int32_T exitg4;
  boolean_T guard11 = false;

  /* Outputs for Atomic SubSystem: '<S7>/Calc_Resend_Data'
   *
   * Block description for '<S7>/Calc_Resend_Data':
   *  <Inline Rules> Because volatile input
   */
  /* Chart: '<S24>/Chart_Resend_Data' incorporates:
   *  SubSystem: '<S4>/fc_TLE9278BQX_Cfg'
   */
  /* CCaller: '<S13>/TLE9278BQX_Cfg_Bkg' */
  /* Gateway: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/Calc_Resend_Data/Chart_Resend_Data */
  /* During: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/Calc_Resend_Data/Chart_Resend_Data */
  /* Entry Internal: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/Calc_Resend_Data/Chart_Resend_Data */
  /* Transition: '<S27>:4' */
  /* Event: '<S27>:26' */
  TLE9278BQX_Cfg_Bkg();
  cntRetryData = 0U;
  FlgSBCProgErr = 0U;

  /* Chart: '<S24>/Chart_Resend_Data' incorporates:
   *  SubSystem: '<S7>/fc_SendData'
   *
   * Block description for '<S7>/fc_SendData':
   *  <Inline Rules> Because volatile input
   */
  /* Chart: '<S26>/TLE9278BQX_SendData_Machine' incorporates:
   *  Inport: '<Root>/CntRefreshWDT'
   *  Inport: '<Root>/CntSBCWrite'
   *  Inport: '<Root>/EECntSBCResend'
   *  Inport: '<Root>/FlguCTransient'
   *  Inport: '<Root>/KeyReqMsgOnD'
   *  Inport: '<Root>/ReqMsgOnD'
   *  Inport: '<Root>/ReqSBCMode'
   *  Inport: '<Root>/StSBCMode'
   *  Inport: '<Root>/VtSMDiagCode'
   *
   * Block description for '<Root>/CntRefreshWDT':
   *  Request refresh WDT flag
   *
   * Block description for '<Root>/CntSBCWrite':
   *  Request in Normal_Mode & STOP_Mode to write of additional data buffer
   *  by SPI
   *
   * Block description for '<Root>/EECntSBCResend':
   *  EEPROM data resend counter
   *
   * Block description for '<Root>/FlguCTransient':
   *  Discovered error to the transient 5V
   *
   * Block description for '<Root>/KeyReqMsgOnD':
   *  Request Mode custom status of the SBC for debug
   *
   * Block description for '<Root>/ReqMsgOnD':
   *  Request Debug pattern messages one-shot (Both edge triggered)
   *
   * Block description for '<Root>/ReqSBCMode':
   *  Request Mode status of the SBC
   *
   * Block description for '<Root>/StSBCMode':
   *  Mode Status of the SBC feedback
   *
   * Block description for '<Root>/VtSMDiagCode':
   *  Diagnosis Safety mechanism code flags
   */
  /* Event: '<S27>:8' */
  /* Gateway: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine */
  /* During: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine */
  if (((uint32_T)TLE9278BQX_Com_DW.is_active_c14_TLE9278BQX_Com) == 0U) {
    /* Entry: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine */
    TLE9278BQX_Com_DW.is_active_c14_TLE9278BQX_Com = 1U;

    /* Entry Internal: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine */
    /* Entry Internal 'SBC_MODE_CTRL': '<S29>:20' */
    /* Transition: '<S29>:2' */
    CntTrgSBCRes = 0U;
    TLE9278BQX_Com_DW.syncKeyWDTStop = 0U;
    TLE9278BQX_Com_DW.tstWDTExpired = 0U;
    TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
    StSBCSafeTest = SF_NORMAL;
    TLE9278BQX_Com_DW.StSbc = SBC_MODE_INIT;
    SetAdcSel = 0U;
    TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_Com_IN_SBC_MODE_INIT;

    /* Entry Internal 'WDT_MODE_CTRL': '<S29>:21' */
    /* Transition: '<S29>:92' */
    TLE9278BQX_Com_DW.RunBkgWDT_n = 1U;
    TLE9278BQX_Com_DW.flgIvorEE = 0U;
    entryg1 = 0;
  } else {
    /* During 'SBC_MODE_CTRL': '<S29>:20' */
    guard1 = false;
    guard2 = false;
    switch (TLE9278BQX_Com_DW.is_SBC_MODE_CTRL) {
     case TLE9278BQX__IN_SBC_MODE_EXPIRED:
      /* During 'SBC_MODE_EXPIRED': '<S29>:243' */
      /* Transition: '<S29>:292' */
      TLE9278BQX_Com_DW.tstWDTExpired = 1U;

      /* Transition: '<S29>:247' */
      if ((((int32_T)TLE9278BQX_Com_DW.cntTO) > 2) && (((int32_T)FlguCTransient)
           == 0)) {
        /* Transition: '<S29>:278' */
        TLE9278BQX_Com_DW.stSbc = SBC_MODE_NORMAL;
        StSBCSafeTest = SF_WDT_EXPIRED;

        /* Transition: '<S29>:9' */
        TLE9278BQX_Com_DW.StSbc = TLE9278BQX_Com_DW.stSbc;
        TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
        TLE9278BQX_Com_DW.tstWDTExpired = 0U;
        SetSBCSysStat = (uint16_T)0x04;
        TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
      } else {
        /* Transition: '<S29>:277' */
        TLE9278BQX_Com_DW.cntTO = (uint8_T)((int32_T)(((int32_T)
          TLE9278BQX_Com_DW.cntTO) + 1));
      }
      break;

     case TLE9278BQX_Com_IN_SBC_MODE_INIT:
      TLE9278BQX_Com_SBC_MODE_INIT();
      break;

     case TLE9278BQX_Co_IN_SBC_DEBUG_DATA:
      /* During 'SBC_DEBUG_DATA': '<S29>:210' */
      /* Transition: '<S29>:213' */
      if ((KeyReqMsgOnD & 0xFFFFFFFE) != ((uint32_T)0x01CABF38)) {
        /* Transition: '<S29>:215' */
        TLE9278BQX_Com_DW.StSbc = SBC_MODE_NORMAL;
        TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
      } else {
        /* Transition: '<S29>:214' */
      }
      break;

     case TLE9278BQX_C_IN_SBC_MODE_NORMAL:
      /* During 'SBC_MODE_NORMAL': '<S29>:3' */
      /* Transition: '<S29>:99' */
      CntTrgSBCRes = (uint8_T)((int32_T)(((int32_T)CntTrgSBCRes) + 1));
      if ((KeyReqMsgOnD & 0xFFFFFFFE) == ((uint32_T)0x01CABF38)) {
        /* Transition: '<S29>:212' */
        TLE9278BQX_Com_DW.StSbc = SBC_DEBUG_DATA;
        TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_Co_IN_SBC_DEBUG_DATA;
      } else {
        /* Transition: '<S29>:217' */
        /* Transition: '<S29>:1207' */
        if (CntSBCWrite != TLE9278BQX_Com_DW.oldCntSBCWrite) {
          /* Transition: '<S29>:98' */
          TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
          SBCMsgIdx = SBC_NORMAL_WRITE_ID;

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
          /* Event: '<S29>:157' */
          TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

          /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
          /* Event: '<S29>:151' */
          TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
            (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

          /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          /* Event: '<S29>:152' */
          TLE9278BQX_Co_fc_TLE9278BQX_Prs();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          /* Transition: '<S29>:13' */
        } else {
          /* Transition: '<S29>:100' */
        }

        if (((int32_T)ReqSBCMode) == 2) {
          /* Transition: '<S29>:14' */
          TLE9278BQX_Com_DW.syncKeyWDTStop = 1U;
          if (((int32_T)StSBCMode) == 2) {
            /* Transition: '<S29>:74' */
            TLE9278BQX_Com_DW.StSbc = SBC_MODE_STOP;
            TLE9278BQX_Com_DW.syncKeyWDTStop = 0U;
            TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_Com_IN_SBC_MODE_STOP;
          } else {
            /* Transition: '<S29>:75' */
            /* Transition: '<S29>:156' */
            guard2 = true;
          }
        } else {
          /* Transition: '<S29>:16' */
          TLE9278BQX_Com_DW.syncKeyWDTStop = 0U;
          guard2 = true;
        }
      }
      break;

     case TLE9278BQX_Co_IN_SBC_MODE_RESET:
      /* During 'SBC_MODE_RESET': '<S29>:63' */
      /* Transition: '<S29>:68' */
      /*  Wait SW Reset */
      SetSBCSysStat = (uint16_T)0x03;
      SBCMsgIdx = SBC_TO_RESET_WRITE_ID;

      /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
      /* Event: '<S29>:157' */
      TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

      /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

      /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
      /* Event: '<S29>:151' */
      TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
        (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

      /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

      /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
      /* Event: '<S29>:152' */
      TLE9278BQX_Co_fc_TLE9278BQX_Prs();

      /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
      if (((int32_T)ReqSBCMode) != 3) {
        /* Transition: '<S29>:178' */
        TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
      } else {
        /* Transition: '<S29>:69' */
      }
      break;

     case TLE9278BQX_Co_IN_SBC_MODE_SLEEP:
      /* During 'SBC_MODE_SLEEP': '<S29>:5' */
      /* Transition: '<S29>:181' */
      /* Transition: '<S29>:30' */
      /*  Wait Sleep */
      FlgSBCFOEn = 0U;
      SetSBCSysStat = (uint16_T)0x02;
      SetSBCCan1 = SBCCAN01EN;
      SBCMsgIdx = SBC_TO_SLEEP_WRITE_ID;

      /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
      /* Event: '<S29>:157' */
      TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

      /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

      /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
      /* Event: '<S29>:151' */
      TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
        (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

      /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

      /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
      /* Event: '<S29>:152' */
      TLE9278BQX_Co_fc_TLE9278BQX_Prs();

      /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
      break;

     default:
      /* During 'SBC_MODE_STOP': '<S29>:4' */
      /* Transition: '<S29>:103' */
      CntTrgSBCRes = (uint8_T)((int32_T)(((int32_T)CntTrgSBCRes) + 1));
      if (CntSBCWrite != TLE9278BQX_Com_DW.oldCntSBCWrite) {
        /* Transition: '<S29>:105' */
        TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
        SBCMsgIdx = SBC_STOP_WRITE_ID;

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
        /* Event: '<S29>:157' */
        TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

        /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
        /* Event: '<S29>:151' */
        TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
          (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

        /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Event: '<S29>:152' */
        TLE9278BQX_Co_fc_TLE9278BQX_Prs();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Transition: '<S29>:83' */
      } else {
        /* Transition: '<S29>:104' */
      }

      /* Transition: '<S29>:33' */
      SBCMsgIdx = SBC_STOP_READ_ID;

      /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
      /* Event: '<S29>:157' */
      TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

      /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

      /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
      /* Event: '<S29>:151' */
      TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
        (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

      /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

      /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
      /* Event: '<S29>:152' */
      TLE9278BQX_Co_fc_TLE9278BQX_Prs();

      /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
      if (((int32_T)StSBCMode) == 0) {
        /* Transition: '<S29>:34' */
        TLE9278BQX_Com_DW.StSbc = TLE9278BQX_Com_DW.stSbc;
        TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
      } else {
        /* Transition: '<S29>:85' */
      }
      break;
    }

    if (guard2) {
      if (((int32_T)ReqSBCMode) == 3) {
        /* Transition: '<S29>:77' */
        SetSBCSysStat = (uint16_T)0x03;
        SBCMsgIdx = SBC_TO_RESET_WRITE_ID;

        /* Outputs for Function Call SubSystem: '<S4>/fc_InitRoutine5' */
        /* Event: '<S29>:403' */
        TLE9278BQX_Com_fc_InitRoutine5();

        /* End of Outputs for SubSystem: '<S4>/fc_InitRoutine5' */

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
        /* Event: '<S29>:157' */
        TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

        /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
        /* Event: '<S29>:151' */
        TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
          (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

        /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Event: '<S29>:152' */
        TLE9278BQX_Co_fc_TLE9278BQX_Prs();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Transition: '<S29>:78' */
        if (((int32_T)StSBCMode) == 3) {
          /* Transition: '<S29>:17' */
          TLE9278BQX_Com_DW.StSbc = SBC_MODE_RESET;
          TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_Co_IN_SBC_MODE_RESET;
        } else {
          /* Transition: '<S29>:154' */
          guard1 = true;
        }
      } else {
        /* Transition: '<S29>:18' */
        guard1 = true;
      }
    }

    if (guard1) {
      if (((int32_T)ReqSBCMode) == 1) {
        /* Transition: '<S29>:80' */
        FlgSBCFOEn = 0U;
        SetSBCSysStat = (uint16_T)0x02;
        SetSBCCan1 = SBCCAN01EN;
        SBCMsgIdx = SBC_TO_SLEEP_WRITE_ID;

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
        /* Event: '<S29>:157' */
        TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

        /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
        /* Event: '<S29>:151' */
        TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
          (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

        /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Event: '<S29>:152' */
        TLE9278BQX_Co_fc_TLE9278BQX_Prs();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        if (((int32_T)StSBCMode) == 1) {
          /* Transition: '<S29>:66' */
          TLE9278BQX_Com_DW.StSbc = SBC_MODE_SLEEP;
          TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_Co_IN_SBC_MODE_SLEEP;
        } else {
          /* Transition: '<S29>:81' */
        }
      } else {
        /* Transition: '<S29>:65' */
        SBCMsgIdx = SBC_NORMAL_READ_ID;

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
        /* Event: '<S29>:157' */
        TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

        /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
        /* Event: '<S29>:151' */
        TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
          (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

        /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Event: '<S29>:152' */
        TLE9278BQX_Co_fc_TLE9278BQX_Prs();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        SetAdcSel = (uint16_T)((((int32_T)SetAdcSel) == 0) ? 1 : 0);
      }
    }

    /* During 'WDT_MODE_CTRL': '<S29>:21' */
    /* During 'REFRESH_WDT': '<S29>:22' */
    /* Transition: '<S29>:173' */
    if ((KeyReqMsgOnD != TLE9278BQX_Com_DW.oldKeyReqMsgOnD) || (ReqMsgOnD !=
         TLE9278BQX_Com_DW.oldReqMsgOnD)) {
      /* Transition: '<S29>:174' */
      TLE9278BQX_Com_DW.oldKeyReqMsgOnD = KeyReqMsgOnD;
      TLE9278BQX_Com_DW.oldReqMsgOnD = ReqMsgOnD;
      SBCMsgIdx = SBC_CUSTOM_ID;

      /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
      /* Event: '<S29>:157' */
      TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

      /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

      /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
      /* Event: '<S29>:151' */
      TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
        (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

      /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

      /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
      /* Event: '<S29>:152' */
      TLE9278BQX_Co_fc_TLE9278BQX_Prs();

      /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
    } else {
      /* Transition: '<S29>:168' */
    }

    if (((CntRefreshWDT != TLE9278BQX_Com_DW.oldCntRefreshWDT) && (KeyReqMsgOnD
          != ((uint32_T)0x01CABF38))) && (((int32_T)
          TLE9278BQX_Com_DW.tstWDTExpired) == 0)) {
      /* Transition: '<S29>:184' */
      TLE9278BQX_Com_DW.oldCntRefreshWDT = CntRefreshWDT;
      guard11 = false;
      if (((int32_T)StSBCMode) != 2) {
        /* Transition: '<S29>:187' */
        /* Transition: '<S29>:483' */
        if (((int32_T)Core2IvorDetected) != 0) {
          /* Transition: '<S29>:164' */
          /*  Bypass Mode with EE */
          SetSBCSysStat = (uint16_T)0x1E;
          if (((int32_T)TLE9278BQX_Com_DW.flgIvorEE) == 0) {
            /* Transition: '<S29>:532' */
            SBCMsgIdx = SBC_BYPASS_EE_WRITE_ID;
            TLE9278BQX_Com_DW.flgIvorEE = 1U;

            /* Transition: '<S29>:198' */
            guard11 = true;
          } else {
            /* Transition: '<S29>:536' */
            SBCMsgIdx = SBC_BYPASS_WRITE_ID;
            TLE9278BQX_Com_DW.flgIvorEE = 2U;

            /* Transition: '<S29>:537' */
            /* Transition: '<S29>:534' */
            /* Transition: '<S29>:198' */
            guard11 = true;
          }
        } else {
          /* Transition: '<S29>:497' */
          if (((int32_T)FlguCTransient) != 0) {
            /* Transition: '<S29>:500' */
            /* Transition: '<S29>:1180' */
            /* Transition: '<S29>:1176' */
            /*  Bypass Mode */
            SetSBCSysStat = (uint16_T)0x22;
            SBCMsgIdx = SBC_BYPASS_WRITE_ID;

            /* Transition: '<S29>:1182' */
            /* Transition: '<S29>:499' */
            /* Transition: '<S29>:534' */
            /* Transition: '<S29>:198' */
            guard11 = true;
          } else {
            /* Transition: '<S29>:502' */
            if (((int32_T)Flg5PreIgnition) != 0) {
              /* Transition: '<S29>:505' */
              /*  Bypass Mode */
              SetSBCSysStat = (uint16_T)0x23;
              SBCMsgIdx = SBC_BYPASS_WRITE_ID;

              /* Transition: '<S29>:504' */
              /* Transition: '<S29>:1182' */
              /* Transition: '<S29>:499' */
              /* Transition: '<S29>:534' */
              /* Transition: '<S29>:198' */
              guard11 = true;
            } else {
              /* Transition: '<S29>:934' */
              /* Transition: '<S29>:935' */
              n = 0U;
              do {
                exitg3 = 0;
                if ((n < N_VT_SM_DIAG_CODE) && (((int32_T)ENSMLOOP) != 0)) {
                  /* Transition: '<S29>:950' */
                  if (((int32_T)VtSMDiagCode[(n)]) == 0) {
                    /* Transition: '<S29>:954' */
                    /* Transition: '<S29>:953' */
                    n = (uint8_T)((int32_T)(((int32_T)n) + 1));
                  } else {
                    /* Transition: '<S29>:937' */
                    /*  Bypass Mode */
                    SetSBCSysStat = VtSMDiagCode[(n)];
                    SBCMsgIdx = SBC_BYPASS_WRITE_ID;
                    exitg3 = 1;
                  }
                } else {
                  /* Transition: '<S29>:956' */
                  /* Transition: '<S29>:941' */
                  /* Transition: '<S29>:942' */
                  exitg3 = 2;
                }
              } while (exitg3 == 0);

              if (exitg3 == 1) {
                /* Transition: '<S29>:938' */
                /* Transition: '<S29>:504' */
                /* Transition: '<S29>:1182' */
                /* Transition: '<S29>:499' */
                /* Transition: '<S29>:534' */
                /* Transition: '<S29>:198' */
                guard11 = true;
              } else if (((int32_T)TLE9278BQX_Com_DW.syncKeyWDTStop) == 0) {
                /* Transition: '<S29>:165' */
                /*  Normal clear WDT */
                SBCMsgIdx = SBC_CLEAR_WDT_WRITE_ID;

                /* Transition: '<S29>:486' */
                /* Transition: '<S29>:938' */
                /* Transition: '<S29>:504' */
                /* Transition: '<S29>:1182' */
                /* Transition: '<S29>:499' */
                /* Transition: '<S29>:534' */
                /* Transition: '<S29>:198' */
                guard11 = true;
              } else {
                /* Transition: '<S29>:489' */
                /* Transition: '<S29>:492' */
                /*  Clear WDT and Open the Door WD disable in Stop (Optional) */
                SBCMsgIdx = SBC_TO_STOP_WRITE_ID;

                /* Transition: '<S29>:491' */
                /* Transition: '<S29>:486' */
                /* Transition: '<S29>:938' */
                /* Transition: '<S29>:504' */
                /* Transition: '<S29>:1182' */
                /* Transition: '<S29>:499' */
                /* Transition: '<S29>:534' */
                /* Transition: '<S29>:198' */
                guard11 = true;
              }
            }
          }
        }
      } else {
        /* Transition: '<S29>:189' */
        if (((int32_T)ReqSBCMode) == 0) {
          /* Transition: '<S29>:190' */
          /*  Clear WDT and Go to Normal */
          SBCMsgIdx = SBC_TO_NORMAL_WRITE_ID;

          /* Transition: '<S29>:491' */
          /* Transition: '<S29>:486' */
          /* Transition: '<S29>:938' */
          /* Transition: '<S29>:504' */
          /* Transition: '<S29>:1182' */
          /* Transition: '<S29>:499' */
          /* Transition: '<S29>:534' */
          /* Transition: '<S29>:198' */
          guard11 = true;
        } else {
          /* Transition: '<S29>:195' */
          if (((int32_T)FlgSBCWDStmDis) != 0) {
            /* Transition: '<S29>:191' */
            /*  Stop clear WDT */
            entryg1 = 3;
          } else {
            /* Transition: '<S29>:196' */
            /*  Normal clear WDT */
            SBCMsgIdx = SBC_CLEAR_WDT_WRITE_ID;
            guard11 = true;
          }
        }
      }

      if (guard11) {
        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
        /* Transition: '<S29>:167' */
        /* Event: '<S29>:157' */
        TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

        /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
        /* Event: '<S29>:151' */
        TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
          (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

        /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

        /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        /* Event: '<S29>:152' */
        TLE9278BQX_Co_fc_TLE9278BQX_Prs();

        /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */

        /* Outputs for Function Call SubSystem: '<S4>/fc_WDT_Clear' */
        /* CCaller: '<S19>/WDT_T10ms' */
        /* Event: '<S29>:162' */
        WDT_T10ms();

        /* End of Outputs for SubSystem: '<S4>/fc_WDT_Clear' */
        if (((int32_T)TLE9278BQX_Com_DW.flgIvorEE) == 1) {
          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_IvorEE' */
          /* CCaller: '<S16>/TLE9278BQX_IvorEE' */
          /* Transition: '<S29>:527' */
          /* Event: '<S29>:524' */
          TLE9278BQX_IvorEE();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_IvorEE' */
        } else {
          /* Transition: '<S29>:529' */
          /* Transition: '<S29>:530' */
        }

        entryg1 = 2;
      }
    } else {
      /* Transition: '<S29>:26' */
      entryg1 = 1;
    }
  }

  do {
    exitg2 = 0;
    if (entryg1 == 0) {
    } else if (entryg1 == 1) {
      entryg1 = 0;
    } else if (entryg1 == 2) {
      entryg1 = 0;
    } else {
      entryg1 = 0;
    }

    if (((((int32_T)FlgSBCResend) != 0) && (((int32_T)SBCNRESEND) != 0)) ||
        (((int32_T)TLE9278BQX_Com_DW.flgInitDone) == 0)) {
      /* Transition: '<S27>:7' */
      /* Event: '<S27>:8' */
      /* Gateway: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine */
      /* During: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine */
      if (((uint32_T)TLE9278BQX_Com_DW.is_active_c14_TLE9278BQX_Com) == 0U) {
        /* Entry: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine */
        TLE9278BQX_Com_DW.is_active_c14_TLE9278BQX_Com = 1U;

        /* Entry Internal: TLE9278BQX_Com/fc_Bkg/SBC_Messages_Machine/fc_SendData/TLE9278BQX_SendData_Machine */
        /* Entry Internal 'SBC_MODE_CTRL': '<S29>:20' */
        /* Transition: '<S29>:2' */
        CntTrgSBCRes = 0U;
        TLE9278BQX_Com_DW.syncKeyWDTStop = 0U;
        TLE9278BQX_Com_DW.tstWDTExpired = 0U;
        TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
        StSBCSafeTest = SF_NORMAL;
        TLE9278BQX_Com_DW.StSbc = SBC_MODE_INIT;
        SetAdcSel = 0U;
        TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_Com_IN_SBC_MODE_INIT;

        /* Entry Internal 'WDT_MODE_CTRL': '<S29>:21' */
        /* Transition: '<S29>:92' */
        TLE9278BQX_Com_DW.RunBkgWDT_n = 1U;
        TLE9278BQX_Com_DW.flgIvorEE = 0U;
      } else {
        /* During 'SBC_MODE_CTRL': '<S29>:20' */
        guard1 = false;
        guard2 = false;
        switch (TLE9278BQX_Com_DW.is_SBC_MODE_CTRL) {
         case TLE9278BQX__IN_SBC_MODE_EXPIRED:
          /* During 'SBC_MODE_EXPIRED': '<S29>:243' */
          /* Transition: '<S29>:292' */
          TLE9278BQX_Com_DW.tstWDTExpired = 1U;

          /* Transition: '<S29>:247' */
          if ((((int32_T)TLE9278BQX_Com_DW.cntTO) > 2) && (((int32_T)
                FlguCTransient) == 0)) {
            /* Transition: '<S29>:278' */
            TLE9278BQX_Com_DW.stSbc = SBC_MODE_NORMAL;
            StSBCSafeTest = SF_WDT_EXPIRED;

            /* Transition: '<S29>:9' */
            TLE9278BQX_Com_DW.StSbc = TLE9278BQX_Com_DW.stSbc;
            TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
            TLE9278BQX_Com_DW.tstWDTExpired = 0U;
            SetSBCSysStat = (uint16_T)0x04;
            TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
          } else {
            /* Transition: '<S29>:277' */
            TLE9278BQX_Com_DW.cntTO = (uint8_T)((int32_T)(((int32_T)
              TLE9278BQX_Com_DW.cntTO) + 1));
          }
          break;

         case TLE9278BQX_Com_IN_SBC_MODE_INIT:
          TLE9278BQX_Com_SBC_MODE_INIT();
          break;

         case TLE9278BQX_Co_IN_SBC_DEBUG_DATA:
          /* During 'SBC_DEBUG_DATA': '<S29>:210' */
          /* Transition: '<S29>:213' */
          if ((KeyReqMsgOnD & 0xFFFFFFFE) != ((uint32_T)0x01CABF38)) {
            /* Transition: '<S29>:215' */
            TLE9278BQX_Com_DW.StSbc = SBC_MODE_NORMAL;
            TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
          } else {
            /* Transition: '<S29>:214' */
          }
          break;

         case TLE9278BQX_C_IN_SBC_MODE_NORMAL:
          /* During 'SBC_MODE_NORMAL': '<S29>:3' */
          /* Transition: '<S29>:99' */
          CntTrgSBCRes = (uint8_T)((int32_T)(((int32_T)CntTrgSBCRes) + 1));
          if ((KeyReqMsgOnD & 0xFFFFFFFE) == ((uint32_T)0x01CABF38)) {
            /* Transition: '<S29>:212' */
            TLE9278BQX_Com_DW.StSbc = SBC_DEBUG_DATA;
            TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_Co_IN_SBC_DEBUG_DATA;
          } else {
            /* Transition: '<S29>:217' */
            /* Transition: '<S29>:1207' */
            if (CntSBCWrite != TLE9278BQX_Com_DW.oldCntSBCWrite) {
              /* Transition: '<S29>:98' */
              TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
              SBCMsgIdx = SBC_NORMAL_WRITE_ID;

              /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
              /* Event: '<S29>:157' */
              TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

              /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

              /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
              /* Event: '<S29>:151' */
              TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
                (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

              /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

              /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
              /* Event: '<S29>:152' */
              TLE9278BQX_Co_fc_TLE9278BQX_Prs();

              /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
              /* Transition: '<S29>:13' */
            } else {
              /* Transition: '<S29>:100' */
            }

            if (((int32_T)ReqSBCMode) == 2) {
              /* Transition: '<S29>:14' */
              TLE9278BQX_Com_DW.syncKeyWDTStop = 1U;
              if (((int32_T)StSBCMode) == 2) {
                /* Transition: '<S29>:74' */
                TLE9278BQX_Com_DW.StSbc = SBC_MODE_STOP;
                TLE9278BQX_Com_DW.syncKeyWDTStop = 0U;
                TLE9278BQX_Com_DW.is_SBC_MODE_CTRL =
                  TLE9278BQX_Com_IN_SBC_MODE_STOP;
              } else {
                /* Transition: '<S29>:75' */
                /* Transition: '<S29>:156' */
                guard2 = true;
              }
            } else {
              /* Transition: '<S29>:16' */
              TLE9278BQX_Com_DW.syncKeyWDTStop = 0U;
              guard2 = true;
            }
          }
          break;

         case TLE9278BQX_Co_IN_SBC_MODE_RESET:
          /* During 'SBC_MODE_RESET': '<S29>:63' */
          /* Transition: '<S29>:68' */
          /*  Wait SW Reset */
          SetSBCSysStat = (uint16_T)0x03;
          SBCMsgIdx = SBC_TO_RESET_WRITE_ID;

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
          /* Event: '<S29>:157' */
          TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

          /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
          /* Event: '<S29>:151' */
          TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
            (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

          /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          /* Event: '<S29>:152' */
          TLE9278BQX_Co_fc_TLE9278BQX_Prs();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          if (((int32_T)ReqSBCMode) != 3) {
            /* Transition: '<S29>:178' */
            TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
          } else {
            /* Transition: '<S29>:69' */
          }
          break;

         case TLE9278BQX_Co_IN_SBC_MODE_SLEEP:
          /* During 'SBC_MODE_SLEEP': '<S29>:5' */
          /* Transition: '<S29>:181' */
          /* Transition: '<S29>:30' */
          /*  Wait Sleep */
          FlgSBCFOEn = 0U;
          SetSBCSysStat = (uint16_T)0x02;
          SetSBCCan1 = SBCCAN01EN;
          SBCMsgIdx = SBC_TO_SLEEP_WRITE_ID;

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
          /* Event: '<S29>:157' */
          TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

          /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
          /* Event: '<S29>:151' */
          TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
            (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

          /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          /* Event: '<S29>:152' */
          TLE9278BQX_Co_fc_TLE9278BQX_Prs();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          break;

         default:
          /* During 'SBC_MODE_STOP': '<S29>:4' */
          /* Transition: '<S29>:103' */
          CntTrgSBCRes = (uint8_T)((int32_T)(((int32_T)CntTrgSBCRes) + 1));
          if (CntSBCWrite != TLE9278BQX_Com_DW.oldCntSBCWrite) {
            /* Transition: '<S29>:105' */
            TLE9278BQX_Com_DW.oldCntSBCWrite = CntSBCWrite;
            SBCMsgIdx = SBC_STOP_WRITE_ID;

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
            /* Event: '<S29>:157' */
            TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

            /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
            /* Event: '<S29>:151' */
            TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
              (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

            /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            /* Event: '<S29>:152' */
            TLE9278BQX_Co_fc_TLE9278BQX_Prs();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            /* Transition: '<S29>:83' */
          } else {
            /* Transition: '<S29>:104' */
          }

          /* Transition: '<S29>:33' */
          SBCMsgIdx = SBC_STOP_READ_ID;

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
          /* Event: '<S29>:157' */
          TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

          /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
          /* Event: '<S29>:151' */
          TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
            (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

          /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          /* Event: '<S29>:152' */
          TLE9278BQX_Co_fc_TLE9278BQX_Prs();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          if (((int32_T)StSBCMode) == 0) {
            /* Transition: '<S29>:34' */
            TLE9278BQX_Com_DW.StSbc = TLE9278BQX_Com_DW.stSbc;
            TLE9278BQX_Com_DW.is_SBC_MODE_CTRL = TLE9278BQX_C_IN_SBC_MODE_NORMAL;
          } else {
            /* Transition: '<S29>:85' */
          }
          break;
        }

        if (guard2) {
          if (((int32_T)ReqSBCMode) == 3) {
            /* Transition: '<S29>:77' */
            SetSBCSysStat = (uint16_T)0x03;
            SBCMsgIdx = SBC_TO_RESET_WRITE_ID;

            /* Outputs for Function Call SubSystem: '<S4>/fc_InitRoutine5' */
            /* Event: '<S29>:403' */
            TLE9278BQX_Com_fc_InitRoutine5();

            /* End of Outputs for SubSystem: '<S4>/fc_InitRoutine5' */

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
            /* Event: '<S29>:157' */
            TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

            /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
            /* Event: '<S29>:151' */
            TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
              (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

            /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            /* Event: '<S29>:152' */
            TLE9278BQX_Co_fc_TLE9278BQX_Prs();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            /* Transition: '<S29>:78' */
            if (((int32_T)StSBCMode) == 3) {
              /* Transition: '<S29>:17' */
              TLE9278BQX_Com_DW.StSbc = SBC_MODE_RESET;
              TLE9278BQX_Com_DW.is_SBC_MODE_CTRL =
                TLE9278BQX_Co_IN_SBC_MODE_RESET;
            } else {
              /* Transition: '<S29>:154' */
              guard1 = true;
            }
          } else {
            /* Transition: '<S29>:18' */
            guard1 = true;
          }
        }

        if (guard1) {
          if (((int32_T)ReqSBCMode) == 1) {
            /* Transition: '<S29>:80' */
            FlgSBCFOEn = 0U;
            SetSBCSysStat = (uint16_T)0x02;
            SetSBCCan1 = SBCCAN01EN;
            SBCMsgIdx = SBC_TO_SLEEP_WRITE_ID;

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
            /* Event: '<S29>:157' */
            TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

            /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
            /* Event: '<S29>:151' */
            TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
              (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

            /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            /* Event: '<S29>:152' */
            TLE9278BQX_Co_fc_TLE9278BQX_Prs();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            if (((int32_T)StSBCMode) == 1) {
              /* Transition: '<S29>:66' */
              TLE9278BQX_Com_DW.StSbc = SBC_MODE_SLEEP;
              TLE9278BQX_Com_DW.is_SBC_MODE_CTRL =
                TLE9278BQX_Co_IN_SBC_MODE_SLEEP;
            } else {
              /* Transition: '<S29>:81' */
            }
          } else {
            /* Transition: '<S29>:65' */
            SBCMsgIdx = SBC_NORMAL_READ_ID;

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
            /* Event: '<S29>:157' */
            TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

            /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
            /* Event: '<S29>:151' */
            TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
              (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

            /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            /* Event: '<S29>:152' */
            TLE9278BQX_Co_fc_TLE9278BQX_Prs();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            SetAdcSel = (uint16_T)((((int32_T)SetAdcSel) == 0) ? 1 : 0);
          }
        }

        /* During 'WDT_MODE_CTRL': '<S29>:21' */
        /* During 'REFRESH_WDT': '<S29>:22' */
        /* Transition: '<S29>:173' */
        if ((KeyReqMsgOnD != TLE9278BQX_Com_DW.oldKeyReqMsgOnD) || (ReqMsgOnD !=
             TLE9278BQX_Com_DW.oldReqMsgOnD)) {
          /* Transition: '<S29>:174' */
          TLE9278BQX_Com_DW.oldKeyReqMsgOnD = KeyReqMsgOnD;
          TLE9278BQX_Com_DW.oldReqMsgOnD = ReqMsgOnD;
          SBCMsgIdx = SBC_CUSTOM_ID;

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
          /* Event: '<S29>:157' */
          TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

          /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
          /* Event: '<S29>:151' */
          TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
            (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

          /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

          /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
          /* Event: '<S29>:152' */
          TLE9278BQX_Co_fc_TLE9278BQX_Prs();

          /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
        } else {
          /* Transition: '<S29>:168' */
        }

        if (((CntRefreshWDT != TLE9278BQX_Com_DW.oldCntRefreshWDT) &&
             (KeyReqMsgOnD != ((uint32_T)0x01CABF38))) && (((int32_T)
              TLE9278BQX_Com_DW.tstWDTExpired) == 0)) {
          /* Transition: '<S29>:184' */
          TLE9278BQX_Com_DW.oldCntRefreshWDT = CntRefreshWDT;
          guard1 = false;
          if (((int32_T)StSBCMode) != 2) {
            /* Transition: '<S29>:187' */
            /* Transition: '<S29>:483' */
            if (((int32_T)Core2IvorDetected) != 0) {
              /* Transition: '<S29>:164' */
              /*  Bypass Mode with EE */
              SetSBCSysStat = (uint16_T)0x1E;
              if (((int32_T)TLE9278BQX_Com_DW.flgIvorEE) == 0) {
                /* Transition: '<S29>:532' */
                SBCMsgIdx = SBC_BYPASS_EE_WRITE_ID;
                TLE9278BQX_Com_DW.flgIvorEE = 1U;

                /* Transition: '<S29>:198' */
                guard1 = true;
              } else {
                /* Transition: '<S29>:536' */
                SBCMsgIdx = SBC_BYPASS_WRITE_ID;
                TLE9278BQX_Com_DW.flgIvorEE = 2U;

                /* Transition: '<S29>:537' */
                /* Transition: '<S29>:534' */
                /* Transition: '<S29>:198' */
                guard1 = true;
              }
            } else {
              /* Transition: '<S29>:497' */
              if (((int32_T)FlguCTransient) != 0) {
                /* Transition: '<S29>:500' */
                /* Transition: '<S29>:1180' */
                /* Transition: '<S29>:1176' */
                /*  Bypass Mode */
                SetSBCSysStat = (uint16_T)0x22;
                SBCMsgIdx = SBC_BYPASS_WRITE_ID;

                /* Transition: '<S29>:1182' */
                /* Transition: '<S29>:499' */
                /* Transition: '<S29>:534' */
                /* Transition: '<S29>:198' */
                guard1 = true;
              } else {
                /* Transition: '<S29>:502' */
                if (((int32_T)Flg5PreIgnition) != 0) {
                  /* Transition: '<S29>:505' */
                  /*  Bypass Mode */
                  SetSBCSysStat = (uint16_T)0x23;
                  SBCMsgIdx = SBC_BYPASS_WRITE_ID;

                  /* Transition: '<S29>:504' */
                  /* Transition: '<S29>:1182' */
                  /* Transition: '<S29>:499' */
                  /* Transition: '<S29>:534' */
                  /* Transition: '<S29>:198' */
                  guard1 = true;
                } else {
                  /* Transition: '<S29>:934' */
                  /* Transition: '<S29>:935' */
                  n = 0U;
                  do {
                    exitg4 = 0;
                    if ((n < N_VT_SM_DIAG_CODE) && (((int32_T)ENSMLOOP) != 0)) {
                      /* Transition: '<S29>:950' */
                      if (((int32_T)VtSMDiagCode[(n)]) == 0) {
                        /* Transition: '<S29>:954' */
                        /* Transition: '<S29>:953' */
                        n = (uint8_T)((int32_T)(((int32_T)n) + 1));
                      } else {
                        /* Transition: '<S29>:937' */
                        /*  Bypass Mode */
                        SetSBCSysStat = VtSMDiagCode[(n)];
                        SBCMsgIdx = SBC_BYPASS_WRITE_ID;

                        /* Transition: '<S29>:938' */
                        /* Transition: '<S29>:504' */
                        /* Transition: '<S29>:1182' */
                        /* Transition: '<S29>:499' */
                        /* Transition: '<S29>:534' */
                        /* Transition: '<S29>:198' */
                        guard1 = true;
                        exitg4 = 1;
                      }
                    } else {
                      /* Transition: '<S29>:956' */
                      /* Transition: '<S29>:941' */
                      /* Transition: '<S29>:942' */
                      if (((int32_T)TLE9278BQX_Com_DW.syncKeyWDTStop) == 0) {
                        /* Transition: '<S29>:165' */
                        /*  Normal clear WDT */
                        SBCMsgIdx = SBC_CLEAR_WDT_WRITE_ID;

                        /* Transition: '<S29>:486' */
                        /* Transition: '<S29>:938' */
                        /* Transition: '<S29>:504' */
                        /* Transition: '<S29>:1182' */
                        /* Transition: '<S29>:499' */
                        /* Transition: '<S29>:534' */
                        /* Transition: '<S29>:198' */
                        guard1 = true;
                      } else {
                        /* Transition: '<S29>:489' */
                        /* Transition: '<S29>:492' */
                        /*  Clear WDT and Open the Door WD disable in Stop (Optional) */
                        SBCMsgIdx = SBC_TO_STOP_WRITE_ID;

                        /* Transition: '<S29>:491' */
                        /* Transition: '<S29>:486' */
                        /* Transition: '<S29>:938' */
                        /* Transition: '<S29>:504' */
                        /* Transition: '<S29>:1182' */
                        /* Transition: '<S29>:499' */
                        /* Transition: '<S29>:534' */
                        /* Transition: '<S29>:198' */
                        guard1 = true;
                      }

                      exitg4 = 1;
                    }
                  } while (exitg4 == 0);
                }
              }
            }
          } else {
            /* Transition: '<S29>:189' */
            if (((int32_T)ReqSBCMode) == 0) {
              /* Transition: '<S29>:190' */
              /*  Clear WDT and Go to Normal */
              SBCMsgIdx = SBC_TO_NORMAL_WRITE_ID;

              /* Transition: '<S29>:491' */
              /* Transition: '<S29>:486' */
              /* Transition: '<S29>:938' */
              /* Transition: '<S29>:504' */
              /* Transition: '<S29>:1182' */
              /* Transition: '<S29>:499' */
              /* Transition: '<S29>:534' */
              /* Transition: '<S29>:198' */
              guard1 = true;
            } else {
              /* Transition: '<S29>:195' */
              if (((int32_T)FlgSBCWDStmDis) != 0) {
                /* Transition: '<S29>:191' */
                /*  Stop clear WDT */
              } else {
                /* Transition: '<S29>:196' */
                /*  Normal clear WDT */
                SBCMsgIdx = SBC_CLEAR_WDT_WRITE_ID;
                guard1 = true;
              }
            }
          }

          if (guard1) {
            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */
            /* Transition: '<S29>:167' */
            /* Event: '<S29>:157' */
            TLE9278BQX_Co_fc_TLE9278BQX_Mgm();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Mgm' */

            /* Outputs for Function Call SubSystem: '<S4>/SBC_Compose_Data' */
            /* Event: '<S29>:151' */
            TLE9278BQX_Com_SBC_Compose_Data((&(SBCDataTxBuffer[0])),
              (&(SBCDataRxBuffer[0])), &TLE9278BQX_Com_DW.SBC_Compose_Data);

            /* End of Outputs for SubSystem: '<S4>/SBC_Compose_Data' */

            /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_Prs' */
            /* Event: '<S29>:152' */
            TLE9278BQX_Co_fc_TLE9278BQX_Prs();

            /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_Prs' */

            /* Outputs for Function Call SubSystem: '<S4>/fc_WDT_Clear' */
            /* CCaller: '<S19>/WDT_T10ms' */
            /* Event: '<S29>:162' */
            WDT_T10ms();

            /* End of Outputs for SubSystem: '<S4>/fc_WDT_Clear' */
            if (((int32_T)TLE9278BQX_Com_DW.flgIvorEE) == 1) {
              /* Outputs for Function Call SubSystem: '<S4>/fc_TLE9278BQX_IvorEE' */
              /* CCaller: '<S16>/TLE9278BQX_IvorEE' */
              /* Transition: '<S29>:527' */
              /* Event: '<S29>:524' */
              TLE9278BQX_IvorEE();

              /* End of Outputs for SubSystem: '<S4>/fc_TLE9278BQX_IvorEE' */
            } else {
              /* Transition: '<S29>:529' */
              /* Transition: '<S29>:530' */
            }
          }
        } else {
          /* Transition: '<S29>:26' */
        }
      }

      TLE9278BQX_Com_DW.cntSBCResend = EECntSBCResend;
      TLE9278BQX_Com_DW.cntSBCResend = (uint16_T)((int32_T)(((int32_T)
        TLE9278BQX_Com_DW.cntSBCResend) + 1));

      /* Outputs for Function Call SubSystem: '<S7>/fc_EECntSBCResend'
       *
       * Block description for '<S7>/fc_EECntSBCResend':
       *  <Inline Rules> Because volatile input
       */
      /* S-Function (EECntSBCResend_Addr_U16): '<S25>/C//C++ Code Block' incorporates:
       *  Inport: '<Root>/CntRefreshWDT'
       *  Inport: '<Root>/CntSBCWrite'
       *  Inport: '<Root>/EECntSBCResend'
       *  Inport: '<Root>/FlguCTransient'
       *  Inport: '<Root>/KeyReqMsgOnD'
       *  Inport: '<Root>/ReqMsgOnD'
       *  Inport: '<Root>/ReqSBCMode'
       *  Inport: '<Root>/StSBCMode'
       *  Inport: '<Root>/VtSMDiagCode'
       *
       * Block description for '<Root>/CntRefreshWDT':
       *  Request refresh WDT flag
       *
       * Block description for '<Root>/CntSBCWrite':
       *  Request in Normal_Mode & STOP_Mode to write of additional data buffer
       *  by SPI
       *
       * Block description for '<Root>/EECntSBCResend':
       *  EEPROM data resend counter
       *
       * Block description for '<Root>/FlguCTransient':
       *  Discovered error to the transient 5V
       *
       * Block description for '<Root>/KeyReqMsgOnD':
       *  Request Mode custom status of the SBC for debug
       *
       * Block description for '<Root>/ReqMsgOnD':
       *  Request Debug pattern messages one-shot (Both edge triggered)
       *
       * Block description for '<Root>/ReqSBCMode':
       *  Request Mode status of the SBC
       *
       * Block description for '<Root>/StSBCMode':
       *  Mode Status of the SBC feedback
       *
       * Block description for '<Root>/VtSMDiagCode':
       *  Diagnosis Safety mechanism code flags
       */
      /* Event: '<S27>:31' */
      EECntSBCResend_Addr_U16_Outputs_wrapper((&(EECntSBCResend)),
        &TLE9278BQX_Com_DW.CCCodeBlock);

      /* S-Function (fc_EECntSBCResend_SetVal): '<S28>/C//C++ Code Block' */
      fc_EECntSBCResend_SetVal_Outputs_wrapper(&TLE9278BQX_Com_DW.cntSBCResend,
        &TLE9278BQX_Com_DW.CCCodeBlock, &TLE9278BQX_Com_DW.CCCodeBlock_l);

      /* End of Outputs for SubSystem: '<S7>/fc_EECntSBCResend' */
      /* Transition: '<S27>:11' */
      cntRetryData += TLE9278BQX_Com_DW.flgInitDone;
      TLE9278BQX_Com_DW.flgInitDone = 1U;
      if (cntRetryData >= SBCNRESEND) {
        /* Transition: '<S27>:13' */
        FlgSBCProgErr = 1U;
        exitg2 = 1;
      } else {
        /* Transition: '<S27>:12' */
      }
    } else {
      /* Transition: '<S27>:6' */
      exitg2 = 1;
    }
  } while (exitg2 == 0);

  /* End of Chart: '<S26>/TLE9278BQX_SendData_Machine' */

  /* Chart: '<S24>/Chart_Resend_Data' incorporates:
   *  SubSystem: '<S4>/fc_TLE9278BQX_Get'
   */
  /* Transition: '<S27>:15' */
  /* Event: '<S27>:19' */
  TLE9278BQX_Co_fc_TLE9278BQX_Get();

  /* Chart: '<S24>/Chart_Resend_Data' incorporates:
   *  SubSystem: '<S4>/fc_TLE9278BQX_Diag'
   */
  /* Event: '<S27>:24' */
  TLE9278BQX_C_fc_TLE9278BQX_Diag();

  /* End of Outputs for SubSystem: '<S7>/Calc_Resend_Data' */

  /* SignalConversion generated from: '<S4>/CntSBCResend' */
  CntSBCResend = TLE9278BQX_Com_DW.CCCodeBlock_l;

  /* SignalConversion generated from: '<S4>/RunBkgWDT' */
  RunBkgWDT = TLE9278BQX_Com_DW.RunBkgWDT_n;

  /* SignalConversion generated from: '<S4>/StSBC' */
  StSBC = TLE9278BQX_Com_DW.StSbc;

  /* user code (Output function Trailer for TID1) */

  /* System '<S2>/fc_Bkg' */

  /* PILOTAGGIO USCITE - 10ms */
}

/* Start for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Com_fc_Init_Start(void)
{
  /* Start for Constant: '<S5>/ID_VER_TLE9278BQX_COM_DEF' */
  IdVer_TLE9278BQX_Com = ID_VER_TLE9278BQX_COM_DEF;
}

/* Output and update for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Com_fc_Init(void)
{
  {
    /* user code (Output function Header for TID2) */

    /* System '<S2>/fc_Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    TLE9278BQX_Com_initialize();

    /* Constant: '<S5>/ID_VER_TLE9278BQX_COM_DEF' */
    IdVer_TLE9278BQX_Com = ID_VER_TLE9278BQX_COM_DEF;

    /* Constant: '<S5>/SBC_MODE_INIT' */
    StSBC = SBC_MODE_INIT;

    /* Constant: '<S5>/ZERO' */
    RunBkgWDT = 0U;

    /* Constant: '<S5>/ZERO1' */
    CntSBCResend = 0U;

    /* CCaller: '<S5>/TLE9278BQX_Cfg_Init' */
    TLE9278BQX_Cfg_Init();

    /* CCaller: '<S5>/TLE9278BQX_Diag_Init' */
    TLE9278BQX_Diag_Init();

    /* CCaller: '<S5>/TLE9278BQX_Get_Init' */
    TLE9278BQX_Get_Init();

    /* CCaller: '<S5>/TLE9278BQX_IOs_Init' */
    TLE9278BQX_IOs_Init();

    /* CCaller: '<S5>/TLE9278BQX_Mgm_Init' */
    TLE9278BQX_Mgm_Init();

    /* CCaller: '<S5>/TLE9278BQX_Prs_Init' */
    TLE9278BQX_Prs_Init();

    /* user code (Output function Trailer for TID2) */

    /* System '<S2>/fc_Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Model step function */
void TLE9278BQX_Com_Bkg(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' incorporates:
   *  SubSystem: '<S2>/fc_Bkg'
   */
  TLE9278BQX_Com_fc_Bkg();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' */
}

/* Model step function */
void TLE9278BQX_Com_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_Com_fc_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model initialize function */
void TLE9278BQX_Com_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_Com_fc_Init_Start();

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */

  /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' incorporates:
   *  SubSystem: '<S2>/fc_Bkg'
   */
  TLE9278BQX_Com_fc_Bkg_Init();

  /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' */

  /* SystemInitialize for Merge: '<S3>/Merge2' */
  StSBC = SBC_MODE_INIT;
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_TLE9278BQX_COM_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * stateflow                                                                  *
 *============================================================================*/