/*****************************************************************************************************************/
/* $HeadURL::                                                                                        $   */
/* $ Description:                                                                                                */
/* $Revision::        $                                                                                          */
/* $Date::                                                $                                                      */
/* $Author::                         $                                                                       */
/*****************************************************************************************************************/

#ifndef _FLASHMGM_H_
#define _FLASHMGM_H_

/*-----------------------------------*
 * INCLUDE FILES
 *-----------------------------------*/
#include "typedefs.h"
#include "flashmgm_out.h"

/*-----------------------------------*
 * PUBLIC DEFINES
 *-----------------------------------*/
#define FLASH_CRC_NOT_VALID -255

/*-----------------------------------*
 * PUBLIC MACROS
 *-----------------------------------*/
// None

/*-----------------------------------*
 * PUBLIC TYPEDEFS
 *-----------------------------------*/
// None

/*-----------------------------------*
 * IMPORTED CALIBRATION
 *-----------------------------------*/
extern CALQUAL CALQUAL_POST uint16_T TESTCRCPERIODCNT;
extern CALQUAL CALQUAL_POST uint8_T  WAITCNT;

/*-----------------------------------*
 * PUBLIC VARIABLE DECLARATIONS
 *-----------------------------------*/
extern uint32_T checksumCalib;
extern uint32_T checksumAppl;
extern uint32_T checksumCalibAppl;
extern uint8_T calcChecksumState;
extern uint32_T checksumCalibTag;
extern uint32_T checksumApplTag;


/*-----------------------------------*
 * PUBLIC FUNCTION PROTOTYPES
 *-----------------------------------*/
/*--------------------------------------------------------------------------*
 * FLASHMGM_CalcChecksum - Function description
 *
 * Arguments:
 * None
 *
 * Returned value:
 * None
 *
 * Usage notes:
 * None
 *--------------------------------------------------------------------------*/


#endif
