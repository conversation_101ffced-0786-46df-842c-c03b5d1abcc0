/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           TLE9278BQX_Mgm.c
 **  File Creation Date: 20-Jun-2023
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         TLE9278BQX_Mgm
 **  Model Description:
 **  Model Version:      1.652
 **  Model Author:       SalimbeniT - Tue Jan 20 16:01:59 2015
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: LanaL - Tue Jun 20 13:16:47 2023
 **
 **  Last Saved Modification:  LanaL - Mon Jul 11 11:29:57 2022
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "TLE9278BQX_Mgm_out.h"
#include "TLE9278BQX_Mgm_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define ADC_STAT_NORM_R_ID             23U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define ADC_STAT_STOP_R_ID             22U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define BUS_CTRL_0_ID                  3U                        /* Referenced by: '<S7>/BUS_CTRL_0_ID' */

/* mask */
#define BUS_CTRL_0_INIT_W_ID           4U                        /* Referenced by: '<S15>/BUS_CTRL_0_INIT_W_ID' */

/* mask */
#define BUS_CTRL_0_NORM_W_ID           4U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define BUS_CTRL_0_SLEEP_W_ID          2U                        /* Referenced by: '<S19>/BUS_CTRL_0_SLEEP_W_ID' */

/* mask */
#define BUS_CTRL_0_TO_N_W_ID           3U                        /* Referenced by: '<S18>/BUS_CTRL_0_TO_N_W_ID' */

/* mask */
#define BUS_CTRL_0_TO_STOP_W_ID        2U                        /* Referenced by: '<S20>/BUS_CTRL_0_TO_STOP_W_ID' */

/* mask */
#define BUS_CTRL_2_ID                  7U                        /* Referenced by: '<S7>/BUS_CTRL_2_ID' */

/* mask */
#define BUS_CTRL_2_INIT_W_ID           8U                        /* Referenced by: '<S15>/BUS_CTRL_2_INIT_W_ID' */

/* mask */
#define BUS_CTRL_2_NORM_W_ID           8U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define BUS_CTRL_2_SLEEP_W_ID          3U                        /* Referenced by: '<S19>/BUS_CTRL_2_SLEEP_W_ID' */

/* mask */
#define BUS_CTRL_2_TO_N_W_ID           4U                        /* Referenced by: '<S18>/BUS_CTRL_2_TO_N_W_ID' */

/* mask */
#define BUS_CTRL_2_TO_STOP_W_ID        3U                        /* Referenced by: '<S20>/BUS_CTRL_2_TO_STOP_W_ID' */

/* mask */
#define BUS_CTRL_3_ID                  8U                        /* Referenced by: '<S7>/BUS_CTRL_3_ID' */

/* mask */
#define BUS_CTRL_3_INIT_W_ID           9U                        /* Referenced by: '<S15>/BUS_CTRL_3_INIT_W_ID' */

/* mask */
#define BUS_CTRL_3_NORM_W_ID           9U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define BUS_CTRL_3_SLEEP_W_ID          4U                        /* Referenced by: '<S19>/BUS_CTRL_3_SLEEP_W_ID' */

/* mask */
#define BUS_CTRL_3_TO_N_W_ID           5U                        /* Referenced by: '<S18>/BUS_CTRL_3_TO_N_W_ID' */

/* mask */
#define BUS_CTRL_3_TO_STOP_W_ID        4U                        /* Referenced by: '<S20>/BUS_CTRL_3_TO_STOP_W_ID' */

/* mask */
#define BUS_STAT_0_NORM_R_ID           16U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define BUS_STAT_0_STOP_R_ID           15U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define BUS_STAT_2_NORM_R_ID           20U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define BUS_STAT_2_STOP_R_ID           19U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define BUS_STAT_3_NORM_R_ID           21U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define BUS_STAT_3_STOP_R_ID           20U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define DEV_STAT_NORM_R_ID             15U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define DEV_STAT_STOP_R_ID             14U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define HW_CTRL_0_ID                   1U                        /* Referenced by: '<S7>/HW_CTRL_0_ID' */

/* mask */
#define HW_CTRL_0_INIT_W_ID            3U                        /* Referenced by: '<S15>/HW_CTRL_0_INIT_W_ID' */

/* mask */
#define HW_CTRL_0_NORM_W_ID            3U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define HW_CTRL_0_STOP_W_ID            1U                        /* Referenced by: '<S54>/HW_CTRL_0_STOP_W_ID' */

/* mask */
#define HW_CTRL_1_ID                   10U                       /* Referenced by: '<S7>/HW_CTRL_1_ID' */

/* mask */
#define HW_CTRL_1_INIT_W_ID            2U                        /* Referenced by: '<S15>/HW_CTRL_1_INIT_W_ID' */

/* mask */
#define HW_CTRL_1_NORM_W_ID            2U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define ID_VER_TLE9278BQX_MGM_DEF      1652U                     /* Referenced by: '<S5>/ID_VER_TLE9278BQX_MGM_DEF' */

/* ID Version */
#define M_S_CTRL_ID                    0U                        /* Referenced by: '<S7>/M_S_CTRL_ID' */

/* mask */
#define M_S_CTRL_INIT_W_ID             1U                        /* Referenced by: '<S15>/M_S_CTRL_INIT_W_ID' */

/* mask */
#define M_S_CTRL_NORM_W_ID             1U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define M_S_CTRL_SLEEP_W_ID            6U                        /* Referenced by: '<S19>/M_S_CTRL_SLEEP_W_ID' */

/* mask */
#define M_S_CTRL_TO_N_W_ID             1U                        /* Referenced by: '<S18>/M_S_CTRL_TO_N_W_ID' */

/* mask */
#define SMPS_STAT_NORM_R_ID            22U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define SMPS_STAT_STOP_R_ID            21U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define SUP_STAT_0_NORM_R_ID           13U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define SUP_STAT_0_STOP_R_ID           12U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define SUP_STAT_1_NORM_R_ID           12U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define SUP_STAT_1_STOP_R_ID           11U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define SYS_STAT_CTRL_BYPASS_EE_W_ID   3U                        /* Referenced by: '<S48>/SYS_STAT_CTRL_BYPASS_EE_W_ID' */
#define SYS_STAT_CTRL_BYPASS_W_ID      2U                        /* Referenced by: '<S49>/SYS_STAT_CTRL_BYPASS_W_ID' */
#define SYS_STAT_CTRL_ID               11U                       /* Referenced by: '<S7>/SYS_STAT_CTRL_ID' */

/* mask */
#define SYS_STAT_CTRL_INIT_W_ID        11U                       /* Referenced by: '<S15>/SYS_STAT_CTRL_INIT_W_ID' */

/* mask */
#define SYS_STAT_CTRL_RESET_W_ID       1U                        /* Referenced by: '<S55>/SYS_STAT_CTRL_RESET_W_ID' */

/* mask */
#define SYS_STAT_CTRL_SLEEP_W_ID       1U                        /* Referenced by: '<S19>/SYS_STAT_CTRL_SLEEP_W_ID' */

/* mask */
#define SYS_STAT_NORM_W_ID             11U                       /* Referenced by: '<S17>/Chart' */

/* mask */
#define THERM_STAT_NORM_R_ID           14U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define THERM_STAT_STOP_R_ID           13U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define TIMER_CTRL_ID                  9U                        /* Referenced by: '<S7>/TIMER_CTRL_ID' */

/* mask */
#define TIMER_CTRL_NORM_W_ID           10U                       /* Referenced by: '<S17>/Chart' */

/* mask */
#define WD_CTRL_NORM_W_ID              1U                        /* Referenced by: '<S50>/WD_CTRL_NORM_W_ID' */

/* mask */
#define WD_CTRL_TO_N_W_ID              6U                        /* Referenced by: '<S18>/WD_CTRL_TO_N_W_ID' */

/* mask */
#define WD_CTRL_TO_STOP_W_ID           5U                        /* Referenced by: '<S20>/WD_CTRL_TO_STOP_W_ID' */

/* mask */
#define WK_CTRL_0_ID                   4U                        /* Referenced by: '<S7>/WK_CTRL_0_ID' */

/* mask */
#define WK_CTRL_0_INIT_W_ID            5U                        /* Referenced by: '<S15>/WK_CTRL_0_INIT_W_ID' */

/* mask */
#define WK_CTRL_0_NORM_W_ID            5U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define WK_CTRL_0_TO_STOP_W_ID         1U                        /* Referenced by: '<S20>/WK_CTRL_0_TO_STOP_W_ID' */

/* mask */
#define WK_CTRL_1_ID                   5U                        /* Referenced by: '<S7>/WK_CTRL_1_ID' */

/* mask */
#define WK_CTRL_1_INIT_W_ID            6U                        /* Referenced by: '<S15>/WK_CTRL_1_INIT_W_ID' */

/* mask */
#define WK_CTRL_1_NORM_W_ID            6U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define WK_LVL_STAT_NORM_R_ID          18U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define WK_LVL_STAT_STOP_R_ID          17U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define WK_PD_CTRL_ID                  6U                        /* Referenced by: '<S7>/WK_PD_CTRL_ID' */

/* mask */
#define WK_PD_CTRL_INIT_W_ID           7U                        /* Referenced by: '<S15>/WK_PD_CTRL_INIT_W_ID' */

/* mask */
#define WK_PD_CTRL_NORM_W_ID           7U                        /* Referenced by: '<S17>/Chart' */

/* mask */
#define WK_STAT_0_NORM_R_ID            17U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define WK_STAT_0_STOP_R_ID            16U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */
#define WK_STAT_2_NORM_R_ID            19U                       /* Referenced by: '<S16>/Select_messages' */

/* mask */
#define WK_STAT_2_STOP_R_ID            18U                       /* Referenced by: '<S53>/Select_messages' */

/* mask */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_TLE9278BQX_MGM_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/*Init of local calibrations section*/
#include "cal_sec_init.h"

/* Definition for custom storage class: ELD_LOCAL_CALIBRATION */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCADCSEL = 1U;/* Referenced by: '<S7>/SBCADCSEL' */

/* HW CTRL 1 Adc_Sel */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCBOOSTEN = 1U;/* Referenced by: '<S7>/SBCBOOSTEN' */

/* HW CTRL 0 Boost_EN */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCBOOSTV = 1U;/* Referenced by: '<S7>/SBCBOOSTV' */

/* HW CTRL 1 Boost_V */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCCAN00EN = 3U;/* Referenced by: '<S7>/SBCCAN00EN' */

/* BUS CTRL 0 Can0 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCCAN02EN = 0U;/* Referenced by: '<S7>/SBCCAN02EN' */

/* BUS CTRL 2 Can2 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCCAN03EN = 0U;/* Referenced by: '<S7>/SBCCAN03EN' */

/* BUS CTRL 3 Can3 */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCVBSCFG = 0U;/* Referenced by: '<S7>/SBCVBSCFG' */

/* WK CTRL 0  VBSens_EN */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCVIORT = 0U;/* Referenced by: '<S7>/SBCVIORT' */

/* M S CTRL Vio_RT */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCWKEN = 0U;/* Referenced by: '<S7>/SBCWKEN' */

/* WK CTRL 1 WK_EN */
CALQUAL_PRE CALQUAL CALQUAL_POST uint16_T SBCWKPUPD = 0U;/* Referenced by: '<S7>/SBCWKPUPD' */

/* WK PUPD CTRL WK_PUPD */
#include "cal_sec_end.h"

/*End of local calibrations section*/

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
uint16_T AdcSel;                       /* '<S9>/Merge1' */

/* SBC input analog channel selected */
uint8_T CntSBCWrite;                   /* '<S3>/Merge2' */

/* Write command counter */
uint32_T IdVer_TLE9278BQX_Mgm;         /* '<S5>/ID_VER_TLE9278BQX_MGM_DEF' */

/* ID Version */
uint32_T ReqMsgOnD;                    /* '<S3>/Merge1' */

/* Write command on demand */
uint16_T SBCDataTxBuffer[30];          /* '<S9>/Merge' */

/* SPI Tx data message */

/*Init of static test points section*/

/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT uint16_T SBCCustomTxBuff[90];/* '<S2>/Data Store Memory' */

/* Custom messages */

/*End of static test points section*/

/* Block signals and states (default storage) */
DW_TLE9278BQX_Mgm_T TLE9278BQX_Mgm_DW;

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Output and update for function-call system: '<S2>/fc_Bkg' */
void TLE9278BQX_Mgm_fc_Bkg(void)
{
  uint8_T flgView;
  uint8_T size;
  uint8_T rbReg;
  uint8_T clReg;
  uint16_T tmp;
  uint16_T tmp3;
  uint8_T i;
  uint16_T rtb_Selector8;
  boolean_T rtb_LogicalOperator9;
  uint16_T rtb_Selector;
  boolean_T rtb_LogicalOperator1;
  uint16_T rtb_Selector1;
  boolean_T rtb_LogicalOperator2;
  uint16_T rtb_Selector2;
  boolean_T rtb_LogicalOperator4;
  uint16_T rtb_Selector3;
  boolean_T rtb_LogicalOperator5;
  uint16_T rtb_Selector4;
  boolean_T rtb_LogicalOperator6;
  uint16_T rtb_Selector5;
  boolean_T rtb_LogicalOperator7;
  uint16_T rtb_Selector6;
  boolean_T rtb_LogicalOperator8;
  uint16_T rtb_Selector7;
  boolean_T rtb_LogicalOperator3;
  uint16_T rtb_Selector9;
  boolean_T rtb_LogicalOperator10;
  uint16_T rtb_Selector10;
  boolean_T rtb_RelationalOperator21;
  uint16_T SetSBCSysStat_b;
  uint16_T AdcSel_k;
  int32_T i_0;
  int32_T SetSBCSysStat_b_tmp;
  boolean_T guard1 = false;

  /* Selector: '<S7>/Selector8' incorporates:
   *  Constant: '<S7>/M_S_CTRL_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector8 = VtRecSBCMsg[(((uint8_T)M_S_CTRL_ID))];

  /* Logic: '<S7>/Logical Operator9' incorporates:
   *  Constant: '<S7>/M_S_CTRL_ID'
   *  Constant: '<S7>/SBCVIORT'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory12'
   *  Memory: '<S7>/Memory34'
   *  RelationalOperator: '<S7>/Relational Operator11'
   *  RelationalOperator: '<S7>/Relational Operator22'
   *  Selector: '<S7>/Selector8'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator9 = ((SBCVIORT != TLE9278BQX_Mgm_DW.Memory34_PreviousInput)
    || (VtRecSBCMsg[(((uint8_T)M_S_CTRL_ID))] !=
        TLE9278BQX_Mgm_DW.Memory12_PreviousInput));

  /* Selector: '<S7>/Selector' incorporates:
   *  Constant: '<S7>/HW_CTRL_0_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector = VtRecSBCMsg[(((uint8_T)HW_CTRL_0_ID))];

  /* Logic: '<S7>/Logical Operator1' incorporates:
   *  Constant: '<S7>/HW_CTRL_0_ID'
   *  Constant: '<S7>/SBCBOOSTEN'
   *  Inport: '<Root>/FlgSBCFOEn'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory'
   *  Memory: '<S7>/Memory1'
   *  Memory: '<S7>/Memory13'
   *  RelationalOperator: '<S7>/Relational Operator'
   *  RelationalOperator: '<S7>/Relational Operator1'
   *  RelationalOperator: '<S7>/Relational Operator12'
   *  Selector: '<S7>/Selector'
   *
   * Block description for '<Root>/FlgSBCFOEn':
   *  Forced by Software the FO Pin
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator1 = (((FlgSBCFOEn != TLE9278BQX_Mgm_DW.Memory_PreviousInput)
    || (VtRecSBCMsg[(((uint8_T)HW_CTRL_0_ID))] !=
        TLE9278BQX_Mgm_DW.Memory13_PreviousInput)) || (SBCBOOSTEN !=
    TLE9278BQX_Mgm_DW.Memory1_PreviousInput));

  /* Selector: '<S7>/Selector1' incorporates:
   *  Constant: '<S7>/HW_CTRL_1_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector1 = VtRecSBCMsg[(((uint8_T)HW_CTRL_1_ID))];

  /* Logic: '<S7>/Logical Operator11' incorporates:
   *  Constant: '<S7>/SBCADCSEL'
   *  Inport: '<Root>/SetAdcSel'
   *
   * Block description for '<Root>/SetAdcSel':
   *  Set Adc SBC Input
   */
  AdcSel_k = (uint16_T)(((((int32_T)SBCADCSEL) != 0) || (((int32_T)SetAdcSel) !=
    0)) ? 1 : 0);

  /* Logic: '<S7>/Logical Operator2' incorporates:
   *  Constant: '<S7>/HW_CTRL_1_ID'
   *  Constant: '<S7>/SBCBOOSTV'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory14'
   *  Memory: '<S7>/Memory2'
   *  Memory: '<S7>/Memory5'
   *  RelationalOperator: '<S7>/Relational Operator13'
   *  RelationalOperator: '<S7>/Relational Operator2'
   *  RelationalOperator: '<S7>/Relational Operator5'
   *  Selector: '<S7>/Selector1'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator2 = (((SBCBOOSTV != TLE9278BQX_Mgm_DW.Memory2_PreviousInput)
    || (VtRecSBCMsg[(((uint8_T)HW_CTRL_1_ID))] !=
        TLE9278BQX_Mgm_DW.Memory14_PreviousInput)) || (AdcSel_k !=
    TLE9278BQX_Mgm_DW.Memory5_PreviousInput));

  /* Selector: '<S7>/Selector2' incorporates:
   *  Constant: '<S7>/WK_CTRL_0_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector2 = VtRecSBCMsg[(((uint8_T)WK_CTRL_0_ID))];

  /* Logic: '<S7>/Logical Operator4' incorporates:
   *  Constant: '<S7>/SBCVBSCFG'
   *  Constant: '<S7>/WK_CTRL_0_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory15'
   *  Memory: '<S7>/Memory3'
   *  RelationalOperator: '<S7>/Relational Operator14'
   *  RelationalOperator: '<S7>/Relational Operator3'
   *  Selector: '<S7>/Selector2'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator4 = ((SBCVBSCFG != TLE9278BQX_Mgm_DW.Memory3_PreviousInput)
    || (VtRecSBCMsg[(((uint8_T)WK_CTRL_0_ID))] !=
        TLE9278BQX_Mgm_DW.Memory15_PreviousInput));

  /* Chart: '<S7>/Parity' incorporates:
   *  Inport: '<Root>/SetSBCSysStat'
   *
   * Block description for '<Root>/SetSBCSysStat':
   *  Set System status Configuration
   */
  /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Input/Parity */
  /* During: TLE9278BQX_Mgm/fc_Bkg/Input/Parity */
  /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Input/Parity */
  /* Transition: '<S10>:2' */
  /* Transition: '<S10>:4' */
  i_0 = ((int32_T)((uint32_T)(((uint32_T)SetSBCSysStat) >> ((uint32_T)1)))) &
    ((int32_T)0x01);
  SetSBCSysStat_b_tmp = ((int32_T)((uint32_T)(((uint32_T)SetSBCSysStat) >>
    ((uint32_T)3)))) & ((int32_T)0x01);
  SetSBCSysStat_b = (uint16_T)((int32_T)(((((~(((i_0 ^ SetSBCSysStat_b_tmp) ^
    (((int32_T)((uint32_T)(((uint32_T)SetSBCSysStat) >> ((uint32_T)4)))) &
     ((int32_T)0x01))) ^ (((int32_T)((uint32_T)(((uint32_T)SetSBCSysStat) >>
    ((uint32_T)5)))) & ((int32_T)0x01)))) & ((int32_T)0x01)) * 64) | ((((i_0 ^
    (((int32_T)SetSBCSysStat) & ((int32_T)0x01))) ^ (((int32_T)((uint32_T)
    (((uint32_T)SetSBCSysStat) >> ((uint32_T)2)))) & ((int32_T)0x01))) ^
    SetSBCSysStat_b_tmp) * 128)) | (((int32_T)SetSBCSysStat) & ((int32_T)0x3F))));

  /* Selector: '<S7>/Selector3' incorporates:
   *  Constant: '<S7>/SYS_STAT_CTRL_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector3 = VtRecSBCMsg[(((uint8_T)SYS_STAT_CTRL_ID))];

  /* Logic: '<S7>/Logical Operator5' incorporates:
   *  Constant: '<S7>/SYS_STAT_CTRL_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory16'
   *  Memory: '<S7>/Memory4'
   *  RelationalOperator: '<S7>/Relational Operator15'
   *  RelationalOperator: '<S7>/Relational Operator4'
   *  Selector: '<S7>/Selector3'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator5 = ((SetSBCSysStat_b !=
    TLE9278BQX_Mgm_DW.Memory4_PreviousInput) || (VtRecSBCMsg[(((uint8_T)
    SYS_STAT_CTRL_ID))] != TLE9278BQX_Mgm_DW.Memory16_PreviousInput));

  /* Selector: '<S7>/Selector4' incorporates:
   *  Constant: '<S7>/WK_CTRL_1_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector4 = VtRecSBCMsg[(((uint8_T)WK_CTRL_1_ID))];

  /* Logic: '<S7>/Logical Operator6' incorporates:
   *  Constant: '<S7>/SBCWKEN'
   *  Constant: '<S7>/WK_CTRL_1_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory17'
   *  Memory: '<S7>/Memory6'
   *  RelationalOperator: '<S7>/Relational Operator16'
   *  RelationalOperator: '<S7>/Relational Operator6'
   *  Selector: '<S7>/Selector4'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator6 = ((SBCWKEN != TLE9278BQX_Mgm_DW.Memory6_PreviousInput) ||
    (VtRecSBCMsg[(((uint8_T)WK_CTRL_1_ID))] !=
     TLE9278BQX_Mgm_DW.Memory17_PreviousInput));

  /* Selector: '<S7>/Selector5' incorporates:
   *  Constant: '<S7>/WK_PD_CTRL_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector5 = VtRecSBCMsg[(((uint8_T)WK_PD_CTRL_ID))];

  /* Logic: '<S7>/Logical Operator7' incorporates:
   *  Constant: '<S7>/SBCWKPUPD'
   *  Constant: '<S7>/WK_PD_CTRL_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory18'
   *  Memory: '<S7>/Memory7'
   *  RelationalOperator: '<S7>/Relational Operator17'
   *  RelationalOperator: '<S7>/Relational Operator7'
   *  Selector: '<S7>/Selector5'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator7 = ((SBCWKPUPD != TLE9278BQX_Mgm_DW.Memory7_PreviousInput)
    || (VtRecSBCMsg[(((uint8_T)WK_PD_CTRL_ID))] !=
        TLE9278BQX_Mgm_DW.Memory18_PreviousInput));

  /* Selector: '<S7>/Selector6' incorporates:
   *  Constant: '<S7>/BUS_CTRL_0_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector6 = VtRecSBCMsg[(((uint8_T)BUS_CTRL_0_ID))];

  /* Logic: '<S7>/Logical Operator8' incorporates:
   *  Constant: '<S7>/BUS_CTRL_0_ID'
   *  Constant: '<S7>/SBCCAN00EN'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory19'
   *  Memory: '<S7>/Memory9'
   *  RelationalOperator: '<S7>/Relational Operator18'
   *  RelationalOperator: '<S7>/Relational Operator8'
   *  Selector: '<S7>/Selector6'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator8 = ((SBCCAN00EN != TLE9278BQX_Mgm_DW.Memory9_PreviousInput)
    || (VtRecSBCMsg[(((uint8_T)BUS_CTRL_0_ID))] !=
        TLE9278BQX_Mgm_DW.Memory19_PreviousInput));

  /* Selector: '<S7>/Selector7' incorporates:
   *  Constant: '<S7>/BUS_CTRL_2_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector7 = VtRecSBCMsg[(((uint8_T)BUS_CTRL_2_ID))];

  /* Logic: '<S7>/Logical Operator3' incorporates:
   *  Constant: '<S7>/BUS_CTRL_2_ID'
   *  Constant: '<S7>/SBCCAN02EN'
   *  Inport: '<Root>/SetSBCCan1'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory10'
   *  Memory: '<S7>/Memory11'
   *  Memory: '<S7>/Memory20'
   *  RelationalOperator: '<S7>/Relational Operator10'
   *  RelationalOperator: '<S7>/Relational Operator19'
   *  RelationalOperator: '<S7>/Relational Operator9'
   *  Selector: '<S7>/Selector7'
   *
   * Block description for '<Root>/SetSBCCan1':
   *  Set CAN 1 Configuration
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator3 = (((SetSBCCan1 !=
    TLE9278BQX_Mgm_DW.Memory10_PreviousInput) || (VtRecSBCMsg[(((uint8_T)
    BUS_CTRL_2_ID))] != TLE9278BQX_Mgm_DW.Memory20_PreviousInput)) ||
    (SBCCAN02EN != TLE9278BQX_Mgm_DW.Memory11_PreviousInput));

  /* Selector: '<S7>/Selector9' incorporates:
   *  Constant: '<S7>/BUS_CTRL_3_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector9 = VtRecSBCMsg[(((uint8_T)BUS_CTRL_3_ID))];

  /* Logic: '<S7>/Logical Operator10' incorporates:
   *  Constant: '<S7>/BUS_CTRL_3_ID'
   *  Constant: '<S7>/SBCCAN03EN'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory21'
   *  Memory: '<S7>/Memory35'
   *  RelationalOperator: '<S7>/Relational Operator20'
   *  RelationalOperator: '<S7>/Relational Operator23'
   *  Selector: '<S7>/Selector9'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_LogicalOperator10 = ((SBCCAN03EN !=
    TLE9278BQX_Mgm_DW.Memory35_PreviousInput) || (VtRecSBCMsg[(((uint8_T)
    BUS_CTRL_3_ID))] != TLE9278BQX_Mgm_DW.Memory21_PreviousInput));

  /* Selector: '<S7>/Selector10' incorporates:
   *  Constant: '<S7>/TIMER_CTRL_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_Selector10 = VtRecSBCMsg[(((uint8_T)TIMER_CTRL_ID))];

  /* RelationalOperator: '<S7>/Relational Operator21' incorporates:
   *  Constant: '<S7>/TIMER_CTRL_ID'
   *  Inport: '<Root>/VtRecSBCMsg'
   *  Memory: '<S7>/Memory22'
   *  Selector: '<S7>/Selector10'
   *
   * Block description for '<Root>/VtRecSBCMsg':
   *  Messages recovery on action
   */
  rtb_RelationalOperator21 = (VtRecSBCMsg[(((uint8_T)TIMER_CTRL_ID))] !=
    TLE9278BQX_Mgm_DW.Memory22_PreviousInput);

  /* Sum: '<S7>/Add' incorporates:
   *  Logic: '<S7>/Logical Operator'
   *  Memory: '<S7>/Memory8'
   */
  CntSBCWrite = (uint8_T)(((uint32_T)(((((((((((rtb_LogicalOperator9 ||
    rtb_LogicalOperator1) || rtb_LogicalOperator2) || rtb_LogicalOperator4) ||
    rtb_LogicalOperator5) || rtb_LogicalOperator6) || rtb_LogicalOperator7) ||
    rtb_LogicalOperator8) || rtb_LogicalOperator3) || rtb_LogicalOperator10) ||
    rtb_RelationalOperator21) ? 1 : 0)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory8_PreviousInput));

  /* Sum: '<S7>/Add1' incorporates:
   *  Memory: '<S7>/Memory23'
   */
  TLE9278BQX_Mgm_DW.Memory23_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator9 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory23_PreviousInput));

  /* Sum: '<S7>/Add10' incorporates:
   *  Memory: '<S7>/Memory32'
   */
  TLE9278BQX_Mgm_DW.Memory32_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator10 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory32_PreviousInput));

  /* Sum: '<S7>/Add11' incorporates:
   *  Memory: '<S7>/Memory33'
   */
  TLE9278BQX_Mgm_DW.Memory33_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_RelationalOperator21 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory33_PreviousInput));

  /* Sum: '<S7>/Add2' incorporates:
   *  Memory: '<S7>/Memory24'
   */
  TLE9278BQX_Mgm_DW.Memory24_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator1 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory24_PreviousInput));

  /* Sum: '<S7>/Add3' incorporates:
   *  Memory: '<S7>/Memory25'
   */
  TLE9278BQX_Mgm_DW.Memory25_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator2 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory25_PreviousInput));

  /* Sum: '<S7>/Add4' incorporates:
   *  Memory: '<S7>/Memory26'
   */
  TLE9278BQX_Mgm_DW.Memory26_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator4 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory26_PreviousInput));

  /* Sum: '<S7>/Add5' incorporates:
   *  Memory: '<S7>/Memory27'
   */
  TLE9278BQX_Mgm_DW.Memory27_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator5 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory27_PreviousInput));

  /* Sum: '<S7>/Add6' incorporates:
   *  Memory: '<S7>/Memory28'
   */
  TLE9278BQX_Mgm_DW.Memory28_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator6 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory28_PreviousInput));

  /* Sum: '<S7>/Add7' incorporates:
   *  Memory: '<S7>/Memory29'
   */
  TLE9278BQX_Mgm_DW.Memory29_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator7 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory29_PreviousInput));

  /* Sum: '<S7>/Add8' incorporates:
   *  Memory: '<S7>/Memory30'
   */
  TLE9278BQX_Mgm_DW.Memory30_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator8 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory30_PreviousInput));

  /* Sum: '<S7>/Add9' incorporates:
   *  Memory: '<S7>/Memory31'
   */
  TLE9278BQX_Mgm_DW.Memory31_PreviousInput = (uint8_T)(((uint32_T)
    (rtb_LogicalOperator3 ? 1U : 0U)) + ((uint32_T)
    TLE9278BQX_Mgm_DW.Memory31_PreviousInput));

  /* Truth Table: '<S8>/Scheduler_Messages' incorporates:
   *  Constant: '<S14>/SBC_INIT_READ'
   *  Constant: '<S52>/SBC_PRE_INIT_READ'
   *  Inport: '<Root>/SBCMsgIdx'
   *
   * Block description for '<Root>/SBCMsgIdx':
   *  Data messages pattern index selector
   */
  /* Truth Table Function 'TLE9278BQX_Mgm/fc_Bkg/Scheduler/Scheduler_Messages': '<S11>:1' */
  /*  SBC_INIT_WRITE_ID */
  /* Condition '#1': '<S11>:1:23' */
  /*  SBC_INIT_READ_ID */
  /* Condition '#2': '<S11>:1:27' */
  /*  SBC_NORMAL_WRITE_ID */
  /* Condition '#3': '<S11>:1:31' */
  /*  SBC_NORMAL_READ_ID */
  /* Condition '#4': '<S11>:1:35' */
  /*  SBC_TO_NORMAL_WRITE_ID */
  /* Condition '#5': '<S11>:1:39' */
  /*  SBC_TO_STOP_WRITE_ID */
  /* Condition '#6': '<S11>:1:43' */
  /*  SBC_TO_SLEEP_WRITE_ID */
  /* Condition '#7': '<S11>:1:47' */
  /*  SBC_TO_RESET_WRITE_ID */
  /* Condition '#8': '<S11>:1:51' */
  /*  SBC_STOP_WRITE_ID */
  /* Condition '#9': '<S11>:1:55' */
  /*  SBC_STOP_READ_ID */
  /* Condition '#10': '<S11>:1:59' */
  /*  SBC_CLEAR_WDT_WRITE_ID */
  /* Condition '#11': '<S11>:1:63' */
  /*  SBC_BYPASS_WRITE_ID */
  /* Condition '#12': '<S11>:1:67' */
  /*  SBC_CUSTOM_ID */
  /* Condition '#13': '<S11>:1:71' */
  /*  SBC_PRE_INIT_READ_ID */
  /* Condition '#14': '<S11>:1:75' */
  /*  SBC_BYPASS_EE_WRITE_ID */
  /* Condition '#15': '<S11>:1:79' */
  /*  DEFAULT */
  if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_INIT_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S12>/SBC_INIT_WRITE' */
    /* Decision 'D1': '<S11>:1:85' */
    /*  fc_SBC_INIT_WRITE_ID */
    /* Action '1': '<S11>:1:123' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      /* Assignment: '<S15>/Assignment9' incorporates:
       *  Constant: '<S15>/SBC_INIT_WRITE'
       *  SignalConversion generated from: '<S15>/Assignment'
       */
      SBCDataTxBuffer[(i_0)] = SBC_INIT_WRITE[(i_0)];
    }

    /* Assignment: '<S15>/Assignment' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output'
     *  Constant: '<S15>/HW_CTRL_0_INIT_W_ID'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S7>/SBCBOOSTEN'
     *  Inport: '<Root>/FlgSBCFOEn'
     *
     * Block description for '<Root>/FlgSBCFOEn':
     *  Forced by Software the FO Pin
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output */
    /* Transition: '<S21>:2' */
    /* Transition: '<S21>:21' */
    SBCDataTxBuffer[(((uint8_T)HW_CTRL_0_INIT_W_ID))] = (uint16_T)((((((uint32_T)
      SBC_INIT_WRITE[(((uint8_T)HW_CTRL_0_INIT_W_ID))]) & ((uint32_T)0xDFFF)) |
      ((uint32_T)(((uint32_T)FlgSBCFOEn) << ((uint32_T)13)))) & ((uint32_T)
      0xFDFF)) | ((uint32_T)((int32_T)((((int32_T)SBCBOOSTEN) & ((int32_T)0x0001))
      * 512))));

    /* Assignment: '<S15>/Assignment1' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output1'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S15>/WK_CTRL_0_INIT_W_ID'
     *  Constant: '<S7>/SBCVBSCFG'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output1 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output1 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output1 */
    /* Transition: '<S22>:2' */
    /* Transition: '<S22>:21' */
    SBCDataTxBuffer[(((uint8_T)WK_CTRL_0_INIT_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCVBSCFG) & ((int32_T)0x0003)) * 4096))) |
      (((uint32_T)SBC_INIT_WRITE[(((uint8_T)WK_CTRL_0_INIT_W_ID))]) & ((uint32_T)
      0xCFFF)));

    /* Assignment: '<S15>/Assignment2' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output2'
     *  Constant: '<S15>/HW_CTRL_1_INIT_W_ID'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S7>/SBCBOOSTV'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output2 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output2 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output2 */
    /* Transition: '<S23>:2' */
    /* Transition: '<S23>:21' */
    SBCDataTxBuffer[(((uint8_T)HW_CTRL_1_INIT_W_ID))] = (uint16_T)((((uint32_T)
      ((int32_T)((((int32_T)SBC_INIT_WRITE[(((uint8_T)HW_CTRL_1_INIT_W_ID))]) &
                  ((int32_T)0x7FFF)) | ((int32_T)((uint32_T)(((uint32_T)AdcSel_k)
      << ((uint32_T)15))))))) & ((uint32_T)0xF3FF)) | ((uint32_T)((int32_T)
      ((((int32_T)SBCBOOSTV) & ((int32_T)0x0003)) * 1024))));

    /* Assignment: '<S15>/Assignment3' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output3'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S15>/WK_CTRL_1_INIT_W_ID'
     *  Constant: '<S7>/SBCWKEN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output3 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output3 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output3 */
    /* Transition: '<S24>:2' */
    /* Transition: '<S24>:21' */
    SBCDataTxBuffer[(((uint8_T)WK_CTRL_1_INIT_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCWKEN) & ((int32_T)0x0001)) * 256))) | (((uint32_T)
      SBC_INIT_WRITE[(((uint8_T)WK_CTRL_1_INIT_W_ID))]) & ((uint32_T)0xFEFF)));

    /* Assignment: '<S15>/Assignment4' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output4'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S15>/WK_PD_CTRL_INIT_W_ID'
     *  Constant: '<S7>/SBCWKPUPD'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output4 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output4 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output4 */
    /* Transition: '<S25>:2' */
    /* Transition: '<S25>:21' */
    SBCDataTxBuffer[(((uint8_T)WK_PD_CTRL_INIT_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCWKPUPD) & ((int32_T)0x0003)) * 256))) |
      (((uint32_T)SBC_INIT_WRITE[(((uint8_T)WK_PD_CTRL_INIT_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* Assignment: '<S15>/Assignment5' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output5'
     *  Constant: '<S15>/BUS_CTRL_0_INIT_W_ID'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S7>/SBCCAN00EN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output5 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output5 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output5 */
    /* Transition: '<S26>:2' */
    /* Transition: '<S26>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_0_INIT_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCCAN00EN) & ((int32_T)0x0003)) * 256))) |
      (((uint32_T)SBC_INIT_WRITE[(((uint8_T)BUS_CTRL_0_INIT_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* Assignment: '<S15>/Assignment6' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output6'
     *  Constant: '<S15>/BUS_CTRL_2_INIT_W_ID'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S7>/SBCCAN02EN'
     *  Inport: '<Root>/SetSBCCan1'
     *
     * Block description for '<Root>/SetSBCCan1':
     *  Set CAN 1 Configuration
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output6 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output6 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output6 */
    /* Transition: '<S27>:2' */
    /* Transition: '<S27>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_2_INIT_W_ID))] = (uint16_T)(((((uint32_T)
      ((int32_T)((((int32_T)SBCCAN02EN) & ((int32_T)0x0003)) * 2048))) |
      (((uint32_T)SBC_INIT_WRITE[(((uint8_T)BUS_CTRL_2_INIT_W_ID))]) &
       ((uint32_T)0xE7FF))) & ((uint32_T)0xFCFF)) | ((uint32_T)((int32_T)
      ((((int32_T)SetSBCCan1) & ((int32_T)0x0003)) * 256))));

    /* Assignment: '<S15>/Assignment7' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output7'
     *  Constant: '<S15>/BUS_CTRL_3_INIT_W_ID'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S7>/SBCCAN03EN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output7 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output7 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output7 */
    /* Transition: '<S28>:2' */
    /* Transition: '<S28>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_3_INIT_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCCAN03EN) & ((int32_T)0x0003)) * 256))) |
      (((uint32_T)SBC_INIT_WRITE[(((uint8_T)BUS_CTRL_3_INIT_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* Assignment: '<S15>/Assignment8' incorporates:
     *  Assignment: '<S15>/Assignment9'
     *  Chart: '<S15>/Calc_output8'
     *  Constant: '<S15>/M_S_CTRL_INIT_W_ID'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S7>/SBCVIORT'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output8 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output8 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output8 */
    /* Transition: '<S29>:2' */
    /* Transition: '<S29>:21' */
    SBCDataTxBuffer[(((uint8_T)M_S_CTRL_INIT_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCVIORT) & ((int32_T)0x0003)) * 256))) |
      (((uint32_T)SBC_INIT_WRITE[(((uint8_T)M_S_CTRL_INIT_W_ID))]) & ((uint32_T)
      0xFCFF)));

    /* Assignment: '<S15>/Assignment9' incorporates:
     *  Chart: '<S15>/Calc_output9'
     *  Constant: '<S15>/SBC_INIT_WRITE'
     *  Constant: '<S15>/SYS_STAT_CTRL_INIT_W_ID'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output9 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output9 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_INIT_WRITE/Calc_output9 */
    /* Transition: '<S30>:2' */
    /* Transition: '<S30>:21' */
    SBCDataTxBuffer[(((uint8_T)SYS_STAT_CTRL_INIT_W_ID))] = (uint16_T)
      ((SBC_INIT_WRITE[(((uint8_T)SYS_STAT_CTRL_INIT_W_ID))] & ((uint16_T)0x00FF))
       | (SetSBCSysStat_b << ((uint32_T)8)));

    /* SignalConversion generated from: '<S15>/AdcSel' */
    AdcSel = AdcSel_k;

    /* End of Outputs for SubSystem: '<S12>/SBC_INIT_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_INIT_READ_ID)) {
    /* Outputs for Function Call SubSystem: '<S12>/SBC_INIT_READ' */
    /* Decision 'D2': '<S11>:1:87' */
    /*  fc_SBC_INIT_READ_ID */
    /* Action '2': '<S11>:1:129' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = SBC_INIT_READ[(i_0)];
    }

    /* End of Outputs for SubSystem: '<S12>/SBC_INIT_READ' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_NORMAL_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S12>/SBC_NORMAL_WRITE' */
    /* Chart: '<S17>/Chart' incorporates:
     *  Constant: '<S17>/SBC_NORMAL_WRITE'
     *  Constant: '<S7>/SBCBOOSTEN'
     *  Constant: '<S7>/SBCBOOSTV'
     *  Constant: '<S7>/SBCCAN00EN'
     *  Constant: '<S7>/SBCCAN02EN'
     *  Constant: '<S7>/SBCCAN03EN'
     *  Constant: '<S7>/SBCVBSCFG'
     *  Constant: '<S7>/SBCVIORT'
     *  Constant: '<S7>/SBCWKEN'
     *  Constant: '<S7>/SBCWKPUPD'
     *  Inport: '<Root>/FlgSBCFOEn'
     *  Inport: '<Root>/SetSBCCan1'
     *
     * Block description for '<Root>/FlgSBCFOEn':
     *  Forced by Software the FO Pin
     *
     * Block description for '<Root>/SetSBCCan1':
     *  Set CAN 1 Configuration
     */
    /* Decision 'D3': '<S11>:1:89' */
    /*  fc_SBC_NORMAL_WRITE_ID */
    /* Action '3': '<S11>:1:135' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_NORMAL_WRITE/Chart */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_NORMAL_WRITE/Chart */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_NORMAL_WRITE/Chart */
    /* Transition: '<S32>:2' */
    tmp3 = 0U;
    if (TLE9278BQX_Mgm_DW.Memory23_PreviousInput != TLE9278BQX_Mgm_DW.oldMsCtrl)
    {
      /* Transition: '<S32>:74' */
      TLE9278BQX_Mgm_DW.oldMsCtrl = TLE9278BQX_Mgm_DW.Memory23_PreviousInput;
      tmp3 = 1U;
      TLE9278BQX_Mgm_DW.buffOut_k[1] = (uint16_T)(((uint32_T)((int32_T)
        ((((int32_T)SBCVIORT) & ((int32_T)0x0003)) * 256))) | (((uint32_T)
        SBC_NORMAL_WRITE[(((uint8_T)M_S_CTRL_NORM_W_ID))]) & ((uint32_T)0xFCFF)));
    } else {
      /* Transition: '<S32>:76' */
    }

    if (TLE9278BQX_Mgm_DW.Memory24_PreviousInput != TLE9278BQX_Mgm_DW.oldHwCtrl0)
    {
      /* Transition: '<S32>:4' */
      TLE9278BQX_Mgm_DW.oldHwCtrl0 = TLE9278BQX_Mgm_DW.Memory24_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)((((((uint32_T)
        SBC_NORMAL_WRITE[(((uint8_T)HW_CTRL_0_NORM_W_ID))]) & ((uint32_T)0xDFFF))
        | ((uint32_T)(((uint32_T)FlgSBCFOEn) << ((uint32_T)13)))) & ((uint32_T)
        0xFDFF)) | ((uint32_T)((int32_T)((((int32_T)SBCBOOSTEN) & ((int32_T)
        0x0001)) * 512))));
    } else {
      /* Transition: '<S32>:5' */
    }

    if (TLE9278BQX_Mgm_DW.Memory26_PreviousInput != TLE9278BQX_Mgm_DW.oldWkCtrl0)
    {
      /* Transition: '<S32>:14' */
      TLE9278BQX_Mgm_DW.oldWkCtrl0 = TLE9278BQX_Mgm_DW.Memory26_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)(((uint32_T)((int32_T)
        ((((int32_T)SBCVBSCFG) & ((int32_T)0x0003)) * 4096))) | (((uint32_T)
        SBC_NORMAL_WRITE[(((uint8_T)WK_CTRL_0_NORM_W_ID))]) & ((uint32_T)0xCFFF)));
    } else {
      /* Transition: '<S32>:13' */
    }

    if (TLE9278BQX_Mgm_DW.Memory25_PreviousInput != TLE9278BQX_Mgm_DW.oldHwCtrl1)
    {
      /* Transition: '<S32>:25' */
      TLE9278BQX_Mgm_DW.oldHwCtrl1 = TLE9278BQX_Mgm_DW.Memory25_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)((((uint32_T)((int32_T)
        ((((int32_T)SBC_NORMAL_WRITE[(((uint8_T)HW_CTRL_1_NORM_W_ID))]) &
          ((int32_T)0x7FFF)) | ((int32_T)((uint32_T)(((uint32_T)AdcSel_k) <<
        ((uint32_T)15))))))) & ((uint32_T)0xF3FF)) | ((uint32_T)((int32_T)
        ((((int32_T)SBCBOOSTV) & ((int32_T)0x0003)) * 1024))));
    } else {
      /* Transition: '<S32>:26' */
    }

    if (TLE9278BQX_Mgm_DW.Memory27_PreviousInput != TLE9278BQX_Mgm_DW.oldSysCtrl)
    {
      /* Transition: '<S32>:33' */
      TLE9278BQX_Mgm_DW.oldSysCtrl = TLE9278BQX_Mgm_DW.Memory27_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)((SBC_NORMAL_WRITE
        [(((uint8_T)SYS_STAT_NORM_W_ID))] & ((uint16_T)0x00FF)) |
        (SetSBCSysStat_b << ((uint32_T)8)));
    } else {
      /* Transition: '<S32>:31' */
    }

    /* Transition: '<S32>:39' */
    if (TLE9278BQX_Mgm_DW.Memory28_PreviousInput != TLE9278BQX_Mgm_DW.oldWkCtrl1)
    {
      /* Transition: '<S32>:41' */
      TLE9278BQX_Mgm_DW.oldWkCtrl1 = TLE9278BQX_Mgm_DW.Memory28_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)(((uint32_T)((int32_T)
        ((((int32_T)SBCWKEN) & ((int32_T)0x0001)) * 256))) | (((uint32_T)
        SBC_NORMAL_WRITE[(((uint8_T)WK_CTRL_1_NORM_W_ID))]) & ((uint32_T)0xFEFF)));
    } else {
      /* Transition: '<S32>:42' */
    }

    if (TLE9278BQX_Mgm_DW.Memory29_PreviousInput != TLE9278BQX_Mgm_DW.oldWkPull)
    {
      /* Transition: '<S32>:46' */
      TLE9278BQX_Mgm_DW.oldWkPull = TLE9278BQX_Mgm_DW.Memory29_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)(((uint32_T)((int32_T)
        ((((int32_T)SBCWKPUPD) & ((int32_T)0x0003)) * 256))) | (((uint32_T)
        SBC_NORMAL_WRITE[(((uint8_T)WK_PD_CTRL_NORM_W_ID))]) & ((uint32_T)0xFCFF)));
    } else {
      /* Transition: '<S32>:47' */
    }

    if (TLE9278BQX_Mgm_DW.Memory30_PreviousInput !=
        TLE9278BQX_Mgm_DW.oldBusCtrl0) {
      /* Transition: '<S32>:52' */
      TLE9278BQX_Mgm_DW.oldBusCtrl0 = TLE9278BQX_Mgm_DW.Memory30_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)(((uint32_T)((int32_T)
        ((((int32_T)SBCCAN00EN) & ((int32_T)0x0003)) * 256))) | (((uint32_T)
        SBC_NORMAL_WRITE[(((uint8_T)BUS_CTRL_0_NORM_W_ID))]) & ((uint32_T)0xFCFF)));
    } else {
      /* Transition: '<S32>:53' */
    }

    if (TLE9278BQX_Mgm_DW.Memory31_PreviousInput !=
        TLE9278BQX_Mgm_DW.oldBusCtrl2) {
      /* Transition: '<S32>:56' */
      TLE9278BQX_Mgm_DW.oldBusCtrl2 = TLE9278BQX_Mgm_DW.Memory31_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)(((((uint32_T)((int32_T)
        ((((int32_T)SBCCAN02EN) & ((int32_T)0x0003)) * 2048))) | (((uint32_T)
        SBC_NORMAL_WRITE[(((uint8_T)BUS_CTRL_2_NORM_W_ID))]) & ((uint32_T)0xE7FF)))
        & ((uint32_T)0xFCFF)) | ((uint32_T)((int32_T)((((int32_T)SetSBCCan1) &
        ((int32_T)0x0003)) * 256))));
    } else {
      /* Transition: '<S32>:55' */
    }

    if (TLE9278BQX_Mgm_DW.Memory32_PreviousInput !=
        TLE9278BQX_Mgm_DW.oldBusCtrl3) {
      /* Transition: '<S32>:82' */
      TLE9278BQX_Mgm_DW.oldBusCtrl3 = TLE9278BQX_Mgm_DW.Memory32_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = (uint16_T)(((uint32_T)((int32_T)
        ((((int32_T)SBCCAN03EN) & ((int32_T)0x0003)) * 256))) | (((uint32_T)
        SBC_NORMAL_WRITE[(((uint8_T)BUS_CTRL_3_NORM_W_ID))]) & ((uint32_T)0xFCFF)));
    } else {
      /* Transition: '<S32>:83' */
    }

    /* Transition: '<S32>:10' */
    if (TLE9278BQX_Mgm_DW.Memory33_PreviousInput !=
        TLE9278BQX_Mgm_DW.oldTimerCtrl0) {
      /* Transition: '<S32>:89' */
      TLE9278BQX_Mgm_DW.oldTimerCtrl0 = TLE9278BQX_Mgm_DW.Memory33_PreviousInput;
      tmp3 = (uint16_T)((int32_T)(((int32_T)tmp3) + 1));
      TLE9278BQX_Mgm_DW.buffOut_k[tmp3] = SBC_NORMAL_WRITE[(((uint8_T)
        TIMER_CTRL_NORM_W_ID))];
    } else {
      /* Transition: '<S32>:92' */
    }

    /* Transition: '<S32>:91' */
    TLE9278BQX_Mgm_DW.buffOut_k[0] = tmp3;

    /* End of Chart: '<S17>/Chart' */

    /* SignalConversion generated from: '<S17>/data' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = TLE9278BQX_Mgm_DW.buffOut_k[i_0];
    }

    /* End of SignalConversion generated from: '<S17>/data' */

    /* SignalConversion generated from: '<S17>/AdcSel' */
    AdcSel = AdcSel_k;

    /* End of Outputs for SubSystem: '<S12>/SBC_NORMAL_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_NORMAL_READ_ID)) {
    /* Outputs for Function Call SubSystem: '<S12>/SBC_NORMAL_READ' */
    /* Chart: '<S16>/Select_messages' incorporates:
     *  Constant: '<S16>/SBC_NORMAL_READ'
     *  Inport: '<Root>/SBCStatusReg'
     *
     * Block description for '<Root>/SBCStatusReg':
     *  Status diagnosis register
     */
    /* Decision 'D4': '<S11>:1:91' */
    /*  fc_SBC_NORMAL_READ_ID */
    /* Action '4': '<S11>:1:141' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_NORMAL_READ/Select_messages */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_NORMAL_READ/Select_messages */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_NORMAL_READ/Select_messages */
    /* Transition: '<S31>:43' */
    i = 1U;
    tmp3 = (uint16_T)0xFFFF;
    size = 0U;
    flgView = 0U;
    rbReg = 0U;
    clReg = 0U;
    while (((uint16_T)i) <= SBC_NORMAL_READ[0]) {
      /* Transition: '<S31>:46' */
      if (i == ((uint8_T)SUP_STAT_1_NORM_R_ID)) {
        /* Transition: '<S31>:49' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)SBCStatusReg) & 1U) != 0U) {
          /* Transition: '<S31>:56' */
          /* Transition: '<S31>:218' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:55' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:217' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:215' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:222' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)SUP_STAT_1_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:57' */
      } else {
        /* Transition: '<S31>:48' */
      }

      if (i == ((uint8_T)SUP_STAT_0_NORM_R_ID)) {
        /* Transition: '<S31>:60' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)SBCStatusReg) & 1U) != 0U) {
          /* Transition: '<S31>:234' */
          /* Transition: '<S31>:238' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:58' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:241' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:236' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:239' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)SUP_STAT_0_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:64' */
      } else {
        /* Transition: '<S31>:59' */
      }

      if (i == ((uint8_T)THERM_STAT_NORM_R_ID)) {
        /* Transition: '<S31>:68' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)1))) & 1U) != 0U)
        {
          /* Transition: '<S31>:250' */
          /* Transition: '<S31>:245' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:249' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:251' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:243' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:246' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)THERM_STAT_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:70' */
      } else {
        /* Transition: '<S31>:74' */
      }

      if (i == ((uint8_T)DEV_STAT_NORM_R_ID)) {
        /* Transition: '<S31>:78' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)2))) & 1U) != 0U)
        {
          /* Transition: '<S31>:259' */
          /* Transition: '<S31>:263' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:262' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:255' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:257' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:258' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)DEV_STAT_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:79' */
      } else {
        /* Transition: '<S31>:82' */
      }

      if (i == ((uint8_T)BUS_STAT_0_NORM_R_ID)) {
        /* Transition: '<S31>:84' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)3))) & 1U) != 0U)
        {
          /* Transition: '<S31>:271' */
          /* Transition: '<S31>:267' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:274' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:269' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:264' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:265' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)BUS_STAT_0_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:86' */
      } else {
        /* Transition: '<S31>:90' */
      }

      if (i == ((uint8_T)BUS_STAT_2_NORM_R_ID)) {
        /* Transition: '<S31>:94' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)4))) & 1U) != 0U)
        {
          /* Transition: '<S31>:282' */
          /* Transition: '<S31>:283' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:278' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:277' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:279' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:280' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)BUS_STAT_2_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:95' */
      } else {
        /* Transition: '<S31>:98' */
      }

      if (i == ((uint8_T)BUS_STAT_3_NORM_R_ID)) {
        /* Transition: '<S31>:99' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)4))) & 1U) != 0U)
        {
          /* Transition: '<S31>:290' */
          /* Transition: '<S31>:292' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:287' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:286' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:295' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:296' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)BUS_STAT_3_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:101' */
      } else {
        /* Transition: '<S31>:106' */
      }

      if (i == ((uint8_T)WK_STAT_0_NORM_R_ID)) {
        /* Transition: '<S31>:115' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)5))) & 1U) != 0U)
        {
          /* Transition: '<S31>:306' */
          /* Transition: '<S31>:298' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:303' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:301' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:299' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:307' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)WK_STAT_0_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:111' */
      } else {
        /* Transition: '<S31>:118' */
      }

      if (i == ((uint8_T)WK_STAT_2_NORM_R_ID)) {
        /* Transition: '<S31>:122' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)6))) & 1U) != 0U)
        {
          /* Transition: '<S31>:309' */
          /* Transition: '<S31>:313' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:310' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:315' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:316' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:311' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)WK_STAT_2_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:121' */
      } else {
        /* Transition: '<S31>:126' */
      }

      if (i == ((uint8_T)SMPS_STAT_NORM_R_ID)) {
        /* Transition: '<S31>:130' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)7))) & 1U) != 0U)
        {
          /* Transition: '<S31>:320' */
          /* Transition: '<S31>:319' */
          guard1 = true;
        } else {
          /* Transition: '<S31>:325' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl_g < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S31>:328' */
            TLE9278BQX_Mgm_DW.oldMsgCl_g = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S31>:322' */
          }
        }

        if (guard1) {
          /* Transition: '<S31>:329' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = (uint16_T)(SBC_NORMAL_READ
            [(((uint8_T)SMPS_STAT_NORM_R_ID))] & tmp3);
        }

        /* Transition: '<S31>:132' */
      } else {
        /* Transition: '<S31>:134' */
      }

      if (i == ((uint8_T)WK_LVL_STAT_NORM_R_ID)) {
        /* Transition: '<S31>:171' */
        flgView = 1U;

        /* Transition: '<S31>:170' */
        size = (uint8_T)((int32_T)(((int32_T)size) + 1));
        TLE9278BQX_Mgm_DW.buffOut_n[size] = SBC_NORMAL_READ[(i)];

        /* Transition: '<S31>:174' */
      } else {
        /* Transition: '<S31>:177' */
      }

      if (i == ((uint8_T)ADC_STAT_NORM_R_ID)) {
        /* Transition: '<S31>:204' */
        flgView = 1U;

        /* Transition: '<S31>:206' */
        size = (uint8_T)((int32_T)(((int32_T)size) + 1));
        TLE9278BQX_Mgm_DW.buffOut_n[size] = SBC_NORMAL_READ[(i)];
      } else {
        /* Transition: '<S31>:209' */
        /* Transition: '<S31>:210' */
      }

      /* Transition: '<S31>:211' */
      if (((int32_T)flgView) == 0) {
        /* Transition: '<S31>:179' */
        if ((TLE9278BQX_Mgm_DW.oldMsgI_o < i) && (((int32_T)rbReg) == 0)) {
          /* Transition: '<S31>:194' */
          TLE9278BQX_Mgm_DW.oldMsgI_o = i;
          rbReg = 1U;
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_n[size] = SBC_NORMAL_READ[(i)];
        } else {
          /* Transition: '<S31>:193' */
        }

        /* Transition: '<S31>:197' */
      } else {
        /* Transition: '<S31>:141' */
      }

      /* Transition: '<S31>:137' */
      i = (uint8_T)((int32_T)(((int32_T)i) + 1));
      flgView = 0U;
      tmp3 = (uint16_T)0xFFFF;
    }

    /* Transition: '<S31>:139' */
    TLE9278BQX_Mgm_DW.buffOut_n[0] = (uint16_T)size;
    if (((int32_T)rbReg) == 0) {
      /* Transition: '<S31>:201' */
      TLE9278BQX_Mgm_DW.oldMsgI_o = 0U;
    } else {
      /* Transition: '<S31>:200' */
    }

    if (((int32_T)clReg) == 0) {
      /* Transition: '<S31>:224' */
      TLE9278BQX_Mgm_DW.oldMsgCl_g = 0U;
    } else {
      /* Transition: '<S31>:225' */
    }

    /* End of Chart: '<S16>/Select_messages' */

    /* SignalConversion generated from: '<S16>/data' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = TLE9278BQX_Mgm_DW.buffOut_n[i_0];
    }

    /* End of SignalConversion generated from: '<S16>/data' */
    /* End of Outputs for SubSystem: '<S12>/SBC_NORMAL_READ' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_TO_NORMAL_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S12>/SBC_TO_NORMAL_WRITE' */
    /* Decision 'D5': '<S11>:1:93' */
    /*  fc_SBC_TO_NORMAL_WRITE_ID */
    /* Action '5': '<S11>:1:147' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      /* Assignment: '<S18>/Assignment3' incorporates:
       *  Constant: '<S18>/SBC_TO_NORMAL_WRITE'
       *  SignalConversion generated from: '<S18>/Assignment5'
       */
      SBCDataTxBuffer[(i_0)] = SBC_TO_NORMAL_WRITE[(i_0)];
    }

    /* Assignment: '<S18>/Assignment5' incorporates:
     *  Assignment: '<S18>/Assignment3'
     *  Chart: '<S18>/Calc_output5'
     *  Constant: '<S18>/BUS_CTRL_0_TO_N_W_ID'
     *  Constant: '<S18>/SBC_TO_NORMAL_WRITE'
     *  Constant: '<S7>/SBCCAN00EN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output5 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output5 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output5 */
    /* Transition: '<S35>:2' */
    /* Transition: '<S35>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_0_TO_N_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCCAN00EN) & ((int32_T)0x0003)) * 256))) |
      (((uint32_T)SBC_TO_NORMAL_WRITE[(((uint8_T)BUS_CTRL_0_TO_N_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* Assignment: '<S18>/Assignment6' incorporates:
     *  Assignment: '<S18>/Assignment3'
     *  Chart: '<S18>/Calc_output6'
     *  Constant: '<S18>/BUS_CTRL_2_TO_N_W_ID'
     *  Constant: '<S18>/SBC_TO_NORMAL_WRITE'
     *  Constant: '<S7>/SBCCAN02EN'
     *  Inport: '<Root>/SetSBCCan1'
     *
     * Block description for '<Root>/SetSBCCan1':
     *  Set CAN 1 Configuration
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output6 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output6 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output6 */
    /* Transition: '<S36>:2' */
    /* Transition: '<S36>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_2_TO_N_W_ID))] = (uint16_T)(((((uint32_T)
      ((int32_T)((((int32_T)SBCCAN02EN) & ((int32_T)0x0003)) * 2048))) |
      (((uint32_T)SBC_TO_NORMAL_WRITE[(((uint8_T)BUS_CTRL_2_TO_N_W_ID))]) &
       ((uint32_T)0xE7FF))) & ((uint32_T)0xFCFF)) | ((uint32_T)((int32_T)
      ((((int32_T)SetSBCCan1) & ((int32_T)0x0003)) * 256))));

    /* Chart: '<S18>/Calc_even_parity' incorporates:
     *  Constant: '<S18>/SBC_TO_NORMAL_WRITE'
     *  Constant: '<S18>/WD_CTRL_TO_N_W_ID'
     *  Inport: '<Root>/WDSBCPeriod'
     *  Inport: '<Root>/WDSBCWin'
     *
     * Block description for '<Root>/WDSBCPeriod':
     *  Select WDT Period
     *
     * Block description for '<Root>/WDSBCWin':
     *  Enable WDT windowed
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_even_parity */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_even_parity */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_even_parity */
    /* Transition: '<S33>:2' */
    /* Transition: '<S33>:4' */
    i = 0U;
    tmp3 = 0U;
    tmp = (uint16_T)(((((uint32_T)((int32_T)((((int32_T)WDSBCPeriod) & ((int32_T)
      0x0007)) * 256))) | (((uint32_T)SBC_TO_NORMAL_WRITE[(((uint8_T)
      WD_CTRL_TO_N_W_ID))]) & ((uint32_T)0xF8FF))) & ((uint32_T)0xDFFF)) |
                     ((uint32_T)((int32_T)((((int32_T)WDSBCWin) & ((int32_T)
      0x0001)) * 8192))));
    while (((int32_T)i) < 7) {
      /* Transition: '<S33>:7' */
      tmp3 = (uint16_T)(((tmp >> ((uint32_T)((int32_T)(((int32_T)i) + 8)))) &
                         ((uint16_T)0x01)) + tmp3);
      i = (uint8_T)((int32_T)(((int32_T)i) + 1));
    }

    /* Transition: '<S33>:6' */
    if ((((uint32_T)tmp3) & 1U) != 0U) {
      /* Assignment: '<S18>/Assignment1' incorporates:
       *  Assignment: '<S18>/Assignment3'
       */
      /* Transition: '<S33>:11' */
      SBCDataTxBuffer[(((uint8_T)WD_CTRL_TO_N_W_ID))] = (uint16_T)(((uint32_T)
        tmp) | ((uint32_T)0x8000));

      /*  set even parity */
    } else {
      /* Assignment: '<S18>/Assignment1' incorporates:
       *  Assignment: '<S18>/Assignment3'
       */
      /* Transition: '<S33>:12' */
      SBCDataTxBuffer[(((uint8_T)WD_CTRL_TO_N_W_ID))] = tmp;

      /*  set even parity */
    }

    /* End of Chart: '<S18>/Calc_even_parity' */

    /* Assignment: '<S18>/Assignment2' incorporates:
     *  Assignment: '<S18>/Assignment3'
     *  Chart: '<S18>/Calc_output1'
     *  Constant: '<S18>/BUS_CTRL_3_TO_N_W_ID'
     *  Constant: '<S18>/SBC_TO_NORMAL_WRITE'
     *  Constant: '<S7>/SBCCAN03EN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output1 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output1 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output1 */
    /* Transition: '<S34>:2' */
    /* Transition: '<S34>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_3_TO_N_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCCAN03EN) & ((int32_T)0x0003)) * 256))) |
      (((uint32_T)SBC_TO_NORMAL_WRITE[(((uint8_T)BUS_CTRL_3_TO_N_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* Assignment: '<S18>/Assignment3' incorporates:
     *  Chart: '<S18>/Calc_output8'
     *  Constant: '<S18>/M_S_CTRL_TO_N_W_ID'
     *  Constant: '<S18>/SBC_TO_NORMAL_WRITE'
     *  Constant: '<S7>/SBCVIORT'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output8 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output8 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_NORMAL_WRITE/Calc_output8 */
    /* Transition: '<S37>:2' */
    /* Transition: '<S37>:21' */
    SBCDataTxBuffer[(((uint8_T)M_S_CTRL_TO_N_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCVIORT) & ((int32_T)0x0003)) * 256))) |
      (((uint32_T)SBC_TO_NORMAL_WRITE[(((uint8_T)M_S_CTRL_TO_N_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* End of Outputs for SubSystem: '<S12>/SBC_TO_NORMAL_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_TO_STOP_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S12>/SBC_TO_STOP_WRITE' */
    /* Decision 'D6': '<S11>:1:95' */
    /*  fc_SBC_TO_STOP_WRITE_ID */
    /* Action '6': '<S11>:1:153' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      /* Assignment: '<S20>/Assignment2' incorporates:
       *  Constant: '<S20>/SBC_TO_STOP_WRITE'
       *  SignalConversion generated from: '<S20>/Assignment'
       */
      SBCDataTxBuffer[(i_0)] = SBC_TO_STOP_WRITE[(i_0)];
    }

    /* Assignment: '<S20>/Assignment' incorporates:
     *  Assignment: '<S20>/Assignment2'
     *  Chart: '<S20>/Calc_output'
     *  Constant: '<S20>/SBC_TO_STOP_WRITE'
     *  Constant: '<S20>/WK_CTRL_0_TO_STOP_W_ID'
     *  Constant: '<S7>/SBCVBSCFG'
     *  Inport: '<Root>/FlgSBCWDStmDis'
     *
     * Block description for '<Root>/FlgSBCWDStmDis':
     *  Flag to Disable WDT in Stop_Mode
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output */
    /* Transition: '<S44>:2' */
    /* Transition: '<S44>:21' */
    SBCDataTxBuffer[(((uint8_T)WK_CTRL_0_TO_STOP_W_ID))] = (uint16_T)
      (((((uint32_T)((int32_T)((((int32_T)SBCVBSCFG) & ((int32_T)0x0003)) * 4096)))
         | (((uint32_T)SBC_TO_STOP_WRITE[(((uint8_T)WK_CTRL_0_TO_STOP_W_ID))]) &
            ((uint32_T)0xCFFF))) & ((uint32_T)0xFBFF)) | ((uint32_T)(((uint32_T)
          FlgSBCWDStmDis) << ((uint32_T)10))));

    /* Chart: '<S20>/Calc_even_parity' incorporates:
     *  Constant: '<S20>/SBC_TO_STOP_WRITE'
     *  Constant: '<S20>/WD_CTRL_TO_STOP_W_ID'
     *  Inport: '<Root>/FlgSBCWDStmDis'
     *  Inport: '<Root>/WDSBCPeriod'
     *  Inport: '<Root>/WDSBCWin'
     *
     * Block description for '<Root>/FlgSBCWDStmDis':
     *  Flag to Disable WDT in Stop_Mode
     *
     * Block description for '<Root>/WDSBCPeriod':
     *  Select WDT Period
     *
     * Block description for '<Root>/WDSBCWin':
     *  Enable WDT windowed
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_even_parity */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_even_parity */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_even_parity */
    /* Transition: '<S43>:2' */
    /* Transition: '<S43>:4' */
    i = 0U;
    tmp3 = 0U;
    tmp = (uint16_T)(((((((uint32_T)((int32_T)((((int32_T)WDSBCPeriod) &
      ((int32_T)0x0007)) * 256))) | (((uint32_T)SBC_TO_STOP_WRITE[(((uint8_T)
      WD_CTRL_TO_STOP_W_ID))]) & ((uint32_T)0xF8FF))) & ((uint32_T)0xDFFF)) |
                       ((uint32_T)((int32_T)((((int32_T)WDSBCWin) & ((int32_T)
      0x0001)) * 8192)))) & ((uint32_T)0xBFFF)) | ((uint32_T)(((uint32_T)
      FlgSBCWDStmDis) << ((uint32_T)14))));
    while (((int32_T)i) < 7) {
      /* Transition: '<S43>:7' */
      tmp3 = (uint16_T)(((tmp >> ((uint32_T)((int32_T)(((int32_T)i) + 8)))) &
                         ((uint16_T)0x01)) + tmp3);
      i = (uint8_T)((int32_T)(((int32_T)i) + 1));
    }

    /* Transition: '<S43>:6' */
    if ((((uint32_T)tmp3) & 1U) != 0U) {
      /* Assignment: '<S20>/Assignment1' incorporates:
       *  Assignment: '<S20>/Assignment2'
       */
      /* Transition: '<S43>:11' */
      SBCDataTxBuffer[(((uint8_T)WD_CTRL_TO_STOP_W_ID))] = (uint16_T)(((uint32_T)
        tmp) | ((uint32_T)0x8000));

      /*  set even parity */
    } else {
      /* Assignment: '<S20>/Assignment1' incorporates:
       *  Assignment: '<S20>/Assignment2'
       */
      /* Transition: '<S43>:12' */
      SBCDataTxBuffer[(((uint8_T)WD_CTRL_TO_STOP_W_ID))] = tmp;

      /*  set even parity */
    }

    /* End of Chart: '<S20>/Calc_even_parity' */

    /* Assignment: '<S20>/Assignment5' incorporates:
     *  Assignment: '<S20>/Assignment2'
     *  Chart: '<S20>/Calc_output5'
     *  Constant: '<S20>/BUS_CTRL_0_TO_STOP_W_ID'
     *  Constant: '<S20>/SBC_TO_STOP_WRITE'
     *  Constant: '<S7>/SBCCAN00EN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output5 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output5 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output5 */
    /* Transition: '<S45>:2' */
    /* Transition: '<S45>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_0_TO_STOP_W_ID))] = (uint16_T)
      (((uint32_T)((int32_T)((((int32_T)SBCCAN00EN) & ((int32_T)0x0030)) * 16)))
       | (((uint32_T)SBC_TO_STOP_WRITE[(((uint8_T)BUS_CTRL_0_TO_STOP_W_ID))]) &
          ((uint32_T)0xFCFF)));

    /* Assignment: '<S20>/Assignment6' incorporates:
     *  Assignment: '<S20>/Assignment2'
     *  Chart: '<S20>/Calc_output6'
     *  Constant: '<S20>/BUS_CTRL_2_TO_STOP_W_ID'
     *  Constant: '<S20>/SBC_TO_STOP_WRITE'
     *  Constant: '<S7>/SBCCAN02EN'
     *  Inport: '<Root>/SetSBCCan1'
     *
     * Block description for '<Root>/SetSBCCan1':
     *  Set CAN 1 Configuration
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output6 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output6 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output6 */
    /* Transition: '<S46>:2' */
    /* Transition: '<S46>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_2_TO_STOP_W_ID))] = (uint16_T)
      (((((uint32_T)((int32_T)((((int32_T)SBCCAN02EN) & ((int32_T)0x0030)) * 128)))
         | (((uint32_T)SBC_TO_STOP_WRITE[(((uint8_T)BUS_CTRL_2_TO_STOP_W_ID))])
            & ((uint32_T)0xE7FF))) & ((uint32_T)0xFCFF)) | ((uint32_T)((int32_T)
         ((((int32_T)SetSBCCan1) & ((int32_T)0x0030)) * 16))));

    /* Assignment: '<S20>/Assignment2' incorporates:
     *  Chart: '<S20>/Calc_output7'
     *  Constant: '<S20>/BUS_CTRL_3_TO_STOP_W_ID'
     *  Constant: '<S20>/SBC_TO_STOP_WRITE'
     *  Constant: '<S7>/SBCCAN03EN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output7 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output7 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_STOP_WRITE/Calc_output7 */
    /* Transition: '<S47>:2' */
    /* Transition: '<S47>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_3_TO_STOP_W_ID))] = (uint16_T)
      (((uint32_T)((int32_T)((((int32_T)SBCCAN03EN) & ((int32_T)0x0030)) * 16)))
       | (((uint32_T)SBC_TO_STOP_WRITE[(((uint8_T)BUS_CTRL_3_TO_STOP_W_ID))]) &
          ((uint32_T)0xFCFF)));

    /* End of Outputs for SubSystem: '<S12>/SBC_TO_STOP_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_TO_SLEEP_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S12>/SBC_TO_SLEEP_WRITE' */
    /* Decision 'D7': '<S11>:1:97' */
    /*  fc_SBC_TO_SLEEP_WRITE_ID */
    /* Action '7': '<S11>:1:159' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      /* Assignment: '<S19>/Assignment3' incorporates:
       *  Constant: '<S19>/SBC_TO_SLEEP_WRITE'
       *  SignalConversion generated from: '<S19>/Assignment5'
       */
      SBCDataTxBuffer[(i_0)] = SBC_TO_SLEEP_WRITE[(i_0)];
    }

    /* Assignment: '<S19>/Assignment5' incorporates:
     *  Assignment: '<S19>/Assignment3'
     *  Chart: '<S19>/Calc_output5'
     *  Constant: '<S19>/BUS_CTRL_0_SLEEP_W_ID'
     *  Constant: '<S19>/SBC_TO_SLEEP_WRITE'
     *  Constant: '<S7>/SBCCAN00EN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output5 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output5 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output5 */
    /* Transition: '<S39>:2' */
    /* Transition: '<S39>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_0_SLEEP_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCCAN00EN) & ((int32_T)0x0030)) * 16))) |
      (((uint32_T)SBC_TO_SLEEP_WRITE[(((uint8_T)BUS_CTRL_0_SLEEP_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* Assignment: '<S19>/Assignment6' incorporates:
     *  Assignment: '<S19>/Assignment3'
     *  Chart: '<S19>/Calc_output6'
     *  Constant: '<S19>/BUS_CTRL_2_SLEEP_W_ID'
     *  Constant: '<S19>/SBC_TO_SLEEP_WRITE'
     *  Constant: '<S7>/SBCCAN02EN'
     *  Inport: '<Root>/SetSBCCan1'
     *
     * Block description for '<Root>/SetSBCCan1':
     *  Set CAN 1 Configuration
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output6 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output6 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output6 */
    /* Transition: '<S40>:2' */
    /* Transition: '<S40>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_2_SLEEP_W_ID))] = (uint16_T)
      (((((uint32_T)((int32_T)((((int32_T)SBCCAN02EN) & ((int32_T)0x0030)) * 128)))
         | (((uint32_T)SBC_TO_SLEEP_WRITE[(((uint8_T)BUS_CTRL_2_SLEEP_W_ID))]) &
            ((uint32_T)0xE7FF))) & ((uint32_T)0xFCFF)) | ((uint32_T)((int32_T)
         ((((int32_T)SetSBCCan1) & ((int32_T)0x0030)) * 16))));

    /* Assignment: '<S19>/Assignment1' incorporates:
     *  Assignment: '<S19>/Assignment3'
     *  Chart: '<S19>/Calc_output1'
     *  Constant: '<S19>/BUS_CTRL_3_SLEEP_W_ID'
     *  Constant: '<S19>/SBC_TO_SLEEP_WRITE'
     *  Constant: '<S7>/SBCCAN03EN'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output1 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output1 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output1 */
    /* Transition: '<S38>:2' */
    /* Transition: '<S38>:21' */
    SBCDataTxBuffer[(((uint8_T)BUS_CTRL_3_SLEEP_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCCAN03EN) & ((int32_T)0x0030)) * 16))) |
      (((uint32_T)SBC_TO_SLEEP_WRITE[(((uint8_T)BUS_CTRL_3_SLEEP_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* Assignment: '<S19>/Assignment2' incorporates:
     *  Assignment: '<S19>/Assignment3'
     *  Chart: '<S19>/Calc_output9'
     *  Constant: '<S19>/SBC_TO_SLEEP_WRITE'
     *  Constant: '<S19>/SYS_STAT_CTRL_SLEEP_W_ID'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output9 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output9 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output9 */
    /* Transition: '<S42>:2' */
    /* Transition: '<S42>:21' */
    SBCDataTxBuffer[(((uint8_T)SYS_STAT_CTRL_SLEEP_W_ID))] = (uint16_T)
      ((SBC_TO_SLEEP_WRITE[(((uint8_T)SYS_STAT_CTRL_SLEEP_W_ID))] & ((uint16_T)
         0x00FF)) | (SetSBCSysStat_b << ((uint32_T)8)));

    /* Assignment: '<S19>/Assignment3' incorporates:
     *  Chart: '<S19>/Calc_output8'
     *  Constant: '<S19>/M_S_CTRL_SLEEP_W_ID'
     *  Constant: '<S19>/SBC_TO_SLEEP_WRITE'
     *  Constant: '<S7>/SBCVIORT'
     */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output8 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output8 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_1/SBC_TO_SLEEP_WRITE/Calc_output8 */
    /* Transition: '<S41>:2' */
    /* Transition: '<S41>:21' */
    SBCDataTxBuffer[(((uint8_T)M_S_CTRL_SLEEP_W_ID))] = (uint16_T)(((uint32_T)
      ((int32_T)((((int32_T)SBCVIORT) & ((int32_T)0x0003)) * 256))) |
      (((uint32_T)SBC_TO_SLEEP_WRITE[(((uint8_T)M_S_CTRL_SLEEP_W_ID))]) &
       ((uint32_T)0xFCFF)));

    /* End of Outputs for SubSystem: '<S12>/SBC_TO_SLEEP_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_TO_RESET_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S13>/SBC_TO_RESET_WRITE' */
    /* Assignment: '<S55>/Assignment2' incorporates:
     *  Chart: '<S55>/Calc_output9'
     *  Constant: '<S55>/SBC_TO_RESET_WRITE'
     *  Constant: '<S55>/SYS_STAT_CTRL_RESET_W_ID'
     */
    /* Decision 'D8': '<S11>:1:99' */
    /*  fc_SBC_TO_RESET_WRITE_ID */
    /* Action '8': '<S11>:1:165' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_TO_RESET_WRITE/Calc_output9 */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_TO_RESET_WRITE/Calc_output9 */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_TO_RESET_WRITE/Calc_output9 */
    /* Transition: '<S62>:2' */
    /* Transition: '<S62>:21' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = SBC_TO_RESET_WRITE[(i_0)];
    }

    SBCDataTxBuffer[(((uint8_T)SYS_STAT_CTRL_RESET_W_ID))] = (uint16_T)
      ((SBC_TO_RESET_WRITE[(((uint8_T)SYS_STAT_CTRL_RESET_W_ID))] & ((uint16_T)
         0x00FF)) | (SetSBCSysStat_b << ((uint32_T)8)));

    /* End of Assignment: '<S55>/Assignment2' */
    /* End of Outputs for SubSystem: '<S13>/SBC_TO_RESET_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_STOP_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S13>/SBC_STOP_WRITE' */
    /* Assignment: '<S54>/Assignment' incorporates:
     *  Chart: '<S54>/Calc_output'
     *  Constant: '<S54>/HW_CTRL_0_STOP_W_ID'
     *  Constant: '<S54>/SBC_STOP_WRITE'
     *  Constant: '<S7>/SBCBOOSTEN'
     *  Inport: '<Root>/FlgSBCFOEn'
     *
     * Block description for '<Root>/FlgSBCFOEn':
     *  Forced by Software the FO Pin
     */
    /* Decision 'D9': '<S11>:1:101' */
    /*  fc_SBC_STOP_WRITE_ID */
    /* Action '9': '<S11>:1:171' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_STOP_WRITE/Calc_output */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_STOP_WRITE/Calc_output */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_STOP_WRITE/Calc_output */
    /* Transition: '<S61>:2' */
    /* Transition: '<S61>:21' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = SBC_STOP_WRITE[(i_0)];
    }

    SBCDataTxBuffer[(((uint8_T)HW_CTRL_0_STOP_W_ID))] = (uint16_T)((((((uint32_T)
      SBC_STOP_WRITE[(((uint8_T)HW_CTRL_0_STOP_W_ID))]) & ((uint32_T)0xDFFF)) |
      ((uint32_T)(((uint32_T)FlgSBCFOEn) << ((uint32_T)13)))) & ((uint32_T)
      0xFDFF)) | ((uint32_T)((int32_T)((((int32_T)SBCBOOSTEN) & ((int32_T)0x0001))
      * 512))));

    /* End of Assignment: '<S54>/Assignment' */
    /* End of Outputs for SubSystem: '<S13>/SBC_STOP_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_STOP_READ_ID)) {
    /* Outputs for Function Call SubSystem: '<S13>/SBC_STOP_READ' */
    /* Chart: '<S53>/Select_messages' incorporates:
     *  Constant: '<S53>/SBC_STOP_READ'
     *  Inport: '<Root>/SBCStatusReg'
     *
     * Block description for '<Root>/SBCStatusReg':
     *  Status diagnosis register
     */
    /* Decision 'D10': '<S11>:1:103' */
    /*  fc_SBC_STOP_READ_ID */
    /* Action '10': '<S11>:1:177' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_STOP_READ/Select_messages */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_STOP_READ/Select_messages */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_STOP_READ/Select_messages */
    /* Transition: '<S60>:43' */
    i = 1U;
    tmp3 = (uint16_T)0xFFFF;
    size = 0U;
    flgView = 0U;
    rbReg = 0U;
    clReg = 0U;
    while (((uint16_T)i) <= SBC_STOP_READ[0]) {
      /* Transition: '<S60>:46' */
      if (i == ((uint8_T)SUP_STAT_1_STOP_R_ID)) {
        /* Transition: '<S60>:49' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)SBCStatusReg) & 1U) != 0U) {
          /* Transition: '<S60>:56' */
          /* Transition: '<S60>:218' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:55' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:217' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:215' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:222' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)SUP_STAT_1_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:57' */
      } else {
        /* Transition: '<S60>:48' */
      }

      if (i == ((uint8_T)SUP_STAT_0_STOP_R_ID)) {
        /* Transition: '<S60>:60' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)SBCStatusReg) & 1U) != 0U) {
          /* Transition: '<S60>:234' */
          /* Transition: '<S60>:238' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:58' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:241' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:236' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:239' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)SUP_STAT_0_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:64' */
      } else {
        /* Transition: '<S60>:59' */
      }

      if (i == ((uint8_T)THERM_STAT_STOP_R_ID)) {
        /* Transition: '<S60>:68' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)1))) & 1U) != 0U)
        {
          /* Transition: '<S60>:250' */
          /* Transition: '<S60>:245' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:249' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:251' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:243' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:246' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)THERM_STAT_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:70' */
      } else {
        /* Transition: '<S60>:74' */
      }

      if (i == ((uint8_T)DEV_STAT_STOP_R_ID)) {
        /* Transition: '<S60>:78' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)2))) & 1U) != 0U)
        {
          /* Transition: '<S60>:259' */
          /* Transition: '<S60>:263' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:262' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:255' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:257' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:258' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)DEV_STAT_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:79' */
      } else {
        /* Transition: '<S60>:82' */
      }

      if (i == ((uint8_T)BUS_STAT_0_STOP_R_ID)) {
        /* Transition: '<S60>:84' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)3))) & 1U) != 0U)
        {
          /* Transition: '<S60>:271' */
          /* Transition: '<S60>:267' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:274' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:269' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:264' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:265' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)BUS_STAT_0_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:86' */
      } else {
        /* Transition: '<S60>:90' */
      }

      if (i == ((uint8_T)BUS_STAT_2_STOP_R_ID)) {
        /* Transition: '<S60>:94' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)4))) & 1U) != 0U)
        {
          /* Transition: '<S60>:282' */
          /* Transition: '<S60>:283' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:278' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:277' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:279' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:280' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)BUS_STAT_2_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:95' */
      } else {
        /* Transition: '<S60>:98' */
      }

      if (i == ((uint8_T)BUS_STAT_3_STOP_R_ID)) {
        /* Transition: '<S60>:99' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)4))) & 1U) != 0U)
        {
          /* Transition: '<S60>:290' */
          /* Transition: '<S60>:292' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:287' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:286' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:295' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:296' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)BUS_STAT_3_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:101' */
      } else {
        /* Transition: '<S60>:106' */
      }

      if (i == ((uint8_T)WK_STAT_0_STOP_R_ID)) {
        /* Transition: '<S60>:115' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)5))) & 1U) != 0U)
        {
          /* Transition: '<S60>:306' */
          /* Transition: '<S60>:298' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:303' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:301' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:299' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:307' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)WK_STAT_0_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:111' */
      } else {
        /* Transition: '<S60>:118' */
      }

      if (i == ((uint8_T)WK_STAT_2_STOP_R_ID)) {
        /* Transition: '<S60>:122' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)6))) & 1U) != 0U)
        {
          /* Transition: '<S60>:309' */
          /* Transition: '<S60>:313' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:310' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:315' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:316' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:311' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)WK_STAT_2_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:121' */
      } else {
        /* Transition: '<S60>:126' */
      }

      if (i == ((uint8_T)SMPS_STAT_STOP_R_ID)) {
        /* Transition: '<S60>:130' */
        flgView = 1U;
        guard1 = false;
        if ((((uint32_T)(((uint32_T)SBCStatusReg) >> ((uint32_T)7))) & 1U) != 0U)
        {
          /* Transition: '<S60>:320' */
          /* Transition: '<S60>:319' */
          guard1 = true;
        } else {
          /* Transition: '<S60>:325' */
          if ((TLE9278BQX_Mgm_DW.oldMsgCl < i) && (((int32_T)clReg) == 0)) {
            /* Transition: '<S60>:328' */
            TLE9278BQX_Mgm_DW.oldMsgCl = i;
            clReg = 1U;
            tmp3 = (uint16_T)0xFF7F;
            guard1 = true;
          } else {
            /* Transition: '<S60>:322' */
          }
        }

        if (guard1) {
          /* Transition: '<S60>:329' */
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = (uint16_T)(SBC_STOP_READ
            [(((uint8_T)SMPS_STAT_STOP_R_ID))] & tmp3);
        }

        /* Transition: '<S60>:132' */
      } else {
        /* Transition: '<S60>:134' */
      }

      if (i == ((uint8_T)WK_LVL_STAT_STOP_R_ID)) {
        /* Transition: '<S60>:171' */
        flgView = 1U;

        /* Transition: '<S60>:170' */
        size = (uint8_T)((int32_T)(((int32_T)size) + 1));
        TLE9278BQX_Mgm_DW.buffOut_o[size] = SBC_STOP_READ[(i)];

        /* Transition: '<S60>:174' */
      } else {
        /* Transition: '<S60>:177' */
      }

      if (i == ((uint8_T)ADC_STAT_STOP_R_ID)) {
        /* Transition: '<S60>:204' */
        flgView = 1U;

        /* Transition: '<S60>:206' */
        size = (uint8_T)((int32_T)(((int32_T)size) + 1));
        TLE9278BQX_Mgm_DW.buffOut_o[size] = SBC_STOP_READ[(i)];
      } else {
        /* Transition: '<S60>:209' */
        /* Transition: '<S60>:210' */
      }

      /* Transition: '<S60>:211' */
      if (((int32_T)flgView) == 0) {
        /* Transition: '<S60>:179' */
        if ((TLE9278BQX_Mgm_DW.oldMsgI < i) && (((int32_T)rbReg) == 0)) {
          /* Transition: '<S60>:194' */
          TLE9278BQX_Mgm_DW.oldMsgI = i;
          rbReg = 1U;
          size = (uint8_T)((int32_T)(((int32_T)size) + 1));
          TLE9278BQX_Mgm_DW.buffOut_o[size] = SBC_STOP_READ[(i)];
        } else {
          /* Transition: '<S60>:193' */
        }

        /* Transition: '<S60>:197' */
      } else {
        /* Transition: '<S60>:141' */
      }

      /* Transition: '<S60>:137' */
      i = (uint8_T)((int32_T)(((int32_T)i) + 1));
      flgView = 0U;
      tmp3 = (uint16_T)0xFFFF;
    }

    /* Transition: '<S60>:139' */
    TLE9278BQX_Mgm_DW.buffOut_o[0] = (uint16_T)size;
    if (((int32_T)rbReg) == 0) {
      /* Transition: '<S60>:201' */
      TLE9278BQX_Mgm_DW.oldMsgI = 0U;
    } else {
      /* Transition: '<S60>:200' */
    }

    if (((int32_T)clReg) == 0) {
      /* Transition: '<S60>:224' */
      TLE9278BQX_Mgm_DW.oldMsgCl = 0U;
    } else {
      /* Transition: '<S60>:225' */
    }

    /* End of Chart: '<S53>/Select_messages' */

    /* SignalConversion generated from: '<S53>/data' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = TLE9278BQX_Mgm_DW.buffOut_o[i_0];
    }

    /* End of SignalConversion generated from: '<S53>/data' */
    /* End of Outputs for SubSystem: '<S13>/SBC_STOP_READ' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_CLEAR_WDT_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S13>/SBC_CLEAR_WDT_WRITE' */
    /* Chart: '<S50>/Calc_even_parity' incorporates:
     *  Constant: '<S50>/SBC_CLEAR_WDT_WRITE'
     *  Constant: '<S50>/WD_CTRL_NORM_W_ID'
     *  Inport: '<Root>/WDSBCPeriod'
     *  Inport: '<Root>/WDSBCWin'
     *
     * Block description for '<Root>/WDSBCPeriod':
     *  Select WDT Period
     *
     * Block description for '<Root>/WDSBCWin':
     *  Enable WDT windowed
     */
    /* Decision 'D11': '<S11>:1:105' */
    /*  fc_SBC_CLEAR_WDT_WRITE_ID */
    /* Action '11': '<S11>:1:183' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_CLEAR_WDT_WRITE/Calc_even_parity */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_CLEAR_WDT_WRITE/Calc_even_parity */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_CLEAR_WDT_WRITE/Calc_even_parity */
    /* Transition: '<S58>:2' */
    /* Transition: '<S58>:4' */
    i = 0U;
    tmp3 = 0U;
    tmp = (uint16_T)(((((uint32_T)((int32_T)((((int32_T)WDSBCPeriod) & ((int32_T)
      0x0007)) * 256))) | (((uint32_T)SBC_CLEAR_WDT_WRITE[(((uint8_T)
      WD_CTRL_NORM_W_ID))]) & ((uint32_T)0xF8FF))) & ((uint32_T)0xDFFF)) |
                     ((uint32_T)((int32_T)((((int32_T)WDSBCWin) & ((int32_T)
      0x0001)) * 8192))));
    while (((int32_T)i) < 7) {
      /* Transition: '<S58>:7' */
      tmp3 = (uint16_T)(((tmp >> ((uint32_T)((int32_T)(((int32_T)i) + 8)))) &
                         ((uint16_T)0x01)) + tmp3);
      i = (uint8_T)((int32_T)(((int32_T)i) + 1));
    }

    /* Transition: '<S58>:6' */
    if ((((uint32_T)tmp3) & 1U) != 0U) {
      /* Transition: '<S58>:11' */
      tmp = (uint16_T)(((uint32_T)tmp) | ((uint32_T)0x8000));

      /*  set even parity */
    } else {
      /* Transition: '<S58>:12' */
      /*  set even parity */
    }

    /* End of Chart: '<S50>/Calc_even_parity' */

    /* Assignment: '<S50>/Assignment' incorporates:
     *  Constant: '<S50>/SBC_CLEAR_WDT_WRITE'
     *  Constant: '<S50>/WD_CTRL_NORM_W_ID'
     */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = SBC_CLEAR_WDT_WRITE[(i_0)];
    }

    SBCDataTxBuffer[(((uint8_T)WD_CTRL_NORM_W_ID))] = tmp;

    /* End of Assignment: '<S50>/Assignment' */
    /* End of Outputs for SubSystem: '<S13>/SBC_CLEAR_WDT_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_BYPASS_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S13>/SBC_BYPASS_WRITE' */
    /* Assignment: '<S49>/Assignment' incorporates:
     *  Chart: '<S49>/Calc_output'
     *  Constant: '<S49>/SBC_BYPASS_WRITE'
     *  Constant: '<S49>/SYS_STAT_CTRL_BYPASS_W_ID'
     */
    /* Decision 'D12': '<S11>:1:107' */
    /*  fc_SBC_BYPASS_WRITE_ID */
    /* Action '12': '<S11>:1:189' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_BYPASS_WRITE/Calc_output */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_BYPASS_WRITE/Calc_output */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_BYPASS_WRITE/Calc_output */
    /* Transition: '<S57>:2' */
    /* Transition: '<S57>:21' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = SBC_BYPASS_WRITE[(i_0)];
    }

    SBCDataTxBuffer[(((uint8_T)SYS_STAT_CTRL_BYPASS_W_ID))] = (uint16_T)
      ((SBC_BYPASS_WRITE[(((uint8_T)SYS_STAT_CTRL_BYPASS_W_ID))] & ((uint16_T)
         0x00FF)) | (SetSBCSysStat_b << ((uint32_T)8)));

    /* End of Assignment: '<S49>/Assignment' */
    /* End of Outputs for SubSystem: '<S13>/SBC_BYPASS_WRITE' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_CUSTOM_ID)) {
    /* Outputs for Function Call SubSystem: '<S13>/SBC_CUSTOM' */
    /* Chart: '<S51>/Selct_Custom' incorporates:
     *  DataStoreRead: '<S51>/Data Store Read'
     *  Inport: '<Root>/CntRefreshWDT'
     *  Inport: '<Root>/KeyReqMsgOnD'
     *
     * Block description for '<Root>/CntRefreshWDT':
     *  Refresh WDT trigger counter
     *
     * Block description for '<Root>/KeyReqMsgOnD':
     *  Key to request message on demand
     */
    /* Decision 'D13': '<S11>:1:109' */
    /*  fc_SBC_CUSTOM_ID */
    /* Action '13': '<S11>:1:195' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_CUSTOM/Selct_Custom */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_CUSTOM/Selct_Custom */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_CUSTOM/Selct_Custom */
    /* Transition: '<S59>:2' */
    i = 0U;
    if (CntRefreshWDT != TLE9278BQX_Mgm_DW.oldCntRefreshWDT) {
      /* Transition: '<S59>:26' */
      TLE9278BQX_Mgm_DW.oldCntRefreshWDT = CntRefreshWDT;
      if (KeyReqMsgOnD != TLE9278BQX_Mgm_DW.oldReqMsgOnD) {
        /* Transition: '<S59>:21' */
        TLE9278BQX_Mgm_DW.oldReqMsgOnD = KeyReqMsgOnD;
        TLE9278BQX_Mgm_DW.j = 0U;
      } else {
        /* Transition: '<S59>:22' */
      }

      if (((int32_T)TLE9278BQX_Mgm_DW.j) < 3) {
        /* Transition: '<S59>:36' */
        while (((uint16_T)i) <= SBCCustomTxBuff[(TLE9278BQX_Mgm_DW.j)]) {
          /* Transition: '<S59>:5' */
          /* Transition: '<S59>:7' */
          TLE9278BQX_Mgm_DW.buffOut[i] = SBCCustomTxBuff[(3 * ((int32_T)i)) +
            ((int32_T)TLE9278BQX_Mgm_DW.j)];

          /* Transition: '<S59>:8' */
          i = (uint8_T)((int32_T)(((int32_T)i) + 1));
        }

        /* Transition: '<S59>:14' */
        i_0 = ((int32_T)TLE9278BQX_Mgm_DW.j) + 1;
        if ((((int32_T)TLE9278BQX_Mgm_DW.j) < 2) && (((int32_T)SBCCustomTxBuff
              [(i_0)]) != 0)) {
          /* Transition: '<S59>:15' */
          TLE9278BQX_Mgm_DW.ReqMsgOnD_m++;
        } else {
          /* Transition: '<S59>:9' */
        }

        /* Transition: '<S59>:30' */
        TLE9278BQX_Mgm_DW.j = (uint8_T)i_0;
      } else {
        /* Transition: '<S59>:39' */
        /* Transition: '<S59>:31' */
      }
    } else {
      /* Transition: '<S59>:28' */
      /* Transition: '<S59>:38' */
      /* Transition: '<S59>:31' */
    }

    /* End of Chart: '<S51>/Selct_Custom' */

    /* SignalConversion generated from: '<S51>/data' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = TLE9278BQX_Mgm_DW.buffOut[i_0];
    }

    /* End of SignalConversion generated from: '<S51>/data' */
    /* End of Outputs for SubSystem: '<S13>/SBC_CUSTOM' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_PRE_INIT_READ_ID)) {
    /* Outputs for Function Call SubSystem: '<S13>/SBC_PRE_INIT_READ' */
    /* Decision 'D14': '<S11>:1:111' */
    /*  fc_SBC_PRE_INIT_READ_ID */
    /* Action '14': '<S11>:1:201' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = SBC_PRE_INIT_READ[(i_0)];
    }

    /* SignalConversion generated from: '<S52>/AdcSel' incorporates:
     *  Constant: '<S52>/SBC_PRE_INIT_READ'
     */
    AdcSel = AdcSel_k;

    /* End of Outputs for SubSystem: '<S13>/SBC_PRE_INIT_READ' */
  } else if (((uint16_T)SBCMsgIdx) == ((uint16_T)SBC_BYPASS_EE_WRITE_ID)) {
    /* Outputs for Function Call SubSystem: '<S13>/SBC_BYPASS_EE_WRITE' */
    /* Assignment: '<S48>/Assignment' incorporates:
     *  Chart: '<S48>/Calc_output'
     *  Constant: '<S48>/SBC_BYPASS_EE_WRITE'
     *  Constant: '<S48>/SYS_STAT_CTRL_BYPASS_EE_W_ID'
     */
    /* Decision 'D15': '<S11>:1:113' */
    /*  fc_SBC_BYPASS_EE_WRITE_ID */
    /* Action '15': '<S11>:1:207' */
    /* Gateway: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_BYPASS_EE_WRITE/Calc_output */
    /* During: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_BYPASS_EE_WRITE/Calc_output */
    /* Entry Internal: TLE9278BQX_Mgm/fc_Bkg/Subsystem1/Set_2/SBC_BYPASS_EE_WRITE/Calc_output */
    /* Transition: '<S56>:2' */
    /* Transition: '<S56>:21' */
    for (i_0 = 0; i_0 < 30; i_0++) {
      SBCDataTxBuffer[(i_0)] = SBC_BYPASS_EE_WRITE[(i_0)];
    }

    SBCDataTxBuffer[(((uint8_T)SYS_STAT_CTRL_BYPASS_EE_W_ID))] = (uint16_T)
      ((SBC_BYPASS_EE_WRITE[(((uint8_T)SYS_STAT_CTRL_BYPASS_EE_W_ID))] &
        ((uint16_T)0x00FF)) | (SetSBCSysStat_b << ((uint32_T)8)));

    /* End of Assignment: '<S48>/Assignment' */
    /* End of Outputs for SubSystem: '<S13>/SBC_BYPASS_EE_WRITE' */
  } else {
    /* Decision 'D16': '<S11>:1:115' */
    /*  Default */
    /*  fc_DEFAULT */
    /* Action '16': '<S11>:1:213' */
  }

  /* End of Truth Table: '<S8>/Scheduler_Messages' */

  /* SignalConversion generated from: '<S4>/ReqMsgOnD' */
  ReqMsgOnD = TLE9278BQX_Mgm_DW.ReqMsgOnD_m;

  /* user code (Output function Trailer for TID1) */

  /* System '<S2>/fc_Bkg' */

  /* PILOTAGGIO USCITE - 10ms */

  /* Update for Memory: '<S7>/Memory34' incorporates:
   *  Constant: '<S7>/SBCVIORT'
   */
  TLE9278BQX_Mgm_DW.Memory34_PreviousInput = SBCVIORT;

  /* Update for Memory: '<S7>/Memory12' */
  TLE9278BQX_Mgm_DW.Memory12_PreviousInput = rtb_Selector8;

  /* Update for Memory: '<S7>/Memory' incorporates:
   *  Inport: '<Root>/FlgSBCFOEn'
   *
   * Block description for '<Root>/FlgSBCFOEn':
   *  Forced by Software the FO Pin
   */
  TLE9278BQX_Mgm_DW.Memory_PreviousInput = FlgSBCFOEn;

  /* Update for Memory: '<S7>/Memory13' */
  TLE9278BQX_Mgm_DW.Memory13_PreviousInput = rtb_Selector;

  /* Update for Memory: '<S7>/Memory1' incorporates:
   *  Constant: '<S7>/SBCBOOSTEN'
   */
  TLE9278BQX_Mgm_DW.Memory1_PreviousInput = SBCBOOSTEN;

  /* Update for Memory: '<S7>/Memory2' incorporates:
   *  Constant: '<S7>/SBCBOOSTV'
   */
  TLE9278BQX_Mgm_DW.Memory2_PreviousInput = SBCBOOSTV;

  /* Update for Memory: '<S7>/Memory14' */
  TLE9278BQX_Mgm_DW.Memory14_PreviousInput = rtb_Selector1;

  /* Update for Memory: '<S7>/Memory5' */
  TLE9278BQX_Mgm_DW.Memory5_PreviousInput = AdcSel_k;

  /* Update for Memory: '<S7>/Memory3' incorporates:
   *  Constant: '<S7>/SBCVBSCFG'
   */
  TLE9278BQX_Mgm_DW.Memory3_PreviousInput = SBCVBSCFG;

  /* Update for Memory: '<S7>/Memory15' */
  TLE9278BQX_Mgm_DW.Memory15_PreviousInput = rtb_Selector2;

  /* Update for Memory: '<S7>/Memory4' */
  TLE9278BQX_Mgm_DW.Memory4_PreviousInput = SetSBCSysStat_b;

  /* Update for Memory: '<S7>/Memory16' */
  TLE9278BQX_Mgm_DW.Memory16_PreviousInput = rtb_Selector3;

  /* Update for Memory: '<S7>/Memory6' incorporates:
   *  Constant: '<S7>/SBCWKEN'
   */
  TLE9278BQX_Mgm_DW.Memory6_PreviousInput = SBCWKEN;

  /* Update for Memory: '<S7>/Memory17' */
  TLE9278BQX_Mgm_DW.Memory17_PreviousInput = rtb_Selector4;

  /* Update for Memory: '<S7>/Memory7' incorporates:
   *  Constant: '<S7>/SBCWKPUPD'
   */
  TLE9278BQX_Mgm_DW.Memory7_PreviousInput = SBCWKPUPD;

  /* Update for Memory: '<S7>/Memory18' */
  TLE9278BQX_Mgm_DW.Memory18_PreviousInput = rtb_Selector5;

  /* Update for Memory: '<S7>/Memory9' incorporates:
   *  Constant: '<S7>/SBCCAN00EN'
   */
  TLE9278BQX_Mgm_DW.Memory9_PreviousInput = SBCCAN00EN;

  /* Update for Memory: '<S7>/Memory19' */
  TLE9278BQX_Mgm_DW.Memory19_PreviousInput = rtb_Selector6;

  /* Update for Memory: '<S7>/Memory10' incorporates:
   *  Inport: '<Root>/SetSBCCan1'
   *
   * Block description for '<Root>/SetSBCCan1':
   *  Set CAN 1 Configuration
   */
  TLE9278BQX_Mgm_DW.Memory10_PreviousInput = SetSBCCan1;

  /* Update for Memory: '<S7>/Memory20' */
  TLE9278BQX_Mgm_DW.Memory20_PreviousInput = rtb_Selector7;

  /* Update for Memory: '<S7>/Memory11' incorporates:
   *  Constant: '<S7>/SBCCAN02EN'
   */
  TLE9278BQX_Mgm_DW.Memory11_PreviousInput = SBCCAN02EN;

  /* Update for Memory: '<S7>/Memory35' incorporates:
   *  Constant: '<S7>/SBCCAN03EN'
   */
  TLE9278BQX_Mgm_DW.Memory35_PreviousInput = SBCCAN03EN;

  /* Update for Memory: '<S7>/Memory21' */
  TLE9278BQX_Mgm_DW.Memory21_PreviousInput = rtb_Selector9;

  /* Update for Memory: '<S7>/Memory22' */
  TLE9278BQX_Mgm_DW.Memory22_PreviousInput = rtb_Selector10;

  /* Update for Memory: '<S7>/Memory8' */
  TLE9278BQX_Mgm_DW.Memory8_PreviousInput = CntSBCWrite;
}

/* Start for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Mgm_fc_Init_Start(void)
{
  /* Start for Constant: '<S5>/ID_VER_TLE9278BQX_MGM_DEF' */
  IdVer_TLE9278BQX_Mgm = ID_VER_TLE9278BQX_MGM_DEF;
}

/* Output and update for function-call system: '<S2>/fc_Init' */
void TLE9278BQX_Mgm_fc_Init(void)
{
  {
    /* user code (Output function Header for TID2) */

    /* System '<S2>/fc_Init' */
    /* INIZIALIZZAZIONE AUTOMA - Funzione RTW */
    TLE9278BQX_Mgm_initialize();

    /* Constant: '<S5>/ID_VER_TLE9278BQX_MGM_DEF' */
    IdVer_TLE9278BQX_Mgm = ID_VER_TLE9278BQX_MGM_DEF;

    /* Constant: '<S5>/ZERO' */
    CntSBCWrite = 0U;

    /* Constant: '<S5>/ZERO1' */
    ReqMsgOnD = 0U;

    /* user code (Output function Trailer for TID2) */

    /* System '<S2>/fc_Init' */

    /* PILOTAGGIO USCITE - INIT */
    /* Read static variables */
  }
}

/* Model step function */
void TLE9278BQX_Mgm_Bkg(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' incorporates:
   *  SubSystem: '<S2>/fc_Bkg'
   */
  TLE9278BQX_Mgm_fc_Bkg();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_Bkg' */
}

/* Model step function */
void TLE9278BQX_Mgm_Init(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_Mgm_fc_Init();

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* Model initialize function */
void TLE9278BQX_Mgm_initialize(void)
{
  /* Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' incorporates:
   *  SubSystem: '<S2>/fc_Init'
   */
  TLE9278BQX_Mgm_fc_Init_Start();

  /* End of Start for RootInportFunctionCallGenerator generated from: '<Root>/ev_PowerOn' */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#endif                                 // _BUILD_TLE9278BQX_MGM_

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * matlab_report_gen                                                          *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_report_gen                                                        *
 * stateflow                                                                  *
 *============================================================================*/