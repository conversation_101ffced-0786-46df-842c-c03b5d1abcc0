/******************************************************************************/
/* $HeadURL::                                                              $  */
/* $Revision::                                                             $  */
/* $Date::                                                                 $  */
/* $Author::                                                               $  */
/******************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CombAdp
**  Filename        :  TbInjCorrAdEE_mgm.c
**  Created on      :  10-sep-2021 08:00:00
**  Original author :  GiuseppeR
******************************************************************************/
/*****************************************************************************
**
**                        TbInjCorrAdEE_mgm.c Description
**
**  Management of ee-adaptive coefficients for CombAdp
 *
******************************************************************************/

#ifndef _BUILD_TBINJCORRADEE_MGM_C_
#define _BUILD_TBINJCORRADEE_MGM_C_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "TbInjCorrAdEE_mgm.h"

static uint16_T interp(uint16_T yL, uint16_T yR, uint16_T RatioX);

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Get_TbInjCorrAdEE
**
**   Description:
**    return the value of a cell of TbInjCorrAdEE 
**
**   Parameters :
**    uint8_T   row index
**    uint8_T   column index
**
**   Returns:
**    int16_T    cell value
**
******************************************************************************/
uint16_T Get_TbInjCorrAdEE(uint8_T rowIndex, uint8_T columnIndex)
{
    uint16_T result;
    uint16_T index = (rowIndex*TBINJCORRADEE_COLUMN_NUMBER)+columnIndex;
    if ((index < 0u) || (index >= TBINJCORRADEE_LENGTH))
    {    
        result = 0u;
    }
    else
    {
        result = TbInjCorrAdEE[index];
    }
    return result;
}

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Set_TbInjCorrAdEE
**
**   Description:
**    assign the value of a cell of TbInjCorrAdEE 
**
**   Parameters :
**    uint8_T   row index
**    uint8_T   column index
**    int16_T   cell value
**
**   Returns:
**    void
**
******************************************************************************/
void Set_TbInjCorrAdEE(uint8_T rowIndex, uint8_T columnIndex, uint16_T value)
{
    uint16_T index = (rowIndex*TBINJCORRADEE_COLUMN_NUMBER)+columnIndex;
    if ((index >= 0u) && (index < TBINJCORRADEE_LENGTH))
    {
        if ((value > MIN_TBINJCORRADEE) && (value <= MAX_TBINJCORRADEE))
        {
            TbInjCorrAdEE[index] = value;
        }
    }
}

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : Interpolate_TbInjCorrAdEE
**
**   Description:
**    return the value of direct interpolation on TbInjCorrAdEE table
**
**   Parameters :
**    uint16_T   row index
**    uint16_T   row ratio
**    uint16_T   column index
**    uint16_T   column ratio
**
**   Returns:
**    int16_T    interpolated value
**
******************************************************************************/
uint16_T Interpolate_TbInjCorrAdEE(uint16_T IdX, uint16_T RatioX, uint16_T IdY, uint16_T RatioY)
{
    uint16_T OutZ0;
    uint16_T OutZ1;
    uint16_T row0 = IdX * TBINJCORRADEE_COLUMN_NUMBER;
    uint16_T row1 = (IdX+1) * TBINJCORRADEE_COLUMN_NUMBER;
    uint16_T col0 = IdY;
    uint16_T col1 = (IdY+1);
  
    if(IdX == (TBINJCORRADEE_ROW_NUMBER-1))
    {
        OutZ0 = TbInjCorrAdEE[row0+col0];
        OutZ1 = TbInjCorrAdEE[MIN(row0+col1,TBINJCORRADEE_LENGTH-1u)];
    }
    else
    {
        OutZ0 = interp(TbInjCorrAdEE[row0+col0], TbInjCorrAdEE[row1+col0], RatioX);
        OutZ1 = interp(TbInjCorrAdEE[row0+col1], TbInjCorrAdEE[MIN(row1+col1,TBINJCORRADEE_LENGTH-1u)], RatioX);
    }   
    
    return interp( OutZ0,   OutZ1,   RatioY);
}

/******************************************************************************
**   Function    : Reset_TbInjCorrAdEE
**
**   Description:
**    reset the value of  TbInjCorrAdEE table
**
**   Parameters :
**
**   Returns:
**
******************************************************************************/
void Reset_TbInjCorrAdEE(void)
{
    uint16_T idx;                  
    for(idx=0u; idx < TBINJCORRADEE_LENGTH; idx++)    
    {  
        TbInjCorrAdEE[idx] = 32768u; /* 1 LSB 2^-15 */
    }
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/
/******************************************************************************
**   Function    : interp
**
**   Description:
**    Interpolate along one axis
**
**   Parameters :
**    [yL] left value
**    [yR] right value
**    [ratioX] ratio
**    [out] interpolated value
**
**   SW Requirements:
**    NA
**
**
******************************************************************************/

static uint16_T interp(uint16_T yL, uint16_T yR, uint16_T RatioX)
{
	uint16_T outValue;
    if(RatioX == MAX_uint16_T)
    {
        outValue = yR;    
    }
    else if(RatioX == 0)
    {
        outValue = yL;    
    }
    else
    {
        int32_T tmp;
        
        tmp = (int32_T)yR - (int32_T)yL;
        tmp *= (int32_T)RatioX;
        tmp >>= 16;
        tmp += (int32_T)yL;
        
        outValue = (uint16_T)tmp;  
    }
	return outValue;
}


#endif /* _BUILD_TBINJCORRADEE_MGM_C_ */
