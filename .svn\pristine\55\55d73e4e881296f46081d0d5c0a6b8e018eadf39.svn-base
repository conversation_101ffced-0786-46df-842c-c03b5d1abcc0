/*****************************************************************************************************************/
/* $HeadURL::                                                                                                              $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  CanMgmOut
**  Filename        :  CanMgmOut_FTel.h
**  Created on      :  01-dec-2020 09:37:00
**  Original author :  SantoroR
******************************************************************************/
#ifndef CANMGM_OUT_FTEL_H
#define CANMGM_OUT_FTEL_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "canmgmout_out.h"
#include "canmgmin_out.h" 
#include "recmgm_out.h"
#include "Mcan_out.h"
#include "AnalogIn_out.h"
#include "CoilTarget_out.h"
#include "Diagcanmgm.h"
#include "Diagmgm_out.h"
#include "DigIn_out.h"
#include "KnockCorrNom_out.h"
#include "KnockCorrTot_out.h"
#include "Ionacq_out.h"
#include "IonPhaseMgm_out.h"
#include "IonAcqBufRec_out.h"
#include "IonAcqBufMgm_out.h"
#include "IonKnockSpikeDet_out.h"
#include "IonAcqCircMgm_out.h"
#include "IonKnockPower_out.h"
#include "IonDwellMgm_out.h"
#include "IonKnockEn_out.h"
#include "IonIntMgm_out.h"
#include "Ionmisf_out.h"
#include "gtm_eisb_out.h"
#include "Loadmgm_out.h"
#include "Msparkcmd_out.h"
#include "MKnockDet_out.h"
#include "Ignincmd_out.h"
#include "RonDetectEn_out.h"
#include "RonDetectEst_out.h"
#include "RonDetectMgm_out.h"
#include "RonDetectCross_out.h"
#include "RonDetectFuel_out.h"
#include "RonDetectSA_out.h"
#include "SyncMgm_out.h"
#include "temp_mgm.h"
#include "TSparkCtrlAdat_out.h"
#include "mathlib.h"
#include "Utils_out.h"
#include "ccp.h"
#include "KWP2000_out.h"
#include "ionchargectrl_out.h"
#include "CoilAngPattern_out.h"
#include "IonKnockAirCorr_out.h"
#include "TempECUMgm_out.h"

/*****************************************************************************
** PRIVATE DEFINES
******************************************************************************/
// sending telemetry messages on CAN_C
/*#define     TEL1_DX_BUF   15    // ID 0x720
#define     TEL1_SX_BUF   16    // ID 0x721
#define     TEL2_DX_BUF   17    // ID 0x722
#define     TEL2_SX_BUF   18    // ID 0x723
#define     TEL3_DX_BUF   19    // ID 0x724
#define     TEL3_SX_BUF   20    // ID 0x725
#define     TEL4_DX_BUF   21    // ID 0x726
#define     TEL4_SX_BUF   22    // ID 0x727
#define     TEL5_DX_BUF   23    // ID 0x728
#define     TEL5_SX_BUF   24    // ID 0x729
#define     TEL6_DX_BUF   25    // ID 0x730
#define     TEL6_SX_BUF   26    // ID 0x731
#define     TEL7_DX_BUF   27    // ID 0x732
#define     TEL7_SX_BUF   28    // ID 0x733
#define     TEL8_DX_BUF   29    // ID 0x734
#define     TEL8_SX_BUF   30    // ID 0x735
#define     TEL9_DX_BUF   31    // ID 0x736
#define     TEL9_SX_BUF   32    // ID 0x737
*/
#define TOT_NUM_RASTERS 4U
#define MAX_RASTER_SIZE 13U

#define TELEMETRY_TX_MSG_MAX  (MAX_RASTER_SIZE -1u)
#define EFFDWELLTIM_CAN_VS_OS       8u
#define THPEAKCYL_CAN_VS_OS         64u
#define INTION_CAN_VS_OS            128u
#define KNOCK_INT_CAN_MAX           129024u    // 31.5 * 2^12
#define KNOCKINT_CAN_VS_OS          256u
#define THRINTKNOCK_CAN_MAX         16128u
#define THRINTKNOCK_CAN_VS_OS       512u
#define BAT_LOW_8V                  128u     //(8V * 2^4)
#define BAT_LOW_12V                 192u    //(12V * 2^4)
#define BAT_LOW_16V                 256u    //(16V * 2^4)
#define IPRICORR_CAN_VS_OS          -16896
#define TSPARKMEDIAN_CAN_VS_OS      -168
#define TEMPECU_CAN_VS_OS           888

#define VCHARGE_CAN_VS_OS           64u
#define VCHARGE_CAN_

/*****************************************************************************
** PRIVATE MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PRIVATE TYPEDEFS
******************************************************************************/
#pragma ghs startnomisra

/* Private typedef */
typedef struct IONTEL_10_1_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  IonSignal0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  NSample1   :3;
            uint8_T  IonSignal1   :5;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ShownAbsTdc2   :3;
            uint8_T  NSample2   :5;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  SAKnock_13   :3;
            uint8_T  SAKnock_03   :5;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  SAKnock_34   :1;
            uint8_T  SAKnock_24   :5;
            uint8_T  SAKnock_14   :2;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  SAKnock_45   :4;
            uint8_T  SAKnock_35   :4;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  SAKnock_66   :2;
            uint8_T  SAKnock_56   :5;
            uint8_T  SAKnock_46   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  SAKnock_77   :5;
            uint8_T  SAKnock_67   :3;
        } B;
    } Byte7;

} IONTEL_10_1_T;

typedef struct IONTEL_10_2_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FlgCntKnockCohInc_70   :1;
            uint8_T  FlgCntKnockCohInc_60   :1;
            uint8_T  FlgCntKnockCohInc_50   :1;
            uint8_T  FlgCntKnockCohInc_40   :1;
            uint8_T  FlgCntKnockCohInc_30   :1;
            uint8_T  FlgCntKnockCohInc_20   :1;
            uint8_T  FlgCntKnockCohInc_10   :1;
            uint8_T  FlgCntKnockCohInc_00   :1;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_021   :1;
            uint8_T  StDiag_011   :1;
            uint8_T  CntTaskNoSync1   :6;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_342   :1;
            uint8_T  StDiag_332   :1;
            uint8_T  StDiag_302   :1;
            uint8_T  StDiag_282   :1;
            uint8_T  StDiag_192   :1;
            uint8_T  StDiag_082   :1;
            uint8_T  StDiag_072   :1;
            uint8_T  StDiag_062   :1;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_443   :1;
            uint8_T  StDiag_433   :1;
            uint8_T  StDiag_423   :1;
            uint8_T  StDiag_413   :1;
            uint8_T  StDiag_403   :1;
            uint8_T  StDiag_393   :1;
            uint8_T  StDiag_383   :1;
            uint8_T  StDiag_353   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_054   :1;
            uint8_T  StDiag_044   :1;
            uint8_T  StDiag_034   :1;
            uint8_T  StDiag_504   :1;
            uint8_T  StDiag_494   :1;
            uint8_T  StDiag_474   :1;
            uint8_T  StDiag_464   :1;
            uint8_T  StDiag_454   :1;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_165   :1;
            uint8_T  StDiag_155   :1;
            uint8_T  StDiag_145   :1;
            uint8_T  StDiag_135   :1;
            uint8_T  StDiag_125   :1;
            uint8_T  StDiag_115   :1;
            uint8_T  StDiag_105   :1;
            uint8_T  StDiag_095   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_256   :1;
            uint8_T  StDiag_246   :1;
            uint8_T  StDiag_236   :1;
            uint8_T  StDiag_226   :1;
            uint8_T  StDiag_216   :1;
            uint8_T  StDiag_206   :1;
            uint8_T  StDiag_186   :1;
            uint8_T  StDiag_176   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_487   :1;
            uint8_T  StDiag_377   :1;
            uint8_T  StDiag_367   :1;
            uint8_T  StDiag_327   :1;
            uint8_T  StDiag_317   :1;
            uint8_T  StDiag_297   :1;
            uint8_T  StDiag_277   :1;
            uint8_T  StDiag_267   :1;
        } B;
    } Byte7;

} IONTEL_10_2_T;

typedef struct IONTEL_10_3_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Start_Th0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Start_Ch1   :7;
            uint8_T  Start_Th1   :1;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StPhaseCyl2   :3;
            uint8_T  IonSelectCyl2   :3;
            uint8_T  Start_Ch2   :2;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Start_ion3   :1;
            uint8_T  EffDwellTime3   :7;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Start_ion4   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StartFftKnockId5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :3;
            uint8_T  IonAbsTdc6   :3;
            uint8_T  FlgSyncPhased6   :1;
            uint8_T  StartFftKnockId6   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ThPeakId7   :8;
        } B;
    } Byte7;

} IONTEL_10_3_T;

typedef struct IONTEL_10_4_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StMisf_00   :2;
            uint8_T  IntIon_00   :6;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StMisf_11   :2;
            uint8_T  IntIon_11   :6;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StMisf_22   :2;
            uint8_T  IntIon_22   :6;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StMisf_33   :2;
            uint8_T  IntIon_33   :6;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StMisf_44   :2;
            uint8_T  IntIon_44   :6;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StMisf_55   :2;
            uint8_T  IntIon_55   :6;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StMisf_66   :2;
            uint8_T  IntIon_66   :6;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StMisf_77   :2;
            uint8_T  IntIon_77   :6;
        } B;
    } Byte7;

} IONTEL_10_4_T;

typedef struct IONTEL_10_5_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt_10   :1;
            uint8_T  FlgWrongIgnIn_00   :1;
            uint8_T  KnockInt_00   :6;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt_21   :2;
            uint8_T  FlgWrongIgnIn_11   :1;
            uint8_T  KnockInt_11   :5;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt_32   :3;
            uint8_T  FlgWrongIgnIn_22   :1;
            uint8_T  KnockInt_22   :4;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt_43   :4;
            uint8_T  FlgWrongIgnIn_33   :1;
            uint8_T  KnockInt_33   :3;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt_54   :5;
            uint8_T  FlgWrongIgnIn_44   :1;
            uint8_T  KnockInt_44   :2;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  KnockInt_65   :6;
            uint8_T  FlgWrongIgnIn_55   :1;
            uint8_T  KnockInt_55   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  FlgWrongIgnIn_76   :1;
            uint8_T  KnockInt_76   :6;
            uint8_T  FlgWrongIgnIn_66   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  ThrIntKnock7   :8;
        } B;
    } Byte7;
} IONTEL_10_5_T;

typedef struct IONTEL_10_6_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Dummy0     :5;
            uint8_T  VtRec_24_26_28_300   :1;
            uint8_T  VtRec_25_27_29_310     :1;
            uint8_T  Dummy1     :1;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  MegaKnock71   :1;
            uint8_T  MegaKnock61   :1;
            uint8_T  MegaKnock51   :1;
            uint8_T  MegaKnock41   :1;
            uint8_T  MegaKnock31   :1;
            uint8_T  MegaKnock21   :1;
            uint8_T  MegaKnock11   :1;
            uint8_T  MegaKnock01   :1;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  DwellInt12   :2;
            uint8_T  DwellInt02   :6;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  DwellInt23   :4;
            uint8_T  DwellInt13   :4;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  DwellInt34   :6;
            uint8_T  DwellInt24   :2;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  DwellInt55   :2;
            uint8_T  DwellInt45   :6;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  DwellInt66   :4;
            uint8_T  DwellInt56   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  DwellInt77   :6;
            uint8_T  DwellInt67   :2;
        } B;
    } Byte7;
} IONTEL_10_6_T;

typedef struct IONTEL_10_7_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Load0   :8;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  Rpm1   :8;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TAir2   :7;
            uint8_T  Rpm2   :1;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  SAout3   :7;
            uint8_T  TAir3   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TWater4   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy5_0   :4;
            uint8_T  IonKnockEnabled5   :1;
            uint8_T  PlasmaActive5   :1;
            uint8_T  VBattery5   :2;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CALID_56   :4;
            uint8_T  CALID_46   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CALID_77   :4;
            uint8_T  CALID_67   :4;
        } B;
    } Byte7;

} IONTEL_10_7_T;

typedef struct IONTEL_10_8_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CntRonRun0   :4;
            uint8_T  FlgRonStoredEE0   :1;
            uint8_T  RonLevelEE0   :3;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy1_0   :1;
            uint8_T  FlgSteadyStateRon1   :1;
            uint8_T  FlgDLoadTAir1   :1;
            uint8_T  SARon1   :5;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StRonDetect2   :8;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StRonCCheck3   :8;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StReFuel4   :8;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CntRonSuspPos5   :8;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CntRonSuspNeg6   :8;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  CntRonSuspZero7   :8;
        } B;
    } Byte7;

} IONTEL_10_8_T;

typedef struct IONTEL_10_9_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TSparkMedian_00   :4;
            uint8_T  IPriCorr_00   :4;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TSparkMedian_11   :4;
            uint8_T  IPriCorr_11   :4;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TSparkMedian_22   :4;
            uint8_T  IPriCorr_22   :4;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TSparkMedian_33   :4;
            uint8_T  IPriCorr_33   :4;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TSparkMedian_44   :4;
            uint8_T  IPriCorr_44   :4;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TSparkMedian_55   :4;
            uint8_T  IPriCorr_55   :4;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TSparkMedian_66   :4;
            uint8_T  IPriCorr_66   :4;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TSparkMedian_77   :4;
            uint8_T  IPriCorr_77   :4;
        } B;
    } Byte7;

} IONTEL_10_9_T;

typedef struct IONTEL_10_10_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_580   :1;
            uint8_T  StDiag_570   :1;
            uint8_T  StDiag_560   :1;
            uint8_T  StDiag_550   :1;
            uint8_T  StDiag_540   :1;
            uint8_T  StDiag_530   :1;
            uint8_T  StDiag_520   :1;
            uint8_T  StDiag_510   :1;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_661   :1;
            uint8_T  StDiag_651   :1;
            uint8_T  StDiag_641   :1;
            uint8_T  StDiag_631   :1;
            uint8_T  StDiag_621   :1;
            uint8_T  StDiag_611   :1;
            uint8_T  StDiag_601   :1;
            uint8_T  StDiag_591   :1;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_742   :1;
            uint8_T  StDiag_732   :1;
            uint8_T  StDiag_722   :1;
            uint8_T  StDiag_712   :1;
            uint8_T  StDiag_702   :1;
            uint8_T  StDiag_692   :1;
            uint8_T  StDiag_682   :1;
            uint8_T  StDiag_672   :1;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_823   :1;
            uint8_T  StDiag_813   :1;
            uint8_T  StDiag_803   :1;
            uint8_T  StDiag_793   :1;
            uint8_T  StDiag_783   :1;
            uint8_T  StDiag_773   :1;
            uint8_T  StDiag_763   :1;
            uint8_T  StDiag_753   :1;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_904   :1;
            uint8_T  StDiag_894   :1;
            uint8_T  StDiag_884   :1;
            uint8_T  StDiag_874   :1;
            uint8_T  StDiag_864   :1;
            uint8_T  StDiag_854   :1;
            uint8_T  StDiag_844   :1;
            uint8_T  StDiag_834   :1;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  StDiag_985   :1;
            uint8_T  StDiag_975   :1;
            uint8_T  StDiag_965   :1;
            uint8_T  StDiag_955   :1;
            uint8_T  StDiag_945   :1;
            uint8_T  StDiag_935   :1;
            uint8_T  StDiag_925   :1;
            uint8_T  StDiag_915   :1;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :6;
            uint8_T  StDiag_006   :1;
            uint8_T  StDiag_996   :1;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T  TempEcu7   :8;
        } B;
    } Byte7;

} IONTEL_10_10_T;

typedef struct IONTEL_10_11_tag
{
    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy0_0   :2;
            uint8_T  VCharge_00   :6;
        } B;
    } Byte0;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy1_0   :2;
            uint8_T  VCharge_11   :6;
        } B;
    } Byte1;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy2_0   :2;
            uint8_T  VCharge_22   :6;
        } B;
    } Byte2;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy3_0   :2;
            uint8_T  VCharge_33   :6;
        } B;
    } Byte3;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy4_0   :2;
            uint8_T  VChargeObj_04   :6;
        } B;
    } Byte4;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy5_0   :2;
            uint8_T  VChargeObj_15   :6;
        } B;
    } Byte5;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy6_0   :2;
            uint8_T  VChargeObj_26   :6;
        } B;
    } Byte6;

    union
    {
        uint8_T R; 
        struct
        {
            uint8_T Dummy7_0   :2;
            uint8_T  VChargeObj_37   :6;
        } B;
    } Byte7;

} IONTEL_10_11_T;

#pragma ghs endnomisra

/*****************************************************************************
** IMPORTED CALIBRATIONS
******************************************************************************/
extern CALQUAL CALQUAL_POST uint32_T   TELTXENODOMAX;
extern CALQUAL CALQUAL_POST uint8_T    VTCANTXRASTER[TOT_NUM_RASTERS];
extern CALQUAL CALQUAL_POST uint8_T    VTCANTXSET1MESSAGE[MAX_RASTER_SIZE];
extern CALQUAL CALQUAL_POST uint8_T    VTCANTXSET2MESSAGE[MAX_RASTER_SIZE];
extern CALQUAL CALQUAL_POST uint8_T    VTCANTXSET3MESSAGE[MAX_RASTER_SIZE];
extern CALQUAL CALQUAL_POST uint8_T    VTCANTXSET4MESSAGE[MAX_RASTER_SIZE];
extern CALQUAL CALQUAL_POST int16_T    OFFSAKNOCKCAN;
extern CALQUAL CALQUAL_POST uint8_T    CALVER[4];

/*****************************************************************************
** PRIVATE FUNCTION PROTOTYPES
******************************************************************************/
static int16_T CANMGM_IONTEL_10_1(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_2(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_3(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_4(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_5(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_6(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_7(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_8(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_9(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_9_PRI(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_10(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_11(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_12(uint8_T messageBuffer);
static int16_T CANMGM_IONTEL_10_13(uint8_T messageBuffer);
static uint8_T CANMGM_SendTelemetry(CALQUAL CALQUAL_POST uint8_T *canTxSetTmpMessage, uint8_T nextInQueRaster);


#endif

/****************************************************************************
 ****************************************************************************/


