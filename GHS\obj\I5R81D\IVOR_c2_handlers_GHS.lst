
                                   Mon Jun 02 14:54:43 2025           Page 1
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
Command Line:   C:\ghs\comp_201516\asppc.exe -noundefined -elf -b1
                -I..\tree\BIOS\GTM\include -I..\tree\BIOS\GTM\cfg
                -I..\tree\COMMON\CONFIG\asm -I..\tree\COMMON\CONFIG\C
                -I..\tree\COMMON\INCLUDE -I..\tree\COMMON\LIB
                -I..\tree\BIOS\COMMON -I..\tree\AK_OSEK -I..\tree\DD\COMMON
                -I..\tree\APPLICATION\COMMON -I..\tree\EEPCOM -I..\common
                -IC:\ghs\comp_201516\lib\ppc5744 -cpu=ppc5744kz410 -noSPE
                -dbo=C:\Users\<USER>\Desktop\Bugatti\EISB8C_SI_03_RonDetectFuel\GHS\obj\I5R81D\IVOR_c2_handlers_GHS.dbo
                --gh_oname=obj\I5R81D\IVOR_c2_handlers_GHS.o
                --gh_md=obj\I5R81D\IVOR_c2_handlers_GHS.d -asm3g
                -asm3g_driver=C:\ghs\comp_201516\ccppc
                -asm3g_args=@@obj\I5R81D\IVOR_c2_handlers_GHS.a3g
                -accept_unsafe_op_names -o obj\I5R81D\IVOR_c2_handlers_GHS.o
                -list=obj\I5R81D\IVOR_c2_handlers_GHS.lst
                ..\tree\BIOS\STARTUP\IVOR_c2_handlers_GHS.s
Source File:    ..\tree\BIOS\STARTUP\IVOR_c2_handlers_GHS.s
Directory:      C:\Users\<USER>\Desktop\Bugatti\EISB8C_SI_03_RonDetectFuel\GHS
Host OS:        Windows
AS: Copyright (C) 1983-2015 Green Hills Software.  All Rights Reserved.
Release: Compiler v2015.1.6
Build Directory: [Directory] COMP-VAL-WIN31:l:/compiler/release-branch-2015-1-comp/build/v2015.1-2015-10-18/win32-comp-ecom
Revision: [VCInfo] http://toolsvc/branches/release-branch-70/src@543895 (built by auto-compiler)
Revision Date: Mon Oct 19 08:19:32 2015

Release Date: Mon Oct 19 11:04:55 2015

                             1	#**************************************************************************/
                             2	#* FILE NAME: intc_sw_handlers.s            COPYRIGHT (c) Freescale 2004  */
                             3	#*                                                All Rights Reserved     */
                             4	#* DESCRIPTION:                                                           */
                             5	#*        This file creates prolog, epilog for C ISR and enables nested   */
                             6	#*        interrupts. This file starts in memory at the beginning of the  */
                             7	#*        ".xcptn" section designated by the label "IVOR4Handler".        */
                             8	#* WARNING:  This stack frame does not save the SPE s Accumulator, which  */
                             9	#*           is required if SPE instructions are used in ISRs.   If SPE   */
                            10	#*           instructions are used, the stack frame must include the      */
                            11	#*           accumulator, and prologue and epilogue must be modified.     */
                            12	#=========================================================================*/
                            13	#*                                                                        */
                            14	#* REV      AUTHOR       DATE       DESCRIPTION OF CHANGE                 */
                            15	#* ---   -----------   ----------   ---------------------                 */
                            16	#* 1.0:  S. Mihalik    23/Apr/04     Initial version                      */
                            17	#* 1.1:  B. Terry      29/Jul/04    Modified read of IACKR using          */
                            18	#*                                  pointer to determine vector  address. */
                            19	#* 1.2   G. Jackson    30/Jul/04    Added ".xcptn" section designation    */
                            20	#*                                   for placement into mpc5500cfg.       */
                            21	#* 1.3   G. Jackson    12/Oct/04    Green Hills now does not require      */
                            22	#*                                    quotation marks around the section  */
                            23	#*                                  Added syntax to generate symbols for  */
                            24	#*                                    debug.                              */
                            25	#**************************************************************************/
                            26	
                            27	// Designation for the Green Hills compiler
                            28	
                             0*	    .include "../tree/COMMON/CONFIG/ASM/mpc5500_usrdefs.inc"
                             1*	#************************************************************************
                             2*	#* FILE NAME: mpc5500_usrdefs.inc            COPYRIGHT (c) Freescale 2004 
                             3*	#*                                                All Rights Reserved     
                             4*	#* DESCRIPTION:                                                           
                             5*	#* This file contains user definitions for the MPC5500 assembly functions.
                             6*	#* The user will only need to make changes to this file for the assembly
                             7*	#*  portion of this code.
                             8*	#* 
                             9*	#*========================================================================

                                   Mon Jun 02 14:54:43 2025           Page 2
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                            10*	#* ORIGINAL AUTHOR: G. Jackson           
                            11*	#* REV      AUTHOR        DATE       DESCRIPTION OF CHANGE                
                            12*	#* ---   -----------   -----------   ---------------------                  
                            13*	#* 0.1   G. Jackson     12/Apr/04    Initial version        
                            14*	#* 0.2   G. Jackson     15/Apr/04    Added compiler designations
                            15*	#* 0.3   G. Jackson     13/May/04    Added runtime variables
                            16*	#* 0.4   G. Jackson     06/Jun/04    Added EXT_BOOT config option
                            17*	#* 0.5   G. Jackson     30/Jun/04    Added RCHW variables
                            18*	#* 1.0   G. Jackson     07/Oct/04    Internal and external RAM set to 
                            19*	#*                                    CACHE_INHIBIT (TLBs 3 & 11)
                            20*	#************************************************************************
                             0*	    .include "../tree/COMMON/CONFIG/ASM/mpc5500_defs.inc"
                             1*	#*************************************************************************
                             2*	#* FILE NAME: mpc5500_defs.inc              COPYRIGHT (c) Freescale 2004 
                             3*	#*                                                All Rights Reserved     
                             4*	#* DESCRIPTION:                                                           
                             5*	#* This file contains prototypes and definitions for the MPC5500 
                             6*	#*  assembly functions.
                             7*	#*
                             8*	#*  Users should make no changes in this file. 
                             9*	#*  User defines are in mpc5500_usrdefs.inc
                            10*	#*========================================================================
                            11*	#* ORIGINAL AUTHOR: G. Jackson           
                            12*	#* REV      AUTHOR        DATE       DESCRIPTION OF CHANGE                
                            13*	#* ---   -----------   -----------   ---------------------                  
                            14*	#* 0.1   G. Jackson     26/Mar/04    Set up assembly definitions     
                            15*	#* 0.2   G. Jackson     15/Apr/04    Add SRAM address definitions    
                            16*	#* 0.3   G. Jackson     13/May/04    Removed 20MSB address definitions
                            17*	#* 1.0   G. Jackson     25/May/04    Changed OPCNT_OFFSET to IP_ADVANCE
                            18*	#* 1.1   R. Dees        25/May/04    Added more comments
                            19*	#* 1.2   G. Jackson     30/Jun/04    Added RCHW constants
                            20*	#*************************************************************************
                            21*	
                            22*	#*************************************************************
                            23*	#*************************************************************
                            24*	# User should not modify any of the definitions below
                            25*	
                            26*	#************************************************************************
                            27*	#                            Definitions                                 
                            28*	#************************************************************************
                            29*	
                            30*	# Base addresses
                            31*	    .equ FLASH_BASE_ADDR,     0x00000000
                            32*	    .equ SRAM_BASE_ADDR,      0x40000000            ## INT_SRAM_BASE
                            33*	#   .equ INT_SRAM_SIZE,       0x0000C000            ## MPC5632M
                            34*	#   .equ INT_SRAM_SIZE,       0x00010000            ## MPC5633M
                            35*	    .equ INT_SRAM_SIZE,       0x00017800            ## MPC5634M
                            36*	    .equ INT_SRAM_128BYTSEGS, (INT_SRAM_SIZE >> 7)  ## Number of 128 byte segments
                            37*	    .equ SHDW_BLK_ADDR,       0x00FFC000            ## Shadow Block starts at 0xFF_FC00 for 1K size
                            38*	    .equ BAM_BASE_ADDR,       0x11300000
                            39*	    .equ EXTMEM1_BASE_ADDR,   0x20000000
                            40*	    .equ EXTMEM2_BASE_ADDR,   0x3FF80000
                            41*	
                            42*	    .equ PBRIDGEA_BASE_ADDR,  0xC3F00000
                            43*	    .equ EXTBUSINT_BASE_ADDR, 0x20000000
                            44*	    .equ SIU_BASE_ADDR,       0xC3F90000

                                   Mon Jun 02 14:54:43 2025           Page 3
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                            45*	    .equ ETPU_BASE_ADDR,      0xC3FC0000
                            46*	
                            47*	    .equ PBRIDGEB_BASE_ADDR,  0xFFF00000
                            48*	    .equ XBAR_BASE_ADDR,      0xFFF04000
                            49*	    .equ ECSM_BASE_ADDR,      0xFFF40000
                            50*	    .equ EDMA_BASE_ADDR,      0xFFF44000
                            51*	    .equ INTC_BASE_ADDR,      0xFFF48000
                            52*	    .equ EQADC_BASE_ADDR,     0xFFF80000
                            53*	
                            54*	    .equ EXTBUSINT_PHY_ADDR,  0x00000000
                            55*	
                            56*	# Size for defined address spaces
                            57*	    .equ SIZE_4M,    0x00400000  #   4 MB
                            58*	    .equ SIZE_1M,    0x00100000  #   1 MB
                            59*	    .equ SIZE_768K,  0x000C0000  # 768 KB 
                            60*	    .equ SIZE_512K,  0x00080000  # 512 KB 
                            61*	    .equ SIZE_256K,  0x00040000  # 256 KB 
                            62*	    .equ SIZE_64K,   0x00010000  #  64 KB 
                            63*	    .equ SIZE_48K,   0x0000C000  #  48 KB 
                            64*	    .equ SIZE_4K,    0x00001000  #   4 KB
                            65*	
                            66*	#*************** Reset Configuration Half Word constants *********
                            67*	    .equ WDOG_DISABLE, 0x00000000  # Watchdog is default disabled
                            68*	    .equ WDOG_ENABLE,  0x04000000  # Watchdog is write once enabled
                            69*	    .equ CS0_32BIT,    0x00000000  # CS0 external data bus is 32-bits
                            70*	    .equ CS0_16BIT,    0x02000000  # CS0 external data bus is 16-bits
                            71*	    .equ MPC5500_ID,   0x005A0000  # RCHW boot ID for MPC5500 devices
                            72*	
                            73*	#*************** Cache initialization constants **************
                            74*	# Definitions for the L1CSR1 (SPR1011) (L1 Cache control and Status Register 1)
                            75*	# bit reference: L1CSR1 ((ICECE<<16)|(ICEI<<15)|(ICLOC<<13)|(ICEA<<5)|(ICLOINV<<4)|(ICABT<<2)|(ICINV<<1)|(ICE)) 
                            76*	# Fields used for L1CSR1 (L1 Cache Control and Status Register 1)
                            77*	# ICE = Instruction Cache Enable = bit [31]
                            78*	    .equ ICE_ENABLE,   0x00000001  # 0x1
                            79*	    .equ ICE_DISABLE,  0x00000000  # 0x0
                            80*	# ICINV = Instruction Cache Invalidate = bit [30]
                            81*	    .equ ICINV_INV_OP, 0x00000002  # 0x1
                            82*	    .equ ICINV_NO_OP,  0x00000000  # 0x0
                            83*	# ICABT = Instruction Cache Operation Aborted = bit [29]
                            84*	    .equ ICABT_ABORT_OP,       0x00000004  # 0x1
                            85*	    .equ ICINV_CLEANABORT_OP,  0x00000000  # 0x0
                            86*	# ICLOINV = Instruction Cache Lockout Indicator Invalidate = bit [27]
                            87*	    .equ ICLOINV_INV_OP, 0x00000010  # 0x1
                            88*	    .equ ICLOINV_NO_OP,  0x00000000  # 0x0
                            89*	# ICEA = Instruction Cache Error Action = bit [25-26]
                            90*	    .equ ICHECKERR_EXCEN,  0x00000000  # 0x0
                            91*	    .equ ICHECKERR_AUTCOR, 0x00000020  # 0x1
                            92*	# ICLOC = Instruction Cache Lockout Control = bit [17:18]
                            93*	    .equ ICLOC_DISABLE, 0x00000000           # 0x0
                            94*	    .equ ICLOC_ENABLE_NOEXC,  0x00002000     # 0x1
                            95*	    .equ ICLOC_ENABLE_EXCEN,  0x00004000     # 0x2
                            96*	    .equ ICLOC_ENABLE_EXCENTAG,  0x00006000  # 0x3
                            97*	# ICEI = Instruction Cache Error Injection Enable = bit [16], for test purpose
                            98*	    .equ ICERR_INJDIS,  0x00000000  # 0x0
                            99*	    .equ ICERR_INJEN,   0x00008000  # 0x1
                           100*	# ICECE = Cache Error Checking Enable = bit [15]

                                   Mon Jun 02 14:54:43 2025           Page 4
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           101*	    .equ ICHECKERR_DISABLE,  0x00000000  # 0x0
                           102*	    .equ ICHECKERR_ENABLE,   0x00010000  # 0x1
                           103*	
                           104*	#*************** FMPLL initialization constants **************
                           105*	# Define the address for the FMPLL registers 
                           106*	    .equ FMPLL_SYNCRREG, 0xC3F80000
                           107*	    .equ FMPLL_SYNSRREG, 0xC3F80004
                           108*	    .equ SIU_ECCRREG,    0xC3F90984
                           109*	    .equ SIU_SRCRREG,    0xC3F90010
                           110*	
                           111*	#*************************************************************
                           112*	# Definitions for FMPLL_SYNCR (FMPLL Synthesizer Control Register) 
                           113*	# bit reference: SYNCR((MFD<<23)|(RFD<<19)|(locen<<18)|
                           114*	#                 (lolre<<17)|(locre<<16)|(disclk<<15)|(lolirq<<14)|
                           115*	#                 (locirq<<13)|(rate<<12)|(depth<<10)|(exp))
                           116*	
                           117*	# Fields used for PREDIV (Pre-Divider, bits [1:3]) 
                           118*	    .equ PREDIV_1,  0x00000000  # 0x0
                           119*	    .equ PREDIV_2,  0x10000000  # 0x1
                           120*	    .equ PREDIV_3,  0x20000000  # 0x2
                           121*	    .equ PREDIV_4,  0x30000000  # 0x3
                           122*	    .equ PREDIV_5,  0x40000000  # 0x4
                           123*	    .equ PREDIV_6,   0x50000000  # 0x5
                           124*	    .equ PREDIV_7,   0x60000000  # 0x6
                           125*	    .equ PREDIV_INH, 0x70000000  # 0x7 clock inhibit
                           126*	
                           127*	# Fields used for MFD (Muliplication Factor Divider, bits [4:8]) 
                           128*	    .equ MFD_8,  0x02000000  # 0x4
                           129*	    .equ MFD_9,  0x02800000  # 0x5
                           130*	    .equ MFD_10, 0x03000000  # 0x6
                           131*	    .equ MFD_11, 0x03800000  # 0x7
                           132*	    .equ MFD_12, 0x04000000  # 0x8
                           133*	    .equ MFD_13, 0x04800000  # 0x9
                           134*	    .equ MFD_14, 0x05000000  # 0xA
                           135*	    .equ MFD_15, 0x05800000  # 0xB
                           136*	    .equ MFD_16, 0x06000000  # 0xC
                           137*	    .equ MFD_17, 0x06800000  # 0xD
                           138*	    .equ MFD_18, 0x07000000  # 0xE
                           139*	    .equ MFD_19, 0x07800000  # 0xF
                           140*	    .equ MFD_20, 0x08000000  # 0x10
                           141*	    .equ MFD_21, 0x08800000  # 0x11
                           142*	    .equ MFD_22, 0x09000000  # 0x12
                           143*	    .equ MFD_23, 0x09800000  # 0x13
                           144*	    .equ MFD_24, 0x0a000000  # 0x14
                           145*	
                           146*	# Fields used for RFD (Reduced Frequency Divider, bits [10:12]) 
                           147*	    .equ RFD_1,   0x00000000  # 0x0
                           148*	    .equ RFD_2,   0x00080000  # 0x1
                           149*	    .equ RFD_4,   0x00100000  # 0x2
                           150*	
                           151*	# Fields for LOCEN (Loss-of-clock enable, bit [13]) 
                           152*	    .equ LOCEN_DIS, 0x00000000  # 0x0
                           153*	    .equ LOCEN_EN,  0x00040000  # 0x1
                           154*	
                           155*	# Fields for LOLRE (Loss-of-lock reset enable, bit [14]) 
                           156*	    .equ LOLRE_IGNORE,   0x00000000  # 0x0

                                   Mon Jun 02 14:54:43 2025           Page 5
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           157*	    .equ LOLRE_ASSERT,   0x00020000  # 0x1
                           158*	
                           159*	# Fields for LOCRE (Loss-of-clock reset enable, bit [15]) 
                           160*	    .equ LOCRE_IGNORE,   0x00000000  # 0x0
                           161*	    .equ LOCRE_ASSERT,   0x00010000  # 0x1
                           162*	
                           163*	# Fields for DISCLK (Disable CLKOUT, bit [16]) 
                           164*	    .equ DISCLK_NORMAL, 0x00000000  # 0x0
                           165*	    .equ DISCLK_LOW,    0x00008000  # 0x1
                           166*	
                           167*	# Fields for LOLIRQ (Loss-of-lock interrupt request, bit [17]) 
                           168*	    .equ LOLIRQ_NOT_REQUESTED, 0x00000000  # 0x0
                           169*	    .equ LOLIRQ_REQUESTED,     0x00004000  # 0x1
                           170*	
                           171*	# Fields for LOCIRQ (Loss-of-clock interrupt request, bit [18]) 
                           172*	    .equ LOCIRQ_NOT_REQUESTED, 0x00000000  # 0x0
                           173*	    .equ LOCIRQ_REQUESTED,     0x00002000  # 0x1
                           174*	
                           175*	# Fields for RATE (Modulation rate, bit [19]) 
                           176*	    .equ RATE_FREF_80, 0x00000000  # 0x0
                           177*	    .equ RATE_FREF_40, 0x00001000  # 0x1
                           178*	
                           179*	# Fields for DEPTH (Modulation depth percentage, bits [20:21])
                           180*	    .equ DEPTH_0, 0x00000000  # 0x0
                           181*	    .equ DEPTH_1, 0x00000400  # 0x1
                           182*	    .equ DEPTH_2, 0x00000800  # 0x2
                           183*	
                           184*	# Fields for EXP (Expected difference, bits [22:31])
                           185*	    .equ EXP_0, 0x00000000  # 0x0
                           186*	
                           187*	#*************************************************************
                           188*	# Definitions for the FMPLL_SYNSR (Synthesizer Status Register) 
                           189*	# bit reference: SYNSR ((lolf<<9)|(locf<<2))
                           190*	
                           191*	# Fields for LOLF (Loss-of-lock flag, bit [22]) 
                           192*	    .equ LOLF_NO_CHANGE, 0x00000000  # 0x0
                           193*	    .equ LOLF_CLEAR,     0x00000200  # 0x1
                           194*	
                           195*	# Fields for LOCF (Loss-of-clock flag, bit [29]) 
                           196*	    .equ LOCF_NO_CHANGE, 0x00000000  # 0x0
                           197*	    .equ LOCF_CLEAR,     0x00000004  # 0x1
                           198*	
                           199*	#*************************************************************
                           200*	# Definitions for the SIU_ECCR (External Clock Control Register) 
                           201*	# bit reference: ECCR((ENGDIV<<8)|(EBTS<<3)|(EBDF)) 
                           202*	    .equ ECCRREG, 0xC3F90984  # ECCR register address
                           203*	
                           204*	# Fields for ENGDIV (Engineering clock values, bits [18:23]) 
                           205*	    .equ ENGDIV_BY128, 0x00003F00  # 0x3F
                           206*	
                           207*	# Fields for EBTS (external signals hold time, bit [28]) 
                           208*	    .equ EBTS_NO_HOLD, 0x00000000  # 0x0
                           209*	    .equ EBTS_HOLD,    0x00000004  # 0x1
                           210*	
                           211*	# Fields for EBDF (CLKOUT divides, bits [30:31]) 
                           212*	    .equ EBDF_DIVBY2, 0x00000001  # 0x1

                                   Mon Jun 02 14:54:43 2025           Page 6
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           213*	    .equ EBDF_DIVBY4, 0x00000002  # 0x2
                           214*	
                           215*	#*************************************************************
                           216*	# Definitions for the SIU_SRCR (System Reset Control Register) 
                           217*	# bit reference: SIU_SRCR ((SSR<<31)|(SER<<30)|(CRE<<15))
                           218*	
                           219*	# Fields for SSR (software system reset, bit [0]) 
                           220*	    .equ SSR_NORST,  0x00000000  # 0x0
                           221*	    .equ SSR_SYSRST, 0x80000000  # 0x1
                           222*	
                           223*	# Fields for SER (external system reset, bit [1]) 
                           224*	    .equ SER_NORST, 0x00000000  # 0x0
                           225*	    .equ SER_XRST,  0x40000000  # 0x1
                           226*	
                           227*	# Fields for CRE (checkstop reset enable, bit [16]) 
                           228*	    .equ CRE_NO, 0x00000000  # 0x0
                           229*	    .equ CRE_EN, 0x00008000  # 0x1
                           230*	
                           231*	
                           232*	#*************** Flash initialization constants ****************
                           233*	
                           234*	# Definitions for FLASH_BIUCR (Flash BIU Control Register) 
                           235*	# bit reference: FLSHBIUCR ((M3PFE<<19)|(M2PFE<<18)|
                           236*	#               (M1PFE<<17)|(M0PFE<<16)|(APC<<13)|(WWSC<<11)|
                           237*	#               (RWSC<<8)|(DPFEN<<6)|(IPFEN<<4)|(PFLIM<<1)|(BFEN))
                           238*	
                           239*	# Fields for Flash Bus Interface Control 
                           240*	# Fields for Prefetch Control (MnPFE Master n Prefetch Enable)
                           241*	# Fields for M3PFE (Master 3 (EBI) prefetch enable, bit [12])
                           242*	    .equ EBI_PREFTCH_OFF,  0x00000000  # 0x0
                           243*	    .equ EBI_PREFTCH_ON,   0x00080000  # 0x1
                           244*	# Fields for M2PFE (Master 2 (eDMA) prefetch enable, bit [13])
                           245*	    .equ EDMA_PREFTCH_OFF, 0x00000000  # 0x0
                           246*	    .equ EDMA_PREFTCH_ON,  0x00040000  # 0x1
                           247*	# Fields for M1PFE (Master 1 (Nexus) prefetch enable, bit [14])
                           248*	    .equ NEX_PREFTCH_OFF,  0x00000000  # 0x0
                           249*	    .equ NEX_PREFTCH_ON,   0x00020000  # 0x1
                           250*	# Fields for M0PFE (Master 0 (e200Z6 core) prefetch enable, bit [15])
                           251*	    .equ CPU_PREFTCH_OFF,  0x00000000  # 0x0
                           252*	    .equ CPU_PREFTCH_ON,   0x00010000  # 0x1
                           253*	
                           254*	# Fields for APC (access pipelining control, bits [16:18]) 
                           255*	    .equ APC_1,  0x00002000  # 0x1
                           256*	    .equ APC_2,  0x00004000  # 0x2
                           257*	    .equ APC_3,  0x00006000  # 0x3
                           258*	    .equ APC_4,  0x00008000  # 0x4
                           259*	    .equ APC_5,  0x0000A000  # 0x5
                           260*	    .equ APC_6,  0x0000C000  # 0x6
                           261*	    .equ APC_NO, 0x0000E000  # 0x7
                           262*	
                           263*	# Fields for WWSC (write wait state control, bits [19:20]) 
                           264*	    .equ WWSC_1, 0x00000800  # 0x1
                           265*	    .equ WWSC_2, 0x00001000  # 0x2
                           266*	    .equ WWSC_3, 0x00001800  # 0x3
                           267*	
                           268*	# Fields for RWSC (read wait state control, bits [21:23]) 

                                   Mon Jun 02 14:54:43 2025           Page 7
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           269*	    .equ RWSC_0, 0x00000000  # 0x0
                           270*	    .equ RWSC_1, 0x00000100  # 0x1
                           271*	    .equ RWSC_2, 0x00000200  # 0x2
                           272*	    .equ RWSC_3, 0x00000300  # 0x3
                           273*	    .equ RWSC_4, 0x00000400  # 0x4
                           274*	    .equ RWSC_5, 0x00000500  # 0x5
                           275*	    .equ RWSC_6, 0x00000600  # 0x6
                           276*	    .equ RWSC_7, 0x00000700  # 0x7
                           277*	
                           278*	# Fields for DPFEN (data prefetch enable, bits [24:25]) 
                           279*	    .equ DPFEN_NO,   0x00000000  # 0x0
                           280*	    .equ DPFEN_BRST, 0x00000040  # 0x1
                           281*	    .equ DPFEN_ANY,  0x000000C0  # 0x3
                           282*	
                           283*	# Fields for IPFEN (instruction prefetch enable, bits [26:27]) 
                           284*	    .equ IPFEN_NO,   0x00000000  # 0x0
                           285*	    .equ IPFEN_BRST, 0x00000010  # 0x1
                           286*	    .equ IPFEN_ANY,  0x00000030  # 0x3
                           287*	
                           288*	# Fields for PFLIM (additional line prefetch (limit), bits [28:30])
                           289*	    .equ PFLIM_0,  0x00000000  # 0x0 
                           290*	    .equ PFLIM_1,  0x00000002  # 0x1
                           291*	    .equ PFLIM_2,  0x00000004  # 0x2
                           292*	    .equ PFLIM_3,  0x00000006  # 0x3
                           293*	    .equ PFLIM_4,  0x00000008  # 0x4
                           294*	    .equ PFLIM_5,  0x0000000A  # 0x5
                           295*	    .equ PFLIM_U,  0x0000000C  # 0x6
                           296*	
                           297*	# Fields for BFEN (enable line read buffer hits, bit [31]) 
                           298*	    .equ BFEN_DIS, 0x00000000  # 0x0
                           299*	    .equ BFEN_EN,  0x00000001  # 0x1
                           300*	
                           301*	# Define the address for the Flash BIUCR register
                           302*	    .equ FLASH_BIUCRREG, 0xC3F8801C 
                           303*	
                           304*	# set up for errata 105 flash access by all masters.
                           305*	    .equ FLASHACCESS,  0xFFFF
                           306*	    .equ FLASH_BIUAPR, 0xC3F88020
                           307*	
                           308*	#*************************************************************
                           309*	# Definitions for CS0OR0 (Chip Select 0 option register) 
                           310*	# bit reference: CS0OR0AVAL ((AM<<12)|(SCY<<4)|(BSCY<<1))
                           311*	
                           312*	# Fields for CS0OR0
                           313*	# Fields for AM (Address Mask, bits [0:16] ([0:2]=b111))
                           314*	    .equ AMASK_8M,  0xFF800000 # 0xFF800; 8M space  
                           315*	# Fields for SCY (Primary wait states, bits [24:27])
                           316*	    .equ OR0SCY_1,  0x00000010 # 0x1    
                           317*	    .equ OR0SCY_2,  0x00000020 # 0x2    
                           318*	    .equ OR0SCY_3,  0x00000030 # 0x3    
                           319*	    .equ OR0SCY_4,  0x00000040 # 0x4  
                           320*	    .equ OR0SCY_5,  0x00000050 # 0x5
                           321*	# Fields for BSCY (Secondary wait states, bits [29:30])
                           322*	    .equ OR0BSCY_0, 0x00000000 # 0x0
                           323*	    .equ OR0BSCY_1, 0x00000002 # 0x1
                           324*	    .equ OR0BSCY_2, 0x00000004 # 0x2

                                   Mon Jun 02 14:54:43 2025           Page 8
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           325*	    .equ OR0BSCY_3, 0x00000006 # 0x3
                           326*	
                           327*	# Define the addresses for the Chip Select 0 registers
                           328*	    .equ CS0BRREG,   0xC3F84010
                           329*	    .equ CS0ORREG,   0xC3F84014
                           330*	
                           331*	#*************************************************************
                           332*	# Define parameter variables for SRAM download control
                           333*	# Starting SRAM address defined in the linker file 
                           334*	#     __SRAM_LOAD set to ADDR(.heap) defined in the linker file 
                           335*	# Number of opcodes to be loaded defined in the linker file
                           336*	#     __SRAM_LOAD_SIZE set to SIZEOF(.flash_data) defined in the linker file
                           337*	# Instruction pointer advance to next loaded opcode loaded into SRAM
                           338*	    .equ IP_ADVANCE,  4   # Offset to next opcode loaded into SRAM
                           339*	
                           340*	# Copy of ROM data into ".data" and ".sdata" sections of SRAM
                           341*	#   __DATA_ROM set to ROM address for loading into ".data" of SRAM
                           342*	#   __SDATA_ROM set to ROM address for loading into ".sdata" of SRAM
                           343*	#   __ROM_COPY_SIZE number of bytes to be loaded into SRAM from ROM
                           344*	    .equ CPY_OFFSET,  1   # Byte offset to next address and decrement 
                           345*	#                         #  the ROM_COPY_SIZE
                           346*	
                           347*	#*************** MMU initialization constants ****************
                           348*	
                           349*	#********* TLB address offsets *********
                           350*	
                           351*	# Offset addresses for use in defining beginning of flash blocks
                           352*	    .equ OFFSET_1M,    0x00100000  #   1 MB 
                           353*	    .equ OFFSET_768K,  0x000C0000  # 768 KB 
                           354*	    .equ OFFSET_512K,  0x00080000  # 512 KB 
                           355*	    .equ OFFSET_256K,  0x00040000  # 256 KB 
                           356*	    .equ OFFSET_64K,   0x00010000  #  64 KB 
                           357*	    .equ OFFSET_4K,    0x00001000  #   4 KB 
                           358*	
                           359*	# Definitions for MMU (Memory Management Unit Registers) 
                           360*	
                           361*	#*************************************************************
                           362*	# Definitions for MAS0 (MMU Assist Register 0) 
                           363*	# bit reference: MAS0AVAL ((tlbsel<<28)|(eselcam<<16))
                           364*	
                           365*	# Fields for MAS0 (MMU Assist Register 0 ) 
                           366*	# Field for TLBSEL  (TLB Select, bits [2:3]) 
                           367*	    .equ TLB_SELECT,  0x10000000   # Always 0x01 for MPC5554
                           368*	# Fields for ESELCAM (Entry Select for TLB CAM, bits [11:15])
                           369*	    .equ TLB_ENTRY0,  0x00000000   # Select TLB Entry slot 0
                           370*	    .equ TLB_ENTRY1,  0x00010000   # Select TLB Entry slot 1
                           371*	    .equ TLB_ENTRY2,  0x00020000   # Select TLB Entry slot 2
                           372*	    .equ TLB_ENTRY3,  0x00030000   # Select TLB Entry slot 3  
                           373*	    .equ TLB_ENTRY4,  0x00040000   # Select TLB Entry slot 4  
                           374*	    .equ TLB_ENTRY5,  0x00050000   # Select TLB Entry slot 5  
                           375*	    .equ TLB_ENTRY6,  0x00060000   # Select TLB Entry slot 6  
                           376*	    .equ TLB_ENTRY7,  0x00070000   # Select TLB Entry slot 7  
                           377*	    .equ TLB_ENTRY8,  0x00080000   # Select TLB Entry slot 8  
                           378*	    .equ TLB_ENTRY9,  0x00090000   # Select TLB Entry slot 9  
                           379*	    .equ TLB_ENTRY10, 0x000A0000   # Select TLB Entry slot 10 
                           380*	    .equ TLB_ENTRY11, 0x000B0000   # Select TLB Entry slot 11 

                                   Mon Jun 02 14:54:43 2025           Page 9
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           381*	    .equ TLB_ENTRY12, 0x000C0000   # Select TLB Entry slot 12 
                           382*	    .equ TLB_ENTRY13, 0x000D0000   # Select TLB Entry slot 13 
                           383*	    .equ TLB_ENTRY14, 0x000E0000   # Select TLB Entry slot 14 
                           384*	    .equ TLB_ENTRY15, 0x000F0000   # Select TLB Entry slot 15 
                           385*	    .equ TLB_ENTRY16, 0x00100000   # Select TLB Entry slot 16 
                           386*	    .equ TLB_ENTRY17, 0x00110000   # Select TLB Entry slot 17 
                           387*	    .equ TLB_ENTRY18, 0x00120000   # Select TLB Entry slot 18 
                           388*	    .equ TLB_ENTRY19, 0x00130000   # Select TLB Entry slot 19 
                           389*	    .equ TLB_ENTRY20, 0x00140000   # Select TLB Entry slot 20 
                           390*	    .equ TLB_ENTRY21, 0x00150000   # Select TLB Entry slot 21 
                           391*	    .equ TLB_ENTRY22, 0x00160000   # Select TLB Entry slot 22 
                           392*	    .equ TLB_ENTRY23, 0x00170000   # Select TLB Entry slot 23 
                           393*	
                           394*	#*************************************************************
                           395*	# Definitions for MAS1 (MMU Assist Register 1) 
                           396*	# bit reference: MAS1AVAL ((valid<<31)| (iprot<<30)|
                           397*	#                      (tid<<16)|(ts<<12)|(tsize<<8))
                           398*	
                           399*	# Fields for MAS1 (MMU Assist Register 1 ) 
                           400*	# Field for V (Valid, bit [0])
                           401*	    .equ TLB_ENTRY_INVALID,   0x00000000  # 0x0   
                           402*	    .equ TLB_ENTRY_VALID,     0x80000000  # 0x1   
                           403*	# Field for IPROT (Invalidation Protection, bit [1]) 
                           404*	    .equ ENTRY_NOT_PROTECTED, 0x00000000  # 0x0 # From Invalidation  
                           405*	    .equ ENTRY_PROTECTED,     0x40000000  # 0x1 # From Invalidation  
                           406*	# Field for TID (Process ID, bits [8:15])
                           407*	    .equ GLOBAL_MATCH,        0x00000000  # 0x0 # Match all process IDs  
                           408*	# Field for TS (Translation address space, bit [19])
                           409*	    .equ TS_IS_COMPARE,       0x00000000  # 0x0 # Match entry to instruction space  
                           410*	    .equ TS_DS_COMPARE,       0x00001000  # 0x1 # Match entry to data space 
                           411*	# Field for TSIZE (TLB entry page size, bits [20:23])
                           412*	    .equ TSIZ_4K,    0x00000100  # 0x1 # TLB page size =  4K bytes 
                           413*	    .equ TSIZ_16K,   0x00000200  # 0x2 # TLB page size = 16K bytes 
                           414*	    .equ TSIZ_64K,   0x00000300  # 0x3 # TLB page size = 64K bytes 
                           415*	    .equ TSIZ_256K,  0x00000400  # 0x4 # TLB page size =256K bytes 
                           416*	    .equ TSIZ_1M,    0x00000500  # 0x5 # TLB page size =  1M bytes 
                           417*	    .equ TSIZ_2M,    0x00000580  # 0x58 # TLB page size =  2M bytes 
                           418*	    .equ TSIZ_4M,    0x00000600  # 0x6 # TLB page size =  4M bytes 
                           419*	    .equ TSIZ_16M,   0x00000700  # 0x7 # TLB page size = 16M bytes 
                           420*	    .equ TSIZ_64M,   0x00000800  # 0x8 # TLB page size = 64M bytes 
                           421*	    .equ TSIZ_128K,  0x00000380  # 0x38 # TLB page size = 128K bytes    
                           422*	    .equ TSIZ_256M,  0x00000900  # 0x9 # TLB page size = 256M bytes 
                           423*	#*************************************************************
                           424*	# Definitions for MAS2 (MMU Assist Register 2) 
                           425*	# bit reference: MAS2AVAL((epn<<12)|(w<<4)|(i << 3)|
                           426*	#                         (m << 2)|(g << 1)|(e)) 
                           427*	
                           428*	# Fields for MAS2 (MMU Assist Register 2 ) 
                           429*	# The EPN field is defined above with global address space 
                           430*	# EPN (effective page number, bits [0:19]) 
                           431*	# Field for W (Write-through required, bit [27]) 
                           432*	    .equ CACHE_WRITE_BACK, 0x00000000  # 0x0 # Stores write-back to cache 
                           433*	    .equ CACHE_WRITE_THRU, 0x00000010  # 0x1 # Stores write through to memory    
                           434*	    .equ CACHE_ACTIVE,     0x00000000  # 0x0 # Cache is active for TLB entry 
                           435*	# Field for I (Cache Inhibited, bit [28])
                           436*	    .equ CACHE_INHIBIT,    0x00000008  # 0x1 # Cache is inhibited for TLB entry 

                                   Mon Jun 02 14:54:43 2025           Page 10
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           437*	# Field for M (Memory Coherence required, bit [29])
                           438*	    .equ MEM_NO_COHERENCE, 0x00000000  # 0x0 # Only valid setting for MPC5554 
                           439*	    .equ MEM_COHERENCE,    0x00000004  # 0x1 # Not valid--ignored on MPC5554 
                           440*	# Field for G (Page Guarded, bit [30])
                           441*	    .equ PAGE_NOT_GUARDED, 0x00000000  # 0x0 # Cache page not guarded  
                           442*	    .equ PAGE_GUARDED,     0x00000002  # 0x1 # Cache page guarded   
                           443*	# Field for E (Endianess, bit [31])
                           444*	    .equ PAGE_BIG_ENDIAN,  0x00000000  # 0x0 # Big endian byte order 
                           445*	    .equ PAGE_LTL_ENDIAN,  0x00000001  # 0x1 # True little endian byte order  
                           446*	
                           447*	#*************************************************************
                           448*	# Definitions for MAS3 (MMU Assist Register 3) 
                           449*	# bit reference: MAS3AVAL ((rpn<<12)|(permissions))
                           450*	
                           451*	# Fields for MAS3 (MMU Assist Register 3 ) 
                           452*	# The RPN field is defined above with global address space 
                           453*	# RPN == real page number 
                           454*	
                           455*	# Field for U0-U3 (USR bits, bits [22:25])
                           456*	    .equ MAS3_USR0,      0x00000000  # 0x0  # User bit value =0000 
                           457*	# Field for UX (User Execute Access, bit [26])   
                           458*	    .equ USR_NO_EXECUTE, 0x00000000  # 0x0  # User cannot execute code 
                           459*	    .equ USR_EXECUTE,    0x00000020  # 0x1  # User executable permission 
                           460*	# Field for SX (Supervisor Execute Access, bit [27])
                           461*	    .equ SUP_NO_EXECUTE, 0x00000000  # 0x0  # Supervisor cannot execute code 
                           462*	    .equ SUP_EXECUTE,    0x00000010  # 0x1  # Supervisor executable permission
                           463*	# Field for UW (User Write Access, bit [28]) 
                           464*	    .equ USR_NO_WRITE,   0x00000000  # 0x0  # User cannot write code 
                           465*	    .equ USR_WRITE,      0x00000008  # 0x1  # User write permission 
                           466*	# Field for SW (Supervisor Write Access, bit [29])
                           467*	    .equ SUP_NO_WRITE,   0x00000000  # 0x0  # Supervisor cannot write code 
                           468*	    .equ SUP_WRITE,      0x00000004  # 0x1  # Supervisor write permission 
                           469*	# Field for UR (User Read Access, bit [30])
                           470*	    .equ USR_NO_READ,    0x00000000  # 0x0  # User cannot read code 
                           471*	    .equ USR_READ,       0x00000002  # 0x1  # User read permission 
                           472*	# Field for SR (Supervisor Read Access, bit [31])
                           473*	    .equ SUP_NO_READ,    0x00000000  # 0x0  # Supervisor cannot read code 
                           474*	    .equ SUP_READ,       0x00000001  # 0x1  # Supervisor read permission 
                           475*	
                           476*	# Field to define special cases - all access and read+execute, bits [26:31]
                           477*	    .equ READWRITE,        0x0000000F  # 0x0f read, write permission only
                           478*	    .equ READWRITEEXECUTE, 0x0000003F  # 0x3f read, write and execute permission       
                           479*	    .equ READEXECUTE,      0x00000033  # 0x33 read and execute permission only        
                           480*	# bits [0:19] = RPN: Real page number. Same format as EPN. Use RPN        
                           481*	#                   identical to EPN if identical mapping of     
                           482*	#                   effective to physical address.                                         
                           483*	# bits [26:31] = (UX,SX,UW,SW,UR,SR):                                      
                           484*	#             Permission bits. User and supervisor read,                 
                           485*	#             write, and execute permission bits. See                    
                           486*	#             Table 10-3 [1] for more information on                     
                           487*	#             the page permission bits as they are                       
                           488*	#              defined by Book E.                                        
                           489*	#                                                                        
                           490*	
                           491*	#**************************************************************************
                           492*	#           Set up  ERRLOG save error log register                         

                                   Mon Jun 02 14:54:43 2025           Page 11
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           493*	#**************************************************************************
                           494*	
                           495*	# Definitions for ERRLOG (Error Log Register) 
                           496*	
                           497*	# Fields for Error Log Register 
                           498*	    .equ MFDERR_N,     0x00000000  # 0x0
                           499*	    .equ MFDERR_Y,     0x80000000  # 0x1
                           500*	    .equ LOCINTERR_N,  0x00000000  # 0x0
                           501*	    .equ LOCINTERR_Y,  0x40000000  # 0x1
                           502*	    .equ LOLINTERR_N,  0x00000000  # 0x0
                           503*	    .equ LOLINTERR_Y,  0x20000000  # 0x1
                           504*	    .equ PLLNORMERR_N, 0x00000000  # 0x0
                           505*	    .equ PLLNORMERR_Y, 0x10000000  # 0x1
                           506*	
                           507*	# Machine check errors    
                           508*	    .equ ERRLOGCPPER,  0x02000000  # Error log CP_PERR error input
                           509*	    .equ ERRLOGCPER,   0x01000000  # Error log CPERR error input
                           510*	    .equ ERRLOGXCPT,   0x00800000  # Error log exception error input
                           511*	    .equ ERRLOGWRT,    0x00400000  # Error log write error input
                           512*	
                           513*	# Error Log definitions used by cfg_FMPLL and cfg_MCHK_hndlr
                           514*	    .equ ERRLOG_MFDERR, (MFDERR_Y)         # Error on change of FMPLL[MFD]
                           515*	    .equ ERRLOG_LOCINTERR, (LOCINTERR_Y)   # FMPLL fails to lock after LOC interrupt enable
                           516*	    .equ ERRLOG_LOLINTERR, (LOLINTERR_Y)   # FMPLL fails to lock after LOL interrupt enable
                           517*	    .equ ERRLOG_PLLNORMERR, (PLLNORMERR_Y) # FMPLL not in normal mode (loss of clock)
                           518*	    
                           519*	    .equ ERRLOG_PUSHERR, (ERRLOGCPPER)     # Machine check cache push parity error
                           520*	    .equ ERRLOG_PARERR, (ERRLOGCPER)       # Machine check cache parity error
                           521*	    .equ ERRLOG_XERR, (ERRLOGXCPT)         # Machine check from exception error
                           522*	    .equ ERRLOG_WRTERR, (ERRLOGWRT)        # Machine check from write error
                           523*	
                           524*	# Error Log address locations
                           525*	    .equ ERRLOGAD_DMATCD_00,   0xFFF45000  # DMA TCD00
                           526*	    .equ ERRLOGAD_DMATCD_63,   0xFFF457E0  # DMA TCD63
                           527*	    .equ ERRLOGAD_ETPUPRAM_LO, 0xC3FC8000  # Start of eTPU Parameter RAM
                           528*	    .equ ERRLOGAD_ETPUPRAM_HI, 0xC3FC8B00  # Locate at end of eTPU Parameter RAM
                           529*	
                           530*	#**************************************************************
                           531*	
                             0*	    .include "asm_ghs_macros.inc"
                             1*	#************************************************************************
                             2*	#* FILE NAME: asm_ghs_macros.inc            
                             3*	#*
                             4*	#* This file contains macro definitions for PPC ASM to VLE translation
                             5*	#* 
                             6*	#*========================================================================
                             7*	#* GK! 06/12/2011
                             8*	#************************************************************************
                             9*	
                            10*	
                            11*	# macro for subi, use e_add16i with -param 
                            12*	.macro subi RT,RA,SI
                            13*	    e_add16i RT,RA,-SI
                            14*	.endm
                            15*	
                            16*	# ATTENTION: macro for ori, e_or2i uses only 1 register as parameter !!!

                                   Mon Jun 02 14:54:43 2025           Page 12
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                            17*	.macro ori RA,RS,UI
                            18*	    e_or2i RA,UI
                            19*	.endm
                            20*	
                            21*	# macro
                            22*	.macro addic. RA,RS,UI
                            23*	    e_add2i. RA,UI
                            24*	.endm
                            25*	
                            23*	
                            24*	#*************** Runtime Variables *****************
                            25*	#  These runtime variables are used in __start.s
                            26*	# main(), assembly cfg_* calls, and cfg_mpc5500_ccd() need to  **
                            27*	# have far absolute addressing if flashing to ROM with         **
                            28*	# distant addresses on the MPC5500.                            **
                            29*	# The .equ statements below should be set to "1" to be valid   **
                            30*	#  and set to "0" to be inactive.                              **
                            31*	
                            32*	
                            33*	  	.equ SRAM_TEST,        1  # Used to enable SRAM test
                            34*	  	.equ VSRAM_EXIST,      1  # Used to manage the VSRAM memory ram if configured
                            35*	
                            36*	    .equ FAR_ADDRESS,      0  # Used for a FAR_ADDRESS call
                            37*	    .equ FAR_ADDRESS_MAIN, 1  # Used for a FAR_ADDRESS call to main
                            38*	    .equ SIM_VER,          0  # Used with the Code Warrior simulator
                            39*	    .equ VLE_ENABLE,       1  # Enable the VLE instruction set (Pictus has only VLE mode)
                            40*	    
                            41*	    .equ FLSH_RUN,         1  # Set to (1) for code in Flash
                            42*	#                             #     to (0) for code in SRAM
                            43*	
                            44*	    .equ EXT_BOOT,         0  # Set to (1) for External boot. 
                            45*	#                             #  BAM sets up external bus and CS[0]
                            46*	# Reset Configuration Half Word Variables **
                            47*	    .equ RCHW_WTE, WDOG_DISABLE # Watchdog control at reset
                            48*	    .equ RCHW_PS0, CS0_32BIT    # CS0 data port size at reset
                            49*	    .equ BOOT_ID,  MPC5500_ID   # Valid boot ID for MPC5500 devices
                            50*	
                            51*	#*************************************************************
                            52*	#******** Special Initialization Option Constants  ***********
                            53*	# The "I_" prefixed variables are initialization defines      
                            54*	#  Set the value to one ("1") to enable the option.
                            55*	#  Or, set the value to zero ("0") to disable the option.
                            56*	
                            57*	    .equ I_LOCEN,    1 # Set loss of clock enable function
                            58*	    .equ I_BCKUPCLK, 1 # Enable backup clock on loss of clock
                            59*	
                            60*	# Mutually exclusive pair (one set to "0", one set to "1"):
                            61*	    .equ I_LOSSCRST, 0 # Enable reset on loss of clock 
                            62*	    .equ I_LOCINT,   1 # Enable interrupt on loss of clock
                            63*	# Mutually exclusive pair (one set to "0", one set to "1"):
                            64*	    .equ I_LOSSLRST, 0 # Enable reset on loss of lock  
                            65*	    .equ I_LOLINT,   1 # Enable interrupt on loss of lock 
                            66*	
                            67*	
                            68*	# To match MMU entry size:
                            69*	    .equ I_SRAM_SIZE,   SIZE_64K   #   64 KB RAM Size

                                   Mon Jun 02 14:54:43 2025           Page 13
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                            70*	    .equ I_XSRAM_SIZE,  SIZE_512K  #  512 KB External RAM Size
                            71*	    .equ I_XSRAM_SPACE, SIZE_4M    #    4 MB External RAM Space
                            72*	
                            73*	#*************************************************************
                            74*	#      User Defined Options
                            75*	#  These values should be modified based on user requirements
                            76*	#*************************************************************
                            77*	# Cache definitions used by cfg_CACHE and cfg_STACK:
                            78*	#  Copy back mode (CWM=1) and Push buffer disabled (DPB=1) is
                            79*	#   required by errata #32 and #34 to allow MMU control of cache.
                            80*	#   These errata may go away in the future (see current errata)
                            81*	    #.equ CACHE_CLEAR,(CLFC_NO_OP | CINV_INV_OP | CE_DISABLE) ANDORRA
                            82*	    #.equ CACHE_SETTINGS, (CHECKERR_ENABLE | CHECKERR_EDC | CHECKERR_AUTCOR | CORG_32S_4W | CE_ENABLE)  ANDORRA
                            83*	# K2
                            84*	    .equ CACHE_CLEAR,    (ICLOINV_INV_OP | ICINV_INV_OP | ICE_DISABLE)
                            85*	    .equ CACHE_SETTINGS, (ICHECKERR_ENABLE | ICHECKERR_AUTCOR | ICE_ENABLE)   
                            86*	#*******************************************************************
                            87*	# Flash definitions used by cfg_FLASH:
                            88*	#     Internal Flash: FLASH_BIUCR (0xC3F8_801C)    
                            89*	# ap: mod
                            90*	#    .equ FLASH_SETTINGS, (EBI_PREFTCH_ON | APC_3 | WWSC_1 | RWSC_2 | IPFEN_ANY | PFLIM_2 | BFEN_EN)
                            91*	    .equ FLASH_SETTINGS, (EBI_PREFTCH_ON | APC_2 | WWSC_1 | RWSC_2 | IPFEN_ANY | PFLIM_2 | BFEN_EN)
                            92*	#     External Flash: CS0 OR settings used by cfg_FLASH:
                            93*	#     The next line is commented out as an example of optimizing
                            94*	#      external Flash boot times.
                            95*	#    .equ CS0_OR_OPTIONS, (AMASK_8M | OR0SCY_2 | OR0BSCY_0)
                            96*	    
                            97*	#*******************************************************************
                            98*	# FMPLL definitions used by cfg_FMPLL
                            99*	#  Set the internal clock to 32 MHz with MFD=16, and RFD=4.
                           100*	#  Setting 1 is intended to only change the MFD bit with no change to the RFD bit.
                           101*	    .equ FSYS_60,         0
                           102*	    .equ FSYS_80,         1
                           103*	    .equ FSYS_128,        0
                           104*	    
                           105*	    #.if FSYS_128
                           106*	    #.equ FMPLL_SYNCR_SETTING1, (MFD_16 | RFD_4 | LOCEN_EN)  # MFD=16, RFD=4 for  32MHz  #OK???
                           107*	    #.endif
                           108*	
                           109*	    .if FSYS_80
                           110*	    .equ FMPLL_SYNCR_SETTING1, (MFD_8 | PREDIV_3 | RFD_1 | LOCEN_EN)  # for 32 MHz   #OK!!!
                           111*	    .endif
                           112*	    
                           113*	    .if FSYS_60
                           114*	    .equ FMPLL_SYNCR_SETTING1, (MFD_10 | RFD_4 | LOCEN_EN)  # MFD=10, RFD=4 for 30 MHz #OK!!!
                           115*	    .endif
                           116*	
                           117*	#  removed by Akhela Start
                           118*	#  Set the internal clock to 128 MHz with MFD=16, and RFD=1.
                           119*	#  This sequence sets the RFD to divide-by-1 in the FMPLL_SYNCR Register.
                           120*	#  Setting 2 is intended to only change the RFD bit with no change to the MFD bit.
                           121*	#    .equ FMPLL_SYNCR_SETTING2, (MFD_16 | RFD_1 | LOCEN_EN)  # MFD=16, RFD=1 for 128MHz
                           122*	#  removed By Akhela End
                           123*	
                           124*	#  Set the internal clock to 80 MHz with MFD=10, and RFD=1.
                           125*	#  This sequence sets the RFD to divide-by-1 in the FMPLL_SYNCR Register.

                                   Mon Jun 02 14:54:43 2025           Page 14
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           126*	#           Fref * (MFD + 4)
                           127*	#  Fsys = ---------------------
                           128*	#         (PREDIV + 1)* 2^(RFD)
                           129*	
                           130*	    #.if FSYS_128
                           131*	    #.equ FMPLL_SYNCR_SETTING2, (MFD_16 | RFD_1 | LOCEN_EN)  # MFD=16, RFD=1 for 128MHz   #OK???
                           132*	    #.endif
                           133*	
                           134*	    .if FSYS_80
                           135*	    .equ FMPLL_SYNCR_SETTING2, (MFD_20 | PREDIV_3 | RFD_1 | LOCEN_EN)  # MFD_20=16, PREDIV_3=2, RFD_1=0 for 80 MHz #OK!!!
                           136*	    .endif
                           137*	
                           138*	    .if FSYS_60
                           139*	    .equ FMPLL_SYNCR_SETTING2, (MFD_10 | RFD_2 | LOCEN_EN)  # MFD_10=6, RFD_2=1 for 60 MHz  #OK!!!
                           140*	    .endif
                           141*	    
                           142*	#*******************************************************************
                           143*	# SIU definitions used by cfg_FMPLL
                           144*	# The SIU definition below will generate a reset of the device when used.
                           145*	#  A system reset or an external reset will result depending on settings.
                           146*	    .equ SIU_SRCR_SYSRST, (SSR_SYSRST | SER_NORST | CRE_NO)    
                           147*	
                           148*	#*******************************************************************
                           149*	# ERRLOGREG (Error Log Register) address definition
                           150*	    .equ ERRLOGREG, ERRLOGAD_ETPUPRAM_HI # Assembler token address
                           151*	    
                           152*	#*********************************************************************
                           153*	
                           154*	
                           155*	
                           156*	
                           157*	
                            30	
                            31	    .globl   IVOR0_c2
                            32	    .globl   IVOR1_c2
                            33	    .globl   IVOR2_c2
                            34	    .globl   IVOR3_c2
                            35	    .globl   IVOR4_c2
                            36	    .globl   IVOR5_c2
                            37	    .globl   IVOR6_c2
                            38	    .globl   IVOR7_c2
                            39	    .globl   IVOR8_c2
                            40	    .globl   IVOR9_c2
                            41	    .globl   IVOR10_c2
                            42	    .globl   IVOR11_c2
                            43	    .globl   IVOR12_c2
                            44	    .globl   IVOR13_c2
                            45	    .globl   IVOR14_c2
                            46	    .globl   IVOR15_c2
                            47	    .globl   IVOR0Handler_c2
                            48	    .globl   IVOR1Handler_c2
                            49	    .globl   IVOR2Handler_c2
                            50	    .globl   IVOR3Handler_c2
                            51	    .globl   IVOR4Handler_c2
                            52	    .globl   IVOR5Handler_c2
                            53	    .globl   IVOR6Handler_c2

                                   Mon Jun 02 14:54:43 2025           Page 15
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                            54	    .globl   IVOR7Handler_c2
                            55	    .globl   IVOR8Handler_c2
                            56	    .globl   IVOR9Handler_c2
                            57	    .globl   IVOR10Handler_c2
                            58	    .globl   IVOR11Handler_c2
                            59	    .globl   IVOR12Handler_c2
                            60	    .globl   IVOR13Handler_c2
                            61	    .globl   IVOR14Handler_c2
                            62	    .globl   IVOR15Handler_c2
                            63	
                            64	    .extern  IVOR1_Manager_c2              #  defined in Ivor.c file
                            65	    .extern  IVOR_Common_Manager_c2        #  defined in Ivor.c file
                            66	    
                            67	######################################################################################################
                            68	# The following variables are user facilities to detect the type of Ivor # occurred and its own info #
                            69	######################################################################################################
                            70	
                            71	    .extern  IvorIndex_c2                  #  defined in Ivor.c file
                            72	    .extern  CSRR0_Value_c2                #  defined in Ivor.c file
                            73	    .extern  CSRR1_Value_c2                #  defined in Ivor.c file
                            74	    .extern  SRR0_Value_c2                 #  defined in Ivor.c file
                            75	    .extern  SRR1_Value_c2                 #  defined in Ivor.c file
                            76	    .extern  SPR_ESRValue_c2               #  defined in Ivor.c file
                            77	    .extern  SPR_DEARValue_c2              #  defined in Ivor.c file
                            78	    .extern  SPR_MCSRValue_c2              #  defined in Ivor.c file
                            79	    .extern  MCSRR0_Value_c2               #  defined in Ivor.c file
                            80	    .extern  MCSRR1_Value_c2               #  defined in Ivor.c file   
                            81	    
                            82	####################################################
                            83	#   This is the start of the .xcptn_c2 section.    #
                            84	####################################################
                            85	    .section .xcptn_c2,"axv"            // The "ax" generates symbols for debug
                            86	    .vle
                            87	    .align 4                      # Align IVOR handlers on a 16 byte boundary 
                            88	
                            89	    .equ  INTC_CPR_C2,   0xFC040018  # C2 - Current priority register address
                            90	    .equ  INTC_IACKR_C2, 0xFC040028  # C2 - Interrupt Acknowledge Register address
                            91	    .equ  INTC_EOIR_C2,  0xFC040038  # C2 - End Of Interrupt Register address
                            92	
                            93	####################################################
                            94	#                  IVOR Vector                     #
                            95	####################################################
                            96	
00000000 78000000           97	IVOR0_c2:  e_b           IVOR0Handler_c2
00000004 4400440044004400   98	        .align      4
0000000c 44004400        
00000010 78000000           99	IVOR1_c2:  e_b           IVOR1Handler_c2
00000014 4400440044004400  100	        .align      4
0000001c 44004400        
00000020 78000000          101	IVOR2_c2:  e_b           IVOR2Handler_c2
00000024 4400440044004400  102	        .align      4
0000002c 44004400        
00000030 78000000          103	IVOR3_c2:  e_b           IVOR3Handler_c2
00000034 4400440044004400  104	        .align      4
0000003c 44004400        
00000040 78000000          105	IVOR4_c2:  e_b           IVOR4Handler_c2
00000044 4400440044004400  106	        .align      4
0000004c 44004400        
00000050 78000000          107	IVOR5_c2:  e_b           IVOR5Handler_c2
00000054 4400440044004400  108	        .align      4
0000005c 44004400        
00000060 78000000          109	IVOR6_c2:  e_b           IVOR6Handler_c2

                                   Mon Jun 02 14:54:43 2025           Page 16
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
00000064 4400440044004400  110	        .align      4
0000006c 44004400        
00000070 78000000          111	IVOR7_c2:  e_b           IVOR7Handler_c2
00000074 4400440044004400  112	        .align      4
0000007c 44004400        
00000080 78000000          113	IVOR8_c2:  e_b           IVOR8Handler_c2
00000084 4400440044004400  114	        .align      4
0000008c 44004400        
00000090 78000000          115	IVOR9_c2:  e_b           IVOR9Handler_c2
00000094 4400440044004400  116	        .align      4
0000009c 44004400        
000000a0 78000000          117	IVOR10_c2: e_b           IVOR10Handler_c2
000000a4 4400440044004400  118	        .align      4
000000ac 44004400        
000000b0 78000000          119	IVOR11_c2: e_b           IVOR11Handler_c2
000000b4 4400440044004400  120	        .align      4
000000bc 44004400        
000000c0 78000000          121	IVOR12_c2: e_b           IVOR12Handler_c2
000000c4 4400440044004400  122	        .align      4
000000cc 44004400        
000000d0 78000000          123	IVOR13_c2: e_b           IVOR13Handler_c2
000000d4 4400440044004400  124	        .align      4
000000dc 44004400        
000000e0 78000000          125	IVOR14_c2: e_b           IVOR14Handler_c2
000000e4 4400440044004400  126	        .align      4
000000ec 44004400        
000000f0 78000000          127	IVOR15_c2: e_b           IVOR15Handler_c2
                           128	
                           129	
                           130	######################################
                           131	# IVOR 0 - Critical Input Interrupt  #
                           132	######################################
                           133	IVOR0Handler_c2:
000000f4 78000000          134	    e_b  IVOR0Handler_c2
                           135	##################################
                           136	
000000f8 4400440044004400  137	    .align 4
                           138	
                           139	####################################
                           140	# IVOR 1 - Machine Check Interrupt #
                           141	####################################
                           142	IVOR1Handler_c2:
                           143	#ECC flash management
                           144	    .extern ECCflash_ReturnJumpingFaultInstr # Shared between Ivor2 handler and 
                           145	                                          # checkFaultyAddrFromLastIvor2 function
                           146	                                          # to make the Ivor2 return to the address
                           147	                                          # of the faulty instruction + 4,
                           148	                                          # to not execute it again.
                           149	
00000100 182106ac          150	    e_stwu r1, -0x54 (r1)         # Create stack frame and store back chain
00000104 54010024          151	    e_stw  r0, 0x24 (r1)          # Save working registers R0
00000108 7c0802a6          152	    mfLR   r0                     # Store LR (Store now since LR will be used for ISR Vector)
0000010c 54010014          153	    e_stw  r0, 0x14 (r1)
00000110 54610028          154	    e_stw  r3, 0x28 (r1)          # Store a working register R3
                           155	  
                           156	### HandlerSaveContext_ph1 CUSTOMIZATION to change SRR0 to the next address ###
                           157	  
00000114 7c1b02a6          158	    mfSRR1 r0                     # get SRR1 
00000118 54010010          159	    e_stw  r0, 0x10 (r1)          # save it on the stack
                           160	
0000011c 7c1a02a6          161	    mfSRR0 r0                     # get SRR0
00000120 5401000c          162	    e_stw  r0, 0x0C (r1)          # save it on the stack
                           163	
                           164	
00000124 7060e000          165	    e_lis  r3,    ECCflash_ReturnJumpingFaultInstr@h

                                   Mon Jun 02 14:54:43 2025           Page 17
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           166	    ori    r3,r3, ECCflash_ReturnJumpingFaultInstr@l
                           166	    e_or2i RA,UI
00000128 7060c000          166		 nametmp/nt(e_or2i) r3,%lo(ECCflash_ReturnJumpingFau)
0000012c 30030000          167	    e_lbz  r0, 0x0(r3)            # Get an adder for the SRRO address to accomplish
00000130 7c030378          168	    mr     r3, r0                 # ivor2 behaviour explained in the following comment
00000134 7c1a8aa6          169	    mfMCSRR0  r0                  # get MCSRR0 (storeaddress of some instruction that was executing)
00000138 7c601a14          170	    add    r3, r0, r3     #MC,TbV     # adding 4 in case of EEPROM probably corrupted,
                           171	                                  # because we have to continue after interrupt WITHOUT
                           172	                                  # repeat the faulty instruction. The saved MCSRR0
                           173	                                  # will be the value of the PC when IVOR2 returns.
                           174	                                  # It is incremented here by 4(an instruction)
                           175	                                  # and restored when IVOR2_Manager ends
0000013c 54610074          176	    e_stw  r3, 0x74 (r1) # save it on the stack
                           177	
00000140 7c000026          178	    mfCR   r0                     # get CR
00000144 54010020          179	    e_stw  r0, 0x20 (r1)          # save it on the stack
                           180	  
                           181	######### end of HandlerSaveContext_ph1 CUSTOMIZATION
                           182	  
00000148 78000001          183	    e_bl   HandlerSaveContext_ph2    
0000014c 70800001          184	    e_li   r4,1  
00000150 7060e000          185	    e_lis  r3,IvorIndex_c2@ha
00000154 1c630000          186	    e_add16i r3,r3,IvorIndex_c2@l
00000158 34830000          187	    e_stb  r4,0(r3)
                           188	 
0000015c 7c9b8aa6          189	    mfMCSRR1 r4                     # get SRR1 
00000160 7060e000          190	    e_lis  r3,MCSRR1_Value_c2@h 
                           191	    ori    r3,r3,MCSRR1_Value_c2@l
                           191	    e_or2i RA,UI
00000164 7060c000          191		 nametmp/nt(e_or2i) r3,%lo(MCSRR1_Value_c2)
00000168 54830000          192	    e_stw  r4,0(r3)    
                           193	
0000016c 7c9a8aa6          194	    mfMCSRR0 r4                     # get SRR0 
00000170 7060e000          195	    e_lis  r3,MCSRR0_Value_c2@h
                           196	    ori    r3,r3,MCSRR0_Value_c2@l
                           196	    e_or2i RA,UI
00000174 7060c000          196		 nametmp/nt(e_or2i) r3,%lo(MCSRR0_Value_c2)
00000178 54830000          197	    e_stw  r4,0(r3)
                           198	
0000017c 7c9d8aa6          199	    mfspr  r4,573                    # get MCAR; SPR_MCAR=573
00000180 7060e000          200	    e_lis  r3,SPR_MCARValue_c2@h
                           201	    ori    r3,r3,SPR_MCARValue_c2@l
                           201	    e_or2i RA,UI
00000184 7060c000          201		 nametmp/nt(e_or2i) r3,%lo(SPR_MCARValue_c2)
00000188 54830000          202	    e_stw  r4,0(r3)
                           203	
0000018c 7c9c8aa6          204	    mfspr r4,572                    # get MCSR; SPR_MCSR=572 
00000190 7060e000          205	    e_lis r3,SPR_MCSRValue_c2@h
                           206	    ori   r3,r3,SPR_MCSRValue_c2@l
                           206	    e_or2i RA,UI
00000194 7060c000          206		 nametmp/nt(e_or2i) r3,%lo(SPR_MCSRValue_c2)
00000198 54830000          207	    e_stw r4,0(r3)
                           208	
0000019c 7c9e0aa6          209	    mfspr r4,62                     # get ESR; SPR_ESR=62; Unchanged
000001a0 7060e000          210	    e_lis r3,SPR_ESRValue_c2@h
                           211	    ori   r3,r3,SPR_ESRValue_c2@l

                                   Mon Jun 02 14:54:43 2025           Page 18
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           211	    e_or2i RA,UI
000001a4 7060c000          211		 nametmp/nt(e_or2i) r3,%lo(SPR_ESRValue_c2)
000001a8 54830000          212	    e_stw r4,0(r3)
                           213	
                           214	    #mfspr r4,61                    # get ESR; SPR_DEAR=61;Unchanged
                           215	    #e_lis r3,SPR_DEARValue_c2@h
                           216	    #ori   r3,r3,SPR_DEARValue_c2@l
                           217	    #e_stw r4,0(r3)
                           218	
000001ac 7060e000          219	    e_lis  r3,IVOR1_Manager_c2@ha
000001b0 1c630000          220	    e_add16i r3,r3,IVOR1_Manager_c2@l
000001b4 7c6803a6          221	    mtlr r3
000001b8 0005              222	    se_blrl
                           223	
                           224	# MC, This section is executed only when Ivor is triggered by check of memory at the startup;
                           225	# in all the other cases IVOR1_Manager trigger a shutdown
                           226	
000001ba 7c0006ac          227	    mbar 0                        #sinchronization
                           228	                           
000001be 78000001          229	    e_bl  HandlerRestoreContext_ph1
000001c2 78000001          230	    e_bl  HandlerRestoreContext_ph2
                           231	
000001c6 50010074          232	    e_lwz  r0,  0x74 (r1)        
000001ca 7c1a03a6          233	    mtSRR0 r0                     # MC - Restore SRR0 with MCSRR0 + ECCflash_ReturnJumpingFaultInstr 
                           234	                                  # to continue after interrupt WITHOUT repeat the faulty instruction 
                           235	                                  # of check of memory at the startup
                           236	    
000001ce 707fe7ff          237	    e_lis  r3,0xFFFFFFFF@h
                           238	    ori    r3,r3,0xFFFFFFFF@l
                           238	    e_or2i RA,UI
000001d2 707fc7ff          238		 nametmp/nt(e_or2i) r3,%lo(-1)
000001d6 7c7c8ba6          239	    mtmcsr r3                     # MC - Clean of MCSR;  
                           240	                                  # necessary when Ivor is triggered during check of memory at the startup,
                           241	                                  # that not execute a reset leaving the register written
                           242	                                  # and the core not able to store the address of the istruction 
                           243	                                  # that will trigger the next Ivor
                           244	    
000001da 50010014          245	    e_lwz  r0,  0x14 (r1)       # Restore LR
000001de 7c0803a6          246	    mtLR   r0
000001e2 50010024          247	    e_lwz  r0,  0x24 (r1)       # Restore working register
000001e6 1c210054          248	    e_add16i r1,  r1, 0x54      # Restore space on stack
                           249	
000001ea 0008              250	    se_rfi
                           251	
                           252	##################################    
                           253	
000001ec 44004400          254	    .align 4
                           255	
                           256	####################################
                           257	# IVOR 2 - Data Storage Interrupt  #
                           258	####################################
                           259	IVOR2Handler_c2:
                           260	prolog1:                          # PROLOGUE 
000001f0 182106b0          261	    e_stwu  r1, -0x50 (r1)        # create stack frame and store back chain
000001f4 54010024          262	    e_stw   r0, 0x24 (r1)         # save working registers R0
000001f8 54610028          263	    e_stw   r3, 0x28 (r1)         # store a working register

                                   Mon Jun 02 14:54:43 2025           Page 19
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
000001fc 7c0802a6          264	    mfLR    r0                    # store LR (Store now since LR will be used)
00000200 54010014          265	    e_stw   r0, 0x14 (r1)        
                           266	
00000204 70000002          267	    e_li    r0, 2                 # load in R0 IVOR index
00000208 7060e000          268	    e_lis   r3,IvorIndex_c2@ha
0000020c 1c630000          269	    e_add16i  r3,r3,IvorIndex_c2@l
00000210 34030000          270	    e_stb   r0,0(r3)
                           271	
00000214 7c1b02a6          272	    mfSRR1  r0                     # get CSRR1 
00000218 54010010          273	    e_stw   r0, 0x10 (r1)          # save it on the stack
0000021c 7060e000          274	    e_lis   r3, SRR1_Value_c2@h
                           275	    ori     r3, r3, SRR1_Value_c2@l
                           275	    e_or2i RA,UI
00000220 7060c000          275		 nametmp/nt(e_or2i) r3,%lo(SRR1_Value_c2)
00000224 54030000          276	    e_stw   r0, 0(r3)
                           277	
00000228 7c1a02a6          278	    mfSRR0  r0                    # get CSRR0 
0000022c 5401000c          279	    e_stw   r0, 0x0C (r1)         # save it on the stack
00000230 7060e000          280	    e_lis   r3, SRR0_Value_c2@h
                           281	    ori     r3, r3, SRR0_Value_c2@l
                           281	    e_or2i RA,UI
00000234 7060c000          281		 nametmp/nt(e_or2i) r3,%lo(SRR0_Value_c2)
00000238 54030000          282	    e_stw   r0, 0(r3) 
0000023c 7c000026          283	    mfCR    r0                    # get CR
00000240 54010020          284	    e_stw   r0,  0x20 (r1)        # save it on the stack
                           285	  
00000244 7c1d0aa6          286	    mfspr   r0, 61                # get DEAR; SPR_DEAR=61
00000248 7060e000          287	    e_lis   r3, SPR_DEARValue_c2@h
                           288	    ori     r3, r3, SPR_DEARValue_c2@l
                           288	    e_or2i RA,UI
0000024c 7060c000          288		 nametmp/nt(e_or2i) r3,%lo(SPR_DEARValue_c2)
00000250 54030000          289	    e_stw   r0, 0(r3)
                           290	
00000254 7c1c8aa6          291	    mfspr   r0, 572	              # get MCSR; SPR_MCSR=572; unchanged
00000258 7060e000          292	    e_lis   r3, SPR_MCSRValue_c2@h
                           293	    ori     r3, r3, SPR_MCSRValue_c2@l
                           293	    e_or2i RA,UI
0000025c 7060c000          293		 nametmp/nt(e_or2i) r3,%lo(SPR_MCSRValue_c2)
00000260 54030000          294	    e_stw   r0, 0(r3)
                           295	
00000264 7c9e0aa6          296	    mfspr   r4,62                 # get ESR; SPR_ESR=62
00000268 7060e000          297	    e_lis   r3,SPR_ESRValue_c2@h
                           298	    ori     r3,r3,SPR_ESRValue_c2@l
                           298	    e_or2i RA,UI
0000026c 7060c000          298		 nametmp/nt(e_or2i) r3,%lo(SPR_ESRValue_c2)
00000270 54830000          299	    e_stw   r4,0(r3)
                           300	
00000274 78000001          301	    e_bl    HandlerSaveContext_ph2 # branch to routine that save gprs contents
                           302	
00000278 7060e000          303	    e_lis   r3, IVOR_Common_Manager_c2@ha
0000027c 1c630000          304	    e_add16i  r3, r3, IVOR_Common_Manager_c2@l
00000280 7c6803a6          305	    mtLR    r3                    # store ISR address to LR to use for branching later
00000284 0005              306	    se_blrl                       # branch to ISR, but return here
                           307	epilog1:                          # EPILOGUE
                           308	#                                 # STEP 6 :  RESTORE CONTEXT
00000286 7c0006ac          309	    mbar 0                        # ensure interrupt flag has finished clearing

                                   Mon Jun 02 14:54:43 2025           Page 20
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           310	
0000028a 78000001          311	    e_bl    HandlerRestoreContext_ph1
                           312	
0000028e 50010020          313	    e_lwz   r0, 0x20 (r1)         # Restore CR
00000292 7c0ff120          314	    mtcrf   0xff, r0
00000296 50610028          315	    e_lwz   r3,  0x28 (r1)        # restore R3 gprs register
0000029a 5081002c          316	    e_lwz   r4,  0x2C (r1)        # restore R4 gprs register
0000029e 5001000c          317	    e_lwz   r0,  0x0C (r1)        # Restore CSRR0
000002a2 7c1a0ba6          318	    mtCSRR0 r0
000002a6 50010010          319	    e_lwz   r0,  0x10 (r1)        # Restore CSRR1
000002aa 7c1b0ba6          320	    mtCSRR1 r0
000002ae 50010014          321	    e_lwz   r0,  0x14 (r1)        # restore LR
000002b2 7c0803a6          322	    mtLR    r0 
000002b6 50010024          323	    e_lwz   r0,  0x24 (r1)        # restore working register
000002ba 1c210050          324	    e_add16i  r1,  r1, 0x50         # restore space on stack
000002be 4c000066          325	    rfci                          # restores machine state, including reenabling
                           326	                                  # critical interrupts MSR[CE].
                           327	
                           328	################################################################################    
                           329	
000002c2 4400440044004400  330	    .align 4
000002ca 440044004400    
                           331	
                           332	###########################################
                           333	# IVOR 3 - Instruction Storage Interrupt  #
                           334	###########################################
                           335	IVOR3Handler_c2:
000002d0 70800003          336	    e_li   r4,3  
000002d4 78000000          337	    e_b    NCI_Handler
                           338	
                           339	##################################    
                           340	
000002d8 4400440044004400  341	    .align 4
                           342	
                           343	###########################################
                           344	# IVOR 4 - External Input Interrupt       #
                           345	###########################################
                           346	IVOR4Handler_c2:
                           347	prolog4:                          # PROLOGUE 
000002e0 182106b0          348	    e_stwu r1, -0x50 (r1)         # Create stack frame and store back chain
000002e4 54010024          349	    e_stw  r0, 0x24 (r1)          # Save working registers R0
000002e8 7c0802a6          350	    mfLR   r0                     # Store LR (Store now since LR will be used for ISR Vector)
000002ec 54010014          351	    e_stw  r0, 0x14 (r1)
000002f0 54610028          352	    e_stw  r3, 0x28 (r1)          # Store a working register R3
                           353	
000002f4 78000001          354	    e_bl   HandlerSaveContext_ph1
                           355	
000002f8 707fe404          356	    e_lis  r3, INTC_IACKR_C2@h     # Store address of IACKR in r3
                           357	    ori    r3, r3, INTC_IACKR_C2@l
                           357	    e_or2i RA,UI
000002fc 7060c028          357		 nametmp/nt(e_or2i) r3,%lo(INTC_IACKR_C2)
00000300 50630000          358	    e_lwz  r3, 0(r3)               # Store contents of IACKR in r3 (this is vector table
                           359	                                   # address)
00000304 50030000          360	    e_lwz  r0, 0(r3)               # Read ISR address from ISR Vector Table address
00000308 54010008          361	    e_stw  r0, 0x08(r1)
                           362	
                           363	    #lis    r0, 0x02008200@h       # patch for SPE functionalities

                                   Mon Jun 02 14:54:43 2025           Page 21
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           364	    #ori   r0,r0,0x02008200@l
                           365	    #mtmsr  r0
                           366	
0000030c 7c008146          367	    wrteei 1                       # Enable interrupts; Set MSR[EE]=1 (must wait a couple clocks after reading IACKR)
                           368	
00000310 78000001          369	    e_bl HandlerSaveContext_ph2     
                           370	
00000314 50010008          371	    e_lwz r0,  0x8(r1)
00000318 7c0803a6          372	    mtLR  r0                       # Store ISR address to LR to use for branching later
0000031c 0005              373	    se_blrl                        # Branch to ISR, but return here
                           374	
                           375	epilog4:                           # EPILOGUE
                           376	                                   # STEP 6 :  RESTORE CONTEXT
0000031e 7c0006ac          377	    mbar 0                         # Ensure interrupt flag has finished clearing
                           378	                                   # before writing to INTC_EIOR
00000322 78000001          379	    e_bl    HandlerRestoreContext_ph1
                           380	
00000326 7c000146          381	    wrteei 0                       # Disable interrupts
                           382	
0000032a 70600000          383	    e_li     r3,0
0000032e 709fe404          384	    e_lis    r4, INTC_EOIR_C2@ha         # Load upper half of EIOR address to r4
00000332 1c840038          385	    e_add16i r4, r4, INTC_EOIR_C2@l    # Load lower half of EIOR address to R4
00000336 54640000          386	    e_stw    r3, 0(r4)                   # Write 0 to INTC_EOIR_C2, address 0xFFF4 8018
                           387	
0000033a 78000001          388	    e_bl    HandlerRestoreContext_ph2
                           389	
0000033e 50010014          390	    e_lwz r0, 0x14 (r1)         # Restore LR
00000342 7c0803a6          391	    mtLR  r0
00000346 50010024          392	    e_lwz r0, 0x24 (r1)         # Restore working register
0000034a 1c210050          393	    e_add16i r1, r1, 0x50       # Restore space on stack
0000034e 0008              394	    se_rfi                      # End of Interrupt - re-enables interrupts.
                           395	
                           396	##################################    
                           397	
                           398	    .align 4
                           399	
                           400	###########################################
                           401	# IVOR 5 - Alignment Interrupt            #
                           402	###########################################
                           403	IVOR5Handler_c2:
00000350 70800005          404	    e_li   r4,5  
00000354 78000000          405	    e_b    NCI_Handler
                           406	    
                           407	###########################################    
                           408	
00000358 4400440044004400  409	    .align 4
                           410	
                           411	###########################################
                           412	# IVOR 6 - Program Interrupt              #
                           413	###########################################
                           414	IVOR6Handler_c2:
00000360 70800006          415	    e_li   r4,6  
00000364 78000000          416	    e_b    NCI_Handler
                           417	
                           418	###########################################    
                           419	

                                   Mon Jun 02 14:54:43 2025           Page 22
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
00000368 4400440044004400  420	    .align 4
                           421	
                           422	###########################################
                           423	# IVOR 7 - Performance Monitor Interrupt  #
                           424	###########################################
                           425	IVOR7Handler_c2:
00000370 70800007          426	    e_li   r4,7  
00000374 78000000          427	    e_b    NCI_Handler
                           428	
                           429	##########################################    
                           430	
00000378 4400440044004400  431	    .align 4
                           432	
                           433	###########################################
                           434	# IVOR 8 - System Call Interrupt          #
                           435	###########################################
                           436	IVOR8Handler_c2:
00000380 70800008          437	    e_li   r4,8  
00000384 78000000          438	    e_b    NCI_Handler
                           439	 
                           440	###########################################    
                           441	
00000388 4400440044004400  442	    .align 4
                           443	
                           444	###########################################
                           445	# IVOR 9 - Debug Interrupt                #
                           446	###########################################
                           447	IVOR9Handler_c2:
00000390 70800009          448	    e_li   r4,9  
00000394 78000000          449	    e_b    NCI_Handler
                           450	
                           451	###########################################    
                           452	
00000398 4400440044004400  453	    .align 4
                           454	
                           455	####################################################
                           456	# IVOR 10 - Embedded Floating-point Data Interrupt #
                           457	#################################################### 
                           458	IVOR10Handler_c2:
000003a0 7080000a          459	    e_li   r4,10  
000003a4 78000000          460	    e_b    NCI_Handler
                           461	
                           462	####################################################    
                           463	
000003a8 4400440044004400  464	    .align 4
                           465	
                           466	#####################################################
                           467	# IVOR 11 - Embedded Floating-point Round Interrupt #
                           468	#####################################################
                           469	IVOR11Handler_c2:
000003b0 7080000b          470	    e_li   r4,11  
000003b4 78000000          471	    e_b    NCI_Handler
                           472	
                           473	##################################    
                           474	
000003b8 4400440044004400  475	    .align 4

                                   Mon Jun 02 14:54:43 2025           Page 23
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
                           476	
                           477	#####################################################################################
                           478	# IVOR 12 - Embedded Floating-point Unavailable - reserved for future processor use #
                           479	##################################################################################### 
                           480	IVOR12Handler_c2:
000003c0 78000000          481	    e_b IVOR12Handler_c2
                           482	##############################################
                           483	
000003c4 4400440044004400  484	    .align 4
000003cc 44004400        
                           485	
                           486	###############################################
                           487	# IVOR 13 - reserved for future processor use #
                           488	###############################################
                           489	IVOR13Handler_c2:
000003d0 78000000          490	    e_b IVOR13Handler_c2
                           491	##############################################
                           492	
000003d4 4400440044004400  493	    .align 4
000003dc 44004400        
                           494	
                           495	###############################################
                           496	# IVOR 14 - reserved for future processor use #
                           497	###############################################    
                           498	IVOR14Handler_c2:
000003e0 78000000          499	    e_b IVOR14Handler_c2 
                           500	###############################################    
                           501	
000003e4 4400440044004400  502	    .align 4
000003ec 44004400        
                           503	
                           504	###############################################
                           505	# IVOR 15 - reserved for future processor use #
                           506	###############################################
                           507	IVOR15Handler_c2:
000003f0 78000000          508	    e_b  IVOR15Handler_c2 
                           509	
                           510	    
                           511	
                           512	################################## 
                           513	# HandlerSaveContext - phase 1   #
                           514	##################################    
                           515	HandlerSaveContext_ph1:
000003f4 7c1b02a6          516	    mfSRR1 r0                   # get SRR1 
000003f8 54010010          517	    e_stw  r0, 0x10 (r1)        # save it on the stack
000003fc 7c1a02a6          518	    mfSRR0 r0                   # get SRR0
00000400 5401000c          519	    e_stw  r0, 0x0C (r1)        # save it on the stack
00000404 7c000026          520	    mfCR   r0                   # get CR
00000408 54010020          521	    e_stw  r0, 0x20 (r1)        # save it on the stack
0000040c 0004              522	    se_blr
                           523	
                           524	################################## 
                           525	# HandlerSaveContext - phase 2   #
                           526	##################################   
                           527	HandlerSaveContext_ph2:
0000040e 5581004c          528	    e_stw    r12, 0x4C (r1)           # store rest of gprs
00000412 55610048          529	    e_stw    r11, 0x48 (r1)           #    |
00000416 55410044          530	    e_stw    r10, 0x44 (r1)           #    |
0000041a 55210040          531	    e_stw    r9,  0x40 (r1)           #    |

                                   Mon Jun 02 14:54:43 2025           Page 24
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
0000041e 5501003c          532	    e_stw    r8,  0x3C (r1)           #    |
00000422 54e10038          533	    e_stw    r7,  0x38 (r1)           #    |
00000426 54c10034          534	    e_stw    r6,  0x34 (r1)           #    |
0000042a 54a10030          535	    e_stw    r5,  0x30 (r1)           #    |
0000042e 5481002c          536	    e_stw    r4,  0x2c (r1)           #    |
                           537	    #evstwwe r12, 0x50 (r1)           #    |
                           538	    #evstwwe r11, 0x54 (r1)           #    |
                           539	    #evstwwe r10, 0x58 (r1)           #    |
                           540	    #evstwwe r9, 0x5C (r1)            #    |
                           541	    #evstwwe r8, 0x60 (r1)            #    |
                           542	    #evstwwe r7, 0x64 (r1)            #    |
                           543	    #evstwwe r6, 0x68 (r1)            #    |
                           544	    #evstwwe r5, 0x6C (r1)            #    |
                           545	    #evstwwe r4, 0x70 (r1)            #    |
                           546	
00000432 7c0102a6          547	    mfXER r0                         # get XER
00000436 5401001c          548	    e_stw r0,  0x1C (r1)             # save it on the stack
0000043a 7c0902a6          549	    mfCTR r0                         # get CTR
0000043e 54010018          550	    e_stw r0,  0x18 (r1)             # save it on the stack
00000442 0004              551	    se_blr
                           552	
                           553	################################### 
                           554	# HandlerRestoreContext - phase 1 #
                           555	###################################   
                           556	HandlerRestoreContext_ph1:
00000444 50010018          557	    e_lwz r0, 0x18 (r1)            # get CTR off stack
00000448 7c0903a6          558	    mtCTR r0                       # restore it
0000044c 5001001c          559	    e_lwz r0, 0x1C (r1)            # get XER off stack
00000450 7c0103a6          560	    mtXER r0                       # restore it
                           561	    #evlwwsplat r5, 0x6C (r1)
                           562	    #evlwwsplat r6, 0x68 (r1)
                           563	    #evlwwsplat r7, 0x64 (r1)
                           564	    #evlwwsplat r8, 0x60 (r1)
                           565	    #evlwwsplat r9, 0x5C (r1)
                           566	    #evlwwsplat r10, 0x58 (r1)
                           567	    #evlwwsplat r11, 0x54 (r1)
                           568	    #evlwwsplat r12, 0x50 (r1)
00000454 50a10030          569	    e_lwz r5,  0x30 (r1)	        # restore rest of gprs
00000458 50c10034          570	    e_lwz r6,  0x34 (r1)	        #    |
0000045c 50e10038          571	    e_lwz r7,  0x38 (r1)	        #    |
00000460 5101003c          572	    e_lwz r8,  0x3C (r1)	        #    |
00000464 51210040          573	    e_lwz r9,  0x40 (r1)	        #    |
00000468 51410044          574	    e_lwz r10, 0x44 (r1)	        #    |
0000046c 51610048          575	    e_lwz r11, 0x48 (r1)	        #    |
00000470 5181004c          576	    e_lwz r12, 0x4C (r1)	        #____v____
                           577	
00000474 0004              578	    se_blr
                           579	
                           580	################################### 
                           581	# HandlerRestoreContext - phase 2 #
                           582	###################################   
                           583	HandlerRestoreContext_ph2:
00000476 50010020          584	    e_lwz  r0, 0x20 (r1)          # Restore CR
0000047a 7c0ff120          585	    mtcrf  0xff, r0
0000047e 50610028          586	    e_lwz  r3,  0x28 (r1)         # restore R3 gprs register
                           587	    #evlwwsplat r4, 0x70 (r1)

                                   Mon Jun 02 14:54:43 2025           Page 25
                                   Wed May 28 15:43:24 2025   IVOR_c2_handlers_GHS.s
00000482 5081002c          588	    e_lwz  r4,  0x2C (r1)         # restore R4 gprs register
00000486 5001000c          589	    e_lwz  r0,  0x0C (r1)         # Restore SRR0
0000048a 7c1a03a6          590	    mtSRR0 r0
0000048e 50010010          591	    e_lwz  r0,  0x10 (r1)         # Restore SRR1
00000492 7c1b03a6          592	    mtSRR1 r0
00000496 0004              593	    se_blr
                           594	
                           595	##################################    
                           596	
                           597	########################################## 
                           598	# Not Recoverable Interrupt handler body # 
                           599	##########################################    
                           600	NCI_Handler:                      # Non Recoverable Interrupt handler body 
00000498 7060e000          601	    e_lis  r3,IvorIndex_c2@ha
0000049c 1c630000          602	    e_add16i r3,r3,IvorIndex_c2@l
000004a0 34830000          603	    e_stb  r4,0(r3)
                           604	
000004a4 7c9b02a6          605	    mfSRR1 r4                     # get SRR1 
000004a8 7060e000          606	    e_lis  r3,SRR1_Value_c2@h 
                           607	    ori  r3,r3,SRR1_Value_c2@l
                           607	    e_or2i RA,UI
000004ac 7060c000          607		 nametmp/nt(e_or2i) r3,%lo(SRR1_Value_c2)
000004b0 54830000          608	    e_stw  r4,0(r3)    
                           609	
000004b4 7c9a02a6          610	    mfSRR0 r4                     # get SRR0 
000004b8 7060e000          611	    e_lis  r3,SRR0_Value_c2@h
                           612	    ori  r3,r3,SRR0_Value_c2@l
                           612	    e_or2i RA,UI
000004bc 7060c000          612		 nametmp/nt(e_or2i) r3,%lo(SRR0_Value_c2)
000004c0 54830000          613	    e_stw  r4,0(r3)
                           614	
000004c4 7c9e0aa6          615	    mfspr r4,62	                  # get ESR; SPR_ESR=62
000004c8 7060e000          616	    e_lis   r3,SPR_ESRValue_c2@h
                           617	    ori   r3,r3,SPR_ESRValue_c2@l
                           617	    e_or2i RA,UI
000004cc 7060c000          617		 nametmp/nt(e_or2i) r3,%lo(SPR_ESRValue_c2)
000004d0 54830000          618	    e_stw   r4,0(r3)
                           619	
000004d4 7c9d0aa6          620	    mfspr r4,61	                  # get ESR; SPR_DEAR=61
000004d8 7060e000          621	    e_lis   r3,SPR_DEARValue_c2@h
                           622	    ori   r3,r3,SPR_DEARValue_c2@l
                           622	    e_or2i RA,UI
000004dc 7060c000          622		 nametmp/nt(e_or2i) r3,%lo(SPR_DEARValue_c2)
000004e0 54830000          623	    e_stw   r4,0(r3)
                           624	    
000004e4 7060e000          625	    e_lis  r3,IVOR_Common_Manager_c2@ha
000004e8 1c630000          626	    e_add16i r3,r3,IVOR_Common_Manager_c2@l
000004ec 7c6803a6          627	    mtlr r3
000004f0 0005              628	    se_blrl
                           629	#######################################################
                           630	
