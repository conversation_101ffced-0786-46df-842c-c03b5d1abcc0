/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Timing
**  Filename        :  timing_out.h
**  Created on      :  18-giu-2020 10:00:00
**  Original author :  CarboniM
******************************************************************************/
/*****************************************************************************
**
**                        SwcName Description
**
**  In this section write the description of the general behavior of software
**  component
******************************************************************************/

#ifndef TIMING_OUT_H
#define TIMING_OUT_H

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "rtwtypes.h"

/*****************************************************************************
** PUBLIC DEFINES
******************************************************************************/
#define TIMEOUT_PENDING     0x0u
#define TIMEOUT_EXPIRED     0x1u
#define _TEST_TIMING_ // Enable Task time debug.
#define _TEST_ISR_TIMING_ // Enable ISR time debug.

/*****************************************************************************
** PUBLIC MACROS
******************************************************************************/
/* None */

/*****************************************************************************
** PUBLIC TYPEDEFS
******************************************************************************/
typedef uint64_T timeoutHandler_t;

/*****************************************************************************
** PUBLIC VARIABLE DECLARATIONS
******************************************************************************/
extern uint32_T seconds;
extern uint8_T Minutes;
extern uint16_T Hours;
extern uint32_T SecondsWorkTime;
extern uint32_T SecondsRunTime;
extern uint32_T SecondsFirstRunTime;
extern uint32_T ECUTimeStamps;
extern uint32_T ECUTimeStampsEE;
extern uint16_T ECUTimeStampsFromKeyOn;
extern uint16_T ECUTimeStampsFromKeyOnEE;

/*****************************************************************************
** PUBLIC FUNCTION PROTOTYPES
******************************************************************************/
/******************************************************************************
**   Function    : TIMING_Config
**
**   Description:
**    Brief function description 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern int16_T TIMING_Config(void);

/******************************************************************************
**   Function    : TIMING_Init
**
**   Description:
**    Initialize the absolute timer.
**
**   Parameters :
**    void
**
**   Returns:
**    NO_ERROR - Timing functionality correctly initialized.  
**
******************************************************************************/
extern int16_T TIMING_Init(void);

/******************************************************************************
**   Function    : TIMING_GetTaskTime
**
**   Description:
**    This function provide the task time. 
**
**   Parameters :
**    [in] uint16_T *task_time :
**    [in] uint64_T task_timer : 
** 
**   Returns:
**    void
**
******************************************************************************/
extern void TIMING_GetTaskTime(uint16_T *task_time, uint64_T task_timer);

/******************************************************************************
**   Function    : TIMING_GetAbsTimer
**
**   Description:
**    Get the absolute timer. 
**
**   Parameters :
**    [in] uint64_T* abstime : Pointer to variable where the absolute time is stored.
**                            
**   Returns:
**    void
**
******************************************************************************/
extern void TIMING_GetAbsTimer(uint64_T *abstime);

/******************************************************************************
**   Function    : TIMING_TicksToSeconds
**
**   Description:
**    Convert ticks to seconds. 
**
**   Parameters :
**    [in] uint64_T abstime : Absolute time (in ticks). 
**    [in] uint64_T * sTime : Pointer to variable where the converted time is stored.
**
**   Returns:
**    NO_ERROR - Ticks correctly converted.
**
******************************************************************************/
extern int16_T TIMING_TicksToSeconds(uint64_T abstime, uint64_T *sTime);

/******************************************************************************
**   Function    : TIMING_TicksToMilliSeconds
**
**   Description:
**    description of operation: SwcName_PublicFunction1
**
**   Parameters :
**    [in] uint64_T abstime : Absolute time (in ticks). 
**    [in] uint64_T * msTime : Pointer to variable where the converted time is stored.
**
**   Returns:
**    NO_ERROR - Ticks correctly converted.
**
******************************************************************************/
extern int16_T TIMING_TicksToMilliSeconds(uint64_T abstime, uint64_T *msTime);

/******************************************************************************
**   Function    : TIMING_TicksToMicroSeconds
**
**   Description:
**    description of operation: SwcName_PublicFunction1
**
**   Parameters :
**    [in] uint64_T abstime : Absolute time (in ticks). 
**    [in] uint64_T * usTime : Pointer to variable where the converted time is stored.
**
**   Returns:
**    NO_ERROR - Ticks correctly converted.
**
******************************************************************************/
extern int16_T TIMING_TicksToMicroSeconds(uint64_T abstime, uint64_T *usTime);

/******************************************************************************
**   Function    : WorkingTime_T100ms
**
**   Description:
**    Engine operating Minutes. 
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void WorkingTime_T100ms(void);

/******************************************************************************
**   Function    : TIMING_SetTimeout
**
**   Description:
**    
**
**   Parameters :
**    [in] uint32_T us_timeout : 
**    [in] timeoutHandler_t* handler : 
**
**   Returns:
**    NO_ERROR - Timeout correctly set.
**
******************************************************************************/
extern int16_T TIMING_SetTimeout(uint32_T us_timeout, timeoutHandler_t *handler);

/******************************************************************************
**   Function    : TIMING_GetTimeoutStatus
**
**   Description:
**    Get the timeout status. 
**
**   Parameters :
**    [in] timeoutHandler_t handler : 
**    [out] uint8_T* status_l : 
**
**   Returns:
**    void
**
******************************************************************************/
extern int16_T TIMING_GetTimeoutStatus(timeoutHandler_t handler, uint8_T *status_l);

/******************************************************************************
**   Function    : TimeStampsInit
**
**   Description:
**    Time Stamps initialization at each power on.
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void TimeStampsInit(void);

/******************************************************************************
**   Function    : TIMING_UpdateTimeStampsT10ms
**
**   Description:
**    Update eeprom variables (ECUTimeStampsEE, ECUTimeStampsFromKeyOnEE) 
**    with runtime values (ECUTimeStamps, ECUTimeStampsFromKeyOn).
**
**   Parameters :
**    void
**
**   Returns:
**    void
**
******************************************************************************/
extern void TIMING_UpdateTimeStampsT10ms(void);

/******************************************************************************
**   Function    : TIMING_SetDelay
**
**   Description:
**    Set delay in micro seconds
**
**   Parameters :
**    microSeconds to wait
**
**   Returns:
**    void
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
extern void TIMING_SetDelay(uint16_T microSeconds);

#endif // TIMING_OUT_H

/****************************************************************************
 ****************************************************************************/


