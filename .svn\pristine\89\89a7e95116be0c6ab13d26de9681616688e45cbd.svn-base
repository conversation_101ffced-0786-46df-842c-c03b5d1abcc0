/* generated by MCS-Assembler tool ASM-MCS version 0.9 */
/* Copyright (C) 2011-2016 by <PERSON>, Germany */
/* target architecture : mcs24-1 */

#ifndef MCS0_H_
#define MCS0_H_

#define OFFSET_MCS0_MEM     (   0) /* byte address offset for assembled code in array C-array 'mcs0_mem' */
#define SIZE_MCS0_MEM       (6144) /* code size in bytes of assembled code in C-array 'mcs0_mem' */

#define LABEL_MCS0_MEM_CYL0_OUTPUT_DELAY         (  10) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_OUTPUT_DELAY' */
#define LABEL_MCS0_MEM_CYL0_EPWS_CHECK           ( 560) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_CHECK' */
#define LABEL_MCS0_MEM_CYL4_DELAY                ( 952) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_DELAY' */
#define LABEL_MCS0_MEM_CYL6_B_DELAY              (1144) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_B_DELAY' */
#define LABEL_MCS0_MEM_CYL2_CHECK_BUCK_AVB       ( 735) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_CHECK_BUCK_AVB' */
#define LABEL_MCS0_MEM_CYL6_EPWS_LAST_LOOP       (1286) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_LAST_LOOP' */
#define LABEL_MCS0_MEM_CYL0_PSM_DATA_LOADED       ( 499) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_PSM_DATA_LOADED' */
#define LABEL_MCS0_MEM_CYL6_LOAD_PSM             (1112) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_LOAD_PSM' */
#define LABEL_MCS0_MEM_CYL4_EPWS_START           (1024) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_START' */
#define LABEL_MCS0_MEM_CYL4_WAIT_INPUT_SIGNAL       ( 915) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_WAIT_INPUT_SIGNAL' */
#define LABEL_MCS0_MEM_CYL6_EPWS_DATA            ( 153) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_DATA' */
#define LABEL_MCS0_MEM_CYL6_EPWS_WAIT_T_ON_MATCH       ( 448) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_WAIT_T_ON_MATCH' */
#define LABEL_MCS0_MEM_CYL2_CHECK_EDGE           ( 718) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_CHECK_EDGE' */
#define LABEL_MCS0_MEM_CYL6_INIT                 (1091) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_INIT' */
#define LABEL_MCS0_MEM_CYL0_EPWS2_N_PULSE        (  15) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS2_N_PULSE' */
#define LABEL_MCS0_MEM_CYL6_D1_PERDIOD           ( 172) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D1_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_START                ( 478) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_START' */
#define LABEL_MCS0_MEM_BUCK0_STACK               ( 325) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_STACK' */
#define LABEL_MCS0_MEM_CYL0_EPWS_LAST_PULSE       (  24) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_LAST_PULSE' */
#define LABEL_MCS0_MEM_MCS0_PRIM_DISABLE_FAULT       (1523) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIM_DISABLE_FAULT' */
#define LABEL_MCS0_MEM_CYL6_EPWS_LAST_PULSE_END       (1291) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_LAST_PULSE_END' */
#define LABEL_MCS0_MEM_CYL2_EPWS1_N_PULSE        (  59) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS1_N_PULSE' */
#define LABEL_MCS0_MEM_CYL4_D7_PERDIOD           ( 143) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D7_PERDIOD' */
#define LABEL_MCS0_MEM_CYL4_EPWS_TIMEOUT         ( 119) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_TIMEOUT' */
#define LABEL_MCS0_MEM_CYL2_EPWS_WAIT_BUCK_OFF_1       ( 813) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_WAIT_BUCK_OFF_1' */
#define LABEL_MCS0_MEM_CYL6_EPWS_RET             ( 465) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_RET' */
#define LABEL_MCS0_MEM_SETUP_CYLINDER4           (1478) /* Index into C-array 'mcs0_mem' for assembler label 'SETUP_CYLINDER4' */
#define LABEL_MCS0_MEM_CYL2_EPWS_LAST_TIME_OUT       ( 879) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_LAST_TIME_OUT' */
#define LABEL_MCS0_MEM_CYL6_D1_DURATION          ( 171) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D1_DURATION' */
#define LABEL_MCS0_MEM_CYL0_EPWS_WAIT_T_OFF_MATCH       ( 384) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_WAIT_T_OFF_MATCH' */
#define LABEL_MCS0_MEM_CYL2_D6_DURATION          (  92) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D6_DURATION' */
#define LABEL_MCS0_MEM_CYL4_START                ( 891) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_START' */
#define LABEL_MCS0_MEM_CYL0_B_DELAY              ( 525) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_B_DELAY' */
#define LABEL_MCS0_MEM_CYL4_D7_DUTY              ( 144) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D7_DUTY' */
#define LABEL_MCS0_MEM_SETUP_CYLINDER2           (1468) /* Index into C-array 'mcs0_mem' for assembler label 'SETUP_CYLINDER2' */
#define LABEL_MCS0_MEM_CYL6_CHECK_BUCK_AVB       (1148) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_CHECK_BUCK_AVB' */
#define LABEL_MCS0_MEM_CYL2_D7_PERDIOD           (  96) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D7_PERDIOD' */
#define LABEL_MCS0_MEM_CYL2_D4_DURATION          (  86) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D4_DURATION' */
#define LABEL_MCS0_MEM_CYL4_D1_PERDIOD           ( 125) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D1_PERDIOD' */
#define LABEL_MCS0_MEM_B0_L_WA                   ( 364) /* Index into C-array 'mcs0_mem' for assembler label 'B0_L_WA' */
#define LABEL_MCS0_MEM_CYL6_ABORT_COM_OUT        (1193) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_ABORT_COM_OUT' */
#define LABEL_MCS0_MEM_CYL0_D6_DURATION          (  45) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D6_DURATION' */
#define LABEL_MCS0_MEM_CYL0_WAIT_NEW_CONFIG       ( 509) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_WAIT_NEW_CONFIG' */
#define LABEL_MCS0_MEM_CYL4_D4_DUTY              ( 135) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D4_DUTY' */
#define LABEL_MCS0_MEM_BUCK0_REF_MEM_ACK         ( 205) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_REF_MEM_ACK' */
#define LABEL_MCS0_MEM_CYL0_EPWS1_DUTY           (  14) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS1_DUTY' */
#define LABEL_MCS0_MEM_CYL0_EPWS_STOP_IPRI       ( 606) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_STOP_IPRI' */
#define LABEL_MCS0_MEM_CYL0_EPWS_CLOSE           ( 606) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_CLOSE' */
#define LABEL_MCS0_MEM_CYL6_DATA_LOADED          ( 203) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_DATA_LOADED' */
#define LABEL_MCS0_MEM_BUCK0_WAIT_INPUT_START_SIGNAL       (1367) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_WAIT_INPUT_START_SIGNAL' */
#define LABEL_MCS0_MEM_CYL0_EPWS_PH2_START       ( 627) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_PH2_START' */
#define LABEL_MCS0_MEM_CYL6_D5_DURATION          ( 183) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D5_DURATION' */
#define LABEL_MCS0_MEM_CYL0_D3_DUTY              (  38) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D3_DUTY' */
#define LABEL_MCS0_MEM_CYL4_EPWS4_PERIOD         ( 116) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS4_PERIOD' */
#define LABEL_MCS0_MEM_CYL0_NMOS_DURATION        (  27) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_NMOS_DURATION' */
#define LABEL_MCS0_MEM_CYL2_D5_PERDIOD           (  90) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D5_PERDIOD' */
#define LABEL_MCS0_MEM_CYL2_OUTPUT_DELAY         (  57) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_OUTPUT_DELAY' */
#define LABEL_MCS0_MEM_CYL4_EPWS_TIME_OUT        (1014) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_TIME_OUT' */
#define LABEL_MCS0_MEM_CYL6_STACK                ( 261) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_STACK' */
#define LABEL_MCS0_MEM_CYL0_WAIT_INPUT_SIGNAL       ( 504) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_WAIT_INPUT_SIGNAL' */
#define LABEL_MCS0_MEM_CYL4_D5_DUTY              ( 138) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D5_DUTY' */
#define LABEL_MCS0_MEM_CYL0_D4_PERDIOD           (  40) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D4_PERDIOD' */
#define LABEL_MCS0_MEM_CYL2_EPWS1_DUTY           (  61) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS1_DUTY' */
#define LABEL_MCS0_MEM_CYL4_EPWS2_PERIOD         ( 110) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS2_PERIOD' */
#define LABEL_MCS0_MEM_CYL6_WAIT_BUCK_OFF        (1188) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_WAIT_BUCK_OFF' */
#define LABEL_MCS0_MEM_DATA_CYLINDER4            ( 102) /* Index into C-array 'mcs0_mem' for assembler label 'DATA_CYLINDER4' */
#define LABEL_MCS0_MEM_DATA_CYLINDER6            ( 149) /* Index into C-array 'mcs0_mem' for assembler label 'DATA_CYLINDER6' */
#define LABEL_MCS0_MEM_DATA_CYLINDER0            (   8) /* Index into C-array 'mcs0_mem' for assembler label 'DATA_CYLINDER0' */
#define LABEL_MCS0_MEM_MCS0_CYL_EPWS_FUNC        ( 469) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_CYL_EPWS_FUNC' */
#define LABEL_MCS0_MEM_CYL2_D3_DURATION          (  83) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D3_DURATION' */
#define LABEL_MCS0_MEM_CYL2_D8_DUTY              ( 100) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D8_DUTY' */
#define LABEL_MCS0_MEM_CYL2_EPWS2_N_PULSE        (  62) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS2_N_PULSE' */
#define LABEL_MCS0_MEM_CYL6_EPWS4_DUTY           ( 164) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS4_DUTY' */
#define LABEL_MCS0_MEM_CYL6_EPWS1_N_PULSE        ( 153) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS1_N_PULSE' */
#define LABEL_MCS0_MEM_FBUCK0_LOADING            ( 356) /* Index into C-array 'mcs0_mem' for assembler label 'FBUCK0_LOADING' */
#define LABEL_MCS0_MEM_CYL6_EPWS_FUNC            ( 445) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_FUNC' */
#define LABEL_MCS0_MEM_CYL4_D7_DURATION          ( 142) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D7_DURATION' */
#define LABEL_MCS0_MEM_DATA_CYLINDER2            (  55) /* Index into C-array 'mcs0_mem' for assembler label 'DATA_CYLINDER2' */
#define LABEL_MCS0_MEM_CYL4_D3_PERDIOD           ( 131) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D3_PERDIOD' */
#define LABEL_MCS0_MEM_CYL4_NMOS_DURATION        ( 121) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_NMOS_DURATION' */
#define LABEL_MCS0_MEM_CYL2_DATA_LOADED          ( 201) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_DATA_LOADED' */
#define LABEL_MCS0_MEM_CYL4_EPWS_WAIT_T_OFF_MATCH       ( 432) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_WAIT_T_OFF_MATCH' */
#define LABEL_MCS0_MEM_MCS0_PRIM_WAIT_NEW_SAMPLE       (1517) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIM_WAIT_NEW_SAMPLE' */
#define LABEL_MCS0_MEM_BUCK0_WAIT_INPUT_CLOSE       (1409) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_WAIT_INPUT_CLOSE' */
#define LABEL_MCS0_MEM_CYL4_EPWS_WAIT_CPU_TRG       (1009) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_WAIT_CPU_TRG' */
#define LABEL_MCS0_MEM_CYL6_D1_DUTY              ( 173) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D1_DUTY' */
#define LABEL_MCS0_MEM_CYL2_D1_DURATION          (  77) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D1_DURATION' */
#define LABEL_MCS0_MEM_CYL2_PSM_SIZE             (  55) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_PSM_SIZE' */
#define LABEL_MCS0_MEM_CYLINDER0                 (1318) /* Index into C-array 'mcs0_mem' for assembler label 'CYLINDER0' */
#define LABEL_MCS0_MEM_CYL4_EPWS_PH1_START       (1026) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_PH1_START' */
#define LABEL_MCS0_MEM_CYL4_D4_DURATION          ( 133) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D4_DURATION' */
#define LABEL_MCS0_MEM_CYL6_T_OFF_STEP           ( 453) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_T_OFF_STEP' */
#define LABEL_MCS0_MEM_CYL0_PSM_SETUP            ( 483) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_PSM_SETUP' */
#define LABEL_MCS0_MEM_CYL2_EPWS_PH1_START       ( 818) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_PH1_START' */
#define LABEL_MCS0_MEM_CYL6_D3_DURATION          ( 177) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D3_DURATION' */
#define LABEL_MCS0_MEM_CYL2_B_DELAY_WA           ( 727) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_B_DELAY_WA' */
#define LABEL_MCS0_MEM_PRI_B0_CYL_TH             ( 212) /* Index into C-array 'mcs0_mem' for assembler label 'PRI_B0_CYL_TH' */
#define LABEL_MCS0_MEM_CYL0_PSM_SIZE             (   8) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_PSM_SIZE' */
#define LABEL_MCS0_MEM_CYL0_CYLINDER_INDEX       (   9) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_CYLINDER_INDEX' */
#define LABEL_MCS0_MEM_CYL4_EPWS3_N_PULSE        ( 112) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS3_N_PULSE' */
#define LABEL_MCS0_MEM_CYL6_D7_DURATION          ( 189) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D7_DURATION' */
#define LABEL_MCS0_MEM_CYL0_EPWS                 (  11) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS' */
#define LABEL_MCS0_MEM_CYL0_EPWS_DATA            (  12) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_DATA' */
#define LABEL_MCS0_MEM_CYL0_EPWS1_N_PULSE        (  12) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS1_N_PULSE' */
#define LABEL_MCS0_MEM_CYL4_DONE                 (1000) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_DONE' */
#define LABEL_MCS0_MEM_CYL0_EPWS1_PERIOD         (  13) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS1_PERIOD' */
#define LABEL_MCS0_MEM_CYL0_EPWS2_PERIOD         (  16) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS2_PERIOD' */
#define LABEL_MCS0_MEM_CYL0_EPWS2_DUTY           (  17) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS2_DUTY' */
#define LABEL_MCS0_MEM_CYLINDER6                 (1333) /* Index into C-array 'mcs0_mem' for assembler label 'CYLINDER6' */
#define LABEL_MCS0_MEM_BUCK0_START               (1355) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_START' */
#define LABEL_MCS0_MEM_CYL0_EPWS3_N_PULSE        (  18) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS3_N_PULSE' */
#define LABEL_MCS0_MEM_CYL0_EPWS3_PERIOD         (  19) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS3_PERIOD' */
#define LABEL_MCS0_MEM_CYL0_EPWS3_DUTY           (  20) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS3_DUTY' */
#define LABEL_MCS0_MEM_CYL2_EPWS_LAST_PULSE       (  71) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_LAST_PULSE' */
#define LABEL_MCS0_MEM_BUCK0_CURRENT_UNLOADING_STEP       ( 210) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_CURRENT_UNLOADING_STEP' */
#define LABEL_MCS0_MEM_CYL0_EPWS4_N_PULSE        (  21) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS4_N_PULSE' */
#define LABEL_MCS0_MEM_CYL0_EPWS_PH3_START       ( 639) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_PH3_START' */
#define LABEL_MCS0_MEM_CYL2_STACK                ( 229) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_STACK' */
#define LABEL_MCS0_MEM_MCS0_PRIM_DISABLE         (1528) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIM_DISABLE' */
#define LABEL_MCS0_MEM_CYL0_EPWS4_PERIOD         (  22) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS4_PERIOD' */
#define LABEL_MCS0_MEM_MCS0_PRIMARY              (1515) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIMARY' */
#define LABEL_MCS0_MEM_CYL0_END_CONF_PARAM       (  54) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_END_CONF_PARAM' */
#define LABEL_MCS0_MEM_CYL0_EPWS4_DUTY           (  23) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS4_DUTY' */
#define LABEL_MCS0_MEM_CYL2_EPWS3_N_PULSE        (  65) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS3_N_PULSE' */
#define LABEL_MCS0_MEM_CYL4_INPUT_CLOSE          ( 974) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_INPUT_CLOSE' */
#define LABEL_MCS0_MEM_CYL0_EPWS_TIMEOUT         (  25) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_TIMEOUT' */
#define LABEL_MCS0_MEM_SETUP_CYLINDER0           (1458) /* Index into C-array 'mcs0_mem' for assembler label 'SETUP_CYLINDER0' */
#define LABEL_MCS0_MEM_CYL0_PMOS_DURATION        (  26) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_PMOS_DURATION' */
#define LABEL_MCS0_MEM_CYL2_D7_DUTY              (  97) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D7_DUTY' */
#define LABEL_MCS0_MEM_CYL0_COIL_LOADING_STEP       (  28) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_COIL_LOADING_STEP' */
#define LABEL_MCS0_MEM_CYL2_PMOS_DURATION        (  73) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_PMOS_DURATION' */
#define LABEL_MCS0_MEM_CYL4_CHECK_BUCK_AVB       ( 940) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_CHECK_BUCK_AVB' */
#define LABEL_MCS0_MEM_CYL0_COIL_UNLOADING_STEP       (  29) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_COIL_UNLOADING_STEP' */
#define LABEL_MCS0_MEM_CYL0_D1_DURATION          (  30) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D1_DURATION' */
#define LABEL_MCS0_MEM_B0_U_LOOP                 ( 353) /* Index into C-array 'mcs0_mem' for assembler label 'B0_U_LOOP' */
#define LABEL_MCS0_MEM_CYL0_D1_PERDIOD           (  31) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D1_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_D5_PERDIOD           (  43) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D5_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_D1_DUTY              (  32) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D1_DUTY' */
#define LABEL_MCS0_MEM_CYL6_EPWS_CLOSE           (1225) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_CLOSE' */
#define LABEL_MCS0_MEM_CYL0_COIL_SETTINGS        (  33) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_COIL_SETTINGS' */
#define LABEL_MCS0_MEM_CYL0_D2_DURATION          (  33) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D2_DURATION' */
#define LABEL_MCS0_MEM_CYL0_D2_PERDIOD           (  34) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D2_PERDIOD' */
#define LABEL_MCS0_MEM_CYL2_WAIT_INPUT_SIGNAL       ( 710) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_WAIT_INPUT_SIGNAL' */
#define LABEL_MCS0_MEM_CYL4_EPWS_LAST_PULSE_END       (1083) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_LAST_PULSE_END' */
#define LABEL_MCS0_MEM_CYL0_D2_DUTY              (  35) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D2_DUTY' */
#define LABEL_MCS0_MEM_CYL6_BUCK_DELAY           (1135) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_BUCK_DELAY' */
#define LABEL_MCS0_MEM_CYL0_D3_DURATION          (  36) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D3_DURATION' */
#define LABEL_MCS0_MEM_CYL0_D3_PERDIOD           (  37) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D3_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_D4_DURATION          (  39) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D4_DURATION' */
#define LABEL_MCS0_MEM_MCS0_PRIM_DISABLE_NORMAL       (1526) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIM_DISABLE_NORMAL' */
#define LABEL_MCS0_MEM_CYL4_ABORT_COM_OUT        ( 985) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_ABORT_COM_OUT' */
#define LABEL_MCS0_MEM_CYL0_D4_DUTY              (  41) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D4_DUTY' */
#define LABEL_MCS0_MEM_CYL0_D5_DURATION          (  42) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D5_DURATION' */
#define LABEL_MCS0_MEM_CYL2_PSM_DATA_LOADED       ( 707) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_PSM_DATA_LOADED' */
#define LABEL_MCS0_MEM_CYL2_T_OFF_STEP           ( 405) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_T_OFF_STEP' */
#define LABEL_MCS0_MEM_CYL4_WAIT_BUCK_OFF        ( 980) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_WAIT_BUCK_OFF' */
#define LABEL_MCS0_MEM_CYL0_D5_DUTY              (  44) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D5_DUTY' */
#define LABEL_MCS0_MEM_CYL4_D8_DUTY              ( 147) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D8_DUTY' */
#define LABEL_MCS0_MEM_CYL0_D6_PERDIOD           (  46) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D6_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_D6_DUTY              (  47) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D6_DUTY' */
#define LABEL_MCS0_MEM_CYL0_D7_DURATION          (  48) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D7_DURATION' */
#define LABEL_MCS0_MEM_CYL0_D7_PERDIOD           (  49) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D7_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_D7_DUTY              (  50) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D7_DUTY' */
#define LABEL_MCS0_MEM_CYL0_D8_PERDIOD           (  52) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D8_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_D8_DURATION          (  51) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D8_DURATION' */
#define LABEL_MCS0_MEM_CYL0_D8_DUTY              (  53) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_D8_DUTY' */
#define LABEL_MCS0_MEM_CYL2_CYLINDER_INDEX       (  56) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_CYLINDER_INDEX' */
#define LABEL_MCS0_MEM_CYL6_PSM_SETUP            (1104) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_PSM_SETUP' */
#define LABEL_MCS0_MEM_CYL2_EPWS                 (  58) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS' */
#define LABEL_MCS0_MEM_RESET_COIL_STATUS         (1343) /* Index into C-array 'mcs0_mem' for assembler label 'RESET_COIL_STATUS' */
#define LABEL_MCS0_MEM_CYL0_LOAD_PSM             ( 491) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_LOAD_PSM' */
#define LABEL_MCS0_MEM_CYL2_EPWS_DATA            (  59) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_DATA' */
#define LABEL_MCS0_MEM_CYL2_EPWS1_PERIOD         (  60) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS1_PERIOD' */
#define LABEL_MCS0_MEM_CYL4_CYLINDER_INDEX       ( 103) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_CYLINDER_INDEX' */
#define LABEL_MCS0_MEM_CYL2_COIL_UNLOADING_STEP       (  76) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_COIL_UNLOADING_STEP' */
#define LABEL_MCS0_MEM_CYL2_EPWS2_PERIOD         (  63) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS2_PERIOD' */
#define LABEL_MCS0_MEM_CYL2_EPWS2_DUTY           (  64) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS2_DUTY' */
#define LABEL_MCS0_MEM_CYL6_EPWS1_DUTY           ( 155) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS1_DUTY' */
#define LABEL_MCS0_MEM_CYL0_EPWS_WAIT_CPU_TRG       ( 598) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_WAIT_CPU_TRG' */
#define LABEL_MCS0_MEM_CYL0_WAIT_BUCK_OFF        ( 569) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_WAIT_BUCK_OFF' */
#define LABEL_MCS0_MEM_CYL2_EPWS3_PERIOD         (  66) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS3_PERIOD' */
#define LABEL_MCS0_MEM_CYL2_COIL_SETTINGS        (  80) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_COIL_SETTINGS' */
#define LABEL_MCS0_MEM_CYL2_EPWS_MODE            ( 798) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_MODE' */
#define LABEL_MCS0_MEM_CYL2_EPWS3_DUTY           (  67) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS3_DUTY' */
#define LABEL_MCS0_MEM_CYL2_EPWS4_N_PULSE        (  68) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS4_N_PULSE' */
#define LABEL_MCS0_MEM_CYL2_EPWS4_PERIOD         (  69) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS4_PERIOD' */
#define LABEL_MCS0_MEM_CYL4_EPWS1_N_PULSE        ( 106) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS1_N_PULSE' */
#define LABEL_MCS0_MEM_CYL2_EPWS4_DUTY           (  70) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS4_DUTY' */
#define LABEL_MCS0_MEM_BUCK0_INIT                (1347) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_INIT' */
#define LABEL_MCS0_MEM_BUCK0_PRIMARY_INIT        (1500) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_PRIMARY_INIT' */
#define LABEL_MCS0_MEM_CYL2_EPWS_TIMEOUT         (  72) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_TIMEOUT' */
#define LABEL_MCS0_MEM_CYL6_EPWS_PH1_START       (1234) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_PH1_START' */
#define LABEL_MCS0_MEM_B0_U_EXIT                 ( 355) /* Index into C-array 'mcs0_mem' for assembler label 'B0_U_EXIT' */
#define LABEL_MCS0_MEM_CYL2_NMOS_DURATION        (  74) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_NMOS_DURATION' */
#define LABEL_MCS0_MEM_CYL2_COIL_LOADING_STEP       (  75) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_COIL_LOADING_STEP' */
#define LABEL_MCS0_MEM_CYL0_STOP_OUTPUT          ( 555) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_STOP_OUTPUT' */
#define LABEL_MCS0_MEM_CYL2_D1_PERDIOD           (  78) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D1_PERDIOD' */
#define LABEL_MCS0_MEM_CYL2_WAIT_NEW_CONFIG       ( 715) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_WAIT_NEW_CONFIG' */
#define LABEL_MCS0_MEM_CYL2_D1_DUTY              (  79) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D1_DUTY' */
#define LABEL_MCS0_MEM_CYL4_D6_PERDIOD           ( 140) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D6_PERDIOD' */
#define LABEL_MCS0_MEM_CYL4_STACK                ( 245) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_STACK' */
#define LABEL_MCS0_MEM_CYL0_EPWS_END             ( 612) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_END' */
#define LABEL_MCS0_MEM_CYL2_D2_DURATION          (  80) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D2_DURATION' */
#define LABEL_MCS0_MEM_CYL2_EPWS_CHECK           ( 766) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_CHECK' */
#define LABEL_MCS0_MEM_CYL2_D2_PERDIOD           (  81) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D2_PERDIOD' */
#define LABEL_MCS0_MEM_CYL2_D2_DUTY              (  82) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D2_DUTY' */
#define LABEL_MCS0_MEM_CYL2_D3_PERDIOD           (  84) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D3_PERDIOD' */
#define LABEL_MCS0_MEM_BUCK0_EN_DONE             (1346) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_EN_DONE' */
#define LABEL_MCS0_MEM_CYL2_ABORT_COM_OUT        ( 780) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_ABORT_COM_OUT' */
#define LABEL_MCS0_MEM_CYL2_D3_DUTY              (  85) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D3_DUTY' */
#define LABEL_MCS0_MEM_CYL0_EPWS_RET             ( 393) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_RET' */
#define LABEL_MCS0_MEM_CYL2_D4_PERDIOD           (  87) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D4_PERDIOD' */
#define LABEL_MCS0_MEM_CYL4_EPWS_WAIT_BUCK_OFF_1       (1021) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_WAIT_BUCK_OFF_1' */
#define LABEL_MCS0_MEM_CYL2_D4_DUTY              (  88) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D4_DUTY' */
#define LABEL_MCS0_MEM_CYL2_D5_DURATION          (  89) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D5_DURATION' */
#define LABEL_MCS0_MEM_TSK4_STACK                ( 277) /* Index into C-array 'mcs0_mem' for assembler label 'TSK4_STACK' */
#define LABEL_MCS0_MEM_CYL2_D5_DUTY              (  91) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D5_DUTY' */
#define LABEL_MCS0_MEM_CYL4_EPWS_PH2_START       (1038) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_PH2_START' */
#define LABEL_MCS0_MEM_CYL4_STOP_OUTPUT          ( 966) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_STOP_OUTPUT' */
#define LABEL_MCS0_MEM_CYL2_D6_PERDIOD           (  93) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D6_PERDIOD' */
#define LABEL_MCS0_MEM_CYL6_START                (1099) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_START' */
#define LABEL_MCS0_MEM_CYL2_D6_DUTY              (  94) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D6_DUTY' */
#define LABEL_MCS0_MEM_CYL2_D7_DURATION          (  95) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D7_DURATION' */
#define LABEL_MCS0_MEM_CYL0_EPWS_FUNC            ( 373) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_FUNC' */
#define LABEL_MCS0_MEM_CYL2_D8_DURATION          (  98) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D8_DURATION' */
#define LABEL_MCS0_MEM_CYL6_EPWS                 ( 152) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS' */
#define LABEL_MCS0_MEM_CYL2_D8_PERDIOD           (  99) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_D8_PERDIOD' */
#define LABEL_MCS0_MEM_CYL2_EPWS_LAST_LOOP       ( 870) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_LAST_LOOP' */
#define LABEL_MCS0_MEM_CYL2_END_CONF_PARAM       ( 101) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_END_CONF_PARAM' */
#define LABEL_MCS0_MEM_CYL6_EPWS_LAST_PULSE       ( 165) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_LAST_PULSE' */
#define LABEL_MCS0_MEM_CYL4_PSM_SIZE             ( 102) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_PSM_SIZE' */
#define LABEL_MCS0_MEM_CYL4_OUTPUT_DELAY         ( 104) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_OUTPUT_DELAY' */
#define LABEL_MCS0_MEM_CYL4_D2_DURATION          ( 127) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D2_DURATION' */
#define LABEL_MCS0_MEM_CYL4_EPWS                 ( 105) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS' */
#define LABEL_MCS0_MEM_CYL4_D6_DURATION          ( 139) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D6_DURATION' */
#define LABEL_MCS0_MEM_CYL4_EPWS_DATA            ( 106) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_DATA' */
#define LABEL_MCS0_MEM_CYL4_EPWS1_PERIOD         ( 107) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS1_PERIOD' */
#define LABEL_MCS0_MEM_CYL2_EPWS_FUNC            ( 397) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_FUNC' */
#define LABEL_MCS0_MEM_CYL4_EPWS1_DUTY           ( 108) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS1_DUTY' */
#define LABEL_MCS0_MEM_CYL6_D4_DUTY              ( 182) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D4_DUTY' */
#define LABEL_MCS0_MEM_CYL4_EPWS2_N_PULSE        ( 109) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS2_N_PULSE' */
#define LABEL_MCS0_MEM_CYL4_EPWS2_DUTY           ( 111) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS2_DUTY' */
#define LABEL_MCS0_MEM_CYL6_EPWS_PH2_START       (1246) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_PH2_START' */
#define LABEL_MCS0_MEM_CYL4_EPWS3_PERIOD         ( 113) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS3_PERIOD' */
#define LABEL_MCS0_MEM_CYL4_EPWS3_DUTY           ( 114) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS3_DUTY' */
#define LABEL_MCS0_MEM_CYL4_TIMEOUT              ( 990) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_TIMEOUT' */
#define LABEL_MCS0_MEM_CYL0_TIMEOUT              ( 582) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_TIMEOUT' */
#define LABEL_MCS0_MEM_CYL4_EPWS4_N_PULSE        ( 115) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS4_N_PULSE' */
#define LABEL_MCS0_MEM_CYL4_EPWS4_DUTY           ( 117) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS4_DUTY' */
#define LABEL_MCS0_MEM_CYL4_EPWS_LAST_PULSE       ( 118) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_LAST_PULSE' */
#define LABEL_MCS0_MEM_CYL4_PMOS_DURATION        ( 120) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_PMOS_DURATION' */
#define LABEL_MCS0_MEM_CYL4_COIL_LOADING_STEP       ( 122) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_COIL_LOADING_STEP' */
#define LABEL_MCS0_MEM_CYL4_COIL_UNLOADING_STEP       ( 123) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_COIL_UNLOADING_STEP' */
#define LABEL_MCS0_MEM_CYL4_D1_DURATION          ( 124) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D1_DURATION' */
#define LABEL_MCS0_MEM_CYL4_D1_DUTY              ( 126) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D1_DUTY' */
#define LABEL_MCS0_MEM_CYL4_COIL_SETTINGS        ( 127) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_COIL_SETTINGS' */
#define LABEL_MCS0_MEM_BUCK0_TOP_LOOP_WA         (1417) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_TOP_LOOP_WA' */
#define LABEL_MCS0_MEM_CYL4_D2_PERDIOD           ( 128) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D2_PERDIOD' */
#define LABEL_MCS0_MEM_CYL4_D2_DUTY              ( 129) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D2_DUTY' */
#define LABEL_MCS0_MEM_CYL4_EPWS_FUNC            ( 421) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_FUNC' */
#define LABEL_MCS0_MEM_CYL6_D6_PERDIOD           ( 187) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D6_PERDIOD' */
#define LABEL_MCS0_MEM_BUCK0_IPRI_ISEC_CURRENT_CYLINDER       ( 204) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_IPRI_ISEC_CURRENT_CYLINDER' */
#define LABEL_MCS0_MEM_CYL0_EPWS_LAST_START       ( 663) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_LAST_START' */
#define LABEL_MCS0_MEM_CYL4_D3_DURATION          ( 130) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D3_DURATION' */
#define LABEL_MCS0_MEM_CYL4_D3_DUTY              ( 132) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D3_DUTY' */
#define LABEL_MCS0_MEM_BUCK0_DONE                (1498) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_DONE' */
#define LABEL_MCS0_MEM_CYL4_D4_PERDIOD           ( 134) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D4_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_EPWS_START           ( 613) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_START' */
#define LABEL_MCS0_MEM_CYL4_D5_DURATION          ( 136) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D5_DURATION' */
#define LABEL_MCS0_MEM_SET_BUCK_ON               (1304) /* Index into C-array 'mcs0_mem' for assembler label 'SET_BUCK_ON' */
#define LABEL_MCS0_MEM_BUCK0_LOADING             (1379) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_LOADING' */
#define LABEL_MCS0_MEM_CYL4_D5_PERDIOD           ( 137) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D5_PERDIOD' */
#define LABEL_MCS0_MEM_CYL4_EPWS_PH3_START       (1050) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_PH3_START' */
#define LABEL_MCS0_MEM_CYL4_PSM_SETUP            ( 896) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_PSM_SETUP' */
#define LABEL_MCS0_MEM_CYL4_D6_DUTY              ( 141) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D6_DUTY' */
#define LABEL_MCS0_MEM_CYL0_B_DELAY_WA           ( 521) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_B_DELAY_WA' */
#define LABEL_MCS0_MEM_CYL4_D8_DURATION          ( 145) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D8_DURATION' */
#define LABEL_MCS0_MEM_CYL4_D8_PERDIOD           ( 146) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_D8_PERDIOD' */
#define LABEL_MCS0_MEM_CYL4_END_CONF_PARAM       ( 148) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_END_CONF_PARAM' */
#define LABEL_MCS0_MEM_CYL2_EPWS_LOOP            ( 413) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_LOOP' */
#define LABEL_MCS0_MEM_CYL6_PSM_SIZE             ( 149) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_PSM_SIZE' */
#define LABEL_MCS0_MEM_CYL6_CYLINDER_INDEX       ( 150) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_CYLINDER_INDEX' */
#define LABEL_MCS0_MEM_CYL6_EPWS_LAST_TIME_OUT       (1295) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_LAST_TIME_OUT' */
#define LABEL_MCS0_MEM_CYL6_OUTPUT_DELAY         ( 151) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_OUTPUT_DELAY' */
#define LABEL_MCS0_MEM_CYL6_EPWS1_PERIOD         ( 154) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS1_PERIOD' */
#define LABEL_MCS0_MEM_CYL6_EPWS2_N_PULSE        ( 156) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS2_N_PULSE' */
#define LABEL_MCS0_MEM_BUCK0_EN_DISABLE          (1440) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_EN_DISABLE' */
#define LABEL_MCS0_MEM_CYL6_EPWS2_PERIOD         ( 157) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS2_PERIOD' */
#define LABEL_MCS0_MEM_CYL6_EPWS2_DUTY           ( 158) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS2_DUTY' */
#define LABEL_MCS0_MEM_CYL2_EPWS_TIME_OUT        ( 806) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_TIME_OUT' */
#define LABEL_MCS0_MEM_CYL6_EPWS3_N_PULSE        ( 159) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS3_N_PULSE' */
#define LABEL_MCS0_MEM_CYL6_EPWS3_PERIOD         ( 160) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS3_PERIOD' */
#define LABEL_MCS0_MEM_CYL6_INT_VAR              ( 199) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_INT_VAR' */
#define LABEL_MCS0_MEM_CYL4_B_DELAY_WA           ( 932) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_B_DELAY_WA' */
#define LABEL_MCS0_MEM_CYL6_EPWS3_DUTY           ( 161) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS3_DUTY' */
#define LABEL_MCS0_MEM_CYL6_EPWS4_N_PULSE        ( 162) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS4_N_PULSE' */
#define LABEL_MCS0_MEM_CYL6_EPWS4_PERIOD         ( 163) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS4_PERIOD' */
#define LABEL_MCS0_MEM_CYL6_EPWS_TIMEOUT         ( 166) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_TIMEOUT' */
#define LABEL_MCS0_MEM_BUCK0_EN_INIT             (1299) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_EN_INIT' */
#define LABEL_MCS0_MEM_CYL4_EPWS_RET             ( 441) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_RET' */
#define LABEL_MCS0_MEM_CYL6_PMOS_DURATION        ( 167) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_PMOS_DURATION' */
#define LABEL_MCS0_MEM_CYL6_NMOS_DURATION        ( 168) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_NMOS_DURATION' */
#define LABEL_MCS0_MEM_BUCK0_SET_PTR             (1452) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_SET_PTR' */
#define LABEL_MCS0_MEM_CYL6_COIL_LOADING_STEP       ( 169) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_COIL_LOADING_STEP' */
#define LABEL_MCS0_MEM_CYL6_COIL_UNLOADING_STEP       ( 170) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_COIL_UNLOADING_STEP' */
#define LABEL_MCS0_MEM_CYL6_EPWS_MODE            (1211) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_MODE' */
#define LABEL_MCS0_MEM_CYL6_COIL_SETTINGS        ( 174) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_COIL_SETTINGS' */
#define LABEL_MCS0_MEM_CYL0_CHECK_BUCK_AVB       ( 529) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_CHECK_BUCK_AVB' */
#define LABEL_MCS0_MEM_CYL4_WAIT_BUCK_OFF_1       ( 992) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_WAIT_BUCK_OFF_1' */
#define LABEL_MCS0_MEM_CYL6_D2_DURATION          ( 174) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D2_DURATION' */
#define LABEL_MCS0_MEM_CYL6_D2_PERDIOD           ( 175) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D2_PERDIOD' */
#define LABEL_MCS0_MEM_CYL6_D2_DUTY              ( 176) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D2_DUTY' */
#define LABEL_MCS0_MEM_CYL6_D3_PERDIOD           ( 178) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D3_PERDIOD' */
#define LABEL_MCS0_MEM_CYL6_D3_DUTY              ( 179) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D3_DUTY' */
#define LABEL_MCS0_MEM_CYL6_D4_DURATION          ( 180) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D4_DURATION' */
#define LABEL_MCS0_MEM_CYL6_D4_PERDIOD           ( 181) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D4_PERDIOD' */
#define LABEL_MCS0_MEM_CYL0_STACK                ( 213) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_STACK' */
#define LABEL_MCS0_MEM_CYL6_D5_PERDIOD           ( 184) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D5_PERDIOD' */
#define LABEL_MCS0_MEM_CYL6_D5_DUTY              ( 185) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D5_DUTY' */
#define LABEL_MCS0_MEM_CYL6_D6_DURATION          ( 186) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D6_DURATION' */
#define LABEL_MCS0_MEM_CYL6_EPWS_START           (1232) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_START' */
#define LABEL_MCS0_MEM_CYL6_D6_DUTY              ( 188) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D6_DUTY' */
#define LABEL_MCS0_MEM_CYL6_D7_PERDIOD           ( 190) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D7_PERDIOD' */
#define LABEL_MCS0_MEM_BUCK0_LPD_FAULT           (1442) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_LPD_FAULT' */
#define LABEL_MCS0_MEM_CYL0_WAIT_BUCK_OFF_1       ( 584) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_WAIT_BUCK_OFF_1' */
#define LABEL_MCS0_MEM_CYL6_D7_DUTY              ( 191) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D7_DUTY' */
#define LABEL_MCS0_MEM_CYL4_INIT                 ( 883) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_INIT' */
#define LABEL_MCS0_MEM_CYL6_D8_DURATION          ( 192) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D8_DURATION' */
#define LABEL_MCS0_MEM_CYL6_D8_PERDIOD           ( 193) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D8_PERDIOD' */
#define LABEL_MCS0_MEM_CYL6_D8_DUTY              ( 194) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_D8_DUTY' */
#define LABEL_MCS0_MEM_CYL6_END_CONF_PARAM       ( 195) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_END_CONF_PARAM' */
#define LABEL_MCS0_MEM_B0_L_EXIT                 ( 372) /* Index into C-array 'mcs0_mem' for assembler label 'B0_L_EXIT' */
#define LABEL_MCS0_MEM_CYL0_INT_VAR              ( 196) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_INT_VAR' */
#define LABEL_MCS0_MEM_CYL2_INT_VAR              ( 197) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_INT_VAR' */
#define LABEL_MCS0_MEM_CYL2_EPWS_RET             ( 417) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_RET' */
#define LABEL_MCS0_MEM_CYL4_INT_VAR              ( 198) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_INT_VAR' */
#define LABEL_MCS0_MEM_CYL0_DATA_LOADED          ( 200) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_DATA_LOADED' */
#define LABEL_MCS0_MEM_CYL4_COMM_OUTPUT_DELAY       ( 943) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_COMM_OUTPUT_DELAY' */
#define LABEL_MCS0_MEM_CYL4_DATA_LOADED          ( 202) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_DATA_LOADED' */
#define LABEL_MCS0_MEM_BUCK0_STEP_ISR_VAR        ( 206) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_STEP_ISR_VAR' */
#define LABEL_MCS0_MEM_BUCK0_CURRENT_CYLINDER       ( 207) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_CURRENT_CYLINDER' */
#define LABEL_MCS0_MEM_BUCK0_CURRENT_PTR_DATA       ( 208) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_CURRENT_PTR_DATA' */
#define LABEL_MCS0_MEM_CYL2_EPWS_LAST_START       ( 866) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_LAST_START' */
#define LABEL_MCS0_MEM_BUCK0_CURRENT_LOADING_STEP       ( 209) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_CURRENT_LOADING_STEP' */
#define LABEL_MCS0_MEM_BUCK0_PRIMARY_SAMPLE       ( 211) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_PRIMARY_SAMPLE' */
#define LABEL_MCS0_MEM_BUCK0_PRIMARY_STACK       ( 293) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_PRIMARY_STACK' */
#define LABEL_MCS0_MEM_BUCK0_EN_STACK            ( 309) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_EN_STACK' */
#define LABEL_MCS0_MEM_FBUCK0_UNLOADING          ( 341) /* Index into C-array 'mcs0_mem' for assembler label 'FBUCK0_UNLOADING' */
#define LABEL_MCS0_MEM_B0_U_WA                   ( 351) /* Index into C-array 'mcs0_mem' for assembler label 'B0_U_WA' */
#define LABEL_MCS0_MEM_B0_L_LOOP                 ( 368) /* Index into C-array 'mcs0_mem' for assembler label 'B0_L_LOOP' */
#define LABEL_MCS0_MEM_CYL0_EPWS_LOOP            ( 389) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_LOOP' */
#define LABEL_MCS0_MEM_CYL0_T_ON_START           ( 373) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_T_ON_START' */
#define LABEL_MCS0_MEM_CYL0_EPWS_WAIT_T_ON_MATCH       ( 376) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_WAIT_T_ON_MATCH' */
#define LABEL_MCS0_MEM_CYL0_T_OFF_STEP           ( 381) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_T_OFF_STEP' */
#define LABEL_MCS0_MEM_CYL4_EPWS_WAIT_T_ON_MATCH       ( 424) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_WAIT_T_ON_MATCH' */
#define LABEL_MCS0_MEM_CYL0_EPWS_TIME_OUT_RET       ( 395) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_TIME_OUT_RET' */
#define LABEL_MCS0_MEM_CYL2_T_ON_START           ( 397) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_T_ON_START' */
#define LABEL_MCS0_MEM_CYL2_EPWS_WAIT_T_ON_MATCH       ( 400) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_WAIT_T_ON_MATCH' */
#define LABEL_MCS0_MEM_CYL6_EPWS_PH3_START       (1258) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_PH3_START' */
#define LABEL_MCS0_MEM_CYL2_EPWS_WAIT_T_OFF_MATCH       ( 408) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_WAIT_T_OFF_MATCH' */
#define LABEL_MCS0_MEM_CYL2_EPWS_TIME_OUT_RET       ( 419) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_TIME_OUT_RET' */
#define LABEL_MCS0_MEM_CYL4_T_ON_START           ( 421) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_T_ON_START' */
#define LABEL_MCS0_MEM_CYL4_T_OFF_STEP           ( 429) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_T_OFF_STEP' */
#define LABEL_MCS0_MEM_CYL4_EPWS_LOOP            ( 437) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_LOOP' */
#define LABEL_MCS0_MEM_CYL4_EPWS_TIME_OUT_RET       ( 443) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_TIME_OUT_RET' */
#define LABEL_MCS0_MEM_CYL6_EPWS_PH4_START       (1270) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_PH4_START' */
#define LABEL_MCS0_MEM_CYL6_T_ON_START           ( 445) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_T_ON_START' */
#define LABEL_MCS0_MEM_CYL4_EPWS_LAST_LOOP       (1078) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_LAST_LOOP' */
#define LABEL_MCS0_MEM_CYL6_EPWS_WAIT_T_OFF_MATCH       ( 456) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_WAIT_T_OFF_MATCH' */
#define LABEL_MCS0_MEM_CYL6_EPWS_LOOP            ( 461) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_LOOP' */
#define LABEL_MCS0_MEM_CYL6_EPWS_TIME_OUT_RET       ( 467) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_TIME_OUT_RET' */
#define LABEL_MCS0_MEM_CYL0_DELAY                ( 541) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_DELAY' */
#define LABEL_MCS0_MEM_CYL0_INIT                 ( 470) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_INIT' */
#define LABEL_MCS0_MEM_CYL2_RESTART              ( 777) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_RESTART' */
#define LABEL_MCS0_MEM_CYL0_CHECK_EDGE           ( 512) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_CHECK_EDGE' */
#define LABEL_MCS0_MEM_CYL0_BUCK_DELAY           ( 516) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_BUCK_DELAY' */
#define LABEL_MCS0_MEM_CYL0_COMM_OUTPUT_DELAY       ( 532) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_COMM_OUTPUT_DELAY' */
#define LABEL_MCS0_MEM_CYL0_DELAY_WA             ( 537) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_DELAY_WA' */
#define LABEL_MCS0_MEM_CYL0_INPUT_CLOSE          ( 563) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_INPUT_CLOSE' */
#define LABEL_MCS0_MEM_CYL6_STOP_OUTPUT          (1174) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_STOP_OUTPUT' */
#define LABEL_MCS0_MEM_CYL0_RESTART              ( 571) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_RESTART' */
#define LABEL_MCS0_MEM_CYL0_ABORT_COM_OUT        ( 574) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_ABORT_COM_OUT' */
#define LABEL_MCS0_MEM_CYL0_DONE                 ( 592) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_DONE' */
#define LABEL_MCS0_MEM_CYL0_EPWS_PH1_START       ( 615) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_PH1_START' */
#define LABEL_MCS0_MEM_CYL0_EPWS_MODE            ( 595) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_MODE' */
#define LABEL_MCS0_MEM_CYL0_EPWS_TIME_OUT        ( 603) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_TIME_OUT' */
#define LABEL_MCS0_MEM_CYL0_EPWS_WAIT_BUCK_OFF_1       ( 610) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_WAIT_BUCK_OFF_1' */
#define LABEL_MCS0_MEM_CYL0_EPWS_PH4_START       ( 651) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_PH4_START' */
#define LABEL_MCS0_MEM_CYL0_EPWS_LAST_LOOP       ( 667) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_LAST_LOOP' */
#define LABEL_MCS0_MEM_CYL4_WAIT_NEW_CONFIG       ( 920) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_WAIT_NEW_CONFIG' */
#define LABEL_MCS0_MEM_CYL0_EPWS_LAST_PULSE_END       ( 672) /* Index into C-array 'mcs0_mem' for assembler label 'CYL0_EPWS_LAST_PULSE_END' */
#define LABEL_MCS0_MEM_CYL2_EPWS_PH4_START       ( 854) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_PH4_START' */
#define LABEL_MCS0_MEM_CYL2_INIT                 ( 678) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_INIT' */
#define LABEL_MCS0_MEM_CYL2_START                ( 686) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_START' */
#define LABEL_MCS0_MEM_CYL2_PSM_SETUP            ( 691) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_PSM_SETUP' */
#define LABEL_MCS0_MEM_CYL2_LOAD_PSM             ( 699) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_LOAD_PSM' */
#define LABEL_MCS0_MEM_CYL4_DELAY_WA             ( 948) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_DELAY_WA' */
#define LABEL_MCS0_MEM_B0_UNLOADING              (1422) /* Index into C-array 'mcs0_mem' for assembler label 'B0_UNLOADING' */
#define LABEL_MCS0_MEM_CYL2_BUCK_DELAY           ( 722) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_BUCK_DELAY' */
#define LABEL_MCS0_MEM_CYL2_EPWS_LAST_PULSE_END       ( 875) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_LAST_PULSE_END' */
#define LABEL_MCS0_MEM_CYL2_B_DELAY              ( 731) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_B_DELAY' */
#define LABEL_MCS0_MEM_CYL2_COMM_OUTPUT_DELAY       ( 738) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_COMM_OUTPUT_DELAY' */
#define LABEL_MCS0_MEM_CYL2_DELAY_WA             ( 743) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_DELAY_WA' */
#define LABEL_MCS0_MEM_CYL6_WAIT_INPUT_SIGNAL       (1123) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_WAIT_INPUT_SIGNAL' */
#define LABEL_MCS0_MEM_CYL2_DELAY                ( 747) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_DELAY' */
#define LABEL_MCS0_MEM_CYL6_EPWS_STOP_IPRI       (1225) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_STOP_IPRI' */
#define LABEL_MCS0_MEM_CYL2_STOP_OUTPUT          ( 761) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_STOP_OUTPUT' */
#define LABEL_MCS0_MEM_CYL2_INPUT_CLOSE          ( 769) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_INPUT_CLOSE' */
#define LABEL_MCS0_MEM_CYL2_WAIT_BUCK_OFF        ( 775) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_WAIT_BUCK_OFF' */
#define LABEL_MCS0_MEM_CYL2_TIMEOUT              ( 785) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_TIMEOUT' */
#define LABEL_MCS0_MEM_CYL2_WAIT_BUCK_OFF_1       ( 787) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_WAIT_BUCK_OFF_1' */
#define LABEL_MCS0_MEM_CYL2_DONE                 ( 795) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_DONE' */
#define LABEL_MCS0_MEM_TRIGGER_BUCK0_EN          (1384) /* Index into C-array 'mcs0_mem' for assembler label 'TRIGGER_BUCK0_EN' */
#define LABEL_MCS0_MEM_TSK4_INIT                 (1535) /* Index into C-array 'mcs0_mem' for assembler label 'TSK4_INIT' */
#define LABEL_MCS0_MEM_CYL2_EPWS_WAIT_CPU_TRG       ( 801) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_WAIT_CPU_TRG' */
#define LABEL_MCS0_MEM_CYL2_EPWS_CLOSE           ( 809) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_CLOSE' */
#define LABEL_MCS0_MEM_CYL2_EPWS_STOP_IPRI       ( 809) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_STOP_IPRI' */
#define LABEL_MCS0_MEM_CYL2_EPWS_END             ( 815) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_END' */
#define LABEL_MCS0_MEM_CYL2_EPWS_START           ( 816) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_START' */
#define LABEL_MCS0_MEM_CYL2_EPWS_PH2_START       ( 830) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_PH2_START' */
#define LABEL_MCS0_MEM_CYL2_EPWS_PH3_START       ( 842) /* Index into C-array 'mcs0_mem' for assembler label 'CYL2_EPWS_PH3_START' */
#define LABEL_MCS0_MEM_CYL4_LOAD_PSM             ( 904) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_LOAD_PSM' */
#define LABEL_MCS0_MEM_CYL4_PSM_DATA_LOADED       ( 912) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_PSM_DATA_LOADED' */
#define LABEL_MCS0_MEM_CYL4_CHECK_EDGE           ( 923) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_CHECK_EDGE' */
#define LABEL_MCS0_MEM_CYL4_BUCK_DELAY           ( 927) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_BUCK_DELAY' */
#define LABEL_MCS0_MEM_CYL4_B_DELAY              ( 936) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_B_DELAY' */
#define LABEL_MCS0_MEM_CYL4_EPWS_CHECK           ( 971) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_CHECK' */
#define LABEL_MCS0_MEM_CYL4_RESTART              ( 982) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_RESTART' */
#define LABEL_MCS0_MEM_CYL4_EPWS_MODE            (1003) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_MODE' */
#define LABEL_MCS0_MEM_CYL4_EPWS_CLOSE           (1017) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_CLOSE' */
#define LABEL_MCS0_MEM_BUCK0_UNLOADING           (1425) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_UNLOADING' */
#define LABEL_MCS0_MEM_CYL4_EPWS_STOP_IPRI       (1017) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_STOP_IPRI' */
#define LABEL_MCS0_MEM_CYL4_EPWS_END             (1023) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_END' */
#define LABEL_MCS0_MEM_CYL4_EPWS_PH4_START       (1062) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_PH4_START' */
#define LABEL_MCS0_MEM_MCS0_PRIM_CURR            (1508) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIM_CURR' */
#define LABEL_MCS0_MEM_CYL4_EPWS_LAST_START       (1074) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_LAST_START' */
#define LABEL_MCS0_MEM_CYL4_EPWS_LAST_TIME_OUT       (1087) /* Index into C-array 'mcs0_mem' for assembler label 'CYL4_EPWS_LAST_TIME_OUT' */
#define LABEL_MCS0_MEM_BUCK0_STEPS               (1385) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_STEPS' */
#define LABEL_MCS0_MEM_CYL6_PSM_DATA_LOADED       (1120) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_PSM_DATA_LOADED' */
#define LABEL_MCS0_MEM_CYL6_WAIT_NEW_CONFIG       (1128) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_WAIT_NEW_CONFIG' */
#define LABEL_MCS0_MEM_CYL6_CHECK_EDGE           (1131) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_CHECK_EDGE' */
#define LABEL_MCS0_MEM_CYL6_B_DELAY_WA           (1140) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_B_DELAY_WA' */
#define LABEL_MCS0_MEM_CYL6_DONE                 (1208) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_DONE' */
#define LABEL_MCS0_MEM_CYL6_COMM_OUTPUT_DELAY       (1151) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_COMM_OUTPUT_DELAY' */
#define LABEL_MCS0_MEM_CYL6_DELAY_WA             (1156) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_DELAY_WA' */
#define LABEL_MCS0_MEM_CYL6_DELAY                (1160) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_DELAY' */
#define LABEL_MCS0_MEM_CYL6_EPWS_CHECK           (1179) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_CHECK' */
#define LABEL_MCS0_MEM_CYL6_INPUT_CLOSE          (1182) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_INPUT_CLOSE' */
#define LABEL_MCS0_MEM_CYL6_RESTART              (1190) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_RESTART' */
#define LABEL_MCS0_MEM_CYL6_TIMEOUT              (1198) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_TIMEOUT' */
#define LABEL_MCS0_MEM_CYL6_WAIT_BUCK_OFF_1       (1200) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_WAIT_BUCK_OFF_1' */
#define LABEL_MCS0_MEM_CYL6_EPWS_WAIT_CPU_TRG       (1217) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_WAIT_CPU_TRG' */
#define LABEL_MCS0_MEM_CYL6_EPWS_TIME_OUT        (1222) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_TIME_OUT' */
#define LABEL_MCS0_MEM_CYL6_EPWS_WAIT_BUCK_OFF_1       (1229) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_WAIT_BUCK_OFF_1' */
#define LABEL_MCS0_MEM_CYL6_EPWS_END             (1231) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_END' */
#define LABEL_MCS0_MEM_BUCK0_DISABLE             (1436) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_DISABLE' */
#define LABEL_MCS0_MEM_CYL6_EPWS_LAST_START       (1282) /* Index into C-array 'mcs0_mem' for assembler label 'CYL6_EPWS_LAST_START' */
#define LABEL_MCS0_MEM_MCS0_PRIM_DISABLE_REASON       (1520) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIM_DISABLE_REASON' */
#define LABEL_MCS0_MEM_BUCK0_EN_START            (1303) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_EN_START' */
#define LABEL_MCS0_MEM_MOS_START                 (1310) /* Index into C-array 'mcs0_mem' for assembler label 'MOS_START' */
#define LABEL_MCS0_MEM_CYLINDER2                 (1323) /* Index into C-array 'mcs0_mem' for assembler label 'CYLINDER2' */
#define LABEL_MCS0_MEM_CYLINDER4                 (1328) /* Index into C-array 'mcs0_mem' for assembler label 'CYLINDER4' */
#define LABEL_MCS0_MEM_BUCK0_EN_WAIT_CLOSURE       (1338) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_EN_WAIT_CLOSURE' */
#define LABEL_MCS0_MEM_BUCK_B0_INIT              (1359) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK_B0_INIT' */
#define LABEL_MCS0_MEM_BUCK0_TOP_LOADING         (1397) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_TOP_LOADING' */
#define LABEL_MCS0_MEM_BUCK0_TIMEOUT_CHECK       (1411) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_TIMEOUT_CHECK' */
#define LABEL_MCS0_MEM_BUCK0_TOP_LOOP            (1419) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_TOP_LOOP' */
#define LABEL_MCS0_MEM_BUCK0_END_LPD_FAULT       (1458) /* Index into C-array 'mcs0_mem' for assembler label 'BUCK0_END_LPD_FAULT' */
#define LABEL_MCS0_MEM_SETUP_CYLINDER6           (1488) /* Index into C-array 'mcs0_mem' for assembler label 'SETUP_CYLINDER6' */
#define LABEL_MCS0_MEM_MCS0_PRIM_CURR_START       (1512) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIM_CURR_START' */
#define LABEL_MCS0_MEM_MCS0_PRIM_DONE            (1534) /* Index into C-array 'mcs0_mem' for assembler label 'MCS0_PRIM_DONE' */

#endif
