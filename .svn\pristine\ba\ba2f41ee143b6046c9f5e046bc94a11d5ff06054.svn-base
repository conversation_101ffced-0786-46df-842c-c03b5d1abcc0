/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $ Description:                                                                                             $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/

#ifndef _RLI_H_
#define _RLI_H_

#include "diagcanmgm.h"
#include "mathlib.h"

/* includes for access to variables */
#include "Pwrmgm_out.h"
#include "Timing_out.h"
#include "syncmgm_out.h"
#include "canmgm_out.h"
#include "canmgmin_out.h"
#include "canmgmout_out.h"
#include "loadmgm_out.h"
#include "temp_mgm.h"
#include "TempECUMgm_out.h"
#include "MKnockDet_out.h"
#include "Analogin_out.h"
#include "coiltarget_out.h"
#include "IonIntMgm_out.h"
#include "msparkcmd_out.h"
#include "cpumgm_out.h"
#include "Ron_Detect.h"
#include "DigIn_out.h"
#include "IonPhaseMgm_out.h"
#include "IonKnockEn_out.h"
#include "ionacq_out.h"
#include "KnockCorrTot_out.h"
#include "stub.h"
#include "ionmisf_out.h"
#include "IonKnockPower_out.h"
#include "IonDwellMgm_out.h"
#include "IonAcqCircMgm_out.h"
#include "SparkPlugTest_out.h"
#include "IonAcqBufRec_out.h"
#include "diagmgm_out.h"
#include "KnockCorrNom_out.h"
#include "wdt_out.h"
//#include "buckmgm_out.h"
#include "Gtm_eisb_out.h"
#include "TSparkCtrlAdat_out.h"
#include "RonDetect_out.h"
#include "ignincmd_out.h"
#include "ionchargectrl_out.h" // for VChargeObj var scope
#include "TLE9278BQX_Com_out.h"
#include "TLE9278BQX_Get_out.h"
#include "TLE9278BQX_Prs_out.h "
#include "TLE9278BQX_Diag_eep_out.h"
extern uint8_T FuelLevel; 
#define NUM_OF_RLIFUNC (1u)
#define RLI_LENGTH     330u // total number of RDIs with direct link to SW Var; it means that SW Var and output for the extranel tool have same resolution;
                            // DO NOT TAKE INTO ACCOUNT ALL SNAPSHOTS VARIABLES WHICH NEEDES DIFFERENT TREATMENT!!!!


#define FUNC_CONVONOFF              (0)
#define FUNC_SESSIONCONV            (1)
#define FUNC_RLICONV_DATA_BY_16     (2)
#define FUNC_RLICONV_DATA_DIV_16    (3)
#define FUNC_RLICONV_DATA_DIV_256   (4)
#define FUNC_SENSORSUPPLYCONV       (5)
#define FUNC_OILTEMPCONV            (6)
#define FUNC_RLICONV                (7)
#define FUNC_CONVLOCK               (8)

#define RLISTATUS_BIT0  0x01
#define RLISTATUS_BIT1  0x02
#define RLISTATUS_BIT2  0x04
#define RLISTATUS_BIT3  0x08
#define RLISTATUS_BIT4  0x10
#define RLISTATUS_BIT5  0x20
#define RLISTATUS_BIT6  0x40
#define RLISTATUS_BIT7  0x80
#define RESET_RLISTATUS (RLISTATUS_BIT0)

#define RONBUFFSIZE     20  // #item of VtRonLevel
#define RONSAMPLESNUM   4   // #item of VtRonLevel to be used for diagnosis and RDI



typedef enum 
{
    ECUTIMESTAMPS_REQ          = 0x1008u, // CS normed
    ECUTIMESTAMPSFROMKEYON_REQ = 0x1009u, // CS normed
    RESETTYPE_REQ              = 0x1828u,
    STPLASOBJ_REQ              = 0x1829u,
    RONLEVELEE_REQ             = 0x182Au,
    RONLEVEL_1_REQ             = 0x182Bu,
    RONLEVEL_2_REQ             = 0x182Cu,
    KEYSIGNAL_REQ              = 0x182Du,
    RONLEVEL_3_REQ             = 0x182Fu,
    RONLEVEL_4_REQ             = 0x1830u,
    FLGBANKSEL_REQ             = 0x1831u,
    GEARPOS_REQ                = 0x1833u,
    VDLOADSXCAN_REQ            = 0x1842u,
    VDLOADDXCAN_REQ            = 0x1843u,
    VDRPMCAN_REQ               = 0x1844u,
    VDTAIRCAN_REQ              = 0x1845u,
    VDTWATERCAN_REQ            = 0x1846u,
    VDGASPOSCAN_REQ            = 0x1847u,
    FLGSYNCPHASED_REQ          = 0x1848u,
    NTEETHDELETED_REQ          = 0x184Au,
    STSYNC_REQ                 = 0x184Bu,
    LASTSYNCERROR_REQ          = 0x184Cu,
    IONSELECTCYL_REQ           = 0x1854u,
    STECU_REQ                  = 0x18B2u,
    STPHASE_7_REQ              = 0x18B5u,
    STPHASE_6_REQ              = 0x18B6u,
    STPHASE_5_REQ              = 0x18B7u,
    STPHASE_4_REQ              = 0x18B8u,
    STPHASE_3_REQ              = 0x18B9u,
    STPHASE_2_REQ              = 0x18BAu,
    STPHASE_1_REQ              = 0x18BBu,
    STPHASE_0_REQ              = 0x18BCu,
    STMISF_7_REQ               = 0x18BDu,
    STMISF_6_REQ               = 0x18BEu,
    STMISF_5_REQ               = 0x18BFu,
    STMISF_4_REQ               = 0x18C0u,
    STMISF_3_REQ               = 0x18C1u,
    STMISF_2_REQ               = 0x18C2u,
    STMISF_1_REQ               = 0x18C3u,
    STMISF_0_REQ               = 0x18C4u,
    IONKNOCKENABLED_REQ        = 0x18C5u,
    IONSELECT_7_REQ            = 0x18C6u,
    IONSELECT_6_REQ            = 0x18C7u,
    IONSELECT_5_REQ            = 0x18C8u,
    IONSELECT_4_REQ            = 0x18C9u,
    IONSELECT_3_REQ            = 0x18CAu,
    IONSELECT_2_REQ            = 0x18CBu,
    IONSELECT_1_REQ            = 0x18CCu,
    IONSELECT_0_REQ            = 0x18CDu,
    SAKNOCK_7_REQ              = 0x18CEu,
    SAKNOCK_6_REQ              = 0x18CFu,
    SAKNOCK_5_REQ              = 0x18D0u,
    SAKNOCK_4_REQ              = 0x18D1u,
    SAKNOCK_3_REQ              = 0x18D2u,
    SAKNOCK_2_REQ              = 0x18D3u,
    SAKNOCK_1_REQ              = 0x18D4u,
    SAKNOCK_0_REQ              = 0x18D5u,
    CNTTASKNOSYNC_REQ          = 0x18D6u,
    DELTAKNOCKNPOW_7_REQ       = 0x18D8u,
    DELTAKNOCKNPOW_6_REQ       = 0x18D9u,
    DELTAKNOCKNPOW_5_REQ       = 0x18DAu,
    DELTAKNOCKNPOW_4_REQ       = 0x18DBu,
    DELTAKNOCKNPOW_3_REQ       = 0x18DCu,
    DELTAKNOCKNPOW_2_REQ       = 0x18DDu,
    DELTAKNOCKNPOW_1_REQ       = 0x18DEu,
    DELTAKNOCKNPOW_0_REQ       = 0x18DFu,
    START_CH_7_REQ             = 0x18E0u,
    START_CH_6_REQ             = 0x18E1u,
    START_CH_5_REQ             = 0x18E2u,
    START_CH_4_REQ             = 0x18E3u,
    START_CH_3_REQ             = 0x18E4u,
    START_CH_2_REQ             = 0x18E5u,
    START_CH_1_REQ             = 0x18E6u,
    START_CH_0_REQ             = 0x18E7u,
    START_TH_7_REQ             = 0x18E8u,
    START_TH_6_REQ             = 0x18E9u,
    START_TH_5_REQ             = 0x18EAu,
    START_TH_4_REQ             = 0x18EBu,
    START_TH_3_REQ             = 0x18ECu,
    START_TH_2_REQ             = 0x18EDu,
    START_TH_1_REQ             = 0x18EEu,
    START_TH_0_REQ             = 0x18EFu,
    THPEAK_7_REQ               = 0x18F0u,
    THPEAK_6_REQ               = 0x18F1u,
    THPEAK_5_REQ               = 0x18F2u,
    THPEAK_4_REQ               = 0x18F3u,
    THPEAK_3_REQ               = 0x18F4u,
    THPEAK_2_REQ               = 0x18F5u,
    THPEAK_1_REQ               = 0x18F6u,
    THPEAK_0_REQ               = 0x18F7u,
    IGNFAULT_7_REQ             = 0x1900u,
    IGNFAULT_6_REQ             = 0x1901u,
    IGNFAULT_5_REQ             = 0x1902u,
    IGNFAULT_4_REQ             = 0x1903u,
    IGNFAULT_3_REQ             = 0x1904u,
    IGNFAULT_2_REQ             = 0x1905u,
    IGNFAULT_1_REQ             = 0x1906u,
    IGNFAULT_0_REQ             = 0x1907u,
    ENRONDETECTEE_REQ          = 0x1908u,
    RONLEVELIN_REQ             = 0x1909u,
    RONLEVELUSED_REQ           = 0x190Au,
    FLGRONSTOREDEE_REQ         = 0x190Bu,
    STRONDETECT_REQ            = 0x190Cu,
    STREFUEL_REQ               = 0x190Du,
    FUELLEVEL_REQ              = 0x190Eu,
    FUELLEVELEE_REQ            = 0x190Fu,
    VTRONLEVEL_0_REQ           = 0x1910u,
    VTRONLEVEL_1_REQ           = 0x1911u,
    VTRONLEVEL_2_REQ           = 0x1912u,
    VTRONLEVEL_3_REQ           = 0x1913u,
    VTRONLEVEL_4_REQ           = 0x1914u,
    VTRONLEVEL_5_REQ           = 0x1915u,
    VTRONLEVEL_6_REQ           = 0x1916u,
    VTRONLEVEL_7_REQ           = 0x1917u,
    VTRONLEVEL_8_REQ           = 0x1918u,
    VTRONLEVEL_9_REQ           = 0x1919u,
    VTRONLEVEL_10_REQ          = 0x191Au,
    VTRONLEVEL_11_REQ          = 0x191Bu,
    VTRONLEVEL_12_REQ          = 0x191Cu,
    VTRONLEVEL_13_REQ          = 0x191Du,
    VTRONLEVEL_14_REQ          = 0x191Eu,
    VTRONLEVEL_15_REQ          = 0x191Fu,
    VTRONLEVEL_16_REQ          = 0x1920u,
    VTRONLEVEL_17_REQ          = 0x1921u,
    VTRONLEVEL_18_REQ          = 0x1922u,
    VTRONLEVEL_19_REQ          = 0x1923u,
    SARON_REQ                  = 0x1924u,
    FLGRONMAXINC_REQ           = 0x1925u,
    FLGRONMINDEC_REQ           = 0x1926u,
    CNTRONRUN_REQ              = 0x1927u,
    CNTRONMAXINC_REQ           = 0x1928u,
    CNTRONMINDEC_REQ           = 0x1929u,
    CNTDRON0_REQ               = 0x192Au,
    CNTKINTIDXRON_REQ          = 0x192Bu,
    FLGRONINHERITEE_REQ        = 0x192Cu,
    CNTRONSUSPRUNEE_REQ        = 0x192Du,
    CNTRONSUSPEE_REQ           = 0x192Eu,
    VTDRONLEVELSUSPRUN_0_REQ   = 0x192Fu,
    VTDRONLEVELSUSPRUN_1_REQ   = 0x1930u,
    VTDRONLEVELSUSPRUN_2_REQ   = 0x1931u,
    VTDRONLEVELSUSPRUN_3_REQ   = 0x1932u,
    VTDRONLEVELSUSPRUN_4_REQ   = 0x1933u,
    VTDRONLEVELSUSPRUN_5_REQ   = 0x1934u,
    VTDRONLEVELSUSPRUN_6_REQ   = 0x1935u,
    VTDRONLEVELSUSPRUN_7_REQ   = 0x1936u,
    VTDRONLEVELSUSPRUN_8_REQ   = 0x1937u,
    VTDRONLEVELSUSPRUN_9_REQ   = 0x1938u,
    VTDRONLEVELSUSPRUN_10_REQ  = 0x1939u,
    VTDRONLEVELSUSPRUN_11_REQ  = 0x193Au,
    VTDRONLEVELSUSPRUN_12_REQ  = 0x193Bu,
    VTDRONLEVELSUSPRUN_13_REQ  = 0x193Cu,
    VTDRONLEVELSUSPRUN_14_REQ  = 0x193Du,
    VTDRONLEVELSUSPRUN_15_REQ  = 0x193Eu,
    VTDRONLEVELSUSPRUN_16_REQ  = 0x193Fu,
    VTDRONLEVELSUSPRUN_17_REQ  = 0x1940u,
    VTDRONLEVELSUSPRUN_18_REQ  = 0x1941u,
    VTDRONLEVELSUSPRUN_19_REQ  = 0x1942u,
    VTDRONLEVELSUSPRUN_20_REQ  = 0x1943u,
    VTDRONLEVELSUSPRUN_21_REQ  = 0x1944u,
    VTDRONLEVELSUSPRUN_22_REQ  = 0x1945u,
    VTDRONLEVELSUSPRUN_23_REQ  = 0x1946u,
    VTDRONLEVELSUSPRUN_24_REQ  = 0x1947u,
    VTDRONLEVELSUSPRUN_25_REQ  = 0x1948u,
    VTDRONLEVELSUSPRUN_26_REQ  = 0x1949u,
    VTDRONLEVELSUSPRUN_27_REQ  = 0x194Au,
    VTDRONLEVELSUSPRUN_28_REQ  = 0x194Bu,
    VTDRONLEVELSUSPRUN_29_REQ  = 0x194Cu,
    VTDRONLEVELSUSP_0_REQ      = 0x194Du,
    VTDRONLEVELSUSP_1_REQ      = 0x194Eu,
    VTDRONLEVELSUSP_2_REQ      = 0x194Fu,
    VTDRONLEVELSUSP_3_REQ      = 0x1950u,
    VTDRONLEVELSUSP_4_REQ      = 0x1951u,
    VTDRONLEVELSUSP_5_REQ      = 0x1952u,
    VTDRONLEVELSUSP_6_REQ      = 0x1953u,
    VTDRONLEVELSUSP_7_REQ      = 0x1954u,
    VTDRONLEVELSUSP_8_REQ      = 0x1955u,
    VTDRONLEVELSUSP_9_REQ      = 0x1956u,
    STRONCCHECK_REQ            = 0x1957u,
    VTMKDWELLINTEE_0_REQ       = 0x1959u,
    VTMKDWELLINTEE_1_REQ       = 0x195Au,
    VTMKDWELLINTEE_2_REQ       = 0x195Bu,
    VTMKDWELLINTEE_3_REQ       = 0x195Cu,
    VTMKDWELLINTEE_4_REQ       = 0x195Du,
    VTMKKNOCKINTEE_0_REQ       = 0x195Eu,
    VTMKKNOCKINTEE_1_REQ       = 0x195Fu,
    VTMKKNOCKINTEE_2_REQ       = 0x1960u,
    VTMKKNOCKINTEE_3_REQ       = 0x1961u,
    VTMKKNOCKINTEE_4_REQ       = 0x1962u,
    VTMKINTIONEE_0_REQ         = 0x1963u,
    VTMKINTIONEE_1_REQ         = 0x1964u,
    VTMKINTIONEE_2_REQ         = 0x1965u,
    VTMKINTIONEE_3_REQ         = 0x1966u,
    VTMKINTIONEE_4_REQ         = 0x1967u,
    VTMKRPMEE_0_REQ            = 0x1968u,
    VTMKRPMEE_1_REQ            = 0x1969u,
    VTMKRPMEE_2_REQ            = 0x196Au,
    VTMKRPMEE_3_REQ            = 0x196Bu,
    VTMKRPMEE_4_REQ            = 0x196Cu,
    VTMKLOADEE_0_REQ           = 0x196Du,
    VTMKLOADEE_1_REQ           = 0x196Eu,
    VTMKLOADEE_2_REQ           = 0x196Fu,
    VTMKLOADEE_3_REQ           = 0x1970u,
    VTMKLOADEE_4_REQ           = 0x1971u,
    VTMKTOTODOMETERCANEE_0_REQ = 0x1972u,
    VTMKTOTODOMETERCANEE_1_REQ = 0x1973u,
    VTMKTOTODOMETERCANEE_2_REQ = 0x1974u,
    VTMKTOTODOMETERCANEE_3_REQ = 0x1975u,
    VTMKTOTODOMETERCANEE_4_REQ = 0x1976u,
    VTMKIONABSTDCEE_0_REQ      = 0x1977u,
    VTMKIONABSTDCEE_1_REQ      = 0x1978u,
    VTMKIONABSTDCEE_2_REQ      = 0x1979u,
    VTMKIONABSTDCEE_3_REQ      = 0x197Au,
    VTMKIONABSTDCEE_4_REQ      = 0x197Bu,
    DWELLINT_7_REQ             = 0x197Cu,
    DWELLINT_6_REQ             = 0x197Du,
    DWELLINT_5_REQ             = 0x197Eu,
    DWELLINT_4_REQ             = 0x197Fu,
    DWELLINT_3_REQ             = 0x1980u,
    DWELLINT_2_REQ             = 0x1981u,
    DWELLINT_1_REQ             = 0x1982u,
    DWELLINT_0_REQ             = 0x1983u,
    DWELLINTFILT_7_REQ         = 0x1984u,
    DWELLINTFILT_6_REQ         = 0x1985u,
    DWELLINTFILT_5_REQ         = 0x1986u,
    DWELLINTFILT_4_REQ         = 0x1987u,
    DWELLINTFILT_3_REQ         = 0x1988u,
    DWELLINTFILT_2_REQ         = 0x1989u,
    DWELLINTFILT_1_REQ         = 0x198Au,
    DWELLINTFILT_0_REQ         = 0x198Bu,
    SPARKPLUGFAULTCNTEE_7_REQ  = 0x1994u,
    SPARKPLUGFAULTCNTEE_6_REQ  = 0x1995u,
    SPARKPLUGFAULTCNTEE_5_REQ  = 0x1996u,
    SPARKPLUGFAULTCNTEE_4_REQ  = 0x1997u,
    SPARKPLUGFAULTCNTEE_3_REQ  = 0x1998u,
    SPARKPLUGFAULTCNTEE_2_REQ  = 0x1999u,
    SPARKPLUGFAULTCNTEE_1_REQ  = 0x199Au,
    SPARKPLUGFAULTCNTEE_0_REQ  = 0x199Bu,
    IONABSTDC_REQ              = 0x199Cu,
    TSPARK0_REQ                = 0x19B8u,
    TSPARK1_REQ                = 0x19B9u,
    TSPARK2_REQ                = 0x19BAu,
    TSPARK3_REQ                = 0x19BBu,
    TSPARK4_REQ                = 0x19BCu,
    TSPARK5_REQ                = 0x19BDu,
    TSPARK6_REQ                = 0x19BEu,
    TSPARK7_REQ                = 0x19BFu,
    KEYSTSCAN_REQ              = 0x19C0u,
    ENGSTSCAN_REQ              = 0x19C1u,
    EDRIVERSTS_REQ             = 0x19C2u,
    ACTIVEFAULT_0_REQ          = 0x19C3u,
    ACTIVEFAULT_1_REQ          = 0x19C4u,
    ACTIVEFAULT_2_REQ          = 0x19C5u,
    ACTIVEFAULT_3_REQ          = 0x19C6u,
    ACTIVEFAULT_4_REQ          = 0x19C7u,
    ACTIVEFAULT_5_REQ          = 0x19C8u,
    FLGIONTELACTIVE_REQ        = 0x19C9u,
    KEYONCNT_REQ               = 0x200Au, // CS.00051 normed
    PROGRAMMINGSTATUS_REQ      = 0x2010u, // CS.00051 normed
    RPM_REQ                    = 0x4010u,
    RPMCAN_REQ                 = 0x4011u,
    LOAD_REQ                   = 0x4012u,
    TOTODOMETERCAN_REQ         = 0x4013u,
    TWATER_REQ                 = 0x4014u,
    TAIR_REQ                   = 0x4015u,
    TEMPECU_REQ                = 0x4016u,
    IONKONCKENABLECOND_REQ     = 0x4017u,
    VBATTERY_REQ               = 0x4018u,
    GASPOS_REQ                 = 0x4019u,
    DCTSTATE_REQ               = 0x401Au,
    ILEADOBJ_REQ               = 0x401Bu,
    VTILEADPEAK_0_REQ          = 0x401Cu,
    VTILEADPEAK_1_REQ          = 0x401Du,
    VTILEADPEAK_2_REQ          = 0x401Eu,
    VTILEADPEAK_3_REQ          = 0x401Fu,
    VTILEADPEAK_4_REQ          = 0x4020u,
    VTILEADPEAK_5_REQ          = 0x4021u,
    VTILEADPEAK_6_REQ          = 0x4022u,
    VTILEADPEAK_7_REQ          = 0x4023u,
    IPRICORRCYL_REQ            = 0x4024u,
    SAOUT_REQ                  = 0x4025u,
    VEHSPEED_REQ               = 0x4026u,
    SECONDSFIRSTRUNTIME_REQ    = 0x4027u,
    FLGANYABSENTPRI_REQ        = 0x4035u,
    FLGANYABSENTVEH_REQ        = 0x4036u,
    VVFDBKLIVENESS_REQ         = 0x4037u,
    EETEMPECUMAX1_REQ          = 0x4038u,
    EETEMPECUMAX2_REQ          = 0x4039u,
    EETEMPECUMAX3_REQ          = 0x403Au,
    VVTEMPECU1_REQ             = 0x403Bu,
    VVTEMPECU2_REQ             = 0x403Cu,
    VVTEMPECU3_REQ             = 0x403Du,
    CNTRPMCAN_REQ              = 0x403Eu,
    CNTNOSYNCNOSTS_REQ         = 0x403Fu,
    LOADSXCAN_REQ              = 0x4040u,
    LOADDXCAN_REQ              = 0x4041u,
    THPEAKCYL_REQ              = 0x404Du,
    CHPEAKCYL_REQ              = 0x404Eu,
    DTHPEAKCYL_REQ             = 0x404Fu,
    DWELLINTCYL_REQ            = 0x4050u,
    START_IONCYL_REQ           = 0x4051u,
    START_CH_ION_CYL_REQ       = 0x4052u,
    VCHARGE_0_REQ              = 0x4056u,
    VCHARGE_2_REQ              = 0x4057u,
    SPARKLENGTH_0_REQ          = 0x4058u,
    SPARKLENGTH_2_REQ          = 0x4059u,
    SPARKLENGTH_4_REQ          = 0x405Au,
    SPARKLENGTH_6_REQ          = 0x405Bu,
    VTISHOTPEAK_0_REQ          = 0x405Cu,
    VTISHOTPEAK_2_REQ          = 0x405Du,
    VTISHOTPEAK_4_REQ          = 0x405Eu,
    VTISHOTPEAK_6_REQ          = 0x405Fu,
    INTION_0_REQ               = 0x4060u,
    INTION_2_REQ               = 0x4061u,
    INTION_4_REQ               = 0x4062u,
    INTION_6_REQ               = 0x4063u,
    VCHARGE_1_REQ              = 0x4064u,
    VCHARGE_3_REQ              = 0x4065u,
    SPARKLENGTH_1_REQ          = 0x4066u,
    SPARKLENGTH_3_REQ          = 0x4067u,
    SPARKLENGTH_5_REQ          = 0x4068u,
    SPARKLENGTH_7_REQ          = 0x4069u,
    VTISHOTPEAK_1_REQ          = 0x406Au,
    VTISHOTPEAK_3_REQ          = 0x406Bu,
    VTISHOTPEAK_5_REQ          = 0x406Cu,
    VTISHOTPEAK_7_REQ          = 0x406Du,
    INTION_1_REQ               = 0x406Eu,
    INTION_3_REQ               = 0x406Fu,
    INTION_5_REQ               = 0x4070u,
    INTION_7_REQ               = 0x4071u,
    CNTIGNTRGINON_0_REQ        = 0x4072u,
    CNTIGNTRGINON_2_REQ        = 0x4073u,
    CNTIGNTRGINON_4_REQ        = 0x4074u,
    CNTIGNTRGINON_6_REQ        = 0x4075u,
    EFFDWELLTIME_0_REQ         = 0x4076u,
    EFFDWELLTIME_2_REQ         = 0x4077u,
    EFFDWELLTIME_4_REQ         = 0x4078u,
    EFFDWELLTIME_6_REQ         = 0x4079u,
    VBUCK_0_REQ                = 0x407Eu,
    VBUCK_4_REQ                = 0x407Fu,
    VBUCK_10_REQ               = 0x4080u,
    VBUCK_14_REQ               = 0x4081u,
    VBUCK_20_REQ               = 0x4082u,
    VBUCK_24_REQ               = 0x4083u,
    VBUCK_30_REQ               = 0x4084u,
    VBUCK_34_REQ               = 0x4085u,
    CNTIGNTRGINON_1_REQ        = 0x4086u,
    CNTIGNTRGINON_3_REQ        = 0x4087u,
    CNTIGNTRGINON_5_REQ        = 0x4088u,
    CNTIGNTRGINON_7_REQ        = 0x4089u,
    EFFDWELLTIME_1_REQ         = 0x408Au,
    EFFDWELLTIME_3_REQ         = 0x408Bu,
    EFFDWELLTIME_5_REQ         = 0x408Cu,
    EFFDWELLTIME_7_REQ         = 0x408Du,
    VBUCK_5_REQ                = 0x4092u,
    VBUCK_9_REQ                = 0x4093u,
    VBUCK_15_REQ               = 0x4094u,
    VBUCK_19_REQ               = 0x4095u,
    VBUCK_25_REQ               = 0x4096u,
    VBUCK_29_REQ               = 0x4097u,
    VBUCK_35_REQ               = 0x4098u,
    VBUCK_39_REQ               = 0x4099u,
    KCOHDIAGCNT_0_REQ          = 0x409Au,
    KCOHDIAGCNT_1_REQ          = 0x409Bu,
    KCOHDIAGCNT_2_REQ          = 0x409Cu,
    KCOHDIAGCNT_3_REQ          = 0x409Du,
    KCOHDIAGCNT_4_REQ          = 0x409Eu,
    KCOHDIAGCNT_5_REQ          = 0x409Fu,
    KCOHDIAGCNT_6_REQ          = 0x40A0u,
    KCOHDIAGCNT_7_REQ          = 0x40A1u,
    CNTKNOCKCOHEE_0_REQ        = 0x40A2u,
    CNTKNOCKCOHEE_1_REQ        = 0x40A3u,
    CNTKNOCKCOHEE_2_REQ        = 0x40A4u,
    CNTKNOCKCOHEE_3_REQ        = 0x40A5u,
    CNTKNOCKCOHEE_4_REQ        = 0x40A6u,
    CNTKNOCKCOHEE_5_REQ        = 0x40A7u,
    CNTKNOCKCOHEE_6_REQ        = 0x40A8u,
    CNTKNOCKCOHEE_7_REQ        = 0x40A9u,
    KNOCKINT_0_REQ             = 0x40AAu,
    KNOCKINT_1_REQ             = 0x40ABu,
    KNOCKINT_2_REQ             = 0x40ACu,
    KNOCKINT_3_REQ             = 0x40ADu,
    KNOCKINT_4_REQ             = 0x40AEu,
    KNOCKINT_5_REQ             = 0x40AFu,
    KNOCKINT_6_REQ             = 0x40B0u,
    KNOCKINT_7_REQ             = 0x40B1u,
    VBANKSEL_REQ               = 0x40FEu,
    VTTSPARKFILT_0_REQ         = 0x419Du,
    VTTSPARKFILT_1_REQ         = 0x419Eu,
    VTTSPARKFILT_2_REQ         = 0x419Fu,
    VTTSPARKFILT_3_REQ         = 0x41A0u,
    VTTSPARKFILT_4_REQ         = 0x41A1u,
    VTTSPARKFILT_5_REQ         = 0x41A2u,
    VTTSPARKFILT_6_REQ         = 0x41A3u,
    VTTSPARKFILT_7_REQ         = 0x41A4u,
    VTIPRICORR_0_REQ           = 0x41A5u,
    VTIPRICORR_1_REQ           = 0x41A6u,
    VTIPRICORR_2_REQ           = 0x41A7u,
    VTIPRICORR_3_REQ           = 0x41A8u,
    VTIPRICORR_4_REQ           = 0x41A9u,
    VTIPRICORR_5_REQ           = 0x41AAu,
    VTIPRICORR_6_REQ           = 0x41ABu,
    VTIPRICORR_7_REQ           = 0x41ACu,
    SACMDINLEVERRNO_REQ        = 0x41ADu,
    SACMDINLEVCIRBUFF_0_REQ    = 0x41AEu,
    SACMDINLEVCIRBUFF_1_REQ    = 0x41AFu,
    SACMDINLEVCIRBUFF_2_REQ    = 0x41B0u,
    SACMDINLEVCIRBUFF_3_REQ    = 0x41B1u,
    SACMDINLEVCIRBUFF_4_REQ    = 0x41B2u,
    SACMDINLEVCIRBUFF_5_REQ    = 0x41B3u,
    SACMDINLEVCIRBUFF_6_REQ    = 0x41B4u,
    SACMDINLEVCIRBUFF_7_REQ    = 0x41B5u,
    STWDT_REQ                  = 0x41B6u,
    EESACMDINLEVERRNOMAX_REQ   = 0x41B7u,
    EESACMDINLEVERRSUM_REQ     = 0x41B8u,
    CNTFORESYNCSAOUT_REQ       = 0x41B9u,
    SAOUTCYL0_REQ              = 0x41BAu,
    SAOUTCYL1_REQ              = 0x41BBu,
    SAOUTCYL2_REQ              = 0x41BCu,
    SAOUTCYL3_REQ              = 0x41BDu,
    SAOUTCYL4_REQ              = 0x41BEu,
    SAOUTCYL5_REQ              = 0x41BFu,
    SAOUTCYL6_REQ              = 0x41C0u,
    SAOUTCYL7_REQ              = 0x41C1u,
    VCHARGEOBJ0_REQ            = 0x41C2u,
    VCHARGEOBJ1_REQ            = 0x41C3u,
    VCHARGEOBJ2_REQ            = 0x41C4u,
    VCHARGEOBJ3_REQ            = 0x41C5u,
    FLGISHOTOUT0_REQ           = 0x41C6u,
    FLGISHOTOUT1_REQ           = 0x41C7u,
    VTOLSECINT0_REQ            = 0x41C8u,
    VTOLSECINT1_REQ            = 0x41C9u,
    VTOLSECINT2_REQ            = 0x41CAu,
    VTOLSECINT3_REQ            = 0x41CBu,
    VTOLSECINT4_REQ            = 0x41CCu,
    VTOLSECINT5_REQ            = 0x41CDu,
    VTOLSECINT6_REQ            = 0x41CEu,
    VTOLSECINT7_REQ            = 0x41CFu,
    ISUPPLYCOIL1ADC_REQ        = 0x41D0u,
    VSUPPLYCOIL1ADC_REQ        = 0x41D1u,
    IBATTADC_REQ               = 0x41D2u,
    ISUPPLYCOIL2ADC_REQ        = 0x41D3u,
    VSUPPLYCOIL2ADC_REQ        = 0x41D4u,
    SBCSAFESTS_REQ             = 0x41D5u,
    SBCSTAT_REQ                = 0x41D6u,
    SBCSTS_REQ                 = 0x41D7u,
    SBCMODE_REQ                = 0x41D8u,
    EESBCOT_REQ                = 0x41D9u,
    EESBCSC_REQ                = 0x41DAu,
    EESBCUV_REQ                = 0x41DBu,
    EESBCCAN_REQ               = 0x41DCu,
    EECNTSBCUV_REQ             = 0x41DDu,
    EECNTSBCSC_REQ             = 0x41DEu,
    EECNTSBCOT_REQ             = 0x41DFu,
    EECNTSBCCAN_REQ            = 0x41E0u,
    VTIONKNOCKENABLECOND0_REQ  = 0x41E1u,
    VTIONKNOCKENABLECOND1_REQ  = 0x41E2u,
    VTIONKNOCKENABLECOND2_REQ  = 0x41E3u,
    VTIONKNOCKENABLECOND3_REQ  = 0x41E4u,
    VTIONKNOCKENABLECOND4_REQ  = 0x41E5u,
    VTIONKNOCKENABLECOND5_REQ  = 0x41E6u,
    VTIONKNOCKENABLECOND6_REQ  = 0x41E7u,
    VTIONKNOCKENABLECOND7_REQ  = 0x41E8u,
    SNAP6002_REQ               = 0x6002u,
    SNAP6003_REQ               = 0x6003u,
    SNAP6004_REQ               = 0x6004u,
    SNAP6005_REQ               = 0x6005u,
    SNAP6006_REQ               = 0x6006u,
    SNAP6007_REQ               = 0x6007u,
    EVENTCNT_REQ               = 0x6080u,
    MILOFFDRVCYCLES_REQ        = 0x6081u,
    DTCFAILURETYPE_REQ         = 0x6082u,
    DIAGSESSION_REQ            = 0xF186u
}typeRLI;

typedef enum 
{
    U8_TYPE              = 0,
    U16_TYPE                ,
    U32_TYPE                ,
    R32_TYPE
} typeData;

typedef struct typeConvParam
{
    int32_T gain;
    int32_T offset;

} typeConvParam_T;

/* Read Data By Local ID struct */
typedef struct {
    uint8_T         size;           // n� bytes in output on CAN message
    uint32_T        p_data;         // variable address expressed with 4 bytes
    int8_T          type_data;      // input data type (unsigned char, unsigned int, unsigned long, float)
    typeConvParam_T params;         // gain+offset della conversione
    int8_T          function_index; // conversion function index inside Rli_function struct
} rliDataStruct;


// void getRLI_U8(uint8_T * data,uint8_T * dataLength ,uint8_T rli);  // for 8-bits RDIs version
extern void getRLI_U16(uint8_T * data,uint16_T * dataLength ,uint16_T rli); // for 16-bits RDIs version
extern void getRONValFromCircBuff(uint8_T dataIn[], uint8_T dataOut[], int8_T pos);
extern uint8_T convTemp(int16_T value);

#endif //_RLI_H_
