/****************************************************************************
*
* Copyright © 2017-2019 STMicroelectronics - All Rights Reserved
*
* License terms: STMicroelectronics Proprietary in accordance with licensing
* terms SLA0089 at www.st.com
* 
* THIS SOFTWARE IS DISTRIBUTED "AS IS," AND ALL WARRANTIES ARE DISCLAIMED, 
* INCLUDING MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.
*
* EVALUATION ONLY - NOT FOR USE IN PRODUCTION
*****************************************************************************/

/**
 * @file    gtm_tim.c
 * @brief   SPC5xx GTM TOM low level driver code.
 *
 * @addtogroup TIM
 * @{
 */

#include "gtm.h"
//#include <irq.h> MC

#if (SPC5_GTM_USE_TIM == TRUE) || defined(__DOXYGEN__)

/*===========================================================================*/
/* Driver exported variables.                                                */
/*===========================================================================*/

/**
 * @brief   TIM0 driver identifier.
 */
#if (SPC5_GTM_USE_TIM0 == TRUE) || defined(__DOXYGEN__)
GTM_TIMDriver TIMD1;
#endif

/**
 * @brief   TIM1 driver identifier.
 */
#if (SPC5_GTM_USE_TIM1 == TRUE) || defined(__DOXYGEN__)
GTM_TIMDriver TIMD2;
#endif

/**
 * @brief   TIM2 driver identifier.
 */
#if (SPC5_GTM_USE_TIM2 == TRUE) || defined(__DOXYGEN__)
GTM_TIMDriver TIMD3;
#endif

/**
 * @brief   TIM3 driver identifier.
 */
#if (SPC5_GTM_USE_TIM3 == TRUE) || defined(__DOXYGEN__)
GTM_TIMDriver TIMD4;
#endif

/**
 * @brief   TIM4 driver identifier.
 */
#if (SPC5_GTM_USE_TIM4 == TRUE) || defined(__DOXYGEN__)
GTM_TIMDriver TIMD5;
#endif

/**
 * @brief   TIM5 driver identifier.
 */
#if (SPC5_GTM_USE_TIM5 == TRUE) || defined(__DOXYGEN__)
GTM_TIMDriver TIMD6;
#endif


/*===========================================================================*/
/* Driver local variables and types.                                         */
/*===========================================================================*/
#if !defined (__DOXYGEN__)
#if ((SPC5_GTM_USE_TIM0 == TRUE) && \
	((SPC5_GTM_TIM0_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL0_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM0_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_TIM0_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM0 == TRUE) && \
	((SPC5_GTM_TIM0_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL1_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM0_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_TIM0_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM0 == TRUE) && \
	((SPC5_GTM_TIM0_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL2_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM0_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_TIM0_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM0 == TRUE) && \
	((SPC5_GTM_TIM0_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL3_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM0_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_TIM0_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM0 == TRUE) && \
	((SPC5_GTM_TIM0_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL4_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM0_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_TIM0_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM0 == TRUE) && \
	((SPC5_GTM_TIM0_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL5_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM0_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_TIM0_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM0 == TRUE) && \
	((SPC5_GTM_TIM0_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL6_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM0_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_TIM0_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM0 == TRUE) && \
	((SPC5_GTM_TIM0_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM0_CHANNEL7_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM0_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_TIM0_CHANNEL7_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM1 == TRUE) && \
	((SPC5_GTM_TIM1_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL0_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM1_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_TIM1_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM1 == TRUE) && \
	((SPC5_GTM_TIM1_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL1_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM1_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_TIM1_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM1 == TRUE) && \
	((SPC5_GTM_TIM1_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL2_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM1_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_TIM1_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM1 == TRUE) && \
	((SPC5_GTM_TIM1_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL3_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM1_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_TIM1_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM1 == TRUE) && \
	((SPC5_GTM_TIM1_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL4_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM1_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_TIM1_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM1 == TRUE) && \
	((SPC5_GTM_TIM1_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL5_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM1_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_TIM1_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM1 == TRUE) && \
	((SPC5_GTM_TIM1_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL6_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM1_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_TIM1_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM1 == TRUE) && \
	((SPC5_GTM_TIM1_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM1_CHANNEL7_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM1_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_TIM1_CHANNEL7_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM2 == TRUE) && \
	((SPC5_GTM_TIM2_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL0_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM2_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_TIM2_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM2 == TRUE) && \
	((SPC5_GTM_TIM2_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL1_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM2_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_TIM2_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM2 == TRUE) && \
	((SPC5_GTM_TIM2_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL2_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM2_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_TIM2_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM2 == TRUE) && \
	((SPC5_GTM_TIM2_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL3_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM2_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_TIM2_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM2 == TRUE) && \
	((SPC5_GTM_TIM2_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL4_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM2_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_TIM2_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM2 == TRUE) && \
	((SPC5_GTM_TIM2_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL5_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM2_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_TIM2_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM2 == TRUE) && \
	((SPC5_GTM_TIM2_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL6_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM2_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_TIM2_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM2 == TRUE) && \
	((SPC5_GTM_TIM2_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM2_CHANNEL7_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM2_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_TIM2_CHANNEL7_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM3 == TRUE) && \
	((SPC5_GTM_TIM3_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL0_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM3_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_TIM3_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM3 == TRUE) && \
	((SPC5_GTM_TIM3_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL1_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM3_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_TIM3_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM3 == TRUE) && \
	((SPC5_GTM_TIM3_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL2_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM3_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_TIM3_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM3 == TRUE) && \
	((SPC5_GTM_TIM3_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL3_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM3_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_TIM3_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM3 == TRUE) && \
	((SPC5_GTM_TIM3_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL4_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM3_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_TIM3_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM3 == TRUE) && \
	((SPC5_GTM_TIM3_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL5_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM3_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_TIM3_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM3 == TRUE) && \
	((SPC5_GTM_TIM3_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL6_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM3_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_TIM3_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM3 == TRUE) && \
	((SPC5_GTM_TIM3_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM3_CHANNEL7_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM3_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_TIM3_CHANNEL7_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM4 == TRUE) && \
	((SPC5_GTM_TIM4_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL0_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM4_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_TIM4_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM4 == TRUE) && \
	((SPC5_GTM_TIM4_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL1_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM4_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_TIM4_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM4 == TRUE) && \
	((SPC5_GTM_TIM4_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL2_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM4_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_TIM4_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM4 == TRUE) && \
	((SPC5_GTM_TIM4_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL3_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM4_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_TIM4_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM4 == TRUE) && \
	((SPC5_GTM_TIM4_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL4_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM4_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_TIM4_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM4 == TRUE) && \
	((SPC5_GTM_TIM4_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL5_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM4_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_TIM4_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM4 == TRUE) && \
	((SPC5_GTM_TIM4_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL6_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM4_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_TIM4_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM4 == TRUE) && \
	((SPC5_GTM_TIM4_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM4_CHANNEL7_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM4_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_TIM4_CHANNEL7_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM5 == TRUE) && \
	((SPC5_GTM_TIM5_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL0_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM5_CHANNEL0_INT    TRUE
#else
#define SPC5_GTM_TIM5_CHANNEL0_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM5 == TRUE) && \
	((SPC5_GTM_TIM5_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL1_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM5_CHANNEL1_INT    TRUE
#else
#define SPC5_GTM_TIM5_CHANNEL1_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM5 == TRUE) && \
	((SPC5_GTM_TIM5_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL2_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM5_CHANNEL2_INT    TRUE
#else
#define SPC5_GTM_TIM5_CHANNEL2_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM5 == TRUE) && \
	((SPC5_GTM_TIM5_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL3_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM5_CHANNEL3_INT    TRUE
#else
#define SPC5_GTM_TIM5_CHANNEL3_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM5 == TRUE) && \
	((SPC5_GTM_TIM5_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL4_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM5_CHANNEL4_INT    TRUE
#else
#define SPC5_GTM_TIM5_CHANNEL4_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM5 == TRUE) && \
	((SPC5_GTM_TIM5_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL5_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM5_CHANNEL5_INT    TRUE
#else
#define SPC5_GTM_TIM5_CHANNEL5_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM5 == TRUE) && \
	((SPC5_GTM_TIM5_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL6_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM5_CHANNEL6_INT    TRUE
#else
#define SPC5_GTM_TIM5_CHANNEL6_INT    FALSE
#endif

#if ((SPC5_GTM_USE_TIM5 == TRUE) && \
	((SPC5_GTM_TIM5_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE) || \
	 (SPC5_GTM_TIM5_CHANNEL7_INT_GLITCH_ENABLED == TRUE)))

#define SPC5_GTM_TIM5_CHANNEL7_INT    TRUE
#else
#define SPC5_GTM_TIM5_CHANNEL7_INT    FALSE
#endif
#endif /* (__DOXYGEN__) */
/*===========================================================================*/
/* Driver local functions.                                                   */
/*===========================================================================*/
/**
 * @brief   Low level interrupt handler.
 *
 * @param[in] timd       GTM TIM driver pointer
 *
 * @param[in] channel    TIM channel
 *
 * @sa
 * TIM_CHANNEL0, TIM_CHANNEL1, TIM_CHANNEL2, TIM_CHANNEL3, TIM_CHANNEL4, TIM_CHANNEL5, TIM_CHANNEL6, TIM_CHANNEL7
 *
 */
/*static*/ void spc5_gtm_tim_interrupt_channel_handler(GTM_TIMDriver *timd, uint8_t channel) {

	uint32_t status;
	uint32_t enabled;
	GTM_TIM_Channel_Callbacks *callback;

	/* Read interrupt status */
	status = gtm_timGetIntStatus(timd, channel);

#ifdef _BUILD_INTC_CHECK_TRIG_SET_
    /* Run INT_CHECK_TRIGGER_SET mechanism */
    if (status == 0u) // ISR is set but its status flag or register is not set
    {
        SafetyMngr_ReportError((uint32_T)SAFE_ERR_INTC_TRIGGER_SET, FALSE);
    }
#endif // _BUILD_INTC_CHECK_TRIG_SET_

	/* Mask disabled interrupts */
	enabled = gtm_timGetIntEnabled(timd, channel) & status;
	callback = timd->callbacks[channel];

	if (callback != NULL) {

		if ((enabled & SPC5_GTM_TIM_IRQ_STATUS_NEW_VALUE) != 0UL) {
			if (timd->callbacks[channel]->new_value != NULL) {
				timd->callbacks[channel]->new_value(timd, channel);
			}
		}

		if ((enabled & SPC5_GTM_TIM_IRQ_STATUS_EDGE_COUNTER) != 0UL) {
			if (timd->callbacks[channel]->edge_counter != NULL) {
				timd->callbacks[channel]->edge_counter(timd, channel);
			}
		}

		if ((enabled & SPC5_GTM_TIM_IRQ_STATUS_SMU_COUNTER) != 0UL) {
			if (timd->callbacks[channel]->smu_counter != NULL) {
				timd->callbacks[channel]->smu_counter(timd, channel);
			}
		}

		if ((enabled & SPC5_GTM_TIM_IRQ_STATUS_GPRS_DATA_OVERFLOW) != 0UL) {
			if (timd->callbacks[channel]->gprs_data_overflow != NULL) {
				timd->callbacks[channel]->gprs_data_overflow(timd, channel);
			}
		}

		if ((enabled & SPC5_GTM_TIM_IRQ_STATUS_TIMEOUT) != 0UL) {
			if (timd->callbacks[channel]->timeout != NULL) {
				timd->callbacks[channel]->timeout(timd, channel);
			}
		}

		if ((enabled & SPC5_GTM_TIM_IRQ_STATUS_GLITCH) != 0UL) {
			if (timd->callbacks[channel]->glitch != NULL) {
				timd->callbacks[channel]->glitch(timd, channel);
			}
		}
	}

	/* Acknowledge the interrupts */
	gtm_timAckInt(timd, channel, status);
}

/*===========================================================================*/
/* Driver interrupt handlers.                                                */
/*===========================================================================*/
#if 0 //MC
#if (SPC5_GTM_TIM0_CHANNEL0_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 0 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL0
 *
 */
IRQ_HANDLER(SPC5_TIM0_0_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL0);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM0_CHANNEL1_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 1 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_TIM0_1_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL1);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM0_CHANNEL2_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 2 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL2
 *
 */
IRQ_HANDLER(SPC5_TIM0_2_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL2);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM0_CHANNEL3_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 3 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_TIM0_3_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL3);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM0_CHANNEL4_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 4 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL4
 *
 */
IRQ_HANDLER(SPC5_TIM0_4_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL4);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM0_CHANNEL5_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 5 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_TIM0_5_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL5);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM0_CHANNEL6_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 6 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL6
 *
 */
IRQ_HANDLER(SPC5_TIM0_6_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL6);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM0_CHANNEL7_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 7 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_TIM0_7_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD1, TIM_CHANNEL7);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM1_CHANNEL0_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM1 Channel 0 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL0
 *
 */
IRQ_HANDLER(SPC5_TIM1_0_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL0);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM1_CHANNEL1_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM1 Channel 1 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_TIM1_1_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL1);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM1_CHANNEL2_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM1 Channel 2 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL2
 *
 */
IRQ_HANDLER(SPC5_TIM1_2_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL2);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM1_CHANNEL3_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM1 Channel 3 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_TIM1_3_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL3);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM1_CHANNEL4_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM1 Channel 4 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL4
 *
 */
IRQ_HANDLER(SPC5_TIM1_4_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL4);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM1_CHANNEL5_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM0 Channel 5 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_TIM1_5_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL5);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM1_CHANNEL6_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM1 Channel 6 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL6
 *
 */
IRQ_HANDLER(SPC5_TIM1_6_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL6);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM1_CHANNEL7_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM1 Channel 7 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_TIM1_7_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD2, TIM_CHANNEL7);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM2_CHANNEL0_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM2 Channel 0 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL0
 *
 */
IRQ_HANDLER(SPC5_TIM2_0_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL0);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM2_CHANNEL1_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM2 Channel 1 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_TIM2_1_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL1);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM2_CHANNEL2_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM2 Channel 2 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL2
 *
 */
IRQ_HANDLER(SPC5_TIM2_2_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL2);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM2_CHANNEL3_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM2 Channel 3 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_TIM2_3_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL3);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM2_CHANNEL4_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM2 Channel 4 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL4
 *
 */
IRQ_HANDLER(SPC5_TIM2_4_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL4);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM2_CHANNEL5_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM2 Channel 5 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_TIM2_5_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL5);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM2_CHANNEL6_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM2 Channel 6 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL6
 *
 */
IRQ_HANDLER(SPC5_TIM2_6_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL6);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM2_CHANNEL7_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM2 Channel 7 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_TIM2_7_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD3, TIM_CHANNEL7);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM3_CHANNEL0_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM3 Channel 0 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL0
 *
 */
IRQ_HANDLER(SPC5_TIM3_0_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD4, TIM_CHANNEL0);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM3_CHANNEL1_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM3 Channel 1 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_TIM3_1_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD4, TIM_CHANNEL1);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM3_CHANNEL2_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM3 Channel 2 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL2
 *
 */
IRQ_HANDLER(SPC5_TIM3_2_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD4, TIM_CHANNEL2);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM3_CHANNEL3_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM3 Channel 3 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_TIM3_3_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD4, TIM_CHANNEL3);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM3_CHANNEL4_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM3 Channel 4 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL4
 *
 */
IRQ_HANDLER(SPC5_TIM3_4_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD4, TIM_CHANNEL4);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM3_CHANNEL5_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM3 Channel 5 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_TIM3_5_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD4, TIM_CHANNEL5);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM3_CHANNEL6_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM3 Channel 6 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL6
 *
 */
IRQ_HANDLER(SPC5_TIM3_6_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD4, TIM_CHANNEL6);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM3_CHANNEL7_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM3 Channel 7 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_TIM3_7_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD4, TIM_CHANNEL7);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM4_CHANNEL0_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM4 Channel 0 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL0
 *
 */
IRQ_HANDLER(SPC5_TIM4_0_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD5, TIM_CHANNEL0);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM4_CHANNEL1_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM4 Channel 1 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_TIM4_1_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD5, TIM_CHANNEL1);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM4_CHANNEL2_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM4 Channel 2 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL2
 *
 */
IRQ_HANDLER(SPC5_TIM4_2_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD5, TIM_CHANNEL2);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM4_CHANNEL3_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM4 Channel 3 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_TIM4_3_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD5, TIM_CHANNEL3);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM4_CHANNEL4_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM4 Channel 4 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL4
 *
 */
IRQ_HANDLER(SPC5_TIM4_4_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD5, TIM_CHANNEL4);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM4_CHANNEL5_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM4 Channel 5 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_TIM4_5_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD5, TIM_CHANNEL5);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM4_CHANNEL6_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM4 Channel 6 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL6
 *
 */
IRQ_HANDLER(SPC5_TIM4_6_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD5, TIM_CHANNEL6);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM4_CHANNEL7_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM4 Channel 7 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_TIM4_7_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD5, TIM_CHANNEL7);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM5_CHANNEL0_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM5 Channel 0 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL0
 *
 */
IRQ_HANDLER(SPC5_TIM5_0_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD6, TIM_CHANNEL0);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM5_CHANNEL1_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM5 Channel 1 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL1
 *
 */
IRQ_HANDLER(SPC5_TIM5_1_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD6, TIM_CHANNEL1);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM5_CHANNEL2_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM5 Channel 2 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL2
 *
 */
IRQ_HANDLER(SPC5_TIM5_2_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD6, TIM_CHANNEL2);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM5_CHANNEL3_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM5 Channel 3 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL3
 *
 */
IRQ_HANDLER(SPC5_TIM5_3_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD6, TIM_CHANNEL3);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM5_CHANNEL4_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM5 Channel 4 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL4
 *
 */
IRQ_HANDLER(SPC5_TIM5_4_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD6, TIM_CHANNEL4);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM5_CHANNEL5_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM5 Channel 5 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL5
 *
 */
IRQ_HANDLER(SPC5_TIM5_5_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD6, TIM_CHANNEL5);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM5_CHANNEL6_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM5 Channel 6 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL6
 *
 */
IRQ_HANDLER(SPC5_TIM5_6_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD6, TIM_CHANNEL6);

	IRQ_EPILOGUE();
}
#endif

#if (SPC5_GTM_TIM5_CHANNEL7_INT == TRUE) ||	defined(__DOXYGEN__)
/**
 * @brief   TIM5 Channel 7 interrupt hander.
 *
 * @sa
 * TIM_CHANNEL7
 *
 */
IRQ_HANDLER(SPC5_TIM5_7_HANDLER) {

	IRQ_PROLOGUE();

	spc5_gtm_tim_interrupt_channel_handler(&TIMD6, TIM_CHANNEL7);

	IRQ_EPILOGUE();
}
#endif
#endif
/*===========================================================================*/
/* Driver exported functions.                                                */
/*===========================================================================*/


/**
 * @brief   Low level GTM TIM driver initialization.
 *
 * @init
 */ 
void gtm_timInit(void) {

#if (SPC5_GTM_USE_TIM0 == TRUE)
	TIMD1.tim = &(GTM_TIM_0);

	TIMD1.callbacks = (GTM_TIM_Channel_Callbacks **)gtm_tim0_callbacks;

	TIMD1.tim->GTM_TIM_GC_REG(RST).R = SPC5_GTM_TIM0_SW_RESET;
	TIMD1.sw_reset_reg = SPC5_GTM_TIM0_SW_RESET;

	TIMD1.tim->GTM_TIM_GC_REG(IN_SRC).R = SPC5_GTM_TIM0_IN_SRC;
	TIMD1.aux_in_src_reg = SPC5_GTM_TIM0_IN_SRC;


#if (SPC5_GTM_TIM0_CH0_ENABLE == TRUE)
	TIMD1.tim->GTM_CH_REG(0,CTRL).R = SPC5_GTM_TIM0_CH0_CTRL;
	TIMD1.tim->GTM_CH_REG(0,FLT_FE).R = SPC5_GTM_TIM0_CH0_FLT_FE;
	TIMD1.tim->GTM_CH_REG(0,FLT_RE).R = SPC5_GTM_TIM0_CH0_FLT_RE;
	TIMD1.tim->GTM_CH_REG(0,CNT).R = SPC5_GTM_TIM0_CH0_CNT;

	if ((TIMD1.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE & 5U) != 0U) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD1.tim->GTM_CH_REG(0,CNTS).R = SPC5_GTM_TIM0_CH0_CNTS;
		TIMD1.channel[0].cnts_reg = SPC5_GTM_TIM0_CH0_CNTS;
	}

	if (TIMD1.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE == 5U) { /* TGPS MODE */
		TIMD1.tim->GTM_CH_REG(0,GPR1).R = SPC5_GTM_TIM0_CH0_GPR1;
		TIMD1.channel[0].gpr1_reg = SPC5_GTM_TIM0_CH0_GPR1;
	}

	TIMD1.tim->GTM_CH_REG(0,ECTRL).R = SPC5_GTM_TIM0_CH0_ECTRL;

	/* Timeout detection */
	TIMD1.tim->GTM_CH_REG(0,TDUV).R = (SPC5_GTM_TIM0_CH0_TCS << 28) | SPC5_GTM_TIM0_CH0_TOV;

	TIMD1.channel[0].cnt_reg = SPC5_GTM_TIM0_CH0_CTRL;
	TIMD1.channel[0].flt_fe_reg = SPC5_GTM_TIM0_CH0_FLT_FE;
	TIMD1.channel[0].flt_re_reg = SPC5_GTM_TIM0_CH0_FLT_RE;
	TIMD1.channel[0].cnt_reg = SPC5_GTM_TIM0_CH0_CNT;
	TIMD1.channel[0].ectrl_reg = SPC5_GTM_TIM0_CH0_ECTRL;

#if (SPC5_GTM_TIM0_CHANNEL0_INT == TRUE)
	gtm_timSetIRQMode(&TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM0_CHANNEL0_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM0_0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM0_0_INT_PRIORITY);

#if (SPC5_GTM_TIM0_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL0_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM0_CHANNEL0_INT */

#endif /* SPC5_GTM_TIM0_CH0_ENABLE */


#if (SPC5_GTM_TIM0_CH1_ENABLE == TRUE)
	TIMD1.tim->GTM_CH_REG(1,CTRL).R = SPC5_GTM_TIM0_CH1_CTRL;
	TIMD1.tim->GTM_CH_REG(1,FLT_FE).R = SPC5_GTM_TIM0_CH1_FLT_FE;
	TIMD1.tim->GTM_CH_REG(1,FLT_RE).R = SPC5_GTM_TIM0_CH1_FLT_RE;
	TIMD1.tim->GTM_CH_REG(1,CNT).R = SPC5_GTM_TIM0_CH1_CNT;
    if (TIMD1.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD1.tim->GTM_CH_REG(1,CNTS).R = SPC5_GTM_TIM0_CH1_CNTS;
		TIMD1.channel[1].cnts_reg = SPC5_GTM_TIM0_CH1_CNTS;
	}
    if (TIMD1.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD1.tim->GTM_CH_REG(1,GPR1).R = SPC5_GTM_TIM0_CH1_GPR1;
		TIMD1.channel[1].gpr1_reg = SPC5_GTM_TIM0_CH1_GPR1;
    }
	TIMD1.tim->GTM_CH_REG(1,ECTRL).R = SPC5_GTM_TIM0_CH1_ECTRL;

	/* Timeout detection */
	TIMD1.tim->GTM_CH_REG(1,TDUV).R = (SPC5_GTM_TIM0_CH1_TCS << 28) | SPC5_GTM_TIM0_CH1_TOV;

	TIMD1.channel[1].cnt_reg = SPC5_GTM_TIM0_CH1_CTRL;
	TIMD1.channel[1].flt_fe_reg = SPC5_GTM_TIM0_CH1_FLT_FE;
	TIMD1.channel[1].flt_re_reg = SPC5_GTM_TIM0_CH1_FLT_RE;
	TIMD1.channel[1].cnt_reg = SPC5_GTM_TIM0_CH1_CNT;
	TIMD1.channel[1].ectrl_reg = SPC5_GTM_TIM0_CH1_ECTRL;


#if (SPC5_GTM_TIM0_CHANNEL1_INT == TRUE)
	gtm_timSetIRQMode(&TIMD1, TIM_CHANNEL1, SPC5_GTM_TIM0_CHANNEL1_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM0_1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM0_1_INT_PRIORITY);

#if (SPC5_GTM_TIM0_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL1_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM0_CHANNEL1_INT */

#endif /* SPC5_GTM_TIM0_CH1_ENABLE */


#if (SPC5_GTM_TIM0_CH2_ENABLE == TRUE)
	TIMD1.tim->GTM_CH_REG(2,CTRL).R = SPC5_GTM_TIM0_CH2_CTRL;
	TIMD1.tim->GTM_CH_REG(2,FLT_FE).R = SPC5_GTM_TIM0_CH2_FLT_FE;
	TIMD1.tim->GTM_CH_REG(2,FLT_RE).R = SPC5_GTM_TIM0_CH2_FLT_RE;
	TIMD1.tim->GTM_CH_REG(2,CNT).R = SPC5_GTM_TIM0_CH2_CNT;
	if (TIMD1.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD1.tim->GTM_CH_REG(2,CNTS).R = SPC5_GTM_TIM0_CH2_CNTS;
		TIMD1.channel[2].cnts_reg = SPC5_GTM_TIM0_CH2_CNTS;
	}
	if (TIMD1.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD1.tim->GTM_CH_REG(2,GPR1).R = SPC5_GTM_TIM0_CH2_GPR1;
		TIMD1.channel[2].gpr1_reg = SPC5_GTM_TIM0_CH2_GPR1;
	}
	TIMD1.tim->GTM_CH_REG(2,ECTRL).R = SPC5_GTM_TIM0_CH2_ECTRL;

	/* Timeout detection */
	TIMD1.tim->GTM_CH_REG(2,TDUV).R = (SPC5_GTM_TIM0_CH2_TCS << 28) | SPC5_GTM_TIM0_CH2_TOV;

	TIMD1.channel[2].cnt_reg = SPC5_GTM_TIM0_CH2_CTRL;
	TIMD1.channel[2].flt_fe_reg = SPC5_GTM_TIM0_CH2_FLT_FE;
	TIMD1.channel[2].flt_re_reg = SPC5_GTM_TIM0_CH2_FLT_RE;
	TIMD1.channel[2].cnt_reg = SPC5_GTM_TIM0_CH2_CNT;
	TIMD1.channel[2].ectrl_reg = SPC5_GTM_TIM0_CH2_ECTRL;


#if (SPC5_GTM_TIM0_CHANNEL2_INT == TRUE)
	gtm_timSetIRQMode(&TIMD1, TIM_CHANNEL2, SPC5_GTM_TIM0_CHANNEL2_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM0_2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM0_2_INT_PRIORITY);

#if (SPC5_GTM_TIM0_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL2_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM0_CHANNEL2_INT */

#endif /* SPC5_GTM_TIM0_CH2_ENABLE */


#if (SPC5_GTM_TIM0_CH3_ENABLE == TRUE)
	TIMD1.tim->GTM_CH_REG(3,CTRL).R = SPC5_GTM_TIM0_CH3_CTRL;
	TIMD1.tim->GTM_CH_REG(3,FLT_FE).R = SPC5_GTM_TIM0_CH3_FLT_FE;
	TIMD1.tim->GTM_CH_REG(3,FLT_RE).R = SPC5_GTM_TIM0_CH3_FLT_RE;
	TIMD1.tim->GTM_CH_REG(3,CNT).R = SPC5_GTM_TIM0_CH3_CNT;
	if (TIMD1.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD1.tim->GTM_CH_REG(3,CNTS).R = SPC5_GTM_TIM0_CH3_CNTS;
		TIMD1.channel[3].cnts_reg = SPC5_GTM_TIM0_CH3_CNTS;
	}
	if (TIMD1.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD1.tim->GTM_CH_REG(3,GPR1).R = SPC5_GTM_TIM0_CH3_GPR1;
		TIMD1.channel[3].gpr1_reg = SPC5_GTM_TIM0_CH3_GPR1;
	}
	TIMD1.tim->GTM_CH_REG(3,ECTRL).R = SPC5_GTM_TIM0_CH3_ECTRL;

	/* Timeout detection */
	TIMD1.tim->GTM_CH_REG(3,TDUV).R = (SPC5_GTM_TIM0_CH3_TCS << 28) | SPC5_GTM_TIM0_CH3_TOV;

	TIMD1.channel[3].cnt_reg = SPC5_GTM_TIM0_CH3_CTRL;
	TIMD1.channel[3].flt_fe_reg = SPC5_GTM_TIM0_CH3_FLT_FE;
	TIMD1.channel[3].flt_re_reg = SPC5_GTM_TIM0_CH3_FLT_RE;
	TIMD1.channel[3].cnt_reg = SPC5_GTM_TIM0_CH3_CNT;
	TIMD1.channel[3].ectrl_reg = SPC5_GTM_TIM0_CH3_ECTRL;


#if (SPC5_GTM_TIM0_CHANNEL3_INT == TRUE)
	gtm_timSetIRQMode(&TIMD1, TIM_CHANNEL3, SPC5_GTM_TIM0_CHANNEL3_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM0_3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM0_3_INT_PRIORITY);

#if (SPC5_GTM_TIM0_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL3_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM0_CHANNEL3_INT */

#endif /* SPC5_GTM_TIM0_CH3_ENABLE */


#if (SPC5_GTM_TIM0_CH4_ENABLE == TRUE)
	TIMD1.tim->GTM_CH_REG(4,CTRL).R = SPC5_GTM_TIM0_CH4_CTRL;
	TIMD1.tim->GTM_CH_REG(4,FLT_FE).R = SPC5_GTM_TIM0_CH4_FLT_FE;
	TIMD1.tim->GTM_CH_REG(4,FLT_RE).R = SPC5_GTM_TIM0_CH4_FLT_RE;
	TIMD1.tim->GTM_CH_REG(4,CNT).R = SPC5_GTM_TIM0_CH4_CNT;
	if (TIMD1.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD1.tim->GTM_CH_REG(4,CNTS).R = SPC5_GTM_TIM0_CH4_CNTS;
		TIMD1.channel[4].cnts_reg = SPC5_GTM_TIM0_CH4_CNTS;
	}
	if (TIMD1.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD1.tim->GTM_CH_REG(4,GPR1).R = SPC5_GTM_TIM0_CH4_GPR1;
		TIMD1.channel[4].gpr1_reg = SPC5_GTM_TIM0_CH4_GPR1;
	}
	TIMD1.tim->GTM_CH_REG(4,ECTRL).R = SPC5_GTM_TIM0_CH4_ECTRL;

	/* Timeout detection */
	TIMD1.tim->GTM_CH_REG(4,TDUV).R = (SPC5_GTM_TIM0_CH4_TCS << 28) | SPC5_GTM_TIM0_CH4_TOV;

	TIMD1.channel[4].cnt_reg = SPC5_GTM_TIM0_CH4_CTRL;
	TIMD1.channel[4].flt_fe_reg = SPC5_GTM_TIM0_CH4_FLT_FE;
	TIMD1.channel[4].flt_re_reg = SPC5_GTM_TIM0_CH4_FLT_RE;
	TIMD1.channel[4].cnt_reg = SPC5_GTM_TIM0_CH4_CNT;
	TIMD1.channel[4].ectrl_reg = SPC5_GTM_TIM0_CH4_ECTRL;


#if (SPC5_GTM_TIM0_CHANNEL4_INT == TRUE)
	gtm_timSetIRQMode(&TIMD1, TIM_CHANNEL4, SPC5_GTM_TIM0_CHANNEL4_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM0_4_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM0_4_INT_PRIORITY);

#if (SPC5_GTM_TIM0_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL4_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM0_CHANNEL4_INT */

#endif /* SPC5_GTM_TIM0_CH4_ENABLE */


#if (SPC5_GTM_TIM0_CH5_ENABLE == TRUE)
	TIMD1.tim->GTM_CH_REG(5,CTRL).R = SPC5_GTM_TIM0_CH5_CTRL;
	TIMD1.tim->GTM_CH_REG(5,FLT_FE).R = SPC5_GTM_TIM0_CH5_FLT_FE;
	TIMD1.tim->GTM_CH_REG(5,FLT_RE).R = SPC5_GTM_TIM0_CH5_FLT_RE;
	TIMD1.tim->GTM_CH_REG(5,CNT).R = SPC5_GTM_TIM0_CH5_CNT;
	if (TIMD1.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD1.tim->GTM_CH_REG(5,CNTS).R = SPC5_GTM_TIM0_CH5_CNTS;
		TIMD1.channel[5].cnts_reg = SPC5_GTM_TIM0_CH5_CNTS;
	}
	if (TIMD1.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD1.tim->GTM_CH_REG(5,GPR1).R = SPC5_GTM_TIM0_CH5_GPR1;
		TIMD1.channel[5].gpr1_reg = SPC5_GTM_TIM0_CH5_GPR1;
	}
	TIMD1.tim->GTM_CH_REG(5,ECTRL).R = SPC5_GTM_TIM0_CH5_ECTRL;

	/* Timeout detection */
	TIMD1.tim->GTM_CH_REG(5,TDUV).R = (SPC5_GTM_TIM0_CH5_TCS << 28) | SPC5_GTM_TIM0_CH5_TOV;

	TIMD1.channel[5].cnt_reg = SPC5_GTM_TIM0_CH5_CTRL;
	TIMD1.channel[5].flt_fe_reg = SPC5_GTM_TIM0_CH5_FLT_FE;
	TIMD1.channel[5].flt_re_reg = SPC5_GTM_TIM0_CH5_FLT_RE;
	TIMD1.channel[5].cnt_reg = SPC5_GTM_TIM0_CH5_CNT;
	TIMD1.channel[5].ectrl_reg = SPC5_GTM_TIM0_CH5_ECTRL;


#if (SPC5_GTM_TIM0_CHANNEL5_INT == TRUE)
	gtm_timSetIRQMode(&TIMD1, TIM_CHANNEL5, SPC5_GTM_TIM0_CHANNEL5_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM0_5_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM0_5_INT_PRIORITY);

#if (SPC5_GTM_TIM0_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL5_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM0_CHANNEL5_INT */

#endif /* SPC5_GTM_TIM0_CH5_ENABLE */


#if (SPC5_GTM_TIM0_CH6_ENABLE == TRUE)
	TIMD1.tim->GTM_CH_REG(6,CTRL).R = SPC5_GTM_TIM0_CH6_CTRL;
	TIMD1.tim->GTM_CH_REG(6,FLT_FE).R = SPC5_GTM_TIM0_CH6_FLT_FE;
	TIMD1.tim->GTM_CH_REG(6,FLT_RE).R = SPC5_GTM_TIM0_CH6_FLT_RE;
	TIMD1.tim->GTM_CH_REG(6,CNT).R = SPC5_GTM_TIM0_CH6_CNT;
	if (TIMD1.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD1.tim->GTM_CH_REG(6,CNTS).R = SPC5_GTM_TIM0_CH6_CNTS;
		TIMD1.channel[6].cnts_reg = SPC5_GTM_TIM0_CH6_CNTS;
	}
	if (TIMD1.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD1.tim->GTM_CH_REG(6,GPR1).R = SPC5_GTM_TIM0_CH6_GPR1;
		TIMD1.channel[6].gpr1_reg = SPC5_GTM_TIM0_CH6_GPR1;
	}
	TIMD1.tim->GTM_CH_REG(6,ECTRL).R = SPC5_GTM_TIM0_CH6_ECTRL;

	/* Timeout detection */
	TIMD1.tim->GTM_CH_REG(6,TDUV).R = (SPC5_GTM_TIM0_CH6_TCS << 28) | SPC5_GTM_TIM0_CH6_TOV;

	TIMD1.channel[6].cnt_reg = SPC5_GTM_TIM0_CH6_CTRL;
	TIMD1.channel[6].flt_fe_reg = SPC5_GTM_TIM0_CH6_FLT_FE;
	TIMD1.channel[6].flt_re_reg = SPC5_GTM_TIM0_CH6_FLT_RE;
	TIMD1.channel[6].cnt_reg = SPC5_GTM_TIM0_CH6_CNT;
	TIMD1.channel[6].ectrl_reg = SPC5_GTM_TIM0_CH6_ECTRL;


#if (SPC5_GTM_TIM0_CHANNEL6_INT == TRUE)
	gtm_timSetIRQMode(&TIMD1, TIM_CHANNEL6, SPC5_GTM_TIM0_CHANNEL6_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM0_6_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM0_6_INT_PRIORITY);

#if (SPC5_GTM_TIM0_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL6_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM0_CHANNEL6_INT */

#endif /* SPC5_GTM_TIM0_CH6_ENABLE */


#if (SPC5_GTM_TIM0_CH7_ENABLE == TRUE)
	TIMD1.tim->GTM_CH_REG(7,CTRL).R = SPC5_GTM_TIM0_CH7_CTRL;
	TIMD1.tim->GTM_CH_REG(7,FLT_FE).R = SPC5_GTM_TIM0_CH7_FLT_FE;
	TIMD1.tim->GTM_CH_REG(7,FLT_RE).R = SPC5_GTM_TIM0_CH7_FLT_RE;
	TIMD1.tim->GTM_CH_REG(7,CNT).R = SPC5_GTM_TIM0_CH7_CNT;
	if (TIMD1.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD1.tim->GTM_CH_REG(7,CNTS).R = SPC5_GTM_TIM0_CH7_CNTS;
		TIMD1.channel[7].cnts_reg = SPC5_GTM_TIM0_CH7_CNTS;
	}
	if (TIMD1.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD1.tim->GTM_CH_REG(7,GPR1).R = SPC5_GTM_TIM0_CH7_GPR1;
		TIMD1.channel[7].gpr1_reg = SPC5_GTM_TIM0_CH7_GPR1;
	}
	TIMD1.tim->GTM_CH_REG(7,ECTRL).R = SPC5_GTM_TIM0_CH7_ECTRL;

	/* Timeout detection */
	TIMD1.tim->GTM_CH_REG(7,TDUV).R = (SPC5_GTM_TIM0_CH7_TCS << 28) | SPC5_GTM_TIM0_CH7_TOV;

	TIMD1.channel[7].cnt_reg = SPC5_GTM_TIM0_CH7_CTRL;
	TIMD1.channel[7].flt_fe_reg = SPC5_GTM_TIM0_CH7_FLT_FE;
	TIMD1.channel[7].flt_re_reg = SPC5_GTM_TIM0_CH7_FLT_RE;
	TIMD1.channel[7].cnt_reg = SPC5_GTM_TIM0_CH7_CNT;
	TIMD1.channel[7].ectrl_reg = SPC5_GTM_TIM0_CH7_ECTRL;


#if (SPC5_GTM_TIM0_CHANNEL7_INT == TRUE)
	gtm_timSetIRQMode(&TIMD1, TIM_CHANNEL7, SPC5_GTM_TIM0_CHANNEL7_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM0_7_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM0_7_INT_PRIORITY);

#if (SPC5_GTM_TIM0_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM0_CHANNEL7_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM0_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD1, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM0_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM0_CHANNEL7_INT */
#endif /* SPC5_GTM_TIM0_CH7_ENABLE */
#endif /* SPC5_GTM_USE_TIM0 */

#if (SPC5_GTM_USE_TIM1 == TRUE)
	TIMD2.tim = &(GTM_TIM_1);

	TIMD2.callbacks = (GTM_TIM_Channel_Callbacks **)gtm_tim1_callbacks;

	TIMD2.tim->GTM_TIM_GC_REG(RST).R = SPC5_GTM_TIM1_SW_RESET;
	TIMD2.sw_reset_reg = SPC5_GTM_TIM1_SW_RESET;

	TIMD2.tim->GTM_TIM_GC_REG(IN_SRC).R = SPC5_GTM_TIM1_IN_SRC;
	TIMD2.aux_in_src_reg = SPC5_GTM_TIM1_IN_SRC;

#if (SPC5_GTM_TIM1_CH0_ENABLE == TRUE)
	TIMD2.tim->GTM_CH_REG(0,CTRL).R = SPC5_GTM_TIM1_CH0_CTRL;
	TIMD2.tim->GTM_CH_REG(0,FLT_FE).R = SPC5_GTM_TIM1_CH0_FLT_FE;
	TIMD2.tim->GTM_CH_REG(0,FLT_RE).R = SPC5_GTM_TIM1_CH0_FLT_RE;
	TIMD2.tim->GTM_CH_REG(0,CNT).R = SPC5_GTM_TIM1_CH0_CNT;
	if (TIMD2.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD2.tim->GTM_CH_REG(0,CNTS).R = SPC5_GTM_TIM1_CH0_CNTS;
		TIMD2.channel[0].cnts_reg = SPC5_GTM_TIM1_CH0_CNTS;
	}
	if (TIMD2.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE == 5){ /* TGPS MODE */
		TIMD2.tim->GTM_CH_REG(0,GPR1).R = SPC5_GTM_TIM1_CH0_GPR1;
		TIMD2.channel[0].gpr1_reg = SPC5_GTM_TIM1_CH0_GPR1;
	}
	TIMD2.tim->GTM_CH_REG(0,ECTRL).R = SPC5_GTM_TIM1_CH0_ECTRL;

	/* Timeout detection */
	TIMD2.tim->GTM_CH_REG(0,TDUV).R = (SPC5_GTM_TIM1_CH0_TCS << 28) | SPC5_GTM_TIM1_CH0_TOV;

	TIMD2.channel[0].cnt_reg = SPC5_GTM_TIM1_CH0_CTRL;
	TIMD2.channel[0].flt_fe_reg = SPC5_GTM_TIM1_CH0_FLT_FE;
	TIMD2.channel[0].flt_re_reg = SPC5_GTM_TIM1_CH0_FLT_RE;
	TIMD2.channel[0].cnt_reg = SPC5_GTM_TIM1_CH0_CNT;
	TIMD2.channel[0].ectrl_reg = SPC5_GTM_TIM1_CH0_ECTRL;

#if (SPC5_GTM_TIM1_CHANNEL0_INT == TRUE)
	gtm_timSetIRQMode(&TIMD2, TIM_CHANNEL0, SPC5_GTM_TIM1_CHANNEL0_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM1_0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM1_0_INT_PRIORITY);

#if (SPC5_GTM_TIM1_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL0_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM1_CHANNEL0_INT */

#endif /* SPC5_GTM_TIM1_CH0_ENABLE */


#if (SPC5_GTM_TIM1_CH1_ENABLE == TRUE)
	TIMD2.tim->GTM_CH_REG(1,CTRL).R = SPC5_GTM_TIM1_CH1_CTRL;
	TIMD2.tim->GTM_CH_REG(1,FLT_FE).R = SPC5_GTM_TIM1_CH1_FLT_FE;
	TIMD2.tim->GTM_CH_REG(1,FLT_RE).R = SPC5_GTM_TIM1_CH1_FLT_RE;
	TIMD2.tim->GTM_CH_REG(1,CNT).R = SPC5_GTM_TIM1_CH1_CNT;
	if (TIMD2.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD2.tim->GTM_CH_REG(1,CNTS).R = SPC5_GTM_TIM1_CH1_CNTS;
		TIMD2.channel[1].cnts_reg = SPC5_GTM_TIM1_CH1_CNTS;
	}
	if (TIMD2.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE == 5){ /* TGPS MODE */
		TIMD2.tim->GTM_CH_REG(1,GPR1).R = SPC5_GTM_TIM1_CH1_GPR1;
		TIMD2.channel[1].gpr1_reg = SPC5_GTM_TIM1_CH1_GPR1;
	}
	TIMD2.tim->GTM_CH_REG(1,ECTRL).R = SPC5_GTM_TIM1_CH1_ECTRL;

	/* Timeout detection */
	TIMD2.tim->GTM_CH_REG(1,TDUV).R = (SPC5_GTM_TIM1_CH1_TCS << 28) | SPC5_GTM_TIM1_CH1_TOV;

	TIMD2.channel[1].cnt_reg = SPC5_GTM_TIM1_CH1_CTRL;
	TIMD2.channel[1].flt_fe_reg = SPC5_GTM_TIM1_CH1_FLT_FE;
	TIMD2.channel[1].flt_re_reg = SPC5_GTM_TIM1_CH1_FLT_RE;
	TIMD2.channel[1].cnt_reg = SPC5_GTM_TIM1_CH1_CNT;
	TIMD2.channel[1].ectrl_reg = SPC5_GTM_TIM1_CH1_ECTRL;


#if (SPC5_GTM_TIM1_CHANNEL1_INT == TRUE)
	gtm_timSetIRQMode(&TIMD2, TIM_CHANNEL1, SPC5_GTM_TIM1_CHANNEL1_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM1_1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM1_1_INT_PRIORITY);

#if (SPC5_GTM_TIM1_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL1_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM1_CHANNEL1_INT */

#endif /* SPC5_GTM_TIM1_CH1_ENABLE */


#if (SPC5_GTM_TIM1_CH2_ENABLE == TRUE)
	TIMD2.tim->GTM_CH_REG(2,CTRL).R = SPC5_GTM_TIM1_CH2_CTRL;
	TIMD2.tim->GTM_CH_REG(2,FLT_FE).R = SPC5_GTM_TIM1_CH2_FLT_FE;
	TIMD2.tim->GTM_CH_REG(2,FLT_RE).R = SPC5_GTM_TIM1_CH2_FLT_RE;
	TIMD2.tim->GTM_CH_REG(2,CNT).R = SPC5_GTM_TIM1_CH2_CNT;
	if (TIMD2.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD2.tim->GTM_CH_REG(2,CNTS).R = SPC5_GTM_TIM1_CH2_CNTS;
		TIMD2.channel[2].cnts_reg = SPC5_GTM_TIM1_CH2_CNTS;
	}
	if (TIMD2.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE == 5){ /* TGPS MODE */
		TIMD2.tim->GTM_CH_REG(2,GPR1).R = SPC5_GTM_TIM1_CH2_GPR1;
		TIMD2.channel[2].gpr1_reg = SPC5_GTM_TIM1_CH2_GPR1;
	}
	TIMD2.tim->GTM_CH_REG(2,ECTRL).R = SPC5_GTM_TIM1_CH2_ECTRL;

	/* Timeout detection */
	TIMD2.tim->GTM_CH_REG(2,TDUV).R = (SPC5_GTM_TIM1_CH2_TCS << 28) | SPC5_GTM_TIM1_CH2_TOV;

	TIMD2.channel[2].cnt_reg = SPC5_GTM_TIM1_CH2_CTRL;
	TIMD2.channel[2].flt_fe_reg = SPC5_GTM_TIM1_CH2_FLT_FE;
	TIMD2.channel[2].flt_re_reg = SPC5_GTM_TIM1_CH2_FLT_RE;
	TIMD2.channel[2].cnt_reg = SPC5_GTM_TIM1_CH2_CNT;
	TIMD2.channel[2].ectrl_reg = SPC5_GTM_TIM1_CH2_ECTRL;
	
#if (SPC5_GTM_TIM1_CHANNEL2_INT == TRUE)
	gtm_timSetIRQMode(&TIMD2, TIM_CHANNEL2, SPC5_GTM_TIM1_CHANNEL2_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM1_2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM1_2_INT_PRIORITY);

#if (SPC5_GTM_TIM1_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL2_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM1_CHANNEL2_INT */
#endif /* SPC5_GTM_TIM1_CH2_ENABLE */


#if (SPC5_GTM_TIM1_CH3_ENABLE == TRUE)
	TIMD2.tim->GTM_CH_REG(3,CTRL).R = SPC5_GTM_TIM1_CH3_CTRL;
	TIMD2.tim->GTM_CH_REG(3,FLT_FE).R = SPC5_GTM_TIM1_CH3_FLT_FE;
	TIMD2.tim->GTM_CH_REG(3,FLT_RE).R = SPC5_GTM_TIM1_CH3_FLT_RE;
	TIMD2.tim->GTM_CH_REG(3,CNT).R = SPC5_GTM_TIM1_CH3_CNT;
	if (TIMD2.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD2.tim->GTM_CH_REG(3,CNTS).R = SPC5_GTM_TIM1_CH3_CNTS;
		TIMD2.channel[3].cnts_reg = SPC5_GTM_TIM1_CH3_CNTS;
	}
	if (TIMD2.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE == 5){ /* TGPS MODE */
		TIMD2.tim->GTM_CH_REG(3,GPR1).R = SPC5_GTM_TIM1_CH3_GPR1;
		TIMD2.channel[3].gpr1_reg = SPC5_GTM_TIM1_CH3_GPR1;
	}
	TIMD2.tim->GTM_CH_REG(3,ECTRL).R = SPC5_GTM_TIM1_CH3_ECTRL;

	/* Timeout detection */
	TIMD2.tim->GTM_CH_REG(3,TDUV).R = (SPC5_GTM_TIM1_CH3_TCS << 28) | SPC5_GTM_TIM1_CH3_TOV;

	TIMD2.channel[3].cnt_reg = SPC5_GTM_TIM1_CH3_CTRL;
	TIMD2.channel[3].flt_fe_reg = SPC5_GTM_TIM1_CH3_FLT_FE;
	TIMD2.channel[3].flt_re_reg = SPC5_GTM_TIM1_CH3_FLT_RE;
	TIMD2.channel[3].cnt_reg = SPC5_GTM_TIM1_CH3_CNT;
	TIMD2.channel[3].ectrl_reg = SPC5_GTM_TIM1_CH3_ECTRL;
	
#if (SPC5_GTM_TIM1_CHANNEL3_INT == TRUE)
	gtm_timSetIRQMode(&TIMD2, TIM_CHANNEL3, SPC5_GTM_TIM1_CHANNEL3_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM1_3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM1_3_INT_PRIORITY);

#if (SPC5_GTM_TIM1_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL3_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM1_CHANNEL3_INT */

#endif /* SPC5_GTM_TIM1_CH3_ENABLE */


#if (SPC5_GTM_TIM1_CH4_ENABLE == TRUE)
	TIMD2.tim->GTM_CH_REG(4,CTRL).R = SPC5_GTM_TIM1_CH4_CTRL;
	TIMD2.tim->GTM_CH_REG(4,FLT_FE).R = SPC5_GTM_TIM1_CH4_FLT_FE;
	TIMD2.tim->GTM_CH_REG(4,FLT_RE).R = SPC5_GTM_TIM1_CH4_FLT_RE;
	TIMD2.tim->GTM_CH_REG(4,CNT).R = SPC5_GTM_TIM1_CH4_CNT;
	if (TIMD2.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD2.tim->GTM_CH_REG(4,CNTS).R = SPC5_GTM_TIM1_CH4_CNTS;
		TIMD2.channel[4].cnts_reg = SPC5_GTM_TIM1_CH4_CNTS;
	}
	if (TIMD2.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE == 5){ /* TGPS MODE */
		TIMD2.tim->GTM_CH_REG(4,GPR1).R = SPC5_GTM_TIM1_CH4_GPR1;
		TIMD2.channel[4].gpr1_reg = SPC5_GTM_TIM1_CH4_GPR1;
	}
	TIMD2.tim->GTM_CH_REG(4,ECTRL).R = SPC5_GTM_TIM1_CH4_ECTRL;

	/* Timeout detection */
	TIMD2.tim->GTM_CH_REG(4,TDUV).R = (SPC5_GTM_TIM1_CH4_TCS << 28) | SPC5_GTM_TIM1_CH4_TOV;

	TIMD2.channel[4].cnt_reg = SPC5_GTM_TIM1_CH4_CTRL;
	TIMD2.channel[4].flt_fe_reg = SPC5_GTM_TIM1_CH4_FLT_FE;
	TIMD2.channel[4].flt_re_reg = SPC5_GTM_TIM1_CH4_FLT_RE;
	TIMD2.channel[4].cnt_reg = SPC5_GTM_TIM1_CH4_CNT;
	TIMD2.channel[4].ectrl_reg = SPC5_GTM_TIM1_CH4_ECTRL;

#if (SPC5_GTM_TIM1_CHANNEL4_INT == TRUE)
	gtm_timSetIRQMode(&TIMD2, TIM_CHANNEL4, SPC5_GTM_TIM1_CHANNEL4_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM1_4_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM1_4_INT_PRIORITY);

#if (SPC5_GTM_TIM1_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL4_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM1_CHANNEL4_INT */

#endif /*SPC5_GTM_TIM1_CH4_ENABLE */


#if (SPC5_GTM_TIM1_CH5_ENABLE == TRUE)
	TIMD2.tim->GTM_CH_REG(5,CTRL).R = SPC5_GTM_TIM1_CH5_CTRL;
	TIMD2.tim->GTM_CH_REG(5,FLT_FE).R = SPC5_GTM_TIM1_CH5_FLT_FE;
	TIMD2.tim->GTM_CH_REG(5,FLT_RE).R = SPC5_GTM_TIM1_CH5_FLT_RE;
	TIMD2.tim->GTM_CH_REG(5,CNT).R = SPC5_GTM_TIM1_CH5_CNT;
	if (TIMD2.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD2.tim->GTM_CH_REG(5,CNTS).R = SPC5_GTM_TIM1_CH5_CNTS;
		TIMD2.channel[5].cnts_reg = SPC5_GTM_TIM1_CH5_CNTS;
	}
	if (TIMD2.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE == 5){ /* TGPS MODE */
		TIMD2.tim->GTM_CH_REG(5,GPR1).R = SPC5_GTM_TIM1_CH5_GPR1;
		TIMD2.channel[5].gpr1_reg = SPC5_GTM_TIM1_CH5_GPR1;
	}
	TIMD2.tim->GTM_CH_REG(5,ECTRL).R = SPC5_GTM_TIM1_CH5_ECTRL;

	/* Timeout detection */
	TIMD2.tim->GTM_CH_REG(5,TDUV).R = (SPC5_GTM_TIM1_CH5_TCS << 28) | SPC5_GTM_TIM1_CH5_TOV;

	TIMD2.channel[5].cnt_reg = SPC5_GTM_TIM1_CH5_CTRL;
	TIMD2.channel[5].flt_fe_reg = SPC5_GTM_TIM1_CH5_FLT_FE;
	TIMD2.channel[5].flt_re_reg = SPC5_GTM_TIM1_CH5_FLT_RE;
	TIMD2.channel[5].cnt_reg = SPC5_GTM_TIM1_CH5_CNT;
	TIMD2.channel[5].ectrl_reg = SPC5_GTM_TIM1_CH5_ECTRL;

#if (SPC5_GTM_TIM1_CHANNEL5_INT == TRUE)
	gtm_timSetIRQMode(&TIMD2, TIM_CHANNEL5, SPC5_GTM_TIM1_CHANNEL5_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM1_5_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM1_5_INT_PRIORITY);

#if (SPC5_GTM_TIM1_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL5_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM1_CHANNEL5_INT */
#endif /* SPC5_GTM_TIM1_CH5_ENABLE */


#if (SPC5_GTM_TIM1_CH6_ENABLE == TRUE)
	TIMD2.tim->GTM_CH_REG(6,CTRL).R = SPC5_GTM_TIM1_CH6_CTRL;
	TIMD2.tim->GTM_CH_REG(6,FLT_FE).R = SPC5_GTM_TIM1_CH6_FLT_FE;
	TIMD2.tim->GTM_CH_REG(6,FLT_RE).R = SPC5_GTM_TIM1_CH6_FLT_RE;
	TIMD2.tim->GTM_CH_REG(6,CNT).R = SPC5_GTM_TIM1_CH6_CNT;
	if (TIMD2.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD2.tim->GTM_CH_REG(6,CNTS).R = SPC5_GTM_TIM1_CH6_CNTS;
		TIMD2.channel[6].cnts_reg = SPC5_GTM_TIM1_CH6_CNTS;
	}
	if (TIMD2.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE == 5){ /* TGPS MODE */
		TIMD2.tim->GTM_CH_REG(6,GPR1).R = SPC5_GTM_TIM1_CH6_GPR1;
		TIMD2.channel[6].gpr1_reg = SPC5_GTM_TIM1_CH6_GPR1;
	}
	TIMD2.tim->GTM_CH_REG(6,ECTRL).R = SPC5_GTM_TIM1_CH6_ECTRL;

	/* Timeout detection */
	TIMD2.tim->GTM_CH_REG(6,TDUV).R = (SPC5_GTM_TIM1_CH6_TCS << 28) | SPC5_GTM_TIM1_CH6_TOV;

	TIMD2.channel[6].cnt_reg = SPC5_GTM_TIM1_CH6_CTRL;
	TIMD2.channel[6].flt_fe_reg = SPC5_GTM_TIM1_CH6_FLT_FE;
	TIMD2.channel[6].flt_re_reg = SPC5_GTM_TIM1_CH6_FLT_RE;
	TIMD2.channel[6].cnt_reg = SPC5_GTM_TIM1_CH6_CNT;
	TIMD2.channel[6].ectrl_reg = SPC5_GTM_TIM1_CH6_ECTRL;

#if (SPC5_GTM_TIM1_CHANNEL6_INT == TRUE)
	gtm_timSetIRQMode(&TIMD2, TIM_CHANNEL6, SPC5_GTM_TIM1_CHANNEL6_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM1_6_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM1_6_INT_PRIORITY);

#if (SPC5_GTM_TIM1_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL6_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM1_CHANNEL6_INT */
#endif /* SPC5_GTM_TIM1_CH6_ENABLE */


#if (SPC5_GTM_TIM1_CH7_ENABLE == TRUE)
	TIMD2.tim->GTM_CH_REG(7,CTRL).R = SPC5_GTM_TIM1_CH7_CTRL;
	TIMD2.tim->GTM_CH_REG(7,FLT_FE).R = SPC5_GTM_TIM1_CH7_FLT_FE;
	TIMD2.tim->GTM_CH_REG(7,FLT_RE).R = SPC5_GTM_TIM1_CH7_FLT_RE;
	TIMD2.tim->GTM_CH_REG(7,CNT).R = SPC5_GTM_TIM1_CH7_CNT;
	if (TIMD2.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD2.tim->GTM_CH_REG(7,CNTS).R = SPC5_GTM_TIM1_CH7_CNTS;
		TIMD2.channel[7].cnts_reg = SPC5_GTM_TIM1_CH7_CNTS;
	}
	if (TIMD2.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE == 5){ /* TGPS MODE */
		TIMD2.tim->GTM_CH_REG(7,GPR1).R = SPC5_GTM_TIM1_CH7_GPR1;
		TIMD2.channel[7].gpr1_reg = SPC5_GTM_TIM1_CH7_GPR1;
	}
	TIMD2.tim->GTM_CH_REG(7,ECTRL).R = SPC5_GTM_TIM1_CH7_ECTRL;

	/* Timeout detection */
	TIMD2.tim->GTM_CH_REG(7,TDUV).R = (SPC5_GTM_TIM1_CH7_TCS << 28) | SPC5_GTM_TIM1_CH7_TOV;

	TIMD2.channel[7].cnt_reg = SPC5_GTM_TIM1_CH7_CTRL;
	TIMD2.channel[7].flt_fe_reg = SPC5_GTM_TIM1_CH7_FLT_FE;
	TIMD2.channel[7].flt_re_reg = SPC5_GTM_TIM1_CH7_FLT_RE;
	TIMD2.channel[7].cnt_reg = SPC5_GTM_TIM1_CH7_CNT;
	TIMD2.channel[7].ectrl_reg = SPC5_GTM_TIM1_CH7_ECTRL;

#if (SPC5_GTM_TIM1_CHANNEL7_INT == TRUE)
	gtm_timSetIRQMode(&TIMD2, TIM_CHANNEL7, SPC5_GTM_TIM1_CHANNEL7_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM1_7_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM1_7_INT_PRIORITY);

#if (SPC5_GTM_TIM1_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM1_CHANNEL7_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM1_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD2, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM1_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM1_CHANNEL7_INT */
#endif /* SPC5_GTM_TIM1_CH7_ENABLE */
#endif /* SPC5_GTM_USE_TIM1 */

#if (SPC5_GTM_USE_TIM2 == TRUE)
	TIMD3.tim = &(GTM_TIM_2);

	TIMD3.callbacks = (GTM_TIM_Channel_Callbacks **)gtm_tim2_callbacks;

	TIMD3.tim->GTM_TIM_GC_REG(RST).R = SPC5_GTM_TIM2_SW_RESET;
	TIMD3.sw_reset_reg = SPC5_GTM_TIM2_SW_RESET;

	TIMD3.tim->GTM_TIM_GC_REG(IN_SRC).R = SPC5_GTM_TIM2_IN_SRC;
	TIMD3.aux_in_src_reg = SPC5_GTM_TIM2_IN_SRC;


#if (SPC5_GTM_TIM2_CH0_ENABLE == TRUE)
	TIMD3.tim->GTM_CH_REG(0,CTRL).R = SPC5_GTM_TIM2_CH0_CTRL;
	TIMD3.tim->GTM_CH_REG(0,FLT_FE).R = SPC5_GTM_TIM2_CH0_FLT_FE;
	TIMD3.tim->GTM_CH_REG(0,FLT_RE).R = SPC5_GTM_TIM2_CH0_FLT_RE;
	TIMD3.tim->GTM_CH_REG(0,CNT).R = SPC5_GTM_TIM2_CH0_CNT;

	if ((TIMD3.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE & 5U) != 0U) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD3.tim->GTM_CH_REG(0,CNTS).R = SPC5_GTM_TIM2_CH0_CNTS;
		TIMD3.channel[0].cnts_reg = SPC5_GTM_TIM2_CH0_CNTS;
	}

	if (TIMD3.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE == 5U) { /* TGPS MODE */
		TIMD3.tim->GTM_CH_REG(0,GPR1).R = SPC5_GTM_TIM2_CH0_GPR1;
		TIMD3.channel[0].gpr1_reg = SPC5_GTM_TIM2_CH0_GPR1;
	}

	TIMD3.tim->GTM_CH_REG(0,ECTRL).R = SPC5_GTM_TIM2_CH0_ECTRL;

	/* Timeout detection */
	TIMD3.tim->GTM_CH_REG(0,TDUV).R = (SPC5_GTM_TIM2_CH0_TCS << 28) | SPC5_GTM_TIM2_CH0_TOV;

	TIMD3.channel[0].cnt_reg = SPC5_GTM_TIM2_CH0_CTRL;
	TIMD3.channel[0].flt_fe_reg = SPC5_GTM_TIM2_CH0_FLT_FE;
	TIMD3.channel[0].flt_re_reg = SPC5_GTM_TIM2_CH0_FLT_RE;
	TIMD3.channel[0].cnt_reg = SPC5_GTM_TIM2_CH0_CNT;
	TIMD3.channel[0].ectrl_reg = SPC5_GTM_TIM2_CH0_ECTRL;


#if (SPC5_GTM_TIM2_CHANNEL0_INT == TRUE)
	gtm_timSetIRQMode(&TIMD3, TIM_CHANNEL0, SPC5_GTM_TIM2_CHANNEL0_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM2_0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM2_0_INT_PRIORITY);

#if (SPC5_GTM_TIM2_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL0_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM2_CHANNEL0_INT */

#endif /* SPC5_GTM_TIM2_CH0_ENABLE */


#if (SPC5_GTM_TIM2_CH1_ENABLE == TRUE)
	TIMD3.tim->GTM_CH_REG(1,CTRL).R = SPC5_GTM_TIM2_CH1_CTRL;
	TIMD3.tim->GTM_CH_REG(1,FLT_FE).R = SPC5_GTM_TIM2_CH1_FLT_FE;
	TIMD3.tim->GTM_CH_REG(1,FLT_RE).R = SPC5_GTM_TIM2_CH1_FLT_RE;
	TIMD3.tim->GTM_CH_REG(1,CNT).R = SPC5_GTM_TIM2_CH1_CNT;
    if (TIMD3.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD3.tim->GTM_CH_REG(1,CNTS).R = SPC5_GTM_TIM2_CH1_CNTS;
		TIMD3.channel[1].cnts_reg = SPC5_GTM_TIM2_CH1_CNTS;
	}
    if (TIMD3.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD3.tim->GTM_CH_REG(1,GPR1).R = SPC5_GTM_TIM2_CH1_GPR1;
		TIMD3.channel[1].gpr1_reg = SPC5_GTM_TIM2_CH1_GPR1;
    }
	TIMD3.tim->GTM_CH_REG(1,ECTRL).R = SPC5_GTM_TIM2_CH1_ECTRL;

	/* Timeout detection */
	TIMD3.tim->GTM_CH_REG(1,TDUV).R = (SPC5_GTM_TIM2_CH1_TCS << 28) | SPC5_GTM_TIM2_CH1_TOV;

	TIMD3.channel[1].cnt_reg = SPC5_GTM_TIM2_CH1_CTRL;
	TIMD3.channel[1].flt_fe_reg = SPC5_GTM_TIM2_CH1_FLT_FE;
	TIMD3.channel[1].flt_re_reg = SPC5_GTM_TIM2_CH1_FLT_RE;
	TIMD3.channel[1].cnt_reg = SPC5_GTM_TIM2_CH1_CNT;
	TIMD3.channel[1].ectrl_reg = SPC5_GTM_TIM2_CH1_ECTRL;


#if (SPC5_GTM_TIM2_CHANNEL1_INT == TRUE)
	gtm_timSetIRQMode(&TIMD3, TIM_CHANNEL1, SPC5_GTM_TIM2_CHANNEL1_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM2_1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM2_1_INT_PRIORITY);

#if (SPC5_GTM_TIM2_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL1_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM2_CHANNEL1_INT */

#endif /* SPC5_GTM_TIM2_CH1_ENABLE */


#if (SPC5_GTM_TIM2_CH2_ENABLE == TRUE)
	TIMD3.tim->GTM_CH_REG(2,CTRL).R = SPC5_GTM_TIM2_CH2_CTRL;
	TIMD3.tim->GTM_CH_REG(2,FLT_FE).R = SPC5_GTM_TIM2_CH2_FLT_FE;
	TIMD3.tim->GTM_CH_REG(2,FLT_RE).R = SPC5_GTM_TIM2_CH2_FLT_RE;
	TIMD3.tim->GTM_CH_REG(2,CNT).R = SPC5_GTM_TIM2_CH2_CNT;
	if (TIMD3.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD3.tim->GTM_CH_REG(2,CNTS).R = SPC5_GTM_TIM2_CH2_CNTS;
		TIMD3.channel[2].cnts_reg = SPC5_GTM_TIM2_CH2_CNTS;
	}
	if (TIMD3.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD3.tim->GTM_CH_REG(2,GPR1).R = SPC5_GTM_TIM2_CH2_GPR1;
		TIMD3.channel[2].gpr1_reg = SPC5_GTM_TIM2_CH2_GPR1;
	}
	TIMD3.tim->GTM_CH_REG(2,ECTRL).R = SPC5_GTM_TIM2_CH2_ECTRL;

	/* Timeout detection */
	TIMD3.tim->GTM_CH_REG(2,TDUV).R = (SPC5_GTM_TIM2_CH2_TCS << 28) | SPC5_GTM_TIM2_CH2_TOV;

	TIMD3.channel[2].cnt_reg = SPC5_GTM_TIM2_CH2_CTRL;
	TIMD3.channel[2].flt_fe_reg = SPC5_GTM_TIM2_CH2_FLT_FE;
	TIMD3.channel[2].flt_re_reg = SPC5_GTM_TIM2_CH2_FLT_RE;
	TIMD3.channel[2].cnt_reg = SPC5_GTM_TIM2_CH2_CNT;
	TIMD3.channel[2].ectrl_reg = SPC5_GTM_TIM2_CH2_ECTRL;


#if (SPC5_GTM_TIM2_CHANNEL2_INT == TRUE)
	gtm_timSetIRQMode(&TIMD3, TIM_CHANNEL2, SPC5_GTM_TIM2_CHANNEL2_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM2_2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM2_2_INT_PRIORITY);

#if (SPC5_GTM_TIM2_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL2_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM2_CHANNEL2_INT */

#endif /* SPC5_GTM_TIM2_CH2_ENABLE */


#if (SPC5_GTM_TIM2_CH3_ENABLE == TRUE)
	TIMD3.tim->GTM_CH_REG(3,CTRL).R = SPC5_GTM_TIM2_CH3_CTRL;
	TIMD3.tim->GTM_CH_REG(3,FLT_FE).R = SPC5_GTM_TIM2_CH3_FLT_FE;
	TIMD3.tim->GTM_CH_REG(3,FLT_RE).R = SPC5_GTM_TIM2_CH3_FLT_RE;
	TIMD3.tim->GTM_CH_REG(3,CNT).R = SPC5_GTM_TIM2_CH3_CNT;
	if (TIMD3.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD3.tim->GTM_CH_REG(3,CNTS).R = SPC5_GTM_TIM2_CH3_CNTS;
		TIMD3.channel[3].cnts_reg = SPC5_GTM_TIM2_CH3_CNTS;
	}
	if (TIMD3.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD3.tim->GTM_CH_REG(3,GPR1).R = SPC5_GTM_TIM2_CH3_GPR1;
		TIMD3.channel[3].gpr1_reg = SPC5_GTM_TIM2_CH3_GPR1;
	}
	TIMD3.tim->GTM_CH_REG(3,ECTRL).R = SPC5_GTM_TIM2_CH3_ECTRL;

	/* Timeout detection */
	TIMD3.tim->GTM_CH_REG(3,TDUV).R = (SPC5_GTM_TIM2_CH3_TCS << 28) | SPC5_GTM_TIM2_CH3_TOV;

	TIMD3.channel[3].cnt_reg = SPC5_GTM_TIM2_CH3_CTRL;
	TIMD3.channel[3].flt_fe_reg = SPC5_GTM_TIM2_CH3_FLT_FE;
	TIMD3.channel[3].flt_re_reg = SPC5_GTM_TIM2_CH3_FLT_RE;
	TIMD3.channel[3].cnt_reg = SPC5_GTM_TIM2_CH3_CNT;
	TIMD3.channel[3].ectrl_reg = SPC5_GTM_TIM2_CH3_ECTRL;


#if (SPC5_GTM_TIM2_CHANNEL3_INT == TRUE)
	gtm_timSetIRQMode(&TIMD3, TIM_CHANNEL3, SPC5_GTM_TIM2_CHANNEL3_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM2_3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM2_3_INT_PRIORITY);

#if (SPC5_GTM_TIM2_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL3_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM2_CHANNEL3_INT */

#endif /* SPC5_GTM_TIM2_CH3_ENABLE */


#if (SPC5_GTM_TIM2_CH4_ENABLE == TRUE)
	TIMD3.tim->GTM_CH_REG(4,CTRL).R = SPC5_GTM_TIM2_CH4_CTRL;
	TIMD3.tim->GTM_CH_REG(4,FLT_FE).R = SPC5_GTM_TIM2_CH4_FLT_FE;
	TIMD3.tim->GTM_CH_REG(4,FLT_RE).R = SPC5_GTM_TIM2_CH4_FLT_RE;
	TIMD3.tim->GTM_CH_REG(4,CNT).R = SPC5_GTM_TIM2_CH4_CNT;
	if (TIMD3.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD3.tim->GTM_CH_REG(4,CNTS).R = SPC5_GTM_TIM2_CH4_CNTS;
		TIMD3.channel[4].cnts_reg = SPC5_GTM_TIM2_CH4_CNTS;
	}
	if (TIMD3.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD3.tim->GTM_CH_REG(4,GPR1).R = SPC5_GTM_TIM2_CH4_GPR1;
		TIMD3.channel[4].gpr1_reg = SPC5_GTM_TIM2_CH4_GPR1;
	}
	TIMD3.tim->GTM_CH_REG(4,ECTRL).R = SPC5_GTM_TIM2_CH4_ECTRL;

	/* Timeout detection */
	TIMD3.tim->GTM_CH_REG(4,TDUV).R = (SPC5_GTM_TIM2_CH4_TCS << 28) | SPC5_GTM_TIM2_CH4_TOV;

	TIMD3.channel[4].cnt_reg = SPC5_GTM_TIM2_CH4_CTRL;
	TIMD3.channel[4].flt_fe_reg = SPC5_GTM_TIM2_CH4_FLT_FE;
	TIMD3.channel[4].flt_re_reg = SPC5_GTM_TIM2_CH4_FLT_RE;
	TIMD3.channel[4].cnt_reg = SPC5_GTM_TIM2_CH4_CNT;
	TIMD3.channel[4].ectrl_reg = SPC5_GTM_TIM2_CH4_ECTRL;


#if (SPC5_GTM_TIM2_CHANNEL4_INT == TRUE)
	gtm_timSetIRQMode(&TIMD3, TIM_CHANNEL4, SPC5_GTM_TIM2_CHANNEL4_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM2_4_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM2_4_INT_PRIORITY);

#if (SPC5_GTM_TIM2_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL4_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM2_CHANNEL4_INT */

#endif /* SPC5_GTM_TIM2_CH4_ENABLE */


#if (SPC5_GTM_TIM2_CH5_ENABLE == TRUE)
	TIMD3.tim->GTM_CH_REG(5,CTRL).R = SPC5_GTM_TIM2_CH5_CTRL;
	TIMD3.tim->GTM_CH_REG(5,FLT_FE).R = SPC5_GTM_TIM2_CH5_FLT_FE;
	TIMD3.tim->GTM_CH_REG(5,FLT_RE).R = SPC5_GTM_TIM2_CH5_FLT_RE;
	TIMD3.tim->GTM_CH_REG(5,CNT).R = SPC5_GTM_TIM2_CH5_CNT;
	if (TIMD3.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD3.tim->GTM_CH_REG(5,CNTS).R = SPC5_GTM_TIM2_CH5_CNTS;
		TIMD3.channel[5].cnts_reg = SPC5_GTM_TIM2_CH5_CNTS;
	}
	if (TIMD3.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD3.tim->GTM_CH_REG(5,GPR1).R = SPC5_GTM_TIM2_CH5_GPR1;
		TIMD3.channel[5].gpr1_reg = SPC5_GTM_TIM2_CH5_GPR1;
	}
	TIMD3.tim->GTM_CH_REG(5,ECTRL).R = SPC5_GTM_TIM2_CH5_ECTRL;

	/* Timeout detection */
	TIMD3.tim->GTM_CH_REG(5,TDUV).R = (SPC5_GTM_TIM2_CH5_TCS << 28) | SPC5_GTM_TIM2_CH5_TOV;

	TIMD3.channel[5].cnt_reg = SPC5_GTM_TIM2_CH5_CTRL;
	TIMD3.channel[5].flt_fe_reg = SPC5_GTM_TIM2_CH5_FLT_FE;
	TIMD3.channel[5].flt_re_reg = SPC5_GTM_TIM2_CH5_FLT_RE;
	TIMD3.channel[5].cnt_reg = SPC5_GTM_TIM2_CH5_CNT;
	TIMD3.channel[5].ectrl_reg = SPC5_GTM_TIM2_CH5_ECTRL;


#if (SPC5_GTM_TIM2_CHANNEL5_INT == TRUE)
	gtm_timSetIRQMode(&TIMD3, TIM_CHANNEL5, SPC5_GTM_TIM2_CHANNEL5_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM2_5_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM2_5_INT_PRIORITY);

#if (SPC5_GTM_TIM2_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL5_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM2_CHANNEL5_INT */

#endif /* SPC5_GTM_TIM2_CH5_ENABLE */


#if (SPC5_GTM_TIM2_CH6_ENABLE == TRUE)
	TIMD3.tim->GTM_CH_REG(6,CTRL).R = SPC5_GTM_TIM2_CH6_CTRL;
	TIMD3.tim->GTM_CH_REG(6,FLT_FE).R = SPC5_GTM_TIM2_CH6_FLT_FE;
	TIMD3.tim->GTM_CH_REG(6,FLT_RE).R = SPC5_GTM_TIM2_CH6_FLT_RE;
	TIMD3.tim->GTM_CH_REG(6,CNT).R = SPC5_GTM_TIM2_CH6_CNT;
	if (TIMD3.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD3.tim->GTM_CH_REG(6,CNTS).R = SPC5_GTM_TIM2_CH6_CNTS;
		TIMD3.channel[6].cnts_reg = SPC5_GTM_TIM2_CH6_CNTS;
	}
	if (TIMD3.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD3.tim->GTM_CH_REG(6,GPR1).R = SPC5_GTM_TIM2_CH6_GPR1;
		TIMD3.channel[6].gpr1_reg = SPC5_GTM_TIM2_CH6_GPR1;
	}
	TIMD3.tim->GTM_CH_REG(6,ECTRL).R = SPC5_GTM_TIM2_CH6_ECTRL;

	/* Timeout detection */
	TIMD3.tim->GTM_CH_REG(6,TDUV).R = (SPC5_GTM_TIM2_CH6_TCS << 28) | SPC5_GTM_TIM2_CH6_TOV;

	TIMD3.channel[6].cnt_reg = SPC5_GTM_TIM2_CH6_CTRL;
	TIMD3.channel[6].flt_fe_reg = SPC5_GTM_TIM2_CH6_FLT_FE;
	TIMD3.channel[6].flt_re_reg = SPC5_GTM_TIM2_CH6_FLT_RE;
	TIMD3.channel[6].cnt_reg = SPC5_GTM_TIM2_CH6_CNT;
	TIMD3.channel[6].ectrl_reg = SPC5_GTM_TIM2_CH6_ECTRL;


#if (SPC5_GTM_TIM2_CHANNEL6_INT == TRUE)
	gtm_timSetIRQMode(&TIMD3, TIM_CHANNEL6, SPC5_GTM_TIM2_CHANNEL6_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM2_6_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM2_6_INT_PRIORITY);

#if (SPC5_GTM_TIM2_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL6_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM2_CHANNEL6_INT */

#endif /* SPC5_GTM_TIM2_CH6_ENABLE */


#if (SPC5_GTM_TIM2_CH7_ENABLE == TRUE)
	TIMD3.tim->GTM_CH_REG(7,CTRL).R = SPC5_GTM_TIM2_CH7_CTRL;
	TIMD3.tim->GTM_CH_REG(7,FLT_FE).R = SPC5_GTM_TIM2_CH7_FLT_FE;
	TIMD3.tim->GTM_CH_REG(7,FLT_RE).R = SPC5_GTM_TIM2_CH7_FLT_RE;
	TIMD3.tim->GTM_CH_REG(7,CNT).R = SPC5_GTM_TIM2_CH7_CNT;
	if (TIMD3.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD3.tim->GTM_CH_REG(7,CNTS).R = SPC5_GTM_TIM2_CH7_CNTS;
		TIMD3.channel[7].cnts_reg = SPC5_GTM_TIM2_CH7_CNTS;
	}
	if (TIMD3.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD3.tim->GTM_CH_REG(7,GPR1).R = SPC5_GTM_TIM2_CH7_GPR1;
		TIMD3.channel[7].gpr1_reg = SPC5_GTM_TIM2_CH7_GPR1;
	}
	TIMD3.tim->GTM_CH_REG(7,ECTRL).R = SPC5_GTM_TIM2_CH7_ECTRL;

	/* Timeout detection */
	TIMD3.tim->GTM_CH_REG(7,TDUV).R = (SPC5_GTM_TIM2_CH7_TCS << 28) | SPC5_GTM_TIM2_CH7_TOV;

	TIMD3.channel[7].cnt_reg = SPC5_GTM_TIM2_CH7_CTRL;
	TIMD3.channel[7].flt_fe_reg = SPC5_GTM_TIM2_CH7_FLT_FE;
	TIMD3.channel[7].flt_re_reg = SPC5_GTM_TIM2_CH7_FLT_RE;
	TIMD3.channel[7].cnt_reg = SPC5_GTM_TIM2_CH7_CNT;
	TIMD3.channel[7].ectrl_reg = SPC5_GTM_TIM2_CH7_ECTRL;


#if (SPC5_GTM_TIM2_CHANNEL7_INT == TRUE)
	gtm_timSetIRQMode(&TIMD3, TIM_CHANNEL7, SPC5_GTM_TIM2_CHANNEL7_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM2_7_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM2_7_INT_PRIORITY);

#if (SPC5_GTM_TIM2_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM2_CHANNEL7_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM2_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD3, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM2_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM2_CHANNEL7_INT */
#endif /* SPC5_GTM_TIM2_CH7_ENABLE */
#endif /* SPC5_GTM_USE_TIM2 */

#if (SPC5_GTM_USE_TIM3 == TRUE)
	TIMD4.tim = &(GTM_TIM_3);

	TIMD4.callbacks = (GTM_TIM_Channel_Callbacks **)gtm_tim3_callbacks;

	TIMD4.tim->GTM_TIM_GC_REG(RST).R = SPC5_GTM_TIM3_SW_RESET;
	TIMD4.sw_reset_reg = SPC5_GTM_TIM3_SW_RESET;

	TIMD4.tim->GTM_TIM_GC_REG(IN_SRC).R = SPC5_GTM_TIM3_IN_SRC;
	TIMD4.aux_in_src_reg = SPC5_GTM_TIM3_IN_SRC;


#if (SPC5_GTM_TIM3_CH0_ENABLE == TRUE)
	TIMD4.tim->GTM_CH_REG(0,CTRL).R = SPC5_GTM_TIM3_CH0_CTRL;
	TIMD4.tim->GTM_CH_REG(0,FLT_FE).R = SPC5_GTM_TIM3_CH0_FLT_FE;
	TIMD4.tim->GTM_CH_REG(0,FLT_RE).R = SPC5_GTM_TIM3_CH0_FLT_RE;
	TIMD4.tim->GTM_CH_REG(0,CNT).R = SPC5_GTM_TIM3_CH0_CNT;

	if ((TIMD4.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE & 5U) != 0U) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD4.tim->GTM_CH_REG(0,CNTS).R = SPC5_GTM_TIM3_CH0_CNTS;
		TIMD4.channel[0].cnts_reg = SPC5_GTM_TIM3_CH0_CNTS;
	}

	if (TIMD4.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE == 5U) { /* TGPS MODE */
		TIMD4.tim->GTM_CH_REG(0,GPR1).R = SPC5_GTM_TIM3_CH0_GPR1;
		TIMD4.channel[0].gpr1_reg = SPC5_GTM_TIM3_CH0_GPR1;
	}

	TIMD4.tim->GTM_CH_REG(0,ECTRL).R = SPC5_GTM_TIM3_CH0_ECTRL;

	/* Timeout detection */
	TIMD4.tim->GTM_CH_REG(0,TDUV).R = (SPC5_GTM_TIM3_CH0_TCS << 28) | SPC5_GTM_TIM3_CH0_TOV;

	TIMD4.channel[0].cnt_reg = SPC5_GTM_TIM3_CH0_CTRL;
	TIMD4.channel[0].flt_fe_reg = SPC5_GTM_TIM3_CH0_FLT_FE;
	TIMD4.channel[0].flt_re_reg = SPC5_GTM_TIM3_CH0_FLT_RE;
	TIMD4.channel[0].cnt_reg = SPC5_GTM_TIM3_CH0_CNT;
	TIMD4.channel[0].ectrl_reg = SPC5_GTM_TIM3_CH0_ECTRL;


#if (SPC5_GTM_TIM3_CHANNEL0_INT == TRUE)
	gtm_timSetIRQMode(&TIMD4, TIM_CHANNEL0, SPC5_GTM_TIM3_CHANNEL0_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM3_0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM3_0_INT_PRIORITY);

#if (SPC5_GTM_TIM3_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL0_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM3_CHANNEL0_INT */

#endif /* SPC5_GTM_TIM3_CH0_ENABLE */


#if (SPC5_GTM_TIM3_CH1_ENABLE == TRUE)
	TIMD4.tim->GTM_CH_REG(1,CTRL).R = SPC5_GTM_TIM3_CH1_CTRL;
	TIMD4.tim->GTM_CH_REG(1,FLT_FE).R = SPC5_GTM_TIM3_CH1_FLT_FE;
	TIMD4.tim->GTM_CH_REG(1,FLT_RE).R = SPC5_GTM_TIM3_CH1_FLT_RE;
	TIMD4.tim->GTM_CH_REG(1,CNT).R = SPC5_GTM_TIM3_CH1_CNT;
    if (TIMD4.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD4.tim->GTM_CH_REG(1,CNTS).R = SPC5_GTM_TIM3_CH1_CNTS;
		TIMD4.channel[1].cnts_reg = SPC5_GTM_TIM3_CH1_CNTS;
	}
    if (TIMD4.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD4.tim->GTM_CH_REG(1,GPR1).R = SPC5_GTM_TIM3_CH1_GPR1;
		TIMD4.channel[1].gpr1_reg = SPC5_GTM_TIM3_CH1_GPR1;
    }
	TIMD4.tim->GTM_CH_REG(1,ECTRL).R = SPC5_GTM_TIM3_CH1_ECTRL;

	/* Timeout detection */
	TIMD4.tim->GTM_CH_REG(1,TDUV).R = (SPC5_GTM_TIM3_CH1_TCS << 28) | SPC5_GTM_TIM3_CH1_TOV;

	TIMD4.channel[1].cnt_reg = SPC5_GTM_TIM3_CH1_CTRL;
	TIMD4.channel[1].flt_fe_reg = SPC5_GTM_TIM3_CH1_FLT_FE;
	TIMD4.channel[1].flt_re_reg = SPC5_GTM_TIM3_CH1_FLT_RE;
	TIMD4.channel[1].cnt_reg = SPC5_GTM_TIM3_CH1_CNT;
	TIMD4.channel[1].ectrl_reg = SPC5_GTM_TIM3_CH1_ECTRL;


#if (SPC5_GTM_TIM3_CHANNEL1_INT == TRUE)
	gtm_timSetIRQMode(&TIMD4, TIM_CHANNEL1, SPC5_GTM_TIM3_CHANNEL1_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM3_1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM3_1_INT_PRIORITY);

#if (SPC5_GTM_TIM3_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL1_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM3_CHANNEL1_INT */

#endif /* SPC5_GTM_TIM3_CH1_ENABLE */


#if (SPC5_GTM_TIM3_CH2_ENABLE == TRUE)
	TIMD4.tim->GTM_CH_REG(2,CTRL).R = SPC5_GTM_TIM3_CH2_CTRL;
	TIMD4.tim->GTM_CH_REG(2,FLT_FE).R = SPC5_GTM_TIM3_CH2_FLT_FE;
	TIMD4.tim->GTM_CH_REG(2,FLT_RE).R = SPC5_GTM_TIM3_CH2_FLT_RE;
	TIMD4.tim->GTM_CH_REG(2,CNT).R = SPC5_GTM_TIM3_CH2_CNT;
	if (TIMD4.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD4.tim->GTM_CH_REG(2,CNTS).R = SPC5_GTM_TIM3_CH2_CNTS;
		TIMD4.channel[2].cnts_reg = SPC5_GTM_TIM3_CH2_CNTS;
	}
	if (TIMD4.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD4.tim->GTM_CH_REG(2,GPR1).R = SPC5_GTM_TIM3_CH2_GPR1;
		TIMD4.channel[2].gpr1_reg = SPC5_GTM_TIM3_CH2_GPR1;
	}
	TIMD4.tim->GTM_CH_REG(2,ECTRL).R = SPC5_GTM_TIM3_CH2_ECTRL;

	/* Timeout detection */
	TIMD4.tim->GTM_CH_REG(2,TDUV).R = (SPC5_GTM_TIM3_CH2_TCS << 28) | SPC5_GTM_TIM3_CH2_TOV;

	TIMD4.channel[2].cnt_reg = SPC5_GTM_TIM3_CH2_CTRL;
	TIMD4.channel[2].flt_fe_reg = SPC5_GTM_TIM3_CH2_FLT_FE;
	TIMD4.channel[2].flt_re_reg = SPC5_GTM_TIM3_CH2_FLT_RE;
	TIMD4.channel[2].cnt_reg = SPC5_GTM_TIM3_CH2_CNT;
	TIMD4.channel[2].ectrl_reg = SPC5_GTM_TIM3_CH2_ECTRL;


#if (SPC5_GTM_TIM3_CHANNEL2_INT == TRUE)
	gtm_timSetIRQMode(&TIMD4, TIM_CHANNEL2, SPC5_GTM_TIM3_CHANNEL2_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM3_2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM3_2_INT_PRIORITY);

#if (SPC5_GTM_TIM3_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL2_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM3_CHANNEL2_INT */

#endif /* SPC5_GTM_TIM3_CH2_ENABLE */


#if (SPC5_GTM_TIM3_CH3_ENABLE == TRUE)
	TIMD4.tim->GTM_CH_REG(3,CTRL).R = SPC5_GTM_TIM3_CH3_CTRL;
	TIMD4.tim->GTM_CH_REG(3,FLT_FE).R = SPC5_GTM_TIM3_CH3_FLT_FE;
	TIMD4.tim->GTM_CH_REG(3,FLT_RE).R = SPC5_GTM_TIM3_CH3_FLT_RE;
	TIMD4.tim->GTM_CH_REG(3,CNT).R = SPC5_GTM_TIM3_CH3_CNT;
	if (TIMD4.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD4.tim->GTM_CH_REG(3,CNTS).R = SPC5_GTM_TIM3_CH3_CNTS;
		TIMD4.channel[3].cnts_reg = SPC5_GTM_TIM3_CH3_CNTS;
	}
	if (TIMD4.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD4.tim->GTM_CH_REG(3,GPR1).R = SPC5_GTM_TIM3_CH3_GPR1;
		TIMD4.channel[3].gpr1_reg = SPC5_GTM_TIM3_CH3_GPR1;
	}
	TIMD4.tim->GTM_CH_REG(3,ECTRL).R = SPC5_GTM_TIM3_CH3_ECTRL;

	/* Timeout detection */
	TIMD4.tim->GTM_CH_REG(3,TDUV).R = (SPC5_GTM_TIM3_CH3_TCS << 28) | SPC5_GTM_TIM3_CH3_TOV;

	TIMD4.channel[3].cnt_reg = SPC5_GTM_TIM3_CH3_CTRL;
	TIMD4.channel[3].flt_fe_reg = SPC5_GTM_TIM3_CH3_FLT_FE;
	TIMD4.channel[3].flt_re_reg = SPC5_GTM_TIM3_CH3_FLT_RE;
	TIMD4.channel[3].cnt_reg = SPC5_GTM_TIM3_CH3_CNT;
	TIMD4.channel[3].ectrl_reg = SPC5_GTM_TIM3_CH3_ECTRL;


#if (SPC5_GTM_TIM3_CHANNEL3_INT == TRUE)
	gtm_timSetIRQMode(&TIMD4, TIM_CHANNEL3, SPC5_GTM_TIM3_CHANNEL3_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM3_3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM3_3_INT_PRIORITY);

#if (SPC5_GTM_TIM3_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL3_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM3_CHANNEL3_INT */

#endif /* SPC5_GTM_TIM3_CH3_ENABLE */


#if (SPC5_GTM_TIM3_CH4_ENABLE == TRUE)
	TIMD4.tim->GTM_CH_REG(4,CTRL).R = SPC5_GTM_TIM3_CH4_CTRL;
	TIMD4.tim->GTM_CH_REG(4,FLT_FE).R = SPC5_GTM_TIM3_CH4_FLT_FE;
	TIMD4.tim->GTM_CH_REG(4,FLT_RE).R = SPC5_GTM_TIM3_CH4_FLT_RE;
	TIMD4.tim->GTM_CH_REG(4,CNT).R = SPC5_GTM_TIM3_CH4_CNT;
	if (TIMD4.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD4.tim->GTM_CH_REG(4,CNTS).R = SPC5_GTM_TIM3_CH4_CNTS;
		TIMD4.channel[4].cnts_reg = SPC5_GTM_TIM3_CH4_CNTS;
	}
	if (TIMD4.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD4.tim->GTM_CH_REG(4,GPR1).R = SPC5_GTM_TIM3_CH4_GPR1;
		TIMD4.channel[4].gpr1_reg = SPC5_GTM_TIM3_CH4_GPR1;
	}
	TIMD4.tim->GTM_CH_REG(4,ECTRL).R = SPC5_GTM_TIM3_CH4_ECTRL;

	/* Timeout detection */
	TIMD4.tim->GTM_CH_REG(4,TDUV).R = (SPC5_GTM_TIM3_CH4_TCS << 28) | SPC5_GTM_TIM3_CH4_TOV;

	TIMD4.channel[4].cnt_reg = SPC5_GTM_TIM3_CH4_CTRL;
	TIMD4.channel[4].flt_fe_reg = SPC5_GTM_TIM3_CH4_FLT_FE;
	TIMD4.channel[4].flt_re_reg = SPC5_GTM_TIM3_CH4_FLT_RE;
	TIMD4.channel[4].cnt_reg = SPC5_GTM_TIM3_CH4_CNT;
	TIMD4.channel[4].ectrl_reg = SPC5_GTM_TIM3_CH4_ECTRL;


#if (SPC5_GTM_TIM3_CHANNEL4_INT == TRUE)
	gtm_timSetIRQMode(&TIMD4, TIM_CHANNEL4, SPC5_GTM_TIM3_CHANNEL4_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM3_4_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM3_4_INT_PRIORITY);

#if (SPC5_GTM_TIM3_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL4_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM3_CHANNEL4_INT */

#endif /* SPC5_GTM_TIM3_CH4_ENABLE */


#if (SPC5_GTM_TIM3_CH5_ENABLE == TRUE)
	TIMD4.tim->GTM_CH_REG(5,CTRL).R = SPC5_GTM_TIM3_CH5_CTRL;
	TIMD4.tim->GTM_CH_REG(5,FLT_FE).R = SPC5_GTM_TIM3_CH5_FLT_FE;
	TIMD4.tim->GTM_CH_REG(5,FLT_RE).R = SPC5_GTM_TIM3_CH5_FLT_RE;
	TIMD4.tim->GTM_CH_REG(5,CNT).R = SPC5_GTM_TIM3_CH5_CNT;
	if (TIMD4.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD4.tim->GTM_CH_REG(5,CNTS).R = SPC5_GTM_TIM3_CH5_CNTS;
		TIMD4.channel[5].cnts_reg = SPC5_GTM_TIM3_CH5_CNTS;
	}
	if (TIMD4.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD4.tim->GTM_CH_REG(5,GPR1).R = SPC5_GTM_TIM3_CH5_GPR1;
		TIMD4.channel[5].gpr1_reg = SPC5_GTM_TIM3_CH5_GPR1;
	}
	TIMD4.tim->GTM_CH_REG(5,ECTRL).R = SPC5_GTM_TIM3_CH5_ECTRL;

	/* Timeout detection */
	TIMD4.tim->GTM_CH_REG(5,TDUV).R = (SPC5_GTM_TIM3_CH5_TCS << 28) | SPC5_GTM_TIM5_CH4_TOV;

	TIMD4.channel[5].cnt_reg = SPC5_GTM_TIM3_CH5_CTRL;
	TIMD4.channel[5].flt_fe_reg = SPC5_GTM_TIM3_CH5_FLT_FE;
	TIMD4.channel[5].flt_re_reg = SPC5_GTM_TIM3_CH5_FLT_RE;
	TIMD4.channel[5].cnt_reg = SPC5_GTM_TIM3_CH5_CNT;
	TIMD4.channel[5].ectrl_reg = SPC5_GTM_TIM3_CH5_ECTRL;


#if (SPC5_GTM_TIM3_CHANNEL5_INT == TRUE)
	gtm_timSetIRQMode(&TIMD4, TIM_CHANNEL5, SPC5_GTM_TIM3_CHANNEL5_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM3_5_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM3_5_INT_PRIORITY);

#if (SPC5_GTM_TIM3_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL5_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM3_CHANNEL5_INT */

#endif /* SPC5_GTM_TIM3_CH5_ENABLE */


#if (SPC5_GTM_TIM3_CH6_ENABLE == TRUE)
	TIMD4.tim->GTM_CH_REG(6,CTRL).R = SPC5_GTM_TIM3_CH6_CTRL;
	TIMD4.tim->GTM_CH_REG(6,FLT_FE).R = SPC5_GTM_TIM3_CH6_FLT_FE;
	TIMD4.tim->GTM_CH_REG(6,FLT_RE).R = SPC5_GTM_TIM3_CH6_FLT_RE;
	TIMD4.tim->GTM_CH_REG(6,CNT).R = SPC5_GTM_TIM3_CH6_CNT;
	if (TIMD4.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD4.tim->GTM_CH_REG(6,CNTS).R = SPC5_GTM_TIM3_CH6_CNTS;
		TIMD4.channel[6].cnts_reg = SPC5_GTM_TIM3_CH6_CNTS;
	}
	if (TIMD4.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD4.tim->GTM_CH_REG(6,GPR1).R = SPC5_GTM_TIM3_CH6_GPR1;
		TIMD4.channel[6].gpr1_reg = SPC5_GTM_TIM3_CH6_GPR1;
	}
	TIMD4.tim->GTM_CH_REG(6,ECTRL).R = SPC5_GTM_TIM3_CH6_ECTRL;

	/* Timeout detection */
	TIMD4.tim->GTM_CH_REG(6,TDUV).R = (SPC5_GTM_TIM3_CH5_TCS << 28) | SPC5_GTM_TIM5_CH5_TOV;

	TIMD4.channel[6].cnt_reg = SPC5_GTM_TIM3_CH6_CTRL;
	TIMD4.channel[6].flt_fe_reg = SPC5_GTM_TIM3_CH6_FLT_FE;
	TIMD4.channel[6].flt_re_reg = SPC5_GTM_TIM3_CH6_FLT_RE;
	TIMD4.channel[6].cnt_reg = SPC5_GTM_TIM3_CH6_CNT;
	TIMD4.channel[6].ectrl_reg = SPC5_GTM_TIM3_CH6_ECTRL;


#if (SPC5_GTM_TIM3_CHANNEL6_INT == TRUE)
	gtm_timSetIRQMode(&TIMD4, TIM_CHANNEL6, SPC5_GTM_TIM3_CHANNEL6_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM3_6_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM3_6_INT_PRIORITY);

#if (SPC5_GTM_TIM3_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL6_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM3_CHANNEL6_INT */

#endif /* SPC5_GTM_TIM3_CH6_ENABLE */


#if (SPC5_GTM_TIM3_CH7_ENABLE == TRUE)
	TIMD4.tim->GTM_CH_REG(7,CTRL).R = SPC5_GTM_TIM3_CH7_CTRL;
	TIMD4.tim->GTM_CH_REG(7,FLT_FE).R = SPC5_GTM_TIM3_CH7_FLT_FE;
	TIMD4.tim->GTM_CH_REG(7,FLT_RE).R = SPC5_GTM_TIM3_CH7_FLT_RE;
	TIMD4.tim->GTM_CH_REG(7,CNT).R = SPC5_GTM_TIM3_CH7_CNT;
	if (TIMD4.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD4.tim->GTM_CH_REG(7,CNTS).R = SPC5_GTM_TIM3_CH7_CNTS;
		TIMD4.channel[7].cnts_reg = SPC5_GTM_TIM3_CH7_CNTS;
	}
	if (TIMD4.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD4.tim->GTM_CH_REG(7,GPR1).R = SPC5_GTM_TIM3_CH7_GPR1;
		TIMD4.channel[7].gpr1_reg = SPC5_GTM_TIM3_CH7_GPR1;
	}
	TIMD4.tim->GTM_CH_REG(7,ECTRL).R = SPC5_GTM_TIM3_CH7_ECTRL;

	/* Timeout detection */
	TIMD4.tim->GTM_CH_REG(7,TDUV).R = (SPC5_GTM_TIM3_CH7_TCS << 28) | SPC5_GTM_TIM5_CH7_TOV;

	TIMD4.channel[7].cnt_reg = SPC5_GTM_TIM3_CH7_CTRL;
	TIMD4.channel[7].flt_fe_reg = SPC5_GTM_TIM3_CH7_FLT_FE;
	TIMD4.channel[7].flt_re_reg = SPC5_GTM_TIM3_CH7_FLT_RE;
	TIMD4.channel[7].cnt_reg = SPC5_GTM_TIM3_CH7_CNT;
	TIMD4.channel[7].ectrl_reg = SPC5_GTM_TIM3_CH7_ECTRL;


#if (SPC5_GTM_TIM3_CHANNEL7_INT == TRUE)
	gtm_timSetIRQMode(&TIMD4, TIM_CHANNEL7, SPC5_GTM_TIM3_CHANNEL7_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM3_7_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM3_7_INT_PRIORITY);

#if (SPC5_GTM_TIM3_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM3_CHANNEL7_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM3_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD4, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM3_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM3_CHANNEL7_INT */
#endif /* SPC5_GTM_TIM3_CH7_ENABLE */
#endif /* SPC5_GTM_USE_TIM3 */

#if (SPC5_GTM_USE_TIM4 == TRUE)
	TIMD5.tim = &(GTM_TIM_4);

	TIMD5.callbacks = (GTM_TIM_Channel_Callbacks **)gtm_tim4_callbacks;

	TIMD5.tim->GTM_TIM_GC_REG(RST).R = SPC5_GTM_TIM4_SW_RESET;
	TIMD5.sw_reset_reg = SPC5_GTM_TIM4_SW_RESET;

	TIMD5.tim->GTM_TIM_GC_REG(IN_SRC).R = SPC5_GTM_TIM4_IN_SRC;
	TIMD5.aux_in_src_reg = SPC5_GTM_TIM4_IN_SRC;


#if (SPC5_GTM_TIM4_CH0_ENABLE == TRUE)
	TIMD5.tim->GTM_CH_REG(0,CTRL).R = SPC5_GTM_TIM4_CH0_CTRL;
	TIMD5.tim->GTM_CH_REG(0,FLT_FE).R = SPC5_GTM_TIM4_CH0_FLT_FE;
	TIMD5.tim->GTM_CH_REG(0,FLT_RE).R = SPC5_GTM_TIM4_CH0_FLT_RE;
	TIMD5.tim->GTM_CH_REG(0,CNT).R = SPC5_GTM_TIM4_CH0_CNT;

	if ((TIMD5.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE & 5U) != 0U) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD5.tim->GTM_CH_REG(0,CNTS).R = SPC5_GTM_TIM4_CH0_CNTS;
		TIMD5.channel[0].cnts_reg = SPC5_GTM_TIM4_CH0_CNTS;
	}

	if (TIMD5.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE == 5U) { /* TGPS MODE */
		TIMD5.tim->GTM_CH_REG(0,GPR1).R = SPC5_GTM_TIM4_CH0_GPR1;
		TIMD5.channel[0].gpr1_reg = SPC5_GTM_TIM4_CH0_GPR1;
	}

	TIMD5.tim->GTM_CH_REG(0,ECTRL).R = SPC5_GTM_TIM4_CH0_ECTRL;

	/* Timeout detection */
	TIMD5.tim->GTM_CH_REG(0,TDUV).R = (SPC5_GTM_TIM4_CH0_TCS << 28) | SPC5_GTM_TIM4_CH0_TOV;

	TIMD5.channel[0].cnt_reg = SPC5_GTM_TIM4_CH0_CTRL;
	TIMD5.channel[0].flt_fe_reg = SPC5_GTM_TIM4_CH0_FLT_FE;
	TIMD5.channel[0].flt_re_reg = SPC5_GTM_TIM4_CH0_FLT_RE;
	TIMD5.channel[0].cnt_reg = SPC5_GTM_TIM4_CH0_CNT;
	TIMD5.channel[0].ectrl_reg = SPC5_GTM_TIM4_CH0_ECTRL;


#if (SPC5_GTM_TIM4_CHANNEL0_INT == TRUE)
	gtm_timSetIRQMode(&TIMD5, TIM_CHANNEL0, SPC5_GTM_TIM4_CHANNEL0_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM4_0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM4_0_INT_PRIORITY);

#if (SPC5_GTM_TIM4_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL0_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM4_CHANNEL0_INT */

#endif /* SPC5_GTM_TIM4_CH0_ENABLE */


#if (SPC5_GTM_TIM4_CH1_ENABLE == TRUE)
	TIMD5.tim->GTM_CH_REG(1,CTRL).R = SPC5_GTM_TIM4_CH1_CTRL;
	TIMD5.tim->GTM_CH_REG(1,FLT_FE).R = SPC5_GTM_TIM4_CH1_FLT_FE;
	TIMD5.tim->GTM_CH_REG(1,FLT_RE).R = SPC5_GTM_TIM4_CH1_FLT_RE;
	TIMD5.tim->GTM_CH_REG(1,CNT).R = SPC5_GTM_TIM4_CH1_CNT;
    if (TIMD5.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD5.tim->GTM_CH_REG(1,CNTS).R = SPC5_GTM_TIM4_CH1_CNTS;
		TIMD5.channel[1].cnts_reg = SPC5_GTM_TIM4_CH1_CNTS;
	}
    if (TIMD5.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD5.tim->GTM_CH_REG(1,GPR1).R = SPC5_GTM_TIM4_CH1_GPR1;
		TIMD5.channel[1].gpr1_reg = SPC5_GTM_TIM4_CH1_GPR1;
    }
	TIMD5.tim->GTM_CH_REG(1,ECTRL).R = SPC5_GTM_TIM4_CH1_ECTRL;

	/* Timeout detection */
	TIMD5.tim->GTM_CH_REG(1,TDUV).R = (SPC5_GTM_TIM4_CH1_TCS << 28) | SPC5_GTM_TIM4_CH1_TOV;

	TIMD5.channel[1].cnt_reg = SPC5_GTM_TIM4_CH1_CTRL;
	TIMD5.channel[1].flt_fe_reg = SPC5_GTM_TIM4_CH1_FLT_FE;
	TIMD5.channel[1].flt_re_reg = SPC5_GTM_TIM4_CH1_FLT_RE;
	TIMD5.channel[1].cnt_reg = SPC5_GTM_TIM4_CH1_CNT;
	TIMD5.channel[1].ectrl_reg = SPC5_GTM_TIM4_CH1_ECTRL;


#if (SPC5_GTM_TIM4_CHANNEL1_INT == TRUE)
	gtm_timSetIRQMode(&TIMD5, TIM_CHANNEL1, SPC5_GTM_TIM4_CHANNEL1_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM4_1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM4_1_INT_PRIORITY);

#if (SPC5_GTM_TIM4_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL1_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM4_CHANNEL1_INT */

#endif /* SPC5_GTM_TIM4_CH1_ENABLE */


#if (SPC5_GTM_TIM4_CH2_ENABLE == TRUE)
	TIMD5.tim->GTM_CH_REG(2,CTRL).R = SPC5_GTM_TIM4_CH2_CTRL;
	TIMD5.tim->GTM_CH_REG(2,FLT_FE).R = SPC5_GTM_TIM4_CH2_FLT_FE;
	TIMD5.tim->GTM_CH_REG(2,FLT_RE).R = SPC5_GTM_TIM4_CH2_FLT_RE;
	TIMD5.tim->GTM_CH_REG(2,CNT).R = SPC5_GTM_TIM4_CH2_CNT;
	if (TIMD5.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD5.tim->GTM_CH_REG(2,CNTS).R = SPC5_GTM_TIM4_CH2_CNTS;
		TIMD5.channel[2].cnts_reg = SPC5_GTM_TIM4_CH2_CNTS;
	}
	if (TIMD5.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD5.tim->GTM_CH_REG(2,GPR1).R = SPC5_GTM_TIM4_CH2_GPR1;
		TIMD5.channel[2].gpr1_reg = SPC5_GTM_TIM4_CH2_GPR1;
	}
	TIMD5.tim->GTM_CH_REG(2,ECTRL).R = SPC5_GTM_TIM4_CH2_ECTRL;

	/* Timeout detection */
	TIMD5.tim->GTM_CH_REG(2,TDUV).R = (SPC5_GTM_TIM4_CH2_TCS << 28) | SPC5_GTM_TIM4_CH2_TOV;

	TIMD5.channel[2].cnt_reg = SPC5_GTM_TIM4_CH2_CTRL;
	TIMD5.channel[2].flt_fe_reg = SPC5_GTM_TIM4_CH2_FLT_FE;
	TIMD5.channel[2].flt_re_reg = SPC5_GTM_TIM4_CH2_FLT_RE;
	TIMD5.channel[2].cnt_reg = SPC5_GTM_TIM4_CH2_CNT;
	TIMD5.channel[2].ectrl_reg = SPC5_GTM_TIM4_CH2_ECTRL;


#if (SPC5_GTM_TIM4_CHANNEL2_INT == TRUE)
	gtm_timSetIRQMode(&TIMD5, TIM_CHANNEL2, SPC5_GTM_TIM4_CHANNEL2_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM4_2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM4_2_INT_PRIORITY);

#if (SPC5_GTM_TIM4_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL2_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM4_CHANNEL2_INT */

#endif /* SPC5_GTM_TIM4_CH2_ENABLE */


#if (SPC5_GTM_TIM4_CH3_ENABLE == TRUE)
	TIMD5.tim->GTM_CH_REG(3,CTRL).R = SPC5_GTM_TIM4_CH3_CTRL;
	TIMD5.tim->GTM_CH_REG(3,FLT_FE).R = SPC5_GTM_TIM4_CH3_FLT_FE;
	TIMD5.tim->GTM_CH_REG(3,FLT_RE).R = SPC5_GTM_TIM4_CH3_FLT_RE;
	TIMD5.tim->GTM_CH_REG(3,CNT).R = SPC5_GTM_TIM4_CH3_CNT;
	if (TIMD5.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD5.tim->GTM_CH_REG(3,CNTS).R = SPC5_GTM_TIM4_CH3_CNTS;
		TIMD5.channel[3].cnts_reg = SPC5_GTM_TIM4_CH3_CNTS;
	}
	if (TIMD5.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD5.tim->GTM_CH_REG(3,GPR1).R = SPC5_GTM_TIM4_CH3_GPR1;
		TIMD5.channel[3].gpr1_reg = SPC5_GTM_TIM4_CH3_GPR1;
	}
	TIMD5.tim->GTM_CH_REG(3,ECTRL).R = SPC5_GTM_TIM4_CH3_ECTRL;

	/* Timeout detection */
	TIMD5.tim->GTM_CH_REG(3,TDUV).R = (SPC5_GTM_TIM4_CH3_TCS << 28) | SPC5_GTM_TIM4_CH3_TOV;

	TIMD5.channel[3].cnt_reg = SPC5_GTM_TIM4_CH3_CTRL;
	TIMD5.channel[3].flt_fe_reg = SPC5_GTM_TIM4_CH3_FLT_FE;
	TIMD5.channel[3].flt_re_reg = SPC5_GTM_TIM4_CH3_FLT_RE;
	TIMD5.channel[3].cnt_reg = SPC5_GTM_TIM4_CH3_CNT;
	TIMD5.channel[3].ectrl_reg = SPC5_GTM_TIM4_CH3_ECTRL;


#if (SPC5_GTM_TIM4_CHANNEL3_INT == TRUE)
	gtm_timSetIRQMode(&TIMD5, TIM_CHANNEL3, SPC5_GTM_TIM4_CHANNEL3_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM4_3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM4_3_INT_PRIORITY);

#if (SPC5_GTM_TIM4_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL3_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM4_CHANNEL3_INT */

#endif /* SPC5_GTM_TIM4_CH3_ENABLE */


#if (SPC5_GTM_TIM4_CH4_ENABLE == TRUE)
	TIMD5.tim->GTM_CH_REG(4,CTRL).R = SPC5_GTM_TIM4_CH4_CTRL;
	TIMD5.tim->GTM_CH_REG(4,FLT_FE).R = SPC5_GTM_TIM4_CH4_FLT_FE;
	TIMD5.tim->GTM_CH_REG(4,FLT_RE).R = SPC5_GTM_TIM4_CH4_FLT_RE;
	TIMD5.tim->GTM_CH_REG(4,CNT).R = SPC5_GTM_TIM4_CH4_CNT;
	if (TIMD5.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD5.tim->GTM_CH_REG(4,CNTS).R = SPC5_GTM_TIM4_CH4_CNTS;
		TIMD5.channel[4].cnts_reg = SPC5_GTM_TIM4_CH4_CNTS;
	}
	if (TIMD5.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD5.tim->GTM_CH_REG(4,GPR1).R = SPC5_GTM_TIM4_CH4_GPR1;
		TIMD5.channel[4].gpr1_reg = SPC5_GTM_TIM4_CH4_GPR1;
	}
	TIMD5.tim->GTM_CH_REG(4,ECTRL).R = SPC5_GTM_TIM4_CH4_ECTRL;

	/* Timeout detection */
	TIMD5.tim->GTM_CH_REG(4,TDUV).R = (SPC5_GTM_TIM4_CH4_TCS << 28) | SPC5_GTM_TIM4_CH4_TOV;

	TIMD5.channel[4].cnt_reg = SPC5_GTM_TIM4_CH4_CTRL;
	TIMD5.channel[4].flt_fe_reg = SPC5_GTM_TIM4_CH4_FLT_FE;
	TIMD5.channel[4].flt_re_reg = SPC5_GTM_TIM4_CH4_FLT_RE;
	TIMD5.channel[4].cnt_reg = SPC5_GTM_TIM4_CH4_CNT;
	TIMD5.channel[4].ectrl_reg = SPC5_GTM_TIM4_CH4_ECTRL;


#if (SPC5_GTM_TIM4_CHANNEL4_INT == TRUE)
	gtm_timSetIRQMode(&TIMD5, TIM_CHANNEL4, SPC5_GTM_TIM4_CHANNEL4_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM4_4_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM4_4_INT_PRIORITY);

#if (SPC5_GTM_TIM4_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL4_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM4_CHANNEL4_INT */

#endif /* SPC5_GTM_TIM4_CH4_ENABLE */


#if (SPC5_GTM_TIM4_CH5_ENABLE == TRUE)
	TIMD5.tim->GTM_CH_REG(5,CTRL).R = SPC5_GTM_TIM4_CH5_CTRL;
	TIMD5.tim->GTM_CH_REG(5,FLT_FE).R = SPC5_GTM_TIM4_CH5_FLT_FE;
	TIMD5.tim->GTM_CH_REG(5,FLT_RE).R = SPC5_GTM_TIM4_CH5_FLT_RE;
	TIMD5.tim->GTM_CH_REG(5,CNT).R = SPC5_GTM_TIM4_CH5_CNT;
	if (TIMD5.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD5.tim->GTM_CH_REG(5,CNTS).R = SPC5_GTM_TIM4_CH5_CNTS;
		TIMD5.channel[5].cnts_reg = SPC5_GTM_TIM4_CH5_CNTS;
	}
	if (TIMD5.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD5.tim->GTM_CH_REG(5,GPR1).R = SPC5_GTM_TIM4_CH5_GPR1;
		TIMD5.channel[5].gpr1_reg = SPC5_GTM_TIM4_CH5_GPR1;
	}
	TIMD5.tim->GTM_CH_REG(5,ECTRL).R = SPC5_GTM_TIM4_CH5_ECTRL;

	/* Timeout detection */
	TIMD5.tim->GTM_CH_REG(5,TDUV).R = (SPC5_GTM_TIM4_CH5_TCS << 28) | SPC5_GTM_TIM4_CH5_TOV;

	TIMD5.channel[5].cnt_reg = SPC5_GTM_TIM4_CH5_CTRL;
	TIMD5.channel[5].flt_fe_reg = SPC5_GTM_TIM4_CH5_FLT_FE;
	TIMD5.channel[5].flt_re_reg = SPC5_GTM_TIM4_CH5_FLT_RE;
	TIMD5.channel[5].cnt_reg = SPC5_GTM_TIM4_CH5_CNT;
	TIMD5.channel[5].ectrl_reg = SPC5_GTM_TIM4_CH5_ECTRL;


#if (SPC5_GTM_TIM4_CHANNEL5_INT == TRUE)
	gtm_timSetIRQMode(&TIMD5, TIM_CHANNEL5, SPC5_GTM_TIM4_CHANNEL5_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM4_5_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM4_5_INT_PRIORITY);

#if (SPC5_GTM_TIM4_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL5_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM4_CHANNEL5_INT */

#endif /* SPC5_GTM_TIM4_CH5_ENABLE */


#if (SPC5_GTM_TIM4_CH6_ENABLE == TRUE)
	TIMD5.tim->GTM_CH_REG(6,CTRL).R = SPC5_GTM_TIM4_CH6_CTRL;
	TIMD5.tim->GTM_CH_REG(6,FLT_FE).R = SPC5_GTM_TIM4_CH6_FLT_FE;
	TIMD5.tim->GTM_CH_REG(6,FLT_RE).R = SPC5_GTM_TIM4_CH6_FLT_RE;
	TIMD5.tim->GTM_CH_REG(6,CNT).R = SPC5_GTM_TIM4_CH6_CNT;
	if (TIMD5.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD5.tim->GTM_CH_REG(6,CNTS).R = SPC5_GTM_TIM4_CH6_CNTS;
		TIMD5.channel[6].cnts_reg = SPC5_GTM_TIM4_CH6_CNTS;
	}
	if (TIMD5.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD5.tim->GTM_CH_REG(6,GPR1).R = SPC5_GTM_TIM4_CH6_GPR1;
		TIMD5.channel[6].gpr1_reg = SPC5_GTM_TIM4_CH6_GPR1;
	}
	TIMD5.tim->GTM_CH_REG(6,ECTRL).R = SPC5_GTM_TIM4_CH6_ECTRL;

	/* Timeout detection */
	TIMD5.tim->GTM_CH_REG(6,TDUV).R = (SPC5_GTM_TIM4_CH6_TCS << 28) | SPC5_GTM_TIM4_CH6_TOV;

	TIMD5.channel[6].cnt_reg = SPC5_GTM_TIM4_CH6_CTRL;
	TIMD5.channel[6].flt_fe_reg = SPC5_GTM_TIM4_CH6_FLT_FE;
	TIMD5.channel[6].flt_re_reg = SPC5_GTM_TIM4_CH6_FLT_RE;
	TIMD5.channel[6].cnt_reg = SPC5_GTM_TIM4_CH6_CNT;
	TIMD5.channel[6].ectrl_reg = SPC5_GTM_TIM4_CH6_ECTRL;


#if (SPC5_GTM_TIM4_CHANNEL6_INT == TRUE)
	gtm_timSetIRQMode(&TIMD5, TIM_CHANNEL6, SPC5_GTM_TIM4_CHANNEL6_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM4_6_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM4_6_INT_PRIORITY);

#if (SPC5_GTM_TIM4_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL6_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM4_CHANNEL6_INT */

#endif /* SPC5_GTM_TIM4_CH6_ENABLE */


#if (SPC5_GTM_TIM4_CH7_ENABLE == TRUE)
	TIMD5.tim->GTM_CH_REG(7,CTRL).R = SPC5_GTM_TIM4_CH7_CTRL;
	TIMD5.tim->GTM_CH_REG(7,FLT_FE).R = SPC5_GTM_TIM4_CH7_FLT_FE;
	TIMD5.tim->GTM_CH_REG(7,FLT_RE).R = SPC5_GTM_TIM4_CH7_FLT_RE;
	TIMD5.tim->GTM_CH_REG(7,CNT).R = SPC5_GTM_TIM4_CH7_CNT;
	if (TIMD5.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD5.tim->GTM_CH_REG(7,CNTS).R = SPC5_GTM_TIM4_CH7_CNTS;
		TIMD5.channel[7].cnts_reg = SPC5_GTM_TIM4_CH7_CNTS;
	}
	if (TIMD5.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD5.tim->GTM_CH_REG(7,GPR1).R = SPC5_GTM_TIM4_CH7_GPR1;
		TIMD5.channel[7].gpr1_reg = SPC5_GTM_TIM4_CH7_GPR1;
	}
	TIMD5.tim->GTM_CH_REG(7,ECTRL).R = SPC5_GTM_TIM4_CH7_ECTRL;

	/* Timeout detection */
	TIMD5.tim->GTM_CH_REG(7,TDUV).R = (SPC5_GTM_TIM4_CH7_TCS << 28) | SPC5_GTM_TIM4_CH7_TOV;

	TIMD5.channel[7].cnt_reg = SPC5_GTM_TIM4_CH7_CTRL;
	TIMD5.channel[7].flt_fe_reg = SPC5_GTM_TIM4_CH7_FLT_FE;
	TIMD5.channel[7].flt_re_reg = SPC5_GTM_TIM4_CH7_FLT_RE;
	TIMD5.channel[7].cnt_reg = SPC5_GTM_TIM4_CH7_CNT;
	TIMD5.channel[7].ectrl_reg = SPC5_GTM_TIM4_CH7_ECTRL;


#if (SPC5_GTM_TIM4_CHANNEL7_INT == TRUE)
	gtm_timSetIRQMode(&TIMD5, TIM_CHANNEL7, SPC5_GTM_TIM4_CHANNEL7_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM4_7_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM4_7_INT_PRIORITY);

#if (SPC5_GTM_TIM4_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM4_CHANNEL7_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM4_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD5, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM4_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM4_CHANNEL7_INT */
#endif /* SPC5_GTM_TIM4_CH7_ENABLE */
#endif /* SPC5_GTM_USE_TIM4 */

#if (SPC5_GTM_USE_TIM5 == TRUE)
	TIMD6.tim = &(GTM_TIM_5);

	TIMD6.callbacks = (GTM_TIM_Channel_Callbacks **)gtm_tim5_callbacks;

	TIMD6.tim->GTM_TIM_GC_REG(RST).R = SPC5_GTM_TIM5_SW_RESET;
	TIMD6.sw_reset_reg = SPC5_GTM_TIM5_SW_RESET;

	TIMD6.tim->GTM_TIM_GC_REG(IN_SRC).R = SPC5_GTM_TIM5_IN_SRC;
	TIMD6.aux_in_src_reg = SPC5_GTM_TIM5_IN_SRC;


#if (SPC5_GTM_TIM5_CH0_ENABLE == TRUE)
	TIMD6.tim->GTM_CH_REG(0,CTRL).R = SPC5_GTM_TIM5_CH0_CTRL;
	TIMD6.tim->GTM_CH_REG(0,FLT_FE).R = SPC5_GTM_TIM5_CH0_FLT_FE;
	TIMD6.tim->GTM_CH_REG(0,FLT_RE).R = SPC5_GTM_TIM5_CH0_FLT_RE;
	TIMD6.tim->GTM_CH_REG(0,CNT).R = SPC5_GTM_TIM5_CH0_CNT;

	if ((TIMD6.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE & 5U) != 0U) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD6.tim->GTM_CH_REG(0,CNTS).R = SPC5_GTM_TIM5_CH0_CNTS;
		TIMD6.channel[0].cnts_reg = SPC5_GTM_TIM5_CH0_CNTS;
	}

	if (TIMD6.tim->GTM_CH_REG(0,CTRL).B.TIM_MODE == 5U) { /* TGPS MODE */
		TIMD6.tim->GTM_CH_REG(0,GPR1).R = SPC5_GTM_TIM5_CH0_GPR1;
		TIMD6.channel[0].gpr1_reg = SPC5_GTM_TIM5_CH0_GPR1;
	}

	TIMD6.tim->GTM_CH_REG(0,ECTRL).R = SPC5_GTM_TIM5_CH0_ECTRL;

	/* Timeout detection */
	TIMD6.tim->GTM_CH_REG(0,TDUV).R = (SPC5_GTM_TIM5_CH0_TCS << 28) | SPC5_GTM_TIM5_CH0_TOV;

	TIMD6.channel[0].cnt_reg = SPC5_GTM_TIM5_CH0_CTRL;
	TIMD6.channel[0].flt_fe_reg = SPC5_GTM_TIM5_CH0_FLT_FE;
	TIMD6.channel[0].flt_re_reg = SPC5_GTM_TIM5_CH0_FLT_RE;
	TIMD6.channel[0].cnt_reg = SPC5_GTM_TIM5_CH0_CNT;
	TIMD6.channel[0].ectrl_reg = SPC5_GTM_TIM5_CH0_ECTRL;


#if (SPC5_GTM_TIM5_CHANNEL0_INT == TRUE)
	gtm_timSetIRQMode(&TIMD6, TIM_CHANNEL0, SPC5_GTM_TIM5_CHANNEL0_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM5_0_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM5_0_INT_PRIORITY);

#if (SPC5_GTM_TIM5_CHANNEL0_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL0_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL0_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL0_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL0_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL0_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL0, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL0_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM5_CHANNEL0_INT */

#endif /* SPC5_GTM_TIM5_CH0_ENABLE */


#if (SPC5_GTM_TIM5_CH1_ENABLE == TRUE)
	TIMD6.tim->GTM_CH_REG(1,CTRL).R = SPC5_GTM_TIM5_CH1_CTRL;
	TIMD6.tim->GTM_CH_REG(1,FLT_FE).R = SPC5_GTM_TIM5_CH1_FLT_FE;
	TIMD6.tim->GTM_CH_REG(1,FLT_RE).R = SPC5_GTM_TIM5_CH1_FLT_RE;
	TIMD6.tim->GTM_CH_REG(1,CNT).R = SPC5_GTM_TIM5_CH1_CNT;
    if (TIMD6.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD6.tim->GTM_CH_REG(1,CNTS).R = SPC5_GTM_TIM5_CH1_CNTS;
		TIMD6.channel[1].cnts_reg = SPC5_GTM_TIM5_CH1_CNTS;
	}
    if (TIMD6.tim->GTM_CH_REG(1,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD6.tim->GTM_CH_REG(1,GPR1).R = SPC5_GTM_TIM5_CH1_GPR1;
		TIMD6.channel[1].gpr1_reg = SPC5_GTM_TIM5_CH1_GPR1;
    }
	TIMD6.tim->GTM_CH_REG(1,ECTRL).R = SPC5_GTM_TIM5_CH1_ECTRL;

	/* Timeout detection */
	TIMD6.tim->GTM_CH_REG(1,TDUV).R = (SPC5_GTM_TIM5_CH1_TCS << 28) | SPC5_GTM_TIM5_CH1_TOV;

	TIMD6.channel[1].cnt_reg = SPC5_GTM_TIM5_CH1_CTRL;
	TIMD6.channel[1].flt_fe_reg = SPC5_GTM_TIM5_CH1_FLT_FE;
	TIMD6.channel[1].flt_re_reg = SPC5_GTM_TIM5_CH1_FLT_RE;
	TIMD6.channel[1].cnt_reg = SPC5_GTM_TIM5_CH1_CNT;
	TIMD6.channel[1].ectrl_reg = SPC5_GTM_TIM5_CH1_ECTRL;


#if (SPC5_GTM_TIM5_CHANNEL1_INT == TRUE)
	gtm_timSetIRQMode(&TIMD6, TIM_CHANNEL1, SPC5_GTM_TIM5_CHANNEL1_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM5_1_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM5_1_INT_PRIORITY);

#if (SPC5_GTM_TIM5_CHANNEL1_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL1_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL1_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL1_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL1_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL1_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL1, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL1_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM5_CHANNEL1_INT */

#endif /* SPC5_GTM_TIM5_CH1_ENABLE */


#if (SPC5_GTM_TIM5_CH2_ENABLE == TRUE)
	TIMD6.tim->GTM_CH_REG(2,CTRL).R = SPC5_GTM_TIM5_CH2_CTRL;
	TIMD6.tim->GTM_CH_REG(2,FLT_FE).R = SPC5_GTM_TIM5_CH2_FLT_FE;
	TIMD6.tim->GTM_CH_REG(2,FLT_RE).R = SPC5_GTM_TIM5_CH2_FLT_RE;
	TIMD6.tim->GTM_CH_REG(2,CNT).R = SPC5_GTM_TIM5_CH2_CNT;
	if (TIMD6.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD6.tim->GTM_CH_REG(2,CNTS).R = SPC5_GTM_TIM5_CH2_CNTS;
		TIMD6.channel[2].cnts_reg = SPC5_GTM_TIM5_CH2_CNTS;
	}
	if (TIMD6.tim->GTM_CH_REG(2,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD6.tim->GTM_CH_REG(2,GPR1).R = SPC5_GTM_TIM5_CH2_GPR1;
		TIMD6.channel[2].gpr1_reg = SPC5_GTM_TIM5_CH2_GPR1;
	}
	TIMD6.tim->GTM_CH_REG(2,ECTRL).R = SPC5_GTM_TIM5_CH2_ECTRL;

	/* Timeout detection */
	TIMD6.tim->GTM_CH_REG(2,TDUV).R = (SPC5_GTM_TIM5_CH2_TCS << 28) | SPC5_GTM_TIM5_CH2_TOV;

	TIMD6.channel[2].cnt_reg = SPC5_GTM_TIM5_CH2_CTRL;
	TIMD6.channel[2].flt_fe_reg = SPC5_GTM_TIM5_CH2_FLT_FE;
	TIMD6.channel[2].flt_re_reg = SPC5_GTM_TIM5_CH2_FLT_RE;
	TIMD6.channel[2].cnt_reg = SPC5_GTM_TIM5_CH2_CNT;
	TIMD6.channel[2].ectrl_reg = SPC5_GTM_TIM5_CH2_ECTRL;


#if (SPC5_GTM_TIM5_CHANNEL2_INT == TRUE)
	gtm_timSetIRQMode(&TIMD6, TIM_CHANNEL2, SPC5_GTM_TIM5_CHANNEL2_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM5_2_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM5_2_INT_PRIORITY);

#if (SPC5_GTM_TIM5_CHANNEL2_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL2_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL2_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL2_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL2_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL2_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL2, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL2_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM5_CHANNEL2_INT */

#endif /* SPC5_GTM_TIM5_CH2_ENABLE */


#if (SPC5_GTM_TIM5_CH3_ENABLE == TRUE)
	TIMD6.tim->GTM_CH_REG(3,CTRL).R = SPC5_GTM_TIM5_CH3_CTRL;
	TIMD6.tim->GTM_CH_REG(3,FLT_FE).R = SPC5_GTM_TIM5_CH3_FLT_FE;
	TIMD6.tim->GTM_CH_REG(3,FLT_RE).R = SPC5_GTM_TIM5_CH3_FLT_RE;
	TIMD6.tim->GTM_CH_REG(3,CNT).R = SPC5_GTM_TIM5_CH3_CNT;
	if (TIMD6.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD6.tim->GTM_CH_REG(3,CNTS).R = SPC5_GTM_TIM5_CH3_CNTS;
		TIMD6.channel[3].cnts_reg = SPC5_GTM_TIM5_CH3_CNTS;
	}
	if (TIMD6.tim->GTM_CH_REG(3,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD6.tim->GTM_CH_REG(3,GPR1).R = SPC5_GTM_TIM5_CH3_GPR1;
		TIMD6.channel[3].gpr1_reg = SPC5_GTM_TIM5_CH3_GPR1;
	}
	TIMD6.tim->GTM_CH_REG(3,ECTRL).R = SPC5_GTM_TIM5_CH3_ECTRL;

	/* Timeout detection */
	TIMD6.tim->GTM_CH_REG(3,TDUV).R = (SPC5_GTM_TIM5_CH3_TCS << 28) | SPC5_GTM_TIM5_CH3_TOV;

	TIMD6.channel[3].cnt_reg = SPC5_GTM_TIM5_CH3_CTRL;
	TIMD6.channel[3].flt_fe_reg = SPC5_GTM_TIM5_CH3_FLT_FE;
	TIMD6.channel[3].flt_re_reg = SPC5_GTM_TIM5_CH3_FLT_RE;
	TIMD6.channel[3].cnt_reg = SPC5_GTM_TIM5_CH3_CNT;
	TIMD6.channel[3].ectrl_reg = SPC5_GTM_TIM5_CH3_ECTRL;


#if (SPC5_GTM_TIM5_CHANNEL3_INT == TRUE)
	gtm_timSetIRQMode(&TIMD6, TIM_CHANNEL3, SPC5_GTM_TIM5_CHANNEL3_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM5_3_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM5_3_INT_PRIORITY);

#if (SPC5_GTM_TIM5_CHANNEL3_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL3_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL3_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL3_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL3_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL3_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL3, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL3_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM5_CHANNEL3_INT */

#endif /* SPC5_GTM_TIM5_CH3_ENABLE */


#if (SPC5_GTM_TIM5_CH4_ENABLE == TRUE)
	TIMD6.tim->GTM_CH_REG(4,CTRL).R = SPC5_GTM_TIM5_CH4_CTRL;
	TIMD6.tim->GTM_CH_REG(4,FLT_FE).R = SPC5_GTM_TIM5_CH4_FLT_FE;
	TIMD6.tim->GTM_CH_REG(4,FLT_RE).R = SPC5_GTM_TIM5_CH4_FLT_RE;
	TIMD6.tim->GTM_CH_REG(4,CNT).R = SPC5_GTM_TIM5_CH4_CNT;
	if (TIMD6.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE & 5) { /* (TGPS | TIPM | TBCM) MODE */
		TIMD6.tim->GTM_CH_REG(4,CNTS).R = SPC5_GTM_TIM5_CH4_CNTS;
		TIMD6.channel[4].cnts_reg = SPC5_GTM_TIM5_CH4_CNTS;
	}
	if (TIMD6.tim->GTM_CH_REG(4,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD6.tim->GTM_CH_REG(4,GPR1).R = SPC5_GTM_TIM5_CH4_GPR1;
		TIMD6.channel[4].gpr1_reg = SPC5_GTM_TIM5_CH4_GPR1;
	}
	TIMD6.tim->GTM_CH_REG(4,ECTRL).R = SPC5_GTM_TIM5_CH4_ECTRL;

	/* Timeout detection */
	TIMD6.tim->GTM_CH_REG(4,TDUV).R = (SPC5_GTM_TIM5_CH4_TCS << 28) | SPC5_GTM_TIM5_CH4_TOV;

	TIMD6.channel[4].cnt_reg = SPC5_GTM_TIM5_CH4_CTRL;
	TIMD6.channel[4].flt_fe_reg = SPC5_GTM_TIM5_CH4_FLT_FE;
	TIMD6.channel[4].flt_re_reg = SPC5_GTM_TIM5_CH4_FLT_RE;
	TIMD6.channel[4].cnt_reg = SPC5_GTM_TIM5_CH4_CNT;
	TIMD6.channel[4].ectrl_reg = SPC5_GTM_TIM5_CH4_ECTRL;


#if (SPC5_GTM_TIM5_CHANNEL4_INT == TRUE)
	gtm_timSetIRQMode(&TIMD6, TIM_CHANNEL4, SPC5_GTM_TIM5_CHANNEL4_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM5_4_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM5_4_INT_PRIORITY);

#if (SPC5_GTM_TIM5_CHANNEL4_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL4_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL4_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL4_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL4_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL4_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL4, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL4_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM5_CHANNEL4_INT */

#endif /* SPC5_GTM_TIM5_CH4_ENABLE */


#if (SPC5_GTM_TIM5_CH5_ENABLE == TRUE)
	TIMD6.tim->GTM_CH_REG(5,CTRL).R = SPC5_GTM_TIM5_CH5_CTRL;
	TIMD6.tim->GTM_CH_REG(5,FLT_FE).R = SPC5_GTM_TIM5_CH5_FLT_FE;
	TIMD6.tim->GTM_CH_REG(5,FLT_RE).R = SPC5_GTM_TIM5_CH5_FLT_RE;
	TIMD6.tim->GTM_CH_REG(5,CNT).R = SPC5_GTM_TIM5_CH5_CNT;
	if (TIMD6.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD6.tim->GTM_CH_REG(5,CNTS).R = SPC5_GTM_TIM5_CH5_CNTS;
		TIMD6.channel[5].cnts_reg = SPC5_GTM_TIM5_CH5_CNTS;
	}
	if (TIMD6.tim->GTM_CH_REG(5,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD6.tim->GTM_CH_REG(5,GPR1).R = SPC5_GTM_TIM5_CH5_GPR1;
		TIMD6.channel[5].gpr1_reg = SPC5_GTM_TIM5_CH5_GPR1;
	}
	TIMD6.tim->GTM_CH_REG(5,ECTRL).R = SPC5_GTM_TIM5_CH5_ECTRL;

	/* Timeout detection */
	TIMD6.tim->GTM_CH_REG(5,TDUV).R = (SPC5_GTM_TIM5_CH5_TCS << 28) | SPC5_GTM_TIM5_CH5_TOV;

	TIMD6.channel[5].cnt_reg = SPC5_GTM_TIM5_CH5_CTRL;
	TIMD6.channel[5].flt_fe_reg = SPC5_GTM_TIM5_CH5_FLT_FE;
	TIMD6.channel[5].flt_re_reg = SPC5_GTM_TIM5_CH5_FLT_RE;
	TIMD6.channel[5].cnt_reg = SPC5_GTM_TIM5_CH5_CNT;
	TIMD6.channel[5].ectrl_reg = SPC5_GTM_TIM5_CH5_ECTRL;


#if (SPC5_GTM_TIM5_CHANNEL5_INT == TRUE)
	gtm_timSetIRQMode(&TIMD6, TIM_CHANNEL5, SPC5_GTM_TIM5_CHANNEL5_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM5_5_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM5_5_INT_PRIORITY);

#if (SPC5_GTM_TIM5_CHANNEL5_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL5_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL5_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL5_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL5_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL5_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL5, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL5_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM5_CHANNEL5_INT */

#endif /* SPC5_GTM_TIM5_CH5_ENABLE */


#if (SPC5_GTM_TIM5_CH6_ENABLE == TRUE)
	TIMD6.tim->GTM_CH_REG(6,CTRL).R = SPC5_GTM_TIM5_CH6_CTRL;
	TIMD6.tim->GTM_CH_REG(6,FLT_FE).R = SPC5_GTM_TIM5_CH6_FLT_FE;
	TIMD6.tim->GTM_CH_REG(6,FLT_RE).R = SPC5_GTM_TIM5_CH6_FLT_RE;
	TIMD6.tim->GTM_CH_REG(6,CNT).R = SPC5_GTM_TIM5_CH6_CNT;
	if (TIMD6.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD6.tim->GTM_CH_REG(6,CNTS).R = SPC5_GTM_TIM5_CH6_CNTS;
		TIMD6.channel[6].cnts_reg = SPC5_GTM_TIM5_CH6_CNTS;
	}
	if (TIMD6.tim->GTM_CH_REG(6,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD6.tim->GTM_CH_REG(6,GPR1).R = SPC5_GTM_TIM5_CH6_GPR1;
		TIMD6.channel[6].gpr1_reg = SPC5_GTM_TIM5_CH6_GPR1;
	}
	TIMD6.tim->GTM_CH_REG(6,ECTRL).R = SPC5_GTM_TIM5_CH6_ECTRL;

	/* Timeout detection */
	TIMD6.tim->GTM_CH_REG(6,TDUV).R = (SPC5_GTM_TIM5_CH6_TCS << 28) | SPC5_GTM_TIM5_CH6_TOV;

	TIMD6.channel[6].cnt_reg = SPC5_GTM_TIM5_CH6_CTRL;
	TIMD6.channel[6].flt_fe_reg = SPC5_GTM_TIM5_CH6_FLT_FE;
	TIMD6.channel[6].flt_re_reg = SPC5_GTM_TIM5_CH6_FLT_RE;
	TIMD6.channel[6].cnt_reg = SPC5_GTM_TIM5_CH6_CNT;
	TIMD6.channel[6].ectrl_reg = SPC5_GTM_TIM5_CH6_ECTRL;


#if (SPC5_GTM_TIM5_CHANNEL6_INT == TRUE)
	gtm_timSetIRQMode(&TIMD6, TIM_CHANNEL6, SPC5_GTM_TIM5_CHANNEL6_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM5_6_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM5_6_INT_PRIORITY);

#if (SPC5_GTM_TIM5_CHANNEL6_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL6_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL6_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL6_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL6_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL6_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL6, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL6_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM5_CHANNEL6_INT */

#endif /* SPC5_GTM_TIM5_CH6_ENABLE */


#if (SPC5_GTM_TIM5_CH7_ENABLE == TRUE)
	TIMD6.tim->GTM_CH_REG(7,CTRL).R = SPC5_GTM_TIM5_CH7_CTRL;
	TIMD6.tim->GTM_CH_REG(7,FLT_FE).R = SPC5_GTM_TIM5_CH7_FLT_FE;
	TIMD6.tim->GTM_CH_REG(7,FLT_RE).R = SPC5_GTM_TIM5_CH7_FLT_RE;
	TIMD6.tim->GTM_CH_REG(7,CNT).R = SPC5_GTM_TIM5_CH7_CNT;
	if (TIMD6.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE & 5){ /* (TGPS | TIPM | TBCM) MODE */
		TIMD6.tim->GTM_CH_REG(7,CNTS).R = SPC5_GTM_TIM5_CH7_CNTS;
		TIMD6.channel[7].cnts_reg = SPC5_GTM_TIM5_CH7_CNTS;
	}
	if (TIMD6.tim->GTM_CH_REG(7,CTRL).B.TIM_MODE == 5) { /* TGPS MODE */
		TIMD6.tim->GTM_CH_REG(7,GPR1).R = SPC5_GTM_TIM5_CH7_GPR1;
		TIMD6.channel[7].gpr1_reg = SPC5_GTM_TIM5_CH7_GPR1;
	}
	TIMD6.tim->GTM_CH_REG(7,ECTRL).R = SPC5_GTM_TIM5_CH7_ECTRL;

	/* Timeout detection */
	TIMD6.tim->GTM_CH_REG(7,TDUV).R = (SPC5_GTM_TIM5_CH7_TCS << 28) | SPC5_GTM_TIM5_CH7_TOV;

	TIMD6.channel[7].cnt_reg = SPC5_GTM_TIM5_CH7_CTRL;
	TIMD6.channel[7].flt_fe_reg = SPC5_GTM_TIM5_CH7_FLT_FE;
	TIMD6.channel[7].flt_re_reg = SPC5_GTM_TIM5_CH7_FLT_RE;
	TIMD6.channel[7].cnt_reg = SPC5_GTM_TIM5_CH7_CNT;
	TIMD6.channel[7].ectrl_reg = SPC5_GTM_TIM5_CH7_ECTRL;


#if (SPC5_GTM_TIM5_CHANNEL7_INT == TRUE)
	gtm_timSetIRQMode(&TIMD6, TIM_CHANNEL7, SPC5_GTM_TIM5_CHANNEL7_IRQ_MODE);
	//MC, INTC_PSR(SPC5_TIM5_7_INT_NUMBER) = INTC_PSR_ENABLE(INTC_PSR_MAINCORE, SPC5_TIM5_7_INT_PRIORITY);

#if (SPC5_GTM_TIM5_CHANNEL7_INT_NEW_VALUE_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_NEW_VALUE);
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_NEW_VALUE_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL7_INT_GPRS_DATA_OVERFLOW_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GPRS_DATA_OVERFLOW);
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_GPRS_DATA_OVERFLOW_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL7_INT_TIMEOUT_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_TIMEOUT);
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_TIMEOUT_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL7_INT_SMU_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_SMU_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_SMU_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL7_INT_EDGE_COUNTER_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_EDGE_COUNTER);
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_EDGE_COUNTER_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#if (SPC5_GTM_TIM5_CHANNEL7_INT_GLITCH_ENABLED == TRUE)
#if (SPC5_GTM_TIM5_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL)
	gtm_timEnableInt(&TIMD6, TIM_CHANNEL7, SPC5_GTM_TIM_IRQ_ENABLE_GLITCH);
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_ERROR)
#elif (SPC5_GTM_TIM5_CHANNEL7_INT_GLITCH_MODE == SPC5_GTM_TIM_INT_MODE_NORMAL_AND_ERROR)
#endif
#endif

#endif /* SPC5_GTM_TIM5_CHANNEL7_INT */
#endif /* SPC5_GTM_TIM5_CH7_ENABLE */
#endif /* SPC5_GTM_USE_TIM5 */
}

/**
 * @brief   Start TIM channel
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @api
 */
void gtm_timStart(GTM_TIMDriver *timd, uint8_t channel){

	if(timd != NULL) {
		switch (channel) {
		case 0:
			timd->tim->GTM_CH_REG(0,CTRL).R |= (ENABLE_FIELD << CH_ENABLE);
			break;
		case 1:
			timd->tim->GTM_CH_REG(1,CTRL).R |= (ENABLE_FIELD << CH_ENABLE);
			break;
		case 2:
			timd->tim->GTM_CH_REG(2,CTRL).R |= (ENABLE_FIELD << CH_ENABLE);
			break;
		case 3:
			timd->tim->GTM_CH_REG(3,CTRL).R |= (ENABLE_FIELD << CH_ENABLE);
			break;
		case 4:
			timd->tim->GTM_CH_REG(4,CTRL).R |= (ENABLE_FIELD << CH_ENABLE);
			break;
		case 5:
			timd->tim->GTM_CH_REG(5,CTRL).R |= (ENABLE_FIELD << CH_ENABLE);
			break;
		case 6:
			timd->tim->GTM_CH_REG(6,CTRL).R |= (ENABLE_FIELD << CH_ENABLE);
			break;
		case 7:
			timd->tim->GTM_CH_REG(7,CTRL).R |= (ENABLE_FIELD << CH_ENABLE);
			break;
		default:
			/* MISRA check */
			break;
		}
		timd->channel[channel].ctrl_reg |= 1UL;
	}
}

/**
 * @brief   Stop TIM channel
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @api
 */
void gtm_timStop(GTM_TIMDriver *timd, uint8_t channel){
	if(timd != NULL) {
		switch (channel) {
		case 0:
			timd->tim->GTM_CH_REG(0,CTRL).R &= 0xFFFFFFFEUL;
			break;
		case 1:
			timd->tim->GTM_CH_REG(1,CTRL).R &= 0xFFFFFFFEUL;
			break;
		case 2:
			timd->tim->GTM_CH_REG(2,CTRL).R &= 0xFFFFFFFEUL;
			break;
		case 3:
			timd->tim->GTM_CH_REG(3,CTRL).R &= 0xFFFFFFFEUL;
			break;
		case 4:
			timd->tim->GTM_CH_REG(4,CTRL).R &= 0xFFFFFFFEUL;
			break;
		case 5:
			timd->tim->GTM_CH_REG(5,CTRL).R &= 0xFFFFFFFEUL;
			break;
		case 6:
			timd->tim->GTM_CH_REG(6,CTRL).R &= 0xFFFFFFFEUL;
			break;
		case 7:
			timd->tim->GTM_CH_REG(7,CTRL).R &= 0xFFFFFFFEUL;
			break;
		default:
			/* MISRA check */
			break;
		}
		timd->channel[channel].ctrl_reg &= 0xFFFFFFFEUL;
	}
}

/**
 * @brief   Set a TIM channel configuration
 *
 *  @param[in] timd        GTM TIM driver pointer
 *
 *  @param[in] channel     GTM TIM channel number
 *
 *  @param[in] channel_cfg GTM TIM channel configuration
 *
 * @api
 */
void gtm_timSetCfg(GTM_TIMDriver *timd, uint8_t channel, TIM_Channel_Cfg channel_cfg) {
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,CNT).R = channel_cfg.cnt_reg;
		timd->tim->GTM_CH_REG(0,CNTS).R = channel_cfg.cnts_reg;
		timd->tim->GTM_CH_REG(0,CTRL).R = channel_cfg.ctrl_reg;
		timd->tim->GTM_CH_REG(0,ECNT).R = channel_cfg.ecnt_reg;
		timd->tim->GTM_CH_REG(0,ECTRL).R = channel_cfg.ectrl_reg;
		timd->tim->GTM_CH_REG(0,FLT_FE).R = channel_cfg.flt_fe_reg;
		timd->tim->GTM_CH_REG(0,FLT_RE).R = channel_cfg.flt_re_reg;
		timd->tim->GTM_CH_REG(0,GPR1).R = channel_cfg.gpr1_reg;
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,CNT).R = channel_cfg.cnt_reg;
		timd->tim->GTM_CH_REG(1,CNTS).R = channel_cfg.cnts_reg;
		timd->tim->GTM_CH_REG(1,CTRL).R = channel_cfg.ctrl_reg;
		timd->tim->GTM_CH_REG(1,ECNT).R = channel_cfg.ecnt_reg;
		timd->tim->GTM_CH_REG(1,ECTRL).R = channel_cfg.ectrl_reg;
		timd->tim->GTM_CH_REG(1,FLT_FE).R = channel_cfg.flt_fe_reg;
		timd->tim->GTM_CH_REG(1,FLT_RE).R = channel_cfg.flt_re_reg;
		timd->tim->GTM_CH_REG(1,GPR1).R = channel_cfg.gpr1_reg;
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,CNT).R = channel_cfg.cnt_reg;
		timd->tim->GTM_CH_REG(2,CNTS).R = channel_cfg.cnts_reg;
		timd->tim->GTM_CH_REG(2,CTRL).R = channel_cfg.ctrl_reg;
		timd->tim->GTM_CH_REG(2,ECNT).R = channel_cfg.ecnt_reg;
		timd->tim->GTM_CH_REG(2,ECTRL).R = channel_cfg.ectrl_reg;
		timd->tim->GTM_CH_REG(2,FLT_FE).R = channel_cfg.flt_fe_reg;
		timd->tim->GTM_CH_REG(2,FLT_RE).R = channel_cfg.flt_re_reg;
		timd->tim->GTM_CH_REG(2,GPR1).R = channel_cfg.gpr1_reg;
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,CNT).R = channel_cfg.cnt_reg;
		timd->tim->GTM_CH_REG(3,CNTS).R = channel_cfg.cnts_reg;
		timd->tim->GTM_CH_REG(3,CTRL).R = channel_cfg.ctrl_reg;
		timd->tim->GTM_CH_REG(3,ECNT).R = channel_cfg.ecnt_reg;
		timd->tim->GTM_CH_REG(3,ECTRL).R = channel_cfg.ectrl_reg;
		timd->tim->GTM_CH_REG(3,FLT_FE).R = channel_cfg.flt_fe_reg;
		timd->tim->GTM_CH_REG(3,FLT_RE).R = channel_cfg.flt_re_reg;
		timd->tim->GTM_CH_REG(3,GPR1).R = channel_cfg.gpr1_reg;
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,CNT).R = channel_cfg.cnt_reg;
		timd->tim->GTM_CH_REG(4,CNTS).R = channel_cfg.cnts_reg;
		timd->tim->GTM_CH_REG(4,CTRL).R = channel_cfg.ctrl_reg;
		timd->tim->GTM_CH_REG(4,ECNT).R = channel_cfg.ecnt_reg;
		timd->tim->GTM_CH_REG(4,ECTRL).R = channel_cfg.ectrl_reg;
		timd->tim->GTM_CH_REG(4,FLT_FE).R = channel_cfg.flt_fe_reg;
		timd->tim->GTM_CH_REG(4,FLT_RE).R = channel_cfg.flt_re_reg;
		timd->tim->GTM_CH_REG(4,GPR1).R = channel_cfg.gpr1_reg;
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,CNT).R = channel_cfg.cnt_reg;
		timd->tim->GTM_CH_REG(5,CNTS).R = channel_cfg.cnts_reg;
		timd->tim->GTM_CH_REG(5,CTRL).R = channel_cfg.ctrl_reg;
		timd->tim->GTM_CH_REG(5,ECNT).R = channel_cfg.ecnt_reg;
		timd->tim->GTM_CH_REG(5,ECTRL).R = channel_cfg.ectrl_reg;
		timd->tim->GTM_CH_REG(5,FLT_FE).R = channel_cfg.flt_fe_reg;
		timd->tim->GTM_CH_REG(5,FLT_RE).R = channel_cfg.flt_re_reg;
		timd->tim->GTM_CH_REG(5,GPR1).R = channel_cfg.gpr1_reg;
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,CNT).R = channel_cfg.cnt_reg;
		timd->tim->GTM_CH_REG(6,CNTS).R = channel_cfg.cnts_reg;
		timd->tim->GTM_CH_REG(6,CTRL).R = channel_cfg.ctrl_reg;
		timd->tim->GTM_CH_REG(6,ECNT).R = channel_cfg.ecnt_reg;
		timd->tim->GTM_CH_REG(6,ECTRL).R = channel_cfg.ectrl_reg;
		timd->tim->GTM_CH_REG(6,FLT_FE).R = channel_cfg.flt_fe_reg;
		timd->tim->GTM_CH_REG(6,FLT_RE).R = channel_cfg.flt_re_reg;
		timd->tim->GTM_CH_REG(6,GPR1).R = channel_cfg.gpr1_reg;
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,CNT).R = channel_cfg.cnt_reg;
		timd->tim->GTM_CH_REG(7,CNTS).R = channel_cfg.cnts_reg;
		timd->tim->GTM_CH_REG(7,CTRL).R = channel_cfg.ctrl_reg;
		timd->tim->GTM_CH_REG(7,ECNT).R = channel_cfg.ecnt_reg;
		timd->tim->GTM_CH_REG(7,ECTRL).R = channel_cfg.ectrl_reg;
		timd->tim->GTM_CH_REG(7,FLT_FE).R = channel_cfg.flt_fe_reg;
		timd->tim->GTM_CH_REG(7,FLT_RE).R = channel_cfg.flt_re_reg;
		timd->tim->GTM_CH_REG(7,GPR1).R = channel_cfg.gpr1_reg;
		break;
	default:
		/* MISRA check */
		break;
	}

	timd->channel[channel].cnt_reg = channel_cfg.cnt_reg;
	timd->channel[channel].cnts_reg = channel_cfg.cnts_reg;
	timd->channel[channel].ctrl_reg = channel_cfg.ctrl_reg;
	timd->channel[channel].ecnt_reg = channel_cfg.ecnt_reg;
	timd->channel[channel].ectrl_reg = channel_cfg.ectrl_reg;
	timd->channel[channel].flt_fe_reg = channel_cfg.flt_fe_reg;
	timd->channel[channel].flt_re_reg = channel_cfg.flt_re_reg;
	timd->channel[channel].gpr1_reg = channel_cfg.gpr1_reg;
}

/**
 * @brief   Get a TIM channel configuration
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @return TIM_Channel_Cfg Channel configuration
 * @api
 */
TIM_Channel_Cfg gtm_timGetCfg(GTM_TIMDriver *timd, uint8_t channel) {
	TIM_Channel_Cfg local_cfg;

	local_cfg.cnt_reg = timd->channel[channel].cnt_reg;
	local_cfg.cnts_reg = timd->channel[channel].cnts_reg;
	local_cfg.ctrl_reg = timd->channel[channel].ctrl_reg;
	local_cfg.ecnt_reg = timd->channel[channel].ecnt_reg;
	local_cfg.ectrl_reg = timd->channel[channel].ecnt_reg;
	local_cfg.flt_fe_reg = timd->channel[channel].flt_fe_reg;
	local_cfg.flt_re_reg = timd->channel[channel].flt_re_reg;
	local_cfg.gpr0_reg = timd->channel[channel].gpr0_reg;
	local_cfg.gpr1_reg = timd->channel[channel].gpr1_reg;

	return local_cfg;
}

/**
 * @brief   Set TIM channel software reset
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @api
 */
void gtm_timSetSWreset(GTM_TIMDriver *timd, uint8_t channel) {
	timd->tim->GTM_TIM_GC_REG(RST).R |= (ENABLE_FIELD << channel);
	timd->sw_reset_reg |= (ENABLE_FIELD << channel);
}

/**
 * @brief   Set TIM channel AUX IN source
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] mode       GTM TIM channel AUX_IN mode
 *
 * @param[in] val        GTM TIM channel AUX_IN value
 *
 * @api
 */
void gtm_timSetAUX_IN(GTM_TIMDriver *timd, uint8_t channel, uint32_t mode, uint32_t val) {
	timd->aux_in_src_reg |= ((mode << (IN_SRC_CH_MODE_OFFSET + (channel * IN_SRC_CH_BASE))) + (val << (channel * IN_SRC_CH_BASE)));
	timd->tim->GTM_TIM_GC_REG(IN_SRC).R |= ((mode << (IN_SRC_CH_MODE_OFFSET + (channel * IN_SRC_CH_BASE))) + (val << (channel * IN_SRC_CH_BASE)));
}

/**
 * @brief   Set TIM Channel Timeout Mode
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] mode        GTM TIM channel mode
 *
 * @sa
 * SPC5_GTM_TIM_CH_TOCTRL_DISABLED, SPC5_GTM_TIM_CH_TOCTRL_RISING_EDGE_ONLY,
 * SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY, SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH
 *
 * @api
 */
void gtm_timSetTimeoutMode(GTM_TIMDriver *timd, uint8_t channel, uint8_t mode) {
	uint32_t value;

	mode &= 0x03U;

	switch(channel) {
	case 0:
		value = timd->tim->GTM_CH_REG(0,CTRL).R;
		timd->tim->GTM_CH_REG(0,CTRL).R = (value & 0x3FFFFFFFUL) | ((uint32_t)mode << CH_TOCTRL);
		break;
	case 1:
		value = timd->tim->GTM_CH_REG(1,CTRL).R;
		timd->tim->GTM_CH_REG(1,CTRL).R = (value & 0x3FFFFFFFUL) | ((uint32_t)mode << CH_TOCTRL);
		break;
	case 2:
		value = timd->tim->GTM_CH_REG(2,CTRL).R;
		timd->tim->GTM_CH_REG(2,CTRL).R = (value & 0x3FFFFFFFUL) | ((uint32_t)mode << CH_TOCTRL);
		break;
	case 3:
		value = timd->tim->GTM_CH_REG(3,CTRL).R;
		timd->tim->GTM_CH_REG(3,CTRL).R = (value & 0x3FFFFFFFUL) | ((uint32_t)mode << CH_TOCTRL);
		break;
	case 4:
		value = timd->tim->GTM_CH_REG(4,CTRL).R;
		timd->tim->GTM_CH_REG(4,CTRL).R = (value & 0x3FFFFFFFUL) | ((uint32_t)mode << CH_TOCTRL);
		break;
	case 5:
		value = timd->tim->GTM_CH_REG(5,CTRL).R;
		timd->tim->GTM_CH_REG(5,CTRL).R = (value & 0x3FFFFFFFUL) | ((uint32_t)mode << CH_TOCTRL);
		break;
	case 6:
		value = timd->tim->GTM_CH_REG(6,CTRL).R;
		timd->tim->GTM_CH_REG(6,CTRL).R = (value & 0x3FFFFFFFUL) | ((uint32_t)mode << CH_TOCTRL);
		break;
	case 7:
		value = timd->tim->GTM_CH_REG(7,CTRL).R;
		timd->tim->GTM_CH_REG(7,CTRL).R = (value & 0x3FFFFFFFUL) | ((uint32_t)mode << CH_TOCTRL);
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Get TIM Channel Timeout Mode
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @return Timeout mode
 *
 * @sa
 * SPC5_GTM_TIM_CH_TOCTRL_DISABLED, SPC5_GTM_TIM_CH_TOCTRL_RISING_EDGE_ONLY,
 * SPC5_GTM_TIM_CH_TOCTRL_FALLING_EDGE_ONLY, SPC5_GTM_TIM_CH_TOCTRL_ENABLED_BOTH
 *
 * @api
 */
uint8_t gtm_timGetTimeoutMode(GTM_TIMDriver *timd, uint8_t channel) {
	uint32_t mode;

	switch(channel) {
	case 0:
		mode = timd->tim->GTM_CH_REG(0,CTRL).R;
		break;
	case 1:
		mode = timd->tim->GTM_CH_REG(1,CTRL).R;
		break;
	case 2:
		mode = timd->tim->GTM_CH_REG(2,CTRL).R;
		break;
	case 3:
		mode = timd->tim->GTM_CH_REG(3,CTRL).R;
		break;
	case 4:
		mode = timd->tim->GTM_CH_REG(4,CTRL).R;
		break;
	case 5:
		mode = timd->tim->GTM_CH_REG(5,CTRL).R;
		break;
	case 6:
		mode = timd->tim->GTM_CH_REG(6,CTRL).R;
		break;
	case 7:
		mode = timd->tim->GTM_CH_REG(7,CTRL).R;
		break;
	default:
		mode = 0;
		break;
	}

	return (uint8_t)((mode >> CH_TOCTRL) & 0x03U);
}

/**
 * @brief   Set TIM Channel Timeout value
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] tov         GTM TIM channel timeout value
 *
 * @api
 */
void gtm_timSetTimeoutValue(GTM_TIMDriver *timd, uint8_t channel, uint8_t tov) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = timd->tim->GTM_CH_REG(0,TDUV).R;
		timd->tim->GTM_CH_REG(0,TDUV).R = (value & 0xF0000000UL) | tov;
		break;
	case 1:
		value = timd->tim->GTM_CH_REG(1,TDUV).R;
		timd->tim->GTM_CH_REG(1,TDUV).R = (value & 0xF0000000UL) | tov;
		break;
	case 2:
		value = timd->tim->GTM_CH_REG(2,TDUV).R;
		timd->tim->GTM_CH_REG(2,TDUV).R = (value & 0xF0000000UL) | tov;
		break;
	case 3:
		value = timd->tim->GTM_CH_REG(3,TDUV).R;
		timd->tim->GTM_CH_REG(3,TDUV).R = (value & 0xF0000000UL) | tov;
		break;
	case 4:
		value = timd->tim->GTM_CH_REG(4,TDUV).R;
		timd->tim->GTM_CH_REG(4,TDUV).R = (value & 0xF0000000UL) | tov;
		break;
	case 5:
		value = timd->tim->GTM_CH_REG(5,TDUV).R;
		timd->tim->GTM_CH_REG(5,TDUV).R = (value & 0xF0000000UL) | tov;
		break;
	case 6:
		value = timd->tim->GTM_CH_REG(6,TDUV).R;
		timd->tim->GTM_CH_REG(6,TDUV).R = (value & 0xF0000000UL) | tov;
		break;
	case 7:
		value = timd->tim->GTM_CH_REG(7,TDUV).R;
		timd->tim->GTM_CH_REG(7,TDUV).R = (value & 0xF0000000UL) | tov;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Get TIM Channel Timeout value
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @return Timeout value
 *
 * @api
 */
uint8_t gtm_timGetTimeoutValue(GTM_TIMDriver *timd, uint8_t channel) {
	uint32_t tov;

	switch(channel) {
	case 0:
		tov = timd->tim->GTM_CH_REG(0,TDUV).R;
		break;
	case 1:
		tov = timd->tim->GTM_CH_REG(1,TDUV).R;
		break;
	case 2:
		tov = timd->tim->GTM_CH_REG(2,TDUV).R;
		break;
	case 3:
		tov = timd->tim->GTM_CH_REG(3,TDUV).R;
		break;
	case 4:
		tov = timd->tim->GTM_CH_REG(4,TDUV).R;
		break;
	case 5:
		tov = timd->tim->GTM_CH_REG(5,TDUV).R;
		break;
	case 6:
		tov = timd->tim->GTM_CH_REG(6,TDUV).R;
		break;
	case 7:
		tov = timd->tim->GTM_CH_REG(7,TDUV).R;
		break;
	default:
		tov = 0;
		break;
	}

	return (uint8_t)(tov & 0xFFU);
}

/**
 * @brief   Set TIM Channel Timeout Clock
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] tcs         GTM TIM channel clock
 *
 * @sa
 * SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK1, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK2,
 * SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK3, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK4, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK5,
 * SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK6, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
 *
 * @api
 */
void gtm_timSetTimeoutClock(GTM_TIMDriver *timd, uint8_t channel, uint8_t tcs) {
	uint32_t value;

	tcs &= 0x07U;

	switch(channel) {
	case 0:
		value = timd->tim->GTM_CH_REG(0,TDUV).R;
		timd->tim->GTM_CH_REG(0,TDUV).R = (value & 0x000000FFUL) | ((uint32_t)tcs << 28);
		break;
	case 1:
		value = timd->tim->GTM_CH_REG(1,TDUV).R;
		timd->tim->GTM_CH_REG(1,TDUV).R = (value & 0x000000FFUL) | ((uint32_t)tcs << 28);
		break;
	case 2:
		value = timd->tim->GTM_CH_REG(2,TDUV).R;
		timd->tim->GTM_CH_REG(2,TDUV).R = (value & 0x000000FFUL) | ((uint32_t)tcs << 28);
		break;
	case 3:
		value = timd->tim->GTM_CH_REG(3,TDUV).R;
		timd->tim->GTM_CH_REG(3,TDUV).R = (value & 0x000000FFUL) | ((uint32_t)tcs << 28);
		break;
	case 4:
		value = timd->tim->GTM_CH_REG(4,TDUV).R;
		timd->tim->GTM_CH_REG(4,TDUV).R = (value & 0x000000FFUL) | ((uint32_t)tcs << 28);
		break;
	case 5:
		value = timd->tim->GTM_CH_REG(5,TDUV).R;
		timd->tim->GTM_CH_REG(5,TDUV).R = (value & 0x000000FFUL) | ((uint32_t)tcs << 28);
		break;
	case 6:
		value = timd->tim->GTM_CH_REG(6,TDUV).R;
		timd->tim->GTM_CH_REG(6,TDUV).R = (value & 0x000000FFUL) | ((uint32_t)tcs << 28);
		break;
	case 7:
		value = timd->tim->GTM_CH_REG(7,TDUV).R;
		timd->tim->GTM_CH_REG(7,TDUV).R = (value & 0x000000FFUL) | ((uint32_t)tcs << 28);
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Get TIM Channel Timeout Clock
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @return Clock identifier
 *
 * @sa
 * SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK0, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK1, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK2,
 * SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK3, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK4, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK5,
 * SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK6, SPC5_GTM_TIM_CH_CLK_SRC_CMU_CLK7
 *
 * @api
 */
uint8_t gtm_timGetTimeoutClock(GTM_TIMDriver *timd, uint8_t channel) {
	uint32_t tcs;

	switch(channel) {
	case 0:
		tcs = timd->tim->GTM_CH_REG(0,TDUV).R;
		break;
	case 1:
		tcs = timd->tim->GTM_CH_REG(1,TDUV).R;
		break;
	case 2:
		tcs = timd->tim->GTM_CH_REG(2,TDUV).R;
		break;
	case 3:
		tcs = timd->tim->GTM_CH_REG(3,TDUV).R;
		break;
	case 4:
		tcs = timd->tim->GTM_CH_REG(4,TDUV).R;
		break;
	case 5:
		tcs = timd->tim->GTM_CH_REG(5,TDUV).R;
		break;
	case 6:
		tcs = timd->tim->GTM_CH_REG(6,TDUV).R;
		break;
	case 7:
		tcs = timd->tim->GTM_CH_REG(7,TDUV).R;
		break;
	default:
		tcs = 0;
		break;
	}

	return (uint8_t)((tcs >> 28) & 0x0FU);
}

/**
 * @brief   Set TIM Channel IRQ mode
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] mode        GTM TIM channel IRQ mode
 *
 * @api
 */
void gtm_timSetIRQMode(GTM_TIMDriver *timd, uint8_t channel, uint8_t mode) {
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,IRQ_MODE).B.IRQ_MODE = mode;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Enable TIM Channel interrupt
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] int_num   GTM TIM interrupt to enable
 *
 * @api
 */
void gtm_timEnableInt(GTM_TIMDriver *timd, uint8_t channel, uint32_t int_num) {
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,IRQ_EN).R |= int_num;
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,IRQ_EN).R |= int_num;
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,IRQ_EN).R |= int_num;
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,IRQ_EN).R |= int_num;
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,IRQ_EN).R |= int_num;
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,IRQ_EN).R |= int_num;
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,IRQ_EN).R |= int_num;
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,IRQ_EN).R |= int_num;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Software notification of TIM Channel interrupt
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] int_num   GTM TIM interrupt to notify
 *
 * @api
 */
void gtm_timNotifyInt(GTM_TIMDriver *timd, uint8_t channel, uint8_t int_num) {
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,IRQ_FORCINT).R = int_num;
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,IRQ_FORCINT).R = int_num;
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,IRQ_FORCINT).R = int_num;
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,IRQ_FORCINT).R = int_num;
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,IRQ_FORCINT).R = int_num;
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,IRQ_FORCINT).R = int_num;
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,IRQ_FORCINT).R = int_num;
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,IRQ_FORCINT).R = int_num;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Acknowledge TIM Channel interrupt
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] int_num   GTM TIM interrupt to acknowledge
 *
 * @api
 */
void gtm_timAckInt(GTM_TIMDriver *timd, uint8_t channel, uint32_t int_num) {
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,IRQ_NOTIFY).R = int_num;
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,IRQ_NOTIFY).R = int_num;
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,IRQ_NOTIFY).R = int_num;
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,IRQ_NOTIFY).R = int_num;
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,IRQ_NOTIFY).R = int_num;
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,IRQ_NOTIFY).R = int_num;
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,IRQ_NOTIFY).R = int_num;
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,IRQ_NOTIFY).R = int_num;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Disable TIM Channel interrupt
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @param[in] int_num   GTM TIM interrupt to disable
 *
 * @api
 */
void gtm_timDisableInt(GTM_TIMDriver *timd, uint8_t channel, uint32_t int_num) {
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,IRQ_EN).R &= ~int_num;
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,IRQ_EN).R &= ~int_num;
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,IRQ_EN).R &= ~int_num;
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,IRQ_EN).R &= ~int_num;
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,IRQ_EN).R &= ~int_num;
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,IRQ_EN).R &= ~int_num;
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,IRQ_EN).R &= ~int_num;
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,IRQ_EN).R &= ~int_num;
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Return TIM Channel interrupt status
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @return Interrupt status
 *
 * @api
 */
uint32_t gtm_timGetIntStatus(GTM_TIMDriver *timd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = timd->tim->GTM_CH_REG(0,IRQ_NOTIFY).R;
		break;
	case 1:
		value = timd->tim->GTM_CH_REG(1,IRQ_NOTIFY).R;
		break;
	case 2:
		value = timd->tim->GTM_CH_REG(2,IRQ_NOTIFY).R;
		break;
	case 3:
		value = timd->tim->GTM_CH_REG(3,IRQ_NOTIFY).R;
		break;
	case 4:
		value = timd->tim->GTM_CH_REG(4,IRQ_NOTIFY).R;
		break;
	case 5:
		value = timd->tim->GTM_CH_REG(5,IRQ_NOTIFY).R;
		break;
	case 6:
		value = timd->tim->GTM_CH_REG(6,IRQ_NOTIFY).R;
		break;
	case 7:
		value = timd->tim->GTM_CH_REG(7,IRQ_NOTIFY).R;
		break;
	default:
		value = 0;
		break;
	}

	return value;
}

/**
 * @brief   Return TIM Channel interrupt enabled
 *
 * @param[in] timd       GTM TIM driver pointer
 *
 * @param[in] channel    GTM TIM channel number
 *
 * @return Interrupt enabled
 *
 * @sa
 * TIM_CHANNEL0, TIM_CHANNEL1, TIM_CHANNEL2, TIM_CHANNEL3, TIM_CHANNEL4, TIM_CHANNEL5, TIM_CHANNEL6, TIM_CHANNEL7,
 * <br>
 * SPC5_GTM_TIM_IRQ_STATUS_CCU0, SPC5_GTM_TIM_IRQ_STATUS_CCU1
 *
 * @api
 */
uint32_t gtm_timGetIntEnabled(GTM_TIMDriver *timd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = timd->tim->GTM_CH_REG(0,IRQ_EN).R;
		break;
	case 1:
		value = timd->tim->GTM_CH_REG(1,IRQ_EN).R;
		break;
	case 2:
		value = timd->tim->GTM_CH_REG(2,IRQ_EN).R;
		break;
	case 3:
		value = timd->tim->GTM_CH_REG(3,IRQ_EN).R;
		break;
	case 4:
		value = timd->tim->GTM_CH_REG(4,IRQ_EN).R;
		break;
	case 5:
		value = timd->tim->GTM_CH_REG(5,IRQ_EN).R;
		break;
	case 6:
		value = timd->tim->GTM_CH_REG(6,IRQ_EN).R;
		break;
	case 7:
		value = timd->tim->GTM_CH_REG(7,IRQ_EN).R;
		break;
	default:
		value = 0UL;
		break;
	}

	return value;
}

/**
 * @brief   Enable TIM Channel Filter
 *
 * @param[in] timd       GTM TIM driver pointer
 *
 * @param[in] channel    GTM TIM channel number
 *
 * @sa
 * TIM_CHANNEL0, TIM_CHANNEL1, TIM_CHANNEL2, TIM_CHANNEL3, TIM_CHANNEL4, TIM_CHANNEL5, TIM_CHANNEL6, TIM_CHANNEL7,
 *
 * @api
 */
void gtm_timEnableFilter(GTM_TIMDriver *timd, uint8_t channel) {
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,CTRL).R |= (ENABLE_FIELD << CH_FLT);
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,CTRL).R |= (ENABLE_FIELD << CH_FLT);
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,CTRL).R |= (ENABLE_FIELD << CH_FLT);
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,CTRL).R |= (ENABLE_FIELD << CH_FLT);
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,CTRL).R |= (ENABLE_FIELD << CH_FLT);
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,CTRL).R |= (ENABLE_FIELD << CH_FLT);
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,CTRL).R |= (ENABLE_FIELD << CH_FLT);
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,CTRL).R |= (ENABLE_FIELD << CH_FLT);
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Disable TIM Channel Filter
 *
 * @param[in] timd       GTM TIM driver pointer
 *
 * @param[in] channel    GTM TIM channel number
 *
 * @sa
 * TIM_CHANNEL0, TIM_CHANNEL1, TIM_CHANNEL2, TIM_CHANNEL3, TIM_CHANNEL4, TIM_CHANNEL5, TIM_CHANNEL6, TIM_CHANNEL7,
 *
 * @api
 */
void gtm_timDisableFilter(GTM_TIMDriver *timd, uint8_t channel) {
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,CTRL).R &= ~(ENABLE_FIELD << CH_FLT);
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,CTRL).R &= ~(ENABLE_FIELD << CH_FLT);
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,CTRL).R &= ~(ENABLE_FIELD << CH_FLT);
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,CTRL).R &= ~(ENABLE_FIELD << CH_FLT);
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,CTRL).R &= ~(ENABLE_FIELD << CH_FLT);
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,CTRL).R &= ~(ENABLE_FIELD << CH_FLT);
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,CTRL).R &= ~(ENABLE_FIELD << CH_FLT);
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,CTRL).R &= ~(ENABLE_FIELD << CH_FLT);
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Disable TIM Channel Filter
 *
 * @param[in] timd       GTM TIM driver pointer
 *
 * @param[in] channel    GTM TIM channel number
 *
 * @param[in] flt_cnt_frq    GTM TIM Filter Counter frequency select
 * @sa
 * TIM_CHANNEL0, TIM_CHANNEL1, TIM_CHANNEL2, TIM_CHANNEL3, TIM_CHANNEL4, TIM_CHANNEL5, TIM_CHANNEL6, TIM_CHANNEL7,
 *
 * @api
 */
void gtm_timSetFilterClockFreq(GTM_TIMDriver *timd, uint8_t channel, uint8_t flt_cnt_frq) {
	uint32_t value;

	if((flt_cnt_frq >= 0U) && (flt_cnt_frq <= 3U)) {
		switch(channel) {
		case 0:
			value = timd->tim->GTM_CH_REG(0,CTRL).R;
			timd->tim->GTM_CH_REG(0,CTRL).R = ((value & 0xFFF9FFFF) | (flt_cnt_frq << CH_FLT_CNT_FREQ));
			break;
		case 1:
			value = timd->tim->GTM_CH_REG(1,CTRL).R;
			timd->tim->GTM_CH_REG(1,CTRL).R = ((value & 0xFFF9FFFF) | (flt_cnt_frq << CH_FLT_CNT_FREQ));
			break;
		case 2:
			value = timd->tim->GTM_CH_REG(2,CTRL).R;
			timd->tim->GTM_CH_REG(2,CTRL).R = ((value & 0xFFF9FFFF) | (flt_cnt_frq << CH_FLT_CNT_FREQ));
			break;
		case 3:
			value = timd->tim->GTM_CH_REG(3,CTRL).R;
			timd->tim->GTM_CH_REG(3,CTRL).R = ((value & 0xFFF9FFFF) | (flt_cnt_frq << CH_FLT_CNT_FREQ));
			break;
		case 4:
			value = timd->tim->GTM_CH_REG(4,CTRL).R;
			timd->tim->GTM_CH_REG(4,CTRL).R = ((value & 0xFFF9FFFF) | (flt_cnt_frq << CH_FLT_CNT_FREQ));
			break;
		case 5:
			value = timd->tim->GTM_CH_REG(5,CTRL).R;
			timd->tim->GTM_CH_REG(5,CTRL).R = ((value & 0xFFF9FFFF) | (flt_cnt_frq << CH_FLT_CNT_FREQ));
			break;
		case 6:
			value = timd->tim->GTM_CH_REG(6,CTRL).R;
			timd->tim->GTM_CH_REG(6,CTRL).R = ((value & 0xFFF9FFFF) | (flt_cnt_frq << CH_FLT_CNT_FREQ));
			break;
		case 7:
			value = timd->tim->GTM_CH_REG(7,CTRL).R;
			timd->tim->GTM_CH_REG(7,CTRL).R = ((value & 0xFFF9FFFF) | (flt_cnt_frq << CH_FLT_CNT_FREQ));
			break;
		default:
			/* MISRA check */
			break;
		}
	}
}

/**
 * @brief   Return TIM Channel Filter counter frequency
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @return Filter counter frequency
 *
 * @api
 */
uint8_t gtm_timGetFilterClockFreq(GTM_TIMDriver *timd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = timd->tim->GTM_CH_REG(0,CTRL).R;
		break;
	case 1:
		value = timd->tim->GTM_CH_REG(1,CTRL).R;
		break;
	case 2:
		value = timd->tim->GTM_CH_REG(2,CTRL).R;
		break;
	case 3:
		value = timd->tim->GTM_CH_REG(3,CTRL).R;
		break;
	case 4:
		value = timd->tim->GTM_CH_REG(4,CTRL).R;
		break;
	case 5:
		value = timd->tim->GTM_CH_REG(5,CTRL).R;
		break;
	case 6:
		value = timd->tim->GTM_CH_REG(6,CTRL).R;
		break;
	case 7:
		value = timd->tim->GTM_CH_REG(7,CTRL).R;
		break;
	default:
		value = 0UL;
		break;
	}
	return (uint8_t)((value & 0x00060000UL) >> 17UL);
}

/**
 * @brief   Set TIM Channel Filter
 *
 * @param[in] timd       GTM TIM driver pointer
 *
 * @param[in] channel    GTM TIM channel number
 *
 * @param[in] edge       GTM TIM Filter edge select
 *
 * @param[in] mode       GTM TIM Filter mode select
 *
 * @param[in] time       GTM TIM Filter acceptance and de-glitch time
 *
 * @sa
 * TIM_CHANNEL0, TIM_CHANNEL1, TIM_CHANNEL2, TIM_CHANNEL3, TIM_CHANNEL4, TIM_CHANNEL5, TIM_CHANNEL6, TIM_CHANNEL7,
 *
 *edge: 0: Falling edge, 1: Rising edge
 *
 *mode: 0: Immediate, 1: de-glitch (upper/down counter), 2: de-glitch (hold counter)
 *
 * @api
 */
void gtm_timSetFilter(GTM_TIMDriver *timd, uint8_t channel, uint8_t edge, uint8_t mode, uint32_t time) {
	uint32_t flt_mode, flt_ctr, flt_time;

	if (edge == 0) { /* Falling edge */
		switch(mode) {
		case 0: /* immediate */
			flt_mode = (0UL << CH_FLT_M_FE);
			flt_ctr  = (0UL << CH_FLT_CTR_FE);
			break;
		case 1: /* de-glitch (upper/down counter) */
			flt_mode = (1UL << CH_FLT_M_FE);
			flt_ctr  = (0UL << CH_FLT_CTR_FE);
			break;
		case 2: /* de-glitch (hold counter) */
			flt_mode = (1UL << CH_FLT_M_FE);
			flt_ctr  = (1UL << CH_FLT_CTR_FE);
			break;
		default:
			flt_mode = (0UL << CH_FLT_M_FE);
			flt_ctr  = (0UL << CH_FLT_CTR_FE);
			break;
		}
	}
	if (edge == 1) { /* Rising edge */
		switch(mode) {
		case 0: /* immediate */
			flt_mode = (0UL << CH_FLT_M_RE);
			flt_ctr  = (0UL << CH_FLT_CTR_RE);
			break;
		case 1: /* de-glitch (upper/down counter) */
			flt_mode = (1UL << CH_FLT_M_RE);
			flt_ctr  = (0UL << CH_FLT_CTR_RE);
			break;
		case 2: /* de-glitch (hold counter) */
			flt_mode = (1UL << CH_FLT_M_RE);
			flt_ctr  = (1UL << CH_FLT_CTR_RE);
			break;
		default:
			flt_mode = (0UL << CH_FLT_M_RE);
			flt_ctr  = (0UL << CH_FLT_CTR_RE);
			break;
		}
	}
	switch(channel) {
	case 0:
		timd->tim->GTM_CH_REG(0,CTRL).R |= (flt_mode | flt_ctr);
		if (edge == 0) { /* Falling edge */
			timd->tim->GTM_CH_REG(0,FLT_FE).R = (0x00FFFFFF & time);
		}
		if (edge == 1) { /* Rising edge */
			timd->tim->GTM_CH_REG(0,FLT_RE).R = (0x00FFFFFF & time);
		}
		break;
	case 1:
		timd->tim->GTM_CH_REG(1,CTRL).R |= (flt_mode | flt_ctr);
		if (edge == 0) { /* Falling edge */
			timd->tim->GTM_CH_REG(1,FLT_FE).R = (0x00FFFFFF & time);
		}
		if (edge == 1) { /* Rising edge */
			timd->tim->GTM_CH_REG(1,FLT_RE).R = (0x00FFFFFF & time);
		}
		break;
	case 2:
		timd->tim->GTM_CH_REG(2,CTRL).R |= (flt_mode | flt_ctr);
		if (edge == 0) { /* Falling edge */
			timd->tim->GTM_CH_REG(2,FLT_FE).R = (0x00FFFFFF & time);
		}
		if (edge == 1) { /* Rising edge */
			timd->tim->GTM_CH_REG(2,FLT_RE).R = (0x00FFFFFF & time);
		}
		break;
	case 3:
		timd->tim->GTM_CH_REG(3,CTRL).R |= (flt_mode | flt_ctr);
		if (edge == 0) { /* Falling edge */
			timd->tim->GTM_CH_REG(3,FLT_FE).R = (0x00FFFFFF & time);
		}
		if (edge == 1) { /* Rising edge */
			timd->tim->GTM_CH_REG(3,FLT_RE).R = (0x00FFFFFF & time);
		}
		break;
	case 4:
		timd->tim->GTM_CH_REG(4,CTRL).R |= (flt_mode | flt_ctr);
		if (edge == 0) { /* Falling edge */
			timd->tim->GTM_CH_REG(4,FLT_FE).R = (0x00FFFFFF & time);
		}
		if (edge == 1) { /* Rising edge */
			timd->tim->GTM_CH_REG(4,FLT_RE).R = (0x00FFFFFF & time);
		}
		break;
	case 5:
		timd->tim->GTM_CH_REG(5,CTRL).R |= (flt_mode | flt_ctr);
		if (edge == 0) { /* Falling edge */
			timd->tim->GTM_CH_REG(5,FLT_FE).R = (0x00FFFFFF & time);
		}
		if (edge == 1) { /* Rising edge */
			timd->tim->GTM_CH_REG(5,FLT_RE).R = (0x00FFFFFF & time);
		}
		break;
	case 6:
		timd->tim->GTM_CH_REG(6,CTRL).R |= (flt_mode | flt_ctr);
		if (edge == 0) { /* Falling edge */
			timd->tim->GTM_CH_REG(6,FLT_FE).R = (0x00FFFFFF & time);
		}
		if (edge == 1) { /* Rising edge */
			timd->tim->GTM_CH_REG(6,FLT_RE).R = (0x00FFFFFF & time);
		}
		break;
	case 7:
		timd->tim->GTM_CH_REG(7,CTRL).R |= (flt_mode | flt_ctr);
		if (edge == 0) { /* Falling edge */
			timd->tim->GTM_CH_REG(7,FLT_FE).R = (0x00FFFFFF & time);
		}
		if (edge == 1) { /* Rising edge */
			timd->tim->GTM_CH_REG(7,FLT_RE).R = (0x00FFFFFF & time);
		}
		break;
	default:
		/* MISRA check */
		break;
	}
}

/**
 * @brief   Return TIM Channel Filter settings
 *
 * @param[in] timd        GTM TIM driver pointer
 *
 * @param[in] channel     GTM TIM channel number
 *
 * @return Filter configuration settings
 *
 * @sa
 * Value return is a mask of bit filter fields in the CTRL register
 * @api
 */
uint32_t gtm_timGetFilter(GTM_TIMDriver *timd, uint8_t channel) {
	uint32_t value;

	switch(channel) {
	case 0:
		value = timd->tim->GTM_CH_REG(0,CTRL).R;
		break;
	case 1:
		value = timd->tim->GTM_CH_REG(1,CTRL).R;
		break;
	case 2:
		value = timd->tim->GTM_CH_REG(2,CTRL).R;
		break;
	case 3:
		value = timd->tim->GTM_CH_REG(3,CTRL).R;
		break;
	case 4:
		value = timd->tim->GTM_CH_REG(4,CTRL).R;
		break;
	case 5:
		value = timd->tim->GTM_CH_REG(5,CTRL).R;
		break;
	case 6:
		value = timd->tim->GTM_CH_REG(6,CTRL).R;
		break;
	case 7:
		value = timd->tim->GTM_CH_REG(7,CTRL).R;
		break;
	default:
		value = 0;
		break;
	}
	return (value & 0x00F70000); /*Return only Filter configuration bit */
}
#endif
/** @} */

