/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
**  COPYRIGHT
**  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
**
******************************************************************************/
/******************************************************************************
**  SWC             :  Vsram
**  Filename        :  Vsram.c
**  Created on      :  08-apr-2020 14:00:00
**  Original author :  CarboniM
******************************************************************************/
#ifdef _BUILD_VSRAM_

/*****************************************************************************
** INCLUDE FILES
******************************************************************************/
#include "Vsram.h"
/* No other .h files shall be added here */

/*****************************************************************************
** PUBLIC VARIABLE DEFINITIONS
******************************************************************************/


/*****************************************************************************
** PRIVATE VARIABLES AND TESTPOINTS
******************************************************************************/

/*****************************************************************************
** PUBLIC FUNCTION DEFINITIONS
******************************************************************************/
 /******************************************************************************
 **   Function    : VSRAM_ComputeCheckSum
 **
 **   Description:
 **    Calculates the VSRAM checksum value.
 **
 **   Parameters :
 **    [out] uint32_T *checkSumVal: calculated checksum value
 **
 **   Returns:
 **    NO_ERROR - No error
 **
 **   SW Requirements:
 **    NA
 **
 **   Implementation Notes:
 **
 **   EA GUID:
 ******************************************************************************/
int16_T VSRAM_ComputeCheckSum(uint32_T *checkSumVal)
{
    int16_T result = NO_ERROR;
    uint16_T vsramSizeWord;
    uint32_T *pData;

    *checkSumVal = 0u;
    pData = VSRAM_SRAM_START_ADDR + 1u;
    vsramSizeWord = (uint16_T) ((uint16_T)(VSRAM_SRAM_SIZE) - (uint16_T)(1u));

    while (vsramSizeWord != 0u)
    {
        *checkSumVal += *pData;
        pData++;
        vsramSizeWord--;
    }

    return result;
}

/******************************************************************************
**   Function    : VSRAM_InitAllMemory
**
**   Description:
**    initializes all the VSRAM memory to the init value
**
**   Parameters :
**    [in] uint32_T vsramInitWord: init value  
**
**   Returns:
**    NO_ERROR - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T VSRAM_InitAllMemory(uint32_T vsramInitWord)
{
    int16_T result = NO_ERROR;
    uint16_T  vsramSizeWord;
    uint32_T  *pData;

    pData = VSRAM_SRAM_START_ADDR;
    vsramSizeWord = VSRAM_SRAM_SIZE;

    while (vsramSizeWord != 0u)
    {
        *pData = vsramInitWord;
        pData++;
        vsramSizeWord--;
    }

    return result;
}

/******************************************************************************
**   Function    : VSRAM_WriteCheckSum
**
**   Description:
**    writes the checksum value
**
**   Parameters :
**    [in] uint32_T checkSumVal: checksum value
**
**   Returns:
**    NO_ERROR - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T VSRAM_WriteCheckSum(uint32_T checkSumVal)
{
    int16_T result = NO_ERROR;

    VSRAM_CheckSumVal = checkSumVal;

    return result;
}

/******************************************************************************
**   Function    : VSRAM_ReadCheckSum
**
**   Description:
**    reads the checksum value
**
**   Parameters :
**    [out] uint32_T *checkSumVal: checksum value
**
**   Returns:
**    NO_ERROR - No error
**
**   SW Requirements:
**    NA
**
**   Implementation Notes:
**
**   EA GUID:
******************************************************************************/
int16_T VSRAM_ReadCheckSum(uint32_T *checkSumVal)
{
    int16_T    result = NO_ERROR;
    uint32_T   *pCheckSum;

    pCheckSum = checkSumVal;

    *pCheckSum = VSRAM_CheckSumVal;

    return result;
}

/*****************************************************************************
** PRIVATE FUNCTION DEFINITIONS
******************************************************************************/


#endif /* _BUILD_VSRAM_ */
/****************************************************************************
 ****************************************************************************/
