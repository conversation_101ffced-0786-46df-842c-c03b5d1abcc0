/*****************************************************************************************************************/
/* $HeadURL::                                                                                                 $  */
/* $Revision::                                                                                                $  */
/* $Date::                                                                                                    $  */
/* $Author::                                                                                                  $  */
/*****************************************************************************************************************/
/******************************************************************************
 **  COPYRIGHT
 **  Copyright (c) 2019 by Eldor Corporation S.P.A. ,    All rights reserved.
 **
 ******************************************************************************/
/**
 *******************************************************************************
 **  FILE INFORMATION:
 **  Filename:           IonKnockFFT.c
 **  File Creation Date: 21-Apr-2021
 **
 **  ABSTRACT:
 **
 **
 **  NOTES:
 **
 **
 **  MODEL INFORMATION:
 **  Model Name:         IonKnockFFT
 **  Model Description:  This model applies an Hanning window on the part of ion signal used for spectrum analysis, in order to make the signal periodic, then FFT analysis is performed.
   The model is triggered by End Of Acquisition event.

 **  Model Version:      1.1447
 **  Model Author:       RoccaG - Fri Feb 01 10:04:55 2019
 **
 **  MODIFICATION HISTORY:
 **  Model at Code Generation: RoccaG - Wed Apr 21 14:49:34 2021
 **
 **  Last Saved Modification:  RoccaG - Wed Apr 21 14:40:42 2021
 **
 **
 *******************************************************************************
 **/
/*****************************************************************************
 ** INCLUDE FILES
 ******************************************************************************/
#include "IonKnockFFT_out.h"
#include "IonKnockFFT_private.h"

/*****************************************************************************
 ** PRIVATE DEFINES
 ******************************************************************************/

/* Named constants for Chart: '<Root>/Scheduler' */
#define IonKn_event_IonKnockFFT_PowerOn (0)
#define IonKnockF_event_IonKnockFFT_EOA (1)

/* Exported data define */

/* Definition for custom storage class: ELD_LOCAL_DEFINE */
#define FFT_BUFF_SIZE                  130U                      /* Referenced by: '<S7>/DataBufferBuild' */

/* Size of the buffer used for FFT. */
#define ID_VER_IONKNOCKFFT_DEF         11447U                    /* Referenced by: '<Root>/Scheduler' */

/* Model Version. */
#define LOG2_FFT_SAMPLE                7U                        /* Referenced by: '<S6>/Constant' */

/* Log2 on fft sample number. */

/*****************************************************************************
 ** PRIVATE MACROS
 ******************************************************************************/

/*****************************************************************************
 ** PRIVATE TYPEDEFS
 ******************************************************************************/

/* user code (top of source file) */
/* System '<Root>' */
#ifdef _BUILD_IONKNOCKFFT_

/*****************************************************************************
 ** LOCAL VARIABLES, CALIBRATIONS, OUTPUTS AND TESTPOINTS
 ******************************************************************************/

/* Exported data definition */

/* Definition for custom storage class: ELD_LOCAL_CONST */
static const uint16_T HANN_WINDOW[64] = { 0U, 40U, 160U, 360U, 639U, 997U, 1433U,
  1945U, 2533U, 3194U, 3929U, 4733U, 5607U, 6546U, 7550U, 8616U, 9741U, 10922U,
  12156U, 13441U, 14774U, 16150U, 17567U, 19021U, 20509U, 22027U, 23571U, 25138U,
  26723U, 28323U, 29934U, 31552U, 33173U, 34793U, 36408U, 38014U, 39607U, 41183U,
  42739U, 44271U, 45774U, 47245U, 48681U, 50078U, 51433U, 52742U, 54002U, 55211U,
  56364U, 57459U, 58494U, 59467U, 60373U, 61213U, 61982U, 62681U, 63305U, 63856U,
  64330U, 64727U, 65045U, 65285U, 65445U, 65525U } ;/* Referenced by: '<S9>/Constant1' */

/* Hanning window to prepare ion buffer for fft */

/*Memory section for Output interface*/
/* Definition for custom storage class: ELD_OUT_INTERFACE */
int32_T ImgData[64];                   /* '<S5>/Merge1' */

/* ImgData contains imaginary terms of fft */
int32_T RealData[64];                  /* '<S5>/Merge' */

/* RealData contains real terms of fft */

/*Static test point definition*/
/* Definition for custom storage class: ELD_STATIC_TEST_POINT */
STATIC_TEST_POINT int32_T DataBufferForFFT[131];

/* Ion signal modified by hanning window */
STATIC_TEST_POINT uint32_T IdVer_IonKnockFFT;

/* Model Version */

/*****************************************************************************
 ** FUNCTIONS DECLARATION
 ******************************************************************************/

/* Forward declaration for local functions */
static void IonKno_chartstep_c1_IonKnockFFT(const int32_T *sfEvent);

/***************************** FILE SCOPE DATA ********************************/

/*****************************************************************************
 ** FUNCTIONS
 ******************************************************************************/

/* Function for Chart: '<Root>/Scheduler' */
static void IonKno_chartstep_c1_IonKnockFFT(const int32_T *sfEvent)
{
  int32_T rtb_C_Caller[131];
  uint16_T index;
  uint16_T hanningIndex;

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
   *  Requirements related to each task functionality are directly linked to subsystems.
   */
  /* Chart: '<Root>/Scheduler' incorporates:
   *  Inport: '<Root>/IonKnockEnabled'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
   *  Requirements related to each task functionality are directly linked to subsystems.
   */
  /* During: Scheduler */
  /* This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
     Requirements related to EOA task are directly linked to subsystems. */
  /* Entry Internal: Scheduler */
  /* Transition: '<S2>:40' */
  if ((*sfEvent) == ((int32_T)IonKn_event_IonKnockFFT_PowerOn)) {
    /* Outputs for Function Call SubSystem: '<S1>/PowerOn_Task'
     *
     * Block description for '<S1>/PowerOn_Task':
     *  Outputs reset. Requirements related to this functionality has been
     *  linked to previous scheduler.
     */
    /* SignalConversion generated from: '<S4>/DataBufferForFFT' */
    /* Transition: '<S2>:8' */
    /* Transition: '<S2>:19':
     *  1. EISB_FCA6CYL_SW_REQ_1745: Software shall set to 0 each output produced for Fast Fourier Transform algorithm after ECU power on (ECU_SW_Requirements#4336)
     */
    /* Event: '<S2>:31' */
    memset((&(DataBufferForFFT[0])), 0, 131U * (sizeof(int32_T)));

    /* SignalConversion generated from: '<S4>/RealData' */
    memset((&(RealData[0])), 0, (sizeof(int32_T)) << 6U);

    /* SignalConversion generated from: '<S4>/ImgData' */
    memset((&(ImgData[0])), 0, (sizeof(int32_T)) << 6U);

    /* End of Outputs for SubSystem: '<S1>/PowerOn_Task' */
    IdVer_IonKnockFFT = ID_VER_IONKNOCKFFT_DEF;
  } else {
    /* Transition: '<S2>:10' */
    /*  IonKnockFFT_EOA */
    if (((int32_T)IonKnockEnabled) != 0) {
      /* Outputs for Function Call SubSystem: '<S3>/Hanning' */
      /* Chart: '<S7>/DataBufferBuild' incorporates:
       *  Constant: '<S9>/Constant1'
       *  Inport: '<Root>/IonBufferMod'
       *  Inport: '<Root>/NSampIonPower'
       *  Product: '<S9>/Product'
       *  Selector: '<S9>/Selector'
       *  Selector: '<S9>/Selector1'
       *  SignalConversion generated from: '<S7>/DataBufferForFFT'
       *
       * Block description for '<S7>/DataBufferBuild':
       *  This block performs the for-iteration used to apply hanning and zero padding to the buffer that will be analyzed by FFT.
       *  Because an hanning window is symmetric only the increasing part is recorded in HANN_WINDOW constant.
       *  So the algorithm executes two steps:
       *  1. it mulitplies the ion buffer for the HANN_WINDOW scanning it with a positive verse.
       *  2. when the last index of HANN_WINDOW is reached, the index is decremented in order to perform decreasing part of the hanning.
       *
       *  If the number of valid samples is lower than 128 than the input buffer is filled with zero values in order to provide always 128 elements to FFT.
       */
      /* Transition: '<S2>:12' */
      /* Transition: '<S2>:15' */
      /* Event: '<S2>:32' */
      /* Gateway: IonKnockFFT_FC/EOA_Task/Hanning/DataBufferBuild */
      /* During: IonKnockFFT_FC/EOA_Task/Hanning/DataBufferBuild */
      /* This block performs the for-iteration used to apply hanning and zero padding to the buffer that will be analyzed by FFT.
         Because an hanning window is symmetric only the increasing part is recorded in HANN_WINDOW constant.
         So the algorithm executes two steps:
         1. it mulitplies the ion buffer for the HANN_WINDOW scanning it with a positive verse.
         2. when the last index of HANN_WINDOW is reached, the index is decremented in order to perform decreasing part of the hanning.

         If the number of valid samples is lower than 128 than the input buffer is filled with zero values in order to provide always 128 elements to FFT. */
      /* Entry Internal: IonKnockFFT_FC/EOA_Task/Hanning/DataBufferBuild */
      /* Transition: '<S8>:2' */
      hanningIndex = 0U;
      index = 0U;

      /* Transition: '<S8>:75' */
      DataBufferForFFT[0] = 0;
      while (index < ((uint16_T)FFT_BUFF_SIZE)) {
        /* Transition: '<S8>:6' */
        /* Transition: '<S8>:9' */
        /*  HANNING  */
        if ((index < ((uint16_T)FFT_SAMPLE_DIV2)) && (index < NSampIonPower)) {
          /* Outputs for Function Call SubSystem: '<S7>/Hanning'
           *
           * Block description for '<S7>/Hanning':
           *  This block applies hanning window on the i_th sample.
           *
           * Block requirements for '<S7>/Hanning':
           *  1. EISB_FCA6CYL_SW_REQ_1157: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1181)
           */
          /* Transition: '<S8>:13' */
          /* Transition: '<S8>:24' */
          /* Event: '<S8>:25' */
          DataBufferForFFT[((int32_T)index) + 1] = (int32_T)((uint32_T)
            ((((uint32_T)IonBufferMod[(index)]) * ((uint32_T)HANN_WINDOW
            [(hanningIndex)])) >> ((uint64_T)16)));

          /* End of Outputs for SubSystem: '<S7>/Hanning' */
          hanningIndex = (uint16_T)((int32_T)(((int32_T)hanningIndex) + 1));

          /* Transition: '<S8>:32' */
          /* Transition: '<S8>:35' */
        } else {
          /* Transition: '<S8>:18' */
          if ((index < ((uint16_T)FFT_SAMPLE)) && (index < NSampIonPower)) {
            /* Transition: '<S8>:20' */
            /* Transition: '<S8>:31' */
            if (((int32_T)hanningIndex) <= 1) {
              hanningIndex = 1U;
            }

            hanningIndex = (uint16_T)((int32_T)(((int32_T)hanningIndex) - 1));

            /* Outputs for Function Call SubSystem: '<S7>/Hanning'
             *
             * Block description for '<S7>/Hanning':
             *  This block applies hanning window on the i_th sample.
             *
             * Block requirements for '<S7>/Hanning':
             *  1. EISB_FCA6CYL_SW_REQ_1157: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1181)
             */
            /* Event: '<S8>:25' */
            DataBufferForFFT[((int32_T)index) + 1] = (int32_T)((uint32_T)
              ((((uint32_T)IonBufferMod[(index)]) * ((uint32_T)HANN_WINDOW
              [(hanningIndex)])) >> ((uint64_T)16)));

            /* End of Outputs for SubSystem: '<S7>/Hanning' */
            /* Transition: '<S8>:35' */
          } else {
            /* Transition: '<S8>:34':
             *  1. EISB_FCA6CYL_SW_REQ_1157: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1181)
             */
            DataBufferForFFT[((int32_T)index) + 1] = 0;
          }
        }

        /* Transition: '<S8>:36' */
        index = (uint16_T)((int32_T)(((int32_T)index) + 1));
      }

      /* End of Chart: '<S7>/DataBufferBuild' */
      /* End of Outputs for SubSystem: '<S3>/Hanning' */

      /* Outputs for Function Call SubSystem: '<S3>/FFT'
       *
       * Block description for '<S3>/FFT':
       *  This block performs Fast Fourier Transform on ion signal and returns
       *  two array, one for real terms and one for imaginary terms.
       *
       * Block requirements for '<S3>/FFT':
       *  1. EISB_FCA6CYL_SW_REQ_1158: Every time that knock detection strategy is enabled, software shal... (ECU_SW_Requirements#1182)
       */
      /* CCaller: '<S6>/C_Caller' incorporates:
       *  Constant: '<S6>/Constant'
       */
      /* Transition: '<S8>:38' */
      /* Event: '<S2>:38' */
      memcpy(&rtb_C_Caller[0], (&(DataBufferForFFT[0])), 131U * (sizeof(int32_T)));
      fft_real(rtb_C_Caller, (uint16_T)((uint8_T)LOG2_FFT_SAMPLE));

      /* SignalConversion generated from: '<S6>/ImgData' */
      ImgData[0] = rtb_C_Caller[2];
      ImgData[1] = rtb_C_Caller[4];
      ImgData[2] = rtb_C_Caller[6];
      ImgData[3] = rtb_C_Caller[8];
      ImgData[4] = rtb_C_Caller[10];
      ImgData[5] = rtb_C_Caller[12];
      ImgData[6] = rtb_C_Caller[14];
      ImgData[7] = rtb_C_Caller[16];
      ImgData[8] = rtb_C_Caller[18];
      ImgData[9] = rtb_C_Caller[20];
      ImgData[10] = rtb_C_Caller[22];
      ImgData[11] = rtb_C_Caller[24];
      ImgData[12] = rtb_C_Caller[26];
      ImgData[13] = rtb_C_Caller[28];
      ImgData[14] = rtb_C_Caller[30];
      ImgData[15] = rtb_C_Caller[32];
      ImgData[16] = rtb_C_Caller[34];
      ImgData[17] = rtb_C_Caller[36];
      ImgData[18] = rtb_C_Caller[38];
      ImgData[19] = rtb_C_Caller[40];
      ImgData[20] = rtb_C_Caller[42];
      ImgData[21] = rtb_C_Caller[44];
      ImgData[22] = rtb_C_Caller[46];
      ImgData[23] = rtb_C_Caller[48];
      ImgData[24] = rtb_C_Caller[50];
      ImgData[25] = rtb_C_Caller[52];
      ImgData[26] = rtb_C_Caller[54];
      ImgData[27] = rtb_C_Caller[56];
      ImgData[28] = rtb_C_Caller[58];
      ImgData[29] = rtb_C_Caller[60];
      ImgData[30] = rtb_C_Caller[62];
      ImgData[31] = rtb_C_Caller[64];
      ImgData[32] = rtb_C_Caller[66];
      ImgData[33] = rtb_C_Caller[68];
      ImgData[34] = rtb_C_Caller[70];
      ImgData[35] = rtb_C_Caller[72];
      ImgData[36] = rtb_C_Caller[74];
      ImgData[37] = rtb_C_Caller[76];
      ImgData[38] = rtb_C_Caller[78];
      ImgData[39] = rtb_C_Caller[80];
      ImgData[40] = rtb_C_Caller[82];
      ImgData[41] = rtb_C_Caller[84];
      ImgData[42] = rtb_C_Caller[86];
      ImgData[43] = rtb_C_Caller[88];
      ImgData[44] = rtb_C_Caller[90];
      ImgData[45] = rtb_C_Caller[92];
      ImgData[46] = rtb_C_Caller[94];
      ImgData[47] = rtb_C_Caller[96];
      ImgData[48] = rtb_C_Caller[98];
      ImgData[49] = rtb_C_Caller[100];
      ImgData[50] = rtb_C_Caller[102];
      ImgData[51] = rtb_C_Caller[104];
      ImgData[52] = rtb_C_Caller[106];
      ImgData[53] = rtb_C_Caller[108];
      ImgData[54] = rtb_C_Caller[110];
      ImgData[55] = rtb_C_Caller[112];
      ImgData[56] = rtb_C_Caller[114];
      ImgData[57] = rtb_C_Caller[116];
      ImgData[58] = rtb_C_Caller[118];
      ImgData[59] = rtb_C_Caller[120];
      ImgData[60] = rtb_C_Caller[122];
      ImgData[61] = rtb_C_Caller[124];
      ImgData[62] = rtb_C_Caller[126];
      ImgData[63] = rtb_C_Caller[128];

      /* SignalConversion generated from: '<S6>/RealData' */
      RealData[0] = rtb_C_Caller[1];
      RealData[1] = rtb_C_Caller[3];
      RealData[2] = rtb_C_Caller[5];
      RealData[3] = rtb_C_Caller[7];
      RealData[4] = rtb_C_Caller[9];
      RealData[5] = rtb_C_Caller[11];
      RealData[6] = rtb_C_Caller[13];
      RealData[7] = rtb_C_Caller[15];
      RealData[8] = rtb_C_Caller[17];
      RealData[9] = rtb_C_Caller[19];
      RealData[10] = rtb_C_Caller[21];
      RealData[11] = rtb_C_Caller[23];
      RealData[12] = rtb_C_Caller[25];
      RealData[13] = rtb_C_Caller[27];
      RealData[14] = rtb_C_Caller[29];
      RealData[15] = rtb_C_Caller[31];
      RealData[16] = rtb_C_Caller[33];
      RealData[17] = rtb_C_Caller[35];
      RealData[18] = rtb_C_Caller[37];
      RealData[19] = rtb_C_Caller[39];
      RealData[20] = rtb_C_Caller[41];
      RealData[21] = rtb_C_Caller[43];
      RealData[22] = rtb_C_Caller[45];
      RealData[23] = rtb_C_Caller[47];
      RealData[24] = rtb_C_Caller[49];
      RealData[25] = rtb_C_Caller[51];
      RealData[26] = rtb_C_Caller[53];
      RealData[27] = rtb_C_Caller[55];
      RealData[28] = rtb_C_Caller[57];
      RealData[29] = rtb_C_Caller[59];
      RealData[30] = rtb_C_Caller[61];
      RealData[31] = rtb_C_Caller[63];
      RealData[32] = rtb_C_Caller[65];
      RealData[33] = rtb_C_Caller[67];
      RealData[34] = rtb_C_Caller[69];
      RealData[35] = rtb_C_Caller[71];
      RealData[36] = rtb_C_Caller[73];
      RealData[37] = rtb_C_Caller[75];
      RealData[38] = rtb_C_Caller[77];
      RealData[39] = rtb_C_Caller[79];
      RealData[40] = rtb_C_Caller[81];
      RealData[41] = rtb_C_Caller[83];
      RealData[42] = rtb_C_Caller[85];
      RealData[43] = rtb_C_Caller[87];
      RealData[44] = rtb_C_Caller[89];
      RealData[45] = rtb_C_Caller[91];
      RealData[46] = rtb_C_Caller[93];
      RealData[47] = rtb_C_Caller[95];
      RealData[48] = rtb_C_Caller[97];
      RealData[49] = rtb_C_Caller[99];
      RealData[50] = rtb_C_Caller[101];
      RealData[51] = rtb_C_Caller[103];
      RealData[52] = rtb_C_Caller[105];
      RealData[53] = rtb_C_Caller[107];
      RealData[54] = rtb_C_Caller[109];
      RealData[55] = rtb_C_Caller[111];
      RealData[56] = rtb_C_Caller[113];
      RealData[57] = rtb_C_Caller[115];
      RealData[58] = rtb_C_Caller[117];
      RealData[59] = rtb_C_Caller[119];
      RealData[60] = rtb_C_Caller[121];
      RealData[61] = rtb_C_Caller[123];
      RealData[62] = rtb_C_Caller[125];
      RealData[63] = rtb_C_Caller[127];

      /* End of Outputs for SubSystem: '<S3>/FFT' */
    } else {
      /* Outputs for Function Call SubSystem: '<S1>/PowerOn_Task'
       *
       * Block description for '<S1>/PowerOn_Task':
       *  Outputs reset. Requirements related to this functionality has been
       *  linked to previous scheduler.
       */
      /* SignalConversion generated from: '<S4>/DataBufferForFFT' */
      /* Transition: '<S2>:17':
       *  1. EISB_FCA6CYL_SW_REQ_1746: Software shall set to 0 each output produced for Fast Fourier Tran... (ECU_SW_Requirements#4337)
       */
      /* Event: '<S2>:31' */
      memset((&(DataBufferForFFT[0])), 0, 131U * (sizeof(int32_T)));

      /* SignalConversion generated from: '<S4>/RealData' */
      memset((&(RealData[0])), 0, (sizeof(int32_T)) << 6U);

      /* SignalConversion generated from: '<S4>/ImgData' */
      memset((&(ImgData[0])), 0, (sizeof(int32_T)) << 6U);

      /* End of Outputs for SubSystem: '<S1>/PowerOn_Task' */
    }
  }

  /* End of Chart: '<Root>/Scheduler' */
}

/*
 * Output and update for function-call system: '<Root>/Scheduler'
 * Block description for: '<Root>/Scheduler'
 *   This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
 *   Requirements related to each task functionality are directly linked to subsystems.
 */
void IonKnockFFT_Scheduler(int32_T controlPortIdx)
{
  int8_T rtb_inputevents[2];
  int32_T i;

  /* Chart: '<Root>/Scheduler' incorporates:
   *  TriggerPort: '<S2>/input events'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
   *  Requirements related to each task functionality are directly linked to subsystems.
   */
  for (i = 0; i < 2; i++) {
    rtb_inputevents[i] = 0;
  }

  rtb_inputevents[controlPortIdx] = 2;

  /* Gateway: Scheduler */
  if (rtb_inputevents[0U] == 2) {
    /* Event: '<S2>:2' */
    i = (int32_T)IonKn_event_IonKnockFFT_PowerOn;
    IonKno_chartstep_c1_IonKnockFFT(&i);
  }

  if (rtb_inputevents[1U] == 2) {
    /* Event: '<S2>:3' */
    i = (int32_T)IonKnockF_event_IonKnockFFT_EOA;
    IonKno_chartstep_c1_IonKnockFFT(&i);
  }
}

/* Model step function */
void IonKnockFFT_EOA(void)
{
  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
   *  Requirements related to each task functionality are directly linked to subsystems.
   */

  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockFFT_EOA' */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
   *  Requirements related to each task functionality are directly linked to subsystems.
   */
  IonKnockFFT_Scheduler(1);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockFFT_EOA' */
}

/* Model step function */
void IonKnockFFT_PowerOn(void)
{
  /* RootInportFunctionCallGenerator generated from: '<Root>/IonKnockFFT_PowerOn' incorporates:
   *  Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
   *  Requirements related to each task functionality are directly linked to subsystems.
   */

  /* Chart: '<Root>/Scheduler'
   *
   * Block description for '<Root>/Scheduler':
   *  This chart schedules IonKnockFFT module and disables EOA runnable if knock detection strategy is not enabled.
   *  Requirements related to each task functionality are directly linked to subsystems.
   */
  IonKnockFFT_Scheduler(0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/IonKnockFFT_PowerOn' */
}

/* Model initialize function */
void IonKnockFFT_initialize(void)
{
  /* (no initialization code required) */
}

/* user code (bottom of source file) */
/* System '<Root>' */
#else

int32_T RealData[FFT_SAMPLE_DIV2];
int32_T ImgData[FFT_SAMPLE_DIV2];
void IonKnockFFT_Stub(void)
{
  uint8_T idx;
  for (idx=0;idx<FFT_SAMPLE_DIV2;idx++) {
    RealData[idx] = 0;
    ImgData[idx] = 0;
  }
}

void IonKnockFFT_PowerOn(void)
{
  IonKnockFFT_Stub();
}

void IonKnockFFT_EOA(void)
{
  IonKnockFFT_Stub();
}

#endif                                 /* _BUILD_IONKNOCKFFT_ */

/*======================== TOOL VERSION INFORMATION ==========================*
 * MATLAB 9.7 (R2019b)18-Jul-2019                                             *
 * Simulink 10.0 (R2019b)18-Jul-2019                                          *
 * Simulink Coder 9.2 (R2019b)18-Jul-2019                                     *
 * Embedded Coder 7.3 (R2019b)18-Jul-2019                                     *
 * Stateflow 10.1 (R2019b)18-Jul-2019                                         *
 * Fixed-Point Designer 6.4 (R2019b)18-Jul-2019                               *
 *============================================================================*/

/*======================= LICENSE IN USE INFORMATION =========================*
 * fixed_point_toolbox                                                        *
 * matlab                                                                     *
 * matlab_coder                                                               *
 * real-time_workshop                                                         *
 * rtw_embedded_coder                                                         *
 * simulink                                                                   *
 * simulink_requirements                                                      *
 * stateflow                                                                  *
 *============================================================================*/