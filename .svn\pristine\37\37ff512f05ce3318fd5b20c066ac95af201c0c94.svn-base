#-------------------------------------------------------------------------------
# Name:        modulo1
# Purpose:
#
# Author:      SalimbeniT
#
# Created:     28/05/2013
# Copyright:   (c) SalimbeniT 2013
# Licence:     <your licence>
#-------------------------------------------------------------------------------
import csv
import os
def main():
    pass

if __name__ == '__main__':
    main()
LenArray=10005;


"""                PARSE CSV                      """


s = input('CSV input file name: ')

CfgCANFile=open("CanMgmInTest\CanMgmInTestConfig.txt","r")

with open(s+'.csv', 'r') as csvfile:
    spamreader = csv.reader(csvfile)
    rowcount=0
    for row in spamreader:
        rowcount +=1

    csvfile.seek(0)
    SignalNameTot=next(spamreader)


    SignalTaskTot=[]
    csvfile.seek(0)
    spamreader.__next__()
    row=spamreader.__next__()
    for i in row:
        SignalTaskTot.append(int(i))

    MexName=[]
    mexname=[]
    Signal=[]
    SignalName=[]
    SignalTask=[]
    Index=[]


    for Nome in SignalNameTot:

        if Nome=="dac1":    # nome che appare nel CSV
            Signal.append(Nome)     # nome del segnale cos come lo si trova nel dbc
            mexname.append("dac_1_4")   # la variabile di tipo messaggio da dichiarare nel CAPL
            MexName.append("DAC_1_4")   # nome del messaggio can
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="dac2":
            Signal.append(Nome)
            mexname.append("dac_1_4")
            MexName.append("DAC_1_4")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="dac3":
            Signal.append(Nome)
            mexname.append("dac_1_4")
            MexName.append("DAC_1_4")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="dac4":
            Signal.append(Nome)
            mexname.append("dac_1_4")
            MexName.append("DAC_1_4")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="dac5":
            Signal.append(Nome)
            mexname.append("dac_5_8")
            MexName.append("DAC_5_8")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="dac6":
            Signal.append(Nome)
            mexname.append("dac_5_8")
            MexName.append("DAC_5_8")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="dac7":
            Signal.append(Nome)
            mexname.append("dac_5_8")
            MexName.append("DAC_5_8")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

        if Nome=="dac8":
            Signal.append(Nome)
            mexname.append("dac_5_8")
            MexName.append("DAC_5_8")
            SignalName.append(Nome)
            Index.append(SignalNameTot.index(Nome))
            SignalTask.append(SignalTaskTot[SignalNameTot.index(Nome)])

    for j in range (8,len(SignalNameTot)):
        for line in CfgCANFile:
            if "Name" in line:
                if (line.split(':')[1]).strip()==SignalNameTot[j]:
                    line=next(CfgCANFile)
                    MexName.append((line.split(':')[1]).strip())
                    mexname.append((line.split(':')[1]).strip()+"_s")
                    line=next(CfgCANFile)
                    Signal.append((line.split(':')[1]).strip())
                    SignalName.append(SignalNameTot[j])
                    Index.append(j)
                    SignalTask.append(SignalTaskTot[j])

        CfgCANFile.seek(0)







    segnale1=[0] * (rowcount+LenArray)
    segnale2=[0] * (rowcount+LenArray)
    segnale3=[0] * (rowcount+LenArray)
    segnale4=[0] * (rowcount+LenArray)
    segnale5=[0] * (rowcount+LenArray)
    segnale6=[0] * (rowcount+LenArray)

    SignalMatrix= [[0] * len(SignalName)] * (rowcount+LenArray)


    csvfile.seek(0)
    spamreader.__next__()
    row=spamreader.__next__()
    riga=[]

    for i in range (2,rowcount):
        row=spamreader.__next__()
        riga=[]
        for j in range (0,len(Index)):
            riga.append(row[Index[j]])

        SignalMatrix[i-2]=riga

"""interfaccia utente"""

Tempo=(rowcount-2)*int(min(SignalTask))/1000
minuti=int(Tempo/60)
secondi=Tempo-(minuti*60)
print("\nExpected duration of the Test: %s seconds (%s:%s minutes)"%(Tempo, minuti,int(secondi)))
print("Number of processed signals: %s\n"%len(SignalName))

s = input('Specify a different name for CAPL.can? (y/n): ')
if s=="y":
    r = input('File name: ')
    out_file = open(r+".can","w")
else:
    out_file = open("CAPL.can","w")


"""                PARSE CAPL                      """

"""                VARIABLES                      """

out_file.write("/*@@var:*/\n")
out_file.write("variables\n")
out_file.write("{\n")
Task=[]
for i in SignalTask:
    if i not in Task:
        Task.append(i)
for i in Task:
    out_file.write("msTimer t%sms;\n"%i)
    out_file.write("word Cnt%sms=0;\n"%i)
    out_file.write("word CountArray%sms=0;\n"%i)

mex=[]
Mex=[]
for i in range (0,len(mexname)):
    if mexname[i] not in mex:
        mex.append(mexname[i])
        Mex.append(MexName[i])
for i in range(0,len(mex)):
    out_file.write("message %s %s;\n"%(Mex[i],mex[i]))



out_file.write("word LenArray=%s;\n" % LenArray)

NumOfArray=[]

for i in Task:
    LenInput=(rowcount-2)*int(min(SignalTask))/int(i);
    tmp=int(LenInput/LenArray)+1
    NumOfArray.append(tmp)
    out_file.write("word LenInput%sms=%s;\n" % (i,LenInput))
    out_file.write("word NumOfArray%sms=%s;\n" % (i,NumOfArray[len(NumOfArray)-1]))


#ARRAY SEGNALI

for i in range(0,len(SignalName)):
    ind=Task.index(SignalTask[i])
    for j in range (0,NumOfArray[ind]):
        out_file.write("float %s_%s[%s]="%(SignalName[i],j+1,LenArray))
        out_file.write("{")
        for k in range (j*LenArray,(j+1)*LenArray-1):
            out_file.write("%s,"%SignalMatrix[k][i])
        out_file.write("%s};\n"%SignalMatrix[(j+1)*LenArray-1][i])




out_file.write("}\n")
out_file.write("/*@@end*/\n")


"""                START                      """
out_file.write("/*@@startStart:Start:*/\n")
out_file.write("on start\n")
out_file.write("{\n")
out_file.write("\n")
for i in Task:
    if ((i==2)|(i==4)):
        out_file.write("t%sms.set(0);\n"%i)
    else:
        out_file.write("t%sms.set(1);\n"%i)

out_file.write("\n")
out_file.write("}\n")
out_file.write("/*@@end*/\n")


"""                TASK                       """
for i in range (0,len(Task)):
    out_file.write("/*@@timer:t%sms:*/\n"%Task[i])
    out_file.write("on timer t%sms\n"%Task[i])
    out_file.write("{\n")
    out_file.write("t%sms.set(%s);\n"%(Task[i],Task[i]))
    out_file.write("if(Cnt%sms>=LenArray)\n"%Task[i])
    out_file.write("{\n")
    out_file.write("Cnt%sms=0;\n"%Task[i])
    out_file.write("CountArray%sms++;\n"%Task[i])
    out_file.write("}\n\n")
    out=[]

    for j in range (0,NumOfArray[i]):

        out_file.write("if(CountArray%sms==%s)\n"%(Task[i],(j)))
        out_file.write("{\n")
        for k in range (0,len(SignalName)):
            if SignalTask[k]==Task[i]:
                out_file.write("%s.%s.phys=%s_%s[Cnt%sms];\n"%(mexname[k],Signal[k],SignalName[k],(j+1),Task[i]))
                message=mexname[k]
                if message not in out:
                    out.append(message)
        for h in out:
            out_file.write("output(%s);\n"%h)

        out_file.write("Cnt%sms++;\n"%Task[i])
        out_file.write("}\n")
    out_file.write("}\n")
    out_file.write("/*@@end*/\n")


out_file.close()

print("\n\nCAPL FILE GENERATED\n\n")

s=input("Open Vector CANoe? [y/n]:")
if s=="y":
##    osCommandString = "cd c:\\"
##    os.system(osCommandString)
    osCommandString = "start CANoe32.exe"
    os.system(osCommandString)



